FROM ubuntu:20.04

# The Python version to install
ARG PYTHON_VERSION=3.10

ARG USERNAME=vscode
ARG USER_UID=1000
ARG USER_GID=1000
# Create the user
RUN groupadd --gid $USER_GID $USERNAME \
    && useradd -l --uid $USER_UID --gid $USER_GID -m $USERNAME -s /bin/bash \
    && apt-get update \
    && apt-get install -y sudo \
    && echo $USERNAME ALL=\(root\) NOPASSWD:ALL > /etc/sudoers.d/$USERNAME \
    && chmod 0440 /etc/sudoers.d/$USERNAME

#
# Install system packages
#
RUN apt -y update \
    && apt -y --no-install-recommends install software-properties-common \
    && add-apt-repository -y ppa:deadsnakes/ppa \
    && apt -y update \
    && apt -y upgrade \
    && apt -y --no-install-recommends install tzdata \
    && TZ=Etc/UTC \
    && apt -y --no-install-recommends install \
        build-essential \
        ca-certificates \
        cmake \
        cmake-data \
        git \
	git-lfs \
        pkg-config \
        libcurl4 \
        libsm6 \
        libxext6 \
        libssl-dev \
        libffi-dev \
        libxml2-dev \
        libxslt1-dev \
        zlib1g-dev \
        unzip \
        curl \
        wget \
        python${PYTHON_VERSION} \
        python${PYTHON_VERSION}-dev \
        python${PYTHON_VERSION}-venv \
        python${PYTHON_VERSION}-distutils \
        ffmpeg \
        libtbb2 \
        libtbb-dev \
        vim \
        nano \
        htop \
        iputils-ping \
        tmux \
        less \
        ca-certificates \
        curl \
        apt-transport-https \
        lsb-release gnupg \
    && ln -s /usr/bin/python${PYTHON_VERSION} /usr/local/bin/python \
    && ln -s /usr/local/lib/python${PYTHON_VERSION} /usr/local/lib/python \
    && curl https://bootstrap.pypa.io/get-pip.py | python \
    && rm -rf /var/lib/apt/lists/*


RUN pip --no-cache-dir install --upgrade pip setuptools wheel ipython

RUN pip --no-cache-dir install pandas \
                               pyarrow \
                               azure-identity

RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash
# Start container as non-root user
USER $USERNAME
