#!/bin/bash

# Find an absolute path for a folder where the current script (run_container.sh) is located
ABSOLUTE_SCRIPT_PATH=$(realpath "$0")
WORKSPACE_FOLDER=$(dirname "$ABSOLUTE_SCRIPT_PATH")
HOST_WORKSPACE="${WORKSPACE_FOLDER}/../../" # Home/Workspace path for pace on the host
DOCKER_WORKSPACE="/home/<USER>" # Home/Workspace path for pace in the docker


if [[ -z "$HOST_WORKSPACE" ]]; then
    echo "Usage: $(basename "$0") <workspace>"
    exit 1
fi

docker run --rm --network host -it \
--mount source=/var/run/docker.sock,target=/var/run/docker.sock,type=bind \
--mount source="$HOST_WORKSPACE",target=$DOCKER_WORKSPACE,type=bind,consistency=cached \
--workdir $DOCKER_WORKSPACE \
--env HOME=$DOCKER_WORKSPACE \
--name dataloop-"$(whoami)" \
--cap-add SYS_ADMIN \
dataloop-"$(whoami)":latest