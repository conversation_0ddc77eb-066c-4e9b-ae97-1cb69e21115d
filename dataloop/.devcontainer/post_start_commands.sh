#!/bin/bash

[ -z "$ARTIFACTORY_USER" ] && \
    echo "ERROR: environment variable ARTIFACTORY_USER not set" && \
    exit 1 || \
    echo "INFO: environment variable ARTIFACTORY_USER is set" 

[ -z "$ARTIFACTORY_TOKEN" ] && \
    echo "ERROR: environment variable ARTIFACTORY_TOKEN not set" && \
    exit 1 || \
    echo "INFO: environment variable ARTIFACTORY_TOKEN  is set" 

mkdir -p $HOME

export PATH=~/.local/bin:$PATH
export PYTHONPATH=~/local/lib/python3.10/site-packages:$PYTHONPATH

pip install --user \
    --index-url https://$ARTIFACTORY_USER:$<EMAIL>/artifactory/api/pypi/shared-pypi-prod/simple \
    dataloop-sdk 

pip install --user \
    --index-url https://$ARTIFACTORY_USER:$<EMAIL>/artifactory/api/pypi/shared-pypi-prod/simple \
    mdm-viper-lib

pip install --user \
    --index-url https://$ARTIFACTORY_USER:$<EMAIL>/artifactory/api/pypi/shared-pypi-prod/simple \
    datasets-client

#az login --tenant a6c60f0f-76aa-4f80-8dba-092771d439f0 --use-device-code
