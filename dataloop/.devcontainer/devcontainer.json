// For format details, see https://aka.ms/devcontainer.json. For config options, see the
// README at: https://github.com/devcontainers/templates/tree/main/src/python
{
    "name": "Dataloop",
    // Or use a Dockerfile or Docker Compose file. More info: https://containers.dev/guide/dockerfile
    // "image": "mcr.microsoft.com/devcontainers/python:3.8-bullseye",
    "build": {
        // Path is relative to the devcontainer.json file.
        "dockerfile": "Dockerfile"
    },

    // Features to add to the dev container. More info: https://containers.dev/features.
    // "features": {},

    "runArgs": [
        "--name",
        "dataloop-${localEnv:USER}",
        // Networking
        "--network=host"
    ],

    "remoteEnv": {
        "WORKSPACE_FOLDER": "${containerWorkspaceFolder}",
        "DISPLAY": "${localEnv:DISPLAY}",
        "ARTIFACTORY_USER": "${localEnv:ARTIFACTORY_USER}",
        "ARTIFACTORY_TOKEN": "${localEnv:ARTIFACTORY_TOKEN}",
        "HOME": "/tmp/vscode-home"
    },

    "mounts": [
        // Git Config Mount
        "source=${localEnv:HOME}/.gitconfig,target=/home/<USER>/.gitconfig,type=bind",
        // Git Credentials Mount
        "source=${localEnv:HOME}/.git-credentials,target=/home/<USER>/.git-credentials,type=bind",
        // Netrc Credentials
        "source=${localEnv:HOME}/.netrc,target=/home/<USER>/.netrc,type=bind"
    ],

    // Use 'forwardPorts' to make a list of ports inside the container available locally.
    // "forwardPorts": [],

    // Use 'postCreateCommand' to run commands after the container is created.
    // "postCreateCommand": "pip3 install --user -r requirements.txt",

    // Configure tool-specific properties.
    "customizations": {
        "vscode": {
            "extensions": [
                "ms-python.python",
                "ms-python.vscode-pylance",
                "njpwerner.autodocstring",
                "timonwong.shellcheck",
                "databricks.databricks"
            ]
        }
    },

    // Uncomment to connect as root instead. More info: https://aka.ms/dev-containers-non-root.
    "remoteUser": "vscode",


    "postStartCommand": "./.devcontainer/post_start_commands.sh"

}
