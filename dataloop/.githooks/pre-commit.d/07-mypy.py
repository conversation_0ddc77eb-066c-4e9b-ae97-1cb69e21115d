#!/usr/bin/env python3
"""MyPy githooks."""
__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import subprocess
import sys
from typing import List

from helpers import LOGGER, skip_on_merge_commit


@skip_on_merge_commit
def main(modified_files: List[str]) -> int:
    """Run MyPy.

    Args:
        modified_files: list of files changed in commit

    Returns:
        0 in case of success, 1 otherwise.
    """

    mypy_command = ["mypy", "--config-file=pyproject.toml"]

    try:
        subprocess.check_call(mypy_command + modified_files)
    except subprocess.CalledProcessError:
        LOGGER.warning(
            "\nNote on mypy in the githook vs the gatekeeper:\n"
            "The githook fails on all mypy errors in files you are trying to commit, while the the gatekeeper \n"
            "only fails for mypy errors introduced with your changes.\n"
            "While it is possible to ignore those mypy errors not introduced by you, everyone\n"
            "is encouraged to fix all or some of the mypy errors in existing code! This way we can reduce the \n"
            "number of mypy errors in old code gradually over time in a community effort."
        )
        return 1
    return 0


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("files", nargs="+", help="Python files to be checked")
    args = parser.parse_args()
    sys.exit(main(modified_files=args.files))
