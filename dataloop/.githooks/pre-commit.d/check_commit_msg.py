"""Commit Message Hook.

Checks for a certain branch name pattern that should include the ticket id
If the branch name fulfills the pattern requirement, commit messages is expanded
by ADO ticket id.
Copied from https://github.com/PACE-INT/pace/blob/main/tools/git_hooks/check_commit_msg.py (modified).
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import re
import subprocess
import sys
from pathlib import Path
from typing import List, Optional

BRANCH_PREFIXES = [
    "feature",
    "bugfix",
    "release",
    "eval",
    "hotfix",
]


def is_commit(test_string: str) -> bool:
    """Test if string is a commit id.

    Args:
        test_string: string to test for commit
    """
    if re.match(r"^[0-9a-f]+$", test_string):  # noqa: SIM103
        return True
    return False


def get_ticket_id(branch_name: str) -> Optional[str]:
    """Check if branch name has the correct style.

    Args:
        branch_name: name of the branch
    """
    branch_prefix = "|".join(BRANCH_PREFIXES)
    match = re.match(rf"^(?:{branch_prefix})\/((?:[A-Z]+-)?\d+)", branch_name)
    if not match:
        return None
    return match.group(1)


def get_branch_name(commit_message_file: str) -> str:
    """Extract the branch name from .git.

    Args:
        commit_message_file: path to the commit message file
    """
    head_file = Path(commit_message_file).parent / "HEAD"
    head_file_content = head_file.read_text().strip()
    print(f"HEAD file content: {head_file_content}")

    branch_name = head_file_content.replace("ref: refs/heads/", "")
    print(f"Reduced branch name to: {branch_name}")

    if not is_commit(branch_name):
        return branch_name

    print(f"HEAD content is a commit id: {branch_name}")
    head_name_file = Path(commit_message_file).parent / "rebase-merge" / "head-name"

    if not head_name_file.exists():
        print(f"Not existing: {head_name_file!s}")
        head_name_file = Path(commit_message_file).parent / "rebase-apply" / "head-name"

    if not head_name_file.exists():
        print(f"Not existing: {head_name_file!s}")
        print("No choices left.")
        sys.exit(1)

    head_name_file_content = head_name_file.read_text().strip()
    print(f"head-name file content: {head_name_file_content}")

    branch_name = head_name_file_content.replace("refs/heads/", "")
    print(f"Reduced branch name to: {branch_name}")
    return branch_name


def main(args: List[str]) -> None:
    """Entry Point.

    Args:
        args: list of arguments passed to the script
    """
    commit_message_file = args[0]
    branch_name = get_branch_name(commit_message_file)
    ticket_id = get_ticket_id(branch_name)

    if ticket_id:
        print(f"Found ticket ID in branch: {ticket_id}")
    else:
        print("Incorrect branch name. Allowed pattern: PREFIX/TICKET-ID-custom-name")
        print(f"Allowed PREFIX: {BRANCH_PREFIXES}")
        print("Allowed ID: ID for ADO Ticket")
        print("\nPlease rename your branch")
        sys.exit(1)

    trailer_cmd = [
        "git",
        "interpret-trailers",
        "--in-place",
        commit_message_file,
        "--trim-empty",
        "--if-exists",
        "doNothing",
        "--trailer",
        f"issue-id:AB#{ticket_id}",
    ]
    subprocess.run(trailer_cmd, check=True)


if __name__ == "__main__":
    main(sys.argv[1:])
