"""Helpers for common githook functionality."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import functools
import logging
import os
import subprocess
import sys
from pathlib import Path
from typing import Any, Callable

from pkg_resources import get_distribution, parse_requirements


def configure_githook_logging(level: int = logging.INFO) -> None:
    """Configure logging for githooks.

    If available, logging will support colored output

    Args:
        level: logging level as an integer
    """

    format_str = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"

    try:
        from coloredlogs import ColoredFormatter

        formatter = ColoredFormatter(format_str)
    except ImportError:
        formatter = logging.Formatter(format_str)

    stream_handler = logging.StreamHandler(sys.stdout)
    stream_handler.setFormatter(formatter)

    # Set-up root logger
    root_logger = logging.getLogger()
    root_logger.handlers = []
    root_logger.addHandler(stream_handler)

    # Set the log level of the root logger (all other loggers should inherit this level)
    root_logger.setLevel(level)


configure_githook_logging()
LOGGER = logging.getLogger("GitHooks-Logger")
_REQUIREMENTS_PATH = Path(__file__).parent.parent.parent / "dev.txt"
_DISABLE_STAGING = "XFLOW_HOOKS_DISABLE_STAGING"


def _extract_version_from_requirements_file(requirements_file: Path, package_name: str) -> str:
    """Extract the versions of the package from the requirements file.

    Args:
        requirements_file: Path to the requirements file.
        package_name: Package name for which the version should be extracted.
    """

    with open(requirements_file, "r", encoding="utf-8") as handle:
        requirements_lines = handle.readlines()
        requirements_parsed = list(parse_requirements(requirements_lines))

    for req in requirements_parsed:
        if req.name.lower() == package_name.lower():
            return req.specs[0][-1]  # get the version from the spec

    raise AssertionError(f"Package '{package_name}' not found in '{requirements_file}'")


def check_version_compatibility(package_name: str) -> Callable[..., Callable[..., Any]]:
    """Decorator factory for checking the installed versions of the packages match the expected versions."""

    def _decorator(func: Callable[..., Any]) -> Callable[..., Any]:
        @functools.wraps(func)
        def _wrapper(*args: Any, **kwargs: Any) -> Any:
            expected_version = _extract_version_from_requirements_file(_REQUIREMENTS_PATH, package_name)
            installed_version = get_distribution(package_name).version
            if installed_version != expected_version:
                warning_str = (
                    f"Version of package '{package_name}' is not as expected. Consider updating your environment.\n"
                    f" Expected version ({package_name}): {expected_version}\n"
                    f"Installed version ({package_name}): {installed_version}"
                )
                LOGGER.warning(warning_str)
            return func(*args, **kwargs)

        return _wrapper

    return _decorator


def allow_automatic_staging(func: Callable[..., Any]) -> Callable[..., Any]:
    """Decorator to automatically stage modified files after a successful run of the decorated function."""

    @functools.wraps(func)
    def _wrapper(*args: Any, **kwargs: Any) -> Any:
        return_value = func(*args, **kwargs)
        modified_files = kwargs["modified_files"]  # relies on passing the modified_files as a keyword argument
        if not os.environ.get(_DISABLE_STAGING, False):
            LOGGER.info(f"Automatic staging is enabled. If you dislike this behavior you can set {_DISABLE_STAGING}=1")
            subprocess.check_output(["git", "add"] + list(modified_files))
        else:
            LOGGER.warning(
                f"Automatic staging of githook changes was disabled via {_DISABLE_STAGING} "
                "(unset to get default behavior). You might have new unstaged changes."
            )

        return return_value

    return _wrapper


def skip_on_merge_commit(func: Callable[..., Any]) -> Callable[..., Any]:
    """Decorator to skip pre-commit checks on merge commits."""

    def _wrapper(*args: Any, **kwargs: Any) -> Any:
        if check_merge():
            LOGGER.warning("Skipping hook because commit is a merge commit.")
            return 0
        return func(*args, **kwargs)

    return _wrapper


def check_merge() -> bool:
    """Return True if commit is of type merge, return False otherwise."""
    try:
        subprocess.check_output("git rev-parse -q --verify MERGE_HEAD", shell=True)
        return True
    except subprocess.CalledProcessError:
        return False
