#!/usr/bin/env python3
"""Ensure correct copyright in changed python files."""


__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import datetime
import os
import re
import sys
from typing import List

from helpers import LOGGER, allow_automatic_staging, skip_on_merge_commit

PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

ATHENA_BOSCH_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

ATHENA_BOSCH_L4_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2021-2023 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

BOSCH_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

BOSCH_L4_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2023 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

CARIAD_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Cariad SE. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

CARIAD_2024_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
C O P Y R I G H T
-----------------------------------------------------------------------------------
Copyright (c) 2021-2024 Cariad SE. All rights reserved.
Copyright (c) 2024-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

copyright_strings = {
    "pace": PACE_COPYRIGHT,
    "athena_bosch_pace": ATHENA_BOSCH_PACE_COPYRIGHT,
    "bosch_pace": BOSCH_PACE_COPYRIGHT,
    "cariad_pace": CARIAD_PACE_COPYRIGHT,
    "cariad_2024_pace": CARIAD_2024_PACE_COPYRIGHT,
    "athena_bosch_l4_pace": ATHENA_BOSCH_L4_PACE_COPYRIGHT,
    "bosch_l4_pace": BOSCH_L4_PACE_COPYRIGHT,
}
now = datetime.datetime.now()
# update the list of copyright headers
for copyright_key, copyright_string in copyright_strings.items():
    # add the current year to the copyright head
    copyright_strings[copyright_key] = copyright_string.format(now.year)


def check_copyright(file_content: str) -> bool:
    """Check if a correct copyright is already present in the file content.

    Args:
        file_content: The file content to check

    Returns:
        Whether or not the file contains a correct copyright
    """
    has_correct_copyright = any(copyright_head in file_content for copyright_head in copyright_strings.values())

    return has_correct_copyright


def update_copyright(file_content: str) -> str:  # noqa: C901
    """Update the copyright in the file content.

    Args:
        file_content: The file content to be updated

    Returns:
        The file content with an updated or newly added copyright

    Raises:
        ValueError: If an existing copyright is unknown and cant be updated, or
                    if there is no copyright but it cant be added because the file format is unexpected
    """
    regex_flags = re.DOTALL | re.MULTILINE
    new_line = r"[\r\n|\n]"
    python_copyright_pattern = rf"^__copyright__ = \"\"\"(.*?)\"\"\"{new_line}"
    match = re.search(python_copyright_pattern, file_content, flags=regex_flags)

    default_copyright = copyright_strings["pace"]
    updated_file_content = file_content
    # Replace an existing copyright with the latest format
    if match is not None:
        copyright = match.group(0).lower()

        if "bosch" not in copyright and "cariad" not in copyright:
            raise ValueError(f"Encountered unexpected copyright string:\n'{match.group(0)}'")
        if "daimler" in copyright:
            if "2023 Robert Bosch GmbH.".lower() in copyright:
                updated_copyright = copyright_strings["athena_bosch_l4_pace"]
            else:
                updated_copyright = copyright_strings["athena_bosch_pace"]
        elif copyright.count("bosch") > copyright.count("cariad"):
            if "2023 Robert Bosch GmbH.".lower() in copyright:
                updated_copyright = copyright_strings["bosch_l4_pace"]
            else:
                updated_copyright = copyright_strings["bosch_pace"]
        elif copyright.count("bosch") < copyright.count("cariad"):
            if "2024 Cariad SE".lower() in copyright:
                updated_copyright = copyright_strings["cariad_2024_pace"]
            else:
                updated_copyright = copyright_strings["cariad_pace"]
        else:
            updated_copyright = default_copyright

        updated_file_content = re.sub(python_copyright_pattern, updated_copyright, file_content, flags=regex_flags)
    else:
        # Check for comments like """(c) 2024 ...""" without "__copyright__ =" (optional comment or docstring in front)
        copyright_comment_pattern = rf"(^#.+?{new_line})*(^\"\"\".+?\"\"\"{new_line})*\s*\"\"\"\s*\(c\) *\d\d\d\d"
        match = re.search(copyright_comment_pattern, file_content, flags=re.MULTILINE)
        if match is not None and match.start() == 0:
            raise ValueError("Found comment that seems to be a copyright header. Default copyright not inserted.")

        # Insert a new copyright near the top of the file i.e. after the module docstring if that exists
        updated_file_content = re.sub(
            rf"(?P<comments>(^#.+?{new_line})*)(?P<module_docstring>^\"\"\".+?\"\"\"{new_line})?(?P<code>.+)",
            rf"\g<comments>\g<module_docstring>\n{default_copyright}\g<code>",
            file_content,
            flags=regex_flags,
        )
        if updated_file_content == file_content:
            raise ValueError("File does not match expected format. Unable to add copyright.")

    return updated_file_content


@allow_automatic_staging
@skip_on_merge_commit
def main(modified_files: List[str]) -> int:
    """Run copyright check.

    Args:
        modified_files: list of python files changed in commit

    Returns:
        0 in case of success, 1 otherwise.
    """

    for file_path in modified_files:
        if not os.path.isfile(file_path):  # Deleted files are always fine
            continue
        with open(file_path, "r", encoding="utf-8") as file:
            file_content = file.read()
        if not check_copyright(file_content):
            try:
                updated_file_content = update_copyright(file_content)
                LOGGER.info(f"Updating copyright in {file_path}")
                with open(file_path, "w", encoding="utf-8") as file:
                    file.write(updated_file_content)
            except ValueError as e:
                LOGGER.error(f"Unable to update copyright for file '{file_path}'. Please fix it manually. {str(e)}")
                return 1
    return 0


if __name__ == "__main__":
    parser = argparse.ArgumentParser()
    parser.add_argument("files", nargs="+", help="Python files to be checked")
    args = parser.parse_args()
    sys.exit(main(modified_files=args.files))
