## Repo in general, Coding standards and CI/CD pipelines (not complete yet)
* @PACE-INT/dataloop-library-admins

.github/CODEOWNERS @PACE-INT/dataloop-library-admins

## Responsibilities for individual modules
mdm-viper-lib/src/mdm_viper_lib/azure_credential            @Alp-Emek_pace @efrain-lima-miranda_pace
mdm-viper-lib/src/mdm_viper_lib/calstorage_interface        @PACE-INT/viper-data-extraction-admin
mdm-viper-lib/src/mdm_viper_lib/dataloop_event_lib/         @alaa-alloush_pace @benjamin-classen_pace
mdm-viper-lib/src/mdm_viper-lib/dataloop_integration/       # To be split into service-specific-part
mdm-viper-lib/src/mdm_viper-lib/knowledge_layer_client/     @udo-paschke_pace @stefan-leun_pace @arsen-sagoian_pace
mdm-viper-lib/src/mdm_viper_lib/mdm_label_client            @<PERSON>-<PERSON><PERSON><PERSON>_pace @marcel-straub_pace @Johannes-Rieger2_pace
mdm-viper-lib/src/mdm_viper_lib/mdm_sdk_extension           # To be deprecated, so no Codeowner
mdm-viper-lib/src/mdm_viper_lib/mdm_search_module           @simon-munder_pace
mdm-viper-lib/src/mdm_viper_lib/mdm_state_validator         # To be deprecated, so no Codeowner
mdm-viper-lib/src/mdm_viper_lib/sama_client                 @marcel-straub_pace
mdm-viper-lib/src/mdm_viper_lib/ingest_landingzone          @PACE-INT/data-delivery-lift-admin

## Fiftyone Teams Responsibles
/.devcontainer/dataloop_fiftyone/                           @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone                                    @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/traffic_light                          @PACE-INT/trafficlights-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/traffic_light.py                  @PACE-INT/trafficlights-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/traffic_sign                           @PACE-INT/trafficsigns-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/traffic_sign.py                   @PACE-INT/trafficsigns-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/vehicles                               @PACE-INT/vipervehicles-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/vehicles.py                       @PACE-INT/vipervehicles-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/vru                                    @PACE-INT/vulnerableroadusers-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/vru.py                            @PACE-INT/vulnerableroadusers-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/dyo_common                             @PACE-INT/vulnerableroadusers-xflow-devs @PACE-INT/vipervehicles-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/semseg                                 @PACE-INT/aisemantic-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/semseg.py                         @PACE-INT/aisemantic-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/stixel                                 @PACE-INT/aisemantic-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/stixel.py                         @PACE-INT/aisemantic-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/multi_stixel                           @PACE-INT/aisemantic-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/multi_stixel.py                   @PACE-INT/aisemantic-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/ingestors/environment_classification             @PACE-INT/environment-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
/dataloop-viper-fiftyone/src/dataloop_viper_fiftyone/pipeline/tasks/environment_classification.py     @PACE-INT/environment-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers
dataloop/dataloop-viper-fiftyone/tests/integration/pipeline/tasks/test_environment_classification.py  @PACE-INT/environment-xflow-devs @PACE-INT/dataloop-fiftyone-maintainers

# Responsible for datasets-client
/datasets-client @PACE-INT/dll-workflows
.github/workflows/build_and_publish_datasets_client.yml @PACE-INT/dll-workflows

## Databricks responsibles
databricks/                                                                        @PACE-INT/dataloop-library-databricks-maintainers
databricks/notebooks/dd_leadership/                                                @PACE-INT/080-domain-leadership-data-delivery-reader
databricks/pipelines/ada_dataproduct_silver/mdd/**/*environment_classification*    @PACE-INT/environment-xflow-devs
databricks/notebooks/viper_dspde/                                                  @PACE-INT/viper-data-extraction-admin
databricks/pipelines/ada_dataproduct_bronze/cero/                                  @PACE-INT/data-delivery-connected-endurance-run-reader
databricks/pipelines/ada_dataproduct_silver/cero/                                  @PACE-INT/data-delivery-connected-endurance-run-reader
databricks/pipelines/ada_dataproduct_bronze/azureml/                               @PACE-INT/viper-core-ai-model-performance-evaluation-reader
databricks/pipelines/ada_dataproduct_silver/azureml/                               @PACE-INT/viper-core-ai-model-performance-evaluation-reader
databricks/pipelines/ada_dataproduct_gold/azureml/                                 @PACE-INT/viper-core-ai-model-performance-evaluation-reader
databricks/pipelines/ada_dataproduct_gold/cero/                                    @PACE-INT/data-delivery-connected-endurance-run-reader
databricks/pipelines/ada_dataproduct_gold/viper_environment/                       @PACE-INT/environment-xflow-devs
databricks/notebooks/viper_dsp_al                                                  @PACE-INT/viper-active-learning-admin @PACE-INT/viper-active-learning-reader
databricks/pipelines/ada_dataproduct_gold/active_learning                          @PACE-INT/viper-active-learning-admin @PACE-INT/viper-active-learning-reader
databricks/pipelines/ada_dataproduct_silver/mdd/src/active_learning                @PACE-INT/viper-active-learning-admin @PACE-INT/viper-active-learning-reader
databricks/pipelines/ada_dataproduct_gold/data_extraction                          @PACE-INT/viper-data-extraction-admin
databricks/pipelines/ada_dataproduct_gold/datacuration                             @PACE-INT/dataloop-fiftyone-maintainers
databricks/pipelines/ada_dataproduct_silver/sipa                                   @PACE-INT/customer-function-sipa
databricks/notebooks/viper_lane_enabler                                            @PACE-INT/dataloop-atlatec-codeowner @PACE-INT/viper-lane-reader
databricks/pipelines/ada_dataproduct_bronze/digital_testing                        @PACE-INT/092-recompute-reader
databricks/pipelines/ada_dataproduct_silver/digital_testing                        @PACE-INT/092-recompute-reader
databricks/pipelines/ada_dataproduct_gold/digital_testing                          @PACE-INT/092-recompute-reader
databricks/notebooks/cufu_ai_dataloop                                              @PACE-INT/customer-function-ai-data-loop
databricks/pipelines/ada_dataproduct_gold/gmd_parking                              @PACE-INT/viper-parking-stitched-tv-perception

# Databricks MDM responsibilities

/.github/workflows/mdm_deploy_delta_sharing_views.yaml                                 @PACE-INT/mdm-filecatalog-codereviewers
/databricks/pipelines/ada_dataproduct_bronze/mdm                                   @PACE-INT/mdm-filecatalog-codereviewers
/databricks/delta-sharing                                                          @PACE-INT/mdm-filecatalog-codereviewers

## Databricks libraries responsibles
/databricks/libraries/dbxlib        @PACE-INT/RDD
/databricks/libraries/rddlib        @PACE-INT/RDD
/databricks/libraries/dbx_schemas   @PACE-INT/data-delivery-analytics-platform-reader

# dbx_schemas responsibles
## public schemas to be (partially) used by downstream applications -> producers of the table
/databricks/libraries/dbx_schemas/src/dbx_schemas/silver/viper_training_data_curation       @Johannes-Rieger2_pace @benedikt-kansy_pace
## contract tests against required fields from upstream tables -> consumers of the table
/databricks/libraries/dbx_schemas/tests/silver/viper_training_data_curation/traffic_sign    @PACE-INT/trafficsigns-xflow-devs
/databricks/libraries/dbx_schemas/tests/silver/viper_training_data_curation/traffic_light   @PACE-INT/trafficlights-xflow-devs
/databricks/libraries/dbx_schemas/tests/silver/viper_training_data_curation/pole            @PACE-INT/viper-static-objects-poles

## RDD Usecases Folder
/databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/  @PACE-INT/RDD
/databricks/pipelines/ada_dataproduct_silver/pace_metrics/          @PACE-INT/RDD
/databricks/pipelines/ada_dataproduct_silver/sef/                   @PACE-INT/RDD
/databricks/pipelines/ada_dataproduct_silver/needs/                 @PACE-INT/RDD
/databricks/pipelines/ada_dataproduct_silver/rng_metrics/           @PACE-INT/RDD
/databricks/pipelines/ada_dataproduct_gold/ada_kpi_metrics/         @PACE-INT/RDD
/databricks/pipelines/ada_dataproduct_gold/release_graph/           @PACE-INT/RDD
/.github/workflows/rdd_*                                            @PACE-INT/RDD
/.github/rdd_scripts/                                               @PACE-INT/RDD
/databricks/pipelines/common/rdd_orchestration/                     @PACE-INT/RDD

# LAPI Usecases Folder
/databricks/pipelines/ada_dataproduct_e2e_workflows/lapi_ng     @PACE-INT/data-delivery-lapi-admin

## Contract KPIs
databricks/pipelines/ada_dataproduct_gold/ada_kpi_metrics/functional_kpis/calculate_cufu_ild_u2d_kpis.py @PACE-INT/042-inlanedriving-system-integration-reader

## Databricks DMV responsibilities
databricks/notebooks/dd_dmv/                                             @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_bronze/azure_costs/                 @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/data_quality/                @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/dmv_stats/                   @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/mdd/**/*dmv*                 @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/mdd/variables.yml            @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/mdd/**/*datamanagement*      @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/mdd/src/mapinfo.py           @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_gold/azure_costs/                   @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_gold/dmv_stats/                     @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/common/dmv_housekeeping/                            @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
.github/workflows/dmv_*                                                  @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
.github/workflows/azure_costs_*                                          @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
.github/workflows/dq_*                                                   @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/ada_measurement_time_series/ @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
databricks/pipelines/ada_dataproduct_silver/ada_ontology/                @PACE-INT/data-delivery-data-management-verticalization-dmv-admin

## Databricks Analytics Platform responsibilities
databricks/pipelines/ada_dataproduct_silver/pioneering_fleet/        @PACE-INT/data-delivery-analytics-platform-reader
databricks/pipelines/ada_dataproduct_silver/drive_time_series/       @PACE-INT/data-delivery-analytics-platform-reader
databricks/pipelines/ada_dataproduct_gold/drive_time_series/         @PACE-INT/data-delivery-analytics-platform-reader
.github/workflows/drive_time_series_*                                @PACE-INT/data-delivery-analytics-platform-reader

## Databricks Maintainers
databricks/pipelines/ada_dataproduct_silver/mdd/**/*datamanagement_helper.py    @PACE-INT/dataloop-library-databricks-maintainers

# Dataloop Usecases Folder
/dataloop-usecases/src/dataloop_usecases/lane/                  @PACE-INT/viperlane-xflow-devs
/dataloop-usecases/tests/lane/                                  @PACE-INT/viperlane-xflow-devs
/dataloop-usecases/src/dataloop_usecases/light/                 @PACE-INT/viper-lsr-reader
/dataloop-usecases/tests/light                                  @PACE-INT/viper-lsr-reader
/dataloop-usecases/src/dataloop_usecases/trailer/               @PACE-INT/viper-trailer-reader
/dataloop-usecases/tests/trailer                                @PACE-INT/viper-trailer-reader
/dataloop-usecases/src/dataloop_usecases/traffic_sign           @PACE-INT/trafficsigns-xflow-devs
/dataloop-usecases/src/dataloop_usecases/traffic_light          @PACE-INT/trafficlights-xflow-devs
/dataloop-usecases/src/dataloop_usecases/occupancy_25d          @PACE-INT/occupany-2-5d-bev-devs
/dataloop-usecases/tests/**/occupancy_25d                       @PACE-INT/occupany-2-5d-bev-devs
/dataloop-usecases/src/dataloop_usecases/freespace              @PACE-INT/viper-freespace-reader
/dataloop-usecases/tests/**/freespace                           @PACE-INT/viper-freespace-reader
/dataloop-usecases/src/dataloop_usecases/goc_kpi_gt_pipeline    @PACE-INT/viper-generic-objects-reader @Sangami-ilangovan_pace @Martin-Bergen_pace
/dataloop-usecases/tests/**/goc_kpi_gt_pipeline                 @PACE-INT/viper-generic-objects-reader @Sangami-ilangovan_pace @Martin-Bergen_pace
/dataloop-usecases/docker                                       @PACE-INT/occupany-2-5d-bev-devs  @PACE-INT/aisemantic-xflow-devs

# Dataloop Documentation Folder
/doc/                                                       @PACE-INT/dataloop-library-docs-maintainers
/doc/data_products/silver/gnss_gt.rst                       @PACE-INT/mdm-indexingenrichment-codereviewers
/.github/workflows/core_doc_build_publish.yml               @PACE-INT/dataloop-library-databricks-maintainers @PACE-INT/dataloop-library-admins @PACE-INT/dataloop-library-docs-maintainers

## For now, we allow the following do not have Code ownership yet
tests/
examples/

# Dataloop data formats
/dataloop-data-formats/src/dataloop_data_formats/joint_box_format/    @PACE-INT/vipervehicles-xflow-devs @PACE-INT/vulnerableroadusers-xflow-devs
/dataloop-data-formats/src/dataloop_data_formats/semseg_god/          @PACE-INT/aisemantic-xflow-devs
/dataloop-data-formats/src/dataloop_data_formats/semantics_gt_gen/    @PACE-INT/aisemantic-xflow-devs
/dataloop-data-formats/tests/**/semantics_gt_gen/                     @PACE-INT/aisemantic-xflow-devs
/dataloop-data-formats/tests/**/semseg_god/                           @PACE-INT/aisemantic-xflow-devs
/dataloop-data-formats/src/dataloop_data_formats/occupancy_25d/       @PACE-INT/occupany-2-5d-bev-devs
/dataloop-data-formats/tests/**/occupancy_25d/                        @PACE-INT/occupany-2-5d-bev-devs

# Label set
/label-set/src/label_set/label_sets/vision/box3d/               @PACE-INT/vipervehicles-xflow-devs
/label-set/src/label_set/label_sets/vision/blockage/            @PACE-INT/environment-xflow-devs
/label-set/src/label_set/label_sets/vision/road_type/           @PACE-INT/environment-xflow-devs
/label-set/src/label_set/label_sets/vision/tunnel/              @PACE-INT/environment-xflow-devs
/label-set/src/label_set/label_sets/vision/road_condition/      @PACE-INT/environment-xflow-devs
/label-set/src/label_set/label_sets/vision/fog_detection/       @PACE-INT/environment-xflow-devs
/label-set/src/label_set/label_sets/vision/visual_relationship/ @PACE-INT/environment-xflow-devs
/label-set/src/label_set/label_sets/vision/semseg/              @PACE-INT/aisemantic-xflow-devs
/label-set/src/label_set/label_sets/vision/lost_cargo/          @PACE-INT/lostcargo-xflow-devs
/label-set/src/label_set/label_sets/vision/trailer/             @PACE-INT/trailer-xflowdevs
/label-set/src/label_set/label_sets/vision/lane_markings/       @PACE-INT/viperlane-xflow-devs
/label-set/src/label_set/label_sets/lidar/                      @PACE-INT/lidarperception-xflow-devs
/label-set/src/label_set/label_sets/radar/                      @PACE-INT/radarperception-xflow-devs
/label-set/src/label_set/label_sets/vision/traffic_light/       @PACE-INT/trafficlights-xflow-devs
/label-set/src/label_set/label_sets/vision/occupancy_25d/       @PACE-INT/occupany-2-5d-bev-devs

# Dataloop SDK
/sdk/  @martin-mozer_pace @michael-kaeser_pace @marcel-straub_pace
/sdk/dataloop-validation/  @PACE-INT/data-delivery-data-management-verticalization-dmv-admin

# Human in the loop
/human-in-the-loop/  @PACE-INT/viper-groundtruth-x-calibration

# Offline calibration client
/offline-calibration-client/  @PACE-INT/viper-groundtruth-x-calibration
/offline-calibration-mgmt/  @PACE-INT/viper-groundtruth-x-calibration

# Dataloop schemas
/dataloop-schemas/                   @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
.github/workflows/dmv_*     @PACE-INT/data-delivery-data-management-verticalization-dmv-admin
