#!/bin/env python3
"""This script renames a bundle and its workflows for a PR specific deployment."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import re
from pathlib import Path
from typing import Any

import yaml
from bundle_paths import ALL_BUNDLES

BASE_DIR = Path(__file__).parents[2]
_OUTPUT_FILE_PREFIX = ""


def _parse_bundle_directory(raw_value: str) -> Path:
    value = (BASE_DIR / raw_value).resolve()
    if not (value.is_dir() and value.exists() and (value / "databricks.yml").exists()):
        raise argparse.ArgumentTypeError(
            f'Given path "{value}" isn\'t a valid bundle directory. '
            'It must be an existing directory that contains a "databricks.yml" file.'
        )

    return value


def _parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Rename a list of bundles and its workflows for a PR specific deployment."
    )
    parser.add_argument("--pr_number", type=int, required=True, help="The number of the PR to use for renaming.")
    parser.add_argument(
        "--skip_jobs", action="store_true", help="Skip renaming jobs in the bundles. Only rename the bundle itself."
    )
    parser.add_argument(
        "--test", action="store_true", help='Output files are prepended with "pr_" if this option is set.'
    )
    parser.add_argument(
        "bundles", type=_parse_bundle_directory, nargs="+", help="The relative paths of the bundles to rename."
    )
    return parser.parse_args()


def _rename_bundle(bundle_dir: Path, pr_number: int) -> tuple[str, str]:
    # Load bundle config
    with (bundle_dir / "databricks.yml").open("r") as f:
        bundle_config = yaml.safe_load(f)

    # Try to get bundle name, otherwise infer it from directory name
    try:
        name = bundle_config["bundle"]["name"]
    except KeyError:
        print("Bundle has no explicit name defined. Infering name from directory.")
        name = bundle_dir.name

    new_name = f"pr_{pr_number}_{name}"

    print(f'Rename bundle from "{name}" to "{new_name}".')
    bundle_config["bundle"]["name"] = new_name
    # Save new bundle config
    with (bundle_dir / f"{_OUTPUT_FILE_PREFIX}databricks.yml").open("w") as f:
        yaml.safe_dump(bundle_config, f)

    return name, new_name


def _rename_jobs(workflow_file: Path, pr_number: int) -> dict[str, str]:
    # Load workflow config
    with workflow_file.open("r") as f:
        workflow_config = yaml.safe_load(f)

    # Get jobs if any defined, otherwise return
    try:
        jobs: dict[str, Any] = workflow_config["resources"]["jobs"]
    except KeyError:
        print("No jobs defined. Skipping file.")
        return {}

    # Process all jobs
    job_id_map: dict[str, str] = {}
    new_jobs: dict[str, Any] = {}
    for job_id, job in jobs.items():
        # Try to get job name, otherwise infer it from job ID
        try:
            job_name = job["name"]
        except KeyError:
            print("Job has no explicit name defined. Infering name from ID.")
            job_name = job_id

        new_job_id = f"pr_{pr_number}_{job_id}"
        new_name = f"PR {pr_number} - {job_name}"

        job["name"] = new_name
        new_jobs[new_job_id] = job
        job_id_map[job_id] = new_job_id
        print(f'Rename job from "{job_id} ({job_name})" to "{new_job_id} ({new_name})".')

    # Replace jobs with renamed ones
    workflow_config["resources"]["jobs"] = new_jobs

    # Save new workflow config
    with (workflow_file.parent / f"{_OUTPUT_FILE_PREFIX}{workflow_file.name}").open("w") as f:
        yaml.safe_dump(workflow_config, f)

    return job_id_map


def _adapt_references_run_job_task(
    specific_task: dict[str, Any], bundle_name_map: dict[str, str], job_id_map: dict[str, str]
) -> None:
    job_id_ref = specific_task["job_id"]
    if isinstance(job_id_ref, int):
        print("No references need to be adapted.")
        return

    # Check for local reference of job ID
    match = re.match(r"^\$\{resources\.jobs\.(.+)\.id\}$", job_id_ref)
    if match is None or match.group(1) not in job_id_map:
        print("No references need to be adapted.")
        return

    new_job_id_ref = f"${{resources.jobs.{job_id_map[match.group(1)]}.id}}"
    specific_task["job_id"] = new_job_id_ref
    print(f'Replace local reference "{job_id_ref}" with "{new_job_id_ref}".')


def _adapt_references_spark_python_task(
    specific_task: dict[str, Any], bundle_name_map: dict[str, str], job_id_map: dict[str, str]
) -> None:
    python_file = specific_task["python_file"]
    # Check for workspace reference of job ID
    match = re.match(r"^(\/Workspace\/Users\/\${var\.run_sp}\/\.bundle\/\${bundle\.target}\/)(.+?)(\/.+)$", python_file)
    if match is None:
        print("No references need to be adapted.")
        return

    new_bundle_name = bundle_name_map.get(match.group(2), None)
    if new_bundle_name is None:
        print("No references need to be adapted.")
        return

    new_python_file = f"{match.group(1)}{new_bundle_name}{match.group(3)}"

    specific_task["python_file"] = new_python_file
    print(f'Replace workspace reference "{python_file}" with "{new_python_file}".')


ADAPT_REFERENCES_BY_TASK_TYPE = {
    "run_job_task": _adapt_references_run_job_task,
    "spark_python_task": _adapt_references_spark_python_task,
}


def _adapt_references(workflow_file: Path, bundle_name_map: dict[str, str], job_id_map: dict[str, str]) -> None:
    # Load workflow config
    with workflow_file.open("r") as f:
        workflow_config = yaml.safe_load(f)

    # Get jobs if any defined, otherwise return
    try:
        jobs: dict[str, Any] = workflow_config["resources"]["jobs"]
    except KeyError:
        print("No jobs defined. Skipping file.")
        return

    # Process all jobs
    task: dict[str, Any]
    for job in jobs.values():
        for task in job["tasks"]:
            # Adapt references depending on task type
            for task_type, adapt_references_func in ADAPT_REFERENCES_BY_TASK_TYPE.items():
                specific_task = task.get(task_type, None)
                if specific_task is not None:
                    print(f'Replace references in task "{task["task_key"]}" of type {task_type}.')
                    adapt_references_func(specific_task, bundle_name_map, job_id_map)
                    break
            else:
                print(f'No references where replaced in task "{task["task_key"]}".')

    # Save new workflow config
    with (workflow_file).open("w") as f:
        yaml.safe_dump(workflow_config, f)


def main(bundle_dirs_rel: list[str], pr_number: int, skip_jobs: bool) -> None:
    """Rename a list of bundles and its workflows for a PR specific deployment."""
    # Rename all changed bundles and their jobs
    bundle_name_map: dict[str, str] = {}
    job_id_map: dict[str, str] = {}
    for bundle_dir_rel in bundle_dirs_rel:
        bundle_dir = BASE_DIR / bundle_dir_rel

        # Rename bundle
        bundle_name, bundle_new_name = _rename_bundle(bundle_dir, pr_number)
        bundle_name_map[bundle_name] = bundle_new_name

        if skip_jobs:
            continue

        # Rename all jobs in bundle
        workflow_files = list((bundle_dir / "workflows").glob("*.yml"))
        for workflow_file in workflow_files:
            print(f'Found workflow file "{workflow_file}".')
            bundle_job_id_map = _rename_jobs(workflow_file, pr_number)
            job_id_map.update(bundle_job_id_map)

    if skip_jobs:
        return

    # Adapt references of all jobs
    for bundle_dir_rel in ALL_BUNDLES.values():
        bundle_dir = BASE_DIR / bundle_dir_rel
        workflow_files = list((bundle_dir / "workflows").glob(f"{_OUTPUT_FILE_PREFIX}*.yml"))
        for workflow_file in workflow_files:
            print(f'Found workflow file "{workflow_file}".')
            _adapt_references(workflow_file, bundle_name_map, job_id_map)


if __name__ == "__main__":
    args = _parse_args()
    if args.test:
        _OUTPUT_FILE_PREFIX = "pr_"

    main(args.bundles, args.pr_number, args.skip_jobs)
