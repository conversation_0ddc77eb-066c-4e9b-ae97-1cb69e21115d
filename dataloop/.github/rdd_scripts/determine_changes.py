#!/bin/env python3
"""This script determines the changes between either two commits or a tag and its predecessor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import json
import os
import shlex
import subprocess
from collections import defaultdict
from pathlib import Path
from typing import Dict, NamedTuple

from bundle_paths import ALL_BUNDLES, ORCHESTRATED_BUNDLES, RELEASE_GRAPH_BUNDLES

BundleMetadata = NamedTuple("BundleMetadata", [("name", str), ("path", Path), ("deploy_functions", bool)])


def _parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Determines the changes between either two commits or a tag and its predecessor."
    )
    subparsers = parser.add_subparsers(dest="mode")
    # Commit mode
    commit_parser = subparsers.add_parser("commit", help="Determines the changes between two commits.")
    commit_parser.add_argument("base", type=str)
    commit_parser.add_argument("target", type=str)
    # Tag mode
    tag_parser = subparsers.add_parser("tag", help="Determines the changes between a tag and its predecessor.")
    tag_parser.add_argument("tag", type=str)

    return parser.parse_args()


def _find_predecessor_tag(tag: str) -> str:
    tag_prefix = tag.split("_")[0]
    # Find the tag's predecessor
    tags = subprocess.check_output(["git", "tag", "--sort=-creatordate"]).decode("utf-8").splitlines()
    related_tags = [t for t in tags if t.startswith(tag_prefix)]
    tag_index = related_tags.index(tag)
    if tag_index == len(related_tags) - 1:
        raise ValueError(f'Tag "{tag}" is the first "{tag_prefix}" tag in the repository and has no predecessor.')
    return related_tags[tag_index + 1]


def _get_tag_commit(tag: str) -> str:
    return subprocess.check_output(["git", "rev-list", "-n", "1", tag]).decode("utf-8").strip()


def _get_changed_files(base: str, target: str) -> list[Path]:
    return [
        Path(raw_file)
        for raw_file in subprocess.check_output(["git", "diff", "--name-only", base, target])
        .decode("utf-8")
        .splitlines()
    ]


def _determine_deploy_orchestration(
    changed_bundles: dict[str, BundleMetadata],
) -> tuple[bool, Dict[str, BundleMetadata]]:
    deploy_orchestration = False
    # Remove rdd_orchestration bundle as it gets deployed independently
    if "rdd_orchestration" in changed_bundles:
        changed_bundles.pop("rdd_orchestration", None)
        deploy_orchestration = True

    # Check if rdd_orchestration needs to be deployed
    if not deploy_orchestration and any((bundle in changed_bundles) for bundle in ORCHESTRATED_BUNDLES.keys()):
        deploy_orchestration = True

    return deploy_orchestration, changed_bundles


def _determine_changed_bundles(base: str, target: str) -> Dict[str, BundleMetadata]:
    changed_files = _get_changed_files(base, target)
    changed_bundle_files: dict[str, list[Path]] = defaultdict(list)

    legacy_release_graph_common_changed = False
    # Determine the changed bundles and their changed files
    for file in changed_files:
        # Treat legacy release graph bundles as changed if a common file is changed
        if file.is_relative_to("databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/common"):
            legacy_release_graph_common_changed = True
            continue

        # Find the bundle that contains the changed file
        for bundle, raw_path in ALL_BUNDLES.items():
            path = Path(raw_path)
            if file.is_relative_to(path):
                changed_bundle_files[bundle].append(file)
                break

    changed_bundles: dict[str, BundleMetadata] = {}
    for bundle, changed_files in changed_bundle_files.items():
        path = Path(ALL_BUNDLES[bundle])
        # Check if functions need to be deployed
        deploy_functions = any(file.is_relative_to(path / "sql_functions") for file in changed_files)
        # Set metadata
        changed_bundles[bundle] = BundleMetadata(bundle, path, deploy_functions)

    # Add legacy release graph bundles if common file is changed
    if legacy_release_graph_common_changed:
        for bundle, raw_path in RELEASE_GRAPH_BUNDLES.items():
            path = Path(raw_path)
            # Skip already added bundles
            if bundle in changed_bundles:
                continue

            changed_bundles[bundle] = BundleMetadata(bundle, path, False)

    return changed_bundles


def _write_github_output(changed_bundles: Dict[str, BundleMetadata], deploy_orchestration: bool) -> None:
    gh_matrix = {
        "include": [
            {"name": bundle, "work-dir": str(path), "deploy-functions": str(deploy_functions).lower()}
            for bundle, (_, path, deploy_functions) in changed_bundles.items()
        ]
    }

    gh_matrix_str = json.dumps(gh_matrix, indent=None)
    gh_matrix_escaped_str = shlex.quote(gh_matrix_str)
    gh_deploy_orchestration = str(deploy_orchestration).lower()
    gh_output = os.environ.get("GITHUB_OUTPUT")

    print(f"deploy_orchestration={gh_deploy_orchestration}")
    print(f"matrix={json.dumps(gh_matrix, indent=4)}")
    if gh_output is not None:
        with open(gh_output, "a") as fp:
            print(f"matrix={gh_matrix_str}", file=fp)
            print(f"matrix_escaped={gh_matrix_escaped_str}", file=fp)
            print(f"deploy_orchestration={gh_deploy_orchestration}", file=fp)


if __name__ == "__main__":
    args = _parse_args()

    if args.mode == "commit":
        base, target = args.base, args.target
        print(f"Comparing commits {base}..{target}")
    elif args.mode == "tag":
        base_tag = args.tag
        target_tag = _find_predecessor_tag(base_tag)
        base, target = _get_tag_commit(base_tag), _get_tag_commit(target_tag)
        print(f"Comparing tags {base}..{target}")
    else:
        raise ValueError("Invalid mode")

    changed_bundles = _determine_changed_bundles(base, target)
    deploy_orchestration, changed_bundles = _determine_deploy_orchestration(changed_bundles)
    _write_github_output(changed_bundles, deploy_orchestration)
