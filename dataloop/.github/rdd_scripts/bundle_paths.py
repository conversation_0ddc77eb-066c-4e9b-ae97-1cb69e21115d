"""Defines the bundle name to bundle path mappings."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
COMMON_BUNDLES = {"rdd_orchestration": "databricks/pipelines/common/rdd_orchestration/"}

RELEASE_GRAPH_BUNDLES = {
    "ada_dataproduct_e2e_ado": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/ado/",
    "ada_dataproduct_e2e_adx": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/adx/",
    "ada_dataproduct_e2e_github": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/github/",
    "ada_dataproduct_e2e_jira": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/jira/",
    "ada_dataproduct_e2e_load": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/load/",
    "ada_dataproduct_e2e_needs": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/needs/",
    "ada_dataproduct_e2e_needs_manual_ingest": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/needs_reingest/",  # noqa: E501
    "ada_dataproduct_test_reports_metrics": "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/test_reports_metrics/",  # noqa: E501
}

RELEASE_GRAPH_V2_BUNDLES = {
    "release_graph": "databricks/pipelines/ada_dataproduct_gold/release_graph/",
}

KPI_METRICS_BUNDLES = {
    "ada_kpi_metrics_gold": "databricks/pipelines/ada_dataproduct_gold/ada_kpi_metrics/",
}

SILVER_BUNDLES = {
    "needs_silver": "databricks/pipelines/ada_dataproduct_silver/needs/",
    "pace_metrics_silver": "databricks/pipelines/ada_dataproduct_silver/pace_metrics/",
    "sef_silver": "databricks/pipelines/ada_dataproduct_silver/sef/",
    "rng_metrics_silver": "databricks/pipelines/ada_dataproduct_silver/rng_metrics/",
}

ALL_BUNDLES = {
    **COMMON_BUNDLES,
    **RELEASE_GRAPH_BUNDLES,
    **KPI_METRICS_BUNDLES,
    **SILVER_BUNDLES,
    **RELEASE_GRAPH_V2_BUNDLES,
}

ORCHESTRATED_BUNDLES = {
    **SILVER_BUNDLES,
    **RELEASE_GRAPH_V2_BUNDLES,
    **{
        k: RELEASE_GRAPH_BUNDLES[k]
        for k in ["ada_dataproduct_e2e_ado", "ada_dataproduct_e2e_jira", "ada_dataproduct_e2e_github"]
    },
}
