#!/bin/env python3
"""This script finds bundles associated with closed pull requests for cleanup."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
import os
import re
import time

import requests
from bundle_paths import ALL_BUNDLES
from databricks.sdk import WorkspaceClient
from databricks.sdk.service.workspace import ObjectType

# GitHub API token and repository details
GITHUB_TOKEN = os.getenv("GITHUB_TOKEN")
GITHUB_REPO = "PACE-INT/dataloop"

# Databricks token and host details
DATABRICKS_TOKEN = os.getenv("DATABRICKS_TOKEN")
DATABRICKS_USER = os.getenv("DATABRICKS_USER")
DATABRICKS_HOST = os.getenv("DATABRICKS_HOST")


def _list_bundles() -> list[str]:
    """List all bundles in a workspace using the Databricks SDK."""

    # Assert that the necessary environment variables are not None
    assert DATABRICKS_HOST is not None, "DATABRICKS_HOST is not set"
    assert DATABRICKS_TOKEN is not None, "DATABRICKS_TOKEN is not set"

    client = WorkspaceClient(host=DATABRICKS_HOST, token=DATABRICKS_TOKEN)
    bundles_path = f"/Workspace/Users/<USER>/.bundle/dev"
    bundles = client.workspace.list(bundles_path)

    bundle_paths: list[str] = []

    for item in bundles:
        # Check if the object is a directory
        if item.object_type == ObjectType.DIRECTORY and item.path is not None:
            bundle_paths.append(item.path)

    return bundle_paths


def _extract_pr_numbers(bundle_list: list[str]) -> list[str]:
    """Extract all PR numbers from the bundles list."""
    pr_numbers = set()
    for line in bundle_list:
        # Match PR numbers in the format "pr_<digits>"
        # Example: "pr_1234_some_path" -> matches "1234"
        match = re.search(r"pr_(\d+)", line)
        if match:
            pr_numbers.add(match.group(1))
    return list(pr_numbers)


def _get_closed_prs(pr_numbers: list[str]) -> list[str]:
    """Check the state of each PR using the GitHub API and return a list of closed PRs."""
    headers = {
        "Authorization": f"token {GITHUB_TOKEN}",
        "Accept": "application/vnd.github.v3+json",
    }
    closed_prs = []

    for pr_number in pr_numbers:
        while True:  # Retry loop to handle rate limit
            response = requests.get(
                f"https://api.github.com/repos/{GITHUB_REPO}/pulls/{pr_number}",
                headers=headers,
            )
            if response.status_code == 200:
                pr_data = response.json()
                if pr_data["state"] == "closed":
                    closed_prs.append(pr_number)
                break
            elif (
                response.status_code == 403
                and "x-ratelimit-remaining" in response.headers
                and response.headers["X-RateLimit-Remaining"] == "0"
            ):
                # If rate limit is hit, calculate how long to sleep
                reset_time = int(response.headers.get("x-ratelimit-limit", time.time() + 60))
                sleep_duration = max(0, reset_time - time.time())
                print(f"Rate limit hit, sleeping for {sleep_duration} seconds...")
                time.sleep(sleep_duration)
            else:
                # Handle other failures
                print(f"Failed to fetch PR {pr_number}: {response.status_code}")
                time.sleep(1)
                break  # Exit the loop after a non-rate-limit failure

    return closed_prs


def _prepare_pr_bundles_map(bundle_list: list[str], closed_prs: list[str]) -> dict[str, list[str]]:
    """Map closed pull requests to their associated bundles."""
    pr_bundles_map: dict[str, list[str]] = {}

    for line in bundle_list:
        # Match PR numbers in the format "pr_<digits>"
        # Example: "pr_1234_some_path" -> matches "1234"
        match = re.search(r"pr_(\d+)", line)
        if not match:
            continue

        pr_number = match.group(1)

        # Check if the PR number is in the list of closed PRs
        if pr_number not in closed_prs:
            continue

        # Match the directory key after the PR number in the format "pr_<digits>_<non-whitespace characters>"
        # Example: "pr_1234_some_path" -> matches "some_path"
        directory_match = re.search(r"pr_\d+_(\S+)", line)
        if not directory_match:
            continue

        directory_key = directory_match.group(1)

        # Check if the directory key exists in ALL_PATHS
        if directory_key not in ALL_BUNDLES:
            continue

        # Append the directory path to the corresponding PR in pr_bundles_map
        if pr_number not in pr_bundles_map:
            pr_bundles_map[pr_number] = []

        pr_bundles_map[pr_number].append(ALL_BUNDLES[directory_key])

    return pr_bundles_map


def main() -> None:
    """Delete bundles associated with closed PRs."""
    if not GITHUB_TOKEN or not DATABRICKS_TOKEN or not DATABRICKS_USER or not DATABRICKS_HOST:
        print("Error: Missing required environment variables for GitHub or Databricks")
        return

    bundle_list = _list_bundles()
    if not bundle_list:
        print("No bundles found in workspace path. Exiting...")
        return

    pr_numbers = _extract_pr_numbers(bundle_list)
    if not pr_numbers:
        print("No pr bundles found. Exiting...")
        return

    closed_prs = _get_closed_prs(pr_numbers)
    if not closed_prs:
        print("No closed pull requests found. Exiting...")
        return

    print(f"Found {len(closed_prs)} closed pull requests for cleanup: {closed_prs}")

    pr_bundles_map = _prepare_pr_bundles_map(bundle_list, closed_prs)

    matrix_data = []
    for pr_number, paths in pr_bundles_map.items():
        for path in paths:
            matrix_data.append(f"{pr_number}_{path}")

    matrix_output = json.dumps(matrix_data)

    with open(os.environ["GITHUB_OUTPUT"], "a") as fh:
        print("pr_bundles_matrix<<EOF", file=fh)
        print(matrix_output, file=fh)
        print("EOF", file=fh)


if __name__ == "__main__":
    main()
