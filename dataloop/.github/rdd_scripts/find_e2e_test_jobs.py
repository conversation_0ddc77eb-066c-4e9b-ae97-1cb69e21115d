#!/bin/env python3
"""This script finds the E2E test jobs of a bundle.

Sets the following Gitub output variables:

- job_names: space-separated list of E2E test job names
- should_run_e2e: boolean flag that is 'true' if at least one E2E test job was found,
                  otherwise false.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import os
from pathlib import Path

import yaml


def _parse_bundle_directory(raw_value: str) -> Path:
    value = Path(raw_value).resolve()
    if not (value.is_dir() and value.exists() and (value / "databricks.yml").exists()):
        raise argparse.ArgumentTypeError(
            f'Given path "{value}" isn\'t a valid bundle directory. '
            'It must be an existing directory that contains a "databricks.yml" file.'
        )

    return value


def _parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Finds the E2E test jobs of a bundle.")
    parser.add_argument("bundle", type=_parse_bundle_directory, help="The relative path of the bundle.")
    return parser.parse_args()


def _set_github_outputs(jobs: list[str] | None) -> None:
    if jobs is not None:
        jobs_str = " ".join(jobs)
        should_run_e2e_str = "true"
        print(f"E2E test jobs: {jobs_str}")
    else:
        jobs_str = ""
        should_run_e2e_str = "false"
        print("No E2E test jobs found.")

    with Path(os.environ["GITHUB_OUTPUT"]).open("a") as fp:
        print(f"job_names={jobs_str}", file=fp)
        print(f"should_run_e2e={should_run_e2e_str}", file=fp)


def _find_e2e_test_jobs(bundle_dir: Path) -> list[str] | None:
    # Look for "workflows" directory
    workflows_dir = bundle_dir / "workflows"
    if not workflows_dir.exists():
        print("Bundle contains no 'workflows' directory. Exiting...")
        return None

    # Look for jobs in "*_test.yml" files
    jobs = []
    for test_file in workflows_dir.glob("*_test.yml"):
        test_config = yaml.safe_load(test_file.read_text())
        for job_name in test_config["resources"]["jobs"].keys():
            jobs.append(job_name)

    return jobs if len(jobs) >= 1 else None


if __name__ == "__main__":
    args = _parse_args()
    bundle_dir: Path = args.bundle
    jobs = _find_e2e_test_jobs(bundle_dir)
    _set_github_outputs(jobs)
