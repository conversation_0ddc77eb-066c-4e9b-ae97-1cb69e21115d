#!/bin/env python3
"""Creates compute jobs for either deploying or destroying user-defined SQL functions of a Databricks bundle."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import json
import os
from pathlib import Path
from typing import Any, Dict, List

ENV_CATALOG_SUFFIX_MAP = {"dev": "_dev", "qa": "_qa", "prod": ""}

WORKFLOW_DEFINTION = """
variables:
  instance_pool_id:
    lookup:
      instance_pool: "default_D4ads_v5_rt14.3"
resources:
  jobs:
    __deploy_sql_functions:
      tasks:
        - task_key: __deploy_sql_functions
          spark_python_task:
            python_file: ${workspace.root_path}/files/__deploy_functions.py
          job_cluster_key: sql_function_job_cluster
      job_clusters:
        - job_cluster_key: sql_function_job_cluster
          new_cluster:
            data_security_mode: SINGLE_USER
            spark_version: "15.4.x-scala2.12"
            instance_pool_id: ${var.instance_pool_id}
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            custom_tags:
              "ResourceClass": "SingleNode"
    __destroy_sql_functions:
      tasks:
        - task_key: __destroy_sql_functions
          spark_python_task:
            python_file: ${workspace.root_path}/files/__destroy_functions.py
          job_cluster_key: sql_function_job_cluster
      job_clusters:
        - job_cluster_key: sql_function_job_cluster
          new_cluster:
            data_security_mode: SINGLE_USER
            spark_version: "15.4.x-scala2.12"
            instance_pool_id: ${var.instance_pool_id}
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            custom_tags:
              "ResourceClass": "SingleNode"
"""


def _parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(
        description="Constructs compute jobs for deploying and destroying user-defined SQL functions."
    )
    parser.add_argument(
        "--env", "-e", required=True, choices=ENV_CATALOG_SUFFIX_MAP.keys(), help="Databricks environment"
    )
    return parser.parse_args()


def _construct_deploy_statements(function_meta: Dict[str, Any], env: str) -> List[str]:
    catalog = f"{function_meta['catalog']}{ENV_CATALOG_SUFFIX_MAP[env]}"
    schema = function_meta["schema"]
    name = function_meta["name"]
    full_name = f"`{catalog}`.`{schema}`.`{name}`"

    # Load the function definition
    definition = function_meta.get("definition")
    if definition is None:
        sql_file_path = Path(f"./sql_functions/{name}.sql")
        if sql_file_path.exists():
            definition = sql_file_path.read_text()
        else:
            raise ValueError(
                f'Function metadata is missing definition field and no SQL file "{sql_file_path}" could be found.'
            )
    else:
        sql_file_path = Path(f"./sql_functions/{definition}")
        definition = sql_file_path.read_text()

    if definition.count("$FUNCTION") != 1:
        raise ValueError('The definition does not contain "$FUNCTION" exactly once.')

    # Replace placeholders in the definition
    definition = (
        definition.replace("$FUNCTION", f"CREATE FUNCTION {full_name}")
        .replace("$CATALOG", catalog)
        .replace("$SCHEMA", schema)
    )

    return [
        f'spark.sql("DROP FUNCTION IF EXISTS {full_name}")',
        f'spark.sql("""{definition}""")',
    ]


def _construct_destroy_statements(function_meta: Dict[str, Any], env: str) -> List[str]:
    catalog = f"{function_meta['catalog']}{ENV_CATALOG_SUFFIX_MAP[env]}"
    schema = function_meta["schema"]
    name = function_meta["name"]
    full_name = f"`{catalog}`.`{schema}`.`{name}`"

    return [f'spark.sql("DROP FUNCTION IF EXISTS {full_name}")']


def _write_outputs(deploy_statements: List[str], destroy_statements: List[str]) -> None:
    # End early if no statements were constructed
    if (len(deploy_statements) == 0) and (len(destroy_statements) == 0):
        with open(os.getenv("GITHUB_OUTPUT"), "a") as fp:  # type: ignore[arg-type]
            print("result=false", file=fp)
        return

    # Write statements to files
    with Path("./__deploy_functions.py").open("w") as fp:
        fp.write("\n".join(deploy_statements))
    with Path("./__destroy_functions.py").open("w") as fp:
        fp.write("\n".join(destroy_statements))

    # Write workflow definition
    with Path("./workflows/__sql_functions.yml").open("w") as fp:
        fp.write(WORKFLOW_DEFINTION)

    # Write Github output
    with open(os.getenv("GITHUB_OUTPUT"), "a") as fp:  # type: ignore[arg-type]
        print("result=true", file=fp)


if __name__ == "__main__":
    # Ensure that the script is being run from a bundle root
    if not Path("./databricks.yml").exists():
        raise Exception("The script must be run from a bundle root")
    # End early if no SQL functions are defined
    if not Path("./sql_functions/meta.json").exists():
        with open(os.getenv("GITHUB_OUTPUT"), "a") as fp:  # type: ignore[arg-type]
            print("result=false", file=fp)
        exit(0)

    # Parse args
    args = _parse_args()
    env = args.env
    print(f"Constructing compute jobs for {env} environment")

    # Read metadata
    with open("./sql_functions/meta.json", "r") as fp:
        meta: List[Dict[str, Any]] = json.load(fp)

    # Construct statements
    deploy_statements = []
    destroy_statements = []
    for function_meta in meta:
        deploy_statements.extend(_construct_deploy_statements(function_meta, env))
        destroy_statements.extend(_construct_destroy_statements(function_meta, env))

    _write_outputs(deploy_statements, destroy_statements)
