name: Upload needs.json to Azure Blob Storage
description: Compress and rename needs.json, then use AzCopy to upload to Azure Blob Storage, with provided credentials.

inputs:
  tenant-id:
    description: Azure Tenant ID
    required: true
  client-id:
    description: Client ID
    required: true
  source:
    description: Name of the source for the documentation, rdd, pace, dataloop
    required: true

runs:
  using: composite
  steps:
    - name: Check if needs.json exists
      id: check_needs_json
      shell: bash
      run: |
        if [ ! -f "$NEEDS_JSON_PATH" ]; then
          echo "Error: needs.json file not found."
          exit 1
        fi
    # File format for needs.json in storage account:
    #   PR: pr_all_needs_<SHA>_<TIMESTAMP>.json.gz
    #   Main merge: main_all_needs_<SHA>_<TIMESTAMP>.json.gz
    - name: Compress needs.json and set new filename
      id: compress_and_rename
      shell: bash
      run: |
        # Get the current unix timestamp and GitHub SHA
        TIMESTAMP=$(date +%s)
        GITHUB_SHA=${{ github.sha }}

        # Check if it's a pull request or a main branch merge
        if [[ "${{ github.event_name }}" == "pull_request" ]]; then
          NEW_FILENAME="pr_all_needs_${GITHUB_SHA}_${TIMESTAMP}.json.gz"
        else
          NEW_FILENAME="main_all_needs_${GITHUB_SHA}_${TIMESTAMP}.json.gz"
        fi

        # Compress the file
        gzip -c ${{ env.NEEDS_JSON_PATH }} > needs.json.gz

        # Rename the compressed file
        mv needs.json.gz $NEW_FILENAME

        # Pass the new filename to subsequent steps
        echo "NEW_FILENAME=$NEW_FILENAME" >> $GITHUB_ENV

    # Azure login using provided tenant- and client-id.
    - name: Azure OIDC Login with SP_PACE_RDD_STORAGE
      uses: azure/login@v2
      with:
        client-id: ${{ inputs.client-id }}
        tenant-id: ${{ inputs.tenant-id }}
        allow-no-subscriptions: true

    - name: Upload needs.json to Azure Blob Storage
      shell: bash
      if: steps.check_needs_json.outcome == 'success'
      env:
        AZCOPY_AUTO_LOGIN_TYPE: AZCLI
        AZCOPY_TENANT_ID: ${{ inputs.tenant-id }}
      run: |
        # Use AzCopy to upload the compressed needs.json file to Azure Blob Storage
        /usr/bin/azcopy copy "$NEW_FILENAME" "https://dbxingestzoneprod.blob.core.windows.net/${{ inputs.source }}/needs/$NEW_FILENAME" --overwrite=true
