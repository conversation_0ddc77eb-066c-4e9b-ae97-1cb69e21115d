# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Poetry Publish
description: Publish a Poetry package

inputs:
  poetry_version:
    description: The version of Poetry to install
    default: "1.8.2"
    required: false
  python_version:
    description: The version of Python to use
    required: true
  package_path:
    description: Path to the Python Poetry package
    required: true
  artifactory_url:
    description: The URL of the repository to publish to
    required: true
  artifactory_user:
    description: The user to authenticate with the repository
    required: true
  artifactory_password:
    description: The password to authenticate with the repository
    required: true
  repository_url:
    description: The URL of the repository to publish to
    required: true

runs:
  using: composite

  steps:
    - name: Install poetry
      shell: bash
      working-directory: ${{ inputs.package_path }}
      run: pipx install poetry==${{ inputs.poetry_version }}

    - name: Set up Python ${{ inputs.python_version }}
      uses: actions/setup-python@v5
      with:
        python-version: ${{ inputs.python_version }}
        cache: "poetry"
        cache-dependency-path: ${{ inputs.package_path }}/poetry.lock

    - name: Install dependencies
      shell: bash
      working-directory: ${{ inputs.package_path }}
      env:
        POETRY_HTTP_BASIC_ADA_ARTIFACTORY_USERNAME: ${{ inputs.artifactory_user }}
        POETRY_HTTP_BASIC_ADA_ARTIFACTORY_PASSWORD: ${{ inputs.artifactory_password }}
      run: |
        poetry install --with=dev --no-interaction

    - name: Build and Publish
      shell: bash
      working-directory: ${{ inputs.package_path }}
      env:
        POETRY_REPOSITORIES_ADA_RELEASE_URL: ${{ inputs.repository_url }}
        POETRY_HTTP_BASIC_ADA_RELEASE_USERNAME: ${{ inputs.artifactory_user }}
        POETRY_HTTP_BASIC_ADA_RELEASE_PASSWORD: ${{ inputs.artifactory_password }}
      run: |
        poetry publish --build --no-interaction --repository ada-release
