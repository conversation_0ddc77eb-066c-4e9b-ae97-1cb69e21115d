# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Destroy bundle
description: Destroys bundle to desired target

inputs:
  environment:
    description: Target environment - dev, qa or prod
    required: true
  deploy-sp:
    description: Deployment service principal id
    required: true
  tenant-id:
    description: Azure tenant id
    required: true
  subscription-id:
    description: Databricks subscription id
    required: true
  project-path:
    description: Path to the asset bundle folder
    required: true
  cli-version:
    description: Databricks CLI version to be used
    default: "0.225.0"
    required: false

runs:
  using: composite

  steps:
    - uses: ./.github/actions/get_databricks_token
      with:
        deploy-sp: ${{ inputs.deploy-sp }}
        tenant-id: ${{ inputs.tenant-id }}
        subscription-id: ${{ inputs.subscription-id }}

    - uses: ./.github/actions/install_databricks_cli
      with:
        version: ${{ inputs.cli-version }}

    # - name: Create workflows for SQL functions
    #   id: create_function_workflows
    #   shell: bash
    #   working-directory: ${{ inputs.project-path }}
    #   run: python ${{ github.workspace }}/.github/rdd_scripts/construct_sql_function_jobs.py --env ${{ inputs.environment }}

    - name: Destroy bundle
      shell: bash
      working-directory: ${{ inputs.project-path }}
      env:
        DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        DATABRICKS_BUNDLE_ENV: ${{ inputs.environment }}
      run: |
        # Don't destroy SQL functions as they are not renamed
        # databricks bundle run "__destroy_sql_functions" --target ${{ inputs.environment }} || echo "No SQL functions to destroy"
        databricks bundle destroy --auto-approve --target ${{ inputs.environment }} || echo "No bundle to destroy"
