# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Install databricks CLI
description: Installs the databricks CLI of the specified version

inputs:
  version:
    description: Version of the databricks CLI to install
    default: "latest"
    required: false

runs:
  using: composite

  steps:
    - name: Install databricks CLI
      shell: bash
      run: |
        # Determine latest version
        if [ "${{ inputs.version }}" == "latest" ]; then
          version=$(curl -s https://api.github.com/repos/databricks/cli/releases/latest | jq -r '.tag_name' | sed 's/^v//')
        else
          version="${{ inputs.version }}"
        fi

        url="https://github.com/databricks/cli/releases/download/v${version}/databricks_cli_${version}_linux_amd64.zip"
        echo "Download URL: $url"

        cd "$(mktemp -d)"
        curl -L -s -o databricks.zip "$url"
        unzip -q databricks.zip
        sudo cp ./databricks /usr/local/bin
        sudo chmod 755 /usr/local/bin/databricks
        echo "Installed databricks CLI v$version"
