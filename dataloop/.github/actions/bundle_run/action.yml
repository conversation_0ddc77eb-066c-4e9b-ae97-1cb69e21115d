# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Run Bundle
description: Runs a specified job in Databricks bundle

inputs:
  environment:
    description: Target environment - dev, qa or prod
    required: true
  deploy-sp:
    description: Deployment service principal id
    required: true
  tenant-id:
    description: Azure tenant id
    required: true
  subscription-id:
    description: Databricks subscription id
    required: true
  project-path:
    description: Path to the asset bundle folder
    required: true
  job-names:
    description: Job names
    required: true
  params:
    description: Arbitrary parameters to pass to the databricks run
    required: false
  cli-version:
    description: Databricks CLI version to be used
    default: "0.225.0"
    required: false

runs:
  using: composite
  steps:
    - uses: ./.github/actions/get_databricks_token
      with:
        deploy-sp: ${{ inputs.deploy-sp }}
        tenant-id: ${{ inputs.tenant-id }}
        subscription-id: ${{ inputs.subscription-id }}

    - uses: ./.github/actions/install_databricks_cli
      with:
        version: ${{ inputs.cli-version }}

    - name: Run Databricks Job
      shell: bash
      working-directory: ${{ inputs.project-path }}
      env:
        DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        DATABRICKS_BUNDLE_ENV: ${{ inputs.environment }}
      run: |
        JOB_NAMES="${{ inputs.job-names }}"
        echo "Running bundle with job names: $JOB_NAMES"
        IFS=' ' read -r -a job_array <<< "$JOB_NAMES"
        for JOB_NAME in "${job_array[@]}"; do
          echo "Running bundle for job: $JOB_NAME"

          # Check if there are any additional params
          if [ ! -z "${{ inputs.params }}" ]; then
            echo "Running command: databricks bundle run \"$JOB_NAME\" --target ${{ inputs.environment }} --params ${{ inputs.params }}"
            databricks bundle run "$JOB_NAME" --target ${{ inputs.environment }} --params ${{ inputs.params }}
          else
            echo "Running command: databricks bundle run \"$JOB_NAME\" --target ${{ inputs.environment }}"
            databricks bundle run "$JOB_NAME" --target ${{ inputs.environment }}
          fi
        done
