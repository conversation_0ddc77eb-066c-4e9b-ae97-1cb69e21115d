# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Validate Bundle
description: Validate bundle to desired target

inputs:
  environment:
    description: Target environment - dev, qa or prod
    required: true
  deploy-sp:
    description: Deployment service principal id
    required: true
  tenant-id:
    description: Azure tenant id
    required: true
  subscription-id:
    description: Databricks subscription id
    required: true
  project-path:
    description: Path to the asset bundle folder
    required: true
  cli-version:
    description: Databricks CLI version to be used
    default: "0.225.0"
    required: false


runs:
  using: composite

  steps:
    - name: Print Bundle Path
      shell: bash
      run: |
        echo "Bundle path: ${{ inputs.project-path }}"

    - uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: Install Dependencies
      shell: bash
      working-directory: .github/scripts/prevalidate_bundle
      run: |
        pip install -r requirements.txt

    - name: Prevalidate Bundle
      shell: bash
      if: ${{ false }}
      working-directory: .github/scripts/prevalidate_bundle
      run: |
        PYTHONPATH=src python -m prevalidate_bundle.cli "../../../${{ inputs.project-path }}"

    - uses: ./.github/actions/get_databricks_token
      with:
        deploy-sp: ${{ inputs.deploy-sp }}
        tenant-id: ${{ inputs.tenant-id }}
        subscription-id: ${{ inputs.subscription-id }}

    - uses: ./.github/actions/install_databricks_cli
      with:
        version: ${{ inputs.cli-version }}

    - name: Validate updated bundle
      shell: bash
      working-directory: ${{ inputs.project-path }}
      env:
        DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        DATABRICKS_BUNDLE_ENV: ${{ inputs.environment }}
      run: databricks bundle validate --target ${{ inputs.environment }}
