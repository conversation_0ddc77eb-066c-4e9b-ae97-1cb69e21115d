# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Get databricks access token
description: Get the databricks access token from Azure

inputs:
  deploy-sp:
    description: Deployment service principal id
    required: true
  tenant-id:
    description: Azure tenant id
    required: true
  subscription-id:
    description: Databricks subscription id
    required: true

outputs:
  token:
    description: Databricks access token
    value: ${{ steps.get-token.outputs.token }}

runs:
  using: composite

  steps:
    - name: Azure Login
      uses: azure/login@v1
      with:
        client-id: ${{ inputs.deploy-sp }}
        tenant-id: ${{ inputs.tenant-id }}
        subscription-id: ${{ inputs.subscription-id }}

    - name: Get Access Token
      shell: bash
      id: get-token
      run: |
        databricks_token=$(az account get-access-token --resource 2ff814a6-3304-4ab8-85cb-cd0e6f879c1d --query accessToken -o tsv)
        echo "::add-mask::$databricks_token"
        echo "token=${databricks_token}" >> $GITHUB_OUTPUT
