# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Find Eligible Bundles for next stage of CI"



inputs:
  cli_options:
    description: "Options to pass to the CLI"
    required: true
  python-version:
    description: "Python version to use"
    required: false
    default: '3.11'

outputs:
  matrix:
    description: "Matrix of bundles found"
    value: ${{ steps.set-matrix.outputs.matrix }}

runs:
  using: composite

  steps:
    - uses: actions/setup-python@v5
      with:
        python-version: '3.11'

    - name: "Install Dependencies"
      shell: bash
      working-directory: .github/scripts/dab_find
      run: |
        pip install -r requirements.txt

    - name: "Find Bundles"
      id: set-matrix
      shell: bash
      working-directory: .github/scripts/dab_find
      run: |
        PYTHONPATH=src python -m dab_find.cli ${{ inputs.cli_options }}
