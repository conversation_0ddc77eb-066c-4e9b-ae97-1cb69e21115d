# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Deploy bundle
description: Deploys bundle to desired target

inputs:
  environment:
    description: Target environment - dev, qa or prod
    required: true
  deploy-sp:
    description: Deployment service principal id
    required: true
  tenant-id:
    description: Azure tenant id
    required: true
  subscription-id:
    description: Databricks subscription id
    required: true
  project-path:
    description: Path to the asset bundle folder
    required: true
  include-functions:
    description: Whether to also deploy SQL functions
    default: "false"
    required: false
  cli-version:
    description: Databricks CLI version to be used
    default: "0.225.0"
    required: false

runs:
  using: composite

  steps:
    - uses: ./.github/actions/get_databricks_token
      with:
        deploy-sp: ${{ inputs.deploy-sp }}
        tenant-id: ${{ inputs.tenant-id }}
        subscription-id: ${{ inputs.subscription-id }}

    - name: "Install Python Tools"
      shell: bash
      run: |
        pip install poetry
        curl -LsSf https://astral.sh/uv/install.sh | sh

    - uses: ./.github/actions/install_databricks_cli
      with:
        version: ${{ inputs.cli-version }}

    - name: Create workflows for SQL functions
      id: create_function_workflows
      if: ${{ inputs.include-functions == 'true' }}
      shell: bash
      working-directory: ${{ inputs.project-path }}
      run: python ${{ github.workspace }}/.github/rdd_scripts/construct_sql_function_jobs.py --env ${{ inputs.environment }}

    - name: Deploy updated bundle
      shell: bash
      working-directory: ${{ inputs.project-path }}
      env:
        DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        DATABRICKS_BUNDLE_ENV: ${{ inputs.environment }}
      run: databricks bundle deploy --target ${{ inputs.environment }}

    - name: Run deploy workflow for SQL functions
      id: run_function_deploy
      if: ${{ inputs.include-functions == 'true' && steps.create_function_workflows.outputs.result == 'true' }}
      shell: bash
      working-directory: ${{ inputs.project-path }}
      env:
        DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        DATABRICKS_BUNDLE_ENV: ${{ inputs.environment }}
      run: databricks bundle run "__deploy_sql_functions" --target ${{ inputs.environment }}
