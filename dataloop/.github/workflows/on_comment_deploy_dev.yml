name: "Deploy Bundle to DEV on Checkbox Comment"

on:
  issue_comment:
    types: [edited]

permissions:
  contents: read
  pull-requests: write
  id-token: write
  issues: write
  actions: read
  statuses: write

jobs:
  get-comment-heading:
    # This job only runs for pull request comments (not issue comments)
    if: ${{ github.event.issue.pull_request }}
    outputs:
      heading: ${{ steps.read-comment-heading.outputs.heading }}
    runs-on: ubuntu-latest
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
        with:
          ref: refs/pull/${{ github.event.issue.number }}/head

      - name: "Read Comment Heading"
        id: read-comment-heading
        working-directory: .github/shared
        run: |
          echo "heading=$(jq -r '.DEPLOY_DEV_BUNDLE_COMMENT_HEADING' constants.json)" >> $GITHUB_OUTPUT

  pr-comment-edited:
    needs: get-comment-heading
    name: "Extract Bundle Path from PR Comment"
    # This job only runs for pull request comments that start with the heading defined in the constants.json file
    if: ${{ github.event.issue.pull_request && startsWith(github.event.comment.body, needs.get-comment-heading.outputs.heading) }}
    runs-on: ubuntu-latest
    outputs:
      bundle: ${{ steps.output-bundle-name.outputs.bundle }}
    steps:
      - name: 'Analyze PR Comment Editing'
        id: set-bundle-path
        uses: actions/github-script@v7
        with:
          script: |
            const originalComment = context.payload.changes.body.from;
            const newComment = context.payload.comment.body;

            console.log(`Original comment: ${originalComment}`);
            console.log(`New comment: ${newComment}`);

            const originalCommentCheckboxLine = originalComment.split('\n')[1];
            const newCommentCheckboxLine = newComment.split('\n')[1];

            const wasUnChecked = originalCommentCheckboxLine.startsWith('- [ ]');
            const isNowChecked = newCommentCheckboxLine.startsWith('- [x]');

            console.log(`Was unchecked: ${wasUnChecked}`);
            console.log(`Is now checked: ${isNowChecked}`);

            if (wasUnChecked && isNowChecked) {
              const matchResult = originalCommentCheckboxLine.match(/`(?<bundleName>.*?)`/);
              return matchResult.groups.bundleName;
            }
            else {
              console.log('Bundle was not checked!');
              return '';
            }
      - name: "Output Bundle Path"
        id: output-bundle-name
        run: |
          echo "bundle=${{ steps.set-bundle-path.outputs.result }}" >> $GITHUB_OUTPUT

  dev-bundle-deploy:
    needs: pr-comment-edited
    if: ${{ needs.pr-comment-edited.outputs.bundle != '' }}
    name: "Deploy Bundle ${{ needs.pr-comment-edited.outputs.bundle }} - DEV"
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
      # this is used to set the status context for this job
      # in both calls to the create status call, if the context is the same, the status will be updated
      STATUS_CONTEXT: "Deploy Bundle / DEV / ${{ needs.pr-comment-edited.outputs.bundle }}"
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-dev
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
        with:
          ref: refs/pull/${{ github.event.issue.number }}/head
          fetch-depth: 0

      - name: "Get PR SHA"
        run: |
          echo "PR_SHA=$(git rev-parse HEAD)" >> $GITHUB_ENV

      - name: "Get Job URL"
        id: job-url
        env:
          GH_TOKEN: ${{ secrets.GITHUB_TOKEN }}
        run: |
          # The name that gets selected needs to match the name of the job
          JOB_ID=$(gh api /repos/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}/jobs | jq -r '.jobs[] | select(.name=="Deploy Bundle ${{ needs.pr-comment-edited.outputs.bundle }} - DEV") | .id')
          echo "JOB_URL=${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}/job/$JOB_ID" >> $GITHUB_ENV
          echo "Job URL: ${GITHUB_SERVER_URL}/${GITHUB_REPOSITORY}/actions/runs/${GITHUB_RUN_ID}/job/$JOB_ID"

      - name: "Set Pending Status"
        uses: actions/github-script@v7
        with:
          script: |
            await github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: process.env.PR_SHA,
              state: 'pending',
              target_url: process.env.JOB_URL,
              description: 'Deploying bundle to DEV...',
              context: process.env.STATUS_CONTEXT
            });

      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: "Deploy bundle to dev"
        id: deploy-bundle
        uses: ./.github/actions/bundle_deploy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ needs.pr-comment-edited.outputs.bundle }}

      - name: "Update Deployment Status"
        if: always()
        uses: actions/github-script@v7
        with:
          script: |
            const success = '${{ steps.deploy-bundle.outcome }}' === 'success';
            const description = success
              ? 'Successfully deployed bundle to DEV'
              : 'Failed to deploy bundle to DEV';

            await github.rest.repos.createCommitStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              sha: process.env.PR_SHA,
              state: success ? 'success' : 'failure',
              target_url: process.env.JOB_URL,
              description: description,
              context: process.env.STATUS_CONTEXT
            });
