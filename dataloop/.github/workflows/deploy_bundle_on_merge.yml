name: "Deploy Bundle on Merge"

on:
  push:
    branches:
      - "main"

jobs:
  deploy-bundle:
    name: "Deploy Bundle - ${{ matrix.environment }}"
    strategy:
      fail-fast: false
      matrix:
        environment:
          - dev
          - qa
          - prod

    uses: ./.github/workflows/deploy_bundle_merge_reusable.yml
    with:
      environment: ${{ matrix.environment }}
    secrets: inherit
