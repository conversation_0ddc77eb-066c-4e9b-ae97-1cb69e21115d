# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Deploy Azure Costs bundle - PROD"

on:
  release:
    types: [published]
  workflow_dispatch:

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read  # required for checkout


jobs:
  prod-azure_costs-bundle-deploy:
    if: ${{ startsWith(github.event.release.tag_name, 'azure_costs') }}
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
      DATABRICKS_RUN_SP: ${{ vars.DATABRICKS_DMV_SP }}
    runs-on: [self-hosted, linux, build]
    environment:
      name: databricks-prod
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Deploy bronze to prod
        uses: ./.github/actions/bundle_deploy
        with:
          environment: prod
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/ada_dataproduct_bronze/azure_costs
