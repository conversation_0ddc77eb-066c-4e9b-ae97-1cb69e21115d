name: "RDD Bundle Cleanup - Manual"

on:
  workflow_dispatch:
    inputs:
      pr_number:
        description: "Pull request number (optional)"
        required: false
      project_path:
        description: "Bundle path to the directory containing databricks.yml"
        required: true

permissions:
  id-token: write
  contents: read
  pull-requests: read

jobs:
  destroy-pr-bundle:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-dev
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip"

      # Rename step, only runs if pr_number is provided
      - name: Rename PR bundle
        if: inputs.pr_number != ''
        run: |
          pip install PyYAML~=6.0.2
          python .github/rdd_scripts/rename_bundles_pr.py ${{ inputs.project_path }} --pr_number ${{ inputs.pr_number }}

      - name: Destroy bundle
        uses: ./.github/actions/bundle_destroy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ inputs.project_path }}
