name: Gatekeeper

on:
  pull_request:
    branches:
    - main
    types:
    - opened
    - synchronize
    - reopened
  push:
    branches:
    - main

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read  # required for checkout
  pull-requests: write # to enable pull-request decoration

# Ensure that only one instance of this workflow is running for a merge branch
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: ${{ github.event_name == 'pull_request' }}

env:
  BASE_IMAGE_NAME: "pacedatabricksdataloop.azurecr.io/dataloop-gatekeeper"
  SUPPORTED_PYTHON_VERSIONS: "3.10 3.11 3.12"
  DEFAULT_PYTHON_VERSION: "3.11"

defaults:
  run:
    shell: bash

jobs:
  prepare:
    name: Prepare
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: docker-push
    outputs:
      # General outputs
      image: ${{ steps.general-outputs.outputs.image }}
      supported-python-versions: ${{ steps.general-outputs.outputs.supported-python-versions }}
      default-python-version: ${{ steps.general-outputs.outputs.default-python-version }}
      # Job flags
      deploy-gatekeeper-image: ${{ steps.select-jobs.outputs.deploy-gatekeeper-image }}
      publish-documentation: ${{ steps.select-jobs.outputs.publish-documentation }}
      static-analysis: ${{ steps.select-jobs.outputs.static-analysis }}
      unit-tests: ${{ steps.select-jobs.outputs.unit-tests }}
      deploy-databricks-libraries: ${{ steps.select-jobs.outputs.deploy-databricks-libraries }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # Only head and previous commit are needed

      - name: Install Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Login to ACR
        uses: docker/login-action@v3
        with:
          registry: pacedatabricksdataloop.azurecr.io
          username: ${{ vars.AZURE_CLIENT_ID }}
          password: ${{ secrets.AZURE_CLIENT_SECRET }}

      - name: Verify uv.lock
        run: |
          pip install uv

          # Check if uv.lock is up-to-date
          uv lock --check

      - name: Set general outputs
        id: general-outputs
        run: |
          IMAGE="${{ env.BASE_IMAGE_NAME }}:$(./.github/scripts/gatekeeper/calc_deps_hash.py)"
          echo "Gatekeeper image: $IMAGE"
          echo "image=$IMAGE" >> $GITHUB_OUTPUT

          echo "Supported Python versions: ${{ env.SUPPORTED_PYTHON_VERSIONS }}"
          echo "supported-python-versions=${{ env.SUPPORTED_PYTHON_VERSIONS }}" >> $GITHUB_OUTPUT

          echo "Default Python version: ${{ env.DEFAULT_PYTHON_VERSION }}"
          echo "default-python-version=${{ env.DEFAULT_PYTHON_VERSION }}" >> $GITHUB_OUTPUT

      - name: Select jobs
        id: select-jobs
        run: |
          ./.github/scripts/gatekeeper/select_jobs.py --base HEAD^ --target HEAD \
            --image-name "${{ steps.general-outputs.outputs.image }}"

  deploy-gatekeeper-image:
    name: Deploy Gatekeeper image
    needs: prepare
    if: ${{ needs.prepare.outputs.deploy-gatekeeper-image == 'true' }}
    uses: ./.github/workflows/deploy_gatekeeper_image.yml
    with:
      image-name: ${{ needs.prepare.outputs.image }}
      supported-python-versions: ${{ needs.prepare.outputs.supported-python-versions }}
      default-python-version: ${{ needs.prepare.outputs.default-python-version }}
    secrets: inherit

  publish-documentation:
    name: Publish dataloop documentation
    needs: [prepare, deploy-gatekeeper-image]
    if: ${{ !failure() && !cancelled() && needs.prepare.outputs.publish-documentation == 'true' }}
    uses: ./.github/workflows/publish_documentation.yml
    with:
      image: ${{ needs.prepare.outputs.image }}
    secrets: inherit

  # static-analysis:
  #   name: Static analysis
  #   needs: [prepare, deploy-gatekeeper-image]
  #   if: ${{ !failure() && !cancelled() && needs.prepare.outputs.static-analysis == 'true' }}
  #   uses: ./.github/workflows/static_analysis.yml
  #   with:
  #     image: ${{ needs.prepare.outputs.image }}
  #   secrets: inherit

  # unit-tests:
  #   name: Unit tests
  #   needs: [prepare, deploy-gatekeeper-image]
  #   if: ${{ !failure() && !cancelled() && needs.prepare.outputs.unit-tests == 'true' }}
  #   uses: ./.github/workflows/unit_tests.yml
  #   with:
  #     image: ${{ needs.prepare.outputs.image }}
  #   secrets: inherit

  deploy-databricks-libraries:
    name: Deploy Databricks libraries to Artifactory
    needs: [prepare, deploy-gatekeeper-image]
    if: ${{ !failure() && !cancelled() && needs.prepare.outputs.deploy-databricks-libraries == 'true' }}
    uses: ./.github/workflows/deploy_databricks_libraries.yml
    with:
      image: ${{ needs.prepare.outputs.image }}
    secrets: inherit

  ensure-success:
    name: Ensure success
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    needs:
      - prepare
      - deploy-gatekeeper-image
      - publish-documentation
      # - static-analysis
      # - unit-tests
      - deploy-databricks-libraries
    if: ${{ always() }}
    steps:
      - name: Ensure checks were successful or skipped
        run: |
          # Convert the needs context to JSON
          NEEDS_JSON=$(cat << EOF
          ${{ toJson(needs) }}
          EOF
          )

          # Iterate over all checks and check if they were successful or skipped
          echo "$NEEDS_JSON" | jq -c 'to_entries[]' | while IFS= read -r entry; do
              step=$(echo "$entry" | jq -r '.key')
              result=$(echo "$entry" | jq -r '.value.result')

              if [[ "$result" != "success" && "$result" != "skipped" ]]; then
                  echo "Job $step failed with status: $result"
                  exit 1
              else
                  echo "Job $step succeeded with status: $result"
              fi
          done
