name: Build and upload documentation

on:
  push:
    branches: [main]
    paths:
      - mdm-viper-lib/docs/**
      - .github/workflows/build_publish_docs_yml.yaml
  pull_request:
    branches: [main]
    paths:
      - mdm-viper-lib/docs/**
      - .github/workflows/build_publish_docs_yml.yaml

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

env:
  artifactory-repository: shared-pypi-prod
  artifactory-repository-dev: shared-pypi-dev

jobs:
  build-and-upload-docs:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}

    steps:
      - name: Workflow Init
        uses: pace-int/pace-actions/workflow-init@main

      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Install Python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: "3.10"

      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository }}/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository-dev }}/simple" >> $GITHUB_ENV

      - name: Install dependencies
        working-directory: ./mdm-viper-lib
        run: |
          pip install -e .[docs] --no-cache-dir

      - name: Generate Documentation
        working-directory: ./mdm-viper-lib
        run: |
          cd docs
          sphinx-build -b html source build

      - name: Upload documentation
        uses: pace-int/pace-actions/publish-docs@main
        with:
          source-path: "mdm-viper-lib/docs/build"
          storage-account-name: ${{ secrets.PACE_DOCS_STORAGE_ACCOUNT_NAME }}
          storage-account-access-key: ${{ secrets.PACE_DOCS_ACCESS_KEY }}
