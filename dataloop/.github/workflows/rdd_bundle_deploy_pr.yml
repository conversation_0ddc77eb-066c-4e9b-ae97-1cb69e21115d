# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Deploy RDD bundle - PR"

on:
  pull_request:
    branches:
      - main
    paths:
      - "databricks/pipelines/common/rdd_orchestration/**"
      - "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/**"
      - "databricks/pipelines/ada_dataproduct_silver/pace_metrics/**"
      - "databricks/pipelines/ada_dataproduct_silver/sef/**"
      - "databricks/pipelines/ada_dataproduct_silver/needs/**"
      - "databricks/pipelines/ada_dataproduct_silver/rng_metrics/**"
      - "databricks/pipelines/ada_dataproduct_gold/ada_kpi_metrics/**"
      - "databricks/pipelines/ada_dataproduct_gold/release_graph/**"
  pull_request_review:
    types:
      - submitted

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read # required for checkout
  pull-requests: read # required for accessing pull request data

concurrency:
  group: rdd-pr-${{ github.event.number }}
  cancel-in-progress: true

env:
  PIP_INDEX_URL: "https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple"
  PIP_EXTRA_INDEX_URL: "https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple"

jobs:
  determine-changes:
    name: "Determine changes"
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      deploy_orchestration: ${{ steps.set-matrix.outputs.deploy_orchestration }}
      run_e2e_tests: ${{ steps.evaluate-pr-state.outputs.run_e2e_tests }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: ${{ github.event.pull_request.commits }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Determine changed bundles
        id: set-matrix
        working-directory: .github/rdd_scripts
        run: |
          git fetch --no-tags origin main
          python determine_changes.py commit "origin/main" "${{ github.sha }}"

      - name: Evaluate PR State for E2E tests run
        id: evaluate-pr-state
        run: |
          # End early if event that triggered workflow isn't pull_request_review
          if [ "${{ github.event_name }}" != "pull_request_review" ]; then
            echo "Run wasn't triggered by a PR review. Skipping e2e tests."
            echo "run_e2e_tests=false" >> $GITHUB_OUTPUT
          fi

          REVIEWS=$(curl -s -H "Authorization: token ${{ secrets.GITHUB_TOKEN }}" \
                -H "Accept: application/vnd.github.v3+json" \
                https://api.github.com/repos/${{ github.repository_owner }}/${{ github.event.pull_request.head.repo.name }}/pulls/${{ github.event.pull_request.number }}/reviews)

          APPROVAL_COUNT=$(echo "$REVIEWS" | jq '[.[] | select(.state == "APPROVED")] | length')
          CHANGE_REQUESTED=$(echo "$REVIEWS" | jq '[.[] | select(.state == "CHANGES_REQUESTED")] | length')

          echo "Approval count: $APPROVAL_COUNT"
          echo "Change requested count: $CHANGE_REQUESTED"

          if [ "$APPROVAL_COUNT" -eq 1 ] && [ "$CHANGE_REQUESTED" -eq 0 ]; then
            run_e2e_tests=true
          else
            run_e2e_tests=false
          fi
          echo "Run e2e2 tests: $run_e2e_tests"
          echo "run_e2e_tests=$run_e2e_tests" >> $GITHUB_OUTPUT

      - name: Rename bundles for PR
        id: rename-bundles
        run: |
          pip install PyYAML~=6.0.2
          # Space separated list of changed bundle directories
          changed_bundles="$(echo ${{ steps.set-matrix.outputs.matrix_escaped }} | jq -r '.include[]."work-dir" | @sh' | tr -d \'\" | tr '\n' ' ')"
          # End early if there are no changed bundles
          if [ -z "$changed_bundles" ]; then
            echo "No changed bundles found. Skipping renaming."
            exit 0
          fi
          python .github/rdd_scripts/rename_bundles_pr.py --pr_number ${{ github.event.pull_request.number }} $changed_bundles

      - name: Cache repository
        id: cache-repository
        uses: actions/cache/save@v4
        with:
          key: repository-pr-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: ${{ github.workspace }}

  rdd-bundle-deploy-pr:
    name: Deploy RDD bundle ${{ matrix.name }} - PR
    needs: determine-changes
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-dev
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.determine-changes.outputs.matrix) }}
    steps:
      - name: Clear pre-checked out repository
        run: rm -rf  ${{ github.workspace }} && mkdir ${{ github.workspace }} && ls -la

      - name: Restore cached repository
        id: restore-cached-repository
        uses: actions/cache/restore@v4
        with:
          key: repository-pr-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: ${{ github.workspace }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Configure pip index
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV

      - name: Install rddlib
        working-directory: databricks/libraries/
        run: pip install -e ./dbxlib[spark] -e ./rddlib

      - name: Install dependencies to run unit tests
        working-directory: ${{ matrix.work-dir }}
        run: |
          # Install packages used to execute tests
          pip install -r ${{ github.workspace }}/databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/test-requirements.txt

          # requirements.txt is optional
          if [ -f "requirements.txt" ]; then
            pip install -r requirements.txt
          fi

      - name: Run unit tests
        working-directory: ${{ matrix.work-dir }}
        run: |
          if [ -d "tests" ]; then
            python3 -m pytest --cov-report term-missing --cov=. tests/
          else
            echo "Skipping unit tests as the test directory does not exist."
          fi

      - name: Deploy PR bundle to dev
        uses: ./.github/actions/bundle_deploy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}
          include-functions: ${{ matrix.deploy-functions}}

      - name: Find E2E test jobs
        id: find_e2e_test_jobs
        shell: bash
        continue-on-error: false
        if: ${{ needs.determine-changes.outputs.run_e2e_tests == 'true' }}
        run: |
          pip install PyYAML~=6.0.2
          python .github/rdd_scripts/find_e2e_test_jobs.py ${{ matrix.work-dir }}

      - name: Run E2E tests bundle
        if: ${{ steps.find_e2e_test_jobs.outputs.should_run_e2e == 'true' && needs.determine-changes.outputs.run_e2e_tests == 'true' }}
        uses: ./.github/actions/bundle_run
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}
          job-names: ${{ steps.find_e2e_test_jobs.outputs.job_names }}
          pr-id: ${{ github.event.pull_request.number }}

  rdd-orchestration-bundle-deploy-pr:
    name: Deploy RDD orchestration bundle - PR
    needs: [rdd-bundle-deploy-pr, determine-changes]
    if: ${{ needs.rdd-bundle-deploy-pr.result == 'success' && needs.determine-changes.outputs.deploy_orchestration == 'true' }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-dev

    steps:
      - name: Clear pre-checked out repository
        run: rm -rf ${{ github.workspace }} && mkdir ${{ github.workspace }} && ls -la

      - name: Restore cached repository
        id: restore-cached-repository
        uses: actions/cache/restore@v4
        with:
          key: repository-pr-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: ${{ github.workspace }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Install databricks CLI
        uses: ./.github/actions/install_databricks_cli

      - name: Install Python Dependencies
        working-directory: ./databricks/pipelines/common/rdd_orchestration
        run: pip install -r requirements.txt

      - name: Get databricks token
        id: get_databricks_token
        uses: ./.github/actions/get_databricks_token
        with:
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}

      - name: Generate nightly workflow
        working-directory: ./databricks/pipelines/common/rdd_orchestration
        env:
          DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        run: |
          python generate_workflow.py --target dev \
            --diagram diagrams/full_orchestration.drawio.svg \
            --template templates/orchestration_nightly.yml.jinja \
            --workflow "RDD Orchestration - Nightly" \
            --pr_number ${{ github.event.pull_request.number }}

      - name: Rename bundle for PR
        id: rename-bundle
        run: |
          pip install PyYAML~=6.0.2
          python .github/rdd_scripts/rename_bundles_pr.py \
            --pr_number ${{ github.event.pull_request.number }} \
            --skip_jobs \
            "./databricks/pipelines/common/rdd_orchestration"

      - name: Deploy PR bundle to dev
        uses: ./.github/actions/bundle_deploy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/common/rdd_orchestration
