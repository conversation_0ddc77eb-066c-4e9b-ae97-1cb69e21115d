name: "RDD Bundle Cleanup - Nightly"

on:
  schedule:
    - cron: "0 1 * * *" # Runs every day at 1 AM UTC

permissions:
  id-token: write
  contents: read
  pull-requests: read

jobs:
  find-bundles-for-closed-prs:
    runs-on: ${{ from<PERSON>son(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
    environment:
      name: databricks-dev
    outputs:
      pr_bundles_matrix: ${{ steps.prepare_bundles.outputs.pr_bundles_matrix }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip"

      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}

      - uses: ./.github/actions/get_databricks_token
        id: get_token
        with:
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}

      - name: Install Python Dependencies
        run: |
          pip install requests
          pip install databricks-sdk

      - name: Run Python Script to prepare PR Bundles List
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          DATABRICKS_HOST: https://adb-505904006080631.11.azuredatabricks.net/
          DATABRICKS_USER: ${{ vars.DATABRICKS_RDD_SP }}
          DATABRICKS_TOKEN: ${{ steps.get_token.outputs.token }}
        id: prepare_bundles
        run: python .github/rdd_scripts/cleanup_bundles_pr.py

      - name: Debug Output
        run: |
          echo "Output from previous step:"
          cat $GITHUB_OUTPUT
          echo "pr_bundles_matrix: ${{ steps.prepare_bundles.outputs.pr_bundles_matrix }}"

  destroy-pr-bundles:
    needs: find-bundles-for-closed-prs
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
    environment:
      name: databricks-dev
    strategy:
      matrix:
        bundle: ${{ fromJson(needs.find-bundles-for-closed-prs.outputs.pr_bundles_matrix) }}
      fail-fast: false
    steps:
      - name: Set PR variables
        id: set_pr_vars
        run: |
          PR_NUMBER=$(echo ${{ matrix.bundle }} | cut -d'_' -f1)
          PROJECT_PATH=$(echo ${{ matrix.bundle }} | cut -d'_' -f2-)
          echo "pr_number=${PR_NUMBER}" >> $GITHUB_OUTPUT
          echo "project_path=${PROJECT_PATH}" >> $GITHUB_OUTPUT
          echo "Processing PR $PR_NUMBER for path $PROJECT_PATH"

      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Azure Login
        uses: azure/login@v1
        with:
          client-id: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip"

      - name: Rename PR bundle
        id: rename_bundle
        run: |
          pip install PyYAML~=6.0.2
          python .github/rdd_scripts/rename_bundles_pr.py \
              ${{ steps.set_pr_vars.outputs.project_path }} --pr_number ${{ steps.set_pr_vars.outputs.pr_number }}

      - name: Destroy PR bundle in dev
        uses: ./.github/actions/bundle_destroy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ steps.set_pr_vars.outputs.project_path }}
