# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

name: "[MDM] Deploy Delta Sharing Views"

on:
  workflow_dispatch:
    inputs:
      environments:
        description: 'Environments to deploy in format ["dev", "qa", "prod"]'
        required: true
        default: '["dev", "qa", "prod"]'
  push:
    branches:
      - main
    paths:
      - databricks/delta-sharing/**
      - databricks/pipelines/ada_dataproduct_bronze/mdm/delta_sharing_views/**
      - .github/workflows/mdm_deploy_delta_sharing_views.yaml
  pull_request:
    branches:
      - main
    paths:
      - databricks/delta-sharing/**
      - databricks/pipelines/ada_dataproduct_bronze/mdm/delta_sharing_views/**
      - .github/workflows/mdm_deploy_delta_sharing_views.yaml

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read  # required for checkout

jobs:
  deploy_and_run_asset_bundle:
    name: "Deploy Asset Bundle to create views for Delta Sharing"
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment: databricks-${{ matrix.environment }}
    strategy:
      matrix:
        environment: ${{ fromJSON(github.event.inputs.environments || '["dev", "qa", "prod"]') }}
    env:
      GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      poetry_version: 1.8.5
      python-version: 3.11
    permissions:
      contents: read
      pull-requests: write
      id-token: write
    steps:
      - name: Checkout
        uses: actions/checkout@v3
      - name: Set up Python ${{ env.python-version }}
        uses: actions/setup-python@v5
        with:
          python-version: ${{ env.python-version }}
      - name: Copy environment folder to asset bundle
        run: |
          cp -r databricks/delta-sharing/configuration/${{ matrix.environment }} databricks/pipelines/ada_dataproduct_bronze/mdm/delta_sharing_views
      - name: Install Databricks CLI
        uses: ./.github/actions/install_databricks_cli
      - name: Install poetry
        run: pipx install poetry==${{ env.poetry_version }}
      - name: Validate asset bundle
        uses: ./.github/actions/bundle_validate
        with:
          environment: ${{ matrix.environment }}
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: "databricks/pipelines/ada_dataproduct_bronze/mdm/delta_sharing_views"
      - name: Deploy asset bundle
        if: github.ref == 'refs/heads/main'
        uses: ./.github/actions/bundle_deploy
        with:
          environment: ${{ matrix.environment }}
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: "databricks/pipelines/ada_dataproduct_bronze/mdm/delta_sharing_views"
      - name: Run Databricks Jobs
        if: github.ref == 'refs/heads/main'
        working-directory: "databricks/pipelines/ada_dataproduct_bronze/mdm/delta_sharing_views"
        shell: bash
        run: |
          for file in ${{ matrix.environment }}/*.yml; do
            yml_file=$(basename $file)
            echo "Running job for $yml_file"
            databricks bundle run -t ${{ matrix.environment }} --python-params --config-file-path=../${{ matrix.environment }}/$yml_file delta_sharing_views_job --no-wait --refresh-all
          done
