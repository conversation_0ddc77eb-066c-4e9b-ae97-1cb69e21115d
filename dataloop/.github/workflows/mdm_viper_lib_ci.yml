name: mdm-viper-lib CI
on:
  push:
    # "**" taken into account any "/" in the branch name and matches it to a character.
    branches:
      - "**"
    tags:
      - "v*"
    paths:
      - "mdm-viper-lib/**"
      - ".github/workflows/mdm_viper_lib_ci.yml"

  workflow_dispatch:

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

env:
  artifactory-repository: shared-pypi-prod
  artifactory-repository-dev: shared-pypi-dev

jobs:
  test_on_push:
    name: Run tests
    strategy:
      matrix:
        python-version: ["3.10", "3.12", "3.13"]

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:${{ matrix.python-version }}
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}

    # The default env has our secrets defined.
    environment: default

    steps:
      - name: Install python3-gi python3-gi-cairo gir1.2-secret-1 azure-cli
        run: |
          apt-get update
          apt-get install -y python3-gi python3-gi-cairo gir1.2-secret-1 azure-cli

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      #This has to be a seperate step as the new env created in this step is not accessible in the same step. Strange github feature.
      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository }}/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository-dev }}/simple" >> $GITHUB_ENV

      - name: Install dependencies
        working-directory: ./mdm-viper-lib
        run: |
          python -m pip install --upgrade pip
          pip install -e .[testing] --no-cache-dir

      - name: Install Azure cli
        run: python -m pip install azure-cli

      - name: Azure OIDC prod tenant Login
        uses: azure/login@v1.5.1 # https://github.com/Azure/login/issues/403
        with:
          client-id: ${{ vars.EM_COMPENSATOR_QA_CLIENT_ID }}
          tenant-id: ${{ vars.NEUTRAL_TENANT_ID }}
          allow-no-subscriptions: true

      - name: Unittest
        working-directory: ./mdm-viper-lib
        env:
          DSP_CONFIG_GITHUB_TOKEN: ${{ secrets.DSP_CONFIG_GITHUB_TOKEN }}
        run: |
          coverage run --source=src -m pytest --junitxml=junit/test-results-${{ matrix.python-version }}.xml tests/unittest tests/legacy

      - name: Coverage Report [Unittest] 60%
        working-directory: ./mdm-viper-lib
        run: |
          coverage report --show-missing --fail-under=60
          coverage xml -o sonar-coverage-report.xml
          coverage erase

      - name: Sonarqube
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: mdm-viper-lib
          args: >
            -Dsonar.python.coverage.reportPaths=sonar-coverage-report.xml
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Integration tests
        working-directory: ./mdm-viper-lib
        continue-on-error: false # TODO fix with login with client id for MDM
        # az account get-access-token --output json --resource api://sp-pace-mdtds-paceqa-westeurope must work
        run: |
          coverage run --source=src -m pytest --junitxml=junit/test-results-integraion-${{ matrix.python-version }}.xml tests/integration

      - name: Coverage Report [Integration] 25%
        working-directory: ./mdm-viper-lib
        run: |
          coverage report --show-missing  --fail-under=25
          coverage erase

      # Test result to be uploaded as an artifact in github
      - name: Upload pytest test results
        uses: actions/upload-artifact@v4
        with:
          name: pytest-results-${{ matrix.python-version }}
          path: |
            junit/test-results-*${{ matrix.python-version }}.xml

  build_on_pull_request:
    name: Build and push wheel
    needs: test_on_push

    strategy:
      matrix:
        python-version: ["3.10", "3.12", "3.13"]
        repository: ["shared-pypi-prod", "shared-pypi-dev"]

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}

    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:${{ matrix.python-version }}
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}

    # The default env has our Azure secrets defined.
    environment: default

    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository }}/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository-dev }}/simple" >> $GITHUB_ENV

      - name: Build wheels
        working-directory: ./mdm-viper-lib
        run: |
          python -m pip install --upgrade pip
          pip install "build>0.7.0,<1.0.0"
          SETUPTOOLS_SCM_DEBUG=1 python -m build -w -C="--build-option=--python-tag" -C="--build-option=py${{ matrix.python-version }}"
          du -sh dist/*.whl

      - name: Push wheels
        working-directory: ./mdm-viper-lib
        if: ${{ github.ref_name == 'main' || startsWith(github.ref, 'refs/tags/v') }}

        run: |
          pip install twine
          twine upload --repository-url https://jfrog.ad-alliance.biz/artifactory/api/pypi/${{ matrix.repository }} -u ${{ secrets.JCA_ARTIFACTORY_PROD_USER }} -p ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }} --verbose --non-interactive dist/*.whl
