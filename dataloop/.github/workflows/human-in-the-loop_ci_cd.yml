# This workflow will perform static code analysis and run tests.

name: Human-in-the-loop CI/CD

on:
  push:
    paths:
      - human-in-the-loop/**
      - .github/workflows/human-in-the-loop_ci_cd.yml

  pull_request:
    branches: [main]
    paths:
      - human-in-the-loop/**
      - .github/workflows/human-in-the-loop_ci_cd.yml

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

env:
  KEY_VAULT: kv-pace-secrets
  DATALOOP_IMAGE_NAME: human-in-the-loop

jobs:

  unittest:
    name: Unitttest
    strategy:
      matrix:
        python-version: ["3.8",  ]
    defaults:
      run:
        working-directory: ./human-in-the-loop

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:bullseye
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Install required SO packages
      run: |
        apt list --installed
        apt update
        apt install -y libtbb2 libtbb-dev libgl1

    - name: Define Extra Index URL
      run: |
        echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
        echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV
        echo "UV_DEFAULT_INDEX=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
        echo "UV_INDEX=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV
        echo "UV_INDEX_STRATEGY=unsafe-any-match" >> $GITHUB_ENV

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip uv
        uv venv --python ${{ matrix.python-version }}
        uv pip install -e .[testing] --no-cache-dir

    - name: Run unittest
      run: |
        uv run coverage run --source=src -m pytest -vvv --junitxml=junit/test-results-${{ matrix.python-version }}.xml tests/unittest

    - name: Coverage Report [Unittest] 70%
      run: |
        uv run coverage report --show-missing --fail-under=70
        uv run coverage xml -o sonar-coverage-report.xml
        uv run coverage erase

  integration:
    name: Integration test
    needs: unittest
    strategy:
      matrix:
        python-version: ["3.8",  ]
    defaults:
      run:
        working-directory: ./human-in-the-loop

    # The default env has our secrets defined.
    environment: default

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:bullseye
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
    steps:
    - name: Checkout
      uses: actions/checkout@v4
      with:
        fetch-depth: 0

    - name: Install required SO packages
      run: |
        apt list --installed
        apt update
        apt install -y libtbb2 libtbb-dev libgl1

    - name: Define Extra Index URL
      run: |
        echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
        echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV
        echo "UV_DEFAULT_INDEX=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
        echo "UV_INDEX=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV
        echo "UV_INDEX_STRATEGY=unsafe-any-match" >> $GITHUB_ENV

    - name: Install dependencies
      run: |
        python -m pip install --upgrade pip uv
        uv venv --python ${{ matrix.python-version }}
        uv pip install -e .[testing] --no-cache-dir

    - name: Install Azure cli
      run: python -m pip install azure-cli

    - name: Azure OIDC prod tenant Login
      uses: azure/login@v2
      with:
        client-id: ${{ secrets.CLIENT_ID_DATALOOP_CICD }}
        tenant-id: ${{ vars.NEUTRAL_TENANT_ID }}
        allow-no-subscriptions: true

    - name: Run integration tests
      run: |
        DEFAULT_IDENTITY_CLIENT_ID="" uv run coverage run --source=src -m pytest -vvv --junitxml=junit/test-results-${{ matrix.python-version }}.xml tests/integration

    - name: Coverage Report [Integration tests] 60%
      run: |
        uv run coverage report --show-missing --fail-under=60
        uv run coverage erase

  build_wheel:
    name: Build wheel
    needs: integration

    outputs:
          wheel_version: ${{ steps.build.outputs.wheel_version }}

    strategy:
      matrix:
        python-version: ["3.8", "3.10" ]
    defaults:
      run:
        working-directory: ./human-in-the-loop

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:bullseye
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
    environment: default

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install required SO packages
        run: |
          apt list --installed
          apt update
          apt install -y libtbb2 libtbb-dev libgl1

      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV
          echo "UV_DEFAULT_INDEX=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
          echo "UV_INDEX=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV
          echo "UV_INDEX_STRATEGY=unsafe-any-match" >> $GITHUB_ENV

      - name: Build wheels
        shell: bash
        id: build
        run: |
          python -m pip install --upgrade pip uv
          uv venv --python ${{ matrix.python-version }}
          uv pip install pkginfo
          uv pip install "build[uv]"
          version_str="${{ matrix.python-version }}"
          version=${version_str//.} # replace all dots

          SETUPTOOLS_SCM_DEBUG=1 uv run python -m build -w -C="--build-option=--python-tag" -C="--build-option=py$version"
          du -sh $PWD/dist/*.whl
          package_path=$(ls -t $PWD/dist/* | head -1)

          echo "wheel_name=$(ls -t $PWD/dist/ | head -1)" >> $GITHUB_OUTPUT
          echo "wheel_path=$package_path" >> $GITHUB_OUTPUT
          echo "wheel_version=$(uv run python -c "from pkginfo import Wheel; print(Wheel('$package_path').version.replace('+', '_'))") " >> $GITHUB_OUTPUT

      - name: Test wheels package
        run: |
          uv venv --python ${{ matrix.python-version }} ~/.clean_env
          . ~/.clean_env/bin/activate
          uv pip install ${{ steps.build.outputs.wheel_path }} --no-cache

          human-in-the-loop --help
          human-in-the-loop queue --help
          human-in-the-loop json --help

      - name: Upload package for later use
        uses: actions/upload-artifact@v4
        with:
          name: ${{ steps.build.outputs.wheel_name }}
          path: ${{ steps.build.outputs.wheel_path }}
          retention-days: 1

  push_wheel:
    name: Push wheel (if main or tag push)
    needs: build_wheel
    if: ${{ github.ref_name == 'main' || startsWith(github.ref, 'refs/tags/human-in-the-loop_v') }}

    strategy:
      matrix:
        repository: ["shared-pypi-prod", "shared-pypi-dev"]

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}

    environment: default

    steps:

      - uses: actions/download-artifact@v4
        with:
          path: /tmp/packages
          merge-multiple: true

      - name: Push wheels

        run: |
          pip install twine
          ls -l /tmp/packages
          twine upload --repository-url https://jfrog.ad-alliance.biz/artifactory/api/pypi/${{ matrix.repository }} -u ${{ secrets.JCA_ARTIFACTORY_PROD_USER }} -p ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}  --skip-existing --verbose --non-interactive /tmp/packages/*.whl

  Docker:
    strategy:
      matrix:
        artifactory:
          [
            "jfrog.ad-alliance.biz/shared-docker-dev",
            "vdeepacrprod.azurecr.io",
          ]
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: default
    needs: build_wheel

    steps:
      - env:
          OUTPUT: ${{ needs.build_wheel.outputs.wheel_version }}
        run: echo "$OUTPUT"

      - name: Check Out Repository
        id: checkout_repository
        uses: actions/checkout@v4

      - name: Get branch name (merge)
        if: github.event_name != 'pull_request'
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_REF#refs/heads/} | tr / -)" >> $GITHUB_ENV

      - name: Get branch name (pull request)
        if: github.event_name == 'pull_request'
        shell: bash
        run: echo "BRANCH_NAME=$(echo ${GITHUB_HEAD_REF} | tr / -)" >> $GITHUB_ENV

      - uses: actions/download-artifact@v4
        with:
          path: /tmp/packages

      # JFROG
      - name: Login Docker to Artifactory JFrog
        uses: docker/login-action@v3
        if: ${{ matrix.artifactory != 'vdeepacrprod.azurecr.io' }}
        with:
          registry: ${{ matrix.artifactory }}
          username: ${{ secrets.JCA_ARTIFACTORY_DEV_USER }}
          password: ${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}

      # ACR
      - name: Azure OIDC prod tenant Login
        if: ${{ matrix.artifactory == 'vdeepacrprod.azurecr.io' }}
        uses: azure/login@v2
        with:
          client-id: ${{ vars.MODELDEVELOPER_PROD_CLIENT_ID }}
          subscription-id: ${{ vars.MLOPS_PROD_SUBSCRIPTION }}
          tenant-id: ${{ vars.NEUTRAL_TENANT_ID }}
          allow-no-subscriptions: true

      - name: Login to ACR (Azure Container Registry)
        if: ${{ matrix.artifactory == 'vdeepacrprod.azurecr.io' }}
        shell: bash
        run: |
          az acr login -n ${{ matrix.artifactory }}


      - name: Build image and push to Azure Container Registry
        uses: docker/build-push-action@v5
        with:
          context: human-in-the-loop
          build-contexts: |
            packages=/tmp/packages
          secrets: |
            PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple/
            PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple/
          tags: |
            ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}:latest
            ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}:run-id-${{ github.run_id }}
            ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}:${{ env.BRANCH_NAME }}
            ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}:v${{ needs.build_wheel.outputs.wheel_version }}
          push: false

      - name: Test image before pushing
        run: |
          docker run --rm ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}:latest --help
          docker run --rm ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}:latest queue --help
          docker run --rm ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}:latest json --help

      - name: Push image to ${{ matrix.artifactory }}
        run: |
          docker images ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}
          docker push --all-tags ${{ matrix.artifactory }}/${{ env.DATALOOP_IMAGE_NAME }}
