name: End-to-End QA Integration Test

on:
  workflow_dispatch:
    inputs:
      from-dmv:
        type: boolean
        description: 'Workflow triggered by DMV'
        default: false
        required: false
  # workflow_call:
  # repository_dispatch:
  #   type: [trigger-workflow]

permissions:
  id-token: write
  contents: read
# Prevent the end-to-end integration test to run multiple times in parallel as they break each other
# All end-to-end tests will have the same concurrency string and hence wait for each other.
concurrency: dsp_end_to_end_test
env:
  artifactory-repository: shared-pypi-prod
  artifactory-repository-dev: shared-pypi-dev

jobs:
  init:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment: default

    strategy:
      matrix:
        python-version: ["3.10"]

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}

      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository }}/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository-dev }}/simple" >> $GITHUB_ENV

      - name: Install dependencies
        run: |
          # python -m pip install --upgrade pip
          pip install -e mdm-viper-lib/. --no-cache-dir

      - name: Install kubectl
        run: |
          curl -LO "https://dl.k8s.io/release/$(curl -Ls https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl"
          chmod +x kubectl
          sudo mv kubectl /usr/local/bin/

      - name: Install kubelogin
        run: |
          curl -LO "https://github.com/Azure/kubelogin/releases/download/v0.0.31/kubelogin-linux-amd64.zip"
          unzip kubelogin-linux-amd64.zip
          sudo mv  bin/linux_amd64/kubelogin  /usr/local/bin/kubelogin
          sudo chmod +x /usr/local/bin/kubelogin

      - name: Install Argo CLI
        run: |
          curl -sLO https://github.com/argoproj/argo/releases/latest/download/argo-linux-amd64.gz
          gunzip -f argo-linux-amd64.gz
          chmod +x argo-linux-amd64
          sudo mv argo-linux-amd64 /usr/local/bin/argo


      - name: Login to Azure with SP
        run: |
          az login --service-principal -u ${{ secrets.CLIENT_ID_INTEGRATIONTEST }} -p  ${{ secrets.CLIENT_SECRET_INTEGRATIONTEST }} --tenant ${{ secrets.PROJECT_TENANT_ID }}
          az account set --subscription sub-data-delivery-dll-001-qa
          az aks get-credentials --name dll-qa-aks --resource-group rg-aks-dll-qa

      - name: Kubelogin
        run: |
          export KUBECONFIG=/home/<USER>/.kube/config
          kubelogin convert-kubeconfig -l spn --client-id ${{ secrets.CLIENT_ID_INTEGRATIONTEST }} --client-secret ${{ secrets.CLIENT_SECRET_INTEGRATIONTEST }}

      - name: Clean up queues
        run: python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/queue_clear.py

      - name: Terminate workflows
        run: |
          chmod +x ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/terminate_workflows.sh
          bash ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/terminate_workflows.sh
        shell: bash

      - name: Clean up TDS, MDD entries
        run: |
          python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/cli.py find e2eqatest -o entries.txt --mode output
          python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/cli.py delete --target entries --stdin < entries.txt

          python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/cli.py find e2eqatest -o namespaces.txt --mode all
          python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/cli.py delete --target namespaces --stdin < namespaces.txt

      - name: Inject messages manually (Triggered by DSP)
        if: ${{ ! inputs.from-dmv }}
        run: |
          python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/cli.py ingest d656d2168ff1f8b59e271ba6d6bc252c5e3dcefb6e0920997e7d925cb648e196
        shell: bash

      - name: Data Extraction - Submit Auto Sensor
        run: |
          argo submit --from=cronwf/zeno-auto-sensor-cron --wait -p upload_storage_pool=g5vprtesttmp -n argo-dsp-de-staging
        shell: bash

      - name: Data Extraction - Submit Workflow starter
        run: |
          sleep 10s
          argo submit --from=cronwf/zeno-workflow-starter-cron --wait -n argo-dsp-de-staging
        shell: bash

      - name: Data Extraction - Wait for extractions to finish
        run: |
          sleep_interval=30
          count_time=0
          max_sleep_time=1800 # time in seconds

          while true; do
              output=$(argo list --status Running -n argo-dsp-de-staging --output name)

              if [[ $output == "No workflows found" ]]; then
                  break
              fi

              echo -e "--------------------------- Running workflows: ---------------------------\n$output"

              if [[ $count_time -ge $max_sleep_time ]]; then
                  echo "Max sleep time reached. Exiting..."
                  exit 1
              fi
              echo -e "Sleeping $sleep_interval seconds - $count_time/$max_sleep_time s"
              sleep $sleep_interval
              count_time=$((count_time + sleep_interval))
          done
        shell: bash

      - name: Data Extraction - Submit Event publication
        run: |
          argo submit --from=cronwf/zeno-event-publication-cron --wait -n argo-dsp-de-staging
        shell: bash

      - name: PLACEHOLDER - Data Extraction - Assert created events
        run: |
          echo "Placeholder - check needed for FrameExtracted and FrameRectified events only"
          # python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/queue_check.py
        shell: bash

      - name: PLACEHOLDER - Active Learning - Submit Scoring
        run: |
          echo "Placeholder for active learning services"
          # argo submit --from=cronwf/scoring_something_AAAAAAAAAAAAAAAAAA --wait -n argo-dsp-al-staging

      # - name: Validate artifacts ready for submission and indexation into index
      #   run: |
      #     python ${PWD}/mdm-viper-lib/tests/integration/dataloop/e2e/queue_check.py
      #     argo submit --namespace "argo-image-search-staging" --from=wftmpl/e2e-validation -p vector_count=$VECTOR_COUNT -w
