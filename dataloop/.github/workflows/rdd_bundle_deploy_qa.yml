# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Deploy RDD bundle - QA"

on:
  push:
    branches:
      - main
    paths:
      - "databricks/pipelines/common/rdd_orchestration/**"
      - "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/**"
      - "databricks/pipelines/ada_dataproduct_silver/pace_metrics/**"
      - "databricks/pipelines/ada_dataproduct_silver/sef/**"
      - "databricks/pipelines/ada_dataproduct_silver/needs/**"
      - "databricks/pipelines/ada_dataproduct_silver/rng_metrics/**"
      - "databricks/pipelines/ada_dataproduct_gold/ada_kpi_metrics/**"
      - "databricks/pipelines/ada_dataproduct_gold/release_graph/**"

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

jobs:
  determine-changes:
    name: "Determine changes"
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      deploy_orchestration: ${{ steps.set-matrix.outputs.deploy_orchestration }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetches all history for all branches and tags

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Determine changed bundles
        id: set-matrix
        working-directory: .github/rdd_scripts
        run: python determine_changes.py commit "${{ github.event.before }}" "${{ github.sha }}"

      - name: Sonar Scan RDD QA
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
        with:
          projectBaseDir: databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph

  rdd-bundle-deploy-qa:
    name: Deploy RDD bundle ${{ matrix.name }} - QA
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
      DATABRICKS_RUN_SP: ${{ vars.DATABRICKS_RDD_SP }}
    needs: determine-changes
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-qa
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.determine-changes.outputs.matrix) }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Fetches only most recent commit

      - name: Deploy bundle to qa
        uses: ./.github/actions/bundle_deploy
        with:
          environment: qa
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}
          include-functions: ${{ matrix.deploy-functions}}

  rdd-orchestration-bundle-deploy-qa:
    name: Deploy RDD orchestration bundle - QA
    needs: [rdd-bundle-deploy-qa, determine-changes]
    if: ${{ needs.rdd-bundle-deploy-qa.result == 'success' && needs.determine-changes.outputs.deploy_orchestration == 'true' }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-qa

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Fetches only most recent commit

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Install databricks CLI
        uses: ./.github/actions/install_databricks_cli

      - name: Install Python Dependencies
        working-directory: ./databricks/pipelines/common/rdd_orchestration
        run: pip install -r requirements.txt

      - name: Get databricks token
        id: get_databricks_token
        uses: ./.github/actions/get_databricks_token
        with:
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}

      - name: Generate nightly workflow
        working-directory: ./databricks/pipelines/common/rdd_orchestration
        env:
          DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        run: |
          python generate_workflow.py --target qa \
            --diagram diagrams/full_orchestration.drawio.svg \
            --template templates/orchestration_nightly.yml.jinja \
            --workflow "RDD Orchestration - Nightly"

      - name: Deploy bundle to qa
        uses: ./.github/actions/bundle_deploy
        with:
          environment: qa
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/common/rdd_orchestration

  generate-test-reports-metrics:
    name: Generate Test Reports metrics for all modules
    needs: [determine-changes, rdd-bundle-deploy-qa] # Will run after both jobs
    if: ${{ always() && !cancelled() && !failure() }} # Ensure it runs if no previous job failed, was cancelled, or skipped
    uses: ./.github/workflows/rdd_transform_load_test_reports.yml
    with:
      environment: qa
    secrets: inherit
