# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Deploy Databricks libraries to Artifactory

on:
  workflow_call:
    inputs:
      image:
        required: true
        type: string
  workflow_dispatch:
    inputs:
      image:
        required: true
        type: string

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

env:
  UV_DEFAULT_INDEX: https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple/
  UV_EXTRA_INDEX: https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple/
  PYTHON_VERSIONS: "3.10 3.11"
  MIN_COVERAGE: "90"

defaults:
  run:
    shell: bash

jobs:
  collect-infos:
    name: Collect information
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      test_matrix: ${{ steps.collect-lib-infos.outputs.test_matrix }}
      deploy_matrix: ${{ steps.collect-lib-infos.outputs.deploy_matrix }}

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 2
          sparse-checkout: |
            databricks/libraries
            .github/scripts/dbx_libraries

      - name: Install Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Collect library information
        id: collect-lib-infos
        run: |
          # Add PR number as dev version if on a PR
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            export DEV_VERSION_ARG="--dev-version dev${{ github.event.pull_request.number}}"
          else
            export DEV_VERSION_ARG=""
          fi

          pip install "tomli~=2.2.0" "packaging~=24.2"
          ./.github/scripts/dbx_libraries/collect_infos.py --base HEAD^ --target HEAD \
            --python-versions ${{ env.PYTHON_VERSIONS }} $DEV_VERSION_ARG

  test:
    name: Unit Test ${{ matrix.library }} - Python ${{ matrix.python_version }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    needs: collect-infos
    if: ${{ github.event_name == 'pull_request' && needs.collect-infos.outputs.test_matrix != '' }}
    environment: docker-push
    container:
      image: ${{ inputs.image }}
      credentials:
        username: ${{ vars.AZURE_CLIENT_ID }}
        password: ${{ secrets.AZURE_CLIENT_SECRET }}
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.collect-infos.outputs.test_matrix) }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:  # Fetches only most recent commit
          fetch-depth: 1
          sparse-checkout: |
            databricks/libraries
            pyproject.toml
            uv.lock

      - name: Set Python ${{ matrix.python_version }}
        run: |
          VENV_PATH="/root/.venv_${{ matrix.python_version }}"

          # Create virtual environment if it does not exist
          if [ ! -d "$VENV_PATH" ]; then
            HOME=/root uv venv --no-project --python "${{ matrix.python_version }}" "$VENV_PATH"
            echo "Created virtual environment $VENV_PATH"
          else
            echo "Virtual environment $VENV_PATH already exists"
          fi

          # Set as environment
          echo "UV_PROJECT_ENVIRONMENT=$VENV_PATH" >> $GITHUB_ENV
          echo "VIRTUAL_ENV=$VENV_PATH" >> $GITHUB_ENV

      - name: Install test dependencies
        run: uv sync --inexact --no-install-workspace --no-default-groups --group test

      - name: Install library dependencies
        working-directory: "databricks/libraries"
        run: uv pip install ${{ matrix.local_pip_args }}

      - name: Test library
        working-directory: ${{ matrix.path }}
        run: uv run --no-project coverage run --branch

      - name: Report test coverage
        working-directory: ${{ matrix.path }}
        run: uv run --no-project coverage report --fail-under=${{ env.MIN_COVERAGE }} --show-missing

  build-and-deploy:
    name: Build and deploy ${{ matrix.library }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    needs: [collect-infos, test]
    if: ${{ !failure() && !cancelled() && needs.collect-infos.outputs.deploy_matrix != '' }}
    environment: docker-push
    container:
      image: ${{ inputs.image }}
      credentials:
        username: ${{ vars.AZURE_CLIENT_ID }}
        password: ${{ secrets.AZURE_CLIENT_SECRET }}
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.collect-infos.outputs.deploy_matrix) }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:  # Fetches only most recent commit
          fetch-depth: 1
          sparse-checkout: |
            databricks/libraries

      - name: Build library
        id: build
        working-directory: ${{ matrix.path }}
        run: |
          # Overwrite the version
          echo "${{ matrix.version }}" > VERSION

          # Build the library
          uv build --wheel

          # Find the built wheel
          WHEEL_FILE="$(cd dist && ls *.whl)"
          echo "Built wheel file: $WHEEL_FILE"

          # Store as output variable
          echo "wheel_file=${WHEEL_FILE}" >> $GITHUB_OUTPUT

      - name: Set remote paths
        id: remote-paths
        shell: bash
        run: |
          # Always deploy to dev
          REMOTE_PATHS=("shared-pypi-dev-local/${{ matrix.library }}/${{ matrix.version }}/${{ steps.build.outputs.wheel_file }}")
          # Deploy to prod only on push to main
          if [[ "${{ github.event_name }}" == "push" ]]; then
            REMOTE_PATHS+=("shared-pypi-prod-local/${{ matrix.library }}/${{ matrix.version }}/${{ steps.build.outputs.wheel_file }}")
          fi

          # Convert bash array to JSON list
          REMOTE_PATHS_JSON=$(printf '%s\n' "${REMOTE_PATHS[@]}" | jq -R . | jq -cs .)

          echo "remote_paths=$REMOTE_PATHS_JSON"
          # Store as output variable
          echo "remote_paths=$REMOTE_PATHS_JSON" >> $GITHUB_OUTPUT

      - name: Upload library to Artifactory
        working-directory: ${{ matrix.path }}
        run: |
          # Iterate over all remote paths
          echo '${{ steps.remote-paths.outputs.remote_paths }}' | jq -r '.[]' | while read -r REMOTE_PATH; do
            echo "Uploading ${{ steps.build.outputs.wheel_file }} to $REMOTE_PATH"

            jf rt u "dist/${{ steps.build.outputs.wheel_file }}" "$REMOTE_PATH" \
                --user=${{ secrets.JCA_ARTIFACTORY_PROD_USER }} \
                --password=${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }} \
                --url=${{ secrets.JCA_ARTIFACTORY_SERVER_URL }}
          done

      - name: Verify upload
        run: |
          # Iterate over all remote paths
          echo '${{ steps.remote-paths.outputs.remote_paths }}' | jq -r '.[]' | while read -r REMOTE_PATH; do
            echo "Looking for ${{ steps.build.outputs.wheel_file }} in $REMOTE_PATH"

            jf rt s "$REMOTE_PATH" \
                --user=${{ secrets.JCA_ARTIFACTORY_PROD_USER }} \
                --password=${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }} \
                --url=${{ secrets.JCA_ARTIFACTORY_SERVER_URL }} \
                --fail-no-op
          done
