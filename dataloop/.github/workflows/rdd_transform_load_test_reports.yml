# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: Transform and Load RDD Test Reports Metrics

on:
  workflow_call:
    inputs:
      environment:
        description: "Name of the Databricks environment"
        required: true
        type: string
  workflow_dispatch:
    inputs:
      environment:
        description: "Name of the Databricks environment"
        required: true
        type: string

permissions:
  id-token: write
  contents: read

env:
  ARTIFACTORY_USER: ${{ secrets.JCA_ARTIFACTORY_QA_USER }}
  ARTIFACTORY_TOKEN: ${{ secrets.JCA_ARTIFACTORY_QA_TOKEN }}
  ARTIFACTORY_URL: ${{ secrets.JCA_ARTIFACTORY_SERVER_URL }}
  PIP_INDEX_URL: "https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple"
  PIP_EXTRA_INDEX_URL: "https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple"

jobs:
  generate-tests-reports-transform-load:
    name: Generate tests reports, transform and load for all modules
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-${{ inputs.environment }}
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
      DATABRICKS_RUN_SP: ${{ vars.DATABRICKS_RDD_SP }}
    strategy:
      fail-fast: false
      matrix:
        include:
          - module: ado
            job-names: "ado_e2e_release_test"
          - module: github
            job-names: "github_commit_e2e_test github_pr_e2e_test github_release_e2e_test"
          - module: needs
            job-names: "needs_e2e_test"
          - module: adx
            job-names: "adx_test_report_e2e_test"
          - module: jira
            job-names: "jira_e2e_release_test"

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip"

      - name: Install dependencies to run unit tests
        if: ${{ inputs.environment != 'prod' }}
        working-directory: databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/${{ matrix.module }}
        run: |
          # Install packages used to execute tests
          pip install -r ${{ github.workspace }}/databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/test-requirements.txt

          # requirements.txt is optional
          if [ -f "requirements.txt" ]; then
            pip install -r requirements.txt
          fi

      - name: Install rddlib
        if: ${{ inputs.environment != 'prod' }}
        working-directory: databricks/libraries/
        run: pip install -e ./rddlib[spark]

      - name: Run unit tests for ${{ matrix.module }}
        if: ${{ inputs.environment != 'prod' }}
        working-directory: databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/${{ matrix.module }}
        run: python3 -m pytest --cov-report json:../test_reports_metrics/${{ matrix.module }}_test_coverage.json -W ignore::DeprecationWarning --cov=. tests/ --json=../test_reports_metrics/${{ matrix.module }}_unit_test_results.json

      - name: Upload unit test reports to artifactory
        if: ${{ inputs.environment != 'prod' }}
        working-directory: databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/${{ matrix.module }}
        run: |
          echo "Uploading test coverage results files..."
          jf rt u "../test_reports_metrics/${{ matrix.module }}_test_coverage.json" \
              "shared-generic-qa-local/rdd/test-reports/${{ github.sha }}/${{ matrix.module }}_test_coverage.json" \
              --user=${{ env.ARTIFACTORY_USER }} \
              --password=${{ env.ARTIFACTORY_TOKEN }} \
              --url=${{ env.ARTIFACTORY_URL }}

          echo "Uploading unit test results files..."
          jf rt u ../test_reports_metrics/${{ matrix.module }}_unit_test_results.json \
              "shared-generic-qa-local/rdd/test-reports/${{ github.sha }}/${{ matrix.module }}_unit_test_results.json" \
              --user=${{ env.ARTIFACTORY_USER }} \
              --password=${{ env.ARTIFACTORY_TOKEN }} \
              --url=${{ env.ARTIFACTORY_URL }}
        shell: bash

      - name: Set start timestamp
        id: start
        run: printf 'timestamp=%(%s)T\n' >> "$GITHUB_OUTPUT"

      - name: Run E2E tests bundle
        if: ${{ inputs.environment != 'prod' }}
        id: run-e2e-tests
        uses: ./.github/actions/bundle_run
        with:
          environment: ${{ inputs.environment }}
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/${{ matrix.module }}
          job-names: ${{ matrix.job-names }}

      - name: Generate E2E tests report
        if: ${{ inputs.environment != 'prod' }}
        run: |
          # some string manipulation to get the correct format and input data for e2e tests
          # getting current timestamp and date
          printf -v now '%(%s)T'
          printf -v date '%(%Y-%m-%d %H:%M:%S.00)T'

          # duration is calculated with the current timestamp and timestamp from start step
          duration=$((now - ${{steps.start.outputs.timestamp}}))

          # the outcome of the e2e tests should be in the same format as other tests, therefore it will be changed here from "success" to "passed"
          if [ ${{ steps.run-e2e-tests.outcome }} = "success" ]; then
            outcome="passed"
          else
            outcome="failed"
          fi

          # if there are multiple job names, multiple test entries have to be generated. job names are first sperated
          IFS=' ' read -ra job_names <<< "${{ matrix.job-names }}"

          # then it will be iterated through all job names, creating a dictonary for saving it into the report
          for name in "${job_names[@]}"; do test=$test,'{"name": "'$name'","duration": '$duration', "outcome": "'$outcome'"}'; done

          # these two steps are there for removing the first comma
          size=${#test}
          tests="${test:1:$size}"

          # saving report into test results json
          echo '{"report": {"tests": ['$tests'],"created_at": "'$date'"}}' > e2e_test_results.json

      - name: Upload E2E Test Report to Artifactory
        if: ${{ inputs.environment != 'prod' }}
        run: |
          jf rt u e2e_test_results.json "shared-generic-qa-local/rdd/test-reports/${{ github.sha }}/${{ matrix.module }}_e2e_test_results.json" \
              --user=${{ env.ARTIFACTORY_USER }} \
              --password=${{ env.ARTIFACTORY_TOKEN }} \
              --url=${{ env.ARTIFACTORY_URL }}
        shell: bash

      - name: Prepare Params
        id: prepare-params
        run: |
          PARAMS="github_sha=${{ github.sha }},module_name=${{ matrix.module }}"
          echo "params=$PARAMS" >> $GITHUB_OUTPUT

      - name: Run Test Reports Metrics Bundle with Params
        uses: ./.github/actions/bundle_run
        with:
          environment: ${{ inputs.environment }}
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/test_reports_metrics
          job-names: test_reports_metrics
          params: ${{ steps.prepare-params.outputs.params }}
