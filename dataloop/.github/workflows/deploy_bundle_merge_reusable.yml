name: "Deploy Bundle"
on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment to deploy against (dev/qa/prod)"

permissions:
  id-token: write
  contents: read


jobs:
  find-eligible-bundles:
    name: "Find Eligible Bundles"
    runs-on: ${{ from<PERSON><PERSON>(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # need only the current merge commit and the source commit before the merge

      - name: "Find Auto Deployment Bundles"
        id: set-matrix
        uses: ./.github/actions/dab_find
        with:
          cli_options: "--environment ${{ inputs.environment }} deployment --context merge"

  bundle-deploy:
    name: "Deploy Bundle ${{ matrix.name }} - ${{ inputs.environment }}"
    needs: find-eligible-bundles
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.find-eligible-bundles.outputs.matrix) }}
    runs-on: ${{ from<PERSON><PERSON>(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-${{ inputs.environment }}
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: "Deploy bundle"
        uses: ./.github/actions/bundle_deploy
        with:
          environment: ${{ inputs.environment }}
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}
