# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

name: build and publish datasets client

on:
  push:
    branches:
      - main
    paths:
      - 'datasets-client/**'
  pull_request:
    paths:
      - 'datasets-client/**'
  workflow_dispatch:
    inputs:
      stage:
        type: choice
        description: Stage of deployment
        options:
        - dev
        - qa
        - prod
        default: dev
env:
  ARTIFACTORY_USER: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
  ARTIFACTORY_TOKEN: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
  ARTIFACTORY_URL: ${{ secrets.JCA_ARTIFACTORY_SERVER_URL }}
  AZURE_TENANT_ID: ${{ secrets.AZURE_TENANT_ID }}
  AZURE_CLIENT_ID: ${{ secrets.DATASETS_CLIENT_ID }}
  AZURE_CLIENT_SECRET: ${{ secrets.DATASETS_CLIENT_SECRET }}
jobs:
  run-ci:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment: datasets-client
    steps:
      - name: Workflow init
        uses: pace-int/pace-actions/workflow-init@main
      - name: Checkout repository
        uses: actions/checkout@v4
      - name: Setting up python 3.10
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
          cache: 'pip' # Caching pip dependencies

      - name: get OpenAPI Specification
        run: |
          jf rt dl "shared-pypi-dev-local/datasets-service-openapi/*" \
            --limit=1 \
            --sort-by=created \
            --sort-order=desc \
            --user=${{ env.ARTIFACTORY_USER }} \
            --password=${{ env.ARTIFACTORY_TOKEN }} \
            --url=${{ env.ARTIFACTORY_URL }}
          mv datasets-service-openapi/*/*.json ./openapi.json
        shell: bash

      - name: generate code
        run: |
          docker run --rm \
            -v ${PWD}:/local \
            swaggerapi/swagger-codegen-cli-v3:3.0.54 generate \
              -i  /local/openapi.json \
              -l python \
              -o /local/datasets-client/src/ \
              -D [packageName="datasets_client", packageVersion="$(echo datasets-service-openapi/* | awk -F/ '{print $2}' | tr -d '\r\n/')"]
        shell: bash

      - name: "Install mdm-sdk-mdd from artifactory"
        run: python -m pip install mdm-sdk-mdd==1.1.6 --extra-index-url https://${{ env.ARTIFACTORY_USER }}:${{env.ARTIFACTORY_TOKEN }}@jfrog.ad-alliance.biz/artifactory/api/pypi/mdm-pypi-public-local/simple
        shell: bash

      - name: Install build dependencies
        run: |
          pip install -r datasets-client/build-requirements.txt
        shell: bash

      - name: Install other dependencies
        run: |
          pip install -r datasets-client/requirements.txt
        shell: bash

      - name: build python wheel
        run: |
          sudo chown $(whoami) -R datasets-client/
          sed -i 's/[[:space:]]*$//'  $(find datasets-client/src/ -type f)
          python -m build datasets-client/src/. --wheel
        shell: bash

      - name: install client
        run: |
          pip install datasets-client/src/dist/datasets_client-*.whl
        shell: bash

      - name: test client
        run: |
          pytest datasets-client/tests/
        shell: bash

      - name: extract version
        run: |
          ls datasets-client/src/dist/datasets_client-*.whl | grep -oP 'datasets_client-\K[0-9]+\.[0-9]+\.[0-9]+' > VERSION
        shell: bash

      - name: publish client
        if: github.event_name == 'workflow_dispatch' && github.ref == 'refs/heads/main'
        run: |
            jf rt u datasets-client/src/dist/datasets_client-$(cat VERSION)-py3-none-any.whl \
            datasets-client/$(cat VERSION)/datasets_client-$(cat VERSION)-py3-none-any.whl \
            --user=${{ env.ARTIFACTORY_USER }} \
            --password=${{ env.ARTIFACTORY_TOKEN }} \
            --url="${{ env.ARTIFACTORY_URL }}/shared-pypi-${{ inputs.stage || 'dev' }}-local"
        shell: bash
