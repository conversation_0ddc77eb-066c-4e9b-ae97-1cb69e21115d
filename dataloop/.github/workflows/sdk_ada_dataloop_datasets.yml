# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

name: sdk/dataloop-datasets

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches:
      - main
    paths:
      - "sdk/dataloop-datasets/**"
      - ".github/workflows/sdk_ada_dataloop_datasets.yml"
  push:
    branches:
      - main
    paths:
      - "sdk/dataloop-datasets/**"
      - ".github/workflows/sdk_ada_dataloop_datasets.yml"

env:
  poetry_version: 1.8.3
  JCA_ARTIFACTORY_URL: https://jfrog.ad-alliance.biz/artifactory/api/pypi/shared-pypi-prod
  package_path: sdk/dataloop-datasets

jobs:
  test:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python_version: ["3.10", "3.11"]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Test
        uses: ./.github/actions/sdk_poetry_test
        with:
          poetry_version: ${{ env.poetry_version }}
          python_version: ${{ matrix.python_version }}
          package_path: ${{ env.package_path }}
          artifactory_url: ${{ env.JCA_ARTIFACTORY_URL }}
          artifactory_user: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
          artifactory_password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}

  release:
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Publish
        uses: ./.github/actions/sdk_poetry_publish
        with:
          poetry_version: ${{ env.poetry_version }}
          python_version: "3.10"
          package_path: ${{ env.package_path }}
          artifactory_url: ${{ env.JCA_ARTIFACTORY_URL }}
          artifactory_user: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
          artifactory_password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
          repository_url: ${{ env.JCA_ARTIFACTORY_URL }}
