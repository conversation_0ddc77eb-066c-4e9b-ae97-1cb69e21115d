# yaml-language-server: $schema=https://json.schemastore.org/github-workflow.json
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================


name: "Run tests and validations for dataloop.schema package"
on:
  pull_request:
    branches:
      - main
    paths:
      - 'dataloop-schemas/**'
      - '.github/workflows/dataloop_schemas_test_package.yaml'

  push:
    branches:
      - main
    paths:
      - 'dataloop-schemas/**'
      - '.github/workflows/dataloop_schemas_test_package.yaml'

jobs:
  test_content_type:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    steps:
      - name: Check out repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0 # Shallow clones should be disabled for a better relevancy of analysis

      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: 3.8

      - name: Install Python package
        working-directory: ./dataloop-schemas
        shell: bash
        run: |
          pip install -e .[test]

      - name: PyLint
        working-directory: ./dataloop-schemas
        shell: bash
        env:
          TMP_FILE: pylint_output
        run: |

          pylint src tests | tee $TMP_FILE

          echo "## PyLint Output" >> $GITHUB_STEP_SUMMARY
          echo "~~~" >> $GITHUB_STEP_SUMMARY
          echo "$(cat $TMP_FILE)" >> $GITHUB_STEP_SUMMARY
          echo "~~~" >> $GITHUB_STEP_SUMMARY

      - name: MyPy
        working-directory: ./dataloop-schemas
        shell: bash
        env:
          TMP_FILE: mypy_output
        run: |

          mypy --package dataloop.schemas | tee $TMP_FILE

          echo "## MyPy Output" >> $GITHUB_STEP_SUMMARY
          echo "~~~" >> $GITHUB_STEP_SUMMARY
          cat $TMP_FILE >> $GITHUB_STEP_SUMMARY
          echo "~~~" >> $GITHUB_STEP_SUMMARY
          rm $TMP_FILE

      - name: Pytest with coverage
        working-directory: ./dataloop-schemas
        shell: bash
        env:
          TMP_FILE: pytest_output
        run: |
          # the option -rap means display detailed summary 'r' of all tests including passed but without output 'ap'
          # see https://docs.pytest.org/en/7.2.x/how-to/output.html#producing-a-detailed-summary-report
          pytest -rap --cov --cov-report=xml --junit-xml=pytest-report.xml tests/ | tee $TMP_FILE

          echo "## PyTest Output" >> $GITHUB_STEP_SUMMARY
          echo "~~~" >> $GITHUB_STEP_SUMMARY
          cat $TMP_FILE >> $GITHUB_STEP_SUMMARY
          echo "~~~" >> $GITHUB_STEP_SUMMARY
          rm $TMP_FILE

      - name: Upload to SonarQube
        uses: sonarsource/sonarqube-scan-action@master
        with:
          projectBaseDir: ./dataloop-schemas
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
