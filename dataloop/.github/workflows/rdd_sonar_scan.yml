# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "SonarQube Scan RDD"

on:
  push:
    branches:
      - main
    paths:
      - 'databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/**'
  pull_request:
    branches:
      - main
    paths:
      - 'databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/**'

# Permissions required to perform the actions in this workflow.
permissions:
  contents: read  # required for checkout

concurrency:
  group: rdd-pr-${{ github.event.number }}-scan
  cancel-in-progress: true

jobs:
  sonar_scan_rdd:
    name: SonarQube Scan RDD
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Scan Release Graph
        uses: sonarsource/sonarqube-scan-action@master
        env:
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}
        with:
          projectBaseDir: databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph
