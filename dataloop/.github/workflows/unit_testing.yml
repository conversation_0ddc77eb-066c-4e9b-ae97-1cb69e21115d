# This workflow will run unit tests of specified folders

name: Unit tests

on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
      - closed
  workflow_dispatch:

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

# Ensure that only a single job or workflow using the same concurrency group will run at a time.
# Any currently running job or workflow in the same concurrency group are cancelled.
concurrency:
  # For PRs, the concurrency group will allow only one workflow run per PR at a time, since github.head_ref is
  # only defined for PRs. Merged PRs have their own concurrency group.
  # github.head_ref is the source branch of the PR.
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}-${{ github.event.pull_request.merged || false}}
  cancel-in-progress: true

env:
  artifactory-repository: shared-pypi-prod
  artifactory-repository-dev: shared-pypi-dev

jobs:
  define_test_matrix:
    name: Define Test Matrix
    runs-on: ${{ from<PERSON>son(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:3.10-bullseye
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
    defaults:
      run:
        shell: bash

    outputs:
      test_package_folders: ${{ steps.test_packages.outputs.package_folders }}
      build_package_folders: ${{ steps.build_packages.outputs.package_folders }}

    steps:

      - name: Set safe directory
        run: git config --add --global safe.directory "$GITHUB_WORKSPACE"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install GitHub CLI
        run: bash .github/scripts/install_gh_cli.sh

      - name: Define packages to test
        id: test_packages
        env:
          GH_TOKEN: ${{ github.token }}
        run: |
          if ${{ github.event_name == 'pull_request' }}; then
            python .github/scripts/package_diff.py --pr ${{ github.event.pull_request.number }} --output package_folders
          else
            python .github/scripts/package_diff.py --get-all-packages --output package_folders
          fi

      - name: Define packages to build
        if: ${{ github.event_name == 'pull_request' }}
        id: build_packages
        env:
          GH_TOKEN: ${{ github.token }}
        run: python .github/scripts/package_diff.py --pr ${{ github.event.pull_request.number }} --output package_folders

  unit_tests:
    name: Unit Test - ${{ matrix.package-folder }}
    needs: define_test_matrix
    if: ${{ needs.define_test_matrix.outputs.test_package_folders != '[]' }}

    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10", ]
        package-folder: ${{ fromJSON(needs.define_test_matrix.outputs.test_package_folders) }}
        exclude:
          - package-folder: "datasets-client"

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    # The old bullseye is required for libtbb2 to be present,
    # which is required by pcmanip (which is developed in ADA)
    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:3.10-bullseye
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
    defaults:
      run:
        shell: bash

    # The default env has our secrets defined.
    environment: default

    steps:
      - name: Install required apt packages
        run: |
          apt-get update
          apt-get install -y python3-gi python3-gi-cairo gir1.2-secret-1 azure-cli libtbb-dev libgl1

      - name: Set safe directory
        run: git config --add --global safe.directory "$GITHUB_WORKSPACE"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      #This has to be a seperate step as the new env created in this step is not accessible in the same step. Strange github feature.
      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository }}/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository-dev }}/simple" >> $GITHUB_ENV

      - name: Install Testing tools
        run: pip install -e .[testing] --no-cache-dir

      - name: Install dependencies
        working-directory: ${{ matrix.package-folder }}
        run: |
          python -m pip install --upgrade pip --no-cache-dir
          pip install -e .[testing] --no-cache-dir

      - name: Install Azure cli
        run: python -m pip install -U azure-cli --no-cache-dir

      - name: Install GitHub CLI
        run: bash .github/scripts/install_gh_cli.sh

      - name: Azure OIDC prod tenant Login
        if: matrix.package-folder != 'human-in-the-loop'
        uses: azure/login@v1.5.1 # https://github.com/Azure/login/issues/403
        with:
          client-id: ${{ vars.EM_COMPENSATOR_QA_CLIENT_ID }}
          tenant-id: ${{ vars.NEUTRAL_TENANT_ID }}
          allow-no-subscriptions: true

      - name: Azure OIDC prod tenant Login
        if: matrix.package-folder == 'human-in-the-loop'
        uses: azure/login@v2
        with:
          client-id: ${{ secrets.CLIENT_ID_DATALOOP_CICD }}
          tenant-id: ${{ vars.NEUTRAL_TENANT_ID }}
          allow-no-subscriptions: true

      - name: Install JDK
        if: contains(matrix.package-folder, 'databricks') || contains(matrix.package-folder, 'dataloop-usecases')
        uses: actions/setup-java@v3
        with:
          java-version: 11
          distribution: zulu

      - name: Unittest
        working-directory: ${{ matrix.package-folder }}
        run: |
          test_path=tests/
          if [[ -d "tests/unittest" ]]; then
            test_path=tests/unittest
          fi
          coverage run --source=src -m pytest --junitxml=junit/unittest-results-${{ matrix.package-folder }}-${{ matrix.python-version }}.xml $test_path

      - name: Code is modified
        if: ${{ github.event_name == 'pull_request' }}
        id: modified
        env:
          GH_TOKEN: ${{ github.token }}
        run: python .github/scripts/package_diff.py --pr ${{ github.event.pull_request.number }} --package ${{ matrix.package-folder }} --python-src-only --output modified_files

      - name: Coverage Report [Unittest] on modified files 60%
        if: steps.modified.outputs.modified_files != ''
        run: coverage report --show-missing --fail-under=60 --include ${{ steps.modified.outputs.modified_files }} --data-file=${{ matrix.package-folder }}/.coverage

      - name: Coverage Report [Unittest] 60%
        working-directory: ${{ matrix.package-folder }}
        continue-on-error: true
        run: |
          coverage report --show-missing --fail-under=60
          coverage xml -o sonar-coverage-report.xml
          coverage erase

      - name: Integration tests
        working-directory: ${{ matrix.package-folder }}
        run: |
          if [[ -d "tests/integration" ]]; then
            test_path=tests/integration
            coverage run --source=src -m pytest --junitxml=junit/integration-test-results-${{ matrix.package-folder }}-${{ matrix.python-version }}.xml $test_path
          fi

      - name: Install MyPy
        run: pip install -e .[mypy-analysis] --no-cache-dir

      - name: Run MyPy checker
        working-directory: ${{ matrix.package-folder }}
        run: |
          bash $GITHUB_WORKSPACE/.github/scripts/mypy_package_check.sh


  unit_test_summary:
    if: ${{ always() }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    name: Unit Test Summary
    needs: [unit_tests]
    steps:
      - run: |
          result="${{ needs.unit_tests.result }}"
          if [[ $result != "failure" ]]; then
            exit 0
          else
            exit 1
          fi


  xflow_docker_tests:
    name: Run unit tests in xflow docker
    needs: define_test_matrix
    continue-on-error: true
    if: false  # This job is disabled for now

    runs-on: [gpu-t4-4core-prod]
    timeout-minutes: 120

    container:
      image: vdeepacrprod.azurecr.io/xflow_release_base:latest
      credentials:
        username: ${{ secrets.MODELDEVELOPER_PROD_CLIENT_ID }}
        password: ${{ secrets.MODELDEVELOPER_PROD_CLIENT_SECRET }}
      options: --gpus all

    # The default env has our secrets defined.
    environment: default

    steps:
      - name: Checkout dataloop repo
        uses: actions/checkout@v4
        with:
          lfs: true

      - name: Checkout xflow repo
        uses: actions/checkout@v4
        with:
          repository: PACE-INT/xflow
          token: ${{ secrets.XFLOW_REPO_CLONE_TOKEN }}
          path: xflow
          lfs: true
          fetch-depth: 0

      - name: Run unit tests in defined xflow packages
        shell: bash
        run: |
          git config --global --add safe.directory .
          git lfs checkout && cd xflow && git lfs checkout && cd ..
          pip install --user -r xflow/.build/requirements/test-requirements.txt
          pip uninstall dataloop-data-formats dataloop-data-converters dataloop-label-set -y
          pip install ./dataloop-data-formats ./label-set
          cd xflow
          git config --global --add safe.directory .
          ./install.sh -x -o /tmp/xflow.txt
          pip install --user -r /tmp/xflow.txt
          export TF_FORCE_GPU_ALLOW_GROWTH=true
          python -m pytest data_formats/tests
          python -m pytest xtension/tests
          python -m pytest evil/tests
          python -m pytest xtensorflow/tests
          cd xusecases/tests  # not all tests in xusecases are executed
          cd classification && python -m pytest .
          cd ../common && python -m pytest .
          cd ../vision && python -m pytest .
          cd ../radar && python -m pytest .
          cd ../light_rnn && python -m pytest .
          cd ../lidar && python -m pytest .

  build_wheel:
    name: Build & push wheels
    needs: [define_test_matrix, unit_test_summary]
    # !failure() && !cancelled() are needed to still run when xflow_docker_tests are skipped
    if: ${{ !failure() && !cancelled() && github.event.pull_request.merged == true && needs.define_test_matrix.outputs.build_package_folders != '[]' }}

    strategy:
      matrix:
        python-version: ["3.10", ]
        repository: ["shared-pypi-dev", "shared-pypi-prod"]
        package-folder: ${{ fromJSON(needs.define_test_matrix.outputs.build_package_folders) }}
        exclude:
          - package-folder: "datasets-client"
          - package-folder: "human-in-the-loop"
          - package-folder: "mdm-viper-lib"
          - package-folder: "dataloop-schemas"

    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    # The old bullseye is required for libtbb2 to be present,
    # which is required by pcmanip (which is developed in ADA)
    container:
      image: jfrog.ad-alliance.biz/docker-hub/python:3.10-bullseye
      credentials:
        username: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
        password: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
    defaults:
      run:
        shell: bash

    # The default env has our secrets defined.
    environment: default

    steps:
      - name: Install required apt packages
        run: |
          apt-get update
          apt-get install -y python3-gi python3-gi-cairo gir1.2-secret-1 azure-cli libtbb-dev libgl1

      - name: Set safe directory
        run: git config --add --global safe.directory "$GITHUB_WORKSPACE"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Install GitHub CLI
        run: bash .github/scripts/install_gh_cli.sh

      #This has to be a seperate step as the new env created in this step is not accessible in the same step. Strange github feature.
      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository }}/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository-dev }}/simple" >> $GITHUB_ENV

      - name: Build wheels
        working-directory: ${{ matrix.package-folder }}
        run: |
          python -m pip install --upgrade pip
          pip install "build>0.7.0,<1.0.0"
          SETUPTOOLS_SCM_DEBUG=1 python -m build -w -C="--build-option=--python-tag" -C="--build-option=py${{ matrix.python-version }}"
          du -sh dist/*.whl

      - name: Push wheels
        working-directory: ${{ matrix.package-folder }}
        run: |
          echo ${{ secrets.GITHUB_TOKEN }} | gh auth login --with-token
          echo "PR number: ${{ github.event.pull_request.number }}"
          changed_path_list=$(gh pr diff ${{ github.event.pull_request.number }} --name-only)
          echo "The following files were changed:"
          echo $changed_path_list

          changed="false"
          for path in $changed_path_list; do
            if [[ $path == "${{ matrix.package-folder }}"* ]]; then
              changed="true"
            fi
          done

          if [[ $changed == "true" ]]; then
            echo "Code changes detected. Proceeding with wheel push."
          else
            echo "No code changes detected. Skipping wheel push."
            exit 0
          fi

          pip install twine
          twine upload --repository-url https://jfrog.ad-alliance.biz/artifactory/api/pypi/${{ matrix.repository }} -u ${{ secrets.JCA_ARTIFACTORY_PROD_USER }} -p ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }} --verbose --non-interactive dist/*.whl
