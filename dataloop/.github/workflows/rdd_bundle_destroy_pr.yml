# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Destroy RDD bundle - PR"

on:
  pull_request:
    types: [closed]
    branches:
      - main
    paths:
      - "databricks/pipelines/common/rdd_orchestration/**"
      - "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/**"
      - "databricks/pipelines/ada_dataproduct_silver/pace_metrics/**"
      - "databricks/pipelines/ada_dataproduct_silver/sef/**"
      - "databricks/pipelines/ada_dataproduct_silver/needs/**"
      - "databricks/pipelines/ada_dataproduct_silver/rng_metrics/**"
      - "databricks/pipelines/ada_dataproduct_gold/ada_kpi_metrics/**"
      - "databricks/pipelines/ada_dataproduct_gold/release_graph/**"

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read # required for checkout
  pull-requests: read # required for accessing pull request data

jobs:
  determine-changes:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      deploy_orchestration: ${{ steps.set-matrix.outputs.deploy_orchestration }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetches all history for all branches and tags

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Determine changed bundles
        id: set-matrix
        working-directory: .github/rdd_scripts
        run: |
          git fetch origin main
          if [ "${{ github.event.pull_request.merged }}" == "true" ]; then
            HEAD_SHA="${{ github.event.pull_request.merge_commit_sha  }}"  # Merge commit on main
            BASE_SHA="$(git rev-parse ${HEAD_SHA}^1)"  # Commit before merge commit on main
          else
            HEAD_SHA="${{ github.sha }}"  # Temporary merge commit on main
            BASE_SHA="${{ github.event.pull_request.base.sha }}"  # Last commit on main
          fi
          python determine_changes.py commit "$BASE_SHA" "$HEAD_SHA"

      - name: Rename bundles for PR
        id: rename-bundles
        run: |
          pip install PyYAML~=6.0.2
          # Space separated list of changed bundle directories
          changed_bundles="$(echo ${{ steps.set-matrix.outputs.matrix_escaped }} | jq -r '.include[]."work-dir" | @sh' | tr -d \'\" | tr '\n' ' ')"
          echo "$changed_bundles"
          python .github/rdd_scripts/rename_bundles_pr.py --pr_number ${{ github.event.pull_request.number }} $changed_bundles

      - name: Cache repository
        id: cache-repository
        uses: actions/cache/save@v4
        with:
          key: repository-pr-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: ${{ github.workspace }}

  rdd-bundle-destroy-pr:
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
      DATABRICKS_RUN_SP: ${{ vars.DATABRICKS_RDD_SP }}
    needs: determine-changes
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-dev
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.determine-changes.outputs.matrix) }}
    steps:
      - name: Clear pre-checked out repository
        run: rm -rf  ${{ github.workspace }} && mkdir ${{ github.workspace }} && ls -la

      - name: Restore cached repository
        id: restore-cached-repository
        uses: actions/cache/restore@v4
        with:
          key: repository-pr-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: ${{ github.workspace }}

      - name: Destroy PR bundle in dev
        uses: ./.github/actions/bundle_destroy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}

  rdd-orchestration-bundle-destroy-pr:
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
      DATABRICKS_RUN_SP: ${{ vars.DATABRICKS_RDD_SP }}
    needs: determine-changes
    if: ${{ needs.determine-changes.outputs.deploy_orchestration == 'true' }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-dev

    steps:
      - name: Clear pre-checked out repository
        run: rm -rf  ${{ github.workspace }} && mkdir ${{ github.workspace }} && ls -la

      - name: Restore cached repository
        id: restore-cached-repository
        uses: actions/cache/restore@v4
        with:
          key: repository-pr-${{ github.run_id }}-${{ github.run_number }}-${{ github.run_attempt }}
          path: ${{ github.workspace }}

      - name: Destroy PR bundle in dev
        uses: ./.github/actions/bundle_destroy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/common/rdd_orchestration
