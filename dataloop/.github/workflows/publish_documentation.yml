name: Publish dataloop documentation

on:
  workflow_call:
    inputs:
      image:
        required: true
        type: string
  workflow_dispatch:
    inputs:
      image:
        required: true
        type: string

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout
  pull-requests: write # to enable pull-request decoration

defaults:
  run:
    shell: bash

env:
  UV_DEFAULT_INDEX: https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple/
  UV_EXTRA_INDEX: https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple/

jobs:
  build-and-publish-doc:
    runs-on: ${{ from<PERSON><PERSON>(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment: docker-push
    container:
      image: ${{ inputs.image }}
      credentials:
        username: ${{ vars.AZURE_CLIENT_ID }}
        password: ${{ secrets.AZURE_CLIENT_SECRET }}
    env:
      DOC_DIR: doc
      HTML_DIR: doc/_build/html
      NEEDS_JSON_PATH: doc/_build/html/needs.json
      GRAPHVIZ_DOT: /usr/bin/dot

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Only the current commit is needed

      - name: Build documentation
        run: uv run --no-project sphinx-build -b html -a -E -W -c "${{ env.DOC_DIR }}" ./ "${{ env.HTML_DIR }}"

      - name: Define documentation publishing path
        id: path
        run: |
          ADD_PATH=core
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            PRID=$(echo ${{ github.ref }} | sed 's%^refs/pull/\([0-9]\+\)/.*%\1%g')
            SUB_PATH="$ADD_PATH/pr/$PRID"
          else
            REF="${{ github.ref }}"
            if [[ "$REF" == "refs/heads/main" || "$REF" == "refs/heads/develop" || "$REF" == refs/heads/release/* ]]; then
                SUB_PATH=$(echo $ADD_PATH/$REF | sed 's|refs/heads/||g')
            fi
          fi
          echo "subdirectory-path=$SUB_PATH" >> $GITHUB_OUTPUT

      - uses: pace-int/pace-actions/publish-docs@main # or specific commit hash
        id: publish
        with:
          destination-path-subdirectory: ${{ steps.path.outputs.subdirectory-path }}
          source-path: ${{ env.HTML_DIR }}
          continue-on-error: false
          # The name of the storage account.
          storage-account-name: ${{ secrets.PACE_DOCS_STORAGE_ACCOUNT_NAME }}
          storage-account-access-key: ${{ secrets.PACE_DOCS_ACCESS_KEY }}

      - name: Upload needs.json to Azure Blob Storage
        uses: ./.github/actions/upload-needs-to-blob
        with:
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          client-id: ${{ vars.SP_DATALOOP_STORAGE }}
          source: dataloop

      - name: Comment PR
        uses: actions/github-script@v6
        if: github.event_name == 'pull_request'
        with:
          script: |
              github.rest.issues.createComment({
                issue_number: context.issue.number,
                owner: context.repo.owner,
                repo: context.repo.repo,
                body: 'Documentation is available at ${{ steps.publish.outputs.destination-url }}'
              })
