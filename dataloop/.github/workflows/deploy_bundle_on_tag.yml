name: "Deploy Bundle on Tag - PROD"

on:
  release:
    types: [published]

permissions:
  id-token: write
  contents: read

jobs:
  find-eligible-bundles:
    name: "Find Eligible Bundles"
    runs-on: ${{ from<PERSON><PERSON>(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4

      - name: "Extract tag name"
        run: echo "TAG_NAME=${GITHUB_REF#refs/tags/}" >> $GITHUB_ENV

      - name: "Find Auto Validation Bundles"
        id: set-matrix
        uses: ./.github/actions/dab_find
        with:
          cli_options: "--environment prod deployment --context release --tag ${{ env.TAG_NAME }}"

  bundle-deploy:
    name: "Deploy Bundle ${{ matrix.name }} - prod"
    needs: find-eligible-bundles
    strategy:
      fail-fast: false
      matrix: ${{ from<PERSON><PERSON>(needs.find-eligible-bundles.outputs.matrix) }}
    runs-on: ${{ from<PERSON><PERSON>(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-prod
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4

      - uses: actions/setup-python@v5
        with:
          python-version: '3.11'

      - name: Install Poetry
        run: |
          pip install poetry

      - name: "Deploy bundle"
        uses: ./.github/actions/bundle_deploy
        with:
          environment: prod
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}
