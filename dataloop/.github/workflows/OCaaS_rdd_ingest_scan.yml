# =============================================================================
#   C O P Y R I G H T
# -----------------------------------------------------------------------------
#   Copyright (c) 2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
# =============================================================================

name: RDD - run OCaaS scan

on:
  schedule:
    - cron: '00 01 * * *'   # Runs at 01:00 UTC
  workflow_dispatch:
    inputs:
      revision:
          description: 'Which branch/tag to run the workflow against'
          required: true
          type: string

jobs:

  run_ocaas_scan:
    name: "Run OCaaS scan for RDD ingest"
    uses: ./.github/workflows/OCaaS_scan.yml
    with:
      custom_ort_path: "databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/.ort.yml"
      project_name: "RDD ingest"
      revision: ${{ inputs.revision || github.head_ref || github.ref_name }}
    secrets: inherit
