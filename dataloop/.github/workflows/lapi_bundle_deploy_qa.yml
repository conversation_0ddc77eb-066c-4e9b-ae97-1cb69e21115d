# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Deploy LAPI bundle - QA"

on:
  push:
    branches:
      - main
    paths:
      - databricks/pipelines/ada_dataproduct_silver/lapi/**

# Permissions required to perform the actions in this workflow.
permissions:
    id-token: write # required for the az login
    contents: read  # required for checkout

jobs:
  qa-lapi-bundle-deploy:
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
    runs-on: [self-hosted, linux, build]
    environment:
      name: databricks-qa
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Deploy to qa
        uses: ./.github/actions/bundle_deploy
        with:
          environment: qa
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/ada_dataproduct_silver/lapi
