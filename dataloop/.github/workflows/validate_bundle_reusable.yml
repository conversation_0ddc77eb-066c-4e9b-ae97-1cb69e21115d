name: "Validate Bundle"
on:
  workflow_call:
    inputs:
      environment:
        required: true
        type: string
        description: "Environment to validate against (dev/qa/prod)"
      add-pr-comment:
        required: false
        type: boolean
        default: false
        description: "Whether to add a comment to the PR"
      pr-number:
        required: true
        type: number
        description: "PR number to add comment to"

permissions:
  id-token: write
  contents: read
  pull-requests: write

# Only one workflow per PR
concurrency:
  group: pr-${{ inputs.pr-number }}-${{ inputs.environment }}
  cancel-in-progress: true

jobs:
  find-eligible-bundles:
    name: "Find Eligible Bundles"
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
        with:
          fetch-depth: 2 # need only the current merge commit and the source commit before the merge

      - name: "Find Auto Validation Bundles"
        id: set-matrix
        uses: ./.github/actions/dab_find
        with:
          cli_options: "--environment ${{ inputs.environment }} validation"

  bundle-validate:
    name: "Validate Bundle ${{ matrix.name }} - ${{ inputs.environment }}"
    needs: find-eligible-bundles
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.find-eligible-bundles.outputs.matrix) }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-${{ inputs.environment }}
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
      - name: "Validate bundle"
        uses: ./.github/actions/bundle_validate
        with:
          environment: ${{ inputs.environment }}
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}

  add-comment:
    if: inputs.add-pr-comment == true
    runs-on: ubuntu-latest
    name: "Add comment to PR"
    needs:
      - find-eligible-bundles
      - bundle-validate
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.find-eligible-bundles.outputs.matrix) }}
    steps:
      - name: "Checkout repository"
        uses: actions/checkout@v4
      - name: "Read Comment Heading"
        id: read-comment-heading
        working-directory: .github/shared
        run: |
          ENVIRONMENT=$(echo "${{ inputs.environment }}" | awk '{print toupper($0)}')
          HEADING_KEY=".DEPLOY_${ENVIRONMENT}_BUNDLE_COMMENT_HEADING"
          echo "DEPLOY_BUNDLE_COMMENT_HEADING=$(jq -r "$HEADING_KEY" constants.json)" >> $GITHUB_ENV
      - name: "Post PR Comment"
        uses: actions/github-script@v7
        with:
          script: |
            const commentBody = `${process.env.DEPLOY_BUNDLE_COMMENT_HEADING}
            - [ ] Click this checkbox to deploy bundle \`${{ matrix.work-dir }}\`.  By clicking, a new action will be trigger to deploy the bundle.`;
            await github.rest.issues.createComment({
              owner: "${{ github.repository_owner }}",
              repo: "${{ github.event.repository.name }}",
              issue_number: "${{ github.event.pull_request.number }}",
              body: commentBody
            });
