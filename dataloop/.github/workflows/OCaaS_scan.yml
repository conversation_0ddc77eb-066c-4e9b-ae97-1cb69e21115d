name: OCaaS Compliance checks

on:
  workflow_call:
    inputs:
      revision:
        description: "Which branch/tag to run the workflow against"
        required: false
        default: ${{ github.head_ref || github.ref_name }}
        type: string
      custom_ort_path:
        description: "Path to ORT file used for OCaaS scan"
        required: true
        type: string
      project_name:
        description: "Name of the project that is scanned"
        required: true
        type: string
      application_category:
        description: "Type of application"
        required: false
        default: "BT10"
        type: string

jobs:
  ocaas-scan:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    steps:
      - name: OCaaS Scans
        id: ocaas
        uses: docker://registry.bosch.cloud/ocaas/ocaas-ci:latest
        continue-on-error: true # Built artifacts also should also be uploaded if the scan finds violations.
        with:
          args: auth generate-token run start download
        env:
          OCAAS_USERNAME: ${{ secrets.OCAAS_USERNAME }}
          OCAAS_PASSWORD: ${{ secrets.OCAAS_PASSWORD }}
          PROJECT_NAME: ${{ inputs.project_name }}
          PIPELINE_ID: 547 # Your pipeline ID. Use 547 for latest snippet choice features.
          VCS_URL: ${{ github.server_url }}/${{ github.repository }}.git
          VCS_REVISION: ${{ inputs.revision }}
          CUSTOM_ORT_YML: ${{ inputs.custom_ort_path }}
          APPLICATION_CATEGORY: ${{ inputs.application_category }}
          BLOCKING: true
          REPORT_FILES: DISCLOSURE_DOCUMENT_PDF,VULNERABILITY_REPORT_PDF,SCAN_REPORT_WEB_APP_HTML,GITHUB_ACTION_SUMMARY,FOSSID_SNIPPET_REPORT
          OUTPUT_DIR: reports/
          FOSSID_SERVER: OCAAS
          FOSSID_PROJECT_NAME: ${{ inputs.project_name }}
          FOSSID_SNIPPET_REPORT: true
          FOSSID_FETCH_MATCHED_LINES: true
          RULE_SET: ALLIANCE
      - name: Create GitHub Summary
        run: cat reports/github-action-summary.md >> $GITHUB_STEP_SUMMARY
      - name: Upload reports
        id: upload
        uses: actions/upload-artifact@v4
        with:
          name: reports
          path: reports/
      - name: Check for violations
        if: steps.ocaas.outcome != 'success' || steps.upload.outcome != 'success'
        run: exit 1
