name: Deploy Gatekeeper image

on:
  workflow_call:
    inputs:
      image-name:
        required: true
        type: string
      supported-python-versions:
        required: true
        type: string
      default-python-version:
        required: true
        type: string
  workflow_dispatch:
    inputs:
      image-name:
        required: true
        type: string
      supported-python-versions:
        required: true
        type: string
      default-python-version:
        required: true
        type: string

# Permissions required to perform the actions in this workflow.
permissions:
  id-token: write # required for the az login
  contents: read  # required for checkout

defaults:
  run:
    shell: bash

jobs:
  build-and-push:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: docker-push

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Only the current commit is needed

      - name: Setup Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          install: true

      - name: Install Python 3.11
        uses: actions/setup-python@v5
        with:
          python-version: "3.11"

      - name: Login to ACR
        uses: docker/login-action@v3
        with:
          registry: pacedatabricksdataloop.azurecr.io
          username: ${{ vars.AZURE_CLIENT_ID }}
          password: ${{ secrets.AZURE_CLIENT_SECRET }}

      - name: Build and push image
        run: |
          ./.github/scripts/gatekeeper/build_image.py --uv-default-index "https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple/" \
            --supported-python-versions ${{ inputs.supported-python-versions }} \
            --default-python-version "${{ inputs.default-python-version }}" \
            --image-name "${{ inputs.image-name }}" --push
