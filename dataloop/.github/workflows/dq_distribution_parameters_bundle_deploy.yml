# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "DQ - Distribution Parameters DAB Deploy"

on:
  push:
    branches:
      - main
    paths:
      - databricks/pipelines/ada_dataproduct_silver/data_quality/src/distribution_parameters/**
      - databricks/pipelines/ada_dataproduct_silver/data_quality/src/data_quality_common/**

# Permissions required to perform the actions in this workflow.
permissions:
    id-token: write # required for the az login
    contents: read  # required for checkout

env:
  artifactory-repository: shared-pypi-prod
  artifactory-repository-dev: shared-pypi-dev

jobs:
  data_quality-bundle-deploy:
    name: Deploy Bundle
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    strategy:
      max-parallel: 1
      matrix:
        environment: [ "dev", "qa", "prod" ]
    environment: "databricks-${{ matrix.environment }}"
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.10.9
          cache: 'pip' # caching pip dependencies

      - name: Deploy Bundle
        uses: ./.github/actions/bundle_deploy
        with:
          environment: ${{ matrix.environment }}
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: "databricks/pipelines/ada_dataproduct_silver/data_quality/src/distribution_parameters"
