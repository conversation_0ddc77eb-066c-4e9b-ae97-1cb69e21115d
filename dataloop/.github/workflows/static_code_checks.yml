# This workflow will perform static code analysis and run tests.

name: Static Code Checks

on:
  pull_request:
    branches:
      - main
    types:
      - opened
      - synchronize
      - reopened
      - ready_for_review
  push:
    branches:
      - main

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

# Ensure that only a single job or workflow using the same concurrency group will run at a time.
# Any currently running job or workflow in the same concurrency group are cancelled.
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: true

env:
  PATHS_TO_TEST: src/ tests/

jobs:
  static_code_checks:
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}

    strategy:
      fail-fast: false
      matrix:
        python-version: ["3.10"]

    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0 # fetch full branch history for copyright scan

      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v3
        with:
          python-version: ${{ matrix.python-version }}

      #This has to be a seperate step as the new env created in this step is not accessible in the same step. Strange github feature.
      - name: Define Extra Index URL
        run: |
          echo "PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple" >> $GITHUB_ENV
          echo "PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple" >> $GITHUB_ENV

      - name: Install dependencies
        run: |
          pip install -e .[static-analysis] --no-cache-dir

      - name: Run isort check
        run: |
          isort  . --check-only --settings-path=pyproject.toml

      - name: Run flake8 check
        run: |
          flake8 --config .flake8

      - name: Run black check
        run: |
          export LC_ALL=C.UTF-8  # Ensures same locale configuration
          black . --check --config pyproject.toml

      - name: Run copyright check
        run: |
          python .github/scripts/run_copyright_check.py

      - name: Run pydocstyle check
        run: |
          bash .github/scripts/pydocstyle_check.sh
