name: Dataloop usecases docker build

on:
  pull_request:
    branches: [main]
    paths:
      - '.github/workflows/dataloop_usecases_docker*' # Workflow changes
      - 'dataloop-usecases/docker/**' # image/build changes
      - 'dataloop-usecases/pyproject.toml' # requirements changes

  push:
    branches: [main]
    paths:
      - '.github/workflows/dataloop_usecases_docker*' # Workflow changes
      - 'dataloop-usecases/docker/**' # image/build changes
      - 'dataloop-usecases/pyproject.toml' # requirements changes

  workflow_dispatch: # manual trigger

permissions:
  id-token: write # required for az login
  contents: read # required for checkout

env:
  AZURE_REGISTRY: vdeepacrprod.azurecr.io
  IMAGE_NAME: dataloop-usecases
  ARTIFACTORY_USERNAME: ${{ secrets.JCA_ARTIFACTORY_PROD_USER }}
  ARTIFACTORY_API_KEY: ${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}
  ARTIFACTORY_PYPI_DEV_URL: jfrog.ad-alliance.biz/artifactory/api/pypi/shared-pypi-dev/simple
  ARTIFACTORY_PYPI_PROD_URL: jfrog.ad-alliance.biz/artifactory/api/pypi/shared-pypi-prod/simple
  ENV_FILE_NAME: .env
  KEY_VAULT: kv-pace-secrets

jobs:
  build_docker:
    name: Build dataloop-usecases docker image
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: default
    concurrency:
      group: "dataloop-usecases-${{ github.ref }}"
      cancel-in-progress: true

    steps:
      - name: Checkout Repository
        uses: actions/checkout@v4

      - name: Setup env vars and create env var file
        run: |
          GIT_SHORT_SHA=$(git rev-parse --short HEAD)
          echo "GIT_SHORT_SHA=${GIT_SHORT_SHA}" >> $GITHUB_ENV;

          echo "AZURE_IMAGE_NAME=${{ env.AZURE_REGISTRY }}/${{ env.IMAGE_NAME }}:latest" >> $GITHUB_ENV;
          echo "AZURE_UNIQUE_IMAGE_NAME=${{ env.AZURE_REGISTRY }}/${{ env.IMAGE_NAME }}:$GIT_SHORT_SHA" >> $GITHUB_ENV;

          echo "ARTIFACTORY_USERNAME=${{ env.ARTIFACTORY_USERNAME }}" >> $GITHUB_WORKSPACE/${{ env.ENV_FILE_NAME }};
          echo "ARTIFACTORY_API_KEY=${{ env.ARTIFACTORY_API_KEY }}" >> $GITHUB_WORKSPACE/${{ env.ENV_FILE_NAME }};
          echo "ARTIFACTORY_PYPI_DEV_URL=${{ env.ARTIFACTORY_PYPI_DEV_URL }}" >> $GITHUB_WORKSPACE/${{ env.ENV_FILE_NAME }};
          echo "ARTIFACTORY_PYPI_PROD_URL=${{ env.ARTIFACTORY_PYPI_PROD_URL }}" >> $GITHUB_WORKSPACE/${{ env.ENV_FILE_NAME }};

      - uses: actions/setup-python@v5
        with:
          python-version: '3.10'  # version should be the same as used by the package

      - name: Build Docker
        # Tag the image with commit sha and :latest
        run: |
          bash $GITHUB_WORKSPACE/dataloop-usecases/docker/build_image.sh -r ${{ env.AZURE_REGISTRY }} -i ${{ env.IMAGE_NAME }} -t ${{ env.GIT_SHORT_SHA }} -l

      - name: Azure OIDC Login
        uses: azure/login@v1
        with:
          client-id: ${{ vars.MODELDEVELOPER_PROD_CLIENT_ID }}
          subscription-id: ${{ vars.MLOPS_PROD_SUBSCRIPTION }}
          tenant-id: ${{ vars.NEUTRAL_TENANT_ID }}
          allow-no-subscriptions: true

      - name: Login to Azure Container Registry
        run: |
           az acr login -n ${{ env.AZURE_REGISTRY }}

        # only on push to main:
        #   publish container with ':latest' tag
        #   publish container with commit sha as tag
      - name: Push Docker image
        if: github.ref == 'refs/heads/main'
        run: |
          echo "::group::push image to ACR"
          docker push ${{ env.AZURE_IMAGE_NAME }}
          docker push ${{ env.AZURE_UNIQUE_IMAGE_NAME }}
          echo "::endgroup::"

        # only on pull requests: push container with unique tag for testing purposes
        # ':pr-<PR No.>_<commit-sha>'
        # e.g. ':pr-126_1a6f396'
      - name: Push Test Docker image
        if: contains(github.event_name, 'pull_request')
        run: |
          echo "::group::push image to ACR"
          AZURE_PR_IMAGE_TAG="pr-${{ github.event.pull_request.number }}_${{ env.GIT_SHORT_SHA }}"
          AZURE_PR_IMAGE_NAME=${{ env.AZURE_REGISTRY }}/${{ env.IMAGE_NAME }}:${AZURE_PR_IMAGE_TAG}

          docker tag ${{ env.AZURE_IMAGE_NAME }} ${AZURE_PR_IMAGE_NAME}
          docker push $AZURE_PR_IMAGE_NAME

          echo "::endgroup::"

      - name: Logout from Azure Container Registry
        run: |
          docker logout ${{ env.AZURE_REGISTRY }}
