name: mdm-viper-lib Docker Image Build and Push

on:
  push:
    branches:
      - "**"
    paths:
      - "mdm-viper-lib/**"
      - ".github/workflows/mdm_viper_lib_docker.yml"

env:
  KEY_VAULT: kv-pace-secrets

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

jobs:
  Docker:
    strategy:
      matrix:
        artifactory:
          [
            vdeepacrprod.azurecr.io,
            crdlldev.azurecr.io,
            crdllqa.azurecr.io,
            crdllprod.azurecr.io,
          ]
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: default

    steps:
      - name: Check Out Repository
        id: checkout_repository
        uses: actions/checkout@v4
      - run: echo "DOCKER_BRANCH_TAG=$(echo ${{ github.ref }} | cut -d'/' -f3- | sed 's/[^a-z0-9_-]/__/gi')" >> $GITHUB_ENV

      - name: Azure OIDC prod tenant Login
        uses: azure/login@v1
        with:
          client-id: ${{ vars.MODELDEVELOPER_PROD_CLIENT_ID }}
          subscription-id: ${{ vars.MLOPS_PROD_SUBSCRIPTION }}
          tenant-id: ${{ vars.NEUTRAL_TENANT_ID }}
          allow-no-subscriptions: true

      - name: Login to ACR
        shell: bash
        run: |
          az acr login -n ${{ matrix.artifactory }}

      - name: Build image and push to Azure Container Registry
        uses: docker/build-push-action@v3
        with:
          context: mdm-viper-lib
          build-contexts: |
            git=.git
          secrets: |
            PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-prod/simple/
            PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_DEV_USER }}:${{ secrets.JCA_ARTIFACTORY_DEV_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/shared-pypi-dev/simple/
          tags: |
            ${{ matrix.artifactory }}/mdm-viper-lib:latest
            ${{ matrix.artifactory }}/mdm-viper-lib:${{ github.sha }}
          push: ${{ github.ref == 'refs/heads/main' || github.ref == 'refs/heads/v1'}}

      - name: Build Dev dataloop integration image and push to Azure Container Registry
        uses: docker/build-push-action@v3
        if: ${{ matrix.artifactory == 'crdlldev.azurecr.io' }}
        with:
          context: mdm-viper-lib
          file: ./mdm-viper-lib/src/mdm_viper_lib/dataloop_integration/Dockerfile_anonymizer_download_upload
          build-args: |
            user=dockeruser
            uid=1000
            ARTIFACTORY=${{ matrix.artifactory }}
          tags: |
            ${{ matrix.artifactory }}/anonymizer-download-upload:latest
            ${{ matrix.artifactory }}/anonymizer-download-upload:${{ env.DOCKER_BRANCH_TAG }}
            ${{ matrix.artifactory }}/anonymizer-download-upload:${{ github.sha }}
          push: true

      - name: Build QA dataloop integration image and push to Azure Container Registry
        uses: docker/build-push-action@v3
        if: ${{ matrix.artifactory == 'crdllqa.azurecr.io' }}
        with:
          context: mdm-viper-lib
          file: ./mdm-viper-lib/src/mdm_viper_lib/dataloop_integration/Dockerfile_anonymizer_download_upload
          build-args: |
            user=dockeruser
            uid=1000
            ARTIFACTORY=${{ matrix.artifactory }}
          tags: |
            ${{ matrix.artifactory }}/anonymizer-download-upload:${{ env.DOCKER_BRANCH_TAG }}
            ${{ matrix.artifactory }}/anonymizer-download-upload:${{ github.sha }}
          push: true

      - name: Build Stable dataloop integration image and push to Azure Container Registry
        uses: docker/build-push-action@v3
        if: ${{ matrix.artifactory != 'crdlldev.azurecr.io' }}
        with:
          context: mdm-viper-lib
          file: ./mdm-viper-lib/src/mdm_viper_lib/dataloop_integration/Dockerfile_anonymizer_download_upload
          build-args: |
            user=dockeruser
            uid=1000
            ARTIFACTORY=${{ matrix.artifactory }}
          tags: |
            ${{ matrix.artifactory }}/anonymizer-download-upload:stable
            ${{ matrix.artifactory }}/anonymizer-download-upload:latest
            ${{ matrix.artifactory }}/anonymizer-download-upload:${{ github.sha }}
          push: ${{ github.ref == 'refs/heads/main' || github.ref == 'refs/heads/v1'}}
