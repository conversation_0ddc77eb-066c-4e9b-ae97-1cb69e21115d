name: "Validate Bundle"
on:
  pull_request:
    branches:
      - main
    paths:
      - databricks/pipelines/**

jobs:
  validate-bundle:
    name: "Validate Bundle - ${{ matrix.environment }}"
    strategy:
      fail-fast: false
      matrix:
        include:
          - environment: "dev"
            add-pr-comment: true
          - environment: "qa"
            add-pr-comment: false
          - environment: "prod"
            add-pr-comment: false

    uses: ./.github/workflows/validate_bundle_reusable.yml
    with:
      environment: ${{ matrix.environment }}
      add-pr-comment: ${{ matrix.add-pr-comment }}
      pr-number: ${{ github.event.number }}
    secrets: inherit
