# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Deploy RDD bundle - PROD"

on:
  release:
    types: [published]

permissions:
  id-token: write
  contents: read

jobs:
  determine-changes:
    name: "Determine changes"
    if: startsWith(github.event.release.tag_name, 'rdd')
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    outputs:
      matrix: ${{ steps.set-matrix.outputs.matrix }}
      deploy_orchestration: ${{ steps.set-matrix.outputs.deploy_orchestration }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Fetches all history for all branches and tags

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Determine changed bundles
        id: set-matrix
        working-directory: .github/rdd_scripts
        run: python determine_changes.py tag "${{ github.event.release.tag_name }}"

  rdd-bundle-deploy-prod:
    name: Deploy RDD bundle ${{ matrix.name }} - PROD
    if: startsWith(github.event.release.tag_name, 'rdd')
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
      DATABRICKS_RUN_SP: ${{ vars.DATABRICKS_RDD_SP }}
    needs: determine-changes
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-prod
    strategy:
      fail-fast: false
      matrix: ${{ fromJson(needs.determine-changes.outputs.matrix) }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Fetches only most recent commit

      - name: Deploy bundle to prod
        uses: ./.github/actions/bundle_deploy
        with:
          environment: prod
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: ${{ matrix.work-dir }}
          include-functions: ${{ matrix.deploy-functions}}

  rdd-orchestration-bundle-deploy-prod:
    name: Deploy RDD orchestration bundle - PROD
    needs: [rdd-bundle-deploy-prod, determine-changes]
    if: ${{ needs.rdd-bundle-deploy-prod.result == 'success' && needs.determine-changes.outputs.deploy_orchestration == 'true' }}
    runs-on: ${{ fromJson(vars.ADA_RUNNER_DEFAULT_LABELS) }}
    environment:
      name: databricks-prod

    steps:
      - name: Checkout repository
        uses: actions/checkout@v4
        with:
          fetch-depth: 1 # Fetches only most recent commit

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: 3.11.10
          cache: "pip" # caching pip dependencies

      - name: Install databricks CLI
        uses: ./.github/actions/install_databricks_cli

      - name: Install Python Dependencies
        working-directory: ./databricks/pipelines/common/rdd_orchestration
        run: pip install -r requirements.txt

      - name: Get databricks token
        id: get_databricks_token
        uses: ./.github/actions/get_databricks_token
        with:
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}

      - name: Generate nightly workflow
        working-directory: ./databricks/pipelines/common/rdd_orchestration
        env:
          DATABRICKS_TOKEN: ${{ steps.get_databricks_token.outputs.token }}
        run: |
          python generate_workflow.py --target prod \
            --diagram diagrams/full_orchestration.drawio.svg \
            --template templates/orchestration_nightly.yml.jinja \
            --workflow "RDD Orchestration - Nightly"

      - name: Deploy bundle to prod
        uses: ./.github/actions/bundle_deploy
        with:
          environment: prod
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/common/rdd_orchestration

  generate-test-reports-metrics:
    name: Generate Test Reports metrics for all modules
    needs: [determine-changes, rdd-bundle-deploy-prod] # Will run after both jobs
    if: startsWith(github.event.release.tag_name, 'rdd')
    uses: ./.github/workflows/rdd_transform_load_test_reports.yml
    with:
      environment: prod
    secrets: inherit
