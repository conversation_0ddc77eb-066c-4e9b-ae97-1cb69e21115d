# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
name: "Deploy Gold Azure Costs bundle - DEV"

on:
  pull_request:
    branches:
      - main
    paths:
      - databricks/pipelines/ada_dataproduct_gold/azure_costs/**

# Permissions required to perform the actions in this workflow.
permissions:
    id-token: write # required for the az login
    contents: read  # required for checkout

jobs:
  dev-azure_costs-gold-bundle-deploy:
    env:
      DATABRICKS_DEPLOYMENT_SP: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
    runs-on: [self-hosted, linux, build]
    environment:
      name: databricks-dev
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Deploy to dev
        uses: ./.github/actions/bundle_deploy
        with:
          environment: dev
          deploy-sp: ${{ vars.DATABRICKS_DEPLOYMENT_SP }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          subscription-id: ${{ vars.DATABRICKS_SUBSCRIPTION_ID }}
          project-path: databricks/pipelines/ada_dataproduct_gold/azure_costs
