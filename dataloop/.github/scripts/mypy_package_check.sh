set +e

function run_mypy_check_for {
    # Find and count mypy errors in current branch.
    local mypy_options="--ignore-missing-imports --follow-imports=silent"
    local mypy_quality="--strict --no-warn-return-any --allow-subclassing-any --allow-untyped-decorators --implicit-reexport"
    local pace_mypy="python -m mypy --config-file ../pyproject.toml --non-interactive --install-types $mypy_options $mypy_quality"

    local output_filename="mypy_output_$1.txt"

    # Print command and path settings
    echo "MyPy COMMAND: $pace_mypy"

    rm -f $output_filename
    echo "Running MyPy for $path_to_check"
    $pace_mypy . 2>&1 | tee $output_filename
    if [ "$?" -gt 1 ]; then
        echo "The mypy call hit an unexpected error on this branch."
        exit 1
    fi
}

function git_checkout {
    local target_branch=$1

    git checkout $target_branch 2> /dev/null
    if [ "$?" -gt 0 ]; then
        echo "Error while switching git branches."
        exit 1
    fi
}

function switch_to_main {
    # Switch to the corresponding main state
    echo "Switching to main branch"
    local corresponding_main=`git merge-base HEAD origin/main`
    git_checkout $corresponding_main
}

function switch_back_to_branch {
    echo "Switching back to our original branch so the rest of the pipeline can continue cleanly."
    git_checkout "-"
}

function sort_and_filter_errors_for {
    # sort errors to have a proper diff later
    local input_filename="mypy_output_$1.txt"
    local errors_filename="mypy_errors_$1.txt"
    grep " error:" "$input_filename" | grep -v "mypy_stubs/" | sort > "$errors_filename"
}

function diff_new_errors {
    rm -f new_mypy_errors.txt

    sort_and_filter_errors_for branch
    sort_and_filter_errors_for main
    diff --old-line-format="" --unchanged-line-format="" --new-line-format="%L" $1 $2 > new_mypy_errors.txt
}

function compare_errors {
    # Count and all new errors and print summary.
    diff_new_errors mypy_errors_main.txt mypy_errors_branch.txt
    local num_new_mypy_errors=`cat new_mypy_errors.txt | wc -l`

    # Print the new (or changed) errors
    if [ "$num_new_mypy_errors" -gt 0 ]; then
        echo "Failure: The number of errors has increased compared to the last main by $num_new_mypy_errors."
        echo

        echo "These errors weren't present before and are either new or just have changed line numbers:"
        cat new_mypy_errors.txt

        exit 1
    else
        echo "All fine - no new mypy errors seem to have been introduced compared to the corresponding main."
    fi
}

function cleanup {
    switch_back_to_branch
    rm -rf mypy_stubs/
    rm -f mypy_output_branch.txt
    rm -f mypy_output_main.txt
    rm -f mypy_errors_branch.txt
    rm -f mypy_errors_main.txt
    rm -f new_mypy_errors.txt
}

# 1) Run MyPy check on current branch
run_mypy_check_for "branch"

# 2) Run MyPy checks on main
switch_to_main
run_mypy_check_for "main"

# 3) Compare errors and cleanup
compare_errors
cleanup
