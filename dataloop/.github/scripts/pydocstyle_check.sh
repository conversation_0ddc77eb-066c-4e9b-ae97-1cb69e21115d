set +e

DIRECTORIES_TO_SCAN="databricks/libraries/ databricks/pipelines/ databricks/vars/ dataloop-* datasets-client examples/ human-in-the-loop/ label-set/ mdm-viper-lib/"

function run_pydocstyle_for {
    # Find and count pydocstyle errors in current branch.
    local output_filename="pydocstyle_output_$1.txt"
    local errors_filename="pydocstyle_errors_$1.txt"
    local warnings_filename="pydocstyle_warnings_$1.txt"

    rm -f "$output_filename"
    rm -f "$errors_filename"
    pydocstyle --config=pyproject.toml $DIRECTORIES_TO_SCAN 2>&1 | tee "$output_filename"

    grep "WARNING" "$output_filename" | tee "$warnings_filename"
    grep -v "WARNING" "$output_filename" | sed 'N;s/\n/ /' | sort > "$errors_filename"
    local num_pydocstyle_errors=`cat "$errors_filename" | wc -l`
    echo "$num_pydocstyle_errors"
}

function git_checkout {
    local target_branch=$1

    git checkout $target_branch 2> /dev/null
    if [ "$?" -gt 0 ]; then
        echo "Error while switching git branches."
        exit 1
    fi
}

function switch_to_main {
    # Switch to the corresponding main state
    echo "Switching to main branch"
    local corresponding_main=`git merge-base HEAD origin/main`
    git_checkout $corresponding_main
}

function switch_back_to_branch {
    echo "Switching back to our original branch so the rest of the pipeline can continue cleanly."
    git_checkout "-"
}

function diff_new_errors {
    local old_errors="pydocstyle_errors_main.txt"
    local new_errors="pydocstyle_errors_branch.txt"
    diff --old-line-format="" --unchanged-line-format="" --new-line-format="%L" $old_errors $new_errors > new_pydocstyle_errors.txt
}

function compare_errors {
    diff_new_errors
    local num_new_mypy_errors=`cat new_pydocstyle_errors.txt | wc -l`

    # Print the new (or changed) ones
    if [ "$num_new_mypy_errors" -gt 0 ]; then
        echo "Failure: The number of errors has increased compared to the last main by $num_new_mypy_errors."
        echo

        echo "These errors weren't present before and are either new or just have changed line numbers:"
        cat new_pydocstyle_errors.txt

        exit 1
    else
        echo "All fine - no new pydocstyle errors seem to have been introduced compared to the corresponding main."
    fi
}

function list_warnings() {
    echo
    echo "The following warnings were raised:"
    cat pydocstyle_warnings_branch.txt
    echo
}


function cleanup_for {
    rm -f "pydocstyle_output_$1.txt"
    rm -f "pydocstyle_errors_$1.txt"
    rm -f "pydocstyle_warnings_$1.txt"
}

function cleanup {
    cleanup_for "branch"
    cleanup_for "main"
    rm -f new_pydocstyle_errors.txt
}

run_pydocstyle_for "branch"
switch_to_main
run_pydocstyle_for "main"
switch_back_to_branch
list_warnings
compare_errors
cleanup
