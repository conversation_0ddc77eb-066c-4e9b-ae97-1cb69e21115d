"""Diff tool for packages in a PR."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import os
import re
import subprocess
from pathlib import Path

# If any of the modified files matches any of these patterns,
# all packages in the repositry will be assumed to have changed
INCLUDE_ALL_PACKAGES_PATTERNS = [
    r"^.github/scripts/package_diff.py$",
    r"^.github/actions/.*$",
    r"^.github/workflows/unit_testing.yml$",
]
# All packages that match any of the following patterns are ignored
IGNORED_PACKAGES_PATTERNS = [
    r"^databricks/libraries/.*$",
]

# Pre-compile regex patterns
_INCLUDE_ALL_PACKAGES_CPATTERNS = [re.compile(pattern) for pattern in INCLUDE_ALL_PACKAGES_PATTERNS]
_IGNORED_PACKAGES_CPATTERNS = [re.compile(pattern) for pattern in IGNORED_PACKAGES_PATTERNS]

BASE_DIR = Path(__file__).parents[2]


class TooManyLinesError(Exception):
    """Error raised when the diff exceeds the maximum number of lines."""

    __module__ = "builtins"


def set_github_output(name: str, value: str) -> None:
    """Write the value to the GitHub output variable given by name."""
    with open(os.environ["GITHUB_OUTPUT"], "a") as gh_output_file:
        gh_output = f"{name}={value}"
        print(f"Writing GitHub output: {gh_output}")
        gh_output_file.write(gh_output)


def handle_gh_error(pr_number: int, result: subprocess.CompletedProcess[str]) -> None:
    """Handle errors from the GitHub CLI."""
    TOO_MANY_LINES_STR = (
        "could not find pull request diff: HTTP 406: Sorry, the diff exceeded the maximum number of lines"
    )

    if result.stderr.startswith(TOO_MANY_LINES_STR):
        raise TooManyLinesError(f"Failed to list files in PR {pr_number}. Reason: {result.stderr}")

    raise ValueError(f"Failed to list files in PR {pr_number}. Reason: {result.stderr}")


def _is_python_src_file(file: Path, package: Path) -> bool:
    """Checks if the given file is a Python source file."""
    return (package / "src") in file.parents and file.suffix == ".py"


def _is_in_package(file: Path, package: Path) -> bool:
    """Checks whether a file is contained in a package."""
    return str(file).startswith(str(package))


def _include_all_packages(modified_files: list[Path]) -> bool:
    """Checks whether any modified file matches one the INCLUDE_ALL_PACKAGES_PATTERNS."""
    for file in modified_files:
        raw_rel_file = str(file.relative_to(BASE_DIR))
        for cpattern in _INCLUDE_ALL_PACKAGES_CPATTERNS:
            if cpattern.match(raw_rel_file):
                return True

    return False


def available_packages_in_repository() -> list[Path]:
    """Yield all available packages in the repository."""
    # Find all directories with a pyproject.toml file
    result = []
    for pyproject_file in BASE_DIR.glob("*/**/pyproject.toml"):
        package_dir = pyproject_file.parent
        # Package must contain src folder
        if not (package_dir / "src").exists():
            continue

        raw_rel_package_dir = str(package_dir.relative_to(BASE_DIR))
        # Apply ignored package filter
        if any([ignore_pattern.match(raw_rel_package_dir) for ignore_pattern in _IGNORED_PACKAGES_CPATTERNS]):
            print(f'Ignore package "{raw_rel_package_dir}"')
            continue

        result.append(package_dir)

    return result


def modified_files_in_pr(pr_number: int) -> list[Path]:
    """List all files modified in a PR except those that are deleted. Requires the PR to be checked out."""
    result = subprocess.run(
        ["gh", "pr", "diff", str(pr_number), "--name-only"],
        capture_output=True,
        text=True,
    )

    if result.returncode != 0:
        handle_gh_error(pr_number, result)
    # Split at linebreakes
    raw_files = result.stdout.split("\n")
    # Remove empty lines
    raw_files.remove("")
    return [BASE_DIR / raw_file for raw_file in raw_files if (BASE_DIR / raw_file).exists()]


def modified_packages_in_pr(pr_number: int) -> list[Path]:
    """Yield all packages modified in a PR. Requires the PR to be checked out."""
    try:
        modified_files = modified_files_in_pr(pr_number)
    except TooManyLinesError:
        # Assume all packages as modified if too many lines error occurs
        return available_packages_in_repository()

    remaining_packages = available_packages_in_repository()
    # Always include all packages if any of the modified files matches any of the defined patterns
    if _include_all_packages(modified_files):
        return remaining_packages

    modified_packages = []
    for file in modified_files:
        # Check if file is in any remaining package
        for idx, package in enumerate(remaining_packages):
            if _is_in_package(file, package):
                # Remove package from remaining to reduce runtime
                del remaining_packages[idx]
                modified_packages.append(package)
                break

    return modified_packages


def modified_package_files_in_pr(pr_number: int, package: Path, python_src_files_only: bool) -> list[Path]:
    """Check if a package is modified in a PR."""
    try:
        # Filter for files in package
        modified_files = [file for file in modified_files_in_pr(pr_number) if _is_in_package(file, package)]
    except TooManyLinesError:
        # Assume all files of package as modified if too many lines error occurs
        modified_files = [path for path in package.rglob("*") if path.is_file()]

    # Apply python src files filter if requested
    if python_src_files_only:
        modified_files = [file for file in modified_files if _is_python_src_file(file, package)]

    return modified_files


def _parse_package_path(v: str) -> Path:
    """Parses a given string as a package path relative to the repository root.

    Raises an ArgumentTypeError if it isn't a valid package path.
    """
    package_dir = BASE_DIR / v
    if not (package_dir / "pyproject.toml").exists() or not (package_dir / "src").exists():
        raise argparse.ArgumentTypeError(
            "Must be a directory path relative to"
            ' the repository root containing a "pyproject.toml" file and a "src" folder.'
        )

    return package_dir


def parse_args() -> argparse.Namespace:
    """Parse command line args in the argument list."""
    parser = argparse.ArgumentParser(description="List all packages modified in a PR.")
    parser.add_argument("--pr", type=int, help="The PR number.")
    parser.add_argument("--package", type=_parse_package_path, help="Provide to check a specific package.")
    parser.add_argument(
        "--get-all-packages", action="store_true", default=False, help="Return all packages in the repo."
    )
    parser.add_argument(
        "--python-src-only",
        action="store_true",
        default=False,
        help="Only check on python files in the package src directory.",
    )
    parser.add_argument("--output", type=str, default=None, help="Write as GitHub output.")

    args = parser.parse_args()

    if (args.output is not None) and not os.environ.get("GITHUB_OUTPUT"):
        raise Exception(
            "GITHUB_OUTPUT environment variable is not set. Please set it to the path of the output file.",
        )

    return args


if __name__ == "__main__":
    args = parse_args()

    if args.package is not None:  # Get modified files in given package
        package: Path = args.package
        modified_files = modified_package_files_in_pr(args.pr, package, args.python_src_only)

        csv_modified_files = ",".join([f'"{f.relative_to(BASE_DIR)}"' for f in modified_files])
        # Print modified files
        print(f'Modified files in package "{package.relative_to(BASE_DIR)}":')
        print(csv_modified_files)

        # Write modified files to Github output
        if args.output is not None:
            set_github_output(args.output, csv_modified_files)
    else:  # Get modified packages
        if args.get_all_packages:
            modified_packages = available_packages_in_repository()
        else:  # find the packages that have changes
            modified_packages = modified_packages_in_pr(args.pr)

        csv_modified_packages = ",".join([f'"{p.relative_to(BASE_DIR)}"' for p in modified_packages])
        # Print modified files
        print("Modified packages:")
        print(csv_modified_packages)

        # Write modified packages to Github output
        if args.output is not None:
            set_github_output(args.output, f"[{csv_modified_packages}]")
