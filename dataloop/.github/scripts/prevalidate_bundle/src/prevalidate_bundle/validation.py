"""Module to validate the contents of a YAML file."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path
from pprint import pprint
from typing import Any

import yaml


class JobValidationError(Exception):
    """Exception raised when a job is missing email_notifications."""

    pass


def validate_yaml(yaml_file: Path) -> None:
    """Validate the contents of a YAML file."""
    print("\nValidating", yaml_file)
    contents = yaml.safe_load(yaml_file.read_text())

    resources_dict = _find_resources_with_job(contents)

    if not resources_dict:
        print(f"\nNo 'resources.jobs' found in {yaml_file}")
        return

    for job_name, job in resources_dict["jobs"].items():
        _validate_job(job_name, job)


def _find_resources_with_job(data: dict[str, Any]) -> dict[str, Any]:
    """Find all 'resources' dictionaries that have 'jobs' as a direct child key.

    Returns the complete 'resources' dictionary for each match.
    """
    match data:
        case {"resources": {"jobs": _} as resources}:
            return resources
        case dict():
            for value in data.values():
                if result := _find_resources_with_job(value):
                    return result
            return {}
        case _:
            return {}


def _validate_job(job_name: str, job: dict[str, Any]) -> None:
    """Validate a job dictionary."""
    print("\nValidating job", job_name)
    match job:
        case {"email_notifications": {"on_failure": list(notifiers)}} if notifiers:
            print(f"Email notifications found {notifiers}")
        case _:
            print(f"\nMissing email_notifications in {job_name}")
            pprint(job)
            raise JobValidationError(f"Missing email_notifications in {job_name}")
