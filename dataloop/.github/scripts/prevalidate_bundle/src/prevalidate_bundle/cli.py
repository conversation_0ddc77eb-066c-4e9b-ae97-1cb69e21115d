#!/bin/env python3
"""This script validates the contents of YAML files in a directory."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from pathlib import Path
from typing import Annotated

import typer

from .validation import validate_yaml

app = typer.Typer()


@app.command()
def pre_validate(
    bundle_path_dir: Annotated[
        Path, typer.Argument(help="Path to the directory where the bundle files can be found")
    ] = Path.cwd(),
) -> None:
    """Validate the contents of YAML files in a directory against a set of conditions."""
    print(f"\nValidating YAML files in {bundle_path_dir}")
    for file in bundle_path_dir.glob("**/*.y*ml"):
        try:
            validate_yaml(file)
        except Exception as e:
            print(f"Error validating {file}: {e}")
            raise typer.Exit(code=1)


if __name__ == "__main__":
    app()
