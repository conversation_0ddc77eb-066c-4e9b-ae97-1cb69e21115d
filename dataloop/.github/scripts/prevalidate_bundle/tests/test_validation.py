"""Tests for the validation module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections.abc import Callable
from pathlib import Path

import pytest
import yaml
from prevalidate_bundle.validation import JobValidation<PERSON>rror, validate_yaml


@pytest.fixture
def create_yaml_file(tmp_path: Path) -> Callable[[str], Path]:
    """Create a YAML file with the given content."""

    def _create_yaml_file(content: str) -> Path:
        file_path = tmp_path / "test.yaml"
        file_path.write_text(content)
        return file_path

    return _create_yaml_file


def test_validate_yaml_valid(create_yaml_file: Callable[[str], Path]) -> None:
    """Test that a valid YAML file is validated successfully."""
    # given a valid YAML file
    yaml_content = """
    resources:
      jobs:
        job1:
          email_notifications:
            on_failure:
              - "<EMAIL>"
    """
    yaml_file = create_yaml_file(yaml_content)
    # when we validate the YAML file
    # then it should not raise any exceptions
    validate_yaml(yaml_file)


def test_validate_yaml_no_jobs(create_yaml_file: Callable[[str], Path]) -> None:
    """Test that a YAML file with no jobs is validated successfully."""
    # given a YAML file with no jobs
    yaml_content = """
    resources:
      other_key:
        some_value: "value"
    """
    # when we validate the YAML file
    # then it should not raise any exceptions
    yaml_file = create_yaml_file(yaml_content)
    validate_yaml(yaml_file)


def test_validate_yaml_missing_email_notifications(create_yaml_file: Callable[[str], Path]) -> None:
    """Test that a YAML file with a job missing email_notifications is validated successfully."""
    # given a YAML file with a job missing email_notifications
    yaml_content = """
    resources:
      jobs:
        job1:
          some_other_key: "value"
    """
    yaml_file = create_yaml_file(yaml_content)
    # when we validate the YAML file
    # then it should raise a JobValidationError
    with pytest.raises(JobValidationError):
        validate_yaml(yaml_file)


def test_validate_yaml_invalid_format(create_yaml_file: Callable[[str], Path]) -> None:
    """Test that a YAML file with invalid format is not validated."""
    # given a invalid YAML file
    yaml_content = """
    invalid_yaml: [no-closed-bracket
    """
    yaml_file = create_yaml_file(yaml_content)
    # when we validate the YAML file
    # then it should raise a yaml.YAMLError
    with pytest.raises(yaml.YAMLError):
        validate_yaml(yaml_file)
