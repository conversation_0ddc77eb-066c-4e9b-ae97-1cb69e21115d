#!/bin/env python3
"""This script collects infos of all Databricks libraries."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import itertools
import json
import os
import subprocess
from dataclasses import dataclass, field
from pathlib import Path

import tomli
from packaging.requirements import Requirement
from packaging.version import Version

REPO_DIR = Path(__file__).resolve().parents[3]
LIBRARIES_DIR = REPO_DIR / "databricks/libraries"


@dataclass
class LibraryInfo:
    """Information about a library."""

    abs_path: Path
    rel_path: Path
    name: str = field(default=None)  # type: ignore[assignment]
    version: Version = field(default=None)  # type: ignore[assignment]
    old_version: Version = field(default=None)  # type: ignore[assignment]
    has_changed: bool = field(default=False)
    extras: list[str] = field(default_factory=list)
    depends_on: list[Requirement] = field(default_factory=list)
    depends_on_local: set[str] = field(default_factory=set)

    def __str__(self) -> str:
        """Return the string representation of the library."""
        result = f"""Library: {self.name}
  Path: {self.rel_path}
  Version: {self.version}
  Old Version: {self.old_version}
  Has changed: {self.has_changed}
  Extras: {self.extras}"""

        if self.depends_on:
            depends_on_str = "\n    ".join([str(dep) for dep in self.depends_on])
            result += f"\n  Depends on:\n    {depends_on_str}"

        if self.depends_on_local:
            depends_on_local_str = "\n    ".join(self.depends_on_local)
            result += f"\n  Depends on local:\n    {depends_on_local_str}"

        return result


def get_changed_files(base: str, target: str) -> list[Path]:
    """Get the files that changed between two commits."""
    return [
        Path(raw_file)
        for raw_file in subprocess.check_output(["git", "diff", "--name-only", base, target])
        .decode("utf-8")
        .splitlines()
    ]


def _find_all_libraries() -> list[Path]:
    """Find all Databricks libraries."""
    return [path for path in LIBRARIES_DIR.iterdir() if path.is_dir() and (path / "pyproject.toml").exists()]


# Library processing


def _merge_requirements(requirements: list[Requirement]) -> Requirement:
    """Merge multiple requirements with the same name into one."""
    merged = Requirement(requirements[0].name)

    for req in requirements:
        assert req.name == merged.name, "Requirements must have the same name to be merged."
        assert req.marker == merged.marker, "Requirements must have the same marker to be merged."
        merged.extras |= req.extras
        merged.specifier &= req.specifier

    return merged


def _get_version_at_commit(version_path: Path, commit: str) -> Version:
    """Get the version of a library at a specific commit."""
    cmd = ["git", "show", f"{commit}:{version_path.relative_to(REPO_DIR)}"]
    process = subprocess.run(cmd, capture_output=True, text=True)
    if process.returncode == 128:
        return Version("0.0.0")

    return Version(process.stdout.strip())


def _process_version(library: LibraryInfo, version_path: Path, base_commit: str) -> LibraryInfo:
    """Process the VERSION file."""
    if not version_path.exists():
        raise FileNotFoundError(f"VERSION file missing in {version_path.parent.relative_to(REPO_DIR)}")

    library.version = Version(version_path.read_text().strip())
    # Make sure the version has only 3 main parts
    main_version = Version(".".join(str(p) for p in library.version.release))
    assert (
        main_version == library.version
    ), f"Version must have only 3 parts in {version_path.parent.relative_to(REPO_DIR)}"

    # Get the old version
    library.old_version = _get_version_at_commit(version_path, base_commit)

    # Make sure the version stayed the same or increased
    assert (
        library.old_version <= library.version
    ), f"{lib.name} version {library.version} is greater than old version {library.old_version}"

    return library


def _process_pyproject_toml(library: LibraryInfo, pyproject_path: Path) -> LibraryInfo:
    """Process the pyproject.toml file."""
    config = tomli.loads(pyproject_path.read_text())
    dependency_sets: dict[str, list[Requirement]] = {}

    def _collect_dependencies(raw_deps: list[str]) -> None:
        """Collect depdenencies und group them by name."""
        for raw_dep in raw_deps:
            dep = Requirement(raw_dep)
            dependency_sets.setdefault(dep.name, []).append(dep)

    # Process project section
    project = config["project"]
    library.name = project["name"]
    assert (
        "version" in project["dynamic"]
    ), f"Version must be defined as dynamic in {pyproject_path.relative_to(REPO_DIR)}"
    _collect_dependencies(project.get("dependencies", []))

    # Process project.optional-dependencies section
    optional_dependencies = project.get("optional-dependencies", {})
    assert ("spark" in optional_dependencies) and ("dbx" in optional_dependencies), (
        "spark and dbx extras must be defined in [project.optional-dependencies]"
        f" in {pyproject_path.relative_to(REPO_DIR)}"
    )
    for extra, extra_deps in optional_dependencies.items():
        # Ignore dbx extra
        if extra == "dbx":
            continue

        # Add others to the extras
        library.extras.append(extra)
        # ... and to the dependencies
        _collect_dependencies(extra_deps)

    # Merge dependencies with the same name
    library.depends_on = [_merge_requirements(reqs) for reqs in dependency_sets.values()]

    # Process tool.setuptools.dynamic section
    dynamics = config["tool"]["setuptools"]["dynamic"]
    assert (
        dynamics["version"]["file"] == "VERSION"
    ), f"Version must be defined as VERSION file in [tool.setuptools.dynamic] in {pyproject_path.relative_to(REPO_DIR)}"

    # Process tool.coverage.run section
    coverage_run = config["tool"]["coverage"]["run"]
    assert (
        "command_line" in coverage_run
    ), f"Default command_line must be defined in [tool.coverage.run] in {pyproject_path.relative_to(REPO_DIR)}"

    return library


def process_library(lib_path: Path, changed_files: list[Path], base_commit: str) -> LibraryInfo:
    """Process the library and return its information."""
    library = LibraryInfo(lib_path, lib_path.relative_to(REPO_DIR))

    _process_pyproject_toml(library, lib_path / "pyproject.toml")
    _process_version(library, lib_path / "VERSION", base_commit)

    # Check if the library has changed
    library.has_changed = any(file.is_relative_to(library.rel_path) for file in changed_files)

    # Make sure version increased if the library has changed
    if library.has_changed:
        assert (
            library.version > library.old_version
        ), f"Version of {library.name} must be increased as the library has changed."

    return library


def process_local_dependencies(libraries: dict[str, LibraryInfo]) -> dict[str, LibraryInfo]:
    """Process local dependencies between libraries."""
    for lib in libraries.values():
        # Check if the local library is compatible
        for dep in lib.depends_on:
            # Ignore non-local dependencies
            if dep.name not in libraries:
                continue

            # Get dependency library
            dep_lib = libraries[dep.name]

            # Check if the local version is compatible
            if dep.specifier.contains(dep_lib.version):
                # Add the dependency to the local list
                lib.depends_on_local.add(f"{dep.name}")

    return libraries


def _create_local_pip_args(lib: LibraryInfo, libraries: dict[str, LibraryInfo]) -> str:
    """Create the local pip install arguments."""
    result = [f"./{lib.rel_path.name}[{','.join(lib.extras)}]"]

    for dep in lib.depends_on_local:
        # Create pip arguments for local dependency with all extras
        dep_lib = libraries[dep]
        result.append(f"./{dep_lib.rel_path.name}[{','.join(dep_lib.extras)}]")

    return f"-e {' -e '.join(result)}"


# Determine test and deploy


def _determine_need_test(libraries: dict[str, LibraryInfo]) -> list[LibraryInfo]:
    """Determine the libraries that need to be tested."""
    result: dict[str, LibraryInfo] = {}
    remaining = list(libraries.values())

    while remaining:
        lib = remaining.pop()

        # Test the library if it has changed
        if lib.has_changed:
            result[lib.name] = lib
            continue

        # Ignore the library if it does not have any local dependencies
        if len(lib.depends_on_local) == 0:
            continue

        # Test the library if any local dependencies are tested
        if any((dep in result) for dep in lib.depends_on_local):
            result[lib.name] = lib
            continue

        # Ignore the library if all local dependencies are not tested
        if all((dep not in result) for dep in lib.depends_on_local):
            continue

        # Otherwise, add the library to the remaining list
        remaining.insert(0, lib)

    return list(result.values())


def determine_need_deploy(libraries: dict[str, LibraryInfo]) -> list[LibraryInfo]:
    """Determine the libraries that need to be deployed."""
    return [lib for lib in libraries.values() if lib.version > lib.old_version]


# Github outputs


def _write_github_matrix(name: str, includes: list[dict[str, str]]) -> None:
    """Write the includes as GitHub Actions matrix."""
    pretty_matrix_str = json.dumps({"include": includes}, indent=4) if len(includes) > 0 else ""
    print(f"{name}={pretty_matrix_str}")

    gh_output = os.environ.get("GITHUB_OUTPUT")
    if gh_output is not None:
        with open(gh_output, "a") as fp:
            compact_matrix_str = json.dumps({"include": includes}, indent=None) if len(includes) > 0 else ""
            print(f"{name}={compact_matrix_str}", file=fp)


def write_github_test_matrix(need_test: list[LibraryInfo], python_versions: list[Version]) -> None:
    """Write the library information as GitHub Actions test matrix."""
    libs_data = [
        {
            "library": lib.name,
            "path": str(lib.rel_path),
            "local_pip_args": _create_local_pip_args(lib, libraries),
        }
        for lib in need_test
    ]

    # Combine the library data with the python versions
    includes = [
        {**lib, "python_version": str(py_version)} for lib, py_version in itertools.product(libs_data, python_versions)
    ]

    _write_github_matrix("test_matrix", includes)


def write_github_deploy_matrix(need_deploy: list[LibraryInfo], dev_version: str | None) -> None:
    """Write the library information as GitHub Actions deploy matrix."""
    includes = []
    for lib in need_deploy:
        # Set dev version if given
        if dev_version is not None:
            version = Version(f"{lib.version}.{dev_version}")
        else:
            version = lib.version

        includes.append(
            {
                "library": lib.name,
                "path": str(lib.rel_path),
                "version": str(version),
            }
        )

    _write_github_matrix("deploy_matrix", includes)


def _parse_args() -> argparse.Namespace:
    """Parse the command line arguments."""
    parser = argparse.ArgumentParser(description="Collect information about all Databricks libraries.")
    parser.add_argument(
        "--python-versions",
        nargs="+",
        default=[Version("3.10"), Version("3.11")],
        type=Version,
        help="Python versions to test against",
    )
    parser.add_argument(
        "--base",
        type=str,
        required=True,
        help="The base commit to compare against.",
    )
    parser.add_argument(
        "--target",
        type=str,
        required=True,
        help="The target commit to compare against.",
    )
    parser.add_argument(
        "--dev-version",
        default=None,
        type=str,
        help="A development version to add to all deployed libraries.",
    )
    return parser.parse_args()


if __name__ == "__main__":
    # Parse the command line arguments
    args = _parse_args()

    # Collect changed files
    changed_files = get_changed_files(args.base, args.target)

    # Collect info about all libraries
    libraries = {}
    for lib_path in _find_all_libraries():
        lib = process_library(lib_path, changed_files, args.base)
        libraries[lib.name] = lib

    # Process local dependencies
    libraries = process_local_dependencies(libraries)

    # Print all libraries
    print("Library information:")
    for lib in libraries.values():
        print(lib)

    # Determine the libraries that need tests
    need_test = _determine_need_test(libraries)
    # Determine the libraries that need to be deployed
    need_deploy = determine_need_deploy(libraries)

    # Write the Github outputs
    print("\nGitHub outputs:")
    write_github_test_matrix(need_test, args.python_versions)
    write_github_deploy_matrix(need_deploy, args.dev_version)
