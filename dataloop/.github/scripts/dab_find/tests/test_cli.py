"""Tests for the CLI module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path
from typing import Dict, Generator
from unittest.mock import Mock, patch

import pytest
from dab_find.cli import AppContext, app
from dab_find.model import Environment, ExecutionContext
from git import Repo, TagReference
from git.objects.commit import Commit
from typer.testing import CliRunner, Result


@pytest.fixture
def mock_repo() -> Mock:
    """Fixture to create a mock repository."""
    repo: Mock = Mock(spec=Repo)
    # Setup working directory
    repo.working_tree_dir = "/mock/working/dir"

    # Setup head commit and parent
    parent_commit: Mock = Mock(spec=Commit)
    parent_commit.hexsha = "parent_hash"

    head_commit: Mock = Mock(spec=Commit)
    head_commit.hexsha = "head_hash"
    head_commit.parents = [parent_commit]

    repo.head.commit = head_commit
    return repo


@pytest.fixture
def runner() -> CliRunner:
    """Fixture to create a CLI runner."""
    return CliRunner()


@pytest.fixture(autouse=True)
def mock_dependencies(mock_repo: Mock) -> Generator[Dict[str, Mock], None, None]:
    """Fixture to mock dependencies for CLI commands."""
    with (
        patch("dab_find.cli.determine_changed_bundles") as mock_determine_changes,
        patch("dab_find.cli.get_validation_bundles") as mock_get_validation,
        patch("dab_find.cli.get_deployment_bundles") as mock_get_deployment,
        patch("dab_find.cli.output_eligible_bundles") as mock_output,
        patch("dab_find.cli.Repo", return_value=mock_repo) as mock_repo_class,
        patch("dab_find.cli.find_predecessor_tag") as mock_find_predecessor,
    ):
        yield {
            "determine_changes": mock_determine_changes,
            "get_validation": mock_get_validation,
            "get_deployment": mock_get_deployment,
            "output": mock_output,
            "repo_class": mock_repo_class,
            "find_predecessor": mock_find_predecessor,
        }


def test_validation_command_no_commits(mock_dependencies: Dict[str, Mock], runner: CliRunner, mock_repo: Mock) -> None:
    """Test validation command without specifying commits."""
    # Setup mocks
    mock_dependencies["determine_changes"].return_value = ["bundle1", "bundle2"]
    mock_dependencies["get_validation"].return_value = ["filtered_bundle"]

    # Run command
    result: Result = runner.invoke(app, ["validation"], obj=AppContext(repo=mock_repo, environment=Environment.DEV))

    # Verify
    assert result.exit_code == 0, result.output
    mock_dependencies["determine_changes"].assert_called_once_with("parent_hash", "head_hash", mock_repo)
    mock_dependencies["get_validation"].assert_called_once_with(["bundle1", "bundle2"], Environment.DEV)
    mock_dependencies["output"].assert_called_once_with(["filtered_bundle"], Path("/mock/working/dir"))


def test_validation_command_with_commits(
    mock_dependencies: Dict[str, Mock], runner: CliRunner, mock_repo: Mock
) -> None:
    """Test validation command with specified commits."""
    # Run command with specific commits
    result: Result = runner.invoke(
        app,
        ["validation", "--commits", "commit1", "commit2"],
        obj=AppContext(repo=mock_repo, environment=Environment.DEV),
    )

    # Verify
    assert result.exit_code == 0, result.output
    mock_dependencies["determine_changes"].assert_called_once_with("commit1", "commit2", mock_repo)


def test_deployment_command_pr_context(mock_dependencies: Dict[str, Mock], runner: CliRunner, mock_repo: Mock) -> None:
    """Test deployment command in PR context."""
    # Setup mocks
    mock_dependencies["determine_changes"].return_value = ["bundle1", "bundle2"]
    mock_dependencies["get_deployment"].return_value = ["filtered_bundle"]

    # Run command
    result: Result = runner.invoke(
        app, ["deployment", "--context", "pr"], obj=AppContext(repo=mock_repo, environment=Environment.DEV)
    )

    # Verify
    assert result.exit_code == 0
    mock_dependencies["determine_changes"].assert_called_once_with("parent_hash", "head_hash", mock_repo)
    mock_dependencies["get_deployment"].assert_called_once_with(
        ["bundle1", "bundle2"], Environment.DEV, ExecutionContext.PR, None
    )


def test_deployment_command_release_context(
    mock_dependencies: Dict[str, Mock], runner: CliRunner, mock_repo: Mock
) -> None:
    """Test deployment command in release context."""
    # Setup tag mocks
    target_tag: Mock = Mock(spec=TagReference)
    target_tag.commit.hexsha = "target_tag_hash"
    base_tag: Mock = Mock(spec=TagReference)
    base_tag.commit.hexsha = "base_tag_hash"

    mock_repo.tags = {"v1.0": target_tag}
    mock_dependencies["find_predecessor"].return_value = base_tag

    # Run command
    result: Result = runner.invoke(
        app,
        ["deployment", "--context", "release", "--tag", "v1.0"],
        obj=AppContext(repo=mock_repo, environment=Environment.DEV),
    )

    # Verify
    assert result.exit_code == 0
    mock_dependencies["determine_changes"].assert_called_once_with("base_tag_hash", "target_tag_hash", mock_repo)
    mock_dependencies["get_deployment"].assert_called_once_with(
        mock_dependencies["determine_changes"].return_value, Environment.DEV, ExecutionContext.RELEASE, "v1.0"
    )


def test_deployment_command_invalid_context_tag_combination(runner: CliRunner, mock_repo: Mock) -> None:
    """Test invalid context and tag combination for deployment command."""
    # Test using tag with PR context
    result: Result = runner.invoke(
        app,
        ["deployment", "--context", "pr", "--tag", "v1.0"],
        obj=AppContext(repo=mock_repo, environment=Environment.DEV),
    )
    assert result.exit_code != 0
    assert "Tag can only be used with 'release' context" in str(result.output)

    # Test release context without tag
    result = runner.invoke(
        app, ["deployment", "--context", "release"], obj=AppContext(repo=mock_repo, environment=Environment.DEV)
    )
    assert result.exit_code != 0
    assert "Tag is required when context is 'release'" in str(result.output)


def test_deployment_command_merge_context(
    mock_dependencies: Dict[str, Mock], runner: CliRunner, mock_repo: Mock
) -> None:
    """Test deployment command in merge context."""
    # Setup mocks
    mock_dependencies["determine_changes"].return_value = ["bundle1", "bundle2"]
    mock_dependencies["get_deployment"].return_value = ["filtered_bundle"]

    # Run command
    result: Result = runner.invoke(
        app,
        ["deployment", "--context", "merge", "--commits", "merge_base", "merge_head"],
        obj=AppContext(repo=mock_repo, environment=Environment.DEV),
    )

    # Verify
    assert result.exit_code == 0
    mock_dependencies["determine_changes"].assert_called_once_with("merge_base", "merge_head", mock_repo)
    mock_dependencies["get_deployment"].assert_called_once_with(
        ["bundle1", "bundle2"], Environment.DEV, ExecutionContext.MERGE, None
    )


def test_main_callback(runner: CliRunner, mock_dependencies: Dict[str, Mock]) -> None:
    """Test main callback function."""
    result: Result = runner.invoke(app, ["--environment", "prod"])

    assert result.exit_code == 0
    mock_dependencies["repo_class"].assert_called_once_with(".", search_parent_directories=True)
