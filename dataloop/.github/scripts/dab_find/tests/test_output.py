"""Test the output_eligible_bundles function."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path

import pytest
from dab_find.model import BundleInfo
from dab_find.output import output_eligible_bundles
from pytest import CaptureFixture


@pytest.fixture
def setup_env_with_file(tmp_path: Path, monkeypatch: pytest.MonkeyPatch) -> Path:
    """Fixture to set up the environment for testing with GITHUB_OUTPUT set."""
    gh_output_file = tmp_path / "github_output.txt"
    monkeypatch.setenv("GITHUB_OUTPUT", str(gh_output_file))
    return gh_output_file


def test_output_eligible_bundles_with_env_var(setup_env_with_file: Path) -> None:
    """Test the output_eligible_bundles function with GITHUB_OUTPUT set."""

    # given
    eligible_bundles = [
        BundleInfo(name="bundleA", path=Path("/path/to/bundleA")),
        BundleInfo(name="bundleB", path=Path("/path/to/bundleB")),
    ]

    # when the function is called
    output_eligible_bundles(eligible_bundles, root_path=Path("/"))

    # then check the content of the file set in GITHUB_OUTPUT
    gh_matrix_content = setup_env_with_file.read_text()
    assert gh_matrix_content == (
        'matrix={"include": [{"name": "bundleA", "work-dir": "path/to/bundleA"}, '
        '{"name": "bundleB", "work-dir": "path/to/bundleB"}]}\n'
    )


def test_output_eligible_bundles_without_env_var(capsys: CaptureFixture[str], monkeypatch: pytest.MonkeyPatch) -> None:
    """Test the output_eligible_bundles function when GITHUB_OUTPUT is not set."""

    # remove the GITHUB_OUTPUT environment variable
    monkeypatch.delenv("GITHUB_OUTPUT", raising=False)

    # given
    eligible_bundles = [
        BundleInfo(name="bundleA", path=Path("/path/to/bundleA")),
        BundleInfo(name="bundleB", path=Path("/path/to/bundleB")),
    ]

    # when
    output_eligible_bundles(eligible_bundles, root_path=Path("/"))

    # then the output should be printed to stdout
    captured = capsys.readouterr()
    assert (
        'matrix={"include": [{"name": "bundleA", "work-dir": "path/to/bundleA"}, '
        '{"name": "bundleB", "work-dir": "path/to/bundleB"}]}\n'
    ) in captured.out
