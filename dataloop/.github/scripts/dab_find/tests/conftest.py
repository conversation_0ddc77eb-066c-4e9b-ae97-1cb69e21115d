"""conftest."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path

import pytest
from git import Repo


@pytest.fixture
def repo(tmp_path: Path) -> Repo:
    """Fixture for a git repository."""
    git_repo = Repo.init(tmp_path / "test-repo")
    git_repo.config_writer().set_value("user", "name", "Test User").release()
    git_repo.config_writer().set_value("user", "email", "<EMAIL>").release()
    return git_repo
