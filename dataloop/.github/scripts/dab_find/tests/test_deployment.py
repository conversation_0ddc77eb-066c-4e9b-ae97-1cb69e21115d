"""Tests for deployment module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path

import pytest
from dab_find.deployment import get_deployment_bundles
from dab_find.model import BundleInfo, Environment, ExecutionContext


@pytest.fixture
def auto_ci_yaml_no_release_template() -> str:
    """Return a template for deployment.yaml file with merge mode."""
    return """
    environment:
        {environment}:
            deployment:
                auto: {auto_deploy}
                mode: {mode}
        """


@pytest.fixture
def auto_ci_yaml_release_template() -> str:
    """Return a template for deployment.yaml file."""
    return """
environment:
    {environment}:
        deployment:
            auto: {auto_deploy}
            mode: {mode}
            release:
                tag-prefix: {tag_prefix}
    """


@pytest.fixture
def databricks_yaml_template() -> str:
    """Return a template for databricks.yaml file."""
    return """
bundle:
    name: {bundle}
    """


@pytest.fixture
def dummy_databricks_yaml(databricks_yaml_template: str) -> str:
    """Return a dummy databricks.yaml file content."""
    return databricks_yaml_template.format(bundle="Bundle A")


@pytest.fixture
def dummy_auto_ci_yaml(auto_ci_yaml_template: str) -> str:
    """Return a dummy deployment.yaml file content."""
    return auto_ci_yaml_template.format(environment="prod", auto_deploy="true", mode="merge", tag_prefix="release-")


def test_get_deployment_bundles_finds_no_bundles_if_path_does_not_contains_auto_ci_yaml(
    tmp_path: Path, dummy_databricks_yaml: str, capsys: pytest.CaptureFixture[str]
) -> None:
    """Test that no bundles are found if the path does not contain deployment.yaml."""
    # given a bundle path without deployment.yaml
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()
    (bundle_a_dir / "databricks.yml").write_text(dummy_databricks_yaml)

    # when
    result = get_deployment_bundles([bundle_a_dir], Environment.PROD, ExecutionContext.MERGE, tag=None)

    # then
    assert result == [], "Should return empty list when deployment.yaml is missing"


def test_get_deployment_bundles_fails_if_auto_ci_yaml_is_invalid(tmp_path: Path, dummy_databricks_yaml: str) -> None:
    """Test that an invalid deployment.yaml raises an error."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add invalid deployment.yaml
    (bundle_a_dir / "auto_ci.yml").write_text("abc\n    def: test")
    (bundle_a_dir / "databricks.yml").write_text(dummy_databricks_yaml)

    # when / then
    with pytest.raises(Exception):
        get_deployment_bundles([bundle_a_dir], Environment.PROD, ExecutionContext.MERGE, tag=None)


def test_get_deployment_bundles_skip_if_deployment_config_not_found(
    tmp_path: Path, databricks_yaml_template: str
) -> None:
    """Test that bundles are skipped when deployment config is missing."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml without deployment config
    (bundle_a_dir / "auto_ci.yml").write_text(
        """
environment:
    prod:
        validation:
            auto: true
    """
    )

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_deployment_bundles([bundle_a_dir], Environment.PROD, ExecutionContext.MERGE, tag=None)

    # then
    assert result == [], "Should return empty list when deployment config is missing"


def test_get_deployment_bundles_skip_if_auto_deploy_is_false(
    tmp_path: Path, auto_ci_yaml_no_release_template: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are skipped when auto-deploy is false."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml with auto-deploy: false
    (bundle_a_dir / "auto_ci.yml").write_text(
        auto_ci_yaml_no_release_template.format(environment="prod", auto_deploy="false", mode="pr")
    )

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_deployment_bundles([bundle_a_dir], Environment.PROD, ExecutionContext.MERGE, tag=None)

    # then
    assert result == [], "Should return empty list when auto-deploy is false"


def test_get_deployment_bundles_skip_if_execution_context_mismatch(
    tmp_path: Path, auto_ci_yaml_no_release_template: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are skipped when execution context doesn't match deployment mode."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml with mode: merge
    (bundle_a_dir / "auto_ci.yml").write_text(
        auto_ci_yaml_no_release_template.format(environment="prod", auto_deploy="true", mode="merge")
    )

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_deployment_bundles(
        [bundle_a_dir], Environment.PROD, ExecutionContext.RELEASE, "release-1.0.0"  # Mismatch with mode: merge
    )

    # then
    assert result == [], "Should return empty list when execution context doesn't match mode"


def test_get_deployment_bundles_skip_if_tag_prefix_mismatch(
    tmp_path: Path, auto_ci_yaml_release_template: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are skipped when tag prefix doesn't match in release context."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml with tag_prefix: release-
    (bundle_a_dir / "auto_ci.yml").write_text(
        auto_ci_yaml_release_template.format(
            environment="prod", auto_deploy="true", mode="release", tag_prefix="release-"
        )
    )

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_deployment_bundles(
        [bundle_a_dir], Environment.PROD, ExecutionContext.RELEASE, tag="hotfix-1.0.0"  # Wrong prefix
    )

    # then
    assert result == [], "Should return empty list when tag prefix doesn't match"


def test_get_deployment_bundles_succeeds_with_valid_configuration(
    tmp_path: Path, auto_ci_yaml_no_release_template: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are found with valid configuration."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml with valid config
    (bundle_a_dir / "auto_ci.yml").write_text(
        auto_ci_yaml_no_release_template.format(environment="prod", auto_deploy="true", mode="merge")
    )

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_deployment_bundles([bundle_a_dir], Environment.PROD, ExecutionContext.MERGE, tag=None)

    # then
    assert len(result) == 1, "Should find exactly one bundle"
    assert result[0] == BundleInfo(name="Bundle A", path=bundle_a_dir)


@pytest.mark.parametrize("context", [ExecutionContext.MERGE, ExecutionContext.PR])
def test_get_deployment_bundles_finds_multiple_valid_bundles_for_no_release_context(
    tmp_path: Path, auto_ci_yaml_no_release_template: str, databricks_yaml_template: str, context: ExecutionContext
) -> None:
    """Test that multiple bundles are found when they all have valid configuration."""
    # given
    bundles_dir = tmp_path / "databricks/pipelines"
    bundles_dir.mkdir(parents=True)
    bundle_a_dir = bundles_dir / "bundleA"
    bundle_b_dir = bundles_dir / "bundleB"
    bundle_a_dir.mkdir()
    bundle_b_dir.mkdir()

    # add deployment.yaml to both bundles
    auto_ci_yaml = auto_ci_yaml_no_release_template.format(environment="prod", auto_deploy="true", mode=context.value)
    (bundle_a_dir / "auto_ci.yml").write_text(auto_ci_yaml)
    (bundle_b_dir / "auto_ci.yml").write_text(auto_ci_yaml)

    # add databricks.yaml to both bundles
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))
    (bundle_b_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle B"))

    # when
    result = get_deployment_bundles([bundle_a_dir, bundle_b_dir], Environment.PROD, context, tag=None)

    # then
    assert len(result) == 2, "Should find exactly two bundles"
    assert result == [
        BundleInfo(name="Bundle A", path=bundle_a_dir),
        BundleInfo(name="Bundle B", path=bundle_b_dir),
    ]


def test_get_deployment_bundles_filter_bundles_based_on_tag_prefix(
    tmp_path: Path, auto_ci_yaml_release_template: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are filtered based on tag prefix in release context."""
    # given
    bundles_dir = tmp_path / "databricks/pipelines"
    bundles_dir.mkdir(parents=True)
    bundle_a_dir = bundles_dir / "bundleA"
    bundle_b_dir = bundles_dir / "bundleB"
    bundle_a_dir.mkdir()
    bundle_b_dir.mkdir()

    # add deployment.yaml to both bundles
    bundle_a_auto_ci_yaml = auto_ci_yaml_release_template.format(
        environment="prod", auto_deploy="true", mode="release", tag_prefix="bundle_a-"
    )
    (bundle_a_dir / "auto_ci.yml").write_text(bundle_a_auto_ci_yaml)

    bundle_b_auto_ci_yaml = auto_ci_yaml_release_template.format(
        environment="prod", auto_deploy="true", mode="release", tag_prefix="bundle_b-"
    )
    (bundle_b_dir / "auto_ci.yml").write_text(bundle_b_auto_ci_yaml)

    # add databricks.yaml to both bundles
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))
    (bundle_b_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle B"))

    # when
    result = get_deployment_bundles(
        [bundle_a_dir, bundle_b_dir], Environment.PROD, ExecutionContext.RELEASE, tag="bundle_a-1.0.0"
    )

    # then
    assert result == [BundleInfo(name="Bundle A", path=bundle_a_dir)]


def test_get_deployment_bundles_fails_on_release_if_tag_not_passed(
    tmp_path: Path, auto_ci_yaml_release_template: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are filtered based on tag prefix in release context."""
    # given
    bundles_dir = tmp_path / "databricks/pipelines"
    bundles_dir.mkdir(parents=True)
    bundle_a_dir = bundles_dir / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml to both bundles
    bundle_a_auto_ci_yaml = auto_ci_yaml_release_template.format(
        environment="prod", auto_deploy="true", mode="release", tag_prefix="bundle_a-"
    )
    (bundle_a_dir / "auto_ci.yml").write_text(bundle_a_auto_ci_yaml)

    # add databricks.yaml to both bundles
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when / then
    with pytest.raises(AssertionError, match="Tag must be provided for release deployment"):
        _ = get_deployment_bundles([bundle_a_dir], Environment.PROD, ExecutionContext.RELEASE, tag=None)
