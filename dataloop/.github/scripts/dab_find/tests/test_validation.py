"""Tests for the get_validation_bundles module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path

import pytest
from dab_find.model import BundleInfo, Environment
from dab_find.validation import get_validation_bundles
from pytest import CaptureFixture


@pytest.fixture
def auto_ci_yaml_template() -> str:
    """Return a template for deployment.yaml file."""
    return """
environment:
    {environment}:
        validation:
            auto: {auto_validate}
    """


@pytest.fixture
def databricks_yaml_template() -> str:
    """Return a template for databricks.yaml file."""
    return """
bundle:
    name: {bundle}
    """


@pytest.fixture
def dummy_databricks_yaml(databricks_yaml_template: str) -> str:
    """Return a dummy databricks.yaml file content."""
    return databricks_yaml_template.format(bundle="Bundle A")


@pytest.fixture
def dummy_auto_ci_yaml(auto_ci_yaml_template: str) -> str:
    """Return a dummy deployment.yaml file content."""
    return auto_ci_yaml_template.format(environment="dev", auto_validate="true")


def test_get_validation_bundles_finds_no_bundles_if_path_does_not_contains_auto_ci_yaml(
    tmp_path: Path, dummy_databricks_yaml: str, capsys: CaptureFixture[str]
) -> None:
    """Test that no bundles are found if the path does not contain deployment.yaml."""
    # given a bundle path without deployment.yaml
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    (bundle_a_dir / "databricks.yml").write_text(dummy_databricks_yaml)

    # when
    result = get_validation_bundles([bundle_a_dir], Environment.DEV)

    # then
    assert result == [], "Should return empty list when deployment.yaml is missing"
    assert "Skipping: Bundle" in capsys.readouterr().out, "Should log a message when deployment.yaml is missing"


def test_get_validation_bundles_fails_if_auto_ci_yaml_is_invalid(tmp_path: Path, dummy_databricks_yaml: str) -> None:
    """Test that an invalid deployment.yaml raises an error."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add invalid deployment.yaml to bundleA
    (bundle_a_dir / "auto_ci.yml").write_text("abc\n    def: test")
    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(dummy_databricks_yaml)

    # when / then
    with pytest.raises(Exception):
        get_validation_bundles([bundle_a_dir], Environment.DEV)


def test_get_validation_bundles_raises_error_if_databricks_yaml_is_not_present(
    tmp_path: Path, dummy_auto_ci_yaml: str
) -> None:
    """Test that no bundles are found if databricks.yaml is missing."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml to bundleA but does not add databricks.yaml
    (bundle_a_dir / "auto_ci.yml").write_text(dummy_auto_ci_yaml)

    # when/ then
    with pytest.raises(ValueError, match="Bundle .* does not have a databricks.yml file"):
        get_validation_bundles([bundle_a_dir], Environment.DEV)


def test_get_validation_bundles_finds_bundles_if_both_deployment_and_databricks_yaml_are_present(
    tmp_path: Path, dummy_auto_ci_yaml: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are found when both required files are present."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml
    (bundle_a_dir / "auto_ci.yml").write_text(dummy_auto_ci_yaml)

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_validation_bundles([bundle_a_dir], Environment.DEV)

    # then
    assert len(result) == 1, "Should find exactly one bundle"
    assert result[0] == BundleInfo(name="Bundle A", path=bundle_a_dir)


def test_get_validation_bundles_finds_bundles_if_both_deployment_and_databricks_yaml_are_present_for_multiple_bundles(
    tmp_path: Path, auto_ci_yaml_template: str, databricks_yaml_template: str
) -> None:
    """Test that multiple bundles are found when they all have the required files."""
    # given
    bundles_dir = tmp_path / "databricks/pipelines"
    bundles_dir.mkdir(parents=True)
    bundle_a_dir = bundles_dir / "bundleA"
    bundle_a_dir.mkdir()
    bundle_b_dir = bundles_dir / "bundleB"
    bundle_b_dir.mkdir()

    # add deployment.yaml to both bundles
    (bundle_a_dir / "auto_ci.yml").write_text(auto_ci_yaml_template.format(environment="dev", auto_validate="true"))
    (bundle_b_dir / "auto_ci.yml").write_text(auto_ci_yaml_template.format(environment="dev", auto_validate="true"))

    # add databricks.yaml to both bundles
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))
    (bundle_b_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle B"))

    # when
    result = get_validation_bundles([bundle_a_dir, bundle_b_dir], Environment.DEV)

    # then
    assert len(result) == 2, "Should find exactly two bundles"
    assert result == [
        BundleInfo(name="Bundle A", path=bundle_a_dir),
        BundleInfo(name="Bundle B", path=bundle_b_dir),
    ]


def test_get_validation_bundles_fails_if_there_is_mismatch_between_environment_option_and_auto_ci_yaml(
    tmp_path: Path, auto_ci_yaml_template: str, databricks_yaml_template: str, capsys: CaptureFixture[str]
) -> None:
    """Test that an error is raised when the environment doesn't match deployment.yaml."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml to bundleA but with environment as prod
    (bundle_a_dir / "auto_ci.yml").write_text(auto_ci_yaml_template.format(environment="prod", auto_validate="true"))

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_validation_bundles([bundle_a_dir], Environment.DEV)

    # then
    assert result == []
    assert "Skipping: Environment DEV not found in" in capsys.readouterr().out


def test_get_validation_bundles_skip_bundle_if_auto_deploy_is_false_in_auto_ci_yaml(
    tmp_path: Path, auto_ci_yaml_template: str, databricks_yaml_template: str
) -> None:
    """Test that bundles are skipped when auto-deploy is false."""
    # given
    bundle_a_dir = tmp_path / "bundleA"
    bundle_a_dir.mkdir()

    # add deployment.yaml to bundleA with auto-deploy: false
    (bundle_a_dir / "auto_ci.yml").write_text(auto_ci_yaml_template.format(environment="dev", auto_validate="false"))

    # add databricks.yaml
    (bundle_a_dir / "databricks.yml").write_text(databricks_yaml_template.format(bundle="Bundle A"))

    # when
    result = get_validation_bundles([bundle_a_dir], Environment.DEV)

    # then
    assert result == [], "Should return empty list when auto-deploy is false"
