"""Tests for the determine_changes.bundles module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path
from tempfile import TemporaryDirectory

from dab_find.bundles import find_yaml


def test_find_yaml_yaml_exists() -> None:
    """Test that the .yaml file is found."""
    with TemporaryDirectory() as directory:
        p = Path(directory)
        (p / "auto_ci.yaml").write_text("test")
        assert find_yaml(p, "auto_ci") is not None


def test_find_yaml_missing() -> None:
    """Test that the function returns None if the file is missing."""
    with TemporaryDirectory() as directory:
        p = Path(directory)
        assert find_yaml(p, "auto_ci") is None


def test_find_yaml_yml_exists() -> None:
    """Test that the .yml file is found."""
    with TemporaryDirectory() as directory:
        p = Path(directory)
        (p / "auto_ci.yml").write_text("test")
        assert find_yaml(p, "auto_ci") is not None


def test_find_yaml_yaml_and_yml_exist() -> None:
    """Test that the .yaml file is preferred over the .yml file."""
    with TemporaryDirectory() as directory:
        p = Path(directory)
        (p / "auto_ci.yml").write_text("test")
        (p / "auto_ci.yaml").write_text("test")
        assert find_yaml(p, "auto_ci") == p / "auto_ci.yaml"
