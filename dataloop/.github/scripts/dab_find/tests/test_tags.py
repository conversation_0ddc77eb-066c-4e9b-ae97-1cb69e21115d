"""Tests for the tags module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import pytest
from dab_find.tags import find_predecessor_tag
from git import Actor, Repo
from pytest import CaptureFixture


def test_find_predecessor_tag_raises_value_error_if_target_tag_has_wrong_name_format(
    repo: Repo,
) -> None:
    """Test that find predecessor tag raises a ValueError if the target tag has the wrong name format."""

    # given a repo with a tag that does not match the expected name format
    repo.index.commit("initial commit")
    repo.create_tag("some_tag", message="tag message")

    # when we try to find the predecessor tag
    with pytest.raises(ValueError) as e:
        find_predecessor_tag(repo.tags["some_tag"], repo)

    # then it should raise a ValueError
    assert str(e.value) == 'Tag "some_tag" does not match the expected format "prefix?_?vX.Y.Z"'


def test_find_predecessor_tag_raises_value_error_if_target_tag_has_no_predecessor(
    repo: Repo,
) -> None:
    """Test that find predecessor tag raises a ValueError if the target tag has no predecessor."""

    # given a repo with a tag that matches the expected name format
    repo.index.commit("initial commit", committer=Actor("committer", "<EMAIL>"))
    repo.create_tag("some_prefix_v1.0.0", message="tag message")

    # when we try to find the predecessor tag
    with pytest.raises(ValueError) as e:
        find_predecessor_tag(repo.tags["some_prefix_v1.0.0"], repo)

    # then it should raise a ValueError
    assert str(e.value) == 'Tag "some_prefix_v1.0.0" has no predecessor'


def test_find_predecessor_tag(repo: Repo) -> None:
    """Test that find predecessor tag should find the predecessor."""

    # given a repo with two tags
    repo.index.commit("initial commit", committer=Actor("committer", "<EMAIL>"))
    repo.create_tag("some_prefix_v1.0.0", message="tag message")

    repo.index.commit("another commit", committer=Actor("committer", "<EMAIL>"))
    repo.create_tag("some_prefix_v1.0.1", message="tag message")

    # when we try to find the predecessor tag
    found_tag = find_predecessor_tag(repo.tags["some_prefix_v1.0.1"], repo)

    # then the previous tag should be found
    assert found_tag == repo.tags["some_prefix_v1.0.0"]


def test_find_predecessor_tag_skips_over_tags_that_do_not_match_expected_name_format(
    repo: Repo, capsys: CaptureFixture[str]
) -> None:
    """Test that find predecessor tag should skip over tags that do not match the expected name format."""

    # given a repo with at least one tag that does not match the expected name format
    repo.index.commit("initial commit", committer=Actor("committer", "<EMAIL>"))
    repo.create_tag("some_tag", message="tag message")
    repo.create_tag("some_prefix_v1.0.0", message="tag message")

    repo.index.commit("another commit", committer=Actor("committer", "<EMAIL>"))
    repo.create_tag("some_prefix_v1.0.1", message="tag message")

    # when we try to find the predecessor tag
    found_tag = find_predecessor_tag(repo.tags["some_prefix_v1.0.1"], repo)

    # then the previous tag should be found
    assert found_tag == repo.tags["some_prefix_v1.0.0"]
    captured = capsys.readouterr()
    assert "Skipping tag some_tag" in captured.out
