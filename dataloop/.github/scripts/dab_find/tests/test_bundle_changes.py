"""Tests for the determine_changes.bundle_changes module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path

import pytest
from dab_find.bundle_changes import determine_changed_bundles
from git import Repo


def test_bundle_changes_raises_assertion_error_when_repo_has_no_working_tree(tmp_path: Path) -> None:
    """Test that determine_changed_bundles raises an AssertionError when the repository has no working tree."""
    # given a repo with a commit
    repo = Repo.init(tmp_path / "test-repo", bare=True)

    # when we try to determine the changed bundles between two commits
    with pytest.raises(AssertionError) as e:
        determine_changed_bundles("HEAD", "HEAD", repo)

    # then it should raise an AssertionError
    assert str(e.value) == "Repository has no working tree"


def test_bundle_changes_raises_value_error_when_base_commit_do_not_exist_(repo: Repo) -> None:
    """Test that determine_changed_bundles raises a ValueError when the base commit does not exist."""
    # given an empty repo

    # when we try to determine the changed bundles between two commits
    with pytest.raises(ValueError) as e:
        determine_changed_bundles("does-not-exist", "not-important", repo)

    # then it should raise a ValueError with the missing commit
    assert str(e.value) == "Commit does-not-exist not found in repository"


def test_bundle_changes_raises_value_error_when_target_commit_do_not_exist(repo: Repo) -> None:
    """Test that determine_changed_bundles raises a ValueError when the target commit does not exist."""
    # given a repo with a commit
    repo.index.commit("initial commit")

    # when we try to determine the changed bundles between two commits
    with pytest.raises(ValueError) as e:
        determine_changed_bundles("HEAD", "does-not-exist", repo)

    # then it should raise a ValueError with the missing commit
    assert str(e.value) == "Commit does-not-exist not found in repository"


def test_bundle_changes_returns_empty_dict_when_no_bundle_files_changed(repo: Repo) -> None:
    """Test that determine_changed_bundles returns an empty dict when no bundle files have changed."""
    # given a repo with two commits
    # to file that are not in bundle paths
    assert repo.working_tree_dir is not None
    repo_dir = Path(repo.working_tree_dir)
    (repo_dir / "file").write_text("content")

    repo.index.add(["file"])
    repo.index.commit("base commit")

    (repo_dir / "file").write_text("changed content")
    repo.index.add(["file"])
    repo.index.commit("target commit")

    # when we try to determine the changed bundles between two commits
    changed_bundles = determine_changed_bundles("HEAD~1", "HEAD", repo)

    # then we should get an empty list
    assert changed_bundles == []


def test_bundle_changes_returns_changed_bundles(repo: Repo) -> None:
    """Test that determine_changed_bundles returns the changed bundles."""
    # given a repo with two commits
    # to file that are in bundle paths
    assert repo.working_tree_dir is not None
    repo_dir = Path(repo.working_tree_dir)

    Path.mkdir(repo_dir / "databricks/pipelines/bundle1", parents=True)
    # bundle file is mandatory
    (repo_dir / "databricks/pipelines/bundle1/databricks.yml").touch()

    repo.index.add(["databricks/pipelines/bundle1/databricks.yml"])
    repo.index.commit("base commit")

    (repo_dir / "databricks/pipelines/bundle1/some_file.py").touch()
    repo.index.add(["databricks/pipelines/bundle1/some_file.py"])
    repo.index.commit("target commit")

    # when we try to determine the changed bundles between two commits
    changed_bundles = determine_changed_bundles("HEAD~1", "HEAD", repo)

    # then we should get the changed bundle
    assert changed_bundles == [repo.working_tree_dir / Path("databricks/pipelines/bundle1")]
