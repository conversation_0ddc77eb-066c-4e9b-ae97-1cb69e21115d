"""This module contains the AutoCIConfig model and the function to load the config from a file."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from enum import Enum
from pathlib import Path

import yaml
from pydantic import BaseModel, Field, model_validator  # type: ignore


class Mode(str, Enum):
    """Enumeration of the supported deployment modes."""

    PR = "pr"
    MERGE = "merge"
    RELEASE = "release"


class ValidationConfig(BaseModel):
    """Model class for the validation configuration."""

    auto: bool


class ReleaseConfig(BaseModel):
    """Model class for the release configuration."""

    tag_prefix: str = Field(..., alias="tag-prefix")


class DeploymentConfig(BaseModel):
    """Model class for the deployment configuration."""

    auto: bool
    mode: Mode = Mode.PR
    release: ReleaseConfig | None = None

    @model_validator(mode="after")
    def validate_release_config(self) -> "DeploymentConfig":
        """Validate the release config based on the deployment mode."""
        if self.mode == Mode.RELEASE and not self.release:
            raise ValueError("Release config is required when mode is 'release'")
        if self.mode != Mode.RELEASE and self.release:
            raise ValueError("Release config should only be provided when mode is 'release'")
        return self


class TargetConfig(BaseModel):
    """Model class for the target configuration."""

    validation: ValidationConfig | None = None
    deployment: DeploymentConfig | None = None


class EnvironmentConfig(BaseModel):
    """Model class for the environment configuration."""

    dev: TargetConfig | None = None
    qa: TargetConfig | None = None
    prod: TargetConfig | None = None


class AutoCIConfig(BaseModel):
    """Model class for the deployment configuration."""

    environment: EnvironmentConfig


def load_auto_ci_config(file_path: Path) -> AutoCIConfig:
    """Load the deployment configuration from the given file."""
    data = yaml.safe_load(file_path.read_text())
    return AutoCIConfig(**data)
