"""Model classes for the filter_eligible_bundles script."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections import namedtuple
from enum import Enum


class Environment(str, Enum):
    """Enumeration of the supported environments."""

    DEV = "dev"
    QA = "qa"
    PROD = "prod"


class ExecutionContext(str, Enum):
    """Enumeration of the supported execution contexts."""

    PR = "pr"
    MERGE = "merge"
    RELEASE = "release"


BundleInfo = namedtuple("BundleInfo", ["name", "path"])
