"""Module for working with the config files of a bundle."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from dataclasses import dataclass
from pathlib import Path
from typing import Any

import yaml
from dab_find.auto_ci_config import AutoCIConfig, TargetConfig, load_auto_ci_config
from dab_find.model import Environment
from typing_extensions import Self


def find_yaml(directory: Path, name: str) -> Path | None:
    r"""Find the YAML file with the given name in the bundle directory.

    I.e. it will find any file matching name\.y(a)?ml in regex notition in that directory preferring the .yaml one.
    """
    candidates = list(directory.glob(f"{name}.y*ml"))
    candidates.sort()
    yaml_file = next(iter([x for x in candidates if x.name in [f"{name}.yml", f"{name}.yaml"]]), None)
    print(yaml_file)
    if yaml_file and yaml_file.exists():
        return yaml_file
    return None


@dataclass(frozen=True)
class BundleConfig:
    """Value object representing a bundle's deployment and Databricks configurations."""

    deployment_config: AutoCIConfig
    databricks_config: dict[str, Any]

    @classmethod
    def from_path(cls, bundle_path: Path) -> Self | None:
        """Create a BundleConfig from a bundle directory path.

        Returns None if required files don't exist.
        """
        auto_ci_yaml = find_yaml(bundle_path, "auto_ci")
        if not auto_ci_yaml:
            print(f"Skipping: Bundle {bundle_path} does not have a auto_ci.y(a)ml file")
            return None

        databricks_yaml = find_yaml(bundle_path, "databricks")
        if not databricks_yaml:
            raise ValueError(f"Bundle {bundle_path} does not have a databricks.yml file")

        print("\nBundle YAML files:")
        print(f"  {auto_ci_yaml}")
        print(f"  {databricks_yaml}")

        deployment_config = load_auto_ci_config(auto_ci_yaml)
        databricks_config = yaml.safe_load(databricks_yaml.read_text())
        return cls(deployment_config=deployment_config, databricks_config=databricks_config)

    @property
    def bundle_name(self) -> str:
        """Return the bundle name."""
        return self.databricks_config["bundle"]["name"]

    def target_config(self, environment: Environment) -> TargetConfig | None:
        """Return the TargetConfig for the given environment."""
        match environment:
            case Environment.DEV:
                if self.deployment_config.environment.dev is None:
                    print(f"Skipping: Environment DEV not found in {self.deployment_config}")
                    return None

                return self.deployment_config.environment.dev
            case Environment.QA:
                if self.deployment_config.environment.qa is None:
                    print(f"Skipping: Environment QA not found in {self.deployment_config}")
                    return None

                return self.deployment_config.environment.qa
            case Environment.PROD:
                if self.deployment_config.environment.prod is None:
                    print(f"Skipping: Environment PROD not found in {self.deployment_config}")
                    return None

                return self.deployment_config.environment.prod
            case _:
                raise ValueError(f"Invalid environment: {environment}")


def bundle_target(bundle_path: Path, environment: Environment) -> tuple[str, TargetConfig] | None:
    """Return the target config for the given environment."""
    bundle_config = BundleConfig.from_path(bundle_path)
    if not bundle_config:
        return None

    target_config = bundle_config.target_config(environment)
    if not target_config:
        return None

    return bundle_config.bundle_name, target_config
