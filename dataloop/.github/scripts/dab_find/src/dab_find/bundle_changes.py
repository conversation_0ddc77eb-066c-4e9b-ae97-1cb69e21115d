#!/bin/env python3
"""This script determines the changes between either two commits or a tag and its predecessor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path

from git import Repo
from gitdb.exc import BadName


def determine_changed_bundles(base: str, target: str, repo: Repo) -> list[Path]:
    """Determine the changed bundles between two commits."""
    tree_dir = repo.working_tree_dir
    assert tree_dir is not None, "Repository has no working tree"

    # sanity check that the base and target commits exist
    # repo.commit is meant to get a commit object from a commit hash
    # if the commit hash does not exist, it will raise a BadName exception
    try:
        _ = repo.commit(base)
    except BadName:
        raise ValueError(f"Commit {base} not found in repository")

    try:
        _ = repo.commit(target)
    except BadName:
        raise ValueError(f"Commit {target} not found in repository")

    changed_files_in_bundles = {filepath for filepath in _get_changed_files(base, target, repo)}

    print("\nChanged files in bundles:")
    print(*changed_files_in_bundles, sep="\n")

    available_bundles = _find_available_bundles(repo_root=Path(tree_dir))

    changed_bundles = {
        available_bundle
        for available_bundle in available_bundles
        for changed_file_in_bundle in changed_files_in_bundles
        if changed_file_in_bundle.is_relative_to(available_bundle)
    }

    return list({tree_dir / filepath for filepath in changed_bundles})


def _find_available_bundles(repo_root: Path) -> set[Path]:
    """Find available bundles in the repository."""
    return {Path(filename).relative_to(repo_root).parent for filename in repo_root.glob("**/databricks.y*ml")}


def _get_changed_files(base: str, target: str, repo: Repo) -> list[Path]:
    """Get the changed files between two commits."""
    # when new tags are created, there is no predecessor, so base and target are the same
    if base == target:
        commit = repo.commit(base)
        diff_files = commit.stats.files
    else:
        diff_files = repo.git.diff("--name-only", base, target).splitlines()

    return [Path(filepath) for filepath in diff_files]
