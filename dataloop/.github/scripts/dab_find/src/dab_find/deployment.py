"""Find bundles for deployment."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path

from dab_find.bundles import bundle_target
from dab_find.model import BundleInfo, Environment, ExecutionContext


def get_deployment_bundles(
    bundle_paths: list[Path], environment: Environment, context: ExecutionContext, tag: str | None
) -> list[BundleInfo]:
    """Return the bundles that are eligible for deployment."""
    return [
        bundle_info
        for bundle_path in bundle_paths
        if (bundle_info := _get_deployment_bundle(bundle_path, environment, context, tag)) is not None
    ]


def _get_deployment_bundle(
    bundle_path: Path, environment: Environment, context: ExecutionContext, tag: str | None
) -> BundleInfo | None:
    result = bundle_target(bundle_path, environment)
    if not result:
        return None

    bundle_name, target_config = result

    if not target_config.deployment:
        print(f"Skipping: Deployment config not found for {bundle_path}")
        return None

    if not target_config.deployment.auto:
        return None

    # context is about a CLI option
    # and mode is about the deployment mode as defined in yml file
    # so keeping them as separate definitions
    if target_config.deployment.mode.value != context.value:
        return None

    if context == ExecutionContext.RELEASE:
        assert tag is not None, "Tag must be provided for release deployment"
        if target_config.deployment.release is not None and target_config.deployment.release.tag_prefix not in tag:
            print(f"Skipping: Tag prefix not found in {tag}")
            return None

    return BundleInfo(name=bundle_name, path=bundle_path)
