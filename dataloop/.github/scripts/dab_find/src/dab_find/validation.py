"""This module provides functionality to find the eligible bundles for the given environment."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path

from dab_find.bundles import bundle_target
from dab_find.model import BundleInfo, Environment


def get_validation_bundles(bundle_paths: list[Path], environment: Environment) -> list[BundleInfo]:
    """Return the bundles that are eligible for validation."""
    print(f"\nChecking eligibility of bundles {bundle_paths} for environment {environment}")
    return [
        bundle_info
        for changed_bundle_path in bundle_paths
        if (bundle_info := _get_validation_bundle(changed_bundle_path, environment)) is not None
    ]


def _get_validation_bundle(bundle_path: Path, environment: Environment) -> BundleInfo | None:
    print(f"\nGetting bundle from {bundle_path} for environment {environment}")
    result = bundle_target(bundle_path, environment)
    if not result:
        return None

    bundle_name, target_config = result
    if not target_config.validation:
        print(f"Skipping: Validation config not found in {bundle_path}")
        return None

    if not target_config.validation.auto:
        return None

    return BundleInfo(name=bundle_name, path=bundle_path)
