#!/bin/env python3
"""CLI that find bundles that are auto deployable."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from dataclasses import dataclass
from pathlib import Path
from typing import Annotated

import typer
from dab_find.bundle_changes import determine_changed_bundles
from dab_find.deployment import get_deployment_bundles
from dab_find.model import Environment, ExecutionContext
from dab_find.output import output_eligible_bundles
from dab_find.tags import find_predecessor_tag
from dab_find.validation import get_validation_bundles
from git import Repo


@dataclass(frozen=True)
class AppContext:
    """Application context containing repository and environment information."""

    repo: Repo
    environment: Environment


app = typer.Typer()


def get_commit_pair(repo: Repo) -> tuple[str, str]:
    """Get the current and parent commit hashes from the repository."""
    try:
        return repo.head.commit.parents[0].hexsha, repo.head.commit.hexsha
    except IndexError:
        print("No parent commit found. Exiting.")
        raise typer.Exit(code=1)


def validate_tag(tag: str | None, context: ExecutionContext) -> str | None:
    """Validate tag based on execution context."""
    match (context, tag):
        case (ExecutionContext.RELEASE, None):
            raise typer.BadParameter("Tag is required when context is 'release'")
        case (_, str()) if context != ExecutionContext.RELEASE:
            raise typer.BadParameter("Tag can only be used with 'release' context")
        case _:
            return tag


def validate_commits(commits: tuple[str, str], context: ExecutionContext) -> tuple[str, str]:
    """Validate commits based on execution context."""
    if context == ExecutionContext.RELEASE and any(commits):
        raise typer.BadParameter("Commits can only be used with 'pr' or 'merge' contexts")
    return commits


@app.command()
def validation(
    ctx: typer.Context,
    commits: Annotated[tuple[str, str], typer.Option(help="Base and Target commits")] = ("", ""),
) -> None:
    """Find bundles for validation."""
    app_ctx: AppContext = ctx.obj
    assert app_ctx.repo.working_tree_dir is not None, "Repository has no working tree"

    base, target = commits if all(commits) else get_commit_pair(app_ctx.repo)

    print(f"Find bundles for validation. Using commits {base} to {target} " f"and environment {app_ctx.environment}.")

    changed_bundles = determine_changed_bundles(base, target, app_ctx.repo)
    filtered_bundles = get_validation_bundles(changed_bundles, app_ctx.environment)
    output_eligible_bundles(filtered_bundles, Path(app_ctx.repo.working_tree_dir))


@app.command()
def deployment(
    ctx: typer.Context,
    context: Annotated[
        ExecutionContext, typer.Option(help="The CI context this command is running in")
    ] = ExecutionContext.PR,
    tag: Annotated[
        str | None,
        typer.Option(
            callback=lambda ctx, param, value: validate_tag(value, ctx.params.get("context")),
            help="Release tag (required for release context)",
        ),
    ] = None,
    commits: Annotated[
        tuple[str, str],
        typer.Option(
            callback=lambda ctx, param, value: validate_commits(value, ctx.params.get("context")),
            help="Base and Target commits (required for pr/merge contexts)",
        ),
    ] = ("", ""),
) -> None:
    """Find bundles for deployment."""
    app_ctx: AppContext = ctx.obj
    assert app_ctx.repo.working_tree_dir is not None, "Repository has no working tree"

    if context == ExecutionContext.RELEASE:
        assert tag is not None, "Tag is required for release context"
        target_tag = app_ctx.repo.tags[tag]
        try:
            base_tag = find_predecessor_tag(target_tag, app_ctx.repo)
            print(f"Analyzing changes for tags from {base_tag} to {target_tag}")
            base, target = base_tag.commit.hexsha, target_tag.commit.hexsha
        except ValueError:
            print(f"Tag {tag} has no predecessor")
            base = target = target_tag.commit.hexsha
    else:
        base, target = commits if all(commits) else get_commit_pair(app_ctx.repo)

    changed_bundles = determine_changed_bundles(base, target, app_ctx.repo)
    filtered_bundles = get_deployment_bundles(changed_bundles, app_ctx.environment, context, tag)
    output_eligible_bundles(filtered_bundles, Path(app_ctx.repo.working_tree_dir))


@app.callback(invoke_without_command=True)
def main(
    ctx: typer.Context,
    environment: Annotated[
        Environment, typer.Option(help="Which workspace environment this applies to")
    ] = Environment.DEV,
) -> None:
    """Find bundles that are auto deployable."""
    repo = Repo(".", search_parent_directories=True)
    ctx.obj = AppContext(repo=repo, environment=environment)


if __name__ == "__main__":
    app()
