"""Module with functionality to find tag's predecessor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import bisect
import re
from collections import defaultdict, namedtuple

from git import Repo, TagReference
from packaging.version import Version

Tag = namedtuple("Tag", ["prefix", "version"])


def find_predecessor_tag(tag: TagReference, repo: Repo) -> TagReference:
    """Find the predecessor tag of the given tag."""
    current_tag = _parse_tag(tag.name)

    tag_dict: defaultdict[str, list[tuple[Version, TagReference]]] = defaultdict(list)

    for each_tag in repo.tags:
        try:
            prefix, version = _parse_tag(each_tag.name)
        except ValueError as e:
            print(f"Skipping tag {each_tag.name}: {e}")
            continue

        bisect.insort(tag_dict[prefix], (version, each_tag), key=lambda x: x[0])

    related_tags = tag_dict[current_tag.prefix]

    position = bisect.bisect_left(related_tags, current_tag.version, key=lambda x: x[0])
    if position == 0:
        raise ValueError(f'Tag "{tag.name}" has no predecessor')

    previous_tag = related_tags[position - 1]
    return previous_tag[1]


def _parse_tag(tag: str) -> Tag:
    """Parse a tag into a prefix and a version."""
    match = re.match(r"^(.*?)_?(v\d+\.\d+\.\d+)$", tag)
    if match is None:
        raise ValueError(f'Tag "{tag}" does not match the expected format "prefix?_?vX.Y.Z"')

    prefix, version = match.groups()

    return Tag(prefix, Version(version))
