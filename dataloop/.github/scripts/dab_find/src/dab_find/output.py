"""Module to output found bundles as text."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import json
import os
from pathlib import Path
from pprint import pprint

from dab_find.model import BundleInfo


def output_eligible_bundles(eligible_bundles: list[BundleInfo], root_path: Path) -> None:
    """Output the changed bundles."""
    eligible_bundles_as_dict = {bundle.name: bundle.path.relative_to(root_path) for bundle in eligible_bundles}
    print("\nEligible bundles:")
    print("\n".join(eligible_bundles_as_dict))
    _write_github_output(eligible_bundles_as_dict)


def _write_github_output(eligible_bundles: dict[str, str]) -> None:
    """Write the changed bundles to the GitHub output."""
    gh_matrix = {"include": [{"name": bundle, "work-dir": str(path)} for bundle, path in eligible_bundles.items()]}

    print("\nGitHub matrix:")
    pprint(gh_matrix)

    gh_matrix_str = json.dumps(gh_matrix, indent=None)
    gh_output = os.environ.get("GITHUB_OUTPUT")

    if gh_output is None:
        print(f"\nmatrix={gh_matrix_str}")
    else:
        with open(gh_output, "a") as fp:
            print(f"matrix={gh_matrix_str}", file=fp)
