"""Check for correct copyright in every changed file."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import datetime
import fnmatch
import os
import subprocess as sp
import sys
from copy import deepcopy
from typing import Set

PACE_COPYRIGHT = """
__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

ATHENA_BOSCH_PACE_COPYRIGHT = """
__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

ATHENA_BOSCH_L4_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2021-2023 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

BOSCH_PACE_COPYRIGHT = """
__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

BOSCH_L4_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2023 Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

CARIAD_PACE_COPYRIGHT = """
__copyright__ = \"\"\"
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Cariad SE. All rights reserved.
 Copyright (c) 2023-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

CARIAD_2024_PACE_COPYRIGHT = """__copyright__ = \"\"\"
===================================================================================
C O P Y R I G H T
-----------------------------------------------------------------------------------
Copyright (c) 2021-2024 Cariad SE. All rights reserved.
Copyright (c) 2024-{} Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
\"\"\"
"""

copyright_strings = [
    PACE_COPYRIGHT,
    ATHENA_BOSCH_PACE_COPYRIGHT,
    ATHENA_BOSCH_L4_PACE_COPYRIGHT,
    BOSCH_PACE_COPYRIGHT,
    BOSCH_L4_PACE_COPYRIGHT,
    CARIAD_PACE_COPYRIGHT,
    CARIAD_2024_PACE_COPYRIGHT,
]
now = datetime.datetime.now()
rootdir = os.path.join(os.path.dirname(__file__), "../..")

# update the list of copyright headers
pprint_copyright_strings = deepcopy(copyright_strings)
for idx, copyright_string in enumerate(copyright_strings):
    # add the current year to the copyright head
    copyright_strings[idx] = copyright_string.format(now.year)
    pprint_copyright_strings[idx] = copyright_string.format(now.year)
    # reformat the copyright string (e.g., removal of \n's)
    copyright_strings[idx] = " ".join(copyright_strings[idx].split())


def file_has_copyright(filepath: str) -> bool:
    """Check if the file at the given path has the correct copyright header.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if the file has the correct copyright string, False otherwise.
    """
    # If the file was deleted, the copyright is ok.
    if not os.path.isfile(filepath):
        return True

    with open(filepath, "r", encoding="utf-8") as f:
        content = " ".join(f.read().split())
        return any([copyright_head in content for copyright_head in copyright_strings])


def is_sphinx_config_file(filepath: str) -> bool:
    """Checks if file is a sphinx conf.py file.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if filepath ends with docs/conf.py, False otherwise
    """
    return filepath.endswith("docs/conf.py")


def is_git_file(filepath: str) -> bool:
    """Checks if filepath contains git_info or .githooks.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if filepath contains git_info or .githooks, False otherwise
    """
    return "git_info" in filepath or ".githooks" in filepath


def is_external_file(filepath: str) -> bool:
    """Checks if file is external.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if file contains xflow/external or .landing_zone , False otherwise
    """
    return "xflow/external" in filepath or ".landing_zone" in filepath


def is_autogenerated_file(filepath: str) -> bool:
    """Checks if file is one of the autogenerated files.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if file has been autogenerated, False otherwise
    """
    return "athena_inference_results/proto" in filepath


def is_databricks_notebook_file(filepath: str) -> bool:
    """Checks if file is a databricks notebooks files.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if file is an Databricks Notebook file, False otherwise
    """

    return "databricks/notebooks/" in filepath


def is_sdk_file(filepath: str) -> bool:
    """Checks if file is an SDK files.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if file is an SDK file, False otherwise
    """
    return filepath.startswith(os.path.join(rootdir, "sdk"))


def is_ignore_file(filepath: str) -> bool:
    """Checks if file should be excluded from the copyright check.

    The functions are called which check if a file is a sphinx config file,
    git file or external file and returns a respective boolean.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if file needs to be ignored, False otherwise
    """
    ignore_fns = [
        is_sphinx_config_file,
        is_git_file,
        is_external_file,
        is_autogenerated_file,
        is_databricks_notebook_file,
        is_sdk_file,  # should not be checked due to generated code and the copyright format cannot be fitted easily
    ]
    ignore_file = [ignore_fn(filepath) for ignore_fn in ignore_fns]
    return any(ignore_file)


def is_invalid_file(filepath: str) -> bool:
    """Checks file validity.

    Args:
        filepath: The path to the file that should be checked.

    Returns:
        True if file is invalid, False otherwise
    """
    return not is_ignore_file(filepath) and not file_has_copyright(filepath)


def get_modified_file_set() -> Set[str]:
    """Return the set of filepaths which have been modified."""
    # Get a common merge base to determine which files have changed in the Pull-Request
    # this command fails if not in a full git repo, e.g. shallow clone for docker build
    # we hence ignore these cases
    try:
        merge_base = sp.check_output(["git", "merge-base", "origin/main", "HEAD"], cwd=rootdir)
        files = sp.check_output(["git", "diff", "--name-only", merge_base.strip()], cwd=rootdir)
    except sp.CalledProcessError:
        print("Cannot determined changed files. Probably not in PR.")
        sys.exit(0)

    file_names = files.decode("utf-8").splitlines()
    # It can happen that the list is empty afterwards
    file_names = fnmatch.filter(file_names, "*.py")
    file_paths = {os.path.join(rootdir, filename) for filename in file_names}

    return file_paths


def check_copyright_everywhere() -> None:
    """Check if all modified files have the correct copyright header.

    Exits with code 0 if all modified files have the correct copyright header, and with code 1 otherwise.
    """
    file_set = get_modified_file_set()

    ignore_files = set(filter(is_ignore_file, file_set))
    invalid_files = set(filter(is_invalid_file, file_set))

    print("Copyright checker...")
    print("------------------------------------------")
    print("The following files were checked: ")
    print("\n".join(sorted(file_set - ignore_files)))
    if ignore_files:
        print("------------------------------------------")
        print("The following files were ignored: ")
        print("\n".join(sorted(ignore_files)))
    print("------------------------------------------\n")

    if invalid_files:
        print("Accepted copyright strings are:")
        print("\n".join(sorted(pprint_copyright_strings)))
        print("Wrong or no license header in the following files: ")
        print("\n".join(sorted(invalid_files)))
        sys.exit(1)
    else:
        print("All files have the correct license header.")
        sys.exit(0)


if __name__ == "__main__":
    check_copyright_everywhere()
