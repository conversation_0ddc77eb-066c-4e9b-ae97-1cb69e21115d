#!/bin/env python3
"""Select the jobs which shall be run by the Gatekeeper."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import os
import subprocess
from pathlib import Path
from typing import Any, Callable

from common import BASE_DIR, DATABRICKS_LIBRARIES_DIR, SCRIPTS_DIR, WORKFLOWS_DIR
from sphinx_includes import is_sphinx_include


def _check_deploy_gatekeeper_image(context: dict[str, Any]) -> bool:
    """Check if the deploy-gatekeeper-image job must be selected to run."""
    # Check if the image is available in the registry
    result = subprocess.run(
        ["docker", "manifest", "inspect", context["image_name"]],
        capture_output=True,
    )

    return result.returncode != 0


CHECK_JOB_FUNC: dict[str, Callable[[dict[str, Any]], bool]] = {
    "deploy-gatekeeper-image": _check_deploy_gatekeeper_image,
    "static-analysis": lambda _: True,
    "unit-tests": lambda _: True,
}


def _check_publish_documentation(file: Path, context: dict[str, Any]) -> bool:
    """Check if the publish-documentation job must be selected to run."""
    result = is_sphinx_include(file) or (file == WORKFLOWS_DIR / "publish_documentation.yml")
    return result


def _check_deploy_databricks_libraries(file: Path, context: dict[str, Any]) -> bool:
    """Check if the deploy-databricks-libraries job must be selected to run."""
    return (
        file.is_relative_to(DATABRICKS_LIBRARIES_DIR)
        or (file == WORKFLOWS_DIR / "deploy_databricks_libraries.yml")
        or (file.is_relative_to(SCRIPTS_DIR / "dbx_libraries"))
    )


FILE_CHECK_JOB_FUNC: dict[str, Callable[[Path, dict[str, Any]], bool]] = {
    "publish-documentation": _check_publish_documentation,
    "deploy-databricks-libraries": _check_deploy_databricks_libraries,
}


def _parse_args() -> argparse.Namespace:
    """Parse the command line arguments."""
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "--base",
        type=str,
        required=True,
        help="The base commit to compare against.",
    )
    parser.add_argument(
        "--target",
        type=str,
        required=True,
        help="The target commit to compare against.",
    )
    parser.add_argument(
        "--image-name",
        type=str,
        required=True,
        help="The name of the Gatekeeper image.",
    )
    return parser.parse_args()


def get_changed_files(base: str, target: str) -> list[Path]:
    """Get the files that changed between two commits."""
    return [
        BASE_DIR / raw_file
        for raw_file in subprocess.check_output(["git", "diff", "--name-only", base, target])
        .decode("utf-8")
        .splitlines()
    ]


def select_jobs(changed_files: list[Path], context: dict[str, Any]) -> dict[str, bool]:
    """Select the jobs which shall be run by the Gatekeeper."""
    # Check jobs without file dependency
    job_flags = {job: check_func(context) for job, check_func in CHECK_JOB_FUNC.items()}

    # Check jobs with file dependency
    job_flags.update({job: False for job in FILE_CHECK_JOB_FUNC.keys()})
    for file in changed_files:
        for job, check_func in FILE_CHECK_JOB_FUNC.items():
            # Skip check if the job is already selected
            flag = job_flags[job]
            if flag:
                continue

            if check_func(file, context):
                job_flags[job] = True

    return job_flags


def write_github_output(job_flags: dict[str, bool]) -> None:
    """Write the job flags as Github Output."""
    gh_output = os.environ.get("GITHUB_OUTPUT")
    fp = open(gh_output, "a") if gh_output is not None else None
    try:
        for job, flag in job_flags.items():
            flag_str = str(flag).lower()
            print(f"{job}: {flag_str}")
            if gh_output is not None:
                print(f"{job}={flag_str}", file=fp)
    finally:
        if fp is not None:
            fp.close()


if __name__ == "__main__":
    args = vars(_parse_args())
    changed_files = get_changed_files(args.pop("base"), args.pop("target"))
    job_flags = select_jobs(changed_files, args)
    write_github_output(job_flags)
