#!/bin/env python3
"""Build the Gatekeeper docker image."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import subprocess

from common import BASE_DIR, GATEKEEPER_DIR

DOCKERFILE = GATEKEEPER_DIR / "Dockerfile"


def build_image(
    image_name: str,
    uv_default_index: str,
    uv_index: list[str],
    supported_python_versions: list[str] | None = None,
    default_python_version: str | None = None,
    push: bool = False,
) -> None:
    """Build the Gatekeeper docker image.

    Args:
        image_name: Resulting image name.
        uv_default_index: Default uv index URL.
        uv_index: Additional uv index URLs.
        supported_python_versions: Supported Python versions.
        default_python_version: Default Python version.
        push: Push the image to the registry.
    """
    # Assemble build args
    build_args = []
    if supported_python_versions:
        build_args.append(f"SUPPORTED_PYTHON_VERSIONS={' '.join(supported_python_versions)}")
    if default_python_version:
        build_args.append(f"DEFAULT_PYTHON_VERSION={default_python_version}")
    # Add --build-arg flag for each build arg
    build_arg_flags = [f"--build-arg={arg}" for arg in build_args]

    # Assemble secrets
    secrets_args = [
        "id=uv-default-index,env=UV_DEFAULT_INDEX",
        "id=uv-index,env=UV_INDEX",
    ]
    # Add --secret flag for each secret
    secrets_flags = [f"--secret={secret}" for secret in secrets_args]

    # Assemble additional args
    additional_args = []
    if push:
        additional_args.append("--push")

    # Assemble environment variables
    env = {
        "DOCKER_BUILDKIT": "1",  # Enable BuildKit for better build performance and secret management
        "UV_DEFAULT_INDEX": uv_default_index,
        "UV_INDEX": " ".join(uv_index),
    }

    subprocess.run(
        [
            "docker",
            "build",
            "-t",
            image_name,
            "-f",
            str(DOCKERFILE),
            *build_arg_flags,
            *secrets_flags,
            *additional_args,
            str(BASE_DIR),
        ],
        env=env,
        check=True,
    )


def parse_args() -> argparse.Namespace:
    """Parse command line arguments."""
    parser = argparse.ArgumentParser(description="Build the Gatekeeper docker image.")
    parser.add_argument("--uv-default-index", required=True, help="Default uv index URLs")
    parser.add_argument("--uv-index", nargs="+", default=[], help="Additional uv index URLs")
    parser.add_argument("--supported-python-versions", nargs="+", required=True, help="Supported Python versions")
    parser.add_argument("--default-python-version", required=True, help="Default Python version")
    parser.add_argument("--image-name", required=True, help="Docker image name")
    parser.add_argument("--push", action="store_true", default=False, help="Push the image to the registry")

    args = parser.parse_args()

    # Check default Python version is in supported Python version
    if args.default_python_version not in args.supported_python_versions:
        raise ValueError("Default Python version must be in supported Python versions")

    return args


if __name__ == "__main__":
    args = parse_args()
    build_image(
        image_name=args.image_name,
        uv_default_index=args.uv_default_index,
        uv_index=args.uv_index,
        supported_python_versions=args.supported_python_versions,
        default_python_version=args.default_python_version,
        push=args.push,
    )
