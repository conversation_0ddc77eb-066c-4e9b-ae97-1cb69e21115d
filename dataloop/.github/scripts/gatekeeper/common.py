"""Common definitions for the Gatekeeper scripts."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path

GATEKEEPER_DIR = Path(__file__).resolve().parent
SCRIPTS_DIR = GATEKEEPER_DIR.parent
GITHUB_DIR = SCRIPTS_DIR.parent
BASE_DIR = GITHUB_DIR.parent
WORKFLOWS_DIR = GITHUB_DIR / "workflows"

DATABRICKS_DIR = BASE_DIR / "databricks"
DATABRICKS_LIBRARIES_DIR = DATABRICKS_DIR / "libraries"
DOC_DIR = BASE_DIR / "doc"
