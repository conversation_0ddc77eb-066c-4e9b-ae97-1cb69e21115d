FROM ubuntu:22.04

# Use bash as default shell
SHELL [ "/bin/bash", "--login", "-c" ]

### Arguments ###

ARG UV_VERSION="0.6.1"
ARG JF_CLI_VERSION="2.71.5"
ARG SUPPORTED_PYTHON_VERSIONS
ARG DEFAULT_PYTHON_VERSION

### Environment variables ###

ENV UV_COMPILE_BYTECODE=1
ENV UV_FROZEN=1
ENV HOME=/root

### Build ###

# Set workdir to root home
WORKDIR ${HOME}

# Install base packages
RUN apt update && apt install --no-install-recommends -y \
    build-essential \
    ca-certificates \
    curl \
    wget \
    git

# Install required packages
RUN apt update && apt install --no-install-recommends -y \
    jq \
    # Java
    openjdk-11-jre \
    # Documentation
    pandoc \
    graphviz

# Install Azure CLI
RUN curl -sL https://aka.ms/InstallAzureCLIDeb | bash
# Install AzCopy
RUN mkdir tmp_azcopy \
    && curl -sSL https://azcopyvnext-awgzd8g7aagqhzhe.b02.azurefd.net/releases/release-10.27.1-20241113/azcopy_linux_amd64_10.27.1.tar.gz -o azcopy_linux_amd64.tar.gz \
    && tar -xvf azcopy_linux_amd64.tar.gz -C tmp_azcopy --strip-components=1 \
    && mv tmp_azcopy/azcopy /usr/bin \
    && chown root /usr/bin/azcopy \
    && rm azcopy_linux_amd64.tar.gz && rm -rf tmp_azcopy
# Install Artifactory CLI
RUN curl -fL https://install-cli.jfrog.io | sh -s -- ${JF_CLI_VERSION}
# Install GitHub CLI
RUN mkdir -p -m 755 /etc/apt/keyrings \
    && out=$(mktemp) && wget -qnv -O$out https://cli.github.com/packages/githubcli-archive-keyring.gpg \
    && cat $out | tee /etc/apt/keyrings/githubcli-archive-keyring.gpg > /dev/null \
    && chmod go+r /etc/apt/keyrings/githubcli-archive-keyring.gpg \
    && echo "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null \
    && apt update \
    && apt install --no-install-recommends -y gh

# Install plantuml from local
RUN mkdir -p "/usr/share/plantuml" && chmod 755 "/usr/share/plantuml"
COPY ".github/bin/plantuml.1.2023.7.jar" "/usr/share/plantuml/plantuml.jar"
RUN chmod 644 "/usr/share/plantuml/plantuml.jar"

# Install uv
ADD --chmod=744 https://astral.sh/uv/${UV_VERSION}/install.sh uv-installer.sh
RUN ./uv-installer.sh && rm uv-installer.sh
ENV PATH="${HOME}/.local/bin/:$PATH"

# Install supported Python versions
ENV DEFAULT_VENV="${HOME}/.venv_${DEFAULT_PYTHON_VERSION}"
RUN for version in "$SUPPORTED_PYTHON_VERSIONS"; do uv python install $version; done
# Disable download of Python from here on
ENV UV_PYTHON_DOWNLOADS=never

# Create default venv
RUN uv venv --no-project --python "$DEFAULT_PYTHON_VERSION" "$DEFAULT_VENV"
ENV UV_PROJECT_ENVIRONMENT="${DEFAULT_VENV}"
ENV VIRTUAL_ENV="${DEFAULT_VENV}"

# Install Python dependencies
RUN --mount=type=bind,source=uv.lock,target=${HOME}/uv.lock \
    --mount=type=bind,source=pyproject.toml,target=${HOME}/pyproject.toml \
    --mount=type=secret,id=uv-default-index,env=UV_DEFAULT_INDEX \
    --mount=type=secret,id=uv-index,env=UV_INDEX \
    uv sync --no-install-workspace --all-groups

# Set uv link mode to copy as hard links are not supported if run in Github Actions
ENV UV_LINK_MODE=copy
