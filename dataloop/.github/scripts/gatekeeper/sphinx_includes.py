"""Module providing functionality to check if a file is included in the Sphinx build."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import importlib.util
import re
import sys
from collections.abc import Callable, Iterable
from importlib.machinery import ModuleSpec
from pathlib import Path
from types import ModuleType

from common import BASE_DIR, DOC_DIR


def _import_sphinx_conf() -> ModuleType:
    """Import the Sphinx configuration file."""
    # Mock needs.general module
    sys.modules["needs.general"] = ModuleType("needs.general")

    # Has to be done via spec_from_file_location as there is a conf folder in doc
    # which gets imported instead of conf.py if we just add the doc folder to sys.path
    spec: ModuleSpec = importlib.util.spec_from_file_location("sphinx_conf", DOC_DIR / "conf.py")  # type: ignore[assignment] # noqa: E501
    sphinx_conf = importlib.util.module_from_spec(spec)
    spec.loader.exec_module(sphinx_conf)  # type: ignore[union-attr]
    return sphinx_conf


def _translate_pattern(pat: str) -> str:  # noqa: C901
    """Translate a shell-style glob pattern to a regular expression.

    This function is taken from Sphix module sphinx.util.matching.
    This way, we can check if a file is included in the Sphinx build
    without having to install Sphinx itself.
    """
    i, n = 0, len(pat)
    res = ""
    while i < n:
        c = pat[i]
        i += 1
        if c == "*":
            if i < n and pat[i] == "*":
                # double star matches slashes too
                i += 1
                res = res + ".*"
            else:
                # single star doesn't match slashes
                res = res + "[^/]*"
        elif c == "?":
            # question mark doesn't match slashes too
            res = res + "[^/]"
        elif c == "[":
            j = i
            if j < n and pat[j] == "!":
                j += 1
            if j < n and pat[j] == "]":
                j += 1
            while j < n and pat[j] != "]":
                j += 1
            if j >= n:
                res = res + "\\["
            else:
                stuff = pat[i:j].replace("\\", "\\\\")
                i = j + 1
                if stuff[0] == "!":
                    # negative pattern mustn't match slashes too
                    stuff = "^/" + stuff[1:]
                elif stuff[0] == "^":
                    stuff = "\\" + stuff
                res = f"{res}[{stuff}]"
        else:
            res += re.escape(c)
    return res + "$"


def _compile_matchers(patterns: Iterable[str]) -> list[Callable[[str], re.Match[str] | None]]:
    """Compiles a list of patterns into a list of match functions."""
    return [re.compile(_translate_pattern(pat)).match for pat in patterns]


sphinx_conf = _import_sphinx_conf()
include_matchers = _compile_matchers(sphinx_conf.include_patterns)
exclude_matchers = _compile_matchers(sphinx_conf.exclude_patterns)


def is_sphinx_include(file: Path) -> bool:
    """Check if the file is included by the Sphinx project."""
    rel_path = file.relative_to(BASE_DIR)

    # Check against excludes first
    for exclude in exclude_matchers:
        if exclude(str(rel_path)):
            return False  # Path is excluded

    # Check against includes
    for include in include_matchers:
        if include(str(rel_path)):
            return True  # Path is included

    return False  # Path is excluded by default
