#!/bin/env python3
"""Calculate the hash of all Gatekeeper image related files."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import hashlib
from pathlib import Path

from common import BASE_DIR, GATEKEEPER_DIR, WORKFLOWS_DIR

# Files and directories which are dependencies of the Gatekeeper image
DEPENDENCY_FILES = [
    BASE_DIR / "pyproject.toml",
    BASE_DIR / "uv.lock",
    GATEKEEPER_DIR / "Dockerfile",
    GATEKEEPER_DIR / "calc_deps_hash.py",
    GATEKEEPER_DIR / "build_image.py",
    WORKFLOWS_DIR / "deploy_gatekeeper_image.yml",
    WORKFLOWS_DIR / "gatekeeper.yml",
]


def _calculate_file_hash(file: Path) -> str:
    """Calculate the hash of a file."""
    with open(file, "rb") as fp:
        return hashlib.file_digest(fp, "sha3_256").hexdigest()


def _calculate_directory_hash(directory: Path) -> str:
    """Calculate the hash of a directory."""
    result_hash = hashlib.sha3_256()
    for file in sorted(directory.rglob("*")):
        if file.is_file():
            result_hash.update(_calculate_file_hash(file).encode("utf-8"))
    return result_hash.hexdigest()


def calculate_dependency_hash() -> str:
    """Calculate the hash of all dependency files."""
    result_hash = hashlib.sha3_256()
    for file in DEPENDENCY_FILES:
        # Ensure all dependency files exist
        if not file.exists():
            raise FileNotFoundError(f"Dependency file {file} does not exist.")

        if file.is_file():
            result_hash.update(_calculate_file_hash(file).encode("utf-8"))
        elif file.is_dir():
            result_hash.update(_calculate_directory_hash(file).encode("utf-8"))

    return result_hash.hexdigest()


if __name__ == "__main__":
    print(calculate_dependency_hash(), end="")
