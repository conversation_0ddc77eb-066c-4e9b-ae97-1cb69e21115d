"""Module that contains preprocessing functions for req_rules table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
from typing import Tuple

import pyspark.sql.functions as f
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.window import Window


def parse_job_parameters() -> argparse.Namespace:
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-s", "--silver_catalog", required=True, type=str)
    parser.add_argument("-t", "--target_catalog", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def get_tables_preprocessed(
    spark_session: SparkSession, silver_catalog: str
) -> Tuple[DataFrame, DataFrame, DataFrame, DataFrame, DataFrame, DataFrame]:
    """Preprocess req rules table."""

    df = spark_session.table(f"{silver_catalog}.pace_metrics.requirement_rules")

    df = df.withColumn("time_stamp", f.date_format("time_stamp", "yyyy-MM-dd HH:mm:ss.SSS"))
    df = df.withColumn("received", f.date_format("received", "yyyy-MM-dd HH:mm:ss.SSS"))
    df = df.withColumn("rating", f.regexp_replace("rating", "\n", " "))
    df = df.withColumn("rating_mod", f.split(f.col("rating"), " ").getItem(0))
    df = df.withColumn("rating_mod", f.regexp_replace("rating_mod", "[^a-zA-Z0-9 ]", ""))
    df = df.filter(f.col("requirement_id") != "BAD_EXAMPLE_PHRASING_RULE")

    df = df.withColumn("year", f.year("time_stamp"))
    df = df.withColumn("month", f.month("time_stamp"))
    df = df.withColumn("day", f.day("time_stamp"))

    df = df.withColumn(
        "short_rating",
        f.when(f.col("rating_mod").rlike(r"^\s*(?i)not"), "N")
        .when(f.col("rating_mod").rlike(r"^\s*(?i)partially"), "P")
        .when(f.col("rating_mod").rlike(r"^\s*(?i)fully"), "F")
        .when(f.col("rating_mod").rlike(r"^\s*(?i)largely"), "L"),
    )

    df = df.withColumn(
        "short_rating_numeric",
        f.when(f.col("short_rating") == "N", 0)
        .when(f.col("short_rating") == "P", 1)
        .when(f.col("short_rating") == "L", 2)
        .when(f.col("short_rating") == "F", 3)
        .otherwise("Invalid"),
    )

    # Calculate the average short rating for each requirement_id
    df_avg_perReqId = df.groupBy("requirement_id").agg(f.avg("short_rating_numeric").alias("average_short_rating"))

    # Calculate the average short rating for each rule_id per month
    df_avg_perRuleId = df.groupBy("rule_id", "year", "month").agg(
        f.avg(f.when(f.col("short_rating_numeric").isNotNull(), f.col("short_rating_numeric")).cast("float")).alias(
            "avg_short_rating_per_month"
        )
    )

    df_count_shortrating = df.groupBy("year", "month").agg(
        f.count(f.when(f.col("short_rating") == "F", 1)).alias("count_Fully"),
        f.count(f.when(f.col("short_rating") == "P", 1)).alias("count_Partially"),
        f.count(f.when(f.col("short_rating") == "L", 1)).alias("count_Largely"),
        f.count(f.when(f.col("short_rating") == "N", 1)).alias("count_Not"),
        f.count(f.when(f.col("short_rating").isNull(), 1)).alias("count_Null"),
        f.count("*").alias("total_count"),
    )

    # drop duplicates in requirement_id and keep only latest requirement_id row based on time_stamp
    df_drop_reqId_dup = df.withColumn(
        "rank", f.row_number().over(Window.partitionBy("requirement_id").orderBy(f.col("time_stamp").desc()))
    )

    # Keep only the rows where the rank is 1 (latest time_stamp per requirement_id)
    df_drop_reqId_dup = df_drop_reqId_dup.filter(f.col("rank") == 1).drop("rank")

    # Mark violation status (0 for no violation, 1 for violation)
    df = df.withColumn("violation", f.when((df["short_rating"] == "N") | (df["short_rating"] == "P"), 1).otherwise(0))

    violation_stats = df.groupBy("rule_id", "year", "month").agg(
        f.sum("violation").alias("total_violations"),  # Sum violations for each period
        f.count("requirement_id").alias("total_reviews"),  # Count reviewed requirements
    )

    # Calculate the deviation rate (violations/reviews)
    violation_stats = violation_stats.withColumn(
        "deviation_rate", f.col("total_violations") / f.col("total_reviews") * 100
    )

    return (df, df_drop_reqId_dup, df_avg_perReqId, df_avg_perRuleId, df_count_shortrating, violation_stats)


def save_table(spark_session: SparkSession, df: DataFrame, target_tbl_name: str) -> None:
    """Save table in catalog."""
    df.write.format("delta").mode("overwrite").option("mergeSchema", "true").saveAsTable(target_tbl_name)


def execute(spark_session: SparkSession, silver_catalog: str, gold_catalog: str) -> None:
    """Execute the trajectory frames pipeline."""

    target_tbl_name1 = f"{gold_catalog}.req_rules.df_mod"
    target_tbl_name2 = f"{gold_catalog}.req_rules.df_drop_reqId_dup"
    target_tbl_name3 = f"{gold_catalog}.req_rules.df_avg_perReqId"
    target_tbl_name4 = f"{gold_catalog}.req_rules.df_avg_perRuleId"
    target_tbl_name5 = f"{gold_catalog}.req_rules.df_count_shortrating"
    target_tbl_name6 = f"{gold_catalog}.req_rules.df_rate_deviation"

    (df, df_drop_reqId_dup, df_avg_perReqId, df_avg_perRuleId, df_count_shortrating, violation_stats) = (
        get_tables_preprocessed(spark_session, silver_catalog)
    )

    save_table(spark_session, df, target_tbl_name1)
    save_table(spark_session, df_drop_reqId_dup, target_tbl_name2)
    save_table(spark_session, df_avg_perReqId, target_tbl_name3)
    save_table(spark_session, df_avg_perRuleId, target_tbl_name4)
    save_table(spark_session, df_count_shortrating, target_tbl_name5)
    save_table(spark_session, violation_stats, target_tbl_name6)


if __name__ == "__main__":
    args = parse_job_parameters()
    spark_session = SparkSession.builder.getOrCreate()
    execute(spark_session, args.silver_catalog, args.target_catalog)
