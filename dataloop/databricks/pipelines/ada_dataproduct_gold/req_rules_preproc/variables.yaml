variables:
  env:
    default: dev
    description: "environment, dev, qa, prod"
  run_sp:
    description: "run service principal"
    lookup:
      service_principal: "sp-pace-dataloop-gvn-eq-${var.env}"
  target_catalog:
    default: gold_dev
    description: "Catalog name where preprocessed quality tables are stored"
  silver_catalog:
    default: silver
    description: "Catalog name where source embedded quality tables are stored"
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"
  spark_version:
    default: "15.4.x-scala2.12"
    description: "Spark version"
  instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E4ads_v5_rt15.4"
  # ms_teams_alert_channel_email:
  #   default: ""
  #   description: "MS Teams alert channel email"
