resources:
  jobs:
    req_rules_gold:
      tags:
        responsible_domain: Governance
        responsible_team: Embedded Quality
        product: Requirement Rules
      # Give permission to Embedded Quality developers to manage run
      permissions:
        - group_name: "sg-pace-github-Governance - Embedded Quality-reader"
          level: CAN_MANAGE_RUN
      run_as:
        service_principal_name: ${var.run_sp}
      name: "Requirement Rules - Preprocessing"
      # email_notifications:
      #   on_failure:
      #     - ${var.ms_teams_alert_channel_email}
      timeout_seconds: 3000
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 1000
      tasks:
        - task_key: req_rules_prepr
          spark_python_task:
            python_file: ./src/req_rules_prepr.py
            parameters:
              - --silver_catalog
              - ${var.silver_catalog}
              - --target_catalog
              - ${var.target_catalog}
          job_cluster_key: req_rules_job_cluster

      job_clusters:
        - job_cluster_key: req_rules_job_cluster
          new_cluster:
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            num_workers:  0
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            data_security_mode: SINGLE_USER
            custom_tags:
              "ResourceClass": "SingleNode"
