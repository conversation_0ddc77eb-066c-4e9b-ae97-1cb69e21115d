"""Module containing the gold table updates for datacuration_dataset_def in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import time

from utils.datacuration_helper import GoldExport, get_databricks_config
from utils.utilities import silver_table_paths


class DatasetDefCreation(GoldExport):
    """Class responsible for update of the datacuration_dataset_def gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer for dataset defintion."""

        silver_table_paths_dict = silver_table_paths()
        SILVER_TDS_ENTRY_TABLE = silver_table_paths_dict["SILVER_TDS_ENTRY_TABLE"]
        SILVER_VIPER_LABELCLIENT_TABLE = silver_table_paths_dict["SILVER_VIPER_LABELCLIENT_TABLE"]
        SILVER_TDS_PARENTS = silver_table_paths_dict["SILVER_TDS_PARENTS"]
        SILVER_AUTOQC_LABEL_OBJECTS = silver_table_paths_dict["SILVER_AUTOQC_LABEL_OBJECTS"]
        SILVER_VIPER_FRAME_METADATA = silver_table_paths_dict["SILVER_VIPER_FRAME_METADATA"]
        SILVER_AUTOQC_LABEL_STREAMS = silver_table_paths_dict["SILVER_AUTOQC_LABEL_STREAMS"]

        file_entries_df = self.spark.table(SILVER_TDS_ENTRY_TABLE)
        file_entries_r_df = file_entries_df.withColumnRenamed("tds_file_url", "blob_file_url")
        file_entries_r_df.createOrReplaceTempView("file_entries_view")
        self.spark.sql(
            f"""
            CREATE OR REPLACE TABLE {self.full_table_name}
            USING DELTA AS
            SELECT                  viper.split_hash,
                                    viper.frame_number,
                                    blob.tds_file_url,
                                    fe.blob_file_url as thumbnail_blob_file_url,
                                    viper.label_label_hash,
                                    viper.label_label_guide_url,
                                    viper.label_batch_id,
                                    viper.label_pipeline_id,
                                    viper.label_tds_file_url,
                                    viper.label_latest_version,
                                    viper.label_team,
                                    viper.ref_img_file_hash,
                                    viper.ref_img_rectification_calstorage_uid,
                                    viper.stream_file_name,
                                    viper.stream_camera_type,
                                    viper.train_img_hash,
                                    viper.train_img_view,
                                    viper.train_img_color_space,
                                    viper.train_img_tds_file_url,
                                    viper.train_img_extractor_version,
                                    viper.train_img_rectification_calstorage_uid,
                                    viper.frame_context,
                                    viper.image_id,
                                    obj.file_hash,
                                    obj.batch_id,
                                    obj.frame_number as frame_no,
                                    obj.stream,
                                    obj.annotation_coordinates,
                                    obj.stream_independent_attributes,
                                    obj.stream_dependent_attributes,
                                    obj.annotation_name,
                                    obj.annotation_type,
                                    obj.feature_team,
                                    obj.class,
                                    obj.frame_sha,
                                    obj.ingest_timestamp,
                                    obj.file_modified_at,
                                    p.parent_file_hash as raw_image_hash,
                                    mt.width_px,
                                    mt.height_px,
                                    mt.intrinsics,
                                    mt.extrinsics,
                                    gps.gps__longitude,
                                    gps.gps__latitude
            FROM {SILVER_VIPER_LABELCLIENT_TABLE} as viper
            LEFT JOIN {SILVER_TDS_ENTRY_TABLE} as blob
                ON viper.ref_img_file_hash = blob.file_hash
            LEFT JOIN
                {SILVER_TDS_PARENTS} AS p ON viper.train_img_hash = p.file_hash
            LEFT JOIN
            (
                SELECT explode(parents) as parent, tds_file_url as blob_file_url, file_name
                FROM {SILVER_TDS_ENTRY_TABLE}
                WHERE file_name LIKE '%_240.%'
            ) as fe ON p.parent_file_hash = fe.parent
            LEFT JOIN
            (
                SELECT gps__longitude, gps__latitude, image_id
                FROM {SILVER_VIPER_FRAME_METADATA}
            ) as gps ON gps.image_id = viper.image_id
            LEFT JOIN
            (
                SELECT width_px, height_px, intrinsics, extrinsics, file_hash, stream
                FROM {SILVER_AUTOQC_LABEL_STREAMS}
                WHERE stream = "FC1"
            ) as mt ON mt.file_hash = viper.label_label_hash
            LEFT JOIN
            (
                select file_hash, batch_id, frame_number, frame_sha, stream, annotation_coordinates,
                stream_independent_attributes, annotation_type, annotation_name, feature_team, class,
                stream_dependent_attributes, ingest_timestamp, file_modified_at
                from {SILVER_AUTOQC_LABEL_OBJECTS}
            ) as obj
                ON viper.ref_img_file_hash == obj.frame_sha
            WHERE
            (
                (viper.stream_camera_type = 'FC1' AND viper.train_img_view = 'Label') AND
                (viper.label_team == "TL (Traffic Light Labeling)" AND obj.feature_team == "TrafficLight") OR
                (viper.label_team == "LSR" AND obj.feature_team == "LightSourceRecognition") OR
                (viper.label_team == "RoadSymbol" AND obj.feature_team == "RoadSymbols") OR
                (viper.label_team == obj.feature_team)
            )
            """
        )
        time.sleep(10)
        self.spark.sql(
            f"""
            INSERT INTO {self.full_table_name}
            (
                split_hash,
                frame_number,
                tds_file_url,
                thumbnail_blob_file_url,
                label_label_hash,
                label_label_guide_url,
                label_batch_id,
                label_pipeline_id,
                label_tds_file_url,
                label_latest_version,
                label_team,
                ref_img_file_hash,
                ref_img_rectification_calstorage_uid,
                stream_file_name,
                stream_camera_type,
                train_img_hash,
                train_img_view,
                train_img_color_space,
                train_img_tds_file_url,
                train_img_extractor_version,
                train_img_rectification_calstorage_uid,
                frame_context,
                image_id,
                file_hash,
                batch_id,
                frame_no,
                stream,
                annotation_coordinates,
                stream_independent_attributes,
                stream_dependent_attributes,
                annotation_name,
                annotation_type,
                feature_team,
                class,
                frame_sha,
                ingest_timestamp,
                file_modified_at,
                raw_image_hash,
                width_px,
                height_px,
                intrinsics,
                extrinsics,
                gps__longitude,
                gps__latitude
            )
            SELECT 
                viper.split_hash,
                viper.frame_number,
                blob.tds_file_url,
                fe.blob_file_url as thumbnail_blob_file_url,
                viper.label_label_hash,
                viper.label_label_guide_url,
                viper.label_batch_id,
                viper.label_pipeline_id,
                viper.label_tds_file_url,
                viper.label_latest_version,
                viper.label_team,
                viper.ref_img_file_hash,
                viper.ref_img_rectification_calstorage_uid,
                viper.stream_file_name,
                viper.stream_camera_type,
                viper.train_img_hash,
                viper.train_img_view,
                viper.train_img_color_space,
                viper.train_img_tds_file_url,
                viper.train_img_extractor_version,
                viper.train_img_rectification_calstorage_uid,
                viper.frame_context,
                viper.image_id,
                obj.file_hash,
                obj.batch_id,
                obj.frame_number as frame_no,
                obj.stream,
                obj.annotation_coordinates,
                obj.stream_independent_attributes,
                obj.stream_dependent_attributes,
                obj.annotation_name,
                obj.annotation_type,
                obj.feature_team,
                obj.class,
                obj.frame_sha,
                obj.ingest_timestamp,
                obj.file_modified_at,
                p.parent_file_hash as raw_image_hash,
                mt.width_px,
                mt.height_px,
                mt.intrinsics,
                mt.extrinsics,
                gps.gps__longitude,
                gps.gps__latitude
            FROM (select * from silver.mdd.viper_labelclient_input WHERE label_team = "TSR (Traffic sign labeling)") as viper
            LEFT JOIN silver.tds.file_entries as blob
                ON viper.ref_img_file_hash = blob.file_hash
            LEFT JOIN silver.tds.parents AS p 
                ON viper.train_img_hash = p.file_hash
            LEFT JOIN
            (
                SELECT explode(parents) as parent, tds_file_url as blob_file_url, file_name
                FROM silver.tds.file_entries
                WHERE file_name LIKE '%_240.%'
            ) as fe 
                ON p.parent_file_hash = fe.parent
            LEFT JOIN
            (
                SELECT gps__longitude, gps__latitude, image_id
                FROM silver.mdd.viper_frame_mdd_metadata
            ) as gps 
                ON gps.image_id = viper.image_id
            LEFT JOIN
            (
                SELECT width_px, height_px, intrinsics, extrinsics, file_hash, stream
                FROM silver.autoqc.label_file_streams
                WHERE stream = "FC1"
            ) as mt 
                ON mt.file_hash = viper.label_label_hash
            LEFT JOIN
            (
                SELECT file_hash, batch_id, frame_number, frame_sha, stream, annotation_coordinates,
                    stream_independent_attributes, annotation_type, annotation_name, feature_team, class,
                    stream_dependent_attributes, ingest_timestamp, file_modified_at
                FROM silver.autoqc.label_file_objects
                WHERE feature_team = "TrafficSign"
            ) as obj
                ON viper.ref_img_file_hash = obj.frame_sha
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = DatasetDefCreation(table="datacuration_dataset_def", args=args)
    gold_df.run()
