bundle:
  name: datacuration

include:
  - resources/*.yml

targets:

  dev:
    variables:
      run_sp: 94009b84-4775-4551-b843-aa5a981a9949
      env: dev
      catalog: gold_dev
      schema: datacuration
    mode: production
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}

  qa:
    variables:
      run_sp: e5d23f9c-b31e-4162-b7e9-1bff9dfe5787
      env: qa
      catalog: gold_qa
      schema: datacuration
    mode: production
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}

  prod:
    variables:
      run_sp: 985434ee-fa91-40a6-b335-62512a6bf786
      env: prod
      catalog: gold
      schema: datacuration
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
