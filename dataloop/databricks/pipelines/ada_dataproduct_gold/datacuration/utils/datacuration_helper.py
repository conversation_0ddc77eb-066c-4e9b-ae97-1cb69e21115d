"""Contains helper class for common functionality used by gold layer scripts."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from abc import abstractmethod
from dataclasses import dataclass
from typing import Optional

import pandas as pd
from databricks.connect import DatabricksSession
from pyspark.sql import SparkSession

SILVER_PATH = "silver.mdd"

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


@dataclass
class RuntimeArgs:
    """Provide default runtime args for cmd line entries."""

    env: str
    catalog: str
    schema: str


def get_databricks_config() -> RuntimeArgs:
    """Get the cmd line arguments and read the databricks config.

    The function passed the args to the gold layer including the run_local
    parameter for debugging in custom folders
    """

    parser = argparse.ArgumentParser()

    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Run in environment, ex. dev, qa, prod",
    )
    parser.add_argument(
        "--run_local",
        action="store_true",
        help="Run in local mode. This will overwrite the environment and always run in dev.",
    )
    parser.add_argument(
        "-c",
        "--catalog",
        help="Catalogue to run against, by default catalogue is `gold`.",
    )
    parser.add_argument(
        "-s",
        "--schema",
        help="Schema to run against, by default schema is `mdd`.",
    )

    args, unknown = parser.parse_known_args()

    return RuntimeArgs(
        env=args.env,
        catalog=args.catalog,
        schema=args.schema,
    )


def get_or_create_databricks_session(databricks_profile_name: Optional[str] = None) -> SparkSession:
    """Gets or creates a Databricks SparkSession.

    Optionally, you can provide a Databricks CLI profile name.
    The profile is then used to retrieve the SparkSession.

    Args:
        databricks_profile_name (Optional[str], optional): Name of a configured
            Databricks Profile.

    Returns:
        SparkSession: SparkSession created
    """
    if databricks_profile_name is None:
        return DatabricksSession.builder.getOrCreate()
    else:
        return DatabricksSession.builder.profile(databricks_profile_name).getOrCreate()


class GoldExport:
    """Class for generation & update of gold tables."""

    def __init__(self, table: str, args: RuntimeArgs):
        """Initiate the class including a spark session.

        Args:
            table (str): The table to write to.
            args (RuntimeArgs): The runtime arguments for the script.
        """
        self.spark = get_or_create_databricks_session()
        self.table = table
        self.args = args

    @property
    def full_table_name(self) -> str:
        """Get the full table name in the format catalog.schema.table."""
        return f"{self.args.catalog}.{self.args.schema}.{self.table}"

    def count_rows(self) -> None:
        """Return number of table rows."""
        num_rows = self.spark.sql(f"""SELECT COUNT(*) FROM {self.full_table_name}""").collect()[0]["count(1)"]
        logger.info(f"Databricks export of gold layer {self.full_table_name} with {num_rows} rows completed")

    def count_check(self) -> None:
        """Return Dataframe with counts or sum of counts of selected frames per target in descending order."""

        gold_table = (
            self.spark.read.option("skipChangeCommits", "true")
            .format("delta")
            .table(f"{self.full_table_name}")
            .limit(0)
        )
        if "count" in gold_table.columns:
            selection_count = self.spark.sql(
                f"""SELECT target, created_by, state, sum(count) as count from {self.full_table_name}
                where state = "SELECTED"
                GROUP BY target, state, created_by
                order by target DESC"""
            )
        else:
            selection_count = self.spark.sql(
                f"""SELECT target, created_by, state, count(*) from {self.full_table_name}
                    where state = "SELECTED"
                    GROUP BY target, state, created_by
                    order by target DESC"""
            )

        with pd.option_context("display.max_rows", None):
            logger.debug(f"Exported frames per target: \n {selection_count.toPandas()}")

    def run(self) -> None:
        """Do the processing of gold table."""
        logger.info(f"Starting gold layer export for {self.full_table_name}..")
        self.create_gold_table()
        self.count_rows()
        # self.count_check()

    @abstractmethod
    def create_gold_table(self) -> None:
        """Override this function with the relevant gold layer function."""
