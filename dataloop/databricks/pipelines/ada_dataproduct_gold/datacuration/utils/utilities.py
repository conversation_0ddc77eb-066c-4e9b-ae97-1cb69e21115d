"""Module maintaining the table paths needed."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Dict


def silver_table_paths() -> Dict[str, str]:
    """Returns silver table paths for each env."""

    silver_table_paths: Dict[str, str] = {}

    silver_table_paths["SILVER_TDS_ENTRY_TABLE"] = "silver.tds.file_entries"
    silver_table_paths["SILVER_VIPER_LABELCLIENT_TABLE"] = "silver.mdd.viper_labelclient_input"
    silver_table_paths["SILVER_DATAMANAGEMENT_CO_DRIVER_TAGS_TABLE"] = "silver.mdd.`datamanagement_co_driver_tags`"
    silver_table_paths["SILVER_TDS_PARENTS"] = "silver.tds.parents"
    silver_table_paths["SILVER_AUTOQC_LABEL_OBJECTS"] = "silver.autoqc.label_file_objects"
    silver_table_paths["SILVER_VIPER_FRAME_METADATA"] = "silver.mdd.viper_frame_mdd_metadata"
    silver_table_paths["SILVER_AUTOQC_LABEL_STREAMS"] = "silver.autoqc.label_file_streams"

    return silver_table_paths
