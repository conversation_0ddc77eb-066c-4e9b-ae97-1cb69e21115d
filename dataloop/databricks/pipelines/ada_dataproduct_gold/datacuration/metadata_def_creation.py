"""Module to develop datacuration tables in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from utils.datacuration_helper import GoldExport, get_databricks_config
from utils.utilities import silver_table_paths


class MetadataDefCreation(GoldExport):
    """Class responsible for update of the metadata datacuration table."""

    def create_gold_table(self) -> None:
        """Create gold layer for codriver tags."""

        silver_table_paths_dict = silver_table_paths()
        SILVER_DATAMANAGEMENT_CO_DRIVER_TAGS_TABLE = silver_table_paths_dict[
            "SILVER_DATAMANAGEMENT_CO_DRIVER_TAGS_TABLE"
        ]
        SILVER_VIPER_LABELCLIENT_TABLE = silver_table_paths_dict["SILVER_VIPER_LABELCLIENT_TABLE"]

        self.spark.sql(
            f"""
            CREATE OR REPLACE TABLE {self.full_table_name}
            USING DELTA
            AS
            SELECT mdd.file_hash, mdd.tag_group, mdd.tag_type, mdd.tag_uid, mdd.created_at, mdd.modified_at
            FROM {SILVER_VIPER_LABELCLIENT_TABLE} AS vli
            INNER JOIN {SILVER_DATAMANAGEMENT_CO_DRIVER_TAGS_TABLE} AS mdd
            ON vli.split_hash = mdd.file_hash
            WHERE vli.stream_camera_type = "FC1"
            GROUP BY mdd.file_hash, mdd.tag_group, mdd.tag_type, mdd.tag_uid, mdd.created_at, mdd.modified_at
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = MetadataDefCreation(table="datacuration_co_driver_tags", args=args)
    gold_df.run()
