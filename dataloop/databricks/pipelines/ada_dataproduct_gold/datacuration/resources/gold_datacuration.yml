variables:
  run_sp:
    default: TBD
    description: "run service principal"
  env:
    default: dev
    description: "environment, dev, qa, prod"
  catalog:
    default: gold_dev
    description: "catalog name"
  spark_version:
    default: "14.3.x-scala2.12"
    description: "Spark version"
  schema:
    default: "datacuration"
    description: "schema of the table"
  notification_email_id:
    default: "<EMAIL>"
    description: "Current channel for job failure notifications"

resources:
  jobs:
    datacuration_gold:
      # Give permission to Unicorn developers to manage run
      permissions:
        - group_name: "sg-pace-github-Data_delivery-Atlas-reader"
          level: CAN_MANAGE_RUN
      run_as:
        service_principal_name: ${var.run_sp}
      name: "Data Curation - Gold table creation"
      schedule:
        # run once a day, ideally after TDS files are imported
        quartz_cron_expression: '0 0 6 ? * * *'
        timezone_id: Europe/Amsterdam
      email_notifications:
        on_failure:
          - ${var.notification_email_id}
        no_alert_for_skipped_runs: true

      tasks:
        - task_key: dataset_definition_creation
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/dataset_def_creation.py
            parameters:
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --env
              - ${var.env}
          job_cluster_key: datacuration_job_cluster

        - task_key: metadata_table_creation
          depends_on:
            - task_key: dataset_definition_creation
          run_if: ALL_SUCCESS
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/metadata_def_creation.py
            parameters:
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --env
              - ${var.env}
          job_cluster_key: datacuration_job_cluster

      job_clusters:
        - job_cluster_key: datacuration_job_cluster
          new_cluster:
            spark_version: 14.3.x-scala2.12
            autoscale:
              min_workers: 2
              max_workers: 4
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1
            node_type_id: Standard_E32ads_v5
            enable_elastic_disk: true
            init_scripts:
            - volumes:
                destination: "/Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh"
            data_security_mode: USER_ISOLATION
            runtime_engine: PHOTON
