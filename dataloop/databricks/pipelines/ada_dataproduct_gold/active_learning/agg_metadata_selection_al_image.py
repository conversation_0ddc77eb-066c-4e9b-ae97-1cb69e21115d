"""Module containing the metadata_selection_al_image gold table for updates in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class AggMetadataSelectionActivelearningImage(GoldExport):
    """Class responsible for update of the aggregated metadata_selection_al_image gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE
            or REPLACE TABLE {self.args.catalog}.{self.args.schema}.vehicle_metadata_intervall_filtered
            select
              case
                when t.temperature < 0 then '< 0'
                when t.temperature >= 30 then '> 30'
                when t.temperature >= 20 then '20 - 30'
                when t.temperature >= 10 then '10 - 20'
                when t.temperature >= 0 then '0 - 10'
                else 'no temperature given'
              end as temperature,
              case
                when t.speed_mean > 100 then '> 100'
                when t.speed_mean >= 70 then '70 - 100'
                when t.speed_mean >= 50 then '50 - 70'
                when t.speed_mean >= 30 then '30 - 50'
                when t.speed_mean >= 15 then '15 - 30'
                when t.speed_mean >= 5 then '5 - 15'
                when t.speed_mean >= 0 then '0 - 5'
                when t.speed_mean < 0 then '< 0'
                else 'no speed_mean given'
              end as speed_mean,
              case
                when t.yaw_rate__stats__min > 1 then '> 1'
                when t.yaw_rate__stats__min >= 0 then '0 - 1'
                when t.yaw_rate__stats__min >= -0.1 then '-0.1 - 0'
                when t.yaw_rate__stats__min >= -0.2 then '-0.2 - -0.1'
                when t.yaw_rate__stats__min >= -0.4 then '-0.4 - -0.2'
                when t.yaw_rate__stats__min >= -0.6 then '-0.6 - -0.4'
                when t.yaw_rate__stats__min >= -0.8 then '-0.8 - -0.6'
                when t.yaw_rate__stats__min >= -1 then '-1 - -0.8'
                when t.yaw_rate__stats__min >= -2 then '-2 - -1'
                when t.yaw_rate__stats__min >= -3 then '-3 - -2'
                when t.yaw_rate__stats__min >= -4 then '-4 - -3'
                when t.yaw_rate__stats__min < -4 then '< -4'
                else 'no yaw_rate__stats__min given'
              end as yaw_rate__stats__min,
              case
                when t.yaw_rate__stats__max >= 3 then '3 - 4'
                when t.yaw_rate__stats__max >= 2 then '2 - 3'
                when t.yaw_rate__stats__max >= 1 then '1 - 2'
                when t.yaw_rate__stats__max >= 0.8 then '0.8 - 1'
                when t.yaw_rate__stats__max >= 0.6 then '0.6 - 0.8'
                when t.yaw_rate__stats__max >= 0.4 then '0.4 - 0.6'
                when t.yaw_rate__stats__max >= 0.2 then '0.2 - 0.4'
                when t.yaw_rate__stats__max >= 0.1 then '0.1 - 0.2'
                when t.yaw_rate__stats__max >= 0 then '0 - 0.1'
                when t.yaw_rate__stats__max >= -1 then '-1 - 0'
                when t.yaw_rate__stats__max <= -1 then '< -1'
                else 'no yaw_rate__stats__max given'
              end as yaw_rate__stats__max,
              case
                when t.precipitation > 75 then '> 75'
                when t.precipitation >= 50 then '50 - 75'
                when t.precipitation >= 25 then '25 - 50'
                when t.precipitation >= 5 then '5 - 25'
                when t.precipitation >= 0 then '0 - 5'
                when t.precipitation < 0 then '< 0'
                else 'no precipitation given'
              end as precipitation,
              case
                when t.lights__front__regular = True then 'True'
                when t.lights__front__regular = False then 'False'
                else 'no precipitation given'
              end as lights__front__regular,
              scored_date,
              camera_stream,
              target,
              state,
              created_by
            FROM
              (
                SELECT
                  base.scored_date,
                  speed__stats__mean as speed_mean,
                  environment__temperature as temperature,
                  environment__precipitation__intensity as precipitation,
                  lights__front__regular,
                  yaw_rate__stats__max,
                  yaw_rate__stats__min,
                  camera_stream,
                  target,
                  state,
                  base.created_by
                FROM
                  {self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image
                  AS base
                  INNER JOIN
                  {SILVER_PATH}.dsp_de_vehicle_metadata AS metadata ON metadata.file_hash = base.split_hash
              ) as t
            """
        )

        self.spark.sql(
            f"""
            CREATE
            or REPLACE TABLE {self.full_table_name} AS
            SELECT
              count(*) as count,
              value,
              attribute,
              scored_date,
              camera_stream,
              target,
              state,
              created_by
            FROM
              (
                SELECT
                    *
                FROM
                    {self.args.catalog}.{self.args.schema}.vehicle_metadata_intervall_filtered
                ) UNPIVOT (
                value FOR attribute IN (
                    temperature,
                    speed_mean,
                    yaw_rate__stats__min,
                    yaw_rate__stats__max,
                    precipitation,
                    lights__front__regular
                )
              )
            GROUP BY ALL
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = AggMetadataSelectionActivelearningImage(table="agg_metadata_selection_al_image", args=args)
    gold_df.run()
    gold_df.update_metadata()
