"""Module containing the agg_blockage gold table for updates in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class AggBlockage(GoldExport):
    """Class responsible for update of the aggregated blockage gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE
            or REPLACE TEMP VIEW blockage_intermediate AS
            SELECT
              base.file_hash, -- needed to count preditions per frame
              scored_date,
              base.created_by,
              camera_stream,
              task,
              state,
              target,
              case
                when size < 0 then '< 0'
                when size >= 0.95 then '> 0.95'
                when size >= 0.9 then '0.9 - 0.95'
                when size >= 0.85 then '0.85 - 0.9'
                when size >= 0.8 then '0.8 - 0.85'
                when size >= 0.75 then '0.75 - 0.8'
                when size >= 0.7 then '0.7 - 0.75'
                when size >= 0.65 then '0.65 - 0.7'
                when size >= 0.6 then '0.6 - 0.65'
                when size >= 0.55 then '0.55 - 0.6'
                when size >= 0.5 then '0.5 - 0.55'
                when size >= 0.45 then '0.45 - 0.5'
                when size >= 0.4 then '0.4 - 0.45'
                when size >= 0.35 then '0.35 - 0.4'
                when size >= 0.3 then '0.3 - 0.35'
                when size >= 0.25 then '0.25 - 0.3'
                when size >= 0.2 then '0.2 - 0.25'
                when size >= 0.15 then '0.15 - 0.2'
                when size >= 0.1 then '0.1 - 0.15'
                when size >= 0.05 then '0.05 - 0.1'
                when size >= 0 then '0 - 0.05'
                else 'no blockage given'
              end as blockage
            FROM
              {self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image
              as base
              INNER JOIN {SILVER_PATH}.mt_predictions_aggregated_segmentation AS predictions
                ON predictions.file_hash = base.file_hash
            WHERE
              task = 'blockage'

          """
        )
        self.spark.sql(
            f"""
            CREATE
                 or REPLACE TABLE {self.full_table_name} AS
            SELECT
                count(*) as count,
                task AS attribute,
                blockage AS value,
                scored_date,
                camera_stream,
                target,
                state,
                created_by
            FROM
                blockage_intermediate
            GROUP BY ALL
          """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = AggBlockage(table="agg_blockage", args=args)
    gold_df.run()
    gold_df.update_metadata()
