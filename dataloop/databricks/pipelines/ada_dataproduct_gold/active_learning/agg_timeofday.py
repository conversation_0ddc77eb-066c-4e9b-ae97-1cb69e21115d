"""Module containing the timeofday_selection_image_activelearning gold table for updates in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class AggTimeofday(GoldExport):
    """Class responsible for update of the aggregated timeofday_selection_image_activelearning gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE or REPLACE TABLE { self.full_table_name }
            SELECT
                count(*) as count,
                'time_of_day' AS attribute,
                time_of_day AS value,
                scored_date,
                camera_stream,
                target,
                state,
                created_by
            FROM
                {self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image AS table
                INNER JOIN {SILVER_PATH}.time_of_day AS time ON time.file_hash = table.split_hash
            GROUP BY ALL
          """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = AggTimeofday(table="agg_timeofday", args=args)
    gold_df.run()
    gold_df.update_metadata()
