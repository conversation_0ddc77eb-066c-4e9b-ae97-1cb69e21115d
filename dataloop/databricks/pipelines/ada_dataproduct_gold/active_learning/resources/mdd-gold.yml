variables:
  env:
    default: dev
    description: "Environment, dev, qa, prod"
  catalog:
    default: gold
    description: "Catalog name"
  schema:
    default: active_learning
    description: "Schema name"
  run_sp:
    description: "run service principal"
    lookup:
      service_principal: "sp-pace-active_learning-dbx-${var.env}"
  pause_status:
    description: Is pipeline "PAUSED"/"UNPAUSED"
    default: "PAUSED"
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"
  spark_version:
    default: "15.4.x-scala2.12"
    description: "Spark version"
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E16ads_v5_rt14.3"
  driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  num_workers:
    default: "1"
    description: "Number of workers"
  ms_teams_alert_channel_email:
    default: "<EMAIL>"
    description: "MS Teams alert channel email"

resources:
  jobs:
    active_learning:
      # Give permission to all users to manage run
      permissions:
      - group_name: users
        level: CAN_MANAGE_RUN

      name: Active Learning Gold
      tags:
        active_learning: ""

      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}

      schedule:
        quartz_cron_expression: '0 17 1 * * ?'
        timezone_id: UTC
        pause_status: ${var.pause_status}

      tasks:

        - task_key: selection_image_X_activelearning
          spark_python_task:
            python_file: ../selection_image_X_activelearning.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: agg_date_tags
          spark_python_task:
            python_file: ../agg_date_tags.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
           - task_key: selection_x_tags
           - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: selected_frames
          spark_python_task:
            python_file: ../selected_frames.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: aggregated_recording_date
          spark_python_task:
            python_file: ../aggregated_recording_date.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: detected_objects_distribution
          spark_python_task:
            python_file: ../detected_objects_distribution.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: agg_blockage
          spark_python_task:
            python_file: ../agg_blockage.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: agg_mapinfo
          spark_python_task:
            python_file: ../agg_mapinfo.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: agg_metadata_selection_al_image
          spark_python_task:
            python_file: ../agg_metadata_selection_al_image.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: agg_proposed_frames
          spark_python_task:
            python_file: ../agg_proposed_frames.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: selection_gps
          spark_python_task:
            python_file: ../selection_gps.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: selection_x_tags
          spark_python_task:
            python_file: ../selection_x_tags.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

        - task_key: agg_timeofday
          spark_python_task:
            python_file: ../agg_timeofday.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: al_job_cluster
          depends_on:
            - task_key: selection_image_X_activelearning
          libraries:
            - pypi:
                package: dbxlib==0.2.0

      job_clusters:
        - job_cluster_key: al_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            num_workers:  ${var.num_workers}
            policy_id: ${var.job_cluster_policy_id}
            enable_elastic_disk: true
            data_security_mode: USER_ISOLATION
            runtime_engine: PHOTON
            init_scripts:
            - volumes:
                destination: "/Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-dev.sh"
