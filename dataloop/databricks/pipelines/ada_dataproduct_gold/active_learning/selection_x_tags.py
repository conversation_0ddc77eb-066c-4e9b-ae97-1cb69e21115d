"""Module containing the gold table updates for selection_x_tags in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class SelectionTags(GoldExport):
    """Class responsible for update of the selection_x_tags gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE
              OR REPLACE TABLE {self.full_table_name} AS
            SELECT
              s.file_hash,
              s.created_by,
              s.state,
              s.target,
              s.camera_stream,
              scored_date,
              c.tag_group as group,
              c.tag_uid as uid
            FROM
              {self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image s
              JOIN {SILVER_PATH}.datamanagement_co_driver_tags c ON s.split_hash = c.file_hash
            WHERE
              cast(s.recorded_at as TIMESTAMP) BETWEEN cast(c.tag_start_at as TIMESTAMP)
              AND cast(c.tag_stop_at as TIMESTAMP); --TODO: range join optimization
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = SelectionTags(table="selection_x_tags", args=args)
    gold_df.run()
    gold_df.update_metadata()
