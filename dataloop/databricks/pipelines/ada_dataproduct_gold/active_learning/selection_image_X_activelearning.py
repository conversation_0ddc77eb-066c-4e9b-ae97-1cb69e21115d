"""Module containing the combined dsp-selection, dsp_de_image and active_learning gold table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class SelectionImage(GoldExport):
    """Class responsible for update of the merged selection, dsp_de_image and active learning gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE
              OR REPLACE TABLE {self.args.catalog}.{self.args.schema}.selections AS
            SELECT
              file_hash,
              created_at,
              created_by,
              state,
              target
            FROM
              {SILVER_PATH}.dsp_selection
            Union
            SELECT
              file_hash,
              created_at,
              "active-learning" AS created_by,
              "SELECTED" as state,
              "random" AS target
            FROM
              {SILVER_PATH}.active_learning
            WHERE
              file_hash LIKE 'aa%';
            """
        )

        self.spark.sql(
            f"""
            CREATE
              OR REPLACE TABLE {self.args.catalog}.{self.args.schema}.gold_table_selection_x_image AS
            SELECT
              selections.target,
              selections.state,
              selections.created_by,
              image.file_hash,
              image.split_hash,
              image.recorded_at,
              image.camera_stream
            FROM
              {self.args.catalog}.{self.args.schema}.selections AS selections
            INNER JOIN (
                SELECT
                  file_hash,
                  split_hash,
                  camera_stream,
                  from_unixtime(unix_timestamp(recorded_at, 'yyyy-MM-dd HH:mm:ss.SSS'),'yyyy-MM-dd HH:mm:ss')
                  as recorded_at
                FROM
                  {SILVER_PATH}.dsp_de_image
            ) AS image ON image.file_hash = selections.file_hash;
            """
        )

        self.spark.sql(
            f"""
            CREATE
              or REPLACE TABLE {self.full_table_name} AS
            SELECT DISTINCT
              date(active_learning.created_at) as scored_date,
              gold_table_selection_x_image.*
            FROM
              {self.args.catalog}.{self.args.schema}.gold_table_selection_x_image as gold_table_selection_x_image
            INNER JOIN {SILVER_PATH}.active_learning AS active_learning ON active_learning.file_hash =
              gold_table_selection_x_image.file_hash;
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = SelectionImage(table="gold_table_selection_x_active_learning_x_dsp_de_image", args=args)
    gold_df.run()
    gold_df.update_metadata()
