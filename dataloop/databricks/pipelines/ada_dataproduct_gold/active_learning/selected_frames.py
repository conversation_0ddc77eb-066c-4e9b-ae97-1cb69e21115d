"""Module containing the gold table updates for selected_frames in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import GoldExport, get_databricks_config


class SelectedFrames(GoldExport):
    """Class responsible for update of the selected_frames gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE
            OR REPLACE TABLE {self.full_table_name} AS
            SELECT
              file_hash,
              target,
              state,
              scored_date,
              camera_stream,
              created_by
            FROM
              {self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image
            WHERE
              state = "SELECTED"
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = SelectedFrames(table="selected_frames", args=args)
    gold_df.run()
    gold_df.update_metadata()
