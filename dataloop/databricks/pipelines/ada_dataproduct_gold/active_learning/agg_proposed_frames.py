"""Module containing the gold table updates for agg_proposed_frames in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import GoldExport, get_databricks_config


class AggregatedProposedFrames(GoldExport):
    """Class responsible for update of the agg_proposed_frames gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
          CREATE
            OR REPLACE TABLE {self.full_table_name} AS -- Can we rename this to `split_duplicates`?
          SELECT
            COUNT(*) * num_duplicates AS count,
            num_duplicates,
            scored_date,
            target,
            camera_stream,
            created_by,
            state
          FROM
            (
              SELECT
                COUNT(*) AS num_duplicates,
                scored_date,
                state,
                target,
                camera_stream,
                created_by
              FROM
                {self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image
              GROUP BY ALL
            )
          GROUP BY ALL
          """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = AggregatedProposedFrames(table="agg_proposed_frames", args=args)
    gold_df.run()
    gold_df.update_metadata()
