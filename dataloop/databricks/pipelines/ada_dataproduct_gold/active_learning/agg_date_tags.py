"""Module containing the gold table updates for agg_date_tags in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from helper import GoldExport, get_databricks_config


class AggregatedDateTags(GoldExport):
    """Class responsible for update of the agg_date_tags gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE
              OR REPLACE TABLE {self.full_table_name} AS
            SELECT
              COUNT(*) AS count,
              scored_date,
              target,
              camera_stream,
              state,
              created_by,
              group,
              uid
            FROM
              {self.args.catalog}.{self.args.schema}.selection_x_tags
            GROUP BY ALL
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = AggregatedDateTags(table="agg_date_tags", args=args)
    gold_df.run()
    gold_df.update_metadata()
