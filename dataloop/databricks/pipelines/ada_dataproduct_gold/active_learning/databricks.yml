# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================


# This is a Databricks asset bundle definition for ada_dataproduct_gold.
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.
bundle:
  name: ada_dataproduct_gold

include:
  - resources/*.yml

targets:
  user-dev:
    mode: development
    variables:
      env: dev
      catalog: viper_dsp_dev
      schema: al_gold
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/

    resources:
      jobs:
        mdd_gold:
          job_clusters:
          - job_cluster_key: al_job_cluster
            new_cluster:
              policy_id: 000CDAE4E9E2CD97
              instance_pool_id: 0524-105609-lens16-pool-fonqzcmq
              autoscale:
                min_workers: 1
                max_workers: 2

  dev:
    mode: production
    variables:
      env: dev
      catalog: gold_dev
      schema: active_learning
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_gold/active_learning/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}


  prod:
    variables:
      env: prod
      catalog: gold
      schema: active_learning
      pause_status: "UNPAUSED"
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_gold/active_learning/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
