"""Module containing the gold table updates for selection_gps in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class SelectionGPS(GoldExport):
    """Class responsible for update of the selection_gps gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        input_table = f"{self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image"

        self.spark.sql(
            f"""
            CREATE OR REPLACE TABLE {self.full_table_name} AS

            -- Get the input data filtered for relevant columns
            WITH input AS (
                SELECT
                    file_hash,
                    split_hash,
                    scored_date,
                    camera_stream,
                    target,
                    created_by,
                    state,
                    recorded_at
                FROM
                    {input_table}
            ),

            -- Join gps data and order by timestamp difference -> first entry = closest gps entry to image.
            _gps_with_timestamp_order AS (
            SELECT
                input.* EXCEPT (recorded_at, split_hash),
                gps.latitude AS lat,
                gps.longitude AS lon,
                ROW_NUMBER() OVER (
                PARTITION BY input.file_hash,
                input.recorded_at
                ORDER BY
                    ABS(
                    TIMESTAMPDIFF(SECOND, gps.time, input.recorded_at)
                    ) ASC
                ) AS rn
            FROM
                {SILVER_PATH}.dsp_de_gps AS gps
                JOIN input ON input.split_hash = gps.split_hash
            )

            -- Get the GPS data with closest timestamp
            SELECT
                * EXCEPT(rn)
            FROM
                _gps_with_timestamp_order
            WHERE
                rn = 1
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = SelectionGPS(table="selection_gps", args=args)
    gold_df.run()
    gold_df.update_metadata()
