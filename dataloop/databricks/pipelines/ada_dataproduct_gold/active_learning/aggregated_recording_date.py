"""Module containing the gold table updates for aggregated_recording_date in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import GoldExport, get_databricks_config


class AggregatedRecordingDate(GoldExport):
    """Class responsible for update of the aggregated_recoding_date gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        self.spark.sql(
            f"""
            CREATE
                OR REPLACE TABLE { self.full_table_name } AS
            SELECT
                COUNT(*) AS count,
                date(recorded_at) as recorded_date,
                scored_date,
                target,
                camera_stream,
                state,
                created_by
            FROM
                { self.args.catalog }.{ self.args.schema }.gold_table_selection_x_active_learning_x_dsp_de_image
            GROUP BY ALL
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = AggregatedRecordingDate(table="agg_recording_date", args=args)
    gold_df.run()
    gold_df.update_metadata()
