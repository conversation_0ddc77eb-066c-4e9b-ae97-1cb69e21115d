"""Module containing the detected_object_distribution gold table for updates in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class DetectedObjectDistribution(GoldExport):
    """Class responsible for update of the detected_object_distribution gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""
        self.spark.sql(
            f"""
            CREATE
                or REPLACE TABLE {self.args.catalog}.{self.args.schema}.detected_object_distribution_intermediate AS
            SELECT
                base.file_hash, -- needed to count preditions per frame
                scored_date,
                base.created_by,
                camera_stream,
                task,
                state,
                target,
                sum(count) as num_predictions
            FROM
                {self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image as base
                INNER JOIN {SILVER_PATH}.mt_predictions_aggregated_detection AS predictions
                    ON predictions.file_hash = base.file_hash
            WHERE
                task NOT IN ('lane', 'blockage', 'semseg', 'depth_from_mono')
            GROUP BY
                ALL;
            """
        )
        self.spark.sql(
            f"""
            CREATE
                or REPLACE TABLE {self.full_table_name} AS
            SELECT
                count(num_predictions) as count,
                scored_date,
                task,
                target,
                num_predictions,
                camera_stream,
                created_by,
                state
            from
                {self.args.catalog}.{self.args.schema}.detected_object_distribution_intermediate
            group by
                ALL

            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = DetectedObjectDistribution(table="detected_object_distribution", args=args)
    gold_df.run()
    gold_df.update_metadata()
