"""Module containing the mapinfo_selection_image_activelearning gold table for updates in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from helper import (
    SILVER_PATH,
    GoldExport,
    get_databricks_config,
)


class AggMapinfo(GoldExport):
    """Class responsible for update of the aggregated mapinfo_selection_image_activelearning gold table."""

    def create_gold_table(self) -> None:
        """Create gold layer."""

        input_table = f"{self.args.catalog}.{self.args.schema}.gold_table_selection_x_active_learning_x_dsp_de_image"

        self.spark.sql(
            f"""
            CREATE or REPLACE TABLE {self.full_table_name} AS

            -- Get the input data filtered for relevlant columns
            WITH input AS (
                SELECT
                    file_hash,
                    split_hash,
                    scored_date,
                    camera_stream,
                    target,
                    created_by,
                    state,
                    recorded_at
                FROM
                    {input_table}
            ),

            -- Join mapinfo data and order by timestamp difference -> first entry = closest mapinfo entry to image.
            mapinfo_with_timestamp_order AS (
                SELECT
                    input.* EXCEPT (recorded_at, split_hash),
                    positions__city,
                    positions__state,
                    positions__is_urban,
                    positions__is_ramp,
                    positions__is_tunnel,
                    positions__iso_country_code_alpha2,
                    positions__speed_limit,
                    ROW_NUMBER() OVER (
                    PARTITION BY input.file_hash,
                    input.recorded_at
                    ORDER BY
                        ABS(
                        TIMESTAMPDIFF(SECOND, other.positions__timestamp, input.recorded_at)
                        ) ASC
                    ) AS rn
                FROM
                    {SILVER_PATH}.mapinfo AS other
                    JOIN input ON input.split_hash = other.file_hash
            ),

            -- Get the input + mapinfo data with closest timestamp
            full_data AS (
                SELECT
                    * EXCEPT(rn)
                FROM
                    mapinfo_with_timestamp_order
                WHERE
                    rn = 1
            )

            -- Unpivot the mapinfo data to have a single row per attribute
            SELECT
                count(*) as count,
                value,
                attribute,
                scored_date,
                camera_stream,
                target,
                state,
                created_by
            FROM
            (
                SELECT
                    *
                FROM
                    full_data
            ) UNPIVOT (
                value FOR attribute IN (
                    positions__is_urban,
                    positions__is_tunnel,
                    positions__is_ramp,
                    positions__iso_country_code_alpha2,
                    positions__speed_limit
                )
            )
            GROUP BY ALL
            """
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = AggMapinfo(table="agg_mapinfo", args=args)
    gold_df.run()
    gold_df.update_metadata()
