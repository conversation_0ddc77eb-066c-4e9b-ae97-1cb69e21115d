# This is a Databricks asset bundle definition for cero_data_curation.
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.

bundle:
  name: cero_data_curation_gold

# General permissions
permissions:
  - group_name: "ap-pace-ado-proj-Pace-team-Data Delivery-Connected Edurance Run-members"
    level: CAN_MANAGE

variables:
  job_name:
    description: "The name of the job for CERO data curation in gold catalog."
    default: "CERO Data Curation > Gold"
  cluster_pool_id:
    description: "The ID of the instance pool to use for the CERO cluster creation."
    default: "1001-074540-scoff137-pool-nn1m66l2"
  run_as_sp:
    description: Service Principal that executes the ETL workflow
    default: 6b39e4eb-566a-41da-b30a-db72c7b14707
  pipeline_quartz_cron_schedule:
    description: "The standard schedule for the pipeline"
    default: "59 59 23 31 12 ? 2099"
  target_catalog:
    description: "The catalog in which to publish data for `gold` targeted workflows."
    default: "gold"
  cero_catalog_bronze:
    description: "The 'bronze' catalog containing CERO migrated (UC) data."
    default: "bronze"
  ms_teams_alert_channel_email:
    description: "Email address of the MS Teams channel used for receiving workflow alerts."
    default: <EMAIL> # Development Infrastructure Monitoring

include:
  - resources/cero_data_curation_gold.yml

sync:
  paths:
    - ../../common/cero
    - .

targets:
  # The 'dev' target, used for development purposes.
  # Whenever a developer deploys using 'dev', they get their own copy.
  dev:
    # We use 'mode: development' to make sure everything deployed to this target gets a prefix
    # like '[dev my_user_name]'. Setting this mode also disables any schedules and
    # automatic triggers for jobs and enables the 'development' mode for Delta Live Tables pipelines.
    mode: development
    default: true
    variables:
      target_catalog: gold_dev
      cero_catalog_bronze: cero_dev
      run_as_sp: 6b39e4eb-566a-41da-b30a-db72c7b14707 # sp-pace-dataloop-cero-dbx-dev
      cluster_pool_id: 1001-074540-scoff137-pool-nn1m66l2 # Standard_D4ads_v5
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net # datalake-dev
  qa:
    # We use 'mode: development' to make sure everything deployed to this target gets a prefix
    # like '[dev my_user_name]'. Setting this mode also disables any schedules and
    # automatic triggers for jobs and enables the 'development' mode for Delta Live Tables pipelines.
    mode: development
    variables:
      target_catalog: gold_qa
      cero_catalog_bronze: cero_qa
      run_as_sp: 20c76c25-8b96-4d38-ad53-3eb89777f9fd # sp-pace-dataloop-cero-dbx-qa
      cluster_pool_id: 1001-074300-sits136-pool-1jrhcwj6 # Standard_D4ads_v5
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net # datalake-qa
  prod:
    variables:
      cero_catalog_bronze: bronze
      run_as_sp: 06dc9a7e-99ee-4ac2-b582-906a119988ae # sp-pace-dataloop-cero-dbx-prod
      pipeline_quartz_cron_schedule: "0 30 6 ? * MON-SAT" # UTC - 06:30
      cluster_pool_id: 1001-075543-shied265-pool-in9cb7w2 # Standard_D4ads_v5
      ms_teams_alert_channel_email: <EMAIL> # CERO Alerts (dataloop)
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net # datalake-prod