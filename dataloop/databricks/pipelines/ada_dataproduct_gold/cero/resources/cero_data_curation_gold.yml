# Env-specific permissions
permissions:
  - service_principal_name: ${var.run_as_sp}
    level: CAN_MANAGE

resources:
  jobs:
    cero_data_curation:
      name: ${var.job_name}
      schedule:
        quartz_cron_expression: ${var.pipeline_quartz_cron_schedule}
        timezone_id: UTC
        pause_status: UNPAUSED
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      job_clusters:
        - job_cluster_key: cero_cluster_unity
          new_cluster:
            spark_version: 15.4.x-scala2.12
            instance_pool_id: ${var.cluster_pool_id}
            data_security_mode: USER_ISOLATION
            num_workers: 1
      max_concurrent_runs: 1
      tasks:
        - task_key: total_vehicle_kpis
          job_cluster_key: cero_cluster_unity
          max_retries: 2
          retry_on_timeout: true
          spark_python_task:
            python_file: ../src/run_sql.py
            parameters:
              - "--target_catalog"
              - "${var.target_catalog}"
              - "--cero_bronze_catalog"
              - "${var.cero_catalog_bronze}"
          libraries:
            - pypi:
                package: Jinja2==3.1.4
      run_as:
        service_principal_name: ${var.run_as_sp}
