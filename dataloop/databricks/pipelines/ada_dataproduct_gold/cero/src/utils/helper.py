"""Contains helper funtions for common functionality used by gold layer scripts."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from argparse import ArgumentParser
from typing import Dict

from jinja2 import Environment, FileSystemLoader

DISCLAIMER_PATH = "../../../common/cero/cero_disclaimer.md"
QUERY_SEPARATOR = "--- EOQ ---"
SQL_DIR_PATH = "./sql"


def parse_command_line_arguments() -> Dict[str, str]:
    """Command line arguments for running gold workflows."""

    arg_parser = ArgumentParser()

    # Since arguments are optional by default, we can include all potentially required ones here.
    # This makes for an easier, more general approach on accessing required parameters throughout the code.
    arg_parser.add_argument(
        "--target_catalog",
        dest="target_catalog",
        help="Catalog in which to publish data.",
    )
    arg_parser.add_argument(
        "--cero_bronze_catalog",
        dest="cero_bronze_catalog",
        help="Catalog containing complete CERO data.",
    )

    return arg_parser.parse_args().__dict__


def get_jinja_sql_template(file_name: str):
    """Reads a given SQL file and returns one ready-to-render jinja template."""

    environment = Environment(loader=FileSystemLoader(SQL_DIR_PATH))
    return environment.get_template(file_name)


def get_disclaimer_message() -> str:
    """Returns the CERO disclaimer message as string. Message is to be used in CERO shared tables."""

    return open(DISCLAIMER_PATH, "r").read()
