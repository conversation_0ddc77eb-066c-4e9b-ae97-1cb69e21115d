"""Modules used to run sql jobs owned by CERO in ADA as python workflows."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from utils.helper import QUERY_SEPARATOR, get_disclaimer_message, get_jinja_sql_template, parse_command_line_arguments


def _split_into_multiple_statements(full_sql_statement: str) -> list:
    return full_sql_statement.split(QUERY_SEPARATOR)


def process_vehicle_kpi_data(args: dict):
    """Function used to create and populate CERO vehicle KPI table."""

    sql_template = get_jinja_sql_template("total_vehicle_kpi.sql")

    veh_kpi_sql_query = sql_template.render(
        target_catalog=args["target_catalog"],
        cero_bronze_catalog=args["cero_bronze_catalog"],
        cero_disclaimer=get_disclaimer_message(),
        end_of_query=QUERY_SEPARATOR,
    )

    statements_to_run: list = _split_into_multiple_statements(veh_kpi_sql_query)

    for sql_statement in statements_to_run:
        spark.sql(sql_statement)


# Workflow entrypoint
if __name__ == "__main__":

    parsed_args = parse_command_line_arguments()
    process_vehicle_kpi_data(parsed_args)
