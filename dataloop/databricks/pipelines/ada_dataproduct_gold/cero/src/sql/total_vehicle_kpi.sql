-- Create target table with predefined schema
CREATE OR REPLACE TABLE {{ target_catalog }}.cero.vehicle_kpis
(
    project_name                    STRING          COMMENT 'Name of the project from which the data was received'
    ,vehicle_name                   STRING          COMMENT 'Name of the vehicle from which the data was received'
    ,vehicle_identification_number  STRING          COMMENT 'Unique identification code of the vehicle (VIN)'
    ,distance_driven_km             DECIMAL(10,2)   COMMENT 'Total distance driven by the vehicle, in kilometers'
    ,time_driven_hours              DECIMAL(10,2)   COMMENT 'Total time driven by the vehicle, in hours'
)
COMMENT "
Total recorded time and distance statistics for each individual vehicle.

{{ cero_disclaimer }}
"
TBLPROPERTIES
(
    'quality' = 'gold'
);

{{ end_of_query }}

-- Populate the target table with data
WITH calculated_distance AS (
  SELECT
      kpi.project_name,
      kpi.vehicle_name,
      kpi.vehicle_id_number AS vehicle_identification_number,
      FLOOR(kpi.distance_driven_km, 2) AS distance_driven_km
  FROM (
      SELECT
          CAST(SUM(af.distance) / 1000 AS FLOAT) AS distance_driven_km,
          af.project_name,
          af.vehicle_name,
          af.vehicle_id_number
      FROM (
          SELECT
              af.*,
              pd.project_name,
              pd.vehicle_name,
              pd.vehicle_id_number,
              hd.condition_id
          FROM {{ cero_bronze_catalog }}.cero.slv_atomic_section_fact af
          NATURAL JOIN {{ cero_bronze_catalog }}.cero.slv_project_dim pd
          NATURAL JOIN {{ cero_bronze_catalog }}.cero.slv_health_condition_dim hd
      ) af
      INNER JOIN {{ cero_bronze_catalog }}.cero.gld_conditions_roots_per_vehicles roots ON
          roots.vehicle_name == af.vehicle_name AND
          roots.condition_id == af.condition_id
      GROUP BY
          af.project_name,
          af.vehicle_name,
          af.vehicle_id_number
  ) kpi
  ORDER BY kpi.project_name, kpi.vehicle_name
),
calculated_time AS (
  SELECT 
    vehicle_id_number, 
    vehicle_name, 
    project_name, 
    organization, 
    FLOOR(SUM(total_duration_seconds / 3600), 2) AS time_driven_hours  
  FROM {{ cero_bronze_catalog }}.cero.gld_vehicle_drive_kpis
  GROUP BY vehicle_id_number, vehicle_name, project_name, organization
)
INSERT OVERWRITE {{ target_catalog }}.cero.vehicle_kpis
SELECT
  calculated_distance.*,
  calculated_time.time_driven_hours
FROM calculated_distance
NATURAL JOIN calculated_time
