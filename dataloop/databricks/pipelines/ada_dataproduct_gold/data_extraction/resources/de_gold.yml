variables:
  env:
    default: dev
    description: "Environment, dev, qa, prod"
  catalog:
    default: gold
    description: "Catalog name"
  schema:
    default: data_extraction
    description: "Schema name"
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"
  spark_version:
    default: "15.4.x-scala2.12"
    description: "Spark version"
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E16ads_v5_rt14.3"
  driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  num_workers:
    default: "1"
    description: "Number of workers"
  run_sp:
    description: "run service principal"
    lookup:
      service_principal: "sp-pace-dataloop-data-extraction-dbx-${var.env}"
  pause_status:
    description: Is pipeline "PAUSED"/"UNPAUSED"
    default: "UNPAUSED"

resources:
  jobs:
    data_extraction:
      # Give permission to all users to manage run
      permissions:
      - group_name: users
        level: CAN_MANAGE_RUN

      name: Data Extraction Gold
      tags:
        data_extraction: ""

      schedule:
        quartz_cron_expression: '0 17 5 * * ?'
        timezone_id: UTC
        pause_status: ${var.pause_status}

      tasks:

        - task_key: dsp_de_tradex_input
          spark_python_task:
            python_file: ../dsp_de_tradex_input.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: data_extraction_job_cluster
        
        - task_key: dsp_de_tradex_input_new
          spark_python_task:
            python_file: ../dsp_de_tradex_input_new.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: data_extraction_job_cluster
        
        - task_key: rectification_source
          spark_python_task:
            python_file: ../rectification_source.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: data_extraction_job_cluster
        
        - task_key: rectification_targets
          spark_python_task:
            python_file: ../rectification_targets.py
            parameters:
              - -e
              - ${var.env}
              - -c
              - ${var.catalog}
              - -s
              - ${var.schema}
          job_cluster_key: data_extraction_job_cluster

      job_clusters:
        - job_cluster_key: data_extraction_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            num_workers:  ${var.num_workers}
            policy_id: ${var.job_cluster_policy_id}
            enable_elastic_disk: true
            data_security_mode: USER_ISOLATION
            runtime_engine: PHOTON
            init_scripts:
            - volumes:
                destination: "/Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-dev.sh"
