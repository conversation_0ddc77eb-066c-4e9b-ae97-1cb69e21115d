# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================


# This is a Databricks asset bundle definition for ada_dataproduct_gold.
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.
bundle:
  name: ada_dataproduct_gold

include:
  - resources/*.yml

targets:
  user-dev:
    mode: development
    variables:
      env: dev
      catalog: viper_dsp_dev
      schema: data_extraction
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/

  dev:
    mode: production
    variables:
      env: dev
      catalog: gold_dev
      schema: data_extraction
      pause_status: "PAUSED"
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_gold/data_extraction/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}

  prod:
    variables:
      env: prod
      catalog: gold
      schema: data_extraction
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_gold/data_extraction/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
