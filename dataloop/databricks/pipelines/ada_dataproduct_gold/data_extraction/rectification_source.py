"""Module containing the RectificationSourceClient class for creating rect source table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

from helper import get_databricks_config, get_or_create_databricks_session

logging.basicConfig()
logger = logging.getLogger("dsp_de_rectification_source")
logger.setLevel(logging.DEBUG)


class RectificationSourceClient:
    """Class for creating the RectificationSourceClient input table in DBX."""

    def __init__(self, schema: str, catalog: str):
        """Initialize the RectificationSourceClient class."""
        self.schema = schema
        self.catalog = catalog
        self.spark = get_or_create_databricks_session()

        self.rectification_source_table = f"{self.catalog}.{self.schema}.rectification_source"

    def create_rect_source_table(self) -> None:
        """Create the rectification source table in silver layer."""

        logger.info(f"Creating Rectification Source table")
        self.spark.sql(
            f"""
            CREATE OR REPLACE TABLE {self.rectification_source_table}
            WITH calstore_aggregated AS (
                SELECT 
                    syscal_int.syscal_uid,
                    syscal_int.syscal_name,
                    syscal_int.device_role_long,
                    syscal_int.device_uid,
                    syscal_int.intrinsic_uid,
                    ARRAY(syscal_int.quaternion_i, syscal_int.quaternion_j, syscal_int.quaternion_k, syscal_int.quaternion_w) as rotation,
                    ARRAY(syscal_int.x_m, syscal_int.y_m, syscal_int.z_m) as position,
                    device_int.serial_no,
                    device_int.contributed_v01,
                    device_int.contributed_v02,
                    device_int.opera_v01,
                    device_int.rational_radial_v01,
                    imager_geom.active_pixels_col_offset_px,
                    ARRAY(imager_geom.active_pixels_col_offset_px, imager_geom.active_pixels_row_offset_px) as roi_offset,
                    ARRAY(imager_geom.active_pixels_width_px, imager_geom.active_pixels_height_px) as roi_dimensions,
                    ARRAY(INT(imager_geom.active_pixels_width_px), INT(imager_geom.active_pixels_height_px)) as active_px_dimensions,
                    ARRAY(INT(imager_geom.sensor_width_px), INT(imager_geom.sensor_height_px)) as sensor_dimensions
                FROM silver.calstore.syscal_intrinsics AS syscal_int
                LEFT JOIN silver.calstore.device_intrinsics AS device_int
                USING (device_uid)
                LEFT JOIN silver.calstore.imager_geometries AS imager_geom
                USING (device_uid)
                WHERE device_int.device_type = 'Camera'
            ),
            rect_source AS (
                SELECT
                    syscal_uid,
                    syscal_name,
                    device_role_long,
                    CASE
                        WHEN device_role_long == "FrontCameraWide" THEN "FC1"
                        WHEN device_role_long == "TopViewCameraLeft" THEN "TVleft"
                        WHEN device_role_long == "TopViewCameraRight" THEN "TVright"
                        WHEN device_role_long == "TopViewCameraFront" THEN "TVfront"
                        WHEN device_role_long == "TopViewCameraRear" THEN "TVrear"
                    end AS camera_stream_name,
                    CASE
                    WHEN contributed_v01 is not NULL THEN named_struct(
                        "name",
                        "Contributed",
                        "params",
                        contributed_v01
                    )
                    WHEN opera_v01 is not NULL THEN named_struct(
                        "name",
                        "Opera",
                        "params",
                        opera_v01
                    )
                    WHEN rational_radial_v01 is not NULL THEN named_struct(
                        "name",
                        "RationalRadial",
                        "params",
                        rational_radial_v01
                    )
                    ELSE NULL
                    END AS pre_cam_model,
                    parse_json(pre_cam_model.params) AS cam_model_params,
                    CASE
                    WHEN syscal_name LIKE "%JP_G01%"
                    THEN active_px_dimensions
                    ELSE sensor_dimensions
                    END AS image_dimensions,
                    named_struct(
                        "type",
                        pre_cam_model.name,
                        "SN",
                        serial_no,
                        "image_dimensions",
                        image_dimensions,
                        "focal_length",
                        cam_model_params :focal_length,
                        "parameter",
                        cam_model_params :parameter,
                        "principal_point",
                        cam_model_params :principal_point,
                        "embedded_lines",
                        ARRAY(0, 0),
                        "roi_offset",
                        roi_offset,
                        "roi_dimensions",
                        roi_dimensions
                    ) AS int_params,
                    named_struct(
                        "translation",
                        position,
                        "rotation",
                        rotation
                    ) AS ext_params,
                    named_struct(
                        "intrinsics",
                        int_params,
                        "extrinsics",
                        ext_params
                        ) AS source_calib,
                    parse_json(to_json(source_calib)) AS source_json
                FROM calstore_aggregated
            )
            SELECT
            syscal_uid,
            syscal_name,
            camera_stream_name,
            device_role_long,
            source_json
            FROM rect_source
            ;
            """
        )


if __name__ == "__main__":  # pragma no cover
    args = get_databricks_config()

    rect_source_client = RectificationSourceClient(schema=args.schema, catalog=args.catalog)
    rect_source_client.create_rect_source_table()

    spark = get_or_create_databricks_session()
    spark.sql(
        f"""
            ALTER TABLE {rect_source_client.rectification_source_table} SET TAGS (
            'responsible_domain'='Visual Perception',
            'responsible_team'='Data Selection and Preparation - Data Extraction',
            'refresh_interval'='P1D'
            );
        """
    )
