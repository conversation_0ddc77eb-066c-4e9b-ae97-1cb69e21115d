"""Module containing the ectificationTargetsClient class for creating rect targets table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

from helper import get_databricks_config, get_or_create_databricks_session

logging.basicConfig()
logger = logging.getLogger("dsp_de_rectification_targets")
logger.setLevel(logging.DEBUG)


class RectificationTargetsClient:
    """Class responsible for creating the rectification targets table in databricks."""

    def __init__(self, schema: str, catalog: str):
        """Initialize the RectificationTargetsClient class."""
        self.schema = schema
        self.catalog = catalog

        self.spark = get_or_create_databricks_session()

        self.rect_targets_table = f"{self.catalog}.{self.schema}.rectification_targets"

    def create_rect_targets_table(self) -> None:
        """Create the rectification targets table."""

        logger.info(f"Creating rectification targets table")
        self.spark.sql(
            f"""
            CREATE
            OR REPLACE TABLE {self.rect_targets_table}
            SELECT
            entry :calstorage_syscal_uid,
            case
                when stream == "image_fc1" then "FC1"
                when stream == "image_tv_left" then "TVleft"
                when stream == "image_tv_right" then "TVright"
                when stream == "image_tv_front" then "TVfront"
                when stream == "image_tv_rear" then "TVrear"
            end as camera_stream_name,
            stream,
            extractor_version,
            int(LPAD(split_part(extractor_version, ".", 1), 3, 0) * 1000000 + 
            LPAD(split_part(extractor_version, ".", 2), 3, 0) * 1000 + 
            LPAD(split_part(extractor_version, ".", 3), 3, 0)) as extractor_version_int,
            entry :rectification_target as rectification_target
            from
            silver.mdd.dsp_de_split_extracted
            where
            stream like "image_%"
            and (
                entry_state = "FINISHED"
                or entry_state is NULL
            )
            and entry :calstorage_syscal_uid is not NULL
            GROUP BY ALL;
            """
        )


if __name__ == "__main__":  # pragma no cover
    args = get_databricks_config()

    rect_targets_client = RectificationTargetsClient(schema=args.schema, catalog=args.catalog)
    rect_targets_client.create_rect_targets_table()

    spark = get_or_create_databricks_session()
    spark.sql(
        f"""
            ALTER TABLE {rect_targets_client.rect_targets_table} SET TAGS (
            'responsible_domain'='Visual Perception',
            'responsible_team'='Data Selection and Preparation - Data Extraction',
            'refresh_interval'='P1D'
            );
        """
    )
