"""Module containing the TradexInput class for creating the viper labelclient input table in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

from helper import get_databricks_config, get_or_create_databricks_session

logging.basicConfig()
logger = logging.getLogger("dsp_de_tradex_input")
logger.setLevel(logging.DEBUG)


class Constants:
    """Class containing the constants for the TradexInputClient class."""

    CUTOFF_VERSION = "109003"


class TradexInputClient:
    """Class responsible for creating the viper labelclient input table in databricks."""

    def __init__(self, schema: str, catalog: str):
        """Initialize the ViperLabelClient class."""
        self.schema = schema
        self.catalog = catalog

        self.spark = get_or_create_databricks_session()

        self.training_input_table = f"{self.catalog}.{self.schema}.dsp_de_training_input"
        self.bk_training_table = f"{self.catalog}.{self.schema}.dsp_de_bookkeeper_training"
        self.bk_training_extended_table = f"{self.catalog}.{self.schema}.dsp_de_bookkeeper_training_extended"

    def create_training_input_table(self) -> None:
        """Create the frame mapping table as silver layer."""

        logger.info(f"Creating table training adam")
        self.spark.sql(
            f"""
            CREATE
            OR REPLACE TABLE {self.training_input_table} AS WITH labeled_frames AS (
            SELECT
                DISTINCT split_hash,
                stream_hash,
                frame_recorded_unix AS timestamp,
                stream_camera_type AS camera_stream,
                frame_number,
                project
            FROM
                silver.mdd.viper_labelclient_input
            where
                project = "ALLIANCE"
            )
            SELECT
            *
            FROM
            labeled_frames
            """
        )

    def create_bookkeeper_training_table(self) -> None:
        """Create the frame mapping table as silver layer."""

        logger.info(f"Creating table bookkeeper training adam")
        self.spark.sql(
            f"""
            CREATE
            OR REPLACE TABLE {self.bk_training_table} WITH artifacts AS (
            SELECT
                DISTINCT stream_hash,
                frame_number,
                color_space,
                view,
                view_type,
                compression__algorithm
            FROM
                silver.mdd.dsp_de_image
            where
                extractor_version_int >= {Constants.CUTOFF_VERSION}
                AND project = "ALLIANCE"
                AND (
                (
                    color_space in ("YUV")
                    AND view in ("Mid", "Far", "Wide", "Label")
                )
                OR color_space == "None"
                )
            ),
            raw_artifacts AS (
            SELECT
                stream_hash,
                frame_number,
                True as raw
            from
                artifacts
            where
                color_space == "None"
            ),
            yuv_all AS (
            SELECT
                stream_hash,
                frame_number,
                view,
                view_type
            from
                artifacts
            where
                color_space == "YUV"
                AND compression__algorithm == ""
            ),
            yuv_label AS (
            SELECT
                DISTINCT stream_hash,
                frame_number,
                True as yuv_label
            from
                yuv_all
            where
                view = "Label"
            ),
            yuv_trifocal AS (
            SELECT
                stream_hash,
                frame_number,
                view
            from
                yuv_all
            where
                view_type = "Trifocal"
            ),
            yuv_far_tri AS (
            SELECT
                stream_hash,
                frame_number,
                True as yuv_far_tri
            from
                yuv_trifocal
            where
                view = "Far"
            ),
            yuv_mid_tri AS (
            SELECT
                stream_hash,
                frame_number,
                True as yuv_mid_tri
            from
                yuv_trifocal
            where
                view = "Mid"
            ),
            yuv_wide_tri AS (
            SELECT
                stream_hash,
                frame_number,
                True as yuv_wide_tri
            from
                yuv_trifocal
            where
                view = "Wide"
            )
            SELECT
            *,
            CASE
                WHEN camera_stream = "FC1" THEN CASE
                    WHEN raw is True
                    and yuv_far_tri is True
                    and yuv_mid_tri is True
                    and yuv_wide_tri is True THEN True
                    ELSE False
                END
                WHEN camera_stream LIKE "TV%" THEN CASE
                    when raw is True
                    and yuv_label is True THEN True
                    ELSE False
                END
            END as is_done
            from
                {self.training_input_table}
                LEFT JOIN raw_artifacts USING (stream_hash, frame_number)
                LEFT JOIN yuv_far_tri USING (stream_hash, frame_number)
                LEFT JOIN yuv_mid_tri USING (stream_hash, frame_number)
                LEFT JOIN yuv_wide_tri USING (stream_hash, frame_number)
                LEFT JOIN yuv_label USING (stream_hash, frame_number)
            """
        )

    def create_bookkeeper_training_extended_table(self) -> None:
        """Create the frame mapping table as silver layer."""

        logger.info(f"Creating table bookkeeper training adam")

        if not self.spark.catalog.tableExists(f"{self.bk_training_extended_table}"):
            self.spark.sql(
                f"""
                CREATE TABLE {self.bk_training_extended_table} (
                    stream_hash STRING,
                    timestamp STRING,
                    status STRING
                );
                """
            )

        self.spark.sql(
            f"""
            CREATE
            OR REPLACE TABLE {self.bk_training_extended_table} WITH tds_streams AS (
            SELECT
                file_hash as stream_hash,
                file_state
            from
                silver.tds.file_entries
            where
                file_extension IN ("avi", "txt")
            ),
            raw_frames AS (
            SELECT
                DISTINCT stream_hash,
                recorded_at_unix as timestamp,
                True as raw_available
            from
                silver.mdd.dsp_de_image
            where
                color_space = "None"
                and extractor_version_int >= 70002
            ),
            existing_status AS (
            SELECT
                DISTINCT
                stream_hash,
                timestamp,
                status as existing_status
            from
                {self.bk_training_extended_table}
            WHERE
                status = "STARTED"
            )
            SELECT
            bookkeeper.*,
            CASE
                WHEN is_done is True THEN "DONE"
                WHEN existing_status = "STARTED" THEN 'STARTED'
                ELSE CASE
                    WHEN file_state = "ACTIVE" THEN "UNPROCESSED"
                    WHEN file_state in ("DELETED", "ARCHIVED") THEN CASE
                        WHEN raw_available is True then "RAW_AVAILABLE"
                        ELSE file_state
                    END
                    ELSE "UNKNOWN"
                END
            END AS status
            from
            {self.bk_training_table} as bookkeeper
            LEFT JOIN tds_streams USING (stream_hash)
            LEFT JOIN raw_frames USING (stream_hash, timestamp)
            LEFT JOIN existing_status as existing USING (stream_hash, timestamp);
            """
        )


if __name__ == "__main__":  # pragma no cover
    args = get_databricks_config()

    viper_label_client = TradexInputClient(schema=args.schema, catalog=args.catalog)
    viper_label_client.create_training_input_table()
    viper_label_client.create_bookkeeper_training_table()
    viper_label_client.create_bookkeeper_training_extended_table()

    spark = get_or_create_databricks_session()
    for table_name in [
        viper_label_client.training_input_table,
        viper_label_client.bk_training_table,
        viper_label_client.bk_training_extended_table,
    ]:
        spark.sql(
            f"""
                ALTER TABLE {table_name} SET TAGS (
                'responsible_domain'='Visual Perception',
                'responsible_team'='Data Selection and Preparation - Data Extraction',
                'refresh_interval'='P1D'
                );
            """
        )
