"""Stardog load class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from pathlib import Path
from typing import Any, ClassVar, Sequence

from jinja2 import Environment, StrictUndefined, Template
from pyspark.sql import DataFrame, SparkSession
from rddlib import setup_databricks_logging
from rddlib.auth_utils import get_databricks_pat, get_rdd_secret, get_stardog_access_token
from rddlib.dbx_utils import FullSchemaName, FullTableName, get_dbx_env_catalog, get_dbx_sql_warehouse_url
from rddlib.utils import get_bundle_dir
from stardog import Connection
from stardog.admin import Admin
from stardog.content import File, MappingRaw
from stardog.exceptions import StardogException

logger = logging.getLogger(__name__)

BUNDLE_DIR = get_bundle_dir()


def _parse_resource_path(value: str) -> Path:
    """Parses the path of a resource.

    The path may be given relative to the bundle root.
    """
    result = Path(value)
    if not result.is_absolute():
        result = BUNDLE_DIR / value

    result = result.resolve()

    if not result.exists() or not result.is_file():
        raise ValueError(f"'{value}' is not an existing file.")

    return result


class StardogLoader:
    """Class for loading data from a Databricks table to Stardog.

    It provides a CLI entrypoint via the 'cli_entrypoint' classmethod
    and this file can be directly called as an executable script.

    You may inherit from this class to introduce custom functionality.
    Override the '_filter_source_table' method to apply custom filtering on the source table.
    In this case, you might want to also override the '_cli_construct_arg_parser' classmethod
    to add additional command line arguments to the CLI entrypoint.
    """

    # DBX
    spark: SparkSession
    source_table_full: FullTableName
    log_schema_full: FullSchemaName
    # Stardog
    stardog_db: str
    mapping_file: Path
    namespaces_file: Path
    ontology_file: Path

    def __init__(
        self,
        spark: SparkSession,
        source_table_full: FullTableName,
        log_schema_full: FullSchemaName,
        stardog_db: str,
        mapping_file: Path,
        namespaces_file: Path,
        ontology_file: Path,
    ) -> None:
        """Initializes a load to Stardog.

        Args:
            spark (SparkSession): _description_
            source_table_full (FullTableName): _description_
            log_schema_full (FullSchemaName): _description_
            stardog_db (str): _description_
            mapping_file (Path): _description_
            namespaces_file (Path): _description_
            ontology_file (Path): _description_
        """
        self.spark = spark
        self.log_schema_full = log_schema_full

        self.source_table_full = source_table_full
        self.stardog_db = stardog_db
        self.mapping_file = mapping_file
        self.namespaces_file = namespaces_file
        self.ontology_file = ontology_file

    def load(self, **kwargs: Any) -> None:
        """Loads data from a source table to a Stardog database.

        All keyword arguments passed to this method get passed down to the '_filter_source_table' method.
        """
        # Init Stardog database
        stardog_conn_options = self._get_stardog_connection_options()
        self._init_stardog_db(stardog_conn_options)

        # Prepare source dataframe
        source_df = self._read_source_table()
        source_df = self._filter_source_table(source_df, **kwargs)

        # Load mapping template
        mapping_template = self.mapping_file.read_text()
        logger.info(f"Load SMS mapping from '{self.mapping_file}'")

        self._load_to_stardog(source_df, mapping_template, stardog_conn_options)

    def _filter_source_table(self, source_df: DataFrame, **kwargs: Any) -> DataFrame:
        """Filters the source table.

        All keyword arguments from 'load' method get passed down to this method.
        You may override this method in a subclass.
        The default implementation does nothing.

        Args:
            source_df (DataFrame): _description_

        Returns:
            DataFrame: _description_
        """
        return source_df

    def _read_source_table(self) -> DataFrame:
        """Reads data from the source table."""
        logger.info(f"Load table '{self.source_table_full.as_str_unquoted()}' as dataframe")
        return self.spark.read.table(str(self.source_table_full))

    def _get_stardog_connection_options(self) -> dict[str, Any]:
        """Returns the connection options to connect to the Stardog server."""
        logger.info("Retrieve Stardog connection options")
        return {
            "endpoint": get_rdd_secret("rdd-stardog-url"),
            "auth": get_stardog_access_token(),
        }

    def _get_databricks_jdbc_connection_options(self) -> dict[str, Any]:
        """Returns the connection options to connect to a Databricks JDBC."""
        logger.info("Retrieve Databricks JDBC connection options")

        from pyspark.dbutils import DBUtils

        url = get_dbx_sql_warehouse_url().replace("jdbc:spark", "jdbc:databricks")
        token = get_databricks_pat()

        dbutils = DBUtils(SparkSession.builder.getOrCreate())
        user = dbutils.secrets.get(scope="rdd", key="stardog-jdbc-spn-client-id")

        return {
            "jdbc.url": url,
            "jdbc.username": user,
            "jdbc.password": token,
            "query.translation": "DEFAULT",
            "jdbc.driver": "com.databricks.client.jdbc.Driver",
        }

    def _init_stardog_db(self, stardog_conn_options: dict[str, Any]) -> None:
        """Initializes the Stardog database.

        Creates the database if it does not exist.
        Imports common namespaces from a file.
        Loads an ontology from a file.
        """
        with Admin(**stardog_conn_options) as admin:
            # Create db if it does not exist
            try:
                db = admin.database(self.stardog_db)
            except StardogException:
                db = admin.new_database(self.stardog_db)
                logger.info(f"Stardog database '{self.stardog_db}' does not exist. Creating it")

            logger.info(f"Initialize Stardog database '{self.stardog_db}'")

            # Import namespaces
            if self.namespaces_file.exists():
                logger.info(f"Import namespaces from '{self.namespaces_file}'")
                db.import_namespaces(File(str(self.namespaces_file)))

        # Load ontology
        if self.ontology_file.exists():
            logger.info(f"Load ontology from '{self.ontology_file}'")
            with Connection(self.stardog_db, **stardog_conn_options) as conn:
                conn.begin()
                conn.add(File(str(self.ontology_file)))
                conn.commit()

    def _materialize_graph_from_table(
        self,
        table_full: FullTableName,
        mapping_template: Template,
        stardog_conn_options: dict[str, Any],
        dbx_jdbc_conn_options: dict[str, Any],
    ) -> None:
        """Materializes a Stardog graph from a Databricks table.

        The mapping template must contain a 'table' argument to properly set the table to load.

        Args:
            table_full (FullTableName): _description_
            mapping_template (Template): _description_
            stardog_conn_options (dict[str, Any]): _description_
            dbx_jdbc_conn_options (dict[str, Any]): _description_
        """
        logger.info("Load data to Stardog")
        mapping = mapping_template.render(table=str(table_full))
        with Admin(**stardog_conn_options) as admin:
            admin.materialize_virtual_graph(
                db=self.stardog_db,
                mappings=MappingRaw(mapping),
                options={
                    **dbx_jdbc_conn_options,
                    "sql.catalog": table_full.catalog,
                    "sql.schemas": str(table_full._schema_full),
                },
            )

    def _load_tmp_table_to_stardog(
        self,
        source_df: DataFrame,
        tmp_table_full: FullTableName,
        mapping_template: Template,
        stardog_conn_options: dict[str, Any],
        dbx_jdbc_conn_options: dict[str, Any],
    ) -> None:
        """Loads the data from a temporary table to the Stardog database."""
        spark = SparkSession.builder.getOrCreate()

        # Write partition to temporary table
        logger.info(f"Write data to temporary table '{tmp_table_full.as_str_unquoted()}'")
        source_df.write.saveAsTable(str(tmp_table_full), format="delta", mode="overwrite")

        # Load materialized view from temporary table
        try:
            self._materialize_graph_from_table(
                tmp_table_full,
                mapping_template,
                stardog_conn_options,
                dbx_jdbc_conn_options,
            )
        finally:
            # Make sure to always remove temporary table
            logger.info(f"Drop temporary table '{tmp_table_full.as_str_unquoted()}'")
            spark.sql(f"DROP TABLE {tmp_table_full}")

    def _load_to_stardog(
        self,
        source_df: DataFrame,
        mapping_template_str: str,
        stardog_conn_options: dict[str, Any],
    ) -> None:
        """Loads the data from the filtered source table to the Stardog database."""
        # Get Databricks JDBC connection options
        dbx_jdbc_conn_options = self._get_databricks_jdbc_connection_options()
        # Load mapping template
        mapping_template = Environment(undefined=StrictUndefined).from_string(mapping_template_str)

        # Short Path: If _filter_source_table was not overridden, directly load from source table
        if type(self)._filter_source_table is StardogLoader._filter_source_table:
            logger.info("Using short execution path as no custom filter is applied")
            self._materialize_graph_from_table(
                self.source_table_full,
                mapping_template,
                stardog_conn_options,
                dbx_jdbc_conn_options,
            )
            return

        tmp_table_full = FullTableName(
            self.log_schema_full, f"stardog_load_{self.source_table_full.as_str_snake_case()}"
        )
        self._load_tmp_table_to_stardog(
            source_df,
            tmp_table_full,
            mapping_template,
            stardog_conn_options,
            dbx_jdbc_conn_options,
        )

    # Class fields
    CLI_DESCRIPTION: ClassVar[str] = "Load data from a Databricks table to a Stardog database."
    """A description which shall be shown when calling CLI help page."""

    @classmethod
    def _cli_construct_arg_parser(cls) -> argparse.ArgumentParser:
        """Parse command line arguments of a Stardog load script."""
        parser = argparse.ArgumentParser(description=cls.CLI_DESCRIPTION)

        parser.add_argument("-r", "--run_id", type=str, default=None)
        parser.add_argument(
            "--table",
            type=FullTableName.parse,
            action="store",
            required=True,
            help="The full name of the table to load to a Stardog database.",
        )
        parser.add_argument(
            "--log_schema",
            type=lambda v: FullSchemaName.parse(f"{get_dbx_env_catalog('gold')}.{v}", is_generic_catalog=False),
            action="store",
            required=True,
            help="The name of the gold schema where to store loggind and other additional information.",
        )
        parser.add_argument(
            "--stardog_db",
            type=str,
            action="store",
            required=True,
            help="The name of the Stardog database to load to.",
        )
        parser.add_argument("--mapping", type=_parse_resource_path, action="store", required=True)
        parser.add_argument("--namespaces", type=_parse_resource_path, action="store", default=None)
        parser.add_argument("--ontology", type=_parse_resource_path, action="store", default=None)

        return parser

    @classmethod
    def _cli_parse_args(cls, argv: Sequence[str] | None = None) -> argparse.Namespace:
        """Parse command line arguments of a Stardog load script."""
        parser = cls._cli_construct_arg_parser()
        return parser.parse_args(argv)

    @classmethod
    def cli_entrypoint(cls, argv: Sequence[str] | None = None) -> None:
        """Entry point for the command line interface of a KPI script."""
        args = vars(cls._cli_parse_args(argv))
        run_id: str = args.pop("run_id")
        source_table_full: FullTableName = args.pop("table")
        log_schema_full: FullSchemaName = args.pop("log_schema")

        spark = SparkSession.builder.getOrCreate()

        # Setup logging
        setup_databricks_logging(
            log_schema_full,
            f"stardog_load/{source_table_full.as_str_snake_case()}",
            run_id=run_id,
            enabled_loggers=["common"],
        )

        stardog_load = cls(
            spark,
            source_table_full,
            log_schema_full,
            args.pop("stardog_db"),
            args.pop("mapping"),
            args.pop("namespaces"),
            args.pop("ontology"),
        )
        stardog_load.load(**args)


if __name__ == "__main__":
    StardogLoader.cli_entrypoint()
