"""Stardog stream version load class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from abc import ABC, abstractmethod
from typing import Any

from pyspark.sql import DataFrame
from rddlib.dbx_utils import FullTableName
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from .stardog_stream_loader import StardogStreamLoader  # noqa: E402

logger = logging.getLogger(__name__)


class StardogStreamVersionLoader(StardogStreamLoader, ABC):
    """Class for loading data from a Databricks table to Stardog using structured streaming.

    The data is filtered by the values of a version table which is read as a stream.
    That way, only versions that have not been loaded before will be used.

    In contrast to the other loaders, this class must be subclassed and the '_filter_by_versions'
    method must be overridden before it can be used.
    Therefore, this file cannot be executed directly.
    """

    def _get_checkpoint_path(self, version_table: FullTableName) -> str:  # type: ignore[override]
        """Construct path for checkpoints."""
        return f"/Volumes/{self.log_schema_full.catalog}/{self.log_schema_full.schema}/checkpoint_locations/stardog_stream_version_load_{version_table.as_str_snake_case()}_{self.source_table_full.as_str_snake_case()}"  # noqa: E501

    def load(self, version_table: FullTableName, **kwargs: Any) -> None:  # type: ignore[override]
        """Loads data from a source table to a Stardog database.

        The source data is filtered by a streaming version table.
        All additional keyword arguments passed to this method get passed down to the '_filter_source_table' method.
        """
        # Init Stardog database
        stardog_conn_options = self._get_stardog_connection_options()
        self._init_stardog_db(stardog_conn_options)

        # Read version table as streaming dataframe
        logger.info(f"Load table '{version_table.as_str_unquoted()}' as streaming dataframe")
        version_df = self.spark.readStream.table(str(version_table))

        # Read source table as regular dataframe
        logger.info(f"Load table '{self.source_table_full.as_str_unquoted()}' as regular dataframe")
        source_df = self.spark.read.table(str(self.source_table_full))

        # Filter source table by versions
        filtered_df = self._filter_by_versions(source_df, version_df)
        assert filtered_df.isStreaming, "The filtered dataframe must be a streaming dataframe."
        # Keep only source_df columns
        filtered_df = filtered_df.select(source_df["*"])
        # Filter source table by additional conditions
        filtered_df = self._filter_source_table(filtered_df, **kwargs)

        # Load mapping template
        mapping_template = self.mapping_file.read_text()
        logger.info(f"Load SMS mapping from '{self.mapping_file}'")

        checkpoint_path = self._get_checkpoint_path(version_table)
        self._load_to_stardog(filtered_df, mapping_template, stardog_conn_options, checkpoint_path)

    @abstractmethod
    def _filter_by_versions(self, source_df: DataFrame, version_df: DataFrame) -> DataFrame:
        """Filter the source table by the versions to load.

        The version_df is a streaming dataframe containing only the versions to load.
        The output dataframe must be a streaming dataframe as well,
        so you should join the source_df with the version_df and return the result.
        This method must be overridden in a subclass.
        """
        pass

    @classmethod
    def _cli_construct_arg_parser(cls) -> argparse.ArgumentParser:
        """Parse command line arguments of a Stardog load script."""
        parser = super()._cli_construct_arg_parser()
        parser.add_argument(
            "--version_table",
            type=FullTableName.parse,
            action="store",
            required=True,
            help="The table containing the versions to load.",
        )

        return parser


if __name__ == "__main__":
    raise RuntimeError(
        "This script cannot be executed directly."
        f" Refer to the documentation of {StardogStreamVersionLoader.__name__} for more information."
    )
