"""Stardog batch load class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from math import ceil
from typing import Any, ClassVar

from jinja2 import Environment, StrictUndefined
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, spark_partition_id
from rddlib.dbx_utils import FullTableName
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from .stardog_loader import StardogLoader

logger = logging.getLogger(__name__)


class StardogBatchLoader(StardogLoader):
    """Class for loading data from a Databricks table to Stardog in batches.

    The maximum batch size can be defined by setting the 'MAX_BATCH_SIZE' class variable
    in a subclass.

    It provides a CLI entrypoint via the 'cli_entrypoint' classmethod
    and this file can be directly called as an executable script.

    You may inherit from this class to introduce custom functionality.
    Override the '_filter_source_table' method to apply custom filtering on the source table.
    In this case you might want to also override the '_cli_construct_arg_parser' classmethod
    to add additional command line arguments to the CLI entrypoint.
    """

    def _load_to_stardog(
        self,
        source_df: DataFrame,
        mapping_template_str: str,
        stardog_conn_options: dict[str, Any],
    ) -> None:
        """Loads the data from the filtered source table to the Stardog database."""
        # Get Databricks JDBC connection options
        dbx_jdbc_conn_options = self._get_databricks_jdbc_connection_options()
        # Load mapping template
        mapping_template = Environment(undefined=StrictUndefined).from_string(mapping_template_str)

        num_rows = source_df.count()
        if num_rows == 0:
            logger.info("No data to be loaded")
            return
        # Determine number of partition
        num_partitions = ceil(num_rows / self.MAX_BATCH_SIZE)

        # Short Path: If number of partitions is 1 and_filter_source_table was not overridden,
        #             directly load from source table
        if (num_partitions == 1) and (type(self)._filter_source_table is StardogLoader._filter_source_table):
            logger.info("Using short execution path as number of partitions is 1 and no custom filter is applied")
            self._materialize_graph_from_table(
                self.source_table_full,
                mapping_template,
                stardog_conn_options,
                dbx_jdbc_conn_options,
            )
            return

        # Separate source table into partitions
        logger.info(f"Separate source data in {num_partitions} partitions")
        tmp_table_full = FullTableName(
            self.log_schema_full, f"stardog_load_{self.source_table_full.as_str_snake_case()}"
        )
        source_df = source_df.repartition(num_partitions).select("*", spark_partition_id().alias("_partition"))

        # Process each partition
        partition_ids = [row[0] for row in source_df.select("_partition").distinct().collect()]
        for partition_id in partition_ids:
            partition_table_full = tmp_table_full.copy_with(table=f"{tmp_table_full.table}_{partition_id}")
            partition_df = source_df.where(col("_partition") == partition_id)
            self._load_tmp_table_to_stardog(
                partition_df,
                partition_table_full,
                mapping_template,
                stardog_conn_options,
                dbx_jdbc_conn_options,
            )

    # Class fields
    CLI_DESCRIPTION: ClassVar[str] = "Load data from a Databricks table to a Stardog database in batches."
    MAX_BATCH_SIZE: ClassVar[int] = 100000
    """The maximum size of a batch that is loaded to Stardog."""


if __name__ == "__main__":
    StardogBatchLoader.cli_entrypoint()
