"""Stardog stream load class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from typing import Any, Callable, ClassVar

from jinja2 import StrictUndefined
from pyspark.sql import DataFrame
from rddlib.dbx_utils import FullTableName
from rddlib.utils import add_bundle_dir_to_path
from stardog import Admin
from stardog.content import MappingRaw

if __name__ == "__main__":
    add_bundle_dir_to_path()

from .stardog_loader import StardogLoader

logger = logging.getLogger(__name__)


class StardogStreamLoader(StardogLoader):
    """Class for loading data from a Databricks table to Stardog using structured streaming.

    The checkpoint for the streaming query is stored under the 'log_schema' in a volume
    called 'checkpoint_locations'.
    Make sure this volume exists before attempting to run this script!

    It provides a CLI entrypoint via the 'cli_entrypoint' classmethod
    and this file can be directly called as an executable script.

    You may inherit from this class to introduce custom functionality.
    Override the '_filter_source_table' method to apply custom filtering on the source table.
    In this case you might want to also override the '_cli_construct_arg_parser' classmethod
    to add additional command line arguments to the CLI entrypoint.
    """

    def _get_checkpoint_path(self) -> str:
        """Construct path for checkpoints."""
        return f"/Volumes/{self.log_schema_full.catalog}/{self.log_schema_full.schema}/checkpoint_locations/stardog_load_{self.source_table_full.as_str_snake_case()}"  # noqa: E501

    def _read_source_table(self) -> DataFrame:
        """Reads data from the given source table as a stream."""
        logger.info(f"Load table '{self.source_table_full.as_str_unquoted()}' as streaming dataframe")
        return self.spark.readStream.table(str(self.source_table_full))

    def _construct_process_batch(
        self, mapping_template_str: str, stardog_conn_options: dict[str, Any], dbx_jdbc_conn_options: dict[str, Any]
    ) -> Callable[[DataFrame, int], None]:
        """Constructs the function used to process the individual batches.

        All objects used in the function must be pickleable!

        Args:
            mapping_template_str (str): _description_
            prefixes (str): _description_

        Returns:
            Callable[[DataFrame, int], None]: _description_
        """
        # Retrieve values from self object so it does not get pickled
        stardog_db = self.stardog_db
        tmp_table_full = FullTableName(
            self.log_schema_full, f"stardog_load_{self.source_table_full.as_str_snake_case()}"
        )
        dbx_jdbc_conn_options["sql.catalog"] = self.log_schema_full.catalog
        dbx_jdbc_conn_options["sql.schemas"] = str(self.log_schema_full)

        def _process_batch(batch_df: DataFrame, batch_id: int) -> None:
            from jinja2 import Environment

            spark = batch_df.sparkSession
            mapping_template = Environment(undefined=StrictUndefined).from_string(mapping_template_str)
            batch_table_full = tmp_table_full.copy_with(table=f"{tmp_table_full.table}_{batch_id}")

            # Write batch to temporary table
            logger.info(f"Write batch to temporary table '{batch_table_full.as_str_unquoted()}'")
            batch_df.write.saveAsTable(str(batch_table_full), mode="overwrite", format="delta")
            # Get size of temporary table
            num_rows = spark.sql(f"SELECT COUNT(*) FROM {batch_table_full}").collect()[0][0]
            logger.info(f"Batch size: {num_rows}")
            # Load materialized view from temporary table
            try:
                logger.info("Load data to Stardog")
                mapping = mapping_template.render(table=str(batch_table_full))
                with Admin(**stardog_conn_options) as admin:
                    admin.materialize_virtual_graph(
                        db=stardog_db,
                        mappings=MappingRaw(mapping),
                        options=dbx_jdbc_conn_options,
                    )
            finally:
                # Make sure to always remove temporary table
                logger.info(f"Drop temporary table '{batch_table_full.as_str_unquoted()}'")
                spark.sql(f"DROP TABLE {batch_table_full}")

        return _process_batch

    def _load_to_stardog(
        self,
        source_df: DataFrame,
        mapping_template_str: str,
        stardog_conn_options: dict[str, Any],
        checkpoint_path: str | None = None,
    ) -> None:
        """Loads the data from the filtered source table to the Stardog database."""
        # Get Databricks JDBC connection options
        dbx_jdbc_conn_options = self._get_databricks_jdbc_connection_options()

        # Run streaming query
        logger.info("Process streaming data")
        if checkpoint_path is None:
            checkpoint_path = self._get_checkpoint_path()
        process_func = self._construct_process_batch(mapping_template_str, stardog_conn_options, dbx_jdbc_conn_options)
        query = (
            source_df.writeStream.trigger(availableNow=True)
            .option("checkpointLocation", checkpoint_path)
            .foreachBatch(process_func)
            .start()
        )
        query.awaitTermination()

        if (query.lastProgress is not None) and "numInputRows" in query.lastProgress:
            num_new_rows = query.lastProgress["numInputRows"]
            logger.info(f"Streamed {num_new_rows} new rows")
        else:
            logger.info("No new rows found since last execution")

    # Class fields
    CLI_DESCRIPTION: ClassVar[str] = (
        "Load data from a Databricks table to a Stardog database using structured streaming."
    )


if __name__ == "__main__":
    StardogStreamLoader.cli_entrypoint()
