@prefix adx: <urn:pace:ontology:adx:> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix pace: <urn:pace:ontology:> .
@prefix gold: <urn:pace:ontology:gold:> .
@prefix sh: <http://www.w3.org/ns/shacl#> .

###  urn:pace:ontology:gold:TestInstance-hasTestCase
gold:TestInstance-hasTestCase
        a sh:PropertyShape ;
        sh:name "hasTestCase" ;
        sh:class pace:KeyPerformanceIndicator ,
                 pace:TestSwInt ,
                 pace:TestSw ,
                 pace:TestSysInt ,
                 pace:TestSys ;
        sh:path gold:hasTestCase .

###  urn:pace:ontology:gold:TestInstance-hasTestReport
gold:TestInstance-hasTestReport
        a sh:PropertyShape ;
        sh:name "hasTestReport" ;
        sh:path gold:hasTestReport ;
        sh:class adx:Report .

###  urn:pace:ontology:gold:TestInstance-hasDeployment
gold:TestInstance-hasDeployment
        a sh:PropertyShape ;
        sh:name "hasDeployment" ;
        sh:path gold:hasDeployment ;
        sh:class gold:Deployment .

###  urn:pace:ontology:gold:TestInstance-hasTestPlatform
gold:TestInstance-hasTestPlatform
        a sh:PropertyShape ;
        sh:name "hasTestPlatform" ;
        sh:path gold:hasTestPlatform ;
        sh:class pace:TestPlatform .

###  urn:pace:ontology:gold:TestInstance-hasScenario
gold:TestInstance-hasScenario
        a sh:PropertyShape ;
        sh:name "hasScenario" ;
        sh:path gold:hasScenario ;
        sh:class pace:Scenario .

###  urn:pace:ontology:gold:TestInstance-hasSystemRequirement
gold:TestInstance-hasSystemRequirement
        a sh:PropertyShape ;
        sh:name "hasSystemRequirement" ;
        sh:path gold:hasSystemRequirement ;
        sh:class pace:SystemRequirement .

###  urn:pace:ontology:gold:Artifact-hasOwner
gold:Artifact-hasOwner
        a sh:PropertyShape ;
        sh:name "hasOwner" ;
        sh:path gold:hasOwner ;
        sh:class gold:Owner .

###  urn:pace:ontology:gold:FeatureIncrement-category
gold:FeatureIncrement-category
    a sh:PropertyShape ;
    sh:name "category" ;
    sh:path gold:category ;
    sh:class gold:Category .

###  urn:pace:ontology:gold:SystemRequirement-hasFeatureIncrement
gold:SystemRequirement-hasFeatureIncrement
    a sh:PropertyShape ;
    sh:name "hasFeatureIncrement" ;
    sh:path gold:hasFeatureIncrement ;
    sh:class gold:FeatureIncrement .

###  urn:pace:ontology:gold:reqLevel
gold:SystemRequirement-reqLevel
    a sh:PropertyShape ;
    sh:name "reqLevel" ;
    sh:path gold:reqLevel ;
    sh:class gold:Level1,
             gold:Level3,
             gold:Level4,
             gold:LevelSw .

#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:gold:Category
gold:Category rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "Category" .

###  urn:pace:ontology:gold:Deployment
gold:Deployment rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "Deployment" .

###  urn:pace:ontology:gold:FeatureIncrement
gold:FeatureIncrement rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "Feature Increment" .

###  urn:pace:ontology:gold:Owner
gold:Owner rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "Owner" .

###  urn:pace:ontology:gold:TestInstance
gold:TestInstance rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "Test Instance" .

###  urn:pace:ontology:gold:Level1
gold:Level1 rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "1" .

###  urn:pace:ontology:gold:Level3
gold:Level3 rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "3" .

###  urn:pace:ontology:gold:Level4
gold:Level4 rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "4" .

###  urn:pace:ontology:gold:LevelSw
gold:LevelSw rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact ;
        rdfs:label "sw" .
