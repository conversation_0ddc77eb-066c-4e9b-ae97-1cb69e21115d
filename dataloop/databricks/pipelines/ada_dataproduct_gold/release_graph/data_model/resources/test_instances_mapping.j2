PREFIX pace: <urn:pace:ontology:>
PREFIX gold: <urn:pace:ontology:gold:>
PREFIX needs: <urn:pace:ontology:needs:>

MAPPING <urn:test_instances>
FROM SQL {
    SELECT *
    FROM {{ table }}
}
TO {
    ?ti_iri a gold:TestInstance ;
        gold:hasTestCase ?test_case_iri ;
        gold:hasTestReport ?report_iri ;
        gold:hasDeployment ?t_deployment_iri ;
        gold:hasTestPlatform ?test_platform_iri ;
        gold:hasScenario ?scenario_iri ;
        gold:hasSystemRequirement ?req_iri ;
        rdfs:label ?ti_id .

    ?version_iri pace:validFor ?ti_iri .

} WHERE {
    # Add namespace prefix and and version to certain columns to generate the IRI
    BIND (template("urn:pace:ontology:gold:{ti_id}_{version}") AS ?ti_iri)
    BIND (template("urn:pace:ontology:adx:{report_id}_{version}") AS ?report_iri)
    BIND (template("urn:pace:ontology:{version}") AS ?version_iri)
    # Some columns are already on the IRI form and are prepared with namespace prefix and version suffix.
    BIND (IRI(?test_case_id) AS ?test_case_iri)
    BIND (IRI(?req_id) AS ?req_iri)
    BIND (IRI(?scenario_id) AS ?scenario_iri)
    BIND (IRI(?test_platform) AS ?test_platform_iri)
    BIND (IRI(?deployment_iri) AS ?t_deployment_iri)
}
