@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

pace-o:KeyPerformanceIndicator
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "TestKpi" ;
  rdfs:comment "Test specification for all kpi evaluations" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-calculates-Failure ;
  sh:property pace-o:TestSpec-calculates-Requirement ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSw
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test-Spec SW" ;
  rdfs:comment "Test specification for all (non tool) tests" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-verifiesReq ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSwInt
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test-Spec SwInt" ;
  rdfs:comment "Test specification for all (non tool) tests" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-architecture ;
  sh:property pace-o:TestSpec-verifiesArch ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSys
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test-Spec Sys" ;
  rdfs:comment "Test specification for all (non tool) tests" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-verifiesReq ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSysInt
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test-Spec SysInt" ;
  rdfs:comment "Test specification for all (non tool) tests" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-architecture ;
  sh:property pace-o:TestSpec-verifiesArch ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:MatureTestSpecConstraint a sh:NodeShape ;
  sh:targetClass pace-o:MatureTestSpec ;
  # Using sh:or to specify that at least one of the properties must be present
  sh:xone (
    [
      sh:property pace-o:TestSpec-hasTestMethod ;
    ]
    [
      sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
    ]
  ) ;
  sh:severity sh:Warning ;
.
pace-o:ReadyForExecTestSpecConstraint a sh:NodeShape ;
  sh:targetClass pace-o:ReadyForExecTestSpec ;
  # Using sh:or to specify that a least one of the properties must be present
  sh:xone (
    [
        sh:property pace-o:TestSpec-hasTestMethod ;
    ]
    [
      sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
    ]
  ) ;
  sh:severity sh:Warning ;
.
pace-o:MatureTestSpec
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Mature Test Specification" ;
  rdfs:subClassOf pace-o:TestSpec ;
  rdfs:comment "Test specification is mature, no further changes expected, ready for execution." ;
  sh:property pace-o:TestSpec-hasTestMethod ;
  sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
.
pace-o:ReadyForExecTestSpec
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Ready for Execution Test Specification" ;
  rdfs:subClassOf pace-o:TestSpec ;
  rdfs:comment "Test specification is ready for execution." ;
  sh:property pace-o:TestSpec-hasTestMethod ;
  sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
.
pace-o:TestSpec-MatureRule
  a sh:SPARQLRule ;
  rdfs:label "rule for mature test-spec" ;
  rdfs:comment "if a test-spec has status mature, constraints for mature test-specs are applied" ;
  sh:construct """CONSTRUCT { ?this a pace-o:MatureTestSpec } WHERE {
      VALUES ?type { pace-o:TestSys pace-o:TestSysInt pace-o:TestSw pace-o:TestSwInt}
      ?this rdf:type ?type ; pace-o:hasTestStatus pace-o:TestStatus-mature . }""" ;
.
pace-o:TestSpec-ReadyForExecutionRule
  a sh:SPARQLRule ;
  rdfs:label "rule for ready-for-exec test-spec" ;
  rdfs:comment "if a test-spec has status ready-for-execution, constraints for ready-for-exec test-specs are applied" ;
  sh:construct """CONSTRUCT { ?this a pace-o:ReadyForExecTestSpec } WHERE {
      VALUES ?type { pace-o:TestSys pace-o:TestSysInt pace-o:TestSw pace-o:TestSwInt}
      ?this rdf:type ?type ; pace-o:hasTestStatus pace-o:TestStatus-readyForExecution . }""" ;
.
pace-o:TestSpec-calculates-Failure
  a sh:PropertyShape ;
  sh:class pace-o:Failure ;
  sh:name "calculates" ;
  sh:path pace-o:calculatesFailure ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-calculates-Requirement
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:name "calculates" ;
  sh:path pace-o:calculatesRequirement ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-datafile
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of these attribute is allowed" ;
  sh:name "datafile" ;
  sh:path pace-o:datafile ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-eval_ref_data
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "Evaluation Reference Data" ;
  sh:path pace-o:evaluationReferenceData ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-evaluationAggregationArgument
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "evaluation aggregation argument" ;
  sh:path pace-o:evaluationAggregationArgument ;
  sh:severity sh:Violation ;
.
pace-o:TestSpec-evaluationAggregationScript
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of one implementation can be linked" ;
  sh:name "evaluation aggregation script (path)" ;
  sh:path pace-o:evaluationAggregationScript ;
  sh:severity sh:Violation ;
.
pace-o:TestSpec-evaluationArgument
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "evaluation argument" ;
  sh:path pace-o:evaluationArgument ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-evaluationScript
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of one implementation can be linked" ;
  sh:name "Eval Scrip (Path)" ;
  sh:path pace-o:evaluationScript ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-evaluationTooling
  a sh:PropertyShape ;
  sh:class pace-o:EvaluationTooling ;
  sh:description "The tooling used to evaluate the test specification." ;
  sh:maxCount 1 ;
  sh:message "Only one tool implementation supported" ;
  sh:name "Evaluation Tooling" ;
  sh:path pace-o:hasEvaluationTooling ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-hasSutProfile
  a sh:PropertyShape ;
  sh:minCount 1 ;
  sh:name "has SuT profile" ;
  sh:path pace-o:hasSutProfile ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-hasTestStatus
  a sh:PropertyShape ;
  sh:class pace-o:TestStatus ;
  sh:maxCount 1 ;
  sh:message "Please define a status of the test spec" ;
  sh:minCount 1 ;
  sh:name "has test status" ;
  sh:path pace-o:hasTestStatus ;
  sh:severity sh:Warning ;
.
pace-o:TestStatus-draft
  a pace-o:TestStatus ;
  rdfs:label "draft" ;
  pace-o:description "Test Spec is in draft, no checks" ;
  pace-o:hasNextStatus pace-o:TestStatus-readyForExecution ;
  pace-o:hasNextStatus pace-o:obsolete ;
.
pace-o:TestStatus-readyForExecution
  a pace-o:TestStatus ;
  rdfs:label "ready-for-execution" ;
  pace-o:description "Test Spec is ready-for-execution, same checks as mature" ;
  pace-o:hasNextStatus pace-o:draft ;
  pace-o:hasNextStatus pace-o:mature ;
  pace-o:hasNextStatus pace-o:obsolete ;
.
pace-o:TestStatus-mature
  a pace-o:TestStatus ;
  rdfs:label "mature" ;
  pace-o:description "Test Spec is mature" ;
  pace-o:hasNextStatus pace-o:draft ;
  pace-o:hasNextStatus pace-o:obsolete ;
.
pace-o:TestSpec-hasTestTrigger
  a sh:PropertyShape ;
  sh:class pace-o:TestTrigger ;
  sh:name "Test Trigger" ;
  sh:path pace-o:hasTestTrigger ;
  sh:severity sh:Violation ;
.
pace-o:TestSpec-issueId
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Only one issue ID supported" ;
  sh:name "Issue-ID" ;
  sh:path pace-o:issueId ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-kpiValueUsed
  a sh:PropertyShape ;
  sh:name "uses KPI value" ;
  sh:path pace-o:kpiValueUsed ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-architecture
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of these attribute is allowed" ;
  sh:name "Architecture" ;
  sh:path pace-o:architecture ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-testsScenario
  a sh:PropertyShape ;
  sh:class pace-o:Scenario ;
  sh:name "tests scenario" ;
  sh:path pace-o:testsScenario ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-verifiesArch
  a sh:PropertyShape ;
  sh:class pace-o:ArchitectureElement ;
  sh:minCount 1 ;
  sh:name "verifies architecture" ;
  sh:path pace-o:verifies ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-verifiesReq
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:minCount 1 ;
  sh:name "verifies requirement" ;
  sh:path pace-o:verifies ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-hasTestMethod
  a sh:PropertyShape ;
  sh:class pace-o:TestMethod ;
  sh:minCount 1 ;
  sh:description "Name covered test method if test-design element is not used." ;
  sh:name "Test Method" ;
  sh:path pace-o:hasTestMethod ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-isSpecifiedByTestDesign
  a sh:PropertyShape ;
  sh:minCount 1 ;
  sh:name "is specified by test design" ;
  sh:path pace-o:isSpecifiedByTestDesign ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "TestSpec" ;
  rdfs:comment "Base class of test spec" ;
  rdfs:subClassOf pace-o:ArtifactWithOwner ;
  sh:property pace-o:TestSpec-datafile ;
  sh:property pace-o:TestSpec-eval_ref_data ;
  sh:property pace-o:TestSpec-evaluationAggregationArgument ;
  sh:property pace-o:TestSpec-evaluationAggregationScript ;
  sh:property pace-o:TestSpec-evaluationArgument ;
  sh:property pace-o:TestSpec-evaluationScript ;
  sh:property pace-o:TestSpec-evaluationTooling ;
  sh:property pace-o:TestSpec-hasSutProfile ;
  sh:property pace-o:TestSpec-hasTestStatus ;
  sh:property pace-o:TestSpec-hasTestTrigger ;
  sh:property pace-o:TestSpec-issueId ;
  sh:property pace-o:TestSpec-kpiValueUsed ;
  sh:property pace-o:TestSpec-references ;
  sh:property pace-o:TestSpec-testsScenario ;
  sh:rule pace-o:RuleTestSpecWithIssueId ;
  sh:rule pace-o:TestSpec-ReadyForExecutionRule ;
  sh:rule pace-o:TestSpec-MatureRule ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
