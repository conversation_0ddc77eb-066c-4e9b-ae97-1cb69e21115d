@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

pace-o:
  a owl:Ontology ;
  rdfs:label "pace-ontology" ;
.
pace-o:ArchitecturePackage
  a pace-o:NeedsPackage ;
  rdfs:label "Architecture" ;
.
pace-o:ConfigurationManagementPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Configuration Management" ;
.
pace-o:DesignDecisionDocumentPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Design Decision Document" ;
.
pace-o:EvaluationTooling-fusion_eval
  a pace-o:EvaluationTooling ;
  rdfs:label "Fusion Eval" ;
  pace-o:description "Fusion specific tool for evaluation" ;
  pace-o:id "fusion_eval" ;
  pace-o:title "Fusion Eval" ;
.
pace-o:EvaluationTooling-custom_eval
  a pace-o:EvaluationTooling ;
  rdfs:label "Custom Eval" ;
  pace-o:description "Custom script for evaluation" ;
  pace-o:id "custom_eval" ;
  pace-o:title "Custom Eval" ;
.
pace-o:EvaluationTooling-easy_eval
  a pace-o:EvaluationTooling ;
  rdfs:label "Easy Eval" ;
  pace-o:description "Common pace tool for evaluation" ;
  pace-o:id "easy_eval" ;
  pace-o:title "Easy Eval" ;
.
pace-o:EvaluationTooling-test_oracle
  a pace-o:EvaluationTooling ;
  rdfs:label "Test Oracle" ;
  pace-o:description "Evaluation using a test_oracle ruleset" ;
  pace-o:id "test_oracle" ;
  pace-o:title "Test Oracle ruleset evaluation" ;
.
pace-o:EvaluationTooling-expert_judgement
  a pace-o:EvaluationTooling ;
  rdfs:label "Expert Judgement" ;
  pace-o:description "Evaluation by an expert." ;
  pace-o:id "expert_judgement" ;
  pace-o:title "Expert Judgement" ;
.
pace-o:Milestone
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Milestone" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
.
pace-o:ParameterPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Parameter" ;
.
pace-o:ProcessPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Process" ;
.
pace-o:RequirementsPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Requirements" ;
.
pace-o:SafetyGoal
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Safety Goal" ;
  rdfs:subClassOf pace-o:StakeholderRequirement ;
.
pace-o:SafetyPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Safety" ;
.
pace-o:ScenarioPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Scenario" ;
.
pace-o:SecurityClassA0
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_A_SC0" ;
.
pace-o:SecurityClassA1
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_A_SC1" ;
.
pace-o:SecurityClassA2
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_A_SC2" ;
.
pace-o:SecurityClassA3
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_A_SC3" ;
.
pace-o:SecurityClassC0
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_C_SC0" ;
.
pace-o:SecurityClassC1
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_C_SC1" ;
.
pace-o:SecurityClassC2
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_C_SC02" ;
.
pace-o:SecurityClassC3
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_C_SC03" ;
.
pace-o:SecurityClassI0
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_I_SC0" ;
.
pace-o:SecurityClassI1
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_I_SC1" ;
.
pace-o:SecurityClassI2
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_I_SC2" ;
.
pace-o:SecurityClassI3
  a pace-o:SecurityClass ;
  rdfs:label "SEC_CLS_I_SC3" ;
.
pace-o:SecurityPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Security" ;
.
pace-o:SystemUnderTestPackage
  a pace-o:NeedsPackage ;
  rdfs:label "System under test" ;
.
pace-o:TestDesignPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Test Design" ;
.
pace-o:TestMethod-back2back_comparison
  a pace-o:TestMethod ;
  rdfs:label "back2back_comparison" ;
  rdfs:comment "Back2Back-Comparison" ;
.
pace-o:TestMethod-error_guessing
  a pace-o:TestMethod ;
  rdfs:label "error_guessing" ;
  rdfs:comment "Error-Guessing Test Method" ;
.
pace-o:TestMethod-fault_injection
  a pace-o:TestMethod ;
  rdfs:label "fault_injection" ;
  rdfs:comment "Fault Injection Test" ;
.
pace-o:TestMethod-interface_based
  a pace-o:TestMethod ;
  rdfs:label "interface_based" ;
  rdfs:comment "Interface-based Test Method" ;
.
pace-o:TestMethod-kpi
  a pace-o:TestMethod ;
  rdfs:label "kpi" ;
  rdfs:comment "Requirement-based Test Method using KPI evaluation" ;
.
pace-o:TestMethod-load
  a pace-o:TestMethod ;
  rdfs:label "load" ;
  rdfs:comment "Load-Test" ;
.
pace-o:TestMethod-req_based
  a pace-o:TestMethod ;
  rdfs:label "req_based" ;
  rdfs:comment "Requirement-based Test Method using a combination of positive-negative, equivalence class partitioning, boundary-value analysis and combinatorial testing" ;
.
pace-o:TestMethod-resource_usage
  a pace-o:TestMethod ;
  rdfs:label "resource_usage" ;
  rdfs:comment "Resource Usage Test" ;
.
pace-o:TestMethod-timing
  a pace-o:TestMethod ;
  rdfs:label "timing" ;
  rdfs:comment "Timing and Timeliness Test" ;
.
pace-o:TestMethod-stress
  a pace-o:TestMethod ;
  rdfs:label "stress" ;
  rdfs:comment "Stress-Test" ;
.
pace-o:TestPlatform-CarBench
  a pace-o:TestPlatform ;
  rdfs:label "CarBench" ;
  pace-o:description "Execution using the CarBench." ;
  pace-o:id "CarBench" ;
  pace-o:title "CarBench" ;
.
pace-o:TestPlatform-HiL
  a pace-o:TestPlatform ;
  rdfs:label "HiL" ;
  pace-o:description "Closed-loop simulation on (target) processor and sensors/communication close-to-target hardware." ;
  pace-o:id "HiL" ;
  pace-o:title "HiL" ;
.
pace-o:TestPlatform-HoL
  a pace-o:TestPlatform ;
  rdfs:label "HoL" ;
  pace-o:description "Open-loop simulation on (target) processor and sensors/communication close-to-target hardware." ;
  pace-o:description "Same as SoL and PoL, just that the stimulation data is injected on hardware interfaces" ;
  pace-o:id "HoL" ;
  pace-o:title "Hardware open Loop" ;
  pace-o:title "HoL" ;
.
pace-o:TestPlatform-PiL
  a pace-o:TestPlatform ;
  rdfs:label "PiL" ;
  pace-o:description "Same as SiL, just that the computation of functionality is executed on the goal (P)rocessor" ;
  pace-o:id "PiL" ;
  pace-o:title "Processor in the Loop" ;
.
pace-o:TestPlatform-PoL
  a pace-o:TestPlatform ;
  rdfs:label "PoL" ;
  pace-o:description "Same as SoL, just that the computation of functionality is executed on the goal (P)rocessor" ;
  pace-o:id "PoL" ;
  pace-o:title "Processor open Loop" ;
.
pace-o:TestPlatform-Robotfw
  a pace-o:TestPlatform ;
  rdfs:label "Robotfw" ;
  pace-o:description "Execution using the Robotframework." ;
  pace-o:id "Robotfw" ;
  pace-o:title "Robotframework" ;
.
pace-o:TestPlatform-SiL
  a pace-o:TestPlatform ;
  rdfs:label "SiL" ;
  pace-o:description "SiL differentiates itself from SoL that there is a feedback path, usually only achievable with a simulation tooling" ;
  pace-o:id "SiL" ;
  pace-o:title "Simulation in the Loop" ;
.
pace-o:TestPlatform-SoL
  a pace-o:TestPlatform ;
  rdfs:label "SoL" ;
  pace-o:description "In SoL there is no feedback path to the input (also known as 'recompute')" ;
  pace-o:id "SoL" ;
  pace-o:title "Simulation open Loop" ;
.
pace-o:TestPlatform-Vehicle
  a pace-o:TestPlatform ;
  rdfs:label "Vehicle" ;
  pace-o:description "The (target) Vehicle, where the sensors and the actuators are connected" ;
  pace-o:id "Vehicle" ;
  pace-o:title "Actual (target) Vehicle" ;
.
pace-o:TestPlatform-ViL
  a pace-o:TestPlatform ;
  rdfs:label "ViL" ;
  pace-o:description "Virtual environment stimulus on a vehicle running the function on close-to-target hardware" ;
  pace-o:id "ViL" ;
  pace-o:title "ViL" ;
.
pace-o:TestPlatform-vPiL
  a pace-o:TestPlatform ;
  rdfs:label "vPiL" ;
  pace-o:description "Same as SiL, just that the computation of functionality is executed on the virtual goal (P)rocessor" ;
  pace-o:id "vPiL" ;
  pace-o:title "Virtual Processor in the Loop" ;
.
pace-o:TestSpecWithIssueId
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpecWithIssueId-issueId ;
.
pace-o:TestSpecificationPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Test Specification" ;
.

pace-o:ToolRequirementsPackage
  a pace-o:NeedsPackage ;
  rdfs:label "Tool Requirements" ;
  rdfs:comment "requirements of internally developed tools" ;
.
pace-o:UseCasePackage
  a pace-o:NeedsPackage ;
  rdfs:label "Use Cases" ;
.
pace-o:VerificationMethodAnalysis
  a pace-o:VerificationMethod ;
  rdfs:label "Analysis" ;
.
pace-o:VerificationMethodDemonstration
  a pace-o:VerificationMethod ;
  rdfs:label "Demonstration" ;
.
pace-o:VerificationMethodKpi
  a pace-o:VerificationMethod ;
  rdfs:label "KPI" ;
  rdfs:comment "used for KPI evaluation (validation)" ;
.
pace-o:VerificationMethodReview
  a pace-o:VerificationMethod ;
  rdfs:label "Review" ;
.
pace-o:VerificationMethodTest
  a pace-o:VerificationMethod ;
  rdfs:label "Test" ;
.
pace-o:accessRights
  a owl:DatatypeProperty ;
  rdfs:label "access rights" ;
.
pace-o:after
  a pace-o:Guideword ;
  rdfs:label "after" ;
  pace-o:description "after" ;
.
pace-o:architecture
  a owl:DatatypeProperty ;
  rdfs:label "architecture" ;
  rdfs:comment "Link architecture elements in case of integration test" ;
.
pace-o:archiving
  a owl:DatatypeProperty ;
  rdfs:label "archiving" ;
.
pace-o:artifactInformation
  a owl:DatatypeProperty ;
  rdfs:label "artifact information" ;
.
pace-o:backupRetrieval
  a owl:DatatypeProperty ;
  rdfs:label "backup retrieval" ;
.
pace-o:baselining
  a owl:DatatypeProperty ;
  rdfs:label "baselining" ;
.
pace-o:before
  a pace-o:Guideword ;
  rdfs:label "before" ;
  pace-o:description "before" ;
.
pace-o:buildConfig
  a owl:DatatypeProperty ;
  rdfs:label "configs" ;
  rdfs:comment "Comma-separated list of bazel build configs" ;
.
pace-o:cbAllocates
  a owl:DatatypeProperty ;
  rdfs:label "Allocates" ;
.
pace-o:cbArchitecture
  a owl:DatatypeProperty ;
  rdfs:label "cbArchitecture" ;
.
pace-o:cbBaselineId
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer baseline Id" ;
.
pace-o:cbBaselineName
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer baseline name" ;
.
pace-o:cbDaSafetyIntegrity
  a owl:DatatypeProperty ;
  rdfs:label "cbDaSafetyIntegrity" ;
.
pace-o:cbFiadReferencePeName
  a owl:DatatypeProperty ;
  rdfs:label "Fiad reference pe name" ;
.
pace-o:cbFiadReferencePeUri
  a owl:DatatypeProperty ;
  rdfs:label "Fiad reference pe uri" ;
.
pace-o:cbId
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer Id" ;
.
pace-o:cbProjectId
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer project Id" ;
.
pace-o:cbProjectName
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer project name" ;
.
pace-o:cbRequestedFromPe
  a owl:DatatypeProperty ;
  rdfs:label "Requested from pe" ;
.
pace-o:cbStatsBosch
  a owl:DatatypeProperty ;
  rdfs:label "Status Bosch" ;
.
pace-o:cbStatsCariad
  a owl:DatatypeProperty ;
  rdfs:label "Status Cariad" ;
.
pace-o:cbStatus
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer status" ;
.
pace-o:cbStatusAda
  a owl:DatatypeProperty ;
  rdfs:label "Status Ada" ;
.
pace-o:cbTrackerId
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer tracker Id" ;
.
pace-o:cbTrackerName
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer tracker name" ;
.
pace-o:cbType
  a owl:DatatypeProperty ;
  rdfs:label "Codebeamer type" ;
.
pace-o:cleanup
  a owl:DatatypeProperty ;
  rdfs:label "cleanup" ;
.
pace-o:completelyDevelopedExternally
  a owl:DatatypeProperty ;
  rdfs:label "completely developed externally" ;
.
pace-o:complianceRating
  a owl:DatatypeProperty ;
  rdfs:label "compliance rating" ;
.
pace-o:configs
  a owl:DatatypeProperty ;
  rdfs:label "configs" ;
  rdfs:comment "Link of additional dt_launcher configs further detailing the system configuration" ;
.
pace-o:constant
  a pace-o:Guideword ;
  rdfs:label "constant/not updated" ;
  pace-o:description "constant/not updated" ;
.
pace-o:DataCategory
  a owl:Class ;
  rdfs:label "Data Category" ;
.
pace-o:dataCategory
  a owl:DatatypeProperty ;
  rdfs:label "data category" ;
.
pace-o:datafile
  a owl:DatatypeProperty ;
  rdfs:label "datafile" ;
  rdfs:comment "Deprecated, link a scenario instead" ;
.
pace-o:dataset
  a owl:DatatypeProperty ;
  rdfs:label "dataset" ;
  rdfs:comment "Path to a .yml file that defines datasets for recompute" ;
.
pace-o:dddApprovalState
  a owl:DatatypeProperty ;
  rdfs:label "DDD approval state" ;
.
pace-o:dddHost
  a owl:DatatypeProperty ;
  rdfs:label "DDD host" ;
.
pace-o:dddSystemElements
  a owl:DatatypeProperty ;
  rdfs:label "DDD system elements" ;
.
pace-o:default
  a owl:DatatypeProperty ;
  rdfs:label "default" ;
.
pace-o:deployment
  a owl:DatatypeProperty ;
  rdfs:label "deployment" ;
  rdfs:comment "One can find all deployments with 'pace inspect deployment'" ;
.
pace-o:deploymentStatus
  a owl:DatatypeProperty ;
  rdfs:label "deployment status" ;
.
pace-o:early
  a pace-o:Guideword ;
  rdfs:label "too early" ;
  pace-o:description "too early" ;
.
pace-o:evaluationAdxIngestName
  a owl:DatatypeProperty ;
  rdfs:label "evaluation ADX ingest name" ;
.
pace-o:evaluationAdxInputFile
  a owl:DatatypeProperty ;
  rdfs:label "evaluation ADX input file" ;
.
pace-o:evaluationAggregationArgument
  a owl:DatatypeProperty ;
  rdfs:label "evaluation aggregation argument" ;
  rdfs:comment "Any arguments required for evaluation aggregation with the eval tool" ;
.
pace-o:evaluationAggregationScript
  a owl:DatatypeProperty ;
  rdfs:label "evaluation aggregation script" ;
  rdfs:comment "Path to aggregation implementation file" ;
.
pace-o:evaluationArgument
  a owl:DatatypeProperty ;
  rdfs:label "evaluation argument" ;
  rdfs:comment "Any arguments required for evaluation with the eval tool" ;
.
pace-o:evaluationReferenceData
  a owl:DatatypeProperty ;
  rdfs:label "evaluation reference data" ;
  rdfs:comment "Link any ground truth data" ;
.
pace-o:evaluationScript
  a owl:DatatypeProperty ;
  rdfs:label "evaluation script" ;
  rdfs:comment "Path to implementation file" ;
.
pace-o:failureFlow
  a owl:DatatypeProperty ;
  rdfs:label "failure flow" ;
.
pace-o:fast
  a pace-o:Guideword ;
  rdfs:label "too fast" ;
  pace-o:description "too fast" ;
.
pace-o:filterNegative
  a owl:DatatypeProperty ;
  rdfs:label "negative filter" ;
  rdfs:comment "The list of all activities which should NOT be executed" ;
.
pace-o:filterPositive
  a owl:DatatypeProperty ;
  rdfs:label "positive filter" ;
  rdfs:comment "The list of all activities which should be executed" ;
.
pace-o:GdprImpactHigh
  a pace-o:GdprImpact ;
  rdfs:label "high" ;
  sh:description "Processes (storing, computing, transforming, anonymizing, labelling, ...) of Video/Lidar/Radar data that depict persons or parts, silhouettes, reflections, shadows of persons. Also audio information on which persons or noises emitted by persons fall into this class. Furthermore, all tools that are involved into automated decision making, e.g. training, verification, validation and inference of neural networks." ;
.
pace-o:GdprImpactLow
  a pace-o:GdprImpact ;
  rdfs:label "low" ;
  sh:description "Management tools, e.g. a vehicle equipment database, or processing of low-level machine telemetry, e.g. cpu load, or sensor calibration data." ;
.
pace-o:GdprImpactMedium
  a pace-o:GdprImpact ;
  rdfs:label "medium" ;
  sh:description "Creation and processing of logfiles that enable traceability of human interactions (who did what and when). This is the same for employees as well as externals. GDPR does not discriminate between both." ;
.
pace-o:GdprImpactNone
  a pace-o:GdprImpact ;
  rdfs:label "none" ;
  sh:description "no GDPR relevance." ;
.
pace-o:higher
  a pace-o:Guideword ;
  rdfs:label "too big / higher" ;
  pace-o:description "too big / higher" ;
.
pace-o:impl
  a owl:DatatypeProperty ;
  rdfs:label "Implementation" ;
.
pace-o:incomplete
  a pace-o:Guideword ;
  rdfs:label "incomplete / partly" ;
  pace-o:description "incomplete / partly" ;
.
pace-o:invalid
  a pace-o:Guideword ;
  rdfs:label "invalid (other than)" ;
  rdfs:label "invalid content" ;
  pace-o:description "invalid (other than)" ;
  pace-o:description "invalid content" ;
.
pace-o:isEvaluationToolingOf
  a owl:ObjectProperty ;
  rdfs:label "is evaluation tooling of" ;
  rdfs:domain pace-o:EvaluationTooling ;
  owl:inverseOf pace-o:hasEvaluationTooling ;
.
pace-o:itContext
  a owl:DatatypeProperty ;
  rdfs:label "IT context" ;
.
pace-o:itImplementedBy
  a owl:DatatypeProperty ;
  rdfs:label "IT implemented by" ;
.
pace-o:itOrigin
  a owl:DatatypeProperty ;
  rdfs:label "IT origin" ;
.
pace-o:itResponsible
  a owl:DatatypeProperty ;
  rdfs:label "IT responsible" ;
.
pace-o:itStatus
  a owl:DatatypeProperty ;
  rdfs:label "IT status" ;
.
pace-o:itTestMethod
  a owl:DatatypeProperty ;
  rdfs:label "IT test method" ;
.
pace-o:itTypeOfSslRelevance
  a owl:DatatypeProperty ;
  rdfs:label "IT type of ssl relevance" ;
.
pace-o:itVerificationCriteria
  a owl:DatatypeProperty ;
  rdfs:label "IT verification criteria" ;
.
pace-o:kpiType
  a owl:DatatypeProperty ;
  rdfs:label "KPI type" ;
.
pace-o:late
  a pace-o:Guideword ;
  rdfs:label "too late" ;
  pace-o:description "too late" ;
.
pace-o:less
  a pace-o:Guideword ;
  rdfs:label "less" ;
  pace-o:description "less" ;
.
pace-o:lower
  a pace-o:Guideword ;
  rdfs:label "too small / lower" ;
  pace-o:description "too small / lower" ;
.
pace-o:max
  a owl:DatatypeProperty ;
  rdfs:label "max" ;
.
pace-o:min
  a owl:DatatypeProperty ;
  rdfs:label "min" ;
.
pace-o:more
  a pace-o:Guideword ;
  rdfs:label "more" ;
  pace-o:description "more" ;
.
pace-o:namingConventions
  a owl:DatatypeProperty ;
  rdfs:label "naming conventions" ;
.
pace-o:necessaryGroundTruth
  a owl:DatatypeProperty ;
  rdfs:label "necessary ground truth" ;
.
pace-o:necessarySignals
  a owl:DatatypeProperty ;
  rdfs:label "necessary signals" ;
.
pace-o:needsGPU
  a owl:DatatypeProperty ;
  rdfs:label "needs GPU" ;
  rdfs:comment "Is a physical GPU required for execution" ;
.
pace-o:no
  a pace-o:FailureSafetyRelevance ;
  rdfs:label "no" ;
  pace-o:description "Failure is not safety relevant since there is no failure trace to a hazardous behaviour expected" ;
  pace-o:hasNextStatus pace-o:n_a ;
  pace-o:hasNextStatus pace-o:yes ;
  pace-o:hasNextStatus pace-o:yes_FuSa ;
.
pace-o:not
  a pace-o:Guideword ;
  rdfs:label "not active / no / not" ;
  pace-o:description "not active / no / not" ;
.
pace-o:objectFlow
  a owl:DatatypeProperty ;
  rdfs:label "object flow" ;
.
pace-o:owner
  a owl:DatatypeProperty ;
  rdfs:label "owner" ;
.
pace-o:ownership_cluster
  a owl:DatatypeProperty ;
  rdfs:label "ownership_cluster" ;
.
pace-o:ownership_domain
  a owl:DatatypeProperty ;
  rdfs:label "ownership_domain" ;
.
pace-o:ownership_team
  a owl:DatatypeProperty ;
  rdfs:label "ownership_team" ;
.
pace-o:ownership_person
  a owl:DatatypeProperty ;
  rdfs:label "ownership_person" ;
.
pace-o:recording
  a owl:DatatypeProperty ;
  rdfs:label "recording file" ;
  rdfs:comment "Define the recording file in here in case you defined a test platform that requires it, can be path, mdm hash, artifactory link, etc as long as dt_launcher supports it" ;
.
pace-o:relatedProcessWorkProduct
  a owl:DatatypeProperty ;
  rdfs:label "related process work product" ;
.
pace-o:releaseNoteRelevance
  a owl:DatatypeProperty ;
  rdfs:label "release note relevance" ;
  rdfs:comment "The indication whether or not a CM Item has to be included in a release note. Permissible options are yes and no." ;
.
pace-o:retention
  a owl:DatatypeProperty ;
  rdfs:label "retention" ;
.
pace-o:reverse
  a pace-o:Guideword ;
  rdfs:label "reverse" ;
  pace-o:description "reverse" ;
.
pace-o:safeState
  a owl:DatatypeProperty ;
  rdfs:label "safe state" ;
.
pace-o:safetyComment
  a owl:DatatypeProperty ;
  rdfs:label "safety comment" ;
.
pace-o:requirementDescription
  a owl:DatatypeProperty ;
  rdfs:label "requirement description" ;
.
pace-o:safetyIntegrity
  a owl:DatatypeProperty ;
  rdfs:label "safety integrity" ;
.
pace-o:safetyLogic
  a owl:DatatypeProperty ;
  rdfs:label "safetyLogic" ;
  rdfs:comment "Definition of the error propagation logic within ensured by the safety measure, like e.g. AND, OR." ;
.
pace-o:safetyRelevance
  a owl:DatatypeProperty ;
  rdfs:label "safety relevance" ;
.
pace-o:scenarioFile
  a owl:DatatypeProperty ;
  rdfs:label "scenario file" ;
  rdfs:comment "Define the scenario file in here in case you defined a test platform that requires it" ;
.
pace-o:securityAttackFeasibilityLevel
  a owl:DatatypeProperty ;
  rdfs:label "security attack feasibility level" ;
.
pace-o:securityImpactCategory
  a owl:DatatypeProperty ;
  rdfs:label "security impact category" ;
.
pace-o:securityImpactRating
  a owl:DatatypeProperty ;
  rdfs:label "security impact rating" ;
.
pace-o:securityProperty
  a owl:DatatypeProperty ;
  rdfs:label "security property" ;
.
pace-o:securityRelevance
  a owl:DatatypeProperty ;
  rdfs:label "security relevance" ;
.
pace-o:securityResponsibleParty
  a owl:DatatypeProperty ;
  rdfs:label "security responsible party" ;
.
pace-o:securityRiskLevel
  a owl:DatatypeProperty ;
  rdfs:label "security risk level" ;
.
pace-o:sharedBetweenCompanies
  a owl:DatatypeProperty ;
  rdfs:label "shared between companies" ;
.
pace-o:signatureApproval
  a owl:DatatypeProperty ;
  rdfs:label "signature approval" ;
  rdfs:comment "Indication whether an additional signature approval on top the the pull-request based review is required in order to release the CM Item. If an additional signature approval is required, indicate the details (at least who and how) in the description of the CM Item." ;
.
pace-o:slow
  a pace-o:Guideword ;
  rdfs:label "too slow" ;
  pace-o:description "too slow" ;
.
pace-o:source
  a owl:DatatypeProperty ;
  rdfs:label "source" ;
.
pace-o:stagesReference
  a owl:DatatypeProperty ;
  rdfs:label "stages reference" ;
.
pace-o:storageLocation
  a owl:DatatypeProperty ;
  rdfs:label "storage location" ;
.
pace-o:storageTool
  a owl:DatatypeProperty ;
  rdfs:label "storage tool" ;
.
pace-o:stuck
  a pace-o:Guideword ;
  rdfs:label "stuck" ;
  pace-o:description "stuck" ;
.
pace-o:syscal
  a owl:DatatypeProperty ;
  rdfs:label "system calibration" ;
  rdfs:comment "Specification of system calibration for recompute as named in calstorage" ;
.
pace-o:system
  a owl:DatatypeProperty ;
  rdfs:label "system" ;
.
pace-o:targetValue
  a owl:DatatypeProperty ;
  rdfs:label "target value" ;
.
pace-o:TCL1
  a pace-o:ToolConfidenceLevel ;
  rdfs:label "TCL1" ;
  rdfs:comment "This is the lowest tool confidence level. The tool does not play an important role regarding the quality of our final product. Therefore, we do not need to have a high confidence in the correct tool behaviour from the ISO 26262 view. A tool qualification is not needed." ;
.
pace-o:TCL2
  a pace-o:ToolConfidenceLevel ;
  rdfs:label "TCL2" ;
  rdfs:comment "This is corresponds to an medium tool confidence level. The tool plays an important role regarding the quality of the our final product, so we need to have an certain level of confidence and therefore need to perform a tool qualification to demonstrate the reliability of that tool." ;
.
pace-o:TCL3
  a pace-o:ToolConfidenceLevel ;
  rdfs:label "TCL3" ;
  rdfs:comment "This is corresponds to a high tool confidence level. The tool plays an important role regarding the quality of the our final product, so we need to have an certain level of confidence and therefore need to perform a tool qualification to demonstrate the reliability of that tool." ;
.
pace-o:TCLTbd
  a pace-o:ToolConfidenceLevel ;
  rdfs:label "tbd" ;
  rdfs:comment "tool confidence level to be defined" ;
.
pace-o:tdp
  a owl:DatatypeProperty ;
  rdfs:label "TDP" ;
.
pace-o:test_failure_notification_channel_url
  a owl:DatatypeProperty ;
  rdfs:label "test_failure_notification_channel_url" ;
.
pace-o:timingRequirements
  a owl:DatatypeProperty ;
  rdfs:label "timing requirements" ;
.
pace-o:toolVersion
  a owl:DatatypeProperty ;
  rdfs:label "tool version" ;
.
pace-o:training
  a owl:DatatypeProperty ;
  rdfs:label "training" ;
.
pace-o:unauthorized
  a pace-o:Guideword ;
  rdfs:label "unauthorized" ;
  pace-o:description "unauthorized" ;
.
pace-o:uninitialized
  a pace-o:Guideword ;
  rdfs:label "uninitialized" ;
  pace-o:description "uninitialized" ;
.
pace-o:unit
  a owl:DatatypeProperty ;
  rdfs:label "unit" ;
.
pace-o:url
  a owl:DatatypeProperty ;
  rdfs:label "url" ;
.
pace-o:usageApproval
  a owl:DatatypeProperty ;
  rdfs:label "usage approval" ;
.
pace-o:variantRestriction
  a owl:DatatypeProperty ;
  rdfs:label "variant restriction" ;
.
pace-o:variationSimulationFile
  a owl:DatatypeProperty ;
  rdfs:label "variation simulation file" ;
  rdfs:comment "If the scenario supports parameter variation you can link it here" ;
.
pace-o:verificationCriteria
  a owl:DatatypeProperty ;
  rdfs:label "verification criteria" ;
.
pace-o:verificationMethod
  a owl:DatatypeProperty ;
  rdfs:label "verification method" ;
.
pace-o:viewEnds
  a owl:ObjectProperty ;
  rdfs:label "view ends by" ;
  owl:inverseOf pace-o:endsView ;
.
pace-o:viperRsrSignCategory
  a owl:DatatypeProperty ;
  rdfs:label "viper TSR sign category" ;
.
pace-o:viperTsrCountry
  a owl:DatatypeProperty ;
  rdfs:label "viper TSR country" ;
.
pace-o:viperTsrExpectedOutput
  a owl:DatatypeProperty ;
  rdfs:label "viper TSR expected output" ;
.
pace-o:viperTsrSignType
  a owl:DatatypeProperty ;
  rdfs:label "viper TSR sign type" ;
.
pace-o:workflow
  a owl:DatatypeProperty ;
  rdfs:label "workflow" ;
.
pace-o:wrong_content
  a pace-o:Guideword ;
  rdfs:label "wrong content" ;
  pace-o:description "wrong content" ;
.
pace-o:wrong_timing
  a pace-o:Guideword ;
  rdfs:label "wrong timing" ;
  pace-o:description "wrong timing" ;
.
pace-o:yes_only_FuSa
  a pace-o:FailureSafetyRelevance ;
  rdfs:label "yes_only_FuSa" ;
  pace-o:description "Failure is safety relevant, but not expected to be caused by insufficiencies. Intended to document possible failures which do not need to be addressed with SOTIF measures" ;
  pace-o:hasNextStatus pace-o:n_a ;
  pace-o:hasNextStatus pace-o:not_safety_relevant ;
  pace-o:hasNextStatus pace-o:yes ;
.
pace-o:Artifact-description
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "description" ;
  sh:path pace-o:description ;
  sh:severity sh:Warning ;
.
pace-o:Artifact-id
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "id" ;
  sh:path pace-o:id ;
  sh:severity sh:Violation ;
.
pace-o:Artifact-status
  a sh:PropertyShape ;
  sh:class pace-o:Status ;
  sh:maxCount 1 ;
  sh:message "One and only one status value of type Status" ;
  sh:minCount 1 ;
  sh:name "status" ;
  sh:path pace-o:hasStatus ;
  sh:severity sh:Warning ;
.
pace-o:Artifact-tag
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "tag" ;
  sh:path pace-o:tag ;
  sh:severity sh:Warning ;
.
pace-o:Artifact-title
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "title" ;
  sh:path pace-o:title ;
  sh:severity sh:Warning ;
.
pace-o:ArtifactStatus-isArtifactStatusOf
  a sh:PropertyShape ;
  sh:class pace-o:Artifact ;
  sh:name "is artifact status of" ;
  sh:path pace-o:isArtifactStatusOf ;
  sh:severity sh:Warning ;
.
pace-o:hasArtifactStatus
  a owl:ObjectProperty ;
  rdfs:label "has artifact status" ;
  rdfs:domain pace-o:Artifact ;
  rdfs:range pace-o:ArtifactStatus ;
  rdfs:subPropertyOf pace-o:hasStatus ;
  owl:inverseOf pace-o:isArtifactStatusOf ;
.
pace-o:isArtifactStatusOf
  a owl:ObjectProperty ;
  rdfs:label "is artifact status of" ;
  rdfs:domain pace-o:ArtifactStatus ;
  rdfs:range pace-o:Artifact ;
  rdfs:subPropertyOf pace-o:isStatusOf ;
  owl:inverseOf pace-o:hasArtifactStatus ;
.
pace-o:FailureHasGuideword
  a owl:ObjectProperty ;
  rdfs:label "has guideword" ;
  rdfs:domain pace-o:Guideword ;
  rdfs:range pace-o:Guideword ;
  owl:inverseOf pace-o:GuidewordOfFailure ;
.
pace-o:FailureSafetyRelevance-isFailureSafetyRelevanceOf
  a sh:PropertyShape ;
  sh:class pace-o:Failure ;
  sh:name "is failure safetyrelevance of" ;
  sh:path pace-o:isFailureSafetyRelevanceOf ;
  sh:severity sh:Warning ;
.
pace-o:FailureTriggers
  a owl:ObjectProperty ;
  rdfs:label "failure triggers" ;
  rdfs:subPropoertyOf pace-o:triggers ;
  owl:inverseOf pace-o:isTriggeredByFailure ;
.
pace-o:FailurehasSafetyRelevance
  a owl:ObjectProperty ;
  rdfs:label "has safety relevance status" ;
  rdfs:domain pace-o:FailureSafetyRelevance ;
  rdfs:range pace-o:FailureSafetyRelevance ;
  owl:inverseOf pace-o:SafetyRelevanceOfFailure ;
.
pace-o:FunctionalityIsAffectedBy
  a owl:ObjectProperty ;
  rdfs:label "functionality is affected by" ;
  rdfs:subPropertyOf pace-o:isAffectedBy ;
  owl:inverseOf pace-o:affectsFunctionality ;
.
pace-o:Guideword-appliedTo
  a sh:PropertyShape ;
  sh:name "is safety relevance of" ;
  sh:path pace-o:SafetyRelevanceOfFailure ;
  sh:severity sh:Warning ;
.
pace-o:GuidewordOfFailure
  a owl:ObjectProperty ;
  rdfs:label "is guideword of" ;
  owl:inverseOf pace-o:FailureHasGuideword ;
.
pace-o:ProxyPortIsAffectedBy
  a owl:ObjectProperty ;
  rdfs:label "ProxyPort is affected by" ;
  rdfs:subPropertyOf pace-o:isAffectedBy ;
  owl:inverseOf pace-o:affectsProxyPort ;
.
pace-o:RequirementIsAffectedBy
  a owl:ObjectProperty ;
  rdfs:label "requirement is affected by" ;
  rdfs:subPropertyOf pace-o:isAffectedBy ;
  owl:inverseOf pace-o:affectsRequirement ;
.
pace-o:Status-hasNextStatus
  a sh:PropertyShape ;
  sh:class pace-o:Status ;
  sh:description "Status precedes another status in a status net." ;
  sh:name "precedes" ;
  sh:path pace-o:hasNextStatus ;
  sh:severity sh:Warning ;
.
pace-o:Status-hasPreviousStatus
  a sh:PropertyShape ;
  sh:class pace-o:Status ;
  sh:description "The state that must apply before changing to this state." ;
  sh:message "The previous state must be of type pace-o:Status." ;
  sh:name "has previous status" ;
  sh:path pace-o:hasPreviousStatus ;
  sh:severity sh:Warning ;
.
pace-o:TestSpecWithIssueId-issueId
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "exactly one issue ID required" ;
  sh:minCount 1 ;
  sh:name "mandatory issue-ID" ;
  sh:path pace-o:issueId ;
  sh:severity sh:Warning ;
.

pace-o:ToolDatabasePackage
  a pace-o:NeedsPackage ;
  rdfs:label "Tool Database" ;
.
pace-o:TriggeringConditionTriggers
  a owl:ObjectProperty ;
  rdfs:label "triggering condition triggers" ;
  rdfs:subPropoertyOf pace-o:triggers ;
  owl:inverseOf pace-o:isTriggeredByTriggeringCondition ;
.
pace-o:accepts
  a owl:ObjectProperty ;
  rdfs:label "accepts" ;
  owl:inverseOf pace-o:isAcceptedWith ;
.
pace-o:affectsFunctionality
  a owl:ObjectProperty ;
  rdfs:label "affects Functionality" ;
  rdfs:subPropertyOf pace-o:affects ;
  owl:inverseOf pace-o:FunctionalityIsAffectedBy ;
.
pace-o:affectsProxyPort
  a owl:ObjectProperty ;
  rdfs:label "affects ProxyPort" ;
  rdfs:subPropertyOf pace-o:affects ;
  owl:inverseOf pace-o:ProxyPortIsAffectedBy ;
.
pace-o:affectsRequirement
  a owl:ObjectProperty ;
  rdfs:label "affects requirement" ;
  rdfs:subPropertyOf pace-o:affects ;
  owl:inverseOf pace-o:RequirementIsAffectedBy ;
.
pace-o:aggregates
  a owl:ObjectProperty ;
  rdfs:label "aggregates" ;
  owl:inverseOf pace-o:isAggregatedBy ;
.
pace-o:allocates
  a owl:ObjectProperty ;
  rdfs:label "allocates" ;
  owl:inverseOf pace-o:isAllocatedTo ;
.
pace-o:appliesToProductVariant
  a owl:ObjectProperty ;
  rdfs:label "applies to variant" ;
  owl:inverseOf pace-o:productVariantIsSubjectTo ;
.
pace-o:associates
  a owl:ObjectProperty ;
  rdfs:label "associates" ;
  owl:inverseOf pace-o:isAssociatedBy ;
.
pace-o:assumed
  a pace-o:ArtifactStatus ;
  rdfs:label "assumed" ;
  pace-o:description "Artifact is reviewed by the team, further changes expected" ;
  pace-o:hasNextStatus pace-o:agreed ;
  pace-o:hasNextStatus pace-o:draft ;
.
pace-o:belongsToFeature
  a owl:ObjectProperty ;
  rdfs:label "belongs to feature" ;
  rdfs:subPropertyOf pace-o:belongsTo ;
  owl:inverseOf pace-o:featureComprises ;
.
pace-o:belongsToFunctionality
  a owl:ObjectProperty ;
  rdfs:label "belongs to functionality" ;
  rdfs:subPropertyOf pace-o:belongsTo ;
  owl:inverseOf pace-o:functionalityComprises ;
.
pace-o:calculatesFailure
  a owl:ObjectProperty ;
  rdfs:label "calculates Failure" ;
  rdfs:comment "Failure which occurrence is evaluated with this kpi-value" ;
  rdfs:subPropertyOf pace-o:calculates ;
  owl:inverseOf pace-o:isCalculatedByFailure ;
.
pace-o:calculatesRequirement
  a owl:ObjectProperty ;
  rdfs:label "calculates" ;
  rdfs:subPropertyOf pace-o:calculates ;
  owl:inverseOf pace-o:isCalculatedByRequirement ;
.
pace-o:composes
  a owl:ObjectProperty ;
  rdfs:label "is composed of" ;
  owl:inverseOf pace-o:decomposes ;
.
pace-o:compromises
  a owl:ObjectProperty ;
  rdfs:label "compromises" ;
  owl:inverseOf pace-o:isCompromisedBy ;
.
pace-o:contains
  a owl:ObjectProperty ;
  rdfs:label "contains" ;
  owl:inverseOf pace-o:isContainedIn ;
.
pace-o:correspondingImpact
  a owl:ObjectProperty ;
  rdfs:label "corresponding impact" ;
  owl:inverseOf pace-o:impactIsPartOf ;
.
pace-o:correspondingMisuseCase
  a owl:ObjectProperty ;
  rdfs:label "corresponding misuse case" ;
  owl:inverseOf pace-o:isCorrespondingMisuseCaseOf ;
.
pace-o:isCorrespondingMisuseCaseOf
  a owl:ObjectProperty ;
  rdfs:label "is corresponding misuse case of" ;
  owl:inverseOf pace-o:correspondingMisuseCase ;
.
pace-o:covers
  a owl:ObjectProperty ;
  rdfs:label "covers" ;
  owl:inverseOf pace-o:isCoveredBy ;
.
pace-o:decomposes
  a owl:ObjectProperty ;
  rdfs:label "decomposes" ;
  owl:inverseOf pace-o:composes ;
.
pace-o:dependsOn
  a owl:ObjectProperty ;
  rdfs:label "depends on" ;
  owl:inverseOf pace-o:hasDependents ;
.
pace-o:deploys
  a owl:ObjectProperty ;
  rdfs:label "deploys" ;
  owl:inverseOf pace-o:isDeployedBy ;
.
pace-o:description
  a owl:DatatypeProperty ;
  rdfs:label "description" ;
  rdfs:comment "Long description of the artifact." ;
.
pace-o:discusses
  a owl:ObjectProperty ;
  rdfs:label "discusses" ;
  owl:inverseOf pace-o:isDiscussedBy ;
.
pace-o:featureComprises
  a owl:ObjectProperty ;
  rdfs:label "feature comprises" ;
  rdfs:subPropertyOf pace-o:comprises ;
  owl:inverseOf pace-o:belongsToFeature ;
.
pace-o:functionalityComprises
  a owl:ObjectProperty ;
  rdfs:label "functionality comprises" ;
  rdfs:subPropertyOf pace-o:comprises ;
  owl:inverseOf pace-o:belongsToFunctionality ;
.
pace-o:functionalityIsImplementedBy
  a owl:ObjectProperty ;
  rdfs:label "functionality is implemented by" ;
  rdfs:subPropertyOf pace-o:isImplementedBy ;
  owl:inverseOf pace-o:implementsFunctionality ;
.
pace-o:hasDependents
  a owl:ObjectProperty ;
  rdfs:label "has dependents" ;
  owl:inverseOf pace-o:dependsOn ;
.
pace-o:hasEvaluationTooling
  a owl:ObjectProperty ;
  rdfs:label "has evaluation tooling" ;
  rdfs:range pace-o:EvaluationTooling ;
  owl:inverseOf pace-o:isFailingReasonOf ;
.
pace-o:hasFailureSafetyRelevance
  a owl:ObjectProperty ;
  rdfs:label "has failure safetyrelevance" ;
  rdfs:domain pace-o:Failure ;
  rdfs:range pace-o:FailureSafetyRelevance ;
  rdfs:subPropertyOf pace-o:hasSafetyRelevance ;
  owl:inverseOf pace-o:isFailureSafetyRelevanceOf ;
.
pace-o:hasInPort
  a owl:ObjectProperty ;
  rdfs:label "has in-port" ;
  owl:inverseOf pace-o:isInPortOf ;
.
pace-o:hasOutPort
  a owl:ObjectProperty ;
  rdfs:label "has out-port" ;
  owl:inverseOf pace-o:isOutPortOf ;
.
pace-o:hasPort
  a owl:ObjectProperty ;
  rdfs:label "has port" ;
  owl:inverseOf pace-o:isPortOf ;
.
pace-o:hasSutProfile
  a owl:ObjectProperty ;
  rdfs:label "has SuT profile" ;
  rdfs:comment "Defines the system config for execution" ;
  rdfs:range pace-o:SutProfile ;
  owl:inverseOf pace-o:isSutProfileOf ;
.
pace-o:hasTestMethod
  a owl:ObjectProperty ;
  rdfs:label "has test method(s)" ;
  rdfs:domain pace-o:TestDesign ;
  rdfs:range pace-o:TestMethod ;
  owl:inverseOf pace-o:testMethodIsUsedBy ;
.
pace-o:id
  a owl:DatatypeProperty ;
  rdfs:label "id" ;
  rdfs:comment "Unique identifier of the artifact" ;
  rdfs:subPropertyOf rdfs:label ;
.
pace-o:impactIsPartOf
  a owl:ObjectProperty ;
  rdfs:label "is part of" ;
  owl:inverseOf pace-o:correspondingImpact ;
.
pace-o:implementsFunctionality
  a owl:ObjectProperty ;
  rdfs:label "implements functionality" ;
  rdfs:subPropertyOf pace-o:implements ;
  owl:inverseOf pace-o:functionalityIsImplementedBy ;
.
pace-o:implementsRequirement
  a owl:ObjectProperty ;
  rdfs:label "implements requirement" ;
  rdfs:subPropertyOf pace-o:implements ;
  owl:inverseOf pace-o:requirementIsImplementedBy ;
.
pace-o:includes
  a owl:ObjectProperty ;
  rdfs:label "includes" ;
  owl:inverseOf pace-o:isIncludedBy ;
.
pace-o:inducesRisk
  a owl:ObjectProperty ;
  rdfs:label "induces risk" ;
  owl:inverseOf pace-o:originatesFromScenario ;
.
pace-o:instantiates
  a owl:ObjectProperty ;
  rdfs:label "instantiates" ;
  owl:inverseOf pace-o:isInstantiatedBy ;
.
pace-o:isAcceptedWith
  a owl:ObjectProperty ;
  rdfs:label "is accepted with" ;
  owl:inverseOf pace-o:accepts ;
.
pace-o:isAggregatedBy
  a owl:ObjectProperty ;
  rdfs:label "is aggregated by" ;
  owl:inverseOf pace-o:aggregates ;
.
pace-o:isAssociatedBy
  a owl:ObjectProperty ;
  rdfs:label "is associated by" ;
  owl:inverseOf pace-o:associates ;
.
pace-o:isCalculatedByFailure
  a owl:ObjectProperty ;
  rdfs:label "Failure is calculated by" ;
  rdfs:subPropertyOf pace-o:isCalculatedBy ;
  owl:inverseOf pace-o:calculatesFailure ;
.
pace-o:isCalculatedByRequirement
  a owl:ObjectProperty ;
  rdfs:label "is calculated by" ;
  rdfs:subPropertyOf pace-o:isCalculatedBy ;
  owl:inverseOf pace-o:calculatesRequirement ;
.
pace-o:isCausedBy
  a owl:ObjectProperty ;
  rdfs:label "is caused by" ;
  owl:inverseOf pace-o:resultsIn ;
.
pace-o:isCompromisedBy
  a owl:ObjectProperty ;
  rdfs:label "is compromised by" ;
  owl:inverseOf pace-o:compromises ;
.
pace-o:isConsequenceOf
  a owl:ObjectProperty ;
  rdfs:label "is consequence of" ;
  owl:inverseOf pace-o:leadsTo ;
.
pace-o:isContainedIn
  a owl:ObjectProperty ;
  rdfs:label "is contained in" ;
  owl:inverseOf pace-o:contains ;
.
pace-o:isCoveredBy
  a owl:ObjectProperty ;
  rdfs:label "is covered by" ;
  owl:inverseOf pace-o:covers ;
.
pace-o:isDeployedBy
  a owl:ObjectProperty ;
  rdfs:label "is deployed by" ;
  owl:inverseOf pace-o:deploys ;
.
pace-o:isDerivedFrom
  a owl:ObjectProperty ;
  rdfs:label "is derived from" ;
  owl:inverseOf pace-o:isDerivedTo ;
.
pace-o:isDerivedTo
  a owl:ObjectProperty ;
  rdfs:label "is derived to" ;
  owl:inverseOf pace-o:isDerivedFrom ;
.
pace-o:isDiscussedBy
  a owl:ObjectProperty ;
  rdfs:label "is discussed by" ;
  owl:inverseOf pace-o:discusses ;
.
pace-o:isInPortOf
  a owl:ObjectProperty ;
  rdfs:label "is in-port of" ;
  owl:inverseOf pace-o:hasInPort ;
.
pace-o:isIncludedBy
  a owl:ObjectProperty ;
  rdfs:label "is included by" ;
  owl:inverseOf pace-o:includes ;
.
pace-o:isInstantiatedBy
  a owl:ObjectProperty ;
  rdfs:label "is instantiated by" ;
  owl:inverseOf pace-o:instantiates ;
.
pace-o:isMitigatedBy
  a owl:ObjectProperty ;
  rdfs:label "is mitigated by" ;
  owl:inverseOf pace-o:mitigates ;
.
pace-o:isMonitoredBy
  a owl:ObjectProperty ;
  rdfs:label "is monitored by" ;
  owl:inverseOf pace-o:monitors ;
.
pace-o:isOutPortOf
  a owl:ObjectProperty ;
  rdfs:label "is out-port of" ;
  owl:inverseOf pace-o:hasOutPort ;
.
pace-o:isPackageOf
  a owl:ObjectProperty ;
  rdfs:label "is package of" ;
  owl:inverseOf pace-o:package ;
.
pace-o:isPortOf
  a owl:ObjectProperty ;
  rdfs:label "is port of" ;
  owl:inverseOf pace-o:hasPort ;
.
pace-o:isPreventedBy
  a owl:ObjectProperty ;
  rdfs:label "is prevented by" ;
  owl:inverseOf pace-o:prevents ;
.
pace-o:isRealizedBy
  a owl:ObjectProperty ;
  rdfs:label "is realized by" ;
  owl:inverseOf pace-o:realizes ;
.
pace-o:isReducedTo
  a owl:ObjectProperty ;
  rdfs:label "is reduced to" ;
  owl:inverseOf pace-o:reduces ;
.
pace-o:isReferencedBy
  a owl:ObjectProperty ;
  rdfs:label "is referenced by" ;
  owl:inverseOf pace-o:references ;
.
pace-o:isRequiredBy
  a owl:ObjectProperty ;
  rdfs:label "is required by" ;
  owl:inverseOf pace-o:requires ;
.
pace-o:isResolvedBy
  a owl:ObjectProperty ;
  rdfs:label "is resolved by" ;
  owl:inverseOf pace-o:resolves ;
.
pace-o:isSecMitigatedBy
  a owl:ObjectProperty ;
  rdfs:label "is sec_mitigated by" ;
  owl:inverseOf pace-o:sec_mitigates ;
.
pace-o:isSecurityClassifiedAs
  a owl:ObjectProperty ;
  rdfs:label "is security classified as" ;
  owl:inverseOf pace-o:isSecurityClassificationOf ;
.
pace-o:isSecurityClassificationOf
  a owl:ObjectProperty ;
  rdfs:label "is security classification of" ;
  owl:inverseOf pace-o:isSecurityClassifiedAs ;
.
pace-o:isSpecifiedByTestDesign
  a owl:ObjectProperty ;
  rdfs:label "is specified by test design" ;
  rdfs:comment "Links the related test-design. This is mandatory for series-ready test cases. Usually a test case is derived from 1 test-design." ;
  owl:inverseOf pace-o:specifiesTestCase ;
.
pace-o:isStoredOn
  a owl:ObjectProperty ;
  rdfs:label "is stored on" ;
  owl:inverseOf pace-o:stores ;
.
pace-o:isSupportedBy
  a owl:ObjectProperty ;
  rdfs:label "is supported by" ;
  owl:inverseOf pace-o:supports ;
.
pace-o:isSutProfileOf
  a owl:ObjectProperty ;
  rdfs:label "is SuT profile of" ;
  rdfs:domain pace-o:SutProfile ;
  owl:inverseOf pace-o:hasSutProfile ;
.
pace-o:isTransferredWith
  a owl:ObjectProperty ;
  rdfs:label "is transferred with" ;
  owl:inverseOf pace-o:transfers ;
.
pace-o:isTriggeredByFailure
  a owl:ObjectProperty ;
  rdfs:label "is triggered by failure" ;
  rdfs:subPropertyOf pace-o:isTriggeredBy ;
  owl:inverseOf pace-o:FailureTriggers ;
.
pace-o:isTriggeredByTriggeringCondition
  a owl:ObjectProperty ;
  rdfs:label "is triggered by triggering condition" ;
  rdfs:subPropertyOf pace-o:isTriggeredBy ;
  owl:inverseOf pace-o:TriggeringConditionTriggers ;
.
pace-o:isVerifiedBy
  a owl:ObjectProperty ;
  rdfs:label "is verified by" ;
  owl:inverseOf pace-o:verifies ;
.
pace-o:issueId
  a owl:DatatypeProperty ;
  rdfs:label "issue ID" ;
  rdfs:comment "Link any ado ticket related to the failure of the test" ;
.
pace-o:kpiValueUsed
  a owl:ObjectProperty ;
  rdfs:label "KPI value used" ;
  owl:inverseOf pace-o:kpiValueUsedBy ;
.
pace-o:kpiValueUsedBy
  a owl:ObjectProperty ;
  rdfs:label "KPI value used by" ;
  owl:inverseOf pace-o:kpiValueUsed ;
.
pace-o:leadsTo
  a owl:ObjectProperty ;
  rdfs:label "leads to" ;
  owl:inverseOf pace-o:isConsequenceOf ;
.
pace-o:mitigates
  a owl:ObjectProperty ;
  rdfs:label "mitigates" ;
  owl:inverseOf pace-o:isMitigatedBy ;
.
pace-o:monitors
  a owl:ObjectProperty ;
  rdfs:label "monitors" ;
  owl:inverseOf pace-o:isMonitoredBy ;
.
pace-o:package
  a owl:ObjectProperty ;
  rdfs:label "package" ;
  owl:inverseOf pace-o:isPackageOf ;
.
pace-o:prevents
  a owl:ObjectProperty ;
  rdfs:label "prevents" ;
  owl:inverseOf pace-o:isPreventedBy ;
.
pace-o:productVariantIsSubjectTo
  a owl:ObjectProperty ;
  rdfs:label "variant is subject to" ;
  owl:inverseOf pace-o:appliesToProductVariant ;
.
pace-o:provides
  a owl:ObjectProperty ;
  rdfs:label "provides" ;
  owl:inverseOf pace-o:isProvidedBy ;
.
pace-o:realizes
  a owl:ObjectProperty ;
  rdfs:label "realizes" ;
  owl:inverseOf pace-o:isRealizedBy ;
.
pace-o:reduces
  a owl:ObjectProperty ;
  rdfs:label "reduces" ;
  owl:inverseOf pace-o:isReducedTo ;
.
pace-o:references
  a owl:ObjectProperty ;
  rdfs:label "references" ;
  owl:inverseOf pace-o:isReferencedBy ;
.
pace-o:requirementImposedBy
  a owl:ObjectProperty ;
  rdfs:label "requirement imposed by" ;
  owl:inverseOf pace-o:requirementImposedBy ;
.
pace-o:requirementImposedBy
  a owl:ObjectProperty ;
  rdfs:label "imposes requirement" ;
  owl:inverseOf pace-o:requirementImposedBy ;
.
pace-o:requirementIsImplementedBy
  a owl:ObjectProperty ;
  rdfs:label "requirement is implemented by" ;
  rdfs:subPropertyOf pace-o:isImplementedBy ;
  owl:inverseOf pace-o:implementsRequirement ;
.
pace-o:requires
  a owl:ObjectProperty ;
  rdfs:label "requires" ;
  owl:inverseOf pace-o:isRequiredBy ;
.
pace-o:resolves
  a owl:ObjectProperty ;
  rdfs:label "resolves" ;
  owl:inverseOf pace-o:isResolvedBy ;
.
pace-o:resultsIn
  a owl:ObjectProperty ;
  rdfs:label "results in" ;
  owl:inverseOf pace-o:isCausedBy ;
.
pace-o:runsScenario
  a owl:ObjectProperty ;
  rdfs:label "runs scenario" ;
  rdfs:domain pace-o:TestPlatform ;
  rdfs:range pace-o:Scenario ;
  owl:inverseOf pace-o:scenarioRunsOnTestPlatform ;
.
pace-o:runsSutProfile
  a owl:ObjectProperty ;
  rdfs:label "runs test profile" ;
  rdfs:domain pace-o:TestPlatform ;
  rdfs:range pace-o:SutProfile ;
  owl:inverseOf pace-o:sutProfileRunsOnTestPlatform ;
.
pace-o:scenarioRunsOnTestPlatform
  a owl:ObjectProperty ;
  rdfs:label "scenario runs on test platform" ;
  rdfs:domain pace-o:Scenario ;
  rdfs:range pace-o:TestPlatform ;
  rdfs:subPropertyOf pace-o:runsOnTestPlatform ;
  owl:inverseOf pace-o:runsScenario ;
.
pace-o:scenarioTestedBy
  a owl:ObjectProperty ;
  rdfs:label "scenario tested by" ;
  owl:inverseOf pace-o:testsScenario ;
.
pace-o:sec_mitigates
  a owl:ObjectProperty ;
  rdfs:label "sec_mitigates" ;
  owl:inverseOf pace-o:isSecMitigatedBy ;
.
pace-o:securityAsset
  a owl:ObjectProperty ;
  rdfs:label "security asset" ;
  owl:inverseOf pace-o:originatesFromRelevance ;
.
pace-o:specifiesTestCase
  a owl:ObjectProperty ;
  rdfs:label "specifies test case" ;
  owl:inverseOf pace-o:isSpecifiedByTestDesign ;
.
pace-o:stores
  a owl:ObjectProperty ;
  rdfs:label "stores" ;
  owl:inverseOf pace-o:isStoredOn ;
.
pace-o:supports
  a owl:ObjectProperty ;
  rdfs:label "supports" ;
  owl:inverseOf pace-o:isSupportedBy ;
.
pace-o:sutProfileRunsOnTestPlatform
  a owl:ObjectProperty ;
  rdfs:label "SuT profile runs on test platform" ;
  rdfs:domain pace-o:SutProfile ;
  rdfs:range pace-o:TestPlatform ;
  rdfs:subPropertyOf pace-o:runsOnTestPlatform ;
  owl:inverseOf pace-o:runsSutProfile ;
.
pace-o:tag
  a owl:DatatypeProperty ;
  rdfs:label "tag" ;
  rdfs:comment "Additional tag for the artifact." ;
.
pace-o:testMethodIsUsedBy
  a owl:ObjectProperty ;
  rdfs:label "test method is used by" ;
  rdfs:domain pace-o:TestMethod ;
  rdfs:range pace-o:TestDesign ;
  owl:inverseOf pace-o:hasTestMethod ;
.
pace-o:testsScenario
  a owl:ObjectProperty ;
  rdfs:label "tests scenario" ;
  owl:inverseOf pace-o:scenarioTestedBy ;
.
pace-o:threadIsPartOf
  a owl:ObjectProperty ;
  rdfs:label "is part of" ;
  owl:inverseOf pace-o:correspondingThreat ;
.
pace-o:title
  a owl:DatatypeProperty ;
  rdfs:label "title" ;
  rdfs:comment "Title of the artifact." ;
.
pace-o:transfers
  a owl:ObjectProperty ;
  rdfs:label "transfers" ;
  owl:inverseOf pace-o:isTransferredWith ;
.
pace-o:verifies
  a owl:ObjectProperty ;
  rdfs:label "verifies" ;
  owl:inverseOf pace-o:isVerifiedBy ;
.
pace-o:viewedBy
  a owl:ObjectProperty ;
  rdfs:label "viewed by" ;
  owl:inverseOf pace-o:views ;
.
pace-o:views
  a owl:ObjectProperty ;
  rdfs:label "views" ;
  owl:inverseOf pace-o:viewedBy ;
.
pace-o:SafetyRelevanceOfFailure
  a owl:ObjectProperty ;
  rdfs:label "is safety relevance of" ;
  owl:inverseOf pace-o:FailurehasSafetyRelevance ;
.
pace-o:agreed
  a pace-o:ArtifactStatus ;
  rdfs:label "agreed" ;
  pace-o:description "Artifact is formally reviewed, no further changes expected" ;
  pace-o:hasNextStatus pace-o:draft ;
  pace-o:hasNextStatus pace-o:obsolete ;
.
pace-o:correspondingThreat
  a owl:ObjectProperty ;
  rdfs:label "corresponding threat" ;
  owl:inverseOf pace-o:threadIsPartOf ;
.
pace-o:endsView
  a owl:ObjectProperty ;
  rdfs:label "ends view" ;
  owl:inverseOf pace-o:endsView ;
.
pace-o:hasNextStatus
  a owl:ObjectProperty ;
  rdfs:label "has next status" ;
  rdfs:comment "Is preceding status." ;
  rdfs:domain pace-o:Status ;
  rdfs:range pace-o:Status ;
  owl:inverseOf pace-o:hasPreviousStatus ;
.
pace-o:hasPreviousStatus
  a owl:ObjectProperty ;
  rdfs:label "has previous status" ;
  rdfs:domain pace-o:Status ;
  rdfs:range pace-o:Status ;
  owl:inverseOf pace-o:hasNextStatus ;
.
pace-o:isAllocatedTo
  a owl:ObjectProperty ;
  rdfs:label "is allocated to" ;
  rdfs:comment "list of tools affected by this requirement" ;
  owl:inverseOf pace-o:allocates ;
.
pace-o:isFailureSafetyRelevanceOf
  a owl:ObjectProperty ;
  rdfs:label "is failure safetyrelevance of" ;
  rdfs:domain pace-o:FailureSafetyRelevance ;
  rdfs:range pace-o:Failure ;
  rdfs:subPropertyOf pace-o:isSafetyRelevanceOf ;
  owl:inverseOf pace-o:hasFailureSafetyRelevance ;
.
pace-o:isStatusOf
  a owl:ObjectProperty ;
  rdfs:label "is status of" ;
  rdfs:domain pace-o:Status ;
  rdfs:range pace-o:ArtifactWithStatus ;
  owl:inverseOf pace-o:hasStatus ;
.
pace-o:isProvidedBy
  a owl:ObjectProperty ;
  rdfs:label "is provided by" ;
  rdfs:label "is provided by / required for" ;
  owl:inverseOf pace-o:provides ;
.
pace-o:originatesFromScenario
  a owl:ObjectProperty ;
  rdfs:label "originates from scenario" ;
  owl:inverseOf pace-o:inducesRisk ;
.
pace-o:originatesFromRelevance
  a owl:ObjectProperty ;
  rdfs:label "originates from relevance" ;
  owl:inverseOf pace-o:securityAsset ;
.
pace-o:runsOnTestPlatform
  a owl:ObjectProperty ;
  rdfs:label "runs on test platform" ;
  rdfs:range pace-o:TestPlatform ;
  owl:inverseOf pace-o:runsArtifact ;
.
pace-o:runsArtifact
  a owl:ObjectProperty ;
  rdfs:label "is test platform" ;
  rdfs:range pace-o:Artifact ;
  owl:inverseOf pace-o:runsOnTestPlatform ;
.
pace-o:belongsTo
  a owl:ObjectProperty ;
  rdfs:label "belongs to" ;
  owl:inverseOf pace-o:comprises ;
.
pace-o:calculates
  a owl:ObjectProperty ;
  rdfs:label "calculates" ;
  owl:inverseOf pace-o:isCalculatedBy ;
.
pace-o:comprises
  a owl:ObjectProperty ;
  rdfs:label "comprises" ;
  owl:inverseOf pace-o:belongsTo ;
.
pace-o:hasStatus
  a owl:ObjectProperty ;
  rdfs:label "has status" ;
  rdfs:range pace-o:Status ;
  owl:inverseOf pace-o:isStatusOf ;
.
pace-o:implements
  a owl:ObjectProperty ;
  rdfs:label "implements" ;
  owl:inverseOf pace-o:isImplementedBy ;
.
pace-o:isCalculatedBy
  a owl:ObjectProperty ;
  rdfs:label "is calculated by" ;
  owl:inverseOf pace-o:calculates ;
.
pace-o:isImplementedBy
  a owl:ObjectProperty ;
  rdfs:label "is implemented by" ;
  owl:inverseOf pace-o:implements ;
.
pace-o:isTriggeredBy
  a owl:ObjectProperty ;
  rdfs:label "is triggered by" ;
  owl:inverseOf pace-o:triggers ;
.

pace-o:n_a
  a pace-o:FailureSafetyRelevance ;
  rdfs:label "n/a" ;
  pace-o:description "Failure is not applicable, should be used for irrelevant combinations of interface with guideword to document completeness" ;
  pace-o:hasNextStatus pace-o:not_safety_relevant ;
  pace-o:hasNextStatus pace-o:yes ;
  pace-o:hasNextStatus pace-o:yes_FuSa ;
.

pace-o:triggers
  a owl:ObjectProperty ;
  rdfs:label "triggers" ;
  owl:inverseOf pace-o:isTriggeredBy ;
.
pace-o:yes
  a pace-o:FailureSafetyRelevance ;
  rdfs:label "yes" ;
  pace-o:description "Failure is safety relevant" ;
  pace-o:hasNextStatus pace-o:n_a ;
  pace-o:hasNextStatus pace-o:not_safety_relevant ;
  pace-o:hasNextStatus pace-o:yes_FuSa ;
.
pace-o:Artifact
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Artifact" ;
  sh:property pace-o:Artifact-description ;
  sh:property pace-o:Artifact-id ;
  sh:property pace-o:Artifact-tag ;
  sh:property pace-o:Artifact-title ;
.
pace-o:affects
  a owl:ObjectProperty ;
  rdfs:label "affects" ;
  owl:inverseOf pace-o:isAffectedBy ;
.
pace-o:GdprImpact
  a owl:Class ;
  rdfs:label "GDPR impact" ;
  sh:description "The GDPR impact shall give an overview how sensitive the data processed by this tool is. Hence, it enables to focus on the tools processing the most sensitive data first. Independent from the actual rating the same processes must be followed." ;
  pace-o:package pace-o:ToolDatabasePackage ;
.
pace-o:isAffectedBy
  a owl:ObjectProperty ;
  rdfs:label "is affected by" ;
  owl:inverseOf pace-o:affects ;
.
pace-o:EvaluationTooling
  a owl:Class ;
  a owl:DatatypeProperty ;
  a sh:NodeShape ;
  rdfs:label "evaluation tooling" ;
  rdfs:comment "Evaluation Toolings supported by Test Collector" ;
  rdfs:subClassOf pace-o:Artifact ;
.
pace-o:VerificationMethod
  a owl:Class ;
  rdfs:label "Verification Method" ;
.
pace-o:obsolete
  a pace-o:ArtifactStatus ;
  rdfs:label "obsolete" ;
  pace-o:description "Artifact is no longer valid, but still relevant to the project" ;
  pace-o:hasNextStatus pace-o:draft ;
.
pace-o:ArtifactWithStatus
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Artifact" ;
  rdfs:subClassOf pace-o:ArtifactWithOwner ;
  sh:property pace-o:Artifact-status ;
.
pace-o:ArtifactWithOwner
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Artifact" ;
  rdfs:subClassOf pace-o:Artifact ;
  sh:property pace-o:ArtifactWithOwner-ownership-team;
  sh:property pace-o:ArtifactWithOwner-ownership-cluster;
  sh:property pace-o:ArtifactWithOwner-ownership-domain;
.
pace-o:ArtifactWithOwner-ownership-team
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:description "Auto generated ownership information." ;
  sh:maxCount 1 ;
  sh:message "One and only one owner is allowed" ;
  sh:minCount 1 ;
  sh:name "ownership-team" ;
  sh:path pace-o:ownership_team ;
  sh:severity sh:Warning ;
.
pace-o:ArtifactWithOwner-ownership-cluster
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:description "Auto generated ownership information." ;
  sh:maxCount 1 ;
  sh:message "One and only one owner is allowed" ;
  sh:name "ownership-cluster" ;
  sh:path pace-o:ownership_cluster ;
  sh:severity sh:Warning ;
.
pace-o:ArtifactWithOwner-ownership-domain
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:description "Auto generated ownership information." ;
  sh:maxCount 1 ;
  sh:message "One and only one owner is allowed" ;
  sh:name "ownership-domain" ;
  sh:path pace-o:ownership_domain ;
  sh:severity sh:Warning ;
.


pace-o:ArtifactStatus
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Artifact Status" ;
  rdfs:comment """Status of the concrete artifact (e.g., requirement status, or test case status)
https://pages.github.boschdevcloud.com/Half-Dome/colibry-builds/methods/artefact-status/status_model.html#generic-status-net""" ;
  rdfs:subClassOf pace-o:Status ;
  sh:property pace-o:ArtifactStatus-isArtifactStatusOf ;
.
pace-o:FailureSafetyRelevance
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Failure SafetyRelevance" ;
  rdfs:comment "SafetyRelevance of the Failure to define the necessary process steps" ;
  rdfs:subClassOf pace-o:SafetyRelevance ;
  sh:property pace-o:FailureSafetyRelevance-isFailureSafetyRelevanceOf ;
.

pace-o:Status
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Status" ;
  sh:property pace-o:Status-hasNextStatus ;
  sh:property pace-o:Status-hasPreviousStatus ;
.
pace-o:TestMethod
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test Method" ;
  rdfs:comment "Possible test methods of a test-design" ;
.
pace-o:NeedsPackage
  a owl:Class ;
  rdfs:label "pace-o:Package" ;
.
pace-o:TestPlatform
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test Platform" ;
  rdfs:comment "Test Platform defines the different test approaches we use in PACE" ;
  rdfs:subClassOf pace-o:Artifact ;
.
pace-o:Guideword
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Guideword" ;
  rdfs:comment "Guideword which was applied to identify the Failure" ;
  sh:property pace-o:Guideword-appliedTo ;
.
