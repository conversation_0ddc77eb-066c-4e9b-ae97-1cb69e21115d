@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

pace-o:ActivityGroup
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Activity Group" ;
  rdfs:subClassOf pace-o:SoftwareArchitectureElement ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:ArchEECU
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "ArchE ECU" ;
  rdfs:subClassOf pace-o:PhysicalArchitectureElement ;
  sh:property pace-o:ArchEECU-composes ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:Assumption
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Assumption" ;
  rdfs:subClassOf pace-o:ArchitectureElement ;
  sh:property pace-o:Assumption-belongsTo ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:Deploy
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Deploy" ;
  rdfs:subClassOf pace-o:SoftwareArchitectureElement ;
  sh:property pace-o:Deploy-deploys ;
  sh:property pace-o:Deploy-implements ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:InGateway
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "In Gateway" ;
  rdfs:subClassOf pace-o:Gateway ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:OutGateway
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Out Gateway" ;
  rdfs:subClassOf pace-o:Gateway ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:Package
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Package" ;
  rdfs:subClassOf pace-o:LogicalArchitectureElement ;
  sh:property pace-o:Package-contains ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:SolutionConcept
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Solution concept" ;
  rdfs:subClassOf pace-o:ArchitectureElement ;
  sh:property pace-o:SolutionConcept-belongsTo ;
  sh:property pace-o:SolutionConcept-realizes ;
  sh:property pace-o:SolutionConcept-resolves ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:ArchEECU-composes
  a sh:PropertyShape ;
  sh:class pace-o:ArchESOC ;
  sh:name "composes" ;
  sh:path pace-o:composes ;
  sh:severity sh:Warning ;
.
pace-o:ArchEPartition
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "ArchE Partition" ;
  rdfs:subClassOf pace-o:PhysicalArchitectureElement ;
  sh:property pace-o:ArchEPartition-contains ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:ArchEPartition-contains
  a sh:PropertyShape ;
  sh:class pace-o:Block ;
  sh:name "contains" ;
  sh:path pace-o:contains ;
  sh:severity sh:Warning ;
.
pace-o:ArchESOC
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "ArchE SOC" ;
  rdfs:subClassOf pace-o:PhysicalArchitectureElement ;
  sh:property pace-o:ArchESOC-composes ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:ArchESOC-composes
  a sh:PropertyShape ;
  sh:class pace-o:ArchEPartition ;
  sh:name "composes" ;
  sh:path pace-o:composes ;
  sh:severity sh:Warning ;
.

pace-o:Assumption-belongsTo
  a sh:PropertyShape ;
  sh:name "belongs to" ;
  sh:path pace-o:belongsTo ;
  sh:severity sh:Warning ;
.
pace-o:Block-aggregates
  a sh:PropertyShape ;
  sh:class pace-o:Part ;
  sh:name "aggregates" ;
  sh:path pace-o:aggregates ;
  sh:severity sh:Violation ;
.
pace-o:Block-associates
  a sh:PropertyShape ;
  sh:name "associates" ;
  sh:path pace-o:associates ;
  sh:severity sh:Warning ;
.
pace-o:Block-composes
  a sh:PropertyShape ;
  sh:class pace-o:Block ;
  sh:name "is composed of" ;
  sh:path pace-o:composes ;
  sh:severity sh:Warning ;
.
pace-o:Block-hasPort
  a sh:PropertyShape ;
  sh:class pace-o:ProxyPort ;
  sh:name "has port" ;
  sh:path pace-o:hasPort ;
  sh:severity sh:Warning ;
.
pace-o:Block-implements-Functionality
  a sh:PropertyShape ;
  sh:class pace-o:Functionality ;
  sh:name "implements" ;
  sh:path pace-o:implementsFunctionality ;
  sh:severity sh:Warning ;
.
pace-o:Block-implements-Requirement
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:name "implements" ;
  sh:path pace-o:implementsRequirement ;
  sh:severity sh:Warning ;
.
pace-o:Deploy-deploys
  a sh:PropertyShape ;
  sh:class pace-o:SoftwareComponent ;
  sh:name "deploys" ;
  sh:path pace-o:deploys ;
  sh:severity sh:Warning ;
.
pace-o:Deploy-implements
  a sh:PropertyShape ;
  sh:name "deploys" ;
  sh:path pace-o:implements ;
  sh:severity sh:Warning ;
.
pace-o:FunctionalChain
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Functional Chain" ;
  rdfs:subClassOf pace-o:FunctionalArchitectureElement ;
  sh:property pace-o:FunctionalChain-contains ;
  sh:property pace-o:FunctionalChain-objectflow ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:FunctionalChain-contains
  a sh:PropertyShape ;
  sh:class pace-o:Functionality ;
  sh:name "contains" ;
  sh:path pace-o:contains ;
  sh:severity sh:Warning ;
.
pace-o:FunctionalChain-objectflow
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "objectflow" ;
  sh:path pace-o:objectFlow ;
  sh:severity sh:Warning ;
.
pace-o:Functionality-belongsTo
  a sh:PropertyShape ;
  sh:class pace-o:Feature ;
  sh:name "belongs to" ;
  sh:path pace-o:belongsTo ;
  sh:severity sh:Warning ;
.
pace-o:Functionality-composes
  a sh:PropertyShape ;
  sh:class pace-o:FunctionalChain ;
  sh:name "is composed of" ;
  sh:path pace-o:composes ;
  sh:severity sh:Warning ;
.
pace-o:Functionality-realizes
  a sh:PropertyShape ;
  sh:class pace-o:SafetyMeasure ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Warning ;
.
pace-o:Functionality-supports
  a sh:PropertyShape ;
  sh:class pace-o:Feature ;
  sh:name "supports" ;
  sh:path pace-o:supports ;
  sh:severity sh:Warning ;
.
pace-o:Functionality-contains
  a sh:PropertyShape ;
  sh:severity sh:Violation ;
  sh:description "The lower level functionalities which are contained in the functional chain of the present functionality." ;
  sh:path pace-o:contains ;
  sh:class pace-o:Functionality ;
  sh:name "contains" ;
.
pace-o:Functionality-objectflow
  a sh:PropertyShape ;
  sh:severity sh:Warning ;
  sh:description "The object flow for the functional chain." ;
  sh:path pace-o:objectFlow ;
  sh:datatype xsd:string ;
  sh:name "objectflow" ;
.
pace-o:InputPort
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Input Port" ;
  rdfs:subClassOf pace-o:SoftwareArchitectureElement ;
  sh:property pace-o:InputPort-realizes ;
  sh:property pace-o:InputPort-requires ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:InputPort-realizes
  a sh:PropertyShape ;
  sh:class pace-o:ProxyPort ;
  sh:description "The proxy port which the input port realizes." ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Violation ;
.
pace-o:InputPort-requires
  a sh:PropertyShape ;
  sh:class pace-o:Interface ;
  sh:name "requires" ;
  sh:path pace-o:requires ;
  sh:severity sh:Warning ;
.
pace-o:Interface-realizes
  a sh:PropertyShape ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Warning ;
.
pace-o:OutputPort
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Output Port" ;
  rdfs:subClassOf pace-o:SoftwareArchitectureElement ;
  sh:property pace-o:OutputPort-provides ;
  sh:property pace-o:OutputPort-realizes ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:OutputPort-provides
  a sh:PropertyShape ;
  sh:class pace-o:Interface ;
  sh:name "provides" ;
  sh:path pace-o:provides ;
  sh:severity sh:Warning ;
.
pace-o:OutputPort-realizes
  a sh:PropertyShape ;
  sh:class pace-o:ProxyPort ;
  sh:description "The proxy port which the output port realizes." ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Violation ;
.
pace-o:Package-contains
  a sh:PropertyShape ;
  sh:name "contains" ;
  sh:path pace-o:contains ;
  sh:severity sh:Warning ;
.
pace-o:Part
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Part" ;
  rdfs:subClassOf pace-o:LogicalArchitectureElement ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:ProxyPort-provides
  a sh:PropertyShape ;
  sh:class pace-o:InterfaceBlock ;
  sh:name "provides" ;
  sh:path pace-o:provides ;
  sh:severity sh:Warning ;
.
pace-o:ProxyPort-requires
  a sh:PropertyShape ;
  sh:class pace-o:InterfaceBlock ;
  sh:name "requires" ;
  sh:path pace-o:requires ;
  sh:severity sh:Warning ;
.
pace-o:SoftwareComponent-hasInPort
  a sh:PropertyShape ;
  sh:class pace-o:InputPort ;
  sh:name "has input port" ;
  sh:path pace-o:hasInPort ;
  sh:severity sh:Warning ;
.
pace-o:SoftwareComponent-hasOutPort
  a sh:PropertyShape ;
  sh:class pace-o:OutputPort ;
  sh:name "has output port" ;
  sh:path pace-o:hasOutPort ;
  sh:severity sh:Warning ;
.
pace-o:SoftwareComponent-implements
  a sh:PropertyShape ;
  sh:name "implements" ;
  sh:path pace-o:implements ;
  sh:severity sh:Warning ;
.
pace-o:SoftwareComponent-instantiates
  a sh:PropertyShape ;
  sh:class pace-o:SoftwareComponent ;
  sh:name "instantiates" ;
  sh:path pace-o:instantiates ;
  sh:severity sh:Warning ;
.
pace-o:SoftwareComponent-realizes
  a sh:PropertyShape ;
  sh:class pace-o:Block ;
  sh:description "The block which the software component realizes." ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Violation ;
.
pace-o:SolutionConcept-belongsTo
  a sh:PropertyShape ;
  sh:name "belongs to" ;
  sh:path pace-o:belongsTo ;
  sh:severity sh:Warning ;
.
pace-o:SolutionConcept-realizes
  a sh:PropertyShape ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Warning ;
.
pace-o:SolutionConcept-resolves
  a sh:PropertyShape ;
  sh:class pace-o:Failure ;
  sh:name "resolves" ;
  sh:path pace-o:resolves ;
  sh:severity sh:Warning ;
.
pace-o:FunctionalArchitectureElement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Functional Architecture Element" ;
  rdfs:subClassOf pace-o:ArchitectureElement ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:Functionality
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Functionality" ;
  rdfs:subClassOf pace-o:FunctionalArchitectureElement ;
  sh:property pace-o:Functionality-belongsTo ;
  sh:property pace-o:Functionality-composes ;
  sh:property pace-o:Functionality-realizes ;
  sh:property pace-o:Functionality-supports ;
  sh:property pace-o:Functionality-contains ;
  sh:property pace-o:Functionality-objectflow ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:Interface
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Interface" ;
  rdfs:subClassOf pace-o:SoftwareArchitectureElement ;
  sh:property pace-o:Interface-realizes ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:InterfaceBlock
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Interface Block" ;
  rdfs:subClassOf pace-o:LogicalArchitectureElement ;
  sh:property pace-o:InterfaceBlock-contains ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:InterfaceBlock-contains
  a sh:PropertyShape ;
  sh:class pace-o:SignalDefinition ;
  sh:name "contains" ;
  sh:path pace-o:contains ;
  sh:property pace-o:InterfaceBlock-contains ;
  sh:severity sh:Warning ;
.
pace-o:SoftwareComponent
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Software Component" ;
  rdfs:subClassOf pace-o:SoftwareArchitectureElement ;
  sh:property pace-o:SoftwareComponent-hasInPort ;
  sh:property pace-o:SoftwareComponent-hasOutPort ;
  sh:property pace-o:SoftwareComponent-implements ;
  sh:property pace-o:SoftwareComponent-instantiates ;
  sh:property pace-o:SoftwareComponent-realizes ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:Block
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Block" ;
  rdfs:subClassOf pace-o:LogicalArchitectureElement ;
  sh:property pace-o:Block-aggregates ;
  sh:property pace-o:Block-associates ;
  sh:property pace-o:Block-composes ;
  sh:property pace-o:Block-hasPort ;
  sh:property pace-o:Block-implements-Functionality ;
  sh:property pace-o:Block-implements-Requirement ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:PhysicalArchitectureElement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Physical Architecture Element" ;
  rdfs:subClassOf pace-o:ArchitectureElement ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:ProxyPort
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Proxy Port" ;
  rdfs:subClassOf pace-o:LogicalArchitectureElement ;
  sh:property pace-o:ProxyPort-provides ;
  sh:property pace-o:ProxyPort-requires ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:LogicalArchitectureElement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Logical Architecture Element" ;
  rdfs:subClassOf pace-o:ArchitectureElement ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:ArchitectureElement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Architecture Element" ;
  rdfs:subClassOf pace-o:ArtifactWithOwner ;
  pace-o:package pace-o:ArchitecturePackage ;
.
pace-o:SoftwareArchitectureElement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Software Architecture Element" ;
  rdfs:subClassOf pace-o:ArchitectureElement ;
  pace-o:package pace-o:ArchitecturePackage ;
.
