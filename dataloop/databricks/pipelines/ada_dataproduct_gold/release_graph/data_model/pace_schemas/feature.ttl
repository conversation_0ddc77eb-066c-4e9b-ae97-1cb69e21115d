@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

# --- feature class and types ---

pace-o:Feature
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Feature" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
  sh:property pace-o:Feature-dependsOn ;
  sh:property pace-o:Feature-isDerivedFrom ;
  sh:property pace-o:Feature-references ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:Feature-dependsOn
  a sh:PropertyShape ;
  sh:class pace-o:Feature ;
  sh:name "depends on" ;
  sh:path pace-o:dependsOn ;
  sh:severity sh:Warning ;
.
pace-o:Feature-isDerivedFrom
  a sh:PropertyShape ;
  sh:name "is derived from" ;
  sh:path pace-o:isDerivedFrom ;
  sh:severity sh:Warning ;
.
pace-o:Feature-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.
