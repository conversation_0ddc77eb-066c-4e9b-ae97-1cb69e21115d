@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

pace-o:Scenario
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Scenario" ;
  rdfs:comment "Element to define scenarios for simulation / recompute in" ;
  rdfs:subClassOf pace-o:ArtifactWithOwner ;
  sh:property pace-o:Scenario-belongsTo ;
  sh:property pace-o:Scenario-configs ;
  sh:property pace-o:Scenario-dataset ;
  sh:property pace-o:Scenario-recording ;
  sh:property pace-o:Scenario-scenarioFile ;
  sh:property pace-o:Scenario-syscal ;
  sh:property pace-o:Scenario-testPlatform ;
  sh:property pace-o:Scenario-variationSimulationFile ;
  sh:property pace-o:TestSpec-hasTestStatus ;
  pace-o:package pace-o:ScenarioPackage ;
.
pace-o:Scenario-belongsTo
  a sh:PropertyShape ;
  sh:name "belongs to" ;
  sh:path pace-o:belongsTo ;
  sh:severity sh:Warning ;
.
pace-o:Scenario-configs
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "Configs" ;
  sh:path pace-o:configs ;
  sh:severity sh:Warning ;
.
pace-o:Scenario-dataset
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of these attribute is allowed" ;
  sh:name "Dataset" ;
  sh:path pace-o:dataset ;
  sh:severity sh:Warning ;
.
pace-o:Scenario-recording
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "Recording" ;
  sh:path pace-o:recording ;
  sh:severity sh:Warning ;
.
pace-o:Scenario-scenarioFile
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of these attribute is allowed" ;
  sh:name "Scenario File" ;
  sh:path pace-o:scenarioFile ;
  sh:severity sh:Warning ;
.
pace-o:Scenario-syscal
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "Syscal" ;
  sh:path pace-o:syscal ;
  sh:severity sh:Warning ;
.
pace-o:Scenario-testPlatform
  a sh:PropertyShape ;
  sh:class pace-o:TestPlatform ;
  sh:message "At least one Test Platform is required" ;
  sh:minCount 1 ;
  sh:name "Test Platform" ;
  sh:path pace-o:scenarioRunsOnTestPlatform ;
  sh:severity sh:Warning ;
.
pace-o:Scenario-variationSimulationFile
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of these attribute is allowed" ;
  sh:name "Variation Simulation File" ;
  sh:path pace-o:variationSimulationFile ;
  sh:severity sh:Warning ;
.
