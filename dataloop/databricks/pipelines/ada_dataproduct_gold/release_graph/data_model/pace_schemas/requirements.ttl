@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

# --- Need new base clase with only core status handling framework but without concrete states/network ---

pace-o:Requirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Requirement" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
  rdfs:subClassOf pace-o:ArtifactWithOwner ;
  rdfs:comment "A requirement describes a desired behaviour or characteristic of the product or one of its elements." ;
  sh:property pace-o:Requirement-Description ;
  sh:property pace-o:Requirement-ProductVariant ;
  sh:property pace-o:Requirement-belongsTo-Feature ;
  sh:property pace-o:Requirement-belongsTo-Functionality ;
  sh:property pace-o:Requirement-isDerivedFrom ;
  sh:property pace-o:Requirement-isDerivedTo ;
  sh:property pace-o:Requirement-references ;
  sh:property pace-o:Requirement-safetyComment ;
  sh:property pace-o:Requirement-safetyIntegrity ;
  sh:property pace-o:Requirement-safetyRelevance ;
  sh:property pace-o:Requirement-securityRelevance ;
  sh:property pace-o:Requirement-Status ;
  sh:property pace-o:Requirement-variantRestriction ;
  sh:property pace-o:Requirement-verificationCriteria ;
  sh:property pace-o:Requirement-verificationMethod ;
  sh:rule pace-o:Requirement-AssumedRule ;
  sh:rule pace-o:Requirement-AgreedRule ;
  pace-o:package pace-o:RequirementsPackage ;
.
# --- Attributes for requirements classes and the base rules ---

pace-o:Requirement-Description
  a sh:PropertyShape ;
  sh:name "Requirement Text" ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:message "The Requirement Text for the requirement is missing" ;
  sh:description "Content of the requirement according to How to write requirements" ;
  sh:path pace-o:description ;
  sh:severity sh:Warning ;
.

# --- Reference to product variant elements ---

pace-o:Requirement-ProductVariant
  a sh:PropertyShape ;
  sh:class pace-o:ProductVariant ;
  sh:message "The Product Variant for the requirement is missing" ;
  sh:description "The variant(s) for which the requirement is applicable." ;
  sh:path pace-o:appliesToProductVariant ;
  sh:severity sh:Violation ;
.

# --- Link Attribute to Feature Element (allows the link to the target element feature) ---

pace-o:Requirement-belongsTo-Feature
  a sh:PropertyShape ;
  sh:class pace-o:Feature ;
  sh:name "belongs to" ;
  sh:message "The belongs to link to feature is missing" ;
  sh:description "Feature and/or functionality to which requirement belongs to." ;
  sh:path pace-o:belongsToFeature ;
  sh:severity sh:Warning ;
.

# --- Link Attribute to Functionality Element (allows the link to the target element functionality) ---

pace-o:Requirement-belongsTo-Functionality
  a sh:PropertyShape ;
  sh:class pace-o:Functionality ;
  sh:name "belongs to" ;
  sh:message "The belongs to link to functionality is missing" ;
  sh:description "Feature and/or functionality to which requirement belongs to." ;
  sh:path pace-o:belongsToFunctionality ;
  sh:severity sh:Warning ;
.

# --- Link Attribute to upper level Requirement incl. CB requirements ---
# --- What about cardinality? over min and max count ---
# --- How is the derived to link initiated? via inverse link, docu in pace ttl ---

pace-o:Requirement-isDerivedFrom
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:name "is derived from" ;
  sh:message "The derived relation to the parent system/software/Codebeamer requirement is missing" ;
  sh:description "Link to the parent system/software/Codebeamer requirement." ;
  sh:path pace-o:isDerivedFrom ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-isDerivedTo
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:name "is derived to" ;
  sh:description "Child of the parent system/software/Codebeamer requirement." ;
  sh:path pace-o:isDerivedTo ;
  sh:severity sh:Warning ;
.

# --- References to definitions, parameters, signals ---
# --- Which are the allowed target types for references? Add only valid types which are allowed ---

pace-o:Requirement-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:description "Reference to required definitions, parameters and signals" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.

# --- Optional attribute for further explanations ---

pace-o:Requirement-safetyComment
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "safety comment" ;
  sh:message "The source of the safety relevance of the requirement is missing" ;
  sh:description "Documents the source of the safety relevance of the requirement. See “derived requirements” as entry, for cases where the safety relevance is clear from the derived link without any further explanation. Add further explanations, if not obvious from the derived link. If the safety relevance/safety integrity is unclear, then the “working assumption on safety relevance” should be documented." ;
  sh:path pace-o:safetyComment ;
  sh:severity sh:Warning ;
.

# --- ??? Shouldn't that be a Class with LOVs (Asil-a, ...) ---

pace-o:Requirement-safetyIntegrity
  a sh:PropertyShape ;
  sh:class pace-o:Rating_SafetyIntegrity ;
  sh:name "safety integrity" ;
  sh:message "The safety rating of the requirement is missing" ;
  sh:description "Indicates the Safety rating of the requirement. Empty value means safety analysis hasn’t been done yet for this requirement." ;
  sh:path pace-o:safetyIntegrity ;
  sh:severity sh:Warning ;
.
pace-o:Rating_SafetyIntegrity
  a owl:Class ;
  rdfs:label "Rating Safety Integrity" ;
.
pace-o:QM
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "QM" ;
.
pace-o:ASIL_A
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_A" ;
.
pace-o:ASIL_B
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_B" ;
.
pace-o:ASIL_C
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_C" ;
.
pace-o:ASIL_D
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_D" ;
.
pace-o:QM_A
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "QM(A)" ;
.
pace-o:QM_B
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "QM(B)" ;
.
pace-o:QM_C
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "QM(C)" ;
.
pace-o:QM_D
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "QM(D)" ;
.
pace-o:ASIL_A_A
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_A(A)" ;
.
pace-o:ASIL_A_B
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_A(B)" ;
.
pace-o:ASIL_A_C
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_A(C)" ;
.
pace-o:ASIL_A_D
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_A(D)" ;
.
pace-o:ASIL_B_B
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_B(B)" ;
.
pace-o:ASIL_B_C
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_B(C)" ;
.
pace-o:ASIL_B_D
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_B(D)" ;
.
pace-o:ASIL_C_C
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_C(C)" ;
.
pace-o:ASIL_C_D
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_C(D)" ;
.
pace-o:ASIL_D_D
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "ASIL_D(D)" ;
.
pace-o:N_A
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "N/A" ;
.
pace-o:SELECT
  a pace-o:Rating_SafetyIntegrity ;
  rdfs:label "<SELECT>" ;
.

# --- Shouldn't that be a Class with LOVs (yes, no) ---

pace-o:Requirement-safetyRelevance
  a sh:PropertyShape ;
  sh:class pace-o:YesNo_Safety_Relevance ;
  sh:name "safety relevance" ;
  sh:message "The safety relevance of the requirement is missing" ;
  sh:description "Defines the safety relevance of a requirement. The source and rationale shall be documented in DA_Safety_Comment." ;
  sh:path pace-o:safetyRelevance ;
  sh:severity sh:Warning ;
.
pace-o:YesNo_Safety_Relevance
  a owl:Class ;
  rdfs:label "Yes No" ;
.
pace-o:No
  a pace-o:YesNo_Safety_Relevance ;
  rdfs:label "No" ;
.
pace-o:Yes
  a pace-o:YesNo_Safety_Relevance ;
  rdfs:label "Yes" ;
.
pace-o:NotRelevant
  a pace-o:YesNo_Safety_Relevance ;
  rdfs:label "Not relevant" ;
.
pace-o:SELECT
  a pace-o:YesNo_Safety_Relevance ;
  rdfs:label "<SELECT>" ;
.

# --- ??? Shouldn't that be a Class with LOVs (yes, no) ---

pace-o:Requirement-securityRelevance
  a sh:PropertyShape ;
  sh:class pace-o:YesNo_Security_Relevance ;
  sh:name "security relevance" ;
  sh:message "The security relevance of the requirement is missing" ;
  sh:description "Indicates whether the requirement is security relevant or not." ;
  sh:path pace-o:securityRelevance ;
  sh:severity sh:Warning ;
.
pace-o:YesNo_Security_Relevance
  a owl:Class ;
  rdfs:label "Yes No" ;
.
pace-o:No
  a pace-o:YesNo_Security_Relevance ;
  rdfs:label "No" ;
.
pace-o:Yes
  a pace-o:YesNo_Security_Relevance;
  rdfs:label "Yes" ;
.
pace-o:NotRelevant
  a pace-o:YesNo_Security_Relevance ;
  rdfs:label "Not relevant" ;
.
pace-o:Relevant
  a pace-o:YesNo_Security_Relevance ;
  rdfs:label "Relevant" ;
.
pace-o:SELECT
  a pace-o:YesNo_Security_Relevance ;
  rdfs:label "<SELECT>" ;
.

# --- In principle ok, any rules missing? ---

pace-o:Requirement-variantRestriction
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:description "The variant feature(s) to define applicability of the requirement to product variants." ;
  sh:name "Variant feature(s)." ;
  sh:path pace-o:variantRestriction ;
  sh:severity sh:Warning ;
.

# --- Add http link to How to: link ---

pace-o:Requirement-verificationCriteria
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "verification criteria" ;
  sh:description "Shall be used if the success criteria are not clear from the requirements itself, see How to define Verification Methods and Verification Criteria for Requirements." ;
  sh:path pace-o:verificationCriteria ;
  sh:severity sh:Warning ;
.

# --- Where are the value restrictions? ---
# --- Add link to How to ---

pace-o:Requirement-verificationMethod
  a sh:PropertyShape ;
  sh:class pace-o:VerificationMethod ;
  sh:class pace-o:Values_ReqVerificationMethod ;
  sh:name "verification method" ;
  sh:message "The valid verification method of the requirement is missing" ;
  sh:description "Documents the methods for the verification of the requirement, see How to define Verification Methods and Verification Criteria for Requirements." ;
  sh:path pace-o:verificationMethod ;
  sh:severity sh:Warning ;
.

# --- Values are already added in pace schema ttl see class pace-o:VerificationMethod  ---

pace-o:Values_ReqVerificationMethod
  a owl:Class ;
  rdfs:label "Values for Requirements Verification Method" ;
.
pace-o:Test
  a pace-o:Values_ReqVerificationMethod ;
  rdfs:label "Test" ;
.
pace-o:Analysis
  a pace-o:Values_ReqVerificationMethod ;
  rdfs:label "Analysis" ;
.
pace-o:Demonstration
  a pace-o:Values_ReqVerificationMethod ;
  rdfs:label "Demonstration" ;
.
pace-o:Review
  a pace-o:Values_ReqVerificationMethod ;
  rdfs:label "Review" ;
.
pace-o:KPI
  a pace-o:Values_ReqVerificationMethod ;
  rdfs:label "KPI" ;
.

pace-o:Requirement-Status
  a sh:PropertyShape ;
  sh:class pace-o:RequirementStatus ;
  sh:name "status" ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:message "Valid requirement status is missing" ;
  sh:description "Status of the requirement, see Requirements State-Model." ;
  sh:path pace-o:hasStatus;
  sh:severity sh:Warning ;
.

# --- Begin Status model section ---

pace-o:RequirementStatus
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Requirement Status" ;
  rdfs:comment "https://pace-docs.azurewebsites.net/pace/main/docs/developer/process_lib/product_engineering/requirements/req_states.html#requirements-state-model" ;
  rdfs:subClassOf pace-o:Status ;
.

# --- Message if a false value for status is used? ---

pace-o:RequirementStatus-draft
  a pace-o:RequirementStatus ;
  rdfs:label "draft" ;
  pace-o:description "https://pace-docs.azurewebsites.net/pace/main/docs/developer/process_lib/product_engineering/requirements/req_states.html#states-flow" ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Draft ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Assumed ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Agreed ;
  pace-o:hasNextStatus pace-o:RequirementStatus-obsolete ;
.
pace-o:RequirementStatus-assumed
  a pace-o:RequirementStatus ;
  rdfs:label "assumed" ;
  pace-o:description "https://pace-docs.azurewebsites.net/pace/main/docs/developer/process_lib/product_engineering/requirements/req_states.html#states-flow" ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Draft ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Assumed ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Agreed ;
  pace-o:hasNextStatus pace-o:RequirementStatus-obsolete ;
.
pace-o:RequirementStatus-agreed
  a pace-o:RequirementStatus ;
  rdfs:label "agreed" ;
  pace-o:description "https://pace-docs.azurewebsites.net/pace/main/docs/developer/process_lib/product_engineering/requirements/req_states.html#states-flow" ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Assumed ;
  pace-o:hasNextStatus pace-o:RequirementStatus-Agreed ;
  pace-o:hasNextStatus pace-o:RequirementStatus-obsolete ;
.
pace-o:RequirementStatus-obsolete
  a pace-o:RequirementStatus ;
  rdfs:label "obsolete" ;
  pace-o:description "https://pace-docs.azurewebsites.net/pace/main/docs/developer/process_lib/product_engineering/requirements/req_states.html#states-flow" ;
.


# --- End Status model section ---


# --- Software requirement class ---

pace-o:SoftwareRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Software Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  pace-o:package pace-o:RequirementsPackage ;
.

# --- System requirement class ---
# --- Do we need this class or only requirements, how to distinguish? ---

pace-o:SystemRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "System Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  sh:property pace-o:SystemRequirement-realizes ;
  pace-o:package pace-o:RequirementsPackage ;
.
# --- not valid for system requirements, clarify if it currently used? ---

pace-o:SystemRequirement-realizes
  a sh:PropertyShape ;
  sh:maxCount 1 ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Warning ;
.

# --- NonProduction Requirement Class ---

pace-o:NonProductionRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Non-Production Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  pace-o:package pace-o:RequirementsPackage ;
.

# --- Data Requirement class ---
pace-o:DataRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Data Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  pace-o:package pace-o:RequirementsPackage ;
.


# --- Complex Rules ---

pace-o:AgreedRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Agreed Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  sh:property pace-o:AgreedRequirement-safetyIntegrity ;
  sh:property pace-o:AgreedRequirement-safetyRelevance ;
  sh:property pace-o:AgreedRequirement-securityRelevance ;
  sh:property pace-o:AgreedRequirement-verificationMethod ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:AgreedRequirement-safetyIntegrity
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "safety integrity" ;
  sh:path pace-o:safetyIntegrity ;
  sh:severity sh:Warning ;
.
pace-o:AgreedRequirement-safetyRelevance
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "safety relevance" ;
  sh:path pace-o:safetyRelevance ;
  sh:severity sh:Warning ;
.
pace-o:AgreedRequirement-securityRelevance
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "security relevance" ;
  sh:path pace-o:securityRelevance ;
  sh:severity sh:Warning ;
.
pace-o:AgreedRequirement-verificationMethod
  a sh:PropertyShape ;
  sh:class pace-o:VerificationMethod ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "verification method" ;
  sh:path pace-o:verificationMethod ;
  sh:severity sh:Warning ;
.
pace-o:AssumedRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Assumed Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  sh:property pace-o:AssumedRequirement-verificationMethod ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:AssumedRequirement-verificationMethod
  a sh:PropertyShape ;
  sh:class pace-o:VerificationMethod ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "verification method" ;
  sh:path pace-o:verificationMethod ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-AgreedRule
  a sh:SPARQLRule ;
  rdfs:label "rule for agreed requirements" ;
  rdfs:comment "For a system or software requirement in status agreed, the attributes verification method, safety relevance, safety integrity, and security relevance shall be set" ;
  sh:construct """CONSTRUCT { ?this a pace-o:AgreedRequirement } WHERE {
      VALUES ?type { pace-o:SoftwareRequirement pace-o:SystemRequirement }
      ?this rdf:type ?type ; pace-o:hasArtifactStatus pace-o:agreed . }""" ;
.
pace-o:Requirement-AssumedRule
  a sh:SPARQLRule ;
  rdfs:label "rule for assumed requirements" ;
  rdfs:comment "For a system or software requirement in status assumed, the attribute verification method shall be set." ;
  sh:construct """CONSTRUCT { ?this a pace-o:AssumedRequirement } WHERE {
      VALUES ?type { pace-o:SoftwareRequirement pace-o:SystemRequirement }
      ?this rdf:type ?type ; pace-o:hasArtifactStatus pace-o:assumed . }""" ;
.


# --- Product Variant Class ---

pace-o:ProductVariant
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Product Variant" ;
  rdfs:subClassOf pace-o:Artifact ;
  sh:property pace-o:ProductVariant-deployment ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:ProductVariant-deployment
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "Deployment" ;
  sh:path pace-o:deployment ;
  sh:severity sh:Warning ;
.
