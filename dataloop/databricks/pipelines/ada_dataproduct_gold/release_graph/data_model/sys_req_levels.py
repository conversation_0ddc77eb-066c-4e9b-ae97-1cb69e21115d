"""SYSTEM REQUIREMENTS - CALCULATE LEVEL add as property."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging

from data_model_common import convert_to_iri_id, get_latest_versions
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import coalesce, col, lit
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging

logger = logging.getLogger(__name__)

GOLD_CATALOG = get_dbx_env_catalog("gold")
SILVER_CATALOG = get_dbx_env_catalog("silver")

NEEDS_EDGES = f"{GOLD_CATALOG}.ada_release_graph.needs_edges_v2"
TARGET_TABLE = f"{GOLD_CATALOG}.ada_release_graph.requirement_levels"

DESCRIPTION = (
    "This tables contains information about system requirement levels. These levels "
    + "are calculated based on the recursive derived level from the feature increment."
)

spark = SparkSession.builder.getOrCreate()


def get_level(edges_raw: DataFrame, target_df: DataFrame, level: int) -> DataFrame:
    """Get the derives edges to the target df, adding the level to the dataframe.

    Args:
        edges_raw (DataFrame): DataFrame with edges, filtered with one version
        target_df (DataFrame): DataFrame with previous level. To generate data for
            level 3 requirements, target_df should be of level 1, etc.
        level (int): The desired level to append to the dataframe.

    Return:
        DataFrame: One DataFrame that contains, id, derives_id, version and level.
            The derives_id indicates which requirement this one was derived from

    """
    return (
        edges_raw.where(edges_raw.type == "derives")
        .join(target_df.select("id"), edges_raw.target == target_df.id)
        .select("start", "version", "id")
        .withColumn("level", lit(level))
        .withColumnRenamed("id", "derives_id")
        .withColumnRenamed("start", "id")
        .dropDuplicates()
    )


def get_sw_level(nodes_raw: DataFrame, edges_raw: DataFrame, reqs: DataFrame) -> DataFrame:
    """Add sw as level for Software Requirements (SW-reqs) and add derives id and feature increment.

    Args:
        nodes_raw (DataFrame): Needs properties(nodes) dataframe that should be filtered for the
            desired version.
        edges_raw (DataFrame): Needs edges that should also be filtered for one version.
        reqs (DataFrame): The dataframe that contains the information about all System Requirements.

    Return:
        DataFrame: This contains all SW-reqs, with the columns id, derives_id, version, level and
            feature_increment.
    """
    req = reqs.alias("req")
    # Get software requirements
    sw_df = (
        nodes_raw.where(nodes_raw.type == "req-sw")
        .select("version", "id")
        .withColumn("level", lit("sw"))
        .dropDuplicates()
        .alias("sw_df")
    )
    # Get derives ID based on derives edges from sw-req
    sw_df = (
        sw_df.join(edges_raw.where("type = 'derives'"), sw_df.id == edges_raw.start)
        .withColumnRenamed("target", "derives_id")
        .select(sw_df.version, sw_df.id, sw_df.level, "derives_id")
    )
    # Combine with existing req to determine feature_increment
    sw_df = (
        sw_df.join(
            req.alias("req_2").select("id", "feature_increment"), sw_df.derives_id == col("req_2.id"), how="left"
        )
        .drop(col("req_2.id"))
        .dropDuplicates()
    )
    return sw_df


def join_levels(target_df: DataFrame, source_df: DataFrame) -> DataFrame:
    """Combine the level dataframes, returning the lowest level for entries with multiple levels.

    source_df is merged into target_df, with a coalesce to make sure the resulting requirement will
    have the lowest level, in case there are conflicts.

    Args:
        target_df (DataFrame): The total amount of requirement levels already calculated.
        source_df (DataFrame): The level that has not yet been combined yet.

    Return:
        DataFrame: The combined target_df.
    """
    return (
        target_df.join(source_df, on=["id", "version"], how="fullouter")
        .dropDuplicates()
        .withColumn("level_y", coalesce(target_df.level, source_df.level))
        .drop(target_df.level, source_df.level)
        .withColumnRenamed("level_y", "level")
    )


def add_feature_increment(total: DataFrame, req_x: DataFrame) -> DataFrame:
    """Combine Feature increment data.

    The feature increment for a requirement in "req_x" is the one that belongs to the requirement
    it was derived from, which is determined by the derives_id.

    Args:
        total (DataFrame): Contains the information about feature_increment and the derives_id.
        req_x (DataFrame): Dataframe with levels not yet populated with feature_increment info.

    Return:
        DataFrame: Total dataframe added with the requirement level info and feature_increment.
    """
    req = req_x.alias("req")

    double_fi = (
        total.join(
            req.select("id", "version", "feature_increment"),
            (req.id == total.derives_id) & (req.version == total.version),
            how="left",
        )
        .drop(col("req.id"), col("req.version"), col("total.derives_id"))
        .dropDuplicates()
    )

    only_total_fi = double_fi.drop(req.feature_increment).where("feature_increment is not null")
    only_req_fi = double_fi.drop(total.feature_increment).where("feature_increment is not null")

    return only_total_fi.unionByName(only_req_fi).dropDuplicates()


def run() -> None:
    """Process the latest versions."""
    versions = get_latest_versions(spark, NEEDS_EDGES, TARGET_TABLE, logger)
    if len(versions) == 0:
        logger.info("No versions found to process!")
        return

    for version in versions:
        logger.info(f"processing version: {version}")
        process_version(version)


def process_version(version: str) -> None:
    """Calculate the levels and update table.

    The logic is that the requirement level is based on how many steps from the feature
    increment it is derived from. Where level 2 is not used. So level 1 is directly related to
    the feature increment, level 3 is derived from level 2 etc.

    The feature increment node it is also kept as a node that can be referenced to from
    each requirement.

    Args:
        version(str): The version to process data from (commit SHA).

    """
    edges_raw = spark.read.table(NEEDS_EDGES).where(f"version ='{version}'")
    nodes_raw = spark.read.table(f"{SILVER_CATALOG}.needs.pace_needs").where(f"version ='{version}'")
    nodes_raw = convert_to_iri_id(nodes_raw)

    # Get Level 1 requirements - get edges of type "belongs" to a feature increment
    req_df_1 = (
        edges_raw.where(edges_raw.type == "belongs")
        .where(edges_raw.target.startswith("urn:pace:ontology:needs:FEATURE_INCREMENT"))
        .select("type", "start", "target")
        .withColumn("feature_increment", col("target"))
        .withColumn("derives_id", lit(None))
    )

    # Make sure the source node is a system requirement with correct status
    req_df_1 = req_df_1.join(
        nodes_raw,
        ((req_df_1.start == nodes_raw.id) & (nodes_raw.type == "req-sys") & (nodes_raw.da_status != "obsolete")),
    )

    req_df_1 = req_df_1.select("id", "version", "feature_increment").withColumn("level", lit(1)).dropDuplicates()

    # Calculate further levels
    req_df_3 = get_level(edges_raw, req_df_1, 3)
    req_df_4 = get_level(edges_raw, req_df_3, 4)

    # Combine the levels, making sure that the lowest level is kept for conflicting requirements
    # Then, add the feature increment for which the requirement is derived from.
    total_req_level = join_levels(req_df_1, req_df_3)
    req_1_3 = add_feature_increment(total_req_level, req_df_1)

    total_req_level = join_levels(total_req_level, req_df_4).drop(total_req_level.derives_id)
    req_1_4 = add_feature_increment(total_req_level, req_1_3).dropDuplicates()

    req_sw = get_sw_level(nodes_raw, edges_raw, req_1_4)

    req_1_sw = req_1_4.unionByName(req_sw)

    CONDITION = (
        "source.id = target.id AND "
        + "source.version = target.version AND "
        + "source.feature_increment = target.feature_increment AND "
        + "source.level = target.level"
    )
    dtu.merge_or_overwrite(req_1_sw, TARGET_TABLE, CONDITION)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Data model system requirements level transform.")
    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(GOLD_CATALOG, "ada_release_graph", False),
        "data_model/req_levels",
        run_id=args.run_id,
        enabled_loggers=["base"],
    )

    run()
    # Update table description.
    dtu.update_table_metadata(TARGET_TABLE, description=DESCRIPTION)
