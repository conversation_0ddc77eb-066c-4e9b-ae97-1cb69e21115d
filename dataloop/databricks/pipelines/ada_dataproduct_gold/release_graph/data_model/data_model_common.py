"""Common logic between the data model script, mainly for keeping track of latest version."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from logging import Logger
from typing import List

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, concat, lit


def get_latest_versions(spark: SparkSession, source_table: str, data_model_table: str, logger: Logger) -> List[str]:
    """Return the versions from the 'source_table' not already processed into the data_model_table.

    Args:
        spark (SparkSession): SparkSession used by the calling script.
        source_table (str): Full path to the source table for getting versions.
        data_model_table (str): Target table for the data model data to process.
        logger (Logger): Logger instance to use.

    Return:
        List[str]: List of strings with versions that has not yet been processed.
    """
    source_df = spark.read.table(source_table).select("version").distinct()
    try:
        data_model_df = spark.read.table(data_model_table).select("version").distinct()
        return [row.version for row in source_df.join(data_model_df, "version", "left_anti").collect()]
    except Exception:
        logger.info(f"Failed to get new versions, default to try returning all versions for: {data_model_table}.")
        return [row.version for row in source_df.collect()]


def convert_to_iri_id(df: DataFrame) -> DataFrame:
    """Return a dataframe where id is replaced with the IRI, including version as suffic.

    ex: REQ_SYS_FOO_BAR -> urn:pace:ontology:needs:REQ_SYS_FOO_BAR_345bc13a283

    Args:
        df (DataFrame): DataFrame which should contain id and version column.

    Return:
        Dataframe: Where "id" column now contains the IRI version instead.
    """
    return (
        df.withColumn("id_iri", concat(lit("urn:pace:ontology:needs:"), df.id, lit("_"), col("version")))
        .drop("id")
        .withColumnRenamed("id_iri", "id")
    )
