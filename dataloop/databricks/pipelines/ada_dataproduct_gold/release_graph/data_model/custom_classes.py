"""Create Feature Increment, Category and Owner classes."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from typing import Tuple

from data_model_common import get_latest_versions
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, concat, explode, lit, regexp_replace, split
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging

logger = logging.getLogger(__name__)

GOLD_CATALOG = get_dbx_env_catalog("gold")

TYPE_IRI = "http://www.w3.org/1999/02/22-rdf-syntax-ns#type"
LABEL_IRI = "http://www.w3.org/2000/01/rdf-schema#label"

NEEDS_EDGES = f"{GOLD_CATALOG}.ada_release_graph.needs_edges_v2"
TABLE_EDGES = f"{GOLD_CATALOG}.ada_release_graph.custom_edges"
TABLE_PROPS = f"{GOLD_CATALOG}.ada_release_graph.custom_properties"

EDGES_DESCRIPTION = (
    "This tables contains custom edges data in the form of start, type "
    + "target, which is used to add custom edges for the Gold metamodel."
)
PROPS_DESCRIPTION = (
    "This tables contains custom node information in the form of start, type "
    + "target, which is used to add custom properties for the Gold metamodel."
)

spark = SparkSession.builder.getOrCreate()


class CustomClass:
    """Class for generating custom classes for the extended data model."""

    props: DataFrame
    edges: DataFrame

    def _get_feature_increments(self) -> Tuple[DataFrame, DataFrame]:
        """Get features that start with FEATURE_INCREMENT.

        Return:
            Tuple with two dataframes, one for the needs properties that are FeatureIncrements
            and one with the needs edges, in that order.
        """

        feat_inc_props = self.props.where(self.props.start.contains("FEATURE_INCREMENT_"))
        feat_inc_edges = self.edges.where(self.edges.start.contains("FEATURE_INCREMENT_"))

        feat_inc_type = (
            feat_inc_edges.select("start", "version")
            .distinct()
            .withColumn("target", lit("urn:pace:ontology:gold:FeatureIncrement"))
            .withColumn("type", lit("type"))
            .withColumn("mapped_type", lit(TYPE_IRI))
        )

        feat_inc_edges = feat_inc_edges.unionByName(feat_inc_type)
        return feat_inc_props, feat_inc_edges

    def _owner(self) -> Tuple[DataFrame, DataFrame]:
        """Create owner nodes from the properties table.

        Return:
            Tuple with two dataframes for the Owner nodes, one for the properties and one for the
            edges, in that order.
        """

        owners_edges = (
            self.props.select("target", "version")
            .where("type == 'owner'")
            .dropDuplicates()
            .withColumn(
                "start",
                concat(
                    lit("urn:pace:ontology:needs:"),
                    regexp_replace(self.props.target, " ", ""),
                    lit("_"),
                    col("version"),
                ),
            )
            .withColumn("type", lit("type"))
            .withColumn("target", lit("urn:pace:ontology:gold:Owner"))
            .withColumn("mapped_type", lit(TYPE_IRI))
        )

        # add label property to owner nodes
        owners_prop = (
            self.props.select("target", "version")
            .where("type == 'owner'")
            .dropDuplicates()
            .withColumn(
                "start",
                concat(
                    lit("urn:pace:ontology:gold:needs:"),
                    regexp_replace(self.props.target, " ", ""),
                    lit("_"),
                    col("version"),
                ),
            )
            .withColumn("type", lit("label"))
            .withColumn("mapped_type", lit(LABEL_IRI))
        )

        # add edges from nodes with owner property to owner node
        edges_to_owners = (
            self.props.where("type == 'owner'")
            .withColumnRenamed("target", "target_old")
            .drop("mapped_type")
            .withColumn(
                "target",
                concat(
                    lit("urn:pace:ontology:needs:"), regexp_replace("target_old", " ", ""), lit("_"), col("version")
                ),
            )
            .withColumn("mapped_type", lit("urn:pace:ontology:gold:hasOwner"))
            .drop("target_old", "mapped_type_old")
        )

        result_edges = owners_edges.unionByName(edges_to_owners)
        return owners_prop, result_edges

    def _category(self) -> Tuple[DataFrame, DataFrame]:
        """Create category classes.

        Return:
            Tuple with two dataframes for the Category nodes, one for the properties and one for
            the edges, in that order.
        """
        category = (
            self.edges.where("type == 'tags' AND target LIKE '%Category%'")
            .select("target", "version")
            .distinct()
            .dropDuplicates()
            .withColumn("start", self.edges.target)
            .withColumn("target", lit("urn:pace:ontology:gold:Category"))
            .withColumn("type", lit("type"))
            .withColumn("mapped_type", lit(TYPE_IRI))
        )

        # add label property to category nodes
        category_prop = (
            category.select("start", "version")
            .withColumn("target", regexp_replace("start", "[^0-9]", ""))
            .withColumn("type", lit("label"))
            .withColumn("mapped_type", lit(LABEL_IRI))
        )

        # update category edges
        category_edges = (
            self.edges.where("type == 'tags' AND target LIKE '%Category%'")
            .dropDuplicates()
            .drop("type", "mapped_type")
            .withColumn("type", lit("category"))
            .withColumn("mapped_type", lit("urn:pace:ontology:gold:category"))
        )

        result_edges = category.unionByName(category_edges)

        return category_prop, result_edges

    def _deployment(self) -> Tuple[DataFrame, DataFrame]:
        """Get deployment from SUT profile."""

        deployment_base = (
            self.props.where("type == 'deployment'")
            .select("target", "version")
            .distinct()
            # split string into arrays if contains comma, replace / with - for IRI, remove newlines
            .withColumn("deployment", split(col("target"), ","))
            .drop("target")
            .withColumn("label", explode(col("deployment")))
            .drop("deployment")
            .withColumn("label", regexp_replace(col("label"), "\n", ""))
            .dropDuplicates()
            .withColumn("iri", concat(lit("urn:pace:ontology:gold:needs:"), regexp_replace(col("label"), "[/:]", "_")))
        )

        deployment_edges = (
            deployment_base.select("version", "iri")
            .withColumnRenamed("iri", "start")
            .withColumn("target", lit("urn:pace:ontology:gold:Deployment"))
            .withColumn("type", lit("type"))
            .withColumn("mapped_type", lit(TYPE_IRI))
        )

        deployment_prop = (
            deployment_base.select("version", "iri", "label")
            .withColumnRenamed("iri", "start")
            .withColumnRenamed("label", "target")
            .withColumn("type", lit("label"))
            .withColumn("mapped_type", lit(LABEL_IRI))
        )

        return deployment_prop, deployment_edges

    def process_classes(self) -> None:
        """Generate custom classes.

        Combine data into two tables - one for properties and one for edges, and update tables.
        """

        feat_inc_prop, feat_inc_edges = self._get_feature_increments()
        own_prop, own_edges = self._owner()
        cat_prop, cat_edges = self._category()
        dep_prop, dep_edges = self._deployment()

        result_props = feat_inc_prop.unionByName(own_prop).unionByName(cat_prop).unionByName(dep_prop)
        result_edges = feat_inc_edges.unionByName(own_edges).unionByName(cat_edges).unionByName(dep_edges)

        CONDITION = (
            "source.start = target.start AND "
            + "source.target = target.target AND "
            + "source.version = target.version AND "
            + "source.type = target.type"
        )
        dtu.merge_or_overwrite(result_props, TABLE_PROPS, CONDITION)
        dtu.merge_or_overwrite(result_edges, TABLE_EDGES, CONDITION)

    def run(self) -> None:
        """Process the classes for the desired version."""
        versions = get_latest_versions(spark, NEEDS_EDGES, TABLE_EDGES, logger)
        if len(versions) == 0:
            logger.info("No versions found to process!")
            return

        for version in versions:
            # Populate properties and edges dataframes based on version.
            self.props = spark.read.table(f"{GOLD_CATALOG}.ada_release_graph.needs_properties_v2").where(
                f"version = '{version}'"
            )
            self.edges = spark.read.table(f"{GOLD_CATALOG}.ada_release_graph.needs_edges_v2").where(
                f"version = '{version}'"
            )
            # Run the processing.
            self.process_classes()


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Data model custom classes transform.")
    parser.add_argument("-v", "--version", help="version (commit sha)")
    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(GOLD_CATALOG, "ada_release_graph", False),
        "data_model/custom_classes",
        run_id=args.run_id,
        enabled_loggers=["base"],
    )

    cc = CustomClass()
    cc.run()
    dtu.update_table_metadata(TABLE_EDGES, description=EDGES_DESCRIPTION)
    dtu.update_table_metadata(TABLE_PROPS, description=PROPS_DESCRIPTION)
