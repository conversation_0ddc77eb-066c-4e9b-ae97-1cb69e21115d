"""Create nodes and links tables from Needs data based on the provided ontology."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from pathlib import Path
from typing import Any, Dict, List

from data_model_common import get_latest_versions
from delta import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, explode, lit, size
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging
from rddlib.needs_utils import get_latest_pace_ontology
from rdflib import OWL, RDF, RDFS, Graph, URIRef, query
from rdflib.namespace import SH

# Fallback if __file__ is not defined when running in a bundle.
if "__file__" in globals():
    DIR_PATH = Path(__file__).parent
else:
    DIR_PATH = Path.cwd()  # Current working directory as fallback

SILVER_CATALOG = get_dbx_env_catalog("silver")

SCHEMA = "needs_data_model"

SILVER_NEEDS_TABLE = f"{SILVER_CATALOG}.needs.pace_needs"

logger = logging.getLogger(__name__)
spark = SparkSession.builder.getOrCreate()


def _get_query(predicate: str, g: Graph) -> query.Result:
    return g.query(
        f"""
    SELECT ?mapped_type ?needs_type
    WHERE {{
       ?mapped_type {predicate} ?needs_type .
    }}
    """
    )


def _get_dict_from_query(query: query.Result) -> dict[str, Any]:
    return {str(mapped_type): str(needs_type) for mapped_type, needs_type in query}


def get_mapping_dicts() -> tuple[dict[str, Any], dict[str, Any], dict[str, Any]]:
    """Generate three mapping dictionaries based on the latest ontology.

    This contains mappings for type, link and option value

    return: a tuple of three mapping dictionaries:
      key: Original needs json keyword, eg. 'activity'
      value: The corresponding value in the ontology, eg. 'urn:pace:ontology:Activity'
      In the order of type, link and option mappings.
    """
    # Get schema from artifactory and parse it into a RDFlib Graph object
    g = Graph()
    g.parse(get_latest_pace_ontology().open())

    # Generate the individual mapping dictionaries for each type of mapping
    type_mappings = _get_dict_from_query(_get_query("pace:needsType", g))
    link_mappings = _get_dict_from_query(_get_query("pace:needsLink", g))
    option_mappings = _get_dict_from_query(_get_query("pace:needsOption", g))
    return (type_mappings, link_mappings, option_mappings)


type_mappings, link_mappings, option_mappings = get_mapping_dicts()


def get_source_name(prop: str) -> str:
    """Return the source class name of a property.

    ex: urn:pace:ontology:Feature-references -> urn:pace:ontology:Feature

    Args:
      prop: Property string

    Return: String with the source name of the property.
    """
    return prop.split("-")[0]


def get_mapped_name(prop: str) -> str:
    """Extract source type and return mapped value.

    ex: urn:pace:ontology:StakeholderRequirement-cbStatsCariad -> req-stk

    Args:
      prop: Property string

    Return: Property source name mapped to needs format, example: req-stk
    """
    return type_mappings.get(get_source_name(prop), prop)


missing_mappings: List[str] = []


def get_mapped_prop(prop: str) -> str | None:
    """Return the mapped version of provided property.

    ex: "urn:pace:ontology:belongsTo" -> "belongs"

    Args:
        prop (str): The property as defined in the ontology (ttl)

    Returns:
        str: The property named from the needs definition
    """
    try:
        return link_mappings[prop]
    except KeyError:
        try:
            return option_mappings[prop]
        except KeyError:
            missing_mappings.append(prop)
            return None


# This dict stores the class as keys and their properties as a value in a list
prop_dict: Dict[str, List[str]] = {}


def _add_from_superclass(mapped_class_name: str, super_class: URIRef) -> None:
    """Appends the properties from super_class to the mappe_class_name in type_dict."""
    props_from_super = prop_dict.get(get_mapped_name(str(super_class)), None)
    if props_from_super:
        for p in props_from_super:
            if p not in prop_dict[mapped_class_name]:
                prop_dict[mapped_class_name].append(p)


def process_class(class_name: URIRef, ontology_graph: Graph) -> None:
    """Process a class and its super classes.

    the result is populated in prop_dict, which will store the Class as key
    and properties as values in a list. To get all the relevant properties,
    for a class, it also has to contain the values from the class which it is
    a sub class of (the 'superclass').

    To determine this superclasses are processed recursively.

    Class name will be mapped to the needs element name ex:
    'urn:pace:ontology:SystemRequirement' -> 'sys-req', but there is generally
    not a mapping for super classes so they will keep their IRI name instead.
    If no mapping is found for a property that property is ignored.

    If a property is a subProperty of a super property, the super property is
    used instead. This is because subProperties are not mapped to the needs data
    """
    # get super class of the class.
    super_class = ontology_graph.value(class_name, RDFS.subClassOf)
    if super_class:  # recursively process super classes
        process_class(super_class, ontology_graph)

    mapped_class_name = get_mapped_name(str(class_name))
    # skip if class is already in the prop_dict
    if mapped_class_name in prop_dict:
        return
    # init empty list in type_dict for the class
    prop_dict[mapped_class_name] = []
    # iterate over all properties in graph
    # add properties from optional super class of the class, if it has any
    if super_class:
        _add_from_superclass(mapped_class_name, super_class)

    for prop, _, _ in ontology_graph.triples((None, RDF.type, SH.PropertyShape)):
        # process the prop if it is a property of the class
        if get_source_name(str(prop)) == str(class_name):
            # if the property is a subProperty use the super property instead
            super_prop = ontology_graph.value(ontology_graph.value(prop, SH.path), RDFS.subPropertyOf)
            if super_prop:
                prop_value = get_mapped_prop(str(super_prop))
            else:
                prop_value = get_mapped_prop(str(ontology_graph.value(prop, SH.path)))
            if not prop_value:
                return
            if prop_value not in prop_dict[mapped_class_name]:
                prop_dict[mapped_class_name].append(prop_value)


link_dict: Dict[str, None] = {}


def _process_types() -> None:
    """Generate the tables for the needs elements. Properties are select based on the data in prop_dict."""
    # process every type (key) in type_dict
    for k in [key for key in prop_dict.keys() if ":" not in key]:
        # process edges for each type
        for prop in prop_dict[k]:
            link_dict[prop] = None  # add prop to link_dict keys

        # get information about node itself
        node_table_name = f"{SILVER_CATALOG}.{SCHEMA}.`{k}`"
        versions = get_latest_versions(spark, SILVER_NEEDS_TABLE, node_table_name, logger)
        if len(versions) == 0:
            continue
        needs_df = spark.read.table(SILVER_NEEDS_TABLE).where(col("version").isin(versions))

        # only get columns that exists in the needs data
        cols = [col for col in prop_dict[k] if col in needs_df.columns]
        missing_cols = [x for x in prop_dict[k] if x not in cols]
        if len(missing_cols) > 0:
            logger.info(f"Property in ontology but not in needs data: {missing_cols}")

        type_df = needs_df.where(f"type == '{k}'").select("id", "type", "version", *cols).dropDuplicates()

        # Write node table
        if not dtu.table_exists(node_table_name):
            type_df.write.saveAsTable(node_table_name)
        else:
            dtu.merge_or_overwrite(
                type_df, node_table_name, "source.id == target.id AND source.version == target.version"
            )


def _process_links() -> None:
    """Generate the tables for links between needs elements, based on the data in link_dict."""
    for link in link_dict.keys():
        # explode arrays with ID's, ignore if they are empty
        link_table_name = f"{SILVER_CATALOG}.{SCHEMA}.`{link}`"
        versions = get_latest_versions(spark, SILVER_NEEDS_TABLE, link_table_name, logger)
        if len(versions) == 0:
            continue

        needs_df = spark.read.table(SILVER_NEEDS_TABLE).where(col("version").isin(versions))
        # only get columns that exists in the needs data
        if link not in needs_df.columns:
            logger.info(f"Needs link found in ontology but not in needs data: {link}")
            continue
        if dict(needs_df.dtypes)[link] == "array<string>":
            linked_df = needs_df.select("id", explode(col(link)).alias("target"), "version").where(size(link) > 0)
        else:
            linked_df = needs_df.select("id", col(link).alias("target"), "version")
        linked_df = linked_df.withColumnRenamed("id", "source").withColumn("type", lit(link))

        if not dtu.table_exists(link_table_name):
            linked_df.write.saveAsTable(link_table_name)
        else:
            # Use custom merge logic instead of DTU because source are target are normally used for
            # merge condition but here they are also used as column names.
            condition = "x.type == y.type AND x.target == y.target AND x.source == y.source AND x.version == y.version"
            existing_df = DeltaTable.forName(spark, link_table_name)
            (
                existing_df.alias("x")
                .merge(linked_df.alias("y"), condition)
                .whenMatchedUpdateAll()
                .whenNotMatchedInsertAll()
                .execute()
            )


def generate_tables() -> None:
    """Generate tables for classes and properties based on files in pace_schemas directory."""
    ontology_graph = Graph()
    ontologies = Path(str(DIR_PATH) + "/pace_schemas")
    # generate base ontology from files in pace_schemas
    for ontology in ontologies.iterdir():
        ontology_graph.parse(source=ontology, format="turtle")

    # process each class in the graph
    for class_name, _, _ in ontology_graph.triples((None, RDF.type, OWL.Class)):
        process_class(class_name, ontology_graph)

    _process_types()
    _process_links()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Split silver Needs data for data model.")
    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()

    setup_databricks_logging(
        FullSchemaName(SILVER_CATALOG, SCHEMA, False),
        "data_model/needs_data_model",
        run_id=args.run_id,
        enabled_loggers=["base"],
    )

    generate_tables()
    if len(missing_mappings) > 0:
        missing_str = ", ".join([str(s) for s in missing_mappings])
        logger.info(f"Missing mappings: {missing_str}")
