"""Generate test instance ID data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging

from data_model_common import convert_to_iri_id
from pyspark.sql import DataFrame, SparkSession, Window
from pyspark.sql.functions import coalesce, col, concat, lit, regexp_replace, row_number
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging

logger = logging.getLogger(__name__)

GOLD_CATALOG = get_dbx_env_catalog("gold")
SILVER_CATALOG = get_dbx_env_catalog("silver")

NEEDS_EDGES = f"{GOLD_CATALOG}.ada_release_graph.needs_edges_v2"
ADX_TEST_REPORTS = f"{SILVER_CATALOG}.ada_release_graph.adx_test_report"
TARGET_TABLE = f"{GOLD_CATALOG}.ada_release_graph.test_instances"

DESCRIPTION = (
    "Test instance table contains information about 'test-instances'. A test instance"
    + " represents an intended run of a test. This is based on a combination of test "
    + "case, test platform, scenario and deployment, resulting in multiple test "
    + "instances for the same test case. This data is generated by combining sphinx "
    + "needs data and the test report data to determine if a test instance has been "
    + "tested or not."
)

spark = SparkSession.builder.getOrCreate()


def process_test_reports(version: str) -> DataFrame:
    """Get the data required from TestReports to create the Test Instance in later steps.

    This includes information about TestCaseId, Scenario, TestPlatform, Requirement and Deployment.

    Args:
        version (str): The desired version for which to process data.

    Return:
        DataFrame: This dataframe will contain information for creating a Test Instance.
    """
    df = spark.read.table(f"{SILVER_CATALOG}.ada_release_graph.adx_test_report").where(f"sut_commit_id == '{version}'")
    # Get relevant data from ADX, including test_platform, deployment, test_case_id, report_id,
    # scenario_id, sut_commit_id reconstruct deployment to deployment IRI
    adx_df = (
        df.select("test_platform", "report_id", "deployment", "test_case_id", "sut_commit_id", "scenario_id")
        .distinct()
        .withColumnRenamed("sut_commit_id", "version")
        .withColumn(
            "deployment_iri",
            concat(lit("urn:pace:ontology:needs:"), regexp_replace(col("deployment"), "[/:]", "_")),
        )
        .withColumn("test_platform", concat(lit("urn:pace:ontology:TestPlatform-"), col("test_platform")))
        .withColumn(
            "test_case_id", concat(lit("urn:pace:ontology:needs:"), col("test_case_id"), lit("_"), col("version"))
        )
        .withColumn(
            "scenario_id", concat(lit("urn:pace:ontology:needs:"), col("scenario_id"), lit("_"), col("version"))
        )
        .dropDuplicates()
    )

    # Add Test Instance ID (use _X_ suffix for ADX reports)
    window_spec = Window.partitionBy("version").orderBy("version")
    adx_df = adx_df.withColumn("ti_id_adx", concat(lit("TI_X_"), row_number().over(window_spec)))

    return adx_df


def process_needs(version: str) -> DataFrame:
    """Get the data required from Sphinx Needs to create the Test Instance in later steps.

    This includes information about TestCaseId, Scenario, TestPlatform, Requirement and Deployment.
    Some of these have to be extracted from the linked SutProfile, while others are edges and others
    properties.


    Args:
        version (str): The desired version for which to process data.

    Return:
        DataFrame: This dataframe will contain information for creating a Test Instance.
    """
    nodes_df = (
        spark.read.table(f"{SILVER_CATALOG}.needs.pace_needs")
        .where(
            """
            type = 'kpi-value' OR
            type = 'test-swint' OR
            type = 'test-sw' OR
            type = 'test-sysint' OR
            type = 'test-sys' OR
            type = 'sut_profile'
        """
        )
        .select("id", "deployment", "version")
        .where(f"version ='{version}'")
    ).alias("nodes_df")

    nodes_df = convert_to_iri_id(nodes_df)

    edges_raw = spark.read.table(NEEDS_EDGES).where(f"version = '{version}'").alias("edges_raw")

    # get sut_profile information from edges
    edges_sut = (
        edges_raw.where("type = 'sut_profile'").withColumnRenamed("target", "sut_profile").drop("type", "mapped_type")
    )

    # get test_platform information from edges and join into sut table
    edges_platform = (
        edges_raw.where("type = 'test_platform'")
        .withColumnRenamed("target", "test_platform")
        .drop("type", "mapped_type")
    )

    edges_df = (
        edges_sut.alias("edges_sut")
        .join(
            edges_platform.alias("edges_platform"),
            col("edges_sut.sut_profile") == col("edges_platform.start"),
        )
        .drop(col("edges_platform.start"), col("edges_platform.version"))
    )

    edges_df = (
        edges_df.alias("edges_df")
        .join(nodes_df.alias("nodes_df"), col("edges_df.sut_profile") == col("nodes_df.id"))
        .drop("id")
        .withColumnRenamed("start", "test_case_id")
        .drop("sut_profile", nodes_df.version)
        .withColumn(
            "deployment_iri",
            concat(lit("urn:pace:ontology:needs:"), regexp_replace(col("deployment"), "[/:]", "_")),
        )
    )

    # get scenario_id from edges and join into table
    edges_scenario = (
        edges_raw.where("type = 'scenario_id'").withColumnRenamed("target", "scenario_id").drop("type", "mapped_type")
    ).alias("edges_scenario")

    edges_df = (
        edges_df.alias("edges_df")
        .join(
            edges_scenario.alias("edges_scenario"),
            (
                (col("edges_df.test_case_id") == col("edges_scenario.start"))
                & ((col("edges_df.version") == col("edges_scenario.version")))
            ),
        )
        .drop(col("edges_scenario.start"), col("edges_scenario.version"))
        .dropDuplicates()
    )

    # Add the corresponding requirement ID for the test case id
    edges_verifies = (
        edges_raw.where(edges_raw.type == "verifies").select("start", "target", "version").alias("edges_verifies")
    )

    edges_df = (
        edges_df.join(
            edges_verifies,
            (edges_df.test_case_id == edges_verifies.start),
        )
        .drop(col("edges_verifies.version"), col("edges_verifies.start"))
        .dropDuplicates()
        .withColumnRenamed("target", "req_id")
    )
    # Add TestInstance IDs
    window_spec = Window.partitionBy("version").orderBy("test_case_id")
    edges_df = edges_df.withColumn("ti_id_needs", concat(lit("TI_"), row_number().over(window_spec)))

    return edges_df


def run() -> None:
    """Determine the latest versions to process and execute."""
    # need to combine versions from needs_edges and adx_test_report and compare with target_table
    adx_df = spark.read.table(ADX_TEST_REPORTS).select("sut_commit_id").distinct()
    needs_df = spark.read.table(NEEDS_EDGES).select("version").distinct()
    previous_versions_df = adx_df.join(needs_df, adx_df.sut_commit_id == needs_df.version, "inner").select("version")

    try:
        test_instances_df = spark.read.table(TARGET_TABLE).select("version").distinct()
        versions = [
            row.version for row in previous_versions_df.join(test_instances_df, "version", "left_anti").collect()
        ]
    except Exception:
        versions = [row.version for row in previous_versions_df.collect()]
        logger.info(f"No versions found in {TARGET_TABLE}, processing all versions: {versions}")

    if len(versions) == 0:
        logger.info("No versions found to process!")
        return

    for version in versions:
        logger.info(f"Processing version: {version}")
        generate_test_instances(version)


def generate_test_instances(version: str) -> None:
    """Transform the data needed from ADX and Needs tables.

    Join them together, add Test Instance ID and write to gold table.

    Args:
        version (str): version for which to generate the test instances.
    """
    adx_df = process_test_reports(version).alias("adx_df")
    edges_df = process_needs(version)

    # combine data from needs and adx
    total_df = (
        edges_df.join(
            adx_df,
            (edges_df.test_case_id == adx_df.test_case_id)
            & (edges_df.version == adx_df.version)
            & (edges_df.test_platform == adx_df.test_platform)
            & (edges_df.scenario_id == adx_df.scenario_id)
            & (edges_df.deployment_iri == adx_df.deployment_iri),
            "left",
        )
        .drop(
            adx_df.test_case_id,
            adx_df.version,
            adx_df.test_platform,
            adx_df.deployment,
            adx_df.scenario_id,
            adx_df.deployment_iri,
        )
        .dropDuplicates()
    )

    # Generate test instance ids
    total_df = total_df.withColumn("ti_id", coalesce(total_df.ti_id_needs, total_df.ti_id_adx)).drop(
        total_df.ti_id_needs, total_df.ti_id_adx
    )

    # IMPORTANT: Only append new versions to the table if they don't already exist in it!
    # Don't do any fancy merge operation! This is because the generation of TI-ids is not
    # deterministic and re-ingesting the same version will end up with duplicated rows of the
    # same test instance but with different TI-ids!
    if dtu.table_exists(TARGET_TABLE):
        table_df = spark.read.table(TARGET_TABLE)
        # don't update table if version already exists
        if table_df.where(f"version = '{version}'").isEmpty():
            dtu.append(total_df, TARGET_TABLE)
        else:
            logger.info(f"Skip since version: {version} already exists in {TARGET_TABLE}")
    else:
        logger.info(f"Creating new table: {TARGET_TABLE}")
        dtu.overwrite(total_df, TARGET_TABLE, overwrite_schema=True)


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Data model test instances transform.")
    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(GOLD_CATALOG, "ada_release_graph", False),
        "data_model/test_instances",
        run_id=args.run_id,
        enabled_loggers=["base"],
    )

    run()
    dtu.update_table_metadata(TARGET_TABLE, description=DESCRIPTION)
