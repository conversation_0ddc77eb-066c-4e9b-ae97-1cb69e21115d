"""Transform basic system test reports from silver to gold using ingest commits."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from contextlib import nullcontext
from typing import ContextManager

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, concat_ws, lit, to_timestamp, when
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging
from rddlib.stream_utils import checkpoint_backup, stream_updates_to_dataframe

logger = logging.getLogger(__name__)

# Constants
SILVER_CATALOG = get_dbx_env_catalog("silver")
GOLD_CATALOG = get_dbx_env_catalog("gold")
SILVER_SCHEMA = "pace_metrics"
GOLD_SCHEMA = "ada_release_graph"
SILVER_TABLE_BASIC_EVALUATION_REPORT = "basic_evaluation_report"
SILVER_TABLE_BASIC_TEST_REPORT = "basic_system_test_report"
GOLD_TABLE_TEST_REPORT = "test_report"
INGEST_COMMITS_TABLE = "ingest_commits"

# Define column mappings for gold table
COLUMN_MAPPINGS = {
    "st.build_config": None,
    "st.commit": "version",
    "st.branch": None,
    "st.datafile": None,
    "st.deployment": None,
    "st.execution_job": None,
    "st.execution_platform": None,
    "st.execution_time_s": None,
    "st.execution_url": None,
    "st.execution_workflow": None,
    "st.index": None,
    "st.report_creation_time": None,
    "st.scenario_id": None,
    "st.status": "system_test_status",
    "st.sut_profile": None,
    "st.test_case": None,
    "st.test_platform": None,
    "st.trace_id": None,
    "st.workflow": None,
    "ev.eval_runner": None,
    "ev.execution_time_s": "eval_execution_time_s",
    "ev.index": "eval_index",
    "ev.status": None,
    "ev.system_test_index": None,
    "ev.test_id": "test_case_id",
    "ev.name": "test_name",
}

DESCRIPTION_GOLD_TABLE = "Contains system tests and their evaluations for a version." " Managed by the RDD Team."


# Spark session is provided by Databricks
spark: SparkSession = SparkSession.builder.getOrCreate()


def _get_checkpoint_path(catalog: str, schema: str, table: str) -> str:
    """Construct the checkpoint path for the given catalog, schema, and table."""
    return f"/Volumes/{catalog}/{schema}/checkpoint_locations/{table}"


def _read_stream_commits_table(full_table_name: str) -> DataFrame:
    """Loads data from ingest commits gold table and returns a streaming DataFrame."""
    return spark.readStream.format("delta").table(full_table_name).select("commit_sha")


def _checkpoint_exists(path: str) -> bool:
    """Check if the checkpoint path exists in DBFS."""
    from pyspark.dbutils import DBUtils

    dbutils = DBUtils(spark)
    try:
        dbutils.fs.ls(path)
        return True
    except Exception:
        return False


def _read_stream_data(full_table_name: str, checkpoint_path: str) -> DataFrame | None:
    """Reads streaming commit data and converts to a batch DataFrame."""
    logger.info(f"Reading stream from ingest commits table: {full_table_name}")
    version_df = _read_stream_commits_table(full_table_name)
    return stream_updates_to_dataframe(version_df, checkpoint_path)


def _extract_versions(df: DataFrame) -> list[str]:
    """Extracts commit versions from DataFrame."""
    versions = [row["commit_sha"] for row in df.select("commit_sha").collect()]
    logger.info(f"Processing ingest commits: {versions}")
    return versions


def _filter_system_tests(versions: list[str]) -> DataFrame:
    """Filters the system test report based on commit versions."""
    basic_system_test_report_table_full = f"{SILVER_CATALOG}.{SILVER_SCHEMA}.{SILVER_TABLE_BASIC_TEST_REPORT}"
    logger.info(f"Reading silver table: {basic_system_test_report_table_full}")

    return (
        spark.read.format("delta")
        .table(basic_system_test_report_table_full)
        .filter(col("commit").isin(versions))
        .filter(col("status").isNotNull())
    )


def _extract_trace_ids(df: DataFrame) -> list[str]:
    """Extracts unique trace IDs from the system test report."""
    trace_ids = [
        row["trace_id"]
        for row in df.select("trace_id")
        .filter(col("trace_id") != "")  # filter out empty strings in trace_id
        .distinct()
        .collect()
    ]
    logger.info(f"Extracted trace_ids: {trace_ids}")
    return trace_ids


def _filter_evaluations(trace_ids: list[str]) -> DataFrame:
    """Filters the evaluation report based on trace IDs."""
    basic_evaluation_report_table_full = f"{SILVER_CATALOG}.{SILVER_SCHEMA}.{SILVER_TABLE_BASIC_EVALUATION_REPORT}"
    logger.info(f"Reading silver table: {basic_evaluation_report_table_full}")

    return spark.read.format("delta").table(basic_evaluation_report_table_full).filter(col("trace_id").isin(trace_ids))


def _transform_data(basic_test_df: DataFrame, eval_df: DataFrame) -> DataFrame:
    """Transforms the system test and evaluation reports into the final format for writing."""
    selected_columns = [col(orig).alias(new) if new else col(orig) for orig, new in COLUMN_MAPPINGS.items()]

    # Add a unique report_id for gold table mappings
    selected_columns.append(
        concat_ws("_", col("ev.test_id"), col("ev.system_test_index"), col("ev.index"), col("st.commit")).alias(
            "report_id"
        )
    )

    # Add test_passed column: True if status is "success", otherwise False
    selected_columns.append(when(col("ev.status") == "success", lit(True)).otherwise(lit(False)).alias("test_passed"))

    return (
        basic_test_df.alias("st")
        .join(
            eval_df.alias("ev"),
            (col("st.trace_id") == col("ev.trace_id")) & (col("st.index") == col("ev.system_test_index")),
            "inner",
        )
        .select(*selected_columns)
        .dropDuplicates()
        .withColumn("report_creation_time", to_timestamp(col("report_creation_time")))
    )


def _write_to_gold_table(df: DataFrame) -> None:
    """Writes the transformed data to the gold table."""
    test_report_gold_table_full = f"{GOLD_CATALOG}.{GOLD_SCHEMA}.{GOLD_TABLE_TEST_REPORT}"
    MERGE_CONDITION = "source.commit = target.commit AND source.trace_id = target.trace_id"

    dtu.merge_or_overwrite(df, test_report_gold_table_full, MERGE_CONDITION)
    dtu.update_table_metadata(test_report_gold_table_full, DESCRIPTION_GOLD_TABLE)


def run_stream() -> None:
    """Run the transformation process using streaming."""
    ingest_commits_table_full = f"{GOLD_CATALOG}.{GOLD_SCHEMA}.{INGEST_COMMITS_TABLE}"
    commits_checkpoint_path = _get_checkpoint_path(
        GOLD_CATALOG, GOLD_SCHEMA, f"test_reports_transform_gold_pace_{INGEST_COMMITS_TABLE}"
    )

    # Check if checkpoint exists
    if _checkpoint_exists(commits_checkpoint_path):
        backup_checkpoint: ContextManager[None] = checkpoint_backup(commits_checkpoint_path)
    else:
        logger.info(f"Checkpoint path does not exist: {commits_checkpoint_path}.")
        backup_checkpoint = nullcontext()

    with backup_checkpoint:
        version_batch_df = _read_stream_data(ingest_commits_table_full, commits_checkpoint_path)
        if version_batch_df is None:
            logger.info("No new versions found. Exiting...")
            return

        versions = _extract_versions(version_batch_df)
        basic_system_test_report_df = _filter_system_tests(versions)
        trace_ids = _extract_trace_ids(basic_system_test_report_df)

        if not trace_ids:
            logger.info("No trace_ids found. Exiting...")
            return

        basic_evaluation_report_df = _filter_evaluations(trace_ids)
        final_df = _transform_data(basic_system_test_report_df, basic_evaluation_report_df)

        _write_to_gold_table(final_df)


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser()
    parser.add_argument("--run_id", required=False, default="default_run")
    args = parser.parse_args()
    setup_databricks_logging(
        FullSchemaName(GOLD_CATALOG, GOLD_SCHEMA, False), "basic_system_test_reports/gold/pace", run_id=args.run_id
    )
    run_stream()
