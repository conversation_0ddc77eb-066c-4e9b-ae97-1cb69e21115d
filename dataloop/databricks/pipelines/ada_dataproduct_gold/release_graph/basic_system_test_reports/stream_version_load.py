"""Load basic system test reports to Stardog."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql import DataFrame
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from common.load import StardogStreamVersionLoader  # noqa: E402


class BasicSystemTestReportStreamVersionLoader(StardogStreamVersionLoader):
    """Stream version loader class for basic system test reports."""

    def _filter_by_versions(self, source_df: DataFrame, version_df: DataFrame) -> DataFrame:
        return version_df.join(source_df, source_df["version"] == version_df["commit_sha"], "inner")


if __name__ == "__main__":
    BasicSystemTestReportStreamVersionLoader.cli_entrypoint()
