PREFIX : <urn:pace:ontology:adx:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>


MAPPING <urn:adx_report>
FROM SQL {
  SELECT *
  FROM {{ table }}
}
TO {
  ?test_report a :Report ;
    :branch ?branch ;
    :buildConfig ?build_config ;
    :dataFile ?datafile ;
    :evalRunner ?eval_runner ;
    :executionPlatform ?execution_platform ;
    :executionUrl ?execution_url ;
    :executionWorkflow ?execution_workflow ;
    :hasDeployment ?deployment ;
    :hasEvalIndex ?eval_index ;
    :hasStatus ?status ;
    :hasSystemTestIndex ?system_test_index ;
    :hasSystemTestStatus ?system_test_status ;
    :hasTestCase ?needs_id ;
    :reportCreationTime ?date_creation_time ;
    :runDurationEval ?d_eval_execution_time_s ;
    :runDurationSystemTest ?d_execution_time_s ;
    :testName ?test_name ;
    :testResultsPassed ?b_test_passed ;
    :traceId ?trace_id ;
    :workflow ?workflow ;
    pace:hasScenario ?scenario_iri ;
    pace:hasSutProfile ?sut_profile_iri ;
    pace:runsOnTestPlatform ?test_platform_iri .

  ?version_iri a pace:ArtifactVersion ;
    pace:validFor ?test_report .
}

WHERE {
  BIND (template("urn:pace:ontology:adx:{report_id}") AS ?test_report)
  BIND (template("urn:pace:ontology:{version}") AS ?version_iri)
  BIND (xsd:boolean(?test_passed) AS ?b_test_passed)
  BIND (xsd:double(?eval_execution_time_s) AS ?d_eval_execution_time_s)
  BIND (xsd:double(?execution_time_s) AS ?d_execution_time_s)
  BIND (xsd:dateTime(?report_creation_time) AS ?date_creation_time)
  BIND (template("urn:pace:ontology:needs:{test_case_id}_{version}") AS ?needs_id)
  BIND (template("urn:pace:ontology:TestPlatform-{test_platform}") AS ?test_platform_iri)
  BIND (template("urn:pace:ontology:needs:{sut_profile}_{version}") AS ?sut_profile_iri)
  BIND (template("urn:pace:ontology:needs:{scenario_id}_{version}") AS ?scenario_iri)
}
