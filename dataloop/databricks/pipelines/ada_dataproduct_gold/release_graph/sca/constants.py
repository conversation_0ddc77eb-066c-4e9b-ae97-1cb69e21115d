"""Constants for the SCA."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql import DataFrame
from rddlib.dbx_utils import get_dbx_env_catalog

SQL_SCA_LEVEL_FILTER = f"""
{get_dbx_env_catalog('gold')}.ada_release_graph.array_sum(
    transform(
        sca_issues,
        x -> array_size(filter(
            x.findings,
            f -> from_json(
                f,
                'STRUCT<filename: STRING, level: BIGINT, line_number: BIGINT, rule_id: STRING, suppressed: BOOLEAN>'
                ).level = {{level:d}}
            ))
    )
)""".replace(
    "\n", ""
)


def select_for_mapping(source_df: DataFrame) -> DataFrame:
    """Selects the columns required by the Stardog mapping for SCA data."""
    return source_df.selectExpr(
        "package",
        "revision",
        "last_changed_date",
        "labels",
        "array_size(sca_setup_problems) AS sca_setup_issues_count",
        f"{SQL_SCA_LEVEL_FILTER.format(level=1)} AS sca_issues_count_by_level_1",
        f"{SQL_SCA_LEVEL_FILTER.format(level=2)} AS sca_issues_count_by_level_2",
        f"{SQL_SCA_LEVEL_FILTER.format(level=3)} AS sca_issues_count_by_level_3",
        f"{SQL_SCA_LEVEL_FILTER.format(level=4)} AS sca_issues_count_by_level_4",
        f"{SQL_SCA_LEVEL_FILTER.format(level=5)} AS sca_issues_count_by_level_5",
        "coverage_doxygen",
        "codeowners",
        "deployments",
    )
