"""Load SCA data to Stardog."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
from typing import Any

from pyspark.sql import DataFrame
from pyspark.sql.functions import col
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from common.load import StardogBatchLoader  # noqa: E402
from sca.constants import select_for_mapping


class ScaLoader(StardogBatchLoader):
    """Loader class for SCA data."""

    def _filter_source_table(  # type: ignore[override]
        self, source_df: DataFrame, task_output: str | None, version: str | None, **kwargs: Any
    ) -> DataFrame:
        if task_output is not None:
            from databricks.sdk import WorkspaceClient

            client = WorkspaceClient()
            task_version: str = client.dbutils.jobs.taskValues.get(taskKey=task_output, key="version")  # type: ignore[func-returns-value] # noqa: E501
            source_df = source_df.where(col("revision") == task_version)
        elif version is not None:
            source_df = source_df.where(col("revision") == version)
        else:
            raise Exception("Exactly one valid argument must be provided")

        return select_for_mapping(source_df)

    @classmethod
    def _cli_construct_arg_parser(cls) -> argparse.ArgumentParser:
        """Parse command line arguments of a Stardog load script."""
        parser = super()._cli_construct_arg_parser()

        mode_group = parser.add_mutually_exclusive_group()
        mode_group.add_argument(
            "--task_output",
            type=str,
            action="store",
            default=None,
            help="Get version from task value. Specify the task name.",
        )
        mode_group.add_argument(
            "--version",
            type=str,
            action="store",
            default=None,
            help="The Github commit SHA of the version to load the test reports for.",
        )

        return parser


if __name__ == "__main__":
    ScaLoader.cli_entrypoint()
