"""Load SCA data to Stardog."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any

from pyspark.sql import DataFrame
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from common.load import StardogStreamVersionLoader  # noqa: E402
from sca.constants import select_for_mapping


class ScaStreamVersionLoader(StardogStreamVersionLoader):
    """Stream version loader class for SCA data."""

    def _filter_by_versions(self, source_df: DataFrame, version_df: DataFrame) -> DataFrame:
        return version_df.join(source_df, source_df["revision"] == version_df["commit_sha"], "inner")

    def _filter_source_table(self, source_df: DataFrame, **kwargs: Any) -> DataFrame:
        return select_for_mapping(source_df)


if __name__ == "__main__":
    ScaStreamVersionLoader.cli_entrypoint()
