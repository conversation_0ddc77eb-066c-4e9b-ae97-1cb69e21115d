@prefix : <urn:pace:ontology:sca:> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix pace: <urn:pace:ontology:> .
@base <urn:pace:ontology:sca:> .


<urn:pace:ontology:sca:> rdf:type owl:Ontology ;
                                                  owl:imports <urn:pace:ontology:> ;
                                                  rdfs:comment "Ontology to define the static code analysis metamodel for the Alliance project." ;
                                                  rdfs:label "Static code analysis metamodel v0.1.0" ;
                                                  owl:versionInfo "0.1.0" .


#################################################################
#    Data properties
#################################################################

###  urn:pace:ontology:sca:hasRevision
:hasRevision rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .


###  urn:pace:ontology:sca:lastChanged
:lastChanged rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:dateTime .

###  urn:pace:ontology:sca:hasLabels
:hasLabels rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:sca:hasName
:hasName rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:sca:hasScaSetupIssuesCount
:hasScaSetupIssuesCount rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:sca:hasScaIssuesCountByLevel1
:hasScaIssuesCountByLevel1 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:sca:hasScaIssuesCountByLevel2
:hasScaIssuesCountByLevel2 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:sca:hasScaIssuesCountByLevel3
:hasScaIssuesCountByLevel3 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:sca:hasScaIssuesCountByLevel4
:hasScaIssuesCountByLevel4 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:sca:hasScaIssuesCountByLevel5
:hasScaIssuesCountByLevel5 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:sca:hasDoxygenCoverage
:hasDoxygenCoverage rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:double .

###  urn:pace:ontology:sca:hasCodeOwners
:hasCodeOwners rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:sca:hasDeployments
:hasDeployments rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:sca:ScaComponent
:ScaComponent rdf:type owl:Class ;
              rdfs:subClassOf pace:Artifact .

###  Generated by the OWL API (version 4.5.25.2023-02-15T19:15:49Z) https://github.com/owlcs/owlapi
