PREFIX : <urn:pace:ontology:sca:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:sca>
FROM SQL {
  SELECT *
  FROM {{ table }}
}
TO {
  ?sca_component a :ScaComponent ;
    rdfs:label ?package ;
    :hasRevision ?revision ;
    :lastChanged ?date_last_changed ;
    :hasLabels ?labels ;
    :hasName ?package ;
    :hasScaSetupIssuesCount ?int_sca_setup_issues_count ;
    :hasScaIssuesCountByLevel1 ?int_sca_issues_count_by_level_1 ;
    :hasScaIssuesCountByLevel2 ?int_sca_issues_count_by_level_2 ;
    :hasScaIssuesCountByLevel3 ?int_sca_issues_count_by_level_3 ;
    :hasScaIssuesCountByLevel4 ?int_sca_issues_count_by_level_4 ;
    :hasScaIssuesCountByLevel5 ?int_sca_issues_count_by_level_5 ;
    :hasDoxygenCoverage ?d_coverage_doxygen ;
    :hasCodeOwners ?codeowners ;
    :hasDeployments ?deployments .

  ?version_iri a pace:ArtifactVersion ;
    pace:validFor ?sca_component .
}

WHERE {
  BIND (template("urn:pace:ontology:sca:{package}_{revision}") AS ?sca_component)
  BIND (template("urn:pace:ontology:{revision}") AS ?version_iri)
  BIND (xsd:dateTime(?last_changed_date) AS ?date_last_changed)
  BIND (xsd:integer(?sca_setup_issues_count) AS ?int_sca_setup_issues_count)
  BIND (xsd:integer(?sca_issues_count_by_level_1) AS ?int_sca_issues_count_by_level_1)
  BIND (xsd:integer(?sca_issues_count_by_level_2) AS ?int_sca_issues_count_by_level_2)
  BIND (xsd:integer(?sca_issues_count_by_level_3) AS ?int_sca_issues_count_by_level_3)
  BIND (xsd:integer(?sca_issues_count_by_level_4) AS ?int_sca_issues_count_by_level_4)
  BIND (xsd:integer(?sca_issues_count_by_level_5) AS ?int_sca_issues_count_by_level_5)
  BIND (xsd:double(?coverage_doxygen) AS ?d_coverage_doxygen)
}
