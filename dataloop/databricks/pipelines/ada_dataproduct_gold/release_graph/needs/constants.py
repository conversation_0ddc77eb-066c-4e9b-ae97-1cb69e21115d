"""Constants for pace needs gold transform."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import StringType, StructField, StructType

SILVER_SCHEMA = "needs"
GOLD_SCHEMA = "ada_release_graph"
SILVER_TABLE = "pace_needs"
GOLD_TABLE_PROPS = "needs_properties_v2"
GOLD_TABLE_EDGES = "needs_edges_v2"
INGEST_COMMITS_TABLE = "ingest_commits"

GOLD_DATA_SCHEMA = StructType(
    [
        StructField("start", StringType(), True),
        StructField("target", StringType(), True),
        StructField("type", StringType(), True),
        StructField("mapped_type", StringType(), True),
        StructField("version", StringType(), True),
    ]
)

SPECIAL_EDGES = ["tags", "test_platform", "test_trigger"]

DESCRIPTION_GOLD_PROPERTIES = (
    "Contains the properties of needs elements like da_status, owner etc."
    " Each row represents an edge connecting a needs node to a property,"
    " classified by type and version. Managed by the RDD Team."
)

DESCRIPTION_GOLD_EDGES = (
    "Defines relationships between needs nodes."
    "Each row represents an edge between two needs nodes,"
    "classified by type and version. Managed by the RDD Team."
)
