"""Load needs to Stardog using Streaming."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from pathlib import Path
from typing import Optional

from pyspark.sql import DataFrame, SparkSession
from rddlib.dbx_utils import FullSchemaName, FullTableName
from rddlib.needs_utils import get_latest_pace_ontology
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from common.load import StardogStreamVersionLoader  # noqa: E402

logger = logging.getLogger(__name__)


def _get_latest_pace_ontology_ttl() -> str:
    """Fetch the latest PACE ontology file from Artifactory and save it locally."""
    file_path = "pace_metamodel.ttl"
    art_path = get_latest_pace_ontology()
    with art_path.open() as fd:
        with open(file_path, "wb") as out:
            out.write(fd.read())
    return file_path


class NeedsStreamVersionLoader(StardogStreamVersionLoader):
    """Stream version loader class for needs."""

    def __init__(
        self,
        spark: SparkSession,
        source_table_full: FullTableName,
        log_schema_full: FullSchemaName,
        stardog_db: str,
        mapping_file: Path,
        namespaces_file: Path,
        ontology_file: Optional[Path] = None,
    ) -> None:
        """Initialize the loader and optionally download the ontology."""
        # Call the base class constructor
        super().__init__(
            spark,
            source_table_full,
            log_schema_full,
            stardog_db,
            mapping_file,
            namespaces_file,
            ontology_file or Path(),  # Pass an empty Path if not provided
        )

        if ontology_file is None:  # Only download if no ontology file was provided
            logger.info("No ontology file provided. Downloading the latest PACE ontology...")
            ontology_path = _get_latest_pace_ontology_ttl()
            logger.info(f"PACE ontology downloaded to: {ontology_path}")

            # Set the ontology file path
            self.ontology_file = Path(ontology_path)
        else:
            logger.info(f"Using provided ontology file: {ontology_file}")

    def _filter_by_versions(self, source_df: DataFrame, version_df: DataFrame) -> DataFrame:
        return version_df.join(source_df, source_df["version"] == version_df["commit_sha"], "inner")


if __name__ == "__main__":
    NeedsStreamVersionLoader.cli_entrypoint()
