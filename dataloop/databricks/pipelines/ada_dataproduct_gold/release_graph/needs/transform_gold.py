"""Transform PACE needs from silver to gold using ingest commits."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from contextlib import nullcontext
from typing import Any, Optional, Tuple

from constants import (
    DESCRIPTION_GOLD_EDGES,
    DESCRIPTION_GOLD_PROPERTIES,
    GOLD_DATA_SCHEMA,
    GOLD_SCHEMA,
    GOLD_TABLE_EDGES,
    GOLD_TABLE_PROPS,
    INGEST_COMMITS_TABLE,
    SILVER_SCHEMA,
    SILVER_TABLE,
    SPECIAL_EDGES,
)
from pyspark.dbutils import DBUtils
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, concat, explode, lit, regexp_replace, split, when
from pyspark.sql.types import ArrayType, StringType
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging
from rddlib.needs_utils import get_latest_pace_ontology
from rddlib.stream_utils import checkpoint_backup, stream_updates_to_dataframe
from rdflib import Graph, query

logger = logging.getLogger(__name__)

# Constants
SILVER_CATALOG = get_dbx_env_catalog("silver")
GOLD_CATALOG = get_dbx_env_catalog("gold")


def _get_query(predicate: str, g: Graph) -> query.Result:
    return g.query(
        f"""
    SELECT ?mapped_type ?needs_type
    WHERE {{
        ?mapped_type {predicate} ?needs_type .
    }}
    """
    )


def _get_dict_from_query(query: query.Result) -> dict[str, Any]:
    return {str(needs_type): str(mapped_type) for mapped_type, needs_type in query}  # type: ignore[misc]


class NeedsGoldTransformer:
    """This class is responsible for transforming silver-level needs data into gold-level."""

    app: str

    def __init__(self, app: str):
        """Initialize a NeedsGoldTransformer instance."""
        self.app = app
        self.spark = SparkSession.builder.getOrCreate()
        self.type_mappings, self.link_mappings, self.option_mappings, self.extra_mappings = self.get_mapping_dicts()

    def get_mapping_dicts(self) -> Tuple[dict[str, Any], dict[str, Any], dict[str, Any], dict[str, Any]]:
        """Generate three mapping dictionaries based on the latest ontology.

        This contains mappings for type, link and option value

        return: a tuple of three mapping dictionaries:
            key: Original needs json keyword, eg. 'activity'
            value: The corresponding value in the ontology, eg. 'urn:pace:ontology:Activity'
            In the order of type, link and option mappings.
        """
        # Get schema from artifactory and parse it into a RDFlib Graph object
        g = Graph()
        g.parse(get_latest_pace_ontology().open())

        # Generate the individual mapping dictionaries for each type of mapping
        type_mappings = _get_dict_from_query(_get_query("pace:needsType", g))
        link_mappings = _get_dict_from_query(_get_query("pace:needsLink", g))
        option_mappings = _get_dict_from_query(_get_query("pace:needsOption", g))

        # Add missing docname and doctype to options_mapping
        missing_mappings = {"docname": "urn:pace:ontology:docname", "doctype": "urn:pace:ontology:doctype"}
        option_mappings = {**option_mappings, **missing_mappings}

        # Extra mappings to include additional values
        extra_mappings = {"da_status": "urn:pace:ontology:da_status"}  # supports existing sparql queries

        return (type_mappings, link_mappings, option_mappings, extra_mappings)

    def _create_label_prop(self, needs_df: DataFrame) -> DataFrame:
        """Create a property dataframe with the 'label' property.

        Args:
            needs_df (DataFrame): The DataFrame containing the 'needs' nodes.

        Returns:
            DataFrame: A DataFrame containing the 'label' property.
        """
        label_prop = (
            needs_df.select("id", "version", "title")
            .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), col("version")))
            .withColumnRenamed("title", "target")
            .withColumn("type", lit("label"))
            .withColumn("mapped_type", lit("http://www.w3.org/2000/01/rdf-schema#label"))
            .drop("id")
        )
        return label_prop

    def _create_tag_props(self, needs_df: DataFrame) -> DataFrame:
        """Create properties for tag nodes, including rdfs:label."""

        # Explode the tags array to create individual rows for each tag
        exploded_tags = needs_df.select(explode("tags").alias("tag"), "version").distinct()

        # Create a DataFrame for each tag node with its rdfs:label
        tag_props = (
            exploded_tags.withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("tag")))  # Tag node
            .withColumnRenamed("tag", "target")  # Rename tag to target
            .withColumn("type", lit("label"))
            .withColumn("mapped_type", lit("http://www.w3.org/2000/01/rdf-schema#label"))
        )

        return tag_props

    def _transform_props(self, needs_df: DataFrame) -> DataFrame:
        """Parse data and extract the properties for the needs nodes.

        Args:
            needs_df (DataFrame): The DataFrame containing the 'needs' data.

        Returns:
            DataFrame: A transformed DataFrame with mapped and cleaned nodes.
        """
        props_df = self.spark.createDataFrame([], GOLD_DATA_SCHEMA)
        schema = needs_df.schema

        # Replace non-digit issue_id with None
        needs_df = needs_df.withColumn(
            "issue_id", when(col("issue_id").rlike(".*?[^0-9].*"), lit(None)).otherwise(col("issue_id"))
        )

        for field in schema:
            column_name = field.name
            data_type = field.dataType
            if (
                isinstance(data_type, StringType)
                and column_name != "version"
                and column_name != "type"
                and column_name != "test_platform"
                and column_name != "test_trigger"
            ):
                prop = needs_df.select(needs_df.id, needs_df[column_name])
                prop = (
                    prop.join(needs_df.select("id", "version"), "id")
                    .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), col("version")))
                    .withColumnRenamed(column_name, "target")
                    .withColumn("type", lit(column_name))
                    .drop("id")
                )

                # Map edge type
                prop = self._add_mapped_values(prop, self.option_mappings, "type", "mapped_type", self.extra_mappings)
                # Add edge to total edges
                props_df = props_df.unionByName(prop)

        # add label property from title column
        props_df = props_df.unionByName(self._create_label_prop(needs_df))
        props_df = props_df.dropDuplicates()

        # add label property for tags
        props_df = props_df.unionByName(self._create_tag_props(needs_df))
        props_df = props_df.dropDuplicates()

        # filter out empty or null properties
        props_df = props_df.where(props_df.target.isNotNull()).where("target != ''")
        return props_df

    def _handle_special_edges(self, needs_df: DataFrame, edges_df: DataFrame) -> DataFrame:
        """Handles corner cases involving, "tags", "test_platform", and "test_trigger".

        Args:
            needs_df (DataFrame): The DataFrame containing the needs data.
            edges_df (DataFrame): The DataFrame containing the total edges.

        Returns:
            DataFrame: A DataFrame containing the specialized edge.
        """

        # Pre-process test-platform and -trigger, by creating arrays from comma separated strings.
        needs_df = needs_df.withColumn("test_platform", split(regexp_replace(col("test_platform"), " ", ""), ","))
        needs_df = needs_df.withColumn("test_trigger", split(regexp_replace(col("test_trigger"), " ", ""), ","))

        for name in SPECIAL_EDGES:
            edge = needs_df.select(needs_df.id, explode(name))
            edge = edge.dropDuplicates()
            # create initial edge with start, type and version columns
            edge = (
                edge.join(needs_df.select("id", "version"), "id")
                .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), col("version")))
                .withColumn("type", lit(name))
                .drop("id")
            )
            # add custom target columns
            if name == "tags":
                # clean up "tags"
                edge = edge.withColumn(
                    "target", regexp_replace(concat(lit("urn:pace:ontology:needs:"), col("col")), " ", "")
                )
                # Create rdf:type for tag
                tag_type_edge = (
                    edge.select(col("target").alias("start"), col("version"))
                    .withColumn("target", lit("urn:pace:ontology:needs:Tag"))
                    .withColumn("type", lit("type"))
                    .withColumn("mapped_type", lit("http://www.w3.org/1999/02/22-rdf-syntax-ns#type"))
                )
                tag_type_edge = tag_type_edge.dropDuplicates()
                edges_df = edges_df.unionByName(tag_type_edge)
            elif name == "test_platform":
                # Link to static TestPlatform node
                edge = edge.withColumn("target", concat(lit("urn:pace:ontology:TestPlatform-"), col("col")))
            elif name == "test_trigger":
                # Link to static TestTrigger node
                edge = edge.withColumn("target", concat(lit("urn:pace:ontology:TestTrigger-"), col("col")))
            edge = edge.drop("col")
            # Map edge type
            edge = self._add_mapped_values(edge, self.option_mappings, "type", "mapped_type")
            # Add edge to total edges
            edges_df = edges_df.unionByName(edge)

        return edges_df

    def _create_type_edge(self, needs_df: DataFrame) -> DataFrame:
        """Create edge to the node type.

        Args:
            needs_df (DataFrame): The DataFrame containing the needs data.

        Returns:
            DataFrame: A DataFrame with the edge to the node type.
        """
        # Add the edge to the node type
        type_edge = needs_df.select(needs_df.id, needs_df.type)
        type_edge = (
            type_edge.join(needs_df.select("id", "version"), "id")
            .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), col("version")))
            .withColumnRenamed("type", "target")
            .withColumn("type", lit("type"))
            .withColumn("mapped_type", lit("http://www.w3.org/1999/02/22-rdf-syntax-ns#type"))
            .drop("id", "col")
        )
        type_edge = self._add_mapped_values(type_edge, self.type_mappings, "target", "mapped_target").drop("target")
        type_edge = type_edge.withColumnRenamed("mapped_target", "target").drop("mapped_target")
        return type_edge

    def _create_version_edge(self, needs_df: DataFrame) -> DataFrame:
        """Create edge to the version node.

        Args:
            needs_df (DataFrame): The DataFrame containing the needs data.

        Returns:
            DataFrame: A DataFrame with the edge to the version node.
        """
        # Add the edge to the version node
        version_edge = needs_df.select(needs_df.id, needs_df.version)
        version_edge = (
            version_edge.withColumn("start", concat(lit("urn:pace:ontology:"), col("version")))
            .withColumn("target", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), col("version")))
            .withColumn("type", lit("version"))
            .withColumn("mapped_type", lit("urn:pace:ontology:validFor"))
            .drop("id")
        )
        return version_edge

    def _create_id_iri_edge(self, needs_df: DataFrame) -> DataFrame:
        """Create a property dataframe with the 'id_iri' property.

        Args:
            needs_df (DataFrame): The DataFrame containing the 'needs' data.

        Returns:
            DataFrame: A DataFrame containing the 'id_iri' property.
        """
        id_iri_edge = (
            needs_df.select("id", "version")
            .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), col("version")))
            .withColumn("target", concat(lit("urn:pace:ontology:needs:"), col("id")))
            .withColumn("type", lit("id_iri"))
            .withColumn("mapped_type", lit("urn:pace:ontology:idIri"))
            .drop("id")
        )
        return id_iri_edge

    def _transform_edges(self, needs_df: DataFrame) -> DataFrame:
        """Creates a DataFrame with mapped and cleaned needs edges from the given DataFrame.

        Args:
            needs_df (DataFrame): DataFrame with needs data to extract into edges table.

        Returns:
            DataFrame: A DataFrame containing the mapped and cleaned needs edges.
        """
        # Creating an empty DataFrame to hold the edges
        edges_df = self.spark.createDataFrame([], GOLD_DATA_SCHEMA)
        schema = needs_df.schema

        for field in schema:
            column_name = field.name
            data_type = field.dataType
            if isinstance(data_type, ArrayType) and not column_name.endswith("_back"):
                if column_name in SPECIAL_EDGES:
                    continue
                edge = needs_df.select(needs_df.id, explode(needs_df[column_name]))
                edge = edge.dropDuplicates()
                # Create edge with start, target and type column
                edge = (
                    edge.join(needs_df.select("id", "version"), "id")
                    .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), col("version")))
                    .withColumn("target", concat(lit("urn:pace:ontology:needs:"), col("col"), lit("_"), col("version")))
                    .withColumn("type", lit(column_name))
                    .drop("col", "id")
                )
                # Map edge type
                edge = self._add_mapped_values(edge, self.link_mappings, "type", "mapped_type")
                # Add edge to total edges
                edges_df = edges_df.unionByName(edge)

        # Add the edge to the node type
        edges_df = edges_df.unionByName(self._create_type_edge(needs_df))

        # Add the edge to the version node
        edges_df = edges_df.unionByName(self._create_version_edge(needs_df))

        # Handle corner cases
        edges_df = self._handle_special_edges(needs_df, edges_df)

        # Add the edge to id_iri
        edges_df = edges_df.unionByName(self._create_id_iri_edge(needs_df))

        return edges_df

    def _add_mapped_values(
        self,
        df: DataFrame,
        mappings: dict[str, Any],
        key: str,
        mapped_key: str,
        extra_mappings: Optional[dict[str, Any]] = None,
    ) -> DataFrame:
        """Add mapped values to a DataFrame based on a mapping dictionary.

        Args:
            df (DataFrame): The DataFrame to add mapped values to.
            mappings (dict): A dictionary mapping original values to their corresponding mapped values.
            key (str): The key column name in the DataFrame to match against the mapping.
            mapped_key (str): The name of the new column to store mapped values.
            extra_mappings (Optional[dict], optional): A dictionary providing additional mappings. Defaults to None.

        Returns:
            DataFrame: DataFrame with added mapped values.
        """
        # Create a DataFrame from the mappings dictionary, using "key" and "mapped_key" as column names
        mapping_df = self.spark.createDataFrame(list(mappings.items()), [key, mapped_key])

        # Apply the primary mappings
        mapped_df = df.join(mapping_df, key, "left_outer")

        # Apply extra mappings
        if extra_mappings:
            extra_keys_list = list(extra_mappings.keys())
            extra_mapping_df = self.spark.createDataFrame(list(extra_mappings.items()), [key, mapped_key])
            extra_mapped_df = df.filter(col(key).isin(extra_keys_list)).join(extra_mapping_df, key, "left_outer")
            return mapped_df.exceptAll(extra_mapped_df).unionByName(extra_mapped_df)

        return mapped_df

    def _get_checkpoint_path(self, catalog: str, schema: str, table: str) -> str:
        """Construct the checkpoint path for the given catalog, schema, and table.

        Checkpoints are used to store progress information during streaming, ensuring that the process
        can resume from the last successful point without reprocessing data.

        Args:
            catalog (str): The catalog name.
            schema (str): The schema name.
            table (str): The table name.

        Returns:
            str: The constructed checkpoint path.
        """
        return f"/Volumes/{catalog}/{schema}/checkpoint_locations/{table}"

    def _read_stream_commits_table(self, full_table_name: str) -> DataFrame:
        """Loads data from ingest commits gold table returns a streaming DataFrame.

        Args:
            full_table_name (str): The full name of the table to read from, including catalog and schema.

        Returns:
            DataFrame: A streaming DataFrame containing the commit sha of ingest commits
        """
        return self.spark.readStream.format("delta").table(full_table_name).select("commit_sha").distinct()

    def _write_streaming_table(self, df: DataFrame, full_table_name: str, checkpoint_path: str) -> None:
        """Writes the streaming DataFrame to a Delta table using checkpoints.

        This function sets up a streaming write operation that appends new data from the DataFrame
        to the specified Delta table based on available data.

        Args:
            df (DataFrame): The DataFrame containing the data to be written.
            full_table_name (str): The name of the target Delta table (including catalog and schema).
            checkpoint_path (str): The path to the checkpoint directory.
        """
        query = (
            df.writeStream.trigger(availableNow=True)
            .option(
                "checkpointLocation",
                checkpoint_path,
            )
            .toTable(full_table_name, format="delta", outputMode="append")
        )

        query.awaitTermination()
        if query.lastProgress and "numInputRows" in query.lastProgress:
            num_new_rows = query.lastProgress["numInputRows"]
            logger.info(f'{num_new_rows} new row(s) added to the gold table "{full_table_name}".')
        else:
            logger.info(f'No progress information available for table "{full_table_name}".')

    def run_stream(self) -> None:
        """Run the transformation process using streaming."""
        silver_table_full = f"{SILVER_CATALOG}.{SILVER_SCHEMA}.{SILVER_TABLE}"
        gold_properties_table_full = f"{GOLD_CATALOG}.{GOLD_SCHEMA}.{GOLD_TABLE_PROPS}"
        gold_edges_table_full = f"{GOLD_CATALOG}.{GOLD_SCHEMA}.{GOLD_TABLE_EDGES}"
        ingest_commits_table_full = f"{GOLD_CATALOG}.{GOLD_SCHEMA}.{INGEST_COMMITS_TABLE}"

        # Define the checkpoint path for the streaming job
        commits_checkpoint_path = transformer._get_checkpoint_path(
            GOLD_CATALOG, GOLD_SCHEMA, f"needs_transform_gold_{self.app}_{INGEST_COMMITS_TABLE}"
        )

        # Check if the checkpoint path exists
        dbutils = DBUtils(self.spark)
        try:
            dbutils.fs.ls(commits_checkpoint_path)
            checkpoint_exists = True
        except Exception:
            checkpoint_exists = False
            logger.info(f"Checkpoint path does not exist: {commits_checkpoint_path}.")

        # Use the context manager only if the checkpoint exists
        context_manager = checkpoint_backup(commits_checkpoint_path) if checkpoint_exists else nullcontext()
        with context_manager:
            # Stream ingest commits table
            logger.info(f"Reading stream from ingest commits table: {ingest_commits_table_full}")
            version_df = self._read_stream_commits_table(ingest_commits_table_full)

            # Convert streaming df to batch df
            version_batch_df = stream_updates_to_dataframe(version_df, commits_checkpoint_path)
            if version_batch_df is None:
                logger.info("No new versions found. Exiting...")
                return

            # Get list of versions as list
            versions = [row["commit_sha"] for row in version_batch_df.select("commit_sha").collect()]

            # Read needs silver table for relevant versions
            logger.info(f"Reading needs silver table: {silver_table_full}")
            source_df = self.spark.read.format("delta").table(silver_table_full).filter(col("version").isin(versions))

            # Transform silver data into properties and edges
            logger.info("Transforming needs properties")
            props_df = self._transform_props(source_df)
            props_df.write.format("delta").mode("append").saveAsTable(gold_properties_table_full)

            logger.info("Transforming needs edges")
            edges_df = self._transform_edges(source_df)
            edges_df.write.format("delta").mode("append").saveAsTable(gold_edges_table_full)

            # Add metadata to the table
            dtu.update_table_metadata(gold_properties_table_full, DESCRIPTION_GOLD_PROPERTIES)
            dtu.update_table_metadata(gold_edges_table_full, DESCRIPTION_GOLD_EDGES)


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Needs Gold Layer Transform")
    parser.add_argument("-a", "--app", help="Application: ex - release, rng")
    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()
    app = args.app

    # Setup logging
    setup_databricks_logging(FullSchemaName(GOLD_CATALOG, GOLD_SCHEMA, False), f"needs/gold/{app}", run_id=args.run_id)

    transformer = NeedsGoldTransformer(app)
    transformer.run_stream()
