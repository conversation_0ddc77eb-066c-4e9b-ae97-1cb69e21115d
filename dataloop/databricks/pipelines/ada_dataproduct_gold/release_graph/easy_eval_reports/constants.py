"""Constants for the SCA."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, concat, lit


def select_for_mapping(source_df: DataFrame) -> DataFrame:
    """Selects the columns required by the Stardog mapping for easy_eval report data."""
    return source_df.withColumn(
        "report_id", concat(col("test.name"), lit("_"), col("run_id"), lit("_"), col("sut_commit_id"))
    ).selectExpr(
        "report_id",
        "sut_commit_id",
        "test.name AS test_name",
        "test.description AS test_description",
        "test.file_path AS test_file_path",
        "test.results.expected AS test_results_expected",
        "test.results.operator AS test_results_operator",
        "test.results.kpi_value AS test_results_kpi_value",
        "test.results.pass AS test_results_pass",
        "test.results.threshold AS test_results_threshold",
        "test.results.lower_threshold AS test_results_lower_threshold",
        "test.results.upper_threshold AS test_results_upper_threshold",
        "test_platform",
        "test.duration AS test_duration",
        "run_duration",
        "run_id",
        "report_creation_time::TIMESTAMP AS report_creation_time",
        "test_case_id",
        "sut_profile",
        "deployment",
        "preset",
        "variation_concrete_parameters",
    )
