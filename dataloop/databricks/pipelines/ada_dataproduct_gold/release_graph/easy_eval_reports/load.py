"""Load easy_eval basic reports to Stardog."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
from typing import Any

from pyspark.sql import DataFrame
from pyspark.sql.functions import col
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from common.load import StardogBatchLoader  # noqa: E402
from easy_eval_reports.constants import select_for_mapping


class EasyEvalReportLoader(StardogBatchLoader):
    """Loader class for easy_eval reports."""

    def _filter_source_table(  # type: ignore[override]
        self, source_df: DataFrame, task_output: str | None, version: str | None, latest_version: bool, **kwargs: Any
    ) -> DataFrame:
        source_df = source_df.withColumn("report_creation_time", col("report_creation_time").cast("timestamp"))

        if task_output is not None:
            from databricks.sdk import WorkspaceClient

            client = WorkspaceClient()
            task_version: str = client.dbutils.jobs.taskValues.get(taskKey=task_output, key="version")  # type: ignore[func-returns-value] # noqa: E501
            source_df = source_df.where(col("sut_commit_id") == task_version)
        elif version is not None:
            source_df = source_df.where(col("sut_commit_id") == version)
        elif latest_version:
            source_df = (
                source_df.sort("report_creation_time", ascending=False)
                .where(~col("sut_commit_id").contains("placeholder"))
                .limit(1)
            )
        else:
            raise Exception("Exactly one valid argument must be provided")

        return select_for_mapping(source_df)

    @classmethod
    def _cli_construct_arg_parser(cls) -> argparse.ArgumentParser:
        """Parse command line arguments of a Stardog load script."""
        parser = super()._cli_construct_arg_parser()

        mode_group = parser.add_mutually_exclusive_group()
        mode_group.add_argument(
            "--task_output",
            type=str,
            action="store",
            default=None,
            help="Get version from task value. Specify the task name.",
        )
        mode_group.add_argument(
            "--version",
            type=str,
            action="store",
            default=None,
            help="The Github commit SHA of the version to load the test reports for.",
        )
        mode_group.add_argument("--latest_version", action="store_true", default=False)

        return parser


if __name__ == "__main__":
    EasyEvalReportLoader.cli_entrypoint()
