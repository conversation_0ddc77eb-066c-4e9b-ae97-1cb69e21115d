PREFIX : <urn:pace:ontology:adx:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>


MAPPING <urn:adx_report>
FROM SQL {
  SELECT *
  FROM {{ table }}
}
TO {
  ?test_report a :Report ;
    rdfs:comment ?test_description ;
    rdfs:label ?test_name ;
    :hasDeployment ?deployment ;
    :hasTestCase ?needs_id ;
    :preset ?preset ;
    :reportCreationTime ?date_creation_time ;
    :runDuration ?d_run_duration ;
    :runId ?run_id ;
    :testDescription ?test_description ;
    :testPath ?test_file_path ;
    :testName ?test_name ;
    :testDuration ?d_test_duration ;
    :testResultsExpected ?test_results_expected ;
    :testResultsOperator ?test_results_operator ;
    :testResultsKpiValue ?test_results_kpi_value ;
    :testResultsLowerThreshold ?d_test_results_lower_threshold ;
    :testResultsPassed ?b_test_results_pass ;
    :testResultsThreshold ?d_test_result_threshold ;
    :testResultsUpperThreshold ?d_test_results_upper_threshold ;
    :variationConcreteParameters ?variation_concrete_parameters ;
    pace:runsOnTestPlatform ?test_platform_iri ;
    pace:hasSutProfile ?sut_profile_iri .

  ?version_iri a pace:ArtifactVersion ;
    pace:validFor ?test_report .
}

WHERE {
  BIND (template("urn:pace:ontology:adx:{report_id}_{sut_commit_id}") AS ?test_report)
  BIND (template("urn:pace:ontology:{sut_commit_id}") AS ?version_iri)
  BIND (xsd:boolean(?test_results_pass) AS ?b_test_results_pass)
  BIND (xsd:double(?test_duration) AS ?d_test_duration)
  BIND (xsd:double(?run_duration) AS ?d_run_duration)
  BIND (xsd:double(?test_results_threshold) AS ?d_test_result_threshold)
  BIND (xsd:double(?test_results_lower_threshold) AS ?d_test_results_lower_threshold)
  BIND (xsd:double(?test_results_upper_threshold) AS ?d_test_results_upper_threshold)
  BIND (xsd:dateTime(?report_creation_time) AS ?date_creation_time)
  BIND (template("urn:pace:ontology:needs:{test_case_id}_{sut_commit_id}") AS ?needs_id)
  BIND (template("urn:pace:ontology:TestPlatform-{test_platform}") AS ?test_platform_iri)
  BIND (template("urn:pace:ontology:needs:{sut_profile}_{sut_commit_id}") AS ?sut_profile_iri)
}
