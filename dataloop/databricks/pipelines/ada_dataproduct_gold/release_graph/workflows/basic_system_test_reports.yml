resources:
  jobs:
    basic_system_test_reports_stream_version:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - basic_system_test Reports - Graph - Stream Version
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "Graph"
        type: "Stream Version"
        "ADA Release Graph": ""
        "Basic System Test Reports": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: load_basic_system_test_reports
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/basic_system_test_reports/stream_version_load.py
            parameters:
              - --log_schema
              - ada_release_graph
              - --table
              - gold.ada_release_graph.test_report
              - --version_table
              - gold.ada_release_graph.ingest_commits
              - --stardog_db
              - ada_release_v2
              - --namespaces
              - common/resources/namespaces.ttl
              - --mapping
              - basic_system_test_reports/resources/mapping.j2
              - --ontology
              - basic_system_test_reports/resources/metamodel.ttl
          job_cluster_key: release_graph_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: release_graph_job_cluster
          new_cluster: ${var.load_job_cluster}
