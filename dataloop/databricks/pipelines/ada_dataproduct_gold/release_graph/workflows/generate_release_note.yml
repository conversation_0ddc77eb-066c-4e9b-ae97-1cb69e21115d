variables:
  generate_release_note_driver_pool_id:
    description: "Instance pool ID for generating release note"
    lookup:
      instance_pool: "nonspot_E4ads_v5_rt15.4"
  generate_release_note_job_cluster:
    description: "Job cluster for generating release note"
    type: complex
    default:
      data_security_mode: SINGLE_USER
      spark_version: ${var.spark_version}
      policy_id: ${var.job_cluster_policy_id}
      instance_pool_id: ${var.generate_release_note_driver_pool_id}
      spark_conf:
        "spark.databricks.cluster.profile": "singleNode"
        "spark.master": "local[*, 4]"
      custom_tags:
        "ResourceClass": "SingleNode"

resources:
  jobs:
    generate_release_note:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - Generate Release Note - Manual
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        schedule: "Manual"
        "ADA Release Graph": ""
        "Generate Release Note": ""
        # Required parameter to evaluate external data version and software version
      parameters:
        - name: ingest_commit_sha
          default: ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: generate_release_note
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "release-note",
                "generate",
                "--template_name",
                "template_split",
                "--config_name",
                "config_template_split.yaml",
                "--external_data_version",
                "{{job.parameters.ingest_commit_sha}}",
                "--software_version",
                "{{job.parameters.ingest_commit_sha}}",
                "--query_parameters",
                "{}",
              ]
          job_cluster_key: release_graph_job_cluster
          libraries:
            - pypi:
                package: rddlib==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: release_graph_job_cluster
          new_cluster: ${var.generate_release_note_job_cluster}
