resources:
  jobs:
    needs_stream_version:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - Needs - Graph - Stream Version
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "Graph"
        type: "Stream Version"
        "ADA Release Graph": ""
        "Needs": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: load_needs_properties
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/needs/stream_version_load.py
            parameters:
              - --log_schema
              - ada_release_graph
              - --table
              - gold.ada_release_graph.needs_properties_v2
              - --version_table
              - gold.ada_release_graph.ingest_commits
              - --stardog_db
              - ada_release_v2
              - --namespaces
              - common/resources/namespaces.ttl
              - --mapping
              - needs/resources/mapping_properties.j2
          job_cluster_key: release_graph_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib[stardog,needs]==${var.rddlib_version}

        - task_key: load_needs_edges
          depends_on:
            - task_key: load_needs_properties
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/needs/stream_version_load.py
            parameters:
              - --log_schema
              - ada_release_graph
              - --table
              - gold.ada_release_graph.needs_edges_v2
              - --version_table
              - gold.ada_release_graph.ingest_commits
              - --stardog_db
              - ada_release_v2
              - --namespaces
              - common/resources/namespaces.ttl
              - --mapping
              - needs/resources/mapping_edges.j2

          job_cluster_key: release_graph_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib[stardog,needs]==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: release_graph_job_cluster
          new_cluster: ${var.load_job_cluster}
