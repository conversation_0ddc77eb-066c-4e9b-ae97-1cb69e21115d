variables:
  data_model_instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E8ads_v5_rt14.3"
  data_model_driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"

resources:
  jobs:
    data_model_gold:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - Data Model - Gold
      tasks:
        - task_key: custom_classes
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/custom_classes.py
          job_cluster_key: needs_data_model_cluster
          libraries:
            - pypi:
                package: rddlib[stardog,needs]==${var.rddlib_version}

        - task_key: sys_req_level
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/sys_req_levels.py
          job_cluster_key: needs_data_model_cluster
          libraries:
            - pypi:
                package: rddlib[stardog,needs]==${var.rddlib_version}

        - task_key: test_instances
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/test_instances.py
          job_cluster_key: needs_data_model_cluster
          libraries:
            - pypi:
                package: rddlib[stardog,needs]==${var.rddlib_version}

        - task_key: split_needs
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/split_needs.py
          job_cluster_key: needs_data_model_cluster
          libraries:
            - pypi:
                package: rddlib[stardog,needs]==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: needs_data_model_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 6
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.data_model_instance_pool_id}
            driver_instance_pool_id: ${var.data_model_driver_instance_pool_id}

    data_model_load:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - Data Model - Load
      tasks:
        - task_key: data_model_load_custom_classes_properties
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/stream_version_load.py
            parameters:
              - --log_schema
              - ada_release_graph
              - --table
              - gold.ada_release_graph.custom_properties
              - --version_table
              - gold.ada_release_graph.ingest_commits
              - --stardog_db
              - ada_release_v2
              - --namespaces
              - common/resources/namespaces.ttl
              - --mapping
              - data_model/resources/custom_properties_mapping.j2
              - --ontology
              - data_model/resources/ReleaseMetamodel.ttl

          job_cluster_key: data_model_load_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: data_model_load_custom_classes_edges
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/stream_version_load.py
            parameters:
              - --log_schema
              - ada_release_graph
              - --table
              - gold.ada_release_graph.custom_edges
              - --version_table
              - gold.ada_release_graph.ingest_commits
              - --stardog_db
              - ada_release_v2
              - --namespaces
              - common/resources/namespaces.ttl
              - --mapping
              - data_model/resources/custom_edges_mapping.j2
              - --ontology
              - data_model/resources/ReleaseMetamodel.ttl

          job_cluster_key: data_model_load_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: data_model_load_requirement_levels
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/stream_version_load.py
            parameters:
              - --log_schema
              - ada_release_graph
              - --table
              - gold.ada_release_graph.requirement_levels
              - --version_table
              - gold.ada_release_graph.ingest_commits
              - --stardog_db
              - ada_release_v2
              - --namespaces
              - common/resources/namespaces.ttl
              - --mapping
              - data_model/resources/req_levels_mapping.j2
              - --ontology
              - data_model/resources/ReleaseMetamodel.ttl

          job_cluster_key: data_model_load_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: data_model_load_test_instances
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/data_model/stream_version_load.py
            parameters:
              - --log_schema
              - ada_release_graph
              - --table
              - gold.ada_release_graph.test_instances
              - --version_table
              - gold.ada_release_graph.ingest_commits
              - --stardog_db
              - ada_release_v2
              - --namespaces
              - common/resources/namespaces.ttl
              - --mapping
              - data_model/resources/test_instances_mapping.j2
              - --ontology
              - data_model/resources/ReleaseMetamodel.ttl

          job_cluster_key: data_model_load_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: data_model_load_cluster
          new_cluster: ${var.load_job_cluster}
