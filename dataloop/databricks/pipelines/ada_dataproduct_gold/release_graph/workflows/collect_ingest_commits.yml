variables:
  collect_ingest_commits_driver_pool_id:
    description: "Instance pool ID for collecting ingest commits"
    lookup:
      instance_pool: "nonspot_E4ads_v5_rt15.4"
  collect_ingest_commits_job_cluster:
    description: "Job cluster for collecting ingest commits"
    type: complex
    default:
      data_security_mode: SINGLE_USER
      spark_version: ${var.spark_version}
      policy_id: ${var.job_cluster_policy_id}
      instance_pool_id: ${var.collect_ingest_commits_driver_pool_id}
      spark_conf:
        "spark.databricks.cluster.profile": "singleNode"
        "spark.master": "local[*, 4]"
      custom_tags:
        "ResourceClass": "SingleNode"

resources:
  jobs:
    collect_ingest_commits:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - Collect Ingest Commits - Gold - Manual
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "Gold"
        schedule: "Manual"
        "ADA Release Graph": ""
        "Collect Ingest Commits": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: collect_vehicle_performance_commits
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/github/collect_vehicle_performance_commits.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: release_graph_job_cluster
          libraries:
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: collect_ingest_commits
          depends_on:
            - task_key: collect_vehicle_performance_commits
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/github/collect_ingest_commits.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: release_graph_job_cluster
          libraries:
            - pypi:
                package: rddlib==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: release_graph_job_cluster
          new_cluster: ${var.collect_ingest_commits_job_cluster}
