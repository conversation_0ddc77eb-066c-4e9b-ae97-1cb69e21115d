variables:
  needs_pace_gold_driver_pool_id:
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  needs_pace_gold_worker_pool_id:
    lookup:
      instance_pool: "default_D4ads_v5_rt14.3"
  needs_pace_gold_job_cluster:
    type: complex
    default:
      spark_version: ${var.spark_version}
      autoscale:
        min_workers: 1
        max_workers: 4
      policy_id: ${var.job_cluster_policy_id}
      instance_pool_id: ${var.needs_pace_gold_worker_pool_id}
      driver_instance_pool_id: ${var.needs_pace_gold_driver_pool_id}


resources:
  jobs:
    needs_pace_gold:
      # Give permissions to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: Needs - PACE - Gold - Nightly
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "Gold"
        schedule: "Nightly"
        "Needs": ""
        "PACE": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: transform_pace
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/needs/transform_gold.py
            parameters:
              - --app
              - "release"
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: release_graph_job_cluster
          libraries:
            - pypi:
                package: rddlib[needs]==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: release_graph_job_cluster
          new_cluster: ${var.needs_pace_gold_job_cluster}
