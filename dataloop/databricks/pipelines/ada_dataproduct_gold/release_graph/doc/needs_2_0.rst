.. ============================================================================================================
   C O P Y R I G H T
   ------------------------------------------------------------------------------------------------------------
   \copyright (C) 2024-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
   ============================================================================================================

.. _needs_2_0:

Needs (V2.0)
############

.. contents::
  :depth: 5

Overview
========

This chapter describes the ingest workflow for the Needs data.
It uses Structured Streaming to ingest the data incrementally into a Knowledge Graph platform.
For better understanding, each component of the workflow is explained in the following sections.

Data Source
-----------

The needs data is produced by the PACE Docs Workflow, which is triggered as part of the Gatekeeper workflow of the PACE repository.
This process generates a needs.json file containing the required data.

The needs.json file is uploaded to the Databricks landing zone storage account, where it is stored for further processing.

For implementation details, refer to the `PACE Docs Workflow`_.

.. _PACE Docs Workflow: https://github.com/PACE-INT/dataloop/blob/main/databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/ado/extract.py

In the Databricks landing zone storage account, the needs.json file is stored in the following path:

.. code-block:: bash

    /pace/needs/YYYY/MM/

- Container: pace
- Folder: needs
- Directory Structure: Organized by year (YYYY) and month (MM) for easy retrieval.

For more details, view the `Databricks landing zone storage account`_ in the Azure Portal.

.. _Databricks landing zone storage account: https://portal.azure.com/#@AADPACE.onmicrosoft.com/resource/subscriptions/ac76df14-c71e-433e-aa63-3c1c3371b54b/resourceGroups/rg-pace-datalake-prod-weu/providers/Microsoft.Storage/storageAccounts/dbxingestzoneprod/overview

Below is an example code snippet that demonstrates how to authenticate with the Databricks landing zone storage account
and list files in the **needs** folder using dbutils:

.. code-block:: python

    ENV = "prod"
    STORAGE_ACC = f"dbxingestzone{ENV}"
    CONTAINER = "pace"

    # Get secrets from keyvault attached to Databricks
    client_secret = dbutils.secrets.get(scope = 'rdd', key = 'job-spn-client-secret')
    client_id = dbutils.secrets.get(scope = 'rdd', key = 'job-spn-client-id')

    spark.conf.set(f"fs.azure.account.auth.type.{STORAGE_ACC}.dfs.core.windows.net", "OAuth")
    spark.conf.set(f"fs.azure.account.oauth.provider.type.{STORAGE_ACC}.dfs.core.windows.net", "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider")
    spark.conf.set(f"fs.azure.account.oauth2.client.id.{STORAGE_ACC}.dfs.core.windows.net", client_id)
    spark.conf.set(f"fs.azure.account.oauth2.client.secret.{STORAGE_ACC}.dfs.core.windows.net", client_secret)
    spark.conf.set(f"fs.azure.account.oauth2.client.endpoint.{STORAGE_ACC}.dfs.core.windows.net", "https://login.microsoftonline.com/a6c60f0f-76aa-4f80-8dba-092771d439f0/oauth2/token")

    FOLDER = "needs/"
    files = dbutils.fs.ls(f"abfss://{CONTAINER}@{STORAGE_ACC}.dfs.core.windows.net/{FOLDER}")
    print(f"Number of files in the folder '{FOLDER}': {len(files)}")
    for file in files:
        file_size_mb = file.size / (1024 * 1024)  # Convert bytes to MB
        print(f"{file.name} {file_size_mb:.2f} MB")


Data Transformation Process in Databricks
=========================================

The Data Transformation Process in Databricks refines raw *needs.json* data into a structured format suitable for the Knowledge Graph.

This transformation follows a medallion architecture, consisting of three layers: Bronze, Silver, and Gold.

Each layer plays a specific role in processing the data. Below, we provide an overview of each layer, starting with the Bronze layer.

Bronze layer
------------

The bronze layer is responsible for ingesting raw data from the storage account into Databricks. This layer retains the data as received from the source,
with minimal processing applied.

In order to get the data from storage account into Databricks, a `needs bronze bundle <https://github.com/PACE-INT/pace-databricks-infra/tree/main/data-sources/needs>`_ is provided.

This uses `Auto Loader <https://docs.databricks.com/en/ingestion/cloud-object-storage/auto-loader/index.html>`_
and Structured Streaming to load the data into the bronze layer of the Delta Lake.

Refer to the `Needs Bronze script <https://github.com/PACE-INT/pace-databricks-infra/blob/main/data-sources/needs/extract_needs.py>`_ for implementation details.

The Needs Bronze table in Databricks stores each needs element as a single row. The content of the needs element is stored as a string.

Some additional metadata like *created_date*, *ingested_date* and *version* is also stored.

Refer to the `Needs Bronze Table <https://adb-****************.9.azuredatabricks.net/explore/data/bronze/needs/pace_needs?o=****************&activeTab=sample>`_ in Databricks to see sample data.

Silver layer
------------

The silver layer represents the next stage in the medallion architecture, where data from the bronze layer is cleaned and structured.

Once again, structured streaming is used to stream data from the bronze layer to the silver layer. This ensures the transformation process
can resume from the last successful operation without reprocessing the entire dataset.

The silver layer contains the cleaned up and transformed data from the bronze layer. Once again, structured streaming is used to stream
the data from bronze Layer, transform it and store in the Silver Layer.

The raw content column from the bronze layer is parsed and split into multiple structured columns.

Refer to the :github:`Needs Silver script <databricks/pipelines/ada_dataproduct_silver/needs/transform.py>` for implementation details.

Refer to the `Needs Silver Table <https://adb-****************.9.azuredatabricks.net/explore/data/silver/needs/pace_needs?o=****************>`_ in Databricks to see sample data.

Gold Layer
----------

The Gold Layer forms the final stage in the medallion architecture, containing fully processed and structured data ready for integration into the Knowledge Graph.

This layer refines data from the Silver Layer into two tables: *needs_properties* and *needs_edges*. This simplifies knowledge graph ingestion by following a
consistent schema.

1. **needs_properties**: Stores data properties associated with needs elements, such as titles, descriptions, or other metadata.
2. **needs_edges**: Stores object properties that define relationships between needs elements, such as relationships or connections to other elements.

Both gold layer tables have an identical schema as shown below. The schema for both tables ensures consistent representation
of properties and edges:

.. code-block:: python

    GOLD_DATA_SCHEMA = StructType(
        [
            StructField("start", StringType(), True),
            StructField("target", StringType(), True),
            StructField("type", StringType(), True),
            StructField("mapped_type", StringType(), True),
            StructField("version", StringType(), True),
        ]
    )

This schema captures both properties and edges in a consistent way.

The table below provides an example of a needs property:

+-----------------+---------------------------------------------------------------------------+
| **Field**       | **Value**                                                                 |
+=================+===========================================================================+
| **start**       | urn:pace:ontology:needs:REQ_SW_1_2b42cb3694764d4e17bc01f0c16ab6cc91e4955f |
+-----------------+---------------------------------------------------------------------------+
| **target**      | Some title                                                                |
+-----------------+---------------------------------------------------------------------------+
| **type**        | title                                                                     |
+-----------------+---------------------------------------------------------------------------+
| **mapped_type** | urn:pace:ontology:title                                                   |
+-----------------+---------------------------------------------------------------------------+
| **version**     | 2b42cb3694764d4e17bc01f0c16ab6cc91e4955f                                  |
+-----------------+---------------------------------------------------------------------------+

Likewise, the table below provides an example of a needs edge:

+-----------------+--------------------------------------------------------------------------------------+
| **Field**       | **Value**                                                                            |
+=================+======================================================================================+
| **start**       | urn:pace:ontology:needs:REQ_SYS_1_2b42cb3694764d4e17bc01f0c16ab6cc91e4955f           |
+-----------------+--------------------------------------------------------------------------------------+
| **target**      | urn:pace:ontology:needs:FEATURE_INCREMENT_1_2b42cb3694764d4e17bc01f0c16ab6cc91e4955f |
+-----------------+--------------------------------------------------------------------------------------+
| **type**        | belongs                                                                              |
+-----------------+--------------------------------------------------------------------------------------+
| **mapped_type** | urn:pace:ontology:belongsTo                                                          |
+-----------------+--------------------------------------------------------------------------------------+
| **version**     | 2b42cb3694764d4e17bc01f0c16ab6cc91e4955f                                             |
+-----------------+--------------------------------------------------------------------------------------+

The latest version of the `PACE ontology <https://jfrog.ad-alliance.biz/artifactory/shared-generic-dev-local/sphinx_needs_turtle/main/schema.ttl>`_ is used to
map raw data types (type) into ontology-specific terms (mapped_type).

Refer to the `Needs Gold Properties Table <https://adb-****************.9.azuredatabricks.net/explore/data/gold/ada_release_graph/needs_properties_v2?o=****************>`_ in Databricks to see sample data.
Refer to the `Needs Gold Edges Table <https://adb-****************.9.azuredatabricks.net/explore/data/gold/ada_release_graph/needs_edges_v2?o=****************>`_ in Databricks to see sample data.

.. note::

    Gold layer transformation is done only for certain github commits.
    These ingest commits are contained in the `gold.ada_release_graph.ingest_commits <https://adb-****************.9.azuredatabricks.net/explore/data/gold/ada_release_graph/ingest_commits?o=****************>`_ table.

Load to Knowledge Graph
-----------------------

The Needs Data is loaded into the Knowledge Graph from the gold layer, as described above.

We use the :ref:`stardog_stream_version_loader` to only load needs data of certain
Github commits to the Knowledge Graph.

These ingest commits are contained in the `gold.ada_release_graph.ingest_commits <https://adb-****************.9.azuredatabricks.net/explore/data/gold/ada_release_graph/ingest_commits?o=****************>`_ table.

The loader relies on an `SMS mapping <https://docs.stardog.com/virtual-graphs/mapping-data-sources#sms2-stardog-mapping-syntax-2>`_ and an associated Ontology to transform the data:

1. **SMS Mapping**: This file defines how relational data is mapped to RDF triples for integration into the Knowledge Graph.

2. **Ontology**: This defines the schema and semantics for the data, providing structure and meaning to the RDF triples being loaded into the Knowledge Graph.

* :github:`Needs Properties SMS mapping file <databricks/pipelines/ada_dataproduct_gold/release_graph/needs/resources/mapping_properties.j2>`

* :github:`Needs Edges SMS mapping file <databricks/pipelines/ada_dataproduct_gold/release_graph/needs/resources/mapping_edges.j2>`

* `Needs Ontology file <https://jfrog.ad-alliance.biz/artifactory/shared-generic-dev-local/sphinx_needs_turtle/main/schema.ttl>`_
