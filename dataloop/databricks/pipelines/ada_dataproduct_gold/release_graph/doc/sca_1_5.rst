.. ============================================================================================================
   C O P Y R I G H T
   ------------------------------------------------------------------------------------------------------------
   \copyright (C) 2024-2025 Robert <PERSON> and Cariad SE. All rights reserved.
   ============================================================================================================

.. _sca_1_5:

Static Code Analysis (V1.5)
#################################

.. contents::
  :depth: 5

Overview
========

This chapter describes the ingest workflow for the Static Code Analysis (SCA) data.
It uses Structured Streaming to ingest the data incrementally into a Knowledge Graph platform.
For better understanding, each component of the workflow is explained in the following sections.

Data Source
-----------

The SCA data is produced by the Gatekeeper workflow of the PACE repository and then published
via `pace-metrics <https://pace-docs.azurewebsites.net/dataloop/core/main/examples/databricks/pace_metrics_guide.html>`_.

Data Transformation Process in Databricks
=========================================

The Data Transformation Process in Databricks is central to the Test Reports data ingest,
which processes and refines raw data into a structured format for the Knowledge Graph.
Every stage of it is thoroughly detailed in the sections that follow.

Bronze layer
------------

In order to get the data from pace-metrics into Databricks, we provide a bundle that uses an `Auto Loader <https://docs.databricks.com/en/ingestion/cloud-object-storage/auto-loader/index.html>`_
and Structured Streaming to load the data into the Bronze layer of the Delta Lake.
This layer is used to store the raw data as it is received from the source.
Check the related `pace-metrics Bronze guide <https://pace-docs.azurewebsites.net/dataloop/core/main/examples/databricks/pace_metrics_guide.html#step-1-create-a-bronze-layer-in-databricks>`_ for more information.

Silver layer
------------

The Silver layer contains the cleaned up and transformed data from the Bronze layer.
A schema is applied to the raw data to give it a structure and allow other downstream workflows to use it.
Check the related `pace-metrics Silver guide <https://pace-docs.azurewebsites.net/dataloop/core/main/examples/databricks/pace_metrics_guide.html#step-2-create-a-silver-layer-in-databricks>`_ for more information.

Load to Knowledge Graph
-----------------------

The SCA data does not need any additional transformation or aggregations and therefore the
Silver layer data can be directly loaded into the Knowledge Graph.
We use the :ref:`stardog_stream_version_loader` to only load the SCA data of certain
Github commits to the Knowledge Graph.
These ingest commits are contained in the `gold.ada_release_graph.ingest_commits <https://adb-8617216030703889.9.azuredatabricks.net/explore/data/gold/ada_release_graph/ingest_commits?o=8617216030703889>`_ table.

The loader requires an `SMS mapping <https://docs.stardog.com/virtual-graphs/mapping-data-sources#sms2-stardog-mapping-syntax-2>`_ and a corresponding ontology.
The mapping file defines how the relational data is transformed to RDF triples in the Knowledge Graph and the ontology describes the semantics of the data.

* :github:`SCA SMS mapping file <databricks/pipelines/ada_dataproduct_gold/release_graph/sca/resources/mapping.j2>`

* :github:`SCA ontology file <databricks/pipelines/ada_dataproduct_gold/release_graph/sca/resources/metamodel.ttl>`
