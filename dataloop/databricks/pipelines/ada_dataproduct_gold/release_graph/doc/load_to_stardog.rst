.. ============================================================================================================
   C O P Y R I G H T
   ------------------------------------------------------------------------------------------------------------
   \copyright (C) 2024-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
   ============================================================================================================

.. _load_to_stardog:

Load to Stardog
###############

Overview
========

This chapter describes the loading of data from Databricks tables to Stardog graph databases.
The basic process and use case specific additions are implemented in a few loader classes which can be found in the
:github:`common.load package <databricks/pipelines/ada_dataproduct_gold/release_graph/common/load>` of the ``release_graph`` bundle.
We explain these basic loader classes and the process itself below.

Basic Loader classes
====================

.. note::
   Most of the following classes can be subclassed to extend their functionality.
   However, you can also directly call the files they are implemented in as scripts.
   Execute them with the ``--help`` flag for a description of their command line interfaces.

.. _stardog_loader:

:github:`StardogLoader <databricks/pipelines/ada_dataproduct_gold/release_graph/common/load/stardog_loader.py>`
---------------------------------------------------------------------------------------------------------------

This is the basic class for loading data from Databricks tables to Stardog.
The general load process is implemented in the ``load`` method.
This method receives arbitrary keyword arguments which are passed down to the ``_filter_source_table`` method (described below).

.. warning::
   This implementation of the load process doesn't provide any load balancing support.
   It **will** fail if you try to ingest too much data!
   Consider using the ``StardogBatchLoader`` for potentially large amounts of data.

1. Initialize the Stardog database
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

It starts by retrieving the connection options for the Stardog instance corresponding to the current Databricks environment
and then tries to initialize the target Stardog database.
First, the database is created if it does not exist.
Then namespace prefixes as well as an ontology from optionally provided turtle files are imported.

2. Prepare the source data
^^^^^^^^^^^^^^^^^^^^^^^^^^

After the graph database initialization, the source Databricks table is read to a Spark dataframe.
Arbitrary filters and transformations can then be applied by overriding the ``_filter_source_table`` method in a subclass.
This method receives the source dataframe and all the arguments given to the ``load`` method which can be used to parameterize these operations.
It is expected to return a dataframe.

.. note::
   The default implementation of ``_filter_source_table`` does nothing and just passes through the input dataframe.
   There are some shorter execution paths for the actual loading to Stardog available, which don't require the creation of a temporary delta table,
   if this method isn't overridden.
   Their conditions are mentioned in the respective sections.


2. Load data to Stardog database
^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^

Before starting the actual loading to the graph database, a required SMS mapping template file is loaded.
This is required, as we are using virtual graph materialization to load the data to Stardog.
For further information about how mapping from relational databases to graph databases is supported by Stardog,
checkout their documentation about `Mapping Data Sources <https://docs.stardog.com/virtual-graphs/mapping-data-sources>`_.
As the data is pulled by Stardog using SQL queries rather than being pushed from Databricks, a JDBC connector
to a SQL warehouse is required.
The credentials for such a connector are therefore automatically retrieved from the RDD keyvault.

Now because Stardog can only do the virtual graph materialization from a Databricks table in the Unity catalog,
all the data from the source dataframe is written to a temporary table.
This table is stored in the log schema which is required when initializing the loader object.
After the data hast been loaded to the graph database, the table is dropped again.
This also happens if an error occurs while loading.

.. warning::
   In case the compute cluster is unexpectedly failing or the job is being force cancelled,
   the temporary table might not be deleted!

.. note::
   If the ``_filter_source_table`` isn't overridden, a short execution path is used instead and the
   data is directly loaded from the existing source table and no temporary table is created.

.. _stardog_batch_loader:

:github:`StardogBatchLoader <databricks/pipelines/ada_dataproduct_gold/release_graph/common/load/stardog_batch_loader.py>`
--------------------------------------------------------------------------------------------------------------------------

This class derives from the ``StardogLoader`` and extends it by splitting the source data into batches
which are loaded sequentially to Stardog.

.. note::
   This implementation should in theory be able to handle any amount of data.
   However, there might be some limitations on Stardog side we are not fully aware of.

The batches are created by first partitioning the source dataframe into a specific number of partitions.
This number is automatically calculated such that the resulting batches are always smaller or equal to
the ``MAX_BATCH_SIZE`` class constant.
You may override this constant in a subclass.
The default is 100'000.
Each batch is then loaded using the same process as described in the ``StardogLoader`` base class.

.. note::
   If the ``_filter_source_table`` isn't overridden and the number of batches is equal to 1,
   a short execution path is used instead and the data is directly loaded from the existing source table
   and no batches and temporary tables are created.

.. _stardog_stream_loader:

:github:`StardogStreamLoader <databricks/pipelines/ada_dataproduct_gold/release_graph/common/load/stardog_stream_loader.py>`
----------------------------------------------------------------------------------------------------------------------------

This class derives from the ``StardogLoader`` and extends it with support for incremental updates
to the Stardog database using `Spark Structured Streaming <https://spark.apache.org/docs/3.5.1/structured-streaming-programming-guide.html>`_.

.. warning::
   This implementation of the load process doesn't provide any load balancing support.
   It **will** fail if you try to ingest to much data which mostly might happen during
   initial executions.

Instead of reading the source table as a regular dataframe, it is treated as a streaming table.
This allows the Spark to detect which rows have already been processed and only process new ones.
For that, checkpoints are required that are stored in a volume in the log schema.
This volume is expected to be named ``checkpoint_locations`` and must already be present in the Unity catalog!
The checkpoints related to loading to Stardog will be placed in directories prefixed with *stardog_load_*.
The checkpoint location may be altered by overriding the ``_get_checkpoint_path`` method in a subclass.

.. _stardog_stream_version_loader:

:github:`StardogStreamVersionLoader <databricks/pipelines/ada_dataproduct_gold/release_graph/common/load/stardog_stream_version_loader.py>`
-------------------------------------------------------------------------------------------------------------------------------------------

This class derives from the ``StardogStreamLoader`` and alters the loading process to filter the data based on
a version table.

.. note::
   This loader contains an abstract method ``_filter_by_versions`` which must be implemented in a subclass.
   Therefore, the containing script cannot be executed directly.

This loader reads the version table as a streaming table and and the source table as a regular dataframe.
The ``filter_by_versions`` method is required to apply the filtering logic to the source dataframe
and must be implemented in a subclass.
It should return a streaming dataframe containing only the rows that should be loaded to Stardog.

API
===

.. autoapimodule:: release_graph.common.load
   :show-inheritance:
   :members:
