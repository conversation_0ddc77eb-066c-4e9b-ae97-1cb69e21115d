.. ============================================================================================================
   C O P Y R I G H T
   ------------------------------------------------------------------------------------------------------------
   \copyright (C) 2024-2025 Robert <PERSON> and Cariad SE. All rights reserved.
   ============================================================================================================

.. _test_concept:

Test Concept
############

.. contents::
  :depth: 5

Overview
========

In order to produce validated data, a proper test concept is essential.
This chapter describes the test concept for the ingest v2 pipelines.
We implement and use the following types of tests to ensure correct and reliable workflows
as well as high data quality:

* Unit Tests (to test individual functions and classes)
* End-to-End Tests (to test the correct operation of an entire pipeline)

In the following, we describe the implementation of each test type in detail.

Unit Tests
----------

Unit tests are used to test the behavior of individual functions and classes to different inputs.
All unit tests are implemented using the `pytest <https://docs.pytest.org/en/stable/>`_ framework.
The tests of a bundle are located in the ``tests`` directory of the respective pipeline.
The directory structure of the tests should mirror the structure of the bundle code
and the test files should have the same name as the code files they are testing with the prefix ``test_``.
The test functions should have the same name as the functions they are testing with the prefix ``test_``
and a suffix that describes the test case (e.g. ``test_update_table_is_empty``).

.. note::
   Unit test are executed in a local Spark instance.
   Therefore, they require the ``pyspark`` rather than the ``databricks-connect`` package to be installed.
   It is recommended to use two separate virtual environments for local and remote execution.

Example
~~~~~~~

The following example shows the directory structure of the tests for an ``example_bundle`` bundle:

.. code-block:: bash

   example_bundle
   ├── common/
   │   ├── constants.py
   │   ├── enums.py
   │   └── extract_base.py
   ├── extract_data.py
   ├── tests/
   |   ├──common/
   │   |   ├── tests_constants.py
   │   |   ├── tests_enums.py
   │   |   └── tests_extract_base.py
   │   └── tests_extract_base.py

Notes on Unit Tests
~~~~~~~~~~~~~~~~~~~

* Only test what you own. Unit tests should only test the code that you have written.
  Do not test external libraries or functions that are not part of your code.
* Do not write the same test twice.
  If you have the same function in multiple bundles, generalize it and write a single test for it.
* Do not write tests for the sake of writing tests.
  Only write tests that are necessary to ensure the correctness of your code.

End-to-End Tests
----------------

End-to-End tests are responsible for testing the correct operation of an entire pipeline.
They are executed in a Databricks environment but use a reduced set of test data rather than the production data.
All end-to-end tests start with this initial test data and run all workflows related to a specific data source or use case.
After the execution, the resulting data is checked for correctness.
If the results are correct, the test is considered successful.
Before and after each test run, all test data is deleted to ensure that the next test run is not influenced by a previous one.

In the following, we describe the specific end-to-ends tests that are implemented for the data sources and the release graph
of the ingest v2 pipelines.

Data Source Tests
~~~~~~~~~~~~~~~~~

A data source is a data set coming from a specific source tool or system (e.g. needs or pace-metrics).
They are (mostly) independent of each other and can be used for different use cases.
The pipeline of a data source consists of the following steps:

1. Load raw data from the ingest zone to the Bronze layer
2. Transform the raw Bronze data to cleaned up Silver data

In order to test the first step, we provide a test container that contains some handpicked test data (for qualitative checks).
as well as some of the latest data from the production container (for quantitative checks).
The second kind of test data is copied from the production container at the start and removed after the test run.

To validate the correctness of the data, we use SQL queries to compare the resulting Bronze and Silver data
with the raw test data as well as specific assumptions.

Release Graph Tests
~~~~~~~~~~~~~~~~~~~

The pipelines ingesting data to the release graph are tested a bit differently than the data source pipelines.
They might create intermediate Gold tables but can also directly load Silver data to the release graph.
The pipeline of a release graph ingest consists of the following steps:

1. (Optional) Create intermediate Gold data from Silver layer
2. Load Silver or Gold data to the release graph

The initial test data contains handpicked (for qualitative checks) as well as latest production data (for quantitative checks).
It is provided as Silver tables to be use by the pipeline.

If the pipeline creates intermediate Gold tables, we use SQL queries to compare the resulting Gold data with the raw test data
as well as specific assumptions.
The result data present in the release graph is validated using SPARQL queries and also uses qualitative as well as quantitative checks.
