"""Constants for github gold transform."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import StringType, StructField, StructType, TimestampType
from rddlib import get_dbx_env_catalog

SILVER_DBX_CATALOG = get_dbx_env_catalog("silver")
GOLD_DBX_CATALOG = get_dbx_env_catalog("gold")
DBX_SCHEMA = "ada_release_graph"
VEHICLE_PERFORMANCE_COMMITS_TABLE = "vehicle_performance_commits"
INGEST_COMMITS_TABLE = "ingest_commits"

SCHEMA = StructType(
    [
        StructField("commit_sha", StringType()),
        StructField("processed_at", TimestampType()),
        StructField("tag", StringType()),
    ]
)

DESCRIPTION_INGEST_COMMITS = (
    "Commits from PACE repository for ADA release graph ingestion"
    " which have all release relevant artifacts."
    " Managed by the RDD Team."
)

DESCRIPTION_VEHICLE_PERFORMANCE_COMMITS = (
    "Commits from PACE repository from nightly vehicle performance workflow. Managed by the RDD Team."
)

DBFS_FILE_PATH_CONTINUE_FLAG = "dbfs:/tmp/rdd_orchestrator_trigger_condition/continue_flag.txt"
