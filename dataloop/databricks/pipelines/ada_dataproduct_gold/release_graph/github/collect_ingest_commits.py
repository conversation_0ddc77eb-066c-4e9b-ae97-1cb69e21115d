"""Collect commit SHAs for ingestion into Release Graph."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging

from constants import (
    DBFS_FILE_PATH_CONTINUE_FLAG,
    DBX_SCHEMA,
    DESCRIPTION_INGEST_COMMITS,
    GOLD_DBX_CATALOG,
    INGEST_COMMITS_TABLE,
    SCHEMA,
    SILVER_DBX_CATALOG,
    VEHICLE_PERFORMANCE_COMMITS_TABLE,
)
from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import any_value, array_contains, col, collect_set, expr, max, size
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import setup_databricks_logging

logger = logging.getLogger(__name__)


# Spark session is provided by databricks
spark: SparkSession = SparkSession.builder.getOrCreate()


def get_commit_list(batch_df: DataFrame) -> list[str]:
    """Extracts a list of distinct commit SHAs from the given batch DataFrame.

    Args:
        batch_df (DataFrame): The input DataFrame containing commit SHAs.

    Returns:
        list[str]: A list of unique commit SHAs.
    """
    return [row.commit_sha for row in batch_df.select("commit_sha").distinct().collect()]


def get_needs_data(commit_list: list[str]) -> DataFrame:
    """Retrieves and filters the needs data for the given list of commits.

    Args:
        commit_list (list[str]): A list of commit SHAs to filter the needs data.

    Returns:
        DataFrame: A filtered DataFrame containing relevant needs data for the given commits.
    """
    needs_df = spark.read.table(f"{SILVER_DBX_CATALOG}.needs.pace_needs")
    return needs_df.filter(needs_df.version.isin(commit_list)).dropDuplicates(["version"])


def get_sca_data(commit_list: list[str]) -> DataFrame:
    """Retrieves and filters SCA data for the given list of commits.

    Args:
        commit_list (list[str]): A list of commit SHAs to filter the SCA data.

    Returns:
        DataFrame: A filtered DataFrame containing relevant SCA data for the given commits.
    """
    sca_df = spark.read.table(f"{SILVER_DBX_CATALOG}.pace_metrics.bazel_package_metrics")
    return sca_df.filter(sca_df.revision.isin(commit_list)).dropDuplicates(["revision"])


def check_basic_system_test_status(commits_df: DataFrame) -> DataFrame:
    """Checks if all system test statuses for each commit in `basic_system_test_report` are valid.

    1. Filters `basic_system_test_report` to include only relevant commits.
    2. Groups by `commit` to collect all unique statuses and track the latest `report_creation_time`.
    3. Filters out commits where a `pending` status exists within the last 24 hours.
    4. Keeps commits where `pending` is older than 24 hours or not present.

    Args:
        commits_df (DataFrame): A DataFrame containing commit SHAs.

    Returns:
        DataFrame: A DataFrame containing only valid commits where all trace IDs have completed tests
        or pending statuses older than 24 hours.
    """

    # Read the system test report (which contains `commit`, `trace_id`, `status`, `report_creation_time`)
    system_test_df = spark.read.table(f"{SILVER_DBX_CATALOG}.pace_metrics.basic_system_test_report")

    # Compute the 24-hour threshold
    time_threshold = expr("current_timestamp() - INTERVAL 24 HOURS")

    # Filter system test report to only include relevant commits
    filtered_test_df = (
        system_test_df.join(commits_df, system_test_df.commit == commits_df.commit_sha, "inner")
        .groupBy("commit")
        .agg(
            collect_set("status").alias("status_set"),
            max("report_creation_time").alias("latest_report_time"),  # Get the latest test report timestamp
        )
        .filter(
            (size(col("status_set")) > 0)  # Exclude empty status sets
            & ((~array_contains(col("status_set"), "pending")) | (col("latest_report_time") < time_threshold))
        )
    )

    return filtered_test_df


def filter_valid_commits(
    batch_df: DataFrame, needs_data: DataFrame, sca_data: DataFrame, basic_system_test_check: DataFrame
) -> DataFrame:
    """Filters and returns commits that meet all necessary conditions for ingestion.

    Args:
        batch_df (DataFrame): The input DataFrame containing commits to be validated.
        needs_data (DataFrame): DataFrame containing needs data for commits.
        sca_data (DataFrame): DataFrame containing SCA data for commits.
        basic_system_test_check (DataFrame): DataFrame containing commits that have passed evaluation checks.

    Returns:
        DataFrame: A filtered DataFrame containing only valid commits that exist in all three datasets.
    """
    return (
        batch_df.join(needs_data, batch_df.commit_sha == needs_data.version, "inner")
        .join(sca_data, batch_df.commit_sha == sca_data.revision, "inner")
        .join(basic_system_test_check, batch_df.commit_sha == basic_system_test_check.commit, "inner")
    )


def merge_into_table(valid_commits: DataFrame, table: DeltaTable) -> None:
    """Merges valid commits into an existing Delta table.

    Args:
        valid_commits (DataFrame): A DataFrame containing commit SHAs, `processed_at` timestamps, and tag.
        table (DeltaTable): The target Delta table where the data will be merged.

    Raises:
        ValueError: If the schema of `valid_commits` does not match the expected `SCHEMA`.

    Returns:
        None
    """
    from pyspark.dbutils import DBUtils

    dbutils = DBUtils(spark)
    if valid_commits.isEmpty():
        logger.info("No valid commits, skipping insertion.")
        dbutils.fs.put(DBFS_FILE_PATH_CONTINUE_FLAG, "false", overwrite=True)
        return

    valid_commits = valid_commits.groupBy("commit_sha").agg(
        max("processed_at").alias("processed_at"),
        any_value("tag").alias("tag"),
    )

    # Validate schema
    if valid_commits.schema != SCHEMA:
        raise ValueError(f"Schema mismatch. Expected: {SCHEMA}, got: {valid_commits.schema}")

    # Perform merge
    (
        table.alias("t")
        .merge(valid_commits.alias("s"), "s.commit_sha = t.commit_sha")
        .whenNotMatchedInsertAll()
        .execute()
    )

    dbutils.fs.put(DBFS_FILE_PATH_CONTINUE_FLAG, "true", overwrite=True)


def update_ingest_commits() -> None:
    """Processes and updates ingest commits."""
    table = f"{GOLD_DBX_CATALOG}.{DBX_SCHEMA}.{INGEST_COMMITS_TABLE}"

    if not dtu.table_exists(table):
        logger.info(f"Creating empty table {table}")
        spark.createDataFrame([], SCHEMA).write.format("delta").mode("overwrite").saveAsTable(table)

    exist_table = DeltaTable.forName(spark, table)

    vehicle_commits_df = spark.read.table(f"{GOLD_DBX_CATALOG}.{DBX_SCHEMA}.{VEHICLE_PERFORMANCE_COMMITS_TABLE}")
    ingest_commits_df = spark.read.table(f"{GOLD_DBX_CATALOG}.{DBX_SCHEMA}.{INGEST_COMMITS_TABLE}")

    new_commits_df = vehicle_commits_df.join(ingest_commits_df, "commit_sha", "left_anti")

    commit_list = get_commit_list(new_commits_df)
    needs_data = get_needs_data(commit_list)
    sca_data = get_sca_data(commit_list)
    basic_system_test_check = check_basic_system_test_status(new_commits_df)

    valid_commits = filter_valid_commits(new_commits_df, needs_data, sca_data, basic_system_test_check)
    logger.info(f"Valid commits count after filtering: {valid_commits.count()}")

    merge_into_table(valid_commits, exist_table)

    dtu.update_table_metadata(table, DESCRIPTION_INGEST_COMMITS)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Collect commit SHAs for ingestion into Release Graph.")
    parser.add_argument("-r", "--run_id", type=str, default=None, dest="run_id")
    argv = parser.parse_args()

    setup_databricks_logging(
        FullSchemaName(GOLD_DBX_CATALOG, DBX_SCHEMA, False), "ada_release_graph/gold", run_id=argv.run_id
    )

    update_ingest_commits()
