"""Collect commit SHAs processed by the nightly vehicle performance workflow."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging

from constants import (
    DBX_SCHEMA,
    DESCRIPTION_VEHICLE_PERFORMANCE_COMMITS,
    GOLD_DBX_CATALOG,
    SILVER_DBX_CATALOG,
    VEHICLE_PERFORMANCE_COMMITS_TABLE,
)
from pyspark.sql import SparkSession
from pyspark.sql.functions import col, expr
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import setup_databricks_logging

logger = logging.getLogger(__name__)


# Spark session is provided by databricks
spark: SparkSession = SparkSession.builder.getOrCreate()


def get_checkpoint_path(catalog: str, schema: str, table: str) -> str:
    """Constructs checkpoint path for Delta tables."""
    return f"/Volumes/{catalog}/{schema}/checkpoint_locations/{table}/"


def update_vehicle_performance_commits() -> None:
    """Extracts and inserts new vehicle performance commits."""
    workflow_run_df = spark.readStream.table(f"{SILVER_DBX_CATALOG}.pace_metrics.github_workflow_run")

    result_df = workflow_run_df.select(
        col("workflow_run.head_sha").alias("commit_sha"),
        col("workflow_run.head_branch").alias("tag"),
        col("workflow_run.created_at").alias("processed_at"),
    ).where(
        (col("workflow.path") == ".github/workflows/workflow_vehicle_performance.yaml")
        & (col("tag").contains("scheduled-dispatch-release-automated-vehicle-ready"))
        & (col("action") == "completed")
        & (expr("HOUR(processed_at) = 0"))  # Keep only scheduled runs at 00:00
    )

    checkpoint_path = get_checkpoint_path(GOLD_DBX_CATALOG, DBX_SCHEMA, VEHICLE_PERFORMANCE_COMMITS_TABLE)
    table = f"{GOLD_DBX_CATALOG}.{DBX_SCHEMA}.{VEHICLE_PERFORMANCE_COMMITS_TABLE}"

    query = (
        result_df.writeStream.trigger(availableNow=True)
        .option("checkpointLocation", checkpoint_path)
        .option("mergeSchema", "true")
        .toTable(table, format="delta", outputMode="append")
    )

    query.awaitTermination()

    dtu.update_table_metadata(table, DESCRIPTION_VEHICLE_PERFORMANCE_COMMITS)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(
        description="Collect commit SHAs processed by the nightly vehicle performance workflow."
    )
    parser.add_argument("-r", "--run_id", type=str, default=None, dest="run_id")
    argv = parser.parse_args()

    setup_databricks_logging(
        FullSchemaName(GOLD_DBX_CATALOG, DBX_SCHEMA, False), "ada_release_graph/gold", run_id=argv.run_id
    )

    update_vehicle_performance_commits()
