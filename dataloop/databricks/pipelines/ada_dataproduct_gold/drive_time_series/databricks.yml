# This is a Databricks asset bundle definition.
# Notes:
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.
bundle:
  name: drive_time_series_gold
permissions:
  - group_name: "sg-pace-github-Analytics_Platform-developer"
    level: CAN_VIEW

include:
  - resources/*.yml

variables:
  run_sp:
    description: The ID of the drive_time_series DEV Service Principle that will be used for the jobs
    default: 3ee9fc25-7c21-49f6-93a5-ab99ad69bf8b
  sp_pace_drive_time_series_dev:
    description: The ID of the drive_time_series DEV Service Principle that will be used for the jobs
    default: 3ee9fc25-7c21-49f6-93a5-ab99ad69bf8b # sp-pace-dataloop-drive-time-series-dev
  sp_pace_drive_time_series_qa:
    description: The ID of the drive_time_series QA Service Principle that will be used for the jobs
    default: 9224096f-c94d-480c-bd00-edf46f5d807a # sp-pace-dataloop-drive-time-series-qa
  sp_pace_drive_time_series_prod:
    description: The ID of the drive_time_series PROD Service Principle that will be used for the jobs
    default: add9497a-c488-4228-a10f-e26031231c5b # sp-pace-dataloop-drive-time-series-prod

targets:
  user-dev:
    variables:
      run_sp: 3ee9fc25-7c21-49f6-93a5-ab99ad69bf8b # sp-pace-dataloop-drive-time-series-dev
      bronze_catalog: bronze_dev
      gold_catalog: dd_unicorn_sbx
      instance_pool_id: "1001-074540-notes282-pool-y1atnfsi" # default_D16ds_v4_rt14.3 on datalake-dev
      warehouse_id: 28c417ff7d45ff63 # Serverless 2X-Small on datalake-dev
      num_workers: 2
    mode: development
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/

  dev:
    variables:
      run_sp: 3ee9fc25-7c21-49f6-93a5-ab99ad69bf8b # sp-pace-dataloop-drive-time-series-dev
      bronze_catalog: bronze_dev
      silver_catalog: silver_dev
      gold_catalog: gold_dev
      instance_pool_id: "1001-074540-notes282-pool-y1atnfsi" # default_D16ds_v4_rt14.3 on datalake-dev
      warehouse_id: 28c417ff7d45ff63 # Serverless 2X-Small on datalake-dev
      num_workers: 2
    mode: production
    default: false
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}

  qa:
    variables:
      run_sp: 9224096f-c94d-480c-bd00-edf46f5d807a # sp-pace-dataloop-drive-time-series-qa
      bronze_catalog: bronze_qa
      silver_catalog: silver_qa
      gold_catalog: gold_qa
      instance_pool_id: "1001-074300-silk277-pool-sb79cecy"
      warehouse_id: 7361bd5894047294 # Serverless 2X-Small on datalake-qa
      num_workers: 2
    mode: production
    default: false
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}

  prod:
    variables:
      run_sp: add9497a-c488-4228-a10f-e26031231c5b # sp-pace-dataloop-drive-time-series-prod
      bronze_catalog: bronze
      silver_catalog: silver
      gold_catalog: gold
      target_schema: drive_time_series
      instance_pool_id: "1001-075544-waltz326-pool-p8g0oxpu"
      warehouse_id: 27651d57b3d114ed # Serverless 2X-Small on datalake-prod
      num_workers: 2
      schedule_pause_status: UNPAUSED
    mode: production
    default: false
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
