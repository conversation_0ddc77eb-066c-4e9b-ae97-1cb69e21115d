"""Tests for primary_situation_schema module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import re
import unittest
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from primary_situation_schema import (
    ColumnData,
    ColumnMetadata,
    _check_and_drop_columns_once,
    _column_exists_in_table,
    _column_exists_in_table_to_drop,
    create_situation_dataset_tbl,
    parse_job_parameters,
)
from pyspark.sql import DataFrame, SparkSession


@pytest.fixture(scope="session")
def mock_spark_session(spark_session: SparkSession) -> MagicMock:
    """Mock spark session for testing."""

    mock_spark_session = MagicMock()

    def side_effect(sql_query: str) -> DataFrame | None:
        # removing cluster by condition, does not work.
        if "ALTER TABLE" in sql_query:
            return None
        new_query = re.sub(r"CLUSTER BY\s*\(.*?\)", "", sql_query)
        return spark_session.sql(new_query)

    mock_spark_session.sql.side_effect = side_effect

    mock_table = MagicMock()
    mock_spark_session.table.return_value = mock_table

    return mock_spark_session


def test_create_trajectory_frames_tbl(mock_spark_session: SparkSession) -> None:
    """Test create_tbl function."""

    create_situation_dataset_tbl(mock_spark_session)
    tables = mock_spark_session.sql("SHOW TABLES").collect()
    assert "situation_dataset_full" in [
        table.tableName for table in tables
    ], "Table: situation_dataset_full not created"


def test_column_exists_in_table_to_drop(mock_spark_session: SparkSession) -> None:
    """Test _column_exists_in_table function."""
    table_name = "test_table"
    column_data = [
        ColumnData(Colname="col1"),
        ColumnData(Colname="col2"),
    ]

    # Simulating the existing columns in the table
    mock_spark_session.table(table_name).columns = ["col1", "col2", "col3"]

    # Call the function
    existing_columns = _column_exists_in_table_to_drop(mock_spark_session, table_name, column_data)

    existing_column_names = [column.Colname for column in existing_columns]

    # Assert that the columns in the table match the expected ones
    assert existing_column_names == ["col1", "col2"]


def test_check_and_drop_columns_once(mock_spark_session: SparkSession) -> None:
    """Test the function that checks and drops columns in a table."""

    columns_to_drop = [
        ColumnData(Colname="col1"),
        ColumnData(Colname="col2"),
    ]
    table_name = "test_table"

    # Simulating the existing columns in the table
    mock_spark_session.table(table_name).columns = ["col1", "col2", "col3"]

    # Call the function
    drop_columns = _check_and_drop_columns_once(mock_spark_session, columns_to_drop, table_name)
    # Optionally, assert that the result is a mock DataFrame (you can check methods or attributes of the result)
    assert drop_columns is None, "column dropped"


def test_column_exists_in_table_all_columns_exist(mock_spark_session: SparkSession) -> None:
    """Test _column_exists_in_table function."""
    table_name = "test_table"
    column_metadata = [
        ColumnMetadata(name="col1", data_type="STRING", comment="Column 1"),
        ColumnMetadata(name="col2", data_type="INT", comment="Column 2"),
    ]

    # Simulating the existing columns in the table
    mock_spark_session.table(table_name).columns = ["col1", "col2", "col3"]

    # Call the function
    missing_columns = _column_exists_in_table(mock_spark_session, table_name, column_metadata)

    # Assert that no columns are missing
    assert missing_columns == []


def test_column_exists_in_table_some_columns_missing(mock_spark_session: SparkSession) -> None:
    """Test _column_exists_in_table if columns missing function."""
    # Mock data
    table_name = "test_table"
    column_metadata = [
        ColumnMetadata(name="col1", data_type="STRING", comment="Column 1"),
        ColumnMetadata(name="col2", data_type="INT", comment="Column 2"),
        ColumnMetadata(name="col4", data_type="FLOAT", comment="Column 4"),
    ]

    # Simulating the existing columns in the table
    mock_spark_session.table(table_name).columns = ["col1", "col3"]

    # Call the function
    missing_columns = _column_exists_in_table(mock_spark_session, table_name, column_metadata)
    # Assert that only 'col2' and 'col4' are missing
    assert len(missing_columns) == 2
    assert missing_columns[0].name == "col2"
    assert missing_columns[1].name == "col4"


def test_column_exists_in_table_no_columns_exist(mock_spark_session: SparkSession) -> None:
    """Test _column_exists_in_table if no columns exists function."""
    # Mock data
    table_name = "test_table"
    column_metadata = [
        ColumnMetadata(name="col1", data_type="STRING", comment="Column 1"),
        ColumnMetadata(name="col2", data_type="INT", comment="Column 2"),
    ]

    # Simulating no existing columns in the table
    mock_spark_session.table(table_name).columns = []

    # Call the function
    missing_columns = _column_exists_in_table(mock_spark_session, table_name, column_metadata)

    # Assert that all columns are missing
    assert len(missing_columns) == 2
    assert missing_columns[0].name == "col1"
    assert missing_columns[1].name == "col2"


class TestMain(unittest.TestCase):
    """Tests for main."""

    @patch("argparse.ArgumentParser.parse_known_args")
    def test_parse_job_parameters(self, mock_parse_known_args: Any) -> None:
        """Test parse_job_parameters."""
        mock_parse_known_args.return_value = (
            argparse.Namespace(
                target_catalog="target_catalog_value",
                target_schema="target_schema_value",
            ),
            [],
        )
        args = parse_job_parameters()
        self.assertEqual(args.target_catalog, "target_catalog_value")
        self.assertEqual(args.target_schema, "target_schema_value")
