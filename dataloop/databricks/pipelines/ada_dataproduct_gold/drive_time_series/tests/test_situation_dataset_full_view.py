"""Tests for schema evolution functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import re
import unittest
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from pyspark.sql import SparkSession
from situation_dataset_full_view import create_situation_dataset_view, parse_job_parameters


@pytest.fixture(scope="session")
def mock_spark_session(spark_session: SparkSession) -> MagicMock:
    """Mock spark session for testing."""

    mock_spark_session = MagicMock()

    def side_effect(sql_query: str) -> None:
        # Mocks to create temp views for running queries.
        if "CREATE OR REPLACE VIEW" in sql_query:
            spark_session.sql(
                """CREATE TEMP VIEW recordings AS
                SELECT
                    '2023-01-01' as recording_started_at,
                    'vin1' as vin,
                    'url1' as file_url
                """
            )
            spark_session.sql(
                "CREATE TEMP VIEW datamanagement_drive_info AS SELECT 'hash1' as file_hash, 'plate1' as plate"
            )
            spark_session.sql(
                """CREATE TEMP VIEW files_scenes AS
                SELECT
                    '2023-01-01' as recording_started_at,
                    'vin1' as vin,
                    '2023-01-01' as started_at,
                    'hash1' as file_hash,
                    'http://testing.hdf5' as file_url
                """
            )
            spark_session.sql("CREATE TEMP VIEW time_of_day AS SELECT 'hash1' as file_hash, 'day' as time_of_day")
            spark_session.sql(
                """
                CREATE TEMP VIEW trajectory_frames AS
                SELECT
                    '2024-07-30T10:35:00.000+00:00' as recording_started_at,
                    '2024-07-30T10:35:00.000+00:00' as recorded_at,
                    'vin1' as vin,
                    '2023-01-01' as scene_started_at,
                    'hash1' as file_hash,
                    'day' as time_of_day,
                    true as is_paved,
                    'Germany' as country,
                    'DEU' as iso_country_code_alpha3,
                    'Berlin' as city,
                    'street1' as street,
                    'category1' as road_category,
                    2 as lane_count,
                    60 as speed_limit,
                    true as is_roundabout,
                    false as is_tunnel,
                    false as is_ramp,
                    'xyz' as plate,
                    'abc123' as drive_hash,
                    'ab123' as split_hash,
                    null as latitude,
                    null as longitude
                """
            )
        elif "ALTER VIEW" in sql_query:
            return None

        new_query = re.sub(r"(\w+\.\w+)\.(\w+)", r"\2", sql_query)  # Convert multi-part to single-part namespace
        new_query = new_query.replace(
            "silver_mock.mdd.datamanagement_drive_info", "datamanagement_drive_info"
        )  # ignore test failure related to backticks and hypens
        new_query = new_query.replace(
            "CREATE OR REPLACE VIEW", "CREATE OR REPLACE TEMP VIEW"
        )  # To create temp view for testing purposes
        spark_session.sql(new_query)

    # Set the side_effect to the mock_spark_session
    mock_spark_session.sql.side_effect = side_effect
    return mock_spark_session


def test_create_situation_dataset_view_valid(mock_spark_session: SparkSession) -> None:
    """Test create_tbl function."""

    # The main purpose of this test is to verify the validity of the SQL query and ensure it passes SonarQube checks.
    # For example, it will fail if the query is changed to something invalid like "CREATE VIE".
    create_situation_dataset_view(mock_spark_session, "silver_mock", "gold_mock")

    assert mock_spark_session.sql.call_count == 2  # type: ignore


class TestMain(unittest.TestCase):
    """Tests for main."""

    @patch("argparse.ArgumentParser.parse_known_args")
    def test_parse_job_parameters(self, mock_parse_known_args: Any) -> None:
        """Test parse_job_parameters."""
        mock_parse_known_args.return_value = (
            argparse.Namespace(
                target_catalog="target_catalog_value",
                target_schema="target_schema_value",
            ),
            [],
        )
        args = parse_job_parameters()
        self.assertEqual(args.target_catalog, "target_catalog_value")
        self.assertEqual(args.target_schema, "target_schema_value")
