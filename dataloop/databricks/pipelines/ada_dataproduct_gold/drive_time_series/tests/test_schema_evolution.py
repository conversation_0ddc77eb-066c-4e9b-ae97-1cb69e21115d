"""Tests for schema evolution functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import re
import unittest
from typing import Any
from unittest.mock import MagicMock, patch

import pytest
from pyspark.sql import DataFrame, SparkSession
from trajectory_frames_schema import create_trajectory_frames_tbl, parse_job_parameters


@pytest.fixture(scope="session")
def mock_spark_session(spark_session: SparkSession) -> MagicMock:
    """Mock spark session for testing."""

    mock_spark_session = MagicMock()

    def side_effect(sql_query: str) -> DataFrame | None:
        # removing cluster by condition, does not work.
        if "ALTER TABLE" in sql_query:
            return None
        new_query = re.sub(r"CLUSTER BY\s*\(.*?\)", "", sql_query)
        return spark_session.sql(new_query)

    mock_spark_session.sql.side_effect = side_effect
    return mock_spark_session


def test_create_trajectory_frames_tbl(mock_spark_session: SparkSession) -> None:
    """Test create_tbl function."""

    create_trajectory_frames_tbl(mock_spark_session)
    tables = mock_spark_session.sql("SHOW TABLES").collect()
    assert "trajectory_frames" in [table.tableName for table in tables], "Table: trajectory_frames not created"


class TestMain(unittest.TestCase):
    """Tests for main."""

    @patch("argparse.ArgumentParser.parse_known_args")
    def test_parse_job_parameters(self, mock_parse_known_args: Any) -> None:
        """Test parse_job_parameters."""
        mock_parse_known_args.return_value = (
            argparse.Namespace(
                target_catalog="target_catalog_value",
                target_schema="target_schema_value",
            ),
            [],
        )
        args = parse_job_parameters()
        self.assertEqual(args.target_catalog, "target_catalog_value")
        self.assertEqual(args.target_schema, "target_schema_value")
