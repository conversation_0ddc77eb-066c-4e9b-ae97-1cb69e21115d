"""Configuration for Pytest fixtures."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import re
import shutil
import tempfile
from pathlib import Path
from typing import Generator
from unittest.mock import patch

import pytest
from delta import configure_spark_with_delta_pip
from delta.tables import DeltaTable
from pyspark.sql import DataFrame, Row, SparkSession
from tests.dataframe_mocks_gold import trajectory_frames_mock_rows


@pytest.fixture(scope="session")
def spark_session() -> Generator[SparkSession, None, None]:
    """Fixture to create a SparkSession."""

    logger = logging.getLogger("py4j")
    logger.setLevel(logging.WARN)

    with tempfile.TemporaryDirectory() as tmp_path:
        builder = (
            SparkSession.builder.master("local[2]")
            .appName("pytest-pyspark-local-testing")
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
            .config("spark.sql.legacy.createHiveTableByDefault", False)
            .config("spark.databricks.delta.schema.typeCheck.enabled", False)
            .config("spark.sql.warehouse.dir", Path(tmp_path) / "spark-warehouse")
        )
        spark = configure_spark_with_delta_pip(builder).getOrCreate()
        yield spark
        spark.stop()


class SpySparkSession(SparkSession):
    """A subclass of SparkSession that keeps track of queries executed on it."""

    query_history: list[str]


@pytest.fixture(scope="session")
def mock_spark_session1(spark_session: SparkSession) -> Generator[SpySparkSession, None, None]:
    """Mock SparkSession for testing using unittest's patch."""

    # keep a history of queries executed on the SparkSession
    spark_session.query_history = []

    # store the original sql method to avoid infinite recursion
    # when later patching the `sql` method
    original_sql = spark_session.sql

    def mocked_sql_with_side_effect(sql_query: str) -> DataFrame | None:
        spark_session.query_history.append(sql_query)
        # remove unsupported statements and clauses
        if "ALTER TABLE" in sql_query:
            return None
        if "ALTER VIEW" in sql_query:
            return None
        new_query = re.sub(r"CLUSTER BY\s*\(.*?\)", "", sql_query)
        return original_sql(new_query)

    # Patch the `sql` method on SparkSession
    with patch.object(spark_session, "sql", side_effect=mocked_sql_with_side_effect):
        yield spark_session


@pytest.fixture(scope="session")
def tempdir() -> Generator[str, None, None]:
    """Fixture to create a temporary directory for delta table resources."""

    print("Initial setup")
    resources_dir = tempfile.mkdtemp(prefix="pyspark-test-")
    yield resources_dir
    print(f"Final teardown {resources_dir}")
    shutil.rmtree(resources_dir)


def create_delta_table(spark_session: SparkSession, tempdir: str, table_name: str, data: list[Row]) -> DeltaTable:
    """Create a Delta table with the given data."""

    table_path = f"{tempdir}/{table_name}"
    df = spark_session.createDataFrame(data)
    df.write.format("delta").save(table_path)
    delta_table = DeltaTable.forPath(spark_session, path=table_path)
    return delta_table


@pytest.fixture(scope="session")
def target_table_trajectory_frames(spark_session: SparkSession, tempdir: str) -> DeltaTable:
    """Fixture to create a Delta table for frame_files."""
    return create_delta_table(spark_session, tempdir, "trajectory_frames", trajectory_frames_mock_rows)
