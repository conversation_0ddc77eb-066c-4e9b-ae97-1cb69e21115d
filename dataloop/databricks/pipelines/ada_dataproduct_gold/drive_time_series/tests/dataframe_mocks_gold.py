"""Dataframe mock data for testing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from datetime import datetime, timedelta, timezone
from typing import Final

from pyspark.sql import Row

# drive1
now = datetime.now(timezone.utc)
_DRIVE1_RECORDED_AT: Final = now - timedelta(hours=12)
_SCENE11_STARTED_AT: Final = now - timedelta(hours=11)
_ENTRY11_CREATED_AT: Final = now - timedelta(hours=6)

# drive2
_DRIVE2_RECORDED_AT: Final = now - timedelta(hours=12)
_SCENE21_STARTED_AT: Final = now - timedelta(hours=11)


file_scenes_mock_rows = [
    Row(
        file_hash="split_hash_scene11",
        vin="VIN1",
        recording_started_at=_DRIVE1_RECORDED_AT,
        started_at=_SCENE11_STARTED_AT,
    )
]


time_of_day_mock_rows = [
    Row(
        file_hash="split_hash_scene11",
        time_of_day="Day",
    )
]

frames_fc1_mock_rows = [
    Row(
        recorded_at=_ENTRY11_CREATED_AT,
        timestamp_closest_second=round(_DRIVE1_RECORDED_AT.timestamp()),
        frame_number=1,
        split_hash="split_hash_scene11",
        drive_hash="drive_hash11",
        distorted_image_file_hash="distorted_png_hash11",
        distorted_image_tds_file_url="distorted_png_tds_file_url11",
        label_image_file_hash="label_png_hash11",
        label_image_tds_file_url="label_png_tds_file_url11",
        preview_file_hash="preview_jpg_thumb_hash11",
        preview_tds_file_url="preview_jpg_thumb_tds_file_url11",
        vin="VIN1",
        recording_started_at=_DRIVE1_RECORDED_AT,
        scene_started_at=_SCENE11_STARTED_AT,
    )
]

frames_tvfront_mock_rows = [
    Row(
        recorded_at=_ENTRY11_CREATED_AT,
        timestamp_closest_second=round(_DRIVE1_RECORDED_AT.timestamp()),
        frame_number=1,
        split_hash="split_hash_scene11",
        drive_hash="drive_hash11",
        distorted_image_file_hash="distorted_png_hash12",
        distorted_image_tds_file_url="distorted_png_tds_file_url12",
        label_image_file_hash="label_png_hash12",
        label_image_tds_file_url="label_png_tds_file_url12",
        preview_file_hash="preview_jpg_thumb_hash12",
        preview_tds_file_url="preview_jpg_thumb_tds_file_url12",
        vin="VIN1",
        recording_started_at=_DRIVE1_RECORDED_AT,
        scene_started_at=_SCENE11_STARTED_AT,
    )
]

drive_trajectory_mock_rows = [
    Row(
        recorded_at=_DRIVE1_RECORDED_AT,
        latitude=1.0,
        longitude=1.0,
        country="Germany",
        city="Stuttgart",
        lane_count=2,
        speed_limit=50,
        is_roundabout=False,
        is_tunnel=False,
        is_ramp=False,
        timestamp_closest_second=round(_DRIVE1_RECORDED_AT.timestamp()) + round(_DRIVE1_RECORDED_AT.timestamp()),
        road_category="Urban",
        vin="VIN1",
        recording_started_at=_DRIVE1_RECORDED_AT,
        iso_country_code_alpha3="DEU",
        street="Street",
        is_paved=False,
    )
]

drive_timestamps_mock_rows = [
    Row(
        trj_recorded_at_min=_DRIVE1_RECORDED_AT,
        trj_recorded_at_max=_DRIVE1_RECORDED_AT,
        fc1_recorded_at_min=_DRIVE1_RECORDED_AT,
        fc1_recorded_at_max=_DRIVE1_RECORDED_AT,
        start_trj_ahead_of_fc1=round(_DRIVE1_RECORDED_AT.timestamp()),
        end_trj_ahead_of_fc1=round(_DRIVE1_RECORDED_AT.timestamp()),
        vin="VIN1",
        recording_started_at=_DRIVE1_RECORDED_AT,
        timestamp_closest_seconds=15,
    ),
]

drive_info_mock_rows = [
    Row(
        file_hash="drive_hash11",
        plate="Plate1",
    ),
]

# target tbl
trajectory_frames_mock_rows = [
    Row(
        recorded_at=_DRIVE2_RECORDED_AT,
        timestamp_closest_second=round(_DRIVE2_RECORDED_AT.timestamp()),
        vin="VIN2",
        recording_started_at=_DRIVE2_RECORDED_AT,
        scene_started_at=_SCENE21_STARTED_AT,
        frame_number=1,
        fc1_distorted_view="distorted_png_hash21",
        fc1_label_view="label_png_hash21",
        fc1_preview_url="preview_jpg_thumb_tds_file_url21",
        tvfront_distorted_view="distorted_png_hash22",
        tvfront_label_view="label_png_hash22",
        tvfront_preview_url="preview_jpg_thumb_tds_file_url22",
        tvleft_distorted_view="distorted_png_hash23",
        tvleft_label_view="label_png_hash23",
        tvleft_preview_url="preview_jpg_thumb_tds_file_url23",
        tvrear_distorted_view="distorted_png_hash24",
        tvrear_label_view="label_png_hash24",
        tvrear_preview_url="preview_jpg_thumb_tds_file_url24",
        tvright_distorted_view="distorted_png_hash25",
        tvright_label_view="label_png_hash25",
        tvright_preview_url="preview_jpg_thumb_tds_file_url25",
        time_of_day="Day",
        plate="Plate2",
        latitude=1.0,
        longitude=1.0,
        country="Germany",
        city="Stuttgart",
        lane_count=2,
        speed_limit=50,
        road_category="Urban",
        is_roundabout=False,
        is_tunnel=False,
        is_ramp=False,
        split_hash="split_hash_scene21",
        drive_hash="drive_hash21",
        iso_country_code_alpha3="DEU",
        street="Street",
        is_paved=False,
    )
]
