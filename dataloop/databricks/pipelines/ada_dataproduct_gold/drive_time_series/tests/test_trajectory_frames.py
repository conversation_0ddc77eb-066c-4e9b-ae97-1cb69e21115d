"""Tests for Drive time series pipeline."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from unittest.mock import MagicMock

import pytest
from delta.tables import DeltaTable
from pyspark.sql import DataFrame, SparkSession
from tests.dataframe_mocks_gold import (
    drive_info_mock_rows,
    drive_timestamps_mock_rows,
    drive_trajectory_mock_rows,
    file_scenes_mock_rows,
    frames_fc1_mock_rows,
    frames_tvfront_mock_rows,
    time_of_day_mock_rows,
)
from trajectory_frames import _get_cameras_joined, _get_frames_fc1_ts_corrected, get_data_for_trajectory_frames


@pytest.fixture(scope="session")
def mock_spark_session(spark_session: SparkSession) -> MagicMock:
    """Mock Spark session building and returning table mocks for testing."""
    mock_spark_session = MagicMock()

    def side_effect(tbl: str) -> DataFrame:
        fc1_mock_rows_df = spark_session.createDataFrame(frames_fc1_mock_rows)
        match tbl:
            case "silver_mock.drive_time_series.frames_tvrear":
                return spark_session.createDataFrame([], schema=fc1_mock_rows_df.schema)
            case "silver_mock.drive_time_series.frames_tvright":
                return spark_session.createDataFrame([], schema=fc1_mock_rows_df.schema)
            case "silver_mock.drive_time_series.frames_tvleft":
                return spark_session.createDataFrame([], schema=fc1_mock_rows_df.schema)
            case "silver_mock.drive_time_series.frames_tvfront":
                return spark_session.createDataFrame(frames_tvfront_mock_rows)
            case "silver_mock.drive_time_series.frames_fc1":
                return fc1_mock_rows_df
            case "silver_mock.drive_time_series.drive_timestamps":
                return spark_session.createDataFrame(drive_timestamps_mock_rows)
            case "silver_mock.mdd.time_of_day":
                return spark_session.createDataFrame(time_of_day_mock_rows)
            case "silver_mock.ada_ontology.files_scenes":
                return spark_session.createDataFrame(file_scenes_mock_rows)
            case "silver_mock.drive_time_series.drive_trajectory":
                return spark_session.createDataFrame(drive_trajectory_mock_rows)
            case "silver_mock.mdd.datamanagement_drive_info":
                return spark_session.createDataFrame(drive_info_mock_rows)
            case _:
                raise ValueError(f"Unexpected table name: {tbl}")

    mock_spark_session.table.side_effect = side_effect
    return mock_spark_session


@pytest.mark.usefixtures("mock_spark_session")
def test_get_frames_fc1_ts_corrected(mock_spark_session: SparkSession) -> None:
    """Test joins between drive_timestamps and frames_fc1 tbl."""

    drive_timestamps_tbl = mock_spark_session.table("silver_mock.drive_time_series.drive_timestamps").alias("dt")
    frames_fc1_tbl = mock_spark_session.table("silver_mock.drive_time_series.frames_fc1").alias("fc1_r")

    drive_timestamp_rows = drive_timestamps_tbl.collect()
    drive_timestamp_timestamp_secs = drive_timestamp_rows[0]["start_trj_ahead_of_fc1"]
    frames_fc1_rows = frames_fc1_tbl.collect()
    frames_fc1_timestamp_secs = frames_fc1_rows[0]["timestamp_closest_second"]

    rows = _get_frames_fc1_ts_corrected(mock_spark_session, "silver_mock")
    row_timestamp = rows.collect()[0]["timestamp_closest_second"]
    assert row_timestamp == drive_timestamp_timestamp_secs + frames_fc1_timestamp_secs, "Timestamp not corrected"


@pytest.mark.usefixtures("mock_spark_session")
def test_get_cameras_joined(mock_spark_session: SparkSession) -> None:
    """Test joins between different camera streams frames tbl."""

    rows = _get_cameras_joined(mock_spark_session, "silver_mock")
    row = rows.collect()[0]
    assert row["recorded_at"] is not None, "Recorded at is None"
    assert row["fc1_distorted_view"] != row["tvfront_distorted_view"], "Distorted views are the same"


@pytest.mark.usefixtures("mock_spark_session")
def test_get_data_for_trajectory_frames(mock_spark_session: SparkSession) -> None:
    """Test joins between joined camera stream and drive_trajectory tbl."""

    rows = get_data_for_trajectory_frames(mock_spark_session, "silver_mock")
    row = rows.collect()[0]
    assert row["recorded_at"] is not None, "Recorded at is None"
    assert row["latitude"] is not None, "Drive trajectory data not present"
    assert row["fc1_distorted_view"] is not None, "FC1 Distorted view not present"
    assert row["tvfront_distorted_view"], "TVFront Distorted views are the same"


@pytest.mark.usefixtures("mock_spark_session")
def test_write_data_schema(mock_spark_session: SparkSession, target_table_trajectory_frames: DeltaTable) -> None:
    """Test schema of written data."""

    processed_rows = get_data_for_trajectory_frames(mock_spark_session, "silver_mock")

    actual_fields = sorted((field.name, field.dataType) for field in processed_rows.schema.fields)
    expected_fields = sorted(
        (field.name, field.dataType) for field in target_table_trajectory_frames.toDF().schema.fields
    )
    assert actual_fields == expected_fields
