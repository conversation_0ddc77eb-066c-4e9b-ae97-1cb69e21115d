"""Tests for create primary situation dataset module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import unittest
from typing import Any
from unittest import mock
from unittest.mock import MagicMock, patch

import pyspark.sql.functions as F
import pytest
from create_primary_situation_dataset import (
    deduplicate,
    execute,
    get_datamanagement_distribution_parameters_dataset,
    get_vdso_1hz_dataset,
    parse_job_parameters,
    read_delta_table,
)
from pyspark.sql import DataFrame, SparkSession


# Mock DataFrame for unit tests
@pytest.fixture
def mock_data_frame(spark_session: SparkSession) -> MagicMock:
    """Mock DataFrame with custom attributes and methods."""
    mock_df = MagicMock(spec=DataFrame)

    # Mocking column access for 'vin' and other columns
    mock_df.columns = [
        "split_hash",
        "is_ramp",
        "split_url",
        "weather",
        "city",
        "street",
        "recording_started_at",
        "speed_limit",
        "container_url",
        "country_iso_alpha3",
        "vin",
        "latitude",
        "deleted_at",
        "time_of_day",
        "modified_by",
        "road_type",
        "altitude",
        "emitted_at_unix",
        "emitted_at",
        "is_tunnel",
        "modified_at",
        "drive_hash",
        "container_hash",
        "lane_count",
        "curvature",
        "is_parking_area",
        "ego_direction",
        "is_paved",
        "drive_url",
        "plate",
        "longitude",
        "raw_measurement_data_state",
        "is_roundabout",
        "is_parking_garage",
        "ego_speed",
        "archived_at",
        "created_at",
        "source_fleet",
        "country",
        "created_by",
        "geography",
        "weather_atmospheric_condition",
        "weather_precipitation",
        "weather_sky",
    ]

    # Mocking the behavior of select, filter, and withColumn to return mock DataFrame
    mock_df.select.return_value = mock_df
    mock_df.withColumn.return_value = mock_df
    mock_df.filter.return_value = mock_df
    mock_df.alias.return_value = mock_df

    # Mock column access behavior as per .__getitem__
    def column_access(col_name: Any) -> MagicMock:
        if col_name in mock_df.columns:
            return mock_df
        raise AttributeError(f"Mock DataFrame has no column: {col_name}")

    mock_df.__getitem__.side_effect = column_access

    return mock_df


class TestMain(unittest.TestCase):
    """Tests for main."""

    @patch("argparse.ArgumentParser.parse_known_args")
    def test_parse_job_parameters(self, mock_parse_known_args: Any) -> None:
        """Test parse_job_parameters."""
        mock_parse_known_args.return_value = (
            argparse.Namespace(
                silver_catalog="silver_catalog",
                target_catalog="target_catalog",
            ),
            [],
        )
        args = parse_job_parameters()
        self.assertEqual(args.silver_catalog, "silver_catalog")
        self.assertEqual(args.target_catalog, "target_catalog")


@mock.patch("create_primary_situation_dataset.read_delta_table")
def test_get_vdso_1hz_dataset(mock_read_delta_table: MagicMock, spark_session: SparkSession) -> None:
    """Test the get_vdso_1hz_dataset function."""

    # Prepare the mocked DataFrame that `read_delta_table` will return
    mock_df = MagicMock(spec=DataFrame)

    # Set up the behavior of the mock to return the mock DataFrame
    mock_read_delta_table.return_value = mock_df

    # Configure the mock DataFrame's methods (e.g., select and withColumn)
    mock_df.select.return_value = mock_df  # Chain mock for select
    mock_df.withColumn.return_value = mock_df  # Chain mock for withColumn

    # Call the function under test
    result_df = get_vdso_1hz_dataset(spark_session, "silver_catalog")

    # Assert that the `read_delta_table` method was called with the correct parameters
    mock_read_delta_table.assert_called_once_with(spark_session, "silver_catalog", "drive_time_series", "vdso_1hz")

    # Check that the `select` method was called on the DataFrame with the expected columns
    mock_df.select.assert_called_once_with(
        "emitted_at", "vin", "recorded_at", mock.ANY  # `mock.ANY` is used for alias expression `ego_speed`
    )

    # Check that the `withColumn` method was called with the expected column transformations
    mock_df.withColumn.assert_called_with(
        "ego_direction",  # Column name for the new column
        mock.ANY,  # Expression created by `F.when` function for `ego_direction`
    )

    # Since we are chaining, we don't need to check the final DataFrame directly here.
    # The mock ensures that the methods are called correctly, and we're asserting the method calls.

    # Assert that the result is the mock DataFrame
    assert result_df == mock_df


@mock.patch("create_primary_situation_dataset.read_delta_table")
def test_get_datamanagement_distribution_parameters_dataset(
    mock_read_delta_table: MagicMock, spark_session: SparkSession
) -> None:
    """Test the get_datamanagement_distribution_parameters_dataset function."""

    # Prepare the mocked DataFrame that `read_delta_table` will return
    mock_df = MagicMock(spec=DataFrame)

    # Set up the behavior of the mock to return the mock DataFrame
    mock_read_delta_table.return_value = mock_df

    # Configure the mock DataFrame's methods (e.g., select and withColumn)
    mock_df.select.return_value = mock_df  # Chain mock for select
    mock_df.withColumns.return_value = mock_df  # Chain mock for withColumn

    # Call the function under test
    get_datamanagement_distribution_parameters_dataset(spark_session, "silver_catalog")

    # Assert that the `read_delta_table` method was called with the correct parameters
    mock_read_delta_table.assert_called_once_with(
        spark_session, "silver_catalog", "mdd", "datamanagement_distribution_parameters"
    )

    # Check that the `select` method was called on the DataFrame with the expected columns
    mock_df.select.assert_any_call(
        "file_hash", "weather", mock.ANY  # `mock.ANY` is used for alias expression `time_of_day`
    )

    # mock_df.select.assert_called_with(*_WEATHER_REQUIRED_COLUMNS)
    assert mock_df.withColumns.call_count == 1


def test_deduplicate(spark_session: SparkSession) -> None:
    """Test the deduplicate function."""

    # Create a mock DataFrame
    mock_df = MagicMock(spec=DataFrame)

    # Mock methods on the DataFrame so they return the DataFrame itself
    mock_df.withColumn.return_value = mock_df
    mock_df.filter.return_value = mock_df
    mock_df.select.return_value = mock_df

    # Call the deduplicate function
    result_df = deduplicate(mock_df)

    # Assert that the withColumn method was called with the correct arguments
    # Verify that "instance" column is created and `row_number().over(window_spec)` is used
    mock_df.withColumn.assert_called_once()
    called_args = mock_df.withColumn.call_args[0]
    assert called_args[0] == "instance"  # Check column name
    assert isinstance(called_args[1], F.Column)  # Ensure it's a PySpark Column (expression)
    assert "row_number" in str(called_args[1])  # Ensure row_number is part of the expression

    # Assert that the filter method was called with the correct conditions
    mock_df.filter.assert_any_call("instance = 1")
    mock_df.filter.assert_any_call("vin IS NOT NULL")
    mock_df.filter.assert_any_call("emitted_at IS NOT NULL")

    # Assert that the select method was called with the correct arguments
    mock_df.select.assert_called_once()

    # Assert that the result is a DataFrame (mocked)
    assert isinstance(result_df, MagicMock)
    assert isinstance(result_df, DataFrame)


def test_read_delta_table(spark_session: SparkSession) -> None:
    """Test the read_delta_table function."""

    # Define the catalog, schema, and table name
    catalog = "my_catalog"
    schema = "my_schema"
    table = "my_table"

    # Create a mock Spark session
    mock_spark_session = MagicMock(spec=SparkSession)

    # Mock the `read` attribute to return a mock object with the `format` and `table` methods
    mock_read = MagicMock()
    mock_spark_session.read = mock_read

    # Mock the `format` method to return the mock_read object (so that we can chain `.table()`)
    mock_format = MagicMock()
    mock_read.format.return_value = mock_format

    # Create a mock DataFrame to return when table is called
    mock_df = MagicMock(spec=DataFrame)
    mock_format.table.return_value = mock_df

    # Call the read_delta_table function
    result_df = read_delta_table(mock_spark_session, catalog, schema, table)

    # Check that the format method was called with "delta"
    mock_spark_session.read.format.assert_called_once_with("delta")

    # Check that the table method was called with the correct table path
    mock_spark_session.read.format.return_value.table.assert_called_once_with(f"{catalog}.{schema}.{table}")

    # Assert that the result is the mocked DataFrame
    assert result_df == mock_df


@mock.patch("create_primary_situation_dataset.get_gnss_base_dataset")
@mock.patch("create_primary_situation_dataset.get_vdso_1hz_dataset")
@mock.patch("create_primary_situation_dataset.get_datamanagement_distribution_parameters_dataset")
@mock.patch("create_primary_situation_dataset.get_one_second_interval_wise_emitted_at_col")
@mock.patch("create_primary_situation_dataset.create_the_situation_dataset_df")
@mock.patch("create_primary_situation_dataset.get_time_of_day_dataset")
@mock.patch("create_primary_situation_dataset.read_delta_table")
@mock.patch("create_primary_situation_dataset.deduplicate")
@mock.patch("pyspark.sql.DataFrame.write")
def test_execute(
    mock_write: MagicMock,
    mock_deduplicate: MagicMock,
    mock_read_delta_table: MagicMock,
    mock_get_time_of_day_dataset: MagicMock,
    mock_create_the_situation_dataset_df: MagicMock,
    mock_get_one_second_interval_wise_emitted_at_col: MagicMock,
    mock_datamanagement_distribution_parameters_dataset: MagicMock,
    mock_get_vdso_1hz_dataset: MagicMock,
    mock_get_gnss_base_dataset: MagicMock,
    spark_session: SparkSession,
) -> None:
    """Test the execute function."""

    # Create mock DataFrames for the dependencies
    mock_gnss_df = MagicMock()
    mock_vdso_df = MagicMock()
    mock_datamanagement_distribution_parameters_df = MagicMock()
    mock_emitted_df = MagicMock()
    mock_situation_df = MagicMock()
    mock_target_df = MagicMock()
    mock_time_of_day_df = MagicMock()

    # Set up the return values for the mocked functions
    mock_get_gnss_base_dataset.return_value = mock_gnss_df
    mock_get_vdso_1hz_dataset.return_value = mock_vdso_df
    mock_datamanagement_distribution_parameters_dataset.return_value = mock_datamanagement_distribution_parameters_df
    mock_get_one_second_interval_wise_emitted_at_col.return_value = mock_emitted_df
    mock_get_time_of_day_dataset.return_value = mock_time_of_day_df
    mock_create_the_situation_dataset_df.return_value = mock_situation_df
    mock_read_delta_table.return_value = mock_target_df

    # Mock the behavior of the `write` method (to avoid actual file writing)
    mock_write.format.return_value = mock_write
    mock_write.mode.return_value = mock_write
    mock_write.saveAsTable.return_value = None

    # Call the function under test
    execute(spark_session, "silver_catalog", "target_catalog")

    # Assert that the mock functions are called
    mock_get_gnss_base_dataset.assert_called_once_with(spark_session, "silver_catalog")
    mock_get_vdso_1hz_dataset.assert_called_once_with(spark_session, "silver_catalog")
    mock_get_one_second_interval_wise_emitted_at_col.assert_called_once_with(spark_session, "silver_catalog")
    mock_datamanagement_distribution_parameters_dataset.assert_called_once_with(spark_session, "silver_catalog")
    mock_get_time_of_day_dataset.assert_called_once_with(spark_session, "silver_catalog")
    mock_create_the_situation_dataset_df.assert_called_once_with(
        mock_gnss_df, mock_vdso_df, mock_emitted_df, mock_datamanagement_distribution_parameters_df, mock_time_of_day_df
    )
    mock_read_delta_table.assert_called_once_with(
        spark_session, "target_catalog", "drive_time_series", "situation_dataset_full"
    )

    # Assert that schema enforcement (select and cast) was done on the situation dataset
    mock_situation_df.select.assert_called_once()

    # Assert that deduplication was called on the situation dataset after select
    mock_deduplicate.assert_called_once_with(mock_situation_df.select.return_value)

    # # Assert that the data was written to the target catalog using the 'overwrite' mode
    # mock_write.saveAsTable.assert_called_once_with("target_catalog.drive_time_series.situation_dataset_full")

    # Check for exception handling
    try:
        # Simulate an error during the execution
        mock_create_the_situation_dataset_df.side_effect = Exception("Test exception")
        with pytest.raises(Exception):
            execute(spark_session, "silver_catalog", "target_catalog")
    except Exception as e:
        # Validate if the error is raised and traceback is printed
        assert str(e) == "Test exception"


if __name__ == "__main__":
    pytest.main()
