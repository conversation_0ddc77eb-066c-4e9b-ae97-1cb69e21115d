"""Test module for create_table_content_tags_with_gps_position."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import re
from unittest.mock import MagicMock

import pytest
from pyspark.sql import SparkSession
from pytest import MonkeyPatch
from src.create_table_content_tags_with_gps_position import (
    create_table_content_tags_with_gps_position,
    create_view_content_tags_with_route_category,
    parse_job_parameters,
)
from tests.conftest import SpySparkSession


@pytest.fixture(scope="session")
def mock_spark_session(spark_session: SparkSession) -> MagicMock:
    """Mock spark session for testing."""

    mock_spark_session = MagicMock()

    def side_effect(sql_query: str) -> None:
        # Mocks to create temp views for running queries.
        if "CREATE OR REPLACE VIEW" in sql_query:
            spark_session.sql(
                """
                CREATE TEMP VIEW trajectory_signals_full AS
                SELECT
                    '2024-07-30T10:35:00.000+00:00' as emitted_at,
                    'vin1' as vin,
                    'hash1' as gmdm_file_hash,
                    '2023-01-01' as created_at,
                    10 as absolute_velocity,
                    '2024-07-30T10:35:00.000+00:00' as recorded_at,
                    'hash1' as file_hash,
                    null as gps_latitude,
                    null as gps_longitude,
                    null as gps_altitude_m,
                    'Germany' as mapinfo_country,
                    'DEU' as mapinfo_iso_country_code_alpha3,
                    'Berlin' as mapinfo_city,
                    'street1' as mapinfo_street,
                    'category1' as route_category,
                    2 as mapinfo_lane_count,
                    60 as mapinfo_speed_limit,
                    true as mapinfo_is_roundabout,
                    false as mapinfo_is_tunnel,
                    false as mapinfo_is_ramp,
                    true as mapinfo_is_paved
                """
            )
        elif "ALTER VIEW" in sql_query:
            return None

        new_query = re.sub(r"(\w+\.\w+)\.(\w+)", r"\2", sql_query)  # Convert multi-part to single-part namespace
        new_query = new_query.replace(
            "CREATE OR REPLACE VIEW", "CREATE OR REPLACE TEMP VIEW"
        )  # To create temp view for testing purposes
        spark_session.sql(new_query)

    # Set the side_effect to the mock_spark_session
    mock_spark_session.sql.side_effect = side_effect
    return mock_spark_session


def test_create_table_content_tags_with_gps_position(mock_spark_session1: SpySparkSession) -> None:
    """Test create_table_content_tags_with_gps_position."""

    # given a SparkSession
    # given a target catalog and schema
    target_catalog = mock_spark_session1.catalog.currentCatalog()
    target_schema = "drive_time_series"

    # when create_table_content_tags_with_gps_position is called
    create_table_content_tags_with_gps_position(mock_spark_session1, target_catalog, target_schema)
    # then the "content_tags_with_gps_position" table should be available in the "drive_time_series" database
    tables = mock_spark_session1.catalog.listTables(target_schema)
    assert any(table.name == "content_tags_with_gps_position" for table in tables)

    # and the table should be a Delta table
    table_info = mock_spark_session1.sql(f"DESCRIBE DETAIL {target_schema}.content_tags_with_gps_position").collect()
    assert table_info[0]["format"] == "delta"

    # and the table should have the correct schema
    expected_schema = {
        "vin": "string",
        "file_hash": "string",
        "gmdm_file_hash": "string",
        "recorded_at": "string",
        "started_at": "timestamp",
        "duration_s": "double",
        "group_name": "string",
        "name": "string",
        "gps_emitted_at": "timestamp",
        "gps_latitude": "double",
        "gps_longitude": "double",
        "gps_lag": "interval day to second",
    }
    content_tags_table = mock_spark_session1.table(f"{target_schema}.content_tags_with_gps_position")
    actual_schema = {field.name: field.dataType.simpleString() for field in content_tags_table.schema.fields}
    assert expected_schema == actual_schema

    # and querying the table should return an empty DataFrame
    assert content_tags_table.count() == 0

    # check tags were set for the "content_tags_with_gps_position" table
    assert (
        """ALTER TABLE content_tags_with_gps_position SET TAGS ('responsible_domain'='Data Delivery',
        'responsible_team'='Analytics Platform','refresh_interval'='PT3H')"""
        == mock_spark_session1.query_history[2]
    )


def test_create_view_content_tags_with_route_category(mock_spark_session: SparkSession) -> None:
    """Test create_view_content_tags_with_route_category."""

    # when create_table_content_tags_with_gps_position is called
    create_view_content_tags_with_route_category(mock_spark_session, "gold_mock", "drive_time_series")

    assert mock_spark_session.sql.call_count == 3


def test_parse_job_parameters(monkeypatch: MonkeyPatch) -> None:
    """Test parse_job_parameters."""
    test_args = ["script_name", "--gold_catalog", "test_catalog", "--target_schema", "test_schema"]
    monkeypatch.setattr("sys.argv", test_args)
    args = parse_job_parameters()
    assert args.gold_catalog == "test_catalog"
    assert args.target_schema == "test_schema"
