# ==============================================================================
#
# C O P Y R I G H T
# ------------------------------------------------------------------------------
#
#  Copyright (C) 2024 Robert <PERSON> GmbH
#  Copyright (C) 2024 by CARIAD and Robert <PERSON>sch GmbH. All rights reserved.
#  The reproduction, distribution and utilization of this file as
#  well as the communication of its contents to others without express
#  authorization is prohibited. Offenders will be held liable for the
#  payment of damages. All rights reserved in the event of the grant
#  of a patent, utility model or design.
# ==============================================================================

[tool.poetry]
name = "drive-time-series-asset-bundle"
description = "Drive Time Series asset bundle for Databricks."
version = "0.0.1" # dynamic versioning enabled, standard placeholder
readme = "README.md"
authors = ["Data Delivery"]
packages = [{ include = "*", from = "./src" }]

[build-system]
requires = ["poetry-core>=1.0.0", "poetry-dynamic-versioning>=1.0.0,<1.7.1"]
build-backend = "poetry_dynamic_versioning.backend"

[tool.pytest.ini_options]
pythonpath = "./src"
log_cli = "True"
log_level = "DEBUG"

[tool.poetry.dependencies]
python = "^3.10"
pyspark = "~3.5.0"
pandas = { version = "^2.2.0", optional = true }
pyarrow = { version = ">=4.0.0", optional = true }
numpy = { version = "^1.21.0", optional = true }
delta-spark = { version = "^3.2.1", optional = true }

[tool.poetry.extras]
testing = ["pytest", "pyspark", "delta-spark", "dataloop-schemas", "pandas", "pyarrow", "requests"]

[tool.poetry-dynamic-versioning]
enable = true
vcs = "git"
bump = "true"
strict = "true"
style = "semver"
format-jinja = "{% if distance == 0 %}{{ base }}{% else %}{{ bump_version(base) }}-dev{{timestamp}}{% endif %}"
