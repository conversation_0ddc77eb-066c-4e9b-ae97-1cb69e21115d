# The main job for drive-time-series.
variables:
  ms_teams_alert_channel_email:
    default: "<EMAIL>"
    description: "MS Teams alert channel email"
  run_sp:
    default: "3ee9fc25-7c21-49f6-93a5-ab99ad69bf8b"
    description: "run service principal"
  spark_version:
    default: "14.3.x-scala2.12"
    description: "Spark version"
  instance_pool_id:
    default: "1001-074540-notes282-pool-y1atnfsi" # default_D16ds_v4_rt14.3 on datalake-dev
    description: "Instance pool id"
  warehouse_id:
    default: "28c417ff7d45ff63"
    description: "Warehouse id"
  num_workers:
    default: "1"
    description: "Number of workers"
  bronze_catalog:
    default: bronze_dev
    description: "Bronze Catalog name"
  silver_catalog:
    default: silver_dev
    description: "Silver Catalog name"
  gold_catalog:
    default: gold_dev
    description: "Gold Catalog name"
  target_schema:
    default: drive_time_series
    description: "Target schema name"
  driver_instance_pool_id_drive_ontology:
    description: "Instance pool id"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  instance_pool_id_drive_ontology:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E16ads_v5_rt14.3"
  num_workers_drive_ontology:
    default: "2"
    description: "Number of workers"
  schedule_pause_status:
    default: "PAUSED"
    description: "Schedule pause status"

resources:
  jobs:
    content_tags_with_gps_position:
      name: Content Tags with gps Position
      max_concurrent_runs: 1
      run_as:
        service_principal_name: ${var.run_sp}
      tags:
        responsible_domain: "Data Delivery"
        responsible_team: "Analytics Platform"
        product: "drive-time-series"
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
        no_alert_for_skipped_runs: true
      schedule:
        quartz_cron_expression: 0 0 0/3 ? * * *
        timezone_id: Europe/Berlin
        pause_status: UNPAUSED
      timeout_seconds: 7200 # 2 hours
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 5400 # 1 hour 30 minutes

      tasks:
        - task_key: create_table_content_tags_with_gps_position
          spark_python_task:
            python_file: ../src/create_table_content_tags_with_gps_position.py
            parameters:
              - --gold_catalog
              - ${var.gold_catalog}
              - --target_schema
              - ${var.target_schema}
          job_cluster_key: drive_time_series_job_cluster

        - task_key: get_gps_position_for_content_tags
          depends_on:
            - task_key: create_table_content_tags_with_gps_position
          run_if: ALL_SUCCESS
          spark_python_task:
            python_file: ../src/get_gps_position_content_tags.py
            parameters:
              - --bronze_catalog
              - ${var.bronze_catalog}
              - --gold_catalog
              - ${var.gold_catalog}
          job_cluster_key: drive_time_series_job_cluster

      job_clusters:
          - job_cluster_key: drive_time_series_job_cluster
            new_cluster:
              driver_instance_pool_id: ${var.instance_pool_id}
              instance_pool_id: ${var.instance_pool_id}
              spark_version: ${var.spark_version}
              num_workers:  ${var.num_workers}
              data_security_mode: "USER_ISOLATION"
    trajectory_frames:
      name: Drive Ontology - Trajectory Frames - Gold
      max_concurrent_runs: 1
      run_as:
        service_principal_name: ${var.run_sp}
      tags:
        responsible_domain: "Data Delivery"
        responsible_team: "Analytics Platform"
        product: "drive-time-series"
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
        no_alert_for_skipped_runs: true
      schedule:
        # run once a day, at 2:30, silver job ideally finishes at 0:30
        quartz_cron_expression: '0 30 2 ? * * *'
        timezone_id: Europe/Berlin
        pause_status: ${var.schedule_pause_status}
      timeout_seconds: 5400 # 1 hour 30 mins
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600 # 1 hour

      tasks:
        - task_key: create_table_trajectory_frames
          spark_python_task:
            python_file: ../src/trajectory_frames_schema.py
            parameters:
              - --target_catalog
              - ${var.gold_catalog}
              - --target_schema
              - ${var.target_schema}
          job_cluster_key: drive_ontology_job_clusters

        - task_key: update_table_trajectory_frames
          depends_on:
            - task_key: create_table_trajectory_frames
          spark_python_task:
            python_file: ../src/trajectory_frames.py
            parameters:
              - --silver_catalog
              - ${var.silver_catalog}
              - --target_catalog
              - ${var.gold_catalog}
              - --target_schema
              - ${var.target_schema}
          job_cluster_key: drive_ontology_job_clusters

        - task_key: create_view_situation_dataset
          depends_on:
            - task_key: update_table_trajectory_frames
          spark_python_task:
            python_file: ../src/situation_dataset_full_view.py
            parameters:
              - --gold_catalog
              - ${var.gold_catalog}
              - --silver_catalog
              - ${var.silver_catalog}
              - --target_schema
              - ${var.target_schema}
          job_cluster_key: drive_ontology_job_clusters

      job_clusters:
          - job_cluster_key: drive_ontology_job_clusters
            new_cluster:
              driver_instance_pool_id: ${var.driver_instance_pool_id_drive_ontology}
              instance_pool_id: ${var.instance_pool_id_drive_ontology}
              spark_version: ${var.spark_version}
              num_workers:  ${var.num_workers_drive_ontology}
              data_security_mode: "USER_ISOLATION"
    primary_situation_dataset:
      name: Primary Situation Dataset - Gold
      max_concurrent_runs: 1
      run_as:
        service_principal_name: ${var.run_sp}
      tags:
        responsible_domain: "Data Delivery"
        responsible_team: "Analytics Platform"
        product: "drive-time-series"
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
        - group_name: "sg-pace-databricks-Data Delivery-Data-Management-Verticalization-(DMV)-admin"
          level: CAN_VIEW
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
        no_alert_for_skipped_runs: true
      schedule:
        # run once a day, at 4:30, tragectory frames gold job ideally finishes at 2:30
        quartz_cron_expression: '0 30 4 ? * * *'
        timezone_id: Europe/Berlin
        pause_status: ${var.schedule_pause_status}
      timeout_seconds: 5400 # 1 hour 30 mins
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600 # 1 hour

      tasks:
        - task_key: create_situation_dataset_full_tbl
          spark_python_task:
            python_file: ../src/primary_situation_schema.py
            parameters:
              - --target_catalog
              - ${var.gold_catalog}
              - --target_schema
              - ${var.target_schema}
          job_cluster_key: primary_situation_job_clusters

        - task_key: update_primary_situation_dataset
          depends_on:
            - task_key: create_situation_dataset_full_tbl
          spark_python_task:
            python_file: ../src/create_primary_situation_dataset.py
            parameters:
              - --silver_catalog
              - ${var.silver_catalog}
              - --target_catalog
              - ${var.gold_catalog}
          job_cluster_key: primary_situation_job_clusters

      job_clusters:
          - job_cluster_key: primary_situation_job_clusters
            new_cluster:
              driver_instance_pool_id: ${var.driver_instance_pool_id_drive_ontology}
              instance_pool_id: ${var.instance_pool_id_drive_ontology}
              spark_version: ${var.spark_version}
              num_workers:  ${var.num_workers_drive_ontology}
              data_security_mode: "USER_ISOLATION"
