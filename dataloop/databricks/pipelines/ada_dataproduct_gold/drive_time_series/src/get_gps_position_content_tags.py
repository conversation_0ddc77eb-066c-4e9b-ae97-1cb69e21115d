"""Get gps position for content tags."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
from typing import Final

from pyspark.sql import DataFrame, SparkSession

parser = argparse.ArgumentParser()
parser.add_argument("--bronze_catalog", help="Bronze catalog name", required=True)
parser.add_argument("--gold_catalog", help="Gold catalog name", required=True)
args, unknown = parser.parse_known_args()

BRONZE_CATALOG: Final = args.bronze_catalog
GOLD_CATALOG: Final = args.gold_catalog

CONTENT_TAGS_WITH_GPS_POSITION_TABLE: Final = f"{GOLD_CATALOG}.drive_time_series.content_tags_with_gps_position"

PROCESSING_QUERY: Final = f"""
        WITH gps_latest AS (
        SELECT
            vin, record_date, abstime,
            created_at,
            latitude, longitude, altitude_m, number_of_visible_satellites,
            sha
        FROM {BRONZE_CATALOG}.drive_time_series.gps_latest
        ),
    ct_latest AS (
        SELECT
            vin, record_date, start_date, end_date, duration_s,
            created_at,
            group_name, name,
            sha
        FROM (
            SELECT
                *,
                ROW_NUMBER() OVER (PARTITION BY vin, record_date, start_date, end_date, duration_s,name,group_name ORDER BY created_at DESC) rn
            FROM {BRONZE_CATALOG}.drive_time_series.content_tags_latest
            )
            WHERE rn = 1
        ),
    ct_normalized AS (
        SELECT
            *,
            CASE WHEN duration_s > 0 THEN start_date ELSE start_date - INTERVAL 30 SECONDS END normalized_start_date,
            CASE WHEN duration_s > 0 THEN end_date   ELSE start_date + INTERVAL 30 SECONDS END normalized_end_date -- for content tags with 0 duration take the nearest GPS point in the previous/next 15s
        FROM ct_latest
        ),
    res AS (
        SELECT * FROM (
            SELECT
            gps.abstime gps_emitted_at,
            gps.latitude gps_latitude,
            gps.longitude gps_longitude,
            ct.* except(sha),
            ct.sha as gmdm_sha, -- gmdm.json sha
            gps.sha, -- sha of the file that contains gps data
            ROW_NUMBER() OVER (PARTITION BY ct.vin, ct.record_date, ct.start_date, ct.end_date, ct.duration_s, ct.group_name, ct.name ORDER BY abs(gps.abstime - start_date) ASC) rn
            FROM ct_normalized ct
            LEFT JOIN gps_latest gps ON ct.vin = gps.vin
                                AND ct.record_date = gps.record_date
                                AND gps.abstime >= ct.normalized_start_date
                                AND gps.abstime <= ct.normalized_end_date
        )
        WHERE rn = 1
        )

    SELECT
        vin, sha file_hash, gmdm_sha gmdm_file_hash, record_date recorded_at, start_date started_at, duration_s,
        group_name, name,
        gps_emitted_at, gps_latitude, gps_longitude,
        gps_emitted_at - start_date gps_lag -- GPS quality metric
    FROM res
    WHERE gps_emitted_at IS NOT NULL
"""

spark: SparkSession = SparkSession.builder.getOrCreate()
content_tags_with_gps_position_df: DataFrame = spark.sql(PROCESSING_QUERY)

content_tags_with_gps_position_df.write.format("delta").mode("overwrite").saveAsTable(
    CONTENT_TAGS_WITH_GPS_POSITION_TABLE
)
