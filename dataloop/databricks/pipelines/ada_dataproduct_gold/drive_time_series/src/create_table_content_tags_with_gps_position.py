"""Create table content_tags_with_gps_position."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse

from pyspark.sql import SparkSession


def _update_table_tag(spark_session: SparkSession, table_name: str) -> None:
    spark_session.sql(
        f"""ALTER TABLE {table_name} SET TAGS ('responsible_domain'='Data Delivery',
        'responsible_team'='Analytics Platform','refresh_interval'='PT3H')"""  # noqa: E501
    )


def _update_view_tag(spark_session: SparkSession, table_name: str) -> None:
    spark_session.sql(
        f"""ALTER VIEW {table_name} SET TAGS ('responsible_domain'='Data Delivery',
        'responsible_team'='Analytics Platform','refresh_interval'='PT3H')"""  # noqa: E501
    )


def create_table_content_tags_with_gps_position(
    spark_session: SparkSession, target_catalog: str, target_schema: str
) -> None:
    """Create table content_tags_with_gps_position."""
    spark_session.catalog.setCurrentCatalog(target_catalog)
    spark_session.sql(f"CREATE SCHEMA IF NOT EXISTS {target_schema}")
    spark_session.catalog.setCurrentDatabase(target_schema)
    spark_session.sql(
        """
        CREATE TABLE IF NOT EXISTS content_tags_with_gps_position(
            vin STRING COMMENT 'Vehicle Identification Number',
            file_hash STRING COMMENT 'SHA of the HDF5 file',
            gmdm_file_hash STRING COMMENT 'SHA of the gmdm.json file',
            recorded_at STRING COMMENT 'Measurement record date from gmdm',
            started_at TIMESTAMP COMMENT 'Content tag triggered at timestamp',
            duration_s DOUBLE COMMENT 'Tag duration in seconds',
            group_name STRING COMMENT 'Tag group name',
            name STRING COMMENT 'Tag name',
            gps_emitted_at TIMESTAMP COMMENT 'Time of GPS data capture',
            gps_latitude DOUBLE COMMENT 'Latitude of the GPS data',
            gps_longitude DOUBLE COMMENT 'Longitude of the GPS data',
            gps_lag INTERVAL DAY TO SECOND COMMENT 'GPS lag compared to the Content tag started timestamp')
        USING DELTA
        """
    )

    _update_table_tag(spark_session, "content_tags_with_gps_position")


def create_view_content_tags_with_route_category(
    spark_session: SparkSession, target_catalog: str, target_schema: str
) -> None:
    """Create view content_tags_with_route_category."""
    spark_session.catalog.setCurrentCatalog(target_catalog)
    spark_session.sql(f"CREATE SCHEMA IF NOT EXISTS {target_schema}")
    spark_session.catalog.setCurrentDatabase(target_schema)
    spark_session.sql(
        f"""
        CREATE OR REPLACE VIEW content_tags_with_route_category AS
            SELECT route_category,
                    vin,
                    file_hash,
                    gmdm_file_hash,
                    recorded_at,
                    started_at,
                    duration_s,
                    group_name,
                    name,
                    gps_emitted_at,
                    gps_latitude,
                    gps_longitude,
                    gps_lag
            FROM (
                SELECT
                    s.route_category,
                    ct.vin,
                    ct.file_hash,
                    ct.gmdm_file_hash,
                    ct.recorded_at,
                    ct.started_at,
                    ct.duration_s,
                    ct.group_name,
                    ct.name,
                    ct.gps_emitted_at,
                    ct.gps_latitude,
                    ct.gps_longitude,
                    ct.gps_lag,
                    ROW_NUMBER() OVER (
                        PARTITION BY ct.vin, ct.recorded_at, ct.started_at,
                        ct.duration_s, ct.group_name, ct.name
                        ORDER BY s.emitted_at ASC
                    ) AS rn
                FROM {target_catalog}.{target_schema}.content_tags_with_gps_position ct
                LEFT JOIN {target_catalog}.{target_schema}.trajectory_signals_full s
                ON s.vin = ct.vin
                AND s.recorded_at = ct.recorded_at
                AND s.emitted_at >= ct.started_at - INTERVAL 30 SECONDS
                AND s.emitted_at <= ct.started_at + INTERVAL 30 SECONDS
            ) AS ranked_signals
            WHERE rn = 1;
        """
    )

    _update_view_tag(spark_session, "content_tags_with_route_category")


def parse_job_parameters() -> argparse.Namespace:
    """Parse job parameters."""
    parser = argparse.ArgumentParser()
    parser.add_argument("--gold_catalog", help="Gold catalog name", required=True)
    parser.add_argument("--target_schema", help="The schema name", required=True)
    args, _ = parser.parse_known_args()
    return args


if __name__ == "__main__":
    args = parse_job_parameters()
    spark: SparkSession = SparkSession.builder.getOrCreate()
    create_table_content_tags_with_gps_position(spark, args.gold_catalog, args.target_schema)
    create_view_content_tags_with_route_category(spark, args.gold_catalog, args.target_schema)
