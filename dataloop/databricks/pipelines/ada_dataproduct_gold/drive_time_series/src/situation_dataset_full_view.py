"""Schema for situation dataset."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse

from pyspark.sql import SparkSession


def parse_job_parameters() -> argparse.Namespace:
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-s", "--silver_catalog", required=True, type=str)
    parser.add_argument("-t", "--gold_catalog", required=True, type=str)
    parser.add_argument("-ts", "--target_schema", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def create_situation_dataset_view(spark_session: SparkSession, silver: str, gold: str) -> None:
    """Create Primary Situation dataset view."""

    """Contains data for all available data at a temporal resolution of 1 Hz."""

    spark_session.sql(
        f"""
        CREATE OR REPLACE VIEW situation_dataset_full_old
          COMMENT "All available data at a temporal resolution of 1 Hz. Currently WIP!"
          AS WITH SITUATION_DATASET_CL AS (
            SELECT
              emitted_at,
              unix_timestamp(emitted_at) as emitted_at_unix,
              tsf.vin,
              recorded_at as recording_started_at,
              di.plate,
              gmdm_file_hash as drive_hash,
              rec.file_url as drive_url,
              fs.file_hash as split_hash,
              fs.file_url as split_url,
              tsf.file_hash as container_hash,
              null as container_url,
              null as raw_measurement_data_state,
              null as source_fleet,
              gps_latitude as latitude,
              gps_longitude as longitude,
              gps_altitude_m as altitude,
              mapinfo_country as country,
              mapinfo_iso_country_code_alpha3 as country_iso_alpha3,
              mapinfo_city as city,
              mapinfo_street as street,
              route_category as road_type,
              mapinfo_lane_count as lane_count,
              mapinfo_speed_limit as speed_limit,
              null as curvature,
              mapinfo_is_roundabout as is_roundabout,
              mapinfo_is_tunnel as is_tunnel,
              mapinfo_is_ramp as is_ramp,
              null as is_parking_area,
              null as is_parking_garage,
              mapinfo_is_paved as is_paved,
              tod.time_of_day,
              null as weather,
              null as geography,
              absolute_velocity as ego_speed,
              null as ego_direction,
              null as archived_at,
              null as deleted_at,
              tsf.created_at,
              null as created_by,
              null as modified_at,
              null as modified_by
              FROM {gold}.drive_time_series.trajectory_signals_full tsf
              LEFT JOIN {silver}.ada_ontology.recordings rec ON rec.recording_started_at = tsf.recorded_at
                AND rec.vin = tsf.vin
              LEFT JOIN {silver}.mdd.datamanagement_drive_info di ON di.file_hash = tsf.gmdm_file_hash
              LEFT JOIN {silver}.ada_ontology.files_scenes fs ON fs.recording_started_at = rec.recording_started_at
                  AND fs.vin = rec.vin
                  AND tsf.emitted_at BETWEEN fs.started_at AND fs.started_at + INTERVAL 30 SECOND
              LEFT JOIN {silver}.mdd.time_of_day tod on tod.file_hash = fs.file_hash
            ),
            SITUATION_DATASET_DC AS (
              SELECT
              recorded_at as emitted_at,
              unix_timestamp(recorded_at) as emitted_at_unix,
              tf.vin,
              tf.recording_started_at,
              plate,
              drive_hash,
              rec.file_url as drive_url,
              split_hash,
              fs.file_url as split_url,
              null as container_hash,
              null as container_url,
              null as raw_measurement_data_state,
              null as source_fleet,
              latitude,
              longitude,
              null as altitude,
              country,
              iso_country_code_alpha3 as country_iso_alpha3,
              city,
              street,
              road_category as road_type,
              lane_count,
              speed_limit,
              null as curvature,
              CAST(is_roundabout as BOOLEAN),
              CAST(is_tunnel as BOOLEAN),
              CAST(is_ramp as BOOLEAN),
              null as is_parking_area,
              null as is_parking_garage,
              is_paved,
              time_of_day,
              null as weather,
              null as geography,
              null as ego_speed,
              null as ego_direction,
              null as archived_at,
              null as deleted_at,
              null as created_at,
              null as created_by,
              null as modified_at,
              null as modified_by
              FROM {gold}.drive_time_series.trajectory_frames tf
              LEFT JOIN {silver}.ada_ontology.recordings rec ON rec.recording_started_at = tf.recording_started_at
                AND rec.vin = tf.vin
              LEFT JOIN {silver}.ada_ontology.files_scenes fs on fs.vin = tf.vin AND fs.started_at = tf.scene_started_at
            )
            SELECT * FROM SITUATION_DATASET_CL
            UNION ALL
            SELECT * FROM SITUATION_DATASET_DC
    """
    )

    spark_session.sql(
        """ALTER VIEW situation_dataset_full_old SET TAGS ('responsible_domain' = 'Data Delivery',
        'responsible_team' = 'Analytics Platform','refresh_interval' = 'P1D')"""
    )


def create_or_update_view(
    spark_session: SparkSession, silver_catalog: str, gold_catalog: str, target_schema: str
) -> None:
    """Create tables in the target schema."""

    spark_session.catalog.setCurrentCatalog(gold_catalog)
    spark_session.catalog.setCurrentDatabase(target_schema)

    create_situation_dataset_view(spark_session, silver_catalog, gold_catalog)


if __name__ == "__main__":
    args = parse_job_parameters()
    spark_session = SparkSession.builder.getOrCreate()
    create_or_update_view(spark_session, args.silver_catalog, args.gold_catalog, args.target_schema)
