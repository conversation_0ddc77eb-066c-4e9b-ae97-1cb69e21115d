"""Create primary situation dataset."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import traceback

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F
from pyspark.sql.window import Window

_PRIMARY_JOIN_COLUMNS = ["emitted_at", "vin", "recording_started_at", "emitted_at_tai"]
_UTC_TO_TAI_SECONDS = 37
_GNSS_GT_REQUIRED_COLUMNS = [
    "split_hash",
    "is_ramp",
    "city",
    "street",
    "recording_started_at",
    "state",
    "vin",
    "latitude",
    "altitude",
    "emitted_at",
    "drive_hash",
    "lane_count",
    "country_iso_alpha3",
    "is_paved",
    "plate",
    "longitude",
    "country",
    "is_roundabout",
    "is_tunnel",
    "speed_limit",
    "road_type",
    "curvature",
    "emitted_at_tai",
]
_WEATHER_REQUIRED_COLUMNS = [
    "file_hash",
    "weather_atmospheric_condition",
    "weather_precipitation",
    "weather_sky",
    "daytime",
]


def parse_job_parameters() -> argparse.Namespace:
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-j", "--silver_catalog", required=True, type=str)
    parser.add_argument("-t", "--target_catalog", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def get_one_second_interval_wise_emitted_at_col(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Get the new emitted at col from rec on 1 sec interval basis based on the start and end time."""
    recordings_df = read_delta_table(spark_session, silver_catalog, "ada_ontology", "recordings")
    files_scenes_df = read_delta_table(spark_session, silver_catalog, "ada_ontology", "files_scenes")
    win_spec = Window.partitionBy("vin", "recording_started_at").orderBy(F.col("started_at").desc())
    files_scenes_df = files_scenes_df.withColumn("ranking", F.dense_rank().over(win_spec)).where(F.col("ranking") == 1)
    files_scenes_df = files_scenes_df.withColumn(
        "recording_ended_at", F.col("started_at") + F.expr("INTERVAL 30 SECOND")
    ).select("vin", "recording_started_at", "recording_ended_at", "file_url")
    joined_df = (
        recordings_df.alias("rec")
        .join(
            files_scenes_df.alias("fs"),
            (recordings_df.vin == files_scenes_df.vin)
            & (recordings_df.recording_started_at == files_scenes_df.recording_started_at),
            "left",
        )
        .select(
            "rec.recording_started_at",
            "rec.vin",
            "fs.recording_ended_at",
            F.col("rec.file_url").alias("drive_url"),
            F.col("fs.file_url").alias("split_url"),
            F.when(
                (F.col("rec.file_pool") == "g3closedloop")
                | (F.col("rec.file_pool") == "g5cfclnar")
                | (F.col("rec.file_pool") == "g3openloop"),
                "ADA_DEVELOPMENT_FLEET",
            )
            .when(
                (F.col("rec.file_pool") == "g3vprdaq") | (F.col("rec.file_pool") == "g5vprnar01"),
                "ADA_DATA_COLLECTION_FLEET",
            )
            .otherwise(F.lit(None))
            .alias("source_fleet"),
        )
        .where((F.col("fs.recording_ended_at").isNotNull()) & (F.col("split_url").isNotNull()))
    )
    win_spec_next_rec_start_time = Window.partitionBy("vin").orderBy(F.col("recording_started_at"))
    joined_df = joined_df.withColumn(
        "next_recording_started_at", F.lead("recording_started_at", 1).over(win_spec_next_rec_start_time)
    )
    joined_df = joined_df.withColumn(
        "new_recording_ended_at",
        F.when(F.col("recording_ended_at") < F.col("next_recording_started_at"), F.col("recording_ended_at"))
        .when(F.col("next_recording_started_at").isNull(), F.col("recording_ended_at"))
        .otherwise(F.col("next_recording_started_at") - F.expr("INTERVAL 1 SECOND")),
    )
    joined_df = joined_df.filter(F.col("new_recording_ended_at") >= F.col("recording_started_at"))
    df_with_emitted_at_1_sec_interval = joined_df.withColumn(
        "sequence", F.expr("sequence(recording_started_at, new_recording_ended_at, interval 1 second)")
    )
    df_with_emitted_at_1_sec_interval = df_with_emitted_at_1_sec_interval.withColumn(
        "new_emitted_at", F.explode(F.col("sequence"))
    )
    return df_with_emitted_at_1_sec_interval.select(
        F.date_trunc("second", F.col("new_emitted_at")).alias("new_emitted_at"),
        F.col("vin").alias("indexed_vin"),
        F.col("recording_started_at").alias("indexed_recording_started_at"),
        "drive_url",
        "split_url",
        "source_fleet",
    ).withColumn("emitted_at_unix", F.unix_timestamp(F.col("new_emitted_at")))


def read_delta_table(spark_session: SparkSession, catalog: str, schema: str, table: str) -> DataFrame:
    """Read a delta table into a DataFrame."""
    return spark_session.read.format("delta").table(f"{catalog}.{schema}.{table}")


def get_gnss_base_dataset(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Get the gnss base dataset."""
    gnss_base_dataset_df = read_delta_table(spark_session, silver_catalog, "gnss_groundtruth", "gnss_base_dataset")

    # Define all the new columns and their transformations
    transformations = {
        "street": F.when(F.col("street") == "", F.lit(None)).otherwise(F.col("street")),
        "emitted_at_tai": (F.date_trunc("second", F.col("emitted_at")))
        + F.expr(f"INTERVAL {str(_UTC_TO_TAI_SECONDS)} SECOND"),
    }

    for cols in _GNSS_GT_REQUIRED_COLUMNS:
        if cols not in gnss_base_dataset_df.columns and cols != "emitted_at_tai":
            transformations[cols] = F.lit(None)

    # Use withColumns to add all new columns at once
    gnss_base_dataset_df = gnss_base_dataset_df.withColumns(transformations)

    # Select only the required columns in the final step
    return gnss_base_dataset_df.select([col for col in _GNSS_GT_REQUIRED_COLUMNS])


def get_vdso_1hz_dataset(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Get the gnss base dataset."""
    vdso_1hz_df = read_delta_table(spark_session, silver_catalog, "drive_time_series", "vdso_1hz")
    vdso_1hz_df = vdso_1hz_df.select(
        "emitted_at", "vin", "recorded_at", (F.col("absolute_velocity") * 3.6).alias("ego_speed")
    )
    vdso_1hz_df = vdso_1hz_df.withColumn(
        "ego_direction",
        F.when(F.col("ego_speed") > 0, "frontwards")
        .when(F.col("ego_speed") < 0, "backwards")
        .when(F.col("ego_speed") == 0, "static")
        .otherwise("undefined"),
    )
    return vdso_1hz_df


def create_the_situation_dataset_df(
    gnss_base_dataset_df: DataFrame,
    vdso_1hz_df: DataFrame,
    second_interval_wise_emitted_at_col: DataFrame,
    datamanagement_distribution_parameters_df: DataFrame,
    time_of_day_df: DataFrame,
) -> DataFrame:
    """Create the situation dataset."""

    gnss_base_dataset_df_intermediate = gnss_base_dataset_df.alias("gnssi").join(
        datamanagement_distribution_parameters_df.alias("ddp"),
        (F.col("gnssi.split_hash") == F.col("ddp.file_hash")),
        "left",
    )
    gnss_base_dataset_df_intermediate = gnss_base_dataset_df_intermediate.select(
        [col for col in gnss_base_dataset_df_intermediate.columns if col != "file_hash"]
    )

    gnss_base_dataset_df_staging = gnss_base_dataset_df_intermediate.alias("gnsss").join(
        time_of_day_df.alias("gtd"),
        (F.col("gnsss.split_hash") == F.col("gtd.file_hash")),
        "left",
    )

    gnss_base_dataset_df_staging = gnss_base_dataset_df_staging.withColumn(
        "time_of_day", F.coalesce(F.col("time_of_day"), F.col("daytime"))
    ).select([col for col in gnss_base_dataset_df_staging.columns if col not in ["file_hash", "daytime"]])

    situation_dataset_df_intermediate = second_interval_wise_emitted_at_col.alias("sie").join(
        gnss_base_dataset_df_staging.alias("gnss"),
        (F.col("sie.indexed_vin") == F.col("gnss.vin"))
        & (F.col("sie.new_emitted_at") == F.col("emitted_at_tai"))
        & (F.col("sie.indexed_recording_started_at") == (F.col("gnss.recording_started_at"))),
        "left",
    )
    situation_dataset_df_intermediate = situation_dataset_df_intermediate.select(
        [col for col in situation_dataset_df_intermediate.columns if col not in _PRIMARY_JOIN_COLUMNS]
    )

    situation_dataset_df = situation_dataset_df_intermediate.alias("sd").join(
        vdso_1hz_df.alias("vdso"),
        (F.col("sd.indexed_vin") == F.col("vdso.vin"))
        & (F.col("sd.new_emitted_at") == (F.date_trunc("second", F.col("vdso.emitted_at"))))
        & (F.col("sd.indexed_recording_started_at") == F.col("vdso.recorded_at")),
        "left",
    )
    situation_dataset_df = situation_dataset_df.select(
        [col for col in situation_dataset_df.columns if col not in _PRIMARY_JOIN_COLUMNS]
    )
    return (
        situation_dataset_df.withColumnRenamed("new_emitted_at", "emitted_at")
        .withColumnRenamed("indexed_vin", "vin")
        .withColumnRenamed("indexed_recording_started_at", "recording_started_at")
        .withColumn("raw_measurement_data_state", F.lit("ACTIVE"))
    )


def get_datamanagement_distribution_parameters_dataset(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Get the waether base dataset."""
    datamanagement_distribution_parameters_df = read_delta_table(
        spark_session, silver_catalog, "mdd", "datamanagement_distribution_parameters"
    )
    datamanagement_distribution_parameters_df = datamanagement_distribution_parameters_df.select(
        "file_hash", "weather", "daytime"
    )

    transformations = {
        "weather_atmospheric_condition": F.when(F.col("weather").isin("cloudy", "clear+dry"), "Clear")
        .when(F.col("weather") == "foggy+dry", "Fog")
        .when(F.col("weather").isin("hail", "heavy rain", "snowy", "foggy+rainy", "unknown"), "Unknown")
        .otherwise(None),
        "weather_precipitation": F.when(F.col("weather").isin("clear+dry", "foggy+dry", "cloudy"), "Dry")
        .when(F.col("weather") == "hail", "Hail")
        .when(F.col("weather") == "heavy rain", "HeavyRain")
        .when(F.col("weather") == "foggy+rainy", "Rain")
        .when(F.col("weather") == "snowy", "Snow")
        .when(F.col("weather") == "unknown", "Unknown")
        .otherwise(None),
        "weather_sky": F.when(F.col("weather").isin("cloudy", "hail", "heavy rain", "snowy"), "Overcast")
        .when(F.col("weather") == "clear+dry", "Clear")
        .when(F.col("weather").isin("foggy+rainy", "foggy+dry", "unknown"), "Unknown")
        .otherwise(None),
    }

    datamanagement_distribution_parameters_df = datamanagement_distribution_parameters_df.withColumns(transformations)

    return datamanagement_distribution_parameters_df.select([col for col in _WEATHER_REQUIRED_COLUMNS])


def get_time_of_day_dataset(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Get the time of day dataset."""
    time_of_day_df = read_delta_table(spark_session, silver_catalog, "mdd", "time_of_day")
    return time_of_day_df.select("file_hash", "time_of_day")


def deduplicate(merged_df: DataFrame) -> DataFrame:
    """Deduplicates the recordings namespaces."""
    return (
        merged_df.withColumn(
            "instance",
            F.row_number().over(Window.partitionBy("vin", "emitted_at").orderBy(F.col("recording_started_at").desc())),
        )
        .filter("instance = 1")
        .filter("vin IS NOT NULL")
        .filter("emitted_at IS NOT NULL")
        .select([col for col in merged_df.columns if col != "instance"])
    )


def execute(spark_session: SparkSession, silver_catalog: str, target_catalog: str) -> None:
    """Execute the situation dataset pipeline."""
    target_schema = "drive_time_series"
    target_table = "situation_dataset_full"

    try:
        # Create the gnss_gt dataframe
        gnss_base_dataset_df = get_gnss_base_dataset(spark_session, silver_catalog)

        # Create the vdso_1hz dataframe
        vdso_1hz_df = get_vdso_1hz_dataset(spark_session, silver_catalog)

        # Create the indexed dataframe
        new_emitted_at_timestamp_df = get_one_second_interval_wise_emitted_at_col(spark_session, silver_catalog)

        datamanagement_distribution_parameters_df = get_datamanagement_distribution_parameters_dataset(
            spark_session, silver_catalog
        )

        # Create the time_of_day dataframe
        time_of_day_df = get_time_of_day_dataset(spark_session, silver_catalog)

        # Create the situation_dataset dataframe
        situation_dataset_df = create_the_situation_dataset_df(
            gnss_base_dataset_df,
            vdso_1hz_df,
            new_emitted_at_timestamp_df,
            datamanagement_distribution_parameters_df,
            time_of_day_df,
        )

        # Read target schema
        target_tbl_df = read_delta_table(spark_session, target_catalog, target_schema, target_table)

        # Schema Enforcement by type casting
        situation_dataset_df = situation_dataset_df.select(
            *[
                F.col(field.name).cast(field.dataType)
                for field in target_tbl_df.schema.fields
                if field.name in situation_dataset_df.columns
            ],
            *[
                F.lit(None).alias(field.name).cast(field.dataType)
                for field in target_tbl_df.schema.fields
                if field.name not in situation_dataset_df.columns
            ],
        )

        # deduplication of the situation_dataset dataframe
        situation_dataset_df = deduplicate(situation_dataset_df)

        # write the situation dataset full into the target
        situation_dataset_df.write.format("delta").mode("overwrite").saveAsTable(
            f"{target_catalog}.{target_schema}.{target_table}"
        )

    except Exception as e:
        tb_exception = traceback.TracebackException.from_exception(e)
        print("Custom Traceback:")
        for line in tb_exception.format():
            print(line, end="")
        raise e


if __name__ == "__main__":
    args = parse_job_parameters()
    spark_session = SparkSession.builder.getOrCreate()
    execute(spark_session, args.silver_catalog, args.target_catalog)
