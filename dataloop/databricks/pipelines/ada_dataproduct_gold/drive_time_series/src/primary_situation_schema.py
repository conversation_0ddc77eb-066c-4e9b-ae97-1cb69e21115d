"""Schema updates for primary_situation_dataset."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
from dataclasses import dataclass

from pyspark.sql import SparkSession


def parse_job_parameters() -> argparse.Namespace:
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-t", "--target_catalog", required=True, type=str)
    parser.add_argument("-ts", "--target_schema", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def _update_table_tag(spark_session: SparkSession, table_name: str) -> None:
    spark_session.sql(
        f"ALTER TABLE {table_name} SET TAGS ('responsible_domain'='Data Delivery','responsible_team'='Analytics Platform','refresh_interval'='P1D')"  # noqa: E501
    )


@dataclass
class ColumnMetadata:
    """This is a Column Meta data holder data class."""

    name: str
    data_type: str
    comment: str


@dataclass
class ColumnData:
    """This is a Column data holder data class."""

    Colname: str


def _column_exists_in_table(
    spark_session: SparkSession, table_name: str, column_metadata: list[ColumnMetadata]
) -> list[ColumnMetadata]:
    """Checks if columns exist in the table and returns the missing columns."""

    # Fetch the existing columns in one call (only once)
    existing_columns = set(spark_session.table(table_name).columns)

    # Return the list of columns that do not exist in the table
    return [column for column in column_metadata if column.name not in existing_columns]


def _check_and_add_columns_once(
    spark_session: SparkSession, column_metadata: list[ColumnMetadata], table_name: str
) -> None:
    """Efficiently checks if columns exist in the table and adds missing ones (one-time usage)."""

    # Get columns to add by checking with the `column_exists_in_table` helper function
    columns_to_add = _column_exists_in_table(spark_session, table_name, column_metadata)

    if columns_to_add:
        # Generate a single ALTER TABLE SQL statement for the missing columns
        alter_statements = ", ".join(
            [f"{column.name} {column.data_type} COMMENT '{column.comment}'" for column in columns_to_add]
        )

        # Construct the SQL query
        alter_query = f"ALTER TABLE {table_name} ADD COLUMNS ({alter_statements})"

        print(alter_query)
        # Execute the ALTER TABLE query
        spark_session.sql(alter_query)
        print(f"Executed query: {alter_query}")
    else:
        print("No new columns to add.")


def _column_exists_in_table_to_drop(
    spark_session: SparkSession, table_name: str, column_data: list[ColumnData]
) -> list[ColumnData]:
    """Checks if columns exist in the table and returns the missing columns."""

    # Fetch the existing columns in one call (only once)
    existing_columns = set(spark_session.table(table_name).columns)

    # Return the list of columns that do not exist in the table
    return [column for column in column_data if column.Colname in existing_columns]


def _check_and_drop_columns_once(spark_session: SparkSession, column_data: list[ColumnData], table_name: str) -> None:
    """Efficiently checks if columns exist in the table and adds missing ones (one-time usage)."""

    columns_to_drop = _column_exists_in_table_to_drop(spark_session, table_name, column_data)

    if columns_to_drop:
        # Alter table delete column properties
        spark_session.sql(
            """ALTER TABLE gold.drive_time_series.situation_dataset_full SET TBLPROPERTIES (
                          'delta.columnMapping.mode' = 'name',
                          'delta.minReaderVersion' = '2',
                          'delta.minWriterVersion' = '5'
                          )
                          """
        )
        # Generate a single SQL statement for dropping the columns
        alter_query = "; ".join(
            [f"ALTER TABLE {table_name} DROP COLUMN {column.Colname}" for column in columns_to_drop]
        )

        # Construct the SQL query for dropping the columns
        # alter_query = f"ALTER TABLE {table_name} {drop_statements}"

        # Execute the ALTER TABLE query
        spark_session.sql(alter_query)
        print(f"Executed query: {alter_query}")
    else:
        print("No column to drop.")


def create_situation_dataset_tbl(spark_session: SparkSession) -> None:
    """Create trajectory frames table."""

    spark_session.sql(
        """
        CREATE TABLE IF NOT EXISTS situation_dataset_full (
            emitted_at TIMESTAMP COMMENT 'Time when the data was emitted at its source',
            emitted_at_unix BIGINT COMMENT 'Time when the data was emitted at its source formatted as unix timestamp',
            vin STRING COMMENT 'Vehicle Identification Number',
            recording_started_at TIMESTAMP COMMENT 'Measurement record date from gmdm.json',
            plate STRING COMMENT 'License plate of vehicle',
            drive_hash STRING COMMENT 'SHA2-256 of the drive/recording',
            drive_url STRING COMMENT 'Direct link to the primary storage location of the drive/recording file (gmdm.json)',
            split_hash STRING COMMENT 'SHA2-256 of the split',
            split_url STRING COMMENT 'Direct link to the primary storage location of the split (*.scene) file',
            container_hash STRING COMMENT 'SHA2-256 of the HDF5 file where the value was extracted from',
            container_url STRING COMMENT 'Direct link to the primary storage location of the container file (*.h5)',
            raw_measurement_data_state STRING COMMENT 'This determines the state of raw measurement data files',
            source_fleet STRING COMMENT 'Defines the data source system',
            latitude DOUBLE COMMENT 'The north south position of a point on the surface of the Earth in degrees using WGS84 geodetic system',
            longitude DOUBLE COMMENT 'The east west position of a point on the surface of the Earth in degrees using WGS84 geodetic system',
            altitude FLOAT COMMENT 'Vertical distance from sea level as used in using WGS84 geodetic system',
            country STRING COMMENT 'The country in which the the current frame was recorded',
            country_iso_alpha3 STRING COMMENT 'The country for the current frame represented as ISO 3166-1 alpha-3',
            state STRING COMMENT 'The state where the the current frame was recorded',
            city STRING COMMENT 'The city where the the current frame was recorded',
            street STRING COMMENT 'The street in which the the current frame was recorded',
            road_type STRING COMMENT 'The main road type for the current frame',
            lane_count INT COMMENT 'The number of lanes visible in the current frame',
            speed_limit INT COMMENT 'The allowed speed_limit for the current frame',
            curvature FLOAT COMMENT 'The road curvature in the current frame',
            is_roundabout BOOLEAN COMMENT 'Whether the current frame is in a roundabout',
            is_tunnel BOOLEAN COMMENT 'Whether the current frame is in a tunnel',
            is_ramp BOOLEAN COMMENT 'Whether the current frame is on a ramp',
            is_parking_area BOOLEAN COMMENT 'Whether the current frame is in a parking area',
            is_parking_garage BOOLEAN COMMENT 'Whether the current frame is in a parking garage',
            is_paved BOOLEAN COMMENT 'Whether the current frame is on a paved road',
            time_of_day STRING COMMENT 'The daytime of the current frame',
            weather_atmospheric_condition STRING COMMENT 'Atmospheric condition of the current frame',
            weather_precipitation STRING COMMENT 'Precipitation of the current frame',
            weather_sky STRING COMMENT 'Sky condition of the current frame',
            geography STRING COMMENT 'The geography of the current frame',
            ego_speed FLOAT COMMENT 'The recording vehicles speed',
            ego_direction STRING COMMENT 'The recording vehicles direction',
            archived_at TIMESTAMP COMMENT 'When was the referenced file archived',
            deleted_at TIMESTAMP COMMENT 'When was the referenced file deleted',
            created_at TIMESTAMP COMMENT 'When was this row created by the workflow',
            created_by STRING COMMENT 'Who (human user or service principal) created this row initially',
            modified_at TIMESTAMP COMMENT 'When was this row the last time updated',
            modified_by STRING COMMENT 'Who (human user or service principal) last modified this row'
        )
        CLUSTER BY (emitted_at, vin)
        TBLPROPERTIES (
            'delta.enableDeletionVectors' = 'true',
            'delta.dataSkippingStatsColumns' = 'emitted_at, vin, recording_started_at'
        )"""  # noqa: E501
    )

    _update_table_tag(spark_session, "situation_dataset_full")

    # This method call is only for internal use & one time run &
    # it should be cleaned up & not going to stay in the code base.
    columns_to_add = [
        ColumnMetadata(
            name="weather_atmospheric_condition",
            data_type="STRING",
            comment="Atmospheric condition of the current frame",
        ),
        ColumnMetadata(name="weather_precipitation", data_type="STRING", comment="Precipitation of the current frame"),
        ColumnMetadata(name="weather_sky", data_type="STRING", comment="Sky condition of the current frame"),
    ]

    # Call the function to update the table with new columns (once)
    _check_and_add_columns_once(spark_session, columns_to_add, "situation_dataset_full")

    columns_to_drop = [
        ColumnData(Colname="weather"),
    ]

    # Call the function to alter the table with drop columns
    _check_and_drop_columns_once(spark_session, columns_to_drop, "situation_dataset_full")


def create_or_update_schema(spark_session: SparkSession, gold_catalog: str, target_schema: str) -> None:
    """Create tables in the target schema."""

    spark_session.catalog.setCurrentCatalog(gold_catalog)
    spark_session.catalog.setCurrentDatabase(target_schema)

    create_situation_dataset_tbl(spark_session)


if __name__ == "__main__":
    args = parse_job_parameters()
    spark_session = SparkSession.builder.getOrCreate()
    create_or_update_schema(spark_session, args.target_catalog, args.target_schema)
