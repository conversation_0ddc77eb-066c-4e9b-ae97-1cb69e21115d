"""Schema updates for drive-time-series."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse

from pyspark.sql import SparkSession


def parse_job_parameters() -> argparse.Namespace:
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-t", "--target_catalog", required=True, type=str)
    parser.add_argument("-ts", "--target_schema", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def _update_table_tag(spark_session: SparkSession, table_name: str) -> None:
    spark_session.sql(
        f"ALTER TABLE {table_name} SET TAGS ('responsible_domain'='Data Delivery','responsible_team'='Analytics Platform','refresh_interval'='P1D')"  # noqa: E501
    )


def create_trajectory_frames_tbl(spark_session: SparkSession) -> None:
    """Create trajectory frames table."""

    spark_session.sql(
        """
        CREATE TABLE IF NOT EXISTS trajectory_frames (
            recorded_at TIMESTAMP COMMENT 'Timestamp of the frame recording',
            timestamp_closest_second BIGINT COMMENT 'Closest second to the frame recording timestamp',
            recording_started_at TIMESTAMP COMMENT 'Timestamp of the drive recording start',
            scene_started_at TIMESTAMP COMMENT 'Timestamp of the scene start',
            frame_number INT COMMENT 'Frame number in the scene',
            fc1_distorted_view STRING COMMENT 'SHA2-256bit of the image file with distorted view for camera stream FC1',
            fc1_label_view STRING COMMENT 'SHA2-256bit of the image file with label view for camera stream FC1',
            fc1_preview_url STRING COMMENT 'Reference to file for downloading of latest active source in TDS',
            tvfront_distorted_view STRING COMMENT 'SHA2-256bit of the image file with distorted view for camera stream TVFRONT',
            tvfront_label_view STRING COMMENT 'SHA2-256bit of the image file with label view for camera stream TVFRONT',
            tvfront_preview_url STRING COMMENT 'Reference to file for downloading of latest active source in TDS',
            tvright_distorted_view STRING COMMENT 'SHA2-256bit of the image file with distorted view for camera stream TVRight',
            tvright_label_view STRING COMMENT 'SHA2-256bit of the image file with label view for camera stream TVRight',
            tvright_preview_url STRING COMMENT 'Reference to file for downloading of latest active source in TDS',
            tvrear_distorted_view STRING COMMENT 'SHA2-256bit of the image file with distorted view for camera stream TVRear',
            tvrear_label_view STRING COMMENT 'SHA2-256bit of the image file with label view for camera stream TVRear',
            tvrear_preview_url STRING COMMENT 'Reference to file for downloading of latest active source in TDS',
            tvleft_distorted_view STRING COMMENT 'SHA2-256bit of the image file with distorted view for camera stream TVLeft',
            tvleft_label_view STRING COMMENT 'SHA2-256bit of the image file with label view for camera stream TVLeft',
            tvleft_preview_url STRING COMMENT 'Reference to file for downloading of latest active source in TDS',
            latitude DOUBLE COMMENT 'Latitude of the frame recording',
            longitude DOUBLE COMMENT 'Longitude of the frame recording',
            vin STRING COMMENT 'Vehicle Identification Number',
            plate STRING COMMENT 'License plate of the vehicle',
            country STRING COMMENT 'Country of the drive recording',
            city STRING COMMENT 'City of the drive recording',
            lane_count STRING,
            speed_limit STRING,
            road_category STRING,
            is_roundabout STRING,
            is_tunnel STRING,
            is_ramp STRING,
            time_of_day STRING COMMENT 'The time of day, one of Day, Night, Dawn or Dusk',
            split_hash STRING COMMENT 'SHA2-256bit of the scene file',
            drive_hash STRING COMMENT 'SHA2-256bit of the gmdm file'
        )
        CLUSTER BY (recorded_at, vin)
        TBLPROPERTIES (
            'delta.enableDeletionVectors' = 'true',
            'delta.dataSkippingStatsColumns' = 'recorded_at, vin, recording_started_at'
        )"""  # noqa: E501
    )

    _update_table_tag(spark_session, "trajectory_frames")


def create_or_update_schema(spark_session: SparkSession, gold_catalog: str, target_schema: str) -> None:
    """Create tables in the target schema."""

    spark_session.catalog.setCurrentCatalog(gold_catalog)
    spark_session.catalog.setCurrentDatabase(target_schema)

    create_trajectory_frames_tbl(spark_session)


if __name__ == "__main__":
    args = parse_job_parameters()
    spark_session = SparkSession.builder.getOrCreate()
    create_or_update_schema(spark_session, args.target_catalog, args.target_schema)
