"""Trajectory frames."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse

from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col


def parse_job_parameters() -> argparse.Namespace:
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-s", "--silver_catalog", required=True, type=str)
    parser.add_argument("-t", "--target_catalog", required=True, type=str)
    parser.add_argument("-ts", "--target_schema", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def _get_frames_fc1_ts_corrected(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Join drive_timetamps and frames_fc1 to get corrected timestamp."""

    drive_timestamps_tbl = spark_session.table(f"{silver_catalog}.drive_time_series.drive_timestamps").alias("dt")
    frames_fc1_tbl = spark_session.table(f"{silver_catalog}.drive_time_series.frames_fc1").alias("fc1_r")

    frames_fc1_tbl_ts_corrected = (
        frames_fc1_tbl.join(drive_timestamps_tbl, ["recording_started_at", "vin"], how="left")
        .withColumn(
            "timestamp_closest_second", col("fc1_r.timestamp_closest_second") + col("dt.start_trj_ahead_of_fc1")
        )
        .select("fc1_r.*", "timestamp_closest_second")
    )
    return frames_fc1_tbl_ts_corrected


def _get_cameras_joined(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Join all the camera frames tables."""

    frames_fc1_tbl = _get_frames_fc1_ts_corrected(spark_session, silver_catalog)

    frames_tvfront_tbl = spark_session.table(f"{silver_catalog}.drive_time_series.frames_tvfront").alias("tvf")
    frames_tvright_tbl = spark_session.table(f"{silver_catalog}.drive_time_series.frames_tvright").alias("tvright")
    frames_tvrear_tbl = spark_session.table(f"{silver_catalog}.drive_time_series.frames_tvrear").alias("tvrear")
    frames_tvleft_tbl = spark_session.table(f"{silver_catalog}.drive_time_series.frames_tvleft").alias("tvl")

    time_of_day_tbl = spark_session.table(f"{silver_catalog}.mdd.time_of_day").alias("tod")

    # keeping the col names consistent with existing sbx tbl, need to update once data is verified.
    cameras_joined = (
        frames_fc1_tbl.alias("fc1")
        .join(frames_tvfront_tbl, ["scene_started_at", "vin", "frame_number"], how="left")
        .join(frames_tvright_tbl, ["scene_started_at", "vin", "frame_number"], how="left")
        .join(frames_tvrear_tbl, ["scene_started_at", "vin", "frame_number"], how="left")
        .join(frames_tvleft_tbl, ["scene_started_at", "vin", "frame_number"], how="left")
        .join(time_of_day_tbl, col("fc1.split_hash") == col("tod.file_hash"), how="left")
        .select(
            "fc1.recorded_at",
            "fc1.timestamp_closest_second",
            "fc1.vin",
            "fc1.recording_started_at",
            "fc1.scene_started_at",
            "fc1.frame_number",
            "fc1.split_hash",
            "fc1.drive_hash",
            col("fc1.distorted_image_file_hash").alias("fc1_distorted_view"),
            col("fc1.label_image_file_hash").alias("fc1_label_view"),
            col("fc1.preview_tds_file_url").alias("fc1_preview_url"),
            col("tvf.distorted_image_file_hash").alias("tvfront_distorted_view"),
            col("tvf.label_image_file_hash").alias("tvfront_label_view"),
            col("tvf.preview_tds_file_url").alias("tvfront_preview_url"),
            col("tvright.distorted_image_file_hash").alias("tvright_distorted_view"),
            col("tvright.label_image_file_hash").alias("tvright_label_view"),
            col("tvright.preview_tds_file_url").alias("tvright_preview_url"),
            col("tvrear.distorted_image_file_hash").alias("tvrear_distorted_view"),
            col("tvrear.label_image_file_hash").alias("tvrear_label_view"),
            col("tvrear.preview_tds_file_url").alias("tvrear_preview_url"),
            col("tvl.distorted_image_file_hash").alias("tvleft_distorted_view"),
            col("tvl.label_image_file_hash").alias("tvleft_label_view"),
            col("tvl.preview_tds_file_url").alias("tvleft_preview_url"),
            "tod.time_of_day",
        )
    )
    return cameras_joined


def get_data_for_trajectory_frames(spark_session: SparkSession, silver_catalog: str) -> DataFrame:
    """Get final table for trajectory frames."""

    cameras_joined = _get_cameras_joined(spark_session, silver_catalog)

    drive_trajectory_tbl = spark_session.table(f"{silver_catalog}.drive_time_series.drive_trajectory").alias("trj")
    dm_drive_info_tbl = spark_session.table(f"{silver_catalog}.mdd.datamanagement_drive_info").alias("di")

    joined_df = (
        drive_trajectory_tbl.join(
            cameras_joined.alias("cmj"), ["recording_started_at", "vin", "timestamp_closest_second"], how="inner"
        )
        .join(dm_drive_info_tbl, col("cmj.drive_hash") == col("di.file_hash"), how="left")
        .select(
            "cmj.*",
            "di.plate",
            "trj.latitude",
            "trj.longitude",
            "trj.country",
            "trj.city",
            "trj.lane_count",
            "trj.speed_limit",
            "trj.road_category",
            "trj.is_roundabout",
            "trj.is_tunnel",
            "trj.is_ramp",
            "trj.iso_country_code_alpha3",
            "trj.street",
            "trj.is_paved",
        )
    )
    return joined_df


def execute(spark_session: SparkSession, silver_catalog: str, gold_catalog: str) -> None:
    """Execute the trajectory frames pipeline."""

    target_tbl_name = f"{gold_catalog}.drive_time_series.trajectory_frames"
    target_tbl = DeltaTable.forName(spark_session, target_tbl_name)

    results_df = get_data_for_trajectory_frames(spark_session, silver_catalog)
    results_df.write.format("delta").mode("overwrite").option("mergeSchema", "true").saveAsTable(target_tbl_name)
    target_tbl.optimize().executeCompaction()


if __name__ == "__main__":
    args = parse_job_parameters()
    spark_session = SparkSession.builder.getOrCreate()
    execute(spark_session, args.silver_catalog, args.target_catalog)
