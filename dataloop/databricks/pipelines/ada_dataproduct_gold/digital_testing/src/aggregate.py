#!/usr/bin/env python3
"""Aggregate Digital Testing reports."""
__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
import sys
from typing import Sequence

from dbxlib.databricks import get_dbx_env_catalog
from dbxlib.tables.metadata import update_table_metadata
from delta.tables import DeltaTable
from pyspark.sql import SparkSession
from pyspark.sql.functions import (
    col,
    count,
    expr,
    sum,
    window,
)
from pyspark.sql.types import DoubleType, LongType, StructField, StructType, TimestampType

# Constants
AGGREGATION_WINDOW = "1 day"
DBX_SCHEMA = "digital_testing"

# Table metadata
RESPONSIBLE_TEAM = "Cloud Test Execution"
RESPONSIBLE_DOMAIN = "SoCoTeC"
REFRESH_INTERVAL = "P1D"
DBX_TABLE_DESCRIPTION = "This table contains daily statistics of Digital Testing."
DBX_TABLE_COLUMN_COMMENTS = {
    "window": "Start and end of time window.",
    "total_recompute_duration_h": "Total duration of Recompute in hours.",
    "recompute_count": "Count of Recomputes.",
}
COLUMN_TIMESYSTEMS = {
    "window": "UTC",
}

_logger = logging.getLogger(__name__)


def setup_logging(loglevel: int) -> None:
    """Configure basic logging.

    Args:
        loglevel (int): minimum loglevel for emitting messages

    Returns:
        None
    """
    logformat = "[%(asctime)s] %(levelname)s:%(name)s:%(lineno)d: %(message)s"
    logging.basicConfig(level=loglevel, stream=sys.stdout, format=logformat, datefmt="%Y-%m-%d %H:%M:%S")


def _parse_args(args: Sequence[str]) -> argparse.Namespace:
    """Parse command line parameters.

    Args:
        args (List[str]): command line parameters as list of strings (for example  ``["--help"]``).

    Returns:
        :obj:`argparse.Namespace`: command line parameters namespace
    """
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument("--source-table", help="Source table")
    parser.add_argument("--target-table", help="Target table")
    parser.add_argument(
        "-v",
        "--verbose",
        dest="loglevel",
        help="set loglevel to INFO",
        action="store_const",
        const=logging.INFO,
    )
    parser.add_argument(
        "-vv",
        "--very-verbose",
        dest="loglevel",
        help="set loglevel to DEBUG",
        action="store_const",
        const=logging.DEBUG,
    )
    return parser.parse_args(args)


def prepare_table(spark: SparkSession, target_table_name: str, schema: StructType) -> None:
    """Ensure that the target table exists.

    Args:
        spark (SparkSession): The Spark session to use.
        target_table_name (str): The name of the target table.
        schema (StructType): The schema of the target table.

    Returns:
        None
    """
    if not spark.catalog.tableExists(f"{target_table_name}"):
        spark.createDataFrame(
            spark.sparkContext.emptyRDD(),
            schema=schema,
        ).write.format(
            "delta"
        ).saveAsTable(f"{target_table_name}")
    else:
        existing_schema = spark.table(target_table_name).schema
        if set(schema.fieldNames()) != set(existing_schema.fieldNames()):
            df = spark.createDataFrame(spark.sparkContext.emptyRDD(), schema)
            df.write.format("delta").mode("append").option("mergeSchema", "true").saveAsTable(target_table_name)


def main(args: Sequence[str]) -> None:
    """Ingest digital testing reports.

    Args:
        args (List[str]): command line parameters as list of strings (for example  ``["--help"]``).
    """
    parsed_args = _parse_args(args)
    setup_logging(parsed_args.loglevel)

    source_catalog = get_dbx_env_catalog("silver")
    target_catalog = get_dbx_env_catalog("gold")

    source_table_name_full = f"{source_catalog}.{DBX_SCHEMA}.{parsed_args.source_table}"
    _logger.info("Transforming data from table: %s", source_table_name_full)
    target_table_name_full = f"{target_catalog}.{DBX_SCHEMA}.{parsed_args.target_table}"
    _logger.info("Writing data into table: %s", target_table_name_full)

    spark: SparkSession = SparkSession.builder.getOrCreate()

    df = spark.read.table(f"{source_table_name_full}")
    df_aggregated = df.groupBy(window(col("created_at"), AGGREGATION_WINDOW)).agg(
        sum("recompute_duration_s").alias("total_recompute_duration_s"),
        count("*").alias("recompute_count"),
    )
    df_aggregated = df_aggregated.withColumn("total_recompute_duration_h", expr("total_recompute_duration_s / 3600"))
    df_aggregated = df_aggregated.select("window", "total_recompute_duration_h", "recompute_count")

    schema = StructType(
        [
            StructField(
                "window",
                StructType(
                    [
                        StructField("start", TimestampType(), True),
                        StructField("end", TimestampType(), True),
                    ]
                ),
                True,
            ),
            StructField("total_recompute_duration_h", DoubleType(), True),
            StructField("recompute_count", LongType(), True),
        ]
    )

    prepare_table(spark, target_table_name_full, schema)
    target_table = DeltaTable.forName(spark, f"{target_table_name_full}")
    target_table.alias("target").merge(
        df_aggregated.alias("source"), "target.window = source.window"
    ).whenMatchedUpdateAll().whenNotMatchedInsertAll().execute()

    update_table_metadata(
        table=target_table_name_full,
        description=DBX_TABLE_DESCRIPTION,
        responsible_team=RESPONSIBLE_TEAM,
        responsible_domain=RESPONSIBLE_DOMAIN,
        refresh_interval=REFRESH_INTERVAL,
        column_descriptions=DBX_TABLE_COLUMN_COMMENTS,
        column_timesystems=COLUMN_TIMESYSTEMS,
    )


def run() -> None:
    """Entry point for console_scripts."""
    main(sys.argv[1:])


if __name__ == "__main__":
    run()
