"""Script for creating the recording table.

Maintainer: <PERSON><PERSON><PERSON><PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from argparse import ArgumentParser

from helper import DeltaTableReader, DeltaTableWriter
from logs import LoggerFactory
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F


# Recordings_and_Files_counting
class Recordings:
    """Class create Recordings table."""

    spark: SparkSession
    delta_write: DeltaTableWriter
    delta_read: DeltaTableReader

    def __init__(self, spark: SparkSession, read_tables: str, write_tables: str, logger: logging.Logger):
        """Initializes the Recordings with specific parameters.

        Args:
            spark (SparkSession): The active Spark session.
            read_tables (str): Dictionary of tables for reading operations.
            write_tables (str): Dictionary of tables for writing operations.
            logger (logging.Logger, optional): Logger instance. If not provided, a default logger will be used.
        """
        self.spark = spark
        self.read_tables = read_tables
        self.write_tables = write_tables
        self.logger = logger

        # Initialize DeltaTableReader and DeltaTableWriter with the logger
        self.delta_read: DeltaTableReader = DeltaTableReader(logger=self.logger, spark=self.spark)
        self.delta_write: DeltaTableWriter = DeltaTableWriter(logger=self.logger, spark=self.spark)

    def get_recordings(self) -> DataFrame:
        """Reads from silver tables and aggregates data."""
        # Aggregation query to create gold table.This table contains aggragted value that can be shown on the dashboard
        result = (
            self.delta_read.read(self.read_tables)
            .withColumn("created_at_formatted", F.date_format(F.col("created_at").cast("timestamp"), "yyyy-MM-dd"))
            .groupBy("file_hash", "created_at_formatted")
            .agg(
                F.first("file_hash").alias("sha"),
                F.max("created_at_formatted").alias("created_at"),
                F.max("VIN").alias("VIN"),
                F.max("record_countries")[0].alias("Country"),
                F.max("plate").alias("Plate"),
                F.avg("scene_count_total").alias("SceneCount"),
                F.max("status").alias("Upload"),
                F.avg(F.when(F.col("namespace_name") == "split-ingest", F.col("counted_scenes"))).alias("Metaparser"),
                F.avg(F.when(F.col("namespace_name") == "dsp_de_split_extracted", F.col("counted_scenes"))).alias(
                    "Extraction"
                ),
                F.avg(F.when(F.col("namespace_name") == "active_learning", F.col("counted_scenes"))).alias(
                    "Active_Learning"
                ),
                F.avg(F.when(F.col("namespace_name") == "dsp_selection", F.col("counted_scenes"))).alias("Selection"),
                F.avg(F.when(F.col("namespace_name") == "dsp_sequence_info", F.col("counted_scenes"))).alias(
                    "Sequencing"
                ),
                F.min("tags").alias("tags"),
            )
            .orderBy(F.col("created_at").desc())
        )
        return result.drop("created_at_formatted")

    def write_recordings(self, recordings: DataFrame) -> None:
        """Write to recordings table."""
        self.delta_write.overwrite(recordings, self.write_tables, True)


if __name__ == "__main__":
    # Recordings Stats and Files Counting
    logger_factory = LoggerFactory()  # This creates an instance of LoggerFactory
    log = logger_factory.get_logger()  # Use the instance to get a logger
    spark = SparkSession.builder.getOrCreate()
    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    args = parser.parse_args()
    # Define the output tables
    write_tables = {
        "dev": "gold_dev.dmv_stats.dmv_stats_recordings",
        "qa": "gold_qa.dmv_stats.dmv_stats_recordings",
        "prod": "gold.dmv_stats.dmv_stats_recordings",
    }

    read_tables = "silver.dmv_stats.dmv_stats_recordings"

    # Artifacts Statistics
    recordings = Recordings(
        spark=spark,
        read_tables=read_tables,
        write_tables=write_tables[args.env],
        logger=log,
    )
    df = recordings.get_recordings()
    recordings.write_recordings(df)
