resources:
  jobs:
    dmv_stats_dmv_stats-recordings_pipeline:
      permissions:
        - group_name: "sg-pace-databricks-Data Delivery-Data-Management-Verticalization-(DMV)-admin"
          level: CAN_MANAGE_RUN
        - service_principal_name: ${var.run_sp}
          level: CAN_MANAGE_RUN

      name: DMV-STATS Recordings Pipeline
      schedule:
        quartz_cron_expression: '0 0 4 * * ?'  # Set a time when you want the job to run.
        timezone_id: UTC
        pause_status: ${var.schedule_pause_status}
      timeout_seconds: 3600 # 1 hour
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 1800 # 30 minutes

      tasks:
        - task_key: create_dmv_stats-recordings
          spark_python_task:
            python_file:  ../dmv-stats-recordings.py
            parameters:
            - -e
            - ${var.env}
          job_cluster_key: dmv_stats_job_cluster
          libraries:
            - pypi:
                package: pyspark==3.5.2

          description: Create recordings

      job_clusters:
        - job_cluster_key: dmv_stats_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            num_workers: ${var.num_workers}
            runtime_engine: PHOTON
            spark_env_vars:
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
            init_scripts:
              - volumes:
                  destination: /Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh
