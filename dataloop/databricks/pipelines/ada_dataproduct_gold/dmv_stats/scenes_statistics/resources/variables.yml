variables:
  env:
    default: dev
    description: "environment, dev, qa, prod"
  spark_version:
    default: "14.3.x-scala2.12"
    description: "Spark version"
  instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "default_D16ds_v4_rt14.3"
  num_workers:
    default: "4"
    description: "Number of workers"
  driver_instance_pool_id:
    description: "Memory-optimized non spot instance pool to be used for driver node"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"
  schedule_pause_status:
    default: "PAUSED"
    description: "Status of the schedule"
  ms_teams_alert_channel_email:
    default: "<EMAIL>"
    description: "MS Teams alert channel email for PROD alerts"
  run_sp:
    description: "run service principal"
    lookup:
      service_principal: "sp-pace-dataloop-dmv-${var.env}"
