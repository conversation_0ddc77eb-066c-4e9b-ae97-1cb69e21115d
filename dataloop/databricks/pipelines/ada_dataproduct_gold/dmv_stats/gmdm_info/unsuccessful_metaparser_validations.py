"""Script for creating Unsuccessful MetaParser Validations for DMV-STATS.

Maintainer: <PERSON><PERSON><PERSON><PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from argparse import ArgumentParser

from helper import DeltaTableReader, DeltaTableWriter

# DMV-STATS_Get_gdmd_file_info_LAST_DAY
from logs import LoggerFactory
from pyspark.sql import SparkSession
from pyspark.sql import functions as F
from pyspark.sql.utils import AnalysisException


class UnsuccessfulMetaParserValidations:
    """Class to process unsuccessful metaparser validations and store the results in the gold layer."""

    def __init__(
        self,
        spark: SparkSession,
        read_tables: str,
        write_tables: str,
        logger: logging.Logger,
    ):
        """Initializes the UnsuccessfulMetaParserValidations with specific parameters.

        Args:
            spark (SparkSession): The active Spark session.
            read_tables (dict): Dictionary of tables for reading operations.
            write_tables (dict): Dictionary of tables for writing operations.
            logger (logging.Logger, optional): Logger instance. If not provided, a default logger will be used.
        """
        self.spark = spark
        self.read_tables = read_tables
        self.write_tables = write_tables
        self.logger = logger

        # Initialize DeltaTableReader and DeltaTableWriter with the logger
        self.delta_read: DeltaTableReader = DeltaTableReader(logger=self.logger, spark=spark)
        self.delta_write: DeltaTableWriter = DeltaTableWriter(logger=self.logger, spark=spark)

    def process_and_store_validations(self) -> None:
        """Main method to process and store the metaparser validation data."""

        try:
            # Load the bronze layer DataFrame
            bronze_df = self.delta_read.read(self.read_tables)

            # Filter rows where namespace_name is "ingest-validation-result"
            filtered_df = bronze_df.filter(bronze_df["namespace_name"] == "ingest-validation-result")

            # Extract the status from the JSON and truncate the created_at column by day
            gold_df = (
                filtered_df.withColumn("status", F.get_json_object("json_document", "$.status"))
                .withColumn("created_at", F.date_trunc("DAY", "created_at"))
                .select("status", "created_at")
            )
            # Store it in the gold layer
            self.delta_write.overwrite(df=gold_df, table=self.write_tables)
        except AnalysisException as e:
            self.logger.error(f"Error during processing and storing validations: {e}")


if __name__ == "__main__":

    # Ingest Validation Result into Gold Layer
    logger_factory = LoggerFactory()  # This creates an instance of LoggerFactory
    log = logger_factory.get_logger()  # Use the instance to get a logger

    spark = SparkSession.builder.getOrCreate()
    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    args = parser.parse_args()

    # Define the output tables
    write_tables = {
        "dev": "gold_dev.dmv_stats.validations",
        "qa": "gold_qa.dmv_stats.validations",
        "prod": "gold.dmv_stats.validations",
    }

    read_tables = "bronze.mdm.mdd_namespaces_latest_v2"

    unsuccessful_metaparser_validations = UnsuccessfulMetaParserValidations(
        spark=spark,
        read_tables=read_tables,
        write_tables=write_tables[args.env],
        logger=log,
    )
    unsuccessful_metaparser_validations.process_and_store_validations()
