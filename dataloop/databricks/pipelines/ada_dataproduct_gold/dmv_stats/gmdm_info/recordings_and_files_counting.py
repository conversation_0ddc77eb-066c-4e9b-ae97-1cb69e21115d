"""Script for creating the recording status and the file counts (max in the last 30 days).

Maintainer: <PERSON><PERSON><PERSON><PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

# Get the gmdm files content:
from argparse import ArgumentParser
from typing import Dict

# DMV-STATS_Get_gdmd_file_info_LAST_DAY
from helper import DeltaTableReader, DeltaTableWriter
from logs import LoggerFactory
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F
from pyspark.sql.utils import AnalysisException


# Recordings_and_Files_counting
class RecordingsAndFilesProcessor:
    """Class to process and count recordings and scenes, and store the results."""

    spark: SparkSession
    delta_write: DeltaTableWriter
    delta_read: DeltaTableReader

    def __init__(self, spark: SparkSession, read_tables: Dict[str, str], write_tables: str, logger: logging.Logger):
        """Initializes the RecordingsAndFilesProcessor with specific parameters.

        Args:
            spark (SparkSession): The active Spark session.
            read_tables (dict): Dictionary of tables for reading operations.
            write_tables (dict): Dictionary of tables for writing operations.
            logger (logging.Logger, optional): Logger instance. If not provided, a default logger will be used.
        """
        self.spark = spark
        self.read_tables = read_tables
        self.write_tables = write_tables
        self.logger = logger

        # Initialize DeltaTableReader and DeltaTableWriter with the logger
        self.delta_read: DeltaTableReader = DeltaTableReader(logger=self.logger, spark=self.spark)
        self.delta_write: DeltaTableWriter = DeltaTableWriter(logger=self.logger, spark=self.spark)

    def process_recordings_stats(self) -> DataFrame:
        """Process recordings stats, join with scene counts and tags, and compute the status."""
        parents_df = self.delta_read.read(self.read_tables["parents"])
        file_entries_df = self.delta_read.read(self.read_tables["file_entries"]).filter(
            F.col("file_extension") == "scene"
        )
        counted_scenes = (
            parents_df.join(file_entries_df, "file_hash", "left")
            .groupBy("parent_file_hash")
            .agg(F.count("file_hash").alias("counted_scenes"))
        )

        get_tags = (
            self.delta_read.read(self.read_tables["co_driver_tags"])
            .groupBy("file_hash")
            .agg(F.collect_set(F.to_json(F.struct("tag_group", "tag_type", "tag_uid", "tag_value"))).alias("tags"))
        )

        parents_tags = (
            get_tags.join(self.delta_read.read(self.read_tables["parents"]), "file_hash", "left")
            .groupBy("parent_file_hash")
            .agg(F.max("tags").alias("tags"))
        )

        # Final selection for recordings_stats
        recordings_stats = (
            self.delta_read.read(self.read_tables["recordings_total_splits"])
            .alias("rts")
            .join(counted_scenes.alias("cs"), F.col("rts.file_hash") == F.col("cs.parent_file_hash"), "left")
            .join(parents_tags.alias("pt"), F.col("pt.parent_file_hash") == F.col("cs.parent_file_hash"), "left")
            .select(
                F.col("rts.file_hash"),
                F.col("rts.scene_count_total"),
                F.col("cs.counted_scenes"),
                F.col("rts.created_at"),
                F.when(F.col("rts.scene_count_total") == F.col("cs.counted_scenes"), "UploadComplete")
                .otherwise("UploadIncomplete")
                .alias("status"),
                F.col("pt.tags"),
            )
        )
        return recordings_stats

    def process_and_store_statistics(self) -> None:
        """Main method to process and store all recordings and files statistics."""
        try:
            # Process and compute recordings stats
            recordings_stats = self.process_recordings_stats()
            # Save the results in Delta Lake
            self.delta_write.overwrite(recordings_stats, self.write_tables)
        except AnalysisException as e:
            self.logger.error(f"Error processing and storing recordings statistics: {e}")


if __name__ == "__main__":

    # Recordings Stats and Files Counting
    logger_factory = LoggerFactory()  # This creates an instance of LoggerFactory
    log = logger_factory.get_logger()  # Use the instance to get a logger
    spark = SparkSession.builder.getOrCreate()
    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    args = parser.parse_args()
    # Define the output tables
    write_tables = {
        "dev": "gold_dev.dmv_stats.recordings_scenes_totals",
        "qa": "gold_qa.dmv_stats.recordings_scenes_totals",
        "prod": "gold.dmv_stats.recordings_scenes_totals",
    }

    read_tables = {
        "recordings_total_splits": (
            f"silver_{args.env}.dmv_stats.recordings_total_splits"
            if args.env in ["dev", "qa"]
            else "silver.dmv_stats.recordings_total_splits"
        ),
        "parents": "silver.tds.parents",
        "co_driver_tags": "silver.mdd.datamanagement_co_driver_tags",
        "file_entries": "silver.tds.file_entries",
    }

    recordings_and_files = RecordingsAndFilesProcessor(
        spark=spark,
        read_tables=read_tables,
        write_tables=write_tables[args.env],
        logger=log,
    )
    recordings_and_files.process_and_store_statistics()
