"""LoggerFactory module for logging."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import logging
import sys
from typing import Optional


class LoggerFactory:
    """Factory class to create and configure loggers."""

    @staticmethod
    def get_logger(
        name: str = __name__, level: int = logging.INFO, handler: Optional[logging.Handler] = None
    ) -> logging.Logger:
        """Set up and return a logger with the specified name and level.

        Args:
            name (str): Name of the logger. Defaults to __name__.
            level (int): Logging level (e.g., logging.INFO). Defaults to logging.INFO.
            handler (Optional[logging.Handler]): Optional logging handler. Defaults to None.

        Returns:
            logging.Logger: Configured logger instance.
        """
        logger = logging.getLogger(name)

        # Check if the logger already has handlers to avoid duplicate handlers
        if not logger.handlers:
            logger.setLevel(level)

            # Set up the console handler
            handler = logging.StreamHandler(sys.stdout)
            formatter = logging.Formatter("%(asctime)s - %(name)s - %(levelname)s - %(message)s")
            handler.setFormatter(formatter)

            # Add the handler to the logger
            logger.addHandler(handler)

        return logger

    @staticmethod
    def error(message: str) -> None:
        """Log an error message."""
        logger = logging.getLogger(__name__)
        logger.error(message)

    @staticmethod
    def info(message: str) -> None:
        """Log an info message."""
        logger = logging.getLogger(__name__)
        logger.info(message)

    @staticmethod
    def debug(message: str) -> None:
        """Log a debug message."""
        logger = logging.getLogger(__name__)
        logger.debug(message)
