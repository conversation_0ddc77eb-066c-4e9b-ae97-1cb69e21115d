resources:
  jobs:
    dmv_stats_artifacts_statistics_pipeline:
      permissions:
        - group_name: "sg-pace-databricks-Data Delivery-Data-Management-Verticalization-(DMV)-admin"
          level: CAN_MANAGE_RUN
        - service_principal_name: ${var.run_sp}
          level: CAN_MANAGE_RUN

      name: DMV-STATS Artifacts Statistics Pipeline
      schedule:
        quartz_cron_expression: '0 0 1 * * ?'  # Set a time when you want the job to run.
        timezone_id: UTC
        pause_status: ${var.schedule_pause_status}
      timeout_seconds: 14400 # 4 hours
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 7200 # 2 hours

      tasks:
        # Task 1: Create artifacts statistics
        - task_key: create_artifacts_statistics
          spark_python_task:
            python_file:  ../artifacts_statistics_processor.py
            parameters:
            - -e
            - ${var.env}
          job_cluster_key: dmv_stats_job_cluster
          libraries:
            - pypi:
                package: pyspark==3.5.2

          description: Create artifacts statistics

        # Task 2: Recordings and Files counting
        - task_key: recordings_and_files_counting
          spark_python_task:
            python_file:  ../recordings_and_files_counting.py
            parameters:
            - -e
            - ${var.env}
          job_cluster_key: dmv_stats_job_cluster
          description: Count recordings and files

        # Task 3: Unsuccessful validations
        - task_key: unsuccessful_validations
          depends_on:
            - task_key: recordings_and_files_counting  # Depends on Task 3
          spark_python_task:
            python_file:  ../unsuccessful_metaparser_validations.py
            parameters:
            - -e
            - ${var.env}
          job_cluster_key: dmv_stats_job_cluster
          description: Process unsuccessful validations

      job_clusters:
        - job_cluster_key: dmv_stats_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            num_workers: ${var.num_workers}
            runtime_engine: PHOTON
            spark_env_vars:
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
            init_scripts:
              - volumes:
                  destination: /Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh
