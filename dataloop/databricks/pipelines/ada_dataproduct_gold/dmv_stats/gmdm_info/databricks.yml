bundle:
  name: ada_dataproduct_dmv_artifacts_stats_gold

include:
  - resources/*.yml

targets:
  dev:
    variables:
      env: dev
    mode: production
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}

  qa:
    variables:
      env: qa
    mode: production
    default: true
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}

  prod:
    variables:
      env: prod
      schedule_pause_status: "PAUSED"
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
    resources:
      jobs:
        dmv_stats_artifacts_statistics_pipeline:
          email_notifications:
            on_failure:
              - ${var.ms_teams_alert_channel_email}
