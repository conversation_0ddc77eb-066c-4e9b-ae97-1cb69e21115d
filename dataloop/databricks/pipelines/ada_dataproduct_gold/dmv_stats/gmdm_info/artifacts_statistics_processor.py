"""Script for creating Artifact Statistics for DMV-STATS.

Maintainer: <PERSON><PERSON><PERSON><PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import logging
import typing

# Get the gmdm files content:
from argparse import ArgumentParser

from helper import DeltaTableReader, DeltaTableWriter
from logs import LoggerFactory
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F


# Create_artifacts_statistics
class ArtifactsStatisticsProcessor:
    """Class to process and store artifact statistics."""

    # Set the current catalog and database
    spark: SparkSession
    delta_write: DeltaTableWriter
    delta_read: DeltaTableReader

    def __init__(
        self,
        spark: SparkSession,
        read_tables: typing.Dict[str, str],
        write_tables: typing.Dict[str, str],
        logger: logging.Logger,
    ):
        """Initializes the ArtifactsStatisticsProcessor with specific parameters.

        Args:
            Args:
            spark (SparkSession): The active Spark session.
            read_tables (dict): Dictionary of tables for reading operations.
            write_tables (dict): Dictionary of tables for writing operations.
            logger (logging.Logger, optional): Logger instance. If not provided, a default logger will be used.
        """
        self.spark = spark
        self.read_tables = read_tables
        self.write_tables = write_tables
        self.logger = logger

        # Initialize DeltaTableReader and DeltaTableWriter with the logger
        self.delta_read: DeltaTableReader = DeltaTableReader(logger=self.logger, spark=spark)
        self.delta_write: DeltaTableWriter = DeltaTableWriter(logger=self.logger, spark=spark)

    def filter_artifacts_last_30_days(self) -> DataFrame:
        """Filter artifact records with createdDate within the last 30 days."""

        # Load the bronze table
        bronze_df = self.delta_read.read(self.read_tables["dsp_de_split_extracted_latest"])

        base_artifacts = (
            bronze_df.select(
                F.col("split_hash").alias("file_hash"),
                F.col("stream"),
                F.col("entry"),
                F.col("entry_timestamp"),
                F.col("extractor_version"),
                F.col("number_of_artifacts"),
                F.col("created_at"),
                F.col("modified_at"),
                F.col("revision"),
            )
            .filter(F.col("created_at") > F.date_sub(F.current_date(), 30))
            .select(  # Subtract 30 days
                F.col("stream"),
                F.col("number_of_artifacts"),
                F.date_trunc("day", F.col("created_at")).alias("date"),
            )
        )

        # Group by stream and date, and count the number_of_artifacts
        artifacts_per_date = (
            base_artifacts.groupBy("stream", "date")
            .agg(F.count("number_of_artifacts").alias("artifacts"))
            .withColumnRenamed("stream", "type")
        )

        return artifacts_per_date

    def process_and_store_statistics(self) -> None:
        """Main method to process and store artifacts statistics."""

        # Process artifacts and generate statistics
        artifacts_per_date = self.filter_artifacts_last_30_days()

        # Save artifacts statistics to Delta Lake
        self.delta_write.overwrite(artifacts_per_date, self.write_tables["artifacts_stats"], True)

        # Additional process for dsp_selection_df (just an example if required)
        dsp_selection_df = self.delta_read.read(self.read_tables["dsp_selection"]).select(
            "file_hash", F.date_trunc("day", F.col("created_at")).alias("created_at_day"), "target", "state"
        )

        self.delta_write.overwrite(dsp_selection_df, self.write_tables["frames_status_stats"])


if __name__ == "__main__":
    # Recordings Stats and Files Counting
    logger_factory = LoggerFactory()  # This creates an instance of LoggerFactory
    log = logger_factory.get_logger()  # Use the instance to get a logger
    spark = SparkSession.builder.getOrCreate()
    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    args = parser.parse_args()
    # Define the output tables
    write_tables = {
        "dev": {
            "artifacts_stats": "gold_dev.dmv_stats.artifacts_stats",
            "frames_status_stats": "gold_dev.dmv_stats.frames_status_stats",
        },
        "qa": {
            "artifacts_stats": "gold_qa.dmv_stats.artifacts_stats",
            "frames_status_stats": "gold_qa.dmv_stats.frames_status_stats",
        },
        "prod": {
            "artifacts_stats": "gold.dmv_stats.artifacts_stats",
            "frames_status_stats": "gold.dmv_stats.frames_status_stats",
        },
    }

    read_tables = {
        "dsp_de_split_extracted_latest": "silver.mdd.dsp_de_split_extracted_latest",
        "dsp_selection": "silver.mdd.dsp_selection",
    }
    # Artifacts Statistics
    artifacts_statistics = ArtifactsStatisticsProcessor(
        spark=spark,
        read_tables=read_tables,
        write_tables=write_tables[args.env],
        logger=log,
    )
    artifacts_statistics.process_and_store_statistics()
