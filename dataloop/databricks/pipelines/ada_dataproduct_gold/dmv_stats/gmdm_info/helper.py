"""Helper module for delta table operations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import logging

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.utils import AnalysisException


class DeltaTableWriter:
    """Class for delta table operations."""

    def __init__(self, logger: logging.Logger, spark: SparkSession):
        """Initialize the DeltaTableWriter class."""
        self.logger = logger
        self.spark = spark

    def overwrite(self, df: DataFrame, table: str, overwrite_schema: bool = False):  # type: ignore
        """Perform an overwrite operation on delta table."""
        try:
            # Set overwriteSchema option if requested
            if overwrite_schema:
                write = df.write.option("overwriteSchema", "true")
            else:
                write = df.write.option("mergeSchema", "true")

            write.format("delta").mode("overwrite").saveAsTable(table)

        except AnalysisException as e:
            raise RuntimeError(f"Analysis exception in delta table overwrite: {str(e)}")

    def append(self, df: DataFrame, table: str):  # type: ignore
        """Perform an append operation on delta table."""
        try:
            df.write.option("mergeSchema", "true").format("delta").mode("append").saveAsTable(table)
        except AnalysisException as e:
            raise RuntimeError(f"Analysis exception in delta table append: {str(e)}")


class DeltaTableReader:
    """Class for delta table operations."""

    def __init__(self, logger: logging.Logger, spark: SparkSession):
        """Initialize the DeltaTableReader class."""
        self.logger = logger
        self.spark = spark

    def read(self, table: str) -> DataFrame:
        """Read a Delta table into a Spark DataFrame.

        Args:
            table (str): The name or path of the Delta table to read.

        Returns:
            DataFrame: The Spark DataFrame representing the Delta table.

        Raises:
            SystemExit: If an AnalysisException occurs during the read operation.
        """

        try:
            return self.spark.table(table)
        except AnalysisException as e:
            raise RuntimeError(f"Error during processing and storing validations: {e}")
