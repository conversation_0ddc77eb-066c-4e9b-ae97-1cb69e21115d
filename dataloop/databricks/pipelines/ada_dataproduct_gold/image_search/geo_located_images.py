# Databricks notebook source
import dlt
from pyspark.sql.functions import abs, asc, col, explode, from_json, from_unixtime, row_number
from pyspark.sql.types import *
from pyspark.sql.window import Window

# COMMAND ----------


# COMMAND ----------


@dlt.table(
    name="geo_located_images",
    comment="Images with geo location",
    table_properties={"quality": "gold"},
)
def gold_geo_located_images():
    df_gps = dlt.read("dsp_de_gps")

    return (
        dlt.read("dsp_de_image")
        # join the gps coordinates on the split
        .join(df_gps, df_gps.split_hash == col("LIVE.dsp_de_image.split_hash"))
        # partition and rank
        .withColumn(
            "gps_match_quality", abs(col("LIVE.dsp_de_image.recorded_at") - col("LIVE.dsp_de_gps.gps_time"))
        ).withColumn(
            "_row_number",
            row_number().over(Window.partitionBy("LIVE.dsp_de_image.file_hash").orderBy(asc("gps_match_quality"))),
        )
        # only keep the best gps match
        .where(col("_row_number") == 1)
        # organize output
        .select(
            col("LIVE.dsp_de_image.project").alias("project"),
            col("LIVE.dsp_de_image.split_hash").alias("split_hash"),
            col("LIVE.dsp_de_image.stream_hash").alias("stream_hash"),
            col("LIVE.dsp_de_image.file_hash").alias("image_file_hash"),
            col("LIVE.dsp_de_image.parent_hash").alias("image_file_parent_hash"),
            col("LIVE.dsp_de_image.created_at").alias("image_file_created_at"),
            col("LIVE.dsp_de_image.modified_at").alias("image_file_modified_at"),
            col("LIVE.dsp_de_image.camera_stream").alias("camera_stream"),
            col("LIVE.dsp_de_image.frame_number").alias("frame_number"),
            col("LIVE.dsp_de_image.recorded_at").alias("recorded_at"),
            col("LIVE.dsp_de_gps.gps_time").alias("gps_time"),
            col("LIVE.dsp_de_gps.latitude").alias("gps_latitude"),
            col("LIVE.dsp_de_gps.longitude").alias("gps_longitude"),
            col("LIVE.dsp_de_gps.elevation").alias("gps_elevation"),
            col("LIVE.dsp_de_image.is_raw_image").alias("is_raw_image"),
            col("LIVE.dsp_de_image.color_space").alias("color_space"),
            col("LIVE.dsp_de_image.context").alias("context"),
            col("LIVE.dsp_de_image.bit_depth").alias("bit_depth"),
            col("LIVE.dsp_de_image.is_compressed").alias("is_compressed"),
            col("LIVE.dsp_de_image.compression_algorithm").alias("compression_algorithm"),
            col("LIVE.dsp_de_image.isp_algorithm").alias("isp_algorithm"),
            col("LIVE.dsp_de_image.anonymizer_git_commit").alias("anonymizer_git_commit"),
            col("LIVE.dsp_de_image.anonymizer_algorithm_used").alias("anonymizer_algorithm_used"),
            col("LIVE.dsp_de_image.anonymizer_configuration").alias("anonymizer_configuration"),
            col("LIVE.dsp_de_image.rectification_type").alias("rectification_type"),
            col("LIVE.dsp_de_image.rectification_warper_version").alias("rectification_warper_version"),
            col("LIVE.dsp_de_image.rectification_calstorage_uid").alias("rectification_calstorage_uid"),
            col("LIVE.dsp_de_gps.gps_bin_file_hash").alias("gps_bin_file_hash"),
            col("LIVE.dsp_de_gps.gps_csv_file_hash").alias("gps_csv_file_hash"),
            col("LIVE.dsp_de_gps.created_at").alias("gps_csv_file_created_at"),
            col("LIVE.dsp_de_gps.modified_at").alias("gps_csv_file_modified_at"),
            col("LIVE.dsp_de_image.extractor_version").alias("image_extractor_version"),
            col("LIVE.dsp_de_image.extractor_git_hash").alias("image_extractor_git_hash"),
            col("LIVE.dsp_de_image.extractor_created_at").alias("image_extractor_created_at"),
            col("LIVE.dsp_de_gps.gps_extractor_version").alias("gps_extractor_version"),
            col("LIVE.dsp_de_gps.gps_extractor_git_hash").alias("gps_extractor_git_hash"),
            col("gps_match_quality"),
        )
    )
