-- Databricks notebook source
-- MAGIC %md
-- <PERSON><PERSON><PERSON> # [MDD] Analyse Namespace Usage
-- MAGIC
-- MAGIC Notebook creates and updates tables enriched by inverse namespace version numbers (`row_number`) where the lowest integer shows the most recent version for simpler filtering.
-- MAGIC
-- MAGIC Based on this table statistics about the namespace usage are calculated, e.g. how many files have this namespace attached, how often are these namespaces updated...

-- COMMAND ----------

CREATE TABLE IF NOT EXISTS ada_dataproduct_gold.mdm_statistics.mdd_namespace
  (
    row_number                BIGINT COMMENT 'Youngest to oldest version of namespace content sorted number for a (SHA,Namespace)-tuple'
    ,namespace                STRING COMMENT 'Name of the namespace'
    ,sha                      STRING COMMENT 'TDS file the namespace content belongs to'
    ,modified_at              TIMESTAMP COMMENT 'Timestamp of this modification of the namespace and SHA'
    ,created_at               TIMESTAMP COMMENT 'Timestamp namespace content was initially published for this SHA'
    ,custom_fields            STRING COMMENT 'Content of the namespace as <PERSON>SO<PERSON> document'
    ,custom_fields_size_bytes BIGINT COMMENT 'Size of the JSON document in bytes'
  )
  COMMENT 'MDD namespace view with update sorted list'
  PARTITIONED BY (namespace)
  ;

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW mdd_namespace_view
  (
    row_number COMMENT 'Youngest to oldest version of namespace content sorted number for a (SHA,Namespace)-tuple'
    ,namespace COMMENT 'Name of the namespace'
    ,sha COMMENT 'TDS file the namespace content belongs to'
    ,modified_at COMMENT 'Timestamp of this modification of the namespace and SHA'
    ,created_at COMMENT 'Timestamp namespace content was initially published for this SHA'
    ,custom_fields COMMENT 'Content of the namespace as JSON document'
    ,custom_fields_size_bytes COMMENT 'Size of the JSON document in bytes'
  )
  COMMENT 'MDD namespace view with update sorted list'
  AS
  SELECT
    row_number() OVER (PARTITION BY sha, namespace ORDER BY modifiedDate DESC) AS row_number
    ,namespace AS namespace
    ,sha AS sha
    ,modifiedDate AS modified_at
    ,createdDate AS created_at
    ,customFields AS custom_fields
    ,length(customFields) AS custom_fields_size_bytes
  FROM raw.mdm.namespaces
;

-- COMMAND ----------

CREATE OR REPLACE TABLE ada_dataproduct_gold.mdm_statistics.mdd_duplicates AS
  SELECT
    source.sha
    ,source.namespace
    ,source.created_at
    ,source.modified_at
    ,source.custom_fields_size_bytes
    ,COUNT(*) AS duplications
  FROM ada_dataproduct_gold.mdm_statistics.mdd_namespace AS target
  LEFT JOIN mdd_namespace_view AS source
    ON
      source.sha = target.sha
      AND source.namespace = target.namespace
      AND source.created_at = target.created_at
      AND source.modified_at = target.modified_at
      AND source.custom_fields_size_bytes = target.custom_fields_size_bytes
      AND source.custom_fields = target.custom_fields
  WHERE target.row_number=1
  GROUP BY source.sha, source.namespace, source.created_at, source.modified_At, source.custom_fields_size_bytes


-- COMMAND ----------

MERGE INTO ada_dataproduct_gold.mdm_statistics.mdd_namespace AS target
USING mdd_namespace_view AS source
ON
  source.sha = target.sha
  AND source.namespace = target.namespace
  AND source.modified_at = target.modified_at
WHEN MATCHED AND source.row_number <> target.row_number THEN
  UPDATE SET target.row_number = source.row_number
WHEN NOT MATCHED BY TARGET THEN
  INSERT (
    row_number
    ,namespace
    ,sha
    ,modified_at
    ,created_at
    ,custom_fields
    ,custom_fields_size_bytes
  )
  VALUES (
    source.row_number
    ,source.namespace
    ,source.sha
    ,source.modified_at
    ,source.created_at
    ,source.custom_fields
    ,source.custom_fields_size_bytes
  )

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Update the mdd statistics table

-- COMMAND ----------

CREATE TABLE IF NOT EXISTS ada_dataproduct_gold.mdm_statistics.mdd_statistics
  (
    namespace                     STRING    COMMENT 'Name of the MDD namespace'
    ,number_assigned_entries      BIGINT    COMMENT 'number of entries having this namespace assigned to'
    ,total_document_size          BIGINT    COMMENT 'how many bytes are stored in all documents of this namespace together.'
    ,minimal_writes_to_namespace  FLOAT     COMMENT 'Minimal number of writes to this namespace per entry it is assigned on'
    ,maximal_writes_to_namespace  FLOAT     COMMENT 'Maximal number of writes to this namespace per entry it is assigned on'
    ,avg_writes_to_namespace      FLOAT     COMMENT 'Average number of writes to this namespace per entry it is assigned on'
    ,added_at                     TIMESTAMP COMMENT 'When was this statistics line added to the table'
  )
  COMMENT 'MDD namespace view with update sorted list'
  --PARTITIONED BY (namespace) // that will be a small table so partitioning should not happen
  ;

-- COMMAND ----------

CREATE OR REPLACE TABLE ada_dataproduct_gold.mdm_statistics.mdd_statistics
AS
  WITH CTE AS (
    SELECT
      namespace
      ,COUNT(*) AS number_entries_with_namespace
      ,SUM(custom_fields_size_bytes) AS total_namespace_sizte_bytes
    FROM ada_dataproduct_gold.mdm_statistics.mdd_namespace
    WHERE row_number=1
    GROUP BY namespace
  ),
  NumberOfUpdates AS (
    SELECT
      namespace
      ,sha
      ,COUNT(*) AS updates_to_entry_namespace
    FROM ada_dataproduct_gold.mdm_statistics.mdd_namespace
    GROUP BY namespace, sha
  ),
  NumberOfUpdatesStats AS (
    SELECT
      namespace
      ,MIN(updates_to_entry_namespace) AS minimal_writes_to_namespace
      ,MAX(updates_to_entry_namespace) AS maximal_writes_to_namespace
      ,AVG(updates_to_entry_namespace) AS avg_writes_to_namespace
    FROM NumberOfUpdates
    GROUP BY namespace
  )
  SELECT
    CTE.namespace
    ,CTE.number_entries_with_namespace
    ,CTE.total_namespace_sizte_bytes
    ,NumberOfUpdatesStats.minimal_writes_to_namespace
    ,NumberOfUpdatesStats.maximal_writes_to_namespace
    ,NumberOfUpdatesStats.avg_writes_to_namespace
  FROM CTE
  LEFT JOIN NumberOfUpdatesStats ON NumberOfUpdatesStats.namespace = CTE.namespace


-- COMMAND ----------

SELECT * FROM ada_dataproduct_gold.mdm_statistics.mdd_statistics

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Clean-up if analysis finished

-- COMMAND ----------

DROP TABLE IF EXISTS ada_dataproduct_gold.mdm_statistics.mdd_statistics;

-- COMMAND ----------

DROP TABLE IF EXISTS ada_dataproduct_gold.mdm_statistics.mdd_namespace;

-- COMMAND ----------

DROP TABLE IF EXISTS ada_dataproduct_gold.mdm_statistics.mdd_duplicates;

-- COMMAND ----------

