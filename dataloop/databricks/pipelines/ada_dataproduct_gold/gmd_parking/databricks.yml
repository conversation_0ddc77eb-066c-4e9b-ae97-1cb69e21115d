bundle:
  name: gmd_parking

include:
  - resources/*.yml

targets:
  # user-dev:
  #   mode: development
  #   variables:
  #     env: dev
  #     catalog: gold_dev
  #     schema: gmd_parking
  #   default: true
  #   workspace:
  #     host: https://adb-505904006080631.11.azuredatabricks.net/

  #   resources:
  #     jobs:
  #       viper_environment_gold:
  #         job_clusters:
  #           - job_cluster_key: gmd_parking_job_cluster
  #             new_cluster:
  #               policy_id: 000CDAE4E9E2CD97
  #               driver_instance_pool_id: ${var.driver_instance_pool_id}
  #               instance_pool_id: ${var.instance_pool_id}
  #               num_workers: 2

  dev:
    variables:
      env: dev
      catalog: gold
      schema: gmd_parking
    mode: production
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Workspace/Jobs/ada_dataproduct_gold/${bundle.name}
    run_as:
      service_principal_name: e7852fdc-3ef0-4611-87cd-d53a3192712f

  qa:
    variables:
      env: qa
      catalog: gold
      schema: gmd_parking
    mode: production
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net
      root_path: /Workspace/Jobs/ada_dataproduct_gold/${bundle.name}
    run_as:
      service_principal_name: 7098b21f-bec4-4ff0-b5f3-c1eb2d8108af

  prod:
    variables:
      env: prod
      catalog: gold
      schema: gmd_parking
      pause_status: "UNPAUSED"
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Workspace/Jobs/ada_dataproduct_gold/${bundle.name}
    run_as:
      service_principal_name: be1e2205-53bc-4116-b5d7-7e2c26e620d2
