"""Contains helper class for common functionality used by gold layer scripts."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import configparser
import logging
import os
from abc import abstractmethod
from dataclasses import dataclass
from typing import Optional

from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


@dataclass
class RuntimeArgs:
    """Provide default runtime args for cmd line entries."""

    run_local: bool
    env: str
    catalog: str
    schema: str
    overwrite: bool


def get_databricks_config() -> RuntimeArgs:
    """Get the cmd line arguments and read the databricks config.

    The function passed the args to the gold layer including the run_local
    parameter for debugging in custom folders
    """

    parser = argparse.ArgumentParser()

    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Run in environment, ex. dev, qa, prod",
    )
    parser.add_argument(
        "--run_local",
        action="store_true",
        help="Run in local mode. This will overwrite the environment and always run in dev.",
    )
    parser.add_argument(
        "-c",
        "--catalog",
        help="Catalogue to run against, by default catalogue is `gold`.",
    )
    parser.add_argument(
        "-s",
        "--schema",
        help="Schema to run against, by default schema is `mdd`.",
    )
    # in the databricks UI, you can only specify parameters, not flags -> we need to handle the parameter here.
    parser.add_argument(
        "-o",
        "--overwrite",
        default="False",
        help="Overwrite the existing table if it exists. Default is False",
        type=lambda x: str(x).casefold() == "true",
    )

    args, unknown = parser.parse_known_args()

    if args.run_local:
        config = configparser.ConfigParser()
        configFilePath = r"/home/<USER>/.databrickscfg"
        config.read(configFilePath)

        config_section = "DEFAULT"

        if args.env.upper() in config.sections():
            config_section = args.env.upper()
        section = config[config_section]

        host = section["host"].lstrip("https://")
        SPARK_REMOTE = f"sc://{host}:443/;token={section['token']};" f"x-databricks-cluster-id={section['cluster_id']}"

        print(SPARK_REMOTE)
        os.environ["SPARK_REMOTE"] = SPARK_REMOTE

        print("RUNNING IN DATABRICKS")

    return RuntimeArgs(
        run_local=args.run_local,
        env=args.env,
        catalog=args.catalog,
        schema=args.schema,
        overwrite=args.overwrite,
    )


def get_table_name(
    full_table_name: Optional[str] = None,
    *,
    table_name: Optional[str] = None,
    catalog: Optional[str] = None,
    schema: Optional[str] = None,
    env: Optional[str] = None,
) -> str:
    """Get the full table name in the format catalog.schema.table."""
    assert (full_table_name is not None) ^ (
        table_name is not None
    ), "Either full_table_name or table_name must be provided, but not both."
    if full_table_name is not None:
        catalog_, schema_, table_name_ = full_table_name.split(".")
    else:
        assert catalog is not None, "catalog must be provided if table_name is provided."
        assert schema is not None, "schema must be provided if table_name is provided."
        table_name_ = table_name  # type: ignore [assignment]

    if catalog is not None:
        catalog_ = catalog
    if schema is not None:
        schema_ = schema
    if env is not None:
        catalog_name = catalog_.split("_")[0]
        # no suffix for sbx and prod environment
        if env in ("dev", "qa") and "sbx" not in catalog_:
            env_suffix = f"_{env}"
        else:
            env_suffix = ""
        catalog_ = f"{catalog_name}{env_suffix}"

    return f"{catalog_}.{schema_}.{table_name_}"


def get_or_create_databricks_session() -> SparkSession:
    """Gets or creates a Databricks SparkSession.

    Optionally, you can provide a Databricks CLI profile name.
    The profile is then used to retrieve the SparkSession.

    Args:
        databricks_profile_name (Optional[str], optional): Name of a configured
            Databricks Profile.

    Returns:
        SparkSession: SparkSession created
    """
    return SparkSession.builder.getOrCreate()


class GoldExport:
    """Class for generation & update of gold tables."""

    def __init__(self, table: str, args: RuntimeArgs):
        """Initiate the class including a spark session.

        Args:
            table (str): The table to write to.
            args (RuntimeArgs): The runtime arguments for the script.
        """
        self.spark = get_or_create_databricks_session()
        self.table = table
        self.args = args

    @property
    def full_table_name(self) -> str:
        """Get the full table name in the format catalog.schema.table."""
        return f"{self.args.catalog}.{self.args.schema}.{self.table}"

    def log_row_count(self) -> None:
        """Return number of table rows."""
        num_rows = self.spark.sql(f"""SELECT COUNT(*) AS row_count FROM {self.full_table_name}""").collect()[0][
            "row_count"
        ]
        logger.info(f"Databricks export of gold layer {self.full_table_name} with {num_rows} rows completed")

    def run(self) -> None:
        """Do the processing of gold table."""
        logger.info(f"Starting gold layer export for {self.full_table_name}..")
        gold_table = self.create_gold_table()
        gold_table.write.format("delta").mode("overwrite").option("overwriteSchema", "true").saveAsTable(
            self.full_table_name
        )
        self.log_row_count()
        logger.info(f"Gold layer export for {self.full_table_name} completed")

    def optimize_table(self) -> None:
        """Optimize a table by running the DeltaTable.optimize() method."""
        logger.info(f"Optimizing table: {self.full_table_name}")
        existing_table = DeltaTable.forName(self.spark, self.full_table_name)
        existing_table.optimize().executeCompaction()

    @abstractmethod
    def create_gold_table(self) -> DataFrame:
        """Override this function with the relevant gold layer function."""
