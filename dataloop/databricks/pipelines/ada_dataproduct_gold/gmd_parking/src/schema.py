"""Schema for test data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import (
    BooleanType,
    IntegerType,
    LongType,
    StringType,
    StructField,
    StructType,
    TimestampType,
)

SILVER__AUTOQC__LABEL_FILE_FRAME_CONTEXT_SCHEMA = StructType(
    [
        StructField("file_hash", StringType(), True),
        StructField("file_content_type", StringType(), True),
        StructField("batch_id", LongType(), True),
        StructField("label_task_id", StringType(), True),
        StructField("file_modified_at", TimestampType(), True),
        StructField("feature_team", StringType(), True),
        StructField("parse_timestamp", TimestampType(), True),
        StructField("job_run_id", StringType(), True),
        StructField("frame_number", StringType(), True),
        StructField("frame_sha", StringType(), True),
        StructField("timestamp", StringType(), True),
        StructField("lapi_uri", StringType(), True),
        StructField("stream", StringType(), True),
        StructField("stream_type", StringType(), True),
        StructField("intrinsics", StringType(), True),
        StructField("extrinsics", StringType(), True),
        StructField("height_px", StringType(), True),
        StructField("width_px", StringType(), True),
        StructField("ingest_timestamp", TimestampType(), True),
        StructField("image_id", StringType(), True),
    ]
)

SILVER__MDD_DSP_DE_IMAGE_SCHEMA = StructType(
    [
        StructField("file_hash", StringType(), True),
        StructField("created_at", TimestampType(), True),
        StructField("modified_at", TimestampType(), True),
        StructField("project", StringType(), True),
        StructField("split_hash", StringType(), True),
        StructField("stream_hash", StringType(), True),
        StructField("camera_stream", StringType(), True),
        StructField("view", StringType(), True),
        StructField("view_type", StringType(), True),
        StructField("frame_number", IntegerType(), True),
        StructField("recorded_at", TimestampType(), True),
        StructField("recorded_at_unix", LongType(), True),
        StructField("is_raw_image", BooleanType(), True),
        StructField("color_space", StringType(), True),
        StructField("context", StringType(), True),
        StructField("bitdepth_ch1", IntegerType(), True),
        StructField("bitdepth_ch2", IntegerType(), True),
        StructField("bitdepth_ch3", IntegerType(), True),
        StructField("is_compressed", BooleanType(), True),
        StructField("compression__algorithm", StringType(), True),
        StructField("compression__mode", StringType(), True),
        StructField("camera_frame_count", IntegerType(), True),
        StructField("isp", StringType(), True),
        StructField("isp_version", StringType(), True),
        StructField("isp_settings__tonemapper", StringType(), True),
        StructField("anonymizer__git_commit", StringType(), True),
        StructField("anonymizer__algorithm_used", StringType(), True),
        StructField("anonymizer__configuration", StringType(), True),
        StructField("rectification__type", StringType(), True),
        StructField("rectification__warper_version", StringType(), True),
        StructField("rectification__calstorage_uid", StringType(), True),
        StructField("extractor_version", StringType(), True),
        StructField("extractor_version_int", IntegerType(), True),
        StructField("extractor_git_hash", StringType(), True),
        StructField("extractor_created_at", TimestampType(), True),
        StructField("schema_version", IntegerType(), True),
        StructField("schema_link", StringType(), True),
    ]
)


SILVER__MDD__DSP_DE_STITCHED = StructType(
    [
        StructField("file_hash", StringType(), True),
        StructField("created_at", TimestampType(), True),
        StructField("modified_at", TimestampType(), True),
        StructField("front_image_hash", StringType(), True),
        StructField("front_image_timestamp_ns", LongType(), True),
        StructField("front_image_timestamp", TimestampType(), True),
        StructField("front_calstorage_uid", StringType(), True),
        StructField("rear_image_hash", StringType(), True),
        StructField("rear_image_timestamp_ns", LongType(), True),
        StructField("rear_image_timestamp", TimestampType(), True),
        StructField("rear_calstorage_uid", StringType(), True),
        StructField("left_image_hash", StringType(), True),
        StructField("left_image_timestamp_ns", LongType(), True),
        StructField("left_image_timestamp", TimestampType(), True),
        StructField("left_calstorage_uid", StringType(), True),
        StructField("right_image_hash", StringType(), True),
        StructField("right_image_timestamp_ns", LongType(), True),
        StructField("right_image_timestamp", TimestampType(), True),
        StructField("right_calstorage_uid", StringType(), True),
        StructField("matching__tolerance", IntegerType(), True),
        StructField("anonymization__used_anonymizer", StringType(), True),
        StructField("anonymization__anonymizer_commit", StringType(), True),
        StructField("project", StringType(), True),
        StructField("split_hash", StringType(), True),
        StructField("extractor_version", StringType(), True),
        StructField("extractor_git_hash", StringType(), True),
        StructField("extractor_created_at", TimestampType(), True),
        StructField("schema_version", IntegerType(), True),
        StructField("schema_link", StringType(), True),
    ]
)

SILVER__MDD__FILE_ENTRIES = StructType(
    [
        StructField("time_partition", IntegerType(), True),
        StructField("tds_pool", StringType(), True),
        StructField("content_type", StringType(), True),
        StructField("file_extension", StringType(), True),
        StructField("created_at", TimestampType(), True),
        StructField("modified_at", TimestampType(), True),
        StructField("file_hash", StringType(), True),
        StructField("file_size_bytes", LongType(), True),
        StructField("file_name", StringType(), True),
        StructField("file_location", StringType(), True),
        StructField("file_provider", StringType(), True),
        StructField("file_state", StringType(), True),
        StructField("tds_file_url", StringType(), True),
        StructField("parents", StringType()),
        StructField("tags", StringType()),
        StructField("sources", StringType()),
    ]
)
