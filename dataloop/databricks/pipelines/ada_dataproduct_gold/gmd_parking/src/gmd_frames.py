"""Create blockage frames table containing image and label information."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging

from helper import GoldExport, RuntimeArgs, get_databricks_config
from pyspark.sql import DataFrame
from pyspark.sql.functions import col

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class ParkingFrames(GoldExport):
    """Creates a comprehensive blockage frame table."""

    def __init__(self, table: str, args: RuntimeArgs):
        """Initialize the BlockageFrames class."""
        super().__init__(table, args)

    def get_label_files(self) -> DataFrame:
        """Read the label_file_streams_parking table and rename columns to be unique."""
        return (
            self.read_label_file_streams_parking()
            .alias("label_file_streams_parking")
            .withColumnRenamed("file_hash", "label_file_hash")
        )

    def read_label_file_streams_parking(self) -> DataFrame:
        """Read the label_file_streams_parking table."""
        parking_label_info_table_name = "silver.autoqc.label_file_streams_parking"
        return self.spark.read.format("delta").table(parking_label_info_table_name)

    def get_stitched_frames(self) -> DataFrame:
        """Read the dsp_de_stitched table and rename columns to be unique."""
        return self.read_dsp_de_stitched().alias("dsp_de_stitched").withColumnRenamed("file_hash", "label_image_hash")

    def read_dsp_de_stitched(self) -> DataFrame:
        """Read the stitched dsp_de_stitched."""
        dsp_de_stitched_table_name = "silver.mdd.dsp_de_stitched"
        return self.spark.read.format("delta").table(dsp_de_stitched_table_name)

    def get_training_images(self) -> dict[str, DataFrame]:
        """Read the training images table and filter for relevant columns, restructure as dict per camera stream."""
        df = self.read_dsp_de_image().alias("dsp_de_image")
        dfs = {}
        for camera_stream in ["front", "rear", "left", "right"]:
            dfs[camera_stream] = (
                df.where(col("view") == "BEV")
                .where(col("color_space") == "YUV")
                .where(col("created_at") > "2025-03-01T00:00:00.000+00:00")
                .where(col("camera_stream") == "TV" + camera_stream)
                .select(
                    [
                        col("split_hash"),
                        # col("frame_number"),
                        col("file_hash").alias("training_image_" + camera_stream + "_hash"),
                        col("recorded_at_unix"),
                    ]
                )
            )

        return dfs

    def read_dsp_de_image(self) -> DataFrame:
        """Read the dsp_de_image table."""
        table_name = "silver.mdd.dsp_de_image"
        return self.spark.read.format("delta").table(table_name)

    def get_file_paths(self) -> DataFrame:
        """Read the file_entries table and filter for relevant columns."""
        return self.read_file_entries().alias("file_entries").select([col("file_hash"), col("tds_file_url")])

    def read_file_entries(self) -> DataFrame:
        """Read the file_entries table."""
        file_hash_table_name = "silver.mdd.file_entries"
        return self.spark.read.format("delta").table(file_hash_table_name)

    def read_situation_dataset(self) -> DataFrame:
        """Get the first timestamp of each split from the situation dataset."""
        situation_dataset_table_name = "gold.drive_time_series.situation_dataset_full"
        return self.spark.read.format("delta").table(situation_dataset_table_name)

    def get_situation_dataset(self) -> DataFrame:
        """Get the situation dataset."""
        df = self.read_situation_dataset().alias("situation_dataset")
        return df

    def create_gold_table(self) -> DataFrame:
        """Create gold layer."""
        label_files = self.get_label_files()
        dsp_de_stitched = self.get_stitched_frames()
        gold_table = label_files.join(
            dsp_de_stitched, on=label_files["frame_sha"] == dsp_de_stitched["label_image_hash"], how="inner"
        )

        # get situation dataset for frame level enrichment
        # situation_dataset = self.read_situation_dataset_start_of_split()

        # labels_with_split_and_gps = labels_with_split.join(
        #     situation_dataset, on=labels_with_split["split_hash"] == situation_dataset["split_hash"], how="inner"
        # )

        # Get path information for all files
        file_paths = self.get_file_paths()

        # Get label file paths for label file
        gold_table = (
            gold_table.join(
                file_paths,
                on=gold_table["label_file_hash"] == file_paths["file_hash"],
                how="inner",
            )
            .withColumnRenamed("tds_file_url", "label_file_path")
            .drop("file_hash")
        )

        # Get label file paths for label image
        gold_table = (
            gold_table.join(
                file_paths,
                on=gold_table["label_file_streams_parking.frame_sha"] == file_paths["file_hash"],
                how="inner",
            )
            .withColumnRenamed("tds_file_url", "label_image_path")
            .drop("file_hash")
        )

        # add training image information
        training_images_per_stream = self.get_training_images()
        for camera_stream, training_images in training_images_per_stream.items():
            gold_table = gold_table.join(
                training_images,
                on=[
                    gold_table["dsp_de_stitched.split_hash"] == training_images["split_hash"],
                    gold_table[camera_stream + "_image_timestamp_ns"] == training_images["recorded_at_unix"],
                ],
                how="inner",
            )

            # Get training image paths
            gold_table = (
                gold_table.join(
                    file_paths,
                    on=gold_table[f"training_image_{camera_stream}_hash"] == file_paths["file_hash"],
                    how="inner",
                )
                .withColumnRenamed("tds_file_url", f"training_image_{camera_stream}_path")
                .drop("file_hash")
            )

        return gold_table.select(
            [
                col("dsp_de_stitched.split_hash").alias(
                    "split_hash",
                    metadata={"comment": "Split hash"},
                ),
                col("label_file_hash").alias(
                    "label_file_hash",
                    metadata={"comment": "SHA of the label file"},
                ),
                col("label_file_path").alias(
                    "label_file_path",
                    metadata={"comment": "TDS Path to the label file"},
                ),
                col("label_file_streams_parking.file_modified_at").alias(
                    "label_file_modified_at", metadata={"comment": "SHA of the label file"}
                ),
                col("label_image_hash").alias(
                    "label_image_hash",
                    metadata={"comment": "SHA of the label image"},
                ),
                col("label_image_path").alias(
                    "label_image_path",
                    metadata={"comment": "TDS Path to the label image"},
                ),
                col("label_file_streams_parking.stream").alias(
                    "label_image_stream",
                    metadata={"comment": "Stream name of the label image"},
                ),
                col("dsp_de_stitched.front_calstorage_uid").alias(
                    "calstorage_uid",
                    metadata={"comment": "The calstorage UID"},
                ),
                col("training_image_front_hash").alias(
                    "training_image_front_hash",
                    metadata={"comment": "SHA of the front training image"},
                ),
                col("training_image_front_path").alias(
                    "training_image_front_path",
                    metadata={"comment": "Path of the front training image"},
                ),
                col("training_image_left_hash").alias(
                    "training_image_left_hash",
                    metadata={"comment": "SHA of the left training image"},
                ),
                col("training_image_left_path").alias(
                    "training_image_left_path",
                    metadata={"comment": "Path of the left training image"},
                ),
                col("training_image_right_hash").alias(
                    "training_image_right_hash",
                    metadata={"comment": "SHA of the right training image"},
                ),
                col("training_image_right_path").alias(
                    "training_image_right_path",
                    metadata={"comment": "Path of the right training image"},
                ),
                col("training_image_rear_hash").alias(
                    "training_image_rear_hash",
                    metadata={"comment": "SHA of the rear training image"},
                ),
                col("training_image_rear_path").alias(
                    "training_image_rear_path",
                    metadata={"comment": "Path of the rear training image"},
                ),
            ]
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = ParkingFrames(table="gmd_frames", args=args)
    gold_df.run()
    gold_df.spark.sql(
        f"""
            ALTER TABLE {gold_df.full_table_name} SET TAGS (
            'responsible_domain'='Viper',
            'responsible_team'='GMD',
            'refresh_interval'='P3D',
            'timesystem'='UTC'
            );
        """
    )
    gold_df.optimize_table()
