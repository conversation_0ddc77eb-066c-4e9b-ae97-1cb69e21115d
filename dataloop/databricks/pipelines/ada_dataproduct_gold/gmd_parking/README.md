# Databricks / Pipelines / ada_data_product_gold / gmd_frames

## Responsible Team
[Parking Cluster / Stitched Birds-Eye View Perception](https://pace-project.atlassian.net/wiki/x/NYTzLQ)

The purpose of this asset bundle is to aggregate information about labeled frames across different sources from silver tables and combine them to have direct access to the SHAs and paths of label images, label files and

## Debugging with pytest

```sh
# Leave any entered venvs
deactivate

cd databricks/pipelines/ada_dataproduct_gold/viper_environment

# create venv and activate
python3 -m venv .venv
source .venv/bin/activate

# install python requirements
pip install -e ".[testing]"

pytest .

```

Make sure you don't have `databricks-connect` installed, otherwise all tests will fail due to a mismatch between `pyspark` and `databricks-connect`.
