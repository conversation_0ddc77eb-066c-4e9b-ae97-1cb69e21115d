# ==============================================================================
#
# C O P Y R I G H T
# ------------------------------------------------------------------------------
#
#  Copyright (C) 2024 Robert <PERSON>
#  Copyright (C) 2024 by CARIAD and Robert <PERSON> GmbH. All rights reserved.
#  The reproduction, distribution and utilization of this file as
#  well as the communication of its contents to others without express
#  authorization is prohibited. Offenders will be held liable for the
#  payment of damages. All rights reserved in the event of the grant
#  of a patent, utility model or design.
# ==============================================================================

[project]
name = "gmd-frames-asset-bundle"
description = "GMD Frames Asset Bundle"
requires-python = ">=3.10"
version = "0.0.1"
dependencies = [
    "pre-commit~=3.7.1",
    "deltalake==0.20.0",
    "ipython",
    "jedi",
    "numpy",
    "matplotlib-inline",
    "mypy",
    "flake8",
    "isort",
    "black",
    "pydocstyle",
    "pandas",
]

[project.optional-dependencies]
run_local = ["databricks-connect~=14.3.2"]
testing = ["pytest", "pyspark", "delta-spark", "requests"]

[tool.pytest.ini_options]
pythonpath = "./src"
log_cli = "True"
log_level = "DEBUG"

[tool.coverage.run]
omit = [
    "src/helper.py",
    "src/gmd_frames.py",
]
