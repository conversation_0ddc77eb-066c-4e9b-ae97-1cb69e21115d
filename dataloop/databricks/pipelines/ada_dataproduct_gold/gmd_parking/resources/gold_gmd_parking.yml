variables:
  env:
    default: dev
    description: "environment, dev, qa, prod"
  catalog:
    default: gold
    description: "catalog name"
  spark_version:
    default: "15.4.x-scala2.12"
    description: "Spark version"
  schema:
    default: "gmd_parking"
    description: "schema of the table"
  overwrite:
    default: False
    description: "Overwrite the existing table"
  pause_status:
    description: Is pipeline "PAUSED"/"UNPAUSED"
    default: "PAUSED"
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E16ads_v5_rt14.3"
  driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E4ads_v5_rt15.4"

resources:
  jobs:
    gmd_parking_gold:
      permissions:
        - group_name: "sg-pace-github-Viper-Parking-Stitched-TV-Perception-developer"
          level: CAN_MANAGE_RUN
      name: "GMD Parking - Gold table creation"
      schedule:
        quartz_cron_expression: "0 0 19 ? * SUN *"
        timezone_id: UTC
        pause_status: ${var.pause_status}
      timeout_seconds: 28800 # 8 hours

      tasks:
        - task_key: gmd_parking
          spark_python_task:
            python_file: ../src/gmd_frames.py
            parameters:
              - --env
              - ${var.env}
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --overwrite
              - ${var.overwrite}
          job_cluster_key: gmd_parking_job_cluster
          # depends_on:
          #   - task_key: environment_classification_frames_cdf


      job_clusters:
        - job_cluster_key: gmd_parking_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            num_workers: 2
            enable_elastic_disk: true
            init_scripts:
              - volumes:
                  destination: "/Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh"
            spark_env_vars:
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
            data_security_mode: USER_ISOLATION
            runtime_engine: PHOTON
