"""Test module for gmd_frames."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from datetime import datetime
from typing import Any
from unittest.mock import patch

import pandas as pd
import pytest
from gmd_frames import ParkingFrames
from helper import RuntimeArgs
from pyspark.sql import Row, SparkSession
from schema import (
    SILVER__AUTOQC__LABEL_FILE_FRAME_CONTEXT_SCHEMA,
    SILVER__MDD__DSP_DE_STITCHED,
    SILVER__MDD__FILE_ENTRIES,
    SILVER__MDD_DSP_DE_IMAGE_SCHEMA,
)


def convert_csv_to_dict(path: str) -> list[dict[str, Any]]:
    """Convert a CSV file to a Row object."""
    # Load the CSV data into a pandas DataFrame
    data = pd.read_csv(path).to_dict(orient="records")
    for row in data:
        for key, value in row.items():

            if key in [
                "created_at",
                "modified_at",
                "file_modified_at",
                "parse_timestamp",
                "ingest_timestamp",
                "front_image_timestamp",
                "rear_image_timestamp",
                "left_image_timestamp",
                "right_image_timestamp",
                "extractor_created_at",
                "recorded_at",
                "label_file_modified_at",
            ]:
                try:
                    row[key] = datetime.fromisoformat(value)
                except ValueError:
                    row[key] = datetime.strptime(value, "%Y-%m-%dT%H:%M:%S.%fZ")

    return data


def add_table_prefix(prefix: str, data: list[dict[str, Any]]) -> list[dict[str, Any]]:
    """Add a prefix to the keys in a list of dictionaries."""
    # Iterate through each dictionary in the list
    new_data = []
    for d in data:
        # Create a new dictionary with updated keys
        new_dict = {prefix + key: value for key, value in d.items()}
        new_data.append(new_dict)
    return new_data


@pytest.fixture()
def mock_silver__mdd__dsp_de_stitched() -> list[dict[str, Any]]:
    """Mock ´silver.mdd.dsp_de_stitched data."""
    path = "tests/test_data/silver__mdd__dsp_de_stitched.csv"
    d = convert_csv_to_dict(path)
    return d


@pytest.fixture()
def mock_silver__autoqc__label_file_frame_contexts_parking() -> list[dict[str, Any]]:
    """Mock silver.autoqc.label_file_frame_contexts_parking data."""
    path = "tests/test_data/silver__autoqc__label_file_streams_parking.csv"
    d = convert_csv_to_dict(path)
    return d


@pytest.fixture()
def mock_silver__mdd__dsp_de_image() -> list[dict[str, Any]]:
    """Mock silver.mdd.dsp_de_image data."""
    path = "tests/test_data/silver__mdd__dsp_de_image.csv"
    d = convert_csv_to_dict(path)
    return d


@pytest.fixture()
def mock_silver__mdd__file_entries() -> list[dict[str, Any]]:
    """Mock silver.mdd.dsp_de_image data."""
    path = "tests/test_data/silver__mdd__file_entries.csv"
    d = convert_csv_to_dict(path)
    return d


@pytest.fixture
def expected_gold_gmd_frames_data() -> list[Row]:
    """Fixture for the expected gold gmd_frames data."""
    path = "tests/test_data/expected_gold__gmd_parking__gmd_frames.csv"

    d = convert_csv_to_dict(path)
    return [Row(**d[0])]


@pytest.fixture
def mock_gold_parking_frames_processor() -> ParkingFrames:
    """Fixture to create a ParkingFrames object."""
    return ParkingFrames(
        table="gmd_frames",
        args=RuntimeArgs(run_local=False, env="dev", catalog="gold", schema="gmd_frames", overwrite=False),
    )


def test_gmd_frame(
    mock_spark_session: SparkSession,
    mock_gold_parking_frames_processor: ParkingFrames,
    expected_gold_gmd_frames_data: list[Row],
    mock_silver__mdd__dsp_de_stitched: Row,
    mock_silver__autoqc__label_file_frame_contexts_parking: Row,
    mock_silver__mdd__dsp_de_image: Row,
    mock_silver__mdd__file_entries: Row,
) -> None:
    """Test gmd frame."""

    with (
        patch("gmd_frames.ParkingFrames.read_label_file_streams_parking") as _read_label_files,
        patch("gmd_frames.ParkingFrames.read_dsp_de_stitched") as _read_dsp_de_stitched,
        patch("gmd_frames.ParkingFrames.read_dsp_de_image") as _read_training_images,
        patch("gmd_frames.ParkingFrames.read_file_entries") as _read_file_paths,
    ):

        _read_label_files.return_value = mock_spark_session.createDataFrame(
            mock_silver__autoqc__label_file_frame_contexts_parking,
            schema=SILVER__AUTOQC__LABEL_FILE_FRAME_CONTEXT_SCHEMA,
        )

        _read_dsp_de_stitched.return_value = mock_spark_session.createDataFrame(
            mock_silver__mdd__dsp_de_stitched,
            schema=SILVER__MDD__DSP_DE_STITCHED,
        )

        _read_training_images.return_value = mock_spark_session.createDataFrame(
            mock_silver__mdd__dsp_de_image,
            schema=SILVER__MDD_DSP_DE_IMAGE_SCHEMA,
        )

        _read_file_paths.return_value = mock_spark_session.createDataFrame(
            mock_silver__mdd__file_entries,
            schema=SILVER__MDD__FILE_ENTRIES,
        )

        gold_gmd_frames = mock_gold_parking_frames_processor.create_gold_table()

    # check if table content is identical to expected data
    for i, row in enumerate(gold_gmd_frames.collect()):
        assert row == expected_gold_gmd_frames_data[i]
