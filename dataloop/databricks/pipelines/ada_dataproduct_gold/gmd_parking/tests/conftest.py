"""Configuration for Pytest fixtures."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import tempfile
from pathlib import Path
from typing import Generator

import pytest
from delta import configure_spark_with_delta_pip
from pyspark.sql import SparkSession


@pytest.fixture(scope="session")
def mock_spark_session() -> Generator[SparkSession, None, None]:
    """Fixture to create a SparkSession."""

    logger = logging.getLogger("py4j")
    logger.setLevel(logging.WARN)

    with tempfile.TemporaryDirectory() as tmp_path:
        builder = (
            SparkSession.builder.master("local[2]")
            .appName("pytest-pyspark-local-testing")
            .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
            .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
            .config("spark.sql.legacy.createHiveTableByDefault", False)
            .config("spark.databricks.delta.schema.typeCheck.enabled", False)
            .config("spark.sql.warehouse.dir", Path(tmp_path) / "spark-warehouse")
        )
        spark = configure_spark_with_delta_pip(builder).getOrCreate()
        yield spark
        spark.stop()
