resources:
  jobs:
    azure_costs_gold:
      # Give permission to DMV admin users to manage run
      permissions:
      - group_name: "sg-pace-databricks-Data Delivery-Data-Management-Verticalization-(DMV)-admin"
        level: CAN_MANAGE_RUN

      name: Create gold tables for Azure cost data

      schedule:
        # Every week on Monday at 5 AM
        quartz_cron_expression: '0 0 5 ? * Mon'
        timezone_id: UTC
        pause_status: ${var.schedule_pause_status}
      timeout_seconds: 7200 # 2 hours
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600 # 1 hour

      tasks:
        - task_key: get_hourly_costs
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/src/cost_per_hour.py
            parameters:
              - -e
              - ${var.env}
          job_cluster_key: azure_gold_hourly_cost_data_job_cluster
          libraries:
            - pypi:
                package: pyspark==3.5.2

        - task_key: get_cost_allocation
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/src/cost_allocation.py
            parameters:
              - -e
              - ${var.env}
          job_cluster_key: azure_gold_hourly_cost_data_job_cluster
          libraries:
            - pypi:
                package: pyspark==3.5.2

      job_clusters:
        - job_cluster_key: azure_gold_hourly_cost_data_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            num_workers: ${var.num_workers}
            runtime_engine: PHOTON
            spark_env_vars:
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
            init_scripts:
              - volumes:
                  destination: /Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh
