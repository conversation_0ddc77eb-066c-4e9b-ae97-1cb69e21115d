"""Contains class AzureCostPerHour for calculating cost of each recorded hour.

Maintainer: <PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from argparse import ArgumentParser
from datetime import datetime

from helper import GoldTables
from pyspark.sql.functions import col
from pyspark.sql.functions import sum as spark_sum
from pyspark.sql.functions import to_date
from pyspark.sql.window import Window


class AzureCostPerHour(GoldTables):
    """Class for calculating cost of recorded hour.

    This class provides functions to calculate aggregated cost
    of each recorded hour.
    """

    COLUMN_COMMENTS = {
        "total_recordings": "Total recording on the time period",
        "total_scenes": "Total scenes on the time period",
        "total_hours": "Total recorded hours for the time period as #scenes*30s, in hours",
        "size_bytes": "Total size of the files generated in the time period in Bytes",
        "size_in_megas": "Total size of the files generated in the time period in MegaBytes",
        "size_in_gigas": "Total size of the files generated in the time period in GigaBytes",
        "size_in_teras": "Total size of the files generated in the time period in TeraBytes",
        "size_in_petas": "Total size of the files generated in the time period in PetaBytes",
        "cost": "Total cost generated on the time period in Euros",
        "cost_per_hour": "Cost in euros per hour for the time period",
        "cost_per_pb": "Cost in euros per hour per PB",
        "tb_per_hour": "TeraBytes generated per hour for the time period",
        "hours_cum": "Cummulated hours for the time period",
        "cost_cum": "Cummulated Cost for the time period",
        "pb_cum": "Size cumulated for the time period",
        "cost_per_hour_cum": "Cost cumulated in euros per hour",
        "cost_per_pb_cum": "Cost cumulated in euros per PB",
        "tb_per_hour_cum": "Size in TB per hour cumulated",
    }

    def get_costs_per_hour(self) -> None:
        """Get all cost per hour, according to the granularity selected."""
        recording_dataframe = self.get_stats_recordings()
        recording_size_dataframe = self.get_sizes()
        cost_dataframe = self.get_costs()

        # Define the output dataframe
        self.merged_df = self.spark.createDataFrame([], schema=self.cost_per_hour_gold_table_schema())

        # Perform the joins
        self.merged_df = recording_dataframe.join(recording_size_dataframe, on="time_period", how="inner")
        self.merged_df = self.merged_df.join(cost_dataframe, on="time_period", how="inner")

        self.merged_df = self.merged_df.withColumn("cost_per_hour", col("cost") / col("total_hours"))
        self.merged_df = self.merged_df.withColumn("cost_per_pb", col("cost") / col("size_in_petas"))
        self.merged_df = self.merged_df.withColumn("tb_per_hour", col("size_in_petas") * 1024 / col("total_hours"))

        # Calculate cumulative sums
        window_spec = Window.orderBy("time_period")
        self.merged_df = self.merged_df.withColumn("hours_cum", spark_sum("total_hours").over(window_spec))
        self.merged_df = self.merged_df.withColumn("cost_cum", spark_sum("cost").over(window_spec))
        self.merged_df = self.merged_df.withColumn("pb_cum", spark_sum("size_in_petas").over(window_spec))

        self.merged_df = self.merged_df.withColumn("cost_per_hour_cum", col("cost_cum") / col("hours_cum"))
        self.merged_df = self.merged_df.withColumn("cost_per_pb_cum", col("cost_cum") / col("pb_cum"))
        self.merged_df = self.merged_df.withColumn("tb_per_hour_cum", col("pb_cum") * 1024 / col("hours_cum"))

        # Format date to match table schema
        self.merged_df = self.merged_df.withColumn("time_period", to_date(self.merged_df["time_period"]))

    def save_costs_per_hour(self) -> None:
        """Save the cost per hour to the golden table."""
        # Remove current golden table
        self.spark.sql(f"DROP TABLE IF EXISTS {self.GOLD_TABLE}")
        # Write to golden table
        self.merged_df.write.mode("overwrite").option("mergeSchema", "true").saveAsTable(self.GOLD_TABLE)
        for col_name, comment in self.COLUMN_COMMENTS.items():
            self.spark.sql(f"ALTER TABLE {self.GOLD_TABLE} ALTER COLUMN {col_name} COMMENT '{comment}'")


if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )

    args = parser.parse_args()

    source_bronze_cost_data_tables = {
        "dev": "bronze_dev.costs.azure_costs_historical",
        "qa": "bronze_qa.costs.azure_costs_historical",
        "prod": "bronze.costs.azure_costs_historical",
    }

    target_gold_cost_data_tables = {
        "dev": "gold_dev.costs.cost_recordings_per_hour",
        "qa": "gold_qa.costs.cost_recordings_per_hour",
        "prod": "gold.costs.cost_recordings_per_hour",
    }

    SOURCE_TABLE = source_bronze_cost_data_tables[args.env]
    TARGET_TABLE = target_gold_cost_data_tables[args.env]
    SCENES_TABLE = "gold.dmv_stats.recordings_scenes_totals"

    # Historical cost
    from_date = datetime(year=2023, month=1, day=1).strftime("%Y-%m-%d")
    end_date = datetime(year=datetime.now().year, month=datetime.now().month, day=datetime.now().day).strftime(
        "%Y-%m-%d"
    )

    cost_per_hour = AzureCostPerHour(TARGET_TABLE, SOURCE_TABLE, SCENES_TABLE, "month", from_date, end_date)

    cost_per_hour.get_costs_per_hour()
    cost_per_hour.save_costs_per_hour()
