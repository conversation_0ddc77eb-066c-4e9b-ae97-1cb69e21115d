"""Contains helper functions for calculating cost of each recorded hour.

Maintainer: <PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import math
from typing import Dict, List

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, when
from pyspark.sql.types import DateType, DoubleType, IntegerType, StringType, StructField, StructType


class GoldTables:
    """Helper functions for cost gold tables."""

    def __init__(
        self,
        gold_table: str,
        bronze_table: str,
        scenes_table: str,
        granularity: str,
        from_date: str,
        end_date: str,
    ) -> None:
        """Construct AzureCostPerHour.

        Args:
            gold_table (str): Target gold table
            bronze_table (str): Source bronze table where the cost raw data is
            scenes_table (str): Source table for the MDM recordings and scenes
            granularity (str): Selected granularity for the time reference, can be "month" or "day"
            from_date (str): Start date for the cost calculation
            end_date (str): End date for the cost calculation
        """
        self.GOLD_TABLE = gold_table
        self.BRONZE_TABLE = bronze_table
        self.SCENES_TABLE = scenes_table
        self.GRANULARITY = "y-MM-dd" if granularity == "day" else "y-MM"
        self.FROM_DATE = from_date
        self.END_DATE = end_date
        self.spark = SparkSession.builder.getOrCreate()

    @staticmethod
    def cost_per_hour_gold_table_schema() -> StructType:
        """Define the spark schema for the gold table."""
        gold_table_costs_schema = StructType(
            [
                StructField("time_period", DateType(), True, {"comment": "Time period for the cost analysis"}),
                StructField("total_recordings", IntegerType(), True, {"comment": "Total recording on the time period"}),
                StructField("total_scenes", IntegerType(), True, {"comment": "Total scenes on the time period"}),
                StructField(
                    "total_hours",
                    DoubleType(),
                    True,
                    {"comment": "Total recorded hours for the time period as #scenes*30s, in hours"},
                ),
                StructField(
                    "size_bytes",
                    DoubleType(),
                    True,
                    {"comment": "Total size of the files generated in the time period in Bytes"},
                ),
                StructField(
                    "size_in_megas",
                    DoubleType(),
                    True,
                    {"comment": "Total size of the files generated in the time period in MegaBytes"},
                ),
                StructField(
                    "size_in_gigas",
                    DoubleType(),
                    True,
                    {"comment": "Total size of the files generated in the time period in GigaBytes"},
                ),
                StructField(
                    "size_in_teras",
                    DoubleType(),
                    True,
                    {"comment": "Total size of the files generated in the time period in TeraBytes"},
                ),
                StructField(
                    "size_in_petas",
                    DoubleType(),
                    True,
                    {"comment": "Total size of the files generated in the time period in PetaBytes"},
                ),
                StructField(
                    "cost", DoubleType(), True, {"comment": "Total cost generated on the time period in Euros"}
                ),
                StructField(
                    "cost_per_hour", DoubleType(), True, {"comment": "Cost in euros per hour for the time period"}
                ),
                StructField(
                    "cost_per_pb", DoubleType(), True, {"comment": "Cost in euros per hour for the time period"}
                ),
                StructField(
                    "tb_per_hour", DoubleType(), True, {"comment": "TeraBytes generated per hour for the time period"}
                ),
                StructField("hours_cum", DoubleType(), True, {"comment": "Cummulated hours for the time period"}),
                StructField("cost_cum", DoubleType(), True, {"comment": "Cummulated Cost for the time period"}),
                StructField("pb_cum", DoubleType(), True, {"comment": "Size cumulated for the time period"}),
                StructField("cost_per_hour_cum", DoubleType(), True, {"comment": "Cost cumulated in euros per hour"}),
                StructField("cost_per_pb_cum", DoubleType(), True, {"comment": "Cost cumulated in euros per PB"}),
                StructField("tb_per_hour_cum", DoubleType(), True, {"comment": "Size in TB per hour cumulated"}),
            ]
        )

        return gold_table_costs_schema

    def get_stats_recordings(self) -> DataFrame:
        """Get all recordings from the scenes table, according to the granularity selected."""
        # Recordings to time, every record correspond to 30s
        # (TotalRecordingHours = TotalScenes*30/60/60 = TotalScenes*1/120)
        recordings_query = f"""
            select
            to_date(date_format (created_at, '{self.GRANULARITY}')) as time_period,
            count(file_hash) as total_recordings,
            sum(scene_count_total) as total_scenes,
            sum(scene_count_total) / 120 as total_hours
            --scene_count_total as total_scenes
            from
                {self.SCENES_TABLE}
            where
                DATE_FORMAT(created_at,'yyyy-MM-dd hh:mm:ss') BETWEEN '{self.FROM_DATE}' AND '{self.END_DATE}'
            group by time_period
        """

        recordings = self.spark.sql(recordings_query)

        return recordings

    def get_sizes(self) -> DataFrame:
        """Get the sizes TDS table, according to the granularity selected."""
        # Define denominator for sizes
        mega_denominator = math.pow(1024, 2)
        giga_denominator = math.pow(1024, 3)
        tera_denominator = math.pow(1024, 4)
        peta_denominator = math.pow(1024, 5)

        recording_size_query = f"""
                select
                to_date ( date_format (created_at, '{self.GRANULARITY}')) as time_period,
                sum(file_size_bytes) as size_bytes,
                sum(file_size_bytes)/{mega_denominator} as size_in_megas,
                sum(file_size_bytes)/{giga_denominator} as size_in_gigas,
                sum(file_size_bytes)/{tera_denominator} as size_in_teras,
                sum(file_size_bytes)/{peta_denominator} as size_in_petas
                from
                    silver.tds.file_entries fe
                where
                    DATE_FORMAT(created_at,'yyyy-MM-dd hh:mm:ss') BETWEEN '{self.FROM_DATE}' AND '{self.END_DATE}'
                AND
                    fe.file_state = 'ACTIVE'
                group by time_period
        """

        recording_size_dataframe = self.spark.sql(recording_size_query)

        return recording_size_dataframe

    def get_costs(self) -> DataFrame:
        """Get all costs from the bronze table, according to the granularity selected."""
        cost_query = f"""
            select
            to_date ( date_format (day, '{self.GRANULARITY}') ) as time_period,
            sum(cost) as cost
            from
                {self.BRONZE_TABLE}
            where
                day BETWEEN '{self.FROM_DATE}' AND '{self.END_DATE}'
            group by time_period
        """

        costs = self.spark.sql(cost_query)

        return costs

    @staticmethod
    def cost_allocation_gold_table_schema() -> StructType:
        """Define the spark schema for the gold table."""
        gold_table_allocation_schema = StructType(
            [
                StructField("time_period", DateType(), True, {"comment": "Time period for the cost analysis"}),
                StructField("rg", IntegerType(), True, {"comment": "Resource group number"}),
                StructField("id", StringType(), True, {"comment": "Subscription id"}),
                StructField("env", StringType(), True, {"comment": "Environment for subscription"}),
                StructField("subscription_name", StringType(), True, {"comment": "Subscription name"}),
                StructField("team", StringType(), True, {"comment": "Team name in ADA context"}),
                StructField("resource_group", StringType(), True, {"comment": "Resource group name"}),
                StructField("purpose", StringType(), True, {"comment": "Purpose of the team"}),
                StructField(
                    "cost", DoubleType(), True, {"comment": "Total cost generated on the time period in Euros"}
                ),
            ]
        )

        return gold_table_allocation_schema

    def get_costs_per_subscription(self) -> DataFrame:
        """Get all costs per subscription from the bronze table, according to the granularity selected."""
        cost_query = f"""
            select
            to_date ( date_format (day, '{self.GRANULARITY}') ) as time_period,
            resource_group_name as resource_group,
            max(subscription_name) as subscription_name,
            sum(cost) as cost
            from
                {self.BRONZE_TABLE}
            where
                day BETWEEN '{self.FROM_DATE}' AND '{self.END_DATE}'
            group by time_period, resource_group
        """

        print("--> Getting the costs ")
        print(cost_query)

        costs = self.spark.sql(cost_query)

        return costs

    @staticmethod
    def get_costs_by_purpose(cost_dataframe: DataFrame) -> DataFrame:
        """Get all costs by purpose from the cost dataframe.

        Args:
        cost_dataframe (DataFrame): DataFrame with the costs, subscriptions and teams
        """

        # Define the purpose of the cost
        # Add column "purpose" to costs dataframe based on conditions
        purposes_dataframe = cost_dataframe.withColumn(
            "purpose",
            when(col("resource_group").contains("rg-mdtdspool"), "file storage")
            .when(col("resource_group").contains("dmaas"), "dmaas")
            .when(col("team").contains("DSP"), "frame proposal")
            .when(col("team").contains("CoreAI"), "training")
            .when(col("team").contains("buildsystem"), "software builds")
            .when(col("team").contains("Linux support"), "dmaas")
            .when(col("resource_group").contains("databricks"), "lakehouse / olap")
            .when(col("resource_group").contains("datalake"), "lakehouse / olap")
            .when(col("resource_group").contains("cero"), "fleet")
            .when(col("resource_group").contains("ingest"), "ingest")
            .when(col("resource_group").contains("landingzone"), "ingest")
            .when(col("resource_group").contains("upload"), "ingest")
            .when(col("resource_group").contains("default"), "infra")
            .when(col("resource_group").contains("gateway"), "infra")
            .when(col("resource_group").contains("mddeep"), "metadata storage")
            .when(col("resource_group").contains("mddauth"), "infra")
            .when(col("resource_group").contains("mdd"), "metadata storage")
            .when(col("resource_group").contains("fts"), "search / oltp")
            .when(col("resource_group").contains("ias"), "search / oltp")
            .when(col("resource_group").contains("index"), "search / oltp")
            .when(col("resource_group").contains("tds"), "file storage")
            .when(col("resource_group").contains("mdm"), "infra")
            .when(col("resource_group").contains("datasets"), "search / oltp")
            .when(col("resource_group").contains("search"), "search / oltp")
            .when(col("resource_group").contains("infra"), "infra")
            .when(col("resource_group").contains("viz"), "search / oltp")
            .when(col("resource_group").contains("lapi"), "labeling_tools")
            .when(col("resource_group").contains("atlas"), "labeling_tools")
            .when(col("resource_group").contains("dll"), "compute")
            .when(col("resource_group").contains("cake"), "infra")
            .when(col("resource_group").contains("aks"), "infra")
            .when(col("resource_group").contains("rdd"), "search / oltp")
            .otherwise("other"),
        )

        return purposes_dataframe

    @staticmethod
    def allocations_lut() -> List[Dict[str, str]]:
        """Return the allocations look up table."""

        # This dictionary is used to map the name of the data delivery cost to the id of the subscription
        data_delivery_allocations = [
            {
                "id": "ecd02880-f21c-43ad-aa64-d525061adf23",
                "subscription_name": "sub-datadelivery-connectedendurancerun-001-prod",
                "env": "prod",
                "team": "EdgeCloud",
            },
            {
                "id": "d85cfa04-9f64-41db-9b67-d719335cb18c",
                "subscription_name": "sub-datadelivery-connectedendurancerun-001-qa",
                "env": "qa",
                "team": "EdgeCloud",
            },
            {
                "id": "ed135a59-ee7c-4817-99e7-11543cccad47",
                "subscription_name": "sub-datadelivery-connectedendurancerun-001-dev",
                "env": "dev",
                "team": "EdgeCloud",
            },
            {
                "id": "8d9c2ecf-b889-4adc-8388-fa56c87b005b",
                "subscription_name": "sub-datadelivery-ingest-001-prod",
                "env": "prod",
                "team": "Ingest",
            },
            {
                "id": "69c7d321-d870-41ed-80e9-f29fd80525d3",
                "subscription_name": "sub-datadelivery-ingest-001-qa",
                "env": "qa",
                "team": "Ingest",
            },
            {
                "id": "712566ed-4ecb-44b1-9ced-e24303bae0a0",
                "subscription_name": "sub-datadelivery-ingest-001-dev",
                "env": "dev",
                "team": "Ingest",
            },
            {
                "id": "b0ab11fd-7783-4fa0-a94e-7c6f4a4c8d78",
                "subscription_name": "sub-datadelivery-datamanagementverticalization(dmv)-001-prd",
                "env": "prod",
                "team": "DMV",
            },
            {
                "id": "34675052-5a48-46ca-9f6a-35a089d325c2",
                "subscription_name": "sub-datadelivery-datamanagementverticalization(dmv)-001-qa",
                "env": "qa",
                "team": "DMV",
            },
            {
                "id": "5d2c2989-d404-431b-afb3-439f86ffea28",
                "subscription_name": "sub-datadelivery-datamanagementverticalization(dmv)-001-dev",
                "env": "dev",
                "team": "DMV",
            },
            {
                "id": "784dbabe-dfd5-4b06-bc18-11fabfaf846e",
                "subscription_name": "sub-datadelivery-measurementdatamanagement(mdm)-001-prd",
                "env": "prod",
                "team": "MDM/Indexing/S&F",
            },
            {
                "id": "77b1a340-42b9-4cb1-882a-b3bd219a7138",
                "subscription_name": "sub-datadelivery-measurementdatamanagement(mdm)-001-qa",
                "env": "qa",
                "team": "MDM/Indexing/S&F",
            },
            {
                "id": "3f933162-c0b9-406f-837d-70e0b0bdf1a0",
                "subscription_name": "sub-datadelivery-measurementdatamanagement(mdm)-001-dev",
                "env": "dev",
                "team": "MDM/Indexing/S&F",
            },
            {
                "id": "fe3595ad-54de-4ac2-8733-508394f33000",
                "subscription_name": "sub-datadelivery-searchandfind-002-prd",
                "env": "prod",
                "team": "S&F",
            },
            {
                "id": "8a7ebff1-9ad8-484d-beac-7a747d05e1f2",
                "subscription_name": "sub-datadelivery-searchandfind-002-qa",
                "env": "qa",
                "team": "S&F",
            },
            {
                "id": "26e2ad44-3cd7-4b29-aadf-00f7732d38b5",
                "subscription_name": "sub-datadelivery-searchandfind-002-dev",
                "env": "dev",
                "team": "S&F",
            },
            {
                "id": "35d39602-d9c5-4d10-9d57-9beb54bc8014",
                "subscription_name": "sub-data-delivery-dll-001-prd",
                "env": "prod",
                "team": "DLL/LAPI/Atlas/Visu",
            },
            {
                "id": "955c4563-c47d-4603-bb26-5a9130451891",
                "subscription_name": "sub-data-delivery-dll-001-qa",
                "env": "qa",
                "team": "DLL/LAPI/Atlas/Visu",
            },
            {
                "id": "1ed313f7-ed4f-4eae-9a3e-f6641cdc8697",
                "subscription_name": "sub-data-delivery-dll-001-dev",
                "env": "dev",
                "team": "DLL/LAPI/Atlas/Visu",
            },
            {
                "id": "5fb4f2c6-ee74-45b6-8a3e-6bbccc1c197b",
                "subscription_name": "sub-datadelivery-unicorn-001-prod",
                "env": "prod",
                "team": "UNICORN",
            },
            {
                "id": "f2d0c857-dca3-406e-a894-0433f840b0f2",
                "subscription_name": "sub-datadelivery-unicorn-001-qa",
                "env": "qa",
                "team": "UNICORN",
            },
            {
                "id": "72796c3c-853c-44b9-90de-69f4b96557f7",
                "subscription_name": "sub-datadelivery-unicorn-001-dev",
                "env": "dev",
                "team": "UNICORN",
            },
            {
                "id": "f326ee9b-fc83-4e13-af2e-aeca0a067d98",
                "subscription_name": "sub-datadelivery-managedruntime-prod",
                "env": "prod",
                "team": "CAKE",
            },
            {
                "id": "4baf081c-1b7c-4dc1-abff-ab3604b4ed9e",
                "subscription_name": "sub-datadelivery-managedruntime-qa",
                "env": "qa",
                "team": "CAKE",
            },
            {
                "id": "6e7b14b6-0191-4158-b47f-1fd3e1c1ceb5",
                "subscription_name": "sub-datadelivery-managedruntime-dev",
                "env": "dev",
                "team": "CAKE",
            },
            {
                "id": "a6778de4-d7bf-4c5d-a201-e28b33631c4b",
                "subscription_name": "sub-datadelivery-cake-001-prod",
                "env": "prod",
                "team": "CAKE",
            },
            {
                "id": "766e58a6-7d50-4fae-b251-f88c2da1a7ee",
                "subscription_name": "sub-datadelivery-cake-001-qa",
                "env": "qa",
                "team": "CAKE",
            },
            {
                "id": "be03144f-4b7d-4cb6-8a96-43a5d2eb7aa2",
                "subscription_name": "sub-datadelivery-cake-001-dev",
                "env": "dev",
                "team": "CAKE",
            },
            {
                "id": "a556137e-f7b1-48fb-baea-5def11e8420c",
                "subscription_name": "sub-datadelivery-rdd-001-prod",
                "env": "prod",
                "team": "RDD",
            },
            {
                "id": "c804a177-2598-4b48-a109-2c2b0b6ed8ff",
                "subscription_name": "sub-datadelivery-rdd-001-qa",
                "env": "qa",
                "team": "RDD",
            },
            {
                "id": "c0e8f292-f716-4f79-afe4-7b3e5c179bff",
                "subscription_name": "sub-datadelivery-rdd-001-dev",
                "env": "dev",
                "team": "RDD",
            },
            {
                "id": "ac76df14-c71e-433e-aa63-3c1c3371b54b",
                "subscription_name": "sub-datadelivery-datalakehouse-001-prd",
                "env": "prod",
                "team": "DDD",
            },
            {
                "id": "f8289b2e-6404-4909-9633-ab2edaf17187",
                "subscription_name": "sub-datadelivery-datalakehouse-001-qa",
                "env": "qa",
                "team": "DDD",
            },
            {
                "id": "a02fd6f2-785f-4560-8bbc-e2273324d1ad",
                "subscription_name": "sub-datadelivery-datalakehouse-001-dev",
                "env": "dev",
                "team": "DDD",
            },
            {
                "id": "c4f1c7f3-9206-409f-a333-5b89a516e5dd",
                "subscription_name": "sub-viper-coreai&multitasklearning-001-prd",
                "env": "prod",
                "team": "CoreAI",
            },
            {
                "id": "09d7d10f-ea16-4541-aef9-351df1447e9a",
                "subscription_name": "sub-github-001-prd",
                "env": "prod",
                "team": "github",
            },
            {
                "id": "b6938cfc-5530-4dd4-a4bf-c880a17d0c02",
                "subscription_name": "sub-github-007-prd",
                "env": "prod",
                "team": "github",
            },
            {
                "id": "6ac5084d-6982-49bf-b2e7-4580d07d99aa",
                "subscription_name": "Management-PACE-PROD-001",
                "env": "prod",
                "team": "Other",
            },
            {
                "id": "a3a9f2d5-07bc-49ba-bef1-839c9d21529c",
                "subscription_name": "sub-socotec-buildsystem-001-prod",
                "env": "prod",
                "team": "buildsystem",
            },
            {
                "id": "451299ec-51dd-4729-8ed2-f5bcf55c8072",
                "subscription_name": "SoCoTeC - Linux Support (PRD)",
                "env": "prod",
                "team": "Linux support",
            },
            {
                "id": "1f74737a-9546-4869-bcaa-59ae5333e026",
                "subscription_name": "sub-viper-clusterdataselectionandpreparation-001-prod",
                "env": "prod",
                "team": "DSP",
            },
        ]
        return data_delivery_allocations
