"""Contains class AzureCostAllocation for calculating cost for azure subscription.

Maintainer: <PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import json
from argparse import ArgumentParser
from datetime import datetime

import pandas as pd
from helper import GoldTables
from pyspark.sql import DataFrame


class AzureCostAllocation(GoldTables):
    """Class for calculating cost for azure subscription."""

    COLUMNS_COMMENTS = {
        "time_period": "Time period for the cost analysis",
        "rg": "Resource group number",
        "id": "Subscription id",
        "env": "Environment for subscription",
        "subscription_name": "Subscription name",
        "team": "Team name in ADA context",
        "resource_group": "Resource group name",
        "purpose": "Purpose of the team",
        "cost": "Total cost generated on the time period in Euros",
    }

    def get_cost_allocated_by_subscription(self) -> DataFrame:
        """Get all cost allocated by subscription."""
        prod_costs = [cost_entity for cost_entity in self.allocations_lut()]

        df = pd.DataFrame()

        # Create the dataframe from the list of dictionaries
        for e in prod_costs:
            tmp = pd.DataFrame.from_records(
                e, columns=["id", "subscription_name", "env", "team", "resource_groups"]
            ).reset_index()

            column = "resource_groups"
            normalized_rgs = pd.json_normalize(tmp[column].apply(lambda x: {} if pd.isna(x) else json.loads(x)))
            tmp = pd.concat([tmp.drop(columns=[column]), normalized_rgs], axis=1)
            df = pd.concat([df, tmp])

        df = df.reset_index()
        df = df.drop(columns=["level_0"]).rename(columns={"index": "rg"})

        # Convert Pandas DataFrame to PySpark DataFrame
        spark_df = self.spark.createDataFrame(df)

        # Perform the join operation
        costs = spark_df.join(self.get_costs_per_subscription(), on="subscription_name", how="inner")

        return costs

    def calculate_cost_by_subscription_purpose(self, cost_df: DataFrame) -> DataFrame:
        """Calculate cost by subscription purpose.

        Args:
        cost_df (DataFrame): DataFrame with the costs, subscriptions and teams.
        """
        # Get the purposes by cost
        purposes = self.get_costs_by_purpose(cost_df)

        return purposes

    def save_costs_by_subscription_purpose(self, cost_df: DataFrame) -> None:
        """Save the cost by subscription purpose to the golden table.

        Args:
        cost_df (DataFrame): DataFrame with the costs, subscriptions, teams and purposes.
        """
        # Remove current golden table
        self.spark.sql(f"DROP TABLE IF EXISTS {self.GOLD_TABLE}")
        # Write to golden table
        cost_df.write.mode("overwrite").option("mergeSchema", "true").saveAsTable(self.GOLD_TABLE)
        for col_name, comment in self.COLUMNS_COMMENTS.items():
            self.spark.sql(f"ALTER TABLE {self.GOLD_TABLE} ALTER COLUMN {col_name} COMMENT '{comment}';")


if __name__ == "__main__":
    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )

    args = parser.parse_args()

    source_bronze_cost_data_tables = {
        "dev": "bronze_dev.costs.azure_costs_historical",
        "qa": "bronze_qa.costs.azure_costs_historical",
        "prod": "bronze.costs.azure_costs_historical",
    }

    target_gold_cost_data_tables = {
        "dev": "gold_dev.costs.cost_allocation",
        "qa": "gold_qa.costs.cost_allocation",
        "prod": "gold.costs.cost_allocation",
    }

    SOURCE_TABLE = source_bronze_cost_data_tables[args.env]
    TARGET_TABLE = target_gold_cost_data_tables[args.env]

    # Historical cost
    from_date = datetime(year=2023, month=1, day=1).strftime("%Y-%m-%d")
    end_date = datetime(year=datetime.now().year, month=datetime.now().month, day=datetime.now().day).strftime(
        "%Y-%m-%d"
    )

    cost_allocation = AzureCostAllocation(TARGET_TABLE, SOURCE_TABLE, "", "month", from_date, end_date)

    cost_dataframe = cost_allocation.get_cost_allocated_by_subscription()
    cost_purpose_dataframe = cost_allocation.calculate_cost_by_subscription_purpose(cost_dataframe)
    cost_allocation.save_costs_by_subscription_purpose(cost_purpose_dataframe)
