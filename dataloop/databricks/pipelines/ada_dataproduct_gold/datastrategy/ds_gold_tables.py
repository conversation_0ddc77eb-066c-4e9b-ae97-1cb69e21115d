"""Module containing the GoldDatastrategyTables class for creating the gold tables in the datastrategy tables in the gold catalog in databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from dataclasses import dataclass

from helper import get_databricks_config, get_or_create_databricks_session

logging.basicConfig()
logger = logging.getLogger("datastrategy_input")
logger.setLevel(logging.DEBUG)


class GoldDatastrategyTables:
    """Creates the codrivertags_framelevel gold table."""

    def __init__(self, schema: str, catalog: str):
        """Initialize the class."""
        self.schema = schema
        self.catalog = catalog
        self.spark = get_or_create_databricks_session()

        self.codrivertags_framelevel_table = f"{self.catalog}.{self.schema}.codrivertags_framelevel"
        self.sha_mapping_master_table = f"{self.catalog}.{self.schema}.sha_mapping_master"

    def alter_column_descriptions(self, table_name: str) -> None:
        """Alter column description for the datastrategy gold tables."""

        @dataclass
        class Attribute:
            name: str
            comment: str

        if table_name == self.create_codrivertags_framelevel_table:
            alter_attributes = [
                Attribute("img_sha", "Rectified image SHA identifier"),
                Attribute("tag_group", "Co-Driver Tag Category"),
                Attribute("tag_uid", "Value of the Co-Driver Tag category"),
                Attribute("camera_stream", "Camera stream for the corresponding img_sha"),
            ]
        elif table_name == self.sha_mapping_master_table:
            alter_attributes = [
                Attribute("recording_sha", "Recording SHA identifier"),
                Attribute("split_sha", "Split SHA identifier"),
                Attribute("rect_sha", "Rectified Image SHA identifier"),
                Attribute("anon_rect_sha", "Anonymized rectified Image SHA identifier"),
                Attribute("image_id", "Image ID"),
                Attribute("VIN", "Vehicle Identification Number"),
            ]
        for attribute in alter_attributes:
            statement = f"ALTER TABLE {table_name} ALTER COLUMN {attribute.name} COMMENT '{attribute.comment}'"
            self.spark.sql(statement)

    def create_codrivertags_framelevel_table(self) -> None:
        """Creates the codrivertags_framelevel gold table."""
        logger.info(f"Creating {self.codrivertags_framelevel_table} table")

        self.spark.sql(
            f"""
            CREATE OR REPLACE TABLE {self.codrivertags_framelevel_table}
            AS
            SELECT
            img_sha, tag_group, tag_uid, camera_stream
            FROM (
            SELECT
            cdt.tag_group,cdt.tag_uid,ddi.file_hash as img_sha,ddi.camera_stream, cdt.tag_start_at,cdt.tag_stop_at,ddi.recorded_at,
            CASE
            WHEN ddi.recorded_at >= cdt.tag_start_at AND ddi.recorded_at <= cdt.tag_stop_at THEN TRUE
            ELSE FALSE
            END AS is_tag_relevant,
            CASE
            WHEN tag_group in ('Participants','Blockage','InLaneDriving') THEN TRUE
            ELSE FALSE
            END AS is_only_frontrelevant
            FROM silver.mdd.datamanagement_co_driver_tags as cdt
            INNER JOIN silver.mdd.dsp_de_image as ddi on ddi.split_hash = cdt.file_hash
            WHERE  ddi.view = 'Label' and ddi.color_space = 'RGB' and ddi.anonymizer__git_commit IS NULL
            )
            WHERE is_tag_relevant = TRUE AND NOT (is_only_frontrelevant = TRUE AND camera_stream not in ('FC1','TVfront','FC2'))
                        """
        )
        self.alter_column_descriptions(self.codrivertags_framelevel_table)

    def create_sha_mapping_master_table(self) -> None:
        """Creates the sha_mapping gold table."""
        logger.info(f"Creating {self.sha_mapping_master_table} table")

        self.spark.sql(
            f"""
            CREATE OR REPLACE TABLE {self.sha_mapping_master_table}
            AS 
            SELECT rec.file_hash as recording_sha, fs.file_hash as split_sha, pr.file_hash as rect_sha, ch.ref_img_file_hash as anon_rect_sha, pr.image_id,rec.vin as VIN
            FROM silver.ada_ontology.recordings as rec
            INNER JOIN silver.ada_ontology.files_scenes as fs USING(vin,recording_started_at)
            LEFT JOIN silver.mdd.dsp_de_image_extended as pr on pr.split_hash = fs.file_hash
            LEFT JOIN silver.mdd.viper_labelclient_frame_mapping as ch USING(image_id)
            WHERE pr.view = 'Label' and pr.color_space = 'RGB' and pr.anonymizer__git_commit IS NULL
            GROUP BY ALL
            """
        )
        self.alter_column_descriptions(self.sha_mapping_master_table)


if __name__ == "__main__":  # pragma no cover
    args = get_databricks_config()
    dsgold_tablelist = []
    gds_tables = GoldDatastrategyTables(schema=args.schema, catalog=args.catalog)
    gds_tables.create_codrivertags_framelevel_table()
    dsgold_tablelist.append(f"{args.catalog}.{args.schema}.codrivertags_framelevel")
    gds_tables.create_sha_mapping_master_table()
    dsgold_tablelist.append(f"{args.catalog}.{args.schema}.sha_mapping_master")
    spark = get_or_create_databricks_session()

    for table_name in dsgold_tablelist:
        spark.sql(
            f"""
                ALTER TABLE {table_name} SET TAGS (
                'responsible_domain'='DSP',
                'responsible_team'='Data Strategy',
                'refresh_interval'='P1D',
                'timesystem'='UTC'
                );
            """
        )
