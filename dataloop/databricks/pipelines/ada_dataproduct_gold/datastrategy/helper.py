"""Contains helper class for common functionality used by gold layer scripts."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import argparse
import configparser
import logging
import os
from dataclasses import dataclass
from typing import Optional

from databricks.connect import DatabricksSession
from pyspark.sql import SparkSession

logging.basicConfig()
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


@dataclass
class RuntimeArgs:
    """Provide default runtime args for cmd line entries."""

    run_local: bool
    env: str
    catalog: str
    schema: str


def get_databricks_config() -> RuntimeArgs:
    """Get the cmd line arguments and read the databricks config.

    The function passed the args to the gold layer including the run_local
    parameter for debugging in custom folders
    """

    parser = argparse.ArgumentParser()

    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Run in environment, ex. dev, qa, prod",
    )
    parser.add_argument(
        "--run_local",
        action="store_true",
        help="Run in local mode. This will overwrite the environment and always run in dev.",
    )
    parser.add_argument(
        "-c",
        "--catalog",
        help="Catalogue to run against, by default catalogue is `gold`.",
    )
    parser.add_argument(
        "-s",
        "--schema",
        help="Schema to run against, by default schema is `mdd`.",
    )

    args, unknown = parser.parse_known_args()

    if args.run_local:
        config = configparser.ConfigParser()
        configFilePath = r"/home/<USER>/.databrickscfg"
        config.read(configFilePath)

        config_section = "DEFAULT"

        if args.env.upper() in config.sections():
            config_section = args.env.upper()
        section = config[config_section]

        host = section["host"].lstrip("https://")
        SPARK_REMOTE = f"sc://{host}:443/;token={section['token']};" f"x-databricks-cluster-id={section['cluster_id']}"

        print(SPARK_REMOTE)
        os.environ["SPARK_REMOTE"] = SPARK_REMOTE

        print("RUNNING IN DATABRICKS")

    return RuntimeArgs(
        run_local=args.run_local,
        env=args.env,
        catalog=args.catalog,
        schema=args.schema,
    )


def get_or_create_databricks_session(databricks_profile_name: Optional[str] = None) -> SparkSession:
    """Gets or creates a Databricks SparkSession.

    Optionally, you can provide a Databricks CLI profile name.
    The profile is then used to retrieve the SparkSession.

    Args:
        databricks_profile_name (Optional[str], optional): Name of a configured
            Databricks Profile.

    Returns:
        SparkSession: SparkSession created
    """
    if databricks_profile_name is None:
        return DatabricksSession.builder.getOrCreate()
    else:
        return DatabricksSession.builder.profile(databricks_profile_name).getOrCreate()
