bundle:
  name: ada_dataproduct_gold

include:
  - resources/*.yml

targets:
  user-dev:
    mode: development
    variables:
      env: dev
      catalog: viper_dsp_dev
      schema: datastrategy
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/ 

  dev:
    mode: production
    variables:
      env: dev
      catalog: gold_dev
      schema: datastrategy
      pause_status: "PAUSED" 
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/ 
      root_path: /Jobs/ada_dataproduct_gold/datastrategy/${bundle.name} 
    run_as:
      service_principal_name: ${var.run_sp}

  prod:
    variables:
      env: prod
      catalog: gold
      schema: datastrategy
      pause_status: "UNPAUSED"
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/ 
      root_path: /Jobs/ada_dataproduct_gold/datastrategy/${bundle.name} 
    run_as:
      service_principal_name: ${var.run_sp}