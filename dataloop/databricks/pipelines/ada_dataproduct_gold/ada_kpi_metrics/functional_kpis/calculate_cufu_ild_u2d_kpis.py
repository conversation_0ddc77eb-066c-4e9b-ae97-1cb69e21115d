"""This module calculates some functional KPI."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, count, sum, when
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base.functional_kpi_calculation import FunctionalContractKpiCalculationBase


class CuFuIldKpiCalculation(FunctionalContractKpiCalculationBase):
    """Class for Customer Function Inlane Driving KPI calculations."""

    def _load_kpi_data(  # type: ignore[override]
        self, date: datetime.date, gmdm_file_hashes: list[str], **kwargs: Any
    ) -> dict[str, Any]:
        """Loads the data required for calculating the ILD & UD2 KPIs."""
        # Gold and silver tables can be used as source for KPI data.
        trajectory_signals_table = "gold.drive_time_series.trajectory_signals_full"
        aca_feature_state_table = "silver.drive_time_series.aca_feature_state_1Hz"

        trajectory_signals_df = (
            self.spark.read.table(trajectory_signals_table)
            .select("emitted_at", "file_hash", "de_road_model_source", "route_category", "vin", "gmdm_file_hash")
            .filter(col("gmdm_file_hash").isin(gmdm_file_hashes))
        )

        aca_feature_state_df = self.spark.read.table(aca_feature_state_table).filter(
            col("gmdm_file_hash").isin(gmdm_file_hashes)
        )

        return {
            "trajectory_signals_df": trajectory_signals_df,
            "aca_feature_state_df": aca_feature_state_df,
        }

    # Class fields
    # These must be set or there will be an error when trying to create the class.

    # Value of the KPI table "source" column
    SOURCE_NAME: ClassVar[str] = "trajectory_signals_full"
    # Value of the KPI table "domain" column
    OWNER: ClassVar[str] = "MP FT Inlane Driving"
    # Logging messages from this class are be prefixed with ada_kpi_metrics/DOMAIN
    DOMAIN: ClassVar[str] = "Customer Function"

    @staticmethod
    def _calculate_availablity_long_control(
        trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate availability of longitudinal control."""
        result_rows = (
            trajectory_signals_df.filter(col("de_road_model_source") != "map_based")
            .join(
                aca_feature_state_df,
                (trajectory_signals_df.emitted_at == aca_feature_state_df.emitted_at)
                & (trajectory_signals_df.vin == aca_feature_state_df.vin),
                "inner",
            )
            .select(
                (trajectory_signals_df.emitted_at).alias("emitted_at"),
                (aca_feature_state_df.activation_state).alias("activation_state"),
            )
            .withColumn(
                "matching_rows", when(col("activation_state").isin("active", "ready_for_activation"), 1).otherwise(0)
            )
            .agg(count("*").alias("total_rows"), sum("matching_rows").alias("matching_rows"))
            .select((col("matching_rows") / col("total_rows") * 100).alias("value"))
        ).collect()

        return result_rows[0]["value"]

    @staticmethod
    def _kpi_availability_long_control_urban(
        trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate availability of longitudinal control in urban area."""

        trajectory_signals_df_filtered = trajectory_signals_df.filter(col("route_category") == "urban")
        return CuFuIldKpiCalculation._calculate_availablity_long_control(
            trajectory_signals_df_filtered, aca_feature_state_df
        )

    @staticmethod
    def _kpi_availability_long_control_highway(
        trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate availability of longitudinal control on highway."""

        trajectory_signals_df_filtered = trajectory_signals_df.filter(col("route_category") == "highway")
        return CuFuIldKpiCalculation._calculate_availablity_long_control(
            trajectory_signals_df_filtered, aca_feature_state_df
        )

    @staticmethod
    def _kpi_ud2_long_control(
        trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate the sum of all values in a column."""
        result_rows = (
            trajectory_signals_df.filter(
                col("de_road_model_source") == "map_based",
            )
            .join(
                aca_feature_state_df,
                (trajectory_signals_df.emitted_at == aca_feature_state_df.emitted_at)
                & (trajectory_signals_df.vin == aca_feature_state_df.vin),
                "inner",
            )
            .select(
                (trajectory_signals_df.emitted_at).alias("emitted_at"),
                (aca_feature_state_df.activation_state).alias("activation_state"),
            )
            .withColumn(
                "matching_rows",
                when(
                    col("activation_state").isin("active", "ready_for_activation"),
                    1,
                ).otherwise(0),
            )
            .agg(count("*").alias("total_rows"), sum("matching_rows").alias("matching_rows"))
            .select((col("matching_rows") / col("total_rows") * 100).alias("value"))
        ).collect()

        return result_rows[0]["value"]

    @staticmethod
    def _calculate_availablity_lat_control(
        trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate availability of lateral control."""
        result_rows = (
            trajectory_signals_df.filter(col("de_road_model_source") != "map_based")
            .join(
                aca_feature_state_df,
                (trajectory_signals_df.emitted_at == aca_feature_state_df.emitted_at)
                & (trajectory_signals_df.vin == aca_feature_state_df.vin),
                "inner",
            )
            .select(
                (trajectory_signals_df.emitted_at).alias("emitted_at"),
                (aca_feature_state_df.activation_state).alias("activation_state"),
                (trajectory_signals_df.de_road_model_source).alias("de_road_model_source"),
            )
            .withColumn(
                "matching_rows",
                when(
                    (
                        col("activation_state").isin("active", "ready_for_activation")
                        & (col("de_road_model_source") != "ego_based")
                    ),
                    1,
                ).otherwise(0),
            )
            .agg(count("*").alias("total_rows"), sum("matching_rows").alias("matching_rows"))
            .select((col("matching_rows") / col("total_rows") * 100).alias("value"))
        ).collect()

        return result_rows[0]["value"]

    @staticmethod
    def _kpi_availability_lat_control_urban(
        trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate availability of lateral control in urban area."""
        trajectory_signals_df_filtered = trajectory_signals_df.filter(col("route_category") == "urban")
        return CuFuIldKpiCalculation._calculate_availablity_lat_control(
            trajectory_signals_df_filtered, aca_feature_state_df
        )

    @staticmethod
    def _kpi_availability_lat_control_highway(
        trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate availability of lateral control on highway."""
        trajectory_signals_df_filtered = trajectory_signals_df.filter(col("route_category") == "highway")
        return CuFuIldKpiCalculation._calculate_availablity_lat_control(
            trajectory_signals_df_filtered, aca_feature_state_df
        )

    @staticmethod
    def _kpi_ud2_lat_control(trajectory_signals_df: DataFrame, aca_feature_state_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the sum of all values in a column."""
        result_rows = (
            trajectory_signals_df.filter(col("de_road_model_source") == "map_based")
            .join(
                aca_feature_state_df,
                (trajectory_signals_df.emitted_at == aca_feature_state_df.emitted_at)
                & (trajectory_signals_df.vin == aca_feature_state_df.vin),
                "inner",
            )
            .select(
                (trajectory_signals_df.emitted_at).alias("emitted_at"),
                (aca_feature_state_df.activation_state).alias("activation_state"),
            )
            .withColumn(
                "matching_rows",
                when(
                    col("activation_state").isin("active", "ready_for_activation"),
                    1,
                ).otherwise(0),
            )
            .agg(count("*").alias("total_rows"), sum("matching_rows").alias("matching_rows"))
            .select((col("matching_rows") / col("total_rows") * 100).alias("value"))
        ).collect()

        return result_rows[0]["value"]

    # Set your previously defined KPI calculation functions in this mapping
    # with the ID of the corresponding ADO key result as key.
    NEEDS_KPI_ID: ClassVar[dict[str, Callable[..., float]]] = {
        "REQ_METRIC_CONTRACT_KPI_000003_URBAN": _kpi_availability_long_control_urban,
        "REQ_METRIC_CONTRACT_KPI_000003_HIGHWAY": _kpi_availability_long_control_highway,
        "REQ_METRIC_CONTRACT_KPI_000004": _kpi_ud2_long_control,
        "REQ_METRIC_CONTRACT_KPI_000005_URBAN": _kpi_availability_lat_control_urban,
        "REQ_METRIC_CONTRACT_KPI_000005_HIGHWAY": _kpi_availability_lat_control_highway,
        "REQ_METRIC_CONTRACT_KPI_000006": _kpi_ud2_lat_control,
    }


if __name__ == "__main__":  # pragma: no cover
    CuFuIldKpiCalculation.cli_entrypoint()
