"""Calculate Cloud Test Execution KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import sum
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import NonFunctionalContractKpiCalculationBase


class CTXKpiCalculation(NonFunctionalContractKpiCalculationBase):
    """Class for Cloud Test Execution KPI calculations."""

    def _load_kpi_data(self, date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Load the data required for calculating the Cloud Test Execution KPIs."""
        statistics_daily_table = f"{self.gold_catalog}.digital_testing.statistics_daily"

        start_date = datetime.date(2025, 3, 1)  # see: https://pace-project.atlassian.net/l/cp/nMjjkQfH
        end_date = date - datetime.timedelta(days=1)
        return {
            "statistics_daily_df": self.spark.read.table(statistics_daily_table).where(
                f"window.start >= '{start_date.isoformat()}' AND window.end <= '{end_date.isoformat()}'"
            ),
        }

    SOURCE_NAME: ClassVar[str] = "digital_testing_reports"
    OWNER: ClassVar[str] = "Cloud Test Execution"
    DOMAIN: ClassVar[str] = "ctx"

    @staticmethod
    def _kpi_sum_recompute_duration(statistics_daily_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the sum of all values in a column."""
        result_rows = statistics_daily_df.agg(sum("total_recompute_duration_h").alias("value")).collect()
        return result_rows[0]["value"]

    NEEDS_KPI_ID: ClassVar[dict[str, Callable[..., float]]] = {
        "REQ_METRIC_NON_FUNCTIONAL_CONTRACT_KPI_000002": _kpi_sum_recompute_duration,
    }


if __name__ == "__main__":  # pragma: no cover
    CTXKpiCalculation.cli_entrypoint()
