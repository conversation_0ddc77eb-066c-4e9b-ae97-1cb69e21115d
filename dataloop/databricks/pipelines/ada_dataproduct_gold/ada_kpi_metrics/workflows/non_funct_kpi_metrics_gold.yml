resources:
  jobs:
    ada_non_funct_kpi_metrics_gold:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA KPI Metrics - Non-Functional - Gold - Nightly
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "Gold"
        schedule: "Nightly"
        "ADA non functional KPI Metrics": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      schedule:
        quartz_cron_expression: ${var.nightly_schedule}
        timezone_id: UTC
        pause_status: ${var.nightly_trigger}

      tasks:
        # Template task
        # - task_key: calculate_xyz_kpis
        #   spark_python_task:
        #     python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/non_functional_kpis/calculate_xyz_kpis.py
        #   job_cluster_key: ada_kpi_metrics_job_cluster
        #   libraries:
        #     - pypi:
        #         package: rddlib==${var.rddlib_version}
        - task_key: calculate_ctx_kpis
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/non_functional_kpis/calculate_ctx_kpis.py
          job_cluster_key: ada_kpi_metrics_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}


      job_clusters:
        - job_cluster_key: ada_kpi_metrics_job_cluster
          new_cluster: ${var.job_cluster}
