# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================
bundle:
  name: ada_kpi_metrics_gold

include:
  - ../../common/rdd_orchestration/env_targets.yml
  - workflows/*.yml

variables:
  driver_pool_id:
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  worker_pool_id:
    lookup:
      instance_pool: "default_D4ads_v5_rt14.3"
  job_cluster:
    type: complex
    default:
      spark_version: ${var.spark_version}
      autoscale:
        min_workers: 1
        max_workers: 6
      policy_id: ${var.job_cluster_policy_id}
      instance_pool_id: ${var.worker_pool_id}
      driver_instance_pool_id: ${var.driver_pool_id}

# TODO: Remove when include supported by VSCode extension
targets:
  user-dev:
    variables:
      run_sp: ${workspace.current_user.userName}
      rddlib_version: "2.1.0"
      teams_channel: e83a8487-5008-4c45-b013-774c41401a02
    mode: development
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}

  dev:
    variables:
      run_sp: a0125883-86f0-4967-af40-e69f011ec0db
      rddlib_version: "2.1.0"
      teams_channel: e83a8487-5008-4c45-b013-774c41401a02
    mode: production
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: a0125883-86f0-4967-af40-e69f011ec0db

  qa:
    variables:
      run_sp: 8275abc3-ff23-48eb-97d3-8a3673ab14dd
      rddlib_version: "2.1.0"
      teams_channel: e8a49137-ce9c-48a9-b46c-8dbc97725a4c
      # Nightly at 5 AM UTC every Wednesday
      nightly_schedule: "0 0 5 ? * Wed"
      nightly_trigger: "UNPAUSED"
    mode: production
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: 8275abc3-ff23-48eb-97d3-8a3673ab14dd

  prod:
    variables:
      run_sp: 73a62a2d-609e-43c6-bdaf-d103d7a4eb43
      rddlib_version: "2.1.0"
      teams_channel: 949087fc-6505-43a1-9b3e-9b9910d1e167
      # Nightly at 5 AM UTC every day
      nightly_schedule: "0 0 5 * * ?"
      nightly_trigger: "UNPAUSED"
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: 73a62a2d-609e-43c6-bdaf-d103d7a4eb43
