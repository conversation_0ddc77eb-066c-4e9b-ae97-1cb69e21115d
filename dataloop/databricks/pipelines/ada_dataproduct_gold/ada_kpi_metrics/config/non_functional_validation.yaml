# ======================================================================================
# C O P Y R I G H T
# --------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ======================================================================================
# This file includes the configuration for the great expectations setup and the different
# tables in dev environment that need to be validated.
results_to_validate:
  - domain: "General"
    expectations:
      - type: "ExpectColumnValuesToBeUnique"
        args:
          column: "needs_kpi_id"

  - domain: "ctx"
    data_source_name: "validation_ctx"
    data_asset: "validation_ctx_data_sources"
    batch_definition: "validation_ctx_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"

  # - domain: ""
  #   data_source_name: "validation_cufu"
  #   data_asset: "validation_cufu_data_sources"
  #   batch_definition: "validation_cufu_batch_definition"
  #   expectations:
  #     - type: "ExpectColumnValuesToBeBetween"
  #       args:
  #         max_value: 100
  #         min_value: 50
