# ======================================================================================
# C O P Y R I G H T
# --------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ======================================================================================
# This file includes the configuration for the great expectations setup and the different
# tables in dev environment that need to be validated.
results_to_validate:
  - domain: "General"
    expectations:
      - type: "ExpectColumnValuesToBeUnique"
        args:
          column: "needs_kpi_id"
      - type: "ExpectColumnValuesToMatchRegex"
        args:
          column: "commit_sha"
          regex: "^[a-z0-9]+$"
      # - type: "ExpectColumnDistinctValuesToBeInSet"
      #   args:
      #     column: "dataset"
      #     value_set: ["dataset_drives", "performance_dataset"]

  # Template:
  # - domain: "xyz"
  #   data_source_name: "validation_xyz"
  #   data_asset: "validation_xyz_data_sources"
  #   batch_definition: "validation_xyz_batch_definition"
  #   expectations:
  #     - type: "ExpectColumnValuesToBeBetween"
  #       args:
  #         max_value: 100
  #         min_value: 50
  #     - type: "ExpectColumnValuesToBeUnique"

  - domain: "Customer Function"
    data_source_name: "validation_cufu"
    data_asset: "validation_cufu_data_sources"
    batch_definition: "validation_cufu_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToBeBetween"
        args:
          column: "current_value"
          max_value: 100
          min_value: 0
