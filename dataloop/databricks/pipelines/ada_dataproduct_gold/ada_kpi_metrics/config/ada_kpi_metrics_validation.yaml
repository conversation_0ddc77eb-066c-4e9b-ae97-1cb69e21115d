# ======================================================================================
# C O P Y R I G H T
# --------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ======================================================================================
# This file includes the configuration for the great expectations setup and the different
# tables in dev environment that need to be validated.
results_to_validate:
  - domain: "General"
    expectations:
      - type: "ExpectColumnValuesToBeUnique"
        args:
          column: "key_result_id"

  - domain: "maploc"
    data_source_name: "validation_maploc"
    data_asset: "validation_maploc_data_sources"
    batch_definition: "validation_maploc_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"

  - domain: "dsp"
    data_source_name: "validation_dsp"
    data_asset: "validation_dsp_data_sources"
    batch_definition: "validation_dsp_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"

  - domain: "dora"
    data_source_name: "validation_dora"
    data_asset: "validation_dora_data_sources"
    batch_definition: "validation_dora_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"

  - domain: "cufu"
    data_source_name: "validation_cufu"
    data_asset: "validation_cufu_data_sources"
    batch_definition: "validation_cufu_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"

  - domain: "cufu_v2"
    data_source_name: "validation_cufu_v2"
    data_asset: "validation_cufu_v2_data_sources"
    batch_definition: "validation_cufu_v2_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"

  - domain: "ado"
    data_source_name: "validation_ado_kpis"
    data_asset: "validation_ado_kpis_data_sources"
    batch_definition: "validation_ado_kpis_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"

  - domain: "aggregate_cufu_kpi_data"
    data_source_name: "validation_aggregate_cufu_kpi_data"
    data_asset: "validation_aggregate_cufu_kpi_data_data_sources"
    batch_definition: "validation_aggregate_cufu_kpi_data_batch_definition"
    expectations:
      - type: "ExpectColumnValuesToNotBeNull"
        args:
          column: "current_value"
