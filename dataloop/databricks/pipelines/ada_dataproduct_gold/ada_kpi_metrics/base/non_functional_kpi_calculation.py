"""Non functional KPI calculation base class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
import logging
import sys
from abc import ABC
from typing import Any, ClassVar

from base.contract_kpi_calculation import ContractKpiCalculationBase
from pyspark.sql import DataFrame
from pyspark.sql.types import ArrayType, DateType, DoubleType, StringType, StructField, StructType

logger = logging.getLogger(__name__)


class NonFunctionalContractKpiCalculationBase(ContractKpiCalculationBase, ABC):
    """Base class for non-functional KPI calculations of a specific domain."""

    def calculate(self, date: datetime.date) -> DataFrame:
        """Calculate all key result KPIs for a given date.

        Args:
            date (datetime.date): The date to calculate the KPIs for.

        Returns:
            DataFrame: A dataframe containing one row per calculated KPI.
        """
        logger.info(f"Calculate KPIs for owner {self.OWNER} and date {date.isoformat()}")

        result_rows: list[dict[str, Any]] = []
        for params in self._iterate_parameter_sets():
            logger.info(f"Calculate KPIs for parameter set {params}")

            kpi_data = self._load_kpi_data(date, **params)
            kpi_rows = self._calculate_from_data(kpi_data)

        params_strs = [f"{k}={v}" for k, v in params.items()] if len(params) > 0 else None
        for row in kpi_rows:
            # Add additional information to row
            row["owner"] = self.OWNER
            row["updated_at"] = date
            row["source"] = self.SOURCE_NAME
            row["parameters"] = params_strs
            row["kpi_logic_file"] = str(sys.argv[0]).split("/")[-1]
            result_rows.append(row)

        # Create Spark dataframe
        return self.spark.createDataFrame(result_rows, schema=self.GOLD_SCHEMA_KPIS)

    GOLD_TABLE_KPIS: ClassVar[str] = "non_functional_contract_kpis"

    GOLD_SCHEMA_KPIS: ClassVar[StructType] = StructType(
        [
            StructField("needs_kpi_id", StringType(), True),
            StructField("title", StringType(), True),
            StructField("owner", StringType(), True),
            StructField("kpi_logic_file", StringType(), True),
            StructField("updated_at", DateType(), True),
            StructField("current_value", DoubleType(), True),
            StructField("parameters", ArrayType(StringType()), True),
        ]
    )
