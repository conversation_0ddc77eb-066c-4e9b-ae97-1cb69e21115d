"""Class to validate kpi results."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from typing import Any

import great_expectations as gx
import yaml
from great_expectations.core.batch_definition import BatchDefinition
from great_expectations.data_context.data_context.ephemeral_data_context import EphemeralDataContext
from great_expectations.datasource.fluent.spark_datasource import DataFrameAsset, SparkDatasource
from pyspark.sql import DataFrame

CONFIG_FILE_MAPPING = {
    "functional_contract_kpis": "functional_validation.yaml",
    "non_functional_contract_kpis": "non_functional_validation.yaml",
    "key_result_kpis": "ada_kpi_metrics_validation.yaml",
}

logger = logging.getLogger(__name__)


class ValidateKpiResults:
    """Validation Class for KPI results."""

    file_name: str
    context: EphemeralDataContext
    data_source: SparkDatasource
    data_asset: DataFrameAsset
    batch_definition: BatchDefinition
    expectations: list[dict[str, Any]]
    config: dict[str, Any]

    def __init__(self, table_name: str, domain: str) -> None:
        """Init function for class.

        Args:
            table_name (str): _description_
            domain (str): _description_
        """
        self.file_name = CONFIG_FILE_MAPPING[table_name]
        self._load_config(domain)

        self.context = gx.get_context()
        self.data_source = self.context.data_sources.add_spark(name=self.config["data_source_name"])
        self.data_asset = self.data_source.add_dataframe_asset(name=self.config["data_asset"])
        self.batch_definition = self.data_asset.add_batch_definition_whole_dataframe(self.config["data_asset"])

        self.expectations.extend(entries for entries in self.config["expectations"])

    def _load_config(self, domain: str) -> None:
        """Loag config from yaml file.

        Args:
            domain (str): the domain whose file is run in the pipeline
        """
        # add exception handling
        with open(f"../config/{self.file_name}", "r", encoding="utf-8") as file:
            yaml_content = yaml.safe_load(file)

        for configs in yaml_content["results_to_validate"]:
            if configs["domain"] == "General":
                self.expectations = configs["expectations"]
            elif configs["domain"] == domain:
                self.config = configs
                logger.info(f"The config {self.config} was loaded for validating {domain}'s KPIs.")

        if self.config is None:
            raise NotImplementedError(f"Logging domain {domain} not found in {self.file_name}")

    def validate_kpi_results(self, kpi_df: DataFrame) -> None:
        """Validate the KPI results.

        Args:
            kpi_df (DataFrame): calculated KPIs

        Raises:
            ValueError: if there is data that is not as expected a error will be raised
        """

        failed_results = []
        failed_type = []
        for expectation in self.expectations:
            result = self.run_expectations(kpi_df, expectation)
            if result["success"]:
                continue
            elif result["success"] is False:
                failed_results.append({"result": result})
                failed_type.append({"type": expectation["type"]})

        if len(failed_results) > 0:
            logger.error(f"Validation failed for {self.config['domain']}: {failed_type}")
            raise ValueError(f"Validation failed for {self.config['domain']}")

    def run_expectations(self, kpi_df: DataFrame, conf: dict[str, Any]) -> dict[str, Any]:
        """Validation of KPIs.

        Args:
            kpi_df (DataFrame): calculated KPIs

        Returns:
            dict: result of given expectation
        """
        batch = self.batch_definition.get_batch(batch_parameters={"dataframe": kpi_df})
        # loop through all expectations
        match conf["type"]:
            case "ExpectColumnValuesToBeBetween":
                expectation = gx.expectations.ExpectColumnValuesToBeBetween(
                    column="current_value",
                    min_value=conf["args"]["min_value"],
                    max_value=conf["args"]["max_value"],
                )
            case "ExpectColumnValuesToBeUnique":
                expectation = gx.expectations.ExpectColumnValuesToBeUnique(column=conf["args"]["column"])
            case "ExpectColumnValuesToBeOfType":
                expectation = gx.expectations.ExpectColumnValuesToBeOfType(
                    column=conf["args"]["column"], type_="NUMBER"
                )
            case "ExpectColumnValuesToMatchRegex":
                expectation = gx.expectations.ExpectColumnValuesToMatchRegex(
                    column=conf["args"]["column"], regex=conf["args"]["regex"]
                )
            case "ExpectColumnDistinctValuesToBeInSet":
                expectation = gx.expectations.ExpectColumnDistinctValuesToBeInSet(
                    column=conf["args"]["column"], value_set=conf["args"]["value_set"]
                )
            case "ExpectColumnValuesToNotBeNull":
                expectation = gx.expectations.ExpectColumnValuesToNotBeNull(column=conf["args"]["column"])

        result = batch.validate(expectation, result_format="SUMMARY")

        return result
