"""Key result KPI calculation base class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
import itertools
import logging
import math
from abc import ABC, abstractmethod
from functools import cache
from typing import Any, Callable, ClassVar, Generator

from base.kpi_calculation import KpiCalculationBase
from constants.common import (
    GOLD_SCHEMA_KEY_RESULT_KPIS,
    GOLD_TABLE_KEY_RESULT_KPIS,
    SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH,
    SILVER_TABLE_ADO_NODES,
)
from pyspark.sql import DataFrame
from pyspark.sql.types import StructType
from rddlib import delta_table_utils as dtu

logger = logging.getLogger(__name__)


class KeyResultKpiCalculationBase(KpiCalculationBase, ABC):
    """Base class for key result KPI calculations of a specific domain."""

    def calculate(self, date: datetime.date) -> DataFrame:
        """Calculate all key result KPIs for a given date.

        Args:
            date (datetime.date): The date to calculate the KPIs for.

        Returns:
            DataFrame: A dataframe containing one row per calculated KPI.
        """
        logger.info(f"Calculate KPIs for domain {self.DOMAIN_NAME} and date {date.isoformat()}")

        result_rows: list[dict[str, Any]] = []
        for params in self._iterate_parameter_sets():
            logger.info(f"Calculate KPIs for parameter set {params}")
            kpi_data = self._load_kpi_data(date, **params)
            kpi_rows = self._calculate_from_data(kpi_data)

            params_strs = [f"{k}={v}" for k, v in params.items()] if len(params) > 0 else None
            for row in kpi_rows:
                # Add additional information to row
                row["domain"] = self.DOMAIN_NAME
                row["updated_at"] = date
                row["source"] = self.SOURCE_NAME
                row["parameters"] = params_strs
                result_rows.append(row)

        # Create Spark dataframe
        return self.spark.createDataFrame(result_rows, schema=self.GOLD_SCHEMA_KPIS)

    def _calculate_from_data(self, kpi_data: dict[str, DataFrame]) -> list[dict[str, Any]]:
        """Calculates the key result KPIs from the given data.

        Args:
            kpi_data (dict[str, DataFrame]): The data used for calculating the KPIs.

        Returns:
            list[dict[str, Any]]: A list of dicts containing the calculated KPI value
                as well as metadata of the corresponding key result.
        """
        result_rows: list[dict[str, Any]] = []
        # Calculate KPIs
        for key_result_id, kpi_func in self.KPI_DEFINITIONS.items():
            raw_kpi_value = kpi_func(**kpi_data)
            # Skip KPI if no valid value could be calculated
            if raw_kpi_value is None or math.isnan(raw_kpi_value):
                logger.info(f"No valid value for key result {key_result_id} could be calculated.")
                continue

            # Query key result data and add KPI value
            result_row = self._query_key_result_data(key_result_id).copy()  # Copy to not modify the cachaed value
            result_row["current_value"] = float(raw_kpi_value)
            result_rows.append(result_row)

        return result_rows

    @abstractmethod
    def _load_kpi_data(self, date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Loads all the data required for the KPI calculations of this class.

        A value of the returned mapping is passed to a KPI calculation function
        if it defines an argument named as the mapping key.
        Must be implemented in a subclass.

        Args:
            date (datetime.date): The date for which the KPIs shall be calculated.

        Returns:
            dict[str, Any]: A mapping of KPI calculation function argument names
                and values.
        """
        pass

    @cache
    def _query_key_result_data(self, key_result_id: int) -> dict[str, Any]:
        """Queries the key result metadata from the corresponding ADO ticket.

        Args:
            key_result_id (int): The ID of the related key result.

        Returns:
            dict[str, Any]: A mapping of all key result information relevant for the KPI.
        """
        # Retrieve corresponding key result from ADO nodes
        ado_nodes_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH}.{SILVER_TABLE_ADO_NODES}"
        ado_nodes_df = self.spark.read.table(ado_nodes_table)
        ado_results = ado_nodes_df.where(f"id = {key_result_id}").collect()
        assert len(ado_results) == 1, f"Exactly one ADO workitem with ID {key_result_id} expected."
        key_result = ado_results[0]
        assert key_result["workitem_type"] == "Key Result", "ADO workitem of type Key Result expected."

        return {
            "key_result_id": key_result_id,
            "title": key_result["title"],
            "target_value": key_result["key_result_target_value"],
            "unit": key_result["key_result_unit"],
            "type": key_result["key_result_type"],
        }

    def update_delta_table(self, kpi_df: DataFrame) -> None:
        """Update the corresponding delta table with given KPI data.

        The key result ID and the date form a unique index, so rows with a
        key result ID - date pair not yet existing in the delta tale will be
        added while existing will be overwritten.

        Args:
            kpi_df (DataFrame): A dataframe containing one row per calculated KPI.
                Raises a ValueError if the schema does not match.
        """
        super().update_delta_table(kpi_df)

        # Set table metadata
        dtu.update_table_metadata(
            self.table_full,
            (
                "This table contains the values of key result KPIs of different domains."
                " The logic for calculating the KPIs is maintained by the respective domains."
            ),
        )

    # Class fields

    GOLD_TABLE_KPIS: ClassVar[str] = GOLD_TABLE_KEY_RESULT_KPIS

    GOLD_SCHEMA_KPIS: ClassVar[StructType] = GOLD_SCHEMA_KEY_RESULT_KPIS

    SCHEMA_ID: ClassVar[str] = "key_result_id"

    SOURCE_NAME: ClassVar[str] = NotImplemented
    """The name of the data source used for calculating the KPIs."""
    DOMAIN_NAME: ClassVar[str] = NotImplemented
    """The name of the project domain."""
    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = NotImplemented
    """A mapping from ADO key result ticket IDs to KPI calculation functions."""
    PARAMETERIZATION: ClassVar[dict[str, list[Any]] | None] = None
    """A mapping from parameter names to all possible values of the parameters.

    If defined, the _load_kpi_data_parameterized must be implemented instead of
    the _load_kpi_data function."""

    def __init_subclass__(cls) -> None:
        """Check if all necessary class fields are implemented."""
        # Skip checks if sublass is still ABC
        if ABC in cls.__bases__:
            return

        if cls.SOURCE_NAME is NotImplemented:
            raise NotImplementedError("SOURCE_NAME must be set in the subclass.")
        if cls.DOMAIN_NAME is NotImplemented:
            raise NotImplementedError("DOMAIN_NAME must be set in the subclass.")
        if cls.KPI_DEFINITIONS is NotImplemented:
            raise NotImplementedError("KPI_DEFINITIONS must be set in the subclass.")

        cls.CLI_DESCRIPTION = f'Calculates "{cls.DOMAIN_NAME}" key result KPIs.'
        super().__init_subclass__()

    @classmethod
    def _iterate_parameter_sets(cls) -> Generator[dict[str, Any], None, None]:
        """Generates all possible sets of parameters from the parameterization.

        If no parameterization is given, returns a single empty parameter set.

        Yields:
            Generator[dict[str, Any], None, None]: A set of parameters as mapping.
        """
        if cls.PARAMETERIZATION is None:
            yield {}
            return

        for param_set_values in itertools.product(*cls.PARAMETERIZATION.values()):
            param_set = {key: param_set_values[idx] for idx, key in enumerate(cls.PARAMETERIZATION.keys())}
            yield param_set
