"""KPI calculation base class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import datetime
import logging
from abc import ABC, abstractmethod
from typing import ClassVar, Sequence, TypeVar

from base.kpi_validation import ValidateKpiResults
from constants.common import GOLD_DBX_SCHEMA
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType
from rddlib import FullSchemaName, FullTableName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging

logger = logging.getLogger(__name__)


def _parse_date_ranges(value: str) -> list[datetime.date]:
    """Parses a date range given as string as a date list.

    The input may either be a single date in ISO format
    or a range in the form of "{start_date}_{end_date}"
    with start and end dates also in ISO format.
    The end date can also be omitted to use the current date.

    Args:
        value (str): _description_

    Raises:
        ValueError: If the start is after the end date.

    Returns:
        list[datetime.date]: All dates between and including
            start and end date or the single date.
    """
    # Check if only single value provided
    if "_" not in value:
        return [datetime.date.fromisoformat(value)]

    raw_start, raw_end = value.split("_")
    start = datetime.date.fromisoformat(raw_start)
    end = datetime.date.fromisoformat(raw_end) if raw_end != "" else datetime.date.today()
    if start > end:
        raise ValueError("Start date must not be after end date.")

    return [start + datetime.timedelta(days=day_offset) for day_offset in range((end - start).days + 1)]


T = TypeVar("T")


def flatten(seq_of_seqs: Sequence[Sequence[T]]) -> list[T]:
    """Flattens a list of lists."""
    return [e for seq in seq_of_seqs for e in seq]


class KpiCalculationBase(ABC):
    """Base class for KPI calculations."""

    spark: SparkSession
    silver_catalog: str
    gold_catalog: str
    table_full: FullTableName
    test_mode: bool

    def __init__(
        self,
        spark: SparkSession,
        silver_catalog: str,
        gold_catalog: str,
        test_mode: bool,
    ) -> None:
        """Initializes a KPI calculation.

        Args:
            spark (SparkSession): The Spark session object.
            silver_catalog (str): The name of the silver catalog.
            gold_catalog (str): The name of the gold catalog.
            test_mode (bool): Provide True for an end to end test run.
        """
        self.spark = spark
        self.silver_catalog = silver_catalog
        self.gold_catalog = gold_catalog
        self.test_mode = test_mode

        table = self.GOLD_TABLE_KPIS
        self.schema = self.GOLD_SCHEMA_KPIS
        if self.test_mode:
            table += "_test"
        self.table_full = FullTableName(FullSchemaName(gold_catalog, GOLD_DBX_SCHEMA, False), table)

    @abstractmethod
    def calculate(self, date: datetime.date) -> DataFrame:
        """Calculate all KPIs for a given date.

        Must be implemented in a subclass.

        Args:
            date (datetime.date): The date to calculate the KPIs for.

        Returns:
            DataFrame: A dataframe containing one row per calculated KPI.
        """

    def update_delta_table(self, kpi_df: DataFrame) -> None:
        """Update the corresponding delta table with given KPI data.

        The key result ID and the date form a unique index, so rows with a
        key result ID - date pair not yet existing in the delta tale will be
        added while existing will be overwritten.

        Args:
            kpi_df (DataFrame): A dataframe containing one row per calculated KPI.
                Raises a ValueError if the schema does not match.
        """
        # Ensure kpi_df has correct schema
        if kpi_df.schema != self.schema:
            raise ValueError(f"KPI data has invalid schema: {kpi_df.schema}. Expected: {self.schema}")

        if dtu.table_exists(str(self.table_full)):
            dtu.merge(
                kpi_df,
                str(self.table_full),
                (
                    f"target.{self.SCHEMA_ID} = source.{self.SCHEMA_ID}"
                    " AND target.updated_at = source.updated_at"
                    f" AND {self.gold_catalog}.{GOLD_DBX_SCHEMA}.array_equals(target.parameters, source.parameters)"
                ),
            )
        else:
            dtu.overwrite(kpi_df, str(self.table_full))

    def validate_data_frame(self, kpi_df: DataFrame) -> None:
        """Validate the KPI dataframe.

        Checks if the schema of the given dataframe matches the expected schema.

        Args:
            kpi_df (DataFrame): The dataframe to validate.

        Raises:
            ValueError: If the schema does not match.
        """
        validation_kpi_results = ValidateKpiResults(self.GOLD_TABLE_KPIS, self.DOMAIN)
        validation_kpi_results.validate_kpi_results(kpi_df)

        logger.info(f"Validated KPI data for {self.DOMAIN}. All Validation passed!")

    # Class fields
    GOLD_SCHEMA_KPIS: ClassVar[StructType] = NotImplemented
    """The schema of the KPI data to be written to the delta table."""
    GOLD_TABLE_KPIS: ClassVar[str] = NotImplemented
    """The name of the gold table to write the KPI values to."""
    DOMAIN: ClassVar[str] = NotImplemented
    """A custom domain name to include in the logging messages."""
    SCHEMA_ID: ClassVar[str] = NotImplemented
    """A custom name for defining the ids for merging the tables."""
    CLI_DESCRIPTION: ClassVar[str | None] = None
    """A description which shall be shown when calling CLI help page."""

    def __init_subclass__(cls) -> None:
        """Check if all necessary class fields are implemented."""
        super().__init_subclass__()
        # Skip checks if sublass is still ABC
        if ABC in cls.__bases__:
            return

        if cls.GOLD_SCHEMA_KPIS is NotImplemented:
            raise NotImplementedError("GOLD_SCHEMA_KPIS must be set in the subclass.")
        if cls.GOLD_TABLE_KPIS is NotImplemented:
            raise NotImplementedError("GOLD_TABLE_KPIS must be set in the subclass.")
        if cls.DOMAIN is NotImplemented:
            raise NotImplementedError("DOMAIN must be set in the subclass.")
        if cls.SCHEMA_ID is NotImplemented:
            raise NotImplementedError("SCHEMA_ID must be set in the subclass.")

    @classmethod
    def _cli_parse_args(cls, argv: Sequence[str] | None = None) -> argparse.Namespace:
        """Parse command line arguments of a KPI script."""
        parser = argparse.ArgumentParser(description=cls.CLI_DESCRIPTION)

        parser.add_argument("-r", "--run_id", type=str, default=None, dest="run_id")
        parser.add_argument(
            "-e",
            "--env",
            choices=["dev", "qa", "prod"],
            default="dev",
            help="Environment, ex. dev, qa, prod",
        )
        parser.add_argument(
            "--dates",
            type=_parse_date_ranges,
            help=(
                "Dates for which to calculate the KPIs."
                " This may either be a single date in ISO format"
                ' or a range in the form of "{start_date}_{end_date}"'
                " with start and end dates also in ISO format."
                " The end date can also be omitted to use the current date."
            ),
            nargs="+",
        )
        parser.add_argument(
            "-m",
            "--test_mode",
            action="store_true",
            help="Test Mode. If set, uses test table.",
        )

        return parser.parse_args(argv)

    @classmethod
    def cli_entrypoint(cls, argv: Sequence[str] | None = None) -> None:
        """Entry point for the command line interface of a KPI script."""
        args = cls._cli_parse_args(argv)
        run_id = args.run_id
        test_mode = args.test_mode

        spark = SparkSession.builder.getOrCreate()

        silver_catalog = get_dbx_env_catalog("silver")
        gold_catalog = get_dbx_env_catalog("gold")

        # Setup logging
        setup_databricks_logging(
            FullSchemaName(gold_catalog, GOLD_DBX_SCHEMA, False),
            f"ada_kpi_metrics/{cls.DOMAIN}",
            run_id=run_id,
            enabled_loggers=["base"],
        )

        if args.dates is None:
            dates = [datetime.date.today()]
            logger.info(f"No explicit date requested. Using current date {dates[0]}.")
        else:
            # Flatten list of list of dates, remove duplicates and sort ascending
            dates = sorted(list(set(flatten(args.dates))))

        kpi_calculation = cls(spark, silver_catalog, gold_catalog, test_mode)

        # Calculate KPIs for each requested date
        kpi_df = kpi_calculation.calculate(dates[0])
        for date in dates[1:]:
            kpi_df = kpi_df.union(kpi_calculation.calculate(date))
        # Write results to delta table
        kpi_calculation.validate_data_frame(kpi_df)
        kpi_calculation.update_delta_table(kpi_df)
