"""Contract KPI calculation base class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
import itertools
import logging
import math
from abc import ABC, abstractmethod
from functools import cache
from typing import Any, Callable, ClassVar, Generator

from base.kpi_calculation import KpiCalculationBase
from constants.common import SILVER_DBX_SCHEMA_NEEDS, SILVER_TABLE_PACE_NEEDS
from pyspark.sql import DataFrame
from rddlib import delta_table_utils as dtu

logger = logging.getLogger(__name__)


class ContractKpiCalculationBase(KpiCalculationBase, ABC):
    """Base class for contract KPI calculations of a specific domain."""

    def _calculate_from_data(self, kpi_data: dict[str, DataFrame]) -> list[dict[str, Any]]:
        """Calculates the contract KPIs from the given data.

        Args:
            kpi_data (dict[str, DataFrame]): The data used for calculating the KPIs.

        Returns:
            list[dict[str, Any]]: A list of dicts containing the calculated KPI value
                as well as metadata of the corresponding contract kpi needs element.
        """
        result_rows: list[dict[str, Any]] = []
        # Calculate KPIs
        for needs_kpi_id, kpi_func in self.NEEDS_KPI_ID.items():
            raw_kpi_value = kpi_func(**kpi_data)
            # Skip KPI if no valid value could be calculated
            if raw_kpi_value is None or math.isnan(raw_kpi_value):
                logger.info(f"No valid value for needs id {needs_kpi_id} could be calculated.")
                continue

            # Query needs data and add KPI value
            result_row = self._query_needs_kpi_id_data(needs_kpi_id).copy()  # Copy to not modify the cached value
            result_row["current_value"] = float(raw_kpi_value)
            result_rows.append(result_row)

        return result_rows

    @abstractmethod
    def _load_kpi_data(self, date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Loads all the data required for the KPI calculations of this class.

        A value of the returned mapping is passed to a KPI calculation function
        if it defines an argument named as the mapping key.
        Must be implemented in a subclass.

        Args:
            date (datetime.date): The date for which the KPIs shall be calculated.

        Returns:
            dict[str, Any]: A mapping of KPI calculation function argument names
                and values.
        """
        pass

    @cache
    def _query_needs_kpi_id_data(self, needs_kpi_id: str) -> dict[str, Any]:
        """Queries the needs kpi id metadata from the corresponding needs element.

        Args:
            needs_kpi_id (int): The ID of the related needs kpi.

        Returns:
            dict[str, Any]: A mapping of all contract kpi needs elements relevant for the KPI.
        """
        # Retrieve corresponding needs kpi ids from ADO nodes
        needs_nodes_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_NEEDS}.{SILVER_TABLE_PACE_NEEDS}"
        needs_nodes_df = self.spark.read.table(needs_nodes_table)
        # The newest needs element will be taken
        needs_results = (
            needs_nodes_df.where(f"id = '{needs_kpi_id}'")
            .select("version", "created_date", "title")
            .orderBy("created_date", ascending=False)
            .limit(1)
            .collect()
        )
        needs_kpi_id_result = needs_results[0]

        return {
            "needs_kpi_id": needs_kpi_id,
            "title": needs_kpi_id_result["title"],
        }

    def update_delta_table(self, kpi_df: DataFrame) -> None:
        """Update the corresponding delta table with given KPI data.

        The needs kpi id and the date form a unique index, so rows with a
        needs kpi id - date pair not yet existing in the delta tale will be
        added while existing will be overwritten.

        Args:
            kpi_df (DataFrame): A dataframe containing one row per calculated KPI.
                Raises a ValueError if the schema does not match.
        """
        super().update_delta_table(kpi_df)

        # Set table metadata
        dtu.update_table_metadata(
            self.table_full,
            (
                "This table contains the values of contract KPIs of different domains."
                " The logic for calculating the KPIs is maintained by the respective domains."
            ),
        )

    # Class fields
    SCHEMA_ID: ClassVar[str] = "needs_kpi_id"

    SOURCE_NAME: ClassVar[str] = NotImplemented
    """The name of the data source used for calculating the KPIs."""
    OWNER: ClassVar[str] = NotImplemented
    """The name of the owner of the kpi."""
    NEEDS_KPI_ID: ClassVar[dict[str, Callable[..., float]]] = NotImplemented
    """A mapping from the needs element to KPI calculation functions."""
    PARAMETERIZATION: ClassVar[dict[str, list[Any]] | None] = None
    """A mapping from parameter names to all possible values of the parameters.

    If defined, the _load_kpi_data_parameterized must be implemented instead of
    the _load_kpi_data function."""

    def __init_subclass__(cls) -> None:
        """Check if all necessary class fields are implemented."""
        # Skip checks if sublass is still ABC
        if ABC in cls.__bases__:
            return

        if cls.SOURCE_NAME is NotImplemented:
            raise NotImplementedError("SOURCE_NAME must be set in the subclass.")
        if cls.OWNER is NotImplemented:
            raise NotImplementedError("OWNER must be set in the subclass.")
        if cls.NEEDS_KPI_ID is NotImplemented:
            raise NotImplementedError("NEEDS_KPI_ID must be set in the subclass.")

        cls.CLI_DESCRIPTION = f'Calculates "{cls.OWNER}" contract KPIs.'
        super().__init_subclass__()

    @classmethod
    def _iterate_parameter_sets(cls) -> Generator[dict[str, Any], None, None]:
        """Generates all possible sets of parameters from the parameterization.

        If no parameterization is given, returns a single empty parameter set.

        Yields:
            Generator[dict[str, Any], None, None]: A set of parameters as mapping.
        """
        if cls.PARAMETERIZATION is None:
            yield {}
            return

        for param_set_values in itertools.product(*cls.PARAMETERIZATION.values()):
            param_set = {key: param_set_values[idx] for idx, key in enumerate(cls.PARAMETERIZATION.keys())}
            yield param_set
