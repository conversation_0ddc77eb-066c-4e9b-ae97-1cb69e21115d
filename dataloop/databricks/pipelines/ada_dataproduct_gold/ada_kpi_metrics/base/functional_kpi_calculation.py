"""Functional KPI calculation base class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
import logging
import sys
from abc import ABC
from pathlib import Path
from typing import Any, ClassVar

from base.contract_kpi_calculation import ContractKpiCalculationBase
from constants.common import SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH, SILVER_TABLE_GITHUB_PR
from pyspark.sql import DataFrame
from pyspark.sql.functions import col
from pyspark.sql.types import ArrayType, DateType, DoubleType, StringType, StructField, StructType

SILVER_TABLE_DATASET = ["sipa_kpi_dataset"]  # todo: will be soon implemented: "performance_drives"]

logger = logging.getLogger(__name__)


class FunctionalContractKpiCalculationBase(ContractKpiCalculationBase, ABC):
    """Base class for functional KPI calculations of a specific domain."""

    def calculate(self, date: datetime.date) -> DataFrame:
        """Calculate all key result KPIs for a given date.

        Args:
            date (datetime.date): The date to calculate the KPIs for.

        Returns:
            DataFrame: A dataframe containing one row per calculated KPI.
        """
        logger.info(f"Calculate KPIs for owner {self.OWNER} and date {date.isoformat()}")

        result_rows: list[dict[str, Any]] = []
        for datasets in SILVER_TABLE_DATASET:
            for params in self._iterate_parameter_sets():
                logger.info(f"Calculate KPIs for parameter set {params}")
                dataset_df = self._get_dataset_data(datasets)
                params["gmdm_file_hashes"] = dataset_df["gmdm_file_hashes"]

                kpi_data = self._load_kpi_data(date, **params)
                kpi_rows = self._calculate_from_data(kpi_data)

            params_strs = [f"{k}={v}" for k, v in params.items()] if len(params) > 0 else None
            for row in kpi_rows:
                # Add additional information to row
                row["owner"] = self.OWNER
                row["updated_at"] = date
                row["source"] = self.SOURCE_NAME
                row["parameters"] = params_strs
                row["kpi_logic_file"] = Path(sys.argv[0]).name
                row["commit_sha"] = dataset_df["commit_sha"]
                row["dataset"] = datasets
                result_rows.append(row)

        # Create Spark dataframe
        return self.spark.createDataFrame(result_rows, schema=self.GOLD_SCHEMA_KPIS)

    def _get_dataset_data(self, datasets: str) -> dict[str, Any]:
        """The latest dataset will be returned with the corresponding gmdm_file_hashes.

        Returns:
            dict[str, Any]: rearranged dataset data
        """
        dataset_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH}.{datasets}"
        github_pr_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH}.{SILVER_TABLE_GITHUB_PR}"

        commits_df = self.spark.read.table(github_pr_table).select("commit_sha", "merged_date")
        dataset_df = self.spark.read.table(dataset_table).select("commit_sha", "file_hash")
        latest_commit_sha = (
            dataset_df.join(commits_df, "commit_sha", "left")
            .orderBy("merged_date", ascending=False)
            .limit(1)
            .select("commit_sha")
            .collect()
        )

        gmdm_file_hashes_list = [
            row["file_hash"]
            for row in dataset_df.filter(col("commit_sha") == latest_commit_sha[0]["commit_sha"])
            .select("file_hash")
            .collect()
        ]
        dataset_data_df = {"commit_sha": latest_commit_sha[0]["commit_sha"], "gmdm_file_hashes": gmdm_file_hashes_list}

        return dataset_data_df

    def _get_sipa_lpi_dataset(self, datasets: str) -> DataFrame:
        dataset_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH}.{datasets}"
        dataset_df = self.spark.read.table(dataset_table).select("file_hash", "commit_sha")

        return dataset_df

    GOLD_TABLE_KPIS: ClassVar[str] = "functional_contract_kpis"

    GOLD_SCHEMA_KPIS: ClassVar[StructType] = StructType(
        [
            StructField("needs_kpi_id", StringType(), True),
            StructField("title", StringType(), True),
            StructField("owner", StringType(), True),
            StructField("dataset", StringType(), True),
            StructField("commit_sha", StringType(), True),
            StructField("kpi_logic_file", StringType(), True),
            StructField("updated_at", DateType(), True),
            StructField("current_value", DoubleType(), True),
            StructField("parameters", ArrayType(StringType()), True),
        ]
    )
