"""Helper module to define ADO related constants."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import ArrayType, DateType, DoubleType, IntegerType, StringType, StructField, StructType

GOLD_DBX_SCHEMA = "ada_kpi_metrics"
SILVER_DBX_SCHEMA_PACE_METRICS = "pace_metrics"
SILVER_DBX_SCHEMA_NEEDS = "needs"
SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH = "ada_release_graph"
GOLD_DBX_SCHEMA_DRIVE_TIME_SERIES = "drive_time_series"
GOLD_TABLE_KEY_RESULT_KPIS = "key_result_kpis"
SILVER_TABLE_GITHUB_PR = "github_pullrequests"
SILVER_TABLE_PACE_NEEDS = "pace_needs"
SILVER_TABLE_ADO_NODES = "ado_nodes"
SILVER_TABLE_ADO_COMMENTS = "ado_comments"

# Schemas

GOLD_SCHEMA_KEY_RESULT_KPIS = StructType(
    [
        StructField("key_result_id", IntegerType(), True),
        StructField("title", StringType(), True),
        StructField("domain", StringType(), True),
        StructField("updated_at", DateType(), True),
        StructField("current_value", DoubleType(), True),
        StructField("target_value", DoubleType(), True),
        StructField("unit", StringType(), True),
        StructField("type", StringType(), True),
        StructField("source", StringType(), True),
        StructField("parameters", ArrayType(StringType()), True),
    ]
)
