"""Module contains constants related to Lead Time key result KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import (
    ArrayType,
    BooleanType,
    DoubleType,
    IntegerType,
    StringType,
    StructField,
    StructType,
    TimestampType,
)

# Table names

GOLD_TABLE_LEADTIME_KPI_DATA = "lead_time_kpi_data"

# Schemas

GOLD_SCHEMA_LEADTIME_KPI_DATA = StructType(
    [
        StructField("html_url", StringType(), True),
        StructField("pull_request_number", IntegerType(), True),
        StructField("created_at", TimestampType(), True),
        StructField("ready_for_review_at", TimestampType(), True),
        StructField("merged_at", TimestampType(), True),
        StructField("repository_name", StringType(), True),
        StructField("title", StringType(), True),
        StructField("level", ArrayType(StringType(), True), True),
        StructField("merge_commit_sha", StringType(), True),
        StructField("target_branch", StringType(), True),
        StructField("lead_time_ready_for_review_to_merge", TimestampType(), True),
        StructField("lead_time_open_to_merge", TimestampType(), True),
    ]
)
