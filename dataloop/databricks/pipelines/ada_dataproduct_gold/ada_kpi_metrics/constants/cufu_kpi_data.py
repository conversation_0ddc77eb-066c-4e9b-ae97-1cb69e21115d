"""Module contains constants related to ADX key result KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import (
    ArrayType,
    BooleanType,
    DoubleType,
    IntegerType,
    StringType,
    StructField,
    StructType,
    TimestampType,
)

# Table names

GOLD_TABLE_CUFU_KPI_DATA = "cufu_kpi_data"

# Schemas

GOLD_SCHEMA_CUFU_KPI_DATA = StructType(
    [
        StructField("version", StringType(), True),
        StructField("mdm_hash", StringType(), True),
        StructField("file_name", StringType(), True),
        StructField("driving_scenario", ArrayType(StringType(), True), True),
        StructField("commit_id", StringType(), True),
        StructField("release_names", ArrayType(StringType(), True), True),
        StructField("current_time", TimestampType(), True),
        StructField("is_full_drive", BooleanType(), True),
        StructField("direction", StringType(), True),
        # Data fields not in initial df, index 9:
        StructField("hwp_driven_distance_km", DoubleType(), True),
        StructField("total_driven_distance_km", DoubleType(), True),
        StructField("number_of_unplanned_driver_takeovers", IntegerType(), True),
        StructField("comfort_steering_automated_high_velocity", DoubleType(), True),
        StructField("comfort_steering_automated_mid_velocity", DoubleType(), True),
        StructField("comfort_steering_automated_low_velocity", DoubleType(), True),
        StructField("relative_longitudinal_comfort", DoubleType(), True),
        StructField("number_of_customer_relevant_tags", IntegerType(), True),
    ]
)
