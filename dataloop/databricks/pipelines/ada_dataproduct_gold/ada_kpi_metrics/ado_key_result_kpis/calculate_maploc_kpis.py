"""Calculate Mapping & Localization KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import avg, col, greatest, max, when
from pyspark.sql.types import DateType
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import KeyResultKpiCalculationBase
from constants.common import SILVER_DBX_SCHEMA_PACE_METRICS


class MapLocKpiCalculation(KeyResultKpiCalculationBase):
    """Class for Mapping & Localization KPI calculations."""

    def _load_kpi_data(self, date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Loads the data required for calculating the Mapping & Localization KPIs.

        The resulting table contains data for the 7 days before the given date.
        """
        eval_job_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.eval_job"
        batch_job_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.batch_job"
        eval_kpi_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.eval_kpi"
        workflow_run_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.github_workflow_run"
        ticket_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.ticket"

        start_date = date - datetime.timedelta(days=7)
        end_date = date - datetime.timedelta(days=1)

        eval_kpi_df = (
            self.spark.read.table(batch_job_table)
            .withColumn("execution_date", col("begin_time").cast(DateType()).alias("execution_date"))
            .where((col("execution_date") >= start_date) & (col("execution_date") <= end_date))
            .join(self.spark.read.table(eval_job_table), on="batch_job_id", how="inner")
            .join(self.spark.read.table(eval_kpi_table), on=["batch_job_id", "eval_job_id"], how="inner")
            .join(
                self.spark.read.table(workflow_run_table),
                on=(col("build_id") == col("workflow_run.id")),
                how="inner",
            )
            .where(
                (col("branch") == "main")
                & (col("workflow_run.event") == "schedule")
                & (col("workflow_run.status") == "completed")
                & col("show_in_maploc_kpi_dashboard")
            )
            .select("execution_date", "kpi", "value")
        )

        ticket_df = self.spark.read.table(ticket_table)
        latest_as_of_df = ticket_df.select(max(col("as_of")).alias("latest_as_of"))
        closed_bugs_df = ticket_df.join(
            latest_as_of_df,
            on=(latest_as_of_df.latest_as_of == ticket_df.as_of),
            how="inner",
        ).where(
            (col("closed_date").cast(DateType()) >= start_date)
            & (col("closed_date").cast(DateType()) <= end_date)
            & (col("work_item_type") == "bug")
            & (col("area_path") == "PACE\\MapLoc Domain\\MapLoc - Integration")
            & col("tags").contains("CuFu_Feedback")
        )
        return {"eval_kpi_df": eval_kpi_df, "closed_bugs_df": closed_bugs_df}

    # Class fields
    # These must be set or there will be an error when trying to create the class.

    # Value of the KPI table "source" column
    SOURCE_NAME: ClassVar[str] = "pace_metrics"
    # Value of the KPI table "domain" column
    DOMAIN_NAME: ClassVar[str] = "Mapping & Localization"
    # Logging messages from this class are be prefixed with ada_kpi_metrics/DOMAIN
    DOMAIN: ClassVar[str] = "maploc"

    # KPI definitions

    @staticmethod
    def _kpi_localization_pose_error(eval_kpi_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the absolute localization pose error.

        This is the maximum of the average longitudinal and lateral errors.
        """
        result_rows = eval_kpi_df.select(
            greatest(
                avg(when(col("kpi") == "error_x_abs_mean", col("value"))),
                avg(when(col("kpi") == "error_y_abs_mean", col("value"))),
            ).alias("value")
        ).collect()

        return result_rows[0]["value"]

    @staticmethod
    def _kpi_time_to_react_on_critical_map_bugs(closed_bugs_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the time to react on critical map bugs in hours."""
        result_rows = closed_bugs_df.select(
            avg((col("closed_date") - col("created_date")).cast("long") / 3600.0).alias("value")
        ).collect()

        return result_rows[0]["value"]

    # Set your previously defined KPI calculation functions in this mapping
    # with the ID of the corresponding ADO key result as key.
    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = {
        128084: _kpi_localization_pose_error,
        197501: _kpi_time_to_react_on_critical_map_bugs,
    }


if __name__ == "__main__":  # pragma: no cover
    MapLocKpiCalculation.cli_entrypoint()
