"""Calculate both lead time metrics for all merged pull requests in given time-frame."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from datetime import datetime, <PERSON><PERSON>ta
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F
from pyspark.sql.functions import (
    any_value,
    col,
    collect_list,
    count,
    count_if,
    isnotnull,
    like,
    lit,
    max,
    unix_timestamp,
    when,
)
from pyspark.sql.types import DateType
from rddlib import FullSchemaName, FullTableName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, quality_check, setup_databricks_logging
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from constants.common import GOLD_DBX_SCHEMA, SILVER_DBX_SCHEMA_PACE_METRICS
from constants.lead_time_kpi_data import GOLD_SCHEMA_LEADTIME_KPI_DATA, GOLD_TABLE_LEADTIME_KPI_DATA

logger = logging.getLogger(__name__)


class LeadTimeDataGoldAggregator:
    """Class for calculating lead times for pull requests."""

    spark: SparkSession
    silver_catalog: str
    gold_catalog: str
    test_mode: bool

    def __init__(
        self,
        spark: SparkSession,
        catalog_silver: str,
        catalog_gold: str,
        test_mode: bool,
    ) -> None:
        """Initializes lead time aggregator.

        Args:
            spark (SparkSession): The Spark session object.
            catalog_silver (str): The name of the silver catalog.
            catalog_gold (str): The name of the Gold catalog.
            test_mode (bool): Provide True for an end to end test run.
        """
        self.spark = spark
        self.silver_catalog = catalog_silver
        self.gold_catalog = catalog_gold
        self.test_mode = test_mode

    def aggregate_lead_time_data(self, start_date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Loads the data required for calculating the Lead Time KPIs."""

        logger.info("Aggregating Lead Time Data.")
        if start_date is not None:
            logger.info(f"Using start date {start_date}.")
        else:
            today = datetime.today().date()
            start_date = today - timedelta(days=7)
            logger.info(f"Start date unset, using last week of data since: {start_date}.")

        lead_time_relevant_events_for_merged_prs = self._get_lead_time_relevant_events_for_merged_prs(start_date)

        lead_times_per_pr = self._calculate_lead_times_per_pr(lead_time_relevant_events_for_merged_prs)

        self._run_quality_check_and_improvement(lead_times_per_pr)
        self._update_delta_table(lead_times_per_pr, "target.html_url = source.html_url")

    def _run_quality_check_and_improvement(self, df: DataFrame) -> DataFrame:
        """Ensure the lead time data is of high quality.

        This function will:
        - Check for missing values in 'lead_time_ready_for_review_to_merge' and 'lead_time_open_to_merge'
        - Deduplicate the data
        - Check for null values in all columns
        - Perform range checks on lead time columns
        """
        # Missing values check for 'lead_time_ready_for_review_to_merge' and 'lead_time_open_to_merge'
        # Count of rows for df
        total_count = df.count()

        missing_value_rows = quality_check.missing_value_detection(df, column="html_url")
        missing_value_rows.union(quality_check.missing_value_detection(df, column="lead_time_open_to_merge"))
        missing_value_rows.union(
            quality_check.missing_value_detection(df, column="lead_time_ready_for_review_to_merge")
        )

        missing_values_count = missing_value_rows.count()
        if missing_values_count == 0:
            quality_check.log_quality_check_result(True, "missing_value_detection", None)
        else:
            quality_check.log_quality_check_result(False, "missing_value_detection", missing_values_count)

        # Duplicate check
        df_deduplicated, duplicate_id_rows = quality_check.deduplicate(  # type: ignore
            df,
            subset=["html_url"],
            time_window=None,
            use_hashing=False,
            just_test=False,
        )
        count_duplicates = len(duplicate_id_rows)
        if count_duplicates == 0:
            quality_check.log_quality_check_result(True, "deduplicate", None)
        else:
            quality_check.log_quality_check_result(False, "deduplicate", f"{count_duplicates / total_count}")

        # Null value check on all columns of the dataframe
        null_violations = quality_check.null_check(df_deduplicated)

        null_violations_with_total = null_violations.withColumn(
            "total", sum(null_violations[col] for col in null_violations.columns)
        )

        if null_violations_with_total.head()["total"] == 0:
            quality_check.log_quality_check_result(True, "null_value_check", None)
        else:
            quality_check.log_quality_check_result(False, "null_value_check", f"{null_violations.show()} violations")

        return df_deduplicated

    def _get_lead_time_relevant_events_for_merged_prs(self, start_date: datetime.date) -> DataFrame:
        github_pull_request_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.github_pull_request"
        logger.info(f"Starting to obtain silver layer data from {github_pull_request_table}.")

        # Find all pull requests which were closed in since start date -> only care about the PR Repo & number
        merged_pr_numbers = (
            self.spark.read.table(github_pull_request_table)
            .where(f"CAST(pull_request.merged_at AS DATE) >= '{start_date.isoformat()}'")
            .filter(col("pull_request.merged") & col("action").isin(["closed"]))
            .select(col("pull_request.html_url"))
        )
        merged_unique_pr_htmls = set(
            [row.html_url for row in merged_pr_numbers.collect()]
        )  # A list of pull requests which were merged in the given time frame

        # Get all relevant events (open, merged, ready for review, reopened) and related to the merged pull requests
        events_of_merged_pull_request = (
            self.spark.read.table(github_pull_request_table)
            .filter(
                col("pull_request.html_url").isin(merged_unique_pr_htmls)
                & col("action").isin(["opened", "closed", "ready_for_review", "reopened"])
            )
            .select(
                col("number"),
                col("repository.name"),
                col("pull_request.html_url"),
                col("pull_request.created_at"),
                col("pull_request.updated_at"),
                col("pull_request.merged_at"),
                col("action"),
                col("pull_request.merge_commit_sha"),
                col("pull_request.title"),
                col("pull_request.labels"),
                col("pull_request.base.ref"),
            )
        )
        merged_prs_with_events = set(
            [row.html_url for row in events_of_merged_pull_request.collect()]
        )  # A list of pull requests which were merged in the given time frame

        assert len(merged_unique_pr_htmls) == len(
            merged_prs_with_events
        ), "Could not find events for all merged pull requests."

        return events_of_merged_pull_request

    def _calculate_lead_times_per_pr(self, pr_silver_data: DataFrame) -> DataFrame:
        # Silver layer data has all entriers
        logger.info("Calculating lead times per pull request using silver layer data.")

        # Add a new column for most recent open time to account for re-openings
        final_df = pr_silver_data.withColumn(
            "most_recent_open_time",
            F.when(pr_silver_data.action == "reopened", pr_silver_data.updated_at).otherwise(pr_silver_data.created_at),
        )
        # Add a new column for ready_for_review_time based on condition
        final_df = final_df.withColumn(
            "ready_for_review_time",
            F.when(final_df.action == "ready_for_review", final_df.updated_at).otherwise(
                final_df.most_recent_open_time
            ),
        )

        # Perform aggregation (summarize) by pull request html which includes repo and number
        final_df = final_df.groupBy("html_url").agg(
            F.max("number").alias("number"),
            F.max("most_recent_open_time").alias("opened_at"),
            F.max("ready_for_review_time").alias("ready_for_review_at"),
            F.max("merged_at").alias("merged_at"),
            F.max("name").alias("repository_name"),
            F.max("title").alias("title"),
            F.max("labels").alias("labels"),
            F.max("merge_commit_sha").alias("merge_commit_sha"),
            F.max("ref").alias("target_ref"),
        )

        # Extend the data with the calculated lead times - ready for review to merge
        final_df = final_df.withColumn(
            "lead_time_ready_for_review_to_merge",
            F.col("merged_at") - F.col("ready_for_review_at"),
        )

        # Extend the data with the calculated lead times - open to merge
        final_df = final_df.withColumn("lead_time_open_to_merge", F.col("merged_at") - F.col("opened_at"))

        return final_df

    def _update_delta_table(self, data: DataFrame, merge_condition: str) -> None:
        """Update the delta table with the given data."""
        table = GOLD_TABLE_LEADTIME_KPI_DATA
        logger.info(f"Updating delta table {table} with the calculated data.")
        if self.test_mode:
            table += "_test"
        table_full = FullTableName.parse(f"gold_dev.{GOLD_DBX_SCHEMA}.{table}", False)

        if dtu.table_exists(table_full):
            # Check if schema is consistent
            if dtu.table_has_schema(table_full, data.schema):
                dtu.merge(data, table_full, merge_condition)
            else:
                dtu.overwrite(data, table_full, overwrite_schema=True)
        else:
            dtu.overwrite(data, table_full)


if __name__ == "__main__":  # pragma: no cover

    parser = argparse.ArgumentParser(description="Aggregate lead time metrics for pull requests")
    parser.add_argument(
        "-m", "--test_mode", action="store_true", default=False, help="Test Mode. If set, uses test table."
    )
    parser.add_argument("-r", "--run_id", dest="run_id")
    parser.add_argument(
        "--since",
        type=lambda v: datetime.date.fromisoformat(v),
        help="Start date for which to collect merged pull requests since.",
    )
    args, unknown = parser.parse_known_args()

    run_id = args.run_id
    test_mode = args.test_mode

    spark = SparkSession.builder.getOrCreate()

    catalog_silver = get_dbx_env_catalog("silver")
    catalog_gold = get_dbx_env_catalog("gold")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_gold, GOLD_DBX_SCHEMA, False), "ada_kpi_metrics/pace_metrics", run_id=run_id
    )

    calculator = LeadTimeDataGoldAggregator(spark, catalog_silver, catalog_gold, test_mode)
    calculator.aggregate_lead_time_data(args.since)
