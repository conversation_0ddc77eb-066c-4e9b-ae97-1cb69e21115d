# This is a template for the implementation of key result KPI calculations
# of a specific project domain.
#
# Use it according to the following steps:
#   1. Copy the template to the parent 'ada_kpi_metrics' folder
#   2. Replace the xyz in the filename with the lower abbreviation of your
#      domain (e.g. cufu for Customer Function) and remove the .template extension
#   3. Replace all occurences in the template accordingly:
#       - xyz -> lower abbreviation of your domain (e.g. cufu for Customer Function)
#       - Xyz -> capital abbreviation of your domain (e.g. CuFu for Customer Function)
#       - XYZ -> full name of your domain (e.g. Customer Function)
#   4. Implement the _load_kpi_data method according to your needs
#   5. Set the remaining class fields.
#   6. Implement the KPI calculation functions as described below.
#   7. Add the KPI calculation functions to the KPI_DEFINITIONS mapping.
"""Calculate XYZ KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, sum
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import KeyResultKpiCalculationBase

class XyzKpiCalculation(KeyResultKpiCalculationBase):
    """Class for XYZ KPI calculations."""

    def _load_kpi_data(self, date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Loads the data required for calculating the XYZ KPIs."""
        # Gold and silver tables can be used as source for KPI data.
        data_1_table = f"{self.gold_catalog}.abc.abc_data_1"
        data_2_table = f"{self.silver_catalog}.abc.abc_data_2"

        start_date = date - datetime.timedelta(days=7)
        end_date = date - datetime.timedelta(days=1)
        return {
            # The KPI data may be filtered by the date or using any other fixed filter
            "data_1_df": self.spark.read.table(data_1_table).where(
                f"CAST(current_time AS DATE) >= '{start_date.isoformat()}'"
                f"AND CAST(current_time AS DATE) <= '{end_date.isoformat()}'"
                f"AND filter_col != 'invalid-value'"
            ),
            # ... or it may not be filtered at all
            "data_2_df": self.spark.table(data_2_table),
        }

    # Class fields
    # These must be set or there will be an error when trying to create the class.

    # Value of the KPI table "source" column
    SOURCE_NAME: ClassVar[str] = None
    # Value of the KPI table "domain" column
    DOMAIN_NAME: ClassVar[str] = "XYZ"
    # Logging messages from this class are be prefixed with ada_kpi_metrics/DOMAIN
    DOMAIN: ClassVar[str] = "xyz"

    # KPI definitions
    # Define your KPI calculation functions here similar to the given examples.
    # They must be static methods with an arbitrary number of arguments ending
    # with the **kwargs variable length argument.
    # The named arguments are retrieved from the corresponding key in the result
    # of the _load_kpi_data method, so make sure they are included there.
    # The function must return a float or None.
    # If the result is None or NaN, the KPI value will be discarded and not stored.
    # It is recommended that the function name starts with _kpi_ and ends with a
    # very short summary name of the KPI in snake case.
    #
    # DO NOT FORGET to add the KPI calculation functions to the KPI_DEFINITIONS
    # mapping below as they won't be calculated otherwise!

    @staticmethod
    def _kpi_sum_of_values(data_1_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the sum of all values in a column."""
        result_rows = data_1_df.agg(sum("number_column").alias("value")).collect()
        return result_rows[0]["value"]

    @staticmethod
    def _kpi_count_of_values(data_2_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the count of all occurences of a value in a column."""
        return data_2_df.where(col("column") == "count-value").count()

    @staticmethod
    def _kpi_ratio_of_values(data_1_df: DataFrame, data_2_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the ratio of the sum of all values from two dataframes."""
        sum_1 = data_1_df.agg(sum("number_column").alias("value")).collect()[0]["value"]
        sum_2 = data_2_df.agg(sum("other_column").alias("value")).collect()[0]["value"]
        return sum_1 / sum_2

    # Set your previously defined KPI calculation functions in this mapping
    # with the ID of the corresponding ADO key result as key.
    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = {
        123: _kpi_sum_of_values,
        456: _kpi_count_of_values,
        789: _kpi_ratio_of_values,
    }


if __name__ == "__main__":  # pragma: no cover
    XyzKpiCalculation.cli_entrypoint()
