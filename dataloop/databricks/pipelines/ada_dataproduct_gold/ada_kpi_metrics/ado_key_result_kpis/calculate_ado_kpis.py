"""Calculate key result KPIs based on ADO data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import datetime
import logging
from typing import ClassVar

from pyspark.sql import DataFrame, Window
from pyspark.sql.functions import col, lit, lower, row_number
from pyspark.sql.types import DoubleType, StructType
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import KpiCalculationBase
from constants.common import (
    GOLD_SCHEMA_KEY_RESULT_KPIS,
    GOLD_TABLE_KEY_RESULT_KPIS,
    SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH,
    SILVER_TABLE_ADO_COMMENTS,
    SILVER_TABLE_ADO_NODES,
)
from spark_functions import nullable

logger = logging.getLogger(__name__)

ADO_DOMAIN_KEY_RESULTS_MAP = {
    "Overarching System Development": [
        "Share of ACA 5.0 Stakeholder-Requirements (agreed)",
        "Share of ACA5.0 System-Req (Assumed)",
        "Test specification coverage of ACA 5.0 system requirements",
        "Total number of ACA 5.0 Stakeholder-Requirements",
        "Total number of ACA 5.0 System-Requirements",
    ],
    "Environment Model": [
        "Framerate",
        "Detection of dynamic objects (recompute, FE reference route)",
        "Takeover due to wrong detection of dynamic objects (estimated, FE reference route)",
        "Detection of Generic Objects (Recompute, Fe Reference Route)",
        "Detection of Lanes (Recompute, Fe Reference Route)",
        "Detection of Regulatory objects (TL, Recompute, Fe Reference Route)",
        "Take over due to wrong detection of regulatory objects (estimated, FE reference route)",
    ],
    "MapLoc": ["Localization Pose Error", "Time to react on Map bugs rated critically 1&2 [automated ingest]"],
    "Data Collection": ["Number of Data Collection Vehicles", "Amount of Data recorded (12/12 cameras used)"],
    "Data Upload": [
        "Data availability lead time CL/OL",
        "Data availability lead time Data Collection (incl. 3 days shipping to Amsterdam)",
        "Data ingest lead time",
    ],
    "Data Processing": [
        "Amount of Labeled Data (+ Planned Labeled Throughput vs. Completed)",
        "Amount of Labeled Data used in Series Networks",
        "Split Takerate of Selection",
        "Turnaround Time 2D/3D Labeling (Disclaimer: Overselection increases Turnaround Time)",
        "Turnaround Time of (Manual) Selection until LAPI Submission",
        "Turnaround Time Prio Data Labeling",
    ],
    "SW Stack Improvements": ["Recomputed data on PoL per day", "Software build time"],
    "Dora CICD": [
        "Deployment frequency CI/CD (number of integrated PR/week)",
        "Lead time for changes",
        "Change failure rate CI/CD",
        "Average Time till a failed main is fixed over the last weeks",
    ],
    # "Dora Feedback Veh to Dev": [
    #     "Lead Time from merge to feedback from test drive (estimated)",
    #     "Vehicle change failure rate",
    #     "Vehicle deployment frequency [8650] per week",
    # ],
    "Customer Release": [
        "Release period (Internal releases of ADA Stack)",
        "Deployment period to target vehicle (ADA Stack)",
        "Lead time release branch external releases (ADA Stack)",
        "AOS Core release period",
        "AOS Deploy-ment period in ALLIANCE",
    ],
    # "Test Trending": [
    #     "Release period (Internal releases of ADA Stack)",
    #     "Deployment period to target vehicle (ADA Stack)",
    #     "Lead time release branch external releases (ADA Stack)",
    #     "AOS Core release period",
    #     "AOS Deploy-ment period in ALLIANCE",
    # ],
}


class ADOKeyResultKpiCalculation(KpiCalculationBase):
    """Class for KPI calculations based on ADO data."""

    # Class fields
    GOLD_SCHEMA_KPIS: ClassVar[StructType] = GOLD_SCHEMA_KEY_RESULT_KPIS
    GOLD_TABLE_KPIS: ClassVar[str] = GOLD_TABLE_KEY_RESULT_KPIS
    SCHEMA_ID: ClassVar[str] = "key_result_id"
    DOMAIN: ClassVar[str] = "ado"
    CLI_DESCRIPTION: ClassVar[str | None] = "Calculates key result KPIs based on ADO data."

    def calculate(self, date: datetime.date) -> DataFrame:
        """Calculate the ADO KPIs for the given date."""
        result_df = self.spark.createDataFrame([], schema=self.GOLD_SCHEMA_KPIS)

        # Calculate key result KPIs for each domain
        for domain, key_result_titles in ADO_DOMAIN_KEY_RESULTS_MAP.items():
            logger.info(f"Calculate KPIs for {domain} domain.")
            domain_df = self._calculate_domain_kpis(key_result_titles, date)

            # Add missing columns
            domain_df = domain_df.withColumns(
                {
                    "domain": nullable(lit(domain)),
                    "source": nullable(lit("ADO")),
                    "parameters": nullable(lit(None)),
                }
            ).select(*self.GOLD_SCHEMA_KPIS.fieldNames())

            result_df = result_df.union(domain_df)

        return result_df

    def _calculate_domain_kpis(self, titles: list[str], date: datetime.date) -> DataFrame:
        """Calculate KPIs for the key results of a domain with the given titles."""
        # Convert the titles list to lowercase for comparison
        titles_lower = [title.lower() for title in titles]

        # Construct silver table names
        table_suffix = "_test" if self.test_mode else ""
        table_silver_nodes = (
            f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH}.{SILVER_TABLE_ADO_NODES}{table_suffix}"
        )
        table_silver_comments = (
            f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_ADA_RELEASE_GRAPH}.{SILVER_TABLE_ADO_COMMENTS}{table_suffix}"
        )

        # Gather key result comments
        comments_df = (
            self.spark.table(table_silver_comments)
            .where((col("updated_at") <= date) & col("kr_value").isNotNull())
            .select("id", "kr_value", "updated_at")
        )
        # Determine the latest comments for each node
        latest_comments_df = comments_df.withColumn(
            "row_number", row_number().over(Window.partitionBy("id").orderBy(col("updated_at").desc()))
        ).where("row_number = 1")

        # Get the result dataframes by joining the nodes with latest comments
        # and selecting the columns of interest
        nodes_df = self.spark.table(table_silver_nodes)
        result_df = (
            nodes_df.join(latest_comments_df, on="id", how="inner")
            .where(lower(col("title")).isin(titles_lower))
            .select(
                col("id").alias("key_result_id"),
                "title",
                "updated_at",
                col("kr_value").cast(DoubleType()).alias("current_value"),
                col("key_result_target_value").alias("target_value"),
                col("key_result_unit").alias("unit"),
                col("key_result_type").alias("type"),
            )
        )

        return result_df


if __name__ == "__main__":
    ADOKeyResultKpiCalculation.cli_entrypoint()
