"""Calculate Data Selection and Preparation KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, count, count_distinct, datediff, max, min, when
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import KeyResultKpiCalculationBase


class DspKpiCalculation(KeyResultKpiCalculationBase):
    """Class for Data Selection and Preparation KPI calculations."""

    def _load_kpi_data(self, date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Loads the data required for calculating the Data Selection and Preparation KPIs."""
        return {
            "date": date,
            "lapi_df": self.spark.read.table(f"{self.silver_catalog}.lapi.batches"),
            "traceability_df": self.spark.read.table(f"{self.silver_catalog}.dsp_traceability.e2e_traceability_mev"),
            "mev_df": self.spark.read.table(f"{self.silver_catalog}.event_system.measurement_events_v2"),
            # Table doesn't exist on QA
            "training_df": self.spark.read.table(
                f"{self.silver_catalog.removesuffix('_qa')}.azureml.training_datasets_split_latest_v2"
            ),
        }

    # Class fields

    SOURCE_NAME: ClassVar[str] = "databricks"
    DOMAIN_NAME: ClassVar[str] = "Data Selection and Preparation"
    DOMAIN: ClassVar[str] = "dsp"

    # KPI definitions

    @staticmethod
    def _kpi_turnaround_time_prio_data_labeling(
        date: datetime.date, lapi_df: DataFrame, traceability_df: DataFrame, **kwargs: Any
    ) -> float:
        """Timespan between LAPI "To be labeled" until "Completed" State (95% percentile) for prio data."""
        start_date = date - datetime.timedelta(days=7)
        end_date = date - datetime.timedelta(days=1)

        prio_df = traceability_df.where(
            f"CAST(requested_datetime AS DATE) >= '{start_date.isoformat()}'"
            f"AND CAST(requested_datetime AS DATE) <= '{end_date.isoformat()}'"
            f"AND tags LIKE '%priority%'"
            f"AND tags NOT LIKE '%low_priority%'"
        ).withColumn("turnaround_time", col("updated_datetime") - col("requested_datetime"))

        lapi_df = lapi_df.where(
            f"CAST(batch_created_at AS DATE) >= '{start_date.isoformat()}'"
            f"AND CAST(batch_created_at AS DATE) <= '{end_date.isoformat()}'"
            f"AND batch_status = 'Completed'"
        )

        filtered_df = prio_df.join(lapi_df, prio_df.lapi_batch_id == lapi_df.batch_id, "inner").withColumn(
            "turnaround_time", col("batch_updated_at") - col("batch_created_at")
        )

        if filtered_df.count() > 0:
            return filtered_df.selectExpr("percentile_approx(turnaround_time, 0.95)").collect()[0][0].days
        else:
            return 0

    @staticmethod
    def _kpi_amount_of_labelled_data(lapi_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the number of batches that have completed labelling in LAPI."""
        return round(
            lapi_df.where("batch_status = 'Completed'").selectExpr("sum(batch_frames)").collect()[0][0] / 1e6,
            2,
        )

    @staticmethod
    def _kpi_turnaround_time_of_selection_to_lapi_submission(
        date: datetime.date, mev_df: DataFrame, **kwargs: Any
    ) -> float:
        """Timespan between Selection via Selection API until submission to LAPI (95% percentile)."""
        start_date = date - datetime.timedelta(days=7)
        end_date = date - datetime.timedelta(days=1)

        filtered_mev_df = mev_df.where(
            "activity = 'ACTIVITY_LABELING_REQUEST_PREPARATION'"
            f"AND CAST(event_time AS DATE) >= '{start_date.isoformat()}'"
            f"AND CAST(event_time AS DATE) <= '{end_date.isoformat()}'"
        )

        completed_records_df = filtered_mev_df.where(
            "process = 'LAPI_SUBMITTER' AND output_artifact_uri LIKE 'lapi%'"
        ).select("input_artifact_uri")

        processed_df = filtered_mev_df.drop("input_artifact_uri").join(
            completed_records_df,
            filtered_mev_df.output_artifact_uri == completed_records_df.input_artifact_uri,
            "inner",
        )
        processed_df = (
            processed_df.withColumn(
                "grouping_column",
                when(col("process") == "DSP_SELECTION_API", col("output_artifact_uri")).otherwise(
                    col("input_artifact_uri")
                ),
            )
            .groupBy("grouping_column")
            .agg(
                count("*").alias("count"),
                min("event_time").alias("min_event_time"),
                max("event_time").alias("max_event_time"),
            )
            .withColumn("turnaround_time", datediff(col("max_event_time"), col("min_event_time")))
            .filter(col("count") > 1)
        )

        return processed_df.selectExpr("percentile_approx(turnaround_time, 0.95)").collect()[0][0]

    @staticmethod
    def _kpi_amount_of_labelled_data_in_series_network(training_df: DataFrame, **kwargs: Any) -> float:
        """Amount of labeled data used in series networks."""
        filtered_df = training_df.filter(
            (col("dataset_name") == "PaceGeneralYUV") | (col("dataset_name") == "TopView_MultitaskYUV")
        )
        filtered_df = filtered_df.groupBy("task").agg(count_distinct("image_id").alias("distinct_image_count"))
        return round(filtered_df.selectExpr("sum(distinct_image_count)").collect()[0][0] / 1e6, 2)

    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = {
        128055: _kpi_amount_of_labelled_data,
        128039: _kpi_turnaround_time_prio_data_labeling,
        220711: _kpi_amount_of_labelled_data_in_series_network,
        128038: _kpi_turnaround_time_of_selection_to_lapi_submission,
    }


if __name__ == "__main__":  # pragma: no cover
    DspKpiCalculation.cli_entrypoint()
