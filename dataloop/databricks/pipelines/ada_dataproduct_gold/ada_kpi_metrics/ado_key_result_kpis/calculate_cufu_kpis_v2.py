"""Calculate Customer Function KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, count, date_trunc, sum
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import KeyResultKpiCalculationBase

DEMO_COMMITS = [
    "26d06881f62fbd9ee58c29ec958629efc9276f94",
]


UNPLANNED_TAKEOVER_EVENTS = [
    "brake_pedal_pressed",
    "steering_wheel_driver_torque_applied",
    "brake_pedal_pressed_and_steering_wheel_driver_torque_applied",
    "accelerator_pressed_and_steering_wheel_driver_torque_applied",
    "accelerator_pressed",
    "no_active_trajectory",
    "planning_distance",
    # "deactivate_pressed",
]


class CuFuKpiCalculation(KeyResultKpiCalculationBase):
    """Class for Customer Function KPI calculations."""

    def _load_kpi_data(  # type: ignore[override]
        self,
        date: datetime.date,
        route_category: str,
        **kwargs: Any,
    ) -> dict[str, Any]:
        """Loads the data required for calculating the Customer Function KPIs.

        The resulting table contains data for the 7 days before the given date.
        """
        tsi_table = "silver.drive_time_series.time_series_index"
        disengagements_table = "silver.drive_time_series.disengagements"
        tags_table = "gold.drive_time_series.content_tags_with_gps_position"

        # Use drives from previous 7 days
        start_date = date - datetime.timedelta(days=7)
        end_date = date - datetime.timedelta(days=1)
        time_filter = (
            f"CAST(recorded_at AS DATE) >= '{start_date.isoformat()}'"
            f" AND CAST(recorded_at AS DATE) <= '{end_date.isoformat()}'"
        )
        # # Determine correct release name filter
        # release_filter: str
        # match drive_category:
        #     case "vehicle-ready":
        #         release_filter = "exists(release_names, x -> x LIKE 'automated-vehicle-ready-%')"
        #     case "vehicle-performance":
        #         release_filter = "exists(release_names, x -> x LIKE 'automated-vehicle-performance-%')"
        #     case "demo":
        #         commits_str = ", ".join(f"'{commit}'" for commit in DEMO_COMMITS)
        #         release_filter = f"array_contains(array({commits_str}), commit_id)"

        tsi_df = self.spark.read.table(tsi_table).where(f"route_category = '{route_category}' AND {time_filter}")
        driven_dist_df = tsi_df.select(
            sum("absolute_velocity").alias("driven_distance_m"),
            (col("driven_distance_m") / 1000).alias("driven_distance_km"),
        )
        dis_df = (
            self.spark.read.table(disengagements_table)
            .where(time_filter)
            .withColumn("emitted_at", date_trunc("second", "emitted_at"))
        )
        tags_df = (
            self.spark.read.table(tags_table)
            .where(time_filter)
            .withColumn("emitted_at", date_trunc("second", "started_at"))
        )
        return {"tsi_df": tsi_df, "driven_dist_df": driven_dist_df, "dis_df": dis_df, "tags_df": tags_df}

    # Class fields

    SOURCE_NAME: ClassVar[str] = "pace_metrics"
    DOMAIN_NAME: ClassVar[str] = "Customer Function"
    DOMAIN: ClassVar[str] = "cufu"
    PARAMETERIZATION = {
        # "drive_category": ["vehicle-ready", "vehicle-performance", "demo"],
        "route_category": ["highway", "rural", "urban"],
    }

    # KPI definitions

    @staticmethod
    def _kpi_assisted_driving_coverage(tsi_df: DataFrame, driven_dist_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the assisted driving coverage.

        This is done by computing the ratio of HWP driven distance to the total driven distance.
        """
        result_rows = (
            (tsi_df.where("aca_state = 'active'").select(sum("absolute_velocity").alias("assisted_driven_distance_m")))
            .join(driven_dist_df, how="full")
            .selectExpr("(assisted_driven_distance_m / driven_distance_m * 100) AS value")
            .collect()
        )

        return result_rows[0]["value"]

    @staticmethod
    def _kpi_number_of_unplanned_driver_takeovers_per_100km(
        tsi_df: DataFrame, driven_dist_df: DataFrame, dis_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate the number of unplanned driver takeovers per 100km."""
        dis_tsi_df = tsi_df.join(
            dis_df.select("vin", "emitted_at", "deactivation_reason"),
            on=["vin", "emitted_at"],
            how="inner",
        )

        result_rows = (
            dis_tsi_df.where(col("deactivation_reason").isin(UNPLANNED_TAKEOVER_EVENTS))
            .select(count("*").alias("unplanned_takeovers"))
            .join(driven_dist_df, how="full")
            .selectExpr("(unplanned_takeovers / driven_distance_km * 100) AS value")
            .collect()
        )

        return result_rows[0]["value"]

    # @staticmethod
    # def _kpi_longitudinal_comfort(kpi_data_df: DataFrame, **kwargs: Any) -> float:
    #     """Calculate the longitudinal comfort."""
    #     result_rows = kpi_data_df.agg(
    #         weighted_avg(col("relative_longitudinal_comfort"), col("total_driven_distance_km")).alias("value")
    #     ).collect()

    #     return result_rows[0]["value"]

    # @staticmethod
    # def _kpi_steering_nervosity(kpi_data_df: DataFrame, **kwargs: Any) -> float:
    #     """Calculate the steering nervosity compared to a human driver."""
    #     result_rows = kpi_data_df.agg(
    #         (
    #             (
    #                 weighted_avg(col("comfort_steering_automated_high_velocity"), col("total_driven_distance_km"))
    #                 + weighted_avg(col("comfort_steering_automated_mid_velocity"), col("total_driven_distance_km"))
    #                 + weighted_avg(col("comfort_steering_automated_low_velocity"), col("total_driven_distance_km"))
    #             )
    #             / 3.0
    #         ).alias("value")
    #     ).collect()

    #     return result_rows[0]["value"]

    @staticmethod
    def _kpi_number_of_customer_relevant_tags_per_100km(
        tsi_df: DataFrame, driven_dist_df: DataFrame, tags_df: DataFrame, **kwargs: Any
    ) -> float:
        """Calculate the number of customer relevant tags per 100km."""
        tags_tsi_df = tsi_df.join(
            tags_df.select("vin", "emitted_at", "group_name"), on=["vin", "emitted_at"], how="inner"
        )

        result_rows = (
            tags_tsi_df.where(col("group_name") == "note_event")
            .select(count("*").alias("number_relevant_tags"))
            .join(driven_dist_df, how="full")
            .selectExpr("(number_relevant_tags / driven_distance_km * 100) AS value")
            .collect()
        )
        return result_rows[0]["value"]

    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = {
        145659: _kpi_assisted_driving_coverage,
        145661: _kpi_number_of_unplanned_driver_takeovers_per_100km,
        # 269873: _kpi_longitudinal_comfort,
        # 145666: _kpi_steering_nervosity,
        281726: _kpi_number_of_customer_relevant_tags_per_100km,
    }


if __name__ == "__main__":  # pragma: no cover
    CuFuKpiCalculation.cli_entrypoint()
