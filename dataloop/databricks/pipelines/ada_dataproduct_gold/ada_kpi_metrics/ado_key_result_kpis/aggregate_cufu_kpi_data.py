"""Aggregate data used for calculating Customer Function KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import datetime
import logging
from typing import Callable, ClassVar

from dbxlib.pyspark import compare_struct_types
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import any_value, col, collect_list, count, count_if, isnotnull, like, lit, max, when
from rddlib import FullSchemaName, FullTableName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, quality_check, setup_databricks_logging
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from constants.common import GOLD_DBX_SCHEMA, SILVER_DBX_SCHEMA_PACE_METRICS
from constants.cufu_kpi_data import GOLD_SCHEMA_CUFU_KPI_DATA, GOLD_TABLE_CUFU_KPI_DATA
from spark_functions import array_nullable, nullable

logger = logging.getLogger(__name__)


class CuFuKpiDataGoldAggregator:
    """Class that aggregates data used for calculating Customer Function KPIs from pace_metrics silver data."""

    spark: SparkSession
    silver_catalog: str
    gold_catalog: str
    test_mode: bool

    def __init__(
        self,
        spark: SparkSession,
        catalog_silver: str,
        catalog_gold: str,
        test_mode: bool,
    ) -> None:
        """Initializes key result KPI points Gold layer calculator.

        Args:
            spark (SparkSession): The Spark session object.
            catalog_silver (str): The name of the silver catalog.
            catalog_gold (str): The name of the Gold catalog.
            test_mode (bool): Provide True for an end to end test run.
        """
        self.spark = spark
        self.silver_catalog = catalog_silver
        self.gold_catalog = catalog_gold
        self.test_mode = test_mode

    def aggregate_kpi_data(self, start_date: datetime.date | None) -> None:
        """Aggregate the data needed to calculate all Customer Function KPIs and updating the delta table."""
        logger.info("Aggregating data for Customer Function KPIs.")
        if start_date is not None:
            logger.info(f"Using start date {start_date}.")

        result_df = self._get_initial_df(start_date)
        for silver_tables, kpi_data_func in self.KPI_DATA_FUNCTIONS:
            # Load required silver dataframe
            silver_dfs = [self._get_silver_data(silver_tables, start_date)]
            # Aggregate KPI data
            kpi_data_df: DataFrame = kpi_data_func(self, *silver_dfs)
            # Add KPI data to result dataframe
            result_df = result_df.join(kpi_data_df, on="mdm_hash", how="left")

        # Run the data quality check
        self._run_quality_check(result_df, column_to_check="commit_id")

        # Make sure the schema matches
        # TODO: Figure out why result_df contains rows with empty mdm_hash
        result_df = result_df.where(isnotnull("mdm_hash"))
        result_df = result_df.select(*GOLD_SCHEMA_CUFU_KPI_DATA.fieldNames())
        assert compare_struct_types(
            result_df.schema, GOLD_SCHEMA_CUFU_KPI_DATA
        ), "The schema of the resulting dataframe is incorrect."
        # Update delta table
        self._update_delta_table(result_df, "target.mdm_hash = source.mdm_hash")

    def _run_quality_check(self, df: DataFrame, column_to_check: str) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        total_count = df.count()

        missing_value_rows = quality_check.missing_value_detection(df, column=column_to_check)
        missing_values_count = missing_value_rows.count()
        if missing_values_count == 0:
            quality_check.log_quality_check_result(True, "missing_value_detection", None)
        else:
            quality_check.log_quality_check_result(False, "missing_value_detection", missing_values_count)

        # Duplicate check
        _, duplicate_id_rows = quality_check.deduplicate(  # type: ignore
            df,
            subset=[column_to_check],
            time_window=None,
            use_hashing=False,
            just_test=False,
        )
        count_duplicates = len(duplicate_id_rows)
        if count_duplicates == 0:
            quality_check.log_quality_check_result(True, "deduplicate", None)
        else:
            quality_check.log_quality_check_result(False, "deduplicate", f"{count_duplicates / total_count}")

        # Value range check for critical fields (Example: 'hwp_driven_distance_km', 'total_driven_distance_km')
        hwp_driven_distance_violations = quality_check.value_range_check(
            df, "hwp_driven_distance_km", min_val=0, max_val=1000
        )
        if hwp_driven_distance_violations == 0:
            quality_check.log_quality_check_result(True, "value_range_check_hwp_driven_distance_km", None)
        else:
            quality_check.log_quality_check_result(
                False, "value_range_check_hwp_driven_distance_km", f"{hwp_driven_distance_violations} violations"
            )

        total_driven_distance_violations = quality_check.value_range_check(
            df, "total_driven_distance_km", min_val=0, max_val=1000
        )
        if total_driven_distance_violations == 0:
            quality_check.log_quality_check_result(True, "value_range_check_total_driven_distance_km", None)
        else:
            quality_check.log_quality_check_result(
                False, "value_range_check_total_driven_distance_km", f"{total_driven_distance_violations} violations"
            )

        # Date range check for 'current_time'
        date_range_violations = quality_check.datetime_consistency_check(df, "current_time", "yyyy-MM-dd")
        if date_range_violations.count() == 0:
            quality_check.log_quality_check_result(True, "date_range_check", None)
        else:
            quality_check.log_quality_check_result(
                False, "date_range_check", f"{date_range_violations.count()} violations"
            )

        # Null value check on critical fields
        null_violations = quality_check.null_check(df)
        if null_violations.count() == 0:
            quality_check.log_quality_check_result(True, "null_value_check", None)
        else:
            quality_check.log_quality_check_result(False, "null_value_check", f"{null_violations.count()} violations")

        # Consistency check between related fields
        consistency_violations = quality_check.consistency_check(
            df, "hwp_driven_distance_km", "total_driven_distance_km"
        )
        if consistency_violations == 0:
            quality_check.log_quality_check_result(True, "consistency_check", None)
        else:
            quality_check.log_quality_check_result(False, "consistency_check", f"{consistency_violations} violations")

    def _get_initial_df(self, start_date: datetime.date | None) -> DataFrame:
        """Returns the initial dataframe that is built upon.

        It is based on the 'kpi_location_of_the_drive' table aggregated by mdm_hash.
        """
        df = self._get_silver_data("kpi_location_of_the_drive", start_date)

        # Query Github tags and group them by commit hash
        releases_df = (
            self.spark.read.table(f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.github_release")
            .groupBy("release.tag_name")
            .agg(max("release.name").alias("release_name"), any_value("release.target_commitish").alias("commit_id"))
            .groupBy("commit_id")
            .agg(array_nullable(collect_list("release_name")).alias("release_names"))
            .select("commit_id", "release_names")
            .where(col("commit_id").isNotNull())
        )

        # Get highest versions for each mdm_hash
        max_version_df = df.groupBy("mdm_hash").agg(max("version").alias("max_version"))
        # Join to get the rows with the highest version
        df = (
            df.alias("df")
            .join(
                max_version_df.alias("max_version_df"),
                on=[
                    col("df.mdm_hash") == col("max_version_df.mdm_hash"),
                    col("df.version") == col("max_version_df.max_version"),
                ],
                how="inner",
            )
            .select(*[f"df.{name}" for name in df.columns])
        )

        # There are still duplicates in the data
        # Some occur because driving_scenario arrays have different sorting
        # distinct() does not help in this case
        df = df.groupBy("mdm_hash").agg(
            any_value("version").alias("version"),
            any_value("file_name").alias("file_name"),
            any_value("driving_scenario").alias("driving_scenario"),
            any_value("commit_id").alias("commit_id"),
            any_value("current_time").alias("current_time"),
            (any_value("Full_drive") == 1.0).alias("is_full_drive"),
            nullable(when(any_value("Direction_As_Reference") == 1.0, "opposite").otherwise("same")).alias("direction"),
        )

        return df.join(releases_df, on="commit_id", how="left").select(GOLD_SCHEMA_CUFU_KPI_DATA.fieldNames()[:9])

    def _get_silver_data(self, table_name: str, start_date: datetime.date | None) -> DataFrame:
        """Get data from silver layer optionally starting from the given date."""
        # Ensure configuration is using appropriate parts
        silver_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.{table_name}"
        if self.test_mode:
            silver_table += "_test"

        silver_df = self.spark.read.table(silver_table)
        if start_date is not None:
            silver_df = silver_df.where(f"CAST(current_time AS DATE) >= '{start_date.isoformat()}'")

        return silver_df

    def _update_delta_table(self, data: DataFrame, merge_condition: str) -> None:
        """Update the delta table with the given data."""
        table = GOLD_TABLE_CUFU_KPI_DATA
        if self.test_mode:
            table += "_test"
        table_full = FullTableName.parse(f"{self.gold_catalog}.{GOLD_DBX_SCHEMA}.{table}", False)

        if dtu.table_exists(table_full):
            # Check if schema is consistent
            if dtu.table_has_schema(table_full, data.schema):
                dtu.merge(data, table_full, merge_condition)
            else:
                logger.info(f"Schema of table '{table_full}' changed. The table will be overwritten.")
                dtu.overwrite(data, table_full, overwrite_schema=True)
        else:
            logger.info(f"Creating a new delta table '{table_full}'.")
            dtu.overwrite(data, table_full)

        # Set table metadata
        dtu.update_table_metadata(
            table_full,
            (
                "This table will be deprecated in favour of the time series index!\n\n"
                "This table contains aggregated data for calculating the Customer Function KPIs."
            ),
        )

    # KPI Data Definitions...

    def _data_assisted_driving_coverage(self, silver_df: DataFrame) -> DataFrame:
        """Aggregate the data required to calculate the assisted driving coverage."""
        return silver_df.groupBy("mdm_hash").agg(
            (max("hwp_driven_distance_meters") / 1000.0).alias("hwp_driven_distance_km"),
            (max("total_driven_distance_meters") / 1000.0).alias("total_driven_distance_km"),
        )

    def _data_number_of_unplanned_driver_takeovers(self, silver_df: DataFrame) -> DataFrame:
        """Determine the number of unplanned driver takeovers by summing up the respective events."""
        return (
            silver_df.where(like("metric", lit("0/_%"), escapeChar=lit("/")))
            .groupBy("mdm_hash")
            .agg(
                count_if(
                    col("deactivation_reason").isin(
                        [
                            "BRAKE_PEDAL_PRESSED",
                            "STEERING_WHEEL_DRIVER_TORQUE_APPLIED",
                            "BRAKE_PEDAL_PRESSED_AND_STEERING_WHEEL_DRIVER_TORQUE_APPLIED",
                            "ACCELERATOR_PRESSED_AND_STEERING_WHEEL_DRIVER_TORQUE_APPLIED",
                            "ACCELERATOR_PRESSED",
                            "NO_ACTIVE_TRAJECTORY",
                            "PLANNING_DISTANCE",
                            "DEACTIVATE_PRESSED",
                        ]
                    )
                    & (
                        col("deactivated_function").contains("handson")
                        | col("deactivated_function").contains("handsfree")
                    )
                )
                .cast("int")
                .alias("number_of_unplanned_driver_takeovers")
            )
        )

    def _data_longitudinal_comfort(self, silver_df: DataFrame) -> DataFrame:
        return silver_df.groupBy("mdm_hash").agg(
            (100.0 * max("longitudinal_comfort") / 1.5005).alias("relative_longitudinal_comfort")
        )

    def _data_steering_nervosity(self, silver_df: DataFrame) -> DataFrame:
        return silver_df.groupBy("mdm_hash").agg(
            (100.0 * max("comfort_steering_automated_high_velocity") / 0.2127).alias(
                "comfort_steering_automated_high_velocity"
            ),
            (100.0 * max("comfort_steering_automated_mid_velocity") / 0.7237).alias(
                "comfort_steering_automated_mid_velocity"
            ),
            (100.0 * max("comfort_steering_automated_low_velocity") / 1.2880).alias(
                "comfort_steering_automated_low_velocity"
            ),
        )

    def _data_number_of_customer_relevant_tags(self, silver_df: DataFrame) -> DataFrame:
        """Determine the number of customer-relevant tags by counting respective events."""
        # Count customer relevant tags per file
        return silver_df.groupBy("mdm_hash").agg(count("name").cast("int").alias("number_of_customer_relevant_tags"))

    KPI_DATA_FUNCTIONS: ClassVar[list[tuple[str, Callable[["CuFuKpiDataGoldAggregator", DataFrame], DataFrame]]]] = [
        ("kpi_piloted_distance_time", _data_assisted_driving_coverage),
        ("kpi_safety_driver_takeovers", _data_number_of_unplanned_driver_takeovers),
        ("kpi_aggregated_results", _data_longitudinal_comfort),
        ("kpi_aggregated_results", _data_steering_nervosity),
        ("kpi_content_tags", _data_number_of_customer_relevant_tags),
    ]


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Transform ado workitems")
    parser.add_argument(
        "-m", "--test_mode", action="store_true", default=False, help="Test Mode. If set, uses test table."
    )
    parser.add_argument("-r", "--run_id", dest="run_id")
    parser.add_argument(
        "--since", type=lambda v: datetime.date.fromisoformat(v), help="Start date for which to calculate the KPIs."
    )
    args, unknown = parser.parse_known_args()

    run_id = args.run_id
    test_mode = args.test_mode

    spark = SparkSession.builder.getOrCreate()

    catalog_silver = get_dbx_env_catalog("silver")
    catalog_gold = get_dbx_env_catalog("gold")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_gold, GOLD_DBX_SCHEMA, False), "ada_kpi_metrics/pace_metrics", run_id=run_id
    )

    calculator = CuFuKpiDataGoldAggregator(spark, catalog_silver, catalog_gold, test_mode)
    calculator.aggregate_kpi_data(args.since)
