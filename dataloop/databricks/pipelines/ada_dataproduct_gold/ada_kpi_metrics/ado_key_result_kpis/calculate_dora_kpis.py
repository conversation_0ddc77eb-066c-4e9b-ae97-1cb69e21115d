"""Calculate DevOps Research & Assessment KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import any_value, array_max, col, count, filter, max
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import KeyResultKpiCalculationBase
from constants.common import GOLD_DBX_SCHEMA, SILVER_DBX_SCHEMA_PACE_METRICS
from constants.cufu_kpi_data import GOLD_TABLE_CUFU_KPI_DATA


class DoraKpiCalculation(KeyResultKpiCalculationBase):
    """Class for DevOps Research & Assessment KPI calculations."""

    def _load_kpi_data(self, date: datetime.date, **kwargs: Any) -> dict[str, Any]:
        """Loads the data required for calculating the DevOps Research & Assessment KPIs.

        The resulting table contaings data for the 7 days before the given date.
        """
        start_date = date - datetime.timedelta(days=7)
        end_date = date - datetime.timedelta(days=1)

        cufu_kpi_table = f"{self.gold_catalog}.{GOLD_DBX_SCHEMA}.{GOLD_TABLE_CUFU_KPI_DATA}"
        drives_df = (
            self.spark.read.table(cufu_kpi_table)
            .where(
                f"CAST(current_time AS DATE) >= '{start_date.isoformat()}'"
                f" AND CAST(current_time AS DATE) <= '{end_date.isoformat()}'"
                " AND hwp_driven_distance_km > 0"
            )
            .select(
                "commit_id",
                "file_name",
                # Determine longest vehicle ready release name
                array_max(
                    filter(
                        "release_names",
                        lambda x: x.like("automated-vehicle-ready-%"),
                    )
                ).alias("release_name"),
            )
        )

        releases_table = f"{self.silver_catalog}.{SILVER_DBX_SCHEMA_PACE_METRICS}.github_release"
        vr_releases_df = (
            self.spark.read.table(releases_table)
            .where(
                f"CAST(release.created_at AS DATE) >= '{start_date.isoformat()}'"
                f" AND CAST(release.created_at AS DATE) <= '{end_date.isoformat()}'"
                " AND release.name LIKE 'automated-vehicle-ready-%'"
            )
            # Group by tag to get latest release name
            .groupBy("release.tag_name")
            .agg(
                any_value("release.target_commitish").alias("commit_id"),
                max("release.name").alias("release_name"),
            )
            # Group by commit to get longest name of all releases on that commit
            .groupBy("commit_id")
            .agg(max("release_name").alias("release_name"))
            .select("commit_id", "release_name")
            .where(col("commit_id").isNotNull())
        )

        return {"drives_df": drives_df, "vr_releases_df": vr_releases_df}

    # Class fields
    # These must be set or there will be an error when trying to create the class.

    # Value of the KPI table "source" column
    SOURCE_NAME: ClassVar[str] = "pace_metrics"
    # Value of the KPI table "domain" column
    DOMAIN_NAME: ClassVar[str] = "DevOps Research & Assessment"
    # Logging messages from this class are be prefixed with ada_kpi_metrics/DOMAIN
    DOMAIN: ClassVar[str] = "dora"

    # KPI definitions

    @staticmethod
    def _kpi_vehicle_ready_versions_driven(drives_df: DataFrame, vr_releases_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the sum of vehicle-ready versions driven at least once."""
        return (
            drives_df.join(vr_releases_df, on="commit_id", how="inner")
            .select(vr_releases_df.release_name)
            .distinct()
            .count()
        )

    @staticmethod
    def _kpi_vehicle_ready_deployment_percentage(drives_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the ratio of vehicle-ready drives to all drives."""
        result_rows = (
            drives_df.where(col("release_name").isNotNull())
            .select(count("*").alias("vr_drives"))
            .join(drives_df.select(count("*").alias("total_drives")), how="cross")
            .select((col("vr_drives") / col("total_drives") * 100).alias("value"))
            .collect()
        )

        return result_rows[0]["value"]

    @staticmethod
    def _kpi_fn_rate_offline_testing(drives_df: DataFrame, vr_releases_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the FN rate of offline tests.

        This is the ratio of vehicle-ready versions driven without a recording divided
        to vehicle-ready versions driven at least once.
        """
        result_rows = (
            (
                # Determine number of driven VR releases wo recording
                vr_releases_df.join(drives_df, on="commit_id", how="left")
                .select("commit_id", vr_releases_df.release_name, "file_name")
                .where(col("file_name").isNull())
                .where(
                    col("release_name").contains("failed")
                    | col("release_name").contains("passed")
                    | col("release_name").contains("verified")
                )
                .select("release_name")
                .distinct()
                .select(count("*").alias("vr_driven_wo_recording"))
            )
            .join(
                # Determine number of driven VR releases
                drives_df.join(vr_releases_df, on="commit_id", how="inner")
                .select(vr_releases_df.release_name)
                .distinct()
                .select(count("*").alias("vr_driven")),
                how="cross",
            )
            .select((col("vr_driven_wo_recording") / col("vr_driven") * 100).alias("value"))
        ).collect()

        return result_rows[0]["value"]

    # Set your previously defined KPI calculation functions in this mapping
    # with the ID of the corresponding ADO key result as key.
    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = {
        128094: _kpi_vehicle_ready_versions_driven,
        128083: _kpi_vehicle_ready_deployment_percentage,
        128086: _kpi_fn_rate_offline_testing,
    }


if __name__ == "__main__":  # pragma: no cover
    DoraKpiCalculation.cli_entrypoint()
