"""Calculate Customer Function KPIs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any, Callable, ClassVar

from pyspark.sql import DataFrame
from pyspark.sql.functions import col
from rddlib.utils import add_bundle_dir_to_path

if __name__ == "__main__":
    add_bundle_dir_to_path()

from base import KeyResultKpiCalculationBase
from constants.common import GOLD_DBX_SCHEMA
from constants.cufu_kpi_data import GOLD_TABLE_CUFU_KPI_DATA
from spark_functions import weighted_avg

DEMO_COMMITS = [
    "26d06881f62fbd9ee58c29ec958629efc9276f94",
]


class CuFuKpiCalculation(KeyResultKpiCalculationBase):
    """Class for Customer Function KPI calculations."""

    def _load_kpi_data(  # type: ignore[override]
        self,
        date: datetime.date,
        drive_category: str,
        **kwargs: Any,
    ) -> dict[str, Any]:
        """Loads the data required for calculating the Customer Function KPIs.

        The resulting table contains data for the 7 days before the given date.
        """
        kpi_data_table = f"{self.gold_catalog}.{GOLD_DBX_SCHEMA}.{GOLD_TABLE_CUFU_KPI_DATA}"

        # Use drives from previous 7 days
        start_date = date - datetime.timedelta(days=7)
        end_date = date - datetime.timedelta(days=1)
        # Determine correct release name filter
        release_filter: str
        match drive_category:
            case "vehicle-ready":
                release_filter = "exists(release_names, x -> x LIKE 'automated-vehicle-ready-%')"
            case "vehicle-performance":
                release_filter = "exists(release_names, x -> x LIKE 'automated-vehicle-performance-%')"
            case "demo":
                commits_str = ", ".join(f"'{commit}'" for commit in DEMO_COMMITS)
                release_filter = f"array_contains(array({commits_str}), commit_id)"

        return {
            "kpi_data_df": self.spark.read.table(kpi_data_table).where(
                f"{release_filter}"
                f"AND CAST(current_time AS DATE) >= '{start_date.isoformat()}'"
                f"AND CAST(current_time AS DATE) <= '{end_date.isoformat()}'"
            ),
        }

    # Class fields

    SOURCE_NAME: ClassVar[str] = "pace_metrics"
    DOMAIN_NAME: ClassVar[str] = "Customer Function"
    DOMAIN: ClassVar[str] = "cufu"
    PARAMETERIZATION = {
        "drive_category": ["vehicle-ready", "vehicle-performance", "demo"],
    }

    # KPI definitions

    # @staticmethod
    # def _kpi_assisted_driving_coverage(kpi_data_df: DataFrame, **kwargs: Any) -> float:
    #     """Calculate the assisted driving coverage.

    #     This is done by computing the ratio of HWP driven distance to the total driven distance.
    #     """
    #     result_rows = kpi_data_df.agg(
    #         (sum("hwp_driven_distance_km") / sum("total_driven_distance_km") * 100).alias("value")
    #     ).collect()

    #     return result_rows[0]["value"]

    # @staticmethod
    # def _kpi_number_of_unplanned_driver_takeovers_per_100km(kpi_data_df: DataFrame, **kwargs: Any) -> float:
    #     """Calculate the number of unplanned driver takeovers per 100km."""
    #     result_rows = kpi_data_df.agg(
    #         (sum("number_of_unplanned_driver_takeovers") / sum("total_driven_distance_km") * 100).alias("value")
    #     ).collect()

    #     return result_rows[0]["value"]

    @staticmethod
    def _kpi_longitudinal_comfort(kpi_data_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the longitudinal comfort."""
        result_rows = kpi_data_df.agg(
            weighted_avg(col("relative_longitudinal_comfort"), col("total_driven_distance_km")).alias("value")
        ).collect()

        return result_rows[0]["value"]

    @staticmethod
    def _kpi_steering_nervosity(kpi_data_df: DataFrame, **kwargs: Any) -> float:
        """Calculate the steering nervosity compared to a human driver."""
        result_rows = kpi_data_df.agg(
            (
                (
                    weighted_avg(col("comfort_steering_automated_high_velocity"), col("total_driven_distance_km"))
                    + weighted_avg(col("comfort_steering_automated_mid_velocity"), col("total_driven_distance_km"))
                    + weighted_avg(col("comfort_steering_automated_low_velocity"), col("total_driven_distance_km"))
                )
                / 3.0
            ).alias("value")
        ).collect()

        return result_rows[0]["value"]

    # @staticmethod
    # def _kpi_number_of_customer_relevant_tags_per_100km(kpi_data_df: DataFrame, **kwargs: Any) -> float:
    #     """Calculate the number of customer relevant tags per 100km."""
    #     result_rows = kpi_data_df.agg(
    #         (sum("number_of_customer_relevant_tags") / sum("total_driven_distance_km") * 100).alias("value")
    #     ).collect()

    #     return result_rows[0]["value"]

    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = {
        # 145659: _kpi_assisted_driving_coverage,
        # 145661: _kpi_number_of_unplanned_driver_takeovers_per_100km,
        269873: _kpi_longitudinal_comfort,
        145666: _kpi_steering_nervosity,
        # 281726: _kpi_number_of_customer_relevant_tags_per_100km,
    }


if __name__ == "__main__":  # pragma: no cover
    CuFuKpiCalculation.cli_entrypoint()
