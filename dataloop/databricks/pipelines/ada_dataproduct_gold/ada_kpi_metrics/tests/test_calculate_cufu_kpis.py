"""Unit tests for Customer Function KPI calculation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date, datetime, timedelta
from functools import partial
from unittest.mock import MagicMock, Mock, PropertyMock, patch

import pytest
from ado_key_result_kpis.calculate_cufu_kpis import CuFuKpiCalculation
from constants.common import GOLD_SCHEMA_KEY_RESULT_KPIS
from dbxlib.pyspark import compare_struct_types
from pyspark.sql import DataFrame, SparkSession
from rddlib.utils import full_qualname

DATETIME_1 = datetime(2024, 1, 1, 1, 0, 0)
DATETIME_2 = datetime(2024, 1, 2, 1, 0, 0)
DATE_2 = date(2024, 1, 2)
GOLD_DATA_CUFU_KPI_DATA = [
    {
        "current_time": DATETIME_1,
        "commit_id": "commit_1",
        "release_names": ["automated-vehicle-ready-2024.1.1"],
        "hwp_driven_distance_km": 5.0,
        "total_driven_distance_km": 10.0,
        "number_of_unplanned_driver_takeovers": 5,
        "comfort_steering_automated_high_velocity": 1.0,
        "comfort_steering_automated_mid_velocity": 1.5,
        "comfort_steering_automated_low_velocity": 2.0,
        "relative_longitudinal_comfort": 0.1,
    },
    {
        "current_time": DATETIME_1,
        "commit_id": "commit_2",
        "release_names": ["automated-vehicle-performance-2024.1.1"],
        "hwp_driven_distance_km": 2.0,
        "total_driven_distance_km": 5.0,
        "number_of_unplanned_driver_takeovers": 3,
        "comfort_steering_automated_high_velocity": 0.8,
        "comfort_steering_automated_mid_velocity": 0.3,
        "comfort_steering_automated_low_velocity": 0.2,
        "relative_longitudinal_comfort": 0.25,
    },
    {
        "current_time": DATETIME_2,
        "release_names": ["automated-vehicle-ready-2024.1.2"],
        "hwp_driven_distance_km": 7.5,
        "total_driven_distance_km": 10.0,
        "number_of_unplanned_driver_takeovers": 3,
        "comfort_steering_automated_high_velocity": 0.5,
        "comfort_steering_automated_mid_velocity": 1.0,
        "comfort_steering_automated_low_velocity": 1.5,
        "relative_longitudinal_comfort": 0.5,
    },
]
SILVER_DATA_ADO_NODES = [
    {
        "id": 1001,
        "workitem_type": "Key Result",
        "title": "Test key result KPI",
        "key_result_type": "greater-than",
        "key_result_unit": "%",
        "key_result_target_value": 100.0,
    },
    {
        "id": 1002,
        "workitem_type": "Key Result",
        "title": "Other test key result KPI",
        "key_result_type": "smaller-than",
        "key_result_unit": "m",
        "key_result_target_value": 20.0,
    },
]


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for ADO Aggregator").getOrCreate()


@pytest.fixture
def kpi_calculation(spark: SparkSession) -> CuFuKpiCalculation:
    """Fixture to create an instance of ADOKeyResultKpiCalculation for testing."""
    return CuFuKpiCalculation(spark, "silver_test", "gold_test", False)


def spark_table_func(full_table_name: str, name_df_map: dict[str, DataFrame]) -> DataFrame:
    """Returns the dataframe with a given name from the given mapping.

    Used to mock the spark.table method.
    """
    table_name = full_table_name.rsplit(".", 1)[-1]
    return name_df_map[table_name]


@pytest.fixture(scope="session")
def mock_spark_table(spark: SparkSession) -> MagicMock:
    """Mock for the spark.table method."""
    return MagicMock(
        side_effect=partial(
            spark_table_func,
            name_df_map={
                "cufu_kpi_data": spark.createDataFrame(GOLD_DATA_CUFU_KPI_DATA),
                "ado_nodes": spark.createDataFrame(SILVER_DATA_ADO_NODES),
            },
        )
    )


# _load_kpi_data


@pytest.mark.parametrize(
    ("date, drive_category, expected_hwp_driven_distance_km"),
    [
        (DATETIME_1 + timedelta(days=1), "vehicle-ready", [5.0]),
        (DATETIME_2 + timedelta(days=1), "vehicle-ready", [5.0, 7.5]),
        (DATETIME_2 + timedelta(days=1), "vehicle-performance", [2.0]),
        (DATETIME_2 + timedelta(days=1), "demo", []),
        (datetime(2020, 1, 1), "vehicle-ready", []),
    ],
)
def test_load_kpi_data(
    mock_spark_table: MagicMock,
    kpi_calculation: CuFuKpiCalculation,
    date: datetime,
    drive_category: str,
    expected_hwp_driven_distance_km: list[float],
) -> None:
    """Test _load_kpi_data method."""
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_rows = kpi_calculation._load_kpi_data(date, drive_category)["kpi_data_df"].collect()
        assert len(result_rows) == len(expected_hwp_driven_distance_km)
        if len(expected_hwp_driven_distance_km) >= 1:
            assert result_rows[0]["hwp_driven_distance_km"] == expected_hwp_driven_distance_km[0]
        if len(expected_hwp_driven_distance_km) >= 2:
            assert result_rows[1]["hwp_driven_distance_km"] == expected_hwp_driven_distance_km[1]


# calculate


def test_calculate(mock_spark_table: MagicMock, kpi_calculation: CuFuKpiCalculation) -> None:
    """Test calculate method."""
    test_kpi_definitions = {1001: CuFuKpiCalculation._kpi_longitudinal_comfort}

    with (
        patch(f"{full_qualname(CuFuKpiCalculation)}.KPI_DEFINITIONS", test_kpi_definitions),
        patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property,
    ):
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_df = kpi_calculation.calculate(DATE_2 + timedelta(days=1))

        assert compare_struct_types(result_df.schema, GOLD_SCHEMA_KEY_RESULT_KPIS)
        assert result_df.count() == 2

        first_row = result_df.collect()[0]
        assert first_row["key_result_id"] == 1001
        assert first_row["title"] == "Test key result KPI"
        assert first_row["domain"] == "Customer Function"
        assert first_row["updated_at"] == DATE_2 + timedelta(days=1)
        assert first_row["current_value"] == (0.1 * 10.0 + 0.5 * 10.0) / (10.0 + 10.0)
        assert first_row["target_value"] == 100.0
        assert first_row["unit"] == "%"
        assert first_row["type"] == "greater-than"
        assert first_row["source"] == "pace_metrics"
        assert first_row["parameters"] == ["drive_category=vehicle-ready"]

        second_row = result_df.collect()[1]
        assert second_row["key_result_id"] == 1001
        assert second_row["current_value"] == 0.25
        assert second_row["parameters"] == ["drive_category=vehicle-performance"]
