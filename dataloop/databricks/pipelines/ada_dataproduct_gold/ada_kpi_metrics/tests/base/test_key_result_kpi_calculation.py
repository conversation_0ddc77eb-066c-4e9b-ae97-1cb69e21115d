"""Unit tests for Customer Function KPI calculation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date
from functools import partial
from typing import Any, Callable, ClassVar
from unittest.mock import MagicMock, Mock, PropertyMock, patch

import pytest
from base.key_result_kpi_calculation import KeyResultKpiCalculationBase
from pyspark.sql import DataFrame, SparkSession
from rddlib.utils import full_qualname

SILVER_DATA_ADO_NODES = [
    {
        "id": 1001,
        "workitem_type": "Key Result",
        "title": "Test key result KPI",
        "key_result_type": "greater-than",
        "key_result_unit": "%",
        "key_result_target_value": 100.0,
    },
    {
        "id": 1002,
        "workitem_type": "Key Result",
        "title": "Other test key result KPI",
        "key_result_type": "smaller-than",
        "key_result_unit": "m",
        "key_result_target_value": 20.0,
    },
]


class KeyResultKpiCalculation(KeyResultKpiCalculationBase):
    """Dummy subclass used for testing."""

    # Class fields

    DOMAIN: ClassVar[str] = "test_domain"
    SOURCE_NAME: ClassVar[str] = "test_source"
    DOMAIN_NAME: ClassVar[str] = "test_domain"
    KPI_DEFINITIONS: ClassVar[dict[int, Callable[..., float]]] = {}

    def _load_kpi_data(self, date: date, **kwargs: Any) -> dict[str, Any]:
        """Dummy implementation used for testing."""
        return {}


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for ADO Aggregator").getOrCreate()


@pytest.fixture
def kpi_calculation(spark: SparkSession) -> KeyResultKpiCalculation:
    """Fixture to create an instance of ADOKeyResultKpiCalculation for testing."""
    return KeyResultKpiCalculation(spark, "silver_test", "gold_test", False)


def spark_table_func(full_table_name: str, name_df_map: dict[str, DataFrame]) -> DataFrame:
    """Returns the dataframe with a given name from the given mapping.

    Used to mock the spark.table method.
    """
    table_name = full_table_name.rsplit(".", 1)[-1]
    return name_df_map[table_name]


@pytest.fixture(scope="session")
def mock_spark_table(spark: SparkSession) -> MagicMock:
    """Mock for the spark.table method."""
    return MagicMock(
        side_effect=partial(
            spark_table_func,
            name_df_map={"ado_nodes": spark.createDataFrame(SILVER_DATA_ADO_NODES)},
        )
    )


# _query_key_result_data


def test_query_key_result_data(mock_spark_table: MagicMock, kpi_calculation: KeyResultKpiCalculation) -> None:
    """Test _create_kpi_row method."""
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result = kpi_calculation._query_key_result_data(1001)
        assert result["key_result_id"] == 1001
        assert result["title"] == "Test key result KPI"
        assert result["target_value"] == 100.0
        assert result["unit"] == "%"
        assert result["type"] == "greater-than"
