"""Unit tests for Customer Function KPI calculation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date
from functools import partial
from typing import Any, Callable, ClassVar
from unittest.mock import MagicMock, Mock, PropertyMock, patch

import pytest
from base.contract_kpi_calculation import ContractKpiCalculationBase
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType
from rddlib.utils import full_qualname

SILVER_DATA_PACE_NEEDS = [
    {
        "id": "REQ_METRIC_CONTRACT_KPI_123",
        "version": "123test",
        "title": "Test needs element",
        "created_date": "2025-02-16T16:49:36.000+00:00",
    },
    {
        "id": "REQ_METRIC_CONTRACT_KPI_234",
        "version": "123test",
        "title": "Other test needs element",
        "created_date": "2025-01-16T16:49:36.000+00:00",
    },
]


class ContractKpiCalculation(ContractKpiCalculationBase):
    """Dummy subclass used for testing."""

    # Class fields

    GOLD_SCHEMA_KPIS: ClassVar[StructType] = StructType([])
    GOLD_TABLE_KPIS: ClassVar[str] = "test_contract_kpis"
    DOMAIN: ClassVar[str] = "test_domain"
    SOURCE_NAME: ClassVar[str] = "test_source"
    OWNER: ClassVar[str] = "test_owner"
    NEEDS_KPI_ID: ClassVar[dict[str, Callable[..., float]]] = {}

    def _load_kpi_data(self, date: date, **kwargs: Any) -> dict[str, Any]:
        """Dummy implementation used for testing."""
        return {}

    def calculate(self, date: date) -> DataFrame:
        """Dummy implementation used for testing."""
        return super().calculate(date)  # type: ignore[safe-super]


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for ADO Aggregator").getOrCreate()


@pytest.fixture
def kpi_calculation(spark: SparkSession) -> ContractKpiCalculation:
    """Fixture to create an instance of ADOKeyResultKpiCalculation for testing."""
    return ContractKpiCalculation(spark, "silver_test", "gold_test", False)


def spark_table_func(full_table_name: str, name_df_map: dict[str, DataFrame]) -> DataFrame:
    """Returns the dataframe with a given name from the given mapping.

    Used to mock the spark.table method.
    """
    table_name = full_table_name.rsplit(".", 1)[-1]
    return name_df_map[table_name]


@pytest.fixture(scope="session")
def mock_spark_table(spark: SparkSession) -> MagicMock:
    """Mock for the spark.table method."""
    return MagicMock(
        side_effect=partial(
            spark_table_func,
            name_df_map={"pace_needs": spark.createDataFrame(SILVER_DATA_PACE_NEEDS)},
        )
    )


# _query_needs_kpi_id_data


def test_query_needs_id_result_data(mock_spark_table: MagicMock, kpi_calculation: ContractKpiCalculation) -> None:
    """Test _create_kpi_row method."""
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result = kpi_calculation._query_needs_kpi_id_data("REQ_METRIC_CONTRACT_KPI_123")
        assert result["needs_kpi_id"] == "REQ_METRIC_CONTRACT_KPI_123"
        assert result["title"] == "Test needs element"
