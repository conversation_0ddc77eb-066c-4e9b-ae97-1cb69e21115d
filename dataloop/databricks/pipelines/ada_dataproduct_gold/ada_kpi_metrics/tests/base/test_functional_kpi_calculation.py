"""Unit tests for functional Customer Function KPI calculation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date
from functools import partial
from typing import Any, Callable, ClassVar
from unittest.mock import MagicMock, Mock, PropertyMock, patch

import pytest
from base.functional_kpi_calculation import FunctionalContractKpiCalculationBase
from pyspark.sql import DataFrame, SparkSession
from rddlib.utils import full_qualname

SILVER_DATASET_TABLE = [
    {
        "date": "2025-02-17",
        "location": "Feuerbach",
        "commit_sha": "123456",
        "file_hash": "555555",
    },
    {
        "date": "2025-02-17",
        "location": "Feuerbach",
        "commit_sha": "123456",
        "file_hash": "777777",
    },
    {
        "date": "2025-02-18",
        "location": "Feuerbach",
        "commit_sha": "987654",
        "file_hash": "999999",
    },
]

SILVER_GITHUB_PR_TABLE = [
    {"commit_sha": "123456", "merged_date": "2025-01-06T10:26:41.000+00:00"},
    {"commit_sha": "987654", "merged_date": "2025-01-05T14:48:12.000+00:00"},
]


class FunctionalContractKpiCalculation(FunctionalContractKpiCalculationBase):
    """Dummy subclass used for testing."""

    # Class fields

    DOMAIN: ClassVar[str] = "test_domain"
    SOURCE_NAME: ClassVar[str] = "test_source"
    OWNER: ClassVar[str] = "test_owner"
    NEEDS_KPI_ID: ClassVar[dict[str, Callable[..., float]]] = {}

    def _load_kpi_data(self, date: date, **kwargs: Any) -> dict[str, Any]:
        """Dummy implementation used for testing."""
        return {}


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for ADO Aggregator").getOrCreate()


@pytest.fixture
def kpi_calculation(spark: SparkSession) -> FunctionalContractKpiCalculation:
    """Fixture to create an instance of ADOKeyResultKpiCalculation for testing."""
    return FunctionalContractKpiCalculation(spark, "silver_test", "gold_test", False)


def spark_table_func(full_table_name: str, name_df_map: dict[str, DataFrame]) -> DataFrame:
    """Returns the dataframe with a given name from the given mapping.

    Used to mock the spark.table method.
    """
    table_name = full_table_name.rsplit(".", 1)[-1]
    return name_df_map[table_name]


@pytest.fixture(scope="session")
def mock_spark_table(spark: SparkSession) -> MagicMock:
    """Mock for the spark.table method."""
    return MagicMock(
        side_effect=partial(
            spark_table_func,
            name_df_map={
                "github_pullrequests": spark.createDataFrame(SILVER_GITHUB_PR_TABLE),
                "test_dataset": spark.createDataFrame(SILVER_DATASET_TABLE),
            },
        )
    )


# _get_dataset_data


def test_get_dataset_data(mock_spark_table: MagicMock, kpi_calculation: FunctionalContractKpiCalculation) -> None:
    """Test _get_dataset_data method."""
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_df = kpi_calculation._get_dataset_data("test_dataset")

        assert result_df == {"commit_sha": "123456", "gmdm_file_hashes": ["555555", "777777"]}
