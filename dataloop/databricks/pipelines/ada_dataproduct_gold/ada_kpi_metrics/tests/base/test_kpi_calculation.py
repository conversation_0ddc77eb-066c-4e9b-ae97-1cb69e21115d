"""Unit tests for ADO key result KPIs gold aggregator."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date
from typing import ClassVar
from unittest.mock import patch

import pytest
from base.kpi_calculation import KpiCalculationBase
from constants.common import GOLD_DBX_SCHEMA, GOLD_SCHEMA_KEY_RESULT_KPIS
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType

DATE_1 = date(2024, 1, 1)
DATE_2 = date(2024, 1, 2)
DATA_KPI_TABLE = [
    {
        "key_result_id": 1000,
        "title": "Test key result KPI",
        "domain": "Test domain",
        "updated_at": DATE_1,
        "current_value": 50.0,
        "target_value": 100.0,
        "unit": "m",
        "type": "greater-than",
        "source": "test_data",
    },
    {
        "key_result_id": 1000,
        "title": "Test key result KPI",
        "domain": "Test domain",
        "updated_at": DATE_2,
        "current_value": 80.0,
        "target_value": 100.0,
        "unit": "m",
        "type": "greater-than",
        "source": "test_data",
    },
]


class KpiCalculation(KpiCalculationBase):
    """Dummy subclass used for testing."""

    # Class fields
    GOLD_SCHEMA_KPIS: ClassVar[StructType] = GOLD_SCHEMA_KEY_RESULT_KPIS
    GOLD_TABLE_KPIS: ClassVar[str] = "test_kpis"
    DOMAIN: ClassVar[str] = "test"
    CLI_DESCRIPTION: ClassVar[str | None] = None
    SCHEMA_ID: ClassVar[str] = "key_result_id"

    def calculate(self, date: date) -> DataFrame:
        """Dummy implementation used for testing."""
        return super().calculate(date)  # type: ignore[safe-super]


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for ADO Aggregator").getOrCreate()


@pytest.fixture
def kpi_calculation(spark: SparkSession) -> KpiCalculation:
    """Fixture to create an instance of ADOKeyResultKpiCalculation for testing."""
    return KpiCalculation(
        spark,
        "silver_tests",
        "gold_test",
        True,
    )


@pytest.fixture
def kpi_df(spark: SparkSession) -> DataFrame:
    """Fixture to create a spark dataframe containing test KPI data."""
    return spark.createDataFrame(DATA_KPI_TABLE, GOLD_SCHEMA_KEY_RESULT_KPIS)


# update_delta_table


def test_update_delta_table_invalid_schema(spark: SparkSession, kpi_calculation: KpiCalculationBase) -> None:
    """Test _update_delta_table being called with invalid schema."""
    kpi_df = spark.createDataFrame([{"a": 1, "b": 2}])

    with pytest.raises(ValueError, match="KPI data has invalid schema"):
        kpi_calculation.update_delta_table(kpi_df)


def test_update_delta_table_test_mode(kpi_df: DataFrame, kpi_calculation: KpiCalculationBase) -> None:
    """Test _update_delta_table being called in test mode."""
    with (
        patch("rddlib.delta_table_utils.merge") as mock_merge,
        patch("rddlib.delta_table_utils.table_exists", return_value=True) as mock_table_exists,
        patch("rddlib.delta_table_utils.table_has_schema", return_value=True),
        patch.object(kpi_calculation, "test_mode", True),
    ):
        kpi_calculation.update_delta_table(kpi_df)
        mock_table_exists.assert_called_once_with(f"gold_test.{GOLD_DBX_SCHEMA}.{KpiCalculation.GOLD_TABLE_KPIS}_test")
        mock_merge.assert_called_once()


@pytest.mark.parametrize(
    ("table_exists, expected_merge_called, expected_overwrite_called"),
    [
        (
            True,
            True,
            False,
        ),  # Table exists, expecting merge to be called
        (
            False,
            False,
            True,
        ),  # Table doesn't exist, expecting overwrite to be called
    ],
)
def test_update_delta_table_combinations(
    kpi_df: DataFrame,
    kpi_calculation: KpiCalculationBase,
    table_exists: bool,
    expected_merge_called: bool,
    expected_overwrite_called: bool,
) -> None:
    """Test _update_delta_table table state combinations."""
    with (
        patch("rddlib.delta_table_utils.merge") as mock_merge,
        patch("rddlib.delta_table_utils.overwrite") as mock_overwrite,
        patch("rddlib.delta_table_utils.table_exists", return_value=table_exists),
    ):
        kpi_calculation.update_delta_table(kpi_df)
        if expected_merge_called:
            mock_merge.assert_called_once()
        if expected_overwrite_called:
            mock_overwrite.assert_called_once()
