"""Unit tests for Customer Function KPIs gold aggregator."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date, datetime
from functools import partial
from unittest.mock import MagicMock, Mock, PropertyMock, patch

import pytest
from ado_key_result_kpis.aggregate_cufu_kpi_data import CuFuKpiDataGoldAggregator
from constants.cufu_kpi_data import GOLD_SCHEMA_CUFU_KPI_DATA
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType
from rddlib.utils import full_qualname

DATETIME_1 = datetime(2024, 1, 1, 1, 0, 0)
DATETIME_2 = datetime(2024, 1, 2, 1, 0, 0)
DATE_1 = date(2024, 1, 1)
DATE_2 = date(2024, 1, 2)

# Sample data for testing
SILVER_DATA_KPI_LOCATION = [
    {
        "mdm_hash": "hash1",
        "version": "v1",
        "file_name": "file1.dat",
        "driving_scenario": ["scenario1", "scenario2"],
        "commit_id": "commit1",
        "current_time": DATETIME_1,
        "Full_drive": 1.0,
        "Direction_As_Reference": 0.0,
    },
    {
        "mdm_hash": "hash2",
        "version": "v2",
        "file_name": "file2.dat",
        "driving_scenario": ["scenario3"],
        "commit_id": "commit2",
        "current_time": DATETIME_2,
        "Full_drive": 0.0,
        "Direction_As_Reference": 1.0,
    },
]

SILVER_DATA_GITHUB_RELEASE = [
    {"release": {"target_commitish": "commit1", "name": "release1", "tag_name": "tag_1"}},
    {"release": {"target_commitish": "commit1", "name": "release2", "tag_name": "tag_2"}},
    {"release": {"target_commitish": "commit2", "name": "release3", "tag_name": "tag_3"}},
]

SILVER_DATA_KPI_PILOTED_DISTANCE = [
    {
        "mdm_hash": "hash1",
        "hwp_driven_distance_meters": 5000.0,
        "total_driven_distance_meters": 10000.0,
        "current_time": DATETIME_1,
    },
    {
        "mdm_hash": "hash2",
        "hwp_driven_distance_meters": 7500.0,
        "total_driven_distance_meters": 10000.0,
        "current_time": DATETIME_2,
    },
]

SILVER_DATA_KPI_SAFETY_DRIVER_TAKEOVERS = [
    {
        "mdm_hash": "hash1",
        "metric": "0/metric1",
        "deactivation_reason": "BRAKE_PEDAL_PRESSED",
        "deactivated_function": "handson",
        "current_time": DATETIME_1,
    },
    {
        "mdm_hash": "hash1",
        "metric": "0/metric2",
        "deactivation_reason": "STEERING_WHEEL_DRIVER_TORQUE_APPLIED",
        "deactivated_function": "handsfree",
        "current_time": DATETIME_1,
    },
    {
        "mdm_hash": "hash2",
        "metric": "0/metric3",
        "deactivation_reason": "ACCELERATOR_PRESSED",
        "deactivated_function": "handson",
        "current_time": DATETIME_2,
    },
    {
        "mdm_hash": "hash2",
        "metric": "1/metric4",  # This one should be filtered out
        "deactivation_reason": "DEACTIVATE_PRESSED",
        "deactivated_function": "handsfree",
        "current_time": DATETIME_2,
    },
]

SILVER_DATA_KPI_AGGREGATED_RESULTS = [
    {
        "mdm_hash": "hash1",
        "longitudinal_comfort": 1.2,
        "comfort_steering_automated_high_velocity": 0.15,
        "comfort_steering_automated_mid_velocity": 0.5,
        "comfort_steering_automated_low_velocity": 0.9,
        "current_time": DATETIME_1,
    },
    {
        "mdm_hash": "hash2",
        "longitudinal_comfort": 1.0,
        "comfort_steering_automated_high_velocity": 0.1,
        "comfort_steering_automated_mid_velocity": 0.4,
        "comfort_steering_automated_low_velocity": 0.8,
        "current_time": DATETIME_2,
    },
]

SILVER_DATA_KPI_CONTENT_TAGS = [
    {"mdm_hash": "hash1", "name": "tag1"},
    {"mdm_hash": "hash1", "name": "tag2"},
    {"mdm_hash": "hash2", "name": "tag3"},
]


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests.

    Returns:
        SparkSession: A SparkSession instance for testing.
    """
    return SparkSession.builder.master("local[2]").appName("Unit Testing for CuFu KPI Data Aggregator").getOrCreate()


@pytest.fixture
def kpi_aggregator(spark: SparkSession) -> CuFuKpiDataGoldAggregator:
    """Fixture to create an instance of CuFuKpiDataGoldAggregator for testing.

    Args:
        spark (SparkSession): The SparkSession instance.

    Returns:
        CuFuKpiDataGoldAggregator: An instance of the CuFuKpiDataGoldAggregator for testing.
    """
    return CuFuKpiDataGoldAggregator(spark, "silver_test", "gold_test", False)


def spark_table_func(full_table_name: str, name_df_map: dict[str, DataFrame]) -> DataFrame:
    """Returns the dataframe with a given name from the given mapping."""
    table_name = full_table_name.rsplit(".", 1)[-1]
    return name_df_map[table_name]


@pytest.fixture(scope="session")
def mock_spark_table(spark: SparkSession) -> MagicMock:
    """Mock for the spark.table method.

    Args:
        spark (SparkSession): The SparkSession instance.

    Returns:
        MagicMock: A mock object for the spark.table method.
    """
    return MagicMock(
        side_effect=partial(
            spark_table_func,
            name_df_map={
                "kpi_location_of_the_drive": spark.createDataFrame(SILVER_DATA_KPI_LOCATION),
                "github_release": spark.createDataFrame(SILVER_DATA_GITHUB_RELEASE),
                "kpi_piloted_distance_time": spark.createDataFrame(SILVER_DATA_KPI_PILOTED_DISTANCE),
                "kpi_safety_driver_takeovers": spark.createDataFrame(SILVER_DATA_KPI_SAFETY_DRIVER_TAKEOVERS),
                "kpi_aggregated_results": spark.createDataFrame(SILVER_DATA_KPI_AGGREGATED_RESULTS),
                "kpi_content_tags": spark.createDataFrame(SILVER_DATA_KPI_CONTENT_TAGS),
            },
        )
    )


def test_get_initial_df(mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator) -> None:
    """Test the _get_initial_df method of CuFuKpiDataGoldAggregator.

    This test verifies that the _get_initial_df method correctly processes and combines
    the initial data from various sources.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_df = kpi_aggregator._get_initial_df(None)

        assert result_df.count() == 2
        first_row = result_df.where("mdm_hash = 'hash1'").collect()[0]
        assert first_row["version"] == "v1"
        assert first_row["file_name"] == "file1.dat"
        assert first_row["driving_scenario"] == ["scenario1", "scenario2"]
        assert first_row["commit_id"] == "commit1"
        assert first_row["current_time"] == DATETIME_1
        assert first_row["is_full_drive"] is True
        assert first_row["direction"] == "same"
        assert first_row["release_names"] == ["release1", "release2"]


def test_data_assisted_driving_coverage(mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator) -> None:
    """Test the _data_assisted_driving_coverage method of CuFuKpiDataGoldAggregator.

    This test verifies that the _data_assisted_driving_coverage method correctly calculates
    the assisted driving coverage based on the input data.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_df = kpi_aggregator._data_assisted_driving_coverage(
            kpi_aggregator.spark.read.table("kpi_piloted_distance_time")
        )

        assert result_df.count() == 2
        first_row = result_df.where("mdm_hash = 'hash1'").collect()[0]
        assert first_row["hwp_driven_distance_km"] == 5.0
        assert first_row["total_driven_distance_km"] == 10.0


def test_data_number_of_unplanned_driver_takeovers(
    mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator
) -> None:
    """Test the _data_number_of_unplanned_driver_takeovers method of CuFuKpiDataGoldAggregator.

    This test verifies that the _data_number_of_unplanned_driver_takeovers method correctly
    calculates the number of unplanned driver takeovers based on the input data.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    # to be implemented
    pass


def test_data_longitudinal_comfort(mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator) -> None:
    """Test the _data_longitudinal_comfort method of CuFuKpiDataGoldAggregator.

    This test verifies that the _data_longitudinal_comfort method correctly calculates
    the longitudinal comfort based on the input data.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_df = kpi_aggregator._data_longitudinal_comfort(kpi_aggregator.spark.read.table("kpi_aggregated_results"))

        assert result_df.count() == 2
        assert result_df.where("mdm_hash = 'hash1'").collect()[0]["relative_longitudinal_comfort"] == pytest.approx(
            79.97, 0.01
        )
        assert result_df.where("mdm_hash = 'hash2'").collect()[0]["relative_longitudinal_comfort"] == pytest.approx(
            66.64, 0.01
        )


def test_data_steering_nervosity(mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator) -> None:
    """Test the _data_steering_nervosity method of CuFuKpiDataGoldAggregator.

    This test verifies that the _data_steering_nervosity method correctly calculates
    the steering nervosity based on the input data.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_df = kpi_aggregator._data_steering_nervosity(kpi_aggregator.spark.read.table("kpi_aggregated_results"))

        assert result_df.count() == 2
        first_row = result_df.where("mdm_hash = 'hash1'").collect()[0]
        assert first_row["comfort_steering_automated_high_velocity"] == pytest.approx(70.52, 0.01)
        assert first_row["comfort_steering_automated_mid_velocity"] == pytest.approx(69.09, 0.01)
        assert first_row["comfort_steering_automated_low_velocity"] == pytest.approx(69.88, 0.01)


def test_data_number_of_customer_relevant_tags(
    mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator
) -> None:
    """Test the _data_number_of_customer_relevant_tags method of CuFuKpiDataGoldAggregator.

    This test verifies that the _data_number_of_customer_relevant_tags method correctly calculates
    the number of customer relevant tags based on the input data.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    with patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property:
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        result_df = kpi_aggregator._data_number_of_customer_relevant_tags(
            kpi_aggregator.spark.read.table("kpi_content_tags")
        )

        assert result_df.count() == 2
        assert result_df.where("mdm_hash = 'hash1'").collect()[0]["number_of_customer_relevant_tags"] == 2
        assert result_df.where("mdm_hash = 'hash2'").collect()[0]["number_of_customer_relevant_tags"] == 1


def test_aggregate_kpi_data(mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator) -> None:
    """Test the aggregate_kpi_data method of CuFuKpiDataGoldAggregator.

    This test verifies that the aggregate_kpi_data method correctly aggregates all the KPI data
    and produces the expected output.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    with (
        patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property,
        patch(f"{full_qualname(CuFuKpiDataGoldAggregator)}._update_delta_table") as mock_update_delta_table,
        patch(f"{full_qualname(CuFuKpiDataGoldAggregator)}._run_quality_check") as mock_run_quality_check,
        patch(f"{full_qualname(CuFuKpiDataGoldAggregator)}._get_silver_data") as mock_get_silver_data,
    ):
        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        # Mock _get_silver_data to return our test DataFrames
        mock_get_silver_data.side_effect = lambda table_name, start_date: mock_spark_table(table_name)

        kpi_aggregator.aggregate_kpi_data(DATE_1)

        # Check if all the necessary methods were called
        mock_update_delta_table.assert_called_once()
        mock_run_quality_check.assert_called_once()

        # Get the DataFrame passed to _update_delta_table
        result_df = mock_update_delta_table.call_args[0][0]

        # Verify the structure and content of the result DataFrame
        assert result_df.count() > 0
        assert set(result_df.columns) == set(GOLD_SCHEMA_CUFU_KPI_DATA.fieldNames())

        # Check if specific columns exist in the result DataFrame
        assert "hwp_driven_distance_km" in result_df.columns
        assert "total_driven_distance_km" in result_df.columns
        assert "number_of_unplanned_driver_takeovers" in result_df.columns
        assert "relative_longitudinal_comfort" in result_df.columns
        assert "number_of_customer_relevant_tags" in result_df.columns


def test_run_quality_check(mock_spark_table: MagicMock, kpi_aggregator: CuFuKpiDataGoldAggregator) -> None:
    """Test the _run_quality_check method of CuFuKpiDataGoldAggregator.

    This test verifies that the _run_quality_check method correctly performs quality checks
    on the aggregated data.

    Args:
        mock_spark_table (MagicMock): A mock object for the spark.table method.
        kpi_aggregator (CuFuKpiDataGoldAggregator): An instance of the CuFuKpiDataGoldAggregator.
    """
    with (
        patch(f"{full_qualname(SparkSession)}.read", new_callable=PropertyMock) as mock_read_property,
        patch("rddlib.quality_check.missing_value_detection") as mock_missing_value_detection,
        patch("rddlib.quality_check.deduplicate") as mock_deduplicate,
        patch("rddlib.quality_check.value_range_check") as mock_value_range_check,
        patch("rddlib.quality_check.datetime_consistency_check") as mock_datetime_consistency_check,
        patch("rddlib.quality_check.null_check") as mock_null_check,
        patch("rddlib.quality_check.consistency_check") as mock_consistency_check,
        patch("rddlib.quality_check.log_quality_check_result") as mock_log_quality_check_result,
    ):

        mock_read = Mock()
        mock_read.table = mock_spark_table
        mock_read_property.return_value = mock_read

        # Set up mock returns
        mock_missing_value_detection.return_value = kpi_aggregator.spark.createDataFrame([], StructType([]))
        mock_deduplicate.return_value = (None, [])
        mock_value_range_check.return_value = 0
        mock_datetime_consistency_check.return_value = kpi_aggregator.spark.createDataFrame([], StructType([]))
        mock_null_check.return_value = kpi_aggregator.spark.createDataFrame([], StructType([]))
        mock_consistency_check.return_value = 0

        # Create a sample DataFrame for testing
        sample_df = kpi_aggregator.spark.createDataFrame(
            [("hash1", 5.0, 10.0, DATETIME_1), ("hash2", 7.5, 10.0, DATETIME_2)],
            ["mdm_hash", "hwp_driven_distance_km", "total_driven_distance_km", "current_time"],
        )

        # Run the quality check
        kpi_aggregator._run_quality_check(sample_df, "mdm_hash")

        # Assert that all quality checks were called
        mock_missing_value_detection.assert_called_once()
        mock_deduplicate.assert_called_once()
        mock_value_range_check.assert_called()
        mock_datetime_consistency_check.assert_called_once()
        mock_null_check.assert_called_once()
        mock_consistency_check.assert_called_once()

        # Check that logger.quality_check was called for each check
        assert mock_log_quality_check_result.call_count == 7  # One for each quality check


if __name__ == "__main__":
    pytest.main()
