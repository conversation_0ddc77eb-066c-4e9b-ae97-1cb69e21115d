"""Unit tests for ADO key result KPIs gold aggregator."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date, datetime
from functools import partial
from unittest.mock import MagicMock, patch

import pytest
from ado_key_result_kpis.calculate_ado_kpis import ADO_DOMAIN_KEY_RESULTS_MAP, ADOKeyResultKpiCalculation
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import DateType, DoubleType, IntegerType, StringType, StructField, StructType, TimestampType
from rddlib.utils import full_qualname, method_self_name

DATETIME = datetime(2024, 1, 1, 12, 0, 0)
DATE_1 = date(2024, 1, 1)
DATE_2 = date(2024, 1, 2)
DATE_3 = date(2024, 1, 3)
SILVER_DATA_NODES = [
    {  # Total number of ACA 5.0 System-Requirements
        "id": 1,
        "parent": 0,
        "severity": "high",
        "reason": "reason1",
        "iteration_path": "iteration_path1",
        "state": "state1",
        "value_area": "value_area1",
        "tags": "tags1",
        "area_path": "area_path1",
        "priority": "priority1",
        "workitem_type": "workitem_type1",
        "story_points": 1.0,
        "changed_date": DATETIME,
        "state_change_date": DATETIME,
        "created_date": DATETIME,
        "closed_date": DATETIME,
        "activated_date": DATETIME,
        "ado_type": "KeyResult",
        "ado_state": "inFocus",
        # Relevant fields for key result KPIs
        "title": "Total number of ACA 5.0 System-Requirements",
        "key_result_type": "greater-than",
        "key_result_unit": "%",
        "key_result_data_source": "Comments in this ticket",
        "key_result_target_value": 100.0,
    },
    {  # Total number of ACA 5.0 Stakeholder-Requirements
        "id": 2,
        "parent": 0,
        "severity": "high",
        "reason": "reason1",
        "iteration_path": "iteration_path1",
        "state": "state1",
        "value_area": "value_area1",
        "tags": "tags1",
        "area_path": "area_path1",
        "priority": "priority1",
        "workitem_type": "workitem_type1",
        "story_points": 1.0,
        "changed_date": DATETIME,
        "state_change_date": DATETIME,
        "created_date": DATETIME,
        "closed_date": DATETIME,
        "activated_date": DATETIME,
        "ado_type": "KeyResult",
        "ado_state": "inFocus",
        # Relevant fields for key result KPIs
        "title": "Total number of ACA 5.0 Stakeholder-Requirements",
        "key_result_type": "equals",
        "key_result_unit": "%",
        "key_result_data_source": "Comments in this ticket",
        "key_result_target_value": 90.0,
    },
]

SILVER_DATA_COMMENTS = [
    # Total number of ACA 5.0 System-Requirements
    {
        "id": 1,
        "comment_id": 101,
        "updated_at": DATE_1,
        "kr_value": 50.0,
        "te_status": None,
        "te_forecast": None,
    },
    {
        "id": 1,
        "comment_id": 102,
        "updated_at": DATE_2,
        "kr_value": 75.0,
        "te_status": None,
        "te_forecast": None,
    },
    {
        "id": 1,
        "comment_id": 103,
        "updated_at": DATE_3,
        "kr_value": 100.0,
        "te_status": None,
        "te_forecast": None,
    },
    # Total number of ACA 5.0 Stakeholder-Requirements
    {
        "id": 2,
        "comment_id": 201,
        "updated_at": DATE_1,
        "kr_value": 3.0,
        "te_status": None,
        "te_forecast": None,
    },
    {
        "id": 2,
        "comment_id": 202,
        "updated_at": DATE_2,
        "kr_value": 9.0,
        "te_status": None,
        "te_forecast": None,
    },
]

# TODO: Replace with import from ado.constants.ado_kpis
SILVER_SCHEMA_NODES = StructType(
    [
        StructField("id", IntegerType(), True),
        StructField("parent", IntegerType(), True),
        StructField("severity", StringType(), True),
        StructField("reason", StringType(), True),
        StructField("iteration_path", StringType(), True),
        StructField("title", StringType(), True),
        StructField("state", StringType(), True),
        StructField("value_area", StringType(), True),
        StructField("tags", StringType(), True),
        StructField("area_path", StringType(), True),
        StructField("priority", StringType(), True),
        StructField("workitem_type", StringType(), True),
        StructField("story_points", DoubleType(), True),
        StructField("changed_date", TimestampType(), True),
        StructField("state_change_date", TimestampType(), True),
        StructField("created_date", TimestampType(), True),
        StructField("closed_date", TimestampType(), True),
        StructField("activated_date", TimestampType(), True),
        StructField("key_result_type", StringType(), True),
        StructField("key_result_unit", StringType(), True),
        StructField("key_result_data_source", StringType(), True),
        StructField("key_result_target_value", DoubleType(), True),
        StructField("ado_type", StringType(), True),
        StructField("ado_state", StringType(), True),
    ]
)

SILVER_SCHEMA_COMMENTS = StructType(
    [
        StructField("id", IntegerType(), True),
        StructField("comment_id", IntegerType(), True),
        StructField("updated_at", DateType(), True),
        StructField("kr_value", DoubleType(), True),
        StructField("te_status", StringType(), True),
        StructField("te_forecast", StringType(), True),
    ]
)


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for ADO Aggregator").getOrCreate()


@pytest.fixture
def kpi_calculation(spark: SparkSession) -> ADOKeyResultKpiCalculation:
    """Fixture to create an instance of ADOKeyResultKpiCalculation for testing."""
    return ADOKeyResultKpiCalculation(
        spark,
        "mock_catalog_silver",
        "mock_catalog_gold",
        False,
    )


def spark_table_func(full_table_name: str, name_df_map: dict[str, DataFrame]) -> DataFrame:
    """Returns the dataframe with a given name from the given mapping.

    Used to mock the spark.table method.
    """
    table_name = full_table_name.rsplit(".", 1)[-1]
    return name_df_map[table_name]


@pytest.fixture(scope="session")
def mock_spark_table(spark: SparkSession) -> MagicMock:
    """Mock for the spark.table method."""
    nodes_df = spark.createDataFrame(SILVER_DATA_NODES, SILVER_SCHEMA_NODES)
    comments_df = spark.createDataFrame(SILVER_DATA_COMMENTS, SILVER_SCHEMA_COMMENTS)

    return MagicMock(
        side_effect=partial(
            spark_table_func,
            name_df_map={
                "ado_nodes": nodes_df,
                "ado_comments": comments_df,
            },
        )
    )


# _calculate_domain_kpis


def test_calculate_domain_kpis(
    spark: SparkSession, kpi_calculation: ADOKeyResultKpiCalculation, mock_spark_table: MagicMock
) -> None:
    """Test _calculate_domain_kpis method."""
    with patch.object(*method_self_name(spark.table), mock_spark_table):  # type: ignore[arg-type]
        result_df = kpi_calculation._calculate_domain_kpis(["Total number of ACA 5.0 System-Requirements"], DATE_3)

    all_rows = result_df.collect()
    assert len(all_rows) == 1
    first_row = all_rows[0]
    assert first_row["key_result_id"] == 1
    assert first_row["title"] == "Total number of ACA 5.0 System-Requirements"
    assert first_row["current_value"] == 100
    assert first_row["target_value"] == 100
    assert first_row["unit"] == "%"
    assert first_row["type"] == "greater-than"


def test_calculate_domain_kpis_invalid_title(
    spark: SparkSession, kpi_calculation: ADOKeyResultKpiCalculation, mock_spark_table: MagicMock
) -> None:
    """Test _calculate_domain_kpis method with exception."""
    with patch.object(*method_self_name(spark.table), mock_spark_table):  # type: ignore[arg-type]
        result_df = kpi_calculation._calculate_domain_kpis(["Not existing key result"], DATE_3)

    assert result_df.count() == 0


# calculate


def test_calculate(kpi_calculation: ADOKeyResultKpiCalculation) -> None:
    """Test process_all_key_result_kpis method."""
    mock_df = MagicMock(name="DataFrame")
    with (
        patch(
            full_qualname(ADOKeyResultKpiCalculation._calculate_domain_kpis),  # type: ignore[arg-type]
            return_value=mock_df,
        ) as mock_calculate_domain_kpis,
        patch(full_qualname(DataFrame.union)),  # type: ignore[arg-type]
    ):
        kpi_calculation.calculate(DATE_1)

        domain_count = len(ADO_DOMAIN_KEY_RESULTS_MAP.keys())
        assert mock_calculate_domain_kpis.call_count == domain_count
        # assert mock_union.call_count == domain_count
