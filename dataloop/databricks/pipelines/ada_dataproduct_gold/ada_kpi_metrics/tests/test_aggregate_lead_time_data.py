"""Unit tests for Lead Time gold layer aggregator."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import pytest
from ado_key_result_kpis.aggregate_lead_time_data import LeadTimeDataGoldAggregator
from constants.lead_time_kpi_data import GOLD_SCHEMA_LEADTIME_KPI_DATA, GOLD_TABLE_LEADTIME_KPI_DATA


def test_run_quality_check() -> None:
    """Test run_quality_check method."""
    pass


if __name__ == "__main__":
    pytest.main()
