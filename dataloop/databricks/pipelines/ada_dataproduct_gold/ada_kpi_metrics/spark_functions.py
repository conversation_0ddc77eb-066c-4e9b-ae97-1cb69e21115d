"""Module containing additional spark functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql import Column
from pyspark.sql.functions import lit, sum, transform, when


def nullable(col: Column) -> Column:
    """Ensures the given column is nullable."""
    return when(lit(True), col)


def array_nullable(col: Column) -> Column:
    """Ensures the elements of the arrays as well as the arrays themselves in the given column are nullable."""
    return nullable(transform(col, lambda x: nullable(x)))


def weighted_avg(value_col: Column, weight_col: Column) -> Column:
    """Calculates the weighted average of a column.

    The values of another column are used as weights.

    Args:
        value_col (Column): The column to calculate the weighted average for.
        weight_col (Column): The column containing the weights.
    """
    return sum(value_col * weight_col) / sum(weight_col)
