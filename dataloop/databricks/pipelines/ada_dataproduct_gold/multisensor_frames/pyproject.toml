# ==============================================================================
#
# C O P Y R I G H T
# ------------------------------------------------------------------------------
#
#  Copyright (C) 2024 Robert <PERSON> GmbH
#  Copyright (C) 2024 by CARIAD and Robert <PERSON> GmbH. All rights reserved.
#  The reproduction, distribution and utilization of this file as
#  well as the communication of its contents to others without express
#  authorization is prohibited. Offenders will be held liable for the
#  payment of damages. All rights reserved in the event of the grant
#  of a patent, utility model or design.
# ==============================================================================

[project]
name = "multisensor_frames"
description = "Multisensor Frames asset bundle for Databricks."
readme = "README.md"
authors = [{ name = "Data Delivery" }]
requires-python = ">=3.10"
packages = [{ include = "*", from = "./src" }]
dynamic = ["version"]
dependencies = [
    "delta-spark~=3.2.1",
    "pyspark ~= 3.5.0",
]

[project.optional-dependencies]
"testing" = ["dbx-schemas @ {root:uri}/../../../libraries/dbx_schemas", "pytest", "coverage"]

[project.scripts]
prepare = "multisensor_frames.create_table_multisensor_frames_prep:main"
multisensor_frames = "multisensor_frames.create_table_multisensor_frames:main"

[tool.uv.sources]
dbx-schemas = { path = "../../../libraries/dbx_schemas", editable = true }

[build-system]
requires = ["hatchling", "uv-dynamic-versioning"]
build-backend = "hatchling.build"

[tool.hatch.version]
source = "uv-dynamic-versioning"

[tool.hatch.metadata]
allow-direct-references = true

[tool.hatch.build.targets.sdist]
packages = ["src/multisensor_frames", "dbx_schemas"]

[tool.hatch.build.targets.sdist.force-include]
"../../../libraries/dbx_schemas/src/dbx_schemas" = "dbx_schemas"

[tool.hatch.build.targets.wheel]
packages = ["multisensor_frames", "dbx_schemas"]

[tool.pytest.ini_options]
pythonpath = ["./src", "../../../libraries/dbx_schemas/src"]
log_cli = "True"
log_level = "DEBUG"

# coverage.py configuration
[tool.coverage.run]
branch = true
command_line = "-m pytest tests/"
source = ["src"]
omit = [
    "__init__.py",
]

[tool.coverage.report]
format = "text"
show_missing = true
