variables:
  env:
    default: dev
    description: "environment, dev, qa, prod"
  ms_teams_alert_channel_email:
    default: "<EMAIL>"
    description: "MS Teams alert channel email"
  run_sp:
    default: "3ee9fc25-7c21-49f6-93a5-ab99ad69bf8b"
    lookup:
      service_principal: "sp-pace-dataloop-drive-time-series-${var.env}"
    description: "run service principal"
  spark_version:
    default: "16.2.x-scala2.12"
    description: "Spark version"
  num_workers:
    default: "1"
    description: "Number of workers"
  bronze_catalog:
    default: bronze_dev
    description: "Bronze Catalog name"
  silver_catalog:
    default: silver_dev
    description: "Silver Catalog name"
  target_silver_catalog:
    default: silver_dev
    description: "Silver Catalog name"
  target_gold_catalog:
    default: gold_dev
    description: "Gold Catalog name"
  target_schema:
    default: drive_time_series
    description: "Target schema name"
  driver_instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E16ads_v5_rt14.3"
  schedule_pause_status:
    default: "PAUSED"
    description: "Schedule pause status"
