"""Intermediate Tables for the Multisensor Frame gold table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from typing import Final

from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession, Window
from pyspark.sql.functions import col, row_number

from .multisensor_shared import (
    CAM_FRAMES_SCHEMA_MAPPING,
    CAMERA_NAME_MAPPING,
    CAMERAS,
    DSP_DE_IMAGE_SCHEMA_MAPPING,
    FILE_ENTRIES_SCHEMA_MAPPING,
    FILES_IMAGES_SCHEMA_MAPPING,
    make_table_if_not_exists,
    merge_file_entries,
)

logging.basicConfig()
logger = logging.getLogger(__name__)

TARGET_SCHEMA: Final = "drive_time_series"


def check_point_path(*, catalog: str, schema: str, table: str) -> str:
    """Calculate path for checkpoints."""
    return f"/Volumes/{catalog}/{schema}/checkpoint_locations/{table}/"


def _merge_dsp_de_image_camera(
    spark_session: SparkSession, df: DataFrame, target_table_fqn: str
) -> None:  # pragma: no cover
    """Merge with the dsp_de_image camera table."""
    target_table = DeltaTable.forName(spark_session, target_table_fqn)
    (
        target_table.alias("target")
        .merge(source=df.alias("source").dropDuplicates(["file_hash"]), condition="target.file_hash=source.file_hash")
        .whenMatchedUpdateAll("(target.modified_at < source.modified_at)")
        .whenNotMatchedInsertAll()
        .execute()
    )


def filter_dsp_de_image_for_camera(dsp_de_image: DataFrame, camera: str) -> DataFrame:  # pragma: no cover
    """Removes unwanted entries from dsp_de_image for a specific camera."""
    return (
        dsp_de_image.filter("_change_type != 'delete'")
        .filter(f"camera_stream = '{CAMERA_NAME_MAPPING[camera]}'")
        .filter("color_space = 'RGB'")  # only RGB images, not haglix stuff
        .filter(col("view").isNotNull())
        .filter(col("view_type").isNotNull())
        .filter(col("context").isNotNull())
        .select(
            "file_hash",
            "created_at",
            "modified_at",
            "split_hash",
            "stream_hash",
            "view",
            "view_type",
            "frame_number",
            col("recorded_at").alias("emitted_at"),
            "context",
            "rectification__warper_version",
            "rectification__calstorage_uid",
            "extractor_version",
        )
    )


def create_streaming_dsp_de_image_camera_table(
    *, spark_session: SparkSession, silver_catalog: str, target_catalog: str, camera: str
) -> None:  # pragma: no cover
    """Create a camera specific table from the dsp_de_image namespace."""
    table_name = f"dsp_de_image_{camera}"
    target_table = f"{target_catalog}.{TARGET_SCHEMA}.{table_name}"
    schema = DSP_DE_IMAGE_SCHEMA_MAPPING[camera]
    make_table_if_not_exists(spark_session=spark_session, table=schema, target_catalog=target_catalog)
    dsp_de_image_stream = spark_session.readStream.option("readChangeFeed", "true").table(
        f"{silver_catalog}.mdd.dsp_de_image"
    )
    dsp_de_image_stream = filter_dsp_de_image_for_camera(dsp_de_image_stream, camera)

    (
        dsp_de_image_stream.writeStream.trigger(availableNow=True)
        .option("maxBytesPerTrigger", 60 * 1024 * 1024 * 1024)
        .option(
            "checkpointLocation",
            check_point_path(catalog=target_catalog, schema=TARGET_SCHEMA, table=table_name),
        )
        .queryName(f"create_streaming_dsp_de_image_camera_table: {camera}")
        .foreachBatch(
            lambda batch_df, batch_id: _merge_dsp_de_image_camera(
                spark_session=spark_session,
                df=batch_df,
                target_table_fqn=target_table,
            )
        )
        .start()
        .awaitTermination()
    )


def create_streaming_dsp_de_image_tables(
    *, spark_session: SparkSession, silver_catalog: str, target_catalog: str
) -> None:  # pragma: no cover
    """Create the table for the streaming DSP DE image."""
    for cam in CAMERAS:
        create_streaming_dsp_de_image_camera_table(
            spark_session=spark_session, silver_catalog=silver_catalog, target_catalog=target_catalog, camera=cam
        )


def create_cam_file_entries(
    spark_session: SparkSession, silver_catalog: str, target_catalog: str
) -> None:  # pragma: no cover
    """Create filtered file entries for each camera with just the essential values."""
    for cam in CAMERAS:
        target_table = f"_file_entries_{cam}"
        target_table_fqn = f"{target_catalog}.{TARGET_SCHEMA}.{target_table}"
        schema = FILE_ENTRIES_SCHEMA_MAPPING[cam]
        make_table_if_not_exists(spark_session=spark_session, table=schema, target_catalog=target_catalog)

        (
            spark_session.readStream.option("readChangeFeed", "true")
            .table(f"{silver_catalog}.mdd.file_entries")
            .filter("_change_type != 'delete'")
            .filter(f"tds_file_url like '%\\_{CAMERA_NAME_MAPPING[cam]}%' and file_state='ACTIVE'")
            .select("tds_file_url", "file_hash")
            .writeStream.trigger(availableNow=True)
            .option(
                "checkpointLocation",
                check_point_path(catalog=target_catalog, schema=TARGET_SCHEMA, table=target_table),
            )
            .queryName(f"create_cam_file_entries: {cam}")
            .foreachBatch(
                lambda batch_df, batch_id: merge_file_entries(
                    spark_session=spark_session, batch_df=batch_df, table_fqn=target_table_fqn
                )
            )
            .start()
            .awaitTermination()
        )


def build_files_images_content(dsp_de_image: DataFrame, files_scenes: DataFrame, file_entries: DataFrame) -> DataFrame:
    """Build the content for the files_images table."""
    window = Window.partitionBy("split_hash", "frame_number", "view", "view_type", "context", "emitted_at").orderBy(
        col("modified_at").desc()
    )
    dsp_de_image_with_row = dsp_de_image.select(
        "file_hash",
        "created_at",
        "modified_at",
        "split_hash",
        "stream_hash",
        "view",
        "view_type",
        "frame_number",
        "emitted_at",
        "context",
        "rectification__warper_version",
        "rectification__calstorage_uid",
        "extractor_version",
        row_number().over(window).alias("row_number"),
    )
    return (
        dsp_de_image_with_row.alias("image")
        .join(files_scenes.alias("scenes"), dsp_de_image_with_row.split_hash == files_scenes.file_hash)
        # we use a range join on the file_hash to make use of the clustering in the file entries table
        .join(
            file_entries.hint("range_join", 1000).alias("fe"),
            dsp_de_image_with_row.file_hash == file_entries.file_hash,
            "left",
        )
        .where(col("row_number") == 1)
        .select(
            "scenes.vin",
            "scenes.recording_started_at",
            "image.file_hash",
            "image.created_at",
            "image.modified_at",
            "image.split_hash",
            "image.stream_hash",
            "image.view",
            "image.view_type",
            "image.frame_number",
            "image.emitted_at",
            "image.context",
            "image.rectification__warper_version",
            "image.rectification__calstorage_uid",
            "image.extractor_version",
            "fe.tds_file_url",
        )
    )


def create_files_images_tables(
    spark_session: SparkSession, silver_catalog: str, target_catalog: str
) -> None:  # pragma: no cover
    """Create the files_images tables for each camera.

    These tables should be already highly useful for downstream processing, they contain everything needed for ML on
    the individual cameras. A UNION view of all camera tables should be enough to get information on all the cameras.
    """
    for cam in CAMERAS:
        schema = FILES_IMAGES_SCHEMA_MAPPING[cam]
        target_table_fqn = schema.full_table_name(target_catalog)
        make_table_if_not_exists(spark_session=spark_session, table=schema, target_catalog=target_catalog)
        dsp_de_image = spark_session.table(DSP_DE_IMAGE_SCHEMA_MAPPING[cam].full_table_name(target_catalog))
        files_scenes = spark_session.table(f"{silver_catalog}.ada_ontology.files_scenes")
        file_entries = spark_session.table(FILE_ENTRIES_SCHEMA_MAPPING[cam].full_table_name(target_catalog))
        (
            build_files_images_content(
                dsp_de_image=dsp_de_image, files_scenes=files_scenes, file_entries=file_entries
            ).write.insertInto(target_table_fqn, overwrite=True)
        )


def build_cam_table_content(files_images: DataFrame) -> DataFrame:
    """Build the content for a camera_frame table."""

    distorted = files_images.filter(files_images.view == "Distorted").alias("distorted")
    label = files_images.filter(files_images.view == "Label").alias("label")
    return distorted.join(label, ["vin", "recording_started_at", "emitted_at"]).select(
        distorted.vin,
        distorted.recording_started_at,
        distorted.emitted_at,
        col("distorted.file_hash").alias("distorted_rgb_file_hash"),
        col("distorted.tds_file_url").alias("distorted_rgb_file_url"),
        col("label.file_hash").alias("label_image_file_hash"),
        col("label.tds_file_url").alias("label_image_file_url"),
        distorted.frame_number,
        distorted.context,
        "label.rectification__warper_version",
        "label.rectification__calstorage_uid",
        distorted.extractor_version,
    )


def create_cam_tables(spark_session: SparkSession, target_catalog: str) -> None:  # pragma: no cover
    """Create camera tables with vin and recorded_at columns."""
    for cam in CAMERAS:
        target_table = CAM_FRAMES_SCHEMA_MAPPING[cam]
        source_table = FILES_IMAGES_SCHEMA_MAPPING[cam]
        make_table_if_not_exists(spark_session=spark_session, table=target_table, target_catalog=target_catalog)

        files_images = spark_session.table(source_table.full_table_name(target_catalog))

        table_content = build_cam_table_content(files_images)

        table_content.write.insertInto(target_table.full_table_name(target_catalog), overwrite=True)


def parse_job_parameters() -> argparse.Namespace:  # pragma: no cover
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-j", "--silver_catalog", required=True, type=str)
    parser.add_argument("-t", "--target_catalog", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def main() -> None:  # pragma: no cover
    """Main function for building the intermediate tables to support the multisensor frames tables creation."""
    args = parse_job_parameters()
    try:
        logger.info("starting the prep work")
        session = SparkSession.builder.getOrCreate()
        logger.info("preparing streaming dsp_de_image tables")
        create_streaming_dsp_de_image_tables(
            spark_session=session, silver_catalog=args.silver_catalog, target_catalog=args.target_catalog
        )
        logger.info("preparing file entries")
        create_cam_file_entries(
            spark_session=session, silver_catalog=args.silver_catalog, target_catalog=args.target_catalog
        )
        logger.info("preparing files_images tables")
        create_files_images_tables(
            spark_session=session, silver_catalog=args.silver_catalog, target_catalog=args.target_catalog
        )
        logger.info("preparing cam tables")
        create_cam_tables(spark_session=session, target_catalog=args.target_catalog)
    except Exception:
        logger.error("error in the prep work", exc_info=True)
        raise


if __name__ == "__main__":
    main()
