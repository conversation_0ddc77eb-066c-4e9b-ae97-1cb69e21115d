"""Produces the Multisensor Frame gold table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging

from dbx_schemas.gold.drive_time_series import MULTISENSOR_FRAMES_CAMERA_SCHEMA
from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, expr, greatest, least, make_interval

from .multisensor_shared import CAM_FRAMES_SCHEMA_MAPPING, CAMERAS, make_table_if_not_exists

logging.basicConfig()
logger = logging.getLogger(__name__)


def check_point_path(*, catalog: str, schema: str, table: str) -> str:
    """Calculate path for checkpoints."""
    return f"/Volumes/{catalog}/{schema}/checkpoint_locations/{table}/"


def optimize_sources(*, spark_session: SparkSession, silver_catalog: str) -> None:
    """Optimize the source tables."""
    for cam in CAMERAS:
        DeltaTable.forName(
            spark_session, CAM_FRAMES_SCHEMA_MAPPING[cam].full_table_name(silver_catalog)
        ).optimize().executeCompaction()


def analyze_sources(*, spark_session: SparkSession, silver_catalog: str) -> None:
    """Compute statistics on the source tables."""
    for cam in CAMERAS:
        spark_session.sql(
            f"""
        ANALYZE {CAM_FRAMES_SCHEMA_MAPPING[cam].full_table_name(silver_catalog)}
        COMPUTE STATISTICS FOR COLUMNS
        vin, recording_started_at, emitted_at
        """
        )


def cam_df(*, spark_session: SparkSession, silver_catalog: str, cam: str) -> DataFrame:
    """Get the camera dataframe."""
    table_name_fqn = CAM_FRAMES_SCHEMA_MAPPING[cam].full_table_name(silver_catalog)
    return spark_session.table(table_name_fqn).select(
        "vin",
        "recording_started_at",
        "emitted_at",
        "distorted_rgb_file_hash",
        "distorted_rgb_file_url",
        "label_image_file_hash",
        "label_image_file_url",
        "frame_number",
        "context",
        "rectification__warper_version",
        "rectification__calstorage_uid",
        "extractor_version",
    )


def build_multisensor_frames_content(
    *,
    fc1_df: DataFrame,
    tv_front_df: DataFrame,
    tv_right_df: DataFrame,
    tv_rear_df: DataFrame,
    tv_left_df: DataFrame,
    rcw_df: DataFrame,
) -> DataFrame:
    """Build the content for the multisensor_frames table."""
    fc1 = fc1_df.alias("fc1")
    tv_front = tv_front_df.hint("range_join", 0.03).alias("tv_front")
    tv_right = tv_right_df.hint("range_join", 0.03).alias("tv_right")
    tv_rear = tv_rear_df.hint("range_join", 0.03).alias("tv_rear")
    tv_left = tv_left_df.hint("range_join", 0.03).alias("tv_left")
    rcw = rcw_df.hint("range_join", 0.03).alias("rcw")
    match_window_size = make_interval(secs=expr("0.003"))
    result = fc1
    for df, df_name in [
        (tv_front, "tv_front"),
        (tv_right, "tv_right"),
        (tv_rear, "tv_rear"),
        (tv_left, "tv_left"),
        (rcw, "rcw"),
    ]:
        result = result.join(
            df,
            [
                col("fc1.vin") == col(f"{df_name}.vin"),
                col("fc1.recording_started_at") == col(f"{df_name}.recording_started_at"),
                col("fc1.emitted_at").between(
                    col(f"{df_name}.emitted_at") - match_window_size, col(f"{df_name}.emitted_at") + match_window_size
                ),
            ],
            "left",
        )
    cam_cols = []
    for cam in CAMERAS:
        cam_cols += [
            col(f"{cam}.emitted_at").alias(f"{cam}_emitted_at"),
            col(f"{cam}.distorted_rgb_file_hash").alias(f"{cam}_distorted_rgb_file_hash"),
            col(f"{cam}.distorted_rgb_file_url").alias(f"{cam}_distorted_rgb_file_url"),
            col(f"{cam}.label_image_file_hash").alias(f"{cam}_label_image_file_hash"),
            col(f"{cam}.label_image_file_url").alias(f"{cam}_label_image_file_url"),
            col(f"{cam}.frame_number").alias(f"{cam}_frame_number"),
            col(f"{cam}.context").alias(f"{cam}_context"),
            col(f"{cam}.rectification__warper_version").alias(f"{cam}_rectification_warper_version"),
            col(f"{cam}.rectification__calstorage_uid").alias(f"{cam}_rectification_calstorage_uid"),
            col(f"{cam}.extractor_version").alias(f"{cam}_extractor_version"),
        ]
    return result.select(
        "fc1.vin",
        "fc1.recording_started_at",
        least(
            col("fc1.emitted_at"),
            col("tv_front.emitted_at"),
            col("tv_right.emitted_at"),
            col("tv_rear.emitted_at"),
            col("tv_left.emitted_at"),
        ).alias("emitted_at_min"),
        greatest(
            col("fc1.emitted_at"),
            col("tv_front.emitted_at"),
            col("tv_right.emitted_at"),
            col("tv_rear.emitted_at"),
            col("tv_left.emitted_at"),
        ).alias("emitted_at_max"),
        *cam_cols,
    )


def create_table_multisensor_frames(
    *, spark_session: SparkSession, silver_catalog: str, target_catalog: str
) -> None:  # pragma: no cover
    """Create the table for the multisensor frames."""

    table_name_fqn = MULTISENSOR_FRAMES_CAMERA_SCHEMA.full_table_name(target_catalog)
    make_table_if_not_exists(
        spark_session=spark_session, table=MULTISENSOR_FRAMES_CAMERA_SCHEMA, target_catalog=target_catalog
    )

    content = build_multisensor_frames_content(
        fc1_df=cam_df(spark_session=spark_session, silver_catalog=silver_catalog, cam="fc1"),
        tv_front_df=cam_df(spark_session=spark_session, silver_catalog=silver_catalog, cam="tv_front"),
        tv_right_df=cam_df(spark_session=spark_session, silver_catalog=silver_catalog, cam="tv_right"),
        tv_rear_df=cam_df(spark_session=spark_session, silver_catalog=silver_catalog, cam="tv_rear"),
        tv_left_df=cam_df(spark_session=spark_session, silver_catalog=silver_catalog, cam="tv_left"),
        rcw_df=cam_df(spark_session=spark_session, silver_catalog=silver_catalog, cam="rcw"),
    )
    # let's watch the plan to see if range joins are used.
    content.explain()
    content.write.insertInto(table_name_fqn, overwrite=True)


def parse_job_parameters() -> argparse.Namespace:  # pragma: no cover
    """Parse job parameters from command line args."""
    parser = argparse.ArgumentParser()
    parser.add_argument("-j", "--silver_catalog", required=True, type=str)
    parser.add_argument("-t", "--target_catalog", required=True, type=str)
    args, _ = parser.parse_known_args()
    return args


def main() -> None:  # pragma: no cover
    """Main function for building the final multisensor frames tables."""
    args = parse_job_parameters()
    try:

        spark_session = SparkSession.builder.getOrCreate()
        optimize_sources(spark_session=spark_session, silver_catalog=args.silver_catalog)
        # analyze_sources(spark_session=spark_session, target_catalog=args.target_catalog)
        logger.info("creating multisensor frames table")
        create_table_multisensor_frames(
            spark_session=spark_session, silver_catalog=args.silver_catalog, target_catalog=args.target_catalog
        )
    except Exception:
        logger.error("Failed to create multisensor frames table", exc_info=True)
        raise


if __name__ == "__main__":
    main()
