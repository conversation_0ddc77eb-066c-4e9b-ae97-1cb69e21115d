"""Shared constants and code for the multisensor frame jobs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from typing import Final

from dbx_schemas.common.functions import Table
from dbx_schemas.silver.drive_time_series import (
    DSP_DE_IMAGE_CAMERA_FC1_SCHEMA,
    DSP_DE_IMAGE_CAMERA_RCW_SCHEMA,
    DSP_DE_IMAGE_CAMERA_TV_FRONT_SCHEMA,
    DSP_DE_IMAGE_CAMERA_TV_LEFT_SCHEMA,
    DSP_DE_IMAGE_CAMERA_TV_REAR_SCHEMA,
    DSP_DE_IMAGE_CAMERA_TV_RIGHT_SCHEMA,
    FILES_IMAGES_FC1_SCHEMA,
    FILES_IMAGES_RCW_SCHEMA,
    FILES_IMAGES_TV_FRONT_SCHEMA,
    FILES_IMAGES_TV_LEFT_SCHEMA,
    FILES_IMAGES_TV_REAR_SCHEMA,
    FILES_IMAGES_TV_RIGHT_SCHEMA,
)
from dbx_schemas.silver.drive_time_series._cam_frames import (
    _CAM_FRAMES_FC1_SCHEMA,
    _CAM_FRAMES_RCW_SCHEMA,
    _CAM_FRAMES_TV_FRONT_SCHEMA,
    _CAM_FRAMES_TV_LEFT_SCHEMA,
    _CAM_FRAMES_TV_REAR_SCHEMA,
    _CAM_FRAMES_TV_RIGHT_SCHEMA,
)
from dbx_schemas.silver.drive_time_series._file_entries import (
    _FILE_ENTRIES_FC1_SCHEMA,
    _FILE_ENTRIES_RCW_SCHEMA,
    _FILE_ENTRIES_TV_FRONT_SCHEMA,
    _FILE_ENTRIES_TV_LEFT_SCHEMA,
    _FILE_ENTRIES_TV_REAR_SCHEMA,
    _FILE_ENTRIES_TV_RIGHT_SCHEMA,
)
from dbx_schemas.utils import apply_all_tags_and_comments
from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession

logging.basicConfig()
logger = logging.getLogger(__name__)
CAMERAS: Final = ["fc1", "tv_front", "tv_right", "tv_rear", "tv_left", "rcw"]

CAMERA_NAME_MAPPING: Final = {
    "fc1": "FC1",
    "tv_front": "TVfront",
    "tv_right": "TVright",
    "tv_rear": "TVrear",
    "tv_left": "TVleft",
    "rcw": "RCW",
}
FILES_IMAGES_SCHEMA_MAPPING: Final[dict[str, Table]] = {
    "fc1": FILES_IMAGES_FC1_SCHEMA,
    "tv_front": FILES_IMAGES_TV_FRONT_SCHEMA,
    "tv_right": FILES_IMAGES_TV_RIGHT_SCHEMA,
    "tv_rear": FILES_IMAGES_TV_REAR_SCHEMA,
    "tv_left": FILES_IMAGES_TV_LEFT_SCHEMA,
    "rcw": FILES_IMAGES_RCW_SCHEMA,
}

FILE_ENTRIES_SCHEMA_MAPPING: Final[dict[str, Table]] = {
    "fc1": _FILE_ENTRIES_FC1_SCHEMA,
    "tv_front": _FILE_ENTRIES_TV_FRONT_SCHEMA,
    "tv_right": _FILE_ENTRIES_TV_RIGHT_SCHEMA,
    "tv_rear": _FILE_ENTRIES_TV_REAR_SCHEMA,
    "tv_left": _FILE_ENTRIES_TV_LEFT_SCHEMA,
    "rcw": _FILE_ENTRIES_RCW_SCHEMA,
}
DSP_DE_IMAGE_SCHEMA_MAPPING: Final[dict[str, Table]] = {
    "fc1": DSP_DE_IMAGE_CAMERA_FC1_SCHEMA,
    "tv_front": DSP_DE_IMAGE_CAMERA_TV_FRONT_SCHEMA,
    "tv_right": DSP_DE_IMAGE_CAMERA_TV_RIGHT_SCHEMA,
    "tv_rear": DSP_DE_IMAGE_CAMERA_TV_REAR_SCHEMA,
    "tv_left": DSP_DE_IMAGE_CAMERA_TV_LEFT_SCHEMA,
    "rcw": DSP_DE_IMAGE_CAMERA_RCW_SCHEMA,
}
CAM_FRAMES_SCHEMA_MAPPING: Final[dict[str, Table]] = {
    "fc1": _CAM_FRAMES_FC1_SCHEMA,
    "tv_front": _CAM_FRAMES_TV_FRONT_SCHEMA,
    "tv_right": _CAM_FRAMES_TV_RIGHT_SCHEMA,
    "tv_rear": _CAM_FRAMES_TV_REAR_SCHEMA,
    "tv_left": _CAM_FRAMES_TV_LEFT_SCHEMA,
    "rcw": _CAM_FRAMES_RCW_SCHEMA,
}


def make_table_if_not_exists(spark_session: SparkSession, table: Table, target_catalog: str) -> None:
    """Create the table if it does not exist and apply all tags and comments."""
    logger.info(
        "Creating table %s with schema %s", table.full_table_name(target_catalog), table.as_spark_schema().json()
    )
    (
        DeltaTable.createIfNotExists(spark_session)
        .tableName(table.full_table_name(target_catalog))
        .addColumns(table.as_spark_schema())
        .clusterBy(table.cluster_by)
        .property("delta.enableChangeDataFeed", "true")
        .property("delta.enableDeletionVectors", "true")
        .property("delta.feature.deletionVectors", "supported")
        .execute()
    )
    apply_all_tags_and_comments(spark_session=spark_session, uc_catalog=target_catalog, table=table)


def merge_file_entries(spark_session: SparkSession, batch_df: DataFrame, table_fqn: str) -> None:  # pragma: no cover
    """Merge the file entries dataframe into the target table."""
    target_table: DeltaTable = DeltaTable.forName(spark_session, table_fqn)
    (
        target_table.alias("target")
        .merge(batch_df.alias("source").dropDuplicates(["file_hash"]), "target.file_hash=source.file_hash")
        .whenMatchedUpdateAll()
        .whenNotMatchedInsertAll()
        .execute()
    )
