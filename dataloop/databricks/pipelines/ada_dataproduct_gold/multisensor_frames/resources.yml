resources:
  jobs:
    multisensor_frames_prep:
      # Give permission to Unicorn developers to manage run
      name: "[Analytics Platform] Multisensor Frames Preparation"
      max_concurrent_runs: 1
      queue:
        enabled: false
      run_as:
        service_principal_name: ${var.run_sp}
      tags:
        responsible_domain: "Data Delivery"
        responsible_team: "Analytics Platform"
        product: "drive-time-series"
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
        no_alert_for_skipped_runs: true
      schedule:
        quartz_cron_expression: 0 0 0/3 ? * * *
        timezone_id: Europe/Berlin
        pause_status: "${var.schedule_pause_status}"
      timeout_seconds: 14400 # 4 hours
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 7200 # 2 hours
      tasks:
        - task_key: prepare_multisensor_frames_preprocessing
          libraries:
            - whl: ./dist/*.whl
          python_wheel_task:
            entry_point: prepare
            package_name: multisensor_frames
            parameters:
              - --silver_catalog
              - ${var.silver_catalog}
              - --target_catalog
              - ${var.target_silver_catalog}
          job_cluster_key: multisensor_job_cluster
      job_clusters:
        - job_cluster_key: multisensor_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            num_workers: ${var.num_workers}
    multisensor_frames:
      # Give permission to Unicorn developers to manage run
      name: "[Analytics Platform] Multisensor Frames"
      max_concurrent_runs: 1
      queue:
        enabled: false
      run_as:
        service_principal_name: ${var.run_sp}
      tags:
        responsible_domain: "Data Delivery"
        responsible_team: "Analytics Platform"
        product: "drive-time-series"
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
        no_alert_for_skipped_runs: true
      schedule:
        quartz_cron_expression: 0 5 0/3 ? * * *
        timezone_id: Europe/Berlin
        pause_status: "${var.schedule_pause_status}"
      timeout_seconds: 3600 # 1 hours
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 1800 # 30 minutes
      tasks:
        - task_key: multisensor_frames
          libraries:
            - whl: ./dist/*.whl
          python_wheel_task:
            entry_point: multisensor_frames
            package_name: multisensor_frames
            parameters:
              - --silver_catalog
              - ${var.target_silver_catalog}
              - --target_catalog
              - ${var.target_gold_catalog}
          job_cluster_key: multisensor_job_cluster
      job_clusters:
        - job_cluster_key: multisensor_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            num_workers: ${var.num_workers}
