"""Tests for the multisensor frames shared functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import MagicMock

from dbx_schemas.gold.drive_time_series import MULTISENSOR_FRAMES_CAMERA_SCHEMA
from multisensor_frames.multisensor_shared import make_table_if_not_exists


def test_make_table_if_not_exists() -> None:
    """Test the _make_table_if_not_exists function."""
    spark_session = MagicMock()
    make_table_if_not_exists(spark_session=spark_session, table=MULTISENSOR_FRAMES_CAMERA_SCHEMA, target_catalog="gold")
    spark_session._sc._jvm.io.delta.tables.DeltaTable.createIfNotExists.assert_called()
    # once for comment per field, once for tags per field with timedomain tags, once for table tags.
    assert spark_session.sql.call_count == len(MULTISENSOR_FRAMES_CAMERA_SCHEMA) + 9 + 1
