"""Tests for the multisensor frames table creation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Final

from conftest import assert_schema_match, ts
from dbx_schemas.gold.drive_time_series import MULTISENSOR_FRAMES_CAMERA_SCHEMA
from multisensor_frames.create_table_multisensor_frames import build_multisensor_frames_content
from multisensor_frames.multisensor_shared import CAM_FRAMES_SCHEMA_MAPPING
from pyspark.sql import SparkSession

T1: Final = ts("2024-01-01T00:00:00")
T2: Final = ts("2024-02-01T00:00:00")
T3: Final = ts("2024-03-01T00:00:00")
T4: Final = ts("2024-04-01T00:00:00")


def test_build_multisensor_frames(spark_session: SparkSession) -> None:
    """Test that the multisensor frames tables gets built correctly."""
    cam_frames_fc1 = spark_session.createDataFrame(
        [("vin", T1, T2, "hash", "url", "hash", "url", 1, "context", "version", "uid", "version")],
        schema=CAM_FRAMES_SCHEMA_MAPPING["fc1"].as_spark_schema(),
    )

    result = build_multisensor_frames_content(
        fc1_df=cam_frames_fc1,
        tv_left_df=cam_frames_fc1,
        tv_rear_df=cam_frames_fc1,
        tv_front_df=cam_frames_fc1,
        tv_right_df=cam_frames_fc1,
        rcw_df=cam_frames_fc1,
    )
    assert_schema_match(MULTISENSOR_FRAMES_CAMERA_SCHEMA.as_spark_schema(), result.schema)
