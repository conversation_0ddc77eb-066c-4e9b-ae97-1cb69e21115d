"""Fixtures."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

# flake8: noqa: E501

import logging
import tempfile
from datetime import datetime
from pathlib import Path
from typing import Any, Generator

import pytest
from delta import configure_spark_with_delta_pip
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType
from pytest import TempPathFactory


@pytest.fixture(scope="session")
def spark_session(tmp_path_factory: TempPathFactory) -> Generator[SparkSession, None, None]:
    """Fixture to create a SparkSession."""

    logger = logging.getLogger("py4j")
    logger.setLevel(logging.WARN)

    builder = (
        SparkSession.builder.master("local[2]")
        .appName("pytest-pyspark-local-testing")
        .config("spark.driver.host", "127.0.0.1")
        .config("spark.driver.bindAddress", "127.0.0.1")
        .config("spark.sql.extensions", "io.delta.sql.DeltaSparkSessionExtension")
        .config("spark.sql.catalog.spark_catalog", "org.apache.spark.sql.delta.catalog.DeltaCatalog")
        .config("spark.sql.legacy.createHiveTableByDefault", False)
        .config("spark.sql.warehouse.dir", Path(tmp_path_factory.mktemp("spark-warehouse")))
    )
    spark = configure_spark_with_delta_pip(builder).getOrCreate()
    yield spark
    spark.stop()


def ts(s: str) -> datetime:
    """Convert string to timestamp."""
    return datetime.fromisoformat(s)


def assert_schema_match(expected: StructType, actual: StructType) -> None:
    """Asserts that the schema of a and b are a match by comparing their field names and data types."""
    assert len(expected) == len(
        actual
    ), f"Number of fields does not match. Expected: {len(expected)}, got: {len(actual)}"
    for a_field in expected:
        assert a_field.name == actual[a_field.name].name
        assert (
            a_field.dataType == actual[a_field.name].dataType
        ), f"type of expected {a_field.name}:{a_field.dataType} does not match actual {actual[a_field.name].dataType}"
