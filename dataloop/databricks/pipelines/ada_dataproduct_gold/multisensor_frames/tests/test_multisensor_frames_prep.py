"""Tests for the preparation job."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Final

import pytest
from conftest import assert_schema_match, ts
from dbx_schemas.silver.ada_ontology import FILES_SCENES_SCHEMA
from dbx_schemas.silver.drive_time_series._cam_frames import _CAM_FRAMES_FC1_SCHEMA
from dbx_schemas.silver.drive_time_series._file_entries import _FILE_ENTRIES_RCW_SCHEMA
from dbx_schemas.silver.drive_time_series.dsp_de_image_cameras import DSP_DE_IMAGE_CAMERA_RCW_SCHEMA
from dbx_schemas.silver.drive_time_series.files_images import FILES_IMAGES_FC1_SCHEMA
from multisensor_frames.create_table_multisensor_frames_prep import build_cam_table_content, build_files_images_content
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType

T1: Final = ts("2024-01-01T00:00:00")
T2: Final = ts("2024-02-01T00:00:00")
T3: Final = ts("2024-03-01T00:00:00")
T4: Final = ts("2024-04-01T00:00:00")
T5: Final = ts("2024-04-01T00:00:00")
T6: Final = ts("2024-04-01T00:00:00")
T7: Final = ts("2024-04-01T00:00:00")


def test_build_cam_table_content(spark_session: SparkSession) -> None:
    """Test the build_cam_table_content function."""
    files_images = spark_session.createDataFrame(
        [
            (
                "vin",
                T1,
                "file_hash1",
                T2,
                T3,
                "split_hash1",
                "stream_hash1",
                "Distorted",
                "view_type",
                1,
                T4,
                "context",
                "rwv1",
                "rcu1",
                "ev1",
                "file_url1",
            ),
            (
                "vin",
                T1,
                "file_hash2",
                T5,
                T6,
                "split_hash2",
                "stream_hash2",
                "Label",
                "view_type",
                1,
                T4,
                "context",
                "rwv2",
                "rcu2",
                "ev2",
                "file_url2",
            ),
        ],
        schema=StructType(FILES_IMAGES_FC1_SCHEMA.fields),
    )
    result = build_cam_table_content(files_images)
    assert_schema_match(result.schema, _CAM_FRAMES_FC1_SCHEMA)


@pytest.fixture
def dsp_de_image(spark_session: SparkSession) -> DataFrame:
    """Fixture for the DSP DE image DataFrame."""
    return spark_session.createDataFrame(
        [
            (
                T1,
                T2,
                T3,
                "file_hash1",
                "split_hash1",
                "stream_hash1",
                "Distorted",
                "view_type",
                1,
                "A",
                "rwv1",
                "rcu1",
                "ev1",
            ),
        ],
        DSP_DE_IMAGE_CAMERA_RCW_SCHEMA.as_spark_schema(),
    )


@pytest.fixture
def files_scenes(spark_session: SparkSession) -> DataFrame:
    """Fixture for the files scenes DataFrame."""
    return spark_session.createDataFrame(
        [
            ("vin", T1, T2, T3, "split_file_url2", 123, "split_hash1", T4),
        ],
        FILES_SCENES_SCHEMA.as_spark_schema(),
    )


@pytest.fixture
def file_entries(spark_session: SparkSession) -> DataFrame:
    """Fixture for the file entries DataFrame."""
    return spark_session.createDataFrame(
        [
            ("file_hash1", "file_url1"),
        ],
        _FILE_ENTRIES_RCW_SCHEMA.as_spark_schema(),
    )


def test_build_files_images_content(
    spark_session: SparkSession, dsp_de_image: DataFrame, files_scenes: DataFrame, file_entries: DataFrame
) -> None:
    """Test the build_files_images_content function."""
    dsp_de_image.show(1)
    files_scenes.show(1)
    file_entries.show(1)
    result = build_files_images_content(
        dsp_de_image=dsp_de_image, files_scenes=files_scenes, file_entries=file_entries
    ).cache()
    assert_schema_match(result.schema, FILES_IMAGES_FC1_SCHEMA.as_spark_schema())
    assert result.count() == 1
