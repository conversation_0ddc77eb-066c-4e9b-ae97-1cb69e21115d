"""Tests for the Multisensor Frames Camera Schema."""

from dbx_schemas.gold.drive_time_series import MULTISENSOR_FRAMES_CAMERA_SCHEMA

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


def test_multisensor_frames_camera_table() -> None:
    """Test the schema of the multisensor_frames_camera table."""
    assert MULTISENSOR_FRAMES_CAMERA_SCHEMA["vin"].nullable is False
