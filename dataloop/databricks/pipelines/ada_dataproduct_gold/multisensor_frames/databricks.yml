# This is a Databricks asset bundle definition.
# Notes:
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.
bundle:
  name: multisensor_frames
permissions:
  - group_name: "sg-pace-github-Analytics_Platform-developer"
    level: CAN_MANAGE

include:
  - variables.yml
  - resources.yml

artifacts:
  default:
    type: whl
    build: uv build
    path: .

targets:
  user-dev:
    variables:
      env: dev
      silver_catalog: silver
      target_silver_catalog: dd_unicorn_sbx
      target_gold_catalog: dd_unicorn_sbx
      num_workers: 2
    mode: development
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/

  dev:
    variables:
      env: dev
      silver_catalog: silver
      target_silver_catalog: silver_dev
      target_gold_catalog: gold_dev
      num_workers: 8
    mode: production
    default: false
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_gold/multisensor_frames/${bundle.name}

  qa:
    variables:
      env: qa
      silver_catalog: silver_qa
      target_silver_catalog: silver_qa
      target_gold_catalog: gold_qa
      num_workers: 4
    mode: production
    default: false
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_gold/multisensor_frames/${bundle.name}

  prod:
    variables:
      env: prod
      silver_catalog: silver
      target_silver_catalog: silver
      target_gold_catalog: gold
      target_schema: drive_time_series
      num_workers: 4
      schedule_pause_status: UNPAUSED
    mode: production
    default: false
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_gold/multisensor_frames/${bundle.name}
