variables:
  run_sp:
    default: 011157e5-a1ca-4053-a240-430e618a6b3a
    description: "run service principal"
  env:
    default: dev
    description: "environment, dev, qa, prod"
  pause_status:
    description: Is pipeline "PAUSED"/"UNPAUSED"
    default: "UNPAUSED"

resources:
  jobs:
    azureml_raw:
      # Give permission to all users to manage run
      permissions:
      - group_name: users
        level: CAN_MANAGE_RUN

      name: Azure ML training datasets gold metadata table.

      schedule:
        quartz_cron_expression: '0 17 1 * * ?'
        timezone_id: UTC
        pause_status: ${var.pause_status}

      tasks:
        - task_key: azureml_training_datatsets_metadata
          spark_python_task:
            python_file: ../training_datasets_gold_metadata.py
            parameters:
              - -e
              - ${var.env}
          job_cluster_key: azureml_job_cluster

      job_clusters:
        - job_cluster_key: azureml_job_cluster
          new_cluster:
            spark_version: 15.4.x-scala2.12
            autoscale:
              min_workers: 1
              max_workers: 2

            # attributes define non-spot instance
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1

            # large compute node
            node_type_id: Standard_E8ads_v5
            driver_node_type_id: Standard_E4ads_v5
            enable_elastic_disk: true
            init_scripts:
            data_security_mode: USER_ISOLATION
            runtime_engine: PHOTON
