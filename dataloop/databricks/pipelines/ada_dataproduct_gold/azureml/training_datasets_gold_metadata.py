"""Pipeline to generate training dataset table enriched with metadata."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging

from pyspark.sql import DataFrame
from pyspark.sql.functions import max as max_  # To not interfere with built-in max() function

LOGGER = logging.getLogger(__name__)


def _get_verified_columns_to_keep(
    table_1: DataFrame, table_2: DataFrame, expected_overlapping_columns: list[str]
) -> list[str]:
    """Get the columns from table_2 that are not in table_1 and verify that the overlapping columns are as expected.

    Args:
        table_1: DataFrame of the first table.
        table_2: DataFrame of the second table.
        expected_overlapping_columns: The columns that are expected to overlap between table_1 and table_2.

    Returns:
        The columns from table_2 that are not in table_1.
    """
    # Calculate the columns from table_2, that are not in table_1
    table_2_columns_to_keep = [el for el in table_2.columns if el not in table_1.columns]

    # Verify that the overlapping columns are the expected ones
    if sorted(table_2.columns) != sorted(table_2_columns_to_keep + expected_overlapping_columns):
        overlapping_columns = [el for el in table_2.columns if el in table_1.columns]
        msg = f"Overlapping columns not as expected: {overlapping_columns=} vs. {expected_overlapping_columns=}"
        raise ValueError(msg)

    return table_2_columns_to_keep


def _filter_latest_datasets(training_datasets: DataFrame) -> DataFrame:
    """Get only the latest dataset versions for every dataset_name.

    Args:
        training_datasets: Training dataset containing history of all dataset versions.

    Returns:
        Filtered DataFrame with latest dataset versions.
    """
    # Create a DataFrame with the latest version for each dataset
    latest_versions = training_datasets.groupBy("dataset_name").agg(max_("dataset_version").alias("dataset_version"))

    # Join the original DataFrame with the latest versions DataFrame
    latest_training_datasets = training_datasets.join(
        latest_versions, on=["dataset_name", "dataset_version"], how="inner"
    )

    return latest_training_datasets


def _add_metadata(
    latest_training_datasets: DataFrame,
    metadata: DataFrame,
    metadata_columns_to_keep: list[str],
) -> DataFrame:
    """Add metadata to training dataset table.

    The left join between the training and metadata datasets is performed on the "image_id" column.

    Args:
        latest_training_datasets: DataFrame containing training data with latest dataset versions.
        metadata: The metadata DataFrame.
        metadata_columns_to_keep: The list of metadata columns to keep.
    """
    # Select the columns to keep from the metadata table
    metadata_table_columns_to_keep = metadata.select(["image_id"] + metadata_columns_to_keep)

    # Perform the left join
    return latest_training_datasets.join(metadata_table_columns_to_keep, on="image_id", how="left")


def main() -> None:
    """Run dataset registration."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    args = parser.parse_args()

    silver_catalog = {
        "dev": "silver_dev",
        "qa": "silver_qa",
        "prod": "silver",
    }

    gold_catalog = {
        "dev": "gold_dev",
        "qa": "gold_qa",
        "prod": "gold",
    }

    spark.conf.set("spark.sql.cbo.enabled", True)
    spark.conf.set("spark.sql.cbo.joinReorder.enabled", True)

    training_datasets_table = silver_catalog[args.env] + ".azureml.training_datasets_v2"
    metadata_table = silver_catalog[args.env] + ".mdd.viper_frame_mdd_metadata"
    target_table = gold_catalog[args.env] + ".azureml.training_datasets_latest_metadata_v2"

    # Load DataFrame
    training_datasets = spark.table(training_datasets_table)
    metadata = spark.table(metadata_table)

    # Filter training datasets to latest dataset versions
    latest_training_datasets = _filter_latest_datasets(training_datasets)
    # Persist the filtered datasets to ensure the filtering is executed and materialized
    # The persisted DataFrame is much smaller and will be used in the subsequent steps
    latest_training_datasets = latest_training_datasets.cache()

    # In order to avoid unexpected outcomes for future new, we define and verify the expected overlapping columns
    # The join will be done on the image_id column. Other overlapping columns will be ignored in the metadata table
    expected_overlapping_columns = ["image_id", "split_hash"]
    metadata_columns_to_keep = _get_verified_columns_to_keep(training_datasets, metadata, expected_overlapping_columns)
    training_datasets_metadata = _add_metadata(
        latest_training_datasets,
        metadata,
        metadata_columns_to_keep,
    )

    # Set target table description
    target_table_description = f"""This table merges `{training_datasets_table}` and `{metadata_table}`. It
                                enriches the latest version of training datasets with high-quality metadata from
                                namespaces like time_of_day, mapinfo, etc. using a left join on image_id."""

    # Write the training dataset joined with metadata to the target table
    training_datasets_metadata.write.mode("overwrite").saveAsTable(target_table, comment=target_table_description)

    # Alter the table to add comments to the columns
    # No pyspark API for adding comments to columns. Hence, using SQL query.
    columns_comments = {
        "split_hash": "Extracted from the dsp_de_image table",
        "image_id": "Constructed from file name and frame number",
        "input_data_sha": "SHA of the source frame where the input data frames are derived from",
        "split": "Split type of the training data (train, val, test)",
        "input_data": "Dict with input data column(s) and path(s) to the frame",
        "task": "Associated task (e.g. lane, vehicle, vru, traffic_light, etc.)",
        "label_path": "Path to the label relative to the ingest_cache blobstore",
        "dataset_name": "Name of the dataset (e.g. PaceGeneralYUV)",
        "dataset_version": "Version of the dataset",
        "project": "Name of the project (e.g. ALLIANCE)",
        "stream_hash": "Sha of the stream",
        "recorded_at": "Timestamp of the recording",
        "recorded_at_unix": "Unix timestamp of the recording",
        "iso_country_code_alpha2": "Two-letter country code (e.g. DE) from mapinfo namespace with closest timestamp to the frame",
        "gps__elevation": "Elevation at GPS location derived from the dsp_de_gps namespace with closest timestamp to the frame",
        "gps__longitude": "Longitude coordinate of GPS location derived from the dsp_de_gps namespace with closest timestamp to the frame",
        "gps__latitude": "Latitude coordinate of GPS location derived from the dsp_de_gps namespace with closest timestamp to the frame",
        "time_of_day": "Time of day (e.g. Day, Night, etc.) derived from the time_of_day namespace",
    }

    for column, comment in columns_comments.items():
        spark.sql(f"ALTER TABLE {target_table} ALTER COLUMN {column} COMMENT '{comment}'")

    # Add tags to assign table responsibilities
    spark.sql(
        f"ALTER TABLE {target_table} SET TAGS ('responsible_domain'='Visual Perception','responsible_team'='Model Performance Evaluation', 'refresh_interval'='P1D')"
    )
    spark.catalog.refreshTable(target_table)

    LOGGER.info(f"Optimizing table: {target_table}")
    spark.sql(f"ALTER TABLE {target_table} CLUSTER BY (dataset_name, dataset_version, image_id)")
    spark.sql(f"OPTIMIZE {target_table}")


if __name__ == "__main__":
    main()
