"""This module contains the CDFStream class, which is used to read and write data from and to CDF streams."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from abc import ABC, abstractmethod
from pathlib import Path

from delta import DeltaTable
from delta.tables import ColumnMapping
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, row_number
from pyspark.sql.types import StructType
from pyspark.sql.window import Window
from utils.helper import get_or_create_databricks_session

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class BaseDeltaStreamingTable(ABC):
    """Base class for managing Delta streaming tables.

    Args:
        target_table_name (str): Name of the target table.
        full_streaming_table_name (str): Name of the full streaming table.
        merge_condition (str): Condition for merging data.
        env (str): Environment (e.g., 'dev', 'qa', 'prod').
        catalog (str): Catalog name.
        schema (str): Schema name.
        target_table_primary_key (str, optional): Primary key of the target table. Defaults to "image_id".
        stream_table_primary_key (str, optional): Primary key of the stream table. Defaults to "image_id".
        overwrite (bool, optional): Whether to overwrite the table. Defaults to False.
    """

    def __init__(
        self,
        target_table_name: str,
        streaming_table_name: str,
        merge_condition: str,
        env: str,
        catalog: str,
        schema: str,
        target_table_primary_key: str = "image_id",
        stream_table_primary_key: str = "image_id",
        *,
        overwrite: bool = False,
    ):
        """Init of BaseDeltaStreamingTable.

        Args:
             target_table_name: Name of the target table.
             streaming_table_name: Name of the full streaming table.
             merge_condition: Condition for merging data.
             env: Environment (e.g., 'dev', 'qa', 'prod').
             catalog: Catalog name (e.g., 'gold_dev', 'gold_qa', 'gold').
             schema: Schema name (e.g., 'viper_environment').
             target_table_primary_key: Primary key of the target table. Defaults to "image_id".
             stream_table_primary_key: Primary key of the stream table. Defaults to "image_id".
             overwrite: Whether to overwrite the table. Defaults to False.
        """
        self.table_name = target_table_name
        self.catalog = catalog
        self.env = env
        self.schema = schema
        self.overwrite = overwrite
        self.streaming_table_name = streaming_table_name
        self.merge_condition = merge_condition
        self.target_table_primary_key = target_table_primary_key
        self.stream_table_primary_key = stream_table_primary_key

        self.spark: SparkSession = get_or_create_databricks_session()

    @property
    def target_table_name(self) -> str:
        """Returns the fully qualified name of the target table."""
        return f"{self.catalog}.{self.schema}.{self.table_name}"

    @property
    def full_streaming_table_name(self) -> str:
        """Returns the fully qualified name of the target table."""
        return f"{self.catalog}.{self.schema}.{self.streaming_table_name}"

    @property
    def checkpoint_dir(self) -> Path:
        """Returns the checkpoint directory path."""
        snack_case_table_name = self.table_name.replace(".", "_")
        snack_case_table_name = snack_case_table_name.replace("`", "")
        return Path(f"/Volumes/{self.catalog}/{self.schema}/checkpoint_locations/{snack_case_table_name}")

    def run(self) -> None:
        """Runs the streaming table update process."""
        if self.overwrite:
            self._overwrite_table()
        self.create_output_table_if_not_exists()

        self.update_table()

        self.optimize_table()

    def update_table(self) -> None:
        """Updates the target table with data from the streaming table."""
        table_stream = self.spark.readStream.format("delta").option("readChangeFeed", "true")
        streaming_df = table_stream.table(self.full_streaming_table_name)
        # Currently, we are not interested in previous values of the records
        streaming_df = streaming_df.filter(col("_change_type") != "update_preimage")

        streaming_df = self.transform_data(streaming_df)

        query = (
            streaming_df.writeStream.format("delta")
            .trigger(availableNow=True)
            .outputMode("update")
            .option("checkpointLocation", str(self.checkpoint_dir))
            .foreachBatch(self._merge)
            .start()
        )

        query.awaitTermination()
        query.stop()

    def _merge(self, batch_df: DataFrame, batch_id: int) -> None:
        """Merges the batch DataFrame into the target table."""
        window_spec = Window.partitionBy("image_id").orderBy(col("_commit_version").desc())

        # Add a row number to each row within the partition
        batch_df = batch_df.withColumn("row_num", row_number().over(window_spec))

        # Filter to keep only the latest change for each record
        batch_df = batch_df.filter(col("row_num") == 1)
        merge_column_mapping = self.get_merge_column_mapping()
        merge_operation = (
            DeltaTable.forName(self.spark, self.target_table_name)
            .alias("existing")
            .merge(batch_df.alias("batch"), self.merge_condition)
            .whenMatchedUpdate(col("batch._change_type") != "delete", merge_column_mapping)
            .whenNotMatchedInsert(col("batch._change_type") != "delete", merge_column_mapping)
            .whenMatchedDelete(col("batch._change_type") == "delete")
        )
        merge_operation.execute()

    def create_output_table_if_not_exists(self) -> None:
        """Creates the output table if it does not already exist."""
        if not self.spark.catalog.tableExists(self.target_table_name):
            logger.info("Table does not exist, creating it.")
            df = self.spark.createDataFrame([], self.get_output_table_schema())
            df.write.format("delta").mode("overwrite").option("delta.enableChangeDataFeed", "true").saveAsTable(
                self.target_table_name
            )
            logger.info(f"Table {self.target_table_name} created.")

    def get_merge_column_mapping(self) -> ColumnMapping:
        """Returns the column mapping for the merge operation."""
        output_schema = self.get_output_table_schema()
        return {f"existing.{field.name}": col(f"batch.{field.name}") for field in output_schema.fields}

    def _overwrite_table(self) -> None:
        """Overwrites the target table by deleting the checkpoint and dropping the table."""
        self._delete_checkpoint()
        self._drop_table()

    def _drop_table(self) -> None:
        """Drops the target table if it exists."""
        self.spark.sql(f"DROP TABLE IF EXISTS {self.target_table_name}")
        logger.info(f"Table {self.target_table_name} dropped")

    def _delete_checkpoint(self) -> None:
        """Deletes the checkpoint directory."""
        try:
            from pyspark.dbutils import DBUtils
        except ImportError as e:
            error_msg = "DBUtils is not available in local mode. Please run this code in Databricks."
            raise ImportError(error_msg) from e
        dbutils = DBUtils(self.spark)
        dbutils.fs.rm(str(self.checkpoint_dir), True)
        logger.info(f"Checkpoint directory {self.checkpoint_dir} removed.")

    def optimize_table(self) -> None:
        """Optimizes the target table by executing compaction."""
        logger.info(f"Optimizing table {self.target_table_name}.")
        existing_table = DeltaTable.forName(self.spark, self.target_table_name)
        existing_table.optimize().executeCompaction()

    @abstractmethod
    def transform_data(self, streaming_table_df: DataFrame) -> DataFrame:
        """Abstract method to apply transformations to the source DataFrame.

        Args:
            streaming_table_df (DataFrame): Source DataFrame.
        """
        pass

    @abstractmethod
    def get_output_table_schema(self) -> StructType:
        """Abstract method to define the schema of the output table."""
        pass
