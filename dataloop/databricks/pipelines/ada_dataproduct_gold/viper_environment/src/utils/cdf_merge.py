"""Module to create a CDF table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from abc import ABC, abstractmethod
from typing import Dict, List

from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType
from utils.helper import get_or_create_databricks_session

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class BaseDeltaMerger(ABC):
    """Base class to create a CDF table."""

    def __init__(
        self,
        table_name: str,
        env: str,
        catalog: str,
        schema: str,
        full_source_table_names: List[str],
        merge_condition: str,
        when_matched_update_condition: str,
        *,
        overwrite: bool = False,
    ):
        """Init method.

        Args:
             table_name: Name of the target table.
             env: Environment (e.g., 'dev', 'qa', 'prod').
             catalog: Catalog name (e.g., 'gold_dev', 'gold_qa', 'gold').
             schema: Schema name (e.g., 'viper_environment').
             full_source_table_names: List of full source table names.
             merge_condition: Condition for merging data.
             when_matched_update_condition: Condition for updating data.
             overwrite: Whether to overwrite the table. Defaults to False.
        """
        self.table_name = table_name
        self.catalog = catalog
        self.env = env
        self.schema = schema
        self.overwrite = overwrite
        self.source_tables = full_source_table_names
        self.merge_condition = merge_condition
        self.when_matched_update_condition = when_matched_update_condition

        self.spark: SparkSession = get_or_create_databricks_session()

    @property
    def target_table_name(self) -> str:
        """Returns the full name of the target table."""
        return f"{self.catalog}.{self.schema}.{self.table_name}"

    def create_output_table_if_not_exists(self, transformed_df: DataFrame) -> None:
        """Creates the output Delta table if it does not exist, and merges the transformed DataFrame into it."""
        logger.info("Overwrite flag is set or table does not exist, creating it.")

        schema = self.get_output_table_schema()
        DeltaTable.createOrReplace(self.spark).addColumns(schema).tableName(self.target_table_name).property(
            "delta.enableChangeDataFeed", "true"
        ).execute().alias("existing").merge(transformed_df.alias("new"), self.merge_condition).whenNotMatchedInsert(
            values={f"existing.{col_name}": f"new.{col_name}" for col_name in transformed_df.columns}
        ).execute()
        logger.info(f"Table {self.target_table_name} created.")

    def read_source_tables(self) -> Dict[str, DataFrame]:
        """Reads the source tables into DataFrames."""
        return {
            full_table_name.split(".")[-1]: self.spark.table(full_table_name) for full_table_name in self.source_tables
        }

    def run(self) -> None:
        """Run routine.

        Executes the data merging process, including reading source tables, transforming data,
        and updating or creating the target table.
        """
        source_dfs = self.read_source_tables()
        transformed_df = self.transform_data(**source_dfs)

        if not self.spark.catalog.tableExists(self.target_table_name) or self.overwrite:
            self.create_output_table_if_not_exists(transformed_df)
        else:
            self.update_table(transformed_df)
        self.optimize_table()

    def update_table(self, transformed_df: DataFrame) -> None:
        """Updates the target Delta table by merging the transformed DataFrame into it.

        Args:
            transformed_df (DataFrame): The transformed DataFrame to be merged.
        """
        logger.info(f"Table {self.target_table_name} already exists, updating it.")
        logger.info(f"Starting merge operation of table {self.target_table_name}.")

        DeltaTable.forName(self.spark, self.target_table_name).alias("existing").merge(
            source=transformed_df.alias("new"), condition=self.merge_condition
        ).whenMatchedUpdate(
            condition=self.when_matched_update_condition,
            set={f"existing.{col_name}": f"new.{col_name}" for col_name in transformed_df.columns},
        ).whenNotMatchedInsert(
            values={f"existing.{col_name}": f"new.{col_name}" for col_name in transformed_df.columns},
        ).whenNotMatchedBySourceDelete().execute()

        logger.info(f"Table {self.target_table_name} updated.")

    def optimize_table(self) -> None:
        """Optimizes the target Delta table by executing compaction."""
        logger.info(f"Optimizing table {self.target_table_name}.")
        existing_table = DeltaTable.forName(self.spark, self.target_table_name)
        existing_table.optimize().executeCompaction()

    @abstractmethod
    def transform_data(self, **source_dfs: DataFrame) -> DataFrame:
        """Abstract method to apply transformations to the source DataFrames.

        Args:
            source_dfs (DataFrame): Source DataFrames to be transformed.

        Returns:
            DataFrame: The transformed DataFrame.
        """
        pass

    @abstractmethod
    def get_output_table_schema(self) -> StructType:
        """Abstract method to get the schema of the output table.

        Returns:
            StructType: The schema of the output table.
        """
        pass
