"""Helpers for environment classification tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, expr, regexp_extract, when


def add_usage_column(df: DataFrame, task: str) -> DataFrame:
    """Add usage columns to the dataframe."""

    pattern = rf"{task}_(train|val|test|tiny_val|calibration)_prod"
    # NOTE: only one `_prod` split must be present
    df = df.withColumn(f"split_{task}_prod", expr(f"try_element_at(filter(split_{task}, x -> x rlike '_prod$'), 1)"))

    df = df.withColumn(
        f"usage_{task}",
        when(col(f"split_{task}_prod").isNotNull(), regexp_extract(col(f"split_{task}_prod"), pattern, 1)).otherwise(
            None
        ),
    ).drop(f"split_{task}_prod")
    return df
