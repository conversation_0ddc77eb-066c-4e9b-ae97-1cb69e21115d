"""Module maintaining the table paths needed."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

SILVER_BLOCKAGE_TABLE_NAME = "silver.mdd.viper_blockage_labeled_frames"
SILVER_ENV_CLASS_TABLE_NAME = "silver.mdd.environment_classification_image"
SILVER_MDD_FILE_ENTRIES_TABLE_NAME = "silver.mdd.file_entries"
SILVER_DSP_DE_IMAGE_TABLE_NAME = "silver.mdd.dsp_de_image"
SILVER_DSP_DE_IMAGE_EXTENDED_TABLE_NAME = "silver.mdd.dsp_de_image_extended"

GOLD_ENV_FRAMES_TABLE_NAME = "gold.viper_environment.environment_classification_frames"

LABELER_ID_TO_NAME = {
    "d8172e20-0286-4412-b03e-6ba347c3ec4a": "AAEye",
    "d31d179d-0237-4673-a124-1876b4c90946": "LabelPlant",
    "e1de395a-be65-4adb-ae8f-21e0091dd8cb": "LabelPlantTraning",
}


IMAGE_ID_REGEX = r"([a-zA-Z]+-.+_f\d{5})_.+.png"


YUV_422_EXTRACTOR_VERSION_START = 70_002


BLOCKAGE_FRAME_ATTRIBUTES = [
    "skipConditions",
    "blockageCondition",
    "lightCondition",
    "weatherPrecipitation",
    "atmosphericCondition",
    "roadCondition",
    "sceneDescription",
    "timeOfDay",
]


BLOCKAGE_LABEL_TRANSLATION_LOW = {
    "label_set_name": "blockage_label_set",
    "label_set_version": "0.0.0",
    "rules": {
        "Blockage": {
            "conditions": [{"class_name": "Blockage", "attributes": [{"name": "obscurity", "values": ["Low"]}]}]
        },
        "Unlabeled": {"conditions": [{"class_name": "EgoVehicle"}]},
        "Clean": {"default": True},
    },
}

BLOCKAGE_LABEL_TRANSLATION_MEDIUM = {
    "label_set_name": "blockage_label_set",
    "label_set_version": "0.0.0",
    "rules": {
        "Blockage": {
            "conditions": [{"class_name": "Blockage", "attributes": [{"name": "obscurity", "values": ["Medium"]}]}]
        },
        "Unlabeled": {"conditions": [{"class_name": "EgoVehicle"}]},
        "Clean": {"default": True},
    },
}

BLOCKAGE_LABEL_TRANSLATION_HIGH = {
    "label_set_name": "blockage_label_set",
    "label_set_version": "0.0.0",
    "rules": {
        "Blockage": {
            "conditions": [{"class_name": "Blockage", "attributes": [{"name": "obscurity", "values": ["High"]}]}]
        },
        "Unlabeled": {"conditions": [{"class_name": "EgoVehicle"}]},
        "Clean": {"default": True},
    },
}
