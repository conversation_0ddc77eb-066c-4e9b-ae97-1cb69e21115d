"""Schemas module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import ArrayType, BooleanType, DoubleType, LongType, StringType, StructField, StructType

# Generated by: SELECT schema_of_json_agg(label_file_content) FROM silver_dev.mdd.viper_blockage_labeled_frames
blockage_gaf_schema = StructType(
    [
        StructField(
            "campaign",
            StructType(
                [
                    StructField("createdAt", StringType(), True),
                    StructField("description", StringType(), True),
                    StructField("id", StringType(), True),
                    StructField("labelerGroup", StringType(), True),
                    StructField("maxNumberOfRetries", LongType(), True),
                    StructField("modelId", StringType(), True),
                    StructField(
                        "moduleDefinition",
                        StructType(
                            [
                                StructField(
                                    "Meta",
                                    StructType(
                                        [
                                            StructField("description", StringType(), True),
                                            StructField("hash", StringType(), True),
                                            StructField("module", StringType(), True),
                                            StructField("version", StringType(), True),
                                        ]
                                    ),
                                    True,
                                )
                            ]
                        ),
                        True,
                    ),
                    StructField("name", StringType(), True),
                    StructField("projectCode", StringType(), True),
                    StructField("qaCanEdit", BooleanType(), True),
                    StructField("qaGroup", StringType(), True),
                    StructField("tags", ArrayType(StringType()), True),
                ]
            ),
            True,
        ),
        StructField(
            "export_errors",
            StructType(
                [
                    StructField("missing_modules", ArrayType(StringType()), True),
                    StructField("missing_seqs", ArrayType(StringType()), True),
                ]
            ),
            True,
        ),
        StructField("exported_at", StringType(), True),
        StructField("exported_by", StringType(), True),
        StructField("ga_build_version", StringType(), True),
        StructField("hostname", StringType(), True),
        StructField(
            "job",
            StructType(
                [
                    StructField("approvedAt", StringType(), True),
                    StructField("approvedBy", StringType(), True),
                    StructField("campaignId", StringType(), True),
                    StructField("createdAt", StringType(), True),
                    StructField("id", StringType(), True),
                    StructField("name", StringType(), True),
                    StructField("sequenceId", StringType(), True),
                    StructField(
                        "tasks",
                        ArrayType(
                            StructType(
                                [
                                    StructField("currentAssigneeUserId", StringType(), True),
                                    StructField("currentStatus", StringType(), True),
                                    StructField("description", StringType(), True),
                                    StructField("id", StringType(), True),
                                    StructField("startedAt", StringType(), True),
                                    StructField("timestamp", StringType(), True),
                                    StructField("type", StringType(), True),
                                ]
                            )
                        ),
                        True,
                    ),
                ]
            ),
            True,
        ),
        StructField("jobType", StringType(), True),
        StructField(
            "labels",
            StructType(
                [
                    StructField(
                        "annotationLabels",
                        ArrayType(
                            StructType(
                                [
                                    StructField("alignedObjectInfo", StringType(), True),
                                    StructField(
                                        "attributes",
                                        ArrayType(
                                            StructType(
                                                [
                                                    StructField("name", StringType(), True),
                                                    StructField(
                                                        "values",
                                                        StructType(
                                                            [
                                                                StructField("False", ArrayType(StringType()), True),
                                                                StructField("Full", ArrayType(StringType()), True),
                                                                StructField("High", ArrayType(StringType()), True),
                                                                StructField("Low", ArrayType(StringType()), True),
                                                                StructField("Medium", ArrayType(StringType()), True),
                                                                StructField("NotSet", ArrayType(StringType()), True),
                                                                StructField("True", ArrayType(StringType()), True),
                                                            ]
                                                        ),
                                                        True,
                                                    ),
                                                ]
                                            )
                                        ),
                                        True,
                                    ),
                                    StructField("author", StringType(), True),
                                    StructField("frameNb", LongType(), True),
                                    StructField("globalObjectId", StringType(), True),
                                    StructField("groups", ArrayType(StringType()), True),
                                    StructField("id", StringType(), True),
                                    StructField("initialLabelId", StringType(), True),
                                    StructField("lastEdit", StringType(), True),
                                    StructField("name", StringType(), True),
                                    StructField("objectId", LongType(), True),
                                    StructField(
                                        "shape",
                                        StructType(
                                            [
                                                StructField(
                                                    "Polygon",
                                                    StructType(
                                                        [
                                                            StructField(
                                                                "points",
                                                                ArrayType(
                                                                    StructType(
                                                                        [
                                                                            StructField(
                                                                                "attributes",
                                                                                ArrayType(StringType()),
                                                                                True,
                                                                            ),
                                                                            StructField("x", DoubleType(), True),
                                                                            StructField("y", DoubleType(), True),
                                                                        ]
                                                                    )
                                                                ),
                                                                True,
                                                            )
                                                        ]
                                                    ),
                                                    True,
                                                )
                                            ]
                                        ),
                                        True,
                                    ),
                                    StructField("taskId", StringType(), True),
                                    StructField("zIndex", LongType(), True),
                                ]
                            )
                        ),
                        True,
                    ),
                    StructField(
                        "frameLabels",
                        ArrayType(
                            StructType(
                                [
                                    StructField(
                                        "attributes",
                                        StructType(
                                            [
                                                StructField("name", StringType(), True),
                                                StructField(
                                                    "values",
                                                    StructType(
                                                        [
                                                            StructField("Artificial", ArrayType(StringType()), True),
                                                            StructField("Bridge", ArrayType(StringType()), True),
                                                            StructField("Clean", ArrayType(StringType()), True),
                                                            StructField("Clear", ArrayType(StringType()), True),
                                                            StructField("CommonScene", ArrayType(StringType()), True),
                                                            StructField(
                                                                "ConstructionSite", ArrayType(StringType()), True
                                                            ),
                                                            StructField("Daytime", ArrayType(StringType()), True),
                                                            StructField(
                                                                "DefectiveBlurring", ArrayType(StringType()), True
                                                            ),
                                                            StructField(
                                                                "DefectiveCamera", ArrayType(StringType()), True
                                                            ),
                                                            StructField("DividedRoad", ArrayType(StringType()), True),
                                                            StructField("Dry", ArrayType(StringType()), True),
                                                            StructField("Dust", ArrayType(StringType()), True),
                                                            StructField("Earlymorning", ArrayType(StringType()), True),
                                                            StructField("Evening", ArrayType(StringType()), True),
                                                            StructField("Flooded", ArrayType(StringType()), True),
                                                            StructField("Fog", ArrayType(StringType()), True),
                                                            StructField("FoggedUp", ArrayType(StringType()), True),
                                                            StructField("Glare", ArrayType(StringType()), True),
                                                            StructField("Hail", ArrayType(StringType()), True),
                                                            StructField("HeavyRain", ArrayType(StringType()), True),
                                                            StructField("HeavySnow", ArrayType(StringType()), True),
                                                            StructField("Ice", ArrayType(StringType()), True),
                                                            StructField("Iced", ArrayType(StringType()), True),
                                                            StructField("LightRays", ArrayType(StringType()), True),
                                                            StructField("LowSun", ArrayType(StringType()), True),
                                                            StructField("Moist", ArrayType(StringType()), True),
                                                            StructField("MotionBlur", ArrayType(StringType()), True),
                                                            StructField("Mud", ArrayType(StringType()), True),
                                                            StructField("NaturalLight", ArrayType(StringType()), True),
                                                            StructField("Night", ArrayType(StringType()), True),
                                                            StructField("None", ArrayType(StringType()), True),
                                                            StructField("Other", ArrayType(StringType()), True),
                                                            StructField("Oversaturated", ArrayType(StringType()), True),
                                                            StructField("ParkingGarage", ArrayType(StringType()), True),
                                                            StructField("PartialWet", ArrayType(StringType()), True),
                                                            StructField("Rain", ArrayType(StringType()), True),
                                                            StructField("Reflections", ArrayType(StringType()), True),
                                                            StructField("RoadSpray", ArrayType(StringType()), True),
                                                            StructField("Shadows", ArrayType(StringType()), True),
                                                            StructField("Smoke", ArrayType(StringType()), True),
                                                            StructField("Snow", ArrayType(StringType()), True),
                                                            StructField("SnowPartial", ArrayType(StringType()), True),
                                                            StructField("SnowSlush", ArrayType(StringType()), True),
                                                            StructField(
                                                                "SuddenChangeOfIllumination",
                                                                ArrayType(StringType()),
                                                                True,
                                                            ),
                                                            StructField("Tape", ArrayType(StringType()), True),
                                                            StructField("Tunnel", ArrayType(StringType()), True),
                                                            StructField(
                                                                "TunnelEntrance", ArrayType(StringType()), True
                                                            ),
                                                            StructField("TunnelExit", ArrayType(StringType()), True),
                                                            StructField("Unsure", ArrayType(StringType()), True),
                                                            StructField(
                                                                "UnsureDryOrMoist", ArrayType(StringType()), True
                                                            ),
                                                            StructField("Water", ArrayType(StringType()), True),
                                                            StructField(
                                                                "WaterOnWindshield", ArrayType(StringType()), True
                                                            ),
                                                            StructField("Wet", ArrayType(StringType()), True),
                                                            StructField("Wiper", ArrayType(StringType()), True),
                                                            StructField("WiperInImage", ArrayType(StringType()), True),
                                                        ]
                                                    ),
                                                    True,
                                                ),
                                            ]
                                        ),
                                        True,
                                    ),
                                    StructField("author", StringType(), True),
                                    StructField("frameNb", LongType(), True),
                                    StructField("id", StringType(), True),
                                    StructField("initialLabelId", StringType(), True),
                                    StructField("lastEdit", StringType(), True),
                                    StructField("taskId", StringType(), True),
                                ]
                            )
                        ),
                        True,
                    ),
                    StructField("sequenceLabels", ArrayType(StringType()), True),
                ]
            ),
            True,
        ),
        StructField(
            "sequence",
            StructType(
                [
                    StructField("countryCode", StringType(), True),
                    StructField("createdAt", StringType(), True),
                    StructField("description", StringType(), True),
                    StructField("id", StringType(), True),
                    StructField("parentVideo", StringType(), True),
                    StructField("pdpUrl", StringType(), True),
                    StructField("predecessor", StringType(), True),
                    StructField("prelabels", StringType(), True),
                    StructField("projectCode", StringType(), True),
                    StructField("rawDataUrls", ArrayType(StringType()), True),
                    StructField("successor", StringType(), True),
                    StructField("tags", ArrayType(StringType()), True),
                    StructField(
                        "video",
                        StructType(
                            [
                                StructField("codec", StringType(), True),
                                StructField("durationInSeconds", DoubleType(), True),
                                StructField("fps", DoubleType(), True),
                                StructField("framesCount", LongType(), True),
                                StructField("height", LongType(), True),
                                StructField(
                                    "metadata",
                                    StructType(
                                        [
                                            StructField("_ls_prelabels", StringType(), True),
                                            StructField("_ls_sequence_id", StringType(), True),
                                            StructField("batch_no", LongType(), True),
                                            StructField("country_code", StringType(), True),
                                            StructField("frames_id", ArrayType(StringType()), True),
                                            StructField("labeling_guide_version", StringType(), True),
                                            StructField("labeling_task", StringType(), True),
                                            StructField("part_of_sequence", BooleanType(), True),
                                            StructField("sensor", StringType(), True),
                                            StructField("sequence", ArrayType(StringType()), True),
                                            StructField("sequence_id", LongType(), True),
                                            StructField("sequence_name", StringType(), True),
                                            StructField("tags", ArrayType(StringType()), True),
                                            StructField("video_name", StringType(), True),
                                        ]
                                    ),
                                    True,
                                ),
                                StructField("sizeInBytes", LongType(), True),
                                StructField("storageUri", StringType(), True),
                                StructField("timestamps", ArrayType(DoubleType()), True),
                                StructField("width", LongType(), True),
                            ]
                        ),
                        True,
                    ),
                ]
            ),
            True,
        ),
    ]
)
