"""Testing utility functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any, Dict, List, Optional

from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession


def compare_dataframe(
    output_df: DataFrame,
    expected_data: list[Dict[str, Any]],
    sort_keys: Optional[List[str]] = None,
    *,
    all_columns_must_match: bool = True,
) -> None:
    """Compares the output DataFrame with the expected data.

    Args:
        output_df (DataFrame): The output DataFrame to compare.
        expected_data (list[Dict[str, Any]]): The expected data to compare against.
        sort_keys (Optional[List[str]]): The keys to sort the data by.
        all_columns_must_match (bool): Whether all columns must match between the output and expected data.
    """
    if sort_keys is None:
        sort_keys = ["image_id"]
    output_data = output_df.collect()
    assert len(output_data) == len(expected_data)
    sorted_output_data = sorted(output_data, key=lambda x: "_".join(str(x[key]) for key in sort_keys))
    sorted_expected_data = sorted(expected_data, key=lambda x: "_".join(str(x[key]) for key in sort_keys))

    for i, (output_row, expected_row) in enumerate(zip(sorted_output_data, sorted_expected_data)):
        output_row_dict = output_row.asDict()
        if all_columns_must_match:
            assert set(output_row_dict.keys()) == set(expected_row.keys())
        for key in expected_row.keys():
            assert (
                output_row[key] == expected_row[key]
            ), f"Row {i}, key {key}: Output does not match expected `{output_row[key]} != {expected_row[key]}`."


class MockDataHelper:
    """Helper class for creating and manipulating mock data in Delta tables."""

    def __init__(self, table_name: str, spark: SparkSession, table_name_schema_map: Dict[str, str]) -> None:
        """Initializes the MockDataHelper.

        Args:
            table_name (str): The name of the table.
            spark (SparkSession): The Spark session.
            table_name_schema_map (Dict[str, str]): A map of table names to their schemas.
        """
        self.table_name = table_name
        self.schema = table_name_schema_map[table_name]
        self.spark = spark

    def create_table(self, data: Dict[str, Any], *, enable_change_data_feed: bool = False) -> DataFrame:
        """Creates a Delta table with the given data.

        Args:
            data (Dict[str, Any]): The data to populate the table with.
            enable_change_data_feed (bool): Whether to enable change data feed for the table.

        Returns:
            DataFrame: The created DataFrame.
        """
        df = self.spark.createDataFrame(data, self.schema)
        df.write.format("delta").mode("overwrite").option(
            "delta.enableChangeDataFeed", enable_change_data_feed
        ).saveAsTable(self.table_name)
        return df

    def add_data(self, data: Dict[str, Any], merge_column: str) -> None:
        """Adds data to the Delta table.

        Args:
            data (Dict[str, Any]): The data to add to the table.
            merge_column (str): The column to use for merging the data.
        """
        df = self.spark.createDataFrame(data, schema=self.schema)
        DeltaTable.forName(self.spark, self.table_name).alias("existing").merge(
            df.alias("new"), f"existing.{merge_column} = new.{merge_column}"
        ).whenNotMatchedInsertAll().whenMatchedUpdateAll().execute()

    def delete_record(self, column: str, value: str) -> None:
        """Deletes a record from the Delta table.

        Args:
            column (str): The column to use for identifying the record to delete.
            value (str): The value of the column to identify the record to delete.
        """
        self.spark.sql(f"DELETE FROM {self.table_name} WHERE {column} = '{value}'")


def _read_table(spark: SparkSession, table_name: str, *, with_changes: bool = False) -> DataFrame:
    """Reads a Delta table.

    Args:
        spark (SparkSession): The Spark session.
        table_name (str): The name of the table to read.
        with_changes (bool): Whether to read the table with change data feed enabled.

    Returns:
        DataFrame: The read DataFrame.
    """
    if with_changes:
        df = spark.read.format("delta").options(**{"readChangeFeed": True, "startingVersion": 0}).table(table_name)
    else:
        df = spark.read.format("delta").table(table_name)
    return df
