"""Environment classification frames table containing images and split information."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import logging

from pyspark.sql import DataFrame
from pyspark.sql.functions import broadcast, col, when
from utils.definitions import GOLD_ENV_FRAMES_TABLE_NAME, SILVER_MDD_FILE_ENTRIES_TABLE_NAME
from utils.environment_classification import add_usage_column
from utils.helper import GoldExport, get_databricks_config, get_table_name

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


DEBUG = False


class EnvironmentClassificationTraining(GoldExport):
    """Creates a comprehensive environment classification table."""

    def create_gold_table(self) -> DataFrame:
        """Create gold layer."""

        env_class_frames = self.read_env_frames_table()

        file_entries_table = self.read_silver_mdd_file_entries_table()

        env_class_training = self.add_image_urls(env_class_frames, file_entries_table)

        for task in ["fog_detection", "tunnel", "road_type", "road_condition"]:
            env_class_training = add_usage_column(env_class_training, task)

        return env_class_training.select(
            col("image_id"),
            col("yuv_image_url_sha"),
            col("image_type"),
            col("yuv_image_url"),
            col("yuv_tri_wide_sha"),
            col("yuv_tri_wide_image_url"),
            col("yuv_tri_mid_sha"),
            col("yuv_tri_mid_image_url"),
            col("yuv_tri_far_sha"),
            col("yuv_tri_far_image_url"),
            col("usage_fog_detection"),
            col("usage_tunnel"),
            col("usage_road_type"),
            col("usage_road_condition"),
            col("namespace_document"),
        )

    def read_env_frames_table(self) -> DataFrame:
        """Read the environment classification frames table."""
        gold_env_class_frames_name = get_table_name(GOLD_ENV_FRAMES_TABLE_NAME, env=self.args.env)
        environment_classification_frames = (
            self.spark.read.format("delta")
            .table(gold_env_class_frames_name)
            .where(
                col("split_fog_detection").isNotNull()
                | col("split_tunnel").isNotNull()
                | col("split_road_type").isNotNull()
                | col("split_road_condition").isNotNull()
            )
        )
        if DEBUG:
            logger.info(
                "Read environment classification frames table with %d rows", environment_classification_frames.count()
            )
        return environment_classification_frames

    def read_silver_mdd_file_entries_table(self) -> DataFrame:
        """Read the silver mdd file entries table."""
        silver_file_entries_table = SILVER_MDD_FILE_ENTRIES_TABLE_NAME
        file_entries_table = (
            self.spark.read.format("delta")
            .table(silver_file_entries_table)
            .where(((col("file_extension") == "png") & (col("file_state") == "ACTIVE")))
            .select("file_hash", "tds_file_url")
        )
        if DEBUG:
            logger.info("file_entries_table df has %d rows", file_entries_table.count())
        return file_entries_table

    def add_image_urls(self, df: DataFrame, file_entries_table: DataFrame) -> DataFrame:
        """Add the image URLs to the dataframe by using the file entries table."""

        # for non-trifocal use either bi-wide or tri-mid image
        df = (
            df.alias("env_frames")
            .withColumn(
                "yuv_image_url_sha",
                when(col("env_frames.yuv_tri_mid_sha").isNotNull(), col("env_frames.yuv_tri_mid_sha")).otherwise(
                    col("env_frames.yuv_bi_wide_sha")
                ),
            )
            .withColumn(
                "image_type",
                when(col("env_frames.yuv_tri_mid_sha").isNotNull(), "yuv_tri_mid").otherwise("yuv_bi_wide"),
            )
        )
        if DEBUG:
            logger.info("env_frames df yuv_image_url_sha has %d rows", df.count())

        df = (
            broadcast(df.alias("env_frames"))
            .join(
                file_entries_table.alias("file_entries"),
                col("env_frames.yuv_image_url_sha") == col("file_entries.file_hash"),
            )
            .join(
                file_entries_table.alias("file_entries_wide"),
                col("env_frames.yuv_tri_wide_sha") == col("file_entries_wide.file_hash"),
            )
            .join(
                file_entries_table.alias("file_entries_mid"),
                col("env_frames.yuv_tri_mid_sha") == col("file_entries_mid.file_hash"),
            )
            .join(
                file_entries_table.alias("file_entries_far"),
                col("env_frames.yuv_tri_far_sha") == col("file_entries_far.file_hash"),
            )
            .select(
                col("env_frames.*"),
                col("file_entries.tds_file_url").alias("yuv_image_url"),
                col("file_entries_wide.tds_file_url").alias("yuv_tri_wide_image_url"),
                col("file_entries_mid.tds_file_url").alias("yuv_tri_mid_image_url"),
                col("file_entries_far.tds_file_url").alias("yuv_tri_far_image_url"),
            )
        )
        if DEBUG:
            logger.info("env_frames df after merge of image urls has %d rows", df.count())
        return df


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = EnvironmentClassificationTraining(table="environment_classification_training", args=args)
    gold_df.run()

    gold_df.spark.sql(
        f"""
            ALTER TABLE {gold_df.full_table_name} SET TAGS (
            'responsible_domain'='Viper',
            'responsible_team'='Environment',
            'refresh_interval'='P2D',
            'timesystem'='UTC'
            );
        """
    )

    gold_df.optimize_table()
    print("Environment Classification Training table created successfully.")
