"""Environment classification frames table containing images and data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from typing import Literal

from pyspark.sql import DataFrame
from pyspark.sql.functions import broadcast, col, desc, explode, first, lit, row_number, when
from pyspark.sql.types import ArrayType, StringType
from pyspark.sql.window import Window
from utils.definitions import (
    SILVER_DSP_DE_IMAGE_EXTENDED_TABLE_NAME,
    SILVER_ENV_CLASS_TABLE_NAME,
    YUV_422_EXTRACTOR_VERSION_START,
)
from utils.helper import GoldExport, get_databricks_config, get_table_name

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

EXPECTED_DYNAMIC_VALUES = [
    "split_road_type",
    "split_fog_detection",
    "split_road_condition",
    "split_tunnel",
    "label_campaign",
    "lapi_label_reference",
]


DEBUG = False


class EnvironmentClassificationFrames(GoldExport):
    """Creates a comprehensive Environment Classification frame table."""

    def read_env_class_table(self) -> DataFrame:
        """Read the environment classification table."""
        silver_env_class_table = get_table_name(SILVER_ENV_CLASS_TABLE_NAME, env=self.args.env)
        environment_classification = self.spark.read.format("delta").table(silver_env_class_table)
        logger.info("Read environment classification table with %d rows", environment_classification.count())
        return environment_classification

    def read_silver_dsp_de_image_extended_table(self) -> DataFrame:
        """Read the silver dsp de image extended table."""
        silver_dsp_de_image_extended_table = SILVER_DSP_DE_IMAGE_EXTENDED_TABLE_NAME
        silver_dsp_de_image_extended = self.spark.read.format("delta").table(silver_dsp_de_image_extended_table)
        return silver_dsp_de_image_extended

    def create_gold_table(self) -> DataFrame:
        """Create gold layer."""
        environment_classification = self.read_env_class_table()
        dsp_de_image_extended = self.read_silver_dsp_de_image_extended_table()

        env_de_image = (
            broadcast(environment_classification.alias("env_class"))
            .join(
                dsp_de_image_extended.alias("de_image_ex"),
                col("env_class.image_id") == col("de_image_ex.image_id"),
            )
            .select(
                col("env_class.file_hash").alias("namespace_file_hash"),
                col("env_class.namespace_struct"),
                col("env_class.namespace_document"),
                col("env_class.revision"),
                col("de_image_ex.*"),
            )
        )

        # extract attributes (properties) from env namespace
        env_namespace_attributes_df = self.env_list_of_attributes_to_df(env_de_image, mode="attributes")
        all_attributes = [col(column) for column in env_namespace_attributes_df.columns if column != "image_id"]

        # extract metadata from env namespace
        env_namespace_metadata_df = self.env_list_of_attributes_to_df(env_de_image, mode="metadata")

        # ensure the dynamic columns are present
        for column in EXPECTED_DYNAMIC_VALUES:
            if column not in env_namespace_metadata_df.columns:
                env_namespace_metadata_df = env_namespace_metadata_df.withColumn(
                    column, lit(None).cast(ArrayType(StringType()))
                )

        # only consider split for the environment classification tasks and label_campaign
        env_namespace_metadata_df = env_namespace_metadata_df.select(
            col("image_id").alias("image_id"),
            col("split_road_type").alias("split_road_type"),
            col("split_fog_detection").alias("split_fog_detection"),
            col("split_road_condition").alias("split_road_condition"),
            col("split_tunnel").alias("split_tunnel"),
            col("label_campaign").alias("label_campaign"),
            col("lapi_label_reference").alias("lapi_label_reference"),
        ).alias("env_namespace_metadata_df")

        # all relevant metadata, e.g., split and label_campaign
        all_metadata = [col(column) for column in env_namespace_metadata_df.columns if column != "image_id"]

        env_de_image = self.add_image_type(env_de_image)
        env_de_image = env_de_image.alias("env_de_image")

        if DEBUG:
            logger.info("Joined environment classification with dsp_de_image_ex now has %d rows.", env_de_image.count())

        label_df = env_de_image.where((col("env_de_image.image_type") == "rgb_label")).alias("label_df")

        if DEBUG:
            logger.info("Label df has %d rows", label_df.count())
        raw_image_df = env_de_image.where((col("env_de_image.image_type") == "raw_image")).alias("raw_image_df")

        # training images must be uncompressed
        env_de_image_no_compression = env_de_image.where(col("env_de_image.compression__algorithm") == "")
        yuv_bi_wide_df = env_de_image_no_compression.where((col("env_de_image.image_type") == "yuv_bi_wide")).alias(
            "yuv_bi_wide_df"
        )
        yuv_wide_df = env_de_image_no_compression.where((col("env_de_image.image_type") == "yuv_tri_wide")).alias(
            "yuv_wide_df"
        )
        yuv_mid_df = env_de_image_no_compression.where((col("env_de_image.image_type") == "yuv_tri_mid")).alias(
            "yuv_mid_df"
        )
        yuv_far_df = env_de_image_no_compression.where((col("env_de_image.image_type") == "yuv_tri_far")).alias(
            "yuv_far_df"
        )

        if DEBUG:
            logger.info("No-compression df has %d rows", env_de_image_no_compression.count())
            logger.info("YUV bi wide df has %d rows", yuv_bi_wide_df.count())
            logger.info("YUV tri wide df has %d rows", yuv_wide_df.count())
            logger.info("YUV tri mid df has %d rows", yuv_mid_df.count())
            logger.info("YUV tri far df has %d rows", yuv_far_df.count())
            logger.info("Raw image df has %d rows", raw_image_df.count())

        # sort for all image types
        image_id_window = Window.partitionBy(col("label_df.image_id")).orderBy(
            [
                desc(col("yuv_bi_wide_df.extractor_version_int")),
                desc(col("yuv_wide_df.extractor_version_int")),
                desc(col("yuv_mid_df.extractor_version_int")),
                desc(col("yuv_far_df.extractor_version_int")),
                desc(col("label_df.extractor_version_int")),
            ]
        )

        logger.info("Joining the dataframes to create the environment classification frames.")

        return (
            label_df.join(
                yuv_bi_wide_df,
                col("label_df.image_id") == col("yuv_bi_wide_df.image_id"),
                how="left",
            )
            .join(
                yuv_wide_df,
                (col("label_df.image_id") == col("yuv_wide_df.image_id")),
                how="left",
            )
            .join(
                yuv_mid_df,
                (col("label_df.image_id") == col("yuv_mid_df.image_id")),
                how="left",
            )
            .join(
                yuv_far_df,
                (col("label_df.image_id") == col("yuv_far_df.image_id")),
                how="left",
            )
            .join(
                raw_image_df,
                (col("label_df.image_id") == col("raw_image_df.image_id")),
                how="left",
            )
            .join(
                env_namespace_attributes_df,
                col("label_df.image_id") == col("env_namespace_attributes_df.image_id"),
                how="left",
            )
            .join(
                env_namespace_metadata_df,
                col("label_df.image_id") == col("env_namespace_metadata_df.image_id"),
                how="left",
            )
            .withColumn("row", row_number().over(image_id_window))
            .filter(col("row") == 1)
            .select(
                col("label_df.image_id").alias("image_id"),
                col("label_df.namespace_document").alias("namespace_document"),
                col("label_df.file_hash").alias("rgb_label_sha"),
                col("label_df.revision").alias("revision"),
                col("label_df.extractor_version").alias("rgb_label_extractor_version"),
                col("yuv_bi_wide_df.file_hash").alias("yuv_bi_wide_sha"),
                col("yuv_bi_wide_df.extractor_version").alias("yuv_bi_wide_extractor_version"),
                col("yuv_wide_df.file_hash").alias("yuv_tri_wide_sha"),
                col("yuv_wide_df.extractor_version").alias("yuv_tri_wide_extractor_version"),
                col("yuv_mid_df.file_hash").alias("yuv_tri_mid_sha"),
                col("yuv_mid_df.extractor_version").alias("yuv_tri_mid_extractor_version"),
                col("yuv_far_df.file_hash").alias("yuv_tri_far_sha"),
                col("yuv_far_df.extractor_version").alias("yuv_tri_far_extractor_version"),
                when(col("raw_image_df.is_raw_image") == True, True)  # noqa: E712
                .otherwise(False)
                .alias("has_raw_image"),
                *all_attributes,
                *all_metadata,
            )
        )

    def env_list_of_attributes_to_df(
        self,
        env_de_image: DataFrame,
        mode: Literal["attributes", "metadata"],
    ) -> DataFrame:
        """Extract the attributes or metadata from the namespace_struct and divide them into columns."""

        # extract the properties from the namespace_struct
        env_namespace_properties_exploded_df = env_de_image.select(
            col("image_id"),
            explode(f"namespace_struct.{mode}").alias(f"{mode}"),
        ).select(
            col("image_id").alias("image_id"),
            col(f"{mode}.name").alias(f"{mode}_name"),
            col(f"{mode}.value").alias(f"{mode}_value"),
        )

        # and subsequently divide the properties into columns
        env_namespace_extracted_attributes_df = (
            env_namespace_properties_exploded_df.groupBy("image_id").pivot(f"{mode}_name").agg(first(f"{mode}_value"))
        ).alias(f"env_namespace_{mode}_df")

        return env_namespace_extracted_attributes_df

    def add_image_type(self, env_de_image: DataFrame) -> DataFrame:
        """Add the image type to the environment classification dataframe.

        The image_type columns is one of the following:
            - rgb_label
            - yuv_bi_wide
            - yuv_tri_wide
            - yuv_tri_mid
            - yuv_tri_far
            - raw_image
        """

        env_de_image = env_de_image.alias("env_de_image")

        # match the file_hash of the rgb rectified image with the file_hash of the environment classification
        rgb_label_condition = col("env_de_image.namespace_file_hash") == col("env_de_image.file_hash")

        # ASSUMPTION: for yuv images -> PYISP only
        yuv_bi_wide_condition = (
            (col("env_de_image.view") == "Wide")
            & ((col("env_de_image.view_type") != "Trifocal") | (col("env_de_image.view_type").isNull()))
            & (col("env_de_image.color_space") == "YUV")
            & (col("env_de_image.isp") == "PYISP")
            & (col("env_de_image.extractor_version_int") >= YUV_422_EXTRACTOR_VERSION_START)
        )

        yuv_tri_wide_condition = (
            (col("env_de_image.view") == "Wide")
            & (col("env_de_image.view_type") == "Trifocal")
            & (col("env_de_image.color_space") == "YUV")
            & (col("env_de_image.isp") == "PYISP")
            & (col("env_de_image.extractor_version_int") >= YUV_422_EXTRACTOR_VERSION_START)
        )

        yuv_tri_mid_condition = (
            (col("env_de_image.view") == "Mid")
            & (col("env_de_image.view_type") == "Trifocal")
            & (col("env_de_image.color_space") == "YUV")
            & (col("env_de_image.isp") == "PYISP")
            & (col("env_de_image.extractor_version_int") >= YUV_422_EXTRACTOR_VERSION_START)
        )

        yuv_tri_far_condition = (
            (col("env_de_image.view") == "Far")
            & (col("env_de_image.view_type") == "Trifocal")
            & (col("env_de_image.color_space") == "YUV")
            & (col("env_de_image.isp") == "PYISP")
            & (col("env_de_image.extractor_version_int") >= YUV_422_EXTRACTOR_VERSION_START)
        )

        raw_condition = col("env_de_image.is_raw_image") == True  # noqa: E712

        env_de_image = env_de_image.withColumn(
            "image_type",
            when(rgb_label_condition, "rgb_label")
            .when(yuv_bi_wide_condition, "yuv_bi_wide")
            .when(yuv_tri_wide_condition, "yuv_tri_wide")
            .when(yuv_tri_mid_condition, "yuv_tri_mid")
            .when(yuv_tri_far_condition, "yuv_tri_far")
            .when(raw_condition, "raw_image")
            .otherwise(None),
        )
        return env_de_image.where(col("image_type").isNotNull())


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = EnvironmentClassificationFrames(table="environment_classification_frames", args=args)
    gold_df.run()

    gold_df.spark.sql(
        f"""
            ALTER TABLE {gold_df.full_table_name} SET TAGS (
            'responsible_domain'='Viper',
            'responsible_team'='Environment',
            'refresh_interval'='P2D',
            'timesystem'='UTC'
            );
        """
    )
    gold_df.optimize_table()
