"""Create blockage frames table containing image and label information."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging

from pyspark.sql import DataFrame
from pyspark.sql.functions import col
from utils.helper import GoldExport, RuntimeArgs, get_databricks_config

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


class BlockageFrames(GoldExport):
    """Creates a comprehensive blockage frame table."""

    def __init__(self, table: str, mpc4: bool, args: RuntimeArgs):
        """Initialize the BlockageFrames class."""
        super().__init__(table, args)

        self.mpc4 = mpc4

    def read_blockage_label_info_table(self) -> DataFrame:
        """Read blockage label info table."""
        blockage_label_info_table_name = "gold.viper_environment.blockage_label_info"
        return self.spark.read.format("delta").table(blockage_label_info_table_name)

    def read_blockage_labeled_frames_table(self) -> DataFrame:
        """Read blockage labeled frames table."""
        blockage_labeled_frames_table = f"silver.mdd.viper_blockage_labeled_frames{'_mpc4' if self.mpc4 else ''}"
        return self.spark.read.format("delta").table(blockage_labeled_frames_table)

    def create_gold_table(self) -> DataFrame:
        """Create gold layer."""
        blockage_label_info_df = self.read_blockage_label_info_table()
        blockage_labeled_frames_df = self.read_blockage_labeled_frames_table()

        blockage_label_info_df = blockage_label_info_df.alias("blockage_label_info_df")
        blockage_labeled_frames_df = blockage_labeled_frames_df.alias("blockage_labeled_frames_df")

        # NOTE: blockage_label_info contains all labels (also duplicates due to relabeling)
        # NOTE: blockage_labeled_frames contains only newest labels!
        # NOTE: thus merge needs on both, reference_sha (of anonymized image) and label_sha
        blockage_frames_df = blockage_labeled_frames_df.join(
            blockage_label_info_df,
            (col("blockage_labeled_frames_df.reference_sha") == col("blockage_label_info_df.reference_sha"))
            & (col("blockage_labeled_frames_df.label_sha") == col("blockage_label_info_df.label_sha")),
        )

        blockage_frames_df = blockage_frames_df.drop(col("blockage_label_info_df.reference_sha"))
        blockage_frames_df = blockage_frames_df.drop(col("blockage_label_info_df.label_sha"))

        return blockage_frames_df


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = BlockageFrames(table="blockage_frames", mpc4=False, args=args)
    gold_df.run()
    gold_df.spark.sql(
        f"""
            ALTER TABLE {gold_df.full_table_name} SET TAGS (
            'responsible_domain'='Viper',
            'responsible_team'='Environment',
            'refresh_interval'='P2D',
            'timesystem'='UTC'
            );
        """
    )
    gold_df.optimize_table()

    gold_df = BlockageFrames(table="blockage_frames_mpc4", mpc4=True, args=args)
    gold_df.run()
    gold_df.spark.sql(
        f"""
            ALTER TABLE {gold_df.full_table_name} SET TAGS (
            'responsible_domain'='Viper',
            'responsible_team'='Environment',
            'refresh_interval'='P2D',
            'timesystem'='UTC'
            );
        """
    )
    gold_df.optimize_table()
