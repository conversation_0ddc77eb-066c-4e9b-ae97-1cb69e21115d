"""Environment classification frames table containing images and split information."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging

from pyspark.sql import DataFrame
from pyspark.sql.functions import broadcast, col, when
from pyspark.sql.types import StringType, StructField, StructType
from utils.cdf_stream import BaseDeltaStreamingTable
from utils.definitions import SILVER_MDD_FILE_ENTRIES_TABLE_NAME
from utils.environment_classification import add_usage_column
from utils.helper import get_databricks_config

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)


DEBUG = False


class EnvironmentClassificationTrainingCDF(BaseDeltaStreamingTable):
    """Creates a comprehensive environment classification table."""

    def transform_data(self, streaming_table_df: DataFrame) -> DataFrame:
        """Abstract method to apply transformations to the source DataFrame."""
        streaming_table_df = streaming_table_df.filter(
            col("split_fog_detection").isNotNull()
            | col("split_tunnel").isNotNull()
            | col("split_road_type").isNotNull()
            | col("split_road_condition").isNotNull()
        )

        file_entries_table = self.read_silver_mdd_file_entries_table()

        env_class_training = self.add_image_urls(streaming_table_df, file_entries_table)

        for task in ["fog_detection", "tunnel", "road_type", "road_condition"]:
            env_class_training = add_usage_column(env_class_training, task)

        return env_class_training.select(
            col("image_id"),
            col("yuv_image_url_sha"),
            col("image_type"),
            col("yuv_image_url"),
            col("yuv_tri_wide_sha"),
            col("yuv_tri_wide_image_url"),
            col("yuv_tri_mid_sha"),
            col("yuv_tri_mid_image_url"),
            col("yuv_tri_far_sha"),
            col("yuv_tri_far_image_url"),
            col("usage_fog_detection"),
            col("usage_tunnel"),
            col("usage_road_type"),
            col("usage_road_condition"),
            col("namespace_document"),
            col("_change_type"),
            col("_commit_version"),
            col("_commit_timestamp"),
        )

    def read_silver_mdd_file_entries_table(self) -> DataFrame:
        """Read the silver mdd file entries table."""
        file_entries_table = (
            self.spark.read.format("delta")
            .table(SILVER_MDD_FILE_ENTRIES_TABLE_NAME)
            .filter(((col("file_extension") == "png") & (col("file_state") == "ACTIVE")))
            .select("file_hash", "tds_file_url")
        )
        if DEBUG:
            logger.info("file_entries_table df has %d rows", file_entries_table.count())
        return file_entries_table

    def add_image_urls(self, df: DataFrame, file_entries_table: DataFrame) -> DataFrame:
        """Add the image URLs to the dataframe by using the file entries table."""

        # for non-trifocal use either bi-wide or tri-mid image
        df = (
            df.alias("env_frames")
            .withColumn(
                "yuv_image_url_sha",
                when(col("env_frames.yuv_tri_mid_sha").isNotNull(), col("env_frames.yuv_tri_mid_sha")).otherwise(
                    col("env_frames.yuv_bi_wide_sha")
                ),
            )
            .withColumn(
                "image_type",
                when(col("env_frames.yuv_tri_mid_sha").isNotNull(), "yuv_tri_mid").otherwise("yuv_bi_wide"),
            )
        )
        if DEBUG:
            logger.info("env_frames df yuv_image_url_sha has %d rows", df.count())

        df_with_tri_wide = broadcast(df.alias("env_frames")).join(
            file_entries_table.alias("file_entries_wide"),
            col("env_frames.yuv_tri_wide_sha") == col("file_entries_wide.file_hash"),
        )
        df_with_tri_mid = broadcast(df_with_tri_wide).join(
            file_entries_table.alias("file_entries_mid"),
            col("env_frames.yuv_tri_mid_sha") == col("file_entries_mid.file_hash"),
        )
        df_with_tri_far = broadcast(df_with_tri_mid).join(
            file_entries_table.alias("file_entries_far"),
            col("env_frames.yuv_tri_far_sha") == col("file_entries_far.file_hash"),
        )
        df = (
            broadcast(df_with_tri_far)
            .join(
                file_entries_table.alias("file_entries"),
                col("env_frames.yuv_image_url_sha") == col("file_entries.file_hash"),
            )
            .select(
                col("env_frames.*"),
                col("file_entries.tds_file_url").alias("yuv_image_url"),
                col("file_entries_wide.tds_file_url").alias("yuv_tri_wide_image_url"),
                col("file_entries_mid.tds_file_url").alias("yuv_tri_mid_image_url"),
                col("file_entries_far.tds_file_url").alias("yuv_tri_far_image_url"),
            )
        )
        if DEBUG:
            logger.info("env_frames df after merge of image urls has %d rows", df.count())
        return df

    def get_output_table_schema(self) -> StructType:
        """Get the schema for the output table."""
        return StructType(
            [
                StructField("image_id", StringType(), True),
                StructField("yuv_image_url_sha", StringType(), True),
                StructField("image_type", StringType(), True),
                StructField("yuv_image_url", StringType(), True),
                StructField("yuv_tri_wide_sha", StringType(), True),
                StructField("yuv_tri_wide_image_url", StringType(), True),
                StructField("yuv_tri_mid_sha", StringType(), True),
                StructField("yuv_tri_mid_image_url", StringType(), True),
                StructField("yuv_tri_far_sha", StringType(), True),
                StructField("yuv_tri_far_image_url", StringType(), True),
                StructField("usage_fog_detection", StringType(), True),
                StructField("usage_tunnel", StringType(), True),
                StructField("usage_road_type", StringType(), True),
                StructField("usage_road_condition", StringType(), True),
                StructField("namespace_document", StringType(), True),
            ]
        )


if __name__ == "__main__":
    args = get_databricks_config()
    env_class_training_cdf = EnvironmentClassificationTrainingCDF(
        target_table_name="environment_classification_training_cdf",
        streaming_table_name="environment_classification_frames_cdf",
        merge_condition="existing.image_id = batch.image_id",
        env=args.env,
        catalog=args.catalog,
        schema=args.schema,
        target_table_primary_key="image_id",
        overwrite=args.overwrite,
    )
    env_class_training_cdf.run()

    env_class_training_cdf.spark.sql(
        f"""
            ALTER TABLE {env_class_training_cdf.target_table_name} SET TAGS (
            'responsible_domain'='Viper',
            'responsible_team'='Environment',
            'refresh_interval'='P2D',
            'timesystem'='UTC'
            );
        """
    )

    env_class_training_cdf.optimize_table()
    logger.info("Environment Classification Training table created successfully.")
