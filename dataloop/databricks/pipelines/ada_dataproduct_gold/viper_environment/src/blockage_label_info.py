"""Create table containing information about blockage labels and statistic about blockage polygons."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import json
import logging
from typing import Dict, List, Optional, Tuple, TypedDict

import numpy as np
from data_formats.blockage.parser import BlockageLabelParserFactory
from data_formats.blockage.rasterization import BlockageLabelRasterizer
from data_formats.blockage.representation import BlockageLabel, BlockageLabelValidationState, Polygon
from pyspark.sql import Column, DataFrame
from pyspark.sql.functions import (
    col,
    explode,
    from_json,
    get_json_object,
    regexp_replace,
    row_number,
    transform,
    udf,
)
from pyspark.sql.types import ArrayType, FloatType, IntegerType, MapType, StringType, StructField, StructType
from pyspark.sql.window import Window
from utils.definitions import (
    BLOCKAGE_FRAME_ATTRIBUTES,
    BLOCKAGE_LABEL_TRANSLATION_HIGH,
    BLOCKAGE_LABEL_TRANSLATION_LOW,
    BLOCKAGE_LABEL_TRANSLATION_MEDIUM,
    LABELER_ID_TO_NAME,
)
from utils.helper import GoldExport, get_databricks_config

log_format = "%(asctime)s - %(levelname)s - %(name)s - %(message)s"
logging.basicConfig(format=log_format, datefmt="%Y-%m-%d %H:%M:%S")
logger = logging.getLogger(__name__)
logger.setLevel(logging.DEBUG)

BLOCKAGE_ID = 0
CLEAN_ID = 1
RASTERIZER_CONFIGS = {
    "low": BLOCKAGE_LABEL_TRANSLATION_LOW,
    "medium": BLOCKAGE_LABEL_TRANSLATION_MEDIUM,
    "high": BLOCKAGE_LABEL_TRANSLATION_HIGH,
}

POLYGON_STATISTICS = [
    "n_polygons",
    "n_valid_polygons",
    "n_invalid_polygons",
    "n_blockage_polygons",
    "n_ego_vehicle_polygons",
]


class BlockageLabelInfo(TypedDict):
    """Blockage information for a label returned by UDF (reflecting blockage_info_schema)."""

    supplier_team: Optional[str]
    campaign_name: Optional[str]
    sequence_description: Optional[str]
    validation_status: str
    properties: Dict[str, List[str]]
    polygon_stats: Dict[str, int]
    rasterization_ratios: Dict[str, Optional[float]]


blockage_label_info_schema = StructType(
    [
        StructField("supplier_team", StringType(), True),
        StructField("campaign_name", StringType(), True),
        StructField("sequence_description", StringType(), True),
        StructField("validation_status", StringType(), True),
        StructField("properties", MapType(StringType(), ArrayType(StringType(), True), True), True),
        StructField("polygon_stats", MapType(StringType(), IntegerType(), True), True),
        StructField("rasterization_ratios", MapType(StringType(), FloatType(), True), True),
    ]
)


def aggregate_polygon_statistics(polygons: List[Polygon]) -> Dict[str, int]:
    """Aggregate polygon statistics."""
    all_polygons_valid = all(polygon.valid for polygon in polygons)
    n_polygons = len(polygons)
    n_valid_polygons = n_polygons if all_polygons_valid else len([True for polygon in polygons if polygon.valid])
    n_invalid_polygons = len([True for polygon in polygons if not polygon.valid])

    n_blockage_polygons = len([True for polygon in polygons if polygon.hierarchy_class == "Blockage" and polygon.valid])
    n_ego_vehicle_polygons = len(
        [True for polygon in polygons if polygon.hierarchy_class == "EgoVehicle" and polygon.valid]
    )

    return {
        "n_polygons": n_polygons,
        "n_valid_polygons": n_valid_polygons,
        "n_invalid_polygons": n_invalid_polygons,
        "n_blockage_polygons": n_blockage_polygons,
        "n_ego_vehicle_polygons": n_ego_vehicle_polygons,
    }


def aggregate_rasterization(blockage_label: BlockageLabel) -> Dict[str, Optional[float]]:
    """Aggregate rasterization statistics."""
    rasterization_ratios: Dict[str, Optional[float]] = {}
    for name, config in RASTERIZER_CONFIGS.items():
        norm_blc_ratio = None
        if blockage_label.validation_state == BlockageLabelValidationState.VALID:
            rasterizer = BlockageLabelRasterizer(translation_config=config)
            training_label_polygon_tuples = rasterizer.translate_annotations(blockage_label.polygon_annotations)
            training_mask = rasterizer.generate_mask(blockage_label.shape, training_label_polygon_tuples)

            # FIXME: unsure about these ratios
            blc_ratio = int(np.sum(training_mask == BLOCKAGE_ID)) / training_mask.size
            clean_ratio = int(np.sum(training_mask == CLEAN_ID)) / training_mask.size
            norm_blc_ratio = blc_ratio / (blc_ratio + clean_ratio)

        rasterization_ratios[f"blockage_ratio_{name}"] = norm_blc_ratio

    return rasterization_ratios


@udf(returnType=blockage_label_info_schema)
def extract_blockage_label_info_udf(
    label_spec: str, frame_number: str, label_file_content: str
) -> Optional[BlockageLabelInfo]:
    """Get the frame labels for a given frame."""

    try:
        label_dict = json.loads(label_file_content)
        id_ = label_dict.get("campaign", {}).get("labelerGroup")
        supplier_team = LABELER_ID_TO_NAME.get(id_, None)
        campaign_name = label_dict.get("campaign", {}).get("name")
        sequence_description = label_dict.get("sequence", {}).get("description")
        # parse label
        parser = BlockageLabelParserFactory.create("GoldenAlgo", label_spec)
        blockage_label = parser.parse_from_dict(label_dict, int(frame_number))

        polygon_statistics = aggregate_polygon_statistics(blockage_label.polygon_annotations)

        rasterization_ratios = aggregate_rasterization(blockage_label)

        # e.g., roadCondition etc.
        properties = {
            annotation.name: list(annotation.value) if isinstance(annotation.value, tuple) else [annotation.value]
            for annotation in blockage_label.frame_annotations
        }

        # returnType conform return
        blockage_info = BlockageLabelInfo(
            supplier_team=supplier_team,
            campaign_name=campaign_name,
            sequence_description=sequence_description,
            validation_status=blockage_label.validation_state.value,
            properties=properties,
            polygon_stats=polygon_statistics,
            rasterization_ratios=rasterization_ratios,
        )

        return blockage_info

    except Exception as e:
        logger.error("Error while extracting frame label attributes: %s", e)
    return None


def extend_df_with_blockage_label_info(df: DataFrame) -> Tuple[DataFrame, List[Column]]:
    """Extend the blockage label information to DataFrame and bookkeeping of relevant columns references."""

    df = df.withColumn(
        "blockage_label_info",
        extract_blockage_label_info_udf(
            col("gaf_label_spec"),
            col("gaf_frame_number"),
            col("label_file_content"),
        ),
    )

    # create references to all struct fields
    property_column_refs = [
        col("blockage_label_info.properties").getItem(name).alias(name) for name in BLOCKAGE_FRAME_ATTRIBUTES
    ]

    polygon_stats_column_refs = [
        col("blockage_label_info.polygon_stats").getItem(name).alias(name) for name in POLYGON_STATISTICS
    ]

    rasterization_ratio_column_refs = [
        col("blockage_label_info.rasterization_ratios")
        .getItem(f"blockage_ratio_{name}")
        .alias(f"blockage_ratio_{name}")
        for name in RASTERIZER_CONFIGS.keys()
    ]

    relevant_column_refs: List[Column] = []
    relevant_column_refs.extend(property_column_refs)
    relevant_column_refs.extend(polygon_stats_column_refs)
    relevant_column_refs.extend(rasterization_ratio_column_refs)
    relevant_column_refs.extend(
        [
            col("blockage_label_info.supplier_team").alias("supplier_team", metadata={"comment": "Supplier team."}),
            col("blockage_label_info.campaign_name").alias(
                "campaign_name", metadata={"comment": "The campaign name from the GAF label."}
            ),
            col("blockage_label_info.sequence_description").alias(
                "sequence_description", metadata={"comment": "The sequence name from the GAF label."}
            ),
            col("blockage_label_info.validation_status").alias(
                "validation_status",
                metadata={
                    "comment": "The BlockageLabelValidationStatus from data-formats, "
                    "e.g., valid, skip_condition, invalid_polygons."
                },
            ),
        ]
    )
    return df, relevant_column_refs


class BlockageLabelInfoExport(GoldExport):
    """Creates a table with aggregated blockage label stats/info."""

    def create_gold_table(self) -> DataFrame:
        """Create blockage info/stats gold table."""
        blockage_label_file_content_df = self.get_blockage_label_info_from_lapi()
        blockage_label_and_info_df = self.add_blockage_info(blockage_label_file_content_df)

        return blockage_label_and_info_df

    def read_lapi_label_file_content(self) -> DataFrame:
        """Read the lapi label file content table."""
        lapi_label_file_content_table_name = "bronze.lapi.label_file_content"
        return self.spark.table(lapi_label_file_content_table_name)

    def get_blockage_label_info_from_lapi(self) -> DataFrame:
        """Create blockage info table."""

        label_file_content_df = self.read_lapi_label_file_content()

        # Extract gaf label info: spec and reference shas
        blockage_label_file_content_df = (
            label_file_content_df.filter(col("label_task_team").isin("Blockage", "blockage"))
            .select(
                col("file_hash").alias("label_sha"),
                col("label_file_content"),
                get_json_object(col("label_file_content"), "$.sequence.video.metadata.sequence").alias(
                    "gaf_reference_string"
                ),
                transform(
                    from_json(col("gaf_reference_string"), ArrayType(StringType())), lambda x: x.substr(1, 64)
                ).alias("gaf_reference_shas"),
                get_json_object(col("label_file_content"), "$.campaign.moduleDefinition.Meta.version").alias(
                    "gaf_label_spec"
                ),
            )
            .drop("gaf_reference_string")
        )

        # cleanup label_file_content
        blockage_label_file_content_df = blockage_label_file_content_df.withColumn(
            "label_file_content",
            regexp_replace(col("label_file_content"), r"(Defective\sBlurring)", "DefectiveBlurring"),
        ).withColumn(
            "label_file_content",
            regexp_replace(col("label_file_content"), r"(Defective\sCamera)", "DefectiveCamera"),
        )

        # explode reference shas to obtain gaf_frame_number
        # -> this is required for gaf label files with multiple frames (legacy)
        blockage_label_file_content_df = blockage_label_file_content_df.withColumn(
            "reference_sha", explode(col("gaf_reference_shas"))
        )
        window_spec = Window.partitionBy(col("label_sha")).orderBy(col("label_sha"))
        blockage_label_file_content_df = blockage_label_file_content_df.withColumn(
            "gaf_frame_number", row_number().over(window_spec)
        ).drop(col("gaf_reference_shas"))

        return blockage_label_file_content_df

    def add_blockage_info(self, blockage_label_file_content_df: DataFrame) -> DataFrame:
        """Add blockage info/stats to table."""

        blockage_label_and_info_df, blockage_info_column_names = extend_df_with_blockage_label_info(
            blockage_label_file_content_df
        )

        return blockage_label_and_info_df.select(
            col("label_sha"),
            col("reference_sha"),
            col("gaf_label_spec"),
            col("gaf_frame_number"),
            *blockage_info_column_names,
        )


if __name__ == "__main__":
    args = get_databricks_config()
    gold_df = BlockageLabelInfoExport(table="blockage_label_info", args=args)
    gold_df.run()

    gold_df.spark.sql(
        f"""
            ALTER TABLE {gold_df.full_table_name} SET TAGS (
            'responsible_domain'='Viper',
            'responsible_team'='Environment',
            'refresh_interval'='P2D',
            'timesystem'='UTC'
            );
        """
    )

    gold_df.optimize_table()
