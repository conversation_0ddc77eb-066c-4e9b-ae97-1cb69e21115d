"""Script to delete tables from the viper_environment schema in the gold catalogue."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from argparse import ArgumentParser

from utils.helper import get_or_create_databricks_session

if __name__ == "__main__":
    # Initialize Spark session
    spark = get_or_create_databricks_session()

    parser = ArgumentParser()
    parser.add_argument(
        "--catalog", required=True, help="Catalogue to run against, by default catalogue is `gold`.", default="gold"
    )

    args = parser.parse_args()

    # Set the catalog and schema
    spark.sql(f"USE CATALOG {args.catalog}")
    spark.sql("USE SCHEMA viper_environment")

    # List of tables to drop
    tables_to_drop = [
        "environment_classification_fog_detection",
        "environment_classification_tunnel",
        "environment_classification_road_type",
        "environment_classification_road_condition",
    ]

    # Drop tables if they exist
    for table in tables_to_drop:
        spark.sql(f"DROP TABLE IF EXISTS {table}")
