"""Tests for the BaseDeltaStreamingTable class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import shutil
import tempfile
from pathlib import Path
from typing import Generator
from unittest.mock import PropertyMock, patch

import pytest
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StringType, StructField, StructType
from utils.cdf_stream import BaseDeltaStreamingTable
from utils.testing_utils import MockDataHelper, _read_table, compare_dataframe

ENV_TRAINING_NAME = "environment_classification_training"
ENV_FRAMES_NAME = "environment_classification_frames"
FILE_ENTRIES_TABLE_NAME = "file_entries"


MOCK_ENV_FRAMES_SCHEMA = StructType(
    [
        StructField("image_id", StringType(), False),
        StructField("image_sha", StringType(), False),
    ]
)
MOCK_FILE_ENTRIES = StructType(
    [
        StructField("file_hash", StringType(), False),
        StructField("tds_file_url", StringType(), False),
    ]
)

MOCK_ENV_TRAINING_SCHEMA = StructType(
    [
        StructField("image_id", StringType(), False),
        StructField("image_sha", StringType(), False),
        StructField("tds_file_url", StringType(), False),
    ]
)


TABLE_NAME_SCHEMA_MAP = {
    ENV_TRAINING_NAME: MOCK_ENV_TRAINING_SCHEMA,
    ENV_FRAMES_NAME: MOCK_ENV_FRAMES_SCHEMA,
    FILE_ENTRIES_TABLE_NAME: MOCK_FILE_ENTRIES,
}


class MinimalTableV1(BaseDeltaStreamingTable):
    """Minimal implementation of BaseDeltaStreamingTable for testing purposes."""

    def transform_data(self, streaming_table_df: DataFrame) -> DataFrame:
        """Transforms the streaming table DataFrame by joining with file entries."""
        file_entries_df = self.spark.read.table(FILE_ENTRIES_TABLE_NAME)
        return streaming_table_df.join(
            file_entries_df, streaming_table_df.image_sha == file_entries_df.file_hash, "inner"
        ).select(
            streaming_table_df.image_id,
            streaming_table_df.image_sha,
            streaming_table_df._change_type,
            streaming_table_df._commit_version,
            file_entries_df.tds_file_url,
        )

    def get_output_table_schema(self) -> StructType:
        """Returns the schema of the output table."""
        return MOCK_ENV_TRAINING_SCHEMA


@pytest.fixture(name="write_checkpoint")
def fixture_write_checkpoint() -> Generator[Path, None, None]:
    """Fixture to create a temporary directory for checkpointing."""
    with tempfile.TemporaryDirectory() as tmp_path:
        checkpoint_dir = Path(tmp_path) / "checkpoint"
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        yield checkpoint_dir


@pytest.fixture(name="write_checkpoint_overwrite")
def fixture_write_checkpoint_overwrite() -> Generator[Path, None, None]:
    """Fixture to create a temporary directory for checkpointing with overwrite."""
    with tempfile.TemporaryDirectory() as tmp_path:
        checkpoint_dir = Path(tmp_path) / "checkpoint_overwrite"
        checkpoint_dir.mkdir(parents=True, exist_ok=True)
        yield checkpoint_dir


@pytest.fixture(name="minimal_stream_table")
def fixture_minimal_delta_merger(
    spark_session_mock: SparkSession, write_checkpoint: Path
) -> Generator[MinimalTableV1, None, None]:
    """Fixture to create a MinimalTableV1 instance for testing."""
    with patch(
        "utils.cdf_stream.BaseDeltaStreamingTable.full_streaming_table_name", new_callable=PropertyMock
    ) as mock_streaming_table_name:
        with patch(
            "utils.cdf_stream.BaseDeltaStreamingTable.target_table_name", new_callable=PropertyMock
        ) as mock_target_table_name:
            with patch(
                "utils.cdf_stream.BaseDeltaStreamingTable.checkpoint_dir", new_callable=PropertyMock
            ) as mock_checkpoint_dir:
                mock_checkpoint_dir.return_value = write_checkpoint
                mock_target_table_name.return_value = ENV_TRAINING_NAME
                mock_streaming_table_name.return_value = ENV_FRAMES_NAME
                yield MinimalTableV1(
                    target_table_name=ENV_TRAINING_NAME,
                    streaming_table_name=ENV_FRAMES_NAME,
                    merge_condition="existing.image_id = batch.image_id",
                    env="dev",
                    catalog="gold",
                    schema="viper_environment",
                )


@pytest.fixture(name="minimal_stream_table_overwrite")
def fixture_minimal_stream_table_overwrite(
    spark_session_mock: SparkSession, write_checkpoint_overwrite: Path
) -> Generator[MinimalTableV1, None, None]:
    """Fixture to create a MinimalTableV1 instance with overwrite for testing."""

    def mock_overwrite_table() -> None:
        spark_session_mock.sql(f"DROP TABLE IF EXISTS {ENV_TRAINING_NAME}")
        shutil.rmtree(write_checkpoint_overwrite)
        write_checkpoint_overwrite.mkdir(parents=True, exist_ok=True)

    with patch(
        "utils.cdf_stream.BaseDeltaStreamingTable.target_table_name", new_callable=PropertyMock
    ) as mock_target_table_name:
        with patch(
            "utils.cdf_stream.BaseDeltaStreamingTable.full_streaming_table_name", new_callable=PropertyMock
        ) as mock_streaming_table_name:
            with patch(
                "utils.cdf_stream.BaseDeltaStreamingTable.checkpoint_dir", new_callable=PropertyMock
            ) as mock_checkpoint_dir:
                with patch(
                    "utils.cdf_stream.BaseDeltaStreamingTable._overwrite_table",
                    side_effect=mock_overwrite_table,
                ):
                    mock_checkpoint_dir.return_value = write_checkpoint_overwrite
                    mock_streaming_table_name.return_value = ENV_FRAMES_NAME
                    mock_target_table_name.return_value = ENV_TRAINING_NAME
                    yield MinimalTableV1(
                        target_table_name=ENV_TRAINING_NAME,
                        streaming_table_name=ENV_FRAMES_NAME,
                        merge_condition="existing.image_id = batch.image_id",
                        env="dev",
                        catalog="gold",
                        schema="viper_environment",
                        overwrite=True,
                    )


def test_insert(spark_session_mock: SparkSession, minimal_stream_table: MinimalTableV1) -> None:
    """Tests if the table is created correctly when it does not exist.

    The source CDF table has an 'append-only' history.
    """
    env_frames_helper = MockDataHelper(ENV_FRAMES_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)
    file_entries_helper = MockDataHelper(FILE_ENTRIES_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
        }
    ]
    file_entries_data = [
        {
            "file_hash": "sha_1",
            "tds_file_url": "url_1",
        }
    ]

    env_frames_helper.create_table(env_data, enable_change_data_feed=True)
    file_entries_helper.create_table(file_entries_data, enable_change_data_feed=False)

    minimal_stream_table.run()

    result_df = spark_session_mock.table(ENV_TRAINING_NAME)
    assert result_df.count() == 1
    expected_data = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
        }
    ]
    compare_dataframe(result_df, expected_data)


def test_insert_with_update(spark_session_mock: SparkSession, minimal_stream_table: MinimalTableV1) -> None:
    """Tests if the table is updated correctly when it does not exist.

    The source CDF table has a record with 'update' history.
    """
    env_frames_helper = MockDataHelper(ENV_FRAMES_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)
    file_entries_helper = MockDataHelper(FILE_ENTRIES_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data_1 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
        }
    ]
    env_data_2 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_2",
        }
    ]
    file_entries_data = [
        {
            "file_hash": "sha_1",
            "tds_file_url": "url_1",
        },
        {
            "file_hash": "sha_2",
            "tds_file_url": "url_2",
        },
    ]

    env_frames_helper.create_table(env_data_1, enable_change_data_feed=True)
    env_frames_helper.add_data(env_data_2, merge_column="image_id")
    file_entries_helper.create_table(file_entries_data, enable_change_data_feed=False)

    minimal_stream_table.run()

    result_df = spark_session_mock.table(ENV_TRAINING_NAME)
    assert result_df.count() == 1
    expected_data = [
        {
            "image_id": "id_1",
            "image_sha": "sha_2",
            "tds_file_url": "url_2",
        }
    ]
    compare_dataframe(result_df, expected_data)


def test_insert_with_delete(spark_session_mock: SparkSession, minimal_stream_table: MinimalTableV1) -> None:
    """Tests if the table is updated correctly when it does not exist.

    The source CDF table has a record with 'delete' history.
    """
    env_frames_helper = MockDataHelper(ENV_FRAMES_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)
    file_entries_helper = MockDataHelper(FILE_ENTRIES_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data_1 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
        },
        {
            "image_id": "id_2",
            "image_sha": "sha_2",
        },
    ]
    file_entries_data = [
        {
            "file_hash": "sha_1",
            "tds_file_url": "url_1",
        },
        {
            "file_hash": "sha_2",
            "tds_file_url": "url_2",
        },
    ]

    env_frames_helper.create_table(env_data_1, enable_change_data_feed=True)
    env_frames_helper.delete_record("image_id", "id_1")
    file_entries_helper.create_table(file_entries_data, enable_change_data_feed=False)

    minimal_stream_table.run()

    result_df = spark_session_mock.table(ENV_TRAINING_NAME)
    assert result_df.count() == 1
    expected_data = [
        {
            "image_id": "id_2",
            "image_sha": "sha_2",
            "tds_file_url": "url_2",
        }
    ]
    compare_dataframe(result_df, expected_data)


def test_insert_with_overwrite(
    spark_session_mock: SparkSession, minimal_stream_table_overwrite: MinimalTableV1
) -> None:
    """Tests if the table is updated correctly when it does not exist.

    The source CDF table has a record with 'delete' history.
    """
    env_frames_helper = MockDataHelper(ENV_FRAMES_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)
    file_entries_helper = MockDataHelper(FILE_ENTRIES_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_training_helper = MockDataHelper(ENV_TRAINING_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    training_data = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
        },
        {
            "image_id": "id_2",
            "image_sha": "sha_2",
            "tds_file_url": "url_2",
        },
    ]
    env_training_helper.create_table(training_data, enable_change_data_feed=True)

    existing_training_df = spark_session_mock.table(ENV_TRAINING_NAME)
    assert existing_training_df.count() == 2

    env_data_1 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
        },
    ]
    file_entries_data = [
        {
            "file_hash": "sha_1",
            "tds_file_url": "url_1",
        },
        {
            "file_hash": "sha_2",
            "tds_file_url": "url_2",
        },
    ]

    env_frames_helper.create_table(env_data_1, enable_change_data_feed=True)
    file_entries_helper.create_table(file_entries_data, enable_change_data_feed=False)

    minimal_stream_table_overwrite.run()

    training_df = spark_session_mock.table(ENV_TRAINING_NAME)
    assert training_df.count() == 1
    expected_data = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
        }
    ]
    compare_dataframe(training_df, expected_data)


def test_incremental_no_update(spark_session_mock: SparkSession, minimal_stream_table: MinimalTableV1) -> None:
    """Tests if the table is created correctly when it already exists.

    The source table has not changed since the last run, thus no changes to the target table are expected.

    Source changes:
        - none
    Expected target changes:
        - CDF does not change
    """
    env_frames_helper = MockDataHelper(ENV_FRAMES_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)
    file_entries_helper = MockDataHelper(FILE_ENTRIES_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
        }
    ]
    file_entries_data = [
        {
            "file_hash": "sha_1",
            "tds_file_url": "url_1",
        }
    ]

    env_frames_helper.create_table(env_data, enable_change_data_feed=True)
    file_entries_helper.create_table(file_entries_data, enable_change_data_feed=False)

    # Run first time, table should be created. History shows one row with 'insert' change type.
    minimal_stream_table.run()

    result_df_1 = _read_table(spark_session_mock, ENV_TRAINING_NAME, with_changes=True)

    assert result_df_1.count() == 1
    expected_data = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
            "_change_type": "insert",
            # Not sure why commit version starts at 1, maybe because we create df empty and then write to it?
            "_commit_version": 1,
        }
    ]
    compare_dataframe(result_df_1, expected_data, all_columns_must_match=False)

    # Source has not changed, run again. No changes expected.
    minimal_stream_table.run()

    result_df_2 = _read_table(spark_session_mock, ENV_TRAINING_NAME, with_changes=True)
    assert result_df_2.count() == 1
    compare_dataframe(result_df_2, expected_data, all_columns_must_match=False)


def test_incremental_record_update(spark_session_mock: SparkSession, minimal_stream_table: MinimalTableV1) -> None:
    """Tests if the table is updated correctly when it already exists.

    The source table has changed since the last run, thus the target table should be updated.

    Source changes:
        - record updated
    Expected target changes:
        - CDF reflects changes
    """
    env_frames_helper = MockDataHelper(ENV_FRAMES_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)
    file_entries_helper = MockDataHelper(FILE_ENTRIES_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data_1 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
        }
    ]

    file_entries_data = [
        {
            "file_hash": "sha_1",
            "tds_file_url": "url_1",
        },
        {
            "file_hash": "sha_2",
            "tds_file_url": "url_2",
        },
    ]

    env_frames_helper.create_table(env_data_1, enable_change_data_feed=True)
    file_entries_helper.create_table(file_entries_data, enable_change_data_feed=False)

    # Run first time, table should be created. History shows one row with 'insert' change type.
    minimal_stream_table.run()

    result_df_1 = _read_table(spark_session_mock, ENV_TRAINING_NAME, with_changes=True)
    assert result_df_1.count() == 1
    expected_data_1 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
            "_change_type": "insert",
            "_commit_version": 1,
        }
    ]
    compare_dataframe(result_df_1, expected_data_1, all_columns_must_match=False)

    # Source has changed, run again. Target table should be updated.
    env_data_2 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_2",
        }
    ]
    env_frames_helper.add_data(env_data_2, merge_column="image_id")
    minimal_stream_table.run()

    result_df_2 = _read_table(spark_session_mock, ENV_TRAINING_NAME, with_changes=True)
    assert result_df_2.count() == 3

    expected_data_2 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
            "_change_type": "insert",
            "_commit_version": 1,
        },
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
            "_change_type": "update_preimage",
            "_commit_version": 2,
        },
        {
            "image_id": "id_1",
            "image_sha": "sha_2",
            "tds_file_url": "url_2",
            "_change_type": "update_postimage",
            "_commit_version": 2,
        },
    ]
    compare_dataframe(
        result_df_2,
        expected_data_2,
        sort_keys=["image_id", "_commit_version", "_change_type"],
        all_columns_must_match=False,
    )


def test_incremental_record_delete(spark_session_mock: SparkSession, minimal_stream_table: MinimalTableV1) -> None:
    """Tests if the table is updated correctly when it already exists.

    The source table has changed since the last run, thus the target table should be updated.

    Source changes:
        - record deleted
    Expected target changes:
        - CDF shows delete
    """
    env_frames_helper = MockDataHelper(ENV_FRAMES_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)
    file_entries_helper = MockDataHelper(FILE_ENTRIES_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data_1 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
        },
        {
            "image_id": "id_2",
            "image_sha": "sha_2",
        },
    ]

    file_entries_data = [
        {
            "file_hash": "sha_1",
            "tds_file_url": "url_1",
        },
        {
            "file_hash": "sha_2",
            "tds_file_url": "url_2",
        },
    ]

    env_frames_helper.create_table(env_data_1, enable_change_data_feed=True)
    file_entries_helper.create_table(file_entries_data, enable_change_data_feed=False)

    # Run first time, table should be created. History shows one row with 'insert' change type.
    minimal_stream_table.run()

    result_df_1 = _read_table(spark_session_mock, ENV_TRAINING_NAME, with_changes=True)
    assert result_df_1.count() == 2
    expected_data_1 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
            "_change_type": "insert",
            "_commit_version": 1,
        },
        {
            "image_id": "id_2",
            "image_sha": "sha_2",
            "tds_file_url": "url_2",
            "_change_type": "insert",
            "_commit_version": 1,
        },
    ]
    compare_dataframe(result_df_1, expected_data_1, all_columns_must_match=False)

    # Source has changed, run again. Target table should be updated.
    env_frames_helper.delete_record("image_id", "id_1")
    minimal_stream_table.run()

    result_df_2 = _read_table(spark_session_mock, ENV_TRAINING_NAME, with_changes=True)
    assert result_df_2.count() == 3

    expected_data_2 = [
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
            "_change_type": "insert",
            "_commit_version": 1,
        },
        {
            "image_id": "id_1",
            "image_sha": "sha_1",
            "tds_file_url": "url_1",
            "_change_type": "delete",
            "_commit_version": 2,
        },
        {
            "image_id": "id_2",
            "image_sha": "sha_2",
            "tds_file_url": "url_2",
            "_change_type": "insert",
            "_commit_version": 1,
        },
    ]
    compare_dataframe(
        result_df_2,
        expected_data_2,
        sort_keys=["image_id", "_commit_version", "_change_type"],
        all_columns_must_match=False,
    )
