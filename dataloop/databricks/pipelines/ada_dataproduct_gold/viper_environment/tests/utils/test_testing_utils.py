"""Tests for the testing utilities."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import pytest
from pyspark.sql import SparkSession
from pyspark.sql.types import IntegerType, StringType, StructField, StructType
from utils.testing_utils import MockDataHelper, compare_dataframe

SILVER_ENV_TABLE_NAME = "slim_environment_classification"


MOCK_SLIM_ENV_SCHEMA = StructType(
    [
        StructField("image_id", StringType(), False),
        StructField("revision", IntegerType(), False),
        StructField("road_type", StringType(), False),
    ]
)


TABLE_NAME_SCHEMA_MAP = {
    SILVER_ENV_TABLE_NAME: MOCK_SLIM_ENV_SCHEMA,
}


def test_mock_data_helper_create(spark_session_mock: SparkSession) -> None:
    """Test that the target table is created if it does not exist."""
    env_table_helper = MockDataHelper(SILVER_ENV_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data = [{"image_id": "id_1", "revision": 1, "road_type": "Highway"}]

    env_table_helper.create_table(env_data)

    df = spark_session_mock.sql(f"SELECT * FROM {SILVER_ENV_TABLE_NAME}")

    compare_dataframe(df, env_data)


def test_mock_data_helper_add_data(spark_session_mock: SparkSession) -> None:
    """Test that data is added or updated to the target table."""
    env_table_helper = MockDataHelper(SILVER_ENV_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data_1 = [{"image_id": "id_1", "revision": 1, "road_type": "Highway"}]
    env_table_helper.create_table(env_data_1)

    env_data_2 = [{"image_id": "id_2", "revision": 2, "road_type": "City"}]
    env_table_helper.add_data(env_data_2, "image_id")
    df = spark_session_mock.sql(f"SELECT * FROM {SILVER_ENV_TABLE_NAME}")
    compare_dataframe(df, env_data_1 + env_data_2)

    env_data_3 = [{"image_id": "id_1", "revision": 2, "road_type": "City"}]
    env_table_helper.add_data(env_data_3, "image_id")
    df = spark_session_mock.sql(f"SELECT * FROM {SILVER_ENV_TABLE_NAME}")
    compare_dataframe(df, env_data_2 + env_data_3)


def test_mock_data_helper_delete_record(spark_session_mock: SparkSession) -> None:
    """Test that a record is deleted from the target table."""
    env_table_helper = MockDataHelper(SILVER_ENV_TABLE_NAME, spark_session_mock, TABLE_NAME_SCHEMA_MAP)

    env_data = [{"image_id": "id_1", "revision": 1, "road_type": "Highway"}]
    env_table_helper.create_table(env_data)

    env_table_helper.delete_record("image_id", "id_1")
    df = spark_session_mock.sql(f"SELECT * FROM {SILVER_ENV_TABLE_NAME}")
    compare_dataframe(df, [])


def test_compare_dataframe_works_as_expected(spark_session_mock: SparkSession) -> None:
    """Test that the compare_dataframe function works as expected."""
    data = [{"image_id": "id_1", "revision": 1, "road_type": "Highway"}]
    df = spark_session_mock.createDataFrame(data, MOCK_SLIM_ENV_SCHEMA)

    compare_dataframe(df, data)


def test_compare_dataframe_works_as_expected_sort_keys(spark_session_mock: SparkSession) -> None:
    """Test that the compare_dataframe function works as expected."""
    data = [
        {"image_id": "id_1", "revision": 1, "road_type": "Highway"},
        {"image_id": "id_1", "revision": 2, "road_type": "City"},
    ]
    df = spark_session_mock.createDataFrame(data, MOCK_SLIM_ENV_SCHEMA)

    compare_dataframe(df, data, sort_keys=["image_id", "revision"])


def test_compare_dataframe_raises_when_data_mismatches(spark_session_mock: SparkSession) -> None:
    """Test that the compare_dataframe function raises an AssertionError when the data mismatches."""
    data = [{"image_id": "id_1", "revision": 1, "road_type": "Highway"}]
    df = spark_session_mock.createDataFrame(data, MOCK_SLIM_ENV_SCHEMA)

    data_2 = [{"image_id": "id_1", "revision": 2, "road_type": "City"}]
    pytest.raises(AssertionError, compare_dataframe, df, data_2)
