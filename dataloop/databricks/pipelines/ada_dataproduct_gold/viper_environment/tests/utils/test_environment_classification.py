"""Tests for environment classification utils."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql import SparkSession
from utils.environment_classification import add_usage_column
from utils.testing_utils import compare_dataframe


def test_usage_column(spark_session_mock: SparkSession) -> None:
    """Test the usage column addition."""
    data = [
        ("ab", "sha1", ["fog_detection_train_prod", "fog_detection_train_other"], 1),
        ("da", "sha2", ["fog_detection_train_other", "fog_detection_val_prod"], 2),
        ("af", "sha3", ["fog_detection_train_other"], 3),
        ("fa", "sha4", ["fog_detection_tiny_val_prod"], 4),
        ("ca", "sha5", None, 5),
        ("df", "sha6", ["fog_detection_calibration_other", "fog_detection_train_otherv2"], 5),
        ("aa", "sha7", [], 1337),
    ]

    schema = ["image_id", "sha", "split_fog_detection", "important_id"]

    df = spark_session_mock.createDataFrame(data, schema=schema)
    task = "fog_detection"
    df = add_usage_column(df, task)

    expected_data = [
        {
            "image_id": "ab",
            "sha": "sha1",
            "split_fog_detection": ["fog_detection_train_prod", "fog_detection_train_other"],
            "important_id": 1,
            "usage_fog_detection": "train",
        },
        {
            "image_id": "da",
            "sha": "sha2",
            "split_fog_detection": ["fog_detection_train_other", "fog_detection_val_prod"],
            "important_id": 2,
            "usage_fog_detection": "val",
        },
        {
            "image_id": "af",
            "sha": "sha3",
            "split_fog_detection": ["fog_detection_train_other"],
            "important_id": 3,
            "usage_fog_detection": None,
        },
        {
            "image_id": "fa",
            "sha": "sha4",
            "split_fog_detection": ["fog_detection_tiny_val_prod"],
            "important_id": 4,
            "usage_fog_detection": "tiny_val",
        },
        {"image_id": "ca", "sha": "sha5", "split_fog_detection": None, "important_id": 5, "usage_fog_detection": None},
        {
            "image_id": "df",
            "sha": "sha6",
            "split_fog_detection": ["fog_detection_calibration_other", "fog_detection_train_otherv2"],
            "important_id": 5,
            "usage_fog_detection": None,
        },
        {"image_id": "aa", "sha": "sha7", "split_fog_detection": [], "important_id": 1337, "usage_fog_detection": None},
    ]

    compare_dataframe(df, expected_data)
