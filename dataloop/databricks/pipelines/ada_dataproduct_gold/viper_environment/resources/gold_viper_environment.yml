variables:
  env:
    default: dev
    description: "environment, dev, qa, prod"
  catalog:
    default: gold
    description: "catalog name"
  spark_version:
    default: "15.4.x-scala2.12"
    description: "Spark version"
  schema:
    default: "viper_environment"
    description: "schema of the table"
  overwrite:
    default: False
    description: "Overwrite the existing table"
  pause_status:
    description: Is pipeline "PAUSED"/"UNPAUSED"
    default: "PAUSED"
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E16ads_v5_rt14.3"
  driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E4ads_v5_rt15.4"

resources:
  jobs:
    viper_environment_gold:
      permissions:
        - group_name: "sg-pace-github-ViPer-Environment-reader"
          level: CAN_MANAGE_RUN
      name: "Viper Environment - Gold table creation"
      schedule:
        quartz_cron_expression: "0 0 19 ? * TUE,THU,SUN *"
        timezone_id: UTC
        pause_status: ${var.pause_status}
      timeout_seconds: 12600 # 3.5 hours

      tasks:
        - task_key: environment_classification_frames_cdf
          spark_python_task:
            python_file: ../src/cdf_environment_classification_frames.py
            parameters:
              - --env
              - ${var.env}
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --overwrite
              - ${var.overwrite}
          job_cluster_key: viper_environment_job_cluster

        - task_key: environment_classification_training_cdf
          spark_python_task:
            python_file: ../src/cdf_environment_classification_training.py
            parameters:
              - --env
              - ${var.env}
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --overwrite
              - ${var.overwrite}
          job_cluster_key: viper_environment_job_cluster
          depends_on:
            - task_key: environment_classification_frames_cdf

        - task_key: environment_classification_frames
          spark_python_task:
            python_file: ../src/environment_classification_frames.py
            parameters:
              - --env
              - ${var.env}
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --overwrite
              - ${var.overwrite}
          job_cluster_key: viper_environment_job_cluster

        - task_key: environment_classification_training
          spark_python_task:
            python_file: ../src/environment_classification_training.py
            parameters:
              - --env
              - ${var.env}
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --overwrite
              - ${var.overwrite}
          job_cluster_key: viper_environment_job_cluster
          depends_on:
            - task_key: environment_classification_frames

        - task_key: blockage_label_info
          spark_python_task:
            python_file: ../src/blockage_label_info.py
            parameters:
              - --env
              - ${var.env}
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --overwrite
              - ${var.overwrite}
          job_cluster_key: viper_environment_job_cluster
          libraries:
            - pypi:
                package: xflow-data-formats==0.1.dev1+f685adfe

        - task_key: blockage_frames
          spark_python_task:
            python_file: ../src/blockage_frames.py
            parameters:
              - --env
              - ${var.env}
              - --catalog
              - ${var.catalog}
              - --schema
              - ${var.schema}
              - --overwrite
              - ${var.overwrite}
          job_cluster_key: viper_environment_job_cluster
          depends_on:
            - task_key: blockage_label_info
          libraries:
            - pypi:
                package: xflow-data-formats==0.1.dev1+f685adfe
            - pypi:
                package: xflow-label-set==0.1.dev1+7293327c

        # - task_key: delete_tables
        #   spark_python_task:
        #     python_file: ../src/delete_tables.py
        #     parameters:
        #       - --catalog
        #       - ${var.catalog}
        # job_cluster_key: viper_environment_job_cluster

      job_clusters:
        - job_cluster_key: viper_environment_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            azure_attributes:
              first_on_demand: 1
              availability: ON_DEMAND_AZURE
              spot_bid_max_price: -1
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            num_workers: 2
            enable_elastic_disk: true
            init_scripts:
              - volumes:
                  destination: "/Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh"
            spark_env_vars:
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
            data_security_mode: USER_ISOLATION
            runtime_engine: PHOTON
