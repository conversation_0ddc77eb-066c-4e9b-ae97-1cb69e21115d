"""Spark accessor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Optional

from databricks.connect import DatabricksSession
from pyspark.sql import SparkSession


def get_or_create_databricks_session(
    databricks_profile_name: Optional[str] = None,
) -> SparkSession:
    """Gets or creates a Databricks SparkSession.

    Optionally, you can provide a Databricks CLI profile name. The profile is then used to retrieve the SparkSession.

    Args:
        databricks_profile_name (Optional[str], optional): Name of a configured
            Databricks Profile.

    Returns:
        SparkSession: SparkSession created
    """
    if databricks_profile_name is None:
        return DatabricksSession.builder.getOrCreate()
    else:
        return DatabricksSession.builder.profile(databricks_profile_name).getOrCreate()


def is_running_on_databricks(spark_session: SparkSession) -> bool:
    """Checks if this is executed within Databricks.

    Args:
        spark_session (SparkSession): Spark Session to test.

    Returns:
        bool: true is returned if this is natively executed on Databricks,
            otherwise false. E.g. execution on a notebook will result in false.
    """
    return spark_session.conf.get("spark.databricks.service.client.enabled") == "true"
