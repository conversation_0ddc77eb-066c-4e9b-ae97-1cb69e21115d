"""Base class for ETL jobs in LAPI NG Sync Jobs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import os
from argparse import ArgumentParser, Namespace
from typing import Any, Dict, Optional, Union
from urllib.parse import quote_plus

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, to_json
from pyspark.sql.streaming.readwriter import DataStreamReader
from pyspark.sql.types import ArrayType, NullType, StringType, StructField, StructType
from shared.secrets_accessors import DatabricksSecretAccessor
from shared.shared_definitions import Environment, JobConstants
from shared.spark_accessor import get_or_create_databricks_session


class ETLJob:
    """Base class for ETL jobs.

    This class provides the foundational structure for ETL (Extract, Transform, Load) jobs,
    including argument parsing, Spark session initialization, and secret management.

    Attributes:
        args (Namespace): Parsed command-line arguments.
        spark_session (SparkSession): Spark session for executing Spark jobs.
        secrets_accessor (DatabricksSecretAccessor): Accessor for retrieving secrets from Databricks.
        connection_string (Optional[str]): MongoDB connection string.
        target_catalog_name (Optional[str]): Target catalog name for storing output.
        target_schema_name (Optional[str]): Target schema name for storing output.
        logger (logging.Logger): Logger for logging messages.
    """

    def __init__(self, args: Optional[Namespace] = None) -> None:
        """Initializes the ETLJob with the provided arguments."""
        self.args: Namespace = self.parse_arguments(additional_args=args)
        self.spark_session: SparkSession = get_or_create_databricks_session(self.args.run_local_profile)
        self.secrets_accessor: DatabricksSecretAccessor = DatabricksSecretAccessor(
            spark_session=self.spark_session, scope=JobConstants.DBX_SECRETS_SCOPE.value
        )

        self.connection_string: Optional[str] = None
        self.target_catalog_name: Optional[str] = self.args.target_catalog or self.spark_session.conf.get(
            "catalog.name"
        )
        self.target_schema_name: Optional[str] = self.args.target_schema or self.spark_session.conf.get("schema.name")
        self.env: Optional[str] = self.args.environment or self.spark_session.conf.get("instance.name")

        self.logger: logging.Logger = self._initialize_logger()

    @staticmethod
    def parse_arguments(additional_args: Optional[Union[Dict[str, Any], Namespace]] = None) -> Namespace:
        """Parses command-line arguments for the ETL job.

        This method sets up the argument parser and defines the expected arguments
        for running the ETL job, including environment, local profile, target catalog,
        and target schema. Additional arguments can be passed via a dictionary or Namespace.

        Args:
            additional_args (Optional[Union[Dict[str, Any], Namespace]], optional):
            Additional arguments to be added to the parser.

        Returns:
            Namespace: Parsed command-line arguments.
        """
        parser: ArgumentParser = ArgumentParser(description="LAPI NG Bronze Sync Job")

        parser.add_argument(
            "-e",
            "--env",
            action="store",
            dest="environment",
            type=Environment,
            choices=list(map(lambda x: x.value, Environment)),
            help="Run in environment",
        )
        parser.add_argument(
            "--run-local-profile",
            dest="run_local_profile",
            action="store",
            help="Provide the databricks-cli profile to use for local execution",
        )
        parser.add_argument(
            "--target-catalog",
            dest="target_catalog",
            action="store",
            help="Databricks catalog for storing output on local run",
        )
        parser.add_argument(
            "--target-schema",
            dest="target_schema",
            action="store",
            help="Databricks schema for storing output on local run",
        )

        if additional_args:
            if isinstance(additional_args, dict):
                for arg, params in additional_args.items():
                    parser.add_argument(arg, **params)
            elif isinstance(additional_args, Namespace):
                parser.parse_args(namespace=additional_args)

        args, _ = parser.parse_known_args()
        return args

    @staticmethod
    def _initialize_logger() -> logging.Logger:
        """Initializes the logger for the ETL job.

        Returns:
            logging.Logger: Configured logger instance.
        """
        logging.basicConfig(
            level=logging.INFO,
            format="[%(levelname)s][%(asctime)s][%(name)s][%(module)s:%(lineno)d][%(funcName)s] %(message)s",
        )
        logger = logging.getLogger(JobConstants.JOB_NAME.value)
        logging.getLogger("azure").setLevel(logging.WARN)
        logging.getLogger("urllib3.connectionpool").setLevel(logging.WARN)

        logger.info("Starting %s", JobConstants.JOB_NAME.value)
        return logger

    def initialize_connection(self) -> None:
        """Initializes the MongoDB connection string using secrets stored in Databricks.

        This method retrieves the MongoDB username, password, and connection string template
        from Databricks secrets and formats the connection string.
        """
        db_username = self.secrets_accessor.get_secret(secret_name="mongodb--lapi--username")
        db_password = quote_plus(self.secrets_accessor.get_secret(secret_name="mongodb--lapi--password"))
        connection_string_template = self.secrets_accessor.get_secret(secret_name="mongodb--lapi--conn-str")
        self.connection_string = connection_string_template.format(DB_USERNAME=db_username, DB_PWD=db_password)

    def run(self) -> None:
        """Runs the ETL job. This method should be implemented by subclasses."""
        raise NotImplementedError()

    def read_mongo_data(
        self, database: str, collection: str, load_data: bool = True
    ) -> Optional[Union[DataFrame, DataStreamReader]]:
        """Reads data from MongoDB and returns it as a DataFrame.

        Args:
            database (str): Name of the MongoDB database.
            collection (str): Name of the MongoDB collection.
            load_data (bool, optional): Whether to load the data immediately. Defaults to True.

        Returns:
            Optional[DataFrame]: DataFrame containing the read data, or None if an error occurs.
        """
        if not self.connection_string:
            self.logger.error("Connection string is not established")
            raise NotImplementedError("Connection string is not established")
        reader = (
            self.spark_session.readStream.format("mongodb")
            .option("spark.mongodb.connection.uri", self.connection_string)
            .option("spark.mongodb.readPreference.name", "secondary")
            .option("spark.mongodb.database", database)
            .option("spark.mongodb.collection", collection)
            .option("spark.mongodb.change.stream.publish.full.document.only", "true")
            .option("spark.mongodb.change.stream.micro.batch.max.partition.count", 500)
        )

        if load_data:
            try:
                read_data = reader.load().withColumnRenamed("ns", "namespace").withColumnRenamed("_id", "objectID")
                self.logger.info(f"Successfully read data from {database}.{collection}")
                return read_data
            except Exception as e:
                self.logger.error(f"Error reading data from {database}.{collection}: {e}")
                return None
        else:
            self.logger.info(f"Returning DataFrameReader for {database}.{collection} to add more parameters")
            return reader


class DeltaTableWriter:
    """Class for writing data to Delta tables."""

    def __init__(self, etl_job: ETLJob) -> None:
        """Initializes the DeltaTableWriter with the provided ETL job."""
        self.etl_job = etl_job
        self.spark_session = etl_job.spark_session
        self.logger = etl_job.logger
        self.delta_table = None

    def _check_if_volume_exists(self, commits_checkpoint_path: str) -> None:
        """Checks if the checkpoint path exists."""
        try:
            if not os.path.exists(commits_checkpoint_path):
                os.makedirs(commits_checkpoint_path)
        except Exception as ex:
            self.logger.info(f"Checkpoint path does not exist: {commits_checkpoint_path}.")
            self.logger.debug(f"More details: {ex}.")
            raise ex

    def create_table_if_not_exists(self, schema: StructType) -> None:
        """Creates a Delta table if it does not already exist.

        Args:
            schema (StructType): Schema of the Delta table.
        """
        schema_str = ", ".join([f"`{field.name}` {field.dataType.simpleString()}" for field in schema.fields])
        self.logger.debug(f"Creating Delta table: {schema_str}")
        self.spark_session.sql(f"CREATE TABLE IF NOT EXISTS {self.delta_table} ({schema_str})")

    def write_to_delta_table(self, read_data: DataFrame) -> None:
        """Writes data to a Delta table.

        Args:
            read_data (DataFrame): DataFrame containing the data to write.
        """
        try:
            self.logger.info(
                "Write output to catalog '%s' and schema '%s'",
                self.etl_job.target_catalog_name,
                self.etl_job.target_schema_name,
            )
            # Check better way to handle histories
            # read_data = read_data.withColumn("record_created_date", current_timestamp().cast("date"))
            read_data = self.jsonify_complex_types(read_data)
            self._write_data_to_table(read_data)
        except Exception as e:
            self.logger.error(f"Error writing to Delta table: {e}")
            if "Table or view not found" in str(e):
                self._handle_table_not_found(read_data)
            elif "DELTA_COMPLEX_TYPE_COLUMN_CONTAINS_NULL_TYPE" in str(e):
                self._handle_null_type_in_schema(read_data)
            elif "DATA_SOURCE_NOT_FOUND" in str(e):
                raise Exception(f"Could not connect to MongoDB: {e}")
            else:
                raise e

    def _write_data_to_table(self, read_data: DataFrame, checkpoint_location: Optional[str] = None) -> None:
        """Helper method to write data to a Delta table.

        Args:
            read_data (DataFrame): DataFrame containing the data to write.
            checkpoint_location (Optional[str]): Checkpoint location for the write operation.
        """
        if self.delta_table is None:
            raise ValueError("Delta table name is not set")

        catalog_name, schema_name, table_name = self.delta_table.split(".")
        if not checkpoint_location:
            checkpoint_location = f"/Volumes/{catalog_name}/lapi_ng/checkpoint_locations/{table_name}"

        self._check_if_volume_exists(checkpoint_location)
        self.logger.info(f"Attempting to write to Delta table: {self.delta_table}")
        streaming_query = (
            read_data.writeStream.format("delta")
            .option("mergeSchema", True)
            .option("overwriteSchema", False)
            .option("checkpointLocation", checkpoint_location)
            .outputMode("append")
            .trigger(once=True)
            .table(self.delta_table)
        )
        streaming_query.awaitTermination()

        if streaming_query.lastProgress and "numInputRows" in streaming_query.lastProgress:
            num_new_rows = streaming_query.lastProgress["numInputRows"]
            print(f'{num_new_rows} new row(s) added to the {schema_name} table "{self.delta_table}".')
        else:
            print(f'No progress information available for table "{self.delta_table}".')

    def _handle_table_not_found(self, read_data: DataFrame) -> None:
        """Handles the case where the Delta table is not found by creating the table and retrying the write.

        Args:
            read_data (DataFrame): DataFrame containing the data to write.
        """
        self.logger.warning(f"Table {self.delta_table} not found. Creating table.")
        self.create_table_if_not_exists(read_data.schema)
        self.logger.info(f"Retrying to write to Delta table: {self.delta_table}")
        self._write_data_to_table(read_data)

    def replace_null_types(self, schema: StructType) -> StructType:
        """Replaces NullType fields in the schema with StringType."""
        self.logger.debug("Replacing null types at field level")
        fields = []
        for field in schema.fields:
            if isinstance(field.dataType, NullType):
                fields.append(StructField(field.name, StringType(), field.nullable))
            elif isinstance(field.dataType, ArrayType):
                element_type = field.dataType.elementType
                if isinstance(element_type, NullType):
                    fields.append(StructField(field.name, ArrayType(StringType()), field.nullable))
                else:
                    fields.append(StructField(field.name, StringType(), field.nullable))
            elif isinstance(field.dataType, StructType):
                fields.append(StructField(field.name, StringType(), field.nullable))
            else:
                fields.append(field)
        return StructType(fields)

    def jsonify_complex_types(self, df: DataFrame) -> DataFrame:
        """Converts complex types in the DataFrame to JSON strings."""
        self.logger.info("jsonify-ing schema")
        for field in df.schema.fields:
            if isinstance(field.dataType, (StructType, ArrayType)):
                df = df.withColumn(field.name, to_json(col(field.name)))
        return df

    def _handle_null_type_in_schema(self, read_data: DataFrame) -> None:
        """Handles the case where the schema contains NullType fields by replacing them and retrying the write.

        Args:
            read_data (DataFrame): DataFrame containing the data to write.
        """
        try:
            self.logger.warning("Schema contains NullType. Replacing NullType in schema.")
            new_schema = self.replace_null_types(read_data.schema)
            read_data = read_data.select(
                [col(field.name).cast(new_schema[field.name].dataType) for field in new_schema.fields]
            )
            self.logger.info(f"Retrying to write to Delta table with new schema: {self.delta_table}")
            self._write_data_to_table(read_data)
        except Exception as e:
            self.logger.error(e)
            self.logger.error(f"Error writing to Delta table even after replacing NullType in {self.delta_table}")
