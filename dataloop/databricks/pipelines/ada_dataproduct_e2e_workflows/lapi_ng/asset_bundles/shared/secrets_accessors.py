"""Accessor for secret stores in LAPI."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import abc

from pyspark.sql import SparkSession


class AbstractSecretAccessor(object, metaclass=abc.ABCMeta):
    """Abstract class for a secret accessor.

    You need to implement the method get_secret(secret_name: str) -> str that returns
    the secret from a secret store.
    """

    @abc.abstractmethod
    def get_secret(self, secret_name: str) -> str:
        """Get secret identified by secret_name from a secret store backend.

        Args:
            secret_name (str): Identifier of the secret

        Raises:
            NotImplementedError: As this is an abstract class, failure is a
                given

        Returns:
            str: The actual secret.
        """
        raise NotImplementedError("This is an Abstract. " "Use an actual implementation")


class DatabricksSecretAccessor(AbstractSecretAccessor):
    """Implementation of the AbstractSecretAccessor that access secrets within Databricks secret scopes.

    Args:
        AbstractSecretAccessor (_type_): Abstract Base Class
    """

    def __init__(self, spark_session: SparkSession, scope: str):
        """Constructor for Databricks Secret Accessor. Methods used here are intended to be overridden by subclasses.

        Args:
            spark_session (SparkSession): The Spark session to use for the operations.
            scope (str): Name of Databricks Secrets Scope to encapsulate
        """
        self._scope = scope
        self._spark = spark_session

    def get_secret(self, secret_name: str) -> str:
        """Get secret identified by secret_name from Databricks Secret Scope.

        Args:
            secret_name (str): Identified of the Databricks Secret.

        Returns:
            str: The actual secret
        """
        from pyspark.dbutils import DBUtils

        dbutils = DBUtils(self._spark)
        return dbutils.secrets.get(scope=self._scope, key=secret_name)
