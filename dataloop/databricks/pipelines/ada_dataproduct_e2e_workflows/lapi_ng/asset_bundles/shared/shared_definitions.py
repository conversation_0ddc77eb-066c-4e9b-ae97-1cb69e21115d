"""Shared definitions for this LAPI ETL."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from enum import Enum


class Environment(Enum):
    """Environments identifier."""

    DEV = "dev"
    QA = "qa"
    PROD = "prod"


class JobConstants(Enum):
    """Job-related constants."""

    JOB_NAME = "LAPI NG Datasets ETL"
    DBX_SECRETS_SCOPE = "lapi"


class MongoDBConstants(Enum):
    """MongoDB-related constants."""

    MONGODB_COLLECTIONS = {
        "prod": {
            "label-request-manager-prod": ["label_requests", "event_logs"],
            "asset-manager-prod": ["assets"],
            "workflow-manager-prod": ["job-schedules", "workflows"],
        },
        "qa": {
            "label-request-manager-qa": ["label_requests", "event_logs"],
            "asset-manager-qa": ["assets"],
            "workflow-manager-qa": ["job-schedules", "workflows"],
        },
        "dev": {
            "label-request-manager-ve605b0": ["label_requests", "event_logs"],
            "asset-manager-ve605b0": ["assets"],
            "workflow-manager-ve605b0": ["job-schedules", "workflows"],
        },
    }
