# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

# This is a Databricks asset bundle definition for LAPI ETL workflows.
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.

bundle:
  name: lapi_ng_etl

include:
  - lapi_bronze/resources/*.yml

variables:
  # Basic variables for the LAPI ETL workflow
  cron_expression:
    description: scheduler expression for job
    default: '0 0 0 ? * * *' # run at midnight every day
  schedule_pause_status:
    default: "UNPAUSED"
    description: "Schedule pause status"
  target_catalog_name:
    description: DBX - Catalog (Bronze)
    default: bronze_dev
  schema_name:
    description: DBX - Schema
    default: lapi_ng
  job_name_prefix:
    description: Job name prefix to add environment as well as the slot name
    default: ''
  development_mode:
    description: Development mode flag
    default: true

  # Variables for system configration for the LAPI ETL workflow
  computer_node:
    description: Computer node id for executing the LAPI database extraction - dbx runtime v13.3
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  env:
    default: dev
    description: "Environment, dev, qa, prod"
  run_sp:
    description: "Run as service principal"
    lookup:
      service_principal: "sp-pace-dataloop-lapi-dbx-${var.env}"

sync:
  include:
    - shared/*

targets:
  # The 'local' target, for development purposes. This target is the default.
  local:
    # We use 'mode: local' to indicate this is a personal development copy:
    # - Deployed resources get prefixed with '[dev my_user_name]'
    # - Any job schedules and triggers are paused by default
    # - The 'development' mode is used for Delta Live Tables pipelines
    mode: development
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net # datalake-dev
    variables:
      schedule_pause_status: "PAUSED"
      env: dev
      development_mode: true
      target_catalog_name: bronze_dev
      job_name_prefix: local

  # We use 'mode: dev' to indicate this is a production deployment.
  dev:
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net # datalake-dev
      root_path: /Jobs/ada_dataproduct_e2e_workflows/lapi_ng/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
    permissions:
      - group_name: sg-pace-github-Data_Delivery-LAPI-reader
        level: CAN_VIEW
      - group_name: sg-pace-github-Data-Delivery-LAPI-Developers-all
        level: CAN_VIEW
      - group_name: sg-pace-github-Viper-Data_Strategy_team-admin
        level: CAN_VIEW
      - group_name: sg-pace-github-Viper-Data_Strategy_team-reader
        level: CAN_VIEW
    variables:
      schedule_pause_status: "PAUSED"
      env: dev
      development_mode: true
      target_catalog_name: bronze_dev
      job_name_prefix: "dev_staging"

  # We use 'mode: qa' to indicate this is a production deployment.
  qa:
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net # datalake-qa
      root_path: /Jobs/ada_dataproduct_e2e_workflows/lapi_ng/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
    permissions:
      - group_name: sg-pace-github-Data_Delivery-LAPI-reader
        level: CAN_VIEW
      - group_name: sg-pace-github-Data-Delivery-LAPI-Developers-all
        level: CAN_VIEW
      - group_name: sg-pace-github-Viper-Data_Strategy_team-admin
        level: CAN_VIEW
      - group_name: sg-pace-github-Viper-Data_Strategy_team-reader
        level: CAN_VIEW
    variables:
      env: qa
      target_catalog_name: bronze_qa
      development_mode: true
      job_name_prefix: "pace_qa"

  # We use 'mode: prod' to indicate this is a production deployment.
  prod:
    # We use 'mode: production' to indicate this is a production deployment.
    # Doing so enables strict verification of the settings below.
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net # datalake-prod
      root_path: /Jobs/ada_dataproduct_e2e_workflows/lapi_ng/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
    permissions:
      - group_name: sg-pace-github-Data_Delivery-LAPI-reader
        level: CAN_VIEW
      - group_name: sg-pace-github-Data-Delivery-LAPI-Developers-all
        level: CAN_VIEW
      - group_name: sg-pace-github-Viper-Data_Strategy_team-admin
        level: CAN_VIEW
      - group_name: sg-pace-github-Viper-Data_Strategy_team-reader
        level: CAN_VIEW
    variables:
      env: prod
      cron_expression: '0 0 0/6 ? * * *' # run every 6 hours
      target_catalog_name: bronze
      job_name_prefix: "pace_prod"
