"""This module contains the LAPINGBronzeSync class for synchronizing MongoDB data to Delta tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import os
import sys

# Append the correct path to the shared directory to sys.path
sys.path.append(os.path.abspath(os.path.join(os.getcwd(), "..", "..")))

from shared.etl_base import DeltaTableWriter, ETLJob  # noqa: E402
from shared.shared_definitions import MongoDBConstants  # noqa: E402


class LAPINGBronzeSync(ETLJob):
    """Class for synchronizing MongoDB data to Delta tables."""

    def __init__(self) -> None:
        """Initializes the LAPINGBronzeSync job."""
        super().__init__()
        self.delta_table_writer = DeltaTableWriter(self)
        self.initialize_connection()

    def run(self) -> None:
        """Reads data from MongoDB and writes to Delta tables."""

        delta_table_sub_path = f"{self.target_catalog_name}.{self.target_schema_name}"

        mongo_collection = MongoDBConstants.MONGODB_COLLECTIONS.value.get(self.env)
        self.logger.info(f"Current configration selected from {self.env} <> {mongo_collection}")

        for database, collections in mongo_collection.items():
            for collection in collections:
                self.logger.info(f"Reading data from {database}.{collection}")
                read_data = self.read_mongo_data(database=database, collection=collection, load_data=True)

                if read_data is None:
                    self.logger.warning(f"No data found in {database}.{collection}")
                    continue

                table_name = f"{delta_table_sub_path}.{collection}_{database.replace('-', '_')}"

                # Check if DataFrame has valid columns before writing
                if len(read_data.columns) == 0:
                    self.logger.warning(f"No valid columns found in {database}.{collection}")
                    continue

                # Write to delta table
                if read_data.schema and read_data.isStreaming:
                    self.delta_table_writer.delta_table = table_name
                    self.delta_table_writer.write_to_delta_table(read_data)
                else:
                    self.logger.warning("The DataFrame is empty or has an invalid schema.")


if __name__ == "__main__":
    job = LAPINGBronzeSync()
    job.run()
