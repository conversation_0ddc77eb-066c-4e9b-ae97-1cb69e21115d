# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

# Extraction job for LAPI NG
resources:
  jobs:
    lapi_ng_silver_extraction_job:
      name: "lapi_ng_${var.job_name_prefix}_extraction_job"

      schedule:
        # run nightly once a day
        quartz_cron_expression: ${var.cron_expression}
        timezone_id: Europe/Amsterdam

      tasks:
        - task_key: "${var.job_name_prefix}_db_extraction"
          timeout_seconds: 3600
          job_cluster_key: lapi_sql_extraction_cluster
          libraries:
            - maven:
                coordinates: "org.mongodb.spark:mongo-spark-connector_2.12:10.3.0"
          spark_python_task:
            python_file: ../src/lapi_ng_etl.py

      job_clusters:
        - job_cluster_key: lapi_sql_extraction_cluster
          new_cluster:
            num_workers: 0
            spark_version: 15.4.x-scala2.12
            instance_pool_id: ${var.computer_node}
            driver_instance_pool_id: ${var.computer_node}
            spark_conf:
              spark.master: local[*, 4]
              spark.databricks.cluster.profile: singleNode
              catalog.name: ${var.target_catalog_name}
              schema.name: ${var.schema_name}
              historytables.retention.days: ${var.retention_days_history_tables}
              historytables.retention.enabled: ${var.retention_enabled}
            custom_tags:
              ResourceClass: SingleNode
            data_security_mode: SINGLE_USER

      permissions:
        - group_name: sg-pace-github-Data_Delivery-LAPI-reader
          level: CAN_MANAGE_RUN
        - group_name: sg-pace-github-Data-Delivery-LAPI-Developers-all
          level: CAN_MANAGE_RUN
        - group_name: sg-pace-github-Viper-Data_Strategy_team-admin
          level: CAN_MANAGE_RUN
        - group_name: sg-pace-github-Viper-Data_Strategy_team-reader
          level: CAN_MANAGE_RUN
