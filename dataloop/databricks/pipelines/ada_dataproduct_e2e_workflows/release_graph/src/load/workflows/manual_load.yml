resources:
  jobs:
    manual_load:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "ADA Release Graph - Load - Manual"
      description: Load data into the graph
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "E2E"
        type: "Load"
        "ADA Release Graph": ""
        "Load": ""

      parameters:
        - name: version
          default: ""
        - name: graph
          default: "ada_release_v1"
        - name: mapping_file
          default: needs_release.j2

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: needs_load_manual
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - "{{job.parameters.graph}}"
              - --mapping_file
              - "{{job.parameters.mapping_file}}"
              - --version
              - "{{job.parameters.version}}"
          job_cluster_key: rdd_load_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_needs/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: rdd_load_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
