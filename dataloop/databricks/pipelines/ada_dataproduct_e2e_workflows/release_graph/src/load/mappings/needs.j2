MAPPING <urn:needs_edges>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_edges`
}
TO {
    ?start_iri ?type_iri ?target_iri
} WHERE {
    BIND (IRI(?start) AS ?start_iri)
    BIND (IRI(?target) AS ?target_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
}

;

MAPPING <urn:needs_properties>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_properties`
}
TO {
    ?start_iri ?type_iri ?target
} WHERE {
    BIND (IRI(?start) AS ?start_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
}
