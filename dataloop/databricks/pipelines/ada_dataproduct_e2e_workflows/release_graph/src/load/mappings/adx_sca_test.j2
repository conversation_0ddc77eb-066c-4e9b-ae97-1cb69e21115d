PREFIX : <urn:pace:ontology:adx:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>


MAPPING <urn:adx_sca>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`adx_sca_test`
}
TO {
  ?sca_component a :ScaComponent ;
    rdfs:label ?label ;
    :hasRevision ?revision ;
    :lastChanged ?date_last_changed ;
    :hasLabel ?label ;
    :hasName ?name ;
    :hasSourcePath ?source_path ;
    :hasBuildPath ?build_path ;
    :hasRule ?rule ;
    :hasBuildIssuesCount ?int_build_issues_count ;
    :hasScaIssuesCountBySeverity1 ?int_sca_issues_count_by_severity_1 ;
    :hasScaIssuesCountBySeverity2 ?int_sca_issues_count_by_severity_2 ;
    :hasScaIssuesCountBySeverity3 ?int_sca_issues_count_by_severity_3 ;
    :hasScaIssuesCountBySeverity4 ?int_sca_issues_count_by_severity_4 ;
    :hasScaIssuesCountBySeverity5 ?int_sca_issues_count_by_severity_5 ;
    :hasDoxygenCoverage ?d_doxygen_coverage ;
    :hasCodeOwners ?codeowners ;
    :hasDeployments ?deployments ;
    :hasTags ?tags .

  ?version_iri a pace:ArtifactVersion ;
    pace:validFor ?sca_component .
}

WHERE {
  BIND (template("urn:pace:ontology:adx:{name}_{revision}") AS ?sca_component)
  BIND (template("urn:pace:ontology:{revision}") AS ?version_iri)
  BIND (xsd:dateTime(?last_changed) AS ?date_last_changed)
  BIND (xsd:integer(?build_issues_count) AS ?int_build_issues_count)
  BIND (xsd:integer(?sca_issues_count_by_severity_1) AS ?int_sca_issues_count_by_severity_1)
  BIND (xsd:integer(?sca_issues_count_by_severity_2) AS ?int_sca_issues_count_by_severity_2)
  BIND (xsd:integer(?sca_issues_count_by_severity_3) AS ?int_sca_issues_count_by_severity_3)
  BIND (xsd:integer(?sca_issues_count_by_severity_4) AS ?int_sca_issues_count_by_severity_4)
  BIND (xsd:integer(?sca_issues_count_by_severity_5) AS ?int_sca_issues_count_by_severity_5)
  BIND (xsd:double(?doxygen_coverage) AS ?d_doxygen_coverage)
}
