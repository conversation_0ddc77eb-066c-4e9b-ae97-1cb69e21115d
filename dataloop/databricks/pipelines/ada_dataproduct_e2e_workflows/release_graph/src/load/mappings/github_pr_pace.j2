PREFIX : <urn:pace:ontology:github:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:github_pullrequests_pace>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`github_pullrequests`
}

TO {
  ?pr a :PullRequest ;
    :hasMergeSha ?commit_sha_iri ;
    :hasStatus ?state_iri ;
    :hasNumber ?int_number ;
    :closedAt ?formatted_closed_date ;
    :createdAt ?formatted_created_date ;
    :mergedAt ?formatted_merged_date ;
    :updatedAt ?formatted_updated_date ;
    :hasTitle ?title ;
    :hasHTMLUrl ?html_url ;
    :isLocked ?b_locked ;
    :hasMilestone ?milestone ;
    :isDraft ?b_draft ;
    :hasCommitUrl ?commits_url ;
    :hasLabel ?labels .
}

WHERE {
  BIND (template("urn:pace:ontology:github:PullRequest_{number}") AS ?pr)
  BIND (template("urn:pace:ontology:{commit_sha}") AS ?commit_sha_iri)
  BIND (template("urn:pace:ontology:github:{state}") AS ?state_iri)
  BIND (xsd:integer(?number) AS ?int_number)
  BIND (xsd:boolean(?draft) AS ?b_draft)
  BIND (xsd:boolean(?locked) AS ?b_locked)
  BIND (xsd:dateTime(?created_date) AS ?formatted_created_date)
  BIND (xsd:dateTime(?updated_date) AS ?formatted_updated_date)
  BIND (xsd:dateTime(?merged_date) AS ?formatted_merged_date)
  BIND (xsd:dateTime(?closed_date) AS ?formatted_closed_date)
}
