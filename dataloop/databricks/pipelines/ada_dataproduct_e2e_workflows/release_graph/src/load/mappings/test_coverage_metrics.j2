PREFIX : <urn:pace:ontology:test-metrics:>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:test_coverage_metrics>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`test_coverage_metrics`
  {% if version %} WHERE was_created_for = '{{ version }}' {% endif %}
}

TO {
  ?test_coverage_iri a :TestCoverage ;
    :datasource ?datasource ;
    :coveragePercentage ?int_percent_covered ;
    :wasCreatedFor ?was_created_for_iri .
}

WHERE {
  BIND (template("urn:pace:ontology:test-metrics:test_coverage_{was_created_for}_{datasource}") AS ?test_coverage_iri)
  BIND (template("urn:pace:ontology:{was_created_for}") AS ?was_created_for_iri)
  BIND (xsd:integer(?percent_covered) AS ?int_percent_covered)
}
