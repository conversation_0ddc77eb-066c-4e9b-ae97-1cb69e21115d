PREFIX : <urn:pace:ontology:ado:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:ado_workitems>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`ado_nodes`
}
TO {
  ?work_item a ?mapped_work_item_type ;
    :hasId ?id ;
    :hasStatus ?state ;
    :hasAreaPath ?area_path ;
    :hasSeverity ?severity ;
    :hasIteration ?iteration_path ;
    :hasReason ?reason ;
    :hasCreatedDate ?formatted_created_date ;
    :hasChangeDate ?formatted_changed_date ;
    :hasTitle ?title ;
    :hasActivatedDate ?formatted_activated_date ;
    :hasClosedDate ?formatted_closed_date ;
    :hasTags ?tags ;
    :hasKeyResultType ?key_result_type ;
    :hasKeyResultUnit ?key_result_unit ;
    :hasKeyResultDataSource ?key_result_data_source ;
    :hasKeyResultTargetValue ?d_key_result_target_value ;
    :hasBugSafetyRelevant ?bug_safety_relevant .
}
WHERE {
  BIND (template("urn:pace:ontology:ado:{id}") AS ?work_item)
  BIND (IRI(?ado_state) AS ?state)
  BIND (IRI(?ado_type) AS ?mapped_work_item_type)
  BIND (xsd:dateTime(?created_date) AS ?formatted_created_date)
  BIND (xsd:dateTime(?changed_date) AS ?formatted_changed_date)
  BIND (xsd:dateTime(?activated_date) AS ?formatted_activated_date)
  BIND (xsd:dateTime(?closed_date) AS ?formatted_closed_date)
  BIND (xsd:double(?key_result_target_value) AS ?d_key_result_target_value)
}

;

MAPPING <urn:ado_relations>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`ado_edges`
}
TO {
  ?formatted_source_iri ?type_iri ?formatted_target_iri .
}
WHERE {
  BIND (IRI(?source_iri) AS ?formatted_source_iri)
  BIND (IRI(?target_iri) AS ?formatted_target_iri)
  BIND (IRI(?type) AS ?type_iri)
}

;

MAPPING <urn:ado_key_results>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`ado_comments`
}
TO {
  ?kr_val_iri a :KeyResultValue ;
    :currentValue ?d_kr_cur_val ;
    :updatedAt ?formatted_updated_at .

  ?work_item :hasKeyResultValue ?kr_val_iri .
}
WHERE {
  BIND (template("urn:pace:ontology:ado:{id}") AS ?work_item)
  BIND (template("urn:pace:ontology:ado:KeyResultValue_{comment_id}") AS ?kr_val_iri)
  BIND (xsd:date(?updated_at) AS ?formatted_updated_at)
  BIND (xsd:double(?kr_value) AS ?d_kr_cur_val)
}

;

MAPPING <urn:ado_milestones>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`ado_milestones`
}
TO {
  ?work_item :needsLink ?needs_feature_iri .
}
WHERE {
  BIND (template("urn:pace:ontology:ado:{milestone_id}") AS ?work_item)
  BIND (template("urn:pace:ontology:needs:{needs_feature}") AS ?needs_feature_iri)
}
