PREFIX : <urn:pace:ontology:test-metrics:>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:tests_metrics>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`test_results_metrics`
  {% if version %} WHERE was_created_for = '{{ version }}' {% endif %}
}

TO {
  ?test_case_iri a :TestCase ;
    :datasource ?datasource ;
    :outcome ?outcome ;
    :duration ?float_duration ;
    :name ?name ;
    :createdAt ?formatted_created_at ;
    :type ?type ;
    :wasCreatedFor ?was_created_for_iri .
}

WHERE {
  BIND (template("urn:pace:ontology:test-metrics:{name}_{was_created_for}") AS ?test_case_iri)
  BIND (template("urn:pace:ontology:{was_created_for}") AS ?was_created_for_iri)
  BIND (xsd:dateTime(?created_at) AS ?formatted_created_at)
  BIND (xsd:float(?duration) AS ?float_duration)
}
