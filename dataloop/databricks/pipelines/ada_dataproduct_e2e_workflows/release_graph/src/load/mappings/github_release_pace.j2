PREFIX : <urn:pace:ontology:github:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:github_releases>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`github_releases`
}

TO {
  ?release a :Release ;
    :isRepresentationOf ?commit_sha ;
    :hasTagName ?tag_name ;
    :hasName ?name ;
    :hasTagUrl ?url ;
    :hasZipBallUrl ?zipball_url ;
    :hasTarBallUrl ?tarball_url ;
    :hasNodeId ?node_id ;
    :createdAt ?formatted_created_date ;
    :hasId ?id .
}

WHERE {
  BIND (template("urn:pace:ontology:github:Release_{id}") AS ?release)
  BIND (template("urn:pace:ontology:{commit_sha}") AS ?commit_sha)
  BIND (xsd:dateTime(?created_date) AS ?formatted_created_date)
}
