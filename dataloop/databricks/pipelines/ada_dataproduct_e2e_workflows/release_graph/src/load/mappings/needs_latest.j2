PREFIX : <urn:pace:ontology:>

MAPPING <urn:needs_edges>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_edges_latest`
}
TO {
    ?start_iri ?type_iri ?target_iri
} WHERE {
    BIND (IRI(?start) AS ?start_iri)
    BIND (IRI(?target) AS ?target_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
}

;

MAPPING <urn:needs_properties>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_properties_latest`
}
TO {
    ?start_iri ?type_iri ?target
} WHERE {
    BIND (IRI(?start) AS ?start_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
}

;

MAPPING <urn:needs_version>
FROM SQL {
    SELECT DISTINCT(version),
        CASE
           WHEN target = 'Testcollector-only main project' THEN 'PACE'
           ELSE substring(target from 1 for position(' ' in target) - 1)
        END AS target
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_properties_latest`
    WHERE type = 'project'
}
TO {
    ?target_iri :latest_version ?version_iri
}
WHERE {
    BIND (template("urn:pace:ontology:{version}") AS ?version_iri)
    BIND (template("urn:pace:ontology:{target}") AS ?target_iri)
}
