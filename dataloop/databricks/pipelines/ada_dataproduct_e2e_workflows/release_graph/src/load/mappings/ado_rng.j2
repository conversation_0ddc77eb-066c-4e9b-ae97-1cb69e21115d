PREFIX : <urn:pace:ontology:ado:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>

MAPPING <urn:ado_workitems>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`ado_nodes`
}
TO {
  ?work_item a ?mapped_work_item_type ;
    :hasId ?id ;
    :hasStatus ?state ;
    :hasAreaPath ?area_path ;
    :hasSeverity ?severity ;
    :hasIteration ?iteration_path ;
    :hasReason ?reason ;
    :hasCreatedDate ?formatted_created_date ;
    :hasChangedDate ?formatted_changed_date ;
    :hasTitle ?title ;
    :hasActivatedDate ?formatted_activated_date ;
    :hasClosedDate ?formatted_closed_date ;
    :hasTag ?tags .
}
WHERE {
  BIND (template("urn:pace:ontology:ado:{id}") AS ?work_item)
  BIND (IRI(?rel_type) AS ?rel_type_iri)
  BIND (IRI(?ado_state) AS ?state)
  BIND (IRI(?ado_type) AS ?mapped_work_item_type)
  BIND (xsd:dateTime(?created_date) AS ?formatted_created_date)
  BIND (xsd:dateTime(?changed_date) AS ?formatted_changed_date)
  BIND (xsd:dateTime(?activated_date) AS ?formatted_activated_date)
  BIND (xsd:dateTime(?closed_date) AS ?formatted_closed_date)
}

;

MAPPING <urn:ado_relations>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`ado_edges`
}
TO {
  ?formatted_source_iri ?type_iri ?formatted_target_iri .
}
WHERE {
  BIND (IRI(?source_iri) AS ?formatted_source_iri)
  BIND (IRI(?target_iri) AS ?formatted_target_iri)
  BIND (IRI(?type) AS ?type_iri)
}
