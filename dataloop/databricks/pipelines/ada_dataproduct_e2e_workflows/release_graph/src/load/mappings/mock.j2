PREFIX : <urn:unittest:mock:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>


MAPPING <urn:mock_sms_file>
FROM SQL {
  SELECT *
   FROM `{{ catalog }}`.`{{ schema }}`.`mock`
}

TO {
  ?mock a ?mapped_mock ;
    :hasMockId ?mock_id .
}

WHERE {
  BIND (template("urn:unittest:mock:{mock_id}") AS ?mapped_mock)
}
