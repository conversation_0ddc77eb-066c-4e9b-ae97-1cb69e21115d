PREFIX : <urn:pace:ontology:github:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:github_commits_pace>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`github_commits`
  {% if version %} WHERE commit_sha = '{{ version }}' {% endif %}
}
TO {
  ?commit_artifact a pace:ArtifactVersion ;
    :hasParent ?parent_artifact ;
    :hasCommitMessage ?message ;
    :hasCommitSha ?commit_sha ;
    :hasCommitUrl ?url ;
    :createdAt ?formatted_created_date ;
    :hasHTMLUrl ?html_url .
}

WHERE {
  BIND (template("urn:pace:ontology:{commit_sha}") AS ?commit_artifact)
  BIND (template("urn:pace:ontology:{parent}") AS ?parent_artifact)
  BIND (xsd:dateTime(?created_date) AS ?formatted_created_date)
}
