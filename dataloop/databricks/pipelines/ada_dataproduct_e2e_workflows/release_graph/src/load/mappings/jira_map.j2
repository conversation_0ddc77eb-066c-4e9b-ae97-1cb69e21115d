PREFIX : <urn:pace:ontology:jira:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>
PREFIX pace: <urn:pace:ontology:>

MAPPING <urn:jira_issues>
FROM SQL {
  SELECT *
  FROM `{{ catalog }}`.`{{ schema }}`.`jira`
}

TO {
  ?issue a ?mapped_issue_type ;
    :hasId ?id ;
    :hasLink ?link ;
    :hasKey ?key ;
    :hasSummary ?summary ;
    :hasDescription ?description ;
    :hasUpdatedDate ?formatted_updated_date ;
    :hasCreatedDate ?formatted_created_date ;
    :hasLabel ?label ;
    :hasStatus ?status_iri ;
    :hasIssueType ?issuetype ;
    :hasComponent ?component_name ;
    :hasResolution ?resolution_name ;
    :hasFixVersion ?fix_version_name ;
    :hasProblemType ?problem_type ;
    :hasSafetyRelevance ?safety_relevance ;
    :hasSeverity ?severity ;
    :hasTeamName ?team_name ;
    :hasBranchName ?branch_name .
}

WHERE {
  BIND (template("urn:pace:ontology:jira:{id}") AS ?issue)
  BIND (template("urn:pace:ontology:jira:{issuetype}") AS ?mapped_issue_type)
  BIND (template("urn:pace:ontology:jira:{status}") AS ?status_iri)
  BIND (xsd:dateTime(?created_date) AS ?formatted_created_date)
  BIND (xsd:dateTime(?updated_date) AS ?formatted_updated_date)
}
