PREFIX : <urn:pace:ontology:>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>

MAPPING <urn:needs_nodes_rng_v2>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_nodes`
}
TO {
    ?node_iri a ?type_iri;
        rdfs:label  ?title ;
        :da_status ?da_status ;
        :id ?id ;
        :title ?title ;
        :idIri ?id_iri ;
        :artifact_link ?artifact_link ;
        :test_name ?test_name ;
        :tcl ?tcl ;
        :it_context ?it_context ;
        :feature_ticket ?feature_ticket_iri .

    # Add version node
    ?version_iri a :ArtifactVersion ;
        :validFor ?node_iri .

} WHERE {
    BIND (template("urn:pace:ontology:needs:{id}_{version}") AS ?node_iri)
    BIND (template("urn:pace:ontology:needs:{id}") AS ?id_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
    BIND (template("urn:pace:ontology:{version}") AS ?version_iri)
    BIND (template("urn:pace:ontology:ado:{feature_ticket}") AS ?feature_ticket_iri)

}

;

MAPPING <urn:needs_edges_rng_v2>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_edges`
}
TO {
    ?start_iri ?type_iri ?target_iri
} WHERE {
    BIND (template("urn:pace:ontology:needs:{start}_{version}") AS ?start_iri)
    BIND (template("urn:pace:ontology:needs:{target}_{version}") AS ?target_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
}
