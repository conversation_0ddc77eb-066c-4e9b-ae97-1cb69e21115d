PREFIX : <urn:pace:ontology:>
PREFIX owl: <http://www.w3.org/2002/07/owl#>
PREFIX rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#>
PREFIX rdfs: <http://www.w3.org/2000/01/rdf-schema#>
PREFIX so: <https://schema.org/>
PREFIX stardog: <tag:stardog:api:>
PREFIX xsd: <http://www.w3.org/2001/XMLSchema#>

MAPPING <urn:needs_nodes>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_nodes`
    {% if version %} WHERE version = '{{ version }}' {% endif %}
}
TO {
    ?node_iri a ?type_iri;
        rdfs:label  ?title ;
        :da_status ?da_status ;
        :id ?id ;
        :idIri ?id_iri ;
        :hasDeployment ?deployment ;
        :issueId ?issue_id_iri ;
        :owner ?owner ;
        :ownership_cluster ?ownership_cluster ;
        :ownership_domain ?ownership_domain ;
        :ownership_team ?ownership_team ;
        :variantRestriction ?variant_restriction ;
        :source ?source ;
        :docname ?docname ;
        :tcl ?tcl ;
        :cbBaseLineId ?cb_baseline_id ;
        :cbBaseLineName ?cb_baseline_name ;
        :cbId ?cb_id ;
        :cbType ?cb_type ;
        :cbProjectId ?cb_project_id ;
        :cbProjectName ?cb_project_name ;
        :cbStatus ?cb_status ;
        :cbStatusADA ?cb_status_ada ;
        :cbStatusBosch ?cb_status_bosch ;
        :cbStatusCariad ?cb_status_cariad ;
        :cbTrackerId ?cb_tracker_id ;
        :cbTrackerName ?cb_tracker_name ;
        :cbDASafetyIntegrity ?cb_da_safety_integrity .

    # Add version node
    ?version_iri a :ArtifactVersion ;
        :validFor ?node_iri .

} WHERE {
    BIND (template("urn:pace:ontology:needs:{id}_{version}") AS ?node_iri)
    BIND (template("urn:pace:ontology:needs:{id}") AS ?id_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
    BIND (template("urn:pace:ontology:needs:{da_status}") AS ?status_iri)
    BIND (template("urn:pace:ontology:{version}") AS ?version_iri)
    BIND (template("urn:pace:ontology:ado:{issue_id}") AS ?issue_id_iri)

}

;

MAPPING <urn:needs_edges>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_edges`
    WHERE type != 'tags' AND type != 'test_platform' AND type != 'test_trigger'
    {% if version %} AND version = '{{ version }}' {% endif %}
}
TO {
    ?start_iri ?type_iri ?target_iri
} WHERE {
    BIND (template("urn:pace:ontology:needs:{start}_{version}") AS ?start_iri)
    BIND (template("urn:pace:ontology:needs:{target}_{version}") AS ?target_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
}

;

MAPPING <urn:needs_tags>
FROM SQL {
    SELECT *, REPLACE (target, ' ', '_') clean_target
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_edges`
    WHERE type = 'tags'
    {% if version %} AND version = '{{ version }}' {% endif %}
}
TO {
    ?target_iri a <urn:pace:ontology:needs:Tag> ;
        rdfs:label ?target .

    ?start_iri ?type_iri ?target_iri .

} WHERE {
    BIND (template("urn:pace:ontology:needs:{start}_{version}") AS ?start_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
    BIND (template("urn:pace:ontology:needs:{clean_target}") AS ?target_iri)
}

;

MAPPING <urn:needs_test_platform>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_edges`
    WHERE type = 'test_platform'
    {% if version %} AND version = '{{ version }}' {% endif %}
}
TO {
    ?start_iri ?type_iri ?target_iri .
} WHERE {
    BIND (template("urn:pace:ontology:needs:{start}_{version}") AS ?start_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
    BIND (template("urn:pace:ontology:TestPlatform-{target}") AS ?target_iri)
}

;

MAPPING <urn:needs_test_trigger>
FROM SQL {
    SELECT *
    FROM `{{ catalog }}`.`{{ schema }}`.`needs_edges`
    WHERE type = 'test_trigger'
    {% if version %} AND version = '{{ version }}' {% endif %}
}
TO {
    ?start_iri ?type_iri ?target_iri .
} WHERE {
    BIND (template("urn:pace:ontology:needs:{start}_{version}") AS ?start_iri)
    BIND (IRI(?mapped_type) AS ?type_iri)
    BIND (template("urn:pace:ontology:TestTrigger-{target}") AS ?target_iri)
}
