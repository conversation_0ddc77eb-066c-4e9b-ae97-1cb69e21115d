@prefix : <urn:pace:ontology:adx:> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix pace: <urn:pace:ontology:> .
@base <urn:pace:ontology:adx:> .

<urn:pace:ontology:adx:> rdf:type owl:Ontology ;
                                                   owl:imports <urn:pace:ontology:> ;
                                                   rdfs:comment "Ontology to define the Azure data explorer metamodel for the Allinace project." ;
                                                   rdfs:label "Azure data explorer metamodel v0.0.1" ;
                                                   owl:versionInfo "0.0.3" .

#################################################################
#    Object Properties
#################################################################

###  urn:pace:ontology:adx:hasTestCase
:hasTestCase rdf:type owl:ObjectProperty ;
             rdfs:subPropertyOf owl:topObjectProperty ;
             rdfs:domain :Report ;
             rdfs:range pace:Artifact.

#################################################################
#    Data properties
#################################################################

###  urn:pace:ontology:adx:preset
:preset rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :Report ;
        rdfs:range xsd:string .


###  urn:pace:ontology:adx:runDuration
:runDuration rdf:type owl:DatatypeProperty ;
             rdfs:subPropertyOf owl:topDataProperty ;
             rdfs:domain :Report ;
             rdfs:range xsd:double .


###  urn:pace:ontology:adx:runId
:runId rdf:type owl:DatatypeProperty ;
       rdfs:subPropertyOf owl:topDataProperty ;
       rdfs:domain :Report ;
       rdfs:range xsd:string .


###  urn:pace:ontology:adx:testDescription
:testDescription rdf:type owl:DatatypeProperty ;
                 rdfs:subPropertyOf owl:topDataProperty ;
                 rdfs:domain :Report ;
                 rdfs:range xsd:string .


###  urn:pace:ontology:adx:testDuration
:testDuration rdf:type owl:DatatypeProperty ;
              rdfs:subPropertyOf owl:topDataProperty ;
              rdfs:domain :Report ;
              rdfs:range xsd:double .


###  urn:pace:ontology:adx:testName
:testName rdf:type owl:DatatypeProperty ;
          rdfs:subPropertyOf owl:topDataProperty ;
          rdfs:domain :Report ;
          rdfs:range xsd:string .


###  urn:pace:ontology:adx:testPath
:testPath rdf:type owl:DatatypeProperty ;
          rdfs:subPropertyOf owl:topDataProperty ;
          rdfs:domain :Report ;
          rdfs:range xsd:string .


###  urn:pace:ontology:adx:testResultsExpected
:testResultsExpected rdf:type owl:DatatypeProperty ;
                     rdfs:subPropertyOf owl:topDataProperty ;
                     rdfs:domain :Report ;
                     rdfs:range xsd:double .


###  urn:pace:ontology:adx:testResultsKpiValue
:testResultsKpiValue rdf:type owl:DatatypeProperty ;
                     rdfs:subPropertyOf owl:topDataProperty ;
                     rdfs:domain :Report ;
                     rdfs:range xsd:double .


###  urn:pace:ontology:adx:testResultsLowerThreshold
:testResultsLowerThreshold rdf:type owl:DatatypeProperty ;
                           rdfs:subPropertyOf owl:topDataProperty ;
                           rdfs:domain :Report ;
                           rdfs:range xsd:double .


###  urn:pace:ontology:adx:testResultsOperator
:testResultsOperator rdf:type owl:DatatypeProperty ;
                     rdfs:subPropertyOf owl:topDataProperty ;
                     rdfs:domain :Report ;
                     rdfs:range xsd:string .


###  urn:pace:ontology:adx:testResultsThreshold
:testResultsThreshold rdf:type owl:DatatypeProperty ;
                      rdfs:subPropertyOf owl:topDataProperty ;
                      rdfs:domain :Report ;
                      rdfs:range xsd:double .


###  urn:pace:ontology:adx:testResultsUpperThreshold
:testResultsUpperThreshold rdf:type owl:DatatypeProperty ;
                           rdfs:subPropertyOf owl:topDataProperty ;
                           rdfs:domain :Report ;
                           rdfs:range xsd:double .


###  urn:pace:ontology:adx:testResultsPassed
:testResultsPassed rdf:type owl:DatatypeProperty ;
                   rdfs:subPropertyOf owl:topDataProperty ;
                   rdfs:domain :Report ;
                   rdfs:range xsd:boolean .


###  urn:pace:ontology:adx:reportCreationTime
:reportCreationTime rdf:type owl:DatatypeProperty ;
                   rdfs:subPropertyOf owl:topDataProperty ;
                   rdfs:domain :Report ;
                   rdfs:range xsd:dateTime .

###  urn:pace:ontology:adx:hasDeployment
:hasDeployment rdf:type owl:DatatypeProperty ;
               rdfs:subPropertyOf owl:topDataProperty ;
               rdfs:domain :Report ;
               rdfs:range xsd:string .

###  urn:pace:ontology:adx:variationConcreteParameters
:variationConcreteParameters rdf:type owl:DatatypeProperty ;
               rdfs:subPropertyOf owl:topDataProperty ;
               rdfs:domain :Report ;
               rdfs:range xsd:string .

#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:adx:Report
:Report rdf:type owl:Class ;
        rdfs:subClassOf pace:Artifact

###  Generated by the OWL API (version 4.5.25.2023-02-15T19:15:49Z) https://github.com/owlcs/owlapi
