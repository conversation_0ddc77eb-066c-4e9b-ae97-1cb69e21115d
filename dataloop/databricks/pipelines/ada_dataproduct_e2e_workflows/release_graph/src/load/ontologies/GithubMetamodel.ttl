@prefix : <urn:pace:ontology:github> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix pace: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@base <urn:pace:ontology:github> .

<urn:pace:ontology:github:> rdf:type owl:Ontology ;
                             owl:imports pace: ;
                             rdfs:comment "Ontology to define the github metamodel for the Allinace project." ;
                             rdfs:label "github metamodel v0.0.3" .

#################################################################
#    Object Properties
#################################################################

###  urn:pace:ontology:github:hasMergeSha
:hasMergeSha rdf:type owl:ObjectProperty ;
                        rdfs:domain :PullRequest ;
                        rdfs:range pace:ArtifactVersion ;
                        rdfs:label "has merge sha" .

###  urn:pace:ontology:github:hasStatus
:hasStatus rdf:type owl:ObjectProperty ;
                      rdfs:subPropertyOf owl:topObjectProperty ;
                      rdfs:domain :PullRequest ;
                      rdfs:range :PullRequestStatus ;
                      rdfs:label "has status" .


###  urn:pace:ontology:github:hasParent
:hasParent rdf:type owl:ObjectProperty ;
                      rdfs:subPropertyOf owl:topObjectProperty ;
                      rdfs:domain pace:ArtifactVersion ;
                      rdfs:range pace:ArtifactVersion ;
                      rdfs:label "has parent" .

###  urn:pace:ontology:github:isRepresentationOf
:isRepresentationOf rdf:type owl:ObjectProperty ;
                               rdfs:domain :Tag ;
                               rdfs:range pace:ArtifactVersion ;
                               rdfs:label "is prepresentation of" .


#################################################################
#    Data properties
#################################################################

###  urn:pace:ontology:github:closedAt
:closedAt rdf:type owl:DatatypeProperty ;
                     rdfs:domain :PullRequest ;
                     rdfs:range xsd:dateTime ;
                     rdfs:label "closed at" .


###  urn:pace:ontology:github:createdAt
:createdAt rdf:type owl:DatatypeProperty ;
                      rdfs:domain :PullRequest ,
                                  pace:ArtifactVersion ,
                                  :tag ;
                      rdfs:range xsd:dateTime ;
                      rdfs:label "created at" .

###  urn:pace:ontology:github:hasCommitSha
:hasCommitSha rdf:type owl:DatatypeProperty ;
                         rdfs:domain pace:ArtifactVersion ;
                         rdfs:range xsd:string ;
                         rdfs:label "has commit sha" .

###  urn:pace:ontology:github:hasCommitMessage
:hasCommitMessage rdf:type owl:DatatypeProperty ;
                             rdfs:domain pace:ArtifactVersion ;
                             rdfs:range xsd:string ;
                             rdfs:label "has commit message" .

###  urn:pace:ontology:github:hasHTMLUrl
:hasHTMLUrl rdf:type owl:DatatypeProperty ;
                      rdfs:domain :PullRequest ;
                      rdfs:range xsd:anyURI ;
                      rdfs:label "has url" .

###  urn:pace:ontology:github:hasCommitUrl
:hasCommitUrl rdf:type owl:DatatypeProperty ;
                         rdfs:domain pace:ArtifactVersion,
                                    :pullrequest ;
                         rdfs:range xsd:anyURI ;
                         rdfs:label "has commit url" .

###  urn:pace:ontology:github:isLocked
:isLocked rdf:type owl:DatatypeProperty ;
                         rdfs:domain :PullRequest ;
                         rdfs:range xsd:boolean ;
                         rdfs:label "is in locked state" .

###  urn:pace:ontology:github:isDraft
:isDraft rdf:type owl:DatatypeProperty ;
                         rdfs:domain :PullRequest ;
                         rdfs:range xsd:boolean ;
                         rdfs:label "is in draft state" .

###  urn:pace:ontology:github:hasMilestone
:hasMilestone rdf:type owl:DatatypeProperty ;
                         rdfs:domain :PullRequest ;
                         rdfs:range xsd:string ;
                         rdfs:label "has milestone" .

###  urn:pace:ontology:github:hasNumber
:hasNumber rdf:type owl:DatatypeProperty ;
                      rdfs:domain :PullRequest ;
                      rdfs:range xsd:int ;
                      rdfs:label "has number" .

###  urn:pace:ontology:github:hasTitle
:hasTitle rdf:type owl:DatatypeProperty ;
                     rdfs:domain :PullRequest ;
                     rdfs:range xsd:string ;
                     rdfs:label "has title" .

###  urn:pace:ontology:github:hasTagMessage
:hasTagMessage rdf:type owl:DatatypeProperty ;
                          rdfs:domain :Tag ;
                          rdfs:range xsd:string ;
                          rdfs:label "has message" .

###  urn:pace:ontology:github:hasTagName
:hasTagName rdf:type owl:DatatypeProperty ;
                       rdfs:domain :Tag ;
                       rdfs:range xsd:string ;
                       rdfs:label "has tag name" .

###  urn:pace:ontology:github:hasName
:hasName rdf:type owl:DatatypeProperty ;
                       rdfs:domain :Tag ;
                       rdfs:range xsd:string ;
                       rdfs:label "has name" .

###  urn:pace:ontology:github:hasTagUrl
:hasTagUrl rdf:type owl:DatatypeProperty ;
                      rdfs:domain :Tag ;
                      rdfs:range xsd:anyURI ;
                      rdfs:label "has tag url" .

###  urn:pace:ontology:github:hasZipBallUrl
:hasZipBallUrl rdf:type owl:DatatypeProperty ;
                      rdfs:domain :Tag ;
                      rdfs:range xsd:anyURI ;
                      rdfs:label "has zip ball url" .

###  urn:pace:ontology:github:hasTarBallUrl
:hasTarBallUrl rdf:type owl:DatatypeProperty ;
                      rdfs:domain :Tag ;
                      rdfs:range xsd:anyURI ;
                      rdfs:label "has tar ball url" .

###  urn:pace:ontology:github:hasNodeId
:hasNodeId rdf:type owl:DatatypeProperty ;
                     rdfs:domain :Tag ;
                     rdfs:range xsd:string ;
                     rdfs:label "has node id" .

###  urn:pace:ontology:github:hasId
:hasId rdf:type owl:DatatypeProperty ;
                     rdfs:domain :Tag ;
                     rdfs:range xsd:string ;
                     rdfs:label "has node id" .

###  urn:pace:ontology:github:mergedAt
:mergedAt rdf:type owl:DatatypeProperty ;
                     rdfs:domain :PullRequest ;
                     rdfs:range xsd:dateTime ;
                     rdfs:label "merged at" .


#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:ArtifactVersion
pace:ArtifactVersion rdf:type owl:Class ;
                     rdfs:subClassOf pace:Artifact ;
                     rdfs:comment "The github sha that is used to idenfify the version for each artifact" ;
                     rdfs:label "Version" .


###  urn:pace:ontology:github:PullRequest
:PullRequest rdf:type owl:Class ;
                        rdfs:subClassOf pace:Artifact ;
                        rdfs:comment "If a pull request is merged is has a merged_sha which is represented as Artifact Versions from the artifact metamodel." ;
                        rdfs:label "Github pull request" .


###  urn:pace:ontology:github:PullRequestStatus
:PullRequestStatus rdf:type owl:Class ;
                              rdfs:subClassOf pace:ArtifactStatus ;
                              rdfs:label "Pull Request Status" .


###  urn:pace:ontology:github:Release
:Release rdf:type owl:Class ;
                rdfs:subClassOf pace:Artifact ;
                rdfs:comment "A release extends a tag with some additional info (message and tag naming)." ;
                rdfs:label "Github release" .

###  urn:pace:ontology:github:Tag
:Tag rdf:type owl:Class ;
                rdfs:subClassOf pace:Artifact ;
                rdfs:comment "A tag is a selected commit which can be used to identify special sw versions." ;
                rdfs:label "Github tag" .


#################################################################
#    Individuals
#################################################################

###  urn:pace:ontology:github:closed
:closed rdf:type :PullRequestStatus ;
                   rdfs:label "closed" .


###  urn:pace:ontology:github:open
:open rdf:type :PullRequestStatus ;
                 rdfs:label "open" .


#################################################################
#    Annotations
#################################################################

:hasMergeSha rdfs:label "has merge sha" .


:isRepresentationOf rdfs:label "is representation of" .


###  Generated by the OWL API (version 4.5.25.2023-02-15T19:15:49Z) https://github.com/owlcs/owlapi
