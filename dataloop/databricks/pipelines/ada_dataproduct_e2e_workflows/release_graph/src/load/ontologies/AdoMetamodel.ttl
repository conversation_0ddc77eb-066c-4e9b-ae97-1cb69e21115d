@prefix : <urn:pace:ontology:ado:> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix pace: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@base <urn:pace:ontology:ado:> .

<urn:pace:ontology:ado:> rdf:type owl:Ontology ;
                          owl:imports pace: ;
                          rdfs:comment "Ontology to define the ticket system metamodel for the Alliance project." ;
                          rdfs:label "Azure dev ops metamodel v0.0.1" ;
                          owl:versionInfo "0.0.1" .

#################################################################
#    Object Properties
#################################################################

###  urn:pace:ontology:ado:hasChild
:hasChild rdf:type owl:ObjectProperty ;
          rdfs:domain :Workitem .


###  urn:pace:ontology:ado:hasParent
:hasParent rdf:type owl:ObjectProperty ;
           rdfs:domain :Workitem .


###  urn:pace:ontology:ado:hasPullRequest
:hasPullRequest rdf:type owl:ObjectProperty ;
                rdfs:domain :Workitem .


###  urn:pace:ontology:ado:hasRelated
:hasRelated rdf:type owl:ObjectProperty ;
            rdfs:domain :Workitem .


###  urn:pace:ontology:ado:wasCreatedFor
:wasCreatedFor rdf:type owl:ObjectProperty ;
               rdfs:domain :Workitem .

### urn:pace:ontology:ado:needsLink
:needsLink rdf:type owl:ObjectProperty ;
            rdfs:domain :Workitem .

###  urn:pace:ontology:ado:hasStatus
:hasStatus rdf:type owl:ObjectProperty ;
           rdfs:domain :Workitem .
#################################################################
#    Data properties
#################################################################

###  urn:pace:ontology:ado:hasActivatedDate
:hasActivatedDate rdf:type owl:DatatypeProperty ;
                  rdfs:domain :Workitem ;
                  rdfs:range xsd:dateTime ;
                  rdfs:label "has activated date" .


###  urn:pace:ontology:ado:hasAreaPath
:hasAreaPath rdf:type owl:DatatypeProperty ;
             rdfs:domain :Workitem ;
             rdfs:range xsd:string ;
             rdfs:label "has area path" .


###  urn:pace:ontology:ado:hasChangeDate
:hasChangeDate rdf:type owl:DatatypeProperty ;
               rdfs:domain :Workitem ;
               rdfs:range xsd:dateTime ;
               rdfs:label "has change date" .


###  urn:pace:ontology:ado:hasClosedDate
:hasClosedDate rdf:type owl:DatatypeProperty ;
               rdfs:domain :Workitem ;
               rdfs:range xsd:dateTime ;
               rdfs:label "has closed date" .


###  urn:pace:ontology:ado:hasCreatedDate
:hasCreatedDate rdf:type owl:DatatypeProperty ;
                rdfs:domain :Workitem ;
                rdfs:range xsd:dateTime ;
                rdfs:label "has created date" .


###  urn:pace:ontology:ado:hasId
:hasId rdf:type owl:DatatypeProperty ;
       rdfs:domain :Workitem ;
       rdfs:range xsd:string ;
       rdfs:label "has id" .


###  urn:pace:ontology:ado:hasIteration
:hasIteration rdf:type owl:DatatypeProperty ;
              rdfs:domain :Workitem ;
              rdfs:range xsd:string ;
              rdfs:label "has iteration" .


###  urn:pace:ontology:ado:hasReason
:hasReason rdf:type owl:DatatypeProperty ;
           rdfs:domain :Workitem ;
           rdfs:label "has reason" .


###  urn:pace:ontology:ado:hasSeverity
:hasSeverity rdf:type owl:DatatypeProperty ;
             rdfs:domain :Bug ;
             rdfs:range xsd:string ;
             rdfs:label "has severity" .


###  urn:pace:ontology:ado:hasTags
:hasTags rdf:type owl:DatatypeProperty ;
         rdfs:domain :Workitem ;
         rdfs:range xsd:string ;
         rdfs:label "has tags" .


###  urn:pace:ontology:ado:hasTitle
:hasTitle rdf:type owl:DatatypeProperty ;
          rdfs:domain :Workitem ;
          rdfs:range xsd:string ;
          rdfs:label "has title" .


###  urn:pace:ontology:ado:hasKeyResultType
:hasKeyResultType rdf:type owl:DatatypeProperty ;
          rdfs:domain :Workitem ;
          rdfs:range xsd:string ;
          rdfs:label "has key result type - less than, greater than etc" .


###  urn:pace:ontology:ado:hasKeyResultUnit
:hasKeyResultUnit rdf:type owl:DatatypeProperty ;
          rdfs:domain :Workitem ;
          rdfs:range xsd:string ;
          rdfs:label "has key result unit" .


###  urn:pace:ontology:ado:hasKeyResultDataSource
:hasKeyResultDataSource rdf:type owl:DatatypeProperty ;
          rdfs:domain :Workitem ;
          rdfs:range xsd:string ;
          rdfs:label "has key result data source" .


###  urn:pace:ontology:ado:hasKeyResultDataSource
:hasKeyResultTargetValue rdf:type owl:DatatypeProperty ;
          rdfs:domain :Workitem ;
          rdfs:range xsd:string ;
          rdfs:label "has key result target value" .


###  urn:pace:ontology:ado:hasKeyResultValue
:hasKeyResultValue rdf:type owl:DatatypeProperty ;
          rdfs:domain :Workitem ;
          rdfs:range :KeyResultValue ;
          rdfs:label "has key result value" .


###  urn:pace:ontology:ado:currentValue
:currentValue rdf:type owl:DatatypeProperty ;
          rdfs:domain :KeyResultValue ;
          rdfs:range xsd:string ;
          rdfs:label "has current value" .


###  urn:pace:ontology:ado:updatedAt
:updatedAt rdf:type owl:DatatypeProperty ;
          rdfs:domain :KeyResultValue ;
          rdfs:range xsd:dateTime ;
          rdfs:label "current value updated at" .

#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:ado:Bug
:Bug rdf:type owl:Class ;
     rdfs:subClassOf :Workitem ;
     rdfs:label "Bug" .


###  urn:pace:ontology:ado:Decision
:Decision rdf:type owl:Class ;
          rdfs:subClassOf :Workitem ;
          rdfs:label "Decision" .


###  urn:pace:ontology:ado:Impediment
:Impediment rdf:type owl:Class ;
            rdfs:subClassOf :Workitem ;
            rdfs:label "Impediment" .


###  urn:pace:ontology:ado:KeyResult
:KeyResult rdf:type owl:Class ;
           rdfs:subClassOf :Workitem ;
           rdfs:label "Key Result" .


###  urn:pace:ontology:ado:KeyResultValue
:KeyResultValue rdf:type owl:Class ;
           rdfs:label "Key Result Value" .

###  urn:pace:ontology:ado:Objective
:Objective rdf:type owl:Class ;
           rdfs:subClassOf :Workitem ;
           rdfs:label "Objective" .


###  urn:pace:ontology:ado:RequestForFeature
:RequestForFeature rdf:type owl:Class ;
                   rdfs:subClassOf :Workitem ;
                   rdfs:label "Request for Feature" .


###  urn:pace:ontology:ado:Risk
:Risk rdf:type owl:Class ;
      rdfs:subClassOf :Workitem ;
      rdfs:label "Risk" .


###  urn:pace:ontology:ado:TeamEpic
:TeamEpic rdf:type owl:Class ;
          rdfs:subClassOf :Workitem ;
          rdfs:label "Team Epic" .


###  urn:pace:ontology:ado:UserStory
:UserStory rdf:type owl:Class ;
           rdfs:subClassOf :Workitem ;
           rdfs:label "User Story" .


###  urn:pace:ontology:ado:Impediment
:Impediment rdf:type owl:Class ;
           rdfs:subClassOf :Workitem ;
           rdfs:label "Impediment" .


###  urn:pace:ontology:ado:RequestForFeature
:RequestForFeature rdf:type owl:Class ;
           rdfs:subClassOf :Workitem ;
           rdfs:label "Request for Feature" .


###  urn:pace:ontology:ado:Risk
:Risk rdf:type owl:Class ;
           rdfs:subClassOf :Workitem ;
           rdfs:label "Risk" .


###  urn:pace:ontology:ado:Workitem
:Workitem rdf:type owl:Class ;
          rdfs:subClassOf pace:Artifact ;
          rdfs:label "Work Item" .

###  urn:pace:ontology:ado:WorkitemState
:WorkitemState rdf:type owl:Class ;
          rdfs:subClassOf pace:Artifact-status .


#################################################################
#    Individuals
#################################################################

###  urn:pace:ontology:ado:resolved
:resolved rdf:type :WorkitemState ;
           rdfs:label "Resolved" .

### urn:pace:ontology:ado:closed
:closed rdf:type :WorkitemState ;
        rdfs:label "Closed" .

### urn:pace:ontology:ado:inProgress
:inProgress rdf:type :WorkitemState ;
            rdfs:label "In Progress" .

### urn:pace:ontology:ado:decided
:decided rdf:type :WorkitemState ;
         rdfs:label "Decided" .

### urn:pace:ontology:ado:committed
:committed rdf:type :WorkitemState ;
           rdfs:label "Committed" .

### urn:pace:ontology:ado:rejected
:rejected rdf:type :WorkitemState ;
          rdfs:label "Rejected" .

### urn:pace:ontology:ado:draft
:draft rdf:type :WorkitemState ;
       rdfs:label "Draft" .

### urn:pace:ontology:ado:blocked
:blocked rdf:type :WorkitemState ;
         rdfs:label "Blocked" .

### urn:pace:ontology:ado:inFocus
:inFocus rdf:type :WorkitemState ;
         rdfs:label "In Focus" .

### urn:pace:ontology:ado:inDecision
:inDecision rdf:type :WorkitemState ;
            rdfs:label "In Decision" .

### urn:pace:ontology:ado:new
:new rdf:type :WorkitemState ;
     rdfs:label "New" .

### urn:pace:ontology:ado:accepted
:accepted rdf:type :WorkitemState ;
           rdfs:label "Accepted" .

### urn:pace:ontology:ado:inAnalysis
:inAnalysis rdf:type :WorkitemState ;
             rdfs:label "In Analysis" .

### urn:pace:ontology:ado:cancelled
:cancelled rdf:type :WorkitemState ;
           rdfs:label "Cancelled" .

### urn:pace:ontology:ado:confirmed
:confirmed rdf:type :WorkitemState ;
            rdfs:label "Confirmed" .

### urn:pace:ontology:ado:active
:active rdf:type :WorkitemState ;
        rdfs:label "Active" .

### urn:pace:ontology:ado:approved
:approved rdf:type :WorkitemState ;
           rdfs:label "Approved" .

### urn:pace:ontology:ado:defocused
:defocused rdf:type :WorkitemState ;
           rdfs:label "Defocused" .

### urn:pace:ontology:ado:inmitigation
:inmitigation rdf:type :WorkitemState ;
               rdfs:label "In Mitigation" .

### urn:pace:ontology:ado:removed
:removed rdf:type :WorkitemState ;
         rdfs:label "Removed" .

### urn:pace:ontology:ado:design
:design rdf:type :WorkitemState ;
        rdfs:label "Design" .

### urn:pace:ontology:ado:completed
:completed rdf:type :WorkitemState ;
           rdfs:label "Completed" .

### urn:pace:ontology:ado:owned
:owned rdf:type :WorkitemState ;
       rdfs:label "Owned" .

### urn:pace:ontology:ado:revoked
:revoked rdf:type :WorkitemState ;
         rdfs:label "Revoked" .

### urn:pace:ontology:ado:inDefinition
:inDefinition rdf:type :WorkitemState ;
               rdfs:label "In Definition" .


###  Generated by the OWL API (version 4.5.25.2023-02-15T19:15:49Z) https://github.com/owlcs/owlapi
