@prefix : <urn:pace:ontology> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@base <urn:pace:ontology> .

<urn:pace:ontology:> rdf:type owl:Ontology ;
                        rdfs:comment "Base artifact metamodel." ;
                             rdfs:label "artifact metamodel v0.0.1" .

#################################################################
#    Object Properties
#################################################################

###  urn:pace:ontology:isValidFor
:validFor rdf:type owl:ObjectProperty ;
                        rdfs:domain :Artifact ;
                        rdfs:range :ArtifactVersion ;
                        rdfs:label "is valid for" .

#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:ArtifactVersion
:Artifact rdf:tpye owl:Class;
                    rdfs:comment "Base artifact class" ;
                    rdfs:label "Artifact" .

###  urn:pace:ontology:ArtifactVersion
:ArtifactVersion rdf:type owl:Class ;
                     rdfs:subClassOf pace:Artifact ;
                     rdfs:comment "The github sha that is used to idenfify the version for each artifact" ;
                     rdfs:label "Version" .

###  Generated by the OWL API (version 4.5.25.2023-02-15T19:15:49Z) https://github.com/owlcs/owlapi
