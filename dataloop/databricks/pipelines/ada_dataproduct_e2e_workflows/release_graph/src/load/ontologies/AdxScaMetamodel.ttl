@prefix : <urn:pace:ontology:adx:> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix pace: <urn:pace:ontology:> .
@base <urn:pace:ontology:adx:> .


<urn:pace:ontology:adx:> rdf:type owl:Ontology ;
                                                  owl:imports <urn:pace:ontology:> ;
                                                  rdfs:comment "Ontology to define the Azure data explorer metamodel for the Allinace project." ;
                                                  rdfs:label "Azure data explorer metamodel v0.0.1" ;
                                                  owl:versionInfo "0.0.2" .


#################################################################
#    Data properties
#################################################################

###  urn:pace:ontology:adx:hasRevision
:hasRevision rdf:type owl:DatatypeProperty ;
      rdfs:subPropertyOf owl:topDataProperty ;
      rdfs:domain :ScaComponent ;
      rdfs:range xsd:string .


###  urn:pace:ontology:adx:lastChanged
:lastChanged rdf:type owl:DatatypeProperty ;
          rdfs:subPropertyOf owl:topDataProperty ;
          rdfs:domain :ScaComponent ;
          rdfs:range xsd:dateTime .

###  urn:pace:ontology:adx:hasLabel
:hasLabel rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:adx:hasName
:hasName rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:adx:hasSourcePath
:hasSourcePath rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:adx:hasBuildPath
:hasBuildPath rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:adx:hasRule
:hasRule rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:adx:hasBuildIssuesCount
:hasBuildIssuesCount rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:adx:hasScaIssuesCountBySeverity1
:hasScaIssuesCountBySeverity1 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:adx:hasScaIssuesCountBySeverity2
:hasScaIssuesCountBySeverity2 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:adx:hasScaIssuesCountBySeverity3
:hasScaIssuesCountBySeverity3 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:adx:hasScaIssuesCountBySeverity4
:hasScaIssuesCountBySeverity4 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:adx:hasScaIssuesCountBySeverity5
:hasScaIssuesCountBySeverity5 rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:int .

###  urn:pace:ontology:adx:hasDoxygenCoverage
:hasDoxygenCoverage rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:double .

###  urn:pace:ontology:adx:hasCodeOwners
:hasCodeOwners rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:adx:hasDeployments
:hasDeployments rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

###  urn:pace:ontology:adx:hasTags
:hasTags rdf:type owl:DatatypeProperty ;
        rdfs:subPropertyOf owl:topDataProperty ;
        rdfs:domain :ScaComponent ;
        rdfs:range xsd:string .

#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:adx:ScaComponent
:ScaComponent rdf:type owl:Class ;
              rdfs:subClassOf pace:Artifact .

###  Generated by the OWL API (version 4.5.25.2023-02-15T19:15:49Z) https://github.com/owlcs/owlapi
