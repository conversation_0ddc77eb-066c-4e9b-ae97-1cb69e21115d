@prefix : <urn:pace:ontology:jira:> .
@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
@prefix xml: <http://www.w3.org/XML/1998/namespace> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix pace: <urn:x-evn-master:x_org_xc_adda_eng_pace:> .
@base <urn:pace:ontology:jira:> .

<urn:pace:ontology:jira> rdf:type owl:Ontology ;
                            owl:imports <urn:x-evn-master:x_org_xc_adda_eng_pace:> ;
                            rdfs:comment "Ontology to define the jira ticket system metamodel for the Alliance project." ;
                            rdfs:label "Jira metamodel v0.0.1" ;
                            owl:versionInfo "0.0.1" .

#################################################################
#    Data properties
#################################################################

###  urn:pace:ontology:jira:hasId
:hasId rdf:type owl:DatatypeProperty ;
       rdfs:domain :Issue ;
       rdfs:range xsd:string ;
       rdfs:label "has id" .


###  urn:pace:ontology:jira:hasLink
:hasLink rdf:type owl:DatatypeProperty ;
              rdfs:domain :Issue ;
              rdfs:range xsd:string ;
              rdfs:label "has link" .


###  urn:pace:ontology:jira:hasKey
:hasKey rdf:type owl:DatatypeProperty ;
              rdfs:domain :Issue ;
              rdfs:range xsd:string ;
              rdfs:label "has key" .


###  urn:pace:ontology:jira:hasSummary
:hasSummary rdf:type owl:DatatypeProperty ;
           rdfs:domain :Issue ;
           rdfs:range xsd:string ;
           rdfs:label "has summary" .


###  urn:pace:ontology:jira:hasDescription
:hasDescription rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has description" .


###  urn:pace:ontology:jira:hasUpdatedDate
:hasUpdatedDate rdf:type owl:DatatypeProperty ;
                  rdfs:domain :Issue ;
                  rdfs:range xsd:dateTime ;
                  rdfs:label "has updated date" .


###  urn:pace:ontology:jira:hasCreatedDate
:hasCreatedDate rdf:type owl:DatatypeProperty ;
               rdfs:domain :Issue ;
               rdfs:range xsd:dateTime ;
               rdfs:label "has created date" .


###  urn:pace:ontology:jira:hasLabel
:hasLabel rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has label" .


###  urn:pace:ontology:jira:hasIssueType
:hasIssueType rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has issue type" .

###  urn:pace:ontology:jira:hasComponent
:hasComponent rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has component" .

###  urn:pace:ontology:jira:hasResolution
:hasResolution rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has resolution" .

###  urn:pace:ontology:jira:hasFixVersion
:hasFixVersion rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has fix version" .

###  urn:pace:ontology:jira:hasProblemType
:hasProblemType rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has problem type" .

###  urn:pace:ontology:jira:hasSafetyRelevance
:hasSafetyRelevance rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has safety relevance" .

###  urn:pace:ontology:jira:hasSeverity
:hasSeverity rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has severity" .

###  urn:pace:ontology:jira:hasStatus
:hasStatus rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range :IssueState ;
         rdfs:label "has status" .

###  urn:pace:ontology:jira:hasTeamName
:hasTeamName rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has Team Name" .

###  urn:pace:ontology:jira:hasBranchName
:hasBranchName rdf:type owl:DatatypeProperty ;
         rdfs:domain :Issue ;
         rdfs:range xsd:string ;
         rdfs:label "has Branch Name" .


#################################################################
#    Classes
#################################################################

###  urn:pace:ontology:jira:Objective
:Objective rdf:type owl:Class ;
     rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Subtask
:Subtask rdf:type owl:Class ;
          rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Risk
:Risk rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Impediment
:Impediment rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:ProjectEpic
:ProjectEpic rdf:type owl:Class ;
          rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:ChangeRequest
:ChangeRequest rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:KeyResult
:KeyResult rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Bugfix
:Bugfix rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:OpenItem
:OpenItem rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:ProjectFeature
:ProjectFeature rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Problem
:Problem rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Activity
:Activity rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Epic
:Epic rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Story
:Story rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Task
:Task rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Bug
:Bug rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Hardware
:Hardware rdf:type owl:Class ;
           rdfs:subClassOf :Issue .

###  urn:pace:ontology:jira:Issue
:Issue rdf:type owl:Class ;
          rdfs:subClassOf pace:Artifact .

:IssueState rdf:type owl:Class ;
          rdfs:subClassOf pace:ArtifactState .


#################################################################
#    Individuals
#################################################################

###  urn:pace:ontology:jira:Open
:Open rdf:type 
                    :IssueState ;
           rdfs:label "Open" ;
           rdfs:description "The issue is open and ready for the assignee to start work on it." .


###  urn:pace:ontology:jira:InProgress
:InProgress rdf:type 
                       :IssueState ;
              rdfs:label "In Progress" ;
              rdfs:description "This issue is being actively worked on at the moment by the assignee." .


###  urn:pace:ontology:jira:Closed
:Closed rdf:type 
                        :IssueState ;
               rdfs:label "Closed" ;
               rdfs:description "The issue is considered finished, the resolution is correct. Issues which are closed can be reopened." .


###  urn:pace:ontology:jira:ToDo
:ToDo rdf:type :IssueState ;
               rdfs:label "To Do" .


###  urn:pace:ontology:jira:Done
:Done rdf:type :IssueState ;
        rdfs:label "Done" .


###  urn:pace:ontology:jira:Analyze
:Analyze rdf:type :IssueState ;
             rdfs:label "Analyze" .


###  urn:pace:ontology:jira:InVerification
:InVerification rdf:type :IssueState ;
                   rdfs:label "In Verification" .


###  urn:pace:ontology:jira:Backlog
:Backlog rdf:type :IssueState ;
                   rdfs:label "Backlog" .


###  urn:pace:ontology:jira:Start
:Start rdf:type :IssueState ;
                 rdfs:label "Start" .


###  urn:pace:ontology:jira:InImplementation
:InImplementation rdf:type :IssueState ;
                    rdfs:label "In Implementation" .


###  urn:pace:ontology:jira:Waiting
:Waiting rdf:type :IssueState ;
                    rdfs:label "Waiting" .


###  urn:pace:ontology:jira:InAnalysis
:InAnalysis rdf:type :IssueState ;
                      rdfs:label "In Analysis" .


###  urn:pace:ontology:jira:Moreinfo
:Moreinfo rdf:type :IssueState ;
                            rdfs:label "More info" .


###  urn:pace:ontology:jira:Analyzed
:Analyzed rdf:type :IssueState ;
                 rdfs:label "Analyzed" .


###  urn:pace:ontology:jira:Verified
:Verified rdf:type :IssueState ;
                    rdfs:label "Verified" .


###  urn:pace:ontology:jira:Suplagreed
:Suplagreed rdf:type :IssueState ;
                rdfs:label "Supl agreed" .


###  urn:pace:ontology:jira:Suploffered
:Suploffered rdf:type :IssueState ;
                  rdfs:label "Supl offered" .


###  urn:pace:ontology:jira:Custordered
:Custordered rdf:type :IssueState ;
              rdfs:label "Cust ordered" .


###  urn:pace:ontology:jira:Implemented
:Implemented rdf:type :IssueState ;
                    rdfs:label "Implemented" .


###  urn:pace:ontology:jira:Waitingmigrated
:Waitingmigrated rdf:type :IssueState ;
                rdfs:label "Waiting (migrated)" .


###  urn:pace:ontology:jira:SelectedforDevelopment
:SelectedforDevelopment rdf:type :IssueState ;
                  rdfs:label "Selected for Development" .


###  urn:pace:ontology:jira:ClarificationJQL
:ClarificationJQL rdf:type :IssueState ;
              rdfs:label "Clarification JQL" .


###  urn:pace:ontology:jira:SetMigrationcomment
:SetMigrationcomment rdf:type :IssueState ;
                  rdfs:label "Set Migration comment" .


###  urn:pace:ontology:jira:ClarificationConfidentialInformation
:ClarificationConfidentialInformation rdf:type :IssueState ;
                   rdfs:label "Clarification Confidential Information" .
