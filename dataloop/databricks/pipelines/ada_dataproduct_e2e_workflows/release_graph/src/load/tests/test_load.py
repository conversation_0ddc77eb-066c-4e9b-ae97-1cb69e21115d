"""Test the load module.

This module contains tests for the load to stardog functionality.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from jinja2.exceptions import TemplateNotFound
from load import DB_EXISTS, ONTOLOGIES_DIR, Loader, render_template
from stardog.exceptions import StardogException


@pytest.fixture
def mock_admin():
    """Fixture to provide a mock admin interface for tests.

    Returns:
        MagicMock: A mock admin interface with stubbed methods.
    """
    return MagicMock()


@pytest.fixture
def mock_connection():
    """Fixture to provide a mock database connection for tests.

    Returns:
        MagicMock: A mock database connection with stubbed methods.
    """
    return MagicMock()


@pytest.fixture
def mock_mapping_file_content():
    """Fixture to provide a mock mapping file handler for tests.

    Returns:
        MagicMock: A mock mapping file handler.
    """
    return MagicMock()


@pytest.fixture
def loader(mock_admin):
    """Fixture to create a Loader instance with mocked dependencies for testing.

    Args:
        mock_admin (MagicMock): The mocked admin interface.

    Returns:
        Loader: A Loader instance initialized with mocks.
    """
    return Loader(mock_admin, "test_db")


@pytest.fixture
def ontology_file():
    """Fixture to create a temporary ontology file for testing.

    Yields:
        Path: A temporary ontology file.
    """
    with tempfile.NamedTemporaryFile("w", dir=ONTOLOGIES_DIR, suffix=".ttl") as ontology_file:
        # Add dummy content to the ontology file
        ontology_file.write("test_content")
        ontology_file.flush()

        yield Path(ontology_file.name)


test_dbricks_conn = {
    "jdbc.url": "test_url",
    "jdbc.password": "test_password",
    "jdbc.username": "test_username",
    "query.translation": "DEFAULT",
    "jdbc.driver": "com.databricks.client.jdbc.Driver",
    "sql.catalog": "test_catalog",
    "sql.schemas": "test_schema",
}


def test_load(loader, mock_mapping_file_content, mock_admin):
    """Tests the Loader's load method to verify correct function calls.

    Args:
        loader (Loader): The Loader instance being tested.
        mock_mapping_file_content (MagicMock): A mock mapping file content handler.
        mock_admin (MagicMock): The mocked admin interface.
    """
    mock_mapping_file_content.return_value = MagicMock()
    mock_admin.materialize_virtual_graph.return_value = None
    mapping_file_path = "fake/path/map.sms"
    mapping_file_content = "mock_mapping_file_content"

    with patch("stardog.content.MappingRaw", mock_mapping_file_content):
        loader.load(
            dbricks_conn_options=test_dbricks_conn,
            src_schema="test_schema",
            mapping_file=mapping_file_path,
            mapping_file_content=mapping_file_content,
        )

    mock_mapping_file_content.assert_called_once_with(mapping_file_content)
    mock_admin.materialize_virtual_graph.assert_called()


def test_init_database_new_db(mock_admin, mock_connection, loader, ontology_file):
    """Tests initializing a new database ensuring the new_database method is called.

    Args:
        mock_admin (MagicMock): The mocked admin interface.
        mock_connection (MagicMock): The mocked database connection.
        loader (Loader): The Loader instance being tested.
        ontology_file (Path): The temporary ontology file.
    """
    mock_admin.new_database.return_value = None
    client = MagicMock()
    with (
        patch("stardog.http.client.Client", client),
        patch("load._get_latest_pace_ontology_ttl", return_value=ontology_file),
    ):
        loader.init_database(mock_connection)

    mock_admin.new_database.assert_called()


def test_init_database_existing_db(mock_admin, mock_connection, loader, ontology_file):
    """Tests initializing an existing database to handle the case where the database already exists.

    Args:
        mock_admin (MagicMock): The mocked admin interface.
        mock_connection (MagicMock): The mocked database connection.
        loader (Loader): The Loader instance being tested.
        ontology_file (Path): The temporary ontology file.
    """
    mock_admin.new_database.return_value = None
    mock_admin.new_database.side_effect = StardogException(stardog_code=DB_EXISTS, message="Database already exists")
    with (
        patch("stardog.http.client.Client", MagicMock()),
        patch("load._get_latest_pace_ontology_ttl", return_value=ontology_file),
    ):
        loader.init_database(mock_connection)
    mock_admin.new_database.assert_called()


def test_invalid_ontology_file(mock_admin, loader, mock_connection):
    """Tests behavior when an invalid ontology file is encountered during database initialization.

    Args:
        mock_admin (MagicMock): The mocked admin interface.
        loader (Loader): The Loader instance being tested.
        mock_connection (MagicMock): The mocked database connection.
    """
    mock_admin.new_database.return_value = None

    with (
        tempfile.NamedTemporaryFile(dir=ONTOLOGIES_DIR, suffix=".ttl") as empty_ontology_file,
        patch("stardog.http.client.Client", MagicMock()),
        patch("load._get_latest_pace_ontology_ttl", return_value=Path(empty_ontology_file.name)),
        pytest.raises(FileNotFoundError),
    ):
        loader.init_database(mock_connection)
        mock_admin.new_database.assert_called()


def test_render_template_success():
    """Tests rendering of mapping files."""
    rendered_mapping_file = render_template("mock.j2", "test_catalog", "test_schema")

    assert "test_catalog" in rendered_mapping_file
    assert "test_schema" in rendered_mapping_file


def test_render_template_not_found():
    """Tests template not found error during rendering of mapping files."""
    with pytest.raises(TemplateNotFound):
        render_template("invalid.j2", "test_catalog", "test_schema")
