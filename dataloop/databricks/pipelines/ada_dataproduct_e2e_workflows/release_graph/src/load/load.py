"""Load data from Databricks into Stardog."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import json
import logging
from pathlib import Path
from typing import Any

from jinja2 import Environment, FileSystemLoader
from pyspark.sql import SparkSession
from rddlib import (
    FullSchemaName,
    get_databricks_pat,
    get_rdd_secret,
    get_stardog_access_token,
    setup_databricks_logging,
)
from rddlib.dbx_utils import get_dbx_env_catalog, get_dbx_sql_warehouse_url
from rddlib.needs_utils import get_latest_pace_ontology
from stardog import Admin, Connection, content
from stardog.exceptions import StardogException

logger = logging.getLogger(__name__)

# Fallback if __file__ is not defined when running in a bundle.
if "__file__" in globals():
    base_dir = Path(__file__).parent
else:
    base_dir = Path.cwd()  # Current working directory as fallback

ONTOLOGIES_DIR = base_dir / "ontologies"
MAPPINGS_DIR = base_dir / "mappings"
NAMESPACES_FILE = ONTOLOGIES_DIR / "namespaces.ttl"
ONTOLOGY_SUFFIX = ".ttl"
DB_EXISTS = "0D0DE2"


def _get_latest_pace_ontology_ttl() -> Path:
    file_path = ONTOLOGIES_DIR / "pace_metamodel.ttl"
    art_path = get_latest_pace_ontology()
    with art_path.open() as fd:
        with file_path.open("wb") as out:
            out.write(fd.read())

    print(file_path.stat().st_size)

    return file_path


class Loader:
    """Used for loading data into a Stardog database."""

    def __init__(self, admin: Admin, db: str) -> None:
        """Initialize the Load class for stardog.

        Args:
            admin (Admin): An instance of the stardog admin.
            db (str): The name of the stardog database.
        """
        self.admin = admin
        self.db = db

    def init_database(self, conn: Connection) -> None:
        """Create database and populate with ontologies and namespaces."""
        try:
            self.admin.new_database(self.db)
        except StardogException as e:
            # Continue if database already exists
            if e.stardog_code != DB_EXISTS:
                raise e

        self.admin.database(self.db).import_namespaces(content.File(str(NAMESPACES_FILE)))

        # Add ontologies
        conn.begin()
        for ontology in ONTOLOGIES_DIR.iterdir():
            if ontology.suffix != ONTOLOGY_SUFFIX:
                continue
            conn.add(content.File(str(ontology)))
        pace_ontology = _get_latest_pace_ontology_ttl()

        if pace_ontology.stat().st_size == 0:
            raise FileNotFoundError(f"Failed to download PACE ontology from {pace_ontology}")
        conn.add(content.File(str(pace_ontology)))
        conn.commit()

        logger.info(f"Initialized database: {self.db}.")

    def load(
        self,
        dbricks_conn_options: dict[str, Any],
        src_schema: str,
        mapping_file: str,
        mapping_file_content: str,
    ) -> None:
        """Materialize virtual graph from schema in databricks into Stardog."""

        self.admin.materialize_virtual_graph(
            db=self.db,
            mappings=content.MappingRaw(mapping_file_content),
            options=dbricks_conn_options,
        )

        log_dict = {"schema": src_schema, "target_db": self.db, "mapping_file": mapping_file}
        logger.info(f"[materialized]:{json.dumps(log_dict, indent=None)}")


def render_template(mapping_file: str, catalog: str, schema: str, version: str | None = None) -> str:
    """Render the Jinja2 template with the given catalog and schema.

    Args:
        mapping_file (str): The mapping file to render.
        catalog (str): The catalog name to use in the template.
        schema (str): The schema name to use in the template.
        version (str, optional): The SHA to filter data. Defaults to None.
    """
    env = Environment(loader=FileSystemLoader(MAPPINGS_DIR))
    template = env.get_template(mapping_file)
    rendered_mapping = template.render({"catalog": catalog, "schema": schema, "version": version})
    if version:
        logger.info(f"Filtered data based on version: {version}")
    return rendered_mapping


def get_databricks_warehouse_jdbc_credentials() -> tuple[str, str, str]:
    """Returns the databricks SQL warehouse JDBC credentials."""
    from pyspark.dbutils import DBUtils

    rdd_sql_warehouse_jdbc_url = get_dbx_sql_warehouse_url().replace("jdbc:spark", "jdbc:databricks")
    rdd_sql_warehouse_jdbc_token = get_databricks_pat()

    dbutils = DBUtils(SparkSession.builder.getOrCreate())
    rdd_sql_warehouse_jdbc_user = dbutils.secrets.get(scope="rdd", key="stardog-jdbc-spn-client-id")

    return (rdd_sql_warehouse_jdbc_url, rdd_sql_warehouse_jdbc_user, rdd_sql_warehouse_jdbc_token)


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Load data via Stardog materialize")
    parser.add_argument(
        "-s",
        "--schema",
        dest="schema",
        default="release",
        help="Source schema in databricks",
    )
    parser.add_argument("-d", "--database", dest="target_database", help="Target database in Stardog")
    parser.add_argument("-m", "--mapping_file", dest="mapping_file", help="SMS mapping file")
    parser.add_argument(
        "-e",
        "--environment",
        dest="stardog_env",
        default="test",
        help="Load to dev, test or prod Stardog environment",
    )
    parser.add_argument(
        "-w",
        "--workspace",
        dest="databricks_env",
        default="dev",
        help="Load to dev, qa or prod Databricks workspace",
    )
    parser.add_argument(
        "-l",
        "--level",
        default="silver",
        help="Databricks layer to load from - bronze, silver or gold",
    )
    parser.add_argument("-r", "--run_id", dest="run_id")

    parser.add_argument(
        "-v",
        "--version",
        required=False,
        default=None,
        help="Specify the SHA to load",
    )

    parser.add_argument(
        "-t",
        "--task_output",
        help="Get version from task value, specify which task name",
    )

    args, unknown = parser.parse_known_args()

    sd_conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }

    catalog = get_dbx_env_catalog(args.level)

    # Setup logging
    logger_context = f"load/{args.mapping_file.split('.j2')[0]}"
    setup_databricks_logging(FullSchemaName(catalog, args.schema, False), logger_context, run_id=args.run_id)

    if args.task_output:
        from pyspark.dbutils import DBUtils

        spark = SparkSession.builder.getOrCreate()
        dbutils = DBUtils(spark)
        version = dbutils.jobs.taskValues.get(taskKey=args.task_output, key="version")
    elif args.version:
        version = args.version
    else:
        version = None

    rendered_mapping = render_template(args.mapping_file, catalog, args.schema, version)

    rdd_sql_warehouse_creds = get_databricks_warehouse_jdbc_credentials()
    dbricks_conn_options = {
        "jdbc.url": rdd_sql_warehouse_creds[0],
        "jdbc.username": rdd_sql_warehouse_creds[1],
        "jdbc.password": rdd_sql_warehouse_creds[2],
        "query.translation": "DEFAULT",
        "jdbc.driver": "com.databricks.client.jdbc.Driver",
        "sql.catalog": catalog,
        "sql.schemas": f"{catalog}.{args.schema}",
    }

    admin = Admin(**sd_conn_details)

    load = Loader(admin, args.target_database)
    load.init_database(Connection(args.target_database, **sd_conn_details))
    load.load(dbricks_conn_options, args.schema, args.mapping_file, rendered_mapping)
