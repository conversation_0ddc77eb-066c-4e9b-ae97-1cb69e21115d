# Release Graph Source

The `src` directory houses directories dedicated to each data source, such as ADO, ADX, Jira, and more.
Each of these directory contains a complete set of resources required for the data ingestion process, including extract scripts, transform scripts and workflow YAMLs.

In depth information about ETL process and each module within the `src` directory can be found below in the PACE documentation:
[Graph Ingest Databricks documentation](https://pace-docs.azurewebsites.net/rdd/main/data_engineering/graph_ingest_databricks.html)

## Getting started with Databricks

1. Install the Databricks CLI from https://docs.databricks.com/dev-tools/cli/databricks-cli.html

2. Authenticate to your Databricks workspace:
    ```
    $ databricks configure
    ```

3. To deploy a development copy of this project, type:
    ```
    $ databricks bundle deploy --target dev
    ```
    (Note that "dev" is the default target, so the `--target` parameter
    is optional here.)

    This deploys everything that's defined for this project.
    For example, the default template would deploy a job called
    `[dev yourname] extractors_dab_job` to your workspace.
    You can find that job by opening your workpace and clicking on **Workflows**.

4. Similarly, to deploy a production copy, type:
   ```
   $ databricks bundle deploy --target prod
   ```

5. To run a job or pipeline, use the "run" comand:
   ```
   $ databricks bundle run extractors_dab_job
   ```

6. Optionally, install developer tools such as the Databricks extension for Visual Studio Code from
   https://docs.databricks.com/dev-tools/vscode-ext.html.

7. For documentation on the Databricks asset bundles format used
   for this project, and for CI/CD configuration, see
   https://docs.databricks.com/dev-tools/bundles/index.html.
