"""E2E tests for Github ETL Pipeline."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse

import stardog
from queries import queries
from rddlib import get_rdd_secret, get_stardog_access_token


def test_github_data_in_stardog(db_name: str, resource: str) -> None:
    """Run SPARQL queries to test the presence of specific data in Stardog."""

    conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }

    with stardog.Admin(**conn_details) as admin:
        if not any(db.name == db_name for db in admin.databases()):
            print("Database does not exist. Exiting...")
            assert False

    query_latest = queries[resource]["latest"]
    query_details = queries[resource]["details"]
    expected_edges = queries[resource]["expected_edges"]

    with stardog.Connection(db_name, **conn_details) as conn:
        conn.begin()
        # Get latest entry
        response = conn.select(query_latest)
        bindings = response["results"]["bindings"]

        # Extracting the identifier and date
        if not bindings:
            raise Exception(f"No {resource} found in Stardog.")

        iri = bindings[0]["iri"]["value"]
        date_str = bindings[0]["date"]["value"]
        identifier = bindings[0][list(bindings[0].keys())[2]]["value"]
        print(f"Found latest {resource}: {identifier}, date: {date_str}")

        # Get all edges
        response = conn.select(query_details(iri))
        bindings = response["results"]["bindings"]
        edges = [b["p"]["value"] for b in bindings]

        # Assert that all elements in expected_edges are in edges
        missing_edges = set(expected_edges).difference(edges)
        if len(missing_edges) > 0:
            raise Exception(f"Missing expected edges {missing_edges} for the latest {resource}")

        print(f"All expected edges are present in Stardog for the latest {resource}.")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="E2E Test for the Github Pipeline")
    parser.add_argument("-d", "--db", dest="db", help="Stardog database")
    parser.add_argument(
        "-r", "--resource", dest="resource", choices=["commit", "pr", "release"], help="Type of data to test"
    )
    args, unknown = parser.parse_known_args()
    test_github_data_in_stardog(args.db, args.resource)
