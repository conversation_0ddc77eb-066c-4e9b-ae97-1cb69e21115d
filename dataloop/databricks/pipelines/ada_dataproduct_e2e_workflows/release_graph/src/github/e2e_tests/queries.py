"""SPARQL queries for Github E2E tests."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

PREFIXES = """
    PREFIX pace: <urn:pace:ontology:>
    PREFIX github: <urn:pace:ontology:github:>
"""

DETAILS_QUERY = (
    lambda iri: f"""
    SELECT ?p ?o
    WHERE {{
        <{iri}> ?p ?o .
    }}
"""
)

queries = {
    "commit": {
        "latest": PREFIXES
        + """
            SELECT ?iri ?date ?sha
            WHERE {
                ?iri a pace:ArtifactVersion ;
                    github:createdAt ?date ;
                    github:hasCommitSha ?sha .
            }
            ORDER BY DESC(?date)
            LIMIT 1
        """,
        "details": DETAILS_QUERY,
        "expected_edges": [
            "urn:pace:ontology:github:createdAt",
            "urn:pace:ontology:github:hasCommitMessage",
            "urn:pace:ontology:github:hasCommitSha",
            "urn:pace:ontology:github:hasCommitUrl",
            "urn:pace:ontology:github:hasHTMLUrl",
            "urn:pace:ontology:github:hasParent",
        ],
    },
    "pr": {
        "latest": PREFIXES
        + """
            SELECT ?iri ?date ?number
            WHERE {
                ?iri a github:PullRequest ;
                    github:updatedAt ?date ;
                    github:hasNumber ?number .
            }
            ORDER BY DESC(?date)
            LIMIT 1
        """,
        "details": DETAILS_QUERY,
        "expected_edges": [
            "urn:pace:ontology:github:hasTitle",
            "urn:pace:ontology:github:hasStatus",
            "urn:pace:ontology:github:updatedAt",
            "urn:pace:ontology:github:createdAt",
            "urn:pace:ontology:github:isDraft",
            "urn:pace:ontology:github:hasHTMLUrl",
            "urn:pace:ontology:github:isLocked",
            "urn:pace:ontology:github:hasCommitUrl",
            "urn:pace:ontology:github:hasLabel",
        ],
    },
    "release": {
        "latest": PREFIXES
        + """
            SELECT ?iri ?date ?name
            WHERE {
                ?iri a github:Release ;
                    github:createdAt ?date ;
                    github:hasTagName ?name .
            }
            ORDER BY DESC(?date)
            LIMIT 1
        """,
        "details": DETAILS_QUERY,
        "expected_edges": [
            "urn:pace:ontology:github:createdAt",
            "urn:pace:ontology:github:hasId",
            "urn:pace:ontology:github:hasName",
            "urn:pace:ontology:github:hasNodeId",
            "urn:pace:ontology:github:hasTagName",
            "urn:pace:ontology:github:hasTagUrl",
            "urn:pace:ontology:github:hasTarBallUrl",
            "urn:pace:ontology:github:hasZipBallUrl",
            "urn:pace:ontology:github:isRepresentationOf",
        ],
    },
}
