resources:
  jobs:
    github_commits_etl_rng:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "RDD Release Graph - Github - Commit - E2E - Nightly"
      description: Ingest commits from the RDD Repository
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "E2E"
        schedule: "Nightly"
        "RDD Release Graph": ""
        "Github": ""
        "Commit": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      # Nightly run at 5:13 AM UTC
      schedule:
        quartz_cron_expression: ${var.nightly_schedule}
        timezone_id: UTC
        pause_status: ${var.nightly_trigger}

      tasks:
        - task_key: github_commit_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --resource
              - commit
              - --application
              - rng
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_github_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: github_commit_transform
          depends_on:
            - task_key: github_commit_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --resource
              - commit
              - --application
              - rng
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_github_job_cluster

        - task_key: github_commit_load
          depends_on:
            - task_key: github_commit_transform
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - rdd_release_graph
              - --database
              - rdd_release_v1
              - --mapping_file
              - github_commit_rng.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_github_job_cluster

      job_clusters:
        - job_cluster_key: rdd_github_job_cluster
          new_cluster:
            data_security_mode: SINGLE_USER
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.driver_instance_pool_id}
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            custom_tags:
              "ResourceClass": "SingleNode"
