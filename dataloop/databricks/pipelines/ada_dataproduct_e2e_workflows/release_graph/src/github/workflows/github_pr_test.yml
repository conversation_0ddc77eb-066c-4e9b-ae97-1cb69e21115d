resources:
  jobs:
    github_pr_e2e_test:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "ADA Release Graph - Github - Pull Request - E2E Test"
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "E2E"
        test: ""
        "ADA Release Graph": ""
        "Github": ""
        "Pull Request": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: cleanup_bronze_test_tables
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "bronze_${var.env}",
                "-s",
                "rdd_release_graph",
                "-t",
                "github_pullrequests_test",
              ]
          job_cluster_key: rdd_github_e2e_test_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: github_pr_extract
          depends_on:
            - task_key: cleanup_bronze_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --resource
              - pr
              - --application
              - rng
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: cleanup_silver_test_tables
          depends_on:
            - task_key: github_pr_extract
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "silver_${var.env}",
                "-s",
                "rdd_release_graph",
                "-t",
                "github_pullrequests_test",
              ]
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: github_pr_transform
          depends_on:
            - task_key: cleanup_silver_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --resource
              - pr
              - --application
              - rng
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: cleanup_stardog_test_db
          depends_on:
            - task_key: github_pr_transform
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              ["stardog", "drop", "--database", "ada_github_pr_e2e_test"]

          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: github_pr_load
          depends_on:
            - task_key: cleanup_stardog_test_db
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - rdd_release_graph
              - --database
              - ada_github_pr_e2e_test
              - --mapping_file
              - github_pr_test.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: github_pr_verification
          depends_on:
            - task_key: github_pr_load
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/e2e_tests/test_github.py
            parameters:
              - --db
              - ada_github_pr_e2e_test
              - --resource
              - pr
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: github_pr_extract_and_merge
          depends_on:
            - task_key: github_pr_verification
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --resource
              - pr
              - --application
              - rng
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: github_pr_transform_and_merge
          depends_on:
            - task_key: github_pr_extract_and_merge
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --resource
              - pr
              - --application
              - rng
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: github_pr_delete_edges
          depends_on:
            - task_key: github_pr_transform_and_merge
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "stardog",
                "delete-edges",
                "--database",
                "ada_github_pr_e2e_test",
                "--source",
                "github_pr",
              ]
          job_cluster_key: rdd_github_e2e_test_job_cluster

        - task_key: github_pr_load_again
          depends_on:
            - task_key: github_pr_delete_edges
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - rdd_release_graph
              - --database
              - ada_github_pr_e2e_test
              - --mapping_file
              - github_pr_test.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_github_e2e_test_job_cluster

      job_clusters:
        - job_cluster_key: rdd_github_e2e_test_job_cluster
          new_cluster:
            data_security_mode: SINGLE_USER
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.driver_instance_pool_id}
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            custom_tags:
              "ResourceClass": "SingleNode"
