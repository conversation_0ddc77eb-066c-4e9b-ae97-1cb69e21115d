"""Unit tests for github bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import time
from datetime import date, datetime
from unittest.mock import AsyncMock, MagicMock, patch

import extract
import httpx
import pytest
from extract import GithubBronzeExtractor
from pyspark.sql.utils import AnalysisException


@pytest.fixture
def default_extractor():
    """Fixture to provide a default instance of GithubBronzeExtractor for testing."""
    with patch("extract.SparkSession", MagicMock()):
        return GithubBronzeExtractor(
            "bronze_dev",
            "release",
            "ada_release_graph",
            "commit",
            False,
            None,
            since=date(2023, 11, 1),
            until=date(2023, 12, 1),
        )


def test_extractor_invalid_date():
    """Test that the extractor raises SystemExit on invalid date."""
    with pytest.raises(Exception):
        GithubBronzeExtractor(
            "bronze_dev",
            "release",
            "ada_release_graph",
            "commit",
            False,
            None,
            since=date(2028, 1, 1),
            until=date(1999, 1, 1),
        )


def test_extractor_invalid_app():
    """Test that the extractor raises SystemExit on invalid app."""
    with pytest.raises(Exception):
        GithubBronzeExtractor(
            "bronze_dev",
            "invalid_app",
            "ada_release_graph",
            "commit",
            False,
            None,
            since=date(2028, 1, 1),
            until=date(1999, 1, 1),
        )


@pytest.mark.asyncio
async def test_latest_commit_date():
    """Test get_latest_commit_date method."""
    mock_df = MagicMock(name="df")
    mock_df.select.return_value.sort.return_value = mock_df
    mock_df.first.return_value = mock_df
    mock_df.date = "2024-01-01T16:20:00Z"

    mock_col = MagicMock(name="mock_col")
    with patch("extract.col", return_value=mock_col), patch("extract.desc", return_value=mock_col):
        mock_spark = MagicMock(name="spark")
        mock_spark.builder.getOrCreate.return_value = mock_spark
        mock_spark.read.table.return_value = mock_df

        mock_dbutils = MagicMock(name="test_dbutils")
        mock_dbutils.secrets.get.return_value = "test token"

        with patch("extract.SparkSession", mock_spark):
            extractor = GithubBronzeExtractor(
                "bronze_dev",
                "release",
                "ada_release_graph",
                "commit",
                False,
                None,
                since=date(2023, 11, 1),
                until=date(2023, 12, 1),
            )
            latest_date = extractor.get_latest_commit_ts()
            assert latest_date == datetime(2024, 1, 1, 16, 20)
        mock_spark.read.table.assert_called()
        mock_df.first.assert_called()
        mock_df.select.assert_called()


@pytest.mark.asyncio
async def test_no_latest_commit_date():
    """Test get_latest_commit_date method when no data is available."""
    mock_df = MagicMock()
    mock_df.select.return_value.sort.return_value = mock_df
    mock_df.first.return_value = None

    mock_col = MagicMock(name="mock_col")
    with patch("extract.col", return_value=mock_col), patch("extract.desc", return_value=mock_col):
        mock_spark = MagicMock()
        mock_spark.read.return_value.table.return_value.select.sort = mock_df
        mock_spark.builder.getOrCreate.return_value = mock_spark
        mock_spark.read.table.return_value = mock_df

        mock_dbutils = MagicMock()
        mock_dbutils.secrets.return_value.get.return_value = "test_token"

        with patch("extract.SparkSession", mock_spark):
            with pytest.raises(Exception):
                extractor = GithubBronzeExtractor(
                    "bronze_dev",
                    "release",
                    "ada_release_graph",
                    "commit",
                    False,
                    None,
                    since=date(2023, 11, 1),
                    until=date(2023, 12, 1),
                )
                extractor.get_latest_commit_ts()
        mock_spark.read.table.assert_called()
        mock_df.first.assert_called()
        mock_df.select.assert_called()


@pytest.mark.parametrize(
    "commit_found, expected_result",
    [(1, True), (0, False)],  # Test case where the commit is found  # Test case where the commit is not found
    ids=["commit_found", "commit_not_found"],
)
def test_check_commit_exists(commit_found, expected_result):
    """Test _check_commit_exists method."""
    mock_spark = MagicMock(name="spark")
    mock_df = MagicMock(name="DataFrame")
    mock_spark.read.table.return_value = mock_df
    mock_df.filter.return_value.count.return_value = commit_found

    # Patch the SparkSession.builder.getOrCreate to use the mock
    with patch("extract.SparkSession.builder.getOrCreate", return_value=mock_spark):
        extractor = GithubBronzeExtractor(
            "bronze_dev",
            "release",
            "ada_release_graph",
            "commit",
            False,
            None,
            sha="test_sha",
            since=date(2023, 11, 1),
            until=date(2023, 12, 1),
        )
        extractor.spark = mock_spark
        result = extractor._check_commit_exists()
        assert result == expected_result


def test_get_url(default_extractor):
    """Test get_url method."""
    url = default_extractor.get_url()
    assert type(url) is str


def test_get_url_invalid_resource():
    """Test get_url method with invalid resource."""
    with patch("extract.SparkSession", MagicMock()):
        with pytest.raises(Exception):
            extractor = GithubBronzeExtractor(
                "bronze_dev",
                "release",
                "ada_release_graph",
                "-invalid-",
                False,
                None,
                since=date(2023, 11, 1),
                until=date(2023, 12, 1),
            )
            extractor.get_url()


def test_get_params_no_pages(default_extractor):
    """Test get_params method with no pages."""
    params = default_extractor.get_params()
    with pytest.raises(KeyError):
        assert not params["page"]


def test_get_params_with_pages(default_extractor):
    """Test get_params method with pages."""
    params = default_extractor.get_params(3)
    assert params["page"]


@pytest.mark.asyncio
async def test_fetch_paginated_data(default_extractor):
    """Test fetch_paginated_data method."""
    mock_response = MagicMock(name="mock_response")
    mock_response.status_code = 200
    mock_response.headers = {"test_header": "it is"}
    mock_response.json.return_value = '{"test_json": "it is"}'
    mock_client = AsyncMock(name="mock_client")
    mock_client.get.return_value = mock_response

    with patch("httpx.AsyncClient.__aenter__", return_value=mock_client):
        headers, data = await default_extractor.fetch_paginated_data(5)

    assert type(headers) is dict
    assert type(data) is str


@pytest.mark.asyncio
async def test_fetch_paginated_data_error_code(default_extractor):
    """Test fetch_paginated_data method with error code."""
    mock_response = MagicMock(name="mock_response")
    mock_response.status_code = 404
    mock_client = AsyncMock(name="mock_client")
    mock_client.get.return_value = mock_response

    with patch("httpx.AsyncClient.__aenter__", return_value=mock_client):
        with pytest.raises(Exception):
            await default_extractor.fetch_paginated_data(5)


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "exception",
    [httpx.ReadTimeout, httpx.ConnectTimeout, httpx.WriteTimeout],
    ids=["read_timeout_exception", "connect_timeout_exception", "write_timeout_exception"],
)
async def test_fetch_paginated_data_exceptions(default_extractor, exception):
    """Test fetch_paginated_data method with exceptions."""
    mock_client = AsyncMock(name="mock_client")
    mock_client.return_value.get.side_effect = exception("test_exception")

    with pytest.raises(exception):
        with patch("httpx.AsyncClient.__aenter__", mock_client):
            await default_extractor.fetch_paginated_data(5)

    assert mock_client.return_value.get.call_count == extract.RETRY_ATTEMPTS


def test_get_page_count(default_extractor):
    """Test get_page_count method."""
    TEST_COUNT = 4
    mock_client = MagicMock(name="mock_client")
    mock_client.get.return_value = MagicMock()
    mock_params = MagicMock()
    mock_params.get.return_value = [TEST_COUNT]

    with (
        patch("httpx.Client.__enter__", return_value=mock_client),
        patch("extract.parse_qs", return_value=mock_params),
        patch("extract.urlparse", return_value=MagicMock()),
    ):
        count = default_extractor.get_page_count()
    assert count == TEST_COUNT


def test_get_page_count_key_error(default_extractor):
    """Test get_page_count method for key error."""
    mock_client = MagicMock(name="mock_client")
    mock_client.return_value.get.return_value = MagicMock()

    with (
        patch("httpx.Client.__enter__", mock_client),
        patch("extract.parse_qs", side_effect=KeyError("test error")),
        patch("extract.urlparse", return_value=MagicMock()),
    ):
        count = default_extractor.get_page_count()
    assert count == 1


def test_get_page_count_exception(default_extractor):
    """Test get_page_count method for exceptions."""
    mock_client = MagicMock(name="mock_client")
    mock_client.return_value.get.return_value = MagicMock()

    with pytest.raises(Exception):
        with (
            patch("httpx.Client.__enter__", mock_client),
            patch("extract.parse_qs", side_effect=Exception("test error")),
            patch("extract.urlparse", return_value=MagicMock()),
        ):
            default_extractor.get_page_count()


def test_get_page_count_http_status_error(default_extractor):
    """Test get_page_count method for http status error."""
    mock_response = MagicMock()
    mock_response.raise_for_status.side_effect = httpx.HTTPStatusError(
        "test message", request=MagicMock(), response=MagicMock()
    )
    mock_client = MagicMock(name="mock_client")
    mock_client.return_value.get.return_value = mock_response
    with pytest.raises(httpx.HTTPStatusError):
        with patch("httpx.Client.__enter__", mock_client):
            default_extractor.get_page_count()


@pytest.mark.asyncio
async def test_extract_data(default_extractor):
    """Test that correct amount of data is extracted (PAGES * ENTRIES)."""
    PAGES = 10
    ENTRIES = 100
    # Generate test response, array with dict of headers and array with json formatted strings
    test_response = [
        {
            extract.GITHUB_HEADER_LIMIT_REMAINING: 4000,
            extract.GITHUB_HEADER_LIMIT_RESET: 10,
        },
        ['{"test_data": "test_value"}' for _ in range(ENTRIES)],
    ]
    with (
        patch.object(default_extractor, "get_page_count", return_value=PAGES),
        patch.object(default_extractor, "fetch_paginated_data", return_value=test_response),
    ):
        data = await default_extractor.extract_data()
    assert len(data) == PAGES * ENTRIES


@pytest.mark.asyncio
async def test_extract_data_ratelimited(default_extractor):
    """Test that correct amount of data is extracted (PAGES * ENTRIES), with rate limited."""
    PAGES = 10
    ENTRIES = 100
    # Generate test response, array with dict of headers and array with json formatted strings
    test_response = [
        {
            extract.GITHUB_HEADER_LIMIT_REMAINING: extract.GITHUB_RATELIMIT_THRESHOLD - 1,
            extract.GITHUB_HEADER_LIMIT_RESET: int(time.time()) + 1,
        },
        ['{"test_data": "test_value"}' for _ in range(ENTRIES)],
    ]
    with (
        patch.object(default_extractor, "get_page_count", return_value=PAGES),
        patch.object(default_extractor, "fetch_paginated_data", return_value=test_response),
    ):
        data = await default_extractor.extract_data()
    assert len(data) == PAGES * ENTRIES


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "exception",
    [
        httpx.HTTPStatusError("test message", request=MagicMock(), response=MagicMock()),
        httpx.RequestError("test_message"),
    ],
    ids=["http_status_error", "request_error"],
)
async def test_extract_data_exceptions(default_extractor, exception):
    """Test extract data for exceptions."""
    PAGES = 10
    with pytest.raises(Exception):
        with (
            patch.object(default_extractor, "get_page_count", return_value=PAGES),
            patch.object(default_extractor, "fetch_paginated_data", side_effect=exception),
        ):
            await default_extractor.extract_data()


@pytest.mark.asyncio
async def test_ingest_data_extract_error(default_extractor):
    """Test ingest_data method for runtime error."""
    with pytest.raises(Exception):
        with patch.object(default_extractor, "extract_data", side_effect=RuntimeError("testing")):
            await default_extractor.ingest_data()
