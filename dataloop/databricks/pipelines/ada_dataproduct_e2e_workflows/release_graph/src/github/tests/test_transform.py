"""Unit tests for github silver transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date, datetime
from unittest.mock import patch

import pytest
from constants import bronze_schemas, silver_schemas
from pyspark.errors import AnalysisException
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType
from rddlib import delta_table_utils as dtu
from rddlib.utils import full_qualname
from transform import GithubSilverTransformer

DATE = "2024-02-09T14:19:24Z"
mock_bronze_commit = [
    {
        "sha": "sha1",
        "node_id": "node_id1",
        "commit": {
            "committer": {"date": DATE},
            "message": "First commit",
            "tree": {
                "sha": "tree1",
                "url": "https://api.github.com/repos/PACE-INT/rdd/git/trees/tree1",
            },
            "url": "https://api.github.com/repos/PACE-INT/rdd/git/commits/sha1",
            "comment_count": 0,
            "verification": {
                "verified": "false",
                "reason": "unsigned",
                "signature": None,
                "payload": None,
            },
        },
        "url": "https://api.github.com/repos/PACE-INT/rdd/commits/sha1",
        "html_url": "https://api.github.com/repos/PACE-INT/rdd/commits/sha1",
        "comments_url": "https://api.github.com/repos/PACE-INT/rdd/commits/sha1/comments",
        "parents": [
            {
                "sha": "parent_sha1",
                "url": "https://api.github.com/repos/PACE-INT/rdd/commits/parent_sha1",
                "html_url": "https://github.com/PACE-INT/rdd/commit/parent_sha1",
            },
            {
                "sha": "parent_sha2",
                "url": "https://api.github.com/repos/PACE-INT/rdd/commits/parent_sha2",
                "html_url": "https://github.com/PACE-INT/rdd/commit/parent_sha2",
            },
        ],
    },
    {
        "sha": "sha2",
        "node_id": "node_id1",
        "commit": {
            "committer": {"date": DATE},
            "message": "Second commit",
            "tree": {
                "sha": "tree2",
                "url": "https://api.github.com/repos/PACE-INT/rdd/git/trees/tree2",
            },
            "url": "https://api.github.com/repos/PACE-INT/rdd/git/commits/sha2",
            "comment_count": 0,
            "verification": {
                "verified": "false",
                "reason": "unsigned",
                "signature": None,
                "payload": None,
            },
        },
        "url": "https://api.github.com/repos/PACE-INT/rdd/commits/sha2",
        "html_url": "https://api.github.com/repos/PACE-INT/rdd/commits/sha2",
        "comments_url": "https://api.github.com/repos/PACE-INT/rdd/commits/sha2/comments",
        "parents": [
            {
                "sha": "parent_sha3",
                "url": "https://api.github.com/repos/PACE-INT/rdd/commits/parent_sha3",
                "html_url": "https://github.com/PACE-INT/rdd/commit/parent_sha3",
            }
        ],
    },
]

mock_silver_commit = [
    {"sha": "sha1", "created_date": datetime.strptime(DATE, "%Y-%m-%dT%H:%M:%SZ")},
    {
        "sha": "sha2",
        "created_date": datetime.strptime("2024-02-10T14:19:24Z", "%Y-%m-%dT%H:%M:%SZ"),
    },
]

mock_bronze_pr = [
    {
        "url": "https://url",
        "html_url": "https://url",
        "state": "open",
        "title": "some title",
        "locked": False,
        "id": 123,
        "number": 1,
        "created_at": DATE,
        "updated_at": DATE,
        "closed_at": DATE,
        "merged_at": DATE,
        "merge_commit_sha": "sha1",
        "labels": [{"default": "false", "name": "test_label", "id": "123"}],
        "milestone": None,
        "draft": False,
        "commits_url": "https://url",
    }
]

mock_bronze_release = [
    {
        "tag_name": "test_tag/1.0.0",
        "name": "test tag",
        "target_commitish": "sha1",
        "url": "https://url",
        "tarball_url": "https://url",
        "zipball_url": "https://url",
        "node_id": "node id",
        "created_at": DATE,
        "id": 123,
    }
]

mock_bronze_tag = [
    {
        "name": "test_tag",
        "target_commitish": "sha1",
        "commit": {"sha": "sha1", "url": "https://url"},
        "tarball_url": "https://url",
        "zipball_url": "https://url",
        "node_id": "node id",
    }
]


@pytest.fixture(scope="session")
def spark():
    """Fixture to provide a Spark session object for unit testing."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for Github Transformer").getOrCreate()


@pytest.fixture
def transformer_commit():
    """Fixture to provide a GithubSilverTransformer instance for commit data transformation."""
    return GithubSilverTransformer("dev_bronze", "release", "dev_silver", "commit", False)


@pytest.fixture
def transformer_pr():
    """Fixture to provide a GithubSilverTransformer instance for pull request data transformation."""
    return GithubSilverTransformer("dev_bronze", "release", "dev_silver", "pr", False)


@pytest.fixture
def transformer_release():
    """Fixture to provide a GithubSilverTransformer instance for release data transformation."""
    return GithubSilverTransformer("dev_bronze", "release", "dev_silver", "release", False)


"""
A fixture for setting up temporary views in Spark. This is used to create a mock
environment for testing data transformation functions by providing a temporary view
containing the mock data based on a specified schema. It allows for the simulation
of different input data scenarios in a controlled testing environment.

Arguments:
- spark: The Spark session to be used for creating the DataFrame and temporary view.
- mock_data: The mock data to populate the DataFrame.
- schema: The schema defining the structure of the DataFrame.
- view_name: The name under which the temporary view will be created.
"""


@pytest.fixture(scope="function")
def setup_temp_view(spark):
    """A fixture to create a temporary view based on provided parameters."""

    def _create_temp_view(mock_data, schema, view_name):
        df = spark.createDataFrame(mock_data, schema=schema)
        df.createOrReplaceTempView(view_name)
        return view_name

    return _create_temp_view


def test_get_cleaned_commits(spark, transformer_commit):
    """Test get_cleaned_commits method."""
    df = spark.createDataFrame(mock_bronze_commit, bronze_schemas.COMMIT_RESPONSE)
    transformed_df = transformer_commit.get_cleaned_commits(df)
    assert transformed_df.count() == 3, "The number of commits should match the input data."
    all_rows = transformed_df.collect()
    first_row = all_rows[0]
    assert first_row["commit_sha"] == "sha1", "commit_sha of 1st row does not match expected value"
    assert first_row["parent"] == "parent_sha1", "parent of 1st row does not match expected value"
    second_row = all_rows[1]
    assert second_row["commit_sha"] == "sha1", "commit_sha of 2nd row does not match expected value"
    assert second_row["parent"] == "parent_sha2", "parent of 2nd row does not match expected value"
    third = all_rows[2]
    assert third["commit_sha"] == "sha2", "commit_sha of 3rd row does not match expected value"
    assert third["parent"] == "parent_sha3", "parent of 3rd row does not match expected value"


def test_cleaned_commits_exception(spark, transformer_commit):
    """Test get_cleaned_commits method for exception."""
    df = spark.createDataFrame(mock_bronze_commit, bronze_schemas.COMMIT_RESPONSE)
    df_invalid = df.withColumnRenamed("sha", "sha123")
    with pytest.raises(AnalysisException):
        transformer_commit.get_cleaned_commits(df_invalid)


def test_get_cleaned_pr(spark, transformer_pr):
    """Test get_cleaned_pr method."""
    df = spark.createDataFrame(mock_bronze_pr, bronze_schemas.PULL_REQUEST_REPSONSE)
    transformed_df = transformer_pr.get_cleaned_pr(df)
    assert transformed_df.count() == 1, "The number of pull requests should match the input data."
    first_row = transformed_df.collect()[0]
    assert first_row["number"] == 1, "number does not match expected value"
    assert first_row["labels"][0] == "test_label", "label does not match expected value"


def test_cleaned_pr_exception(spark, transformer_pr):
    """Test get_cleaned_pr method for exception."""
    df = spark.createDataFrame(mock_bronze_pr, bronze_schemas.PULL_REQUEST_REPSONSE)
    df_invalid = df.withColumnRenamed("number", "number123")
    with pytest.raises(AnalysisException):
        transformer_pr.get_cleaned_pr(df_invalid)


def test_get_cleaned_release(spark, transformer_release):
    """Test get_cleaned_release method."""
    df = spark.createDataFrame(mock_bronze_release, bronze_schemas.RELEASE_RESPONSE)
    transformed_df = transformer_release.get_cleaned_release(df)
    assert transformed_df.count() == 1, "The number of releases should match the input data."
    first_row = transformed_df.collect()[0]
    assert first_row["tag_name"] == "test_tag/1.0.0", "tag name does not match expected value"


def test_cleaned_release_exception(spark, transformer_release):
    """Test get_cleaned_release method for exception."""
    df = spark.createDataFrame(mock_bronze_release, bronze_schemas.RELEASE_RESPONSE)
    df_invalid = df.withColumnRenamed("id", "id123")
    with pytest.raises(AnalysisException):
        transformer_release.get_cleaned_release(df_invalid)


def test_get_cleaned_tag(spark, transformer_release):
    """Test get_cleaned_tag method."""
    df = spark.createDataFrame(mock_bronze_tag, bronze_schemas.TAG_RESPONSE)
    transformed_df = transformer_release.get_cleaned_tag(df)
    assert transformed_df.count() == 1, "The number of releases should match the input data."
    first_row = transformed_df.collect()[0]
    assert first_row["name"] == "test_tag", "tag name does not match expected value"


def test_cleaned_tag_exception(spark, transformer_release):
    """Test get_cleaned_tag method for exception."""
    df = spark.createDataFrame(mock_bronze_tag, bronze_schemas.TAG_RESPONSE)
    df_invalid = df.withColumnRenamed("name", "name123")
    with pytest.raises(AnalysisException):
        transformer_release.get_cleaned_tag(df_invalid)


def test_get_latest_silver(spark, transformer_commit, setup_temp_view):
    """Test get_latest_silver method."""
    view_name = setup_temp_view(
        mock_silver_commit, silver_schemas.COMMIT_SCHEMA, "temp_silver_table_commit"
    )  # Setup the temporary view.
    most_recent_date = transformer_commit.get_latest_silver(table_full_name=view_name)
    most_recent_date_str = most_recent_date.strftime("%Y-%m-%d %H:%M:%S")
    expected_date = "2024-02-10 14:19:24"
    assert most_recent_date_str == expected_date, "Most recent date should match the latest date from the mock data"


def test_table_schema_schema(transformer_commit):
    """Test case to verify the schema extraction."""
    schema = transformer_commit.table_schema
    assert type(schema) is StructType


"""
This test verifies the behavior of the transformation function `transform_data`.
It tests whether the correct methods are called based on the existence of a table.
Depending on whether the table exists or not, it checks if the
appropriate data handling action (merge or overwrite) is executed the expected number of times.
This is achieved by parameterizing the test with different scenarios, including different transformer
fixtures for commit, pr or tag, table existence states, expected method call counts, temporary view names,
mock data, and schemas.

Arguments:
- transformer_fixture: The name of the transformer fixture to test - commmit, pr or tag.
- table_exists_return_value: A boolean indicating whether the delta table is supposed to exist (True) or not (False).
- expected_table_exist_call_count: The expected number of times the table existence check is performed.
- expected_merge_call_count: The expected number of times data should be merged into the existing table.
- expected_overwrite_call_count: The expected number of times data should be overwritten into a table.
- temp_view_name: The name of the temporary view to be used in the test.
- mock_data: The mock data to be used for creating a DataFrame for the test.
- schema: The schema to be used for creating the DataFrame from the mock data.
"""


@pytest.mark.parametrize(
    "transformer_fixture, temp_view_name, mock_data, schema",
    [
        (
            "transformer_commit",
            "temp_bronze_table_commit",
            mock_bronze_commit,
            bronze_schemas.COMMIT_RESPONSE,
        ),
        (
            "transformer_commit",
            "temp_bronze_table_commit",
            mock_bronze_commit,
            bronze_schemas.COMMIT_RESPONSE,
        ),
        (
            "transformer_pr",
            "temp_bronze_table_pr",
            mock_bronze_pr,
            bronze_schemas.PULL_REQUEST_REPSONSE,
        ),
        (
            "transformer_pr",
            "temp_bronze_table_pr",
            mock_bronze_pr,
            bronze_schemas.PULL_REQUEST_REPSONSE,
        ),
        (
            "transformer_release",
            "temp_bronze_table_release",
            mock_bronze_release,
            bronze_schemas.RELEASE_RESPONSE,
        ),
        (
            "transformer_release",
            "temp_bronze_table_release",
            mock_bronze_release,
            bronze_schemas.RELEASE_RESPONSE,
        ),
    ],
    ids=[
        "commit_table_exit_true",
        "commit_table_exit_false",
        "pr_table_exit_true",
        "pr_table_exit_false",
        "tag_table_exit_true",
        "tag_table_exit_false",
    ],
)
def test_transform_data_table_exists_or_not(
    request,  # The pytest `request` object, providing access to fixtures.
    setup_temp_view,  # Fixture to setup a temporary Spark view with mock data for testing.
    transformer_fixture,
    temp_view_name,
    mock_data,
    schema,
):
    """Test case for data transformation based on table existence."""
    # Dynamically retrieve the specified transformer fixture to test different transformation scenarios.
    transformer: GithubSilverTransformer = request.getfixturevalue(transformer_fixture)
    # Setup a temporary view with the provided mock data and schema. This simulates the input data source.
    view_name = setup_temp_view(mock_data, schema, temp_view_name)
    with (
        patch.object(transformer, "get_latest_silver", return_value=date(2024, 2, 8)),
        patch("rddlib.delta_table_utils.merge_or_overwrite") as mock_merge_or_overwrite,
        patch("rddlib.delta_table_utils.table_exists", return_value=True),
        patch("rddlib.delta_table_utils.update_table_metadata"),
    ):
        # Perform the data transformation and check method calls
        transformer.transform_data(bronze_table_full=view_name)
        assert mock_merge_or_overwrite.called


def test_transform_data_sha_not_found(request, setup_temp_view, transformer_commit: GithubSilverTransformer):
    """Test case for data transformation when a specific SHA is provided but not found."""
    transformer_commit.sha = "non_existing_sha"
    view_name = setup_temp_view(mock_bronze_commit, bronze_schemas.COMMIT_RESPONSE, "temp_bronze_table_commit")

    # Mock dependencies and check that CommitNotFoundException is raised
    with (
        patch.object(transformer_commit, "get_latest_silver", return_value=date(2024, 2, 8)),
        patch("rddlib.delta_table_utils.table_exists", return_value=True),
    ):

        with pytest.raises(Exception) as e:
            transformer_commit.transform_data(bronze_table_full=view_name)

        # Assert that the exception message is as expected
        assert str(e.value) == "Commit: non_existing_sha not found in bronze layer."
