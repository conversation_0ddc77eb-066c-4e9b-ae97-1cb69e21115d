"""Module for creating a combined table of releases and tags to store the commit SHA along a release."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging

from constants.common import (
    DATABRICKS_TABLE_MAP,
    DATABRICKS_TEST_TABLE_MAP,
    GOLD_TABLE_DESCRIPTION_MAP,
    SILVER_MERGE_CONDITION,
)
from constants.gold_schemas import RELEASE_SCHEMA
from dbxlib.pyspark import compare_struct_types
from pyspark.sql import DataFrame, SparkSession
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import setup_databricks_logging
from rddlib.dbx_utils import DBX_APP_SCHEMA_MAP, get_dbx_env_catalog

logger = logging.getLogger(__name__)

GOLD_TABLE = "github_releases"


def _parse_args() -> argparse.Namespace:
    parser = argparse.ArgumentParser(description="Combine release and tag information.")
    parser.add_argument(
        "-a",
        "--application",
        dest="app",
        default="release",
        help="Application, ex. release, rng",
    )
    parser.add_argument("--test_mode", action="store_true", default=False, help="Run in test mode.")
    parser.add_argument("-i", "--run_id", dest="run_id")

    return parser.parse_args()


def _combine_release_tag(
    spark: SparkSession,
    dbx_catalog_silver: str,
    dbx_schema: str,
    test_mode: bool = False,
) -> DataFrame:
    """Combine release and tag information to store the commit SHA along the release information."""
    table_map = DATABRICKS_TEST_TABLE_MAP if test_mode else DATABRICKS_TABLE_MAP
    # Read release and tag tables
    release_table_full = f"{dbx_catalog_silver}.{dbx_schema}.{table_map['release']}"
    release_df = spark.read.table(release_table_full)
    tag_table_full = f"{dbx_catalog_silver}.{dbx_schema}.{table_map['tag']}"
    tag_df = spark.read.table(tag_table_full)

    result_df = release_df.join(tag_df, on=(release_df.tag_name == tag_df.name), how="left").select(
        release_df["*"], tag_df["commit_sha"]
    )

    return result_df


def _update_delta_table(
    result_df: DataFrame,
    gold_table_full: str,
) -> None:
    """Update the delta table with the new data."""
    # Make sure the schema is correct
    assert compare_struct_types(
        result_df.schema, RELEASE_SCHEMA
    ), f"Invalid schema: {result_df.schema}\nExpected: {RELEASE_SCHEMA}"

    # Update delta table
    dtu.merge_or_overwrite(result_df, gold_table_full, SILVER_MERGE_CONDITION["release"])
    # Add metadata to the table
    description = GOLD_TABLE_DESCRIPTION_MAP["github_releases"]
    dtu.update_table_metadata(gold_table_full, description)


def main() -> None:
    """Main function to combine release and tag information."""
    args = _parse_args()
    app = args.app
    test_mode = args.test_mode

    dbx_catalog_silver = get_dbx_env_catalog("silver")
    dbx_catalog_gold = get_dbx_env_catalog("gold")
    dbx_schema = DBX_APP_SCHEMA_MAP[app]
    # Name of result table
    if test_mode:
        gold_table = DATABRICKS_TEST_TABLE_MAP["release"]
    else:
        gold_table = DATABRICKS_TABLE_MAP["release"]
    gold_table_full = f"{dbx_catalog_gold}.{dbx_schema}.{gold_table}"

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(dbx_catalog_gold, dbx_schema, False), "github_release/gold", run_id=args.run_id
    )
    spark = SparkSession.builder.getOrCreate()

    logger.info("Combining release and tag information.")
    result_df = _combine_release_tag(spark, dbx_catalog_silver, dbx_schema, test_mode)

    logger.info(f'Writing combined release and tag information to gold table "{gold_table_full}".')
    _update_delta_table(result_df, gold_table_full)


if __name__ == "__main__":
    main()
