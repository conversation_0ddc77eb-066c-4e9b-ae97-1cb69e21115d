"""Module to transform commits from bronze to silver layer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from datetime import date

from constants import silver_schemas
from constants.common import (
    DATABRICKS_TABLE_MAP,
    DATABRICKS_TEST_TABLE_MAP,
    SILVER_MERGE_CONDITION,
    SILVER_TABLE_DESCRIPTION_MAP,
)
from dbxlib.pyspark import compare_struct_types
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F
from pyspark.sql.functions import col, explode, expr
from pyspark.sql.types import StructType
from rddlib import DBX_APP_SCHEMA_MAP, FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, quality_check, setup_databricks_logging

logger = logging.getLogger(__name__)


class GithubSilverTransformer:
    """Class for Github silver transform.

    Transform data for the specified application,
    resource (pr, release, tag, commit)
    """

    dbx_catalog_bronze: str
    dbx_schema: str
    dbx_catalog_silver: str
    table: str
    resource: str
    test_mode: bool
    history: bool
    sha: str | None

    @property
    def table_schema(self) -> StructType:
        """Returns the schema for the relevant resource type."""
        return silver_schemas.SILVER_SCHEMA_LOOKUP_TABLE[self.resource]

    def __init__(
        self,
        dbx_catalog_bronze: str,
        dbx_schema: str,
        dbx_catalog_silver: str,
        resource: str,
        test_mode: bool = False,
        history: bool = False,
        sha: str | None = None,
    ):
        """Initialize a GithubSilverTransformer instance."""
        self.resource = resource
        self.test_mode = test_mode
        self.history = history
        self.sha = sha

        self.dbx_catalog_bronze = dbx_catalog_bronze
        self.dbx_schema = dbx_schema
        self.dbx_catalog_silver = dbx_catalog_silver
        self.table = (DATABRICKS_TEST_TABLE_MAP if test_mode else DATABRICKS_TABLE_MAP)[resource]

        self.spark = SparkSession.builder.getOrCreate()
        logger.info(
            f"Using: Bronze catalog: {self.dbx_catalog_bronze}, Silver catalog: {self.dbx_catalog_silver},"
            f"schema: {self.dbx_schema} & table: {self.table}"
        )

    def get_latest_silver(self, table_full_name: str | None = None) -> date:
        """Get the latest commit in silver layer."""
        if not table_full_name:
            table_full_name = f"{self.dbx_catalog_silver}.{self.dbx_schema}.`{self.table}`"
        df_silver = self.spark.read.table(table_full_name)
        most_recent_commit_date = (
            df_silver.agg(F.max("created_date").alias("max_commit_date"))  # type: ignore[union-attr]
            .select("max_commit_date")
            .first()
            .max_commit_date
        )
        logger.info(f"Most recent commit date in silver layer is {most_recent_commit_date}")
        return most_recent_commit_date

    def get_cleaned_commits(self, df: DataFrame) -> DataFrame:
        """Pick fields of interest from commits for silver layer."""
        return df.select(
            col("sha").alias("commit_sha"),
            col("commit.message").alias("message"),
            col("commit.comment_count").alias("comment_count"),
            col("url"),
            F.to_timestamp(col("commit.committer.date")).alias("created_date"),
            col("html_url"),
            explode(expr("transform(parents, x -> x.sha)")).alias("parent"),
        )

    def get_cleaned_pr(self, df: DataFrame) -> DataFrame:
        """Pick fields of interest from pull requests for silver layer."""
        return df.select(
            col("url"),
            col("html_url"),
            col("state"),
            col("title"),
            col("locked"),
            col("id"),
            col("number").cast("int").alias("number"),
            F.to_timestamp(col("created_at")).alias("created_date"),
            F.to_timestamp(col("updated_at")).alias("updated_date"),
            F.to_timestamp(col("closed_at")).alias("closed_date"),
            F.to_timestamp(col("merged_at")).alias("merged_date"),
            col("merge_commit_sha").alias("commit_sha"),
            F.expr("transform(labels, x -> x.name)").alias("labels"),
            col("milestone"),
            col("draft"),
            col("commits_url"),
        )

    def get_cleaned_release(self, df: DataFrame) -> DataFrame:
        """Pick fields of interest from release for silver layer."""
        return df.select(
            col("name"),
            col("tag_name"),
            col("target_commitish"),
            col("url"),
            col("tarball_url"),
            col("zipball_url"),
            col("node_id"),
            F.to_timestamp(col("created_at")).alias("created_date"),
            col("id"),
        )

    def get_cleaned_tag(self, df: DataFrame) -> DataFrame:
        """Pick fields of interest from tag for silver layer."""
        return df.select(
            col("name"),
            col("zipball_url"),
            col("tarball_url"),
            col("commit.sha").alias("commit_sha"),
            col("node_id"),
        )

    def quality_check_run(self, df: DataFrame, column_to_check: str) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        total_count = df.count()

        missing_value_rows = quality_check.missing_value_detection(df, column=column_to_check)
        missing_values_count = missing_value_rows.count()
        if missing_values_count == 0:
            quality_check.log_quality_check_result(True, "missing_value_detection", None)
        else:
            quality_check.log_quality_check_result(False, "missing_value_detection", missing_values_count)

        # Duplicate check
        _, duplicate_id_rows = quality_check.deduplicate(
            df,
            subset=[column_to_check],
            time_window=None,
            use_hashing=False,
            just_test=False,
        )
        count_duplicates = len(duplicate_id_rows)
        if count_duplicates == 0:
            quality_check.log_quality_check_result(True, "deduplicate", None)
        else:
            quality_check.log_quality_check_result(False, "deduplicate", f"{count_duplicates / total_count}")

    def transform_data(self, bronze_table_full: str | None = None) -> None:
        """Update databricks delta tables for github silver layer."""
        if bronze_table_full is None:
            bronze_table_full = f"{self.dbx_catalog_bronze}.{self.dbx_schema}.`{self.table}`"
        df_bronze = self.spark.read.table(bronze_table_full)

        silver_table_full = f"{self.dbx_catalog_silver}.{self.dbx_schema}.`{self.table}`"

        if self.resource == "commit":
            if self.sha:
                df_bronze = df_bronze.filter(col("sha") == self.sha)
                if df_bronze.count() == 0:
                    raise Exception(f"Commit: {self.sha} not found in bronze layer.")
            elif dtu.table_exists(silver_table_full) and not self.history:
                df_bronze = df_bronze.filter(col("commit.committer.date") > self.get_latest_silver())

            df_silver = self.get_cleaned_commits(df_bronze)
            # Run the data quality check
            self.quality_check_run(df_silver, column_to_check="commit_sha")
        elif self.resource == "pr":
            df_silver = self.get_cleaned_pr(df_bronze)
            df_silver = df_silver.dropDuplicates(["number"])
            # Run the data quality check
            self.quality_check_run(df_silver, column_to_check="id")
        elif self.resource == "release":
            df_silver = self.get_cleaned_release(df_bronze)
            # Run the data quality check
            self.quality_check_run(df_silver, column_to_check="id")
        elif self.resource == "tag":
            df_silver = self.get_cleaned_tag(df_bronze)
            # Run the data quality check
            self.quality_check_run(df_silver, column_to_check="node_id")
        else:
            raise Exception(f"Resource {self.resource} not supported")

        # Make sure the schema is correct
        assert compare_struct_types(
            df_silver.schema, self.table_schema
        ), f"Invalid schema: {df_silver.schema}\nExpected: {self.table_schema}"

        # Update delta table
        dtu.merge_or_overwrite(df_silver, silver_table_full, SILVER_MERGE_CONDITION[self.resource])
        # Add metadata to the table
        description = SILVER_TABLE_DESCRIPTION_MAP.get(self.table, "")
        dtu.update_table_metadata(silver_table_full, description)


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Transform github data")

    parser.add_argument(
        "-a",
        "--application",
        dest="app",
        default="release",
        help="Application, ex. release, rng",
    )
    parser.add_argument("-r", "--resource", dest="resource", help="Github resource, ex. commit, pr, release, tag")
    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )
    parser.add_argument(
        "--history",
        action="store_true",
        help="Set True to transform all commits from Bronze Layer",
    )
    parser.add_argument("-i", "--run_id", dest="run_id")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )
    parser.add_argument(
        "--sha",
        dest="sha",
        required=False,
        default=None,
        help="""Specify the SHA of a specific commit to transform.
            If provided, only data for this commit will be processed.""",
    )

    args, unknown = parser.parse_known_args()
    app = args.app
    history = args.history
    test_mode = args.test_mode

    dbx_catalog_bronze = get_dbx_env_catalog("bronze")
    dbx_catalog_silver = get_dbx_env_catalog("silver")
    dbx_schema = DBX_APP_SCHEMA_MAP[app]

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(dbx_catalog_silver, dbx_schema, False), f"github_{args.resource}/silver", run_id=args.run_id
    )

    transformer = GithubSilverTransformer(
        dbx_catalog_bronze, dbx_schema, dbx_catalog_silver, args.resource, test_mode, history, args.sha
    )

    if args.sha:
        logger.info(
            f"Transformation of github data for: app: {app}, resource: {args.resource}, "
            f"sha: {args.sha}, test_mode: {args.test_mode}"
        )
    else:
        logger.info(
            f"Transformation of github data for: app: {app}, resource: {args.resource}, "
            f" test_mode: {args.test_mode}"
        )

    transformer.transform_data()
