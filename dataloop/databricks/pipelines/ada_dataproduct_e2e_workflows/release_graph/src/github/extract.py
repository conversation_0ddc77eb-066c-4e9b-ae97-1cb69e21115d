"""Module to fetch commits, pull requests, releases and tags from github and ingest into Databricks delta lake table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import asyncio
import logging
import time as thread_time
from datetime import date, datetime, time, timedelta, timezone
from typing import Any
from urllib.parse import parse_qs, urlparse

import httpx
import nest_asyncio
from constants import bronze_schemas
from constants.common import (
    API_ENDPOINT_MAP,
    APP_REPO_MAP,
    BRONZE_MERGE_CONDITION,
    BRONZE_TABLE_DESCRIPTION_MAP,
    DATABRICKS_TABLE_MAP,
    DATABRICKS_TEST_TABLE_MAP,
)
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, desc
from pyspark.sql.types import StructType
from pyspark.sql.utils import AnalysisException
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_rdd_secret, setup_databricks_logging
from rddlib.dbx_utils import DBX_APP_SCHEMA_MAP, get_dbx_env_catalog

logger = logging.getLogger(__name__)

BASE_URL = "https://api.github.com"
OWNER_NAME = "PACE-INT"
PER_PAGE = 100  # max value supported by github api
GITHUB_RATELIMIT_THRESHOLD = 100
GITHUB_HEADER_LIMIT_REMAINING = "X-RateLimit-Remaining"
GITHUB_HEADER_LIMIT_RESET = "X-RateLimit-Reset"
RETRY_ATTEMPTS = 3
RETRY_DELAY = 1
DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%SZ"


class GithubBronzeExtractor:
    """Class for Github bronze extraction.

    Ingest data for the specified application,
    resource (pr, tag, commit), for the specified date interval.
    """

    dbx_catalog: str
    dbx_schema: str
    table: str
    app: str
    resource: str
    test_mode: bool
    since: datetime | None
    until: datetime | None
    sha: str | None
    repo: str
    headers: dict[str, Any]
    spark: SparkSession

    @property
    def table_schema(self) -> StructType:
        """Returns the schema for the relevant resource type."""
        return bronze_schemas.BRONZE_SCHEMA_LOOKUP_TABLE[self.resource]

    def __init__(
        self,
        dbx_catalog: str,
        app: str,
        dbx_schema: str,
        resource: str,
        test_mode: bool,
        github_token: str,
        since: date | datetime | None = None,
        until: date | datetime | None = None,
        sha: str | None = None,
    ):
        """Initialize the GithubBronzeExtractor.

        Args:
            dbx_catalog (str): The name of the catalog.
            app (str): The application.
            dbx_schema(str): The name of the schema in the catalog.
            resource (str): The resource type (pr, tag, commit).
            test_mode (bool): Set to True for an end to end test run.
            github_token (str): Github authentication token.
            since (date | datetime | None): The start date of the interval in YYYY-MM-DD format.
            until (date | datetime | None): The end date of the interval in YYYY-MM-DD format.
            sha (str): The commit SHA to ingest.
        """
        # Cast dates to datetimes
        if isinstance(since, date):
            since = datetime.combine(since, time.min, timezone.utc)
        if isinstance(until, date):
            until = datetime.combine(until, time.max, timezone.utc)
        # Validate timerange
        if since is not None and until is not None:
            if until < since:
                raise Exception(f"Invalid timestamp range: since: {since}, until: {until}.")

        self.dbx_catalog = dbx_catalog
        self.dbx_schema = dbx_schema
        self.repo = APP_REPO_MAP[app]
        self.table = (DATABRICKS_TEST_TABLE_MAP if test_mode else DATABRICKS_TABLE_MAP)[resource]
        self.app = app
        self.resource = resource
        self.spark = SparkSession.builder.getOrCreate()

        if since is None:
            if resource == "commit":
                since = self.get_latest_commit_ts()
        else:
            since = since.astimezone(timezone.utc)
        self.since = since
        if until is None:
            until = datetime.now(timezone.utc)
        else:
            until = until.astimezone(timezone.utc)
        self.until = until
        self.sha = sha
        self.test_mode = test_mode

        logger.info(f"Using catalog: {self.dbx_catalog}, schema: {self.dbx_schema} & table: {self.table}")

        self.headers = {
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {github_token}",
            "X-GitHub-Api-Version": "2022-11-28",
        }

    def get_latest_commit_ts(self) -> datetime:
        """Returns the latest commit timestamp in bronze layer."""
        table_full = f"{self.dbx_catalog}.{self.dbx_schema}.`{self.table}`"
        df = self.spark.read.table(table_full)
        df_with_dates = df.select(col("commit").getField("committer").getField("date").alias("date")).sort(desc("date"))
        latest = df_with_dates.first()
        if latest is not None:
            logger.info(f"Latest bronze {self.resource}: {latest.date}")
            return datetime.strptime(latest.date, DATETIME_FORMAT)

        raise Exception("No data found")

    def _check_commit_exists(self) -> bool:
        """Check whether a commit exists in Bronze layer."""
        table_full_name = f"{self.dbx_catalog}.{self.dbx_schema}.{self.table}"
        try:
            df_bronze = self.spark.read.table(table_full_name)
            if df_bronze.filter(col("sha") == self.sha).count() > 0:  # type: ignore[operator]
                return True
        except AnalysisException as e:
            logger.error(f"Error accessing the table {self.table}: {str(e)}")

        return False

    def get_url(self) -> str:
        """Returns github rest api url for the relevant resource."""
        if self.sha:  # If SHA is provided, return the URL for the specific commit
            return f"{BASE_URL}/repos/{OWNER_NAME}/{self.repo}/commits/{self.sha}"

        endpoint = API_ENDPOINT_MAP[self.resource]
        return f"{BASE_URL}/repos/{OWNER_NAME}/{self.repo}/{endpoint}"

    def get_params(self, page: int | None = None) -> dict[str, Any]:
        """Constructs and returns query parameters for an API request."""

        params = {
            "per_page": PER_PAGE,
            # add since and until, if resource is "commit"
            **(
                {
                    "since": self.since.strftime(DATETIME_FORMAT),  # type: ignore[union-attr]
                    "until": self.until.strftime(DATETIME_FORMAT),  # type: ignore[union-attr]
                }
                if self.resource == "commit"
                else {}
            ),
            # add state, if resources is "pr"
            **({"state": "all"} if self.resource == "pr" else {}),
            # add page, if available
            **({"page": page} if page else {}),
        }

        return params

    async def fetch_paginated_data(self, page: int) -> tuple[httpx.Headers, list[dict[str, Any]]]:
        """Get paginated github data with retries."""
        url = self.get_url()
        params = self.get_params(page)

        async with httpx.AsyncClient(headers=self.headers) as client:
            for attempt in range(RETRY_ATTEMPTS):
                try:
                    response = await client.get(url, params=params)
                    if response.status_code == 200:
                        return response.headers, response.json()
                    else:
                        logger.error(f"Error fetching data for page {page}: {response.status_code}")
                        continue
                except (
                    httpx.ReadTimeout,
                    httpx.ConnectTimeout,
                    httpx.WriteTimeout,
                ) as e:
                    if attempt < RETRY_ATTEMPTS - 1:
                        await asyncio.sleep(RETRY_DELAY)
                    else:
                        logger.error(f"{type(e).__name__} occurred after maximum retry attempts.")
                        raise

        raise Exception(f"Could not fetch page {page}.")

    def get_page_count(self) -> int:
        """Get number of pages if api response is paginated."""
        url = self.get_url()
        params = self.get_params()

        with httpx.Client(headers=self.headers) as client:
            response = client.get(url, params=params)
            response.raise_for_status()
            try:
                query_params = parse_qs(urlparse(response.links["last"]["url"]).query)
            except (KeyError, ValueError):
                # No last page in response -> only one page available
                return 1
            return int(query_params.get("page", [1])[0])

    def quality_check_run(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Todo - investigate why this check fails only for github pr (hint: col labels is causing an exception)
        # Duplicate check
        # _, duplicate_id_rows = quality_check.deduplicate(
        #     df,
        #     subset=["node_id"],
        #     time_window=None,
        #     use_hashing=False,
        #     just_test=False
        # )
        # duplicates_existing = False if len(duplicate_id_rows) == 0 else True
        # CHANGE THIS IF RE-ENABLE
        # logger.quality_check(f"Duplicates existing: {duplicates_existing}")

        # Missing value check
        # missing_value_rows = quality_check.missing_value_detection(df, column="url")
        # missing_rows_count = missing_value_rows.count()
        # if missing_rows_count == 0:
        #     logger.quality_check(True, "missing_value_detection", "url")
        # else:
        #     logger.quality_check(False, "missing_value_detection", f"url: {missing_rows_count / df.count()}.")

    async def extract_data(self) -> list[dict[str, Any]]:
        """Get data from GitHub API."""
        if self.sha:
            return self._fetch_data_by_sha()
        return await self._fetch_paginated_by_date_range()

    def _fetch_data_by_sha(self) -> list[dict[str, Any]]:
        """Fetch data for a specific commit SHA."""
        url = self.get_url()
        with httpx.Client(headers=self.headers) as client:
            response = client.get(url)
            response.raise_for_status()
            logger.info(f"Data fetched for commit SHA: {self.sha}")
            return [response.json()]

    async def _fetch_paginated_by_date_range(self) -> list[dict[str, Any]]:
        """Fetch paginated data from GitHub API."""
        last_page = self.get_page_count()
        logger.info(f"Fetching {last_page} pages for {self.resource}")

        # Fetch pages in parallel in batches of 10 to avoid rate limiting
        batch_size = 10
        data: list[dict[str, Any]] = []
        remaining_limit = None  # Default to None if headers are missing
        reset_time = None

        for batch_start in range(1, last_page + 1, batch_size):
            batch_end = min(batch_start + batch_size - 1, last_page)
            logger.info(f"Fetching page {batch_start} to {batch_end}")
            tasks = [self.fetch_paginated_data(page) for page in range(batch_start, batch_end + 1)]
            results = await asyncio.gather(*tasks)

            # Update rate limit from response headers of the last result in the current batch
            for response_headers, result in results:
                data.extend(result)

                # Check for rate limit headers
                remaining_limit_header = response_headers.get(GITHUB_HEADER_LIMIT_REMAINING)
                reset_time_header = response_headers.get(GITHUB_HEADER_LIMIT_RESET)

                if remaining_limit_header is None:
                    remaining_limit = None
                    reset_time = None
                else:
                    # Convert headers to integers if they exist
                    remaining_limit = int(remaining_limit_header)
                    reset_time = int(reset_time_header)

            # Only check and sleep if rate limit headers are present and valid
            if remaining_limit is not None and remaining_limit <= GITHUB_RATELIMIT_THRESHOLD:
                logger.info(f"GitHub API rate limiting exceeded. Remaining: {remaining_limit}, reset at: {reset_time}")
                # Sleep only if reset_time is valid
                if reset_time is not None:
                    duration = max(reset_time - int(thread_time.time()), 0)
                    await asyncio.sleep(duration)

        logger.info(f"Total items fetched: {len(data)}")
        return data

    async def ingest_data(self) -> None:
        """Fetch data from github and load into databricks delta table."""
        if self.sha and self._check_commit_exists():
            logger.info(f"Commit {self.sha} already exists in bronze layer. Exiting...")
            return

        data = await self.extract_data()

        df = self.spark.createDataFrame(data, schema=self.table_schema)
        if self.resource == "pr":
            df = df.dropDuplicates(["number"])

        # Run the data quality check
        self.quality_check_run(df)

        table_full = f"{self.dbx_catalog}.{self.dbx_schema}.{self.table}"
        # Update delta table
        dtu.merge_or_overwrite(df, table_full, BRONZE_MERGE_CONDITION[self.resource])
        # Add metadata to the table
        description = BRONZE_TABLE_DESCRIPTION_MAP.get(self.table, "")
        dtu.update_table_metadata(table_full, description)


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Github Extractor")

    parser.add_argument(
        "-a",
        "--application",
        dest="app",
        default="release",
        help="Application, ex. release, rng",
    )
    parser.add_argument(
        "-r",
        "--resource",
        dest="resource",
        default="pr",
        # required=True,
        help="Github resource, ex. commit, pr, tag",
    )
    parser.add_argument(
        "-s",
        "--since",
        dest="since",
        type=lambda v: date.fromisoformat(v),
        required=False,
        default=None,
        help="Since date (YYYY-MM-DD)",
    )
    parser.add_argument(
        "-u",
        "--until",
        dest="until",
        type=lambda v: date.fromisoformat(v),
        required=False,
        default=None,
        help="Until date (YYYY-MM-DD)",
    )
    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )
    parser.add_argument("-i", "--run_id", dest="run_id")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )
    parser.add_argument(
        "--sha",
        dest="sha",
        required=False,
        default=None,
        help="""Specify the SHA of a specific commit to extract.
            If provided, only data for this commit will be processed.""",
    )

    args, unknown = parser.parse_known_args()
    app = args.app
    test_mode = args.test_mode

    dbx_catalog = get_dbx_env_catalog("bronze")
    dbx_schema = DBX_APP_SCHEMA_MAP[app]

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(dbx_catalog, dbx_schema, False), f"github_{args.resource}/bronze", run_id=args.run_id
    )
    github_token = get_rdd_secret("github-access-token")

    # Set since date to previous day if test_mode is True and resource is commit
    if test_mode and args.resource == "commit":
        args.since = date.today() - timedelta(days=1)
    extractor = GithubBronzeExtractor(
        dbx_catalog,
        app,
        dbx_schema,
        args.resource,
        test_mode,
        github_token,
        args.since,
        args.until,
        args.sha,
    )

    if args.sha:
        logger.info(
            f"Extracting github data for: app: {app}, resource: {args.resource}, "
            f"sha: {args.sha}, test_mode: {args.test_mode}"
        )
    else:
        logger.info(
            f"Extraction github data for: app: {app}, resource: {args.resource}, "
            f"since: {args.since}, until: {args.until}, test_mode: {args.test_mode}"
        )

    nest_asyncio.apply()
    data = asyncio.run(extractor.ingest_data())
