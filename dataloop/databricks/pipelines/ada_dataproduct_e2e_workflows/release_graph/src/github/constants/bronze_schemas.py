"""Bronze schemas for github resources."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import (
    ArrayType,
    BooleanType,
    IntegerType,
    LongType,
    MapType,
    StringType,
    StructField,
    StructType,
)

# Define the schema for the commit with nullable fields
COMMIT_ENTRY = StructType(
    [
        StructField("committer", StructType([StructField("date", StringType(), True)]), True),
        StructField("message", StringType(), True),
        StructField(
            "tree",
            StructType(
                [
                    StructField("sha", StringType(), True),
                    StructField("url", StringType(), True),
                ]
            ),
            True,
        ),
        StructField("url", StringType(), True),
        StructField("comment_count", IntegerType(), True),
        StructField(
            "verification",
            StructType(
                [
                    StructField("verified", StringType(), True),
                    StructField("reason", StringType(), True),
                    StructField("signature", StringType(), True),
                    StructField("payload", StringType(), True),
                ]
            ),
            True,
        ),
    ]
)

# Use the schema for the entire JSON object with nullable fields
COMMIT_RESPONSE = StructType(
    [
        StructField("sha", StringType(), True),
        StructField("node_id", StringType(), True),
        StructField("commit", COMMIT_ENTRY, True),
        StructField("url", StringType(), True),
        StructField("html_url", StringType(), True),
        StructField("comments_url", StringType(), True),
        StructField(
            "parents",
            ArrayType(
                StructType(
                    [
                        StructField("sha", StringType(), True),
                        StructField("url", StringType(), True),
                        StructField("html_url", StringType(), True),
                    ]
                ),
                True,
            ),
        ),
    ]
)


# Define the schema for the pr and base fields with nullable fields
PULL_REQUEST_ENTRY = StructType(
    [
        StructField("label", StringType(), True),
        StructField("ref", StringType(), True),
        StructField("sha", StringType(), True),
        StructField("repo", MapType(StringType(), StringType()), True),
    ]
)

# Define the schema for the pull request JSON object
PULL_REQUEST_REPSONSE = StructType(
    [
        StructField("url", StringType(), True),
        StructField("id", LongType(), True),
        StructField("node_id", StringType(), True),
        StructField("html_url", StringType(), True),
        StructField("diff_url", StringType(), True),
        StructField("patch_url", StringType(), True),
        StructField("issue_url", StringType(), True),
        StructField("number", LongType(), True),
        StructField("state", StringType(), True),
        StructField("locked", BooleanType(), True),
        StructField("title", StringType(), True),
        StructField("body", StringType(), True),
        StructField("created_at", StringType(), True),
        StructField("updated_at", StringType(), True),
        StructField("closed_at", StringType(), True),
        StructField("merged_at", StringType(), True),
        StructField("merge_commit_sha", StringType(), True),
        StructField("requested_teams", ArrayType(StringType()), True),
        StructField("labels", ArrayType(MapType(StringType(), StringType())), True),
        StructField("milestone", StringType(), True),
        StructField("draft", BooleanType(), True),
        StructField("commits_url", StringType(), True),
        StructField("review_comments_url", StringType(), True),
        StructField("review_comment_url", StringType(), True),
        StructField("comments_url", StringType(), True),
        StructField("statuses_url", StringType(), True),
        StructField("head", PULL_REQUEST_ENTRY, True),
        StructField("base", PULL_REQUEST_ENTRY, True),
        StructField("_links", MapType(StringType(), MapType(StringType(), StringType())), True),
        StructField("auto_merge", StringType(), True),
        StructField("active_lock_reason", StringType(), True),
    ]
)

RELEASE_RESPONSE = StructType(
    [
        StructField("url", StringType(), True),
        StructField("assets_url", StringType(), True),
        StructField("upload_url", StringType(), True),
        StructField("html_url", StringType(), True),
        StructField("id", IntegerType(), True),
        StructField("node_id", StringType(), True),
        StructField("tag_name", StringType(), True),
        StructField("target_commitish", StringType(), True),
        StructField("name", StringType(), True),
        StructField("draft", BooleanType(), True),
        StructField("prerelease", BooleanType(), True),
        StructField("created_at", StringType(), True),
        StructField("published_at", StringType(), True),
        StructField("tarball_url", StringType(), True),
        StructField("zipball_url", StringType(), True),
        StructField("body", StringType(), True),
        StructField("mentions_count", IntegerType(), True),
    ]
)

TAG_RESPONSE = StructType(
    [
        StructField("name", StringType(), True),
        StructField("zipball_url", StringType(), True),
        StructField("tarball_url", StringType(), True),
        StructField(
            "commit",  # Provide a name for this field
            StructType(
                [
                    StructField("sha", StringType(), True),
                    StructField("url", StringType(), True),
                ]
            ),
            True,
        ),
        StructField("node_id", StringType(), True),
    ]
)


BRONZE_SCHEMA_LOOKUP_TABLE = {
    "commit": COMMIT_RESPONSE,
    "pr": PULL_REQUEST_REPSONSE,
    "release": RELEASE_RESPONSE,
    "tag": TAG_RESPONSE,
}
