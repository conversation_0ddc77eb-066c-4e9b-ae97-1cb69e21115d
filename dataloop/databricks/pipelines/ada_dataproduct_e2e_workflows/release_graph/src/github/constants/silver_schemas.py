"""Silver schemas for github resources."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import (
    ArrayType,
    BooleanType,
    IntegerType,
    LongType,
    StringType,
    StructField,
    StructType,
    TimestampType,
)

# Define the schema for commit with nullable fields
COMMIT_SCHEMA = StructType(
    [
        StructField("commit_sha", StringType(), True),
        StructField("message", StringType(), True),
        StructField("comment_count", IntegerType(), True),
        StructField("url", StringType(), True),
        StructField("created_date", TimestampType(), True),
        StructField("html_url", StringType(), True),
        StructField("parent", StringType(), True),
    ]
)

# Define the schema for pr with nullable fields
PULL_REQUEST_SCHEMA = StructType(
    [
        StructField("url", StringType(), True),
        StructField("html_url", StringType(), True),
        StructField("state", StringType(), True),
        StructField("title", StringType(), True),
        StructField("locked", BooleanType(), True),
        StructField("id", LongType(), True),
        StructField("number", IntegerType(), True),
        StructField("created_date", TimestampType(), True),
        StructField("updated_date", TimestampType(), True),
        StructField("closed_date", TimestampType(), True),
        StructField("merged_date", TimestampType(), True),
        StructField("commit_sha", StringType(), True),
        StructField("labels", ArrayType(StringType()), True),
        StructField("milestone", StringType(), True),
        StructField("draft", BooleanType(), True),
        StructField("commits_url", StringType(), True),
    ]
)

# Define the schema for release with nullable fields
RELEASE_SCHEMA = StructType(
    [
        StructField("name", StringType(), True),
        StructField("tag_name", StringType(), True),
        StructField("target_commitish", StringType(), True),
        StructField("url", StringType(), True),
        StructField("tarball_url", StringType(), True),
        StructField("zipball_url", StringType(), True),
        StructField("node_id", StringType(), True),
        StructField("created_date", TimestampType(), True),
        StructField("id", IntegerType(), True),
    ]
)

# Define the schema for tag with nullable fields
TAG_SCHEMA = StructType(
    [
        StructField("name", StringType(), True),
        StructField("zipball_url", StringType(), True),
        StructField("tarball_url", StringType(), True),
        StructField("commit_sha", StringType(), True),
        StructField("node_id", StringType(), True),
    ]
)

SILVER_SCHEMA_LOOKUP_TABLE = {
    "commit": COMMIT_SCHEMA,
    "pr": PULL_REQUEST_SCHEMA,
    "release": RELEASE_SCHEMA,
    "tag": TAG_SCHEMA,
}
