"""Helper module to define github related constants."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

DATABRICKS_TABLE_MAP = {
    "commit": "github_commits",
    "pr": "github_pullrequests",
    "release": "github_releases",
    "tag": "github_tags",
}

DATABRICKS_TEST_TABLE_MAP = {
    "commit": "github_commits_test",
    "pr": "github_pullrequests_test",
    "release": "github_releases_test",
    "tag": "github_tags_test",
}

APP_REPO_MAP = {"release": "pace", "rng": "rdd", "ingest": "dataloop"}

API_ENDPOINT_MAP = {"commit": "commits", "pr": "pulls", "release": "releases", "tag": "tags"}

BRONZE_MERGE_CONDITION = {
    "commit": "target.sha = source.sha",
    "pr": "target.number = source.number",
    "release": "target.id = source.id",
    "tag": "target.name = source.name",
}

SILVER_MERGE_CONDITION = {
    "commit": "target.commit_sha = source.commit_sha and target.parent = source.parent",
    "pr": "target.number = source.number",
    "release": "target.id = source.id",
    "tag": "target.name = source.name",
}

BRONZE_TABLE_DESCRIPTION_MAP = {
    "github_commits": (
        "This table contains details of GitHub commits, including commit hash, author, " "date, and associated changes."
    ),
    "github_pullrequests": (
        "This table stores information about GitHub pull requests, such as PR number, "
        "title, status, and associated commits."
    ),
    "github_releases": (
        "This table holds details about GitHub releases, including release name, "
        "creation date, and associated commit or branch."
    ),
    "github_tags": ("This table holds details about GitHub tags, including tag name, commit, " "and other metadata."),
}

SILVER_TABLE_DESCRIPTION_MAP = {
    "github_commits": (
        "This table contains transformed GitHub commit data, including commit SHA, message, "
        "comment count, parent commits, and timestamps for commit creation."
    ),
    "github_pullrequests": (
        "This table stores transformed GitHub pull request data, including PR state, title, "
        "locked status, timestamps for creation, updates, and closure, as well as associated "
        "commit SHAs and labels."
    ),
    "github_releases": (
        "This table contains transformed GitHub release data, including release name, commit/branch, "
        "and URLs related to the tag, with creation timestamp and other related metadata."
    ),
    "github_tags": (
        "This table contains transformed GitHub tag data, including tag name, commit sha, "
        "and other related metadata."
    ),
}

GOLD_TABLE_DESCRIPTION_MAP = {
    "github_releases": (
        "This table contains aggregated GitHub release data, including release name, commit SHA, "
        "and URLs related to the tag, with creation timestamp and other related metadata."
    ),
}
