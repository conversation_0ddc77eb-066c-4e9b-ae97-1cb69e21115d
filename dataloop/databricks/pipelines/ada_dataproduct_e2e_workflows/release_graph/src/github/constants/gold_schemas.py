"""Gold schemas for github resources."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import IntegerType, StringType, StructField, StructType, TimestampType

RELEASE_SCHEMA = StructType(
    [
        StructField("name", StringType(), True),
        StructField("tag_name", StringType(), True),
        StructField("target_commitish", StringType(), True),
        StructField("url", StringType(), True),
        StructField("tarball_url", StringType(), True),
        StructField("zipball_url", StringType(), True),
        StructField("node_id", StringType(), True),
        StructField("created_date", TimestampType(), True),
        StructField("id", IntegerType(), True),
        StructField("commit_sha", StringType(), True),
    ]
)
