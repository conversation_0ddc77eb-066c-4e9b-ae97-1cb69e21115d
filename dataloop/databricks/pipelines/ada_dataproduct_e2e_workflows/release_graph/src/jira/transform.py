"""Transform Jira data and upload to databricks delta lake silver table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse

from constants import APP_NAME, DESCRIPTION_SILVER, REFRESH_INTERVAL, TABLE_NAME
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, explode_outer, regexp_replace, to_timestamp, translate
from rddlib import DBX_APP_SCHEMA_MAP, FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, quality_check, setup_databricks_logging


class JiraSilverTransformer:
    """JIRA silver data transformer class."""

    bronze_dbx_catalog: str
    dbx_schema: str
    silver_dbx_catalog: str
    spark: SparkSession
    test_mode: bool

    def __init__(
        self,
        bronze_dbx_catalog: str,
        dbx_schema: str,
        silver_dbx_catalog: str,
        spark: SparkSession,
        test_mode: bool = False,
    ) -> None:
        """Initializes the JiraSilverTransformer with specific parameters.

        Args:
            bronze_dbx_catalog (str): The name of the catalog where bronze data is stored.
            dbx_schema (str): The name of the schema within the catalog.
            silver_dbx_catalog (str): The name of the catalog where silver data will be stored.
            spark (SparkSession): Spark session.
            test_mode (bool): Set to True for an end to end test run.
        """
        self.bronze_dbx_catalog = bronze_dbx_catalog
        self.dbx_schema = dbx_schema
        self.silver_dbx_catalog = silver_dbx_catalog
        self.spark = spark
        self.test_mode = test_mode

    def quality_check_run(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        total_count = df.count()

        column_to_check = "id"

        missing_value_rows = quality_check.missing_value_detection(df, column=column_to_check)
        missing_values_count = missing_value_rows.count()
        if missing_values_count == 0:
            quality_check.log_quality_check_result(True, "missing_value_detection", None)
        else:
            quality_check.log_quality_check_result(False, "missing_value_detection", missing_values_count)

        # Duplicate check
        _, duplicate_id_rows = quality_check.deduplicate(
            df,
            subset=[column_to_check],
            time_window=None,
            use_hashing=False,
            just_test=False,
        )
        count_duplicates = len(duplicate_id_rows)
        if count_duplicates == 0:
            quality_check.log_quality_check_result(True, "deduplicate", None)
        else:
            quality_check.log_quality_check_result(False, "deduplicate", f"{count_duplicates / total_count}")

    def transform_data(self, bronze_table_full: str | None = None) -> None:
        """Transforms data from the JIRA bronze table to the JIRA silver table."""
        table = f"{TABLE_NAME}_test" if self.test_mode else TABLE_NAME
        if not bronze_table_full:
            bronze_table_full = f"{self.bronze_dbx_catalog}.{self.dbx_schema}.`{table}`"
        df_bronze = self.spark.read.table(bronze_table_full)

        df_silver = (
            df_bronze.withColumn("issuetype", regexp_replace(col("fields.issuetype.name"), " |-", ""))
            .withColumn("status", translate(col("fields.status.name"), " ()", ""))
            .withColumn("label", explode_outer(col("fields.labels")))
            .withColumn("component_name", explode_outer(col("fields.components.name")))
            .withColumn("fix_version_name", explode_outer(col("fields.fixVersions.name")))
            .select(
                col("id").alias("id"),
                col("self").alias("link"),
                col("key"),
                col("fields.summary").alias("summary"),
                col("fields.description").alias("description"),
                to_timestamp(col("fields.updated")).alias("updated_date"),
                to_timestamp(col("fields.created")).alias("created_date"),
                col("issuetype"),
                col("status"),
                col("label"),
                col("component_name"),
                col("fields.resolution.name").alias("resolution_name"),
                col("fix_version_name"),
                col("fields.customfield_10001.name").alias("team_name"),
                col("fields.customfield_10056.value").alias("safety_relevance"),
                col("fields.customfield_10102.value").alias("problem_type"),
                col("fields.customfield_10205.value").alias("severity"),
                col("fields.customfield_10263").alias("branch_name"),
            )
        )

        # Run the data quality check
        self.quality_check_run(df_silver)
        table_silver_full = f"{self.silver_dbx_catalog}.{self.dbx_schema}.`{table}`"
        dtu.overwrite(df_silver, table_silver_full)
        # Add metadata to the table
        dtu.update_table_metadata(table_silver_full, DESCRIPTION_SILVER, refresh_interval=REFRESH_INTERVAL)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Transform JIRA issues")
    parser.add_argument("-e", "--env", default="dev", help="Environment, ex. dev, qa, prod")
    parser.add_argument("-r", "--run_id", dest="run_id")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    args, unknown = parser.parse_known_args()
    run_id = args.run_id
    test_mode = args.test_mode

    dbx_catalog_bronze = get_dbx_env_catalog("bronze")
    dbx_catalog_silver = get_dbx_env_catalog("silver")
    dbx_schema = DBX_APP_SCHEMA_MAP[APP_NAME]

    # Setup logging
    setup_databricks_logging(FullSchemaName(dbx_catalog_silver, dbx_schema, False), "jira/silver", run_id=run_id)
    spark = SparkSession.builder.getOrCreate()

    transformer = JiraSilverTransformer(dbx_catalog_bronze, dbx_schema, dbx_catalog_silver, spark, test_mode)

    transformer.transform_data()
