"""E2E tests for the Jira module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
from datetime import datetime, timedelta

import stardog
from rddlib import get_rdd_secret, get_stardog_access_token
from requests.exceptions import ConnectTimeout, MissingSchema
from stardog.exceptions import StardogException, TransactionException

NAMESPACE = "urn:pace:ontology:jira:"


def get_formatted_date():
    """Returns the date from three days ago in the format 'YYYY-MM-DDTHH:MM:SS.ssssss±HHMM'."""
    three_days_prior = datetime.now() - timedelta(days=3)
    formatted_time = three_days_prior.strftime("%Y-%m-%dT%H:%M:%S.%f%z")
    return formatted_time


def test_jira_data_in_stardog(db_name: str) -> None:
    """Run SPARQL query to test presence of specific data in Stardog."""

    conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }

    with stardog.Admin(**conn_details) as admin:
        if not any(db.name == db_name for db in admin.databases()):
            return  # exit without error

    test_query = f"""
        SELECT ?issue ?date
        WHERE {{
            ?issue a jira:Story ;
                jira:hasUpdatedDate ?date .
            FILTER (?date > "{get_formatted_date()}")
        }}
        ORDER BY DESC(?date)
    """

    with stardog.Connection(db_name, **conn_details) as conn:
        try:
            conn.begin()
            response = conn.select(test_query)
            bindings = response["results"]["bindings"]
            issues = [b["issue"]["value"] for b in bindings]
            assert issues, "Issues list is empty"

            test_query_2 = f"""
                SELECT ?p ?o
                WHERE {{
                    <{issues[0]}> ?p ?o ;
                }}
            """

            response = conn.select(test_query_2)
            bindings = response["results"]["bindings"]
            edges = [b["p"]["value"] for b in bindings]

            expected_edges = [
                f"{NAMESPACE}hasId",
                f"{NAMESPACE}hasLink",
                f"{NAMESPACE}hasKey",
                f"{NAMESPACE}hasStatus",
                f"{NAMESPACE}hasUpdatedDate",
                f"{NAMESPACE}hasCreatedDate",
                f"{NAMESPACE}hasIssueType",
            ]

            for expected_edge in expected_edges:
                assert expected_edge in edges, f"{expected_edge} not found"

        except (TransactionException, MissingSchema, ConnectTimeout, StardogException) as e:
            assert False


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="E2E Test for the Jira Pipeline")

    parser.add_argument("-d", "--db", dest="db", help="Stardog database")

    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()
    db = args.db

    test_jira_data_in_stardog(db)
