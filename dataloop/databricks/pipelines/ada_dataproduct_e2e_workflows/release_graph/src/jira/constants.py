"""Configurations for JIRA extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import ArrayType, BooleanType, LongType, StringType, StructField, StructType

JIRA_BASE_URL = "https://pace-project.atlassian.net/rest/api/2"
TABLE_NAME = "jira"
APP_NAME = "release"
DATEFORMAT = "%Y-%m-%d"
REFRESH_INTERVAL = "PT3H"
DESCRIPTION_BRONZE = (
    "This table stores raw JIRA issue data extracted for the specified"
    " project within a given date range using the JIRA SEARCH API."
    " It includes details such as issue ID, project, issue type, summary,"
    " status, and priority, along with other relevant metadata from the JIRA issue fields."
    " The ingestion and maintenance of this table are handled by the RDD Team."
)
DESCRIPTION_SILVER = (
    "This table contains transformed JIRA issue data, including key details such as issue ID,"
    " key, summary, description, status, and timestamps for creation and updates."
    " It also includes flattened fields for labels, components, fix versions, and custom fields like"
    " team name, safety relevance, problem type, and severity."
    " The ingestion and maintenance of this table are handled by the RDD Team."
)

# Payload for JIRA search API.
SEARCH_PAYLOAD = {
    "expand": ["names", "schema"],
    "fields": [
        "summary",
        "description",
        "status",
        "assignee",
        "updated",
        "created",
        "labels",
        "issuetype",
        "components",
        "resolution",
        "fixVersions",
        "customfield_10001",
        "customfield_10056",
        "customfield_10102",
        "customfield_10205",
        "customfield_10263",
    ],
    "fieldsByKeys": False,
    "jql": "project = {project} AND updated >= '{since}' AND updated <= '{until}'",
    "maxResults": 100,
}
JIRA_ISSUE_SCHEMA = StructType(
    [
        StructField("expand", StringType(), True),
        StructField("id", StringType(), True),
        StructField("self", StringType(), True),
        StructField("key", StringType(), True),
        StructField(
            "fields",
            StructType(
                [
                    StructField("summary", StringType(), True),
                    StructField(
                        "issuetype",
                        StructType(
                            [
                                StructField("self", StringType(), True),
                                StructField("id", StringType(), True),
                                StructField("description", StringType(), True),
                                StructField("iconUrl", StringType(), True),
                                StructField("name", StringType(), True),
                                StructField("subtask", BooleanType(), True),
                                StructField("avatarId", LongType(), True),
                                StructField("hierarchyLevel", LongType(), True),
                            ]
                        ),
                        True,
                    ),
                    StructField(
                        "components",
                        ArrayType(
                            StructType(
                                [
                                    StructField("self", StringType(), True),
                                    StructField("id", StringType(), True),
                                    StructField("name", StringType(), True),
                                ]
                            )
                        ),
                        True,
                    ),
                    StructField("created", StringType(), True),
                    StructField("description", StringType(), True),
                    StructField("customfield_10263", StringType(), True),
                    StructField(
                        "fixVersions",
                        ArrayType(
                            StructType(
                                [
                                    StructField("self", StringType(), True),
                                    StructField("id", StringType(), True),
                                    StructField("description", StringType(), True),
                                    StructField("name", StringType(), True),
                                    StructField("archived", BooleanType(), True),
                                    StructField("released", BooleanType(), True),
                                ]
                            )
                        ),
                        True,
                    ),
                    StructField(
                        "customfield_10056",
                        StructType(
                            [
                                StructField("self", StringType(), True),
                                StructField("id", StringType(), True),
                                StructField("value", StringType(), True),
                            ]
                        ),
                        True,
                    ),
                    StructField(
                        "customfield_10001",
                        StructType(
                            [
                                StructField("id", StringType(), True),
                                StructField("name", StringType(), True),
                                StructField("avatarUrl", StringType(), True),
                                StructField("isVisible", BooleanType(), True),
                                StructField("title", StringType(), True),
                                StructField("isShared", BooleanType(), True),
                            ]
                        ),
                        True,
                    ),
                    StructField(
                        "resolution",
                        StructType(
                            [
                                StructField("self", StringType(), True),
                                StructField("id", StringType(), True),
                                StructField("description", StringType(), True),
                                StructField("name", StringType(), True),
                            ]
                        ),
                        True,
                    ),
                    StructField(
                        "customfield_10102",
                        StructType(
                            [
                                StructField("self", StringType(), True),
                                StructField("id", StringType(), True),
                                StructField("value", StringType(), True),
                            ]
                        ),
                        True,
                    ),
                    StructField("labels", ArrayType(StringType()), True),
                    StructField(
                        "customfield_10205",
                        StructType(
                            [
                                StructField("self", StringType(), True),
                                StructField("id", StringType(), True),
                                StructField("value", StringType(), True),
                            ]
                        ),
                        True,
                    ),
                    StructField("updated", StringType(), True),
                    StructField(
                        "status",
                        StructType(
                            [
                                StructField("self", StringType(), True),
                                StructField("description", StringType(), True),
                                StructField("iconUrl", StringType(), True),
                                StructField("name", StringType(), True),
                                StructField("id", StringType(), True),
                                StructField(
                                    "statusCategory",
                                    StructType(
                                        [
                                            StructField("self", StringType(), True),
                                            StructField("id", LongType(), True),
                                            StructField("key", StringType(), True),
                                            StructField("colorName", StringType(), True),
                                            StructField("name", StringType(), True),
                                        ]
                                    ),
                                    True,
                                ),
                            ]
                        ),
                        True,
                    ),
                ]
            ),
            True,
        ),
    ]
)
