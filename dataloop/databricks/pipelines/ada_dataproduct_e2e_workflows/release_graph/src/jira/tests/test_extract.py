"""Unit tests for Jira Bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from asyncio import TimeoutError
from datetime import date, datetime, time
from unittest.mock import AsyncMock, MagicMock, patch
from zoneinfo import ZoneInfo

import pytest
from extract import JiraBronzeExtractor
from pyspark.sql.utils import AnalysisException
from rddlib import delta_table_utils as dtu
from rddlib.utils import full_qualname

CET = ZoneInfo("Europe/Berlin")


@pytest.fixture
def extractor():
    """Fixture to provide a JiraBronzeExtractor instance for testing.

    Returns:
        JiraBronzeExtractor: An instance of JiraBronzeExtractor configured for testing.
    """
    with patch("extract.SparkSession", MagicMock()):
        return JiraBronzeExtractor(
            "catalog_name", "schema_name", "table_name", "AOSP", None, None, date(2024, 1, 1), date(2024, 1, 31)
        )


def test_initialization(extractor: JiraBronzeExtractor):
    """Test the initialization of the JiraBronzeExtractor."""
    assert extractor.dbx_catalog == "catalog_name"
    assert extractor.dbx_schema == "schema_name"
    assert extractor.table == "table_name"
    assert extractor.project == "AOSP"
    assert extractor.since == datetime.combine(date(2024, 1, 1), time.min, CET)
    assert extractor.until == datetime.combine(date(2024, 1, 31), time.max, CET)


def test_extractor_invalid_date():
    """Test querying JIRA with invalid."""
    with pytest.raises(ValueError):
        JiraBronzeExtractor(
            "catalog_name", "schema_name", "table_name", "AOSP", None, None, date(2028, 1, 1), date(1999, 1, 1)
        )


@pytest.mark.asyncio
async def test_query_jira_success(extractor: JiraBronzeExtractor):
    """Test querying JIRA successfully retrieves issues."""
    extractor.fetch_issue_page = AsyncMock(return_value=([{"id": "1", "fields": {}}, {"id": "2", "fields": {}}], 2))
    data = await extractor.query_jira()
    assert len(data) == 2


@pytest.mark.asyncio
async def test_query_jira_connection_error(extractor: JiraBronzeExtractor):
    """Test handling connection errors when querying JIRA."""
    extractor.fetch_issue_page = AsyncMock(side_effect=TimeoutError("Connection timed out"))

    with pytest.raises(TimeoutError):
        await extractor.query_jira()


@pytest.mark.asyncio
async def test_fetch_issue_page(extractor: JiraBronzeExtractor):
    """Test fetch issue page response."""
    session = AsyncMock()
    response = AsyncMock()
    response.json = MagicMock(
        return_value={"issues": [{"id": "1", "fields": {"updated": "2024-01-01T00:00:00Z"}}], "total": 1}
    )
    session.post.return_value = response

    with patch("rddlib.get_rdd_secret", MagicMock()):
        issues, total = await extractor.fetch_issue_page(session, "AOSP", 0)

    assert total == 1
    assert issues[0]["id"] == "1"


@pytest.mark.asyncio
async def test_store_in_databricks(extractor: JiraBronzeExtractor):
    """Test storing data in Databricks."""
    mock_df = MagicMock(name="df")
    mock_spark = MagicMock(name="spark")
    mock_spark.builder.getOrCreate.return_value = mock_spark
    mock_spark.createDataFrame.return_value = mock_df

    with (
        patch("rddlib.delta_table_utils.merge_or_overwrite") as merge_or_overwrite,
        patch("rddlib.delta_table_utils.update_table_metadata"),
    ):
        data = [{"id": "1", "fields": {"updated": "2024-01-01T00:00:00Z"}}]
        extractor.store_in_databricks(data)

        merge_or_overwrite.assert_called()


@pytest.mark.asyncio
async def test_store_in_databricks_analysis_exception():
    """Test handling AnalysisException in store_in_databricks."""
    mock_spark = MagicMock(name="spark")
    mock_spark.builder.getOrCreate.return_value = mock_spark
    mock_spark.createDataFrame.side_effect = AnalysisException("Schema mismatch")

    data = [{"id": "1", "fields": {"updated": "2024-01-01T00:00:00Z"}}]

    with pytest.raises(AnalysisException):
        with patch("extract.SparkSession", mock_spark):
            extractor = JiraBronzeExtractor(
                "catalog_name",
                "schema_name",
                "table_name",
                "AOSP",
                None,
                None,
                date(2024, 1, 1),
                date(2024, 1, 31),
            )
        extractor.store_in_databricks(data)
