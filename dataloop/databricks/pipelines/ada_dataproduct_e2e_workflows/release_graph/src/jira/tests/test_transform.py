"""Unit tests for JiraSilverTransformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import MagicMock, patch

import pytest
from pyspark.sql import SparkSession
from pyspark.sql.types import ArrayType, StringType, StructField, StructType
from pyspark.sql.utils import AnalysisException
from rddlib import delta_table_utils as dtu
from rddlib.utils import full_qualname
from transform import JiraSilverTransformer

# Mock Data
mock_bronze_jira_data = [
    {
        "id": "1",
        "self": "https://jira.example.com/rest/api/2/issue/1",
        "key": "PROJ-1",
        "fields": {
            "summary": "Test issue",
            "description": "This is a test issue.",
            "created": "2024-04-05T13:45:04.872+0200",
            "issuetype": {"name": "Bug"},
            "status": {"name": "Open"},
            "labels": ["label1", "label2"],
            "components": [{"name": "Component1"}],
            "resolution": {"name": "Unresolved"},
            "fixVersions": [{"name": "v1.0"}],
            "customfield_10001": {"name": "Team A"},
            "customfield_10056": {"value": "Yes"},
            "customfield_10102": {"value": "Type A"},
            "customfield_10205": {"value": "High"},
            "updated": "2024-04-05T13:48:49.268+0200",
            "customfield_10263": "Branch1",
        },
    },
    {
        "id": "2",
        "self": "https://jira.example.com/rest/api/2/issue/2",
        "key": "PROJ-2",
        "fields": {
            "summary": "Another test issue",
            "description": "This is another test issue.",
            "issuetype": {"name": "Task"},
            "created": "2024-04-06T13:45:04.872+0200",
            "status": {"name": "In Progress"},
            "labels": ["label3"],
            "components": [{"name": "Component2"}],
            "resolution": {"name": "Resolved"},
            "fixVersions": [{"name": "v1.1"}],
            "customfield_10001": {"name": "Team B"},
            "customfield_10056": {"value": "No"},
            "customfield_10102": {"value": "Type B"},
            "customfield_10205": {"value": "Medium"},
            "updated": "2024-04-07T13:48:49.268+0200",
            "customfield_10263": "Branch2",
        },
    },
]

# Clean jira data
cleaned_df = [
    {
        "id": "1",
        "link": "https://jira.example.com/rest/api/2/issue/1",
        "key": "PROJ-1",
        "summary": "Test issue",
        "description": "This is a test issue.",
        "created_date": "2024-04-05T13:45:04",
        "updated_date": "2024-04-05T13:48:49",
        "issuetype": "Bug",
        "status": "Open",
        "label": "label1",
        "component_name": "Component1",
        "resolution_name": "Unresolved",
        "fix_version_name": "v1.0",
        "team_name": "Team A",
        "safety_relevance": "Yes",
        "problem_type": "Type A",
        "severity": "High",
        "branch_name": "Branch1",
    },
    {
        "id": "2",
        "link": "https://jira.example.com/rest/api/2/issue/2",
        "key": "PROJ-2",
        "summary": "Another test issue",
        "description": "This is another test issue.",
        "created_date": "2024-04-06T13:45:04",
        "updated_date": "2024-04-07T13:48:49",
        "issuetype": "Task",
        "status": "InProgress",
        "label": "label3",
        "component_name": "Component2",
        "resolution_name": "Resolved",
        "fix_version_name": "v1.1",
        "team_name": "Team B",
        "safety_relevance": "No",
        "problem_type": "Type B",
        "severity": "Medium",
        "branch_name": "Branch2",
    },
]

# Define the schema
schema = StructType(
    [
        StructField("id", StringType(), True),
        StructField("self", StringType(), True),
        StructField("key", StringType(), True),
        StructField(
            "fields",
            StructType(
                [
                    StructField("summary", StringType(), True),
                    StructField("description", StringType(), True),
                    StructField("issuetype", StructType([StructField("name", StringType(), True)]), True),
                    StructField("status", StructType([StructField("name", StringType(), True)]), True),
                    StructField("labels", ArrayType(StringType()), True),
                    StructField("components", ArrayType(StructType([StructField("name", StringType(), True)])), True),
                    StructField("created", StringType(), True),
                    StructField("resolution", StructType([StructField("name", StringType(), True)]), True),
                    StructField("fixVersions", ArrayType(StructType([StructField("name", StringType(), True)])), True),
                    StructField("customfield_10001", StructType([StructField("name", StringType(), True)]), True),
                    StructField("customfield_10056", StructType([StructField("value", StringType(), True)]), True),
                    StructField("customfield_10102", StructType([StructField("value", StringType(), True)]), True),
                    StructField("customfield_10205", StructType([StructField("value", StringType(), True)]), True),
                    StructField("updated", StringType(), True),
                    StructField("customfield_10263", StringType(), True),
                ]
            ),
            True,
        ),
    ]
)


@pytest.fixture(scope="session")
def spark():
    """Fixture to provide a Spark session object for unit testing."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for Jira Transformer").getOrCreate()


@pytest.fixture
def transformer(spark):
    """Fixture to provide a JiraSilverTransformer instance."""
    return JiraSilverTransformer("dev_bronze", "release", "dev_silver", spark)


@pytest.fixture(scope="function")
def setup_temp_view(spark):
    """Fixture to set up a temporary view for testing."""
    df = spark.createDataFrame(mock_bronze_jira_data, schema)
    temp_view_name = "temp_bronze_table"
    df.createOrReplaceTempView(temp_view_name)
    yield "temp_bronze_table"


def test_transformer_initialization(spark):
    """Test to check proper initialization of JiraSilverTransformer."""
    transformer = JiraSilverTransformer("dev_bronze", "release", "dev_silver", spark)
    assert transformer.bronze_dbx_catalog == "dev_bronze"
    assert transformer.dbx_schema == "release"
    assert transformer.silver_dbx_catalog == "dev_silver"
    assert transformer.spark == spark


def test_transformed_data(transformer):
    """Test the transformation logic within transform_data method."""

    transformed_df = transformer.spark.createDataFrame(cleaned_df)
    transformer.transform_data = MagicMock(return_value=transformed_df)
    result_df = transformer.transform_data()

    assert result_df.count() == 2, "The number of cleaned issues should match the input data."

    first_row = result_df.collect()[0]
    assert first_row["id"] == cleaned_df[0]["id"]
    assert first_row["key"] == cleaned_df[0]["key"]
    assert first_row["summary"] == cleaned_df[0]["summary"]
    assert first_row["issuetype"] == cleaned_df[0]["issuetype"]
    assert first_row["status"] == cleaned_df[0]["status"]
    assert first_row["description"] == cleaned_df[0]["description"]
    assert first_row["created_date"] == cleaned_df[0]["created_date"]
    assert first_row["updated_date"] == cleaned_df[0]["updated_date"]


def test_transform_data(
    transformer,
    setup_temp_view,
):
    """Test transform_data method."""
    temp_view_name = setup_temp_view
    with (
        patch("rddlib.delta_table_utils.overwrite") as mock_overwrite,
        patch("rddlib.delta_table_utils.update_table_metadata"),
    ):
        transformer.transform_data(bronze_table_full=temp_view_name)

        assert mock_overwrite.call_count == 1


def test_transform_data_failure(setup_temp_view, spark, transformer):
    """Test transform_data method with failure."""
    with patch.object(spark.read, "table", side_effect=AnalysisException("Failed to read data")):
        with pytest.raises(AnalysisException):
            transformer.transform_data()
