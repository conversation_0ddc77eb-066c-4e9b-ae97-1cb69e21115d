resources:
  jobs:
    jira_etl_release:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "ADA Release Graph - Jira - E2E - Nightly"
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "E2E"
        schedule: "Nightly"
        "ADA Release Graph": ""
        "Jira": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      # Schedule: Runs every 3 hours starting at 22:00 UTC (which corresponds to 00:00 CEST).
      # This job executes 8 times a day to accommodate Jira data updates.
      schedule:
        quartz_cron_expression: ${var.nightly_schedule}
        timezone_id: UTC
        pause_status: ${var.nightly_trigger}

      tasks:
        - task_key: jira_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --project
              - AOSP
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_jira_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: jira_transform
          depends_on:
            - task_key: jira_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_jira_job_cluster

        - task_key: jira_delete_edges
          depends_on:
            - task_key: jira_transform
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "stardog",
                "delete-edges",
                "--database",
                "ada_release_v1",
                "--source",
                "jira",
              ]
          job_cluster_key: rdd_jira_job_cluster

        - task_key: jira_load
          depends_on:
            - task_key: jira_delete_edges
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_release_v1
              - --mapping_file
              - jira_map.j2
          job_cluster_key: rdd_jira_job_cluster

        - task_key: jira_delete_edges_v2
          depends_on:
            - task_key: jira_transform
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "stardog",
                "delete-edges",
                "--database",
                "ada_release_v2",
                "--source",
                "jira",
              ]
          job_cluster_key: rdd_jira_job_cluster

        - task_key: jira_load_v2
          depends_on:
            - task_key: jira_delete_edges_v2
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_release_v2
              - --mapping_file
              - jira_map.j2
          job_cluster_key: rdd_jira_job_cluster

      job_clusters:
        - job_cluster_key: rdd_jira_job_cluster
          new_cluster:
            data_security_mode: SINGLE_USER
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.driver_instance_pool_id}
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            custom_tags:
              "ResourceClass": "SingleNode"
