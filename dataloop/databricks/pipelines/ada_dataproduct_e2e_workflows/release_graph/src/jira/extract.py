"""Query Jira to extract and ingest data into Databricks delta lake bronze table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import asyncio
import logging
from datetime import date, datetime, time, timedelta
from typing import Any
from zoneinfo import ZoneInfo

import nest_asyncio
from constants import (
    APP_NAME,
    DESCRIPTION_BRONZE,
    JIRA_BASE_URL,
    JIRA_ISSUE_SCHEMA,
    REFRESH_INTERVAL,
    SEARCH_PAYLOAD,
    TABLE_NAME,
)
from httpx import AsyncClient, Response
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, desc
from rddlib import DBX_APP_SCHEMA_MAP, FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, get_rdd_secret, quality_check, setup_databricks_logging

logger = logging.getLogger(__name__)

DATETIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%f%z"
QUERY_DATETIME_FORMAT = "%Y-%m-%d %H:%M"
CET = ZoneInfo("Europe/Berlin")


class JiraBronzeExtractor:
    """JIRA bronze data extractor class."""

    dbx_catalog: str
    dbx_schema: str
    table: str
    project: str
    jira_user: str
    jira_token: str
    since: datetime | None
    until: datetime | None

    def __init__(
        self,
        dbx_catalog: str,
        dbx_schema: str,
        table: str,
        project: str,
        jira_user: str,
        jira_token: str,
        since: date | datetime | None,
        until: date | datetime | None,
    ) -> None:
        """Initializes the JiraBronzeExtractor with specific parameters for data extraction and storage.

        Args:
            dbx_catalog (str): The name of the catalog. Ex. rdd_dev
            dbx_schema (str): The name of the schema within the catalog. Ex. bronze_jira
            table (str): The name of the table within the schema. Ex. jira
            project (str): The name of the project. Ex. AOSP
            jira_user (str): Jira username
            jira_token (str): Jira authentication token
            since (date | None): The start date from when to begin data extraction (YYYY-MM-DD).
            until (date | None): The end date until when data extraction is to be performed (YYYY-MM-DD).
        """
        # Cast dates to datetimes
        if isinstance(since, date):
            since = datetime.combine(since, time.min, tzinfo=CET)
        if isinstance(until, date):
            until = datetime.combine(until, time.max, tzinfo=CET)

        # handle incorrect date format, or "until" coming before "since"
        if since is not None and until is not None:
            if until < since:
                raise ValueError(f"Invalid date range: since: {since}, until: {until}")

        self.dbx_catalog = dbx_catalog
        self.dbx_schema = dbx_schema
        self.table = table
        self.spark = SparkSession.builder.getOrCreate()
        self.project = project
        self.jira_user = jira_user
        self.jira_token = jira_token

        # Get the current UTC time
        utc_now = datetime.now(ZoneInfo("UTC"))

        if since is None:
            since = self.get_latest_changed_ts()
        self.since = since
        if until is None:
            # Convert current UTC time to CET
            until = utc_now.astimezone(CET)
        self.until = until

    async def query_jira(self) -> list[dict[str, Any]]:
        """Gets paginated results from JIRA for issues based on initialized project and date range."""
        start_at = 0
        all_results = []

        logger.info(f"Querying JIRA from {self.since} to {self.until}")

        async with AsyncClient() as session:
            while True:
                issue_page, total_issues = await self.fetch_issue_page(session, self.project, start_at)
                all_results.extend(issue_page)

                start_at += SEARCH_PAYLOAD["maxResults"]
                if start_at >= total_issues:
                    break

        return all_results

    async def fetch_issue_page(
        self, session: AsyncClient, project: str, start_at: int
    ) -> tuple[list[dict[str, Any]], int]:
        """Fetches page of JIRA issues based on the provided parameters."""
        formatted_payload = self.format_search_payload(project, start_at)
        response = await self.send_search_request(session, formatted_payload)
        response_json = response.json()
        logger.info(f"Jira API search response is:{response_json}")
        return response_json.get("issues", []), response_json.get("total", 0)

    def format_search_payload(self, project: str, start_at: int) -> dict[str, Any]:
        """Formats the JIRA search payload with project, date range, and pagination start index."""
        payload = SEARCH_PAYLOAD.copy()
        payload["jql"] = payload["jql"].format(  # type: ignore[attr-defined]
            project=project,
            since=self.since.strftime(QUERY_DATETIME_FORMAT),
            until=self.until.strftime(QUERY_DATETIME_FORMAT),
        )
        payload["startAt"] = start_at
        logger.info(f"Formatted search payload for project={project}: " f"JQL={payload['jql']}, startAt={start_at}")
        return payload

    async def send_search_request(self, session: AsyncClient, payload: dict[str, Any]) -> Response:
        """Sends an asynchronous search request to JIRA with the given payload."""
        response = await session.post(f"{JIRA_BASE_URL}/search", json=payload, auth=(self.jira_user, self.jira_token))
        response.raise_for_status()
        return response

    def quality_check_run(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        full_count_rows = df.count()

        # Missing value checks
        # 1. Iterate over each column and check for missing values
        col_names = df.schema.names
        count_of_missing_values = 0
        for col_name in col_names:
            missing_value_rows = quality_check.missing_value_detection(df, column=col_name)
            count_of_missing_values = missing_value_rows.count()
            if count_of_missing_values == 0:
                quality_check.log_quality_check_result(True, "missing_value_detection", col_name)
            else:
                quality_check.log_quality_check_result(
                    False, "missing_value_detection", f"{col_name}: {count_of_missing_values / full_count_rows}."
                )
        # 2. Check missing description in the field column
        null_description_rows = df.filter("fields.description IS Null")
        count_of_missing_descriptions = null_description_rows.count()
        if count_of_missing_descriptions == 0:
            quality_check.log_quality_check_result(True, "missing_description", None)
        else:
            quality_check.log_quality_check_result(
                False, "missing_description", f"{count_of_missing_values / full_count_rows}"
            )

    def store_in_databricks(self, data: list[Any]) -> None:
        """Stores the retrieved JIRA issue data into a Databricks delta table."""
        df = self.spark.createDataFrame(data, schema=JIRA_ISSUE_SCHEMA)

        # Run the data quality check
        self.quality_check_run(df)

        table_full = f"{self.dbx_catalog}.{self.dbx_schema}.`{self.table}`"
        dtu.merge_or_overwrite(df, table_full, "target.id = source.id")
        # Add metadata to the table
        dtu.update_table_metadata(table_full, DESCRIPTION_BRONZE, refresh_interval=REFRESH_INTERVAL)

    def get_latest_changed_ts(self) -> datetime:
        """Returns the latest Jira item date in the bronze layer based on fields.updated."""
        df = self.spark.read.table(f"{self.dbx_catalog}.{self.dbx_schema}.`{self.table}`")

        df_with_dates = df.select(col("fields").getField("updated").alias("date")).sort(desc("date"))

        latest_item = df_with_dates.first()
        if latest_item is None:
            raise Exception("No jira issues found in the bronze layer")

        latest_ts = datetime.strptime(latest_item.date, DATETIME_FORMAT)
        logger.info(f"Latest Jira issues in bronze layer based on updated date: {latest_ts}")
        return latest_ts


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Jira Extractor")

    parser.add_argument(
        "-p",
        "--project",
        dest="project",
        required=False,
        default="AOSP",
        help="Enter project. Example: AOSP",
    )

    parser.add_argument(
        "-s",
        "--since",
        dest="since",
        type=lambda v: date.fromisoformat(v),
        required=False,
        default=None,
        help="Since date (YYYY-MM-DD)",
    )

    parser.add_argument(
        "-u",
        "--until",
        dest="until",
        type=lambda v: date.fromisoformat(v),
        required=False,
        default=None,
        help="Until date (YYYY-MM-DD)",
    )

    parser.add_argument(
        "-r",
        "--run_id",
        dest="run_id",
        required=False,
        default=None,
        help="Optional run ID for logging",
    )

    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )

    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    args, unknown = parser.parse_known_args()
    run_id = args.run_id
    test_mode = args.test_mode

    dbx_catalog = get_dbx_env_catalog("bronze")
    dbx_schema = DBX_APP_SCHEMA_MAP[APP_NAME]

    since_date = args.since
    # Set since date to 3 days ago if test_mode is true and since is not provided
    if test_mode and not since_date:
        since_date = date.today() - timedelta(days=3)

    table = f"{TABLE_NAME}_test" if test_mode else TABLE_NAME

    # Setup logging
    setup_databricks_logging(FullSchemaName(dbx_catalog, dbx_schema, False), "jira/bronze", run_id=run_id)

    jira_user = get_rdd_secret("pace-jira-technical-user-username")
    jira_token = get_rdd_secret("pace-jira-technical-user-token")

    extractor = JiraBronzeExtractor(
        dbx_catalog,
        dbx_schema,
        table,
        args.project,
        jira_user,
        jira_token,
        since=since_date,
        until=args.until,
    )

    nest_asyncio.apply()
    data = asyncio.run(extractor.query_jira())
    if data:
        extractor.store_in_databricks(data)
    else:
        logger.error("No data retrieved from JIRA.")
