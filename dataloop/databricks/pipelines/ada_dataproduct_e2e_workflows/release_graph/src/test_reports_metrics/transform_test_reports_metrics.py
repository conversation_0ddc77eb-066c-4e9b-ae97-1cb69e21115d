"""Module to transform unit test, coverage, and E2E test reports and upload them to Databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import json
import logging
import time
from datetime import datetime
from pathlib import Path
from typing import Any, ClassVar

import requests
from constants import (
    DATABRICKS_TABLE_MAP,
    DATE_TIME_FORMAT,
    RESOURCE_E2E_TEST_RESULTS,
    RESOURCE_TEST_COVERAGE,
    RESOURCE_UNIT_TEST_RESULTS,
    SCHEMA_NAME,
    TABLE_DESCRIPTION_MAP,
    TEST_COVERAGE_SCHEMA,
    TEST_SCHEMA,
)
from pyspark.sql import SparkSession
from pyspark.sql.types import StructType
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_rdd_secret, setup_databricks_logging
from rddlib.dbx_utils import get_dbx_env_catalog

logger = logging.getLogger(__name__)


class TestReportsMetricsTransformer:
    """Class to transform and store test reports metrics into Databricks tables."""

    MAX_ARTIFACTORY_FETCH_RETRIES: ClassVar[int] = 3
    ARTIFACTORY_FETCH_RETRY_WAIT_SEC: ClassVar[float] = 10.0
    ARTIFACTORY_BASE_URL: ClassVar[str] = "https://jfrog.ad-alliance.biz/shared-generic-qa-local/rdd/test-reports/"

    catalog: str
    schema: str
    datasource: str
    version: str
    spark: SparkSession

    def __init__(
        self,
        catalog_name: str,
        schema_name: str,
        datasource: str,
        version: str,
    ) -> None:
        """Initializes the TestReportTransformer with specific parameters for data transformation and storage.

        Args:
            catalog_name (str): The name of the Databricks catalog.
            schema_name (str): The name of the Databricks schema.
            datasource (str): The datasource of the report.
            version (str): The version of the report.
        """
        self.catalog = catalog_name
        self.schema = schema_name
        self.datasource = datasource
        self.version = version
        self.spark = SparkSession.builder.getOrCreate()

    def download_test_reports(self) -> list[Path]:
        """Downloads the test reports from Artifactory."""
        current_directory = Path.cwd()
        files_to_download = []
        if self.datasource == "rng":
            files_to_download = [
                f"{self.datasource}_unit_test_results.json",
                f"{self.datasource}_test_coverage.json",
                f"{self.datasource}_function_e2e_test_results.json",
                f"{self.datasource}_container_e2e_test_results.json",
            ]
        else:
            files_to_download = [
                f"{self.datasource}_unit_test_results.json",
                f"{self.datasource}_test_coverage.json",
                f"{self.datasource}_e2e_test_results.json",
            ]

        art_name = get_rdd_secret("alliance-artifactory-username")
        art_key = get_rdd_secret("alliance-artifactory-token")
        downloaded_files: list[Path] = []

        for file_name in files_to_download:
            file_url = f"{self.ARTIFACTORY_BASE_URL}{self.version}/{file_name}"
            file_path = current_directory / file_name

            retries = 0

            while retries < self.MAX_ARTIFACTORY_FETCH_RETRIES:
                try:
                    auth = (art_name, art_key)
                    response = requests.get(file_url, auth=auth, timeout=10)
                    response.raise_for_status()  # Raise an exception for HTTP errors
                    with open(file_path, "wb") as file:
                        file.write(response.content)
                    logger.info(f"Downloaded {file_name} to {file_path} (size: {file_path.stat().st_size} bytes)")

                    if file_path.stat().st_size == 0:
                        logger.error(f"Downloaded file {file_name} is empty.")
                    else:
                        downloaded_files.append(file_path)  # Add to list if file is not empty
                    break
                except requests.RequestException as e:
                    logger.error(f"Failed to download {file_name}: {str(e)}")
                    retries += 1
                    if retries < self.MAX_ARTIFACTORY_FETCH_RETRIES:
                        logger.info(f"Retrying in {self.ARTIFACTORY_FETCH_RETRY_WAIT_SEC} seconds...")
                        time.sleep(self.ARTIFACTORY_FETCH_RETRY_WAIT_SEC)
                    else:
                        raise Exception(f"Max retries reached. Could not download {file_name}.")

        return downloaded_files

    def transform_test_report(self, report_json: dict[str, Any], test_type: str) -> list[dict[str, Any]]:
        """Transforms the JSON test reports for unit and e2e tests into a format suitable for Spark DataFrame."""
        test_input = report_json["report"]["tests"]
        created_at = report_json["report"]["created_at"]
        transformed_unit_test_report = []

        for test in test_input:
            transformed_unit_test_report_element = {
                "datasource": self.datasource,
                "outcome": str(test.get("outcome")),
                "name": str(test.get("name").split("::")[-1]),
                "created_at": datetime.strptime(created_at, DATE_TIME_FORMAT),
                "was_created_for": self.version,
                "duration": float(test.get("duration")),
                "type": str(test_type),
            }
            transformed_unit_test_report.append(transformed_unit_test_report_element)

        return transformed_unit_test_report

    def transform_coverage_report(self, report_json: dict[str, Any]) -> dict[str, Any]:
        """Transforms the original test coverage report."""
        return {
            "datasource": self.datasource,
            "percent_covered": int(report_json["totals"].get("percent_covered_display", 0)),
            "was_created_for": self.version,
        }

    def store_in_databricks(self, data: list[dict[str, Any]], schema: StructType, table_name: str) -> None:
        """Stores the transformed data into a Databricks delta table."""
        df = self.spark.createDataFrame(data, schema=schema)
        table_full = f"{self.catalog}.{self.schema}.`{table_name}`"
        if dtu.table_exists(table_full):
            if table_name == DATABRICKS_TABLE_MAP[RESOURCE_TEST_COVERAGE]:
                condition = "target.was_created_for = source.was_created_for AND target.datasource = source.datasource"
            else:
                condition = (
                    "target.was_created_for = source.was_created_for"
                    " AND target.datasource = source.datasource"
                    " AND target.type = source.type AND target.name = source.name"
                )
            dtu.merge(df, table_full, condition)
        else:
            dtu.overwrite(df, table_full)
            logger.info(f"New table {table_full} created and data written")
        # Add metadata to the table
        description = TABLE_DESCRIPTION_MAP.get(table_name, "")
        dtu.update_table_metadata(table_full, description)

    def run(self) -> None:
        """Downloads reports, transforms them, and stores the data in Databricks."""
        report_files = self.download_test_reports()

        for report_file in report_files:
            logger.info(f"Input file being transformed is {report_file}")
            with report_file.open("r", encoding="utf-8") as fp:
                report_json = json.load(fp)
            if RESOURCE_UNIT_TEST_RESULTS in report_file.name:
                data = self.transform_test_report(report_json, RESOURCE_UNIT_TEST_RESULTS)
                schema = TEST_SCHEMA
                table_name = DATABRICKS_TABLE_MAP[RESOURCE_UNIT_TEST_RESULTS]
            elif RESOURCE_TEST_COVERAGE in report_file.name:
                data = [self.transform_coverage_report(report_json)]
                schema = TEST_COVERAGE_SCHEMA
                table_name = DATABRICKS_TABLE_MAP[RESOURCE_TEST_COVERAGE]
            elif RESOURCE_E2E_TEST_RESULTS in report_file.name:
                data = self.transform_test_report(report_json, RESOURCE_E2E_TEST_RESULTS)
                schema = TEST_SCHEMA
                table_name = DATABRICKS_TABLE_MAP[RESOURCE_E2E_TEST_RESULTS]
            else:
                logger.error(f"Unsupported input file: {report_file}. Skipping.")
                continue

            self.store_in_databricks(data, schema, table_name)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Transform JSON report and upload to Databricks table.")
    parser.add_argument("-i", "--run_id", dest="run_id")
    parser.add_argument("-v", "--version", required=True, help="GitHub commit sha as report version.")
    parser.add_argument(
        "-d", "--datasource", dest="datasource", required=True, help="Datasource, ex. ado, adx, github etc."
    )

    args = parser.parse_args()

    catalog_name = get_dbx_env_catalog("silver")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_name, SCHEMA_NAME, False), "test_reports/silver", run_id=args.run_id
    )

    transformer = TestReportsMetricsTransformer(catalog_name, SCHEMA_NAME, args.datasource, args.version)
    transformer.run()
