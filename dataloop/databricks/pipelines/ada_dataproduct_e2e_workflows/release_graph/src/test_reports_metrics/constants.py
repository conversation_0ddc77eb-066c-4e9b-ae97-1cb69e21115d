"""Configurations and constants for test reports transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import DoubleType, IntegerType, StringType, StructField, StructType, TimestampType

# Constants

SCHEMA_NAME = "rdd_release_graph"
TABLE_TEST_COVERAGE = "test_coverage_metrics"
RESOURCE_UNIT_TEST_RESULTS = "unit_test"
RESOURCE_TEST_COVERAGE = "coverage"
RESOURCE_E2E_TEST_RESULTS = "e2e_test"
DATE_TIME_FORMAT = "%Y-%m-%d %H:%M:%S.%f"
TABLE_TEST_RESULTS = "test_results_metrics"

# Schema for the unit test and e2e test report
TEST_SCHEMA = StructType(
    [
        StructField("created_at", TimestampType(), True),
        StructField("datasource", StringType(), True),
        StructField("duration", DoubleType(), True),
        StructField("name", StringType(), True),
        StructField("outcome", StringType(), True),
        StructField("type", StringType(), True),
        StructField("was_created_for", StringType(), True),
    ]
)

# Schema for the unit test coverage report
TEST_COVERAGE_SCHEMA = StructType(
    [
        StructField("datasource", StringType(), True),
        StructField("percent_covered", IntegerType(), True),
        StructField("was_created_for", StringType(), True),
    ]
)

# Table Map
DATABRICKS_TABLE_MAP = {
    RESOURCE_UNIT_TEST_RESULTS: TABLE_TEST_RESULTS,
    RESOURCE_TEST_COVERAGE: TABLE_TEST_COVERAGE,
    RESOURCE_E2E_TEST_RESULTS: TABLE_TEST_RESULTS,
}

TABLE_DESCRIPTION_MAP = {
    "test_coverage_metrics": "This table stores information about the test coverage for the unit tests, indicating the percentage of the code covered by tests extracted from the test reports stored in the artifactory. The table records the datasource, the coverage percentage, and the version for which the coverage report was generated. The ingestion and maintenance of this table are handled by the RDD Team.",
    "test_results_metrics": "This table stores the results of end-to-end (E2E) and unit tests extracted from the test reports stored in the artifactory, including the test outcome, test name, execution duration, and the timestamp and the version for which the test report was generated. The ingestion and maintenance of this table are handled by the RDD Team.",
}
