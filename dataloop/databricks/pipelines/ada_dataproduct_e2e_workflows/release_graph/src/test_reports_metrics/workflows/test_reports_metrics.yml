resources:
  jobs:
    test_reports_metrics:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: Test Reports Metrics - Transform and Load
      tags:
        rdd: ""
        metrics: ""
      # Required parameters to evaluate version, datasource and input_file arguments
      parameters:
        - name: github_sha
          default: 479abec03b42cdda697cba4a0a61e1ce85ee5ee0
        - name: module_name
          default: adx

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: transform_test_reports
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform_test_reports_metrics.py
            parameters:
              - --version
              - "{{job.parameters.github_sha}}"
              - --datasource
              - "{{job.parameters.module_name}}"
              - --run_id
              - "{{job.run_id}}"

          job_cluster_key: rdd_test_reports_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: load_test_reports_metrics
          depends_on:
            - task_key: transform_test_reports
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - rdd_release_graph
              - --database
              - rdd_release_v1
              - --mapping_file
              - test_results_metrics.j2
              - --run_id
              - "{{job.run_id}}"
              - --version
              - "{{job.parameters.github_sha}}"
          job_cluster_key: rdd_test_reports_job_cluster

        - task_key: load_test_coverage_reports_metrics
          depends_on:
            - task_key: load_test_reports_metrics
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - rdd_release_graph
              - --database
              - rdd_release_v1
              - --mapping_file
              - test_coverage_metrics.j2
              - --run_id
              - "{{job.run_id}}"
              - --version
              - "{{job.parameters.github_sha}}"
          job_cluster_key: rdd_test_reports_job_cluster

      job_clusters:
        - job_cluster_key: rdd_test_reports_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
