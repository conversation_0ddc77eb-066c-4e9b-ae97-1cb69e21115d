"""This script handles the transformation of silver-level needs data into gold-level needs data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging

from pyspark.sql import SparkSession
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, setup_databricks_logging
from transform_gold import NeedsGoldTransformer

logger = logging.getLogger(__name__)

# Constants
SILVER_SCHEMA = "needs"
SILVER_TABLE_MAP = {
    "rng": "rdd_needs",
    "release": "pace_needs",
}

SCHEMA_GOLD = "rdd_release_graph"  # todo change to correct schema when available
RUN_TYPE = "tdo"
APPLICATION_MAP = ["rng", "release"]

GOLD_TABLES = ["needs_properties_latest", "needs_edges_latest"]


def drop_delta_table(gold_dbx_catalog: str, gold_schema: str) -> None:
    """Drops the gold tables for props and edges latest so that only newest data is present.

    Args:
        gold_dbx_catalog (str): name of gold catalog
        gold_schema (str): name of gold schema
    """
    for table in GOLD_TABLES:
        table_path = f"{gold_dbx_catalog}.{gold_schema}.{table}"
        dtu.drop(table_path)


def get_latest_silver_version(app: str, transformer: NeedsGoldTransformer) -> str:
    """Read the latest distinct needs silver version for the specified application.

    Args:
        app (str): application that will be handled
        transformer (NeedsGoldTransformer): transformer class for gold transformation

    Returns:
        str: The latest version
    """
    silver_table = f"{SILVER_TABLE_MAP[app]}_test" if transformer.test_mode else SILVER_TABLE_MAP[app]
    silver_table_full = f"{transformer.silver_dbx_catalog}.{SILVER_SCHEMA}.{silver_table}"
    # Define the SQL query to get the latest version
    spark = SparkSession.builder.getOrCreate()
    silver_df = spark.read.table(silver_table_full)

    # Filter, sort, and select the latest version
    latest_version = (
        silver_df.select("version", "created_date")  # Select only relevant columns
        .orderBy("created_date", ascending=False)  # Sort by created_date descending
        .limit(1)
        .collect()[0]["version"]  # Collect the DataFrame to extract the 'version'
    )

    # Collect and return the list of new versions
    return latest_version


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Needs Gold Layer Transform")
    parser.add_argument("-r", "--run_id", dest="run_id")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    # Parse command-line arguments
    args, unknown = parser.parse_known_args()

    # Assign parsed arguments to variables
    test_mode = args.test_mode

    # Get the appropriate catalog and schema based on environment and application
    catalog_silver = get_dbx_env_catalog("silver")
    catalog_gold = get_dbx_env_catalog("gold")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_gold, SCHEMA_GOLD, False),
        "needs/gold",
        run_id=args.run_id,
        enabled_loggers=["transform_gold"],
    )

    # loop through different applications
    for idx, app in enumerate(APPLICATION_MAP):

        # drop tables to be able to use needs gold transformer update function
        if idx == 0:
            drop_delta_table(catalog_gold, SCHEMA_GOLD)

        # Initialize the transformer object
        transformer = NeedsGoldTransformer(app, catalog_silver, catalog_gold, SCHEMA_GOLD, test_mode, RUN_TYPE)

        # Get latest silver version for app
        version = get_latest_silver_version(app, transformer)
        # Run the transformation process
        logger.info(f"Transforming needs data for version: {version}")
        transformer.run(version)
