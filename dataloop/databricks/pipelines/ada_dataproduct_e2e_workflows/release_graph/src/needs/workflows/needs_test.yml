resources:
  jobs:
    needs_e2e_test:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "ADA Release Graph - Needs - E2E Test"
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "E2E"
        test: ""
        "ADA Release Graph": ""
        "Needs": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: cleanup_bronze_test_tables
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "bronze_${var.env}",
                "-s",
                "ada_release_graph",
                "-t",
                "needs_test",
              ]
          job_cluster_key: rdd_needs_test_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: needs_extract_tag
          depends_on:
            - task_key: cleanup_bronze_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --application
              - release
              - --sw_version
              - e6724ce3cf15fbd5e32d831ae8c585080fe128cc
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_needs_test_job_cluster

        - task_key: needs_extract_adx
          depends_on:
            - task_key: cleanup_bronze_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --application
              - release
              - --adx
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_needs_test_job_cluster

        - task_key: cleanup_silver_test_tables
          depends_on:
            - task_key: needs_extract_tag
            - task_key: needs_extract_adx
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "silver_${var.env}",
                "-s",
                "ada_release_graph",
                "-t",
                "needs_nodes_test",
                "-t",
                "needs_edges_test",
              ]
          job_cluster_key: rdd_needs_test_job_cluster

        - task_key: needs_transform_tag
          depends_on:
            - task_key: cleanup_silver_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --application
              - release
              - --task_output
              - needs_extract_tag
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_needs_test_job_cluster

        - task_key: needs_transform_adx
          depends_on:
            - task_key: needs_transform_tag
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --application
              - release
              - --task_output
              - needs_extract_adx
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_needs_test_job_cluster

        - task_key: cleanup_stardog_test_db
          depends_on:
            - task_key: needs_transform_adx
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters: ["stardog", "drop", "--database", "ada_needs_e2e_test"]
          job_cluster_key: rdd_needs_test_job_cluster

        - task_key: needs_load
          depends_on:
            - task_key: cleanup_stardog_test_db
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_needs_e2e_test
              - --mapping_file
              - needs_test.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_needs_test_job_cluster

        - task_key: needs_verification
          depends_on:
            - task_key: needs_load
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/e2e_tests/test_needs.py
            parameters:
              - --db
              - ada_needs_e2e_test
          job_cluster_key: rdd_needs_test_job_cluster

      job_clusters:
        - job_cluster_key: rdd_needs_test_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
