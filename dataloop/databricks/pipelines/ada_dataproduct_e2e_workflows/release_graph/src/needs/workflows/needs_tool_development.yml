resources:
  jobs:
    needs_gold_tool_development_overview:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: Tool Development Overview - Gold - Nightly
      description: Transform needs data from Silver to Gold Layer and Load to Stardog - tool development overview
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "Gold"
        schedule: "Nightly"
        pace: ""
        needs: ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600
      schedule:
        quartz_cron_expression: ${var.nightly_schedule}
        timezone_id: UTC
        pause_status: ${var.nightly_trigger}
      tasks:
        - task_key: needs_transform_gold
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform_gold_tool_overview.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_needs_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: cleanup_stardog_tdo
          depends_on:
            - task_key: needs_transform_gold
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters: ["stardog", "drop", "--database", "tool_development_overview"]
          job_cluster_key: rdd_needs_job_cluster

        - task_key: needs_load
          depends_on:
            - task_key: cleanup_stardog_tdo
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --environment
              - ${var.env}
              - --workspace
              - ${var.env}
              - --level
              - gold
              - --schema
              - rdd_release_graph
              - --database
              - tool_development_overview
              - --mapping_file
              - needs_latest.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_needs_job_cluster

      job_clusters:
        - job_cluster_key: rdd_needs_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
