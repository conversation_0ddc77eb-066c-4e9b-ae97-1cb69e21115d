resources:
  jobs:
    needs_etl_rng:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "RDD Release Graph - Needs - E2E - Commit SHA"
      description: Ingest needs for RDD repo based on SHA
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "E2E"
        type: "Commit SHA"
        "RDD Release Graph": ""
        "Needs": ""

      parameters:
        - name: sw_version
          default: ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: needs_extract_version
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --application
              - rng
              - --art_path
              - "https://jfrog.ad-alliance.biz/artifactory/shared-generic-dev/rdd/rng-needs/"
              - --sw_version
              - "{{job.parameters.sw_version}}"
              - sw_version
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_needs_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: needs_transform_version
          depends_on:
            - task_key: needs_extract_version
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --application
              - rng
              - --task_output
              - needs_extract_version
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_needs_job_cluster

        - task_key: needs_load
          depends_on:
            - task_key: needs_transform_version
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - rdd_release_graph
              - --database
              - rdd_release_v1
              - --mapping_file
              - needs_rng.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_needs_job_cluster

      job_clusters:
        - job_cluster_key: rdd_needs_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
