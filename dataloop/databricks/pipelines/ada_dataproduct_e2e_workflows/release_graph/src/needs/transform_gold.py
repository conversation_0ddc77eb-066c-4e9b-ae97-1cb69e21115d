"""This script handles the transformation of silver-level needs data into gold-level needs data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from typing import Any, Callable

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, concat, explode, lit, regexp_replace, split, trim, when
from pyspark.sql.types import ArrayType, StringType, StructField, StructType
from rddlib import DBX_APP_SCHEMA_MAP, FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, quality_check, setup_databricks_logging
from rddlib.needs_utils import get_latest_pace_ontology
from rdflib import Graph, query

logger = logging.getLogger(__name__)

# Constants
SILVER_SCHEMA = "needs"
SILVER_TABLE_MAP = {
    "rng": "rdd_needs",
    "release": "pace_needs",
}

GOLD_TABLE_PROPS = {
    "workflow": "needs_properties",
    "tdo": "needs_properties_latest",
}
GOLD_TABLE_EDGES = {
    "workflow": "needs_edges",
    "tdo": "needs_edges_latest",
}

GOLD_SCHEMA = StructType(
    [
        StructField("start", StringType(), True),
        StructField("target", StringType(), True),
        StructField("type", StringType(), True),
        StructField("mapped_type", StringType(), True),
        StructField("version", StringType(), True),
    ]
)

SPECIAL_EDGES = ["tags", "test_platform", "test_trigger"]

GOLD_TABLE_DESCRIPTION_MAP = {
    "needs_properties_latest": """
    Contains the properties of needs elements in the latest version.
    Each row represents an edge connecting a needs node to a property,
    classified by type and version. Managed by the RDD Team.
    """,
    "needs_edges_latest": """
    Defines relationships between needs nodes in the latest version.
    Each row represents an edge between two needs nodes,
    classified by type and version. Managed by the RDD Team.
    """,
}


def _get_query(predicate: str, g: Graph) -> query.Result:
    return g.query(
        f"""
    SELECT ?mapped_type ?needs_type
    WHERE {{
        ?mapped_type {predicate} ?needs_type .
    }}
    """
    )


def _get_dict_from_query(query: query.Result) -> dict[str, Any]:
    return {str(needs_type): str(mapped_type) for mapped_type, needs_type in query}


class NeedsGoldTransformer:
    """This class is responsible for transforming silver-level needs data into gold-level."""

    app: str
    silver_dbx_catalog: str
    gold_dbx_catalog: str
    gold_schema: str
    test_mode: bool
    run_type: str

    def __init__(
        self,
        app: str,
        silver_dbx_catalog: str,
        gold_dbx_catalog: str,
        gold_schema: str,
        test_mode: bool,
        run_type: str,
    ):
        """Initialize a NeedsGoldTransformer instance."""
        self.app = app
        self.silver_dbx_catalog = silver_dbx_catalog
        self.gold_dbx_catalog = gold_dbx_catalog
        self.gold_schema = gold_schema
        self.test_mode = test_mode
        self.run_type = run_type
        self.spark = SparkSession.builder.getOrCreate()
        self.type_mappings, self.link_mappings, self.option_mappings = self.get_mapping_dicts()
        self.suffix = lit("id") if self.run_type == "tdo" else col("version")

    def get_mapping_dicts(self) -> tuple[dict[str, Any], dict[str, Any], dict[str, Any]]:
        """Generate three mapping dictionaries based on the latest ontology.

        This contains mappings for type, link and option value

        return: a tuple of three mapping dictionaries:
            key: Original needs json keyword, eg. 'activity'
            value: The corresponding value in the ontology, eg. 'urn:pace:ontology:Activity'
            In the order of type, link and option mappings.
        """
        # Get schema from artifactory and parse it into a RDFlib Graph object
        g = Graph()
        g.parse(get_latest_pace_ontology().open())

        # Generate the individual mapping dictionaries for each type of mapping
        type_mappings = _get_dict_from_query(_get_query("pace:needsType", g))
        link_mappings = _get_dict_from_query(_get_query("pace:needsLink", g))
        option_mappings = _get_dict_from_query(_get_query("pace:needsOption", g))
        return (type_mappings, link_mappings, option_mappings)

    def _get_missing_mappings(self, df: DataFrame) -> list[Any]:
        """Returns the list of unique 'type' values that are missing a corresponding 'mapped_type' mapping.

        Args:
            df (DataFrame): DataFrame containing the 'type' and 'mapped_type' columns.

        Returns:
            list[Any]: A list of distinct 'type' values with null 'mapped_type'.
        """
        if "type" not in df.columns or "mapped_type" not in df.columns:
            raise ValueError("The required columns 'type' or 'mapped_type' are missing from the DataFrame.")

        return [row["type"] for row in df.select("type").where(col("mapped_type").isNull()).distinct().collect()]

    def _update_delta_table(self, table: str, df: DataFrame, condition: str) -> None:
        """Update a Gold Layer delta table using merge or overwrite.

        Args:
            table (str): The name of the Delta table being updated.
            df (DataFrame): The DataFrame to update the Delta table with.
            condition (str): The merge condition for upserts.
        """
        table = f"{table}_test" if self.test_mode else table
        table_full = f"{self.gold_dbx_catalog}.{self.gold_schema}.{table}"

        if dtu.table_exists(table_full):
            logger.info(f"Table: {table} already exists")
            # Check if schema is consistent
            if dtu.table_has_schema(table_full, df.schema):
                dtu.merge(df, table_full, condition)
            else:
                logger.info(f"Schema of table '{table_full}' changed. The table will be overwritten.")
                dtu.overwrite(df, table_full, overwrite_schema=True)
        else:
            logger.info(f"Table: {table_full} does not exist")
            dtu.overwrite(df, table_full)
        # Add metadata to the table
        description = GOLD_TABLE_DESCRIPTION_MAP.get(table, "")
        dtu.update_table_metadata(table_full, description)

    def _create_label_prop(self, needs_df: DataFrame) -> DataFrame:
        """Create a property dataframe with the 'label' property.

        Args:
            needs_df (DataFrame): The DataFrame containing the 'needs' nodes.

        Returns:
            DataFrame: A DataFrame containing the 'label' property.
        """
        label_prop = (
            needs_df.select("id", "version", "title")
            .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), self.suffix))
            .withColumnRenamed("title", "target")
            .withColumn("type", lit("label"))
            .withColumn("mapped_type", lit("http://www.w3.org/2000/01/rdf-schema#label"))
            .drop("id")
        )
        return label_prop

    def _transform_props(self, needs_df: DataFrame) -> DataFrame:
        """Parse data and extract the properties for the needs nodes.

        Args:
            needs_df (DataFrame): The DataFrame containing the 'needs' data.

        Returns:
            DataFrame: A transformed DataFrame with mapped and cleaned nodes.
        """

        # add id_iri: urn:pace:ontology:needs:<id>
        props_df = self.spark.createDataFrame([], GOLD_SCHEMA)
        schema = needs_df.schema

        # Apply app-specific transformations
        app_specific_transform = self._get_app_specific_transform()
        needs_df = app_specific_transform(needs_df)

        for field in schema:
            column_name = field.name
            data_type = field.dataType
            if (
                isinstance(data_type, StringType)
                and column_name != "version"
                and column_name != "id"
                and column_name != "type"
            ):
                prop = needs_df.select(needs_df.id, needs_df[column_name])
                prop = (
                    prop.join(needs_df.select("id", "version"), "id")
                    .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), self.suffix))
                    .withColumnRenamed(column_name, "target")
                    .withColumn("type", lit(column_name))
                    .drop("id")
                )

                # Map edge type
                prop = self._add_mapped_values(prop, self.option_mappings, "type", "mapped_type")
                # Add edge to total edges
                props_df = props_df.unionByName(prop)

        # add label property from title column
        props_df = props_df.unionByName(self._create_label_prop(needs_df))
        props_df = props_df.dropDuplicates()

        # filter out empty or null properties
        props_df = props_df.where(props_df.target.isNotNull()).where("target != ''")

        # Perform a quality check for missing mappings
        self._perform_quality_check(props_df)

        return props_df

    def _get_app_specific_transform(self) -> Callable[[DataFrame], DataFrame]:
        """Retrieve the app-specific transformation function based on the app name.

        Returns:
            Callable: The transformation function specific to the current app.

        Raises:
            ValueError: If the app does not have a supported transformation function.
        """
        app_transforms = {
            "release": self._transform_release_app,
            "rng": self._transform_rng_app,
        }

        if self.app in app_transforms:
            return app_transforms[self.app]

        raise ValueError(f"Transformation for app '{self.app}' is not supported.")

    def _transform_release_app(self, needs_df: DataFrame) -> DataFrame:
        """App-specific transformation for 'release' app.

        Args:
            needs_df (DataFrame): The DataFrame containing the needs data.

        Returns:
            DataFrame: A DataFrame with transformations applied.
        """

        # Identify rows with issue_id containing non-digit characters
        bad_issues = needs_df.where(col("issue_id").rlike(".*?[^0-9].*")).select("id", "issue_id")

        if bad_issues.count() > 0:
            bad_issues_list = [(row["id"], row["issue_id"]) for row in bad_issues.collect()]
            logger.info(f"Nodes with invalid issue_id: {bad_issues_list}")

        # Replace non-digit issue_id with None
        needs_df = needs_df.withColumn(
            "issue_id", when(col("issue_id").rlike(".*?[^0-9].*"), lit(None)).otherwise(col("issue_id"))
        )
        return needs_df

    def _transform_rng_app(self, needs_df: DataFrame) -> DataFrame:
        """App-specific transformation for 'rng' app.

        Args:
            needs_df (DataFrame): The DataFrame containing the nodes.

        Returns:
            DataFrame: A DataFrame with transformations applied.
        """

        # Replace 'xxx' with null in the feature_ticket column
        needs_df = needs_df.withColumn(
            "feature_ticket", when(col("feature_ticket") == "xxx", lit(None)).otherwise(col("feature_ticket"))
        )

        # Split the feature_ticket by commas and trim spaces
        needs_df = needs_df.withColumn(
            "feature_ticket",
            split(trim(regexp_replace(col("feature_ticket"), " ", "")), ","),  # Remove spaces, then split by commas
        )

        # Create DataFrame for non-null feature_ticket
        exploded_df = needs_df.select("*").withColumn("feature_ticket", explode(col("feature_ticket")))

        # Create DataFrame for original rows where feature_ticket is null
        null_rows_df = needs_df.filter(col("feature_ticket").isNull())
        null_rows_df = null_rows_df.withColumn(
            "feature_ticket", lit(None).cast("string")
        )  # Ensure this column is STRING

        # Combine both DataFrames
        result_df = exploded_df.union(null_rows_df.select("*"))

        return result_df

    def _perform_quality_check(self, needs_df: DataFrame) -> None:
        """Perform a quality check for missing property mappings in the provided DataFrame.

        Args:
            needs_df (DataFrame): The DataFrame containing property mappings to check.
        """
        missing_mappings = self._get_missing_mappings(needs_df)

        # Log the quality check result based on the presence of missing mappings
        if len(missing_mappings) == 0:
            quality_check.log_quality_check_result(True, "needs_missing_prop_mappings", None)
        else:
            quality_check.log_quality_check_result(False, "needs_missing_prop_mappings", missing_mappings)

    def _handle_special_edges(self, needs_df: DataFrame, edges_df: DataFrame) -> DataFrame:
        """Handles corner cases involving, "tags", "test_platform", and "test_trigger".

        Args:
            needs_df (DataFrame): The DataFrame containing the needs data.
            edges_df (DataFrame): The DataFrame containing the total edges.

        Returns:
            DataFrame: A DataFrame containing the specialized edge.
        """

        # Pre-process test-platform and -trigger, by creating arrays from comma separated strings.
        if self.app == "release":
            needs_df = needs_df.withColumn("test_platform", split(regexp_replace(col("test_platform"), " ", ""), ","))
            needs_df = needs_df.withColumn("test_trigger", split(regexp_replace(col("test_trigger"), " ", ""), ","))

        for name in SPECIAL_EDGES:
            # Skip test_platform and test_trigger for non release application
            if name != "tags" and self.app != "release":
                continue
            edge = needs_df.select(needs_df.id, explode(name))
            edge = edge.dropDuplicates()
            # create initial edge with start, type and version columns
            edge = (
                edge.join(needs_df.select("id", "version"), "id")
                .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), self.suffix))
                .withColumn("type", lit(name))
                .drop("id")
            )
            # add custom target columns
            if name == "tags":
                # clean up "tags"
                edge = edge.withColumn(
                    "target", regexp_replace(concat(lit("urn:pace:ontology:needs:"), col("col")), " ", "")
                )
            elif name == "test_platform":
                # Link to static TestPlatform node
                edge = edge.withColumn("target", concat(lit("urn:pace:ontology:TestPlatform-"), col("col")))
            elif name == "test_trigger":
                # Link to static TestTrigger node
                edge = edge.withColumn("target", concat(lit("urn:pace:ontology:TestTrigger-"), col("col")))
            edge = edge.drop("col")
            # Map edge type
            edge = self._add_mapped_values(edge, self.link_mappings, "type", "mapped_type")
            # Add edge to total edges
            edges_df = edges_df.unionByName(edge)

        return edges_df

    def _create_type_edge(self, needs_df: DataFrame) -> DataFrame:
        """Create edge to the node type.

        Args:
            needs_df (DataFrame): The DataFrame containing the needs data.

        Returns:
            DataFrame: A DataFrame with the edge to the node type.
        """
        # Add the edge to the node type
        type_edge = needs_df.select(needs_df.id, needs_df.type)
        type_edge = (
            type_edge.join(needs_df.select("id", "version"), "id")
            .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), self.suffix))
            .withColumnRenamed("type", "target")
            .withColumn("type", lit("type"))
            .withColumn("mapped_type", lit("http://www.w3.org/1999/02/22-rdf-syntax-ns#type"))
            .drop("id", "col")
        )
        type_edge = self._add_mapped_values(type_edge, self.type_mappings, "target", "mapped_target").drop("target")
        type_edge = type_edge.withColumnRenamed("mapped_target", "target").drop("mapped_target")
        return type_edge

    def _create_version_edge(self, needs_df: DataFrame) -> DataFrame:
        """Create edge to the version node.

        Args:
            needs_df (DataFrame): The DataFrame containing the needs data.

        Returns:
            DataFrame: A DataFrame with the edge to the version node.
        """
        # Add the edge to the version node
        version_edge = needs_df.select(needs_df.id, needs_df.version)
        version_edge = (
            version_edge.withColumn("start", concat(lit("urn:pace:ontology:"), col("version")))
            .withColumn("target", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), self.suffix))
            .withColumn("type", lit("version"))
            .withColumn("mapped_type", lit("urn:pace:ontology:validFor"))
            .drop("id")
        )
        return version_edge

    def _create_id_iri_edge(self, needs_df: DataFrame) -> DataFrame:
        """Create a property dataframe with the 'id_iri' property.

        Args:
            needs_df (DataFrame): The DataFrame containing the 'needs' data.

        Returns:
            DataFrame: A DataFrame containing the 'id_iri' property.
        """
        id_iri_edge = (
            needs_df.select("id", "version")
            .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), self.suffix))
            .withColumn("target", concat(lit("urn:pace:ontology:needs:"), col("id")))
            .withColumn("type", lit("id_iri"))
            .withColumn("mapped_type", lit("urn:pace:ontology:idIri"))
            .drop("id")
        )
        return id_iri_edge

    def _transform_edges(self, needs_df: DataFrame) -> DataFrame:
        """Creates a DataFrame with mapped and cleaned needs edges from the given DataFrame.

        Args:
            needs_df (DataFrame): DataFrame with needs data to extract into edges table.

        Returns:
            DataFrame: A DataFrame containing the mapped and cleaned needs edges.
        """
        # Creating an empty DataFrame to hold the edges
        edges_df = self.spark.createDataFrame([], GOLD_SCHEMA)
        schema = needs_df.schema

        for field in schema:
            column_name = field.name
            data_type = field.dataType
            if isinstance(data_type, ArrayType) and not column_name.endswith("_back"):
                if column_name in SPECIAL_EDGES:
                    continue
                edge = needs_df.select(needs_df.id, explode(needs_df[column_name]))
                edge = edge.dropDuplicates()
                # Create edge with start, target and type column
                edge = (
                    edge.join(needs_df.select("id", "version"), "id")
                    .withColumn("start", concat(lit("urn:pace:ontology:needs:"), col("id"), lit("_"), self.suffix))
                    .withColumn("target", concat(lit("urn:pace:ontology:needs:"), col("col"), lit("_"), self.suffix))
                    .withColumn("type", lit(column_name))
                    .drop("col", "id")
                )
                # Map edge type
                edge = self._add_mapped_values(edge, self.link_mappings, "type", "mapped_type")
                # Add edge to total edges
                edges_df = edges_df.unionByName(edge)

        # Add the edge to the node type
        edges_df = edges_df.unionByName(self._create_type_edge(needs_df))

        # Add the edge to the version node
        edges_df = edges_df.unionByName(self._create_version_edge(needs_df))

        # Handle corner cases
        edges_df = self._handle_special_edges(needs_df, edges_df)

        # Add the edge to id_iri
        edges_df = edges_df.unionByName(self._create_id_iri_edge(needs_df))

        missing_mappings = self._get_missing_mappings(edges_df)

        if not missing_mappings:
            quality_check.log_quality_check_result(True, "needs_missing_edge_mappings", None)
        else:
            quality_check.log_quality_check_result(False, "needs_missing_edge_mappings", missing_mappings)

        return edges_df

    def _add_mapped_values(self, df: DataFrame, mappings: dict[str, Any], key: str, mapped_key: str) -> DataFrame:
        """Add mapped values to a DataFrame based on a mapping dictionary.

        Args:
            df (DataFrame): The DataFrame to add mapped values to.
            mappings (dict): A dictionary mapping original values to their corresponding mapped values.
            key (str): The key column name in the DataFrame to match against the mapping.
            mapped_key (str): The name of the new column to store mapped values.

        Returns:
            DataFrame: DataFrame with added mapped values.
        """
        # Create a DataFrame from the mappings dictionary, using "key" and "mapped_key" as column names
        mapping_df = self.spark.createDataFrame(list(mappings.items()), [key, mapped_key])

        # Perform a left outer join to map the values
        return df.join(mapping_df, key, "left_outer")

    def _get_silver_data(self, version: str) -> DataFrame | None:
        """Read needs silver data for the specified application and column filter.

        Args:
            filter (str): A valid Spark SQL expression to filter the DataFrame, e.g., 'version = "adb123dacd456"'.

        Returns:
            DataFrame | None: A DataFrame with corresponding needs silver data, or None if no data matches the filter.
        """
        silver_table = f"{SILVER_TABLE_MAP[self.app]}_test" if self.test_mode else SILVER_TABLE_MAP[self.app]
        silver_table_full_name = f"{self.silver_dbx_catalog}.{SILVER_SCHEMA}.{silver_table}"

        # Check if the silver table exists in the catalog
        if not dtu.table_exists(silver_table_full_name):
            raise Exception(f"Silver table '{silver_table_full_name}' does not exist!")

        # Load the Delta table
        df = self.spark.read.table(silver_table_full_name)

        # Apply the filter
        if self.run_type == "tdo":
            df = df.filter(
                (df.version == version)
                & (
                    (df.type == "usecase-db")
                    | (df.type == "tool")
                    | (df.type == "req-tool-int-sys")
                    | (df.type == "req-tool-int")
                    | (df.type == "test-tool-int-sys")
                    | (df.type == "test-tool-int")
                    | (df.type == "rel-artifact")
                )
            )
        else:
            df = df.where(f"version = '{version}'")

        # Check if the resulting DataFrame is empty
        if df.isEmpty():
            return None

        return df

    def get_latest_silver_versions(self) -> list[str]:
        """Read the latest distinct needs silver versions for the specified application.

        This function checks the silver data for the specified application and retrieves
        versions that are not present in the corresponding gold nodes table.

        Returns:
            list[str]: A list of versions.
        """
        silver_table = f"{SILVER_TABLE_MAP[app]}_test" if self.test_mode else SILVER_TABLE_MAP[app]
        silver_table_full = f"{self.silver_dbx_catalog}.{SILVER_SCHEMA}.{silver_table}"
        gold_needs_table = (
            f"{GOLD_TABLE_PROPS[self.run_type]}_test" if self.test_mode else GOLD_TABLE_PROPS[self.run_type]
        )
        gold_needs_table_full = f"{self.gold_dbx_catalog}.{self.gold_schema}.{gold_needs_table}"

        # Query to get distinct versions from the silver table
        silver_versions = self.spark.sql(f"SELECT DISTINCT version FROM {silver_table_full}")

        # If the gold nodes table does not exist, return all versions from the silver table
        if not dtu.table_exists(gold_needs_table_full):
            return [row.version for row in silver_versions.select("version").collect()]

        # Query to get distinct versions from the gold nodes table
        gold_versions = self.spark.sql(f"SELECT DISTINCT version FROM {gold_needs_table_full}")

        # Perform an anti-join to find new versions in silver not present in gold
        new_versions = silver_versions.join(gold_versions, on=["version"], how="left_anti")

        # Collect and return the list of new versions
        return [row.version for row in new_versions.select("version").collect()]

    def run(self, version: str) -> None:
        """Run the transformation process for the given version.

        This method retrieves silver data for the specified version, transforms the
        data into properties and edges DataFrames, and updates the corresponding Delta tables.

        Args:
            version (str): The version of the silver data to transform.
        """
        # Retrieve silver data for the specified version
        silver_df = self._get_silver_data(version)
        if silver_df is None:
            logger.error(f"No silver data found for version '{version}'")
            raise RuntimeError(f"Silver data for version '{version}' not found.")

        # Transform silver data into properties and edges
        props_df = self._transform_props(silver_df)
        edges_df = self._transform_edges(silver_df)

        # Merge condition
        MERGE_CONDITION = (
            "target.version = source.version AND "
            + "target.start = source.start AND "
            + "target.target = source.target AND "
            + "target.type = source.type"
        )
        # Update the Delta table for properties and edges
        self._update_delta_table(GOLD_TABLE_PROPS[self.run_type], props_df, MERGE_CONDITION)
        self._update_delta_table(GOLD_TABLE_EDGES[self.run_type], edges_df, MERGE_CONDITION)


if __name__ == "__main__":  # pragma: no cover
    # TODO: Support running this script based on latest ADX test report version after RDD:321582 is completed.
    parser = argparse.ArgumentParser(description="Needs Gold Layer Transform")
    parser.add_argument("-a", "--app", help="Application: ex - release, rng")
    parser.add_argument("-v", "--version", help="Silver needs version (commit sha)")
    parser.add_argument("-r", "--run_id", dest="run_id")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    # Parse command-line arguments
    args, unknown = parser.parse_known_args()

    # Assign parsed arguments to variables
    version = args.version
    app = args.app
    test_mode = args.test_mode

    # Get the appropriate catalog and schema based on environment and application
    catalog_silver = get_dbx_env_catalog("silver")
    catalog_gold = get_dbx_env_catalog("gold")
    schema_gold = DBX_APP_SCHEMA_MAP[app]

    # Setup logging
    setup_databricks_logging(FullSchemaName(catalog_gold, schema_gold, False), f"needs/gold/{app}", run_id=args.run_id)

    # Initialize the transformer object
    transformer = NeedsGoldTransformer(app, catalog_silver, catalog_gold, schema_gold, test_mode, run_type="workflow")

    # Determine which versions to process
    versions: list[str]
    if version:
        # If a specific version is provided, use it
        versions = [version]
    else:
        # If no version or task output is specified, get the latest silver versions
        versions = transformer.get_latest_silver_versions()

    # Process each version found
    for version in versions:
        logger.info(f"Transforming needs data for version: {version}")
        transformer.run(version)
