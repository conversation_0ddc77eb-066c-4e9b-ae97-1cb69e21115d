"""Helper module to define Needs related constants."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

ART_PATH = "https://jfrog.ad-alliance.biz/shared-generic-dev-local/sphinx_needs_json/"
BATCH_SIZE = 100
DESCRIPTION_BRONZE = (
    "The table stores raw needs data extracted from the artifactory. It includes information about constraints,"
    " content, dependencies, modifications, ownership, security, and status, among other attributes."
    " The table also captures relationships between different needs, such as associations, realizations, and implementations,"
    " along with metadata like tags, descriptions, and external references."
    " The ingestion and maintenance of this table are handled by the RDD Team."
)

SILVER_TABLE_DESCRIPTION_MAP = {
    "needs_nodes": (
        "This table contains information about various needs nodes. It includes columns such as id, title, version,"
        " source, type, da_status, mapped_type, variant_restriction, issue_id, owner, docname, deployment, tcl,"
        " and several other columns that provide details about baselines, projects, statuses, and safety integrity."
        " This table captures metadata and status information for different nodes in the ADA release process."
        " The ingestion and maintenance of this table are handled by the RDD Team."
    ),
    "needs_edges": (
        "This table represents relationships or connections between the needs (nodes) in the system."
        " Each row in this table defines an edge, where two nodes (needs) are connected."
        " The edges help define dependencies or associations between needs, and each edge is classified by its type and version."
        " The ingestion and maintenance of this table are handled by the RDD Team."
    ),
}

NEEDS_PREFIX = "urn:pace:ontology:needs:"
PACE_PREFIX = "urn:pace:ontology:"

TYPE_MAPPINGS = {  # pragma: no cover
    "activity-group": "urn:pace:ontology:ActivityGroup",
    "arche-ecu": "urn:pace:ontology:ArchEECU",
    "assumption": "urn:pace:ontology:Assumption",
    "cm-item": "urn:pace:ontology:ConfigurationManagementItem",
    "data-category": "urn:pace:ontology:DataCategory",
    "usecase-db": "urn:pace:ontology:DatabaseUseCase",
    "req-def": "urn:pace:ontology:Definition",
    "deploy": "urn:pace:ontology:Deploy",
    "ddd": "urn:pace:ontology:DesignDecisionDocument",
    "development-section": "urn:pace:ontology:DevelopmentSection",
    "eq-classes": "urn:pace:ontology:EquivalenceClasses",
    "error-propagation-model": "urn:pace:ontology:ErrorPropagationModel",
    "hazard": "urn:pace:ontology:Hazard",
    "hazardous-behaviour": "urn:pace:ontology:HazardousBehaviour",
    "hazardous-event": "urn:pace:ontology:HazardousEvent",
    "in-gateway": "urn:pace:ontology:InGateway",
    "req-tool-int": "urn:pace:ontology:InternallyDevelopedToolRequirement",
    "req-tool-int-sys": "urn:pace:ontology:InternallyDevelopedToolSystemRequirement",
    "kpi-value": "urn:pace:ontology:KeyPerformanceIndicator",
    "req-metric": "urn:pace:ontology:KeyPerformanceIndicatorMetrics",
    "req-tool": "urn:pace:ontology:NonProductionRequirement",
    "safety-os": "urn:pace:ontology:OperationalSituation",
    "out-gateway": "urn:pace:ontology:OutGateway",
    "package": "urn:pace:ontology:Package",
    "param-sys": "urn:pace:ontology:Parameter",
    "process_area": "urn:pace:ontology:ProcessArea",
    "process_landscape": "urn:pace:ontology:ProcessLandscape",
    "process_module": "urn:pace:ontology:ProcessModule",
    "safety-sg": "urn:pace:ontology:SafetyGoal",
    "sec-ast": "urn:pace:ontology:SecurityAsset",
    "sec-asm": "urn:pace:ontology:SecurityAssumption",
    "sec-sc": "urn:pace:ontology:SecurityClaim",
    "sec-ctrl": "urn:pace:ontology:SecurityControl",
    "sec-ds": "urn:pace:ontology:SecurityDamageScenario",
    "sec-goal": "urn:pace:ontology:SecurityGoal",
    "sec-muc": "urn:pace:ontology:SecurityMisuseCase",
    "sec-resrisk": "urn:pace:ontology:SecurityResidualRisk",
    "sec-risk": "urn:pace:ontology:SecurityRisk",
    "sec-th": "urn:pace:ontology:SecurityThreat",
    "sec-scen": "urn:pace:ontology:SecurityThreatScenario",
    "req-sw": "urn:pace:ontology:SoftwareRequirement",
    "solution-concept": "urn:pace:ontology:SolutionConcept",
    "req-sys": "urn:pace:ontology:SystemRequirement",
    "test-sw": "urn:pace:ontology:TestSw",
    "test-swint": "urn:pace:ontology:TestSwInt",
    "test-sys": "urn:pace:ontology:TestSys",
    "test-sysint": "urn:pace:ontology:TestSysInt",
    "test-tool-int": "urn:pace:ontology:InternallyDevelopedToolTest",
    "test-tool-int-sys": "urn:pace:ontology:InternallyDevelopedToolTestSys",
    "tool": "urn:pace:ontology:Tool",
    "usecase": "urn:pace:ontology:UseCase",
    "arche-partition": "urn:pace:ontology:ArchEPartition",
    "arche-soc": "urn:pace:ontology:ArchESOC",
    "functional-chain": "urn:pace:ontology:FunctionalChain",
    "in-port": "urn:pace:ontology:InputPort",
    "milestone": "urn:pace:ontology:Milestone",
    "out-port": "urn:pace:ontology:OutputPort",
    "part": "urn:pace:ontology:Part",
    "safety-measure": "urn:pace:ontology:SafetyMeasure",
    "sig-def": "urn:pace:ontology:SignalDefinition",
    "req-stk": "urn:pace:ontology:StakeholderRequirement",
    "triggering-condition": "urn:pace:ontology:TriggeringCondition",
    "interface": "urn:pace:ontology:Interface",
    "interfaceblock": "urn:pace:ontology:InterfaceBlock",
    "variant": "urn:pace:ontology:ProductVariant",
    "activity": "urn:pace:ontology:SoftwareComponent",
    "comp-sw": "urn:pace:ontology:SoftwareComponent",
    "gateway": "urn:pace:ontology:SoftwareComponent",
    "runnable": "urn:pace:ontology:SoftwareComponent",
    "softwarecomponent": "urn:pace:ontology:SoftwareComponent",
    "block": "urn:pace:ontology:Block",
    "scenario": "urn:pace:ontology:Scenario",
    "test-design": "urn:pace:ontology:TestDesign",
    "feature": "urn:pace:ontology:Feature",
    "proxyport": "urn:pace:ontology:ProxyPort",
    "functionality": "urn:pace:ontology:Functionality",
    "sut_profile": "urn:pace:ontology:SutProfile",
    "failure": "urn:pace:ontology:Failure",
    "sec-class": "urn:pace:ontology:SecurityClass",
    "rel-artifact": "urn:pace:ontology:ReleaseArtifact",
    "rl-expectation": "urn:pace:ontology:ReleaseExpectation",
    "test_name": "urn:pace:ontology:TestName",
}

LINK_MAPPINGS = {  # pragma: no cover
    "sec_accepts": "urn:pace:ontology:accepts",
    "allocates": "urn:pace:ontology:allocates",
    "sec_compromise": "urn:pace:ontology:compromises",
    "sec_scen_damage": "urn:pace:ontology:correspondingImpact",
    "sec_misuse": "urn:pace:ontology:correspondingMisuseCase",
    "discusses": "urn:pace:ontology:discusses",
    "isAggregatedBy": "urn:pace:ontology:isAggregatedBy",
    "is_realized_by": "urn:pace:ontology:isRealizedBy",
    "sec_damage": "urn:pace:ontology:leadsTo",
    "prevents": "urn:pace:ontology:prevents",
    "sec_reduces": "urn:pace:ontology:reduces",
    "results": "urn:pace:ontology:resultsIn",
    "sec_mitigates": "urn:pace:ontology:sec_mitigates",
    "sec_transfers": "urn:pace:ontology:transfers",
    "aggregates": "urn:pace:ontology:aggregates",
    "associates": "urn:pace:ontology:associates",
    "sec_scen_threat": "urn:pace:ontology:correspondingThreat",
    "covers": "urn:pace:ontology:covers",
    "deploys": "urn:pace:ontology:deploys",
    "has_in_port": "urn:pace:ontology:hasInPort",
    "has_out_port": "urn:pace:ontology:hasOutPort",
    "has_port": "urn:pace:ontology:hasPort",
    "sut_profile": "urn:pace:ontology:hasSutProfile",
    "instantiates": "urn:pace:ontology:instantiates",
    "it_allocated_tool": "urn:pace:ontology:isAllocatedTo",
    "it_allocated_element": "urn:pace:ontology:isAllocatedTo",
    "test_design": "urn:pace:ontology:isSpecifiedByTestDesign",
    "tool-for-storage": "urn:pace:ontology:isStoredOn",
    "kpi_value": "urn:pace:ontology:kpiValueUsed",
    "mitigates": "urn:pace:ontology:mitigates",
    "monitors": "urn:pace:ontology:monitors",
    "sec_relevance": "urn:pace:ontology:originatesFrom",
    "sec_scenario": "urn:pace:ontology:originatesFrom",
    "resolves": "urn:pace:ontology:resolves",
    "supports": "urn:pace:ontology:supports",
    "scenario_id": "urn:pace:ontology:testsScenario",
    "applies_to_variants": "urn:pace:ontology:appliesToProductVariant",
    "calculates": "urn:pace:ontology:calculates",
    "depends-on": "urn:pace:ontology:dependsOn",
    "includes": "urn:pace:ontology:includes",
    "security-classification": "urn:pace:ontology:isSecurityClassifiedAs",
    "provides": "urn:pace:ontology:provides",
    "requires": "urn:pace:ontology:requires",
    "implements": "urn:pace:ontology:implements",
    "verifies": "urn:pace:ontology:verifies",
    "validates": "urn:pace:ontology:validates",
    "affects": "urn:pace:ontology:affects",
    "composes": "urn:pace:ontology:composes",
    "derives": "urn:pace:ontology:isDerivedFrom",
    "triggered_by": "urn:pace:ontology:isTriggeredBy",
    "contains": "urn:pace:ontology:contains",
    "references": "urn:pace:ontology:references",
    "realizes": "urn:pace:ontology:realizes",
    "belongs": "urn:pace:ontology:belongsTo",
    "cb_allocates": "urn:pace:ontology:cbAllocates",
    "artifact_link": "urn:pace:ontology:artifactLink",
    "use_case_link": "urn:pace:ontology:uses",
    "rl_expectation": "urn:pace:ontology:expects",
    "test_trigger": "urn:pace:ontology:hasTestTrigger",
    "test_platform": "urn:pace:ontology:runsOnTestPlatform",
    "tags": "urn:pace:ontology:tag",
    "validated_by": "urn:pace:ontology:validatedBy",
}

OPTION_MAPPINGS = {  # pragma: no cover
    "artifact-information": "urn:pace:ontology:artifactInformation",
    "compliance_rating": "urn:pace:ontology:complianceRating",
    "data-category": "urn:pace:ontology:dataCategory",
    "ddd_approval_state": "urn:pace:ontology:dddApprovalState",
    "ddd_host": "urn:pace:ontology:dddHost",
    "ddd_system_elements": "urn:pace:ontology:dddSystemElements",
    "default": "urn:pace:ontology:default",
    "deployment_status": "urn:pace:ontology:deploymentStatus",
    "eval_adx_ingest_name": "urn:pace:ontology:evaluationAdxIngestName",
    "eval_adx_input_file": "urn:pace:ontology:evaluationAdxInputFile",
    "failureflow": "urn:pace:ontology:failureFlow",
    "it_context": "urn:pace:ontology:itContext",
    "it_implemented_by": "urn:pace:ontology:itImplementedBy",
    "it_origin": "urn:pace:ontology:itOrigin",
    "it_responsible": "urn:pace:ontology:itResponsible",
    "it_status": "urn:pace:ontology:itStatus",
    "it_test_method": "urn:pace:ontology:itTestMethod",
    "it_type_of_ssl_relevance": "urn:pace:ontology:itTypeOfSslRelevance",
    "it_verification_criteria": "urn:pace:ontology:itVerificationCriteria",
    "max": "urn:pace:ontology:max",
    "min": "urn:pace:ontology:min",
    "necessary_ground_truth": "urn:pace:ontology:necessaryGroundTruth",
    "necessary_signals": "urn:pace:ontology:necessarySignals",
    "ownership_cluster": "urn:pace:ontology:ownership_cluster",
    "ownership_domain": "urn:pace:ontology:ownership_domain",
    "ownership_team": "urn:pace:ontology:ownership_team",
    "retention": "urn:pace:ontology:retention",
    "da_safe_state": "urn:pace:ontology:safeState",
    "security_attack_feasibility_level": "urn:pace:ontology:securityAttackFeasibilityLevel",
    "security_impact_category": "urn:pace:ontology:securityImpactCategory",
    "security_impact_rating": "urn:pace:ontology:securityImpactRating",
    "security_property": "urn:pace:ontology:securityProperty",
    "security_responsible_party": "urn:pace:ontology:securityResponsibleParty",
    "security_risk_level": "urn:pace:ontology:securityRiskLevel",
    "shared-between-companies": "urn:pace:ontology:sharedBetweenCompanies",
    "source": "urn:pace:ontology:source",
    "stages_reference": "urn:pace:ontology:stagesReference",
    "system": "urn:pace:ontology:system",
    "da_timing_requirements": "urn:pace:ontology:timingRequirements",
    "unit": "urn:pace:ontology:unit",
    "da_viper_tsr_sign_category": "urn:pace:ontology:viperRsrSignCategory",
    "da_viper_tsr_country": "urn:pace:ontology:viperTsrCountry",
    "da_viper_tsr_expected_output": "urn:pace:ontology:viperTsrExpectedOutput",
    "da_viper_tsr_sign_type": "urn:pace:ontology:viperTsrSignType",
    "workflow": "urn:pace:ontology:workflow",
    "access-rights": "urn:pace:ontology:accessRights",
    "architecture": "urn:pace:ontology:architecture",
    "archiving": "urn:pace:ontology:archiving",
    "backup-retrieval": "urn:pace:ontology:backupRetrieval",
    "baselining": "urn:pace:ontology:baselining",
    "cb_allocates": "urn:pace:ontology:cbAllocates",
    "cb_architecture": "urn:pace:ontology:cbArchitecture",
    "cb_baseline_id": "urn:pace:ontology:cbBaselineId",
    "cb_baseline_name": "urn:pace:ontology:cbBaselineName",
    "cb_da_safety_integrity": "urn:pace:ontology:cbDaSafetyIntegrity",
    "cb_id": "urn:pace:ontology:cbId",
    "cb_project_id": "urn:pace:ontology:cbProjectId",
    "cb_project_name": "urn:pace:ontology:cbProjectName",
    "cb_requested_from_pe": "urn:pace:ontology:cbRequestedFromPe",
    "cb_status_bosch": "urn:pace:ontology:cbStatsBosch",
    "cb_status_cariad": "urn:pace:ontology:cbStatsCariad",
    "cb_status": "urn:pace:ontology:cbStatus",
    "cb_tracker_id": "urn:pace:ontology:cbTrackerId",
    "cb_tracker_name": "urn:pace:ontology:cbTrackerName",
    "cb_type": "urn:pace:ontology:cbType",
    "cleanup": "urn:pace:ontology:cleanup",
    "completely-developed-externally": "urn:pace:ontology:completelyDevelopedExternally",
    "datafile": "urn:pace:ontology:datafile",
    "dataset": "urn:pace:ontology:dataset",
    "description": "urn:pace:ontology:description",
    "eval_agg_args": "urn:pace:ontology:evaluationAggregationArgument",
    "eval_agg_script": "urn:pace:ontology:evaluationAggregationScript",
    "eval_args": "urn:pace:ontology:evaluationArgument",
    "eval_ref_data": "urn:pace:ontology:evaluationReferenceData",
    "eval_script": "urn:pace:ontology:evaluationScript",
    "failure_indicator": "urn:pace:ontology:failureIndicator",
    "filter_negative": "urn:pace:ontology:filterNegative",
    "filter_positive": "urn:pace:ontology:filterPositive",
    "id": "urn:pace:ontology:id",
    "naming-conventions": "urn:pace:ontology:namingConventions",
    "needs_gpu": "urn:pace:ontology:needsGPU",
    "objectflow": "urn:pace:ontology:objectFlow",
    "recording": "urn:pace:ontology:recording",
    "related-process-work-product": "urn:pace:ontology:relatedProcessWorkProduct",
    "release-note-relevance": "urn:pace:ontology:releaseNoteRelevance",
    "da_safety_logic": "urn:pace:ontology:safetyLogic",
    "scenario_file": "urn:pace:ontology:scenarioFile",
    "signature-approval": "urn:pace:ontology:signatureApproval",
    "storage-location": "urn:pace:ontology:storageLocation",
    "storage-tool": "urn:pace:ontology:storageTool",
    "syscal": "urn:pace:ontology:syscal",
    "tags": "urn:pace:ontology:tag",
    "tdp": "urn:pace:ontology:tdp",
    "title": "urn:pace:ontology:title",
    "tool-version": "urn:pace:ontology:toolVersion",
    "training": "urn:pace:ontology:training",
    "url": "urn:pace:ontology:url",
    "usage-approval": "urn:pace:ontology:usageApproval",
    "variation_sim_file": "urn:pace:ontology:variationSimulationFile",
    "da_verification_criteria": "urn:pace:ontology:verificationCriteria",
    "da_failure_guideword": "urn:pace:ontology:FailureHasGuideword",
    "da_failure_safety_relevance": "urn:pace:ontology:FailurehasSafetyRelevance",
    "configs": "urn:pace:ontology:configs",
    "deployment": "urn:pace:ontology:deployment",
    "build_config": "urn:pace:ontology:hasBuildConfig",
    "eval_tool": "urn:pace:ontology:hasEvaluationTooling",
    "test_trigger": "urn:pace:ontology:hasTestTrigger",
    "issue_id": "urn:pace:ontology:issueId",
    "test_platform": "urn:pace:ontology:runsOnTestPlatform",
    "da_safety_comment": "urn:pace:ontology:safetyComment",
    "da_safety_relevance": "urn:pace:ontology:safetyRelevance",
    "da_security_relevance": "urn:pace:ontology:securityRelevance",
    "variant_restriction": "urn:pace:ontology:variantRestriction",
    "da_verification_method": "urn:pace:ontology:verificationMethod",
    "da_status": "urn:pace:ontology:hasStatus",
    "test_method": "urn:pace:ontology:hasTestMethod",
    "da_safety_integrity": "urn:pace:ontology:safetyIntegrity",
    "tcl": "urn:pace:ontology:tcl",
    "owner": "urn:pace:ontology:owner",
    "gdpr-impact": "urn:pace:ontology:gdprImpact",
    "feature_ticket": "urn:pace:ontology:feature_ticket",
    "test_name": "urn:pace:ontology:test_name",
    "artifact_link": "urn:pace:ontology:artifact_link",
}
