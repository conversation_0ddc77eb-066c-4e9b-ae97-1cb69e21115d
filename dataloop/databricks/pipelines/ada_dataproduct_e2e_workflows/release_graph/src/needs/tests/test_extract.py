"""Unit tests for needs bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
import tarfile
from datetime import datetime
from io import BytesIO
from unittest.mock import MagicMock, patch

import pytest
from artifactory import ArtifactoryException, ArtifactoryPath
from delta import DeltaTable
from extract import NeedsBronzeExtractor
from pyspark.sql import Row, SparkSession
from rddlib.utils import full_qualname, method_self_name
from urllib3.exceptions import ProtocolError

GITHUB_RELEASES_ENDPOINT = "https://api.github.com/repos/PACE-INT/pace/releases?per_page=100"

# Mock test data
TEST_DATE_1 = "2024-01-01T00:00:00Z"
TEST_DATE_2 = "2024-02-01T00:00:00Z"
MOCK_ART_PATH = "https://jfrog.ad-alliance.biz/shared-generic-dev-local/sphinx_needs_json/123/needs.json"
MOCK_NEEDS_DATA = {
    "current_version": "0.1",
    "project": "Mock project documentation",
    "versions": {
        "0.1": {
            "filters": {},
            "filters_amount": 0,
            "needs": {
                "ACTIVITY_1": {
                    "id": "ACTIVITY_1",
                    "deploys_back": ["DEPLOY_1", "DEPLOY_2"],
                    "default": "",
                    "owner": "",
                    "sections": ["SECTION_1"],
                    "verifies": [],
                },
                "ACTIVITY_2": {
                    "id": "ACTIVITY_2",
                    "deploys_back": ["DEPLOY_3", "DEPLOY_4"],
                    "default": "",
                    "owner": "mock_owner",
                    "sections": ["SECTION_2"],
                    "verifies": [],
                },
            },
        }
    },
}

# Fixtures


@pytest.fixture(scope="session")
def spark():
    """Fixture to create a SparkSession for unit tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for Needs Extract").getOrCreate()


@pytest.fixture
def extractor(spark) -> NeedsBronzeExtractor:
    """Fixture to create a NeedsBronzeExtractor instance."""
    extractor = NeedsBronzeExtractor("dev", "release", "release_test", False)
    extractor.spark = spark
    return extractor


@pytest.fixture
def mock_http_client():
    """Fixture to mock the HTTP client used to fetch releases from GitHub."""
    with patch("extract.HttpxClient") as mock:
        # Simulate paginated response
        # First page simulates having a "next" link
        first_page_response = MagicMock()
        first_page_response.json.return_value = [
            {
                "tag_name": "v1.0.0",
                "created_at": TEST_DATE_1,
                "target_commitish": "sha1",
            }
        ]
        first_page_response.raise_for_status.return_value = None
        first_page_response.links = {
            "next": {"url": f"{GITHUB_RELEASES_ENDPOINT}&page=2"},
            "last": {"url": f"{GITHUB_RELEASES_ENDPOINT}&page=2"},
        }

        # Last page simulates no "next" link
        last_page_response = MagicMock()
        last_page_response.json.return_value = [
            {
                "tag_name": "v1.0.1",
                "created_at": TEST_DATE_2,
                "target_commitish": "sha2",
            }
        ]
        last_page_response.raise_for_status.return_value = None
        last_page_response.links = {}

        # Dynamically return response based on the URL
        def get_side_effect(url, *args, **kwargs):
            if url.endswith("page=2"):
                return last_page_response
            else:
                return first_page_response

        mock.return_value.__enter__.return_value.get.side_effect = get_side_effect
        yield mock


@pytest.fixture
def token():
    """Fixture to return a token."""
    return "test_token"


def create_mock_tgz_with_json(json_data):
    """Create a mock .tgz file containing the given JSON data."""
    json_bytes = json.dumps(json_data).encode("utf-8")
    tar_buffer = BytesIO()

    with tarfile.open(fileobj=tar_buffer, mode="w:gz") as tar:
        tarinfo = tarfile.TarInfo(name="all_needs.json")
        tarinfo.size = len(json_bytes)
        tar.addfile(tarinfo, BytesIO(json_bytes))

    tar_buffer.seek(0)
    return tar_buffer.getvalue()


# extract


def test_extract_with_patch(extractor: NeedsBronzeExtractor):
    """Test case for extracting data with patching of fetch and ingest methods."""
    art_path = MagicMock()

    # Convert MOCK_NEEDS_DATA to TGZ
    MOCK_TGZ = create_mock_tgz_with_json(MOCK_NEEDS_DATA)

    with (
        patch.object(*method_self_name(extractor._fetch_from_artifactory), return_value=MOCK_TGZ) as mock_fetch,
        patch.object(*method_self_name(extractor._update_delta_table)) as mock_ingest,
    ):
        # Call the method under test
        extractor.extract("sha", art_path)

        # Assertions
        mock_fetch.assert_called_once()
        mock_ingest.assert_called_once()

    # No need to delete MOCK_TGZ as it is a bytes object, not a file on disk.


def test_extract_with_missing_key_error(extractor: NeedsBronzeExtractor):
    """Test case for handling missing key error during extraction."""
    art_path = MagicMock()
    # Simulate JSON data missing the required 'versions' key
    invalid_data = {"id": "mock_id"}

    # Create a .tgz file containing the invalid JSON data
    MOCK_BYTES = create_mock_tgz_with_json(invalid_data)

    with (
        patch.object(*method_self_name(extractor._fetch_from_artifactory), return_value=MOCK_BYTES),
        patch.object(*method_self_name(extractor.spark.createDataFrame)),
    ):
        with pytest.raises(KeyError):
            extractor.extract("sha", art_path)

    # No need to delete MOCK_BYTES as it is a bytes object, not a file on disk.


# _add_need


def test_add_need(extractor: NeedsBronzeExtractor):
    """Test case for add need method."""
    # Setup input dictionary
    raw_need = {
        "id": "123",
        "name": "Test Need",
        "description": "",  # Empty attribute should be ignored
        "tags": ["urgent", "high-priority"],  # Non-empty list should be added
        "sections": ["section1", "section2"],  # This list should be ignored
        "empty_list": [],  # Empty list should be ignored
        "none_value": None,  # None value should be ignored
    }

    # Expected result
    expected_result = {
        "id": "123",
        "name": "Test Need",
        "tags": [
            "urgent",
            "high-priority",
        ],  # Sections and empty attributes are not included
    }

    # Call the method under test
    result = extractor._add_need(raw_need)

    # Assertions
    assert result == expected_result, "add_need did not process the input as expected."


# _fetch_from_artifactory


def test_fetch_from_artifactory(extractor: NeedsBronzeExtractor):
    """Test case for fetching data from Artifactory."""
    mock_art_path = ArtifactoryPath("mock_art_path", auth=("mock_art_name", "mock_art_key"))
    sample_json_data = {"versions": {"v1.0": {"needs": {"need1": {"id": "1", "description": "Sample need"}}}}}

    # Convert the sample JSON dictionary to a bytes object, encoded as UTF-8
    sample_json_data_bytes = json.dumps(sample_json_data).encode("utf-8")

    # Use BytesIO for simulating the file object returned by open()
    sample_json_data_file = BytesIO(sample_json_data_bytes)

    with patch(full_qualname(ArtifactoryPath.open), return_value=sample_json_data_file) as mock_file:
        # Call the method under test
        data = extractor._fetch_from_artifactory(mock_art_path)

        # Verify the file was opened as expected
        mock_file.assert_called_once()

        # Assertions
        expected_data = sample_json_data_bytes
        assert data == expected_data, "The fetched data did not match the expected output."


def test_fetch_from_artifactory_with_artifactory_exception(extractor: NeedsBronzeExtractor):
    """Test case for fetching data from Artifactory with ArtifactoryException."""
    with patch(full_qualname(ArtifactoryPath.open), side_effect=ArtifactoryException("File not found")):
        mock_art_path = ArtifactoryPath(MOCK_ART_PATH, auth=("mock_art_name", "mock_art_key"))

        # Call the method under test
        with pytest.raises(ArtifactoryException):
            extractor._fetch_from_artifactory(mock_art_path)


@pytest.mark.parametrize(
    "num_errors, expect_error",
    [
        (NeedsBronzeExtractor.MAX_ARTIFACTORY_FETCH_RETRIES - 1, False),
        (NeedsBronzeExtractor.MAX_ARTIFACTORY_FETCH_RETRIES, False),
        (NeedsBronzeExtractor.MAX_ARTIFACTORY_FETCH_RETRIES + 1, True),
    ],
    ids=["in_range_retries_no_error", "max_retries_no_error", "too_many_retries_error"],
)
def test_fetch_from_artifactory_with_retries(extractor: NeedsBronzeExtractor, num_errors: int, expect_error: bool):
    """Test case for fetching data from Artifactory with ArtifactoryException."""
    mock_art_path = ArtifactoryPath("mock_art_path", auth=("mock_art_name", "mock_art_key"))
    sample_json_data = {"versions": {"v1.0": {"needs": {"need1": {"id": "1", "description": "Sample need"}}}}}

    # Convert the sample JSON dictionary to a bytes object, encoded as UTF-8
    sample_json_data_bytes = json.dumps(sample_json_data).encode("utf-8")

    # Use BytesIO for simulating the file object returned by open()
    sample_json_data_file = BytesIO(sample_json_data_bytes)

    open_side_effect = (num_errors * [ProtocolError()]) + [sample_json_data_file]

    with (
        patch(full_qualname(ArtifactoryPath.open), side_effect=open_side_effect),
        patch("extract.NeedsBronzeExtractor.ARTIFACTORY_FETCH_RETRY_WAIT_SEC", 0.0),  # Don't wait during test
    ):
        if expect_error:
            with pytest.raises(ArtifactoryException):
                extractor._fetch_from_artifactory(mock_art_path)
        else:
            result = extractor._fetch_from_artifactory(mock_art_path)
            assert result == sample_json_data_bytes  # Compare with the original byte content


# get_latest_release_tag


def test_get_latest_release_tag_success(extractor: NeedsBronzeExtractor, token: str, mock_http_client):
    """Test case for getting the latest release tag successfully."""
    # Call the method under test
    latest_tag = extractor.get_latest_release_tag(token)

    # Assertions
    expected_tag = {
        "name": "v1.0.1",
        "created_at": datetime.strptime(TEST_DATE_2, "%Y-%m-%dT%H:%M:%SZ"),
        "sha": "sha2",
    }
    assert latest_tag == expected_tag, "The latest tag was not correctly identified."


# get_latest_adx_version


def test_get_latest_adx_version(extractor: NeedsBronzeExtractor, spark: SparkSession):
    """Test case for getting the latest ADX version."""
    mock_result_df = spark.createDataFrame([Row(tableName="test_reports")])
    spark.sql = MagicMock(return_value=mock_result_df)

    # Mock DeltaTable.forName and subsequent DataFrame operations
    with patch(full_qualname(DeltaTable.forName)) as mock_forName:
        mock_delta_df = spark.createDataFrame(
            [
                Row(SutCommitId="sha1", ReportCreationTime=TEST_DATE_1),
                Row(SutCommitId="sha2", ReportCreationTime=TEST_DATE_2),
            ]
        )
        mock_delta_table = MagicMock()
        mock_forName.return_value = mock_delta_table
        mock_delta_table.toDF.return_value = mock_delta_df

        # Call the method under test
        latest_sut_commit_id, date = extractor.get_latest_adx_version()

        # Assertions
        assert latest_sut_commit_id == "sha2", "The latest SUT commit ID does not match."
        assert date == TEST_DATE_2, "The report creation date does not match."
