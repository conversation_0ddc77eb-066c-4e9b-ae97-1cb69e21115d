"""Unit tests for needs silver transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import MagicMock, call, patch

import pytest
from pyspark.errors import AnalysisException
from pyspark.sql import Row, SparkSession
from pyspark.sql.functions import lit
from rddlib import delta_table_utils as dtu
from rddlib.utils import full_qualname
from transform import SILVER_TABLE_EDGES, SILVER_TABLE_NODES, NeedsSilverTransformer

test_date_1 = "2024-03-26"
test_date_2 = "2024-03-27"

mock_needs_mapping = {
    "da_status": "urn:pace:ontology:hasStatus",
    "req-sys": "urn:pace:ontology:SystemRequirement",
    "test_platform": "urn:pace:ontology:runsOnTestPlatform",
    "test_trigger": "urn:pace:ontology:hasTestTrigger",
    "belongs": "urn:pace:ontology:belongsTo",
}

mock_needs_data = [
    Row(
        id="REQ_MOCK_1",
        title="mock_title",
        created_date="2024-03-27",
        version="mock_version",
        source="mock_source",
        type="req-sys",
        da_status="assumed",
        owner="mock_owner",
        ownership_cluster="mock_ownership_cluster",
        ownership_domain="mock_ownership_domain",
        ownership_team="mock_ownership_team",
        section_name="mock_section_name",
        test_platform="mock_platform1, mock_platform2",
        test_trigger="mock_trigger1, mock_trigger2",
        belongs=["FEATURE_MOCK1", "FEATURE_MOCK2"],
        verifies_back=["TEST_MOCK1", "TEST_MOCK2"],
        variant_restriction="mock_variant",
        issue_id="12345",
        docname="docs/path/test/name",
        tcl="mock_tcl",
        deployment="//mock_deployment/deployment",
        cb_baseline_id="mock_cb_baseline_id",
        cb_baseline_name="mock_cb_baseline_name",
        cb_id="mock_cb_id",
        cb_project_id="mock_cb_project_id",
        cb_project_name="mock_cb_project_name",
        cb_status="mock_cb_status",
        cb_status_ada="mock_cb_status_ada",
        cb_status_bosch="mock_cb_status_bosch",
        cb_status_cariad="mock_cb_status_cariad",
        cb_tracker_id="mock_cb_tracker_id",
        cb_tracker_name="mock_cb_tracker_name",
        cb_da_safety_integrity="mock_cb_safety_integrity",
        cb_type="mock_cb_type",
    ),
]

# needs data with missing attributes
mock_needs_data_invalid_1 = [
    Row(
        id="REQ_MOCK_1",
        type="req-sys",
        status="assumed",
    ),
]

# needs data with missing type attribute
mock_needs_data_invalid_2 = [
    Row(
        id="REQ_MOCK_1",
        status="assumed",
    ),
]

mock_rdf_data = """
    @prefix elefanto: <urn:elefanto:> .
    @prefix owl: <http://www.w3.org/2002/07/owl#> .
    @prefix pace: <urn:pace:instance:> .
    @prefix pace-o: <urn:pace:ontology:> .
    @prefix rdf: <http://www.w3.org/1999/02/22-rdf-syntax-ns#> .
    @prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
    @prefix sh: <http://www.w3.org/ns/shacl#> .
    @prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

    pace-o:ActivityGroup a owl:Class,
        sh:NodeShape ;
        rdfs:label "Activity Group" ;
        rdfs:subClassOf pace-o:SoftwareArchitectureElement ;
        pace:needsType "activity-group" ;
        pace-o:package pace-o:ArchitecturePackage .

    pace-o:deploys a owl:ObjectProperty ;
        rdfs:label "deploys" ;
        owl:inverseOf pace-o:isDeployedBy ;
        pace:needsLink "deploys" .

    pace-o:hasStatus a owl:ObjectProperty ;
        rdfs:label "has status" ;
        rdfs:range pace-o:Status ;
        owl:inverseOf pace-o:isStatusOf ;
        pace:needsOption "da_status" .
    """


# Fixtures


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Fixture to provide a Spark session object for unit testing."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for Needs Extract").getOrCreate()


@pytest.fixture
def transformer(spark: SparkSession):
    """Fixture to provide a NeedsSilverTransformer instance for unit testing."""
    transformer = NeedsSilverTransformer(
        "release", "dev_bronze", "dev_silver", "release_test", "art_name", "art_key", False
    )
    transformer.spark = spark
    return transformer


# _transform_nodes


def test_transform_nodes(transformer: NeedsSilverTransformer, spark: SparkSession):
    """Test case for transforming nodes data."""
    df = spark.createDataFrame(mock_needs_data)
    transformed_df = transformer._transform_nodes(df)

    assert (
        "section_name" not in transformed_df.columns
    ), "The 'section_name' column should not be present in the transformed DataFrame."
    assert "mapped_type" in transformed_df.columns, "The 'mapped_type' column is missing in the transformed DataFrame."
    expected_mapped_type_first_row = "urn:pace:ontology:SystemRequirement"
    actual_mapped_type_first_row = transformed_df.collect()[0]["mapped_type"]
    assert actual_mapped_type_first_row == expected_mapped_type_first_row, (
        f"Expected 'mapped_type' to be '{expected_mapped_type_first_row}' "
        f"for the first row, but got '{actual_mapped_type_first_row}'"
    )
    assert transformed_df.collect()[0]["issue_id"] == mock_needs_data[0]["issue_id"]
    assert transformed_df.collect()[0]["owner"] == mock_needs_data[0]["owner"]
    assert transformed_df.collect()[0]["docname"] == mock_needs_data[0]["docname"]
    assert transformed_df.collect()[0]["deployment"] == mock_needs_data[0]["deployment"]
    assert transformed_df.collect()[0]["cb_baseline_name"] == mock_needs_data[0]["cb_baseline_name"]


def test_transform_nodes_invalid_issue_id(transformer: NeedsSilverTransformer, spark: SparkSession):
    """Test case for transforming nodes data with invalid issue_id."""
    df = spark.createDataFrame(mock_needs_data)

    # Modify the DataFrame to have a non-numeric issue_id
    df_with_invalid_issue_id = df.withColumn("issue_id", lit("invalid_id"))

    transformed_df_invalid = transformer._transform_nodes(df_with_invalid_issue_id)

    # Assertions for the transformation with invalid issue_id
    assert transformed_df_invalid.collect()[0]["issue_id"] is None, (
        f"Expected 'issue_id' to be None for invalid issue_id, but got "
        f"'{transformed_df_invalid.collect()[0]['issue_id']}'"
    )


@pytest.mark.parametrize(
    "mock_data",
    [
        mock_needs_data_invalid_1,
        mock_needs_data_invalid_2,
    ],
)
def test_transform_nodes_exceptions(transformer: NeedsSilverTransformer, spark: SparkSession, mock_data):
    """Test case for handling transform exceptions."""
    df = spark.createDataFrame(mock_data)
    with pytest.raises(AnalysisException):
        transformer._transform_nodes(df)


# _transform_edges


def test_transform_edges(transformer: NeedsSilverTransformer, spark: SparkSession):
    """Test case for transforming edges data."""
    df = spark.createDataFrame(mock_needs_data)
    transformed_df = transformer._transform_edges(df)
    assert transformed_df.count() == 6, "Expected number of edges not found"
    expected_mapped_types = {
        "test_platform": "urn:pace:ontology:runsOnTestPlatform",
        "test_trigger": "urn:pace:ontology:hasTestTrigger",
        "belongs": "urn:pace:ontology:belongsTo",
    }

    for row in transformed_df.collect():
        assert row["mapped_type"] == expected_mapped_types[row["type"]], f"Incorrect mapping for type {row['type']}"


# get_latest_bronze_versions


def test_get_latest_bronze_versions_no_silver_table(transformer: NeedsSilverTransformer, spark: SparkSession):
    """Test case for getting the latest bronze data when no silver table exists."""
    transformer.spark.sql = MagicMock()
    bronze_data = spark.createDataFrame([Row(version="mock_sha1"), Row(version="mock_sha2")])
    transformer.spark.sql.return_value = bronze_data

    with patch("rddlib.delta_table_utils.table_exists", return_value=False):
        result = transformer.get_latest_bronze_versions()

    assert result == [
        "mock_sha1",
        "mock_sha2",
    ], "Should return all bronze versions when no silver table exists"


def test_get_latest_bronze_versions_silver_table_exists(transformer: NeedsSilverTransformer, spark: SparkSession):
    """Test case for getting the latest bronze data when a silver table exists."""
    transformer.spark.sql = MagicMock()
    bronze_data = spark.createDataFrame([Row(version="mock_sha1"), Row(version="mock_sha2")])
    silver_data = spark.createDataFrame(
        [
            Row(version="mock_sha1"),
        ]
    )

    transformer.spark.sql.side_effect = [bronze_data, silver_data]

    with patch("rddlib.delta_table_utils.table_exists", return_value=True):
        result = transformer.get_latest_bronze_versions()

    assert result == ["mock_sha2"], "Should return all bronze versions when no silver table exists"


# run


def test_run_success(transformer: NeedsSilverTransformer, spark: SparkSession):
    """Test case for successful execution of the transformation process."""
    transformer._get_bronze_data = MagicMock()
    transformer._transform_nodes = MagicMock()
    transformer._transform_edges = MagicMock()
    transformer.run_quality_check = MagicMock()
    transformer._update_delta_table = MagicMock()

    # Create mock dataframes for nodes and edges transformation
    nodes_df = spark.createDataFrame([Row(id=1, version="mock_sha")])
    edges_df = spark.createDataFrame([Row(start=1, target=2, type="link", version="1.0")])

    transformer._get_bronze_data.return_value = nodes_df
    transformer._transform_edges.return_value = edges_df
    transformer._transform_nodes.return_value = nodes_df

    transformer.run("mock_sha")

    transformer._get_bronze_data.assert_called_once_with('version = "mock_sha"')
    transformer._transform_nodes.assert_called_once_with(nodes_df)
    transformer.run_quality_check.assert_called_once_with(nodes_df, df_type="nodes")
    transformer._transform_edges.assert_called_once_with(nodes_df)
    transformer._update_delta_table.assert_has_calls(
        [
            call(
                SILVER_TABLE_NODES,
                nodes_df,
                "target.version = source.version AND target.id = source.id",
            ),
            call(
                SILVER_TABLE_EDGES,
                edges_df,
                (
                    "target.version = source.version AND "
                    "target.start = source.start AND "
                    "target.target = source.target AND "
                    "target.type = source.type"
                ),
            ),
        ]
    )


def test_run_node_transform_failure(transformer: NeedsSilverTransformer, spark: SparkSession):
    """Test case for failure during node transformation."""
    transformer._get_bronze_data = MagicMock()
    bronze_df = spark.createDataFrame([Row(version="mock_sha")])
    transformer._get_bronze_data.return_value = bronze_df
    transformer._transform_nodes = MagicMock()
    transformer._transform_nodes.side_effect = Exception("node transform error")
