"""This script transforms bronze-level needs data into silver-level needs data.

Needs silver layer is updated with the most recently ingested bronze level data.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from typing import Any

from constants import LINK_MAPPINGS, SILVER_TABLE_DESCRIPTION_MAP, TYPE_MAPPINGS
from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, explode, lit, regexp_replace, split, when
from pyspark.sql.types import ArrayType, StringType, StructField, StructType
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_rdd_secret, quality_check, setup_databricks_logging
from rddlib.dbx_utils import DBX_APP_SCHEMA_MAP, get_dbx_env_catalog

logger = logging.getLogger(__name__)

BRONZE_TABLE = "needs"
SILVER_TABLE_NODES = "needs_nodes"
SILVER_TABLE_EDGES = "needs_edges"

# Properties of interest for the silver layer nodes table.
# Note: These are required properties. Fail if one of these is
# missing in bronze layer.
NODE_PROPERTIES = {
    "release": [
        "id",
        "title",
        "version",
        "source",
        "type",
        "da_status",
        "mapped_type",
        "variant_restriction",
        "issue_id",
        "owner",
        "ownership_team",
        "ownership_cluster",
        "ownership_domain",
        "docname",
        "deployment",
        "tcl",
        "cb_baseline_id",
        "cb_baseline_name",
        "cb_id",
        "cb_project_id",
        "cb_project_name",
        "cb_status",
        "cb_tracker_id",
        "cb_tracker_name",
        "cb_status_bosch",
        "cb_status_cariad",
        "cb_status_ada",
        "cb_type",
        "cb_da_safety_integrity",
    ],
    "rng": [
        "id",
        "title",
        "version",
        "type",
        "da_status",
        "mapped_type",
        "artifact_link",
        "tcl",
        "test_name",
        "it_context",
        "feature_ticket",
    ],
}


class NeedsSilverTransformer:
    """This class is responsible for transforming bronze-level needs data into silver-level."""

    app: str
    bronze_dbx_catalog: str
    silver_dbx_catalog: str
    dbx_schema: str
    art_name: str
    art_key: str
    test_mode: bool

    def __init__(
        self,
        app: str,
        bronze_dbx_catalog: str,
        silver_dbx_catalog: str,
        dbx_schema: str,
        art_name: str,
        art_key: str,
        test_mode: bool,
    ):
        """Initialize a NeedsSilverTransformer instance."""
        self.app = app
        self.bronze_dbx_catalog = bronze_dbx_catalog
        self.silver_dbx_catalog = silver_dbx_catalog
        self.dbx_schema = dbx_schema
        self.art_name = art_name
        self.art_key = art_key
        self.test_mode = test_mode
        self.spark = SparkSession.builder.getOrCreate()

    def _get_missing_mappings(self, df: DataFrame) -> list[Any]:
        """Returns the values which misses a corresponding mapping.

        Args:
            df: Dataframe of interest
        """
        return [row["type"] for row in df.select("type").where(col("mapped_type").isNull()).distinct().collect()]

    def _update_delta_table(self, table: str, df: DataFrame, condition: str) -> None:
        """Update a delta table using merge (upserts).

        Args:
            table (str): The name of the Delta table being updated.
            df (DataFrame): The DataFrame to update the Delta table with.
            condition (str): The merge condition for upserts.
        """
        table = f"{table}_test" if self.test_mode else table
        table_full = f"{self.silver_dbx_catalog}.{self.dbx_schema}.{table}"

        if dtu.table_exists(table_full):
            logger.info(f"Table: {table} already exists")
            # Check if schema is consistent
            if dtu.table_has_schema(table_full, df.schema):
                dtu.merge(df, table_full, condition)
            else:
                logger.info(f"Schema of table '{table_full}' changed. The table will be overwritten.")
                dtu.overwrite(df, table_full, overwrite_schema=True)
        else:
            logger.info(f"Table: {table} does not exist")
            dtu.overwrite(df, table_full)
        # Add metadata to the table
        description = SILVER_TABLE_DESCRIPTION_MAP.get(table, "")
        dtu.update_table_metadata(table_full, description)

    def run_quality_check(self, df: DataFrame, df_type: str) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        full_count_rows = df.count()

        # Missing value checks
        if df_type == "nodes":
            column_to_check = "id"

            missing_value_rows = quality_check.missing_value_detection(df, column=column_to_check)
            missing_value_count = missing_value_rows.count()
            if missing_value_count == 0:
                quality_check.log_quality_check_result(True, "missing_value_detection", None)
            else:
                quality_check.log_quality_check_result(False, "missing_value_detection", missing_value_count)

            # Duplicate check
            _, duplicate_id_rows = quality_check.deduplicate(
                df,
                subset=[column_to_check],
                time_window=None,
                use_hashing=False,
                just_test=False,
            )
            count_duplicate_values = len(duplicate_id_rows)
            if count_duplicate_values == 0:
                quality_check.log_quality_check_result(True, "deduplicate", None)
            else:
                quality_check.log_quality_check_result(
                    False,
                    "deduplicate",
                    "Count of rows that are duplicate: " + f"{count_duplicate_values} out of {full_count_rows}",
                )

            # Check for desired text pattern in the content_id column (e.g., all capital letters and numbers)
            desired_content_id_pattern = "^[A-Z_]+_+[1-9]*$"
            non_compliant_content_ids = quality_check.text_pattern_check(
                df, column_to_check, desired_content_id_pattern
            )
            count_non_matching_pattern = non_compliant_content_ids.count()
            if count_non_matching_pattern == 0:
                quality_check.log_quality_check_result(True, "text_pattern_check", None)
            else:
                quality_check.log_quality_check_result(
                    False, "text_pattern_check", count_non_matching_pattern / full_count_rows
                )

            # Unique value check
            column_to_check = "da_status"
            values_to_match = [
                "draft",
                "implemented",
                "future",
                "proposed",
                "submitted",
            ]

            df_unique_da_status = quality_check.unique_value_validation(
                df,
                column_to_check,
                values_to_match,
            )
            count_non_valid_rows = df_unique_da_status.count()
            if count_non_valid_rows == 0:
                quality_check.log_quality_check_result(True, "unique_value_validation", None)
            else:
                quality_check.log_quality_check_result(
                    False, "unique_value_validation", count_non_valid_rows / full_count_rows
                )
        elif df_type == "edges":
            # Not yet implemented
            logger.info("The data quality assessment for the edges df is not yet implemented")
        else:
            logger.error("Data quality assessment either for df_type = nodes or = edges.")

    def _transform_nodes(self, needs_df: DataFrame) -> DataFrame:
        """Creates dataframe with mapped and cleaned needs nodes.

        Args:
            needs_df: Dataframe with needs data to extract into nodes dataframe

        Returns:
            DataFrame: A dataframe with needs nodes
        """
        # create a mapping dataframe from node_type dictionary, with "type" and "mapped_type"
        needs_df = self._add_mapped_values(needs_df, TYPE_MAPPINGS, "type", "mapped_type")

        needs_df = needs_df.select(NODE_PROPERTIES.get(self.app))

        if self.app == "release":
            # Clean up in issue_id, remove entries that are not only digits
            bad_issues = needs_df.where(col("issue_id").rlike(".*?[^0-9].*")).select("id", "issue_id")

            # Show bad issues
            bad_issues.show(1000, truncate=False)

            if bad_issues.count() > 0:
                bad_issues_list = [
                    (row["id"], row["issue_id"]) for row in bad_issues.select("id", "issue_id").collect()
                ]
                logger.info(f"Nodes with invalid issue_id: {bad_issues_list}")

            # Replace non-digit issue_id with None
            needs_df = needs_df.withColumn(
                "issue_id", when(col("issue_id").rlike(".*?[^0-9].*"), lit(None)).otherwise(col("issue_id"))
            )

        missing_mappings = self._get_missing_mappings(needs_df)
        if len(missing_mappings) == 0:
            quality_check.log_quality_check_result(True, "needs_missing_node_mappings", None)
        else:
            quality_check.log_quality_check_result(False, "needs_missing_node_mappings", missing_mappings)

        return needs_df

    def _transform_edges(self, needs_df: DataFrame) -> DataFrame:
        """Creates dataframe with mapped and cleaned needs edges.

        Args:
            needs_df: Dataframe with needs data to extract into edges table
        Returns:
            DataFrame: A dataframe with needs edges
        """
        edges_schema = StructType(
            [
                StructField("start", StringType(), True),
                StructField("target", StringType(), True),
                StructField("type", StringType(), True),
                StructField("mapped_type", StringType(), True),
                StructField("version", StringType(), True),
            ]
        )

        # Creating an empty DataFrame.
        edges_df = self.spark.createDataFrame([], edges_schema)

        # remove whitespaces and split string into array, eg. "SiL, SoL" -> ["SiL", "SoL"]
        if self.app == "release":
            needs_df = needs_df.withColumn(
                "test_platform",
                split(regexp_replace(col("test_platform"), " ", ""), ","),
            )
            needs_df = needs_df.withColumn("test_trigger", split(regexp_replace(col("test_trigger"), " ", ""), ","))

        schema = needs_df.schema

        for field in schema:
            column_name = field.name
            data_type = field.dataType
            if isinstance(data_type, ArrayType) and not column_name.endswith("_back"):
                edge = needs_df.select(needs_df.id, explode(needs_df[column_name]))
                # will find for example "derives" and "explode" target to create dataframe
                edge = edge.dropDuplicates()
                edge = (
                    edge.join(needs_df.select("id", "version"), "id")
                    .withColumnRenamed("id", "start")
                    .withColumnRenamed("col", "target")
                )
                edge = edge.withColumn("type", lit(column_name))

                # Map edge type
                edge = self._add_mapped_values(edge, LINK_MAPPINGS, "type", "mapped_type")

                # Add edge to total edges
                edges_df = edges_df.unionByName(edge)

        missing_mappings = self._get_missing_mappings(edges_df)
        if len(missing_mappings) == 0:
            quality_check.log_quality_check_result(True, "needs_missing_edge_mappings", None)
        else:
            quality_check.log_quality_check_result(False, "needs_missing_edge_mappings", missing_mappings)

        return edges_df

    def _add_mapped_values(self, df: DataFrame, mappings: dict[str, Any], key: str, mapped_key: str) -> DataFrame:
        """Add mapped values to a DataFrame based on a mapping dictionary.

        Args:
            df (DataFrame): The DataFrame to add mapped values to.
            mappings (dict): The mapping dictionary.
            key (str): The key column name.
            mapped_key (str): The mapped key column name.

        Returns:
            DataFrame: DataFrame with added mapped values.
        """
        # create a mapping dataframe from node_type dictionary, with "key" and "mapped_key"
        mapping_df = self.spark.createDataFrame(list(mappings.items()), [key, mapped_key])

        # join and map the values
        return df.join(mapping_df, key, "left_outer")

    def _get_bronze_data(self, filter: str) -> DataFrame | None:
        """Read needs bronze data for the specified application and column filter.

        filter: eg. f'version = "adb123dacd456"'

        Returns:
            DataFrame: A DataFrame containing the corresponding needs bronze data.
        """
        bronze_table_name = f"{BRONZE_TABLE}_test" if self.test_mode else BRONZE_TABLE
        bronze_table_full_name = f"{self.bronze_dbx_catalog}.{self.dbx_schema}.{bronze_table_name}"

        if not dtu.table_exists(bronze_table_full_name):
            raise Exception("No bronze table exists!")

        delta_table = DeltaTable.forName(self.spark, bronze_table_full_name)
        df = delta_table.toDF()
        df = df.where(filter)
        if df.count() == 0:
            return None

        return df

    def run(self, version: str) -> None:
        """Run the transformation process for the given version.

        Args:
            version (str): The version of the bronze data to transform.
        """
        bronze_df = self._get_bronze_data(f'version = "{version}"')
        nodes_df = self._transform_nodes(bronze_df)
        self.run_quality_check(nodes_df, df_type="nodes")

        edges_df = self._transform_edges(bronze_df)

        NODES_MERGE_CONDITION = "target.version = source.version AND target.id = source.id"
        self._update_delta_table(SILVER_TABLE_NODES, nodes_df, NODES_MERGE_CONDITION)

        EDGES_MERGE_CONDITION = (
            "target.version = source.version AND "
            + "target.start = source.start AND "
            + "target.target = source.target AND "
            + "target.type = source.type"
        )
        self._update_delta_table(SILVER_TABLE_EDGES, edges_df, EDGES_MERGE_CONDITION)

    def get_latest_bronze_versions(self) -> list[str]:
        """Read the latest needs bronze versions for the specified application."""
        bronze_table = f"{BRONZE_TABLE}_test" if self.test_mode else BRONZE_TABLE
        bronze_table_full = f"{self.bronze_dbx_catalog}.{self.dbx_schema}.{bronze_table}"
        silver_nodes_table = f"{SILVER_TABLE_NODES}_test" if self.test_mode else SILVER_TABLE_NODES
        silver_nodes_table_full = f"{self.silver_dbx_catalog}.{self.dbx_schema}.{silver_nodes_table}"

        bronze_versions = self.spark.sql(f"SELECT DISTINCT version FROM {bronze_table_full}")

        # If silver table does not already exists, use all versions in bronze_table
        if not dtu.table_exists(silver_nodes_table_full):
            return [row.version for row in bronze_versions.select("version").collect()]

        silver_versions = self.spark.sql(f"SELECT DISTINCT version FROM {silver_nodes_table_full}")

        # Anti-join will return the values in bronze_versions which are not in silver_versions
        new_versions = bronze_versions.join(silver_versions, on=["version"], how="left_anti")
        return [row.version for row in new_versions.select("version").collect()]


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Needs Silver Layer Transform, precedence: latest, version, date")
    parser.add_argument("-a", "--application", dest="app", help="Application: ex - release, rng")
    parser.add_argument("-v", "--version", help="Bronze needs version (commit hash, tag)")
    parser.add_argument(
        "-t",
        "--task_output",
        help="Get version from task value, specify which task name",
    )
    parser.add_argument("-r", "--run_id", dest="run_id")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    args, unknown = parser.parse_known_args()
    version = args.version
    app = args.app
    task_output = args.task_output
    test_mode = args.test_mode

    catalog_bronze = get_dbx_env_catalog("bronze")
    catalog_silver = get_dbx_env_catalog("silver")
    schema = DBX_APP_SCHEMA_MAP[app]

    # Setup logging
    setup_databricks_logging(FullSchemaName(catalog_silver, schema, False), "needs/silver", run_id=args.run_id)

    art_name = get_rdd_secret("alliance-artifactory-username")
    art_key = get_rdd_secret("alliance-artifactory-token")

    transformer = NeedsSilverTransformer(app, catalog_bronze, catalog_silver, schema, art_name, art_key, test_mode)

    versions: list[str]
    if version:
        versions = [version]
    elif task_output:
        from pyspark.dbutils import DBUtils

        versions = [DBUtils(transformer.spark).jobs.taskValues.get(taskKey=args.task_output, key="version")]
    else:
        versions = transformer.get_latest_bronze_versions()

    for version in versions:
        logger.info(f"Transforming needs data for version: {version}")
        transformer.run(version)
