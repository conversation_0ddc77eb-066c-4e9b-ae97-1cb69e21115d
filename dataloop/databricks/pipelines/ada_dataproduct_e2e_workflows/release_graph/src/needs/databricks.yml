bundle:
  name: ada_dataproduct_e2e_needs

include:
  # - ../../../../common/rdd_orchestration/env_targets.yml
  # TODO: Remove as soon as VSCode databricks extension is able to correctly import above
  - ../common/env_targets.yml
  - workflows/*.yml

variables:
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E8ads_v5_rt14.3"
  driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"

# TODO: Remove when include supported by VSCode extension
targets:
  qa:
    variables:
      # Nightly
      nightly_schedule: "0 0 3 ? * Wed"
      nightly_trigger: "PAUSED"

  prod:
    variables:
      # Nightly
      nightly_schedule: "0 0 3 * * ?"
      nightly_trigger: "UNPAUSED"
