"""E2E tests for Needs ETL Pipeline."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

# Standard imports
import argparse

import stardog

# 3rd party library imports
from rddlib import get_rdd_secret, get_stardog_access_token
from requests.exceptions import ConnectTimeout, MissingSchema
from stardog.exceptions import StardogException, TransactionException


def connect_to_stardog(db_name: str):
    """Set up the connection to Stardog."""
    conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }

    with stardog.Admin(**conn_details) as admin:
        if not any(db.name == db_name for db in admin.databases()):
            raise Exception(f"Database '{db_name}' does not exist in Stardog.")

    return stardog.Connection(db_name, **conn_details)


def run_query(conn, query: str) -> list | None:
    """Execute a SPARQL query and return the results."""
    try:
        response = conn.select(query)
        return response["results"]["bindings"]
    except (TransactionException, MissingSchema, ConnectTimeout, StardogException) as e:
        print(f"Could not execute query. Error: {str(e)}")
        return None


def test_list_system_requirements(conn):
    """Test listing all system requirements."""
    query = """
        SELECT ?systemReq ?label
        WHERE {
            ?systemReq a pace:SystemRequirement .
            ?systemReq rdfs:label ?label .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No system requirements found."
    print(f"Test passed: {len(results)} system requirements listed.")


def test_list_software_requirements_with_statuses(conn):
    """Test listing all software requirements with their statuses."""
    query = """
        SELECT ?softwareReq ?label ?status
        WHERE {
            ?softwareReq a pace:SoftwareRequirement .
            ?softwareReq rdfs:label ?label .
            ?softwareReq pace:da_status ?status .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No software requirements found with statuses."
    print(f"Test passed: {len(results)} software requirements with statuses listed.")


def test_retrieve_features_and_tags(conn):
    """Test retrieving all features and their related tags."""
    query = """
        SELECT ?feature ?featureLabel ?tagLabel
        WHERE {
            ?feature a pace:Feature .
            ?feature rdfs:label ?featureLabel .
            ?feature pace:tag ?tag .
            ?tag rdfs:label ?tagLabel .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No features and tags found."
    print(f"Test passed: {len(results)} features and tags retrieved.")


def test_requirements_for_product_variant(conn):
    """Test getting all requirements belonging to a specific product variant."""
    query = """
        SELECT ?requirement ?label
        WHERE {
            {
                SELECT DISTINCT ?variant
                WHERE {
                    ?requirement pace:appliesToProductVariant ?variant .
                }
                LIMIT 1
            }
            ?requirement pace:appliesToProductVariant ?variant .
            ?requirement rdfs:label ?label .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No requirements found for the specified product variant."
    print(f"Test passed: {len(results)} requirements found for product variant.")


def test_list_artifacts_with_versions_and_docnames(conn):
    """Test listing all artifacts with their version and associated document name."""
    query = """
        SELECT ?artifact ?version ?docname
        WHERE {
            ?artifact ^pace:validFor ?version ;
                pace:docname ?docname .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No artifacts found with versions and document names."
    print(f"Test passed: {len(results)} artifacts with versions and document names listed.")


def test_count_distinct_tags(conn):
    """Test counting the total number of distinct tags."""
    query = """
        SELECT (COUNT(DISTINCT ?tag) AS ?distinctTagCount)
        WHERE {
            ?element pace:tag ?tag .
        }
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No distinct tags found."
    distinct_tag_count = int(results[0]["distinctTagCount"]["value"])
    assert distinct_tag_count > 0, "The number of distinct tags should be greater than zero."
    print(f"Test passed: {distinct_tag_count} distinct tags counted.")


def test_retrieve_distinct_test_platforms(conn):
    """Test retrieving all distinct test platforms."""
    query = """
        SELECT DISTINCT ?testPlatform
        WHERE {
            ?element pace:runsOnTestPlatform ?testPlatform .
        }
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No distinct test platforms found."
    print(f"Test passed: {len(results)} distinct test platforms retrieved.")


def test_retrieve_distinct_test_triggers(conn):
    """Test retrieving all distinct test triggers."""
    query = """
        SELECT DISTINCT ?testTestTrigger
        WHERE {
            ?element pace:hasTestTrigger ?testTestTrigger .
        }
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No distinct test triggers found."
    print(f"Test passed: {len(results)} distinct test triggers retrieved.")


def main(db: str) -> None:
    """Main function to run all tests."""
    conn = connect_to_stardog(db)
    if conn is None:
        raise Exception("Stardog connection error")

    try:
        test_list_system_requirements(conn)
        test_list_software_requirements_with_statuses(conn)
        test_retrieve_features_and_tags(conn)
        test_requirements_for_product_variant(conn)
        test_list_artifacts_with_versions_and_docnames(conn)
        test_count_distinct_tags(conn)
        test_retrieve_distinct_test_platforms(conn)
        test_retrieve_distinct_test_triggers(conn)
    finally:
        conn.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="E2E Test for the Needs Pipeline")
    parser.add_argument("-d", "--db", dest="db", help="Stardog database")
    args, unknown = parser.parse_known_args()
    main(args.db)
