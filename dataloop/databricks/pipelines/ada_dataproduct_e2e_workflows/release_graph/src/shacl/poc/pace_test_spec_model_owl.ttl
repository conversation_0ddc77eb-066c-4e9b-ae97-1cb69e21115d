PREFIX ado: <urn:pace:ontology:ado:>
PREFIX adx: <urn:pace:ontology:adx:>
PREFIX jira: <urn:pace:ontology:jira:>
PREFIX pace: <urn:pace:ontology:>
PREFIX pace0: <urn:pace:>
PREFIX shacl: <http://www.w3.org/ns/shacl#>
PREFIX x_org_xc_adda_eng_pace: <urn:x-evn-master:x_org_xc_adda_eng_pace:>

Class ado:Bug "Bug" extends ado:Workitem
	ado:hasSeverity "has severity" xsd:string

Class ado:Decision "Decision" extends ado:Workitem

Class ado:Impediment "Impediment" extends ado:Workitem

Class ado:KeyResult "Key Result" extends ado:Workitem

Class ado:KeyResultValue "Key Result Value"
	ado:currentValue "has current value" xsd:string
	ado:updatedAt "current value updated at" xsd:dateTime

Class ado:Objective "Objective" extends ado:Workitem

Class ado:RequestForFeature "Request for Feature" extends ado:Workitem

Class ado:Risk "Risk" extends ado:Workitem

Class ado:TeamEpic "Team Epic" extends ado:Workitem

Class ado:UserStory "User Story" extends ado:Workitem

Class ado:Workitem "Work Item" extends pace:Artifact
	ado:hasActivatedDate "has activated date" xsd:dateTime
	ado:hasAreaPath "has area path" xsd:string
	ado:hasChangeDate "has change date" xsd:dateTime
	ado:hasChild
	ado:hasClosedDate "has closed date" xsd:dateTime
	ado:hasCreatedDate "has created date" xsd:dateTime
	ado:hasId "has id" xsd:string
	ado:hasIteration "has iteration" xsd:string
	ado:hasKeyResultDataSource "has key result data source" xsd:string
	ado:hasKeyResultTargetValue "has key result target value" xsd:string
	ado:hasKeyResultType "has key result type - less than, greater than etc" xsd:string
	ado:hasKeyResultUnit "has key result unit" xsd:string
	ado:hasKeyResultValue "has key result value" ado:KeyResultValue
	ado:hasParent
	ado:hasPullRequest
	ado:hasReason "has reason"
	ado:hasRelated
	ado:hasStatus xsd:string
	ado:hasTags "has tags" xsd:string
	ado:hasTitle "has title" xsd:string
	ado:needsLink
	ado:wasCreatedFor

Class ado:WorkitemState extends pace:Artifact-status

Class adx:Report extends pace:Artifact
	adx:hasTestCase pace:Artifact
	adx:preset xsd:string
	adx:reportCreationTime xsd:dateTime
	adx:runDuration xsd:double
	adx:runId xsd:string
	adx:testDescription xsd:string
	adx:testDuration xsd:double
	adx:testName xsd:string
	adx:testPath xsd:string
	adx:testResultsExpected xsd:double
	adx:testResultsKpiValue xsd:double
	adx:testResultsLowerThreshold xsd:double
	adx:testResultsOperator xsd:string
	adx:testResultsPassed xsd:boolean
	adx:testResultsThreshold xsd:double
	adx:testResultsUpperThreshold xsd:double

Class adx:ScaComponent extends pace:Artifact
	adx:hasBuildIssuesCount xsd:int
	adx:hasBuildPath xsd:string
	adx:hasCodeOwners xsd:string
	adx:hasDeployments xsd:string
	adx:hasDoxygenCoverage xsd:double
	adx:hasLabel xsd:string
	adx:hasName xsd:string
	adx:hasRevision xsd:string
	adx:hasRule xsd:string
	adx:hasScaIssuesCountBySeverity1 xsd:int
	adx:hasScaIssuesCountBySeverity2 xsd:int
	adx:hasScaIssuesCountBySeverity3 xsd:int
	adx:hasScaIssuesCountBySeverity4 xsd:int
	adx:hasScaIssuesCountBySeverity5 xsd:int
	adx:hasSourcePath xsd:string
	adx:hasTags xsd:string
	adx:lastChanged xsd:dateTime

Class jira:Activity extends jira:Issue

Class jira:Bug extends jira:Issue

Class jira:Bugfix extends jira:Issue

Class jira:ChangeRequest extends jira:Issue

Class jira:Epic extends jira:Issue

Class jira:Hardware extends jira:Issue

Class jira:Impediment extends jira:Issue

Class jira:Issue extends x_org_xc_adda_eng_pace:Artifact
	jira:hasBranchName "has Branch Name" xsd:string
	jira:hasComponent "has component" xsd:string
	jira:hasCreatedDate "has created date" xsd:dateTime
	jira:hasDescription "has description" xsd:string
	jira:hasFixVersion "has fix version" xsd:string
	jira:hasId "has id" xsd:string
	jira:hasIssueType "has issue type" xsd:string
	jira:hasKey "has key" xsd:string
	jira:hasLabel "has label" xsd:string
	jira:hasLink "has link" xsd:string
	jira:hasProblemType "has problem type" xsd:string
	jira:hasResolution "has resolution" xsd:string
	jira:hasSafetyRelevance "has safety relevance" xsd:string
	jira:hasSeverity "has severity" xsd:string
	jira:hasStatus "has status" jira:IssueState
	jira:hasSummary "has summary" xsd:string
	jira:hasTeamName "has Team Name" xsd:string
	jira:hasUpdatedDate "has updated date" xsd:dateTime

Class jira:IssueState extends x_org_xc_adda_eng_pace:ArtifactState

Class jira:KeyResult extends jira:Issue

Class jira:Objective extends jira:Issue

Class jira:OpenItem extends jira:Issue

Class jira:Problem extends jira:Issue

Class jira:ProjectEpic extends jira:Issue

Class jira:ProjectFeature extends jira:Issue

Class jira:Risk extends jira:Issue

Class jira:Story extends jira:Issue

Class jira:Subtask extends jira:Issue

Class jira:Task extends jira:Issue

Class owl:Ontology
	# Links a resource with its namespace prefix declarations.
	shacl:declare "declare" shacl:PrefixDeclaration
	# An entailment regime that indicates what kind of inferencing is required by a shapes graph.
	shacl:entailment "entailment" rdfs:Resource
	# Shapes graphs that should be used when validating this data graph.
	shacl:shapesGraph "shapes graph" owl:Ontology
	# Suggested shapes graphs for this ontology. The values of this property may be used in the absence of specific sh:shapesGraph statements.
	shacl:suggestedShapesGraph "suggested shapes graph" owl:Ontology

Class pace0:ontologyArtifact
	pace0:ontologyvalidFor "is valid for" pace0:ontologyArtifactVersion

# The github sha that is used to idenfify the version for each artifact
Class pace0:ontologyArtifactVersion "Version" extends pace:Artifact

Class pace:ActivityGroup "Activity Group" extends pace:SoftwareArchitectureElement

Class pace:AgreedRequirement "Agreed Requirement" extends pace:Requirement

Class pace:ArchEECU "ArchE ECU" extends pace:PhysicalArchitectureElement

Class pace:ArchEPartition "ArchE Partition" extends pace:PhysicalArchitectureElement

Class pace:ArchESOC "ArchE SOC" extends pace:PhysicalArchitectureElement

Class pace:ArchitectureElement "Architecture Element" extends pace:Artifact

Class pace:Artifact "Artifact"
	pace:hasArtifactStatus "has artifact status" pace:ArtifactStatus

Class pace:Artifact-status

# Status of the concrete artifact (e.g., requirement status, or test case status)
# https://pages.github.boschdevcloud.com/Half-Dome/colibry-builds/methods/artefact-status/status_model.html#generic-status-net
Class pace:ArtifactStatus "Artifact Status" extends pace:Status
	pace:isArtifactStatusOf "is artifact status of" pace:Artifact

# The github sha that is used to idenfify the version for each artifact
Class pace:ArtifactVersion "Version" extends pace:Artifact
	pace:githubcreatedAt "created at" xsd:dateTime
	pace:githubhasCommitMessage "has commit message" xsd:string
	pace:githubhasCommitSha "has commit sha" xsd:string
	pace:githubhasCommitUrl "has commit url" xsd:anyURI
	pace:githubhasParent "has parent" pace:ArtifactVersion

Class pace:ArtifactWithStatus "Artifact" extends pace:Artifact
	pace:hasTestStatus "has test status" pace:TestStatus

Class pace:AssumedRequirement "Assumed Requirement" extends pace:Requirement

Class pace:Assumption "Assumption" extends pace:ArchitectureElement

Class pace:Block "Block" extends pace:LogicalArchitectureElement

# The available build configs
Class pace:BuildConfig "BuildConfig"
	pace:isBuildConfigOf "is build_config of" pace:SutProfile

Class pace:ConfigurationManagementItem "CM-Item" extends pace:ArtifactWithStatus

Class pace:DatabaseUseCase "Use Case" extends pace:ArtifactWithStatus

Class pace:Definition "Definition" extends pace:ArtifactWithStatus

Class pace:Deploy "Deploy" extends pace:SoftwareArchitectureElement

Class pace:DesignDecisionDocument "Design Decision Document" extends pace:ArtifactWithStatus

Class pace:DevelopmentSection "Development Section" extends pace:ArtifactWithStatus

# Describes a set of equivalence classes for a single input, created by equivalence class partitioning method.
Class pace:EquivalenceClasses "Eq Classes" extends pace:Artifact

Class pace:ErrorPropagationModel "ErrorPropagationModel" extends pace:ArtifactWithStatus

# Evaluation Toolings supported by Test Collector
Class pace:EvaluationTooling "evaluation tooling" extends pace:ArtifactWithStatus
	pace:isEvaluationToolingOf "is evaluation tooling of"

Class pace:Failure "Failure" extends pace:ArtifactWithStatus
	pace:hasFailureSafetyRelevance "has failure safetyrelevance" pace:FailureSafetyRelevance

Class pace:FailureModelView "FailureModelView" extends pace:ArtifactWithStatus

# SafetyRelevance of the Failure to define the necessary process steps
Class pace:FailureSafetyRelevance "Failure SafetyRelevance" extends pace:SafetyRelevance
	pace:FailurehasSafetyRelevance "has safety relevance status" pace:FailureSafetyRelevance
	pace:isFailureSafetyRelevanceOf "is failure safetyrelevance of" pace:Failure

Class pace:Feature "Feature" extends pace:ArtifactWithStatus

Class pace:FunctionalArchitectureElement "Functional Architecture Element" extends pace:ArchitectureElement

Class pace:FunctionalChain "Functional Chain" extends pace:FunctionalArchitectureElement

Class pace:Functionality "Functionality" extends pace:FunctionalArchitectureElement

Class pace:Gateway

# Guideword which was applied to identify the Failure
Class pace:Guideword "Guideword"
	pace:FailureHasGuideword "has guideword" pace:Guideword

Class pace:Hazard "Hazard" extends pace:ArtifactWithStatus

Class pace:HazardousBehaviour "Hazardous Behaviour" extends pace:ArtifactWithStatus

Class pace:HazardousEvent "Hazardous Event" extends pace:ArtifactWithStatus

Class pace:InGateway "In Gateway" extends pace:Gateway

Class pace:InputPort "Input Port" extends pace:SoftwareArchitectureElement

Class pace:Interface "Interface" extends pace:SoftwareArchitectureElement

Class pace:InterfaceBlock "Interface Block" extends pace:LogicalArchitectureElement

Class pace:InternallyDevelopedToolRequirement "Internally Developed Tool Requirement" extends pace:ArtifactWithStatus

Class pace:InternallyDevelopedToolSystemRequirement "Internally Developed Tool System Requirement" extends pace:ArtifactWithStatus

# Test specification for all kpi evaluations
Class pace:KeyPerformanceIndicator "TestKpi" extends pace:TestSpec

Class pace:KeyPerformanceIndicatorMetrics "Key Performance Indicator Metrics" extends pace:ArtifactWithStatus

Class pace:LogicalArchitectureElement "Logical Architecture Element" extends pace:ArchitectureElement

Class pace:Milestone "Milestone" extends pace:ArtifactWithStatus

Class pace:NeedsPackage "pace-o:Package"

Class pace:NonProductionRequirement "Non-Production Requirement" extends pace:Requirement

Class pace:OperationalSituation "Operational Situation" extends pace:ArtifactWithStatus

Class pace:OutGateway "Out Gateway" extends pace:Gateway

Class pace:OutputPort "Output Port" extends pace:SoftwareArchitectureElement

Class pace:Package "Package" extends pace:LogicalArchitectureElement

Class pace:Parameter "Parameter" extends pace:ArtifactWithStatus

Class pace:Part "Part" extends pace:LogicalArchitectureElement

Class pace:PhysicalArchitectureElement "Physical Architecture Element" extends pace:ArchitectureElement

Class pace:ProcessArea "Process Area" extends pace:ArtifactWithStatus

Class pace:ProcessLandscape "Process Landscape" extends pace:ArtifactWithStatus

Class pace:ProcessModule "Process Module" extends pace:ArtifactWithStatus

Class pace:ProductVariant "Product Variant" extends pace:Artifact

Class pace:ProxyPort "Proxy Port" extends pace:LogicalArchitectureElement

Class pace:Requirement "Requirement" extends pace:ArtifactWithStatus

Class pace:SafetyGoal "Safety Goal" extends pace:StakeholderRequirement

Class pace:SafetyMeasure "SafetyMeasure" extends pace:ArtifactWithStatus

Class pace:SafetyRelevance

# Element to define scenarios for simulation / recompute in
Class pace:Scenario "Scenario" extends pace:Artifact
	pace:scenarioRunsOnTestPlatform "scenario runs on test platform" pace:TestPlatform

Class pace:SecurityAsset "Security Asset" extends pace:ArtifactWithStatus

Class pace:SecurityAssumption "Security Assumption" extends pace:ArtifactWithStatus

Class pace:SecurityClaim "Security Claim" extends pace:ArtifactWithStatus

# the security class is relevant to indicate Availability, Integrity, and Confidentiality of a CM-Item or Tool.
Class pace:SecurityClass "Security Class"

Class pace:SecurityControl "Security Control" extends pace:ArtifactWithStatus

Class pace:SecurityDamageScenario "Security Damage Scenario" extends pace:ArtifactWithStatus

Class pace:SecurityGoal "Security Goal" extends pace:ArtifactWithStatus

Class pace:SecurityMisuseCase "Security Misuse Case" extends pace:ArtifactWithStatus

Class pace:SecurityResidualRisk "Security Residual Risk" extends pace:ArtifactWithStatus

Class pace:SecurityRisk "Security Risk" extends pace:ArtifactWithStatus

Class pace:SecurityThreat "Security Threat" extends pace:ArtifactWithStatus

Class pace:SecurityThreatScenario "Security Threat Scenario" extends pace:ArtifactWithStatus

Class pace:SignalDefinition "Signal Definition" extends pace:ArtifactWithStatus

Class pace:SoftwareArchitectureElement "Software Architecture Element" extends pace:ArchitectureElement

Class pace:SoftwareComponent "Software Component" extends pace:SoftwareArchitectureElement

Class pace:SoftwareRequirement "Software Requirement" extends pace:Requirement

Class pace:SolutionConcept "Solution concept" extends pace:ArchitectureElement

Class pace:StakeholderRequirement "Stakeholder Requirement"

Class pace:Status "Status"
	# Is preceding status.
	pace:hasNextStatus "has next status" pace:Status
	pace:hasPreviousStatus "has previous status" pace:Status
	pace:isStatusOf "is status of"

# Profile for software under test related properties
Class pace:SutProfile "SUT Profile" extends pace:ArtifactWithStatus
	pace:hasBuildConfig "has build_config" pace:BuildConfig
	pace:isSutProfileOf "is SuT profile of"
	pace:sutProfileRunsOnTestPlatform "SuT profile runs on test platform" pace:TestPlatform

Class pace:SystemRequirement "System Requirement" extends pace:Requirement

# Describes the approach when verifying a test object (e.g. requirement) by a test method.
# One test object or a small set of test objects need at least one test design element to cover all required test methods.
# The required test methods are given by the test plan. It is recommended to use one test design element for each test method.
# The resulting test cases are described by the linked test-sw/test-swint/test-sys/test-sysint elements.
Class pace:TestDesign "Test Design" extends pace:ArtifactWithStatus
	pace:hasTestMethod "has test method(s)" pace:TestMethod

# Possible test methods of a test-design
Class pace:TestMethod "Test Method"
	pace:testMethodIsUsedBy "test method is used by" pace:TestDesign

# Test Platform defines the different test approaches we use in PACE
Class pace:TestPlatform "Test Platform" extends pace:ArtifactWithStatus
	pace:runsScenario "runs scenario" pace:Scenario
	pace:runsSutProfile "runs test profile" pace:SutProfile

# Base class of test spec
Class pace:TestSpec "TestSpec" extends pace:Artifact

Class pace:TestSpecWithIssueId extends pace:TestSpec

# Status of the test spec
Class pace:TestStatus "Test Spec Status" extends pace:Status
	pace:isTestStatusOf "is test status of" pace:ArtifactWithStatus

# Test specification for all (non tool) tests
Class pace:TestSw "Test-Spec SW" extends pace:TestSpec

# Test specification for all (non tool) tests
Class pace:TestSwInt "Test-Spec SwInt" extends pace:TestSpec

# Test specification for all (non tool) tests
Class pace:TestSys "Test-Spec Sys" extends pace:TestSpec

# Test specification for all (non tool) tests
Class pace:TestSysInt "Test-Spec SysInt" extends pace:TestSpec

# Define the trigger when the test shall be executed
Class pace:TestTrigger "Test Trigger"
	pace:isTestTriggerOf "is test trigger of"

Class pace:Tool "Tool" extends pace:ArtifactWithStatus

Class pace:TriggeringCondition "Triggering Condition" extends pace:ArtifactWithStatus

Class pace:UseCase "Use Case" extends pace:ArtifactWithStatus

Class pace:VerificationMethod "Verification Method"

Class pace:gdprImpact "GDPR impact"

# If a pull request is merged is has a merged_sha which is represented as Artifact Versions from the artifact metamodel.
Class pace:githubPullRequest "Github pull request" extends pace:Artifact
	pace:githubclosedAt "closed at" xsd:dateTime
	pace:githubcreatedAt "created at" xsd:dateTime
	pace:githubhasHTMLUrl "has url" xsd:anyURI
	pace:githubhasMergeSha "has merge sha" pace:ArtifactVersion
	pace:githubhasMilestone "has milestone" xsd:string
	pace:githubhasNumber "has number" xsd:int
	pace:githubhasStatus "has status" pace:githubPullRequestStatus
	pace:githubhasTitle "has title" xsd:string
	pace:githubisDraft "is in draft state" xsd:boolean
	pace:githubisLocked "is in locked state" xsd:boolean
	pace:githubmergedAt "merged at" xsd:dateTime

Class pace:githubPullRequestStatus "Pull Request Status" extends pace:ArtifactStatus

# A tag is a selected commit that has some additional info (message and tag naming). With tags special sw versions (e.g. for releases) can be identified
Class pace:githubTag "Github tag" extends pace:Artifact
	pace:githubhasId "has node id" xsd:string
	pace:githubhasName "has name" xsd:string
	pace:githubhasNodeId "has node id" xsd:string
	pace:githubhasTagMessage "has message" xsd:string
	pace:githubhasTagName "has tag name" xsd:string
	pace:githubhasTagUrl "has tag url" xsd:anyURI
	pace:githubhasTarBallUrl "has tar ball url" xsd:anyURI
	pace:githubhasZipBallUrl "has zip ball url" xsd:anyURI
	pace:githubisRepresentationOf "is representation of" "is prepresentation of" pace:ArtifactVersion

Class pace:githubpullrequest
	pace:githubhasCommitUrl "has commit url" xsd:anyURI

Class pace:githubtag
	pace:githubcreatedAt "created at" xsd:dateTime

# The Tool Confidence Level is a result of following the Tool Development Process
Class pace:tcl "TCL"

Class rdf:List

Class rdf:Property

Class rdfs:Class

Class rdfs:Datatype

Class rdfs:Resource

# The base class of validation results, typically not instantiated directly.
Class shacl:AbstractResult "Abstract result" extends rdfs:Resource
	# Links a result with other results that provide more details, for example to describe violations against nested shapes.
	shacl:detail "detail" shacl:AbstractResult
	# The focus node that was validated when the result was produced.
	shacl:focusNode "focus node"
	# Human-readable messages explaining the cause of the result.
	shacl:resultMessage "result message"
	# The path of a validation result, based on the path of the validated property shape.
	shacl:resultPath "result path" rdfs:Resource
	# The severity of the result, e.g. warning.
	shacl:resultSeverity "result severity" shacl:Severity
	# The constraint that was validated when the result was produced.
	shacl:sourceConstraint "source constraint"
	# The constraint component that is the source of the result.
	shacl:sourceConstraintComponent "source constraint component" shacl:ConstraintComponent
	# The shape that is was validated when the result was produced.
	shacl:sourceShape "source shape" shacl:Shape
	# An RDF node that has caused the result.
	shacl:value "value"

# The class of constraint components.
Class shacl:ConstraintComponent "Constraint component" extends shacl:Parameterizable
	# The validator(s) used to evaluate a constraint in the context of a node shape.
	shacl:nodeValidator "shape validator" shacl:Validator
	# The validator(s) used to evaluate a constraint in the context of a property shape.
	shacl:propertyValidator "property validator" shacl:Validator
	# The validator(s) used to evaluate constraints of either node or property shapes.
	shacl:validator "validator" shacl:Validator

# The class of SHACL functions.
Class shacl:Function "Function" extends shacl:Parameterizable
	# The expected type of values returned by the associated function.
	shacl:returnType "return type" rdfs:Class

# The class of constraints backed by a JavaScript function.
Class shacl:JSConstraint "JavaScript-based constraint" extends shacl:JSExecutable

# Abstract base class of resources that declare an executable JavaScript.
Class shacl:JSExecutable "JavaScript executable" extends rdfs:Resource
	# The name of the JavaScript function to execute.
	shacl:jsFunctionName "JavaScript function name" xsd:string

# The class of SHACL functions that execute a JavaScript function when called.
Class shacl:JSFunction "JavaScript function" extends shacl:Function, shacl:JSExecutable

# Represents a JavaScript library, typically identified by one or more URLs of files to include.
Class shacl:JSLibrary "JavaScript library" extends rdfs:Resource
	# Declares the URLs of a JavaScript library. This should be the absolute URL of a JavaScript file. Implementations may redirect those to local files.
	shacl:jsLibraryURL "JavaScript library URL" xsd:anyURI

# The class of SHACL rules expressed using JavaScript.
Class shacl:JSRule "JavaScript rule" extends shacl:JSExecutable, shacl:Rule

# The class of targets that are based on JavaScript functions.
Class shacl:JSTarget "JavaScript target" extends shacl:JSExecutable, shacl:Target

# The (meta) class for parameterizable targets that are based on JavaScript functions.
Class shacl:JSTargetType "JavaScript target type" extends shacl:JSExecutable, shacl:TargetType

# A SHACL validator based on JavaScript. This can be used to declare SHACL constraint components that perform JavaScript-based validation when used.
Class shacl:JSValidator "JavaScript validator" extends shacl:JSExecutable, shacl:Validator

# The class of all node kinds, including sh:BlankNode, sh:IRI, sh:Literal or the combinations of these: sh:BlankNodeOrIRI, sh:BlankNodeOrLiteral, sh:IRIOrLiteral.
Class shacl:NodeKind "Node kind" extends rdfs:Resource

# A node shape is a shape that specifies constraint that need to be met with respect to focus nodes.
Class shacl:NodeShape "Node shape" extends shacl:Shape

# The class of parameter declarations, consisting of a path predicate and (possibly) information about allowed value type, cardinality and other characteristics.
Class shacl:Parameter "Parameter" extends shacl:PropertyShape
	# Indicates whether a parameter is optional.
	shacl:optional "optional" xsd:boolean

# Superclass of components that can take parameters, especially functions and constraint components.
Class shacl:Parameterizable "Parameterizable" extends rdfs:Resource
	# Outlines how human-readable labels of instances of the associated Parameterizable shall be produced. The values can contain {?paramName} as placeholders for the actual values of the given parameter.
	shacl:labelTemplate "label template"
	# The parameters of a function or constraint component.
	shacl:parameter "parameter" shacl:Parameter

# The class of prefix declarations, consisting of pairs of a prefix with a namespace.
Class shacl:PrefixDeclaration "Prefix declaration" extends rdfs:Resource
	# The namespace associated with a prefix in a prefix declaration.
	shacl:namespace "namespace" xsd:anyURI
	# The prefix of a prefix declaration.
	shacl:prefix "prefix" xsd:string

# Instances of this class represent groups of property shapes that belong together.
Class shacl:PropertyGroup "Property group" extends rdfs:Resource

# A property shape is a shape that specifies constraints on the values of a focus node for a given property or path.
Class shacl:PropertyShape "Property shape" extends shacl:Shape
	# A default value for a property, for example for user interface tools to pre-populate input fields.
	shacl:defaultValue "default value"
	# Human-readable descriptions for the property in the context of the surrounding shape.
	shacl:description "description"
	# Can be used to link to a property group to indicate that a property shape belongs to a group of related property shapes.
	shacl:group "group" shacl:PropertyGroup
	# Human-readable labels for the property in the context of the surrounding shape.
	shacl:name "name"
	# Specifies the property path of a property shape.
	shacl:path "path" rdfs:Resource

# A class of result annotations, which define the rules to derive the values of a given annotation property as extra values for a validation result.
Class shacl:ResultAnnotation "Result annotation" extends rdfs:Resource
	# The annotation property that shall be set.
	shacl:annotationProperty "annotation property" rdf:Property
	# The (default) values of the annotation property.
	shacl:annotationValue "annotation value"
	# The name of the SPARQL variable from the SELECT clause that shall be used for the values.
	shacl:annotationVarName "annotation variable name" xsd:string

# The class of SHACL rules. Never instantiated directly.
Class shacl:Rule "Rule"
	# The shapes that the focus nodes need to conform to before a rule is executed on them.
	shacl:condition "condition" shacl:Shape

# The class of SPARQL executables that are based on an ASK query.
Class shacl:SPARQLAskExecutable "SPARQL ASK executable" extends shacl:SPARQLExecutable
	# The SPARQL ASK query to execute.
	shacl:ask "ask" xsd:string

# The class of validators based on SPARQL ASK queries. The queries are evaluated for each value node and are supposed to return true if the given node conforms.
Class shacl:SPARQLAskValidator "SPARQL ASK validator" extends shacl:SPARQLAskExecutable, shacl:Validator

# The class of constraints based on SPARQL SELECT queries.
Class shacl:SPARQLConstraint "SPARQL constraint" extends shacl:SPARQLSelectExecutable

# The class of SPARQL executables that are based on a CONSTRUCT query.
Class shacl:SPARQLConstructExecutable "SPARQL CONSTRUCT executable" extends shacl:SPARQLExecutable
	# The SPARQL CONSTRUCT query to execute.
	shacl:construct "construct" xsd:string

# The class of resources that encapsulate a SPARQL query.
Class shacl:SPARQLExecutable "SPARQL executable" extends rdfs:Resource
	# The prefixes that shall be applied before parsing the associated SPARQL query.
	shacl:prefixes "prefixes" owl:Ontology

# A function backed by a SPARQL query - either ASK or SELECT.
Class shacl:SPARQLFunction "SPARQL function" extends shacl:Function, shacl:SPARQLAskExecutable, shacl:SPARQLSelectExecutable

# The class of SHACL rules based on SPARQL CONSTRUCT queries.
Class shacl:SPARQLRule "SPARQL CONSTRUCT rule" extends shacl:Rule, shacl:SPARQLConstructExecutable

# The class of SPARQL executables based on a SELECT query.
Class shacl:SPARQLSelectExecutable "SPARQL SELECT executable" extends shacl:SPARQLExecutable
	# The SPARQL SELECT query to execute.
	shacl:select "select" xsd:string

# The class of validators based on SPARQL SELECT queries. The queries are evaluated for each focus node and are supposed to produce bindings for all focus nodes that do not conform.
Class shacl:SPARQLSelectValidator "SPARQL SELECT validator" extends shacl:SPARQLSelectExecutable, shacl:Validator
	# Links a SPARQL validator with zero or more sh:ResultAnnotation instances, defining how to derive additional result properties based on the variables of the SELECT query.
	shacl:resultAnnotation "result annotation" shacl:ResultAnnotation

# The class of targets that are based on SPARQL queries.
Class shacl:SPARQLTarget "SPARQL target" extends shacl:SPARQLAskExecutable, shacl:SPARQLSelectExecutable, shacl:Target

# The (meta) class for parameterizable targets that are based on SPARQL queries.
Class shacl:SPARQLTargetType "SPARQL target type" extends shacl:SPARQLAskExecutable, shacl:SPARQLSelectExecutable, shacl:TargetType

# The class of SPARQL executables based on a SPARQL UPDATE.
Class shacl:SPARQLUpdateExecutable "SPARQL UPDATE executable" extends shacl:SPARQLExecutable
	# The SPARQL UPDATE to execute.
	shacl:update "update" xsd:string

# The class of validation result severity levels, including violation and warning levels.
Class shacl:Severity "Severity" extends rdfs:Resource

# A shape is a collection of constraints that may be targeted for certain nodes.
Class shacl:Shape "Shape" extends rdfs:Resource
	# Links a shape to its property shapes.
	shacl:property "property" shacl:PropertyShape
	# The rules linked to a shape.
	shacl:rule "rule" shacl:Rule
	# Defines the severity that validation results produced by a shape must have. Defaults to sh:Violation.
	shacl:severity "severity" shacl:Severity
	# Links a shape with SPARQL constraints.
	shacl:sparql "constraint (in SPARQL)" shacl:SPARQLConstraint
	# Links a shape to a target specified by an extension language, for example instances of sh:SPARQLTarget.
	shacl:target "target" shacl:Target
	# Links a shape to a class, indicating that all instances of the class must conform to the shape.
	shacl:targetClass "target class" rdfs:Class
	# Links a shape to individual nodes, indicating that these nodes must conform to the shape.
	shacl:targetNode "target node"
	# Links a shape to a property, indicating that all all objects of triples that have the given property as their predicate must conform to the shape.
	shacl:targetObjectsOf "target objects of" rdf:Property
	# Links a shape to a property, indicating that all subjects of triples that have the given property as their predicate must conform to the shape.
	shacl:targetSubjectsOf "target subjects of" rdf:Property

# The base class of targets such as those based on SPARQL queries.
Class shacl:Target "Target" extends rdfs:Resource

# The (meta) class for parameterizable targets.	Instances of this are instantiated as values of the sh:target property.
Class shacl:TargetType "Target type" extends rdfs:Class, shacl:Parameterizable

Class shacl:TripleRule "A rule based on triple (subject, predicate, object) pattern." extends shacl:Rule
	# An expression producing the nodes that shall be inferred as objects.
	shacl:object "object"
	# An expression producing the properties that shall be inferred as predicates.
	shacl:predicate "predicate"
	# An expression producing the resources that shall be inferred as subjects.
	shacl:subject "subject"

# The class of SHACL validation reports.
Class shacl:ValidationReport "Validation report" extends rdfs:Resource
	# True if the validation did not produce any validation results, and false otherwise.
	shacl:conforms "conforms" xsd:boolean
	# The validation results contained in a validation report.
	shacl:result "result" shacl:ValidationResult
	# If true then the validation engine was certain that the shapes graph has passed all SHACL syntax requirements during the validation process.
	shacl:shapesGraphWellFormed "shapes graph well-formed" xsd:boolean

# The class of validation results.
Class shacl:ValidationResult "Validation result" extends shacl:AbstractResult

# The class of validators, which provide instructions on how to process a constraint definition. This class serves as base class for the SPARQL-based validators and other possible implementations.
Class shacl:Validator "Validator" extends rdfs:Resource

Class x_org_xc_adda_eng_pace:Artifact

Class x_org_xc_adda_eng_pace:ArtifactState

