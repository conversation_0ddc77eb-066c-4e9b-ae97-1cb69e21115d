@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

pace-o:AgreedRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Agreed Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  sh:property pace-o:AgreedRequirement-safetyIntegrity ;
  sh:property pace-o:AgreedRequirement-safetyRelevance ;
  sh:property pace-o:AgreedRequirement-securityRelevance ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:AssumedRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Assumed Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  sh:property pace-o:AssumedRequirement-isDerivedFrom ;
  sh:property pace-o:AssumedRequirement-verificationMethod ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:DataRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Data Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:Definition
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Definition" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
  sh:property pace-o:Definition-isDerivedFrom ;
  sh:property pace-o:Definition-references ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:KeyPerformanceIndicatorMetrics
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Key Performance Indicator Metrics" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
  sh:property pace-o:KeyPerformanceIndicatorMetrics-belongsTo ;
  sh:property pace-o:KeyPerformanceIndicatorMetrics-monitors ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:NonProductionRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Non-Production Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:SignalDefinition
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Signal Definition" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
  sh:property pace-o:SignalDefinition-failureIndicator ;
  sh:property pace-o:SignalDefinition-isDerivedFrom ;
  sh:property pace-o:SignalDefinition-references ;
  sh:property pace-o:SignalDefinition-safetyIntegrity ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:SoftwareRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Software Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:StakeholderRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Stakeholder Requirement" ;
  sh:property pace-o:StakeholderRequirement-cbAllocates ;
  sh:property pace-o:StakeholderRequirement-cbArchitecture ;
  sh:property pace-o:StakeholderRequirement-cbBaselineId ;
  sh:property pace-o:StakeholderRequirement-cbBaselineName ;
  sh:property pace-o:StakeholderRequirement-cbDaSafetyIntegrity ;
  sh:property pace-o:StakeholderRequirement-cbFiadReferencePeName ;
  sh:property pace-o:StakeholderRequirement-cbFiadReferencePeUri ;
  sh:property pace-o:StakeholderRequirement-cbId ;
  sh:property pace-o:StakeholderRequirement-cbProjectId ;
  sh:property pace-o:StakeholderRequirement-cbProjectName ;
  sh:property pace-o:StakeholderRequirement-cbRequestedFromPe ;
  sh:property pace-o:StakeholderRequirement-cbStatsBosch ;
  sh:property pace-o:StakeholderRequirement-cbStatsCariad ;
  sh:property pace-o:StakeholderRequirement-cbStatus ;
  sh:property pace-o:StakeholderRequirement-cbStatusAda ;
  sh:property pace-o:StakeholderRequirement-cbTrackerId ;
  sh:property pace-o:StakeholderRequirement-cbTrackerName ;
  sh:property pace-o:StakeholderRequirement-cbType ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:SystemRequirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "System Requirement" ;
  rdfs:subClassOf pace-o:Requirement ;
  sh:property pace-o:SystemRequirement-realizes ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:AgreedRequirement-safetyIntegrity
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "safety integrity" ;
  sh:path pace-o:safetyIntegrity ;
  sh:severity sh:Warning ;
.
pace-o:AgreedRequirement-safetyRelevance
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "safety relevance" ;
  sh:path pace-o:safetyRelevance ;
  sh:severity sh:Warning ;
.
pace-o:AgreedRequirement-securityRelevance
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "security relevance" ;
  sh:path pace-o:securityRelevance ;
  sh:severity sh:Warning ;
.
pace-o:Artifact-owner
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:description "The name of the team taken from ADO (see .owner.yaml)." ;
  sh:maxCount 1 ;
  sh:message "One and only one owner is allowed" ;
  sh:minCount 1 ;
  sh:name "owner" ;
  sh:path pace-o:owner ;
  sh:severity sh:Warning ;
.
pace-o:AssumedRequirement-verificationMethod
  a sh:PropertyShape ;
  sh:class pace-o:VerificationMethod ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:name "verification method" ;
  sh:path pace-o:verificationMethod ;
  sh:severity sh:Warning ;
.
pace-o:Definition-isDerivedFrom
  a sh:PropertyShape ;
  sh:name "is derived from" ;
  sh:path pace-o:isDerivedFrom ;
  sh:severity sh:Warning ;
.
pace-o:Definition-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.
pace-o:Feature-dependsOn
  a sh:PropertyShape ;
  sh:class pace-o:Feature ;
  sh:name "depends on" ;
  sh:path pace-o:dependsOn ;
  sh:severity sh:Warning ;
.
pace-o:Feature-isDerivedFrom
  a sh:PropertyShape ;
  sh:name "is derived from" ;
  sh:path pace-o:isDerivedFrom ;
  sh:severity sh:Warning ;
.
pace-o:Feature-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.
pace-o:KeyPerformanceIndicatorMetrics-belongsTo
  a sh:PropertyShape ;
  sh:name "belongs to" ;
  sh:path pace-o:belongsTo ;
  sh:severity sh:Warning ;
.
pace-o:KeyPerformanceIndicatorMetrics-monitors
  a sh:PropertyShape ;
  sh:name "monitors" ;
  sh:path pace-o:monitors ;
  sh:severity sh:Warning ;
.
pace-o:ProductVariant
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Product Variant" ;
  rdfs:subClassOf pace-o:Artifact ;
  sh:property pace-o:ProductVariant-deployment ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:ProductVariant-deployment
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "Deployment" ;
  sh:path pace-o:deployment ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-AgreedRule
  a sh:SPARQLRule ;
  rdfs:label "rule for agreed requirements" ;
  rdfs:comment "if a system or software requirement has status agreed, constraints for agreed requirements are applied" ;
  sh:construct """CONSTRUCT { ?this a pace-o:AgreedRequirement } WHERE {
      VALUES ?type { pace-o:SoftwareRequirement pace-o:SystemRequirement }
      ?this rdf:type ?type ; pace-o:hasArtifactStatus pace-o:agreed . }""" ;
.
pace-o:Requirement-ProductVariant
  a sh:PropertyShape ;
  sh:class pace-o:ProductVariant ;
  sh:description "The variant(s) for which the requirement is applicable." ;
  sh:path pace-o:appliesToProductVariant ;
  sh:severity sh:Violation ;
.
pace-o:Requirement-belongsTo-Feature
  a sh:PropertyShape ;
  sh:class pace-o:Feature ;
  sh:name "belongs to" ;
  sh:path pace-o:belongsToFeature ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-belongsTo-Functionality
  a sh:PropertyShape ;
  sh:class pace-o:Functionality ;
  sh:name "belongs to" ;
  sh:path pace-o:belongsToFunctionality ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-isDerivedFrom
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:name "is derived from" ;
  sh:path pace-o:isDerivedFrom ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-safetyComment
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "safety comment" ;
  sh:path pace-o:safetyComment ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-safetyIntegrity
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "safety integrity" ;
  sh:path pace-o:safetyIntegrity ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-safetyRelevance
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "safety relevance" ;
  sh:path pace-o:safetyRelevance ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-securityRelevance
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "security relevance" ;
  sh:path pace-o:securityRelevance ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-variantRestriction
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:description "The variant feature(s) to define applicability of the requirement to product variants." ;
  sh:name "Variant feature(s)." ;
  sh:path pace-o:variantRestriction ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-verificationCriteria
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "verification criteria" ;
  sh:path pace-o:verificationCriteria ;
  sh:severity sh:Warning ;
.
pace-o:Requirement-verificationMethod
  a sh:PropertyShape ;
  sh:class pace-o:VerificationMethod ;
  sh:name "verification method" ;
  sh:path pace-o:verificationMethod ;
  sh:severity sh:Warning ;
.
pace-o:SignalDefinition-failureIndicator
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "failure indicator" ;
  sh:path pace-o:failureIndicator ;
  sh:severity sh:Warning ;
.
pace-o:SignalDefinition-isDerivedFrom
  a sh:PropertyShape ;
  sh:name "is derived from" ;
  sh:path pace-o:isDerivedFrom ;
  sh:severity sh:Warning ;
.
pace-o:SignalDefinition-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.
pace-o:SignalDefinition-safetyIntegrity
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "safety integrity" ;
  sh:path pace-o:safetyIntegrity ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbAllocates
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbAllocates ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbArchitecture
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbArchitecture ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbBaselineId
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:path pace-o:cbBaselineId ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbBaselineName
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbBaselineName ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbDaSafetyIntegrity
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbDaSafetyIntegrity ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbFiadReferencePeName
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbFiadReferencePeName ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbFiadReferencePeUri
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbFiadReferencePeUri ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbId
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:path pace-o:cbId ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbProjectId
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:path pace-o:cbProjectId ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbProjectName
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbProjectName ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbRequestedFromPe
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbRequestedFromPe ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbStatsBosch
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbStatsBosch ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbStatsCariad
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbStatsCariad ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbStatus
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:path pace-o:cbStatus ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbStatusAda
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbStatusAda ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbTrackerId
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:path pace-o:cbTrackerId ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbTrackerName
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:path pace-o:cbTrackerName ;
  sh:severity sh:Warning ;
.
pace-o:StakeholderRequirement-cbType
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:minCount 1 ;
  sh:path pace-o:cbType ;
  sh:severity sh:Warning ;
.
pace-o:SystemRequirement-realizes
  a sh:PropertyShape ;
  sh:maxCount 1 ;
  sh:name "realizes" ;
  sh:path pace-o:realizes ;
  sh:severity sh:Warning ;
.
pace-o:Feature
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Feature" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
  sh:property pace-o:Feature-dependsOn ;
  sh:property pace-o:Feature-isDerivedFrom ;
  sh:property pace-o:Feature-references ;
  pace-o:package pace-o:RequirementsPackage ;
.
pace-o:Requirement
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Requirement" ;
  rdfs:subClassOf pace-o:ArtifactWithStatus ;
  sh:property pace-o:Artifact-owner ;
  sh:property pace-o:Requirement-ProductVariant ;
  sh:property pace-o:Requirement-belongsTo-Feature ;
  sh:property pace-o:Requirement-belongsTo-Functionality ;
  sh:property pace-o:Requirement-isDerivedFrom ;
  sh:property pace-o:Requirement-references ;
  sh:property pace-o:Requirement-safetyComment ;
  sh:property pace-o:Requirement-safetyIntegrity ;
  sh:property pace-o:Requirement-safetyRelevance ;
  sh:property pace-o:Requirement-securityRelevance ;
  sh:property pace-o:Requirement-variantRestriction ;
  sh:property pace-o:Requirement-verificationCriteria ;
  sh:property pace-o:Requirement-verificationMethod ;
  sh:rule pace-o:Requirement-AgreedRule ;
  pace-o:package pace-o:RequirementsPackage ;
.