"""shacl to owl data model. Shacl file as input and owl model as output."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import hashlib
from datetime import datetime

import requests
from rddlib import auth_utils


class SHACLToOWLConverter:
    """SHACL to OWL converter."""

    def __init__(self, database):
        """Initialize the SHACL to OWL converter."""
        self.stardog_base_url = auth_utils.get_rdd_secret("rdd-stardog-url")
        self.auth = auth_utils.get_stardog_access_token()
        self.database = database

    def read_shacl_file(self, file_path: str) -> str:
        """Read SHACL content from file."""
        with open(file_path, "r") as file:
            return file.read()

    def generate_content_hash(self, content: str) -> str:
        """Generate a hash for the SHACL content."""
        return hashlib.sha256(content.encode()).hexdigest()

    def query_database(self, query):
        """Execute a SPARQL query."""
        api_endpoint = f"{self.stardog_base_url}/{self.database}/query"

        headers = {"Content-Type": "application/sparql-query", "Accept": "application/sparql-results+json"}

        try:
            response = requests.post(api_endpoint, auth=self.auth, headers=headers, data=query)
            response.raise_for_status()
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"Query error: {str(e)}")
            if hasattr(e, "response") and e.response is not None:
                print(f"Server response: {e.response.text}")
            raise

    def check_existing_content(self, content_hash: str) -> tuple[bool, dict]:
        """Check if SHACL content with this hash exists."""
        query = f"""
        PREFIX meta: <http://example.org/metadata/>
        SELECT ?version ?timestamp
        WHERE {{
            ?s meta:contentHash "{content_hash}" ;
               meta:version ?version ;
               meta:timestamp ?timestamp .
        }}
        ORDER BY DESC(?timestamp)
        LIMIT 1
        """

        result = self.query_database(query)
        if result["results"]["bindings"]:
            return True, result["results"]["bindings"][0]
        return False, None

    def get_latest_version(self) -> int:
        """Get the latest version number for SHACL content."""
        query = """
        PREFIX meta: <http://example.org/metadata/>
        SELECT ?version
        WHERE {
            ?s meta:version ?version .
        }
        ORDER BY DESC(xsd:integer(?version))
        LIMIT 1
        """

        result = self.query_database(query)
        if result["results"]["bindings"]:
            return int(result["results"]["bindings"][0]["version"]["value"])
        return 0

    def remove_old_version(self, old_hash) -> bool:
        """Remove old version of SHACL content."""
        query = f"""
        DELETE {{
            ?s ?p ?o .
        }}
        WHERE {{
            ?s meta:contentHash "{old_hash}" ;
               ?p ?o .
        }}
        """

        api_endpoint = f"{self.stardog_base_url}/{self.database}/update"
        headers = {"Content-Type": "application/sparql-update"}

        try:
            response = requests.post(api_endpoint, auth=self.auth, headers=headers, data=query)
            response.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            print(f"Error removing old version: {str(e)}")
            return False

    def upload_shacl_with_metadata(self, shacl_content: str, content_hash: str, version: int) -> bool:
        """Upload SHACL content with metadata."""
        timestamp = datetime.now(datetime.timezone.utc).isoformat()

        # Create metadata triple
        metadata = f"""
        PREFIX meta: <http://example.org/metadata/>
        INSERT DATA {{
            _:version meta:contentHash "{content_hash}" ;
                      meta:version "{version}" ;
                      meta:timestamp "{timestamp}" .
        }}
        """

        # Upload metadata
        api_endpoint = f"{self.stardog_base_url}/{self.database}/update"
        headers = {"Content-Type": "application/sparql-update"}

        try:
            # Upload metadata
            response = requests.post(api_endpoint, auth=self.auth, headers=headers, data=metadata)
            response.raise_for_status()

            # Upload SHACL content
            return self.upload_shacl_query(shacl_content)

        except requests.exceptions.RequestException as e:
            print(f"Error uploading content with metadata: {str(e)}")
            return False

    def upload_shacl_query(self, shacl_content) -> bool:
        """Upload SHACL content using SPARQL query endpoint."""
        api_endpoint = f"{self.stardog_base_url}/{self.database}/update"

        query = f"""
        INSERT DATA {{
            {shacl_content}
        }}
        """

        headers = {"Content-Type": "application/sparql-update"}

        try:
            response = requests.post(api_endpoint, auth=self.auth, headers=headers, data=query)
            response.raise_for_status()
            return True
        except requests.exceptions.RequestException as e:
            print(f"Error in SPARQL update: {str(e)}")
            if hasattr(e, "response") and e.response is not None:
                print(f"Server response: {e.response.text}")
            return False

    def generate_model(self) -> str:
        """Generate OWL model from the database content."""
        api_endpoint = f"{self.stardog_base_url}/{self.database}/model"

        headers = {"Accept": "text/turtle"}

        params = {"format": "owl"}

        try:
            response = requests.get(api_endpoint, auth=self.auth, headers=headers, params=params)

            response.raise_for_status()
            return response.text

        except requests.exceptions.RequestException as e:
            print(f"Error generating model: {str(e)}")
            if hasattr(e, "response") and e.response is not None:
                print(f"Server response: {e.response.text}")
            raise

    def save_owl_model(self, owl_content, output_path):
        """Save the OWL model to a file."""
        with open(output_path, "w") as file:
            file.write(owl_content)

    def convert_shacl_to_owl(self, input_path, output_path, force_update=False) -> str:
        """Convert SHACL file to OWL and save result."""
        try:
            # Read SHACL content
            print("Reading SHACL file...")
            shacl_content = self.read_shacl_file(input_path)

            # Generate content hash
            content_hash = self.generate_content_hash(shacl_content)

            # Check if content already exists
            exists, existing_version = self.check_existing_content(content_hash)

            if exists and not force_update:
                print("This SHACL content already exists in the database.")
                print(f"Version: {existing_version['version']['value']}")
                print(f"Timestamp: {existing_version['timestamp']['value']}")
            else:
                # Get next version number
                next_version = self.get_latest_version() + 1

                # If forcing update, remove old version first
                if force_update and exists:
                    print("Removing old version...")
                    self.remove_old_version(content_hash)

                # Upload SHACL content with metadata
                print(f"Uploading SHACL content (version {next_version})...")
                if not self.upload_shacl_with_metadata(shacl_content, content_hash, next_version):
                    raise Exception("Failed to upload SHACL content")

            # Generate model
            print("Generating OWL model...")
            owl_content = self.generate_model()

            # Save result
            print("Saving OWL model...")
            self.save_owl_model(owl_content, output_path)

            return owl_content

        except Exception as e:
            print(f"Conversion failed: {str(e)}")
            raise


def main():
    """Main function."""
    # Initialize converter
    database = "tmp_shacl_experiment"  # Your Stardog database name
    converter = SHACLToOWLConverter(database)

    # Convert SHACL to OWL
    try:
        result = converter.convert_shacl_to_owl(
            input_path="/Workspace/Users/<USER>/dataloop/databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/shacl/poc/test_shacl_report.ttl",
            output_path="/Workspace/Users/<USER>/dataloop/databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/shacl/poc/pace_test_spec_model_owl.ttl",
        )
        print("Conversion successful!")
        print("Generated OWL model:")
        print(result)

    except Exception as e:
        print(f"Conversion failed: {str(e)}")


if __name__ == "__main__":  # pragma: no cover
    main()
