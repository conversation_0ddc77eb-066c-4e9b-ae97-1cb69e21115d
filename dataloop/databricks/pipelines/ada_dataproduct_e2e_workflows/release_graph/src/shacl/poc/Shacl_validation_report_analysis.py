"""shacl validation report to silver ingest. From Bronze to Silver."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, expr, when
from pyspark.sql.types import StringType, StructField, StructType
from rdflib import Graph, Namespace
from rdflib.namespace import RDF


def load_shacl_report_from_string(content: str) -> Graph:
    """Load the SHACL report string and return the RDF graph."""
    g = Graph()
    try:
        # Check if the string is non-empty and properly formatted
        if not content.strip():
            logging.error("The string is empty.")
            return g

        logging.info(f"Parsing the shacl content string")
        g.parse(data=content, format="turtle")
        logging.info(f"Successfully parsed the shacl content string")
        return g
    except Exception as e:
        logging.error(f"Failed to parse the shacl content string. Error: {e}")
        return g


def load_shacl_report_from_file(file_path: str) -> Graph:
    """Load the SHACL report file and return the RDF graph."""
    g = Graph()
    try:
        # Check if the file is non-empty and properly formatted
        with open(file_path, "r", encoding="utf-8") as file:
            if not file.read().strip():
                logging.error(f"The file {file_path} is empty.")
                return g

        logging.info(f"Parsing the file {file_path}")
        g.parse(file_path, format="turtle")
    except Exception as e:
        logging.error(f"Failed to parse the file {file_path}. Error: {e}")
        return g

    logging.info(f"Successfully parsed the file {file_path}")
    return g


def extract_validation_results(graph: Graph) -> list:
    """Extract and structure the validation results from the RDF graph."""
    results = []
    SHACL = Namespace("http://www.w3.org/ns/shacl#")

    for report in graph.subjects(RDF.type, SHACL.ValidationReport):
        conforms = graph.value(report, SHACL.conforms)
        for result in graph.objects(report, SHACL.result):
            result_severity = graph.value(result, SHACL.resultSeverity)
            source_shape = graph.value(result, SHACL.sourceShape)
            source_constraint_component = graph.value(result, SHACL.sourceConstraintComponent)
            value = graph.value(result, SHACL.value)
            focus_node = graph.value(result, SHACL.focusNode)
            result_path = graph.value(result, SHACL.resultPath)
            result_message = graph.value(result, SHACL.resultMessage)

            results.append(
                {
                    "conforms": str(conforms) if conforms else "",
                    "result_severity": get_fragment(result_severity),
                    "source_shape": str(source_shape) if source_shape else "",
                    "source_constraint_component": get_fragment(source_constraint_component),
                    "value": str(value) if value else "",
                    "focus_node": str(focus_node) if focus_node else "",
                    "result_path": str(result_path) if result_path else "",
                    "result_message": str(result_message) if result_message else "",
                }
            )
    return results


def get_fragment(uri: str) -> str:
    """Extract fragment from a URI."""
    return uri.split("#")[-1] if uri and "#" in uri else uri


def results_to_spark_dataframe(results: list) -> DataFrame:
    """Convert the list of results to a Spark DataFrame."""
    spark = SparkSession.builder.appName("SHACLValidationResults").getOrCreate()

    schema = StructType(
        [
            StructField("conforms", StringType(), True),
            StructField("result_severity", StringType(), True),
            StructField("source_shape", StringType(), True),
            StructField("source_constraint_component", StringType(), True),
            StructField("value", StringType(), True),
            StructField("focus_node", StringType(), True),
            StructField("result_path", StringType(), True),
            StructField("result_message", StringType(), True),
        ]
    )

    df = spark.createDataFrame(results, schema)
    return df


def main():
    """Main function."""
    is_table = True  # Set this to either True (for table) or False (for file)
    if not is_table:
        file_path = "/Workspace/Users/<USER>/dataloop/databricks/pipelines/ada_dataproduct_e2e_workflows/release_graph/src/shacl/poc/test_shacl_report.ttl"
        graph = load_shacl_report_from_file(file_path)
    else:
        shacl_table = "bronze_dev.needs.pace_shacl"
        shacl_df = spark.table(shacl_table)
        shacl_content = shacl_df.select("ttl_content").collect()[0]["ttl_content"]
        graph = load_shacl_report_from_string(shacl_content)

    if len(graph) == 0:
        logging.error("Graph is empty. Exiting...")
        return

    results = extract_validation_results(graph)
    shacl_report_df = results_to_spark_dataframe(results)

    # clean focus_node and result_path columns
    shacl_report_df = shacl_report_df.withColumn(
        "focus_node",
        when(col("focus_node").isNotNull(), expr("substring(focus_node, 19, length(focus_node))")).otherwise(
            col("focus_node")
        ),
    )
    shacl_report_df = shacl_report_df.withColumn(
        "result_path",
        when(col("result_path").isNotNull(), expr("substring(result_path, 19, length(result_path))")).otherwise(
            col("result_path")
        ),
    )

    # Read needs_nodes data
    needs_nodes_df = spark.table("silver_dev.ada_release_graph.needs_nodes")

    # clean id column
    needs_nodes_df = needs_nodes_df.withColumn(
        "id", when(col("id").isNotNull(), expr("substring(id, 19, length(id))")).otherwise(col("id"))
    )

    # Merge both DataFrames on the cleaned focus_node and needs_nodes issue_id
    merged_df = shacl_report_df.join(needs_nodes_df, shacl_report_df.focus_node == needs_nodes_df.id, "outer")

    final_df = merged_df.select(shacl_report_df["*"], needs_nodes_df["da_status"], needs_nodes_df["owner"])

    is_save_mode = False  # Set this to either True (for save mode) or False (for display mode)
    if is_save_mode:
        # Save the final DataFrame to a Silver table
        table_name = "silver_dev.ada_release_graph.shacl_report"
        final_df.write.mode("overwrite").option("mergeSchema", "true").saveAsTable(table_name)
        logging.info(f"Data saved to {table_name} successfully.")


if __name__ == "__main__":  # pragma: no cover
    main()
