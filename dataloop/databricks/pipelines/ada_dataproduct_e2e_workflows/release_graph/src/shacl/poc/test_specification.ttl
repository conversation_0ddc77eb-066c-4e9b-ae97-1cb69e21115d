@prefix owl: <http://www.w3.org/2002/07/owl#> .
@prefix pace-o: <urn:pace:ontology:> .
@prefix rdfs: <http://www.w3.org/2000/01/rdf-schema#> .
@prefix sh: <http://www.w3.org/ns/shacl#> .
@prefix xsd: <http://www.w3.org/2001/XMLSchema#> .

pace-o:KeyPerformanceIndicator
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test KPI" ;
  rdfs:comment "Specification for KPI evaluations in the test context" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-calculates-Failure ;
  sh:property pace-o:TestSpec-calculates-Requirement ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSw
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test Specification SW" ;
  rdfs:comment "Test specification for software verification" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-hasTestMethod ;
  sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
  sh:property pace-o:TestSpec-verifiesReq ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSwInt
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test Specification Software Integration" ;
  rdfs:comment "Test specification for software integration tests" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-architecture ;
  sh:property pace-o:TestSpec-hasTestMethod ;
  sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
  sh:property pace-o:TestSpec-testsScenario ;
  sh:property pace-o:TestSpec-verifiesArch ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSys
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test Specification System" ;
  rdfs:comment "Test specification for system-level tests" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-hasTestMethod ;
  sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
  sh:property pace-o:TestSpec-verifiesReq ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:TestSysInt
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test Specification System Integration" ;
  rdfs:comment "Test specification for system integration verification" ;
  rdfs:subClassOf pace-o:TestSpec ;
  sh:property pace-o:TestSpec-architecture ;
  sh:property pace-o:TestSpec-hasTestMethod ;
  sh:property pace-o:TestSpec-isSpecifiedByTestDesign ;
  sh:property pace-o:TestSpec-verifiesArch ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
pace-o:Artifact-owner
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:description "The name of the team taken from ADO (see .owner.yaml)." ;
  sh:maxCount 1 ;
  sh:message "One and only one owner is allowed" ;
  sh:minCount 1 ;
  sh:name "owner" ;
  sh:path pace-o:owner ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-calculates-Failure
  a sh:PropertyShape ;
  sh:class pace-o:Failure ;
  sh:name "calculates" ;
  sh:path pace-o:calculatesFailure ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-calculates-Requirement
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:name "calculates" ;
  sh:path pace-o:calculatesRequirement ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-datafile
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of these attribute is allowed" ;
  sh:name "datafile" ;
  sh:path pace-o:datafile ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-eval_ref_data
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "Evaluation Reference Data" ;
  sh:path pace-o:evaluationReferenceData ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-evaluationAggregationArgument
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "evaluation aggregation argument" ;
  sh:path pace-o:evaluationAggregationArgument ;
.
pace-o:TestSpec-evaluationAggregationScript
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of one implementation can be linked" ;
  sh:name "evaluation aggregation script (path)" ;
  sh:path pace-o:evaluationAggregationScript ;
.
pace-o:TestSpec-evaluationArgument
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "evaluation argument" ;
  sh:path pace-o:evaluationArgument ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-evaluationScript
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of one implementation can be linked" ;
  sh:name "Eval Script (Path)" ;
  sh:path pace-o:evaluationScript ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-evaluationTooling
  a sh:PropertyShape ;
  sh:class pace-o:EvaluationTooling ;
  sh:description "The tooling used to evaluate the test specification." ;
  sh:maxCount 1 ;
  sh:message "Only one tool implementation supported" ;
  sh:name "Evaluation Tooling" ;
  sh:path pace-o:hasEvaluationTooling ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-hasSutProfile
  a sh:PropertyShape ;
  sh:minCount 1 ;
  sh:name "has SuT profile" ;
  sh:path pace-o:hasSutProfile ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-hasTestTrigger
  a sh:PropertyShape ;
  sh:class pace-o:TestTrigger ;
  sh:name "Test Trigger" ;
  sh:path pace-o:hasTestTrigger ;
  sh:severity sh:Violation ;
.
pace-o:TestSpec-issueId
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Only one issue ID supported" ;
  sh:name "Issue-ID" ;
  sh:path pace-o:issueId ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-kpiValueUsed
  a sh:PropertyShape ;
  sh:name "uses KPI value" ;
  sh:path pace-o:kpiValueUsed ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-references
  a sh:PropertyShape ;
  sh:name "references" ;
  sh:path pace-o:references ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-architecture
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:maxCount 1 ;
  sh:message "Maximum one of these attribute is allowed" ;
  sh:name "Architecture" ;
  sh:path pace-o:architecture ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-testsScenario
  a sh:PropertyShape ;
  sh:class pace-o:Scenario ;
  sh:name "tests scenario" ;
  sh:path pace-o:testsScenario ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-verifiesArch
  a sh:PropertyShape ;
  sh:class pace-o:ArchitectureElement ;
  sh:minCount 1 ;
  sh:name "verifies architecture" ;
  sh:path pace-o:verifies ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-verifiesReq
  a sh:PropertyShape ;
  sh:class pace-o:Requirement ;
  sh:minCount 1 ;
  sh:name "verifies requirement" ;
  sh:path pace-o:verifies ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-hasTestMethod
  a sh:PropertyShape ;
  sh:class pace-o:TestMethod ;
  sh:description "Name covered test method if test-design element is not used." ;
  sh:name "Test Method" ;
  sh:path pace-o:hasTestMethod ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-isSpecifiedByTestDesign
  a sh:PropertyShape ;
  sh:name "is specified by test design" ;
  sh:path pace-o:isSpecifiedByTestDesign ;
  sh:severity sh:Warning ;
.
pace-o:TestSpec-state
  a sh:PropertyShape ;
  sh:datatype xsd:string ;
  sh:name "State" ;
  sh:path pace-o:state ;
  sh:severity sh:Warning ;
  sh:in ("Draft" "Review" "Agreed") ;
.
pace-o:TestSpec
  a owl:Class ;
  a sh:NodeShape ;
  rdfs:label "Test Specification" ;
  rdfs:comment "Base class for test specifications" ;
  rdfs:subClassOf pace-o:Artifact ;
  sh:property pace-o:Artifact-owner ;
  sh:property pace-o:TestSpec-datafile ;
  sh:property pace-o:TestSpec-eval_ref_data ;
  sh:property pace-o:TestSpec-evaluationAggregationArgument ;
  sh:property pace-o:TestSpec-evaluationAggregationScript ;
  sh:property pace-o:TestSpec-evaluationArgument ;
  sh:property pace-o:TestSpec-evaluationScript ;
  sh:property pace-o:TestSpec-evaluationTooling ;
  sh:property pace-o:TestSpec-hasSutProfile ;
  sh:property pace-o:TestSpec-hasTestTrigger ;
  sh:property pace-o:TestSpec-issueId ;
  sh:property pace-o:TestSpec-kpiValueUsed ;
  sh:property pace-o:TestSpec-references ;
  sh:property pace-o:TestSpec-testsScenario ;
  sh:property pace-o:TestSpec-state ;
  sh:rule pace-o:RuleTestSpecWithIssueId ;
  pace-o:package pace-o:TestSpecificationPackage ;
.
