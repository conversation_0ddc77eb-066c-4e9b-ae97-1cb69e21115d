"""E2E tests for ADO module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

# standard imports
import argparse
import re

import stardog

# 3rd party library imports
from rddlib import get_rdd_secret, get_stardog_access_token


def test_ado_data_in_db(db_name: str) -> None:
    """Run SPARQL query to test presence of specific data in Stardog."""

    conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }

    with stardog.Admin(**conn_details) as admin:
        if not any(db.name == db_name for db in admin.databases()):
            return  # exit without error

    test_query = """
    SELECT ?s ?o
    {
        ?s  ado:hasStatus ?o
    }
    LIMIT 1
    """

    with stardog.Connection(db_name, **conn_details) as conn:
        conn.begin()
        response = conn.select(test_query)
        bindings = response["results"]["bindings"]
        vals = [b["s"]["value"] for b in bindings]
        regex = re.compile("^urn:pace:ontology:ado:")
        if not regex.match(vals[0]):
            raise ValueError("ADO data not found in Stardog")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="E2E Test for the ADO Pipeline")

    parser.add_argument("-d", "--db", dest="db", help="Stardog database")

    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()
    db = args.db

    test_ado_data_in_db(db)
