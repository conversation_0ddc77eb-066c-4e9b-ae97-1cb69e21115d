"""Query and transform Azure DevOps data and store in Databricks Bronze Layer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from datetime import datetime, timedelta
from typing import Any

from azure.devops.connection import Connection
from azure.devops.v7_0.work_item_tracking.work_item_tracking_client import WorkItemTrackingClient
from azure.devops.v7_1.work_item_tracking.models import Wiql
from constants.bronze import BRONZE_SCHEMA, BRONZE_SCHEMA_COMMENTS, BRONZE_SCHEMA_WORKITEM, DESCRIPTION
from constants.common import (
    ADO_GET_WORKITEM_IDS_BATCH_SIZE,
    ADO_GET_WORKITEM_LIMIT,
    ADO_ORG_URL,
    APP_AREA_MAP,
    APP_ID,
    AUTHORITY,
    SCOPES,
)
from msal import ConfidentialClientApplication
from msrest.authentication import BasicAuthentication
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, desc
from rddlib import DBX_APP_SCHEMA_MAP
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, get_rdd_secret, quality_check, setup_databricks_logging
from rddlib.dbx_utils import FullSchemaName
from rddlib.quality_check import log_quality_check_result
from rddlib.utils import batch

logger = logging.getLogger(__name__)


class ADOBronzeExtractor:
    """Class for ADO bronze extraction."""

    # Class attributes
    COMMENTS_FILTER_WORK_ITEM_TYPES = ["Key Result", "Team Epic", "Initiative"]

    # Instance attributes
    table_full: str
    app: str
    area: str
    since: str
    until: str
    spark: SparkSession

    def __init__(
        self,
        table_full_name: str,
        app: str,
        area: str,
        since: str,
        until: str,
    ):
        """Initializes the ADOBronzeExtractor with specific parameters for data extraction and storage.

        Args:
            table_full_name (str): The full name of the table. Ex. bronze.release.ado
            app (str): The application identifier for which data extraction is being performed. Ex. release/rng
            area (str): The ADO area path. Ex. rng.
            since (str): The start date from when to begin data extraction (YYYY-MM-DD).
            until (str): The end date until when data extraction is to be performed (YYYY-MM-DD).
        """
        self.table_full = table_full_name
        self.app = app
        self.area = area
        self.since = None if since == "" else since
        self.until = None if until == "" else until
        self.spark = SparkSession.builder.getOrCreate()

    # Query & Process

    def query_and_process_ado_work_items(self, token: str) -> tuple[list[Any], list[dict[str, Any]]]:
        """Main function to query ADO for work items and process them, including fetching comments."""
        # Authenticate and create ADO connection
        wiql_client = self._create_ado_connection(token)

        # Initial setup
        results = []
        counter = ADO_GET_WORKITEM_IDS_BATCH_SIZE
        more_results = True
        since_date = self._get_latest_changed_date() if self.since is None else self.since
        if since_date is None:
            raise Exception("No latest changed date found in the bronze layer.")

        until_date = datetime.now().strftime("%Y-%m-%d") if self.until is None else self.until

        # Main loop to query work items
        while more_results:
            start_id = counter - ADO_GET_WORKITEM_IDS_BATCH_SIZE
            end_id = counter
            query = self._construct_query(start_id, end_id, since_date, until_date)
            current_results = self._execute_query(wiql_client, query)
            if current_results:
                results.extend(current_results)
                counter += ADO_GET_WORKITEM_IDS_BATCH_SIZE
            else:
                more_results = False

        # Fetch work items above the last checked ID range
        results.extend(self._fetch_remaining_work_items(wiql_client, counter, since_date, until_date))
        logger.info(f"Found {len(results)} workitems updated between {since_date} and {until_date}")

        # Fetch detailed info for all work items
        ids = [int(work_item.id) for work_item in results]
        work_items = self._fetch_detailed_work_items(wiql_client, ids)

        # Fetch comments for Key Result work items
        comments = self._fetch_comments_for_work_items(wiql_client, work_items)

        return work_items, comments

    def _fetch_work_items(self, wiql_client: WorkItemTrackingClient, ids: list[int]) -> list[Any]:
        """Fetches work items by their IDs and returns them as a list."""
        if not ids:
            return []
        return wiql_client.get_work_items(ids, expand="All")

    def _fetch_detailed_work_items(self, wiql_client: WorkItemTrackingClient, ids: list[int]) -> list[Any]:
        """Fetches detailed information for a list of work item IDs in batches."""
        work_items = []
        for batch_ids in batch(ids, ADO_GET_WORKITEM_LIMIT):
            detailed_work_items = self._fetch_work_items(wiql_client, batch_ids)
            work_items.extend(detailed_work_items)
        return work_items

    def _fetch_remaining_work_items(
        self, wiql_client: WorkItemTrackingClient, counter: int, since_date: str, until_date: str
    ) -> list[Any]:
        """Fetches work items with IDs greater than the last checked range."""
        final_query = self._construct_query(counter, None, since_date, until_date)
        final_results = self._execute_query(wiql_client, final_query)
        return final_results

    def _fetch_comments_for_work_items(
        self, wiql_client: WorkItemTrackingClient, work_items: list[Any]
    ) -> list[dict[str, Any]]:
        """Fetches comments for the specified work items and returns them in a structured list."""
        all_comments = []
        for work_item in work_items:
            if work_item.fields["System.WorkItemType"] in self.COMMENTS_FILTER_WORK_ITEM_TYPES:
                comments = wiql_client.get_comments("PACE", work_item.id).comments
                all_comments.append({"work_item_id": work_item.id, "comments": comments})
        logger.info(
            f"Fetched comments for {len(all_comments)} workitems "
            f"of types {', '.join(self.COMMENTS_FILTER_WORK_ITEM_TYPES)}"
        )
        return all_comments

    def _get_latest_changed_date(self) -> str | None:
        """Returns the latest ADO workitem date in the bronze layer based on fields.System.ChangedDate.

        Returns:
            str | None: The latest changed date of the ADO workitem in the format 'YYYY-MM-DD',
                        or None if no workitems are found in the bronze layer.
        """
        df = self.spark.read.table(self.table_full)
        df_with_dates = df.select(col("fields").getField("System.ChangedDate").alias("date")).sort(desc("date"))
        latest_workitem = df_with_dates.first()
        if latest_workitem is None:
            logger.error("No workitems found in the bronze layer")
            return None

        date_only = latest_workitem.date.split("T")[0]
        logger.info(f"Latest ADO workitem in bronze layer based on updated date: {date_only}")
        return date_only

    def _create_ado_connection(self, token: str) -> WorkItemTrackingClient:
        """Establishes and returns a connection to Azure DevOps using the provided token."""
        credentials = BasicAuthentication("", token)
        connection = Connection(base_url=ADO_ORG_URL, creds=credentials)
        return connection.clients.get_work_item_tracking_client()

    def _construct_query(
        self, start_id: int, end_id: int | None = None, since_date: str | None = None, until_date: str | None = None
    ) -> str:
        """Constructs and returns a WIQL query string based on specified parameters."""
        base_query = f"SELECT System.ID FROM WorkItems WHERE System.ID >= {start_id}"
        if end_id is not None:
            base_query += f" AND System.ID < {end_id}"
        if self.area:
            base_query += f" AND System.AreaPath = '{self.area}'"
        if since_date and until_date:
            base_query += f" AND [System.ChangedDate] >= '{since_date}' AND [System.ChangedDate] <= '{until_date}'"
        return base_query

    def _execute_query(self, wiql_client: WorkItemTrackingClient, query: str) -> list[Any]:
        """Executes the provided WIQL query using the given client."""
        return wiql_client.query_by_wiql(Wiql(query)).work_items

    # Store

    def store_in_databricks(self, work_items: list[Any], comments: list[dict[str, Any]]) -> None:
        """Stores the processed work items and their comments into Databricks delta tables."""
        df_work_items = self.spark.createDataFrame(work_items, schema=BRONZE_SCHEMA_WORKITEM)
        df_comments = self.spark.createDataFrame(comments, schema=BRONZE_SCHEMA_COMMENTS)
        joined_df = df_work_items.join(df_comments, df_work_items.id == df_comments.work_item_id, "left_outer")
        joined_df = joined_df.select("id", "rev", "fields", "url", "relations", "comments")
        # Run the data quality check
        self._run_quality_check(df=joined_df)

        if dtu.table_exists(self.table_full):
            condition = "target.id = source.id"
            dtu.merge(joined_df, self.table_full, condition)
        else:
            dtu.overwrite(joined_df, self.table_full)

        # Add metadata to the table
        dtu.update_table_metadata(self.table_full, DESCRIPTION)

    def _run_quality_check(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Schema validation
        schema_valid, mismatch_details_raw = quality_check.schema_validation(df, desired_schema=BRONZE_SCHEMA)
        log_quality_check_result(schema_valid, "schema_validation", mismatch_details_raw)

        # Missing value check
        missing_value_rows = quality_check.missing_value_detection(df, column="id")
        count_of_missing_values = missing_value_rows.count()
        if count_of_missing_values == 0:
            log_quality_check_result(True, "missing_value_detection", "id")
        else:
            log_quality_check_result(False, "missing_value_detection", f"id: {count_of_missing_values / df.count()}.")

        # Duplicate check
        # _, duplicate_id_rows = quality_check.deduplicate(
        #     df,
        #     subset=["id"],
        #     time_window=None,
        #     use_hashing=False,
        #     just_test=False
        # )
        # duplicates_existing = False if len(duplicate_id_rows) == 0 else True
        # UPDATE LOG IF RE-ENABLING THIS
        # logger.quality_check(f"Duplicates existing: {duplicates_existing}")


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="ADO Extractor")

    parser.add_argument(
        "-a",
        "--application",
        dest="app",
        default="release",
        help="Application, ex. release, rng",
    )

    parser.add_argument(
        "-s",
        "--since",
        dest="since",
        required=False,
        default=None,
        help="Since date (YYYY-MM-DD)",
    )

    parser.add_argument(
        "-u",
        "--until",
        dest="until",
        required=False,
        default=None,
        help="Until date (YYYY-MM-DD)",
    )

    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )

    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()
    run_id = args.run_id
    app = args.app
    test_mode = args.test_mode

    catalog_name = get_dbx_env_catalog("bronze")
    schema_name = DBX_APP_SCHEMA_MAP[app]
    area = APP_AREA_MAP[app]

    # Setup logging
    setup_databricks_logging(FullSchemaName(catalog_name, schema_name, False), "ado/bronze", run_id=run_id)

    # Get ADO token based on secrets in databricks
    secret = get_rdd_secret("sp-ado-client-secret")
    client = ConfidentialClientApplication(
        client_id=APP_ID,
        authority=AUTHORITY,
        client_credential=secret,
    )
    token_info = client.acquire_token_for_client(scopes=SCOPES)

    # Set since date to 24 hours ago if test_mode is true and since is not provided
    since_date = args.since

    if test_mode and not since_date:
        since_date = (datetime.now() - timedelta(days=1)).strftime("%Y-%m-%d")

    # Construct full table name
    table_name = "ado_test" if test_mode else "ado"
    table_full_name = f"{catalog_name}.{schema_name}.{table_name}"

    extractor = ADOBronzeExtractor(table_full_name, app, area, since_date, args.until)

    if (token_info is None) or ("access_token" not in token_info):
        logger.error("Failed to retrieve ADO token information.")
        exit(1)

    work_items, comments = extractor.query_and_process_ado_work_items(token_info["access_token"])
    extractor.store_in_databricks(work_items, comments)
