"""Helper module to define ADO related constants."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

# ADO Workitem related constants
ADO_ORG_URL = "https://dev.azure.com/PACE-ADO"
PACE_REPO_ID = "a31f637e-47a0-4556-b5b0-15eab581a077%2f"
RDD_REPO_ID = "b56cdc6d-bd54-4c03-8d2b-40d7e18fbbde%2F"

# First, ADO extractor gets a list of workitems IDs.
# For this, it uses a batch size of 10000 to avoid rate limiting errors
# Next, ADO extractor fetches each workitem. For this a max batch size of 200 is allowed by the ADO API.
ADO_GET_WORKITEM_IDS_BATCH_SIZE = 10000
ADO_GET_WORKITEM_LIMIT = 200
ADO_WORKITEMS_LIMIT_ERROR_CODE = "VS402337"  # There is a VSO limit of a query returning 20,000 results.

# Prefixes
ADO_PREFIX = "urn:pace:ontology:ado:"
GITHUB_PREFIX = "urn:pace:ontology:github:"
PACE_PREFIX = "urn:pace:ontology:"

AREA_PATH = {"RDD": "PACE\\Data Delivery Domain\\Data Delivery - Release Driven Development"}

ADO_TYPE_LOOKUP = {  # pragma: no cover
    "Objective": ADO_PREFIX + "Objective",
    "Bug": ADO_PREFIX + "Bug",
    "User Story": ADO_PREFIX + "UserStory",
    "Impediment": ADO_PREFIX + "Impediment",
    "Team Epic": ADO_PREFIX + "TeamEpic",
    "Key Result": ADO_PREFIX + "KeyResult",
    "Decision": ADO_PREFIX + "Decision",
    "Request for Feature": ADO_PREFIX + "RequestForFeature",
    "Risk": ADO_PREFIX + "Risk",
    "GitHub Commit": ADO_PREFIX + "wasCreatedFor",
    "GitHub Pull Request": ADO_PREFIX + "hasPullRequest",
    "Child": ADO_PREFIX + "hasChild",
    "Parent": ADO_PREFIX + "hasParent",
    "Related": ADO_PREFIX + "hasRelated",
}

# ADO Access Token related constants
APP_ID = "42997be1-94a7-40c0-8787-c0ce61c6e819"  # Application (client) ID for sp-pace-rdd-ado
TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"  # Tenant ID
AUTHORITY = f"https://login.microsoftonline.com/{TENANT_ID}"
SCOPES = [
    "499b84ac-1321-427f-aa17-267ca6975798/.default"
]  # IMPORTANT: resource-id of azure devops with ./default scope (this is static and not created by us)


APP_AREA_MAP = {
    "release": "",
    "rng": "PACE\\Data Delivery Domain\\Data Delivery - Release Driven Development",
}
