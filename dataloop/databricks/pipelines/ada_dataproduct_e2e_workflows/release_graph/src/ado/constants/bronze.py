"""Define a bronze layer schema for ado workitem."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import (
    ArrayType,
    BooleanType,
    DoubleType,
    IntegerType,
    MapType,
    StringType,
    StructField,
    StructType,
    TimestampType,
)

# Table names

BRONZE_TABLE = "ado"

# Schemas

_BRONZE_SCHEMA_FIELDS = StructType(
    [
        # Common fields in all workitem types
        StructField("Microsoft.VSTS.Common.ActivatedDate", StringType(), True),
        StructField("Microsoft.VSTS.Common.ClosedDate", StringType(), True),
        StructField("Microsoft.VSTS.Common.ResolvedDate", StringType(), True),
        StructField("Microsoft.VSTS.Common.StateChangeDate", StringType(), True),
        StructField("System.AreaId", IntegerType(), True),
        StructField("System.AreaLevel1", StringType(), True),
        StructField("System.AreaLevel2", StringType(), True),
        StructField("System.AreaLevel3", StringType(), True),
        StructField("System.AreaLevel4", StringType(), True),
        StructField("System.AreaLevel5", StringType(), True),
        StructField("System.AreaPath", StringType(), True),
        StructField("System.CommentCount", IntegerType(), True),
        StructField("System.ChangedDate", StringType(), True),
        StructField("System.CreatedDate", StringType(), True),
        StructField("System.ExternalLinkCount", IntegerType(), True),
        StructField("System.Id", IntegerType(), True),
        StructField("System.IterationId", IntegerType(), True),
        StructField("System.IterationLevel1", StringType(), True),
        StructField("System.IterationLevel2", StringType(), True),
        StructField("System.IterationLevel3", StringType(), True),
        StructField("System.IterationPath", StringType(), True),
        StructField("System.NodeName", StringType(), True),
        StructField("System.Parent", IntegerType(), True),
        StructField("System.Reason", StringType(), True),
        StructField("System.RelatedLinkCount", IntegerType(), True),
        StructField("System.State", StringType(), True),
        StructField("System.Tags", StringType(), True),
        StructField("System.TeamProject", StringType(), True),
        StructField("System.Title", StringType(), True),
        StructField("System.WorkItemType", StringType(), True),
        StructField("System.Description", StringType(), True),
        # fields present in 2 or more but not all workitem types
        StructField("Microsoft.VSTS.Common.Priority", IntegerType(), True),
        StructField("Microsoft.VSTS.Common.Risk", StringType(), True),
        StructField("Microsoft.VSTS.Common.ValueArea", StringType(), True),
        StructField("Microsoft.VSTS.Scheduling.FinishDate", StringType(), True),
        StructField("Microsoft.VSTS.Scheduling.OriginalEstimate", StringType(), True),
        StructField("Microsoft.VSTS.Scheduling.StartDate", StringType(), True),
        StructField("Microsoft.VSTS.Scheduling.StoryPoints", DoubleType(), True),
        StructField("Microsoft.VSTS.Scheduling.TargetDate", StringType(), True),
        # Unique fields incluiding custom fields for each workitem type
        # Key Result
        StructField("Custom.CurrentValueDataSource", StringType(), True),
        StructField("Custom.TargetValue", DoubleType(), True),
        StructField("Custom.Type", StringType(), True),
        StructField("Custom.Unit", StringType(), True),
        # Bug
        StructField("Custom.BUG_Safety_Relevant", BooleanType(), True),
        StructField("Custom.BUG_Security_Relevant", BooleanType(), True),
        StructField("Custom.BUG_priority", StringType(), True),
        StructField("Custom.BUG_severity", StringType(), True),
        StructField("Custom.UsedEnvironment", StringType(), True),
        StructField("Microsoft.VSTS.Common.Severity", StringType(), True),
        # Decision
        StructField("Custom.HighImpactDecision", BooleanType(), True),
        StructField("Custom.PACE_Time_Criticality", StringType(), True),
        # Risk
        StructField("Custom.Areaofimpact", StringType(), True),
        StructField("Custom.RiskImpact", StringType(), True),
        StructField("Custom.RiskProbabilityofoccurrence", StringType(), True),
        StructField("Custom.SeverityResult", StringType(), True),
        # Impediment
        StructField("Custom.PACE_Cost_of_no_Decision", StringType(), True),
        # Feature
        StructField("Microsoft.VSTS.Common.BusinessValue", StringType(), True),
        StructField("Microsoft.VSTS.Common.TimeCriticality", StringType(), True),
        StructField("Microsoft.VSTS.Scheduling.Effort", StringType(), True),
        # Data Demand Request
        StructField("Custom.UsedVehicle", StringType(), True),
        StructField("Custom.Country", StringType(), True),
        StructField("Custom.urban", IntegerType(), True),
        StructField("Custom.rural", IntegerType(), True),
        StructField("Custom.highway", IntegerType(), True),
        StructField("Custom.daytime", IntegerType(), True),
        StructField("Custom.nighttime", IntegerType(), True),
        StructField("Custom.twilight", IntegerType(), True),
        StructField("Custom.HDmaprequired", BooleanType(), True),
        StructField("Custom.requireddrivingtime", IntegerType(), True),
    ]
)

_BRONZE_SCHEMA_RELATION = StructType(
    [
        StructField("rel", StringType(), True),
        StructField("url", StringType(), True),
        StructField("attributes", MapType(StringType(), StringType()), True),
    ]
)

_BRONZE_SCHEMA_COMMENT = StructType(
    [
        StructField("work_item_id", IntegerType(), True),
        StructField("id", IntegerType(), True),
        StructField("version", IntegerType(), True),
        StructField("text", StringType(), True),
        StructField("created_date", TimestampType(), True),
        StructField("modified_date", TimestampType(), True),
        StructField("url", StringType(), True),
    ]
)

BRONZE_SCHEMA_COMMENTS = StructType(
    [
        StructField("work_item_id", IntegerType(), True),
        StructField("comments", ArrayType(_BRONZE_SCHEMA_COMMENT), True),
    ]
)

BRONZE_SCHEMA_WORKITEM = StructType(
    [
        StructField("id", IntegerType(), True),
        StructField("rev", IntegerType(), True),
        StructField("fields", _BRONZE_SCHEMA_FIELDS, True),
        StructField("url", StringType(), True),
        StructField("relations", ArrayType(_BRONZE_SCHEMA_RELATION), True),
    ]
)

BRONZE_SCHEMA = StructType(
    [
        StructField("id", IntegerType(), True),
        StructField("rev", IntegerType(), True),
        StructField("fields", _BRONZE_SCHEMA_FIELDS, True),
        StructField("url", StringType(), True),
        StructField("relations", ArrayType(_BRONZE_SCHEMA_RELATION), True),
        StructField("comments", ArrayType(_BRONZE_SCHEMA_COMMENT), True),
    ]
)

DESCRIPTION = "This table is specifically designed to store work item data and associated comments extracted from the Azure DevOps (ADO) REST API.\
The ingestion and maintenance of this table are handled by the RDD Team."
