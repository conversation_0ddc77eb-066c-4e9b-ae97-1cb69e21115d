"""Define a silver layer schema for ado workitem and ado relations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from constants.common import ADO_PREFIX, GITHUB_PREFIX, PACE_PREFIX
from pyspark.sql.types import (
    BooleanType,
    DateType,
    DoubleType,
    IntegerType,
    StringType,
    StructField,
    StructType,
    TimestampType,
)

# Table names

SILVER_TABLE_NODES = "ado_nodes"
SILVER_TABLE_EDGES = "ado_edges"
SILVER_TABLE_COMMENTS = "ado_comments"
SILVER_TABLE_MILESTONES = "ado_milestones"

# Schemas

SILVER_SCHEMA_NODES = StructType(
    [
        StructField("id", IntegerType(), True),
        StructField("parent", IntegerType(), True),
        StructField("severity", StringType(), True),
        StructField("reason", StringType(), True),
        StructField("iteration_path", StringType(), True),
        StructField("title", StringType(), True),
        StructField("state", StringType(), True),
        StructField("value_area", StringType(), True),
        StructField("tags", StringType(), True),
        StructField("area_path", StringType(), True),
        StructField("priority", StringType(), True),
        StructField("workitem_type", StringType(), True),
        StructField("story_points", DoubleType(), True),
        StructField("changed_date", TimestampType(), True),
        StructField("state_change_date", TimestampType(), True),
        StructField("created_date", TimestampType(), True),
        StructField("closed_date", TimestampType(), True),
        StructField("activated_date", TimestampType(), True),
        StructField("key_result_type", StringType(), True),
        StructField("key_result_unit", StringType(), True),
        StructField("key_result_data_source", StringType(), True),
        StructField("key_result_target_value", DoubleType(), True),
        StructField("bug_safety_relevant", BooleanType(), True),
        StructField("description", StringType(), True),
        StructField("vehicle_release_version", StringType(), True),
        StructField("country", StringType(), True),
        StructField("urban", IntegerType(), True),
        StructField("rural", IntegerType(), True),
        StructField("highway", IntegerType(), True),
        StructField("daytime", IntegerType(), True),
        StructField("nighttime", IntegerType(), True),
        StructField("twilight", IntegerType(), True),
        StructField("hd_map_required", BooleanType(), True),
        StructField("required_driving_time", IntegerType(), True),
        StructField("ado_type", StringType(), True),
        StructField("ado_state", StringType(), True),
    ]
)

SILVER_SCHEMA_RELATIONS = StructType(
    [
        StructField("source", StringType(), True),
        StructField("source_iri", StringType(), True),
        StructField("type", StringType(), True),
        StructField("target", StringType(), True),
        StructField("target_iri", StringType(), True),
    ]
)

SILVER_SCHEMA_COMMENTS = StructType(
    [
        StructField("id", IntegerType(), True),
        StructField("comment_id", IntegerType(), True),
        StructField("updated_at", DateType(), True),
        StructField("kr_value", DoubleType(), True),
        StructField("te_status", StringType(), True),
        StructField("te_forecast", StringType(), True),
    ]
)

SILVER_SCHEMA_MILESTONES = StructType(
    [
        StructField("milestone_id", IntegerType(), True),
        StructField("type", StringType(), True),
        StructField("initiative_id", IntegerType(), True),
        StructField("needs_feature", StringType(), True),
    ]
)

# Other

# Fields of interest for silver layer
# A silver layer field can be transformed from multiple bronze layer fields by using a list
# The first field from the list that is non-null will be used
SILVER_NODES_COLUMNS_OF_INTEREST = {
    "parent": "System.Parent",
    "severity": ["Custom.BUG_severity", "Microsoft.VSTS.Common.Severity"],
    "reason": "System.Reason",
    "iteration_path": "System.IterationPath",
    "title": "System.Title",
    "state": "System.State",
    "value_area": "Microsoft.VSTS.Common.ValueArea",
    "tags": "System.Tags",
    "area_path": "System.AreaPath",
    "priority": ["Custom.BUG_priority", "Microsoft.VSTS.Common.Priority"],
    "workitem_type": "System.WorkItemType",
    "story_points": "Microsoft.VSTS.Scheduling.StoryPoints",
    "changed_date": "System.ChangedDate",
    "state_change_date": "Microsoft.VSTS.Common.StateChangeDate",
    "created_date": "System.CreatedDate",
    "closed_date": "Microsoft.VSTS.Common.ClosedDate",
    "activated_date": "Microsoft.VSTS.Common.ActivatedDate",
    "key_result_type": "Custom.Type",
    "key_result_unit": "Custom.Unit",
    "key_result_data_source": "Custom.CurrentValueDataSource",
    "key_result_target_value": "Custom.TargetValue",
    "bug_safety_relevant": "Custom.BUG_Safety_Relevant",
    "description": "System.Description",
    "vehicle_release_version": "Custom.UsedVehicle",
    "country": "Custom.Country",
    "urban": "Custom.urban",
    "rural": "Custom.rural",
    "highway": "Custom.highway",
    "daytime": "Custom.daytime",
    "nighttime": "Custom.nighttime",
    "twilight": "Custom.twilight",
    "hd_map_required": "Custom.HDmaprequired",
    "required_driving_time": "Custom.requireddrivingtime",
}

# Relation mapping for silver layer
SILVER_RELATIONS_MAPPING = {
    "source": "id",
    "source_iri": f"CONCAT('{ADO_PREFIX}', id)",
    "type": f"""
        CASE
            WHEN relation.attributes.name == 'Parent' THEN CONCAT('{ADO_PREFIX}', 'hasParent')
            WHEN relation.attributes.name == 'Child' THEN CONCAT('{ADO_PREFIX}', 'hasChild')
            WHEN relation.attributes.name == 'Related' THEN CONCAT('{ADO_PREFIX}', 'hasRelated')
            WHEN relation.attributes.name == 'Successor' THEN CONCAT('{ADO_PREFIX}', 'hasSuccessor')
            WHEN relation.attributes.name == 'Predecessor' THEN CONCAT('{ADO_PREFIX}', 'hasPredecessor')
            WHEN relation.attributes.name == 'Tested By' THEN CONCAT('{ADO_PREFIX}', 'hasTestedBy')
            WHEN relation.attributes.name == 'References' THEN CONCAT('{ADO_PREFIX}', 'hasReference')
            WHEN relation.attributes.name == 'Duplicate' THEN CONCAT('{ADO_PREFIX}', 'hasDuplicate')
            WHEN relation.attributes.name == 'GitHub Pull Request' THEN CONCAT('{ADO_PREFIX}', 'hasPullRequest')
            WHEN relation.attributes.name == 'GitHub Commit' THEN CONCAT('{ADO_PREFIX}', 'wasCreatedFor')
            WHEN relation.rel == 'AttachedFile' THEN CONCAT('{ADO_PREFIX}', 'hasAttachedFile')
            WHEN relation.rel == 'Hyperlink' THEN CONCAT('{ADO_PREFIX}', 'hasHyperlink')
        END
    """,
    "target": """
        CASE
            WHEN relation.attributes.name == 'Parent' OR
                relation.attributes.name == 'Child' OR
                relation.attributes.name == 'Related' OR
                relation.attributes.name == 'Successor' OR
                relation.attributes.name == 'Predecessor' OR
                relation.attributes.name == 'Tested By' OR
                relation.attributes.name == 'References' OR
                relation.attributes.name == 'Duplicate'
            THEN regexp_extract(relation.url, '/workItems/([0-9]+)', 1)
            WHEN relation.attributes.name == 'GitHub Pull Request'
            THEN regexp_extract(relation.url, '%2[Ff]([0-9]+)', 1)
            WHEN relation.attributes.name == 'GitHub Commit'
            THEN regexp_extract(relation.url, '%2[Ff]([0-9a-fA-F]+)', 1)
            WHEN relation.rel == 'AttachedFile' OR relation.rel == 'Hyperlink'
            THEN relation.url
        END
    """,
    "target_iri": f"""
        CASE
            WHEN relation.attributes.name == 'Parent' OR
                relation.attributes.name == 'Child' OR
                relation.attributes.name == 'Related' OR
                relation.attributes.name == 'Successor' OR
                relation.attributes.name == 'Predecessor' OR
                relation.attributes.name == 'Tested By' OR
                relation.attributes.name == 'References' OR
                relation.attributes.name == 'Duplicate'
            THEN CONCAT('{ADO_PREFIX}', target)
            WHEN relation.attributes.name == 'GitHub Pull Request'
            THEN CONCAT('{GITHUB_PREFIX}', 'PullRequest_', target)
            WHEN relation.attributes.name == 'GitHub Commit'
            THEN CONCAT('{PACE_PREFIX}', target)
            WHEN relation.rel == 'AttachedFile' OR relation.rel == 'Hyperlink'
            THEN target
        END
    """,
}

# Table descriptions map
TABLE_DESCRIPTIONS = {
    SILVER_TABLE_NODES: "This table contains transformed ADO work item nodes from Azure DevOps, including enriched fields such as ado_type and ado_state. The ingestion and maintenance of this table are handled by the RDD Team.",
    SILVER_TABLE_EDGES: "This table captures relationships between  ADO work items, including parent-child and successor-predecessor links. The ingestion and maintenance of this table are handled by the RDD Team.",
    SILVER_TABLE_COMMENTS: "This table holds comments associated with  ADO work items, extracting structured data like KR values and TE statuses. The ingestion and maintenance of this table are handled by the RDD Team.",
    SILVER_TABLE_MILESTONES: "This table links milestones with initiatives, extracting 'needs features' and structuring their relationships. The ingestion and maintenance of this table are handled by the RDD Team.",
}
