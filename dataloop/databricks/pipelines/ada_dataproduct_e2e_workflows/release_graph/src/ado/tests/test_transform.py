"""Unit tests for ado silver transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import date, datetime
from unittest.mock import MagicMock, patch

import pytest
from constants.bronze import BRONZE_SCHEMA, BRONZE_SCHEMA_WORKITEM
from constants.common import ADO_PREFIX, GITHUB_PREFIX, PACE_PREFIX
from pyspark.errors.exceptions.captured import AnalysisException
from pyspark.sql import SparkSession
from rddlib import delta_table_utils as dtu
from rddlib.utils import full_qualname
from transform import ADOSilverTransformer

DATE_TIME_FORMAT = "%Y-%m-%dT%H:%M:%S.%fZ"
DATE = "2024-02-05T12:17:28.343Z"
BRONZE_DATA = [
    {
        "id": 1,
        "fields": {
            "System.Id": 1,
            "System.AreaId": 14,
            "System.AreaPath": "PACE",
            "System.TeamProject": "PACE",
            "System.WorkItemType": "Key Result",
            "System.State": "In Focus",
            "System.CreatedDate": DATE,
            "System.ChangedDate": DATE,
            "System.Title": "Test key result",
            "System.Tags": "tag",
            "Custom.Type": "equal",
            "Custom.Unit": "Feature Increments",
            "Custom.CurrentValueDataSource": "Comments in this ticket",
            "Custom.TargetValue": 100.0,
            "Microsoft.VSTS.Common.Priority": 1,
            "Custom.BUG_priority": "3",
            "Microsoft.VSTS.Common.Severity": "low",
            "Custom.BUG_severity": "high",
            "Custom.BUG_Safety_Relevant": True,
            "System.Description": "Description of this ticket",
            "Custom.UsedVehicle": "DC 0.2",
            "Custom.Country": "Thailand",
            "Custom.urban": 130,
            "Custom.rural": 130,
            "Custom.highway": 130,
            "Custom.daytime": 195,
            "Custom.nighttime": 156,
            "Custom.twilight": 36,
            "Custom.HDmaprequired": False,
            "Custom.requireddrivingtime": 390,
        },
        "relations": [
            {
                "rel": "System.LinkTypes.Hierarchy-Forward",
                "url": "https://dev.azure.com/PACE-ADO/b1a55dc6-f8bb-411f-b408-a393d2437fcc/_apis/wit/workItems/2",
                "attributes": {"name": "Parent", "isLocked": "false"},
            },
            {
                "rel": "ArtifactLink",
                "url": "vstfs:///GitHub/Commit/a31f637e-47a0-4556-b5b0-15eab581a077%2f123",
                "attributes": {
                    "revisedDate": DATE,
                    "name": "GitHub Commit",
                    "authorizedDate": DATE,
                    "id": "7617480",
                    "resourceModifiedDate": DATE,
                    "resourceCreatedDate": DATE,
                },
            },
            {
                "rel": "ArtifactLink",
                "url": "vstfs:///GitHub/PullRequest/a31f637e-47a0-4556-b5b0-15eab581a077%2f123",
                "attributes": {
                    "revisedDate": DATE,
                    "name": "GitHub Pull Request",
                    "authorizedDate": DATE,
                    "id": "7646548",
                    "resourceModifiedDate": DATE,
                    "resourceCreatedDate": DATE,
                },
            },
        ],
        "comments": [
            {
                "work_item_id": 1,
                "id": 100,
                "version": 1,
                "text": "<div><span>KR-Value: 0</span></div><br><div>As-Of: 2024-07-01</div>",
                "created_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "modified_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "url": (
                    "https://dev.azure.com/PACE-ADO/b1a55dc6-f8bb-411f-b408-"
                    "a393d2437fcc/_apis/wit/workItems/1/comments/100"
                ),
            },
            {
                "work_item_id": 1,
                "id": 101,
                "version": 1,
                "text": "<div><span>KR-Value: 50</span></div><br><div>As-Of: 2024-07-02</div>",
                "created_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "modified_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "url": (
                    "https://dev.azure.com/PACE-ADO/b1a55dc6-f8bb-411f-b408-"
                    "a393d2437fcc/_apis/wit/workItems/1/comments/101"
                ),
            },
        ],
    },
    {
        "id": 2,
        "fields": {
            "System.Id": 2,
            "System.AreaId": 12,
            "System.AreaPath": "PACE",
            "System.TeamProject": "PACE",
            "System.WorkItemType": "User Story",
            "System.State": "Closed",
            "System.CreatedDate": DATE,
            "System.ChangedDate": DATE,
            "System.Title": "Test user story",
            "System.Tags": None,
            "Microsoft.VSTS.Common.Priority": 1,
            "Custom.BUG_priority": None,
            "Microsoft.VSTS.Common.Severity": "low",
            "Custom.BUG_severity": "high",
            "Custom.BUG_Safety_Relevant": None,
        },
    },
    {
        "id": 3,
        "fields": {
            "System.Id": 3,
            "System.AreaId": 14,
            "System.AreaPath": "PACE",
            "System.TeamProject": "PACE",
            "System.WorkItemType": "Team Epic",
            "System.State": "In Focus",
            "System.CreatedDate": DATE,
            "System.ChangedDate": DATE,
            "System.Title": "Test team epic",
            "System.Tags": "tag",
            "Custom.Type": "equal",
            "Custom.Unit": "Feature Increments",
            "Custom.CurrentValueDataSource": "Comments in this ticket",
            "Custom.TargetValue": 100.0,
            "Microsoft.VSTS.Common.Priority": None,
            "Custom.BUG_priority": None,
            "Microsoft.VSTS.Common.Severity": "low",
            "Custom.BUG_severity": None,
            "Custom.BUG_Safety_Relevant": False,
        },
        "comments": [
            {
                "work_item_id": 3,
                "id": 300,
                "version": 1,
                "text": "<div>Status: test status</div><br><div>Forecast: Yellow<br></div><div>AsOf: 2024-07-01</div>",
                "created_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "modified_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "url": (
                    "https://dev.azure.com/PACE-ADO/b1a55dc6-f8bb-411f-b408-"
                    "a393d2437fcc/_apis/wit/workItems/2/comments/300"
                ),
            },
            {
                "work_item_id": 3,
                "id": 301,
                "version": 1,
                "text": "<div>Status: other test status</div><br><div>AsOf: 2024-07-01</div>",
                "created_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "modified_date": datetime.strptime(DATE, DATE_TIME_FORMAT),
                "url": (
                    "https://dev.azure.com/PACE-ADO/b1a55dc6-f8bb-411f-b408-"
                    "a393d2437fcc/_apis/wit/workItems/2/comments/301"
                ),
            },
        ],
    },
]


@pytest.fixture(scope="session")
def spark():
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for ADO Transformer").getOrCreate()


@pytest.fixture
def transformer(spark):
    """Fixture to create an instance of ADOSilverTransformer for testing."""
    return ADOSilverTransformer(
        "mock_catalog_bronze",
        "mock_schema_name",
        "mock_catalog_silver",
        "",
        spark,
    )


@pytest.fixture(scope="function")
def setup_temp_view(spark):
    """Fixture to set up a temporary view for testing."""
    df = spark.createDataFrame(BRONZE_DATA)
    temp_view_name = "temp_bronze_table"
    df.createOrReplaceTempView(temp_view_name)
    yield "temp_bronze_table"


# handle_ado_nodes


def test_handle_ado_nodes(spark: SparkSession, transformer: ADOSilverTransformer):
    """Test handle_ado_nodes method."""
    df = spark.createDataFrame(BRONZE_DATA, BRONZE_SCHEMA_WORKITEM)
    transformed_df = transformer._handle_ado_nodes(df)
    assert transformed_df.count() == len(BRONZE_DATA), "The number of rows should match the input data."
    all_rows = transformed_df.collect()
    first_row = all_rows[0]
    assert first_row["ado_type"] == f"{ADO_PREFIX}KeyResult", "ado_type does not match expected value"
    assert first_row["ado_state"] == f"{ADO_PREFIX}inFocus", "ado_state does not match expected value"
    assert first_row["priority"] == "3"
    assert first_row["severity"] == "high"
    assert first_row["bug_safety_relevant"] == True
    assert first_row["description"] == "Description of this ticket"
    assert first_row["vehicle_release_version"] == "DC 0.2"
    assert first_row["country"] == "Thailand"
    assert first_row["urban"] == 130
    assert first_row["rural"] == 130
    assert first_row["highway"] == 130
    assert first_row["daytime"] == 195
    assert first_row["nighttime"] == 156
    assert first_row["twilight"] == 36
    assert first_row["hd_map_required"] == False
    assert first_row["required_driving_time"] == 390
    second_row = all_rows[1]
    assert second_row["ado_type"] == f"{ADO_PREFIX}UserStory", "ado_type does not match expected value"
    assert second_row["ado_state"] == f"{ADO_PREFIX}closed", "ado_state does not match expected value"
    assert second_row["priority"] == "1"
    assert second_row["severity"] == "high"
    assert second_row["bug_safety_relevant"] is None
    third_row = all_rows[2]
    assert third_row["ado_type"] == f"{ADO_PREFIX}TeamEpic", "ado_type does not match expected value"
    assert third_row["ado_state"] == f"{ADO_PREFIX}inFocus", "ado_state does not match expected value"
    assert third_row["priority"] is None
    assert third_row["severity"] == "low"
    assert third_row["bug_safety_relevant"] == False


def test_handle_ado_nodes_exception(spark: SparkSession, transformer: ADOSilverTransformer):
    """Test handle_ado_nodes method with exception."""
    df = spark.createDataFrame(BRONZE_DATA, BRONZE_SCHEMA_WORKITEM)
    df_invalid = df.withColumnRenamed("fields", "fields123")
    with pytest.raises(AnalysisException):
        transformer._handle_ado_nodes(df_invalid)


# handle_ado_edges


def test_handle_ado_edges(spark: SparkSession, transformer: ADOSilverTransformer):
    """Test handle_ado_edges method."""
    df = spark.createDataFrame(BRONZE_DATA, BRONZE_SCHEMA_WORKITEM)
    transformed_df = transformer._handle_ado_edges(df)
    assert transformed_df.count() == 3, "The number of relations should match input data"
    all_rows = transformed_df.collect()
    first_row = all_rows[0]
    assert first_row["source"] == "1", "source does not match expected value in first row"
    assert first_row["target"] == "2", "target does not match expected value in first row"
    assert first_row["type"] == f"{ADO_PREFIX}hasParent", "type does not match expected value in first row"
    second_row = all_rows[1]
    assert second_row["source"] == "1", "source does not match expected value in second row"
    assert second_row["target"] == "123", "target does not match expected value in second row"
    assert second_row["target_iri"] == f"{PACE_PREFIX}123", "target iri does not match expected value in second row"
    assert second_row["type"] == f"{ADO_PREFIX}wasCreatedFor", "type does not match expected value in second row"
    third_row = all_rows[2]
    assert third_row["source"] == "1", "source does not match expected value in third row"
    assert third_row["target"] == "123", "target does not match expected value in third row"
    assert (
        third_row["target_iri"] == f"{GITHUB_PREFIX}PullRequest_123"
    ), "target iri does not match expected value in third row"
    assert third_row["type"] == f"{ADO_PREFIX}hasPullRequest", "type does not match expected value in third row"


def test_handle_ado_edges_exception(spark: SparkSession, transformer: ADOSilverTransformer):
    """Test handle_ado_edges method with exception."""
    df = spark.createDataFrame(BRONZE_DATA, BRONZE_SCHEMA_WORKITEM)
    df_invalid = df.withColumnRenamed("id", "id123")
    with pytest.raises(AnalysisException):
        transformer._handle_ado_edges(df_invalid)


# handle_ado_comments


def test_handle_ado_comments(spark: SparkSession, transformer: ADOSilverTransformer):
    """Test handle_ado_comments method."""
    df = spark.createDataFrame(BRONZE_DATA, BRONZE_SCHEMA)
    transformed_df = transformer._handle_ado_comments(df)
    assert transformed_df.count() == 3, "The number of comments should match input data"
    all_rows = transformed_df.collect()
    first_row = all_rows[0]
    assert first_row["id"] == 1, "id does not match expected value"
    assert first_row["comment_id"] == 100, "comment_id does not match expected value"
    assert first_row["updated_at"] == date(2024, 7, 1), "updated_at does not match expected value"
    assert first_row["kr_value"] == 0, "kr_value does not match expected value"
    assert first_row["te_status"] is None, "te_status does not match expected value"
    assert first_row["te_forecast"] is None, "te_forecast does not match expected value"
    second_row = all_rows[1]
    assert second_row["id"] == 1, "id does not match expected value"
    assert second_row["comment_id"] == 101, "comment_id does not match expected value"
    assert second_row["updated_at"] == date(2024, 7, 2), "updated_at does not match expected value"
    assert second_row["kr_value"] == 50, "kr_value does not match expected value"
    assert second_row["te_status"] is None, "te_status does not match expected value"
    assert second_row["te_forecast"] is None, "te_forecast does not match expected value"
    third_row = all_rows[2]
    assert third_row["id"] == 3, "id does not match expected value"
    assert third_row["comment_id"] == 300, "comment_id does not match expected value"
    assert third_row["updated_at"] == date(2024, 7, 1), "updated_at does not match expected value"
    assert third_row["kr_value"] is None, "kr_value does not match expected value"
    assert third_row["te_status"] == "test status", "te_status does not match expected value"
    assert third_row["te_forecast"] == "Yellow", "te_forecast does not match expected value"


def test_handle_ado_comments_exception(spark: SparkSession, transformer: ADOSilverTransformer):
    """Test handle_ado_comments method with exception."""
    df = spark.createDataFrame(BRONZE_DATA, BRONZE_SCHEMA_WORKITEM)
    df_invalid = df.withColumnRenamed("id", "id123")
    with pytest.raises(AnalysisException):
        transformer._handle_ado_comments(df_invalid)


# transform_data


@pytest.mark.parametrize(
    (
        "table_exists_return_value, table_has_schema_return_value,"
        "expected_merge_call_count, expected_overwrite_call_count"
    ),
    [
        (
            True,
            True,
            4,
            0,
        ),  # Scenario where tables exist and schema matches, expecting merge to be called 3 times
        (
            True,
            False,
            0,
            4,
        ),  # Scenario where tables exist and schema does not match, expecting overwrite to be called 3 times
        (
            False,
            False,
            0,
            4,
        ),  # Scenario where tables do not exist, expecting overwrite to be called 3 times
    ],
    ids=["table_exists_schema_match", "table_exists_schema_no_match", "table_not_exists"],
)
def test_transform_data_table_exists_or_not(
    spark: SparkSession,
    transformer: ADOSilverTransformer,
    setup_temp_view,
    table_exists_return_value,
    table_has_schema_return_value,
    expected_merge_call_count,
    expected_overwrite_call_count,
):
    """Test transform_data method."""
    temp_view_name = "temp_bronze_table"
    mock_df = MagicMock(name="DataFrame")
    with (
        patch(full_qualname(ADOSilverTransformer._handle_ado_nodes), return_value=mock_df) as mock_handle_ado_nodes,
        patch(full_qualname(ADOSilverTransformer._handle_ado_edges), return_value=mock_df) as mock_handle_ado_edges,
        patch(
            full_qualname(ADOSilverTransformer._handle_ado_comments), return_value=mock_df
        ) as mock_handle_ado_comments,
        patch(
            full_qualname(ADOSilverTransformer._handle_ado_milestones), return_value=mock_df
        ) as mock_handle_ado_milestones,
        patch(full_qualname(ADOSilverTransformer._run_quality_check), return_value=None),
        patch("rddlib.delta_table_utils.merge") as mock_merge,
        patch("rddlib.delta_table_utils.overwrite") as mock_overwrite,
        patch("rddlib.delta_table_utils.table_exists", return_value=table_exists_return_value) as mock_table_exists,
        patch(
            "rddlib.delta_table_utils.table_has_schema", return_value=table_has_schema_return_value
        ) as mock_table_has_schema,
        patch("rddlib.delta_table_utils.update_table_metadata"),
    ):
        transformer.transform_data(table_full_name=temp_view_name)

        mock_handle_ado_nodes.assert_called_once()
        mock_handle_ado_edges.assert_called_once()
        mock_handle_ado_comments.assert_called_once()
        mock_handle_ado_milestones.assert_called_once()

        assert mock_table_exists.call_count == 4
        if table_exists_return_value:
            assert mock_table_has_schema.call_count == 4

        assert mock_merge.call_count == expected_merge_call_count
        assert mock_overwrite.call_count == expected_overwrite_call_count
