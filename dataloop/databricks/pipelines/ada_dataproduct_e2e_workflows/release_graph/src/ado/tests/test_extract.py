"""Unit tests for ado bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any
from unittest.mock import Mock, patch

import pytest
from azure.devops.exceptions import AzureDevOpsServiceError
from extract import ADOBronzeExtractor
from rddlib import delta_table_utils as dtu
from rddlib.utils import full_qualname, method_self_name


class MockWorkItem:
    """Mock class representing a work item."""

    id: int
    fields: dict[str, Any]

    def __init__(self, id: int, fields: dict[str, Any] | None = None):
        """Initialize the MockWorkItem."""
        self.id = id
        self.fields = fields if fields is not None else {}

    @classmethod
    def with_type(cls, id: int, type: str) -> "MockWorkItem":
        """Create a MockWorkItem with a specific type."""
        return cls(id, {"System.WorkItemType": type})


def _get_example_key_results() -> list[MockWorkItem]:
    """Return a list of two examplary key result work items."""
    return [
        MockWorkItem.with_type(1, "Key Result"),
        MockWorkItem.with_type(2, "Key Result"),
    ]


@pytest.fixture
def extractor() -> ADOBronzeExtractor:
    """Fixture to create an ADOBronzeExtractor instance for testing."""
    return ADOBronzeExtractor("catalog_name.schema_name.table_name", "app", "area", "2024-01-01", "2024-01-31")


@pytest.fixture
def mock_wiql_client():
    """Fixture for a mock WIQL client."""
    return Mock()


# construct_query


@pytest.mark.parametrize(
    "start_id, end_id, since_date, until_date, expected_query",
    [
        (
            1,
            2,
            "2021-01-01",
            "2021-01-31",
            "SELECT System.ID FROM WorkItems WHERE System.ID >= 1 AND System.ID < 2 "
            "AND System.AreaPath = 'area' AND [System.ChangedDate] >= '2021-01-01' "
            "AND [System.ChangedDate] <= '2021-01-31'",
        ),
        (
            0,
            None,
            "2021-02-01",
            "2021-02-28",
            "SELECT System.ID FROM WorkItems WHERE System.ID >= 0 "
            "AND System.AreaPath = 'area' AND [System.ChangedDate] >= '2021-02-01' "
            "AND [System.ChangedDate] <= '2021-02-28'",
        ),
    ],
    ids=["range_with_start_and_end_dates", "range_without_end_id"],
)
def test_construct_query(extractor: ADOBronzeExtractor, start_id, end_id, since_date, until_date, expected_query):
    """Test construct_query() with parameters."""
    query = extractor._construct_query(start_id, end_id, since_date, until_date)
    assert query == expected_query, "Query does not match expected query for given parameters."


# execute_query


class MockWrappedException:
    """Mock class representing a wrapped exception."""

    def __init__(self, message="Service unavailable", inner_exception=None):
        """Initialize the MockWrappedException."""
        self.message = message
        self.inner_exception = inner_exception
        # Add other required attributes with default values
        self.exception_id = "MockExceptionID"
        self.type_name = "MockTypeName"
        self.type_key = "MockTypeKey"
        self.error_code = 0
        self.event_id = 0
        self.custom_properties = {}


def raise_azure_devops_service_error(*args, **kwargs):
    """Raise AzureDevOpsServiceError."""
    wrapped_exception = MockWrappedException()
    raise AzureDevOpsServiceError(wrapped_exception)


def test_execute_query_success(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test execute query."""
    # Set mocks for returned work items
    mock_wiql_client.query_by_wiql.return_value.work_items = Mock(
        work_items=[
            MockWorkItem.with_type(1, "Key Result"),
            MockWorkItem.with_type(2, "User Story"),
        ]
    )

    query = "SELECT System.ID FROM WorkItems WHERE System.ID >= 1"

    # Call the method under test
    result = extractor._execute_query(mock_wiql_client, query)

    # Assertions
    assert len(result.work_items) == 2, "Method did not return expected number of work items."
    assert result.work_items[0].id == 1
    assert result.work_items[1].id == 2


def test_query_execution_handles_azure_devops_service_error(extractor: ADOBronzeExtractor, mock_wiql_client: Mock):
    """Test handling of AzureDevOpsServiceError during query execution."""
    mock_wiql_client.query_by_wiql.side_effect = raise_azure_devops_service_error

    with pytest.raises(AzureDevOpsServiceError):
        extractor._execute_query(mock_wiql_client, "SELECT System.ID FROM WorkItems WHERE System.ID >= 1")


# fetch_work_items


def test_fetch_work_items_with_ids(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch work items with a list of workitem ids."""
    # Set up mocks
    mock_wiql_client.get_work_items.return_value = ["work_item1", "work_item2"]

    ids = [1, 2]

    # Call the method under test
    result = extractor._fetch_work_items(mock_wiql_client, ids)

    # Assertions
    mock_wiql_client.get_work_items.assert_called_once_with(ids, expand="All")
    assert result == [
        "work_item1",
        "work_item2",
    ], "The fetch_work_items method did not return the expected list of work items."


def test_fetch_work_items_with_no_ids(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch work items for empty list of ids."""
    ids = []  # Empty list of IDs

    # Call the method under test
    result = extractor._fetch_work_items(mock_wiql_client, ids)

    # Assertions
    mock_wiql_client.get_work_items.assert_not_called()

    assert result == [], "The fetch_work_items method should return an empty list when no IDs are provided."


# fetch_comments_for_work_items


def test_fetch_comments_for_key_result_work_items(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch comments for key result work item type."""
    # Set up mocks
    work_items = _get_example_key_results()
    mock_wiql_client.get_comments.return_value.comments = ["Comment 1", "Comment 2"]

    # Call the method under test
    result = extractor._fetch_comments_for_work_items(mock_wiql_client, work_items)

    # Assertions
    assert mock_wiql_client.get_comments.call_count == len(work_items)
    assert len(result) == 2
    assert result[0]["work_item_id"] == 1
    assert len(result[0]["comments"]) == 2


def test_fetch_comments_for_team_epic_work_items(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch comments for team epic work item type."""
    # Set up mocks
    work_items = [
        MockWorkItem.with_type(1, "Team Epic"),
        MockWorkItem.with_type(2, "Team Epic"),
    ]
    mock_wiql_client.get_comments.return_value.comments = ["Comment 1", "Comment 2"]

    # Call the method under test
    result = extractor._fetch_comments_for_work_items(mock_wiql_client, work_items)

    # Assertions
    assert mock_wiql_client.get_comments.call_count == len(work_items)
    assert len(result) == 2
    assert result[0]["work_item_id"] == 1
    assert len(result[0]["comments"]) == 2


def test_fetch_comments_for_initiative_work_items(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch comments for initiative work item type."""
    # Set up mocks
    work_items = [
        MockWorkItem.with_type(1, "Initiative"),
        MockWorkItem.with_type(2, "Initiative"),
    ]
    mock_wiql_client.get_comments.return_value.comments = ["Comment 1", "Comment 2"]

    # Call the method under test
    result = extractor._fetch_comments_for_work_items(mock_wiql_client, work_items)

    # Assertions
    assert mock_wiql_client.get_comments.call_count == len(work_items)
    assert len(result) == 2
    assert result[0]["work_item_id"] == 1
    assert len(result[0]["comments"]) == 2


def test_fetch_comments_for_other_work_items(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch comments for other workitem types which should be filtered out."""
    # Set up mocks
    work_items = [
        MockWorkItem.with_type(1, "User Story"),
        MockWorkItem.with_type(2, "Bug"),
    ]

    # Call the method under test
    result = extractor._fetch_comments_for_work_items(mock_wiql_client, work_items)

    # Assertions
    mock_wiql_client.get_comments.assert_not_called()
    assert result == [], "The method should not return comments for work item types that are filtered out."


def test_fetch_comments_for_mixed_work_items(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch comments for mixed workitem types including some of interest and others."""
    # Set up mocks
    work_items = [
        MockWorkItem.with_type(1, "User Story"),
        MockWorkItem.with_type(2, "Key Result"),
        MockWorkItem.with_type(3, "Team Epic"),
    ]
    mock_wiql_client.get_comments.return_value.comments = ["Comment 1", "Comment 2"]

    # Call the method under test
    result = extractor._fetch_comments_for_work_items(mock_wiql_client, work_items)

    # Assertions
    assert mock_wiql_client.get_comments.call_count == 2
    assert len(result) == 2
    assert result[0]["work_item_id"] == 2
    assert len(result[0]["comments"]) == 2


def test_fetch_comments_for_empty_work_items(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetch comments for when workitem list is empty."""
    work_items = []

    # Call the method under test
    result = extractor._fetch_comments_for_work_items(mock_wiql_client, work_items)

    # Verify get_comments was not called
    mock_wiql_client.get_comments.assert_not_called()

    # Assertions
    assert result == [], "The method should return an empty list when no work items are provided."


# fetch_remaining_work_items


def test_fetch_remaining_work_items(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetching remaining work item ids."""
    mock_execute_query_result = ["work_item3", "work_item4"]

    # Patch the methods to control their return values and verify calls
    with (
        patch.object(*method_self_name(extractor._construct_query), return_value="mock_query") as mock_construct_query,
        patch.object(
            *method_self_name(extractor._execute_query), return_value=mock_execute_query_result
        ) as mock_execute_query,
    ):
        counter = 100
        since_date = "2021-01-01"
        until_date = "2021-01-31"

        # Call the method under test
        result = extractor._fetch_remaining_work_items(mock_wiql_client, counter, since_date, until_date)

        # Assertions
        mock_construct_query.assert_called_once_with(counter, None, since_date, until_date)
        mock_execute_query.assert_called_once_with(mock_wiql_client, "mock_query")
        assert (
            result == mock_execute_query_result
        ), "The method did not return the expected list of remaining work items."


# fetch_detailed_work_items


def test_fetch_detailed_work_items_multiple_batches(monkeypatch, extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetching workitem details in multiple batches."""
    # Setup Mocks
    mock_fetch_work_items = Mock(side_effect=[["work_item1", "work_item2"], ["work_item3"]])

    # The ADO_GET_WORKITEM_LIMIT constant needs to be monkeypatched in the extract module
    # where it is used and not in the module where it is defined, as literals are
    # not referenced through imports but rather copied
    import extract

    monkeypatch.setattr(extract, "ADO_GET_WORKITEM_LIMIT", 2)  # Example limit for testing

    # Patch the fetch_work_items method
    with patch.object(*method_self_name(extractor._fetch_work_items), mock_fetch_work_items):
        ids = [
            1,
            2,
            3,
        ]  # IDs that would form two batches given the ADO_GET_WORKITEM_LIMIT
        result = extractor._fetch_detailed_work_items(mock_wiql_client, ids)

        # Assertions
        assert mock_fetch_work_items.call_count == 2
        mock_fetch_work_items.assert_any_call(mock_wiql_client, [1, 2])
        mock_fetch_work_items.assert_any_call(mock_wiql_client, [3])
        assert result == [
            "work_item1",
            "work_item2",
            "work_item3",
        ], "Did not correctly aggregate work items from multiple batches."


def test_fetch_detailed_work_items_single_batch(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test fetching workitem details in a single batch batches."""
    # Setup Mocks
    mock_fetch_work_items = Mock(side_effect=[["work_item1", "work_item2"]])

    # Patch the fetch_work_items method
    with patch.object(*method_self_name(extractor._fetch_work_items), mock_fetch_work_items):
        ids = [1, 2]  # IDs that would form 1 batch given the ADO_GET_WORKITEM_LIMIT
        result = extractor._fetch_detailed_work_items(mock_wiql_client, ids)

        # Assertions
        assert mock_fetch_work_items.call_count == 1
        mock_fetch_work_items.assert_any_call(mock_wiql_client, [1, 2])
        assert result == [
            "work_item1",
            "work_item2",
        ], "Did not correctly aggregate work items from multiple batches."


# query_and_process_ado_work_items


def mock_query_by_wiql_side_effect(*args, **kwargs) -> Mock:
    """Helper method."""
    if mock_query_by_wiql_side_effect.call_count == 0:
        mock_query_by_wiql_side_effect.call_count += 1
        # Return non empty list of mock work items for the 1st call
        return Mock(work_items=_get_example_key_results())
    else:
        # Return an empty list for subsequent calls
        return Mock(work_items=[])


mock_query_by_wiql_side_effect.call_count = 0


def test_query_and_process_with_mocked_execute_query(extractor: ADOBronzeExtractor, mock_wiql_client):
    """Test query ado logic to fetch workitem and comments."""
    # Setup Mocks
    mock_wiql_client.query_by_wiql.side_effect = mock_query_by_wiql_side_effect

    # Call the method under test
    fetched_work_items = _get_example_key_results()
    with (
        patch.object(*method_self_name(extractor._fetch_work_items), return_value=fetched_work_items),
        patch.object(*method_self_name(extractor._create_ado_connection), return_value=mock_wiql_client),
    ):
        work_items, comments = extractor.query_and_process_ado_work_items("fake_token")

    # Assertions
    assert len(work_items) == 2
    assert len(comments) == 2


# store_in_databricks


@pytest.mark.parametrize(
    "table_exists, expected_merge_call_count, expected_overwrite_call_count",
    [
        (True, 1, 0),  # Scenario where table exists
        (False, 0, 1),  # Scenario where table does not exist
    ],
    ids=["table_exists", "table_does_not_exist"],  # Naming the parameterized test cases
)
def test_store_in_databricks(
    extractor: ADOBronzeExtractor,
    table_exists,
    expected_merge_call_count,
    expected_overwrite_call_count,
):
    """Test store data in databricks method for ado."""
    work_items = [{"id": 1, "rev": 1, "fields": {}, "url": "url1", "relations": []}]
    comments = [
        {
            "work_item_id": 1,
            "comments": [
                {"id": 1, "text": "Comment 1"},
                {"id": 2, "text": "Comment 2"},
            ],
        }
    ]

    with (
        patch("rddlib.delta_table_utils.table_exists", return_value=table_exists) as mock_table_exists,
        patch("rddlib.delta_table_utils.merge") as mock_merge,
        patch("rddlib.delta_table_utils.overwrite") as mock_overwrite,
        patch("rddlib.delta_table_utils.update_table_metadata"),
    ):
        extractor.store_in_databricks(work_items, comments)

        mock_table_exists.assert_called_once_with("catalog_name.schema_name.table_name")
        assert mock_merge.call_count == expected_merge_call_count
        assert mock_overwrite.call_count == expected_overwrite_call_count
