resources:
  jobs:
    ado_etl_release:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "ADA Release Graph - ADO - E2E - Nightly"
      description: Ingest ADO workitems
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "E2E"
        schedule: "Nightly"
        "ADA Release Graph": ""
        "ADO": ""
        dev_schedule_reason: "performance-monitoring"

      parameters:
        - name: graph_database
          default: "ada_release_v1"
        - name: since
          default: ""
        - name: until
          default: ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      # Schedules
      # Dev - PAUSED
      # QA - UNPAUSED, Runs every Wednesday night - 12:13 AM UTC
      # Prod - UNPAUSED Runs nightly at 12:13 AM UTC
      # schedule:
      #   quartz_cron_expression: ${var.nightly_schedule}
      #   timezone_id: UTC
      #   pause_status: ${var.nightly_trigger}

      tasks:
        - task_key: ado_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --application
              - release
              - --since
              - "{{job.parameters.since}}"
              - --until
              - "{{job.parameters.until}}"
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_ado_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: ado_transform
          depends_on:
            - task_key: ado_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --application
              - release
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_ado_job_cluster

        - task_key: ado_delete_edges
          depends_on:
            - task_key: ado_transform
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "stardog",
                "delete-edges",
                "--database",
                "{{job.parameters.graph_database}}",
                "--source",
                "ado",
              ]
          job_cluster_key: rdd_ado_job_cluster

        - task_key: ado_load
          depends_on:
            - task_key: ado_delete_edges
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - "{{job.parameters.graph_database}}"
              - --mapping_file
              - ado_release.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_ado_job_cluster

        - task_key: ado_delete_edges_v2_alpha
          depends_on:
            - task_key: ado_transform
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "stardog",
                "delete-edges",
                "--database",
                "ada_release_v2",
                "--source",
                "ado",
              ]
          job_cluster_key: rdd_ado_job_cluster

        - task_key: ado_load_v2_alpha
          depends_on:
            - task_key: ado_delete_edges_v2_alpha
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - "ada_release_v2"
              - --mapping_file
              - ado_release.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_ado_job_cluster

      job_clusters:
        - job_cluster_key: rdd_ado_job_cluster
          new_cluster:
            data_security_mode: SINGLE_USER
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.driver_instance_pool_id}
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            custom_tags:
              "ResourceClass": "SingleNode"
