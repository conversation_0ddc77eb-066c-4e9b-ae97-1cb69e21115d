resources:
  jobs:
    ado_e2e_release_test:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - ADO - E2E Test
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "E2E"
        test: ""
        "ADA Release Graph": ""
        "ADO": ""

        # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: cleanup_bronze_test_tables
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "bronze_${var.env}",
                "-s",
                "ada_release_graph",
                "-t",
                "ado_test",
              ]
          job_cluster_key: rdd_ado_test_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: ado_extract
          depends_on:
            - task_key: cleanup_bronze_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --application
              - release
              - --test_mode

          job_cluster_key: rdd_ado_test_job_cluster

        - task_key: cleanup_silver_test_tables
          depends_on:
            - task_key: ado_extract
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "silver_${var.env}",
                "-s",
                "ada_release_graph",
                "-t",
                "ado_nodes_test",
                "-t",
                "ado_edges_test",
                "-t",
                "ado_comments_test",
              ]
          job_cluster_key: rdd_ado_test_job_cluster

        - task_key: ado_transform
          depends_on:
            - task_key: cleanup_silver_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --application
              - release
              - --test_mode
          job_cluster_key: rdd_ado_test_job_cluster

        - task_key: cleanup_stardog_test_db
          depends_on:
            - task_key: ado_transform
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters: ["stardog", "drop", "--database", "ada_ado_e2e_test"]
          job_cluster_key: rdd_ado_test_job_cluster

        - task_key: ado_delete_edges
          depends_on:
            - task_key: cleanup_stardog_test_db
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "stardog",
                "delete-edges",
                "--database",
                "ada_ado_e2e_test",
                "--source",
                "ado",
              ]
          job_cluster_key: rdd_ado_test_job_cluster

        - task_key: ado_load
          depends_on:
            - task_key: ado_delete_edges
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_ado_e2e_test
              - --mapping_file
              - ado_release.j2
          job_cluster_key: rdd_ado_test_job_cluster

        - task_key: ado_test_data
          depends_on:
            - task_key: ado_load
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/e2e_tests/test_ado.py
            parameters:
              - --db
              - ada_ado_e2e_test
          job_cluster_key: rdd_ado_test_job_cluster

      job_clusters:
        - job_cluster_key: rdd_ado_test_job_cluster
          new_cluster:
            data_security_mode: SINGLE_USER
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.driver_instance_pool_id}
            spark_conf:
              "spark.databricks.cluster.profile": "singleNode"
              "spark.master": "local[*, 4]"
            custom_tags:
              "ResourceClass": "SingleNode"
