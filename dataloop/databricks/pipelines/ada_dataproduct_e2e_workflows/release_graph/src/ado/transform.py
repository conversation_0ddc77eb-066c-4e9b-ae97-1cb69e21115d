"""Module to transform ADO workitems from bronze to silver layer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging

from constants.bronze import BRONZE_TABLE
from constants.common import ADO_PREFIX
from constants.silver import (
    SILVER_NODES_COLUMNS_OF_INTEREST,
    SILVER_RELATIONS_MAPPING,
    SILVER_SCHEMA_COMMENTS,
    SILVER_SCHEMA_MILESTONES,
    SILVER_SCHEMA_NODES,
    SILVER_SCHEMA_RELATIONS,
    SILVER_TABLE_COMMENTS,
    SILVER_TABLE_EDGES,
    SILVER_TABLE_MILESTONES,
    SILVER_TABLE_NODES,
    TABLE_DESCRIPTIONS,
)
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql import functions as F
from pyspark.sql.functions import (
    coalesce,
    col,
    concat,
    explode,
    expr,
    isnotnull,
    lit,
    regexp_extract,
    regexp_replace,
    to_date,
    to_timestamp,
)
from rddlib import DBX_APP_SCHEMA_MAP, FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_dbx_env_catalog, quality_check, setup_databricks_logging
from rddlib.quality_check import log_quality_check_result
from rddlib.utils import nullif_blank

logger = logging.getLogger(__name__)


class ADOSilverTransformer:
    """Class for ADO silver transformation."""

    catalog_bronze: str
    schema_name: str
    catalog_silver: str
    table_suffix: str
    spark: SparkSession

    def __init__(
        self,
        catalog_bronze: str,
        schema_name: str,
        catalog_silver: str,
        table_suffix: str,
        spark: SparkSession,
    ):
        """Initializes the ADOSilverTransformer with specific parameters.

        Args:
            catalog_bronze (str): The name of the catalog. Ex. bronze_dev
            schema_name (str): The name of the schema within the catalog. Ex. release
            catalog_silver (str): The name of the catalog. Ex. silver_dev
            table_suffix (str): The suffix to append to table names if in test mode.
            spark (SparkSession): Spark session.
        """
        self.catalog_bronze = catalog_bronze
        self.schema_name = schema_name
        self.catalog_silver = catalog_silver
        self.table_suffix = table_suffix
        self.spark = spark

    # Common

    def transform_data(self, table_full_name: str | None = None) -> None:
        """Transform workitem nodes, edges and comments."""
        # Read ADO workitems from the bronze layer
        if not table_full_name:
            table_full_name = f"{self.catalog_bronze}.{self.schema_name}.`{BRONZE_TABLE}{self.table_suffix}`"
        df_bronze = self.spark.read.table(table_full_name)

        # Transform and update workitem nodes
        df_silver_nodes = self._handle_ado_nodes(df_bronze)
        # Run the data quality check
        self._run_quality_check(df_silver_nodes, column_to_check="id")
        merge_condition = "target.id = source.id"
        self._update_delta_table(SILVER_TABLE_NODES, df_silver_nodes, merge_condition)

        # Transform and update workitem edges
        df_silver_relations = self._handle_ado_edges(df_bronze)
        df_silver_relations = df_silver_relations.dropDuplicates(["source", "target", "type"])
        # Run the data quality check
        self._run_quality_check(df_silver_relations, column_to_check="source")
        merge_condition = (
            "target.source = source.source and target.target = source.target and target.type = source.type"
        )
        self._update_delta_table(SILVER_TABLE_EDGES, df_silver_relations, merge_condition)

        # Transform and update comments
        df_silver_comments = self._handle_ado_comments(df_bronze)
        # Run the data quality check
        self._run_quality_check(df_silver_comments, column_to_check="comment_id")
        merge_condition = "target.comment_id = source.comment_id"
        self._update_delta_table(SILVER_TABLE_COMMENTS, df_silver_comments, merge_condition)

        # Transform and update milestone -> initiative linkage
        df_milestones = self._handle_ado_milestones()
        merge_condition = "target.milestone_id = source.milestone_id and target.initiative_id = source.initiative_id"
        self._update_delta_table(SILVER_TABLE_MILESTONES, df_milestones, merge_condition)

    def _update_delta_table(self, table_name: str, data: DataFrame, merge_condition: str) -> None:
        """Update the delta table with the given data."""
        extended_table_name = f"{table_name}{self.table_suffix}"
        table_full = f"{self.catalog_silver}.{self.schema_name}.{extended_table_name}"

        if dtu.table_exists(table_full):
            # Check if schema is consistent
            if dtu.table_has_schema(table_full, data.schema):
                dtu.merge(data, table_full, merge_condition)
            else:
                logger.info(f"Schema of table '{table_full}' changed. The table will be overwritten.")
                dtu.overwrite(data, table_full, overwrite_schema=True)
        else:
            dtu.overwrite(data, table_full)

        # Add metadata to the table
        description = TABLE_DESCRIPTIONS.get(table_name, "")
        dtu.update_table_metadata(table_full, description)

    def _handle_ado_nodes(self, df: DataFrame) -> DataFrame:
        """Handle workitem nodes - pick fields of interest and transform for silver layer."""
        # Handle 'id'
        id_col = col("id").alias("id")

        # Handle workitem fields (fields of interest for silver layer)
        fields_cols = []
        for silver_field, bronze_field in SILVER_NODES_COLUMNS_OF_INTEREST.items():
            if isinstance(bronze_field, list):
                # Multiple possible mappings bronze fields to silver field
                # Ordered by descending priority
                col_expression = coalesce(*[col("fields").getField(field) for field in bronze_field])
            else:
                # Single mapping from bronze to silver field
                col_expression = col("fields").getField(bronze_field)
                # Convert to timestamp if field contains Date
                if "Date" in bronze_field:
                    col_expression = to_timestamp(col_expression)

            # Add column expression to list
            fields_cols.append(col_expression.alias(silver_field))

        # Select 'id' and workitem fields (fields of interest for silver layer)
        df_clean = df.select(id_col, *fields_cols)

        # Add 2 new columns - ado_type and ado_state with PREFIX to help with SMS mapping.
        df_clean = df_clean.withColumn(
            "ado_type",
            concat(
                lit(ADO_PREFIX),
                regexp_replace(col("workitem_type"), " ", ""),
            ),
        )

        # Store ado workitem state in camel case. Ex: Closed -> ado:closed, In Progress -> ado:inProgress
        df_clean = df_clean.withColumn(
            "ado_state",
            concat(
                lit(ADO_PREFIX),
                expr("lower(substring(regexp_replace(state, ' ', ''), 1, 1))"),
                expr("substring(regexp_replace(state, ' ', ''), 2)"),
            ),
        )

        # Create a DataFrame applying the silver layer schema
        return self.spark.createDataFrame(df_clean.rdd, schema=SILVER_SCHEMA_NODES)

    def _handle_ado_edges(self, df: DataFrame) -> DataFrame:
        """Handle workitem edges - relations like parent, child, successor, commit, pull request etc."""
        # Explode the 'relations' array to have one row per relation
        df_exploded = df.select("id", "relations").withColumn("relation", explode("relations"))

        # Iterate through the relations mapping and create new columns based on expressions
        for attribute, expression in SILVER_RELATIONS_MAPPING.items():
            df_exploded = df_exploded.withColumn(attribute, expr(expression))

        # Select the required columns
        df_relations = df_exploded.select("source", "source_iri", "type", "target", "target_iri")

        # Drop temporary columns used during transformation
        df_relations = df_relations.drop("relation", "relations")

        # Create a DataFrame applying the silver layer schema
        return self.spark.createDataFrame(df_relations.rdd, schema=SILVER_SCHEMA_RELATIONS)

    def _handle_ado_comments(self, df: DataFrame) -> DataFrame:
        """Handle workitem comments for Key Results, Team Epics and Initiatives.

        Args:
            df (DataFrame): The bronze dataframe.

        Returns:
            DataFrame: The resulting silver comments dataframe.
        """
        df_exploded = df.select("id", "comments").withColumn("comment", explode("comments"))

        df_result = (
            df_exploded.select(
                col("id"),
                col("comment.id").alias("comment_id"),
                col("comment.text").alias("text"),
                col("comment.created_date").alias("created_date"),
            )
            # Common
            .withColumn(
                "updated_at",
                to_date(regexp_extract("text", r">As-?Of: (\d{4}-\d{2}-\d{2})<", 1), "yyyy-MM-dd"),
            )
            .where(isnotnull("updated_at"))
            # Key Result
            .withColumn(
                "kr_value",
                nullif_blank(regexp_extract("text", r">KR-Value: (\d+)<", 1)).cast("double"),
            )
            # Team Epic & Initiative
            .withColumn(
                "te_status",
                nullif_blank(regexp_extract("text", r">Status: (.+?)<", 1)),
            )
            .withColumn(  # TODO: Should enum values be verified here?
                "te_forecast",
                nullif_blank(regexp_extract("text", r">Forecast: (.+?)<", 1)),
            )
            # Filter for at least one of the columns being defined
            .where("isnotnull(kr_value) OR (isnotnull(te_status) AND isnotnull(te_forecast))")
            .select(
                col("id"),
                col("comment_id"),
                col("updated_at"),
                col("kr_value"),
                col("te_status"),
                col("te_forecast"),
            )
        )

        return self.spark.createDataFrame(df_result.rdd, schema=SILVER_SCHEMA_COMMENTS)

    def _handle_ado_milestones(self) -> DataFrame:
        """Handle links between ADO workitem type Milestone and Needs element.

        - Filters milestones from ADO nodes that have a "hasPredecessor" relationship.
        - Extracts and transforms the "Needs" feature element from the title of linked ADO initiatives.
        - Joins milestones with their corresponding initiatives based on these criteria.

        Returns:
            DataFrame: The resulting silver dataframe for milestones with their linked initiatives.
        """

        silver_table_nodes = f"{self.catalog_silver}.{self.schema_name}.`{SILVER_TABLE_NODES}{self.table_suffix}`"
        silver_table_edges = f"{self.catalog_silver}.{self.schema_name}.`{SILVER_TABLE_EDGES}{self.table_suffix}`"
        ado_nodes = self.spark.read.table(silver_table_nodes)
        ado_edges = self.spark.read.table(silver_table_edges)

        # Filter milestones from ado_nodes
        milestones_df = ado_nodes.filter(ado_nodes.workitem_type == "Milestone").select(
            F.col("id").alias("milestone_id"), F.col("title").alias("milestone_title")
        )

        # Filter milestones which have an hasPredecessor relation
        milestones_df = (
            milestones_df.join(ado_edges, milestones_df.milestone_id == ado_edges.source, "inner")
            .select(milestones_df["milestone_id"], ado_edges["target"], ado_edges["type"])
            .filter(ado_edges["type"] == "urn:pace:ontology:ado:hasPredecessor")
        )

        # Filter initiatives from ado_nodes,
        # Extract the FI_ABC_123 pattern from initiative titles,
        # where ABC represents any three uppercase letters (ex - ACA, HWP etc),
        # and transform it to "FEATURE_INCREMENT_ABC_123"
        initiatives_df = (
            ado_nodes.filter(ado_nodes.workitem_type == "Initiative")
            .select(F.col("id").alias("initiative_id"), F.col("title").alias("initiative_title"))
            .withColumn("extracted_id", F.regexp_extract("initiative_title", r"\[FI_([A-Z]{3}_[0-9]{3})\]", 1))
            .withColumn(
                "needs_feature",
                F.when(
                    F.col("extracted_id") != "", F.concat(F.lit("FEATURE_INCREMENT_"), F.col("extracted_id"))
                ).otherwise(
                    None
                ),  # If no ID is extracted, leave as null
            )
            .filter(F.col("needs_feature").isNotNull())
        )

        # Join filtered milestones with the initiatives that have a valid needs feature
        final_df = milestones_df.join(
            initiatives_df, milestones_df.target == initiatives_df.initiative_id, "inner"
        ).select(
            milestones_df["milestone_id"],
            milestones_df["type"],
            initiatives_df["initiative_id"],
            initiatives_df["needs_feature"],
        )

        return self.spark.createDataFrame(final_df.rdd, schema=SILVER_SCHEMA_MILESTONES)

    def _run_quality_check(self, df: DataFrame, column_to_check: str) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        total_count = df.count()

        missing_value_rows = quality_check.missing_value_detection(df, column=column_to_check)
        missing_values_count = missing_value_rows.count()
        if missing_values_count == 0:
            log_quality_check_result(True, "missing_value_detection", None)
        else:
            log_quality_check_result(False, "missing_value_detection", missing_values_count)

        # Duplicate check
        _, duplicate_id_rows = quality_check.deduplicate(
            df,
            subset=[column_to_check],
            time_window=None,
            use_hashing=False,
            just_test=False,
        )
        count_duplicates = len(duplicate_id_rows)
        if count_duplicates == 0:
            log_quality_check_result(True, "deduplicate", None)
        else:
            log_quality_check_result(False, "deduplicate", f"{count_duplicates / total_count}")


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Transform ado workitems")
    parser.add_argument(
        "-a",
        "--application",
        dest="app",
        default="release",
        help="Application, ex. release, rng",
    )
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )
    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()
    run_id = args.run_id
    app = args.app
    test_mode = args.test_mode

    spark = SparkSession.builder.getOrCreate()

    catalog_bronze = get_dbx_env_catalog("bronze")
    catalog_silver = get_dbx_env_catalog("silver")
    schema_name = DBX_APP_SCHEMA_MAP[app]
    table_suffix = "_test" if test_mode else ""

    # Setup logging
    setup_databricks_logging(FullSchemaName(catalog_silver, schema_name, False), "ado/silver", run_id=run_id)

    transformer = ADOSilverTransformer(catalog_bronze, schema_name, catalog_silver, table_suffix, spark)

    transformer.transform_data()
