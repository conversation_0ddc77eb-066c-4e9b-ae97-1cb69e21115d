"""E2E tests for ADX SCA Component Mapping."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

# Standard imports
import argparse

import stardog

# 3rd party library imports
from rddlib import get_rdd_secret, get_stardog_access_token
from requests.exceptions import ConnectTimeout, MissingSchema
from stardog.exceptions import StardogException, TransactionException


def connect_to_stardog(db_name: str):
    """Set up the connection to Stardog."""
    conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }

    with stardog.Admin(**conn_details) as admin:
        if not any(db.name == db_name for db in admin.databases()):
            raise Exception(f"Database '{db_name}' does not exist in Stardog.")

    return stardog.Connection(db_name, **conn_details)


def run_query(conn, query: str) -> list:
    """Execute a SPARQL query and return the results."""
    try:
        response = conn.select(query)
        return response["results"]["bindings"]
    except (TransactionException, MissingSchema, ConnectTimeout, StardogException) as e:
        print(f"Could not execute query. Error: {str(e)}")
        return None


def test_existence_of_sca_components(conn):
    """Test for the existence of ScaComponent instances."""
    query = """
        SELECT ?sca_component
        WHERE {
            ?sca_component a adx:ScaComponent .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No ScaComponent instances found."
    print(f"Test passed: {len(results)} ScaComponent instances exist.")


def test_sca_component_attributes(conn):
    """Verify that each ScaComponent has all required attributes."""
    query = """
        SELECT ?sca_component ?label ?revision ?date_last_changed ?name
               ?source_path ?build_path ?rule ?int_build_issues_count
               ?d_doxygen_coverage ?codeowners ?deployments ?tags
        WHERE {
            ?sca_component a adx:ScaComponent ;
                           rdfs:label ?label ;
                           adx:hasRevision ?revision ;
                           adx:lastChanged ?date_last_changed ;
                           adx:hasName ?name ;
                           adx:hasSourcePath ?source_path ;
                           adx:hasBuildPath ?build_path ;
                           adx:hasRule ?rule ;
                           adx:hasBuildIssuesCount ?int_build_issues_count ;
                           adx:hasDoxygenCoverage ?d_doxygen_coverage ;
                           adx:hasCodeOwners ?codeowners ;
                           adx:hasDeployments ?deployments ;
                           adx:hasTags ?tags .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "ScaComponent instances do not have all required attributes."
    print(f"Test passed: {len(results)} ScaComponent attributes verified.")


def test_artifact_version_linkage(conn):
    """Test that each ArtifactVersion is linked to a ScaComponent."""
    query = """
        SELECT ?version ?sca_component
        WHERE {
            ?version a pace:ArtifactVersion ;
                     pace:validFor ?sca_component .
            ?sca_component a adx:ScaComponent .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "ArtifactVersion linkage to ScaComponent not found."
    print(f"Test passed: {len(results)} ArtifactVersion linkages verified.")


def test_data_types(conn):
    """Check that data types are correct for numerical attributes."""
    query = """
        SELECT ?sca_component ?int_build_issues_count ?d_doxygen_coverage
        WHERE {
            ?sca_component a adx:ScaComponent ;
                           adx:hasBuildIssuesCount ?int_build_issues_count ;
                           adx:hasDoxygenCoverage ?d_doxygen_coverage .
            FILTER (datatype(?int_build_issues_count) = xsd:integer && datatype(?d_doxygen_coverage) = xsd:double)
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "Incorrect data types for numerical attributes."
    print(f"Test passed: {len(results)} data types verified for numerical attributes.")


def test_sca_issues_count_by_severity(conn):
    """Verify severity counts are properly mapped."""
    query = """
        SELECT ?int_sca_issues_count_by_severity_1
                ?int_sca_issues_count_by_severity_2 ?int_sca_issues_count_by_severity_3
                ?int_sca_issues_count_by_severity_4 ?int_sca_issues_count_by_severity_5
        WHERE {
            adx:viper_light_ray_conversion_extras_24d7d00096b196c381d672083debbf95e9bf1424 a adx:ScaComponent ;
                           adx:hasScaIssuesCountBySeverity1 ?int_sca_issues_count_by_severity_1 ;
                           adx:hasScaIssuesCountBySeverity2 ?int_sca_issues_count_by_severity_2 ;
                           adx:hasScaIssuesCountBySeverity3 ?int_sca_issues_count_by_severity_3 ;
                           adx:hasScaIssuesCountBySeverity4 ?int_sca_issues_count_by_severity_4 ;
                           adx:hasScaIssuesCountBySeverity5 ?int_sca_issues_count_by_severity_5 .
        }
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "Severity counts not mapped correctly."
    expected_values = {
        "int_sca_issues_count_by_severity_1": "3",
        "int_sca_issues_count_by_severity_2": "9",
        "int_sca_issues_count_by_severity_3": "12",
        "int_sca_issues_count_by_severity_4": "16",
        "int_sca_issues_count_by_severity_5": "0",
    }

    for severity, expected in expected_values.items():
        actual = results[0][severity]["value"]
        assert actual == expected, f"Expected {severity} to be {expected}, but got {actual}"
    print(f"Test passed: {len(results)} severity counts verified.")


def test_tags_and_deployments(conn):
    """Verify that tags and deployments exist."""
    query = """
        SELECT ?sca_component ?tags ?deployments
        WHERE {
            ?sca_component a adx:ScaComponent ;
                           adx:hasTags ?tags ;
                           adx:hasDeployments ?deployments .
        } LIMIT 1
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "Tags and deployments not found for ScaComponent."
    print(f"Test passed: {len(results)} tags and deployments verified.")


def main(db: str) -> None:
    """Main function to run all tests."""
    conn = connect_to_stardog(db)
    if conn is None:
        raise Exception("Stardog connection error")

    try:
        test_existence_of_sca_components(conn)
        test_sca_component_attributes(conn)
        test_artifact_version_linkage(conn)
        test_data_types(conn)
        test_sca_issues_count_by_severity(conn)
        test_tags_and_deployments(conn)
    finally:
        conn.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="E2E Test for the ADX SCA Component Mapping")
    parser.add_argument("-d", "--db", dest="db", help="Stardog database")
    args, unknown = parser.parse_known_args()
    main(args.db)
