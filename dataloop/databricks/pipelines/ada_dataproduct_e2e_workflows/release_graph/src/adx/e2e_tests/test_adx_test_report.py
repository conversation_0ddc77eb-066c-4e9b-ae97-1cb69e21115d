"""E2E tests for ADX Test Reports ETL Pipeline."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse

import stardog
from rddlib import get_rdd_secret, get_stardog_access_token
from requests.exceptions import ConnectTimeout, MissingSchema
from stardog.exceptions import StardogException, TransactionException


def connect_to_stardog(db_name: str):
    """Set up the connection to Stardog."""
    conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }

    with stardog.Admin(**conn_details) as admin:
        if not any(db.name == db_name for db in admin.databases()):
            print("Database does not exist. Exiting...")
            raise Exception(f"Database '{db_name}' does not exist in Stardog.")

    return stardog.Connection(db_name, **conn_details)


def run_query(conn, query: str) -> list:
    """Execute a SPARQL query and return the results."""
    try:
        response = conn.select(query)
        return response["results"]["bindings"]
    except (TransactionException, MissingSchema, ConnectTimeout, StardogException) as e:
        print(f"Could not execute query. Error: {str(e)}")
        return None


def test_total_number_of_reports_ingested(conn):
    """Test the total number of reports ingested."""
    query = """
        SELECT (COUNT(?report) AS ?totalReports)
        WHERE {
            ?report a adx:Report .
        }
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No results found for total number of reports."
    total_reports = int(results[0]["totalReports"]["value"])
    assert total_reports > 0, "The total number of reports should be greater than zero."
    print(f"Test passed: {total_reports} reports ingested.")


def test_artifact_version_presence(conn):
    """Test the presence of artifact versions."""
    query = """
        SELECT ?version
        WHERE {
            ?version a pace:ArtifactVersion .
        }
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No artifact versions found."
    print(f"Test passed: {len(results)} artifact versions found.")


def test_retrieve_report_details(conn):
    """Test retrieval of report details."""
    query = """
        SELECT ?report ?testName ?testDescription ?runDuration ?reportCreationTime
        WHERE {
            ?report a adx:Report ;
                    adx:testName ?testName ;
                    adx:testDescription ?testDescription ;
                    adx:runDuration ?runDuration ;
                    adx:reportCreationTime ?reportCreationTime .
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No report details found."
    print(f"Test passed: {len(results)} report details retrieved.")


def test_relationship_between_reports_and_test_cases(conn):
    """Test relationship between reports and test cases."""
    query = """
        SELECT ?report ?testCase
        WHERE {
            ?report a adx:Report ;
                    adx:hasTestCase ?testCase .
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No relationships found between reports and test cases."
    print(f"Test passed: {len(results)} report-test case relationships verified.")


def test_get_passed_tests(conn):
    """Test for passed tests."""
    query = """
        SELECT ?report ?testName
        WHERE {
            ?report a adx:Report ;
                    adx:testName ?testName ;
                    adx:testResultsPassed true .
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No passed tests found."
    print(f"Test passed: {len(results)} passed tests verified.")


def test_verify_datatypes(conn):
    """Test to verify data types."""
    query = """
        SELECT ?report ?testName ?runDuration ?reportCreationTime
        WHERE {
            ?report a adx:Report ;
                    adx:testName ?testName ;
                    adx:runDuration ?runDuration ;
                    adx:reportCreationTime ?reportCreationTime .
            FILTER (datatype(?runDuration) = xsd:double && datatype(?reportCreationTime) = xsd:dateTime)
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No reports found with correct datatypes."
    print(f"Test passed: {len(results)} reports with correct datatypes verified.")


def test_list_test_platforms(conn):
    """Test to list test platforms."""
    query = """
        SELECT ?report ?testPlatform
        WHERE {
            ?report a adx:Report ;
                    pace:runsOnTestPlatform ?testPlatform .
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No test platforms found."
    print(f"Test passed: {len(results)} test platforms listed.")


def test_list_sut_profiles(conn):
    """Test to list SUT profiles."""
    query = """
        SELECT ?report ?sutProfile
        WHERE {
            ?report a adx:Report ;
                    pace:hasSutProfile ?sutProfile .
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No SUT profiles found."
    print(f"Test passed: {len(results)} SUT profiles listed.")


def test_linkage_between_report_and_version(conn):
    """Test linkage between report and version."""
    query = """
        SELECT ?report ?version
        WHERE {
            ?version a pace:ArtifactVersion ;
                     pace:validFor ?report .
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No linkages found between reports and versions."
    print(f"Test passed: {len(results)} report-version linkages verified.")


def test_reports_with_specific_kpis(conn):
    """Test reports with specific KPIs."""
    query_1 = """
        SELECT ?report ?kpiValue
        WHERE {
            ?report a adx:Report ;
                    adx:testResultsKpiValue ?kpiValue .
            FILTER (xsd:double(?kpiValue) > 0.1)
        }
        LIMIT 10
    """
    query_2 = """
        SELECT ?report ?kpiValue
        WHERE {
        ?report a adx:Report ;
                adx:testResultsKpiValue ?kpiValue .
        FILTER (?kpiValue = "false")
        }
        LIMIT 10
    """
    results = run_query(conn, query_1)
    assert results is not None, "Query 1 failed to execute."
    assert len(results) > 0, "No reports found for KPI value greater than 0.1."
    results = run_query(conn, query_2)
    assert results is not None, "Query 2 failed to execute."
    assert len(results) > 0, "No reports found with KPI value set to false"
    print(f"Test passed: {len(results)} reports with specific KPI values verified.")


def test_filter_reports_by_creation_time(conn):
    """Test filter reports by creation time."""
    query = """
        SELECT ?report ?creationTime
        WHERE {
            ?report a adx:Report ;
                    adx:reportCreationTime ?creationTime .
            FILTER (
                ?creationTime >= "2024-01-01T00:00:00"^^xsd:dateTime &&
                ?creationTime <= "2024-12-31T23:59:59"^^xsd:dateTime
            )
        }
        LIMIT 10
    """
    results = run_query(conn, query)
    assert results is not None, "Query failed to execute."
    assert len(results) > 0, "No reports found within the specified creation time."
    print(f"Test passed: {len(results)} reports filtered by creation time.")


def main(db: str) -> None:
    """Main function to run all tests."""
    conn = connect_to_stardog(db)
    if conn is None:
        raise Exception("Stardog connection error")

    try:
        test_total_number_of_reports_ingested(conn)
        test_artifact_version_presence(conn)
        test_retrieve_report_details(conn)
        test_relationship_between_reports_and_test_cases(conn)
        test_get_passed_tests(conn)
        test_verify_datatypes(conn)
        test_list_test_platforms(conn)
        test_list_sut_profiles(conn)
        test_linkage_between_report_and_version(conn)
        test_reports_with_specific_kpis(conn)
        test_filter_reports_by_creation_time(conn)
    finally:
        conn.close()


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="E2E Test for the ADX Test Report Pipeline")
    parser.add_argument("-d", "--db", dest="db", help="Stardog database")
    args, unknown = parser.parse_known_args()
    main(args.db)
