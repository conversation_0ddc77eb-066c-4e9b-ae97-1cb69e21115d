"""Unit tests for adx sca bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from typing import Any
from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest
from constants.sca import SCA_BRONZE_SCHEMA
from extract_sca import ADXSCABronzeExtractor, run_sca_extract
from pyspark.sql import SparkSession
from rddlib.utils import method_self_name

# Constants for re-use throughout the test file.
TODAY_DATE = datetime.date.today()
YESTERDAY_DATE = (datetime.datetime.today() - datetime.timedelta(days=1)).date()
TEST_DATETIME = datetime.datetime(2024, 1, 1, 0, 0, 0)
SCA_DATA_1 = {
    "Revision": "mock_sha_1",
    "LastChanged": TEST_DATETIME,
    "Label": "mock_label_1",
    "Name": "mock_component_1",
    "SourcePath": "mock_source_path_1",
    "BuildPath": "mock_build_path_1",
    "Rule": "mock_rule_1",
    "BuildIssuesCount": 0,
    "ScaIssuesCountBySeverity1": 1,
    "ScaIssuesCountBySeverity2": 2,
    "ScaIssuesCountBySeverity3": 3,
    "ScaIssuesCountBySeverity4": 4,
    "ScaIssuesCountBySeverity5": 5,
    "DoxygenCoverage": 10.5,
    "Codeowners": ["mock_codeowner1", "mock_codeowner2"],
    "Deployments": ["mock_deployment_1", "mock_deployment_2"],
    "Tags": ["mock_tag_1", "mock_tag_2"],
}
SCA_DATA_2 = {
    "Revision": "mock_sha_2",
    "LastChanged": TEST_DATETIME,
    "Label": "mock_label_2",
    "Name": "mock_component_2",
    "SourcePath": "mock_source_path_2",
    "BuildPath": "mock_build_path_2",
    "Rule": "mock_rule_2",
    "BuildIssuesCount": 10,
    "ScaIssuesCountBySeverity1": 10,
    "ScaIssuesCountBySeverity2": 20,
    "ScaIssuesCountBySeverity3": 30,
    "ScaIssuesCountBySeverity4": 40,
    "ScaIssuesCountBySeverity5": 50,
    "DoxygenCoverage": 20.5,
    "Codeowners": ["mock_codeowner3", "mock_codeowner4"],
    "Deployments": ["mock_deployment_3", "mock_deployment_4"],
    "Tags": ["mock_tag_3", "mock_tag_4"],
}

# Fixtures


@pytest.fixture(scope="session")
def spark():
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for adx sca extract").getOrCreate()


@pytest.fixture
def extractor(spark: SparkSession):
    """Fixture to create an ADXSCABronzeExtractor instance for testing."""
    extractor = ADXSCABronzeExtractor(AsyncMock(), "bronze_test", False)
    extractor.spark = spark
    return extractor


@pytest.fixture
def adx_response():
    """Fixture to create a mock ADX response object simulating the ADX response structure."""
    response_mock = MagicMock()
    response_mock.primary_results = [MagicMock()]
    response_mock.primary_results[0].to_dict.return_value = {"data": [SCA_DATA_1, SCA_DATA_2]}
    return response_mock


@pytest.fixture
def mock_fetch_data_for_batch(spark: SparkSession):
    """Fixture to simulate batched response."""
    data_batch_1 = spark.createDataFrame([SCA_DATA_1], schema=SCA_BRONZE_SCHEMA)
    data_batch_2 = spark.createDataFrame([SCA_DATA_2], schema=SCA_BRONZE_SCHEMA)

    async def _mock(mock_version, batch):
        if "mock_component_1" in batch:
            return data_batch_1
        elif "mock_component_2" in batch:
            return data_batch_2
        return None

    return _mock


# get_sca_components


@pytest.mark.asyncio
async def test_get_sca_components(extractor: ADXSCABronzeExtractor):
    """Test get_sca_components method."""
    mock_version = "sha_1"
    mock_data = pd.DataFrame({"Name": ["mock_component_1", "mock_component_2", "mock_component_1"]})

    with patch.object(*method_self_name(extractor._run_kusto_query), return_value=mock_data):
        result = await extractor.get_sca_components(mock_version)

        expected_query = f'BazelTargetMetrics | where Revision == "{mock_version}" | project Name'
        extractor._run_kusto_query.assert_called_once_with(expected_query)

        assert set(result) == {
            "mock_component_1",
            "mock_component_2",
        }, "Component names are not returned correctly"
        assert len(result) == 2, "Duplicate names should be filtered out"


# _fetch_data_for_batch


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "data, expected_result",
    [
        (
            pd.DataFrame(SCA_DATA_1),
            True,
        ),  # Data present, expect DataFrame with count > 0
        (pd.DataFrame(), None),  # No data present, expect None
    ],
    ids=["data_present", "no_data_present"],
)
async def test_fetch_data_for_batch(extractor: ADXSCABronzeExtractor, data, expected_result):
    """Test fetch_data_for_batch method."""
    mock_version = "sha_1"
    batch_components = ["mock_component_1", "mock_component_2"]

    with patch.object(*method_self_name(extractor._run_kusto_query), return_value=data):
        result = await extractor._fetch_data_for_batch(mock_version, batch_components)

        if expected_result is not None:
            assert result.count() > 0, "DataFrame should not be empty"
        else:
            assert result is None, "Should return None for an empty DataFrame"


@pytest.mark.asyncio
async def test_fetch_data_for_batch_df_creation_error(extractor: ADXSCABronzeExtractor):
    """Test fetch_data_for_batch method with DataFrame creation error."""
    # Simulate an error in the dataframe creation
    with (
        patch.object(
            *method_self_name(extractor.spark.createDataFrame),
            side_effect=ValueError("Invalid data"),
        ),
        patch.object(
            *method_self_name(extractor._run_kusto_query),
            return_value=pd.DataFrame(SCA_DATA_1),
        ),
    ):
        with pytest.raises(ValueError):
            await extractor._fetch_data_for_batch("mock_sha_1", ["mock_component_1", "mock_component_2"])


# fetch_data


@pytest.mark.asyncio
async def test_fetch_data(extractor: ADXSCABronzeExtractor, mock_fetch_data_for_batch):
    """Test parallel_fetch_data method."""
    mock_version = "mock_sha_1"
    mock_components = ["mock_component_1", "mock_component_2"]
    mock_batch_size = 1

    with patch.object(
        *method_self_name(extractor._fetch_data_for_batch),
        new=mock_fetch_data_for_batch,
    ):
        result_df = await extractor.fetch_data(mock_version, mock_components, mock_batch_size)
        assert result_df.count() == 2, "DataFrame should contain all components"
        components_in_df = [row.Name for row in result_df.collect()]
        assert all(comp in components_in_df for comp in mock_components)


# get_latest_version_of_date


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "date, response_data, expected_version",
    [
        (TODAY_DATE, pd.DataFrame({"Revision": ["mock_sha_1"]}), "mock_sha_1"),
        (TODAY_DATE, pd.DataFrame(), None),
    ],
    ids=["with_response", "without_response"],
)
async def test_get_latest_version_of_date(
    extractor: ADXSCABronzeExtractor,
    date: datetime.date,
    response_data: Any,
    expected_version: str | None,
):
    """Test get_latest_version_by_date method."""
    with patch.object(*method_self_name(extractor._run_kusto_query), return_value=response_data):
        version = await extractor.get_latest_version_of_date(date)
        assert version == expected_version, f"Expected version to be {expected_version}"


# Mock arguments setup
class MockArgs:
    """Mock arguments class."""

    def __init__(
        self,
        version: str | None = None,
        date: datetime.date | None = None,
        nightly: bool = False,
    ):
        """Initialize mock arguments."""
        self.date = date
        self.nightly = nightly
        self.version = version


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "args, expected_version, expected_date_call, raises_exception",
    [
        (MockArgs(date=TODAY_DATE), "sha1", TODAY_DATE, None),
        (MockArgs(date=TODAY_DATE), None, TODAY_DATE, Exception),
        (MockArgs(nightly=True), "sha1", YESTERDAY_DATE, None),
        (MockArgs(), "sha1", TODAY_DATE, None),
    ],
    ids=[
        "today_sha1_no_exception",
        "today_no_sha_exception",
        "yesterday_sha1_no_exception",
        "no_mock_args_today_sha1_no_exception",
    ],
)
async def test_run_sca_extract(
    extractor: ADXSCABronzeExtractor,
    args: MockArgs,
    expected_version,
    expected_date_call,
    raises_exception,
):
    """Test run_sca_extract function."""
    with (
        patch.object(
            *method_self_name(extractor.get_latest_version_of_date),
            return_value=expected_version,
        ),
        patch.object(
            *method_self_name(extractor.get_sca_components),
            return_value=["mock_component_1", "mock_component_2"],
        ),
        patch.object(*method_self_name(extractor.fetch_data), return_value="mock_dataframe"),
        patch.object(*method_self_name(extractor.ingest_data)),
    ):
        if raises_exception:
            with pytest.raises(raises_exception):
                await run_sca_extract(extractor, args.version, args.date, args.nightly)
            extractor.get_latest_version_of_date.assert_called_with(expected_date_call)
        else:
            version = await run_sca_extract(extractor, args.version, args.date, args.nightly)
            assert version == expected_version
            extractor.get_latest_version_of_date.assert_called_with(expected_date_call)
