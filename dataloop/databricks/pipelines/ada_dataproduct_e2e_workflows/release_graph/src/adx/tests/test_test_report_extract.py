"""Unit tests for adx test report bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from unittest.mock import AsyncMock, patch

import pandas as pd
import pytest
from constants.test_report import TEST_REPORT_BRONZE_SCHEMA
from extract_test_report import ADXTestReportBronzeExtractor, run_test_report_extract
from pyspark.sql import SparkSession

# Constants for reuse throughout the test file.
TEST_DATE = datetime.date(2024, 1, 1)
# Columns must be in the exact order as expected by the schema
TEST_REPORTS_DATA_1 = {
    field: {
        "RunId": f"pace_nightly_tests/scheduled__{TEST_DATE}",
        "SutCommitId": "sha1",
        "TestPlatform": "0.1",
        "TestCaseId": "tc_sw1",
        "TestName": "Mock test",
        "ReportCreationTime": TEST_DATE,
    }.get(field, None)
    for field in TEST_REPORT_BRONZE_SCHEMA.fieldNames()
}
TEST_REPORTS_DATA_2 = {
    field: {
        "RunId": f"pace_nightly_tests/scheduled__{TEST_DATE}",
        "SutCommitId": "sha1",
        "TestPlatform": "0.2",
        "TestCaseId": "tc_sw2",
        "TestName": "Mock test two",
        "ReportCreationTime": TEST_DATE,
    }.get(field, None)
    for field in TEST_REPORT_BRONZE_SCHEMA.fieldNames()
}

#  Fixtures


@pytest.fixture(scope="session")
def spark() -> SparkSession:
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for adx test report extract").getOrCreate()


@pytest.fixture
def extractor(spark: SparkSession) -> ADXTestReportBronzeExtractor:
    """Fixture to create an Extractor instance for testing."""
    extractor = ADXTestReportBronzeExtractor(AsyncMock(), "silver_test", False)
    extractor.spark = spark
    return extractor


# get_latest_version


async def test_get_latest_version(extractor: ADXTestReportBronzeExtractor) -> None:
    """Test get_latest_version method."""
    df = pd.DataFrame({"SutCommitId": ["sha1", "sha2"]})

    with patch.object(extractor, "_run_kusto_query", return_value=df):
        latest_version = await extractor.get_latest_version()
        assert latest_version == "sha1"


# get_nightly_version_of_date


async def test_get_nightly_version_of_date(extractor: ADXTestReportBronzeExtractor) -> None:
    """Test get_test_reports_nightly method."""
    df = pd.DataFrame({"SutCommitId": ["sha1", "sha2"], "RunId": ["pace_nightly_1", "pace_nightly_2"]})

    with patch.object(extractor, "_run_kusto_query", return_value=df):
        nightly_version = await extractor.get_nightly_version_of_date(datetime.date(2024, 1, 1))
        assert nightly_version == "sha1"


# get_versions_of_date


async def test_get_versions_of_date(extractor: ADXTestReportBronzeExtractor) -> None:
    """Test get_test_reports_date method."""
    df = pd.DataFrame(
        {
            "SutCommitId": ["sha1", "sha2"],
            "RunId": ["pace_nightly_1", "pace_nightly_2"],
            "ReportCreationTime": [TEST_DATE, TEST_DATE],
        }
    )

    with patch.object(extractor, "_run_kusto_query", return_value=df):
        versions = await extractor.get_versions_of_date(datetime.date(2024, 1, 1))
        assert versions == ["sha1", "sha2"]


# fetch_data


async def test_fetch_data(extractor: ADXTestReportBronzeExtractor) -> None:
    """Test fetch_data method."""
    run_id_df = pd.DataFrame({"RunId": [f"pace_nightly_tests/scheduled__{TEST_DATE}"]})
    data_df = pd.DataFrame([TEST_REPORTS_DATA_1, TEST_REPORTS_DATA_2])

    # Create an empty DataFrame to simulate the end of batch processing
    empty_df = pd.DataFrame(columns=data_df.columns)

    expected_df = extractor.spark.createDataFrame(data_df, schema=TEST_REPORT_BRONZE_SCHEMA)

    with patch.object(extractor, "_run_kusto_query", side_effect=[run_id_df, data_df, empty_df]):
        result_df = await extractor.fetch_data("sha1")

        assert result_df.count() == expected_df.count()  # Check row count
        result_rows = result_df.collect()
        expected_rows = expected_df.collect()
        assert result_rows[0]["TestCaseId"] == expected_rows[0]["TestCaseId"]
        assert result_rows[0]["TestPlatform"] == expected_rows[0]["TestPlatform"]
        assert result_rows[1]["TestCaseId"] == expected_rows[1]["TestCaseId"]
        assert result_rows[1]["TestPlatform"] == expected_rows[1]["TestPlatform"]


# run_test_report_extract


# Mock arguments setup
class MockArgs:
    """Mock arguments class."""

    def __init__(
        self,
        version: str | None = None,
        date: datetime.date | None = None,
        nightly: bool = False,
    ):
        """Initialize mock arguments."""
        self.date = date
        self.nightly = nightly
        self.version = version


@pytest.mark.asyncio
@pytest.mark.parametrize(
    "args, expected_version, expected_version_method_name, expected_fetch_data_calls",
    [
        (MockArgs(version="sha1"), "sha1", None, 1),
        (MockArgs(nightly=True), "sha1", "get_nightly_version_of_date", 1),
        (MockArgs(nightly=True, date=TEST_DATE), "sha1", None, 1),
        (MockArgs(date=TEST_DATE), None, "get_versions_of_date", 2),
        (MockArgs(), "sha2", "get_latest_version", 1),
    ],
    ids=[
        "sha1_no_method_1_call",
        "sha1_nightly_1_call",
        "yesterday_sha1_no_exception",
        "no_mock_args_today_sha1_no_exception",
        "",
    ],
)
async def test_run_test_report_extract(
    extractor: ADXTestReportBronzeExtractor,
    args: MockArgs,
    expected_version: str | None,
    expected_version_method_name: str | None,
    expected_fetch_data_calls: int,
) -> None:
    """Test run_test_report_extract function."""
    with (
        patch.object(extractor, "get_nightly_version_of_date", return_value="sha1"),
        patch.object(extractor, "get_latest_version", return_value="sha2"),
        patch.object(extractor, "get_versions_of_date", return_value=["sha1", "sha2"]),
        patch.object(extractor, "fetch_data", return_value="mock_df"),
        patch.object(extractor, "ingest_data") as mock_ingest_data,
    ):
        version = await run_test_report_extract(extractor, args.version, args.date, args.nightly)

        assert version == expected_version
        if expected_version_method_name is not None:
            getattr(extractor, expected_version_method_name).assert_called_once()

        assert mock_ingest_data.call_count == expected_fetch_data_calls
