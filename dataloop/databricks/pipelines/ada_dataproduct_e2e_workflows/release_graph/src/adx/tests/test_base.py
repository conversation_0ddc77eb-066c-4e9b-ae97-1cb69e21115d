"""Unit tests for base."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import AsyncMock, MagicMock, patch

import pandas as pd
import pytest
from azure.kusto.data.exceptions import KustoClientError
from base import ADXBaseBronzeExtractor, ADXBaseSilverTransformer
from pyspark.sql import Row, SparkSession
from rddlib import delta_table_utils as dtu
from rddlib.utils import full_qualname, method_self_name

# Fixtures


@pytest.fixture(scope="session")
def spark():
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit Testing for adx sca extract").getOrCreate()


@pytest.fixture
def extractor(spark: SparkSession):
    """Fixture to create an ADXSCABronzeExtractor instance for testing."""

    with patch.multiple(
        ADXBaseBronzeExtractor,
        __abstractmethods__=set(),
        BRONZE_TABLE="adx_test_table",
    ):
        extractor = ADXBaseBronzeExtractor(MagicMock(), "bronze_test", False)
        extractor.spark = spark
        yield extractor


@pytest.fixture
def transformer(spark: SparkSession):
    """Fixture to create an ADXBaseSilverTransformer instance for testing."""
    with patch.multiple(
        ADXBaseSilverTransformer,
        __abstractmethods__=set(),
        BRONZE_TABLE="adx_test_table",
        SILVER_TABLE="adx_test_table",
    ):
        transformer = ADXBaseSilverTransformer("bronze_test", "silver_test", False)
        transformer.spark = spark
        yield transformer


@pytest.fixture(scope="session")
def adx_response():
    """Fixture to create ADX response data for testing."""
    mock = MagicMock()
    mock.primary_results = [{"data": ["test1", "test2"]}]

    return mock


# ADXBaseBronzeExtractor


# _run_kusto_query


@pytest.mark.asyncio
async def test_run_kusto_query_success(extractor: ADXBaseBronzeExtractor):
    """Test run_kusto_query method with successful execution."""
    query = "BazelTargetMetrics | limit 1"
    adx_response = MagicMock()
    adx_response.primary_results = ["mock_response"]
    extractor.adx_client.execute = AsyncMock(return_value=adx_response)

    mock_result_df = pd.DataFrame()

    with patch("base.dataframe_from_result_table", return_value=mock_result_df) as mock_df_from_result_table:
        response = await extractor._run_kusto_query(query)

        extractor.adx_client.execute.assert_called_once_with(extractor.ADX_DB, query)
        mock_df_from_result_table.assert_called_once_with("mock_response")
        assert response is mock_result_df, "Expected response was not returned"


@pytest.mark.asyncio
async def test_run_kusto_query_client_error(extractor: ADXBaseBronzeExtractor):
    """Test run_kusto_query method with client error."""
    extractor.adx_client.execute.side_effect = KustoClientError("Client error")
    query = "BazelTargetMetrics | limit 1"

    with pytest.raises(KustoClientError):
        await extractor._run_kusto_query(query)


# ingest_data


@pytest.mark.parametrize(
    "table_exists, expected_append_call_count, expected_overwrite_call_count",
    [
        (True, 1, 0),  # Scenario where table exists
        (False, 0, 1),  # Scenario where table does not exist
    ],
    ids=["table_exists", "table_does_not_exist"],
)
def test_extractor_ingest_data_table_exists_or_not(
    extractor: ADXBaseBronzeExtractor,
    table_exists,
    expected_append_call_count,
    expected_overwrite_call_count,
):
    """Test ingest_test_reports_version method with table exists or not."""
    mock_data = [Row(sut_commit_id="SHA1", test_name="TC_SWINT_1")]
    df = extractor.spark.createDataFrame(mock_data)

    with (
        patch.object(*method_self_name(extractor._run_quality_check)) as mock_run_quality_check,
        patch("rddlib.delta_table_utils.table_exists", return_value=table_exists) as mock_table_exists,
        patch("rddlib.delta_table_utils.append") as mock_append,
        patch("rddlib.delta_table_utils.overwrite") as mock_overwrite,
        patch("rddlib.delta_table_utils.update_table_metadata"),
    ):
        extractor.ingest_data(df)

        mock_run_quality_check.assert_called_once()
        mock_table_exists.assert_called_once_with("bronze_test.ada_release_graph.adx_test_table")
        assert mock_append.call_count == expected_append_call_count
        assert mock_overwrite.call_count == expected_overwrite_call_count


# ADXBaseSilverTransformer


# ingest_data


@pytest.mark.parametrize(
    "table_exists, expected_append_call_count, expected_overwrite_call_count",
    [
        (True, 1, 0),  # Scenario where table exists
        (False, 0, 1),  # Scenario where table does not exist
    ],
    ids=["table_exists", "table_does_not_exist"],
)
def test_transfomer_ingest_data_table_exists_or_not(
    transformer: ADXBaseSilverTransformer,
    table_exists,
    expected_append_call_count,
    expected_overwrite_call_count,
):
    """Test ingest_test_reports_version method with table exists or not."""
    mock_data = [Row(sut_commit_id="SHA1", test_name="TC_SWINT_1")]
    df = transformer.spark.createDataFrame(mock_data)

    with (
        patch("rddlib.delta_table_utils.table_exists", return_value=table_exists) as mock_table_exists,
        patch("rddlib.delta_table_utils.append") as mock_append,
        patch("rddlib.delta_table_utils.overwrite") as mock_overwrite,
        patch("rddlib.delta_table_utils.update_table_metadata"),
    ):
        transformer.ingest_data(df)

        mock_table_exists.assert_called_once_with("silver_test.ada_release_graph.adx_test_table")
        assert mock_append.call_count == expected_append_call_count
        assert mock_overwrite.call_count == expected_overwrite_call_count
