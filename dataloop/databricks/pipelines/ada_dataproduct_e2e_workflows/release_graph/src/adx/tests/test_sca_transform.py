"""Unit tests for adx sca silver transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import patch

import pytest
from constants.sca import SCA_BRONZE_SCHEMA
from pyspark.sql import SparkSession
from pyspark.sql.utils import AnalysisException
from transform_sca import ADXSCASilverTransformer

from .test_sca_extract import SCA_DATA_1, SCA_DATA_2

# Fixtures


@pytest.fixture(scope="session")
def spark():
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit testing for adx sca transform").getOrCreate()


@pytest.fixture
def transformer(spark):
    """Fixture to create an ADXSCASilverTransformer instance for testing."""
    transformer = ADXSCASilverTransformer("dev_bronze", "dev_silver", False)
    transformer.spark = spark
    return transformer


@pytest.fixture
def mock_df(spark):
    """Create a mock data frame mimicking bronze layer data for testing."""
    return spark.createDataFrame([SCA_DATA_1, SCA_DATA_2], schema=SCA_BRONZE_SCHEMA)


# get_bronze_data_of_version


def test_get_bronze_data_of_version_success(transformer: ADXSCASilverTransformer, mock_df):
    """Test get_bronze_data_of_version method with successful execution."""
    with patch("pyspark.sql.session.SparkSession.read") as mock_read:
        mock_read.table.return_value = mock_df
        df = transformer.get_bronze_data_of_version("mock_sha_1")

        assert df.filter("Revision = 'mock_sha_1'").count() > 0


def test_get_bronze_data_of_version_failure(transformer: ADXSCASilverTransformer):
    """Test get_bronze_data_of_version method with failure."""
    with patch("pyspark.sql.session.SparkSession.read") as mock_read:
        mock_read.table.side_effect = AnalysisException("Failed to read data")
        with pytest.raises(AnalysisException):
            transformer.get_bronze_data_of_version("mock_sha_1")
