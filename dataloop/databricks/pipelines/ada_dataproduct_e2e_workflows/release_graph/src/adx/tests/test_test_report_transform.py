"""Unit tests for adx test report silver transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import datetime
from unittest.mock import patch

import pytest
from pyspark.sql import Row, SparkSession
from pyspark.sql.types import StringType, StructField, StructType
from pyspark.sql.utils import AnalysisException
from transform_test_report import ADXTestReportSilverTransformer

# Constants for reuse throughout the test file.
TEST_DATE = datetime.date(2024, 1, 1)
MOCK_TEST_REPORTS_BRONZE_DATA = {
    "RunId": f"pace_nightly_tests/scheduled__{TEST_DATE}",
    "TestPlatform": "0.1",
    "TestCaseId": "TC_SW1",
    "SutCommitId": "SHA1",
    "TestName": "Mock test",
    "ReportCreationTime": TEST_DATE,
}

# Fixtures


@pytest.fixture(scope="session")
def spark():
    """Session-scoped fixture to create a SparkSession instance for use across multiple tests."""
    return SparkSession.builder.master("local[2]").appName("Unit testing for adx test report transform").getOrCreate()


@pytest.fixture
def transformer(spark):
    """Fixture to create an ADXTestReportSilverTransformer instance for testing."""
    transformer = ADXTestReportSilverTransformer("dev_bronze", "dev_silver", False)
    transformer.spark = spark
    return transformer


@pytest.fixture
def mock_df(spark):
    """Create a mock data frame mimicking bronze layer data for testing."""

    # schema for unit testing; contains fields applicable for testing
    schema = StructType(
        [
            StructField("SutCommitId", StringType(), True),
            StructField("RunId", StringType(), True),
            StructField("TestPlatform", StringType(), True),
            StructField("TestCaseId", StringType(), True),
            StructField("Test_Name", StringType(), True),
            StructField("RunDuration", StringType(), True),
            StructField("Test_Duration", StringType(), True),
            StructField("Exitcode", StringType(), True),
            StructField("ReportCreationTime", StringType(), True),
            StructField("Test_Results_Pass", StringType(), True),
            StructField("Test_Results_Expected", StringType(), True),
            StructField("SutProfile", StringType(), True),
        ]
    )
    data = [
        Row(
            "SHA1",
            "RUN1",
            "PoL",
            "TC_SWINT_1",
            "TN_1",
            "0.10",
            "0.20",
            "1",
            "2024-01-01T00:00:00.000",
            "false",
            "false",
            "SUT_1",
        ),
        Row(
            "SHA1",
            "RUN2",
            "Sol",
            "TC_SWINT_2",
            "TN_2",
            "0.30",
            "0.40",
            "0",
            "2024-01-02T00:00:00.000",
            "true",
            "true",
            "SUT_2",
        ),
        Row(
            "SHA2",
            "RUN1",
            "Sil",
            "TC_SWINT_3",
            "TN_3",
            "0.50",
            "0.60",
            "1",
            "2024-01-03T00:00:00.000",
            "false",
            "true",
            "SUT_3",
        ),
    ]
    return spark.createDataFrame(data, schema)


# get_bronze_data_of_version


def test_get_bronze_data_of_version_success(transformer: ADXTestReportSilverTransformer, mock_df):
    """Test get_bronze_data_of_version method with successful execution."""
    with patch("pyspark.sql.session.SparkSession.read") as mock_read:
        mock_read.table.return_value = mock_df
        df = transformer.get_bronze_data_of_version("SHA1")

        assert df.filter("SutCommitId = 'SHA1'").count() > 0


def test_get_bronze_data_of_version_failure(spark, transformer: ADXTestReportSilverTransformer):
    """Test get_bronze_data_of_version method with failure."""
    with patch("pyspark.sql.session.SparkSession.read") as mock_read:
        mock_read.table.side_effect = AnalysisException("Failed to read data")
        with pytest.raises(AnalysisException):
            transformer.get_bronze_data_of_version("mock_sha_1")


# transform


def test_transform_success(transformer: ADXTestReportSilverTransformer, mock_df):
    """Test transform method with successful execution."""
    transformed_df = transformer.transform(mock_df)

    # Check if new column 'report_id' is added
    assert "report_id" in transformed_df.columns

    # Check data types of new or modified columns
    expected_types = {
        "report_creation_time": "TimestampType()",
        "run_duration": "DoubleType()",
        "exitcode": "IntegerType()",
        "test_duration": "DoubleType()",
        "test_results_expected": "StringType()",
        "test_results_pass": "BooleanType()",
        "report_id": "StringType()",
    }
    for column, expected_type in expected_types.items():
        assert str(transformed_df.schema[column].dataType) == expected_type

    assert "TestName" not in transformed_df.columns
    assert "test_name" in transformed_df.columns


def test_transform_failure_due_to_missing_column(transformer: ADXTestReportSilverTransformer, mock_df):
    """Test transform method failure due to missing column."""
    # Drop the 'Test_Name' column from the DataFrame to simulate the missing column scenario
    modified_df = mock_df.drop("Test_Name")

    with pytest.raises(AnalysisException):
        transformer.transform(modified_df)
