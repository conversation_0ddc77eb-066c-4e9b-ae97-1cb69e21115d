"""ADX test report silver transformation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from typing import ClassVar

from base import ADXBaseSilverTransformer
from constants.common import DBX_SCHEMA
from constants.sca import DESCRIPTION_SILVER, SCA_SILVER_FIELDS_MAP, SCA_TEST_RULES_VALID
from pyspark.sql import DataFrame
from rddlib import FullSchemaName, get_dbx_env_catalog, quality_check, setup_databricks_logging

logger = logging.getLogger(__name__)


class ADXSCASilverTransformer(ADXBaseSilverTransformer):
    """Class for ADX sca silver layer transformation."""

    # Class fields
    BRONZE_TABLE: ClassVar[str] = "adx_sca"
    SILVER_TABLE: ClassVar[str] = "adx_sca"
    SILVER_FIELDS_MAP: ClassVar[dict] = SCA_SILVER_FIELDS_MAP
    TABLE_DESCRIPTION: ClassVar[str] = DESCRIPTION_SILVER

    def get_bronze_data_of_version(self, version: str) -> DataFrame:
        """Get ADX sca data from bronze layer for specific version."""
        table_name = f"{self.BRONZE_TABLE}_test" if self.test_mode else self.BRONZE_TABLE
        bronze_table = f"{self.catalog_bronze}.{DBX_SCHEMA}.{table_name}"
        return self.spark.read.table(bronze_table).filter(f"Revision = '{version}'")

    def _run_quality_check(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        super()._run_quality_check(df)

        # Count of rows for df
        total_count = df.count()

        # Check that sca test rule is one of element in the valid list
        non_matching_rows = quality_check.unique_value_validation(df, "rule", SCA_TEST_RULES_VALID)
        count_of_non_matching_values = non_matching_rows.count()
        if count_of_non_matching_values == 0:
            quality_check.log_quality_check_result(True, "unique_value_validation", None)
        else:
            quality_check.log_quality_check_result(
                False,
                "unique_value_validation",
                count_of_non_matching_values / total_count,
            )


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Transform ADX SCA Report")

    parser.add_argument(
        "-v",
        "--version",
        dest="version",
        required=False,
        help="Commit to transform from bronze to silver layer",
    )

    parser.add_argument(
        "-t",
        "--task_output",
        help="Get version from task value, specify which task name",
    )

    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )

    parser.add_argument("-i", "--run_id", dest="run_id")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    args = parser.parse_args()
    test_mode = args.test_mode

    catalog_bronze = get_dbx_env_catalog("bronze")
    catalog_silver = get_dbx_env_catalog("silver")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_silver, DBX_SCHEMA, False),
        "adx_sca/silver",
        run_id=args.run_id,
        enabled_loggers=["base"],
    )

    transformer = ADXSCASilverTransformer(catalog_bronze, catalog_silver, test_mode)

    if args.task_output:
        from pyspark.dbutils import DBUtils

        dbutils = DBUtils(transformer.spark)
        version = dbutils.jobs.taskValues.get(taskKey=args.task_output, key="version")
    else:
        version = args.version

    logger.info(f"Transforming sca report from: {version}")
    df_bronze = transformer.get_bronze_data_of_version(version)
    df_silver = transformer.transform(df_bronze)
    # Run the data quality check
    transformer._run_quality_check(df_silver)
    transformer.ingest_data(df_silver)
