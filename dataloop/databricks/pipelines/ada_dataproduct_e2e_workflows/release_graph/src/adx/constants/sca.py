"""Module contains schema definitions for ADX SCA."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import ArrayType, FloatType, IntegerType, StringType, StructField, StructType, TimestampType

SCA_BRONZE_SCHEMA = StructType(
    [
        StructField("Revision", StringType(), True),
        StructField("LastChanged", TimestampType(), True),
        StructField("Label", StringType(), True),
        StructField("Name", StringType(), True),
        StructField("SourcePath", StringType(), True),
        StructField("BuildPath", StringType(), True),
        StructField("Rule", StringType(), True),
        StructField("BuildIssuesCount", IntegerType(), True),
        StructField("ScaIssuesCountBySeverity1", IntegerType(), True),
        StructField("ScaIssuesCountBySeverity2", IntegerType(), True),
        StructField("ScaIssuesCountBySeverity3", IntegerType(), True),
        StructField("ScaIssuesCountBySeverity4", IntegerType(), True),
        StructField("ScaIssuesCountBySeverity5", IntegerType(), True),
        StructField("DoxygenCoverage", FloatType(), True),
        StructField("Codeowners", ArrayType(StringType()), True),
        StructField("Deployments", ArrayType(StringType()), True),
        StructField("Tags", ArrayType(StringType()), True),
    ]
)

SCA_COLUMNS_OF_INTEREST = [
    "Revision",
    "LastChanged",
    "Label",
    "Name",
    "SourcePath",
    "BuildPath",
    "Rule",
    "BuildIssuesCount",
    "ScaIssuesCountBySeverity1",
    "ScaIssuesCountBySeverity2",
    "ScaIssuesCountBySeverity3",
    "ScaIssuesCountBySeverity4",
    "ScaIssuesCountBySeverity5",
    "DoxygenCoverage",
    "Codeowners",
    "Deployments",
    "Tags",
]

SCA_SILVER_FIELDS_MAP = {
    "Revision": "revision",
    "LastChanged": "last_changed",
    "Label": "label",
    "Name": "name",
    "SourcePath": "source_path",
    "BuildPath": "build_path",
    "Rule": "rule",
    "BuildIssuesCount": "build_issues_count",
    "ScaIssuesCountBySeverity1": "sca_issues_count_by_severity_1",
    "ScaIssuesCountBySeverity2": "sca_issues_count_by_severity_2",
    "ScaIssuesCountBySeverity3": "sca_issues_count_by_severity_3",
    "ScaIssuesCountBySeverity4": "sca_issues_count_by_severity_4",
    "ScaIssuesCountBySeverity5": "sca_issues_count_by_severity_5",
    "DoxygenCoverage": "doxygen_coverage",
    "Codeowners": "codeowners",
    "Deployments": "deployments",
    "Tags": "tags",
}

SCA_TEST_RULES_VALID = [
    "cc_test",
    "cc_binary",
    "cc_shared_library",
    "cc_library_with_reflection_attachment",
    "cc_library",
]

DESCRIPTION_BRONZE = "This table stores SCA metrics data in raw format from Azure Data Explorer, including component-level details, revisions, and timestamps. The table is populated using data extracted from the EmbeddedComponents database in Azure Data Explorer (ADX). Specifically, the data is queried from the BazelTargetMetrics table within this database.\
The extraction process focuses on data associated with specific revisions or dates, using ADX queries to retrieve relevant metrics and components. The ingestion and maintenance of this table are handled by the RDD Team."
DESCRIPTION_SILVER = "This table stores the transformed SCA (Static Code Analysis) data from the ADX bronze layer into the silver layer. It includes detailed analysis of code quality metrics, such as rule-based violations, test results, and other quality indicators for each commit version.\
The ingestion and maintenance of this table are handled by the RDD Team."
