"""Module contains schema definitions for ADX test reports."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import ArrayType, StringType, StructField, StructType

TEST_REPORT_BRONZE_SCHEMA = StructType(
    [
        StructField("Created", StringType(), True),
        StructField("RunDuration", StringType(), True),
        StructField("RunId", StringType(), True),
        StructField("Environment_Python", StringType(), True),
        StructField("Environment_Platform", StringType(), True),
        StructField("Environment_EasyEvalVersion", StringType(), True),
        StructField("Environment_CurrentCommitId", StringType(), True),
        StructField("Environment_BranchName", StringType(), True),
        StructField("ComponentOrFeatureName", StringType(), True),
        StructField("TestPlatform", StringType(), True),
        StructField("TestCaseId", StringType(), True),
        StructField("Preset", StringType(), True),
        StructField("InputRecording", StringType(), True),
        StructField("ReferenceData", ArrayType(StringType()), True),
        StructField("SutCommitId", StringType(), True),
        StructField("GitDiff", StringType(), True),
        StructField("CustomLogs", StringType(), True),
        StructField("Exitcode", StringType(), True),
        StructField("Warnings", ArrayType(StringType()), True),
        StructField("Test_Name", StringType(), True),
        StructField("Test_Description", StringType(), True),
        StructField("Test_FilePath", StringType(), True),
        StructField("Test_RequirementId", StringType(), True),
        StructField("Test_Duration", StringType(), True),
        StructField("Test_CustomLogs", StringType(), True),
        StructField("Test_Results_Outcome", StringType(), True),
        StructField("Test_Results_KpiValue", StringType(), True),
        StructField("Test_Results_Operator", StringType(), True),
        StructField("Test_Results_Threshold", StringType(), True),
        StructField("Test_Results_LowerThreshold", StringType(), True),
        StructField("Test_Results_UpperThreshold", StringType(), True),
        StructField("Test_Results_Expected", StringType(), True),
        StructField("Test_Results_Unit", StringType(), True),
        StructField("Test_Results_Error", StringType(), True),
        StructField("Test_Parameters", ArrayType(StringType()), True),
        StructField("ReportCreationTime", StringType(), True),
        StructField("SutGitDiff", StringType(), True),
        StructField("Test_Results_Pass", StringType(), True),
        StructField("Test_Results_SkipReason", StringType(), True),
        StructField("VariationConcreteParameters", StringType(), True),
        StructField("ScenarioId", StringType(), True),
        StructField("SutProfile", StringType(), True),
        StructField("Deployment", StringType(), True),
        StructField("GithubEventName", StringType(), True),
    ]
)

TEST_REPORT_SILVER_FIELDS_MAP = {
    "Created": "created",
    "RunDuration": "run_duration",
    "RunId": "run_id",
    "Environment_Python": "environment_python",
    "Environment_Platform": "environment_platform",
    "Environment_EasyEvalVersion": "environment_easy_eval_version",
    "Environment_CurrentCommitId": "environment_current_commit_id",
    "Environment_BranchName": "environment_branch_name",
    "ComponentOrFeatureName": "component_or_feature_name",
    "TestPlatform": "test_platform",
    "TestCaseId": "test_case_id",
    "Preset": "preset",
    "InputRecording": "input_recording",
    "ReferenceData": "reference_data",
    "SutCommitId": "sut_commit_id",
    "GitDiff": "git_diff",
    "CustomLogs": "custom_logs",
    "Exitcode": "exitcode",
    "Warnings": "warnings",
    "Test_Name": "test_name",
    "Test_Description": "test_description",
    "Test_FilePath": "test_file_path",
    "Test_RequirementId": "test_requirement_id",
    "Test_Duration": "test_duration",
    "Test_CustomLogs": "test_custom_logs",
    "Test_Results_Outcome": "test_results_outcome",
    "Test_Results_KpiValue": "test_results_kpi_value",
    "Test_Results_Operator": "test_results_operator",
    "Test_Results_Threshold": "test_results_threshold",
    "Test_Results_LowerThreshold": "test_results_lower_threshold",
    "Test_Results_UpperThreshold": "test_results_upper_threshold",
    "Test_Results_Expected": "test_results_expected",
    "Test_Results_Unit": "test_results_unit",
    "Test_Results_Error": "test_results_error",
    "Test_Parameters": "test_parameters",
    "ReportCreationTime": "report_creation_time",
    "SutGitDiff": "sut_git_diff",
    "Test_Results_Pass": "test_results_pass",
    "Test_Results_SkipReason": "test_results_skip_reason",
    "VariationConcreteParameters": "variation_concrete_parameters",
    "ScenarioId": "scenario_id",
    "SutProfile": "sut_profile",
    "Deployment": "deployment",
    "GithubEventName": "github_event_name",
}

# These columns are selected for quality check, because they are used and always expected
# to not contain none values.
TEST_REPORT_COLUMNS_FOR_QC = [
    "Preset",
    "ReportCreationTime",
    "RunDuration",
    "SutProfile",
    "Test_Description",
    "Test_FilePath",
    "Test_Name",
    "Test_Results_Expected",
    "Test_Results_Operator",
    "TestCaseId",
    "TestPlatform",
]

DESCRIPTION_BRONZE = "This table stores raw ADX test report data extracted from TestReports database in Azure Data Explorer.\
Specifically, the data is queried from the EasyEvalBasicReport table within the TestReports database.\
It includes detailed information about each test run, such as the associated commit version, run IDs, and test results. The ingestion and maintenance of this table are handled by the RDD Team."
DESCRIPTION_SILVER = "This table stores the transformed ADX test report data in the silver layer. It includes structured fields such as sut_commit_id, test_name, report_creation_time,\
run_duration, exitcode, test_duration, and test_results_pass, with appropriate data types. The ingestion and maintenance of this table are handled by the RDD Team."
