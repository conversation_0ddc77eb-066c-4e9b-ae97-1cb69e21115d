"""Helper module to define ADX related constants and schema."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

# ADX information

ADX_CLUSTER = "https://decmetricdata.westeurope.kusto.windows.net/"
ADX_AUTHORITY_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
# Fetch 200 components in a single kusto query (adjusted as per response size limits imposed by ADX)
ADX_BATCH_SIZE = 200

# Databricks information

DBX_SCHEMA = "ada_release_graph"
