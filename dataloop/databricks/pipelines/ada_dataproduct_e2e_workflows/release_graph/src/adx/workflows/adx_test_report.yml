resources:
  jobs:
    needs_etl_adx_release:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "ADA Release Graph - Needs - E2E - Test Report SHA"
      description: Ingest ADX test report and needs for the same sha
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "E2E"
        type: "Test Report SHA"
        "ADA Release Graph": ""
        "Needs": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: needs_extract_adx
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_needs/files/extract.py
            parameters:
              - --application
              - release
              - --adx

          job_cluster_key: rdd_needs_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_needs/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: needs_transform_adx
          depends_on:
            - task_key: needs_extract_adx
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_needs/files/transform.py
            parameters:
              - --application
              - release
              - --task_output
              - needs_extract_adx
          job_cluster_key: rdd_needs_job_cluster

        - task_key: needs_load_adx
          depends_on:
            - task_key: needs_transform_adx
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_release_v1
              - --mapping_file
              - needs_release.j2
              - --task_output
              - needs_extract_adx
          job_cluster_key: rdd_needs_job_cluster

        - task_key: needs_req_levels
          depends_on:
            - task_key: needs_load_adx
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "stardog",
                "add_req_levels",
                "--database",
                "ada_release_v1",
              ]
          job_cluster_key: rdd_needs_job_cluster

      job_clusters:
        - job_cluster_key: rdd_needs_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}

    adx_test_reports_etl_nightly:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "ADA Release Graph - ADX - Test Report - E2E - Nightly"
      description: Ingest test reports from ADX
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "E2E"
        schedule: "Nightly"
        "ADA Release Graph": ""
        "ADX": ""
        "Test Report": ""

      # Schedules
      # Dev - PAUSED
      # QA - UNPAUSED, Runs every Wednesday night - 3:13 AM UTC
      # Prod - UNPAUSED Runs nightly at 3:13 AM UTC
      schedule:
        quartz_cron_expression: ${var.nightly_schedule}
        timezone_id: UTC
        pause_status: ${var.nightly_trigger}

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: adx_test_reports_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract_test_report.py
            parameters:
              - --nightly
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_adx_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: adx_test_reports_transform
          depends_on:
            - task_key: adx_test_reports_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform_test_report.py
            parameters:
              - --task_output
              - adx_test_reports_extract
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_adx_job_cluster

        - task_key: adx_test_reports_load
          depends_on:
            - task_key: adx_test_reports_transform
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_release_v1
              - --mapping_file
              - adx_report.j2
              - --run_id
              - "{{job.run_id}}"
              - --task_output
              - adx_test_reports_extract
          job_cluster_key: rdd_adx_job_cluster

        - task_key: trigger_needs_etl_release
          depends_on:
            - task_key: adx_test_reports_load
          run_job_task:
            job_id: ${resources.jobs.needs_etl_adx_release.id}

      job_clusters:
        - job_cluster_key: rdd_adx_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
