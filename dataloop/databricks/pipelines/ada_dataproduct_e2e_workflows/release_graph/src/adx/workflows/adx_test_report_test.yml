resources:
  jobs:
    adx_test_report_e2e_test:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - ADX - Test Report - E2E Test
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        medallion: "E2E"
        test: ""
        "ADA Release Graph": ""
        "ADX": ""
        "Test Report": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      tasks:
        - task_key: cleanup_bronze_test_tables
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "bronze_${var.env}",
                "-s",
                "ada_release_graph",
                "-t",
                "adx_test_report_test",
              ]
          job_cluster_key: rdd_adx_test_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib[stardog]==${var.rddlib_version}

        - task_key: adx_test_reports_extract
          depends_on:
            - task_key: cleanup_bronze_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract_test_report.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
              - --version
              - "290eafd55f6781dd864a0bc7896ccebab57c4034"
              - --test_mode
          job_cluster_key: rdd_adx_test_job_cluster

        - task_key: cleanup_silver_test_tables
          depends_on:
            - task_key: adx_test_reports_extract
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              [
                "delta-table",
                "drop",
                "-c",
                "silver_${var.env}",
                "-s",
                "ada_release_graph",
                "-t",
                "adx_test_report_test",
              ]
          job_cluster_key: rdd_adx_test_job_cluster

        - task_key: adx_test_reports_transform
          depends_on:
            - task_key: cleanup_silver_test_tables
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform_test_report.py
            parameters:
              - --task_output
              - adx_test_reports_extract
              - --run_id
              - "{{job.run_id}}"
              - --test_mode
          job_cluster_key: rdd_adx_test_job_cluster

        - task_key: cleanup_stardog_test_db
          depends_on:
            - task_key: adx_test_reports_transform
          python_wheel_task:
            package_name: rddlib
            entry_point: rddcli-dbx
            parameters:
              ["stardog", "drop", "--database", "ada_adx_test_report_e2e_test"]
          job_cluster_key: rdd_adx_test_job_cluster

        - task_key: adx_test_reports_load
          depends_on:
            - task_key: cleanup_stardog_test_db
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_adx_test_report_e2e_test
              - --mapping_file
              - adx_report_test.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_adx_test_job_cluster

        - task_key: adx_test_reports_verification
          depends_on:
            - task_key: adx_test_reports_load
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/e2e_tests/test_adx_test_report.py
            parameters:
              - --db
              - ada_adx_test_report_e2e_test
          job_cluster_key: rdd_adx_test_job_cluster

      job_clusters:
        - job_cluster_key: rdd_adx_test_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            autoscale:
              min_workers: 1
              max_workers: 4
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
