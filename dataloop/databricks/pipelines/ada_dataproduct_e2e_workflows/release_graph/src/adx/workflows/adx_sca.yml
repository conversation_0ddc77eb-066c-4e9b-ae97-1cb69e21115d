resources:
  jobs:
    adx_sca_etl_nightly:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: "[DEPRECATED] ADA Release Graph - ADX - SCA - E2E - Nightly"
      description: Ingest SCA reports from ADX
      tags:
        responsible_team: "Release Driven Development"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "E2E"
        schedule: "Nightly"
        "ADA Release Graph": ""
        "ADX": ""
        "SCA": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      # Nightly run at 12:43 AM UTC
      schedule:
        quartz_cron_expression: 0 43 0 * * ?
        timezone_id: UTC
        pause_status: PAUSED

      tasks:
        - task_key: adx_sca_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract_sca.py
            parameters:
              - --nightly
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_adx_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: adx_sca_transform
          depends_on:
            - task_key: adx_sca_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform_sca.py
            parameters:
              - --task_output
              - adx_sca_extract
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_adx_job_cluster

        - task_key: adx_sca_load
          depends_on:
            - task_key: adx_sca_transform
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_release_v1
              - --mapping_file
              - adx_sca.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_adx_job_cluster

      job_clusters:
        - job_cluster_key: rdd_adx_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            autoscale:
              min_workers: 2
              max_workers: 8
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
