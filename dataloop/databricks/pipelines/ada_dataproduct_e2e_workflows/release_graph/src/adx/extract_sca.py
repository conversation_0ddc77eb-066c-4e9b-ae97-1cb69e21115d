"""Module for running SCA extraction and ingesting data into Databricks.

This module provides functionality to extract SCA reports based on specific dates or versions,
and then ingests this data into a Databricks delta table for further processing and analysis.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>sch GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import asyncio
import datetime
import logging
from typing import ClassVar

import nest_asyncio
from base import ADXBaseBronzeExtractor, get_adx_client
from constants.common import ADX_BATCH_SIZE, DBX_SCHEMA
from constants.sca import DESCRIPTION_BRONZE, SCA_BRONZE_SCHEMA, SCA_COLUMNS_OF_INTEREST
from pyspark.sql import DataFrame
from rddlib import FullSchemaName, get_dbx_env_catalog, get_rdd_secret, setup_databricks_logging

logger = logging.getLogger(__name__)


class ADXSCABronzeExtractor(ADXBaseBronzeExtractor):
    """Class for extracting and ingesting SCA data from Azure Data Explorer into a Databricks Delta table."""

    # Class fields
    ADX_DB: ClassVar[str] = "EmbeddedComponents"
    BRONZE_TABLE: ClassVar[str] = "adx_sca"
    COLUMNS_FOR_QC: ClassVar[list[str]] = SCA_COLUMNS_OF_INTEREST
    TABLE_DESCRIPTION: ClassVar[str] = DESCRIPTION_BRONZE

    async def get_latest_version_of_date(self, date: datetime.date) -> str:
        """Retrieve the latest version from ADX for a specified date."""
        query = f"""BazelTargetMetrics | where LastChanged > datetime("{date.isoformat()} 00:00:00+00:00") and
             LastChanged < datetime("{date.isoformat()} 23:59:59+00:00") | distinct Revision"""
        response_df = await self._run_kusto_query(query)
        if response_df.empty:
            return None

        try:
            return response_df.loc[0, "Revision"]
        except KeyError | IndexError as err:
            raise Exception(f"No version found for the specified date: {date}.") from err

    async def get_sca_components(self, version: str) -> list[str]:
        """Retrieve all unique component names for a specified version."""
        query = f'BazelTargetMetrics | where Revision == "{version}" | project Name'
        response_df = await self._run_kusto_query(query)
        component_names = response_df["Name"].unique().tolist()
        logger.info(f"Found {len(component_names)} components for version: {version}")
        return component_names

    async def _fetch_data_for_batch(self, version: str, batch_components: list) -> DataFrame:
        """Fetch data for a specified batch of components asynchronously."""
        component_list = ", ".join(f"'{x}'" for x in batch_components)
        query = f"""
            BazelTargetMetrics |
            where Revision == '{version}' and
            Name in ({component_list}) |
            project {", ".join(SCA_COLUMNS_OF_INTEREST)}
        """
        response_df = await self._run_kusto_query(query)
        if response_df.empty:
            return None

        # Convert the pandas DataFrame to a Spark DataFrame before returning
        spark_df = self.spark.createDataFrame(response_df, schema=SCA_BRONZE_SCHEMA)
        return spark_df

    async def fetch_data(self, version: str, components: list, batch_size: int) -> DataFrame:
        """Fetch data in parallel for components using batches asynchronously."""
        # Split the components into manageable batches according to the batch size
        batches = [components[i : i + batch_size] for i in range(0, len(components), batch_size)]

        # Create asynchronous tasks for fetching data for each batch
        tasks = [self._fetch_data_for_batch(version, batch) for batch in batches]

        # Execute all tasks concurrently and wait for all results
        results = await asyncio.gather(*tasks)

        # Initialize an empty Spark DataFrame with sca bronze schema
        all_components_df = self.spark.createDataFrame([], schema=SCA_BRONZE_SCHEMA)

        # Combine all non-None DataFrames from results into one DataFrame
        for result_df in results:
            if result_df is not None:
                all_components_df = all_components_df.union(result_df)

        return all_components_df


async def run_sca_extract(
    extractor: ADXSCABronzeExtractor,
    version: str | None = None,
    date: datetime.date | None = None,
    nightly: bool = False,
) -> str:
    """Runs the SCA extraction process and ingests data into a Databricks delta table.

    This function determines the date and version for the SCA extraction based on the provided
    arguments. It then fetches and ingests the data corresponding to the determined date and version
    into a Databricks delta table.

    Args:
        extractor (ADXSCABronzeExtractor): An instance of ADXSCABronzeExtractor that handles the
            data extraction logistics.
        version (str, optional): The specific commit version or tag to extract data for.
        date (datetime.date, optional): The specific date to extract data for.
        nightly (bool, optional): Flag to extract data for last nightly run.

    Returns:
        str: The version string of the data extracted, indicating the specific dataset version
             handled during the extraction.
    """
    if version is not None:
        version = version
    else:
        if nightly:
            if date is None:
                date = datetime.date.today() - datetime.timedelta(days=1)
        elif date is not None:
            pass
        else:
            date = datetime.date.today()

        version = await extractor.get_latest_version_of_date(date)
        if not version:
            raise Exception("No version found for today. Exiting.")

    components = await extractor.get_sca_components(version)
    df = await extractor.fetch_data(version, components, ADX_BATCH_SIZE)
    extractor.ingest_data(df)
    # Close ADX client
    await extractor.adx_client.close()

    return version


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(prog="SCA Extractor")
    parser.add_argument("-v", "--version", help="Specific commit version or tag to extract data for.")
    parser.add_argument("-d", "--date", help="Specify the date (YYYY-MM-DD) to extract data for.")
    parser.add_argument(
        "-n",
        "--nightly",
        action="store_true",
        help="Extract data for the previous day.",
    )
    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )
    parser.add_argument("-i", "--run_id", help="Run identifier for logging purposes.")
    parser.add_argument(
        "-m",
        "--test_mode",
        action="store_true",
        help="Test Mode. If set, uses test table.",
    )

    args, unknown = parser.parse_known_args()
    test_mode = args.test_mode
    catalog_name = get_dbx_env_catalog("bronze")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_name, DBX_SCHEMA, False), "adx_sca/bronze", run_id=args.run_id, enabled_loggers=["base"]
    )

    # Create ADX client
    client_id = get_rdd_secret("sp-adx-client-id")
    client_secret = get_rdd_secret("sp-adx-client-secret")
    adx_client = get_adx_client(client_id, client_secret)

    # Create extractor
    extractor = ADXSCABronzeExtractor(adx_client, catalog_name, test_mode)

    # Enable nested asyncio event loops
    nest_asyncio.apply()
    # Run SCA extraction
    version = asyncio.run(run_sca_extract(extractor, args.version, args.date, args.nightly))

    logger.info(f"Setting a job task value: version={version}")
    from pyspark.dbutils import DBUtils

    dbutils = DBUtils(extractor.spark)
    dbutils.jobs.taskValues.set(key="version", value=version)
