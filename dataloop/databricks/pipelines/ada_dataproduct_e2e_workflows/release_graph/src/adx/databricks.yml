bundle:
  name: ada_dataproduct_e2e_adx

include:
  # - ../../../../common/rdd_orchestration/env_targets.yml
  # TODO: Remove as soon as VSCode databricks extension is able to correctly import above
  - ../common/env_targets.yml
  - workflows/*.yml

variables:
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E8ads_v5_rt14.3"
  driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"

targets:
  qa:
    variables:
      nightly_schedule: "0 13 6 ? * Wed"
  prod:
    variables:
      nightly_schedule: "0 13 6 * * ?"
