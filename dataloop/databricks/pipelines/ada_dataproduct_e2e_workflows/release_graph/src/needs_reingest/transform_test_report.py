"""ADX test report silver transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
from typing import ClassVar

from base import ADXBaseSilverTransformer
from constants.common import DBX_SCHEMA
from constants.test_report import TEST_REPORT_SILVER_FIELDS_MAP, versions
from pyspark.sql import DataFrame, Window
from pyspark.sql.functions import col, concat, lit, row_number
from pyspark.sql.types import BooleanType, DoubleType, IntegerType, TimestampType
from rddlib import FullSchemaName, get_dbx_env_catalog, quality_check, setup_databricks_logging

logger = logging.getLogger(__name__)


class ADXTestReportSilverTransformer(ADXBaseSilverTransformer):
    """Class for ADX test report silver layer transformation."""

    # Class fields
    BRONZE_TABLE: ClassVar[str] = "adx_test_report"
    SILVER_TABLE: ClassVar[str] = "adx_test_report"
    SILVER_FIELDS_MAP: ClassVar[dict] = TEST_REPORT_SILVER_FIELDS_MAP

    def get_bronze_data_of_version(self, version: str) -> DataFrame:
        """Get ADX test report from bronze layer for specific version."""
        table_name = f"{self.BRONZE_TABLE}_test" if self.test_mode else self.BRONZE_TABLE
        full_table_name = f"{self.catalog_bronze}.{DBX_SCHEMA}.{table_name}"
        df = self.spark.read.table(full_table_name).filter(f"SutCommitId = '{version}'")
        return df

    def transform(self, df: DataFrame) -> DataFrame:
        """Transform test reports dataframe for silver layer.

        1. Rename column names to adhere to snake_case format
        2. Apply datatypes on certain columns
        3. Add a new column named report_id
        """
        df = super().transform(df)

        w = Window().partitionBy("sut_commit_id").orderBy("test_name")
        df = df.withColumn("report_id", concat(col("test_name"), lit("_"), row_number().over(w)))
        df = df.withColumn(
            "report_creation_time",
            col("report_creation_time").cast(TimestampType()),
        )
        df = df.withColumn("run_duration", col("run_duration").cast(DoubleType()))
        df = df.withColumn("exitcode", col("exitcode").cast(IntegerType()))
        df = df.withColumn("test_duration", col("test_duration").cast(DoubleType()))
        df = df.withColumn("test_results_pass", col("test_results_pass").cast(BooleanType()))

        return df

    def _run_quality_check(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        super()._run_quality_check(df)

        # Count of rows for df
        total_count = df.count()

        # Check for test_results_pass shall only be either true or false
        values_to_match = ["true", "false"]
        non_matching_rows = quality_check.unique_value_validation(df, values_to_match)
        count_of_non_matching_values = non_matching_rows.count()
        if count_of_non_matching_values == 0:
            quality_check.log_quality_check_result(True, "unique_value_validation", None)
        else:
            quality_check.log_quality_check_result(
                False,
                "unique_value_validation",
                f"{count_of_non_matching_values / total_count}.",
            )


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Transform ADX Test Report")

    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )

    parser.add_argument("-i", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()

    catalog_bronze = get_dbx_env_catalog("bronze")
    catalog_silver = get_dbx_env_catalog("silver")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_silver, DBX_SCHEMA, False),
        "adx_test_report/silver",
        run_id=args.run_id,
        enabled_loggers=["base"],
    )

    transformer = ADXTestReportSilverTransformer(catalog_bronze, catalog_silver, False)

    for version in versions:
        logger.info(f"Transforming test report from: {version}")
        df_bronze = transformer.get_bronze_data_of_version(version)
        df_silver = transformer.transform(df_bronze)
        # Run the data quality check
        # TODO: Fix errors in data quality checks before re-enabling
        # transformer.quality_check_run(df_transformed)
        transformer.ingest_data(df_silver)
