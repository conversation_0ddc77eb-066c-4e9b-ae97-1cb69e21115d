"""ADX test report bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from abc import ABC, abstractmethod
from typing import ClassVar

import pandas as pd
from azure.kusto.data import KustoConnectionStringBuilder
from azure.kusto.data.aio import KustoClient
from azure.kusto.data.helpers import dataframe_from_result_table
from constants.common import ADX_AUTHORITY_ID, ADX_CLUSTER, DBX_SCHEMA
from pyspark.sql import DataFrame, SparkSession
from rddlib import delta_table_utils as dtu
from rddlib import quality_check


def get_adx_client(id: str, secret: str) -> KustoClient:
    """Get a KustoClient for ADX."""
    kcsb = KustoConnectionStringBuilder.with_aad_application_key_authentication(
        ADX_CLUSTER, id, secret, ADX_AUTHORITY_ID
    )
    return KustoClient(kcsb)


class ADXBaseBronzeExtractor(ABC):
    """Base class for ADX bronze extraction."""

    # Class fields
    ADX_DB: ClassVar[str] = NotImplemented
    BRONZE_TABLE: ClassVar[str] = NotImplemented
    COLUMNS_FOR_QC: ClassVar[list[str]] = NotImplemented

    def __init_subclass__(cls) -> None:
        """Enforces subclasses to set the required class fields."""
        for field in ["ADX_DB", "BRONZE_TABLE", "COLUMNS_FOR_QC"]:
            if getattr(cls, field) is NotImplemented:
                raise NotImplementedError(f"Class {cls.__name__} must implement the {field} field.")

    # Instance fields
    spark: SparkSession
    adx_client: KustoClient
    catalog_bronze: str
    test_mode: bool

    def __init__(self, adx_client: KustoClient, catalog_bronze: str, test_mode: bool):
        """Initialize a ADXBaseBronzeExtractor.

        Args:
            adx_client (KustoClient): An initialized client to connect to Azure Data Explorer.
            catalog_bronze (str): The name of the bronze catalog. Ex. bronze_dev
            test_mode (bool): Provide True for an end to end test run.
        """
        self.spark = SparkSession.builder.getOrCreate()
        self.adx_client = adx_client
        self.catalog_bronze = catalog_bronze
        self.test_mode = test_mode

    async def _run_kusto_query(self, query: str) -> pd.DataFrame:
        """Execute a Kusto query using the provided ADX client."""
        response = await self.adx_client.execute(self.ADX_DB, query)
        return dataframe_from_result_table(response.primary_results[0])

    def _run_quality_check(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        total_count = df.count()

        # Missing value checks
        # Iterate over each column and check for missing values
        count_of_missing_values = 0
        for col_name in self.COLUMNS_FOR_QC:
            missing_value_rows = quality_check.missing_value_detection(df, column=col_name)
            count_of_missing_values = missing_value_rows.count()
            if count_of_missing_values == 0:
                quality_check.log_quality_check_result(True, "missing_value_detection", col_name)
            else:
                quality_check.log_quality_check_result(
                    False,
                    "missing_value_detection",
                    f"{col_name}: {count_of_missing_values / total_count}.",
                )

    def ingest_data(self, df: DataFrame) -> None:
        """Ingest data into Databricks delta table."""
        df = df.dropDuplicates()

        # Run the data quality check
        self._run_quality_check(df)

        table = f"{self.BRONZE_TABLE}_test" if self.test_mode else self.BRONZE_TABLE
        table_full = f"{self.catalog_bronze}.{DBX_SCHEMA}.{table}"
        if dtu.table_exists(table_full):
            dtu.append(df, table_full)
        else:
            dtu.overwrite(df, table_full)


class ADXBaseSilverTransformer(ABC):
    """Base class for ADX silver transformation."""

    # Class fields
    BRONZE_TABLE: ClassVar[str] = NotImplemented
    SILVER_TABLE: ClassVar[str] = NotImplemented
    SILVER_FIELDS_MAP: ClassVar[dict[str, str]] = NotImplemented

    def __init_subclass__(cls) -> None:
        """Enforces subclasses to set the required class fields."""
        for field in ["BRONZE_TABLE", "SILVER_TABLE", "SILVER_FIELDS_MAP"]:
            if getattr(cls, field) is NotImplemented:
                raise NotImplementedError(f"Class {cls.__name__} must implement the {field} field.")

    # Instance fields
    spark: SparkSession
    catalog_bronze: str
    catalog_silver: str
    test_mode: bool

    def __init__(self, catalog_bronze: str, catalog_silver: str, test_mode: bool):
        """Initialize a ADXBaseBronzeExtractor.

        Args:
            catalog_bronze (str): The name of the bronze catalog. Ex. bronze_dev
            catalog_silver (str): The name of the silver catalog. Ex. silver_dev
            test_mode (bool): Provide True for an end to end test run.
        """
        self.spark = SparkSession.builder.getOrCreate()
        self.catalog_bronze = catalog_bronze
        self.catalog_silver = catalog_silver
        self.test_mode = test_mode

    def _run_quality_check(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        total_count = df.count()

        # Missing value checks
        # Iterate over each column and check for missing values
        col_names = df.schema.names
        for col_name in col_names:
            missing_value_rows = quality_check.missing_value_detection(df, column=col_name)
            missing_rows_count = missing_value_rows.count()
            if missing_rows_count == 0:
                quality_check.log_quality_check_result(True, "missing_value_detection", col_name)
            else:
                quality_check.log_quality_check_result(
                    False,
                    "missing_value_detection",
                    f"{col_name}: {missing_rows_count / total_count}.",
                )

    def transform(self, df: DataFrame) -> DataFrame:
        """Transform dataframe for silver layer.

        Maps the bronze column names according to `self.SILVER_FIELDS_MAP`.
        """
        for original_name, new_name in self.SILVER_FIELDS_MAP.items():
            df = df.withColumnRenamed(original_name, new_name)
        return df

    @abstractmethod
    def get_bronze_data_of_version(self, version: str) -> DataFrame:
        """Retrieve the bronze data of the given version."""
        pass

    def ingest_data(self, df: DataFrame) -> None:
        """Write dataframe to silver layer."""
        table = f"{self.SILVER_TABLE}_test" if self.test_mode else self.SILVER_TABLE
        table_full = f"{self.catalog_silver}.{DBX_SCHEMA}.{table}"
        if dtu.table_exists(table_full):
            dtu.append(df, table_full)
        else:
            dtu.overwrite(df, table_full)
