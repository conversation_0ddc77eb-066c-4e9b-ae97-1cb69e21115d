resources:
  jobs:
    needs_etl_release_reingest:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: ADA Release Graph - Needs & ADX Test Reports - E2E - Manual Reingest
      tasks:
        - task_key: reingest_adx_test_reports_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract_test_report.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_reingest_job_cluster
          libraries:
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/requirements.txt
            - requirements: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/requirements.txt
            - pypi:
                package: rddlib==${var.rddlib_version}

        - task_key: reingest_adx_test_reports_transform
          depends_on:
            - task_key: reingest_adx_test_reports_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform_test_report.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_reingest_job_cluster

        - task_key: reingest_adx_test_reports_load
          depends_on:
            - task_key: reingest_adx_test_reports_transform
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_release_v1
              - --mapping_file
              - adx_report.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_reingest_job_cluster

        - task_key: reingest_needs_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract.py
            parameters:
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_reingest_job_cluster

        - task_key: reingest_needs_transform
          depends_on:
            - task_key: reingest_needs_extract
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters:
              - --application
              - release
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_reingest_job_cluster

        - task_key: reingest_needs_load
          depends_on:
            - task_key: reingest_needs_transform
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/ada_dataproduct_e2e_load/files/load.py
            parameters:
              - --workspace
              - ${var.env}
              - --level
              - silver
              - --schema
              - ada_release_graph
              - --database
              - ada_release_v1
              - --mapping_file
              - needs_release.j2
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: rdd_reingest_job_cluster

      job_clusters:
        - job_cluster_key: rdd_reingest_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
