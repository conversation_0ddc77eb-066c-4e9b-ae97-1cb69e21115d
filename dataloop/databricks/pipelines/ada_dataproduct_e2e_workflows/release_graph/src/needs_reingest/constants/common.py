"""Helper module to define ADX related constants and schema."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

# ADX information

ADX_CLUSTER = "https://decmetricdata.westeurope.kusto.windows.net/"
ADX_AUTHORITY_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
# Fetch 200 components in a single kusto query (adjusted as per response size limits imposed by ADX)
ADX_BATCH_SIZE = 200

# Databricks information

DBX_SCHEMA = "ada_release_graph"

ART_PATH = "https://jfrog.ad-alliance.biz/shared-generic-dev-local/sphinx_needs_json/"
BATCH_SIZE = 100

NEEDS_PREFIX = "urn:pace:ontology:needs:"
PACE_PREFIX = "urn:pace:ontology:"

TYPE_MAPPINGS = {  # pragma: no cover
    "activity-group": "urn:pace:ontology:ActivityGroup",
    "arche-ecu": "urn:pace:ontology:ArchEECU",
    "assumption": "urn:pace:ontology:Assumption",
    "cm-item": "urn:pace:ontology:ConfigurationManagementItem",
    "data-category": "urn:pace:ontology:DataCategory",
    "usecase-db": "urn:pace:ontology:DatabaseUseCase",
    "req-def": "urn:pace:ontology:Definition",
    "deploy": "urn:pace:ontology:Deploy",
    "ddd": "urn:pace:ontology:DesignDecisionDocument",
    "development-section": "urn:pace:ontology:DevelopmentSection",
    "eq-classes": "urn:pace:ontology:EquivalenceClasses",
    "error-propagation-model": "urn:pace:ontology:ErrorPropagationModel",
    "hazard": "urn:pace:ontology:Hazard",
    "hazardous-behaviour": "urn:pace:ontology:HazardousBehaviour",
    "hazardous-event": "urn:pace:ontology:HazardousEvent",
    "in-gateway": "urn:pace:ontology:InGateway",
    "req-tool-int": "urn:pace:ontology:InternallyDevelopedToolRequirement",
    "req-tool-int-sys": "urn:pace:ontology:InternallyDevelopedToolSystemRequirement",
    "kpi-value": "urn:pace:ontology:KeyPerformanceIndicator",
    "req-metric": "urn:pace:ontology:KeyPerformanceIndicatorMetrics",
    "req-tool": "urn:pace:ontology:NonProductionRequirement",
    "safety-os": "urn:pace:ontology:OperationalSituation",
    "out-gateway": "urn:pace:ontology:OutGateway",
    "package": "urn:pace:ontology:Package",
    "param-sys": "urn:pace:ontology:Parameter",
    "process_area": "urn:pace:ontology:ProcessArea",
    "process_landscape": "urn:pace:ontology:ProcessLandscape",
    "process_module": "urn:pace:ontology:ProcessModule",
    "safety-sg": "urn:pace:ontology:SafetyGoal",
    "sec-ast": "urn:pace:ontology:SecurityAsset",
    "sec-asm": "urn:pace:ontology:SecurityAssumption",
    "sec-sc": "urn:pace:ontology:SecurityClaim",
    "sec-ctrl": "urn:pace:ontology:SecurityControl",
    "sec-ds": "urn:pace:ontology:SecurityDamageScenario",
    "sec-goal": "urn:pace:ontology:SecurityGoal",
    "sec-muc": "urn:pace:ontology:SecurityMisuseCase",
    "sec-resrisk": "urn:pace:ontology:SecurityResidualRisk",
    "sec-risk": "urn:pace:ontology:SecurityRisk",
    "sec-th": "urn:pace:ontology:SecurityThreat",
    "sec-scen": "urn:pace:ontology:SecurityThreatScenario",
    "req-sw": "urn:pace:ontology:SoftwareRequirement",
    "solution-concept": "urn:pace:ontology:SolutionConcept",
    "req-sys": "urn:pace:ontology:SystemRequirement",
    "test-sw": "urn:pace:ontology:TestSw",
    "test-swint": "urn:pace:ontology:TestSwInt",
    "test-sys": "urn:pace:ontology:TestSys",
    "test-sysint": "urn:pace:ontology:TestSysInt",
    "tool": "urn:pace:ontology:Tool",
    "usecase": "urn:pace:ontology:UseCase",
    "arche-partition": "urn:pace:ontology:ArchEPartition",
    "arche-soc": "urn:pace:ontology:ArchESOC",
    "functional-chain": "urn:pace:ontology:FunctionalChain",
    "in-port": "urn:pace:ontology:InputPort",
    "milestone": "urn:pace:ontology:Milestone",
    "out-port": "urn:pace:ontology:OutputPort",
    "part": "urn:pace:ontology:Part",
    "safety-measure": "urn:pace:ontology:SafetyMeasure",
    "sig-def": "urn:pace:ontology:SignalDefinition",
    "req-stk": "urn:pace:ontology:StakeholderRequirement",
    "triggering-condition": "urn:pace:ontology:TriggeringCondition",
    "interface": "urn:pace:ontology:Interface",
    "interfaceblock": "urn:pace:ontology:InterfaceBlock",
    "variant": "urn:pace:ontology:ProductVariant",
    "activity": "urn:pace:ontology:SoftwareComponent",
    "comp-sw": "urn:pace:ontology:SoftwareComponent",
    "gateway": "urn:pace:ontology:SoftwareComponent",
    "runnable": "urn:pace:ontology:SoftwareComponent",
    "softwarecomponent": "urn:pace:ontology:SoftwareComponent",
    "block": "urn:pace:ontology:Block",
    "scenario": "urn:pace:ontology:Scenario",
    "test-design": "urn:pace:ontology:TestDesign",
    "feature": "urn:pace:ontology:Feature",
    "proxyport": "urn:pace:ontology:ProxyPort",
    "functionality": "urn:pace:ontology:Functionality",
    "sut_profile": "urn:pace:ontology:SutProfile",
    "failure": "urn:pace:ontology:Failure",
    "sec-class": "urn:pace:ontology:SecurityClass",
    "rel-artifact": "urn:pace:ontology:ReleaseArtifact",
    "rl-expectation": "urn:pace:ontology:ReleaseExpectation",
}

LINK_MAPPINGS = {  # pragma: no cover
    "sec_accepts": "urn:pace:ontology:accepts",
    "allocates": "urn:pace:ontology:allocates",
    "sec_compromise": "urn:pace:ontology:compromises",
    "sec_scen_damage": "urn:pace:ontology:correspondingImpact",
    "sec_misuse": "urn:pace:ontology:correspondingMisuseCase",
    "discusses": "urn:pace:ontology:discusses",
    "isAggregatedBy": "urn:pace:ontology:isAggregatedBy",
    "is_realized_by": "urn:pace:ontology:isRealizedBy",
    "sec_damage": "urn:pace:ontology:leadsTo",
    "prevents": "urn:pace:ontology:prevents",
    "sec_reduces": "urn:pace:ontology:reduces",
    "results": "urn:pace:ontology:resultsIn",
    "sec_mitigates": "urn:pace:ontology:sec_mitigates",
    "sec_transfers": "urn:pace:ontology:transfers",
    "aggregates": "urn:pace:ontology:aggregates",
    "associates": "urn:pace:ontology:associates",
    "sec_scen_threat": "urn:pace:ontology:correspondingThreat",
    "covers": "urn:pace:ontology:covers",
    "deploys": "urn:pace:ontology:deploys",
    "has_in_port": "urn:pace:ontology:hasInPort",
    "has_out_port": "urn:pace:ontology:hasOutPort",
    "has_port": "urn:pace:ontology:hasPort",
    "sut_profile": "urn:pace:ontology:hasSutProfile",
    "instantiates": "urn:pace:ontology:instantiates",
    "it_allocated_tool": "urn:pace:ontology:isAllocatedTo",
    "test_design": "urn:pace:ontology:isSpecifiedByTestDesign",
    "tool-for-storage": "urn:pace:ontology:isStoredOn",
    "kpi_value": "urn:pace:ontology:kpiValueUsed",
    "mitigates": "urn:pace:ontology:mitigates",
    "monitors": "urn:pace:ontology:monitors",
    "sec_relevance": "urn:pace:ontology:originatesFrom",
    "sec_scenario": "urn:pace:ontology:originatesFrom",
    "resolves": "urn:pace:ontology:resolves",
    "supports": "urn:pace:ontology:supports",
    "scenario_id": "urn:pace:ontology:testsScenario",
    "applies_to_variants": "urn:pace:ontology:appliesToProductVariant",
    "calculates": "urn:pace:ontology:calculates",
    "depends-on": "urn:pace:ontology:dependsOn",
    "includes": "urn:pace:ontology:includes",
    "security-classification": "urn:pace:ontology:isSecurityClassifiedAs",
    "provides": "urn:pace:ontology:provides",
    "requires": "urn:pace:ontology:requires",
    "implements": "urn:pace:ontology:implements",
    "verifies": "urn:pace:ontology:verifies",
    "affects": "urn:pace:ontology:affects",
    "composes": "urn:pace:ontology:composes",
    "derives": "urn:pace:ontology:isDerivedFrom",
    "triggered_by": "urn:pace:ontology:isTriggeredBy",
    "contains": "urn:pace:ontology:contains",
    "references": "urn:pace:ontology:references",
    "realizes": "urn:pace:ontology:realizes",
    "belongs": "urn:pace:ontology:belongsTo",
    "cb_allocates": "urn:pace:ontology:cbAllocates",
    "artifact_link": "urn:pace:ontology:artifactLink",
    "use_case_link": "urn:pace:ontology:uses",
    "rl_expectation": "urn:pace:ontology:expects",
    "test_trigger": "urn:pace:ontology:hasTestTrigger",
    "test_platform": "urn:pace:ontology:runsOnTestPlatform",
    "tags": "urn:pace:ontology:tag",
    "test_name": "urn:pace:ontology:test_name",
}

VERSIONS = [
    "5b7296e2fb59beea95eaab342c9b07d6d8512013",
    "862edf06f4b89b762b5e9cfe486e696a639eecf2",
    "dfb7809c9f6f4759f52e315653822eecd68d4b98",
    "50d6e56095037ee6d4be6b142cab24ffc75d8268",
    "7753a3fd98fe763c3d210fa61721e1f405d34385",
    "c264a95cd7e4393aed37ee7d404a63fd4320e444",
    "c404fcdc2481597c56c295e1d69b46dc225f0569",
    "f96f9725b0e9c25824dc6378eca3ad54cc8df001",
    "e5b6647505b4079ce29040e2257d20b37e4d391d",
    "c1ee4eaf11dbf69ff670b0e483fce7256c9d0ed3",
    "aafd2348ba076faedf85f25d7fde634a606b7c2e",
    "ee537897521a4474997456d4e8dfa6fb81f61871",
    "047b6ca366df9d6c0d8ccad5b629ec65b0cb6323",
    "28a3bab171b0c5e8405e9cbc78ac8d814281a765",
    "722bd0917c30e66018c5a07734815bc9c661859c",
    "1b17c14402ec1d09db23574cc434cb336d6e8c71",
    "1aaff6f5c4714e9a41d10923246ae721457f66ea",
    "f6175a87afcb4e5205d397d3122f03a9e3b0fba0",
    "1935cde933706d0d344067c527051628c9413861",
    "a2b093a5d0e23e8fb256967ddfd08a6f29e4e9c7",
    "cdb79c599e0242559bae176d7406bd06491732a2",
    "release/237241-z150-alpha-candidate",
    "de13a07b32212e3c23712aa2b98f2995791e8915",
    "fec8cf9be35be4ed6650d7352f5ad4c77508c1c0",
    "c46902956822969c90c5cabc59e7cdd7d8f0dbac",
    "be4128178a50e717764d45897c12c51c84c56700",
    "c56d18bc07a87a281002b745b63870283e5bcca3",
    "9a27ed9bb04315eea2bea98c820ea6fc72c198ad",
    "fe2c9489242bc6be646d53fb63f7de696896fe14",
    "963b7b7fc094658414288b43355388b2248c873c",
    "1bccf053d2b707237498b7c5cd862fa7531a19ea",
    "5cfb63fc1dff616adb634d3eabc61507958da092",
    "00aff71a26a7922fab74c877b6ba4624b1f234fd",
    "ba3396b2a7caf8a7ecdadd21fcd7fbb15cfe42ca",
    "7185796d514dcc2adb1aadf517388d83e35c1847",
    "6211780baf0d7e95f2814fe44d4616f99d7f26e6",
    "9e1ad0a44f3c35639946e4596f1e80055d441c48",
    "7513072925a4ffc0a3980c1de8a22a334e016d63",
    "75a7029671a0835df7b4d77b0abd21bfede6f39b",
    "3d962ddd650b2660992353fa3f81b458ce6f0e2b",
    "280bc3e49bef97354270582b9e04feb352c1533b",
    "50c66354d14bfa3b2b7e9ff8c16d0da441abf11b",
    "fe32a39b5c22b2e9a8ffc232f8f36ef4b10391f9",
    "e887d9916bcd9ca8fe9d66e560e134e2f29fd423",
    "37c893886f46117b2c953737703270c67d1d2655",
    "aeb3d8a27f970bbc8c11d36e8daeeff0ea12d9dd",
    "1f0d92cad3b82fa6cd112c6ebaae2fa62440620d",
    "2c0e8430e38c932f522cb0216db929fe70696d18",
    "4e7f40cae20aecc6eb30b1e7322581bd846530eb",
    "05a414a46c561c679c2beef1e7f66b0b06edf426",
    "5a5127031ac698a00dfc12b88c0fc96f4ed93231",
    "37632a453dcd3627872536251fb8d946d81bed24",
    "fd499f70cf4f9bee091846a315067f7cc823e737",
    "dc70cac5b28a3007e4bee277154ca60f2348ed98",
    "b1c719b1eab02e8e01f2af0f25faf79fb1bfab4f",
    "40ce42b3807ef041426b7d12f11cf028b81afc88",
    "14d7ca4a0b7384a0846b938d5f92fdfc1dbc9929",
    "b37cdb0a624b25771fea8c587770440454bf1670",
    "58bfe59969d39274fce5dce18af5ed86f4f4ebe7",
    "6ad1d8df2f55790a22593d562e1dc4c4975b7d4f",
    "73e2623e3ce969c9aabd6ba91db7e895f5abfa0d",
    "169f6746da049cfa64e39ca381f33246f88a91c5",
    "38676a2fa4dbdceed7c6080ad9e6e2b98437dbb0",
    "5157ed98c82ad7ff81e7671d813d3640c99fd6bb",
    "be4f61272cf00e74112ff6f97303ffe348577d5f",
    "eea3dfa95814cc284764e5231333b151a68661d1",
    "8a8596cf2be96d994a1166bf57307b0bb12bb5cd",
    "ef222fb38f073d967f81daa44fff19fcb9faa2a6",
    "b80c712873036faffd9ee654c14a3ec91896529e",
    "20716928a532a30f4da98f08f67c5887f2d6a174",
    "59db53419d4fd6e3dadc502f96936b1fd77948dc",
    "a1ab1066edaff8cc475d4925e5021f85c40c049f",
    "8a43dbe1570b3f9d023e71824cd13b0fb01ca26a",
    "ec063a337f43579abc770d3666b74397cdddb387",
    "02e2750644d0a0fdfff75c33489a4f25a84e5b99",
    "03e051ba135e29ef51fd93fe8b99067070c60902",
    "b3e9bd21b9a59120b824b2243965b627b39f5602",
    "e707e867c866070c1ad2d52e345746a4950e3b51",
    "4cfcd8a2fc60201bbdcd9f861228083ea2e25235",
    "8bd9c288f36f2cce52bf6440766e145908b6cb68",
    "c0113a6943c2514e90262f1fdaacb512151c1d22",
    "a4af28ca0b295fe9201e25c222d84a171b5e4d45",
    "226d95989a5ebec4739bda0fec0d0297e315a525",
    "02244c113f09780de25da71e46114530037da44f",
    "1ab3aea73cb1527c339a7c83ce88fadfd6f4504e",
    "70892655b7fb40849906797cf6684b74cbbf2f93",
    "85e7fbcd53b434a95484711b6de4aff5f2c8deb3",
    "840a3444a4ab3e59f86cdef2863d8356fc20faf8",
    "f018e1334234adb5f461e11068daddd182242b40",
    "6627f49f7900b39bed6ba41854cebad2bad981d6",
    "aad2671c52bd110b9cf3115cedbf4336ada17389",
    "f89c05e9cddddc2ec5b7fe0b7a027f9bc4e11eed",
    "c89585351b2b23a7d18ef1019a746d347496aaa4",
    "8073be2fe22af559ca13ee4dc927cf6befa1345e",
    "bd02dc8448352fe8838da5ab168f407e2efa5759",
    "a060b55f386f4ee0c062b0c0c3ff6f0481443bf0",
    "4ca23d822d8ba2e4fe7987e61efc4594435a9088",
    "1f65c5522765df15e35182babd200f4fb564f428",
    "0a65774332372708033c38833647abae1bfb56e7",
    "65c578d001939be5aadf86a89208724701528335",
    "cb37562f57d33c8c741ddc6b3c256f011ad15fc6",
    "3c2d27c42903144569a1b1a0b06815978e3c86a8",
    "f59626771ef1224d4d4fbbbe501c03db3bd51c3b",
    "5992f5300164248fca2471929640324cd33a3363",
    "7accd73274c2ccca2dca8745566f2b290375381a",
    "46f36d76dbd3ae39e1cffcc1db13df237783053a",
    "1ff7e17fe4e7747dbd74dd0ec82a0a3ee3b31777",
    "cec12fcebc76fb1e97580244b72634474bb44525",
    "24ada2f3fcf8a6e2055dac4c2ae167a4ffebe8b7",
    "13ec228afd06052fb484313382d2ac351983b762",
    "b5ea9850f66de8ab80fc76b441f340021f9d6780",
    "f7f5ad05d3a61abf5261550f0402fd849e916ccc",
    "2c07209dd599e1c971a469b890f1fb615f4d48ee",
    "8c9a7383f15a3ce95eff5241ddfd88ee205f7d86",
    "e315d0725c52cb2d84191f24388f77ef5a157c9d",
    "f82bb809d1a6d8aa82d69a65e6b7010f58a9cf56",
    "d553a66925c91e57d8a31f8b7d78ab58b1b64903",
    "4308972a23e2789658ab5658f6dd196ac2d697eb",
    "85e6067a67cdcb318cfc5309547eadcbe7cbbfa8",
    "d2cbe1df9224d0f0fb7793e3bfbee0c635f4669d",
    "f40089f72752675fd3de37785ec6c41680257da5",
    "9237b2ae72cda5ae84aece3c53077c921b925d6f",
    "639e230bec942a18fdb80cc7033acbb8b495a629",
    "60ed47cc721915caa8de17073783524ae4263b97",
    "8cd32d6300598cbaba961cac273ea4eb3adb8101",
    "a5f80b7d78cd3075601eb1298ecc4297c129f123",
    "0d4fcd84a7976d83820454326813349ef013bea7",
    "ac437f10724ef3a4d8d6552f50a9dededa3a9648",
    "c135130c2eef2bac9f67a70cdffc5f1c5c2b4cb3",
    "812999521fd61fac51e421df7bb1e99fcc7116f5",
    "38e0adec382e8ddf7cde5e7bee696e917fec7002",
    "010bd76a07e3a87a1281a2ec9bb7a287602c1b5f",
    "2fb2b14ff9e7b8f17416dbba8c676e6aeb4efada",
    "9ae2bacb754e9db98c581584914fdce55ea44f0a",
    "5960c776704a84a53c9a0d52ffeeaf882b442aac",
    "b9ea4cd414bbeca6c23468b94d6a8c9e501c9686",
    "2b19df31537a91bd00dc7ad576dbab5883ccbe60",
    "76e0dc0e2d5a88f877fb489a6a839d6e780119bb",
    "2aa993f83ee2d2174f869533731ce2cabd8d266d",
    "4920e7c15d7d5b3a5dfa2920898b34857454c6f2",
    "fc5d5c4d0ffe0d6010257a2935c6588e345a36f6",
    "621f9247c7ca5c672e22108cec5429e1bc1dc03e",
    "61edf7904ec00cf731172f9375da23f543c07da3",
    "a2c992e4770385da3bfb7a45fd87236d5b50bb0f",
    "5c9de43b7b930a4fe4dca8bf75d94dde99ac991d",
    "2f8a318ee887aab235610afe387b0643044abfbc",
    "4d1e02d0c797b7b946d33015d0dbaba69b862983",
    "704f6379f81b2d83f181ff3dc50adecd664b8b6b",
    "462dc76deceaa64f772371f69fc336f601b02c9d",
    "0b1a08f85137de9bfedfe3312ec9cc9702322113",
    "725596be2db24594a22232d5483f490ecd5d1727",
    "4a1332714c9c70dc10c6f48b3a43a26b80037b0c",
    "e93a9316fb95cce91643bed400810bf2a3fcd26d",
    "66518ddf0ee3cc0802b478c0a1308a7cc0aa7f4c",
    "93831171e7421c3604e6f24129dc8a90cee39a4d",
    "53feb1e8d65df48f0547662cce3a52e5884c43ff",
    "3d45464b0fc193e72725581055ca3d5193e607fb",
    "239a5f682667a19504757836c012215c178d891f",
    "ef60abb6424e3fa0c05bda9f987c4168127bce2d",
    "50cd1c9383405191adf02500420c16664ad8df77",
    "e14dfe775c7606670eea4de8f36029325eeb98e3",
    "25723bf2be391ccd8b5b5d2a7a2356edfcb7c0e8",
    "107e89859a21fbfd833f42823d56c70269025339",
    "9df1bf7feb1c0adb05b1de405bfb04745b29cced",
    "6a05c04028fa761a9fec94fa78f312c27d434fcb",
    "701664d23ceb12287f6176289463c6bad76960ec",
    "06cc58f801aedfad2f4d6cc0543024a58ccb0f1d",
    "39f1fc6510884622726b6a22f9fdf4f504a878d0",
    "745fc50ae9efff0fe80d9ed33c1f9c501feef83f",
    "699754762f4b85a1f5ed37dfa50c66da190bb41b",
    "67dc3a1b968f8cbdf16f79ac47977d0124704a45",
    "67b79733713b6bc46e8b47679676fa0b0edef21b",
    "7a14f4e7eb84d4457ba17e16e9b57cfee86800f8",
    "ebd832bb94dce3cfac0e2991aeeb4c84a0de1622",
    "e022fd2bc1bcaf366caf58959a43c56e58bb9c41",
    "7ada949ccfdecb67edd14f703b891c64c7294ffc",
    "c9abd24646c0c595435f68e6bff3bb416830b0d4",
    "e22729eff36bb5ec5ce824d76e612d8f7212d42b",
    "e34b7409dd37ea40c34dee928873fc38a016b63f",
    "63824d554f7409b4f07496db3db0f1674c58e550",
    "ffa74e02f2eaf1d9868d45c61cbf879568659b4a",
    "bdc73fc4baebf5277634696f1cc1b955f4fec74b",
    "628c7fe128124d980a93ed6c32838f9caff6ab91",
    "c439b348f09d0cb87857bb7b3c13ff1ad3b4bca6",
    "67c58fe105e07cc7c2a5845952c31c7258d481ed",
    "f4bc4d5ebac8072f11d666caba80ca09669eef07",
    "3ef1b7bea5bb35c593e01d7ceda9db138c6bbcad",
    "f3c373d06ab43e45b9e9331fd8afceb6f49ee72e",
    "80dd77f5a0dafe940ca8eecf6bcc3b8be755f7ae",
    "7bc920919c32001284211f631d668326cf4c1ab4",
    "05ddfbc44f69efd379c52ae200e39c66aca2a425",
    "fb76b91e6fc97f005fa0086b514621351da7d8e0",
    "d508cab3e7a063632d72517b2349d0818d97f418",
    "f1d99e29fd23418c2709472d57a17632f667b247",
    "4fd052f2627b54c305f8675db9eabf527066abbf",
    "a0b673638adc42f2c99afbd43199ed9208d9df93",
    "552503888a2be77a14fcf7d49755169617b924f0",
    "4697ed31ad19465dcd349099eb282760c189fa09",
    "1b95d3dcbd2bcea4be04107cadcbb305529fcd86",
    "5c64059d935eb44926f65532d01d4ef3f199d8b1",
    "d2bd5e8b1a40c0d97e99e5d2c095a50eae384646",
    "5743db9b39b2defd7ab315b02b265e1f89a577ad",
    "19bc2bd2b63046b75d79bba5d701d82e18f8e6f7",
]
