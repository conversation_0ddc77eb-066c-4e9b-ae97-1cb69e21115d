"""Extract needs data and upload to Databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import json
import logging
import tarfile
from datetime import datetime
from io import BytesIO
from time import sleep
from typing import Any, ClassVar

from artifactory import ArtifactoryException, ArtifactoryPath
from constants.common import ART_PATH, VERSIONS
from delta import DeltaTable
from httpx import Client as HttpxClient
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import desc, lit, regexp_replace
from rddlib import FullSchemaName
from rddlib import delta_table_utils as dtu
from rddlib import get_rdd_secret, quality_check, setup_databricks_logging
from rddlib.dbx_utils import DBX_APP_SCHEMA_MAP, get_dbx_env_catalog
from urllib3.exceptions import ProtocolError

logger = logging.getLogger(__name__)

BASE_URL = "https://api.github.com"
TABLE_NAME = "needs"


class NeedsBronzeExtractor:
    """This class is responsible for extracting needs data from Artifactory."""

    MAX_ARTIFACTORY_FETCH_RETRIES: ClassVar[int] = 3
    ARTIFACTORY_FETCH_RETRY_WAIT_SEC: ClassVar[float] = 10.0

    app: str
    dbx_catalog: str
    dbx_schema: str
    test_mode: bool
    spark: SparkSession

    def __init__(self, dbx_catalog: str, app: str, dbx_schema: str, test_mode: bool) -> None:
        """Initialize a NeedsBronzeExtractor instance.

        Args:
            dbx_catalog (str): The name of the catalog.
            app (str): The application for which extraction will be performed.
            dbx_schema (str): The name of the schema in the catalog.
            test_mode (bool): Set to True for an end to end test run.
        """
        self.app = app
        self.dbx_catalog = dbx_catalog
        self.dbx_schema = dbx_schema
        self.test_mode = test_mode
        self.spark = SparkSession.builder.getOrCreate()

    def extract(self, sw_version: str, art_path: ArtifactoryPath) -> None:
        """Extract needs from artifactory and store in databricks."""
        try:
            # Fetch the binary content of all_needs.tgz from Artifactory
            tgz_data = self._fetch_from_artifactory(art_path)

            # Open the .tgz file from the binary data
            with tarfile.open(fileobj=BytesIO(tgz_data), mode="r:gz") as tar:
                # Look for 'all_needs.json' in the tar archive
                try:
                    json_file = tar.getmember("all_needs.json")  # Get the exact file
                except KeyError:
                    raise FileNotFoundError("The file 'all_needs.json' was not found in the tar archive.")

                extracted_file = tar.extractfile(json_file)
                data = json.load(extracted_file)
        except Exception:
            self.app = "not-release"
            data = self._fetch_from_artifactory(art_path)
            self.app = "release"

        needs_list = []
        # flatten json, remove empty fields, add mapped fields where applicable
        # Fetch the first 'version' in the needs structure
        version = list(data["versions"])[0]
        for need in data["versions"][version]["needs"].values():
            # Create a new dictionary for each need and append it to the list
            element = self._add_need(need)
            needs_list.append(element)

        # Convert list <dict> into dataframes
        needs_df = self.spark.createDataFrame(needs_list)

        # Add some metadata and mapped type info
        needs_df = needs_df.withColumn("version", lit(sw_version))

        if self.app == "release":
            # Clean up in owners column - replace email adress or user_name_pace with "Illegal owner value"
            needs_df = needs_df.withColumn(
                "owner",
                regexp_replace("owner", r"(^.*_pace.*$|^.*\@.*$)", "Illegal owner value"),
            )
            illegal_owners = needs_df.select("id", "owner").where(needs_df.owner == "Illegal owner value")
            illegal_owners_count = illegal_owners.count()
            if illegal_owners_count == 0:
                quality_check.log_quality_check_result(True, "illegal_owners", None)
            else:
                quality_check.log_quality_check_result(False, "illegal_owners", illegal_owners_count)

        self._update_delta_table(needs_df)

    def _add_need(self, raw_need: dict[str, Any]) -> dict[str, Any]:
        """Create entries for each need element."""
        new_need = {}
        # Add every non-empty and non-list attribute to element
        for need_key in raw_need.keys():
            attribute = raw_need[need_key]
            # Skip empty attribute
            if not attribute:
                continue

            # Add every list (special case for 'section'),
            # this will be used for relations between different need elements
            if isinstance(attribute, list) and need_key == "sections":
                continue

            new_need[need_key] = attribute

        return new_need

    def _fetch_from_artifactory(self, art_path: ArtifactoryPath) -> bytes:
        """Fetch all_needs.tgz from Artifactory."""
        n_tries = 0

        # Retry if error occurs until max retry count gets reached and wait for a fixed
        # number of seconds in between.
        # This robustifies the extraction against occasional connection issues with Artifactory
        # See https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/287153
        while n_tries <= self.MAX_ARTIFACTORY_FETCH_RETRIES:
            try:
                with art_path.open() as raw_data:
                    if self.app == "release":
                        return raw_data.read()  # Return the binary content of the all_needs.tgz file
                    else:
                        return json.load(raw_data)  # Return the json content of needs.json
            except ProtocolError:
                n_tries += 1
                # Sleep for defined amount of seconds before retry
                sleep(self.ARTIFACTORY_FETCH_RETRY_WAIT_SEC)
                continue

        logger.error("Connection error while fetching needs.json from Artifactory.")
        raise ArtifactoryException(
            "Maximum number of retries to fetch needs.json from Artifactory exceeded."
            f" Related path on Artifactory: {art_path}"
        )

    def _update_delta_table(self, df: DataFrame) -> None:
        """Write to databricks delta table."""
        table = f"{TABLE_NAME}_test" if self.test_mode else TABLE_NAME
        table_full = f"{self.dbx_catalog}.{self.dbx_schema}.{table}"
        if dtu.table_exists(table_full):
            delta_table = DeltaTable.forName(self.spark, table_full)
            current_df = delta_table.toDF()
            version_exists = current_df.filter(f"version == '{sw_version}'").count() > 0
            if version_exists:
                logger.info(f"Version {sw_version} exists in the table. Deleting before ingestion.")
                delta_table.delete(f"version == '{sw_version}'")
            else:
                logger.info(f"Version {sw_version} does not exist in the table.")

        dtu.append(df, table_full)

    def run_quality_check(self, df: DataFrame) -> None:
        """Processes pre-defined data quality checks with the aid of the rddlib.quality_check."""
        # Count of rows for df
        full_count_rows = df.count()

        # Missing value checks
        missing_value_rows = quality_check.missing_value_detection(df, column="id")
        missing_values_count = missing_value_rows.count()
        if missing_values_count == 0:
            quality_check.log_quality_check_result(True, "missing_value_detection", None)
        else:
            quality_check.log_quality_check_result(
                False, "missing_value_detection", f"Missing id values existing: {missing_values_count}"
            )

        # Duplicate check
        _, duplicate_id_rows = quality_check.deduplicate(
            df,
            subset=["content_id"],
            time_window=None,
            use_hashing=False,
            just_test=False,
        )
        count_duplicate_values = len(duplicate_id_rows)
        if count_duplicate_values == 0:
            quality_check.log_quality_check_result(True, "deduplicate", None)
        else:
            quality_check.log_quality_check_result(
                False,
                "deduplicate",
                "Count of rows that are duplicate: " + f"{count_duplicate_values} out of {full_count_rows}",
            )

        # Check for desired text pattern in the content_id column (e.g., all capital letters and numbers)
        desired_content_id_pattern = "^[A-Z_]+_+[1-9]*$"
        non_compliant_content_ids = quality_check.text_pattern_check(df, "content_id", desired_content_id_pattern)
        count_non_matching_pattern = non_compliant_content_ids.count()
        if count_non_matching_pattern == 0:
            quality_check.log_quality_check_result(True, "text_pattern_check", None)
        else:
            quality_check.log_quality_check_result(
                False,
                "text_pattern_check",
                "Count of rows with non-matching pattern: " f"{count_non_matching_pattern} out of {full_count_rows}",
            )

        # Unique value check
        df_unique_da_status = quality_check.unique_value_validation(
            df,
            "da_status",
            values_to_match=["draft", "implemented", "future", "proposed", "submitted"],
        )
        count_non_valid_rows = df_unique_da_status.count()
        if count_non_valid_rows == 0:
            quality_check.log_quality_check_result(True, "unique_value_validation", None)
        else:
            quality_check.log_quality_check_result(
                False, "unique_value_validation", count_non_valid_rows / full_count_rows
            )

    def get_latest_release_tag(self, github_token: str) -> dict[str, Any]:
        """Fetch latest release tag from pace repo."""
        headers = {
            "Accept": "application/vnd.github+json",
            "Authorization": f"Bearer {github_token}",
            "X-GitHub-Api-Version": "2022-11-28",
        }
        url = f"{BASE_URL}/repos/PACE-INT/pace/releases?per_page=100"

        # Get all pages of release tags.
        tags_pages = []
        with HttpxClient(headers=headers) as client:
            logger.info(f"fetching release tags from: {url}")
            response = client.get(url)
            response.raise_for_status()
            tags_pages.append(response.json())
            has_last = response.links.get("last")
            if has_last:
                last_url = has_last["url"]
                while url != last_url:
                    url = response.links["next"]["url"]
                    logger.info(f"fetching release tags from: {url}")
                    response = client.get(url)
                    response.raise_for_status()
                    tags_pages.append(response.json())

        if not tags_pages:
            raise Exception("No release tags found!")

        # Create list of tags with relevant content.
        cleaned_tags = []
        for tags in tags_pages:
            for tag in tags:
                cleaned_tag = {
                    "name": tag.get("tag_name"),
                    "created_at": datetime.strptime(tag.get("created_at"), "%Y-%m-%dT%H:%M:%SZ"),
                    "sha": tag.get("target_commitish"),
                }
                cleaned_tags.append(cleaned_tag)

        cleaned_tags.sort(key=lambda x: x["created_at"])

        return cleaned_tags[-1]

    def get_latest_adx_version(self) -> tuple[str, datetime] | None:
        """Get latest sha which has a test report from adx."""
        report_schema = f"{self.dbx_catalog}.{self.dbx_schema}"
        report_table = "adx_test_report"

        if self.spark.sql(f"SHOW TABLES IN {report_schema} LIKE '{report_table}'").count() == 0:
            return None

        delta_table = DeltaTable.forName(self.spark, f"{report_schema}.{report_table}")

        df = delta_table.toDF()
        df = df.orderBy(desc("ReportCreationTime"))

        latest_sut_commit_id = df.select("SutCommitId").first().SutCommitId
        creation_ts = df.select("ReportCreationTime").first().ReportCreationTime

        return latest_sut_commit_id, creation_ts


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(description="Needs Extractor")
    parser.add_argument("-r", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()

    app = "release"
    run_id = args.run_id

    catalog_name = get_dbx_env_catalog("bronze")
    schema_name = DBX_APP_SCHEMA_MAP[app]

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_name, schema_name, False), "needs/bronze", run_id=run_id, enabled_loggers=["base"]
    )

    extractor = NeedsBronzeExtractor(catalog_name, app, schema_name, False)

    from pyspark.dbutils import DBUtils

    dbutils = DBUtils(extractor.spark)
    art_name = get_rdd_secret("alliance-artifactory-username")
    art_key = get_rdd_secret("alliance-artifactory-token")
    github_token = get_rdd_secret("github-access-token")

    art_path = ART_PATH

    for sw_version in VERSIONS:
        # Skip already ingested version
        if not extractor.spark.sql(
            f"SELECT DISTINCT version FROM {catalog_name}.{schema_name}.needs WHERE version = '{sw_version}'"
        ).isEmpty():
            print(f"Data already exists for this version: {sw_version}")
            continue

        full_art_path = ArtifactoryPath(art_path + sw_version + "/needs.json", auth=(art_name, art_key))
        logger.info(f"Fetching needs.json from {full_art_path}")
        try:
            extractor.extract(sw_version, full_art_path)
            logger.info(f"Extracted needs version: {sw_version}")
        except Exception:
            logger.error(f"Failed to extract needs version: {sw_version}")
        dbutils.jobs.taskValues.set(key="version", value=sw_version)
