"""ADX test report bronze extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import asyncio
import datetime
import logging
from typing import ClassVar

import nest_asyncio
import pandas as pd
from base import ADXBaseBronzeExtractor, get_adx_client
from constants.common import DBX_SCHEMA
from constants.test_report import TEST_REPORT_BRONZE_SCHEMA, TEST_REPORT_COLUMNS_FOR_QC, versions
from pyspark.sql import DataFrame
from rddlib import FullSchemaName, get_dbx_env_catalog, get_rdd_secret, setup_databricks_logging

logger = logging.getLogger(__name__)


class ADXTestReportBronzeExtractor(ADXBaseBronzeExtractor):
    """Class for ADX test report bronze extraction."""

    # Class fields
    ADX_DB: ClassVar[str] = "TestReports"
    BRONZE_TABLE: ClassVar[str] = "adx_test_report"
    COLUMNS_FOR_QC: ClassVar[list[str]] = TEST_REPORT_COLUMNS_FOR_QC

    async def get_latest_version(self) -> str:
        """Get the latest SuT version based on ReportCreationTime."""
        query = """EasyEvalBasicReport | sort by ReportCreationTime desc |
                where SutCommitId !contains "placeholder" | project SutCommitId | take 1"""

        result_df = await self._run_kusto_query(query)
        commit = result_df.loc[0, "SutCommitId"]
        logger.info(f"Version of latest run: {commit}")
        return commit

    async def get_nightly_version_of_date(self, date: datetime.date) -> str:
        """Get the SuT version of the nightly test run for a certain date."""
        query = (
            f"EasyEvalBasicReport | where RunId contains 'pace_vehicle_performance' "
            f"and RunId contains '{date.isoformat()}' | distinct SutCommitId, RunId"
        )
        result_df = await self._run_kusto_query(query)
        if len(result_df.index) == 0:
            raise Exception(f"No data available from: {date}")

        commit = result_df.loc[0, "SutCommitId"]
        logger.info(f"Version of nightly run ({date}): {commit}")
        return commit

    async def get_versions_of_date(self, date: datetime.date) -> list[str]:
        """Get the SuT versions of the test runs for a certain date.

        The date will refer to when the test report was created.
        """
        query = f"""EasyEvalBasicReport | where ReportCreationTime contains "{date.isoformat()}" |
                where SutCommitId !contains "placeholder" | distinct SutCommitId"""
        result_df = await self._run_kusto_query(query)
        if len(result_df.index) == 0:
            raise Exception(f"No commits found at: {date}")

        return result_df["SutCommitId"].to_list()

    async def fetch_data(self, version: str) -> DataFrame:
        """Get test reports for a specific version of the PACE repo from test collector via ADX.

        The version can be a git commit SHA, branch or tag
        """
        logger.info(f"Extracting test reports for version : {version}")

        # Get list of run ID's
        run_id_query = f'EasyEvalBasicReport | where SutCommitId == "{version}" | distinct RunId'
        run_ids: list[str] = (await self._run_kusto_query(run_id_query))["RunId"].to_list()

        # Collect data per version and per run ID in memory
        result_df = pd.DataFrame()
        for run_id in run_ids:
            # remove any trailing and leading whitespaces from run ids
            run_id = run_id.strip()
            test_report_query = f'EasyEvalBasicReport | where SutCommitId == "{version}" and RunId == "{run_id}"'
            run_df = await self._run_kusto_query(test_report_query)
            result_df = pd.concat([result_df, run_df])

        # Create pyspark dataframe
        return self.spark.createDataFrame(data=result_df, schema=TEST_REPORT_BRONZE_SCHEMA)


async def run_test_report_extract(
    extractor: ADXTestReportBronzeExtractor,
    version: str | None = None,
    date: datetime.date | None = None,
    nightly: bool = False,
) -> str | None:
    """Runs the test report extraction process and ingests data into a Databricks delta table.

    This function determines the date and version for the test report extraction based on the provided
    arguments. It then fetches and ingests the data corresponding to the determined date and version
    into a Databricks delta table.

    Args:
        extractor (ADXSCABronzeExtractor): An instance of ADXSCABronzeExtractor that handles the
            data extraction logistics.
        version (str, optional): The specific commit version or tag to extract data for.
        date (datetime.date, optional): The specific date to extract data for.
        nightly (bool, optional): Flag to extract data for last nightly run.

    Returns:
        str: The version string of the data extracted, indicating the specific dataset version
             handled during the extraction, or None if multiple versions where extracted.
    """
    # Determine version for which to extract the test reports
    versions: list[str]
    if version is not None:
        versions = [version]
    else:
        if nightly:
            if date is None:
                date = datetime.date.today() - datetime.timedelta(days=1)

            versions = [await extractor.get_nightly_version_of_date(date)]
        elif date is not None:
            versions = await extractor.get_versions_of_date(date)
        else:
            versions = [await extractor.get_latest_version()]

    for version in versions:
        version_df = await extractor.fetch_data(version)
        extractor.ingest_data(version_df)

    # Close ADX client
    # await extractor.adx_client.close()

    return versions[0] if len(versions) == 1 else None


if __name__ == "__main__":  # pragma: no cover
    parser = argparse.ArgumentParser(prog="Report Extractor")
    parser.add_argument("-v", "--version", help="Commit version/tag for extracting report")
    parser.add_argument(
        "-e",
        "--env",
        default="dev",
        help="Environment, ex. dev, qa, prod",
    )
    parser.add_argument("-i", "--run_id", dest="run_id")

    args, unknown = parser.parse_known_args()

    catalog_name = get_dbx_env_catalog("bronze")
    # Setup logging
    setup_databricks_logging(
        FullSchemaName(catalog_name, DBX_SCHEMA, False),
        "adx_test_report/bronze",
        run_id=args.run_id,
        enabled_loggers=["base"],
    )

    # Create ADX client
    client_id = get_rdd_secret("sp-adx-client-id")
    client_secret = get_rdd_secret("sp-adx-client-secret")
    adx_client = get_adx_client(client_id, client_secret)

    # Create extractor
    extractor = ADXTestReportBronzeExtractor(adx_client, catalog_name, False)

    # Enable nested asyncio event loops
    nest_asyncio.apply()
    # Run test report extraction
    for version in versions:
        try:
            asyncio.run(run_test_report_extract(extractor, version, None, True))
        except Exception as e:
            logger.error(f"Error extracting {version}: {str(e)}")
    extractor.adx_client.close()
