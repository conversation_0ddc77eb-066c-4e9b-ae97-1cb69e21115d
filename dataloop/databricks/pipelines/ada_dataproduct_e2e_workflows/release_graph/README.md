# RELEASE GRAPH

`release_graph` directory contains all necessary scripts, libraries, and workflow bundles that perform ETL (Extract, Transform, Load) processes on data sources.
The primary objective of this directory is to facilitate the loading of transformed data into a graph database, which can then be used for various release activities such as
generating release notes, creating dashboards etc.

## ETL Process Overview

The ETL process is divided into three stages:

1. **Extract:** Raw data is extracted from various data sources and stored in bronze tables in databricks.
2. **Transform:** The extracted data is transformed into silver and gold (optional) standards and persisted into respective tables in databricks.
3. **Load:** The transformed data is loaded into Stardog graph databses using workflows.

### Directory Structure

This directory is organized as follows:

* **scripts**: Bash scripts for determining changes between PRs and git tags.

* **src**: Source code directory for all data sources containing extract, transform, load and workflow yaml files.
More information can be found here: [src documentation](src/README.md)
