# This configuration file is used in OSS scans by OCaaS (Open Source Compliance as a Service).
#
# Please refer to https://github.com/oss-review-toolkit/ort/blob/main/docs/config-file-ort-yml.md
# for further information
analyzer:
  skip_excluded: true
excludes:
  paths:
    - pattern: "dataloop-*/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "examples"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "human-in-the-loop/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "mdm-viper-lib/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "databricks/notebooks/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "databricks/pipelines/ada_dataproduct_bronze/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "databricks/pipelines/ada_dataproduct_gold/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "databricks/pipelines/ada_dataproduct_silver/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "datasets-client/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Not part of release."
    - pattern: "doc/**"
      reason: "OPTIONAL_COMPONENT_OF"
      comment: "Documentation not part of release"
