# The main job for delta_sharing_views.
resources:
  jobs:
    delta_sharing_views_job:
      name: "${var.job_name_prefix} Create DeltaSharing views"
      max_concurrent_runs: 100

      # disable queue not needed
      queue:
        enabled: false

      email_notifications:
        on_failure:
          - <EMAIL>

      tasks:
        - task_key: create_delta_sharing_views
          job_cluster_key: create_delta_sharing_views_cluster
          libraries:
            - pypi:
                package: pyyaml~=6.0.2
          spark_python_task:
            python_file: ../src/create_views.py

      job_clusters:
        - job_cluster_key: create_delta_sharing_views_cluster
          new_cluster:
            spark_version: 15.4.x-scala2.12
            instance_pool_id: ${var.computer_node}
            driver_instance_pool_id: ${var.computer_node}
            autoscale:
              min_workers: 1
              max_workers: 2
