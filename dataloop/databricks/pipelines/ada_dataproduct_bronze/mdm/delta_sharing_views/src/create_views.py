"""Create Views for Delta Sharing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from typing import Any, Dict, Final, List, Optional, Tuple

import yaml
from pyspark.sql import SparkSession
from yaml import YAMLError

JOB_NAME: Final[str] = "Create Views for delta sharing"

# Configuring logger
logging.basicConfig(
    level=logging.INFO,
    format="[%(levelname)s][%(asctime)s][%(name)s][%(module)s:%(lineno)d][%(funcName)s] %(message)s  ",
)


class CreateViewsForDeltaSharing:
    """Class for creating views for delta sharing from configuration file."""

    def __init__(self, spark_session: SparkSession) -> None:
        """Initialize the class.

        Args:
            spark_session (SparkSession): Spark session.
        """
        self._spark: Final[SparkSession] = spark_session
        self._config_data: Dict[str, Any] = {}

    def parse_configuration(self, config_path: str) -> None:
        """Parse delta share configuration file.

        Args:
            config_path (str): Path to the configuration file.

        Raises:
            YAMLError: If there is an error parsing the configuration file.
        """
        with open(config_path, "r", encoding="utf-8") as stream:
            try:
                self._config_data = yaml.safe_load(stream)
            except YAMLError as exc:
                raise YAMLError(f"Error parsing configuration file: {exc}") from exc

    def generate_sql_view_command(
        self,
        source_full_path: str,
        target_schema_public: Optional[str],
        target_schema_csi: Optional[str],
        public_columns: List[str],
        csi_columns: List[str],
    ) -> Tuple[str, str]:
        """Generate SQL command to create views.

        Args:
            source_full_path (str): Full path of the source table.
            target_schema_public (Optional[str]): Target schema for public data.
            target_schema_csi (Optional[str]): Target schema for CSI data.
            public_columns (List[str]): List of public columns.
            csi_columns (List[str]): List of CSI columns.

        Returns:
            Tuple[str, str]: SQL commands for public and CSI views.
        """
        logging.info("Generating SQL command for creating views.")
        logging.info("source_full_path: %s", source_full_path)
        logging.info("target_schema_public: %s", target_schema_public)
        logging.info("target_schema_csi: %s", target_schema_csi)
        logging.info("Number of public columns to be shared: %s", len(public_columns))
        logging.info("Number of csi columns to be shared: %s", len(csi_columns))

        table_name = source_full_path.split(".")[-1]
        public_sql_query = ""
        csi_sql_query = ""

        if public_columns and target_schema_public:
            public_select_clause = ",\n    ".join(public_columns)
            public_sql_query = f"""
                CREATE OR REPLACE VIEW {target_schema_public}.`{table_name}` AS
                SELECT
                    {public_select_clause}
                FROM {source_full_path};
            """
        all_columns = list(set(csi_columns + public_columns))  # Combine public and csi columns
        if csi_columns and target_schema_csi:
            csi_select_clause = ",\n    ".join(all_columns)
            csi_sql_query = f"""
                CREATE OR REPLACE VIEW {target_schema_csi}.`{table_name}` AS
                SELECT
                    {csi_select_clause}
                FROM {source_full_path};
            """
        return public_sql_query.strip(), csi_sql_query.strip()

    def create_views(self) -> None:
        """Create views for delta sharing."""
        if self._config_data:
            shared_entities: List[Dict[str, Any]] = self._config_data.get("shared_entities", [])

            if len(shared_entities) == 0:
                logging.error("No shared entities found in configuration")

            for entity in shared_entities:
                source_full_path = entity.get("source_full_path", None)
                if not isinstance(source_full_path, str):
                    logging.error("source_full_path not found in configuration of an entity")
                    continue

                shared_columns = entity.get("shared_columns", {})
                if not isinstance(shared_columns, Dict) or len(shared_columns) == 0:
                    logging.error("No shared columns found in configuration of an entity")
                    continue

                public_columns = shared_columns.get("public", [])
                csi_columns = shared_columns.get("csi", [])

                if not isinstance(public_columns, list) or len(public_columns) == 0:
                    logging.info("No public columns found in configuration of an entity")

                if not isinstance(csi_columns, list) or len(csi_columns) == 0:
                    logging.info("No csi columns found in configuration of an entity")

                target_schema_public = entity.get("target_schema_public", None)
                target_schema_csi = entity.get("target_schema_csi", None)
                public_sql_command, csi_sql_command = self.generate_sql_view_command(
                    source_full_path=source_full_path,
                    target_schema_public=target_schema_public,
                    target_schema_csi=target_schema_csi,
                    public_columns=public_columns,
                    csi_columns=csi_columns,
                )

                # Execute in Databricks
                if public_sql_command:
                    logging.info(
                        "Executing SQL command for public view:\n %s",
                        public_sql_command,
                    )
                    self._spark.sql(public_sql_command)
                else:
                    logging.warning("No public view SQL command created")
                if csi_sql_command:
                    logging.info("Executing SQL command for csi view:\n %s", csi_sql_command)
                    self._spark.sql(csi_sql_command)
                else:
                    logging.warning("No csi view SQL command created")

        else:
            logging.error("Configuration data not found or empty")


def main() -> None:
    """Setup and start the transformation job."""
    # Spark session is provided by Databricks
    spark = SparkSession.builder.getOrCreate()

    # Argument parsing
    parser = argparse.ArgumentParser()

    parser.add_argument(
        "--config-file-path",
        dest="config_file_path",
        required=True,
        help="The path to the delta share configuration file",
    )

    args, _ = parser.parse_known_args()

    logging.info(
        "Creating views for delta sharing from configuration file path: %s",
        args.config_file_path,
    )

    job = CreateViewsForDeltaSharing(spark_session=spark)

    logging.info("Processing configuration file: %s", args.config_file_path)
    # Parse configuration file
    job.parse_configuration(args.config_file_path)
    # Create views
    job.create_views()


if __name__ == "__main__":  # pragma: no cover
    main()
