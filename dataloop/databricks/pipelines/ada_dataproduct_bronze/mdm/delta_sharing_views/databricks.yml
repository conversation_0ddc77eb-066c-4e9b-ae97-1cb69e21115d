# This is a Databricks asset bundle definition for delta_sharing_views.
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.
bundle:
  name: delta_sharing_views

include:
  - resources/*.yml

variables:
  client_id:
    description: Client ID to run the workflow
    default: aafcde69-3b6f-436a-9bc8-480f73afee19 # sp-pace-dataloop-mdm-dbx-dev
  job_name_prefix:
    description: Job name prefix to add environment as well as the slot name
    default: "[MDM][DEV]"
  computer_node:
    description: Computer node id - 15.4.x-scala2.12
    default: 1001-074540-scoff137-pool-nn1m66l2

targets:
  # The 'local' target, for development purposes.
  local:
    variables:
      job_name_prefix: "[MDM][LOCAL]"
    mode: development
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net # datalake-dev

  dev:
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net # datalake-dev
      root_path: /Users/<USER>/.bundle/${bundle.name}/${bundle.target}
    run_as:
      service_principal_name: ${var.client_id}
    permissions:
      - group_name: sg-pace-github-MDM-FileCatalog-CodeReviewers-developer
        level: CAN_MANAGE

  qa:
    variables:
      job_name_prefix: "[MDM][QA]"
      client_id: 84c6d159-6f4f-4d2e-862e-6ff7df5e754a # sp-pace-dataloop-mdm-dbx-qa
      computer_node: 1001-074300-sits136-pool-1jrhcwj6
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net # datalake-qa
      root_path: /Users/<USER>/.bundle/${bundle.name}/${bundle.target}
    run_as:
      service_principal_name: ${var.client_id}
    permissions:
      - group_name: sg-pace-github-MDM-FileCatalog-CodeReviewers-developer
        level: CAN_MANAGE

  prod:
    mode: production
    variables:
      job_name_prefix: "[MDM][PROD]"
      client_id: c3a60ab7-4050-41a9-b636-cb0dfdd23f1b # sp-pace-dataloop-mdm-dbx-prod
      computer_node: 1001-075543-shied265-pool-in9cb7w2
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net # datalake-prod
      root_path: /Users/<USER>/.bundle/${bundle.name}/${bundle.target}
    run_as:
      service_principal_name: ${var.client_id}
    permissions:
      - group_name: sg-pace-github-MDM-FileCatalog-CodeReviewers-developer
        level: CAN_MANAGE
