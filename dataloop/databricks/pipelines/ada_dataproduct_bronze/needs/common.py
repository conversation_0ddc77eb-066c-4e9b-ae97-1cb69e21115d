"""Common functionality for needs and shacl bronze extraction."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.streaming import StreamingQuery

# Constants
STORAGE_ACCOUNT = "dbxingestzoneprod"
DBX_SCHEMA = "needs"
CONTAINER_MAP = {
    "rdd": "rdd",
    "pace": "pace",
    "dataloop": "dataloop",
}


def configure_az_storage(spark: SparkSession) -> None:
    """Sets the Spark session configurations for connecting to Azure Storage.

    This function retrieves client secrets from Azure Key Vault via Databricks,
    and configures the Spark session to authenticate with Azure Storage using
    OAuth credentials, which include client ID and client secret.

    Returns:
        None
    """
    from pyspark.dbutils import DBUtils

    dbutils = DBUtils(spark)

    # Retrieve secrets
    client_secret = dbutils.secrets.get(scope="rdd", key="job-spn-client-secret")
    client_id = dbutils.secrets.get(scope="rdd", key="job-spn-client-id")

    # Set up Spark configurations for Azure Storage Account
    storage_account = f"{STORAGE_ACCOUNT}.dfs.core.windows.net"
    token_endpoint = "https://login.microsoftonline.com/a6c60f0f-76aa-4f80-8dba-092771d439f0/oauth2/token"
    spark.conf.set(f"fs.azure.account.auth.type.{storage_account}", "OAuth")
    spark.conf.set(
        f"fs.azure.account.oauth.provider.type.{storage_account}",
        "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider",
    )
    spark.conf.set(f"fs.azure.account.oauth2.client.id.{storage_account}", client_id)
    spark.conf.set(f"fs.azure.account.oauth2.client.secret.{storage_account}", client_secret)
    spark.conf.set(f"fs.azure.account.oauth2.client.endpoint.{storage_account}", token_endpoint)


def get_checkpoint_path(catalog: str, table: str) -> str:
    """Construct the checkpoint path for the given catalog, schema, and table.

    Checkpoints are used to store progress information during streaming, ensuring that the process
    can resume from the last successful point without reprocessing data.

    Args:
        catalog (str): The catalog name.
        schema (str): The schema name.
        table (str): The table name.

    Returns:
        str: The constructed checkpoint path.
    """
    return f"/Volumes/{catalog}/{DBX_SCHEMA}/checkpoint_locations/{table}/"


def write_stream_table(df: DataFrame, catalog: str, table, checkpoint_path: str) -> StreamingQuery:
    """Writes the streaming DataFrame to a Delta table using checkpoints.

    This function sets up a streaming write operation that appends new data from the DataFrame
    to the specified Delta table based on available data.

    Args:
        df (DataFrame): The DataFrame containing the data to be written.
        full_table_name (str): The name of the target Delta table (including catalog and schema).
        checkpoint_path (str): The path to the checkpoint directory.
        log (Logger): Logger instance for logging information.

    Returns:
        None
    """
    table_full = f"{catalog}.{DBX_SCHEMA}.{table}"
    query = (
        df.writeStream.trigger(availableNow=True)
        .option("checkpointLocation", checkpoint_path)
        .toTable(table_full, format="delta", outputMode="append")
    )

    query.awaitTermination()
    return query
