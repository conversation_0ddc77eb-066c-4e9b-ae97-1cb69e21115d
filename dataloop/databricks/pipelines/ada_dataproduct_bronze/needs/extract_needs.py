"""Stream raw needs json files from dbx landing zone storage account to bronze layer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from typing import Optional

from common import (
    CONTAINER_MAP,
    DBX_SCHEMA,
    STORAGE_ACCOUNT,
    configure_az_storage,
    get_checkpoint_path,
    write_stream_table,
)
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col,
    current_timestamp,
    explode,
    from_unixtime,
    regexp_replace,
    split,
)
from pyspark.sql.types import IntegerType, MapType, StringType, StructField, StructType
from rddlib import (
    FullSchemaName,
)
from rddlib import delta_table_utils as dtu
from rddlib import (
    get_dbx_env_catalog,
    setup_databricks_logging,
)

logger = logging.getLogger(__name__)

CONTAINER_DIRECTORY = "needs"
TABLE_MAP = {
    "rdd": "rdd_needs",
    "pace": "pace_needs",
    "dataloop": "dataloop_needs",
}

TABLE_DESCRIPTIONS_MAP = {
    "rdd_needs": (
        "The table stores needs data from the rdd repo in a raw format. "
        "Raw needs data is streamed from the databricks landing zone storage account. "
        "The content of each needs element is stored in a separate row. "
        "Some additional metadata like ingested date, created date, "
        "project etc are added for each needs element."
    ),
    "pace_needs": (
        "The table stores needs data from the pace repo in a raw format. "
        "Raw needs data is streamed from the databricks landing zone storage account. "
        "The content of each needs element is stored in a separate row. "
        "Some additional metadata like ingested date, created date, "
        "project etc are added for each needs element."
    ),
    "dataloop_needs": (
        "The table stores needs data from the dataloop repo in a raw format. "
        "Raw needs data is streamed from the databricks landing zone storage account. "
        "The content of each needs element is stored in a separate row. "
        "Some additional metadata like ingested date, created date, "
        "project etc are added for each needs element."
    ),
}

# Define the schema for raw needs.json
NEEDS_SCHEMA = StructType(
    [
        StructField("current_version", StringType(), True),
        StructField("project", StringType(), True),
        StructField(
            "versions",
            MapType(
                StringType(),
                StructType(
                    [
                        StructField("filters", MapType(StringType(), StringType()), True),
                        StructField("filters_amount", IntegerType(), True),
                        StructField("needs_amount", IntegerType(), True),
                        StructField("needs", MapType(StringType(), StringType()), True),
                    ]
                ),
            ),
            True,
        ),
    ]
)


def _read_stream_needs_folder(
    spark: SparkSession, container: str, checkpoint_path: str, schema: StructType
) -> DataFrame:
    """Loads raw needs files from the Azure Storage Account (dbx landing zone) and returns a streaming DataFrame.

    This function reads JSON files from the specified container and directory in the landing zone,
    applying the predefined schema, and prepares the data for further processing.

    Args:
        container (str): The name of the Azure Storage container (e.g., 'rdd' or 'pace').
        checkpoint_path (str): The path to the checkpoint.
        schema (StructType): The schema of the JSON data.

    Returns:
        DataFrame: A streaming DataFrame containing the raw 'needs' data.
    """
    return (
        spark.readStream.format("cloudFiles")
        .option("cloudFiles.format", "json")
        .option("cloudFiles.schemaLocation", checkpoint_path)
        .schema(schema)
        .load(f"abfss://{container}@{STORAGE_ACCOUNT}.dfs.core.windows.net/{CONTAINER_DIRECTORY}/")
        .withColumn("filename", col("_metadata.file_name"))
    )


def _process_needs(df: DataFrame) -> DataFrame:
    """Processes the raw needs DataFrame.

    The function explodes the 'versions' and 'needs' columns from the original JSON structure.
    It also extracts metadata such as 'filename' and timestamps
    from the file name, and adds an 'ingested_date' for tracking.

    Args:
        df (DataFrame): The raw needs DataFrame to be processed.

    Returns:
        DataFrame: The DataFrame to be written to the bronze table.
    """
    df_flat_needs = (
        df.select(
            col("filename"),
            col("current_version"),  # version in needs json, ex - 0.1
            col("project"),  # project in needs json, ex - Automated Driving Alliance main documentation
            explode(col("versions")).alias("version_name", "version_data"),  # Explode versions map with proper aliases
        )
        .select(
            col("version_data.needs").alias("needs"),
            col("version_data.filters").alias("filters"),
            col("version_data.filters_amount").alias("filters_amount"),
            col("version_data.needs_amount").alias("needs_amount"),
            "filename",
            "current_version",
            "project",
        )
        .select(
            explode(col("needs")).alias("need_key", "need_value"),  # Explode the needs map with proper aliases
            "filename",
            "current_version",
            "project",
            "filters",
            "filters_amount",
            "needs_amount",
        )
        .selectExpr(
            "need_key as id",  # The id of the needs element
            "need_value as content",  # The content of the need
            "filename",
            "current_version",
            "project",
            "filters",
            "filters_amount",
            "needs_amount",
        )
    )

    # Add ingested_date and rename timestamp to created_date
    df_final = (
        df_flat_needs.withColumn("split_col", split(col("filename"), "_"))
        .withColumn(
            "created_date",
            from_unixtime(regexp_replace(col("split_col").getItem(4), ".json.gz", "")).cast("timestamp"),
        )
        .withColumn("ingested_date", current_timestamp())
        .withColumn("version", col("split_col").getItem(3))
        .withColumn("type", col("split_col").getItem(0))
        .select(
            "type",  # main or pr
            "id",  # id of the needs element
            "content",  # content of the needs element
            "version",  # sha
            "current_version",
            "project",
            "filters",
            "filters_amount",
            "needs_amount",
            "created_date",  # renamed from timestamp
            "ingested_date",  # new column with current time
        )
    )

    return df_final


def main(run_id: Optional[str], app: str) -> None:
    """Executes the extraction, processing, and writing of 'needs' data to the bronze table.

    Args:
        run_id (Optional[str]): The run identifier for the current job, useful for logging and tracking.
        app (str): The application identifier (e.g., 'rdd', 'pace', 'dataloop') to determine which data to process.

    Returns:
        None
    """
    dbx_catalog = get_dbx_env_catalog("bronze")
    table = TABLE_MAP[app]

    # Setup logging
    setup_databricks_logging(FullSchemaName(dbx_catalog, DBX_SCHEMA, False), "needs/bronze", run_id=run_id)

    # Initialize Spark configurations
    spark = SparkSession.builder.getOrCreate()
    configure_az_storage(spark)

    # Read stream from the 'needs' folder in the blob storage container
    checkpoint_path = get_checkpoint_path(dbx_catalog, table)
    container = CONTAINER_MAP[app]
    logger.info(f"Read stream from blob storage container '{container}.{CONTAINER_DIRECTORY}'")
    df = _read_stream_needs_folder(spark, container, checkpoint_path, NEEDS_SCHEMA)

    # Write the processed needs data to the bronze table
    df_final = _process_needs(df)
    query = write_stream_table(df_final, dbx_catalog, table, checkpoint_path)
    if query.lastProgress and "numInputRows" in query.lastProgress:
        num_new_rows = query.lastProgress["numInputRows"]  # type: ignore[index]
        logger.info(f"{num_new_rows} new {app} needs versions added to the Bronze layer")
    else:
        logger.info(f"No new {app} needs versions added to the Bronze layer")
    # Add metadata to the table
    full_table_name = f"{dbx_catalog}.{DBX_SCHEMA}.{table}"
    description = TABLE_DESCRIPTIONS_MAP.get(table, "")
    dtu.update_table_metadata(full_table_name, description)


if __name__ == "__main__":
    # Parse command-line arguments for app and run_id
    parser = argparse.ArgumentParser(description="Needs bronze extractor")
    parser.add_argument("--app", choices=TABLE_MAP.keys(), required=True)
    parser.add_argument("--run_id", type=str, default=None)
    args, unknown = parser.parse_known_args()

    # Execute the main logic
    main(args.run_id, args.app)
