"""Stream raw report.ttl files from dbx landing zone storage account to bronze layer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from typing import Optional

from common import (
    CONTAINER_MAP,
    DBX_SCHEMA,
    STORAGE_ACCOUNT,
    configure_az_storage,
    get_checkpoint_path,
    write_stream_table,
)
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col, from_unixtime, regexp_replace, split
from rddlib import (
    FullSchemaName,
)
from rddlib import delta_table_utils as dtu
from rddlib import (
    get_dbx_env_catalog,
    setup_databricks_logging,
)

logger = logging.getLogger(__name__)

CONTAINER_DIRECTORY = "shaclreports"
TABLE_MAP = {
    "rdd": "rdd_shacl",
    "pace": "pace_shacl",
}

TABLE_DESCRIPTIONS_MAP = {
    "rdd_shacl": (
        "This table stores raw SHACL validation reports for the RDD process, "
        "extracted from the rdd blob storage container. "
        "Columns include ttl_content (SHACL report content), created_date "
        "(timestamp from the filename), and version (SHA or commit ID). "
        "Enables compliance validation and debugging of RDD data."
    ),
    "pace_shacl": (
        "This table stores SHACL validation reports for the PACE domain, "
        "sourced from the pace blob storage container. "
        "Columns include ttl_content, created_date, and version. "
        "Supports validation and debugging of PACE data."
    ),
}


def _read_stream_shacl_folder(spark: SparkSession, container: str, checkpoint_path: str) -> DataFrame:
    """Loads raw shacl ttl files from the Azure Storage Account (dbx landing zone) and returns a streaming DataFrame.

    This function reads TTL files from the specified container and directory in the landing zone,
    applying the predefined schema, and prepares the data for further processing.

    Args:
        container (str): The name of the Azure Storage container (e.g., 'rdd' or 'pace').
        checkpoint_path (str): The path to the checkpoint.
        log (Logger): Logger instance for logging information.

    Returns:
        DataFrame: A streaming DataFrame containing the raw 'needs' data.
    """
    return (
        spark.readStream.format("cloudFiles")
        .option("cloudFiles.format", "text")  # Read report ttl as text
        .option("wholetext", True)
        .option("cloudFiles.schemaLocation", checkpoint_path)
        .load(f"abfss://{container}@{STORAGE_ACCOUNT}.dfs.core.windows.net/{CONTAINER_DIRECTORY}/")
        .withColumn("filename", col("_metadata.file_name"))
    )


def _process_shacl(df: DataFrame) -> DataFrame:
    """Processes the raw SHACL DataFrame."""
    df_final = (
        df.withColumn("split_col", split(col("filename"), "_"))
        .withColumn(
            "created_date",
            from_unixtime(regexp_replace(col("split_col").getItem(3), ".ttl.gz", "")).cast("timestamp"),
        )
        .withColumnRenamed("value", "ttl_content")
        .withColumn("version", col("split_col").getItem(2))
        .select(
            "ttl_content",  # content of the shacl report
            "created_date",
            "version",  # sha
        )
    )

    return df_final


def main(run_id: Optional[str], app: str) -> None:
    """Executes the extraction, processing, and writing of 'shacl reports' to the bronze table.

    Args:
        run_id (Optional[str]): The run identifier for the current job, useful for logging and tracking.
        app (str): The application identifier (e.g., 'rdd', 'pace') to determine which data to process.

    Returns:
        None
    """
    dbx_catalog = get_dbx_env_catalog("bronze")
    table = TABLE_MAP[app]

    # Setup logging
    setup_databricks_logging(FullSchemaName(dbx_catalog, DBX_SCHEMA, False), "shacl/bronze", run_id=run_id)

    # Initialize Spark configurations
    spark = SparkSession.builder.getOrCreate()
    configure_az_storage(spark)

    # Read stream from the 'shacl' folder in the blob storage container
    checkpoint_path = get_checkpoint_path(dbx_catalog, table)
    container = CONTAINER_MAP[app]
    logger.info(f"Read stream from blob storage container '{container}.{CONTAINER_DIRECTORY}'")
    df = _read_stream_shacl_folder(spark, container, checkpoint_path)

    # Write the processed SHACL data to the bronze table
    df_final = _process_shacl(df)
    query = write_stream_table(df_final, dbx_catalog, table, checkpoint_path)
    if query.lastProgress and "numInputRows" in query.lastProgress:
        num_new_rows = query.lastProgress["numInputRows"]  # type: ignore[index]
        logger.info(f"{num_new_rows} new {app} SHACL versions added to the Bronze layer")
    else:
        logger.info(f"No new {app} SHACL versions added to the Bronze layer")

    # Add metadata to the table
    full_table_name = f"{dbx_catalog}.{DBX_SCHEMA}.{table}"
    description = TABLE_DESCRIPTIONS_MAP.get(table, "")
    dtu.update_table_metadata(full_table_name, description)


if __name__ == "__main__":
    # Parse command-line arguments for app and run_id
    parser = argparse.ArgumentParser(description="SHACL bronze extractor")
    parser.add_argument("--app", choices=TABLE_MAP.keys(), required=True)
    parser.add_argument("--run_id", type=str, default=None)
    args, unknown = parser.parse_known_args()

    # Execute the main logic
    main(args.run_id, args.app)
