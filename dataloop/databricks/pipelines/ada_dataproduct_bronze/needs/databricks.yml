# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024-2025 Robert <PERSON> and Cariad SE. All rights reserved.
# ============================================================================================================

bundle:
  name: needs_bronze

include:
  - resources/*.yml

variables:
  run_sp:
    description: "run service principal"
  instance_pool_id:
    description: "Instance pool id"
    lookup:
      instance_pool: "default_E16ads_v5_rt14.3"
  driver_instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E8ads_v5_rt14.3"
  spark_version:
    description: "spark version to install on the job cluster"
    default: "15.4.x-scala2.12"
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"
  rddlib_version:
    description: "rddlib version"
    default: "2.1.0"
  teams_channel:
    description: "team channel id for notifications"
  nightly_trigger:
    default: "PAUSED"
    description: "Status of nightly trigger . PAUSED/UNPAUSED"

targets:
  user-dev:
    mode: development
    variables:
      run_sp: ${workspace.current_user.userName}
      teams_channel: e83a8487-5008-4c45-b013-774c41401a02
      nightly_trigger: PAUSED
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}

  dev:
    variables:
      run_sp: a0125883-86f0-4967-af40-e69f011ec0db
      teams_channel: e83a8487-5008-4c45-b013-774c41401a02
      nightly_trigger: PAUSED
    mode: production
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: a0125883-86f0-4967-af40-e69f011ec0db

  qa:
    variables:
      run_sp: 8275abc3-ff23-48eb-97d3-8a3673ab14dd
      teams_channel: e8a49137-ce9c-48a9-b46c-8dbc97725a4c
      nightly_trigger: PAUSED
    mode: production
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: 8275abc3-ff23-48eb-97d3-8a3673ab14dd

  prod:
    variables:
      run_sp: 73a62a2d-609e-43c6-bdaf-d103d7a4eb43
      teams_channel: 949087fc-6505-43a1-9b3e-9b9910d1e167
      nightly_trigger: UNPAUSED
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: 73a62a2d-609e-43c6-bdaf-d103d7a4eb43
