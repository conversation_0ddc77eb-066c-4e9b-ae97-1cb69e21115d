# This is a template for creating your own bronze layer extraction
#
# Use it according to the following steps:
#   1. Copy the template to this directory and change all xxx to your
#   team name. and change team_name and domain_name as well
#   2. If desired, change also job cluster and where notifications will be posted when job run fails

resources:
  jobs:
    needs_xxx_bronze:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: Needs - XXX - Bronze - Nightly
      tags:
        responsible_team: "team_name"
        responsible_domain: "domain_name"
        refresh_interval: "P1D"
        medallion: "Bronze"
        schedule: "Nightly"
        "Needs": ""
        "xxx": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      # Nightly run at 0:00 AM  UTC
      schedule:
        quartz_cron_expression: 0 0 0 * * ?
        timezone_id: UTC
        pause_status: ${var.nightly_trigger}

      tasks:
        - task_key: extract_needs_data
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract_needs.py
            parameters:
              - --app
              - xxx
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: needs_job_cluster
          libraries:
            - pypi:
                package: rddlib==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: needs_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            azure_attributes:
              first_on_demand: 1
              availability: SPOT_WITH_FALLBACK_AZURE
              spot_bid_max_price: 100
            autoscale:
              min_workers: 1
              max_workers: 4
            init_scripts:
              - volumes:
                  destination: "/Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-dev.sh"
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            spark_env_vars:
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
