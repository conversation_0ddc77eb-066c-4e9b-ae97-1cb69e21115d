resources:
  jobs:
    needs_pace_bronze:
      # Give permission to all users to manage run
      permissions:
        - group_name: users
          level: CAN_MANAGE_RUN

      name: Needs - PACE - Bronze - Nightly
      tags:
        responsible_team: "RDD"
        responsible_domain: "Data Delivery"
        refresh_interval: "P1D"
        medallion: "Bronze"
        schedule: "Nightly"
        "Needs": ""
        "PACE": ""

      # Notifications
      webhook_notifications:
        on_failure:
          - id: ${var.teams_channel}
        on_duration_warning_threshold_exceeded:
          - id: ${var.teams_channel}
      notification_settings:
        no_alert_for_skipped_runs: true
        no_alert_for_canceled_runs: true
      timeout_seconds: 7200
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 3600

      # Runs every 2 hours at the start of the hour (00:00, 02:00, 04:00, ..., 22:00).
      schedule:
        quartz_cron_expression: 0 0 0/2 * * ? *
        timezone_id: UTC
        pause_status: ${var.nightly_trigger}

      tasks:
        - task_key: extract_needs_data
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/extract_needs.py
            parameters:
              - --app
              - pace
              - --run_id
              - "{{job.run_id}}"
          job_cluster_key: needs_job_cluster
          libraries:
            - pypi:
                package: rddlib==${var.rddlib_version}

      job_clusters:
        - job_cluster_key: needs_job_cluster
          new_cluster:
            spark_version: ${var.spark_version}
            autoscale:
              min_workers: 1
              max_workers: 4
            policy_id: ${var.job_cluster_policy_id}
            instance_pool_id: ${var.instance_pool_id}
            driver_instance_pool_id: ${var.driver_instance_pool_id}
