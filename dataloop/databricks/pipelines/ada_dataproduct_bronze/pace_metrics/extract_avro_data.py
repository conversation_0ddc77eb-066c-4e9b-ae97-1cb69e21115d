"""Read raw avro files from pace_metrics storage account."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import col
from rddlib import (
    FullSchemaName,
)
from rddlib import delta_table_utils as dtu
from rddlib import (
    get_dbx_env_catalog,
    get_rdd_secret,
    setup_databricks_logging,
)

logger = logging.getLogger(__name__)

DBX_SCHEMA = "pace_metrics"
TABLE_SUFFIX = ""

TABLE_AVRO_DIR_MAP = {
    # KPI data
    "kpi_location_of_the_drive": "kpilocationofthedrive",
    "kpi_piloted_distance_time": "kpipiloteddistancetime",
    "kpi_safety_driver_takeovers": "kpisafetydrivertakeovers",
    "kpi_aggregated_results": "kpiaggregatedresults",
    "kpi_gps_tracks": "kpigpstracks",
    "kpi_content_tags": "kpicontenttags",
    "cid_kpis": "cidkpis",
    "cid_intervention_in_intersection": "cidinterventioninintersection",
    # bazel build
    "bazel_package_metrics": "bazelpackagemetrics",
    "bazel_target_metrics": "bazeltargetmetrics",
    # tests-reports
    "easy_eval_basic_report": "easyevalbasicreport",
    "basic_evaluation_report": "basicevaluationreport",
    "basic_system_test_report": "basicsystemtestreport",
    # Github
    "github_pull_request": "githubpullrequest",
    "github_release": "githubrelease",
    "github_workflow_run": "githubworkflowrun",
    "github_workflow_job": "githubworkflowjob",
    "github_merge_group": "githubmergegroup",
    # ADO
    "ticket": "ticket",
    # MapLoc
    "eval_job": "evaljob",
    "batch_job": "batchjob",
    "eval_kpi": "evalkpi",
    # Misc
    "snippets_4_test_snippets_metadata": "snippets4testsnippetsmetadata",
    "requirement_rules": "requirementrules",
}


def get_checkpoint_path(catalog: str, schema: str, table: str) -> str:
    """Construct path for checkpoints."""
    return f"/Volumes/{catalog}/{schema}/checkpoint_locations/{table}/"


def _read_stream_avro_folder(spark: SparkSession, avro_dirname: str, checkpoint_path: str) -> DataFrame:
    """Loads raw avro files from pace_metrics storage account.

    Casts the body of the mesages in the avro file to string, otherwise the body
    would be binary.

    Returns:
        DataFrame: The transformed DataFrame ready for further processing.
    """
    return (
        spark.readStream.format("cloudFiles")
        .option("cloudFiles.format", "avro")
        # The schema location directory keeps track of your data schema over time
        .option("cloudFiles.schemaLocation", checkpoint_path)
        .option("pathGlobfilter", "*.avro")
        .load(f"wasbs://<EMAIL>/evhns-metricdatapipe/{avro_dirname}/")
        .withColumn("Body", col("Body").cast("string"))
    )


def _write_streaming_table(
    df: DataFrame,
    full_table_name: str,
    checkpoint_path: str,
    table_name: str,
) -> None:
    query = (
        df.writeStream.trigger(availableNow=True)
        .option(
            "checkpointLocation",
            checkpoint_path,
        )
        .toTable(f"{full_table_name}{TABLE_SUFFIX}", format="delta", outputMode="append")
    )

    query.awaitTermination()
    num_new_rows = query.lastProgress["numInputRows"]  # type: ignore[index]
    logger.info(f'{num_new_rows} new rows added to table "{full_table_name}".')

    DESCRIPTION = (
        f"This table stores raw streaming data ingested from {table_name} in the bronze layer. "
        "The data originates from Avro files located in the pace_metrics storage account and is ingested "
        "into the Delta table for further processing and analytics in the Databricks environment."
    )
    dtu.update_table_metadata(full_table_name, DESCRIPTION)


def main(run_id: str | None, tables: list[str] | None) -> None:
    """Executes the extraction."""
    dbx_catalog = get_dbx_env_catalog("bronze")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(dbx_catalog, DBX_SCHEMA, False),
        "pace_metrics/bronze",
        run_id=run_id,
    )

    # Spark session is provided by databricks
    spark: SparkSession = SparkSession.builder.getOrCreate()
    # Get SAS token to access pace_metrics data
    spark.conf.set(
        "fs.azure.sas.grafana.ststoragemetrics.blob.core.windows.net",
        get_rdd_secret("ststoragemetrics-sas-token"),
    )

    # Apply table filter if given
    if tables is not None:
        table_avro_dirs = {table: avro_dir for table, avro_dir in TABLE_AVRO_DIR_MAP.items() if table in tables}
    else:
        table_avro_dirs = TABLE_AVRO_DIR_MAP

    # Process all avro tables
    for table_name, avro_dirname in table_avro_dirs.items():
        logger.info(f"Processing table '{table_name}'")
        checkpoint_path = get_checkpoint_path(dbx_catalog, DBX_SCHEMA, table_name)
        df = _read_stream_avro_folder(spark, avro_dirname, checkpoint_path)
        _write_streaming_table(df, f"{dbx_catalog}.{DBX_SCHEMA}.{table_name}", checkpoint_path, table_name)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="pace_metrics avro extractor")
    parser.add_argument("-r", "--run_id", type=str, default=None, dest="run_id")
    parser.add_argument("-t", "--tables", choices=TABLE_AVRO_DIR_MAP.keys(), nargs="+", default=None)
    argv = parser.parse_args()

    main(argv.run_id, argv.tables)
