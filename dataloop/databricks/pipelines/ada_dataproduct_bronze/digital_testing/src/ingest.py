#!/usr/bin/env python3
"""Ingest Digital Testing reports."""
__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
import sys
from typing import Sequence

from dbxlib.databricks import get_dbx_env_catalog
from dbxlib.tables.metadata import update_table_metadata
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.functions import (
    col,
    current_timestamp,
    regexp_extract,
    to_timestamp,
)
from pyspark.sql.streaming import StreamingQuery

# Constants
DBX_SCOPE = "cloudtestexecution"
STORAGE_CONTAINER = "cloudtestexecution"
STORAGE_CONTAINER_DIR = "recompute-hour-statistics"
DBX_SCHEMA = "digital_testing"

# Table metadata
RESPONSIBLE_TEAM = "Cloud Test Execution"
RESPONSIBLE_DOMAIN = "SoCoTeC"
REFRESH_INTERVAL = "P1D"
DBX_TABLE_DESCRIPTION = "This table contains the raw ingested data of the digital testing reports."
DBX_TABLE_COLUMN_COMMENTS = {
    "value": "Raw file content of report.",
    "filename": "Name of report file.",
    "ingest_at": "Timestamp of data ingestion.",
    "created_at": "Timestamp of report creation.",
}
COLUMN_TIMESYSTEMS = {
    "ingest_at": "UTC",
    "created_at": "UTC",
}

_logger = logging.getLogger(__name__)


def setup_logging(loglevel: int) -> None:
    """Configure basic logging.

    Args:
        loglevel (int): minimum loglevel for emitting messages

    Returns:
        None
    """
    logformat = "[%(asctime)s] %(levelname)s:%(name)s:%(lineno)d: %(message)s"
    logging.basicConfig(level=loglevel, stream=sys.stdout, format=logformat, datefmt="%Y-%m-%d %H:%M:%S")


def _parse_args(args: Sequence[str]) -> argparse.Namespace:
    """Parse command line parameters.

    Args:
        args (List[str]): command line parameters as list of strings (for example  ``["--help"]``).

    Returns:
        :obj:`argparse.Namespace`: command line parameters namespace
    """
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument("--source-storage-account", help="Name of storage account to load data from")
    parser.add_argument("--target-table", help="Target table")
    parser.add_argument(
        "-v",
        "--verbose",
        dest="loglevel",
        help="set loglevel to INFO",
        action="store_const",
        const=logging.INFO,
    )
    parser.add_argument(
        "-vv",
        "--very-verbose",
        dest="loglevel",
        help="set loglevel to DEBUG",
        action="store_const",
        const=logging.DEBUG,
    )
    return parser.parse_args(args)


def _configure_az_storage(spark: SparkSession, storage_account_name: str) -> None:
    """Set the Spark session configurations for connecting to Azure Storage.

    This function retrieves client secrets from Azure Key Vault via Databricks,
    and configures the Spark session to authenticate with Azure Storage using
    OAuth credentials, which include client ID and client secret.

    Args:
        spark (SparkSession): Spark session to use.
        storage_account_name (str): Name of the Azure Storage Account.

    Returns:
        None
    """
    from pyspark.dbutils import DBUtils

    dbutils = DBUtils(spark)

    # Retrieve secrets
    client_secret = dbutils.secrets.get(scope=DBX_SCOPE, key="sp-client-key")
    client_id = dbutils.secrets.get(scope=DBX_SCOPE, key="sp-client-id")

    # Set up Spark configurations for Azure Storage Account
    storage_account = f"{storage_account_name}.dfs.core.windows.net"
    token_endpoint = "https://login.microsoftonline.com/a6c60f0f-76aa-4f80-8dba-092771d439f0/oauth2/token"
    spark.conf.set(f"fs.azure.account.auth.type.{storage_account}", "OAuth")
    spark.conf.set(
        f"fs.azure.account.oauth.provider.type.{storage_account}",
        "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider",
    )
    spark.conf.set(f"fs.azure.account.oauth2.client.id.{storage_account}", client_id)
    spark.conf.set(f"fs.azure.account.oauth2.client.secret.{storage_account}", client_secret)
    spark.conf.set(f"fs.azure.account.oauth2.client.endpoint.{storage_account}", token_endpoint)


def _write_stream_table(df: DataFrame, table_name: str, checkpoint_path: str) -> StreamingQuery:
    """Write streaming DataFrame to a Delta table using checkpoints.

    This function sets up a streaming write operation that appends new data from the DataFrame
    to the specified Delta table based on available data.

    Args:
        df (DataFrame): The DataFrame containing the data to be written.
        table_name (str): The name of the Delta table.
        checkpoint_path (str): The path to the checkpoint directory.

    Returns:
        StreamingQuery: The streaming query object.
    """
    query = (
        df.writeStream.trigger(availableNow=True)
        .option("checkpointLocation", checkpoint_path)
        .toTable(table_name, format="delta", outputMode="append")
    )

    query.awaitTermination()
    return query


def _read_stream_directory(
    spark: SparkSession,
    storage_account: str,
    storage_container: str,
    storage_container_dir: str,
) -> DataFrame:
    """Load json files from the Azure Storage Account (dbx landing zone) and return a streaming DataFrame.

    This function reads JSON files from the specified container and directory in the landing zone,
    applying the predefined schema, and prepares the data for further processing.

    Args:
        spark (SparkSession): The Spark session to use.
        storage_account (str): The name of the Azure Storage Account.
        storage_container (str): The name of the Azure Storage Container.
        storage_container_dir (str): The directory path in the Azure Storage Container.

    Returns:
        DataFrame: The streaming DataFrame containing the raw data.
    """
    df = (
        spark.readStream.format("cloudFiles")
        .option("cloudFiles.format", "text")
        .option("wholeText", "true")
        .load(f"abfss://{storage_container}@{storage_account}.dfs.core.windows.net/{storage_container_dir}/")
        .withColumn("filename", col("_metadata.file_name"))
    )

    df = df.withColumn("ingest_at", current_timestamp())
    df = df.withColumn("created_at", to_timestamp(regexp_extract(col("filename"), r"(\d{14})", 1), "yyyyMMddHHmmss"))

    return df


def main(args: Sequence[str]) -> None:
    """Ingest digital testing reports.

    Args:
        args (List[str]): command line parameters as list of strings (for example  ``["--help"]``).
    """
    parsed_args = _parse_args(args)
    setup_logging(parsed_args.loglevel)

    dbx_catalog = get_dbx_env_catalog("bronze")
    table_name_full = f"{dbx_catalog}.{DBX_SCHEMA}.{parsed_args.target_table}"
    _logger.info("Ingesting data into table: %s", table_name_full)

    spark: SparkSession = SparkSession.builder.getOrCreate()
    _configure_az_storage(spark, parsed_args.source_storage_account)
    checkpoint_path = f"/Volumes/{dbx_catalog}/{DBX_SCHEMA}/checkpoint_locations/{parsed_args.target_table}/"
    df = _read_stream_directory(
        spark,
        storage_account=parsed_args.source_storage_account,
        storage_container=STORAGE_CONTAINER,
        storage_container_dir=STORAGE_CONTAINER_DIR,
    )
    _write_stream_table(df, table_name=table_name_full, checkpoint_path=checkpoint_path)
    update_table_metadata(
        table=table_name_full,
        description=DBX_TABLE_DESCRIPTION,
        responsible_team=RESPONSIBLE_TEAM,
        responsible_domain=RESPONSIBLE_DOMAIN,
        refresh_interval=REFRESH_INTERVAL,
        column_descriptions=DBX_TABLE_COLUMN_COMMENTS,
        column_timesystems=COLUMN_TIMESYSTEMS,
    )


def run() -> None:
    """Entry point for console_scripts."""
    main(sys.argv[1:])


if __name__ == "__main__":
    run()
