# Configuration reference:
# https://learn.microsoft.com/en-us/azure/databricks/dev-tools/bundles/settings

bundle:
  name: digital_testing_reports_ingest

variables:
  pause_status:
    description: Pipeline pause status ("PAUSED" or "UNPAUSED")
    default: PAUSED
  source_storage_account:
    description: Storage account to load data from
  target_table:
    description: Name of target table
    default: reports_raw
  # See: https://github.com/PACE-INT/dataloop/tree/main/databricks/libraries#job-cluster
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"

targets:
  # use this target with VS Code extension
  dev-user:
    variables:
      pause_status: PAUSED
      source_storage_account: dbxingestzonedev
      target_table: reports_raw_testing
    mode: development
    default: true
    workspace:
      host: https://adb-***************.11.azuredatabricks.net/
      root_path: ~/.bundle/${bundle.target}/${bundle.name}

  dev:
    variables:
      pause_status: UNPAUSED
      source_storage_account: dbxingestzonedev
      target_table: reports_raw
    mode: production
    workspace:
      host: https://adb-***************.11.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_bronze/digital_testing/${bundle.name}
    run_as:
      service_principal_name: 67b5d3df-f8ff-4b97-98ab-413c29687b8a # sp-pace-dataloop-ctx-dbx-dev

  prod:
    variables:
      pause_status: "UNPAUSED"
      source_storage_account: dbxingestzoneprod
      target_table: reports_raw
    mode: production
    workspace:
      host: https://adb-****************.9.azuredatabricks.net/
      root_path: /Jobs/ada_dataproduct_bronze/digital_testing/${bundle.name}
    run_as:
      service_principal_name: 0bf8cd98-43ad-4f38-8d84-e8132bcddd54 # sp-pace-dataloop-ctx-dbx-prod

resources:
  jobs:
    ingest_digital_testing_reports:
      permissions:
      - group_name: users
        level: CAN_MANAGE_RUN

      name: Ingest digital testing reports.

      schedule:
        quartz_cron_expression: '0 30 3 * * ?' # Please also update REFRESH_INTERVAL in ingest.py when you modify this
        timezone_id: UTC
        pause_status: ${var.pause_status}

      tasks:
        - task_key: ingest_digital_testing_reports
          spark_python_task:
            python_file: ./src/ingest.py
            parameters:
            - --source-storage-account
            - ${var.source_storage_account}
            - --target-table
            - ${var.target_table}
          job_cluster_key: digital_testing_cluster
          libraries:
            - pypi:
                package: dbxlib==0.3.0

      job_clusters:
        - job_cluster_key: digital_testing_cluster
          new_cluster:
            spark_version: 15.4.x-scala2.12
            num_workers: 1
            node_type_id: Standard_D4ads_v5
            policy_id: ${var.job_cluster_policy_id}
