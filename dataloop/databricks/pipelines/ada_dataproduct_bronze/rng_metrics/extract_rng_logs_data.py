"""Stream raw RNG logs json files from azure blob storage account to bronze layer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
from typing import Optional

from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StringType, StructField, StructType
from rddlib import (
    FullSchemaName,
)
from rddlib import delta_table_utils as dtu
from rddlib import (
    get_dbx_env_catalog,
    get_rdd_secret,
    setup_databricks_logging,
)

logger = logging.getLogger(__name__)


# Constants
STORAGE_ACCOUNT = "storrddrngprod8lz"
CONTAINER = "rng-custom-logs-storage"
DBX_SCHEMA = "rng_metrics"
TABLE_NAME = "rng_logs_metrics"
DESCRIPTION = "The table stores RNG logs metrics data extracted from the RNG custom logs stored in \
Azure Blob Storage account. The ingestion and maintenance of this table are handled by the RDD Team."

RNG_LOGS_SCHEMA = StructType(
    [
        StructField("TimeGenerated", StringType(), True),
        StructField("ClientDetails", StringType(), True),
        StructField("FileName", StringType(), True),
        StructField("Parameters", StringType(), True),
        StructField("ResponseStatus", StringType(), True),
        StructField("DeploymentDetails", StringType(), True),
    ]
)


def get_checkpoint_path(catalog: str, schema: str, table: str) -> str:
    """Construct the checkpoint path for the given catalog, schema, and table.

    Checkpoints are used to store progress information during streaming, ensuring that the process
    can resume from the last successful point without reprocessing data.

    Args:
        catalog (str): The catalog name.
        schema (str): The schema name.
        table (str): The table name.

    Returns:
        str: The constructed checkpoint path.
    """
    return f"/Volumes/{catalog}/{schema}/checkpoint_locations/{table}/"


def _read_stream_rng_log_files(spark: SparkSession, checkpoint_path: str, schema: StructType) -> DataFrame:
    """Loads raw rng log files from the Azure Blob Storage Account and returns a streaming DataFrame.

    Args:
        checkpoint_path (str): The path to the checkpoint.
        schema (StructType): The schema of the JSON data.

    Returns:
        DataFrame: A streaming DataFrame containing the raw rng logs data.
    """
    return (
        spark.readStream.format("cloudFiles")
        .option("cloudFiles.format", "json")
        .option("cloudFiles.schemaLocation", checkpoint_path)
        .schema(schema)
        .load(f"wasbs://{CONTAINER}@{STORAGE_ACCOUNT}.blob.core.windows.net/")
    )


def _write_streaming_table(df: DataFrame, full_table_name: str, checkpoint_path: str) -> None:
    """Writes the streaming DataFrame to a Delta table using checkpoints.

    This function sets up a streaming write operation that appends new data from the DataFrame
    to the specified Delta table based on available data.

    Args:
        df (DataFrame): The DataFrame containing the data to be written.
        full_table_name (str): The name of the target Delta table (including catalog and schema).
        checkpoint_path (str): The path to the checkpoint directory.

    Returns:
        None
    """
    query = (
        df.writeStream.trigger(availableNow=True)
        .option(
            "checkpointLocation",
            checkpoint_path,
        )
        .toTable(f"{full_table_name}", format="delta", outputMode="append")
    )

    query.awaitTermination()
    if query.lastProgress and "numInputRows" in query.lastProgress:
        num_new_rows = query.lastProgress["numInputRows"]  # type: ignore[index]
        logger.info(f'{num_new_rows} new rows added to table "{full_table_name}".')
    else:
        logger.info(f'No progress information available for table "{full_table_name}".')


def main(run_id: Optional[str]) -> None:
    """Executes the extraction.

    Args:
        run_id (Optional[str]): The run identifier for the current job, useful for logging and tracking.

    Returns:
        None
    """
    dbx_catalog = get_dbx_env_catalog("bronze")

    # Setup logging
    setup_databricks_logging(
        FullSchemaName(dbx_catalog, DBX_SCHEMA, False),
        "rng_metrics/bronze",
        run_id=run_id,
    )

    # Spark session is provided by databricks
    spark: SparkSession = SparkSession.builder.getOrCreate()
    # Get SAS token to access rng logs data
    spark.conf.set(
        f"fs.azure.sas.{CONTAINER}.{STORAGE_ACCOUNT}.blob.core.windows.net",
        get_rdd_secret("rng-custom-logs-storage-sas-token"),
    )

    checkpoint_path = get_checkpoint_path(dbx_catalog, DBX_SCHEMA, TABLE_NAME)
    df = _read_stream_rng_log_files(spark, checkpoint_path, RNG_LOGS_SCHEMA)
    full_table_name = f"{dbx_catalog}.{DBX_SCHEMA}.{TABLE_NAME}"
    _write_streaming_table(df, full_table_name, checkpoint_path)
    # Add metadata to the table
    dtu.update_table_metadata(full_table_name, DESCRIPTION)


if __name__ == "__main__":
    # Parse command-line arguments for app and run_id
    parser = argparse.ArgumentParser(description="RNG logs metrics bronze extractor")
    parser.add_argument("-r", "--run_id", type=str, default=None, dest="run_id")
    args, unknown = parser.parse_known_args()

    # Execute the main logic
    main(args.run_id)
