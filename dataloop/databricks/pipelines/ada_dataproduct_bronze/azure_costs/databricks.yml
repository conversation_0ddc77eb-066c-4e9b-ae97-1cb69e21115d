# This is a Databricks asset bundle definition for the fetching Azure cost data
bundle:
  name: ada_dataproduct_cost_data_bronze

include:
  - resources/*.yml

targets:
  dev:
    variables:
      env: dev
    mode: production
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}

  qa:
    variables:
      env: qa
    mode: production
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}

  prod:
    variables:
      env: prod
      schedule_pause_status: "UNPAUSED"
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
    resources:
      jobs:
        azure_costs_bronze:
          email_notifications:
            on_failure:
              - ${var.ms_teams_alert_channel_email}
        azure_costs_parameterized:
          email_notifications:
            on_failure:
              - ${var.ms_teams_alert_channel_email}
