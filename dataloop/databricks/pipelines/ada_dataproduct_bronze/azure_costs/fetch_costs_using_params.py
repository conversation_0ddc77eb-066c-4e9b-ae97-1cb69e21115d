"""Script for fetching bronze layer cost data for all available ADA subscriptions.

The available subscriptions depend on the service principal being used when running the script.
After fetching, the data is pushed to a bronze layer table named azure_costs.
The data collected is collected with a daily granularity and for each resource.

This script is started manually and parameterized so that the user can decide
what period of time to fetch cost data for.

The table looks like this:
        cost, FLOAT
        day, DATE
        resource_group_name, STRING
        resource_id, STRING
        location, STRING
        type, STRING
        currency, STRING
        subscription_id, STRING
        subscription_name, STRING
        resource_group_id, STRING
        resource_name, STRING
        billing_period, STRING
        environment, STRING

Maintainer: Brange Henrik (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import logging
import sys
from argparse import ArgumentParser
from datetime import datetime
from logging import LogRecord

from azure.mgmt.costmanagement.models import QueryTimePeriod
from azure_cost_fetcher import AzureCostFetcher
from pytz import UTC

log = logging.getLogger(__name__)


def parse_time_period(start_date_param: str, end_date_param: str) -> QueryTimePeriod:
    """Parse the time period for the query from string argument."""

    # Get job parameters
    date_format = "%Y-%m-%d"

    start_date = datetime.strptime(start_date_param, date_format)
    end_date = datetime.strptime(end_date_param, date_format)
    start_date = start_date.replace(tzinfo=UTC)
    end_date = end_date.replace(tzinfo=UTC)

    return QueryTimePeriod(from_property=start_date, to=end_date)


if __name__ == "__main__":
    log.setLevel(logging.INFO)
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter("%(asctime)s - %(module)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    log.addHandler(handler)

    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    parser.add_argument(
        "--start_date",
        help="The start date for which to fetch cost data",
    )
    parser.add_argument(
        "--end_date",
        help="The end date for which to fetch cost data",
    )
    parser.add_argument(
        "--write_mode",
        choices=["append", "overwrite", "dry-run"],
        help="Whether to overwrite or append the data",
    )

    args = parser.parse_args()

    historical_cost_data_tables = {
        "dev": "bronze_dev.costs.azure_costs_historical",
        "qa": "bronze_qa.costs.azure_costs_historical",
        "prod": "bronze.costs.azure_costs_historical",
    }

    HISTORICAL_TABLE = historical_cost_data_tables[args.env]

    start_date_param = args.start_date
    end_date_param = args.end_date
    write_mode_param = args.write_mode

    # Instantiate AzureCostFetcher and get costs for all available subscriptions
    CLIENT_SECRET = dbutils.secrets.get(scope="dmv", key="appreg--sp-pace-dataloop-dmv--secret")
    AZURE_CLIENT_ID = dbutils.secrets.get(scope="dmv", key="appreg--sp-pace-dataloop-dmv--appid")
    AZURE_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
    AUDIENCE = "https://management.azure.com/.default"
    cost_fetcher = AzureCostFetcher(spark, CLIENT_SECRET, AZURE_CLIENT_ID, AZURE_TENANT_ID, AUDIENCE, args.env, log)

    log.info("Fetching costs...")
    dataframes = cost_fetcher.get_all_costs(parse_time_period(start_date_param, end_date_param))
    assert len(dataframes) > 0
    log.info(f"Successfully fetched data. Number of dataframes to be written: {len(dataframes)}")

    if spark.catalog.tableExists(HISTORICAL_TABLE) and write_mode_param == "overwrite":
        log.info('Write mode is "overwrite", truncating HISTORICAL table...')
        spark.sql(f"TRUNCATE TABLE {HISTORICAL_TABLE}")

    if (write_mode_param == "append") or (write_mode_param == "overwrite"):
        log.info("Appending dataframes to HISTORICAL table..")
        for df in dataframes:
            df.write.mode("append").saveAsTable(HISTORICAL_TABLE)

    elif write_mode_param == "dry-run":
        log.info("Write mode is 'dry-run', data will not be written to any table...")

    log.info("Job successful, exiting...")
