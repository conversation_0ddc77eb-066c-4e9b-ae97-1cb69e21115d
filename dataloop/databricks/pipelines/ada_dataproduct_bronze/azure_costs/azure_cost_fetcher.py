"""Contains class AzureCostFetcher for fetching Azure cost data.

Maintainer: <PERSON><PERSON><PERSON> (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import time
from datetime import datetime, timedelta
from typing import Iterable, Optional

import requests
from azure.identity import *
from azure.identity import ClientSecretCredential
from azure.mgmt.costmanagement.models import QueryTimePeriod
from azure.mgmt.resource import SubscriptionClient
from pyspark.sql import DataFrame
from pyspark.sql.functions import col, concat, lit, split, to_date, when
from pyspark.sql.types import *


class AzureCostFetcher:
    """Class for fetching Azure cost data.

    This class provides functions for fetching Azure cost data either
    for all subscriptions available or for a specific subscription.
    """

    def __init__(self, spark, client_secret, client_id, tenant_id, audience, env, log) -> None:
        """Construct a AzureCostFetcher.

        Args:
            spark (SparkSession): The SparkSession for manipulating DataFrames
            client_secret (str): The client secret used for authentication
            client_id (str): The Azure client ID
            tenant_id(str): The Azure tenant ID
            audience(str): The audience for the token to be generated
            env(str): The execution environment, either dev, qa or prod
            log(logging.Logger): The Logger for logging functionality
        """
        self.spark = spark
        self.CLIENT_SECRET = client_secret
        self.AZURE_CLIENT_ID = client_id
        self.AZURE_TENANT_ID = tenant_id
        self.AUDIENCE = audience
        self.ENV = env
        self.log = log

    def get_costs_for_sub(
        self, subscription: str, time_period: QueryTimePeriod, next_link: Optional[str] = None
    ) -> DataFrame:
        """Call Azure REST API to fetch costs for a given subscription and time period.

        If a request performed by this function results in a response containing 5000
        or more entries, the Azure API will truncate the response and return a next_link
        variable containing a URL to the next page. In this case, this function will call
        itself recursively and pass the optional next_link argument so that the
        next page will be fetched.
        """

        payload = {
            "type": "Usage",
            "timeframe": "Custom",
            "timePeriod": {
                "from": time_period.from_property.strftime("%Y-%m-%d"),
                "to": time_period.to.strftime("%Y-%m-%d"),
            },
            "dataset": {
                "granularity": "Daily",
                "aggregation": {"totalCost": {"name": "Cost", "function": "Sum"}},
                "grouping": [
                    {"type": "Dimension", "name": "ResourceGroup"},
                    {"type": "Dimension", "name": "ResourceId"},
                    {"type": "Dimension", "name": "ResourceLocation"},
                    {"type": "Dimension", "name": "ResourceType"},
                ],
            },
        }

        schema = StructType(
            [
                StructField("cost", FloatType(), False),
                StructField("day", IntegerType(), False),
                StructField("resource_group_name", StringType(), True),
                StructField("resource_id", StringType(), False),
                StructField("location", StringType(), False),
                StructField("type", StringType(), False),
                StructField("currency", StringType(), False),
            ]
        )
        auth_header = self._get_authorization_header()
        subscription_id = subscription.removeprefix("/subscriptions/")
        max_retries = 10
        backoff_time = 2
        result_df = None
        for i in range(max_retries):
            url = f"https://management.azure.com/subscriptions/{subscription_id}/providers/Microsoft.CostManagement/query?api-version=2022-10-01"
            time_period_start = time_period.from_property.strftime("%Y-%m-%d %H:%M:%S")
            time_period_end = time_period.to.strftime("%Y-%m-%d %H:%M:%S")
            self.log.info(
                f"Fetching data for subscription {subscription} from {time_period_start} to {time_period_end}..."
            )

            if next_link is not None:
                result = requests.post(next_link, headers=auth_header, json=payload)
            else:
                result = requests.post(url, headers=auth_header, json=payload)

            result_df = None
            if result.status_code == 200:
                result_dict = result.json()
                result_df = self.spark.createDataFrame(result_dict["properties"]["rows"], schema)
                result_df = result_df.withColumn("day", to_date(col("day").cast("string"), "yyyyMMdd"))

                next_link = result_dict["properties"]["nextLink"]
                if next_link is not None and next_link != "":
                    self.log.info("Getting next page of results...")
                    # Call this function recursively for each page
                    next_page_df = self.get_costs_for_sub(subscription, time_period, next_link)

                    assert result_df is not None, "Failed to fetch next page in 10 retries, exiting..."
                    result_df = result_df.union(next_page_df)
                else:
                    return result_df
                break
            elif result.status_code == 429 or result.status_code >= 500:
                timeout1 = int(result.headers.get("x-ms-ratelimit-microsoft.costmanagement-client-retry-after", 15))
                timeout2 = int(result.headers.get("x-ms-ratelimit-microsoft.costmanagement-entity-retry-after", 15))
                backoff_time = backoff_time * 2
                retry_after = max(timeout1, timeout2, backoff_time)
                self.log.warning(
                    f"Query failed ({result.status_code} - {result.reason}). Retrying after {retry_after} seconds."
                )
                time.sleep(int(retry_after))
            else:
                error_string = (
                    f"Unexpected HTTP error code returned: {result.status_code}\n"
                    + "Request that preceeded error code:\n"
                    + f"Time period, from: {time_period.from_property}\n"
                    + f"Time period, to: {time_period.to}\n"
                    + f"Subscription: {subscription}"
                )
                assert False, error_string  # Incomplete data could corrupt the state of the table, exit...

        assert result_df is not None, "Failed to fetch data in 10 retries, exiting..."
        return result_df

    def get_all_costs(self, time_period) -> list[DataFrame]:
        """Call Azure REST API to fetch cost data for every available subscription.

        Returns a dataframe containing the results.
        """

        subscriptions = self._get_subscriptions()
        result_df = None

        split_time_periods = self._split_time_period(time_period.from_property, time_period.to)

        # Get costs
        dataframes = list[DataFrame]()  # Create list of dataframes to avoid creating a dataframe that exceeds max size
        for split_period in split_time_periods:
            split_period_start = split_period.from_property.strftime("%Y-%m-%d %H:%M:%S")
            split_period_end = split_period.to.strftime("%Y-%m-%d %H:%M:%S")
            self.log.info(f"Fetching data for time period: {split_period_start} - {split_period_end}...")
            for entry in subscriptions:
                subscription = entry.as_dict()
                df = self.get_costs_for_sub(subscription["id"], split_period)

                final_df = (
                    df.withColumn("subscription_id", lit(subscription["id"]))
                    .withColumn("subscription_name", lit(subscription["display_name"]))
                    .withColumn(
                        "resource_group_id",
                        concat(
                            lit(subscription["id"]),
                            lit("/resourceGroups/"),
                            col("resource_group_name"),
                        ),
                    )
                    .withColumn("resource_name", split(col("resource_id"), "/").getItem(8))
                    .withColumn("billing_period", lit("Daily"))
                    .withColumn(
                        "environment",
                        when(col("subscription_name").like("%prod%"), "PROD")
                        .when(col("subscription_name").like("%prd%"), "PROD")
                        .when(col("subscription_name").like("%qa%"), "QA")
                        .when(col("subscription_name").like("%dev%"), "DEV")
                        .otherwise("unknown"),
                    )
                )
                if result_df is None:
                    result_df = final_df
                else:
                    result_df = result_df.union(final_df)
                    if result_df.count() > 50000:  # Append final_df to list of dataframes when it gets too big
                        dataframes.append(result_df)
                        result_df = None

            if result_df is not None:  # Ensure the final dataframe is appended
                dataframes.append(result_df)

        return dataframes

    def _split_time_period(self, start_date: datetime, end_date: datetime) -> list[QueryTimePeriod]:
        """Split a time period into 30-day long chunks.

        Note that the 30-day chunks will contain gaps with regards to the time. This is
        acceptable as the Azure cost API considers only the date.
        For example, querying a time period from 2024-10-01 00:00:00 to 2024-10-01 05:00:00
        will result in Azure returning all data for that day, rather than just the first five hours.

        Args:
            start_date (datetime): The start date of the time period.
            end_date (datetime): The end date of the time period.

        Returns:
            list[QueryTimePeriod]: A list of QueryTimePeriod objects representing 30-day chunks.

        Example:
            If called with:
                start_date = datetime(2024, 1, 1, 0, 0, 0)
                end_date = datetime(2024, 10, 31, 23, 59, 59, 999999, 0)

            The function will return:
            [
                QueryTimePeriod(from_property=datetime(2024, 1, 1, 0, 0, 0), to=datetime(2024, 1, 31, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 2, 1, 0, 0, 0), to=datetime(2024, 3, 2, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 3, 3, 0, 0, 0), to=datetime(2024, 4, 2, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 4, 3, 0, 0, 0), to=datetime(2024, 5, 3, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 5, 4, 0, 0, 0), to=datetime(2024, 6, 3, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 6, 4, 0, 0, 0), to=datetime(2024, 7, 4, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 7, 5, 0, 0, 0), to=datetime(2024, 8, 4, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 8, 5, 0, 0, 0), to=datetime(2024, 9, 4, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 9, 5, 0, 0, 0), to=datetime(2024, 10, 5, 0, 0, 0)),
                QueryTimePeriod(from_property=datetime(2024, 10, 6, 0, 0, 0), to=datetime(2024, 10, 31, 23, 59, 59, 999999, 0))
            ]
        """
        periods: list[QueryTimePeriod] = []
        current_date: datetime = start_date
        next_date: datetime = current_date + timedelta(days=30)

        while next_date < end_date:
            periods.append(QueryTimePeriod(from_property=current_date, to=next_date))
            current_date = next_date + timedelta(days=1)  # Move to the day after the next period
            next_date = current_date + timedelta(days=30)

        periods.append(QueryTimePeriod(from_property=current_date, to=end_date))
        return periods

    def _get_subscriptions(self) -> Iterable["Subscription"]:
        credential = ClientSecretCredential(
            client_id=self.AZURE_CLIENT_ID, client_secret=self.CLIENT_SECRET, tenant_id=self.AZURE_TENANT_ID
        )
        sub_client = SubscriptionClient(credential)
        subscriptions = sub_client.subscriptions.list()

        return subscriptions

    def _get_authorization_header(self) -> dict[str, str]:
        # Get necessary credentials, token
        credential = ClientSecretCredential(
            client_id=self.AZURE_CLIENT_ID, client_secret=self.CLIENT_SECRET, tenant_id=self.AZURE_TENANT_ID
        )
        token = credential.get_token(self.AUDIENCE)
        auth_header = {
            "Authorization": "Bearer " + str(token.token),
            "X-Ms-Command-Name": "CostAnalysis",
            "ClientType": "AzureCostFetcher",
        }
        return auth_header
