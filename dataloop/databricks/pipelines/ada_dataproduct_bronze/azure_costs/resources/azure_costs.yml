resources:
  jobs:
    azure_costs_bronze:
      # Give permission to DMV admin users to manage run
      permissions:
      - group_name: "sg-pace-databricks-Data Delivery-Data-Management-Verticalization-(DMV)-admin"
        level: CAN_MANAGE_RUN

      name: Fetch recent Azure cost data

      schedule:
        quartz_cron_expression: '0 0 4 * * ?'
        timezone_id: UTC
        pause_status: ${var.schedule_pause_status}
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 9000 # 2.5 hours

      tasks:
        - task_key: fetch_current_costs
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/fetch_costs.py
            parameters:
              - -e
              - ${var.env}
          job_cluster_key: azure_recent_cost_data_job_cluster
          libraries:
            - pypi:
                package: azure-identity==1.17.1
            - pypi:
                package: azure-mgmt-resource==23.1.1
            - pypi:
                package: azure-mgmt-costmanagement==4.0.1
            - pypi:
                package: pyspark==3.5.2

      job_clusters:
        - job_cluster_key: azure_recent_cost_data_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            num_workers: ${var.num_workers}
            spark_env_vars:
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
            init_scripts:
              - volumes:
                  destination: /Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh

    azure_costs_parameterized:
      # Give permission to DMV admin users to manage run
      permissions:
      - group_name: "sg-pace-databricks-Data Delivery-Data-Management-Verticalization-(DMV)-admin"
        level: CAN_MANAGE_RUN

      name: Fetch Azure cost data using parameters
      health:
        rules:
          - metric: RUN_DURATION_SECONDS
            op: GREATER_THAN
            value: 9000 # 2.5 hours

      tasks:
        - task_key: fetch_cost_parameterized
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/fetch_costs_using_params.py
            parameters:
              - -e
              - ${var.env}
              - --start_date
              - "{{job.parameters.start_date}}"
              - --end_date
              - "{{job.parameters.end_date}}"
              - --write_mode
              - "{{job.parameters.write_mode}}"

          job_cluster_key: azure_parameterized_cost_data_job_cluster
          libraries:
            - pypi:
                package: azure-identity==1.17.1
            - pypi:
                package: azure-mgmt-resource==23.1.1
            - pypi:
                package: azure-mgmt-costmanagement==4.0.1
            - pypi:
                package: pyspark==3.5.2

      parameters:
        - name: start_date
          default: "2024-01-01"
        - name: end_date
          default: "2024-10-31"
        - name: write_mode
          default: "dry-run"

      job_clusters:
        - job_cluster_key: azure_parameterized_cost_data_job_cluster
          new_cluster:
            driver_instance_pool_id: ${var.driver_instance_pool_id}
            instance_pool_id: ${var.instance_pool_id}
            spark_version: ${var.spark_version}
            policy_id: ${var.job_cluster_policy_id}
            num_workers: ${var.num_workers}
            spark_env_vars:
              PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
              PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
            init_scripts:
              - volumes:
                  destination: /Volumes/central_scripts/scripts/init_scripts/init-pip-conf-datalake-${var.env}.sh
