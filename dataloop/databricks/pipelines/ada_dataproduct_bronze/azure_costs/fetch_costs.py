"""Script for fetching bronze layer cost data for all available ADA subscriptions.

The available subscriptions depend on the service principal being used when running the script.
After fetching, the data is pushed to a bronze layer table named azure_costs.

The data collected is collected with a daily granularity and for each resource.

The table looks like this:
        cost, FLOAT
        day, DATE
        resource_group_name, STRING
        resource_id, STRING
        location, STRING
        type, STRING
        currency, STRING
        subscription_id, STRING
        subscription_name, STRING
        resource_group_id, STRING
        resource_name, STRING
        billing_period, STRING
        environment, STRING

Maintainer: <PERSON><PERSON><PERSON> Henrik (XC/ESX1-SE), Lund, Sweden
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import sys
from argparse import ArgumentParser
from datetime import datetime, timedelta

from azure.mgmt.costmanagement.models import QueryTimePeriod
from azure_cost_fetcher import AzureCostFetcher
from pytz import UTC

log = logging.getLogger(__name__)


def get_time_period() -> QueryTimePeriod:
    """Get the time period for the query."""
    present_date = datetime.now(UTC).replace(hour=0, minute=0, second=0, microsecond=0)

    if present_date.month == 1:
        start_of_previous_month = present_date.replace(year=(present_date.year - 1), month=12, day=1)
    else:
        start_of_previous_month = present_date.replace(month=(present_date.month - 1), day=1)

    start_of_current_month = present_date.replace(day=1)
    # If more than 72h have passed since last month, we have finalized the data already
    start_date = start_of_current_month if present_date.day > 4 else start_of_previous_month
    yesterday_midnight = (present_date - timedelta(days=1)).replace(hour=23, minute=59, second=59, microsecond=999999)
    end_date = yesterday_midnight

    return QueryTimePeriod(from_property=start_date, to=end_date)


if __name__ == "__main__":
    log.setLevel(logging.INFO)
    handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter("%(asctime)s - %(module)s - %(levelname)s - %(message)s")
    handler.setFormatter(formatter)
    log.addHandler(handler)

    parser = ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    args = parser.parse_args()

    # Define the output tables
    current_month_cost_data_tables = {
        "dev": "bronze_dev.costs.azure_costs_current_month",
        "qa": "bronze_qa.costs.azure_costs_current_month",
        "prod": "bronze.costs.azure_costs_current_month",
    }

    historical_cost_data_tables = {
        "dev": "bronze_dev.costs.azure_costs_historical",
        "qa": "bronze_qa.costs.azure_costs_historical",
        "prod": "bronze.costs.azure_costs_historical",
    }

    CURRENT_MONTH_TABLE = current_month_cost_data_tables[args.env]
    HISTORICAL_TABLE = historical_cost_data_tables[args.env]

    # Instantiate AzureCostFetcher and get costs for all available subscriptions
    CLIENT_SECRET = dbutils.secrets.get(scope="dmv", key="appreg--sp-pace-dataloop-dmv--secret")
    AZURE_CLIENT_ID = dbutils.secrets.get(scope="dmv", key="appreg--sp-pace-dataloop-dmv--appid")
    AZURE_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
    AUDIENCE = "https://management.azure.com/.default"
    cost_fetcher = AzureCostFetcher(spark, CLIENT_SECRET, AZURE_CLIENT_ID, AZURE_TENANT_ID, AUDIENCE, args.env, log)

    log.info("Fetching costs...")
    dataframes = cost_fetcher.get_all_costs(get_time_period())
    assert len(dataframes) > 0
    log.info(f"Successfully fetched data. Number of dataframes to be written: {len(dataframes)}")

    log.info("Truncating CURRENT MONTH table...")
    if spark.catalog.tableExists(CURRENT_MONTH_TABLE):
        spark.sql(f"TRUNCATE TABLE {CURRENT_MONTH_TABLE}")  # Clear the table to avoid duplicates

    log.info("Appending dataframes to CURRENT MONTH table...")
    for df in dataframes:
        df.write.mode("append").saveAsTable(CURRENT_MONTH_TABLE)

    present_date = datetime.now(UTC).replace(hour=0, minute=0, second=0, microsecond=0)
    start_of_previous_month = None

    if present_date.month == 1:
        start_of_previous_month = present_date.replace(year=(present_date.year - 1), month=12, day=1)
    else:
        start_of_previous_month = present_date.replace(month=(present_date.month - 1), day=1)

    # If 72h have passed since last month, push the finalized data to the historical data table
    if present_date.day == 4:
        previous_month_df = spark.sql(
            f"SELECT * FROM {CURRENT_MONTH_TABLE} WHERE month(day)=={start_of_previous_month.month}"
        )
        log.info("It is the fourth day of the month, moving data from CURRENT MONTH to HISTORICAL table...")
        previous_month_df.write.mode("append").saveAsTable(HISTORICAL_TABLE)

    log.info("Job successful, exiting...")
