## Install

Based on a post by @marcel-straub_pace.
```sh
# Leave any entered venvs
deactivate || true

cd databricks/pipelines/ada_dataproduct_bronze/azureml

# create venv and activate
python3 -m venv .venv
# or python3 -m virtualenv .venv
source .venv/bin/activate

# install python requirements
pip install -e.[testing,dev]

# Install databricks cli
# Details: https://docs.databricks.com/en/dev-tools/cli/install.html#curl-installation-for-linux-macos-and-windows
curl -fsSL https://raw.githubusercontent.com/databricks/setup-cli/main/install.sh | sh


# Authenticate to databricks
databricks auth login --profile DEFAULT --configure-cluster --host https://adb-505904006080631.11.azuredatabricks.net
```
