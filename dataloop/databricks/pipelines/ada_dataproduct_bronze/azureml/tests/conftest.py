"""Fixtures for AzureML Databricks tests."""  # noqa: INP001

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import os
import shutil
from typing import Any, Generator
from unittest.mock import patch

import pandas as pd
import pytest
from pyspark.sql import SparkSession


@pytest.fixture
def pace_general_dataframe() -> pd.DataFrame:
    """Load the PaceGeneral.csv test data."""
    # Load the PaceGeneral.csv test data in the data folder relative to this file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, "test_data")
    return pd.read_csv(os.path.join(data_dir, "PaceGeneralYUV.csv"))


@pytest.fixture
def pace_general_trifocal_dataframe() -> pd.DataFrame:
    """Load the PaceGeneral-trifocal.csv test data.

    Note:
        The dataset does not have any compressed_* columns.
    """
    # Load the PaceGeneral.csv test data in the data folder relative to this file
    current_dir = os.path.dirname(os.path.abspath(__file__))
    data_dir = os.path.join(current_dir, "test_data")
    return pd.read_csv(os.path.join(data_dir, "PaceGeneralYUV-trifocal.csv"))


@pytest.fixture()
def spark_session_mock() -> Generator[SparkSession, Any, Any]:
    """Fixture to mock Spark session."""
    with patch("training_datasets.get_or_create_databricks_session") as mock_get_or_create:
        mock_spark = SparkSession.builder.master("local[2]").appName("pytest-pyspark-local-testing").getOrCreate()
        mock_spark.sql("CREATE SCHEMA IF NOT EXISTS azureml")
        mock_get_or_create.return_value = mock_spark
        yield mock_spark
        mock_spark.stop()
        shutil.rmtree("spark-warehouse", ignore_errors=True)
