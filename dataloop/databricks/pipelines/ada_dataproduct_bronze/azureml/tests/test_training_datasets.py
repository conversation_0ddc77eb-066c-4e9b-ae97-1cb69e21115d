"""Unit tests for training_datasets.py."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from datetime import datetime
from unittest.mock import Mock, patch

import pytest
from azureml.core import Dataset, Workspace
from pyspark.sql import Row, SparkSession
from training_datasets import DatasetInfo, add_descriptions, get_existing_dataset_versions, sync_dataset

logging.getLogger("py4j.clientserver").setLevel(logging.ERROR)


@pytest.fixture
def training_datasets_metadata_data() -> list[Row]:
    """Fixture creating data for the training_datasets_metadata table."""
    return [
        Row(
            dataset_name="PaceGeneralYUV",
            dataset_version=42,
            creation_time=datetime.strptime("2023-01-01 00:00:00", "%Y-%m-%d %H:%M:%S"),
        ),
        Row(
            dataset_name="PaceGeneralYUV",
            dataset_version=23,
            creation_time=datetime.strptime("2021-01-01 00:00:00", "%Y-%m-%d %H:%M:%S"),
        ),
    ]


def test_get_existing_dataset_versions(
    spark_session_mock: SparkSession, training_datasets_metadata_data: list[Row]
) -> None:
    """Test if the existing dataset versions can be retrieved from the metadata table."""
    # GIVEN a spark session, mock data, table and dataset_name
    metadata_table = "spark_catalog.azureml.training_datasets_metadata"
    spark_session_mock.createDataFrame(training_datasets_metadata_data).write.mode("overwrite").saveAsTable(
        metadata_table
    )
    dataset_name = "PaceGeneralYUV"

    # WHEN calling the function
    existing_dataset_versions = get_existing_dataset_versions(metadata_table, dataset_name, spark_session_mock)

    # THEN
    assert existing_dataset_versions == [23, 42]


def test_get_existing_dataset_versions_table_not_exists(spark_session_mock: SparkSession) -> None:
    """Test if no dataset versions are returned if the metadata table does not exist."""
    # GIVEN a spark session and a non-existing metadata table
    metadata_table = "spark_catalog.azureml.training_datasets_metadata"

    # WHEN calling the function
    existing_dataset_versions = get_existing_dataset_versions(metadata_table, "PaceGeneralYUV", spark_session_mock)

    # THEN
    assert existing_dataset_versions == []


def test_add_descriptions(spark_session_mock: SparkSession, training_datasets_metadata_data: list[Row]) -> None:
    """Test if descriptions can be added to a table and its columns."""
    # GIVEN a table with columns dataset_name, dataset_version and creation_time
    metadata_table = "spark_catalog.azureml.training_datasets_metadata"
    spark_session_mock.createDataFrame(training_datasets_metadata_data).write.mode("overwrite").saveAsTable(
        metadata_table
    )

    # and a table and column descriptions
    table_description = "This is the greatest table ever"
    column_descriptions = {
        "dataset_name": "Name of the dataset (e.g. PaceGeneralYUV)",
        "dataset_version": "Version of the dataset",
        "creation_time": "Creation time of the dataset according to its .csv file creation",
    }

    # WHEN calling the function to add the descriptions
    add_descriptions(metadata_table, column_descriptions, table_description, spark_session_mock)

    # THEN the correct table and column descriptions should be added
    description_results = spark_session_mock.sql(f"DESCRIBE FORMATTED {metadata_table}").toPandas()

    assert description_results[description_results.col_name == "Comment"]["data_type"].values[0] == table_description
    for column, column_description in column_descriptions.items():
        assert description_results[description_results.col_name == column]["comment"].values[0] == column_description


@pytest.mark.parametrize("dataset_fixture", ["pace_general_dataframe", "pace_general_trifocal_dataframe"])
def test_sync_dataset(spark_session_mock: SparkSession, dataset_fixture: str, request) -> None:
    """Test end-to-end dataset sync.

    Note:
        The trifocal dataset does not have any compressed_* columns,
        so this test also checks that the sync works for datasets without compressed_* columns.
    """
    # GIVEN an Azure ML datasets and Databricks target tables
    table = "spark_catalog.azureml.training_datasets"
    metadata_table = "spark_catalog.azureml.training_datasets_metadata"
    dataset_name = "PaceGeneralYUV"
    dataset = DatasetInfo(dataset_name=dataset_name, input_data_id="yuv420", workspace=Mock(spec=Workspace))
    aml_dataset_df = request.getfixturevalue(dataset_fixture)

    mock_aml_dataset = Mock(spec=Dataset)
    mock_aml_dataset.version = 2

    # WHEN syncing the Azure ML datasets to Databricks
    creation_time = datetime.now()
    with (
        patch("training_datasets.Dataset") as mock_dataset_class,
        patch(
            "training_datasets.aml_dataset_to_dataframe",
            side_effect=lambda *args, **kwargs: aml_dataset_df.copy(),  # we need a fresh copy for each call
        ),
        patch("training_datasets.aml_dataset_creation_time", return_value=creation_time),
    ):
        mock_dataset_class.get_by_name.return_value = mock_aml_dataset
        sync_dataset(dataset, spark_session_mock, table, metadata_table)

    # THEN the Databricks table containing the dataset should be correctly populated
    dbx_dataset_df = spark_session_mock.sql(f"SELECT * FROM {table}").toPandas()
    # containing the correct columns
    assert set(dbx_dataset_df.columns) == {
        "split",
        "input_data",
        "task",
        "label_path",
        "dataset_name",
        "dataset_version",
    }
    # and the correct data
    assert (dbx_dataset_df.dataset_name == dataset_name).all()
    assert sorted(dbx_dataset_df.dataset_version.unique()) == [1, 2]

    # and the correct length:
    #   the same dataset is synced twice and stored in long form.
    #   i.e., the expected length is the sum of non-null entries in all task columns times 2
    input_data_columns = aml_dataset_df.columns[aml_dataset_df.columns.str.contains(dataset.input_data_id)].tolist()
    compressed_columns = aml_dataset_df.columns[aml_dataset_df.columns.str.contains("compressed")].tolist()
    task_columns = list(set(aml_dataset_df.columns) - set(input_data_columns) - set(compressed_columns) - {"split"})
    # First sum is per column, second sum over all columns
    expected_length = aml_dataset_df[task_columns].notnull().sum().sum()
    assert len(dbx_dataset_df) == expected_length * 2

    # and the input_data column should contain a dict containing all three input_data columns as keys
    expected_keys = set(input_data_columns)
    assert dbx_dataset_df.input_data.apply(lambda x: set(x.keys()) == expected_keys).all()

    # THEN the metadata table should contain the correct metadata
    metadata_df = spark_session_mock.sql(f"SELECT * FROM {metadata_table}").toPandas()
    assert (metadata_df.dataset_name == dataset_name).all()
    assert sorted(metadata_df.dataset_version.tolist()) == [1, 2]
    assert (metadata_df.creation_time == creation_time).all()
