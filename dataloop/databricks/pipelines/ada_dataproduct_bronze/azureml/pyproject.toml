# ==============================================================================
#
# C O P Y R I G H T
# ------------------------------------------------------------------------------
#
#  Copyright (C) 2024 Robert <PERSON> GmbH
#  Copyright (C) 2024 by CARIAD and Robert <PERSON> GmbH. All rights reserved.
#  The reproduction, distribution and utilization of this file as
#  well as the communication of its contents to others without express
#  authorization is prohibited. Offenders will be held liable for the
#  payment of damages. All rights reserved in the event of the grant
#  of a patent, utility model or design.
# ==============================================================================

[project]
name = "bronze-azureml-asset-bundle"
description = "AzureML bronze layer asset bundle for Databricks."
requires-python = ">=3.10"
version = "0.0.1"
dependencies = [
    "deltalake==0.20.0",
    "pandas",
    "azureml-core",
    "pyspark",
    "delta-spark",
]

[project.optional-dependencies]
run_local = ["databricks-connect~=14.3.2"]
testing = ["pytest", "requests", "coverage"]
dev = [
    "pre-commit~=3.7.1",
    "isort",
    "pydocstyle",
    "jedi",
    "mypy",
    "black",
    "ipython",
    "matplotlib-inline",
    "flake8"
]

[tool.pytest.ini_options]
pythonpath = "./src"
log_cli = "True"
log_level = "DEBUG"
