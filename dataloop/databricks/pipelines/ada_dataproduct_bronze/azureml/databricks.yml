# This is a Databricks asset bundle definition for the azureml pipeline.
# See https://docs.databricks.com/dev-tools/bundles/index.html for documentation.
bundle:
  name: ada_dataproduct_bronze

include:
  - resources/*.yml

targets:
  dev:
    variables:
      run_sp: 011157e5-a1ca-4053-a240-430e618a6b3a
      env: dev
      pause_status: "PAUSED"
    mode: production
    default: true
    workspace:
      host: https://adb-505904006080631.11.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}

  qa:
    variables:
      run_sp: f959d385-9926-456b-9184-5e088fd84ee2
      env: qa
      pause_status: "PAUSED"
    mode: production
    workspace:
      host: https://adb-1833128652588029.9.azuredatabricks.net
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}

  prod:
    variables:
      run_sp: ba9d2ed2-2646-4e02-81f1-f7183f104e31
      env: prod
    mode: production
    workspace:
      host: https://adb-8617216030703889.9.azuredatabricks.net/
      root_path: /Users/<USER>/.bundle/${bundle.target}/${bundle.name}
    run_as:
      service_principal_name: ${var.run_sp}
