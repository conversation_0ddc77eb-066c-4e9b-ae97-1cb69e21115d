"""Pipeline to generate a bronze layer schema for Azure ML Multitask Datasets.

The pipeline generates two bronze layer tables `training_datasets` and `training_datasets_metadata`.


`training_datasets`

    The datasets are originally in a wide-form, with columns rgb, split and one per task.
    The schema is transformed to a long-form, with columns rgb, split, task and path, where

    - input_data: The path(s) to the input image(s) relative to the ingest_cache
    - split: The split of the dataset (train, val, test)
    - task: The task of the dataset (e.g. "traffic_light")
    - label_path: The path to the label file relative to the ingest cache

    Additionally, two columns identifying the dataset are added:

    - dataset_name: The name of the dataset
    - dataset_version: The version of the dataset

`training_dataset_metadata`

    The metadata table contains the creation time of the dataset. It has the following columns:

    - dataset_name: The name of the dataset
    - dataset_version: The version of the dataset
    - creation_time: The creation time of the dataset

"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import datetime
import io
import logging
from dataclasses import dataclass, field

import pandas as pd
from azureml.core import Dataset, Workspace
from azureml.core.authentication import ServicePrincipalAuthentication
from azureml.data import TabularDataset
from pyspark.sql import SparkSession

logging.basicConfig(level=logging.INFO)
LOGGER = logging.getLogger(__name__)


@dataclass
class DatasetInfo:
    """Dataclass to store information about a dataset."""

    dataset_name: str
    input_data_id: str
    workspace: Workspace
    skip_versions: list[int] = field(default_factory=list)


def get_ct_workspace() -> Workspace:  # pragma: no cover
    """Get the continuous_training Azure ML workspace.

    Returns:
        The continuous_training Azure ML workspace.
    """
    # dbutils is available globally in the databricks runtime
    sp_client_id = dbutils.secrets.get(scope="azureml", key="sp-client-id")  # noqa: F821
    sp_client_key = dbutils.secrets.get(scope="azureml", key="sp-client-key")  # noqa: F821

    auth = ServicePrincipalAuthentication(
        tenant_id="a6c60f0f-76aa-4f80-8dba-092771d439f0",
        service_principal_id=sp_client_id,
        service_principal_password=sp_client_key,
    )

    ct_workspace = Workspace(
        subscription_id="c4f1c7f3-9206-409f-a333-5b89a516e5dd",
        resource_group="vdeep-ct-prod",
        workspace_name="continuous_training",
        auth=auth,
    )
    return ct_workspace


def get_or_create_databricks_session() -> SparkSession:  # pragma: no cover
    """Gets or creates a Databricks SparkSession.

    Returns:
        SparkSession: SparkSession created
    """
    return SparkSession.builder.getOrCreate()


def aml_dataset_to_dataframe(dataset: Dataset, workspace: Workspace) -> pd.DataFrame:  # pragma: no cover
    """Load a tabular Azure ML dataset as pandas dataframe.

    Usually that can be done with `dataset.to_pandas_dataframe()`,
    but somehow the service principal authentication does not work
    for accessing the blobstore inside the function. So instead,
    we have to extract the path on the blobstore from the dataset
    and directly download from the blobstore.

    This is a workaround until the service principal authentication
    works as expected.

    Args:
        dataset: A tabular Azure ML dataset.
        workspace: Workspace containing the dataset file.
            Can't be extracted from dataset because the auth object needs
            to be attached. Assumption is that the dataset is stored in the
            default datastore of the workspace.

    Returns:
        Dataset loaded as dataframe.
    """

    if not isinstance(dataset, TabularDataset):
        raise ValueError(f"Dataset must be a TabularDataset, but got type {type(dataset)}.")

    datastore = workspace.get_default_datastore()
    container_client = datastore.blob_service.get_container_client(datastore.container_name)
    # Note: This way of extracting the path might change with other azureml package versions
    path = dataset._dataflow._to_yaml_dict()["paths"][0]["pattern"].split("paths/")[1]

    blobclient = container_client.get_blob_client(path)
    blob_content = blobclient.download_blob().readall()
    return pd.read_csv(io.BytesIO(blob_content), dtype="string")


def aml_dataset_creation_time(dataset: Dataset, workspace: Workspace) -> datetime.datetime:  # pragma: no cover
    """Get the creation time of an Azure ML dataset.

    Args:
        dataset: A tabular Azure ML dataset.
        workspace: Workspace containing the dataset file.

    Return:
        Creation time of the dataset.
    """
    datastore = workspace.get_default_datastore()
    container_client = datastore.blob_service.get_container_client(datastore.container_name)
    # Note: This way of extracting the path might change with other azureml package versions
    path = dataset._dataflow._to_yaml_dict()["paths"][0]["pattern"].split("paths/")[1]
    blobclient = container_client.get_blob_client(path)

    return blobclient.get_blob_properties()["creation_time"]


def get_existing_dataset_versions(table: str, dataset_name: str, spark: SparkSession) -> list[int]:
    """Get the existing versions of the dataset in the table.

    Args:
        table: The table to check for existing versions.
            Must contain the columns `dataset_name` and `dataset_version`.
        dataset_name: The name of the dataset.
        spark: The SparkSession.

    Returns:
        The existing versions of the dataset or an empty array if the table does not exist.
    """
    if not spark.catalog.tableExists(table):
        return []

    return (
        spark.sql(f"SELECT DISTINCT dataset_version FROM {table} WHERE dataset_name = '{dataset_name}'")  # noqa: F821
        .toPandas()["dataset_version"]
        .tolist()
    )


def add_descriptions(
    table: str, column_descriptions: dict[str, str], table_description: str, spark: SparkSession
) -> None:
    """Add table and colun description to the table.

    Args:
        table: The table to add the descriptions to.
        column_descriptions: A dictionary with column names as keys and descriptions as values.
        table_description: The description of the table.
        spark: The SparkSession.
    """
    # Add table description
    spark.sql(f"ALTER TABLE {table} SET TBLPROPERTIES ('comment' = '{table_description}')")

    # Add column descriptions
    for column, description in column_descriptions.items():
        spark.sql(f"ALTER TABLE {table} ALTER COLUMN {column} COMMENT '{description}'")


def sync_dataset(dataset: DatasetInfo, spark: SparkSession, table: str, metadata_table: str) -> None:
    """Sync the dataset to the bronze layer.

    Existing versions are determined and only missing versions are synced to the bronze layer.
    """

    # Sync dataset content - bronze.azureml.training_datasets

    # Determine missing versions to sync
    latest_version = Dataset.get_by_name(dataset.workspace, dataset.dataset_name, version="latest").version

    def missing_versions(table: str) -> list[int]:
        existing_versions = get_existing_dataset_versions(table, dataset.dataset_name, spark)
        return [
            version
            for version in range(1, latest_version + 1)
            if version not in existing_versions and version not in dataset.skip_versions
        ]

    # Sync missing versions to the bronze layer
    for version in missing_versions(table):
        LOGGER.info(f"Syncing {dataset.dataset_name}:{version}")
        aml_dataset = Dataset.get_by_name(dataset.workspace, dataset.dataset_name, version=str(version))
        df = aml_dataset_to_dataframe(aml_dataset, dataset.workspace)

        # Find columns
        input_data_columns = df.columns[df.columns.str.startswith(dataset.input_data_id)].tolist()
        compressed_columns = df.columns[df.columns.str.startswith("compressed")].tolist()
        split_column = "split"
        task_columns = list(set(df.columns) - set(input_data_columns) - set(compressed_columns) - set(split_column))

        # Merge the input data columns into a single column
        df["input_data"] = df[input_data_columns].to_dict(orient="records")

        # Delete the merged input data columns and compressed_* columns
        df.drop(columns=input_data_columns + compressed_columns, inplace=True)

        # convert wide to long form
        id_vars = [split_column, "input_data"]  # columns that are not pivoted
        df = df.melt(id_vars=id_vars, value_vars=task_columns, value_name="label_path", var_name="task")
        # Drop rows with missing label paths introduced by the melt operation
        df = df.dropna(subset="label_path")

        df["dataset_name"] = dataset.dataset_name
        df["dataset_version"] = version

        # Write to databricks table
        psdf = spark.createDataFrame(df)  # noqa: F821
        psdf = psdf.repartition(1000)
        psdf.write.option("mergeSchema", "true").saveAsTable(table, mode="append")

        # Free memory
        psdf.unpersist()
        spark.catalog.clearCache()

    # Sync dataset metadata - bronze.azureml.training_datasets_metadata
    for version in missing_versions(metadata_table):
        aml_dataset = Dataset.get_by_name(dataset.workspace, dataset.dataset_name, version=str(version))
        creation_time = aml_dataset_creation_time(aml_dataset, dataset.workspace)

        df = pd.DataFrame(
            {"dataset_name": [dataset.dataset_name], "dataset_version": [version], "creation_time": [creation_time]}
        )

        # Write to databricks table
        psdf = spark.createDataFrame(df)  # noqa: F821
        psdf.write.saveAsTable(metadata_table, mode="append")

        # Free memory
        psdf.unpersist()
        spark.catalog.clearCache()


def main() -> None:
    """Run dataset registration."""
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "-e",
        "--env",
        choices=["dev", "qa", "prod"],
        help="Run in environment, ex. dev, qa, prod",
    )
    args = parser.parse_args()

    env_to_table = {
        "dev": "bronze_dev.azureml.training_datasets",
        "qa": "bronze_qa.azureml.training_datasets",
        "prod": "bronze.azureml.training_datasets",
    }
    table = env_to_table[args.env]
    metadata_table = f"{table}_metadata"

    # Add version to table name
    table_version = "v3"
    table = f"{table}_{table_version}"

    spark = get_or_create_databricks_session()
    spark.conf.set("spark.sql.execution.arrow.pyspark.enabled", "true")  # noqa: F821

    ct_workspace = get_ct_workspace()
    # input_data_id is the prefix or full name of the columns that contain the paths to the input data
    datasets = [
        DatasetInfo(
            dataset_name="PaceGeneralYUV", input_data_id="yuv420", workspace=ct_workspace, skip_versions=[50, 83]
        ),
        DatasetInfo(dataset_name="TopView_Multitask_YUV", input_data_id="yuv420", workspace=ct_workspace),
        DatasetInfo(dataset_name="PaceGeneralYUV-Japan", input_data_id="yuv420", workspace=ct_workspace),
        DatasetInfo(dataset_name="PaceGeneralYUVTrifocal", input_data_id="yuv420", workspace=ct_workspace),
        DatasetInfo(dataset_name="PaceGeneralYUVTrifocal-Japan", input_data_id="yuv420", workspace=ct_workspace),
    ]

    for dataset in datasets:
        sync_dataset(dataset, spark, table, metadata_table)

    # Add descriptions to the training_dataset table
    table_description = """
    This table contains transformed training datasets such as PaceGeneralYUV,
    PaceGeneral, TopView_Multitask, and TopView_Multitask_YUV originally in
    wide-form, now in long-form. It includes paths to images and label files,
    split type (train, val, test), and task type (e.g. traffic_light).
    Additionally, it includes identifiers for the dataset name and version.

    ### Changes in v3:

    - The `input_data` column now contains a dictionary with the input data columns as keys instead of a string
        - This change is to accommodate datasets with multiple input data columns, i.e. trifocal datasets
    - The `path` column has been renamed to `label_path`
    """
    column_descriptions = {
        "split": "Split type of the training data (train, val, test)",
        "input_data": "Dict with input data column(s) and path(s) to the frame",
        "task": "Associated task (e.g. lane, vehicle, vru, traffic_light, etc.)",
        "label_path": "Path to the label",
        "dataset_name": "Name of the dataset (e.g. PaceGeneralYUV)",
        "dataset_version": "Version of the dataset",
    }
    add_descriptions(table, column_descriptions, table_description, spark)
    # Add descriptions to the metadata table
    table_description = """This table contains information about when a training dataset and its
                        versions have been created."""
    column_descriptions = {
        "dataset_name": "Name of the dataset (e.g. PaceGeneralYUV)",
        "dataset_version": "Version of the dataset",
        "creation_time": "Creation time of the dataset according to its .csv file creation",
    }
    add_descriptions(metadata_table, column_descriptions, table_description, spark)

    # Add tags to assign table responsibilities
    for tbl in [table, metadata_table]:
        spark.sql(
            f"ALTER TABLE {tbl} SET TAGS ('responsible_domain'='Visual Perception', 'responsible_team'='Model Performance Evaluation', 'refresh_interval'='P1D')"
        )
        spark.catalog.refreshTable(tbl)

    LOGGER.info(f"Clustering and optimizing table: {table}")
    spark.sql(f"ALTER TABLE {table} CLUSTER BY (dataset_name, dataset_version)")
    spark.sql(f"OPTIMIZE {table}")


if __name__ == "__main__":
    main()
