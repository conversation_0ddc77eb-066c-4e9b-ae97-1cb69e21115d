variables:
  run_sp:
    default: 011157e5-a1ca-4053-a240-430e618a6b3a
    description: "run service principal"
  env:
    default: dev
    description: "environment, dev, qa, prod"
  pause_status:
    description: Is pipeline "PAUSED"/"UNPAUSED"
    default: "UNPAUSED"

resources:
  jobs:
    azureml_raw:
      # Give permission to all users to manage run
      permissions:
      - group_name: users
        level: CAN_MANAGE_RUN

      name: Sync Azure ML training dataset tables to raw tables.

      schedule:
        quartz_cron_expression: '0 17 1 * * ?'
        timezone_id: UTC
        pause_status: ${var.pause_status}

      tasks:
        - task_key: training_datasets
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/src/training_datasets.py
            parameters:
              - -e
              - ${var.env}
          job_cluster_key: azureml_job_cluster
          libraries:
            - pypi:
                package: azure-ai-ml==1.12.1
            - pypi:
                package: azure-core==1.30.1
            - pypi:
                package: azure-identity==1.16.0
            - pypi:
                package: azureml-core==1.56.0
            - pypi:
                package: azureml-dataset-runtime==1.56.0
            - pypi:
                package: msal-extensions==1.1.0
            - pypi:
                package: msal==1.28.0

      job_clusters:
        - job_cluster_key: azureml_job_cluster
          new_cluster:
            spark_version: 15.4.x-scala2.12
            num_workers: 0
            enable_elastic_disk: true
            node_type_id: Standard_E8ads_v5
            spark_conf:
              spark.master: local[*, 4]
              spark.databricks.cluster.profile: singleNode
              spark.dynamicAllocation.maxExecutors: 4
              spark.rpc.message.maxSize: 1024
              spark.dynamicAllocation.enabled: true
              spark.executor.cores: 4
              spark.dynamicAllocation.minExecutors: 1
            custom_tags:
              ResourceClass: SingleNode
            data_security_mode: SINGLE_USER
