variables:
  env:
    default: dev
    description: "environment, dev, prod"
  run_sp:
    description: "run service principal"
    lookup:
      service_principal: "sp-pace-dataloop-pacemetrics-ingest-${var.env}"
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"
  spark_version:
    default: "16.2.x-scala2.12"
    description: "Spark version"
  instance_pool_id:
    description: "Instance pool id (General purpose nodes)"
    lookup:
      instance_pool: "nonspot_E4ads_v5_rt15.4"
  num_workers:
    default: "0"
    description: "Number of workers"
  ms_teams_alert_channel_email:
    default: "<EMAIL>"
    description: "MS Teams alert channel email"
  storage_account:
    description: "Storage account name"
  resource_group:
    description: "Resource group name"
  subscription_id:
    description: "Subscription ID"
  tenant_id:
    default: "a6c60f0f-76aa-4f80-8dba-092771d439f0"
    description: "Tenant ID"
  target_catalog:
    description: "Target catalog name"
  target_schema:
    description: "Target schema name"
  trigger_processing_time:
    description: "Time to trigger processing"
  job_cluster:
    type: complex
    default:
      data_security_mode: SINGLE_USER
      instance_pool_id: ${var.instance_pool_id}
      spark_version: ${var.spark_version}
      num_workers:  ${var.num_workers}
      policy_id: ${var.job_cluster_policy_id}
      spark_env_vars:
        PYPI_TOKEN: "{{secrets/secrets/artifactory-user-token}}"
        PYPI_USER: "{{secrets/secrets/artifactory-user-username}}"
      spark_conf:
        "spark.databricks.cluster.profile": "singleNode"
        "spark.master": "local[*, 4]"
      custom_tags:
        "ResourceClass": "SingleNode"
