bundle:
  name: pacemetrics_ingest

include:
  - variables.yml
  - resources.yml

artifacts:
  default:
    type: whl
    build: uv build --wheel
    path: .

targets:
  dev:
    variables:
      env: dev
      ms_teams_alert_channel_email: <EMAIL> # Analytics Platform Alerts DEV
      storage_account: dbxingestzonedev
      resource_group: rg-pace-datalake-dev-weu
      subscription_id: a02fd6f2-785f-4560-8bbc-e2273324d1ad # sub-datadelivery-datalakehouse-001-dev
      target_catalog: bronze_dev
      target_schema: pace_metrics_v2
      trigger_processing_time: "10 minutes"
    mode: production
    default: true
    workspace:
      host: https://adb-***************.11.azuredatabricks.net
      root_path: /Jobs/ada_dataproduct_bronze/pacemetrics_ingest/${bundle.name}
    resources:
      jobs:
        pacemetrics_ingest_job:
          continuous:
            pause_status: "PAUSED" # Pause the job in DEV

  prod:
    variables:
      env: prod
      ms_teams_alert_channel_email: <EMAIL> # Analytics Platform Alerts PROD
      storage_account: dbxingestzoneprod
      resource_group: rg-pace-datalake-prod-weu
      subscription_id: ac76df14-c71e-433e-aa63-3c1c3371b54b # sub-datadelivery-datalakehouse-001-prd
      target_catalog: bronze
      target_schema: pace_metrics_v2
      trigger_processing_time: "10 minutes"
    mode: production
    default: true
    workspace:
      host: https://adb-****************.9.azuredatabricks.net
      root_path: /Jobs/ada_dataproduct_bronze/pacemetrics_ingest/${bundle.name}
    resources:
      jobs:
        pacemetrics_ingest_job:
          continuous:
            pause_status: "UNPAUSED"
