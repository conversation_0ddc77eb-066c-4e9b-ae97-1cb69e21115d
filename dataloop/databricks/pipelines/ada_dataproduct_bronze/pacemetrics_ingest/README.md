# pacemetrics_ingest

autoloader job to ingest data from `pacemetrics` and `webhooks` container into `bronze.pace_metrics_v2.

## Local Development

Install uv by astral:

```sh
curl -LsSf https://astral.sh/uv/install.sh | sh
```

Install python 3.10

```sh
uv python install 3.10
```

Set up the env:

```sh
uv sync --all-groups
```

Run the tests:

```sh
PYTHONPATH=. uv run --all-groups pytest tests
```
