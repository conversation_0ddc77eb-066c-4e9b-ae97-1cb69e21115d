resources:
  jobs:
    pacemetrics_ingest_job:
      name: "Pacemetrics Ingest"
      tags:
        responsible_domain: Data Delivery
        responsible_team: Analytics Platform
        product: pacemetrics ingestion
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
      run_as:
        service_principal_name: ${var.run_sp}
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
      tasks:
        - task_key: webhooks_ingest
          job_cluster_key: pacemetrics_ingest_job_cluster
          python_wheel_task:
            package_name: pacemetrics_ingest
            entry_point: pacemetrics-ingest
            parameters:
              - --source-container
              - "webhooks"
              - --target-catalog
              - ${var.target_catalog}
              - --target-schema
              - ${var.target_schema}
              - --trigger-processing-time
              - ${var.trigger_processing_time}
              - --subscription-id
              - ${var.subscription_id}
              - --storage-account
              - ${var.storage_account}
              - --resource-group
              - ${var.resource_group}
              - --tenant-id
              - ${var.tenant_id}
          libraries:
            - whl: ./dist/*.whl

        - task_key: pacemetrics_ingest
          job_cluster_key: pacemetrics_ingest_job_cluster
          python_wheel_task:
            package_name: pacemetrics_ingest
            entry_point: pacemetrics-ingest
            parameters:
              - --source-container
              - "pacemetrics"
              - --target-catalog
              - ${var.target_catalog}
              - --target-schema
              - ${var.target_schema}
              - --trigger-processing-time
              - ${var.trigger_processing_time}
              - --subscription-id
              - ${var.subscription_id}
              - --storage-account
              - ${var.storage_account}
              - --resource-group
              - ${var.resource_group}
              - --tenant-id
              - ${var.tenant_id}
          libraries:
            - whl: ./dist/*.whl

      job_clusters:
        - job_cluster_key: pacemetrics_ingest_job_cluster
          new_cluster: ${var.job_cluster}

    update_pacemetrics_tables:
      name: "Update Pacemetrics Tables"
      tags:
        responsible_domain: Data Delivery
        responsible_team: Analytics Platform
        product: pacemetrics ingestion
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
      run_as:
        service_principal_name: ${var.run_sp}
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
      schedule:
        quartz_cron_expression: 0 0 1 26 3 ? 2025 # run once
        timezone_id: Europe/Amsterdam

      tasks:
        - task_key: update_tables
          job_cluster_key: pacemetrics_update_tables_job_cluster
          python_wheel_task:
            package_name: pacemetrics_ingest
            entry_point: pacemetrics-update-tables
            parameters:
              - --target-catalog
              - ${var.target_catalog}
              - --target-schema
              - ${var.target_schema}
          libraries:
            - whl: ./dist/*.whl

      job_clusters:
        - job_cluster_key: pacemetrics_update_tables_job_cluster
          new_cluster: ${var.job_cluster}

    optimize_pacemetrics_tables:
      name: "Optimize Pacemetrics Tables"
      tags:
        responsible_domain: Data Delivery
        responsible_team: Analytics Platform
        product: pacemetrics ingestion
      permissions:
        - group_name: "sg-pace-github-Analytics_Platform-developer"
          level: CAN_MANAGE_RUN
      run_as:
        service_principal_name: ${var.run_sp}
      email_notifications:
        on_failure:
          - ${var.ms_teams_alert_channel_email}
      schedule:
        quartz_cron_expression: 0 0 23 * * ? # run every day at 11 PM
        timezone_id: Europe/Amsterdam

      tasks:
        - task_key: optimize_tables
          job_cluster_key: pacemetrics_optimize_tables_job_cluster
          python_wheel_task:
            package_name: pacemetrics_ingest
            entry_point: pacemetrics-optimize-tables
            parameters:
              - --target-catalog
              - ${var.target_catalog}
              - --target-schema
              - ${var.target_schema}
          libraries:
            - whl: ./dist/*.whl
          timeout_seconds: 2400 # 40 minutes
          health:
            rules:
              - metric: RUN_DURATION_SECONDS
                op: GREATER_THAN
                value: 600 # 10 minutes

      job_clusters:
        - job_cluster_key: pacemetrics_optimize_tables_job_cluster
          new_cluster: ${var.job_cluster}
