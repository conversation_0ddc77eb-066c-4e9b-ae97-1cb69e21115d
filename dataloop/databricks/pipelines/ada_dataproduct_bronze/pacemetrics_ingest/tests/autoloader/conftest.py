"""Conftest for autoloader tests."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import pytest
from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings


@pytest.fixture
def auth_credentials() -> AuthCredentials:
    """Auth credentials fixture."""
    return AuthCredentials(client_id="test_client_id", client_secret="test_client_secret")


@pytest.fixture
def azure_settings() -> AzureSettings:
    """Azure settings fixture."""
    return AzureSettings(
        storage_account_name="test_storage_account",
        subscription_id="test_subscription_id",
        resource_group="test_resource_group",
        tenant_id="test_tenant_id",
    )
