"""Tests for autoloader."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pacemetrics_ingest.autoloader.settings import PacemetricsAutoLoaderJobSettings, WebhooksAutoLoaderJobSettings
from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings


def test_webhooks_autoloader_job_settings(auth_credentials: AuthCredentials, azure_settings: AzureSettings) -> None:
    """Test WebhooksAutoLoaderJobSettings."""
    settings = WebhooksAutoLoaderJobSettings(
        container_name="webhooks",
        target_catalog="test_catalog",
        target_schema="test_schema",
        trigger_processing_time="30 minutes",
        auth_credentials=auth_credentials,
        azure_settings=azure_settings,
    )

    assert str(settings.table) == "test_catalog.test_schema.webhooks"
    assert settings.load_path == "abfss://webhooks@test_storage_account.dfs.core.windows.net/"
    assert settings.checkpoint_path == "/Volumes/test_catalog/test_schema/checkpoint_locations/webhooks"
    assert settings.read_stream_options == {
        "cloudFiles.backfillInterval": "1 day",
        "cloudFiles.clientId": "test_client_id",
        "cloudFiles.clientSecret": "test_client_secret",
        "cloudFiles.format": "json",
        "cloudFiles.maxFileAge": "90 days",
        "cloudFiles.resourceGroup": "test_resource_group",
        "cloudFiles.subscriptionId": "test_subscription_id",
        "cloudFiles.tenantId": "test_tenant_id",
        "cloudFiles.useNotifications": "true",
        "pathGlobfilter": "*.json",
    }
    assert settings.write_stream_options == {
        "checkpointLocation": "/Volumes/test_catalog/test_schema/checkpoint_locations/webhooks",
        "compression": "zstd",
    }
    assert settings.trigger == {"processingTime": "30 minutes"}
    assert (
        settings.schema
        == """
           event_type STRING,
           payload VARIANT,
           created_at TIMESTAMP,
           filename STRING,
           table_name STRING
        """
    )


def test_pacemetrics_autoloader_job_settings(auth_credentials: AuthCredentials, azure_settings: AzureSettings) -> None:
    """Test PacemetricsAutoLoaderJobSettings."""
    settings = PacemetricsAutoLoaderJobSettings(
        container_name="pacemetrics",
        target_catalog="test_catalog",
        target_schema="test_schema",
        trigger_processing_time="30 minutes",
        auth_credentials=auth_credentials,
        azure_settings=azure_settings,
    )

    assert str(settings.table) == "test_catalog.test_schema.pacemetrics"
    assert settings.table.table == "pacemetrics"
    assert settings.load_path == "abfss://pacemetrics@test_storage_account.dfs.core.windows.net/"
    assert settings.checkpoint_path == "/Volumes/test_catalog/test_schema/checkpoint_locations/pacemetrics"
    assert settings.read_stream_options == {
        "cloudFiles.backfillInterval": "1 day",
        "cloudFiles.clientId": "test_client_id",
        "cloudFiles.clientSecret": "test_client_secret",
        "cloudFiles.format": "text",
        "cloudFiles.maxFileAge": "90 days",
        "cloudFiles.resourceGroup": "test_resource_group",
        "cloudFiles.subscriptionId": "test_subscription_id",
        "cloudFiles.tenantId": "test_tenant_id",
        "cloudFiles.useNotifications": "true",
        "pathGlobfilter": "*.json",
        "wholeText": "true",
    }
    assert settings.write_stream_options == {
        "checkpointLocation": "/Volumes/test_catalog/test_schema/checkpoint_locations/pacemetrics",
        "compression": "zstd",
    }
    assert settings.trigger == {"processingTime": "30 minutes"}
    assert settings.schema is None
