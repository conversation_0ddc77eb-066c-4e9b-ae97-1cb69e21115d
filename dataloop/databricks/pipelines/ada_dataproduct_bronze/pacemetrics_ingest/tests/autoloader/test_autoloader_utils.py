"""Test for utils."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from unittest.mock import MagicMock

import pytest
from pacemetrics_ingest.autoloader.utils import apply_options
from pytest import LogCaptureFixture


@pytest.fixture
def mock_stream() -> MagicMock:
    """Mock stream for testing."""
    mock_stream = MagicMock()
    mock_stream.option.return_value = mock_stream
    return mock_stream


def test_apply_options_basic(mock_stream: MagicMock) -> None:
    """Test applying options to a stream."""

    # given
    options = {"format": "csv", "path": "/data/path", "maxFilesPerTrigger": 1}

    # when
    result = apply_options(mock_stream, options)

    # then
    assert result is mock_stream
    assert mock_stream.option.call_count == 3
    mock_stream.option.assert_any_call("format", "csv")
    mock_stream.option.assert_any_call("path", "/data/path")
    mock_stream.option.assert_any_call("maxFilesPerTrigger", 1)


def test_empty_options(mock_stream: MagicMock) -> None:
    """Test with empty options dictionary."""
    # given/when
    result = apply_options(mock_stream, {})

    # then
    assert result is mock_stream
    mock_stream.option.assert_not_called()


def test_client_secret_not_logged(mock_stream: MagicMock, caplog: LogCaptureFixture) -> None:
    """Test that client secrets are not logged."""

    # given
    caplog.set_level(logging.INFO)

    # Options with client secret
    options = {
        "format": "kafka",
        "clientSecret": "super-secret-value",
        "lowercasesecret": "another-secret",
        "normalOption": "visible-value",
    }

    # when
    apply_options(mock_stream, options)

    # then
    assert "Setting option format=kafka" in caplog.text
    assert "Setting option normalOption=visible-value" in caplog.text
    assert "clientSecret" not in caplog.text
    assert "super-secret-value" not in caplog.text
    assert "lowercasesecret" not in caplog.text
    assert "another-secret" not in caplog.text


def test_logging_capture(mock_stream: MagicMock, caplog: LogCaptureFixture) -> None:
    """Test that logging works correctly for normal options."""

    # given
    caplog.set_level(logging.INFO)
    options = {"format": "json", "path": "/data/path", "trigger": "10 seconds"}

    # when
    apply_options(mock_stream, options)

    # then
    assert "Setting option format=json" in caplog.text
    assert "Setting option path=/data/path" in caplog.text
    assert "Setting option trigger=10 seconds" in caplog.text
    assert len(caplog.records) == 3
