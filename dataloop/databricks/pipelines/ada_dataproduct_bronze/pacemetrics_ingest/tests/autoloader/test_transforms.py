"""Tests for transforms."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import json
from pathlib import Path

import pytest
from pacemetrics_ingest.autoloader.transforms import webhooks_transform
from pyspark.sql import SparkSession
from pyspark.sql.functions import lit
from pytest_mock import MockerFixture


@pytest.fixture
def spark() -> SparkSession:
    """Create a Spark session for testing."""
    return SparkSession.builder.master("local[1]").appName("WebhooksTransformTest").getOrCreate()


def test_webhooks_transform(spark: SparkSession, tmp_path: Path, mocker: MockerFixture) -> None:
    """Test the webhooks_transform function."""

    # patch the current_timestamp function
    mock_timestamp = mocker.patch("pacemetrics_ingest.autoloader.transforms.current_timestamp")
    mock_timestamp.return_value = lit("2025-01-01")

    # given a JSON file in a directory with some dummy data
    (tmp_path / "table_name").mkdir()
    json_file = tmp_path / "table_name" / "test.json"
    with json_file.open("w") as f:
        json.dump({"dummy": "value"}, f)

    # a DF with the JSON file
    df = spark.read.json(str(json_file))

    # when
    load_path_with_trailing_slash = str(tmp_path) + "/"
    result_df = webhooks_transform(load_path=load_path_with_trailing_slash, df=df)

    # then
    row = result_df.collect()[0]
    assert row.table_name == "table_name"
    assert row.created_at == "2025-01-01"
    assert row.filename == json_file.as_uri()
