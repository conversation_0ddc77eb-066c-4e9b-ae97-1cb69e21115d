"""Test for autoloader jobs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import functools
from unittest.mock import Mock

from pacemetrics_ingest.autoloader.job import AutoLoaderJob
from pacemetrics_ingest.autoloader.settings import PacemetricsAutoLoaderJobSettings, WebhooksAutoLoaderJobSettings
from pacemetrics_ingest.autoloader.transforms import webhooks_transform
from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings


def test_pacemetrics_job_str(auth_credentials: AuthCredentials, azure_settings: AzureSettings) -> None:
    """Test."""
    job_settings = PacemetricsAutoLoaderJobSettings(
        container_name="pacemetrics",
        target_catalog="test_catalog",
        target_schema="test_schema",
        trigger_processing_time="30 minutes",
        auth_credentials=auth_credentials,
        azure_settings=azure_settings,
    )
    job = AutoLoaderJob(
        job_settings=job_settings, transform=functools.partial(webhooks_transform, job_settings.load_path), spark=Mock()
    )

    assert str(job) == (
        "AutoLoaderJob Configuration:\n"
        "========================\n"
        "Source:\tabfss://pacemetrics@test_storage_account.dfs.core.windows.net/\n"
        "Target:\ttest_catalog.test_schema.pacemetrics\n"
        "========================\n"
        "Pipeline:\n"
        "spark\n"
        "\t.readStream\n"
        '\t.format("cloudFiles")\n'
        '\t.option("cloudFiles.backfillInterval", "1 day")\n'
        '\t.option("cloudFiles.maxFileAge", "90 days")\n'
        '\t.option("cloudFiles.useNotifications", "true")\n'
        '\t.option("cloudFiles.subscriptionId", "test_subscription_id")\n'
        '\t.option("cloudFiles.resourceGroup", "test_resource_group")\n'
        '\t.option("cloudFiles.tenantId", "test_tenant_id")\n'
        '\t.option("cloudFiles.clientId", "test_client_id")\n'
        '\t.option("cloudFiles.clientSecret", "********")\n'
        '\t.option("cloudFiles.format", "text")\n'
        '\t.option("wholeText", "true")\n'
        '\t.option("pathGlobfilter", "*.json")\n'
        '\t.load("abfss://pacemetrics@test_storage_account.dfs.core.windows.net/")\n'
        "\t.withColumn('created_at', current_timestamp())\n"
        "\t.withColumn('filename', input_file_name())\n"
        "\t.withColumn('table_name', regexp_extract(col('filename'), "
        "f'{load_path}/([^/]+)', 1))\n"
        "\t.writeStream\n"
        "\t.trigger(processingTime='30 minutes')\n"
        '\t.option("checkpointLocation", '
        '"/Volumes/test_catalog/test_schema/checkpoint_locations/pacemetrics")\n'
        '\t.option("compression", "zstd")\n'
        '\t.queryName("pacemetrics_ingest_pacemetrics")\n'
        '\t.toTable("test_catalog.test_schema.pacemetrics", format="delta", '
        'mode="append")\n'
        "========================"
    )


def test_webhooks_job_str(auth_credentials: AuthCredentials, azure_settings: AzureSettings) -> None:
    """Test."""
    job_settings = WebhooksAutoLoaderJobSettings(
        container_name="webhooks",
        target_catalog="test_catalog",
        target_schema="test_schema",
        trigger_processing_time="30 minutes",
        auth_credentials=auth_credentials,
        azure_settings=azure_settings,
    )
    job = AutoLoaderJob(
        job_settings=job_settings, transform=functools.partial(webhooks_transform, job_settings.load_path), spark=Mock()
    )

    assert str(job) == (
        "AutoLoaderJob Configuration:\n"
        "========================\n"
        "Source:\tabfss://webhooks@test_storage_account.dfs.core.windows.net/\n"
        "Target:\ttest_catalog.test_schema.webhooks\n"
        "========================\n"
        "Pipeline:\n"
        "spark\n"
        "\t.readStream\n"
        '\t.format("cloudFiles")\n'
        "\t.schema(\n"
        "           event_type STRING,\n"
        "           payload VARIANT,\n"
        "           created_at TIMESTAMP,\n"
        "           filename STRING,\n"
        "           table_name STRING\n"
        "        )\n"
        '\t.option("cloudFiles.backfillInterval", "1 day")\n'
        '\t.option("cloudFiles.maxFileAge", "90 days")\n'
        '\t.option("cloudFiles.useNotifications", "true")\n'
        '\t.option("cloudFiles.subscriptionId", "test_subscription_id")\n'
        '\t.option("cloudFiles.resourceGroup", "test_resource_group")\n'
        '\t.option("cloudFiles.tenantId", "test_tenant_id")\n'
        '\t.option("cloudFiles.clientId", "test_client_id")\n'
        '\t.option("cloudFiles.clientSecret", "********")\n'
        '\t.option("cloudFiles.format", "json")\n'
        '\t.option("pathGlobfilter", "*.json")\n'
        '\t.load("abfss://webhooks@test_storage_account.dfs.core.windows.net/")\n'
        "\t.withColumn('created_at', current_timestamp())\n"
        "\t.withColumn('filename', input_file_name())\n"
        "\t.withColumn('table_name', regexp_extract(col('filename'), "
        "f'{load_path}/([^/]+)', 1))\n"
        "\t.writeStream\n"
        "\t.trigger(processingTime='30 minutes')\n"
        '\t.option("checkpointLocation", '
        '"/Volumes/test_catalog/test_schema/checkpoint_locations/webhooks")\n'
        '\t.option("compression", "zstd")\n'
        '\t.queryName("pacemetrics_ingest_webhooks")\n'
        '\t.toTable("test_catalog.test_schema.webhooks", format="delta", '
        'mode="append")\n'
        "========================"
    )
