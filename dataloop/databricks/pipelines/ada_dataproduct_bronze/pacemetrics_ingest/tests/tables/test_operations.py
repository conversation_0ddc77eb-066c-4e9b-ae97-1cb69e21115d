"""Tests for table operations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from unittest.mock import Mock

import pytest
from dbxlib import FullSchemaName, FullTableName
from pacemetrics_ingest.tables.operations import add_clustering, optimize_table


@pytest.fixture
def mock_spark() -> Mock:
    """Fixture to create a mock Spark session."""
    spark = Mock()
    mock_result = Mock()
    mock_result.toJSON.return_value.collect.return_value = [b'{"some": "result"}']
    spark.sql.return_value = mock_result
    return spark


@pytest.fixture
def table() -> FullTableName:
    """Fixture to create a FullTableName."""
    return FullTableName(schema_full=FullSchemaName("catalog", "schema"), table="table")


def test_optimize_table_triggers_queries_full_true(mock_spark: Mock, table: FullTableName) -> None:
    """Test that optimize_table triggers the correct queries when full is True."""

    columns = ["col1", "col2"]

    optimize_table(table, mock_spark, columns, full=True)

    expected_queries = [
        "OPTIMIZE catalog.schema.table FULL",
        "VACUUM catalog.schema.table",
        "ANALYZE TABLE catalog.schema.table COMPUTE STATISTICS FOR COLUMNS col1, col2",
    ]

    actual_queries = [call_args[0][0] for call_args in mock_spark.sql.call_args_list]

    assert actual_queries == expected_queries


def test_optimize_table_triggers_queries_full_false(mock_spark: Mock, table: FullTableName) -> None:
    """Test that optimize_table triggers the correct queries when full is False."""
    columns = ["col1", "col2"]

    mock_spark.reset_mock()
    optimize_table(table, mock_spark, columns, full=False)

    expected_queries = [
        "OPTIMIZE catalog.schema.table ",
        "VACUUM catalog.schema.table",
        "ANALYZE TABLE catalog.schema.table COMPUTE STATISTICS FOR COLUMNS col1, col2",
    ]

    actual_queries = [call_args[0][0] for call_args in mock_spark.sql.call_args_list]

    assert actual_queries == expected_queries


def test_add_clustering_triggers_query(mock_spark: Mock, table: FullTableName) -> None:
    """Test that add_clustering triggers the correct query."""
    columns = ["col1", "col2"]

    add_clustering(table, mock_spark, columns)

    mock_spark.sql.assert_called_once_with("ALTER TABLE catalog.schema.table CLUSTER BY (col1, col2)")
