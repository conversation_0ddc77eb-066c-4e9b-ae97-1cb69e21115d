"""Tests for main."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from unittest.mock import MagicMock

import pytest
from pacemetrics_ingest.autoloader.settings import PacemetricsAutoLoaderJobSettings, WebhooksAutoLoaderJobSettings
from pacemetrics_ingest.autoloader_main import app
from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings
from pytest_mock import MockerFixture
from typer.testing import CliRunner, Result

runner: CliRunner = CliRunner()


@pytest.fixture(autouse=True)
def mock_auth_credentials(mocker: MockerFixture) -> None:
    """Mock the AuthCredentials.from_scope method."""
    mocker.patch(
        "pacemetrics_ingest.service_access.auth.AuthCredentials.from_scope",
        return_value=AuthCredentials(client_id="test_client_id", client_secret="test_client_secret"),
    )


def test_main_webhooks(mocker: MockerFixture) -> None:
    """Test main function with webhooks container."""
    mock_auto_loader_job: MagicMock = mocker.patch("pacemetrics_ingest.autoloader_main.AutoLoaderJob")
    result: Result = runner.invoke(
        app,
        [
            "--source-container",
            "webhooks",
            "--target-catalog",
            "bronze",
            "--target-schema",
            "test_schema",
            "--trigger-processing-time",
            "30 minutes",
            "--subscription-id",
            "a02fd6f2-785f-4560-8bbc-e2273324d1ad",
            "--storage-account",
            "dbxingestzonedev",
            "--resource-group",
            "rg-pace-datalake-dev-weu",
        ],
    )
    assert result.exit_code == 0, result.output
    mock_auto_loader_job.assert_called_once()
    settings = mock_auto_loader_job.call_args[1]["job_settings"]

    assert settings == WebhooksAutoLoaderJobSettings(
        container_name="webhooks",
        target_catalog="bronze",
        target_schema="test_schema",
        trigger_processing_time="30 minutes",
        auth_credentials=AuthCredentials(client_id="test_client_id", client_secret="test_client_secret"),
        azure_settings=AzureSettings(
            storage_account_name="dbxingestzonedev",
            subscription_id="a02fd6f2-785f-4560-8bbc-e2273324d1ad",
            resource_group="rg-pace-datalake-dev-weu",
            tenant_id="a6c60f0f-76aa-4f80-8dba-092771d439f0",
        ),
    )

    assert callable(mock_auto_loader_job.call_args[1]["transform"])
    mock_auto_loader_job.return_value.run.assert_called_once()


def test_main_pacemetrics(mocker: MockerFixture) -> None:
    """Test main function with pacemetrics container."""
    mock_auto_loader_job: MagicMock = mocker.patch("pacemetrics_ingest.autoloader_main.AutoLoaderJob")
    result: Result = runner.invoke(
        app,
        [
            "--source-container",
            "pacemetrics",
            "--target-catalog",
            "bronze",
            "--target-schema",
            "test_schema",
            "--trigger-processing-time",
            "30 minutes",
            "--subscription-id",
            "a02fd6f2-785f-4560-8bbc-e2273324d1ad",
            "--storage-account",
            "dbxingestzonedev",
            "--resource-group",
            "rg-pace-datalake-dev-weu",
        ],
    )
    assert result.exit_code == 0, result.output
    mock_auto_loader_job.assert_called_once()
    settings = mock_auto_loader_job.call_args[1]["job_settings"]
    assert settings == PacemetricsAutoLoaderJobSettings(
        container_name="pacemetrics",
        target_catalog="bronze",
        target_schema="test_schema",
        trigger_processing_time="30 minutes",
        auth_credentials=AuthCredentials(client_id="test_client_id", client_secret="test_client_secret"),
        azure_settings=AzureSettings(
            storage_account_name="dbxingestzonedev",
            subscription_id="a02fd6f2-785f-4560-8bbc-e2273324d1ad",
            resource_group="rg-pace-datalake-dev-weu",
            tenant_id="a6c60f0f-76aa-4f80-8dba-092771d439f0",
        ),
    )
    assert callable(mock_auto_loader_job.call_args[1]["transform"])
    mock_auto_loader_job.return_value.run.assert_called_once()
