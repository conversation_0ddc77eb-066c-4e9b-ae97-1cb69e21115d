"""Tests for the update_tables_main module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Generator
from unittest.mock import Mock, patch

import pytest
from dbxlib import FullSchemaName, FullTableName
from pacemetrics_ingest.update_tables_main import app
from typer.testing import CliRunner


@pytest.fixture
def runner() -> CliRunner:
    """Fixture for invoking the CLI."""
    return CliRunner()


@pytest.fixture
def mock_update_table_metadata() -> Generator[Mock, None, None]:
    """Fixture for mocking the update_table_metadata function."""
    with patch("pacemetrics_ingest.update_tables_main.SparkSession.builder") as mock:
        yield mock


@patch("pacemetrics_ingest.update_tables_main.SparkSession.builder")
@patch("pacemetrics_ingest.update_tables_main.update_table_metadata")
@patch("pacemetrics_ingest.update_tables_main.add_clustering")
@patch("pacemetrics_ingest.update_tables_main.optimize_table")
def test_update_tables_main(
    mock_optimize_table: Mock,
    mock_add_clustering: Mock,
    mock_update_table_metadata: Mock,
    mock_spark_session_builder: Mock,
    runner: CliRunner,
) -> None:
    """Test the update_tables_main CLI command."""

    mock_spark_session = Mock()
    mock_spark_session_builder.getOrCreate.return_value = mock_spark_session

    # given/when
    result = runner.invoke(app, ["--target-catalog", "test_catalog", "--target-schema", "test_schema"])

    # then the cli command should succeed
    assert result.exit_code == 0, result.output

    # then the call to update table are correct
    # (I am only interested in checking that the function is called with the correct arguments,
    # because the function that does the update itself is a library function, tested in the library)
    assert mock_update_table_metadata.call_count == 2

    mock_update_table_metadata.assert_any_call(
        table=FullTableName(schema_full=FullSchemaName("test_catalog", "test_schema"), table="pacemetrics"),
        description="This table contains the raw ingested data of the pacemetrics.",
        responsible_team="Analytics Platform",
        responsible_domain="Data Delivery",
        refresh_interval="P10M",
        column_descriptions={
            "payload": "Content of the json file, in variant type.",
            "created_at": "Timestamp of when the row was added to the table.",
            "filename": "Name of json file uploaded by pacemetrics.",
            "table_name": "Pacemetrics table name as defined by the location of the file in storage, e.g.: ArtifactoryBill",  # noqa E501
        },
        column_timesystems={"created_at": "UTC"},
    )
    mock_update_table_metadata.assert_any_call(
        table=FullTableName(schema_full=FullSchemaName("test_catalog", "test_schema"), table="webhooks"),
        description="This table contains the raw ingested data of the webhooks.",
        responsible_team="Analytics Platform",
        responsible_domain="Data Delivery",
        refresh_interval="P10M",
        column_descriptions={
            "event_type": "Type of event associated with the webhook.",
            "payload": "Content of the json file, in variant type.",
            "created_at": "Timestamp of when the row was added to the table.",
            "filename": "Name of json file uploaded by webhooks.",
            "table_name": "Webhooks table name as defined by the location of the file in storage, e.g.: demo-github",  # noqa E501
        },
        column_timesystems={"created_at": "UTC"},
    )

    mock_add_clustering.assert_any_call(
        table=FullTableName(schema_full=FullSchemaName("test_catalog", "test_schema"), table="pacemetrics"),
        spark_session=mock_spark_session,
        columns=["created_at", "table_name"],
    )

    mock_add_clustering.assert_any_call(
        table=FullTableName(schema_full=FullSchemaName("test_catalog", "test_schema"), table="webhooks"),
        spark_session=mock_spark_session,
        columns=["created_at", "table_name"],
    )

    mock_optimize_table.assert_any_call(
        table=FullTableName(schema_full=FullSchemaName("test_catalog", "test_schema"), table="pacemetrics"),
        spark_session=mock_spark_session,
        columns=["created_at", "table_name"],
        full=True,
    )

    mock_optimize_table.assert_any_call(
        table=FullTableName(schema_full=FullSchemaName("test_catalog", "test_schema"), table="webhooks"),
        spark_session=mock_spark_session,
        columns=["created_at", "table_name"],
        full=True,
    )
