"""Test the utils module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import MagicMock

from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings
from pacemetrics_ingest.utils.utils import configure_az_storage
from pyspark.sql import SparkSession


def test_configure_az_storage() -> None:
    """Test the configure_az_storage function."""

    # given
    spark = MagicMock(spec=SparkSession)
    azure_settings = AzureSettings(
        subscription_id="subscription_id",
        storage_account_name="test_account",
        resource_group="test_rg",
        tenant_id="test_tenant",
    )
    auth_credentials = AuthCredentials(client_id="test_client_id", client_secret="test_client_secret")

    configure_az_storage(spark, azure_settings, auth_credentials)

    spark.conf.set.assert_any_call("fs.azure.account.auth.type.test_account.dfs.core.windows.net", "OAuth")
    spark.conf.set.assert_any_call(
        "fs.azure.account.oauth.provider.type.test_account.dfs.core.windows.net",
        "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider",
    )
    spark.conf.set.assert_any_call(
        "fs.azure.account.oauth2.client.id.test_account.dfs.core.windows.net", "test_client_id"
    )
    spark.conf.set.assert_any_call(
        "fs.azure.account.oauth2.client.secret.test_account.dfs.core.windows.net", "test_client_secret"
    )
    spark.conf.set.assert_any_call(
        "fs.azure.account.oauth2.client.endpoint.test_account.dfs.core.windows.net",
        "https://login.microsoftonline.com/test_tenant/oauth2/token",
    )
