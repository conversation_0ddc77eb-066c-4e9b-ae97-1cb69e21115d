[project]
name = "pacemetrics-ingest"
version = "0.1.0"
description = "autoloader job for pacemetrics data"
readme = "README.md"
authors = [
    { name = "<PERSON><PERSON><PERSON>", email = "<EMAIL>" }
]
requires-python = ">=3.10"
dependencies = [
    "dbxlib[spark]~=0.4",
    "pyspark>=3.5.4",
    "typer>=0.15.1",
]

[project.scripts]
pacemetrics-ingest = "pacemetrics_ingest.autoloader_main:run_main"
pacemetrics-update-tables = "pacemetrics_ingest.update_tables_main:run_main"
pacemetrics-optimize-tables = "pacemetrics_ingest.optimize_tables_main:run_main"


[project.optional-dependencies]
testing = [
    "delta-spark>=3.2.0",
    "pytest>=8.3.4",
    "pytest-mock>=3.14.0",
    "pytest-cov>=6.0.0",
]

[build-system]
requires = ["setuptools"]
build-backend = "setuptools.build_meta"

[dependency-groups]
dev = [
    "black>=25.1.0",
]

testing = [
    "delta-spark>=3.2.0",
    "pytest>=8.3.4",
    "pytest-mock>=3.14.0",
    "pytest-cov>=6.0.0",
]

[tool.coverage.run]
omit = [
    "tests/*"
]
