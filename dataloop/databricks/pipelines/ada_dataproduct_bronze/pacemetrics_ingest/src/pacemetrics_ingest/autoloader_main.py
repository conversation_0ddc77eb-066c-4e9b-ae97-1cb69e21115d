"""Pacemetrics Ingest Main."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import functools
import logging
from typing import Annotated

import typer
from pacemetrics_ingest.autoloader.job import AutoLoaderJob
from pacemetrics_ingest.autoloader.settings import (
    BaseAutoLoaderJobSettings,
    PacemetricsAutoLoaderJobSettings,
    WebhooksAutoLoaderJobSettings,
)
from pacemetrics_ingest.autoloader.transforms import pacemetrics_transform, webhooks_transform
from pacemetrics_ingest.models.models import SourceContainer
from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings

app = typer.Typer()


@app.command()
def main(
    source_container: Annotated[
        SourceContainer,
        typer.Option(help="Container where the data to be ingest is located. For example, webhooks"),
    ],
    target_catalog: Annotated[str, typer.Option(help="Catalog where the data will be ingested to")],
    target_schema: Annotated[str, typer.Option(help="Schema where the data will be ingested to")],
    trigger_processing_time: Annotated[str, typer.Option(help="Time to trigger the processing")],
    subscription_id: Annotated[str, typer.Option(help="Azure subscription ID")],
    storage_account: Annotated[str, typer.Option(help="Azure storage account name")],
    resource_group: Annotated[str, typer.Option(help="Azure resource group")],
    tenant_id: Annotated[str, typer.Option(help="Azure tenant ID")] = "a6c60f0f-76aa-4f80-8dba-092771d439f0",
) -> None:
    """Main function."""

    logging.basicConfig(level=logging.INFO)

    auth_credentials = AuthCredentials.from_scope("pacemetrics-ingest")
    azure_environment_settings = AzureSettings(
        subscription_id=subscription_id,
        resource_group=resource_group,
        storage_account_name=storage_account,
        tenant_id=tenant_id,
    )

    settings: BaseAutoLoaderJobSettings
    match source_container:
        case SourceContainer.WEBHOOKS:
            settings = WebhooksAutoLoaderJobSettings(
                container_name=source_container.value,
                target_catalog=target_catalog,
                target_schema=target_schema,
                trigger_processing_time=trigger_processing_time,
                auth_credentials=auth_credentials,
                azure_settings=azure_environment_settings,
            )
            transform = functools.partial(webhooks_transform, settings.load_path)
        case SourceContainer.PACEMETRICS:
            settings = PacemetricsAutoLoaderJobSettings(
                container_name=source_container.value,
                target_catalog=target_catalog,
                target_schema=target_schema,
                trigger_processing_time=trigger_processing_time,
                auth_credentials=auth_credentials,
                azure_settings=azure_environment_settings,
            )
            transform = functools.partial(pacemetrics_transform, settings.load_path)
        case _:
            raise ValueError(f"Unknown source container: {source_container}")

    job = AutoLoaderJob(job_settings=settings, transform=transform)
    logging.info(f"Running job:\n{job}")
    job.run()


def run_main() -> None:
    """Run main."""
    # using typer with databricks has its challenges
    # for some reason, even when typer exits with a zero code
    # it still captures it as an exception and detects it as a error in running
    # the cli

    # by running as standalone_mode=False, we can catch the exit code and
    # only raise an exception if the exit code is not zero
    # all other exceptions will still trigger cli error as usual
    exit_value = app(standalone_mode=False)
    if exit_value:
        raise typer.Exit(code=exit_value)


if __name__ == "__main__":
    run_main()
