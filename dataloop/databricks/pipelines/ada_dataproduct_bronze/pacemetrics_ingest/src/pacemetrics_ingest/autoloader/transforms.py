"""Transforms for the pacemetrics_ingest autoloader."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql import DataFrame
from pyspark.sql.functions import col, current_timestamp, from_json, input_file_name, regexp_extract


def webhooks_transform(load_path: str, df: DataFrame) -> DataFrame:
    """Transform the Webhooks stream."""
    load_path = load_path.rstrip("/")
    return (
        df.withColumn("created_at", current_timestamp())
        .withColumn("filename", input_file_name())
        .withColumn(
            "table_name",
            regexp_extract(col("filename"), f"{load_path}/([^/]+)", 1),
        )
    )


def pacemetrics_transform(load_path: str, df: DataFrame) -> DataFrame:
    """Transform the Pacemetrics stream."""
    load_path = load_path.rstrip("/")
    return (
        df.withColumnRenamed("value", "payload")
        .withColumn("payload", from_json(col("payload"), "variant"))
        .withColumn("created_at", current_timestamp())
        .withColumn("filename", input_file_name())
        .withColumn("table_name", regexp_extract(col("filename"), f"{load_path}/([^/]+)", 1))
    )
