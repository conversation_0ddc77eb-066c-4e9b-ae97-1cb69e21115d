"""Utils for autoloader job."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from typing import Any, overload

from pyspark.sql.streaming import DataStreamReader, DataStreamWriter

logger = logging.getLogger(__name__)


@overload
def apply_options(stream: DataStreamReader, options: dict[str, Any]) -> DataStreamReader: ...  # noqa: E704


@overload
def apply_options(stream: DataStreamWriter, options: dict[str, Any]) -> DataStreamWriter: ...  # noqa: E704


def apply_options(stream, options):  # type: ignore # type is fully defined above
    """Apply the options to the stream."""
    for key, value in options.items():
        if "secret" not in key.lower():
            logger.info("Setting option %s=%s", key, value)
        stream = stream.option(key, value)
    return stream
