"""AutoLoader job for Pacemetrics."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from collections.abc import Callable
from dataclasses import dataclass

from pacemetrics_ingest.autoloader.settings import IAutoLoaderJobSettings
from pacemetrics_ingest.autoloader.utils import apply_options
from pacemetrics_ingest.utils.utils import configure_az_storage, get_return_statement
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.streaming import StreamingQuery

logger = logging.getLogger(__name__)


@dataclass
class AutoLoaderJob:
    """Autoloader job for the Pacemetrics Ingest pipeline."""

    job_settings: IAutoLoaderJobSettings
    transform: Callable[[DataFrame], DataFrame] | None = None
    spark: SparkSession | None = None

    def __post_init__(self) -> None:
        """Post Initialization."""
        if self.spark is None:
            self.spark = (
                SparkSession.builder.appName("Pacemetrics Ingest")
                .config("spark.sql.legacy.createHiveTableByDefault", "false")
                .config("spark.sql.files.ignoreMissingFiles", "true")
                .getOrCreate()
            )

    def run(self) -> None:
        """Run the autoloader job."""
        configure_az_storage(
            self.spark, self.job_settings.azure_settings, self.job_settings.auth_credentials  # type: ignore # mypy doesn't understand __post_init__ # noqa: E501
        )
        self._write_stream(self._read_stream()).awaitTermination()

    def _read_stream(self) -> DataFrame:
        """Read the stream from the source container."""
        stream = self.spark.readStream.format("cloudFiles")  # type: ignore # mypy doesn't understand __post_init__

        if schema := self.job_settings.schema:
            logger.info("Using schema: %s", schema)
            stream = stream.schema(schema)

        stream = apply_options(stream, self.job_settings.read_stream_options)

        if self.transform:
            logger.info("Applying transform function %s", repr(self.transform))
            return self.transform(stream.load(self.job_settings.load_path))

        logger.info("Loading data from %s", self.job_settings.load_path)
        return stream.load(self.job_settings.load_path)

    def _write_stream(self, df: DataFrame) -> StreamingQuery:
        """Write the stream to the target table."""
        logger.info("Using trigger %s", self.job_settings.trigger)
        stream = df.writeStream.trigger(**self.job_settings.trigger)
        stream = apply_options(stream, self.job_settings.write_stream_options)
        stream.queryName(f"pacemetrics_ingest_{self.job_settings.table.table}")
        logger.info("Writing stream to %s", self.job_settings.table)
        return stream.toTable(str(self.job_settings.table), format="delta", mode="append")

    def __str__(self) -> str:
        """String representation of the AutoLoaderJob showing the full pipeline configuration."""
        try:
            read_stream_str = 'spark\n\t.readStream\n\t.format("cloudFiles")'

            # Add schema if present
            if schema := self.job_settings.schema:
                read_stream_str += f"\n\t.schema({schema})"

            # Add read stream options
            for key, value in self.job_settings.read_stream_options.items():
                if "secret" in key.lower():
                    value = "********"
                read_stream_str += f'\n\t.option("{key}", "{value}")'

            # Add load path
            read_stream_str += f'\n\t.load("{self.job_settings.load_path}")'

            # Add transform if present
            transform_str = ""
            if self.transform:
                transform_str = get_return_statement(self.transform)
                transform_str = transform_str.replace("df", "")
                transform_str = transform_str.replace(".", "\n\t.")

            # Build the write stream portion
            write_stream_str = "\n\t.writeStream"

            # Add trigger
            trigger_args = ", ".join(f"{k}={repr(v)}" for k, v in self.job_settings.trigger.items())
            write_stream_str += f"\n\t.trigger({trigger_args})"

            # Add write stream options
            for key, value in self.job_settings.write_stream_options.items():
                write_stream_str += f'\n\t.option("{key}", "{value}")'

            # Add query name
            write_stream_str += f'\n\t.queryName("pacemetrics_ingest_{self.job_settings.table.table}")'

            # Add destination table
            write_stream_str += f'\n\t.toTable("{self.job_settings.table}", format="delta", mode="append")'

            return (
                f"AutoLoaderJob Configuration:\n"
                f"========================\n"
                f"Source:\t{self.job_settings.load_path}\n"
                f"Target:\t{self.job_settings.table}\n"
                f"========================\n"
                f"Pipeline:\n{read_stream_str}{transform_str}{write_stream_str}\n"
                f"========================"
            )
        except Exception as e:
            return f"Error generating job configuration: {str(e)}"
