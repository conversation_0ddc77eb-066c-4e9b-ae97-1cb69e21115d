"""Settings for the Autoloader jobs."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from abc import ABC, abstractmethod
from dataclasses import dataclass
from functools import cached_property
from typing import Any

from dbxlib import FullSchemaName, FullTableName
from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings


class IAutoLoaderJobSettings(ABC):
    """Interface for Autoloader job settings."""

    azure_settings: AzureSettings
    auth_credentials: AuthCredentials

    @cached_property
    @abstractmethod
    def table(self) -> FullTableName:
        """Table where the data will be written."""
        raise NotImplementedError

    @property
    @abstractmethod
    def load_path(self) -> str:
        """Path to the source container."""
        raise NotImplementedError

    @property
    @abstractmethod
    def checkpoint_path(self) -> str:
        """Path to the checkpoint location."""
        raise NotImplementedError

    @property
    @abstractmethod
    def read_stream_options(self) -> dict[str, Any]:
        """Options for reading the stream."""
        raise NotImplementedError

    @property
    @abstractmethod
    def write_stream_options(self) -> dict[str, Any]:
        """Options for writing the stream."""
        raise NotImplementedError

    @property
    @abstractmethod
    def trigger(self) -> dict[str, Any]:
        """Trigger for the stream."""
        raise NotImplementedError

    @property
    @abstractmethod
    def schema(self) -> str | None:
        """Schema for the stream."""
        raise NotImplementedError


@dataclass
class BaseAutoLoaderJobSettings(IAutoLoaderJobSettings):
    """Base Autoloader job settings."""

    container_name: str
    target_catalog: str
    target_schema: str
    trigger_processing_time: str
    auth_credentials: AuthCredentials
    azure_settings: AzureSettings

    @property
    def table(self) -> FullTableName:
        """Table where the data will be written."""
        schema = FullSchemaName(self.target_catalog, self.target_schema)
        return FullTableName(schema, self.container_name)

    @property
    def load_path(self) -> str:
        """Path to the source container."""
        return f"abfss://{self.container_name}@{self.azure_settings.storage_account_name}.dfs.core.windows.net/"

    @property
    def checkpoint_path(self) -> str:
        """Path to the checkpoint location."""
        return f"/Volumes/{self.table.catalog}/{self.table.schema}/checkpoint_locations/{self.table.table}"

    @property
    def read_stream_options(self) -> dict[str, Any]:
        """Options for reading the stream."""
        return {
            "cloudFiles.backfillInterval": "1 day",
            "cloudFiles.maxFileAge": "90 days",
            "cloudFiles.useNotifications": "true",
            "cloudFiles.subscriptionId": self.azure_settings.subscription_id,
            "cloudFiles.resourceGroup": self.azure_settings.resource_group,
            "cloudFiles.tenantId": self.azure_settings.tenant_id,
            "cloudFiles.clientId": self.auth_credentials.client_id,
            "cloudFiles.clientSecret": self.auth_credentials.client_secret,
        }

    @property
    def write_stream_options(self) -> dict[str, Any]:
        """Options for writing the stream."""
        return {"checkpointLocation": self.checkpoint_path, "compression": "zstd"}

    @property
    def trigger(self) -> dict[str, Any]:
        """Trigger for the stream."""
        return {"processingTime": self.trigger_processing_time}

    @property
    @abstractmethod
    def schema(self) -> str | None:
        """Schema for the stream."""
        raise NotImplementedError("Schema must be implemented in the subclass.")


@dataclass
class WebhooksAutoLoaderJobSettings(BaseAutoLoaderJobSettings):
    """Autoloader job settings for the Webhooks container."""

    @property
    def read_stream_options(self) -> dict[str, Any]:
        """Options for reading the stream."""
        base_options = super().read_stream_options
        webhook_options = {
            "cloudFiles.format": "json",
            "pathGlobfilter": "*.json",
        }

        return base_options | webhook_options

    @property
    def schema(self) -> str | None:
        """Schema for the stream."""
        return """
           event_type STRING,
           payload VARIANT,
           created_at TIMESTAMP,
           filename STRING,
           table_name STRING
        """


@dataclass
class PacemetricsAutoLoaderJobSettings(BaseAutoLoaderJobSettings):
    """Autoloader job settings for the Pacemetrics container."""

    @property
    def read_stream_options(self) -> dict[str, Any]:
        """Options for reading the stream."""
        base_options = super().read_stream_options
        pacemetrics_options = {
            "cloudFiles.format": "text",
            "wholeText": "true",
            "pathGlobfilter": "*.json",
        }
        return base_options | pacemetrics_options

    @property
    def schema(self) -> str | None:
        """Schema for the stream."""
        return None
