"""Pacemetrics Optimize Tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from typing import Annotated

import typer
from dbxlib import FullSchemaName, FullTableName
from pacemetrics_ingest.models.models import SourceContainer
from pacemetrics_ingest.tables.operations import optimize_table
from pyspark.sql import SparkSession

logger = logging.getLogger(__name__)


def optimize_tables(tables: list[FullTableName], spark_session: SparkSession) -> None:
    """Optimize tables."""

    for table in tables:
        optimize_table(table, spark_session, columns=["created_at", "table_name"], full=False)


app = typer.Typer()


@app.command()
def main(
    target_catalog: Annotated[str, typer.Option(help="Catalog where the data will be ingested to")],
    target_schema: Annotated[str, typer.Option(help="Schema where the data will be ingested to")],
    debug: Annotated[bool, typer.Option(help="Debug mode")] = False,
) -> None:
    """Main function."""

    logging.basicConfig(level=logging.DEBUG if debug else logging.INFO)

    spark_session = SparkSession.builder.getOrCreate()
    schema = FullSchemaName(target_catalog, target_schema)
    tables = [FullTableName(schema_full=schema, table=table_name.value) for table_name in SourceContainer]
    try:
        optimize_tables(tables, spark_session)
    except Exception as e:
        logger.error(f"Error optimizing tables: {e}")
        raise Exception(f"Error optimizing tables: {e}") from e


def run_main() -> None:
    """Run main."""
    # using typer with databricks has its challenges
    # for some reason, even when typer exits with a zero code
    # it still captures it as an exception and detects it as a error in running
    # the cli

    # by running as standalone_mode=False, we can catch the exit code and
    # only raise an exception if the exit code is not zero
    # all other exceptions will still trigger cli error as usual
    exit_value = app(standalone_mode=False)
    if exit_value:
        raise typer.Exit(code=exit_value)


if __name__ == "__main__":
    run_main()
