"""General utils for spark."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import ast
import inspect
from functools import partial
from typing import Callable, TypeVar

from pacemetrics_ingest.service_access.auth import AuthCredentials
from pacemetrics_ingest.service_access.azure import AzureSettings
from pyspark.sql import SparkSession


def configure_az_storage(
    spark: SparkSession,
    azure_settings: AzureSettings,
    auth_credentials: AuthCredentials,
) -> None:
    """Gives spark access to the storage account."""

    storage_account_name = azure_settings.storage_account_name
    tenant_id = azure_settings.tenant_id
    spark.conf.set(
        f"fs.azure.account.auth.type.{storage_account_name}.dfs.core.windows.net",
        "OAuth",
    )
    spark.conf.set(
        f"fs.azure.account.oauth.provider.type.{storage_account_name}.dfs.core.windows.net",
        "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider",
    )
    spark.conf.set(
        f"fs.azure.account.oauth2.client.id.{storage_account_name}.dfs.core.windows.net",
        auth_credentials.client_id,
    )
    spark.conf.set(
        f"fs.azure.account.oauth2.client.secret.{storage_account_name}.dfs.core.windows.net",
        auth_credentials.client_secret,
    )
    spark.conf.set(
        f"fs.azure.account.oauth2.client.endpoint.{storage_account_name}.dfs.core.windows.net",
        f"https://login.microsoftonline.com/{tenant_id}/oauth2/token",
    )


T = TypeVar("T")


def get_return_statement(func: Callable[..., T]) -> str:
    """Get the return statement of a function."""
    if isinstance(func, partial):
        func = func.func
    source = inspect.getsource(func)
    tree = ast.parse(source)
    for node in ast.walk(tree):
        if isinstance(node, ast.Return):
            return ast.unparse(node.value)  # type: ignore

    return ""
