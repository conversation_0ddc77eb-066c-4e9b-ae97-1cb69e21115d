"""Table operations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import json
import logging
from pprint import pformat

from dbxlib import FullTableName
from pyspark.sql import SparkSession

logger = logging.getLogger(__name__)


def optimize_table(table: FullTableName, spark_session: SparkSession, columns: list[str], full: bool = False) -> None:
    """Optimize table."""

    def _log_query(query: str, spark_session: SparkSession) -> None:
        """Execute query and log the result."""
        logger.info("Executing query: %s", query)
        result = spark_session.sql(query)
        rows = result.toJSON().collect()
        for row in rows:
            logger.info("%s", pformat(json.loads(row)))

    _log_query(f"OPTIMIZE {table} {'FULL' if full else ''}", spark_session)
    _log_query(f"VACUUM {table}", spark_session)
    _log_query(f"ANALYZE TABLE {table} COMPUTE STATISTICS FOR COLUMNS {', '.join(columns)}", spark_session)


def add_clustering(table: FullTableName, spark_session: SparkSession, columns: list[str]) -> None:
    """Add clustering to the table."""
    logger.info(f"Adding clustering to table {table} on columns {columns}")
    spark_session.sql(f"ALTER TABLE {table} CLUSTER BY ({', '.join(columns)})")
