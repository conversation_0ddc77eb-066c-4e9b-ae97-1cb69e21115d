"""Auth module."""

from __future__ import annotations

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from dataclasses import dataclass


@dataclass
class AuthCredentials:
    """Auth Credentials."""

    client_id: str
    client_secret: str

    @classmethod
    def from_scope(cls, secret_scope: str) -> AuthCredentials:
        """Get credentials from a secret scope."""
        from pyspark.dbutils import DBUtils

        dbutils = DBUtils()
        client_id = dbutils.secrets.get(scope=secret_scope, key="sp-client-id")
        client_secret = dbutils.secrets.get(scope=secret_scope, key="sp-client-key")
        return cls(client_id, client_secret)
