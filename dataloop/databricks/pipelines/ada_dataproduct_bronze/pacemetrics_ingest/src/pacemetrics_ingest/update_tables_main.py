"""Pacemetrics Update Tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from typing import Annotated

import typer
from dbxlib import FullSchemaName, FullTableName
from dbxlib.tables import update_table_metadata
from pacemetrics_ingest.models.models import SourceContainer
from pacemetrics_ingest.tables.operations import add_clustering, optimize_table
from pyspark.sql import SparkSession

logger = logging.getLogger(__name__)

RESPONSIBLE_TEAM = "Analytics Platform"
RESPONSIBLE_DOMAIN = "Data Delivery"
REFRESH_INTERVAL = "P10M"

PACEMETRICS_TABLE_METADATA = {
    SourceContainer.PACEMETRICS.value: {
        "description": "This table contains the raw ingested data of the pacemetrics.",
        "columns": {
            "payload": "Content of the json file, in variant type.",
            "created_at": "Timestamp of when the row was added to the table.",
            "filename": "Name of json file uploaded by pacemetrics.",
            "table_name": "Pacemetrics table name as defined by the location of the file in storage, e.g.: ArtifactoryBill",  # noqa E501
        },
        "timesystems": {
            "created_at": "UTC",
        },
    },
    SourceContainer.WEBHOOKS.value: {
        "description": "This table contains the raw ingested data of the webhooks.",
        "columns": {
            "event_type": "Type of event associated with the webhook.",
            "payload": "Content of the json file, in variant type.",
            "created_at": "Timestamp of when the row was added to the table.",
            "filename": "Name of json file uploaded by webhooks.",
            "table_name": "Webhooks table name as defined by the location of the file in storage, e.g.: demo-github",
        },
        "timesystems": {
            "created_at": "UTC",
        },
    },
}


def update_tables(tables: list[FullTableName], spark_session: SparkSession) -> None:
    """Update tables."""

    for table in tables:
        metadata = PACEMETRICS_TABLE_METADATA[table.table]
        update_table_metadata(
            table=table,
            description=metadata["description"],
            responsible_team=RESPONSIBLE_TEAM,
            responsible_domain=RESPONSIBLE_DOMAIN,
            refresh_interval=REFRESH_INTERVAL,
            column_descriptions=metadata["columns"],
            column_timesystems=metadata["timesystems"],
        )
        add_clustering(table=table, spark_session=spark_session, columns=["created_at", "table_name"])
        optimize_table(table=table, spark_session=spark_session, columns=["created_at", "table_name"], full=True)


app = typer.Typer()


@app.command()
def main(
    target_catalog: Annotated[str, typer.Option(help="Catalog where the data will be ingested to")],
    target_schema: Annotated[str, typer.Option(help="Schema where the data will be ingested to")],
    debug: Annotated[bool, typer.Option(help="Debug mode")] = False,
) -> None:
    """Main function."""
    logging.basicConfig(level=logging.DEBUG if debug else logging.INFO)

    spark_session = SparkSession.builder.getOrCreate()
    schema = FullSchemaName(target_catalog, target_schema)
    tables = [FullTableName(schema_full=schema, table=table_name.value) for table_name in SourceContainer]
    try:
        update_tables(tables, spark_session)
    except Exception as e:
        logger.error(f"Error updating tables: {e}")
        raise Exception("Error updating tables") from e


def run_main() -> None:
    """Run main."""
    # using typer with databricks has its challenges
    # for some reason, even when typer exits with a zero code
    # it still captures it as an exception and detects it as a error in running
    # the cli

    # by running as standalone_mode=False, we can catch the exit code and
    # only raise an exception if the exit code is not zero
    # all other exceptions will still trigger cli error as usual
    exit_value = app(standalone_mode=False)
    if exit_value:
        raise typer.Exit(code=exit_value)


if __name__ == "__main__":
    run_main()
