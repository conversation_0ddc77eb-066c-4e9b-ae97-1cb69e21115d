# Delta Sharing

This folder contains resources related to DeltaSharing.
Precisely, it enables to exactly specify which columns of a table are to be shared.
This is defined in a configuration file, which is then used as input for a Databricks Job that deploys custom views.

More information can be found in the [the Use Case section](#use-case).

## Folder structure

The folders are structured as follows:

| Folder                                     | Description                                                      |
| ------------------------------------------ | ---------------------------------------------------------------- |
| configuration                              | Base folder for the configuration of the DeltaShares.            |
| asset_bundles                              | Base folder for databricks asset bundles.                        |

## Use Case

We want to share data with other project's and tenant's using `DeltaSharing`.
However, a lot of tables or views contain `Competitive Sensitive Information (CSI)` which shall not be shared, or only shared with some specific projects, users or groups.
The goal is to provide a mechanism for data owners to precisely define:

- which tables are shared
- for each table:
  - which columns are shared without restrictions
  - which columns are shared with restrictions because they contain `CSI` data
- in which schema and share they are shared

How the resources available in this folder solve this can be found in [Concept](#concept)

## Concept

- for each entity that is shared, we create two schemas: (i) a public schema and a (ii) csi schema
  - e.g. assuming we want to share data from `silver.mdd.namespaces`, we create two schemas `silver.mdd_delta_sharing` and `silver.mdd_delta_sharing_csi`
  - the schemas are defined and applied in `catalogs/env/<env>/<medallion>/main.tf` as usual
  - the schemas are added to the target share as part of the `databricks_shares` variable in `catalogs/env/<env>/<medallion>/main.tf`
  - with this, the whole schemas are added to the share and every entity that is added to that schema in ADA, is also automatically shared
  - we restrict these schemas such that changes can only be done via the `GitHub Workflows SP` and `PACE Catalog Owners`, so everything is protected and audited
  - on the recipient side, the permissions need to be managed accordingly such that only users who are allowed to see `CSI` data have access to the `CSI` schemas
- for each share, we have one config file located in `databricks/delta-sharing/configuration/<env>/<share-name>.yml`
  - in here, the tables that should be shared are defined in the list `shared_entities`
  - each entry defines the source and two target schemas, (i) a public and a (ii) csi schema
    - e.g. source schema would be `silver.mdd.namespaces` and the target schemas would be `silver.mdd_delta_sharing` and `silver.mdd_delta_sharing_csi`
  - for each entry in that list, the columns that are shared are defined
    - if sharing without restrictions, set `is_csi_data` to `false`
    - if sharing to CSI recipients only, set `is_csi_data` to `true`
    - if a column of the source table shall not be shared at all, simply omit it
    - only columns explicitly defined in this list are shared
- in `databricks/pipelines/ada_dataproduct_bronze/mdm/delta_sharing_views` an asset bundle is defined
  - this asset bundle takes the share config YML as input
  - based on the input it creates two views:
    - one view with only the `non CSI` columns -> this is added to the public schema
    - one view with all columns defined in the `shared_columns` list, including the CSI -> this is added to the `CSI` data
  - since it creates the views in the respective schemas, it is automatically shared without any further need of manual involvement/configuration

## User Guide

Following step-by-step guide describes how a user can share tables using this approach.

1. Identify the following things:
   - the table(s) you want to share
   - the columns you want to share
   - the columns you want to share that contain CSI data
2. Identify the `DeltaShare` you want to share the data with
3. Create the two schemas in `catalogs/env/<env>/<medallion>/main.tf` in the `schemas` variable
   - one for sharing without restrictions and one for sharing only to `CSI` whitelisted targets
   - if you want to share for example only `CSI` data, there is no need to add a schema for `non CSI` data of course
4. Add the schema(s) to the target `DeltaShare` in `catalogs/env/<env>/<medallion>/main.tf` in the `databricks_shares` variable
5. Configure the `read_write_schema_sps` in `catalogs/env/<env>/<medallion>/main.tf` such that the `sp-pace-dataloop-mdm-dbx-prod` (executor of the job later) has write access
6. Add the table and columns you want to share to the `<target-share>.yml` in `databricks/delta-sharing/configuration/<env>/<target-share>.yml` file
7. Create a Pull Request
8. Follow the Atlantis instructions on the Pull Request to apply the changes to the `catalogs` module
9. After merging, your view(s) will be automatically deployed and added to the share you specified

## Copyright

All software (if not stated otherwise) in the repository is license under a Bosch proprietary license:

```bash
# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
```
