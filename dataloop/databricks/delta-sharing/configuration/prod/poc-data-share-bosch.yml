# config file of a DeltaSharing share
name: "poc-data-share-bosch" # name of the delta share (should also be the name of the file)
description: "Current PoC DeltaShare to share data from ADA to Bosch"

shared_entities: # table to be shared
  - source_full_path: "silver.mdd.file_entries_v1"
    target_schema_public: "silver.mdd_delta_sharing_bosch"
    target_schema_csi: "silver.mdd_delta_sharing_bosch_csi"
    shared_columns:
      public:
        - file_hash
      csi:
        - tds_file_url
  - source_full_path: "silver.calstore.devices_installed"
    target_schema_public: "silver.calstore_delta_sharing_bosch"
    shared_columns:
      public:
        - vin
        - vehicle_name
        - device_setup_uid
        - device_role
        - device_uid
        - device_name
        - serial_no
        - valid_from
        - valid_until
        - device_type
        - device_model_name
        - vehicle_release_version_long
        - vehicle_release_version_short
        - system_config
        - deviation
  - source_full_path: "gold.azureml.training_datasets_latest_metadata_v2"
    target_schema_public: "gold.azureml_delta_sharing_bosch"
    shared_columns:
      public:
        - image_id
        - dataset_name
        - dataset_version
        - split
        - input_data
        - task
        - label_path
        - input_data_sha
        - split_hash
        - project
        - stream_hash
        - recorded_at
        - recorded_at_unix
        - time_of_day
        - iso_country_code_alpha2
        - gps__elevation
        - gps__longitude
        - gps__latitude
        - route_type_ids
        - is_urban
