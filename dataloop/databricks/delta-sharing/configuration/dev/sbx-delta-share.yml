# config file of a DeltaSharing share
name: "sbx-delta-share" # name of the delta share (should also be the name of the file)
description: "Sandbox DeltaShare for testing." # description of the delta share

shared_entities: # table to be shared
  - source_full_path: "mdm_sbx.mdd.poc_deltasharing" # catalog.schema.table
    target_schema_public: "mdm_sbx.mdd_public_shared" # catalog.schema
    target_schema_csi: "mdm_sbx.mdd_csi_shared" # catalog.schema
    shared_columns: # list of columns that are shared, if a column is not in this list, it is not shared at all
      public:
        - id
      csi:
        - name
