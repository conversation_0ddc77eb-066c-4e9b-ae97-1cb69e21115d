# Databricks notebook source
# MAGIC %md
# MAGIC MOTIVATION FROM https://adb-8617216030703889.9.azuredatabricks.net/sql/dashboardsv3/01ef2d55ea5c1b95b656b31c26db4a99/pages/01ef2d55ea621262b26252d36e4357fb?o=8617216030703889 

# COMMAND ----------

# MAGIC %sql
# MAGIC USE CATALOG viper_dsp_sbx;
# MAGIC USE SCHEMA mm_bev;

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMP VIEW ingested_scene_files AS (
# MAGIC   SELECT
# MAGIC     created_at,
# MAGIC     to_date(regexp_extract(file_name, '([^-]+)-([^\\d]+)[^_]*_[^_]+_([^_]+)_([^_]+)', 3), 'yyyyMMdd') recorded_at,
# MAGIC     file_name
# MAGIC   FROM silver.mdd.file_entries
# MAGIC   WHERE file_name LIKE '%.scene'
# MAGIC );
# MAGIC
# MAGIC CREATE OR REPLACE TEMP VIEW forecasted_scene_files AS (
# MAGIC   SELECT
# MAGIC     to_date(item_created_at, 'yyyy-MM-dd') recorded_at,
# MAGIC     regexp_extract(item_full_path, '([^\\\\]+)$', 1) file_name
# MAGIC   FROM silver.cero.file_events
# MAGIC   WHERE item_full_path LIKE '%.scene'
# MAGIC );
# MAGIC
# MAGIC CREATE OR REPLACE TEMP VIEW ingested_scene_files_recent AS (
# MAGIC   SELECT * FROM ingested_scene_files
# MAGIC   WHERE recorded_at > now() - INTERVAL 180 DAYS
# MAGIC );
# MAGIC
# MAGIC CREATE OR REPLACE TEMP VIEW forecasted_scene_files_recent AS (
# MAGIC   SELECT * FROM forecasted_scene_files
# MAGIC   WHERE recorded_at > now() - INTERVAL 180 DAYS
# MAGIC );

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMP VIEW full_joined_scene_files AS (
# MAGIC   SELECT
# MAGIC     created_at,
# MAGIC     COALESCE(s.recorded_at, fs.recorded_at) c_recorded_at,
# MAGIC     COALESCE(s.file_name, fs.file_name) c_file_name,
# MAGIC     format_string("%04dCW%02d", YEAR(c_recorded_at), WEEKOFYEAR(c_recorded_at)) calendar_week,
# MAGIC     CASE WHEN c_file_name LIKE "ALLIANCE-CL%" THEN "CL" WHEN c_file_name LIKE "ALLIANCE-DC%" THEN "DC" ELSE NULL END vehicle_purpose,
# MAGIC     fs.file_name IS NOT NULL is_forecasted,
# MAGIC     s.file_name IS NOT NULL is_ingested,
# MAGIC     CASE WHEN NOT is_ingested THEN "Forecasted uningested"
# MAGIC          WHEN NOT is_forecasted THEN "Unforecasted ingested"
# MAGIC          ELSE "Forecasted ingested"
# MAGIC     END cat,
# MAGIC     -- TODO create clean mapping to vehicle name that does not rely on regex on the filename and a subsequent static mapping via python dict
# MAGIC     regexp_extract(c_file_name, '_([^_]+)-', 1) AS license_plate,
# MAGIC     -- set null values for data not yet updated to 1000 to get the right sorting behavior. hence in the final tables 1000 days corresponds to data not uploaded
# MAGIC     COALESCE(datediff(created_at, c_recorded_at), 1000) AS upload_time_days  
# MAGIC   FROM ingested_scene_files_recent s
# MAGIC   FULL JOIN forecasted_scene_files_recent fs ON s.file_name = fs.file_name
# MAGIC );

# COMMAND ----------

# create a list of calendar weeks for which we want to calculate upload times
from datetime import datetime, timedelta

# Get the current date
current_date = datetime.today()

# Calculate the start date for the past 26 weeks, as queries restricted to the past 180 days
start_date = current_date - timedelta(weeks=26)

# Generate the list of past 26 calendar weeks
calendar_weeks = [f'{start_date.year}CW{str(start_date.isocalendar()[1]).zfill(2)}']
for _ in range(26):
    start_date += timedelta(weeks=1)
    calendar_weeks.append(f'{start_date.year}CW{str(start_date.isocalendar()[1]).zfill(2)}')

# COMMAND ----------

# TODO replace mapping from filename -> regex License plate -> vehicle name with a robust mapping for all MMBEV relevant vehicles using info available in databricks
license_plate_to_vehicle_name = {
    'LBXR316-D': 'Q807',
    'HNSC837-D': 'Q816',
    'BSVF158E-D': 'B114',
    'BSVF171E-D': 'B116',
    'HNPK439-D': 'Q817',
    'HNSD636-D': 'Q818'
}

# COMMAND ----------

license_plates = list(license_plate_to_vehicle_name.keys())
result_df = None

# Loop over each calendar week and execute the query
for license_plate in license_plates:
    vehicle_name = license_plate_to_vehicle_name[license_plate]
    for week in calendar_weeks:
        query = f"""
        WITH ranked_files AS (
            -- create ordered list of files by upload time for specific calendar week and license plate. row_num is the row number in this ordered list, total_count is the total number of files for this calendar week and license plate
            SELECT *,
                ROW_NUMBER() OVER (ORDER BY upload_time_days) AS row_num,
                COUNT(*) OVER () AS total_count
            FROM full_joined_scene_files
            WHERE license_plate = '{license_plate}' AND calendar_week = '{week}'
        )
        SELECT *, '{vehicle_name}' AS vehicle_name,
               CASE WHEN upload_time_days = 1000 THEN NULL ELSE upload_time_days END AS upload_time_days_nulled
        FROM ranked_files
        WHERE row_num = CAST(0.9 * total_count AS INT)
        """
        df = spark.sql(query)
        if result_df is None:
            result_df = df
        else:
            result_df = result_df.union(df)

display(result_df)

# COMMAND ----------

result_df.createOrReplaceTempView("results_tmp")

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TABLE upload_times_mmbev AS
# MAGIC SELECT *
# MAGIC FROM results_tmp

# COMMAND ----------


