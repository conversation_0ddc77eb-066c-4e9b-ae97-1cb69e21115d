# Databricks notebook source
# MAGIC %sql
# MA<PERSON>C WITH uploaded_table AS (
# MAGIC   SELECT
# MAGIC     recorded.start_time_date,
# MAGIC     COUNT(*) AS entry_count
# MAGIC   FROM
# MAGIC     viper_dsp_sbx.mm_bev.recorded_splits_active recorded
# MAGIC   WHERE
# MAGIC     recorded.start_time_date < CURRENT_DATE - INTERVAL '14 days'
# MAGIC     AND ((recorded.VIN = 'WAUZZZF19LD017469' AND recorded.start_time_date >= "2024-06-16"))
# MAGIC   GROUP BY
# MAGIC     recorded.start_time_date
# MAGIC )
# MAGIC SELECT
# MAGIC   recorded.event_date,
# MAGIC   SUM(recorded.fc1_avi_file_count) AS recorded_count,
# MAGIC   SUM((30 / 3600) * recorded.fc1_avi_file_count) AS total_duration,
# MAGIC   SUM(COALESCE(uploaded_table.entry_count, 0)) AS uploaded_count 
# MAGIC FROM
# MAGIC   (
# MAGIC     SELECT
# MAGIC       DATE(event_registered_at) AS event_date,
# MAGIC       COUNT(*) AS fc1_avi_file_count
# MAGIC     FROM
# MAGIC       silver.cero.file_events
# MAGIC     WHERE
# MAGIC       ((vehicle_identification_number = 'WAUZZZF19LD017469' AND event_registered_at >= "2024-06-16"))
# MAGIC       AND event = 'FileCreated'
# MAGIC       AND item_full_path ILIKE '%fc1.avi'
# MAGIC     GROUP BY
# MAGIC       event_date
# MAGIC   ) recorded
# MAGIC LEFT JOIN
# MAGIC   uploaded_table
# MAGIC ON
# MAGIC   recorded.event_date = uploaded_table.start_time_date
# MAGIC WHERE
# MAGIC   recorded.event_date < CURRENT_DATE - INTERVAL '14 days'
# MAGIC GROUP BY
# MAGIC   recorded.event_date
# MAGIC ORDER BY
# MAGIC   recorded.event_date;

# COMMAND ----------

df = _sqldf.toPandas()

# COMMAND ----------

MDM_cnt = sum(list(df['uploaded_count']))
CERO_cnt = sum(list(df['recorded_count']))
print(MDM_cnt / CERO_cnt, 'of the data has been uploaded.')
assert MDM_cnt / CERO_cnt >= 0.9, "Uploaded less than 90% of recorded for Q807"
