# Databricks notebook source
# MAGIC %sql
# MAGIC SELECT * FROM viper_dsp_sbx.mm_bev.alert_recorded_hours

# COMMAND ----------

df = _sqldf.toPandas()

# COMMAND ----------

if 'Q816' in df['vehicle_name'].values:
    Q816_hour_cnt = float(df[df['vehicle_name'] == 'Q816']['total_duration'].iloc[0])
    print('Q816:', Q816_hour_cnt)
    assert Q816_hour_cnt >= 70, "Less than 70 hours recorded for Q816"
else:
    raise ValueError("Less than 70 hours recorded for Q816")
