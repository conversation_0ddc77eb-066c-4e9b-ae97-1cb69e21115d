# Databricks notebook source
# MAGIC %sql
# MAGIC CREATE
# MAGIC OR REPLACE TABLE viper_dsp_sbx.mm_bev.alert_recorded_hours
# MAGIC SELECT
# MAGIC   mapping.vehicle_name,
# MAGIC   SUM((30 / 3600) * recorded.fc1_avi_file_count) AS total_duration
# MAGIC FROM
# MAGIC   (
# MAGIC     SELECT
# MAGIC       vehicle_identification_number,
# MAGIC       DATE(event_registered_at) AS event_date,
# MAGIC       COUNT(*) AS fc1_avi_file_count
# MAGIC     FROM
# MAGIC       silver.cero.file_events
# MAGIC     WHERE
# MAGIC       (   vehicle_identification_number = 'WAUZZZF16PD000067' 
# MAGIC       OR  vehicle_identification_number = 'WV1ZZZEB7PH000462' 
# MAGIC       OR  vehicle_identification_number = 'WAUZZZF19LD017469'
# MAGIC       OR (vehicle_identification_number = 'WV1ZZZEB4PH000631' AND event_registered_at > '2024-12-03')
# MAGIC       OR (vehicle_identification_number = 'WAUZZZF1XPD000055' AND event_registered_at > '2024-12-03')
# MAGIC       OR (vehicle_identification_number = 'WAUZZZF1XPD000069' AND event_registered_at > '2024-12-03'))
# MAGIC       AND event = 'FileCreated'
# MAGIC       AND item_full_path ILIKE '%fc1.avi'
# MAGIC     GROUP BY
# MAGIC       vehicle_identification_number,
# MAGIC       event_date
# MAGIC   ) recorded
# MAGIC LEFT JOIN
# MAGIC   (SELECT DISTINCT VIN, vehicle_name FROM silver.calstore.devices_installed) mapping
# MAGIC ON
# MAGIC   recorded.vehicle_identification_number = mapping.VIN
# MAGIC WHERE
# MAGIC   recorded.event_date >= CURRENT_DATE - INTERVAL '7 days'
# MAGIC GROUP BY
# MAGIC   mapping.vehicle_name
