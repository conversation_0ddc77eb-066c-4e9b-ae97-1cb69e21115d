# Databricks notebook source
# MAGIC %sql
# MAGIC SELECT * FROM viper_dsp_sbx.mm_bev.alert_recorded_hours

# COMMAND ----------

df = _sqldf.toPandas()

# COMMAND ----------

if 'Q807' in df['vehicle_name'].values:
    Q807_hour_cnt = float(df[df['vehicle_name'] == 'Q807']['total_duration'].iloc[0])
    print('Q807:', Q807_hour_cnt)
    assert Q807_hour_cnt >= 70, "Less than 70 hours recorded for Q807"
else:
    raise ValueError("Less than 70 hours recorded for Q807")

