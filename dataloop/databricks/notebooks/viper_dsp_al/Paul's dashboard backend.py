# Databricks notebook source
# MAGIC %md
# MAGIC # Code for the backend of the Selection method share
# MAGIC
# MAGIC Dashboard: https://adb-8617216030703889.9.azuredatabricks.net/sql/dashboardsv3/01ef8ad75d1c141c9f3f5620aca2fe16/pages/01efd8a959c5175eb47d9222a49a78f9?o=8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889%3Fo%3D8617216030703889&f_01efd8af086f1b9f9ceb53d4297f268d=PaceGeneralYUV&f_01efd8c1c35d1be49006fd7f3bf720b5=182&f_01efd8c1c53a11c28793125bbe01c175=114&f_01efd8c1c6ce10fcb3810506504c6144=66

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE FUNCTION categorize_target(target STRING)
# MAGIC   RETURNS STRING
# MAGIC   RETURN CASE
# MAGIC     WHEN target LIKE "%traffic_light%" THEN "traffic_light"
# MAGIC     WHEN target LIKE "%traffic_sign%" THEN "traffic_sign"
# MAGIC     WHEN target LIKE "%lane%" THEN "lane"
# MAGIC     WHEN target LIKE "%light%" THEN "light"
# MAGIC     WHEN target LIKE "%pole%" THEN "pole"
# MAGIC     WHEN target LIKE "%vehicle%" THEN "vehicle"
# MAGIC     WHEN target LIKE "%vru%" THEN "vru"
# MAGIC     WHEN target LIKE "%blockage%" THEN "blockage"
# MAGIC     WHEN target LIKE "%tunnel%" THEN "tunnel"
# MAGIC     WHEN target LIKE "%road_condition%" THEN "road_condition"
# MAGIC     WHEN target LIKE "%road_symbol%" THEN "road_symbols"
# MAGIC     WHEN target LIKE "%road_type%" THEN "road_type"
# MAGIC     WHEN target LIKE "%fog%" THEN "fog_detection"
# MAGIC     WHEN target LIKE "%semseg%" THEN "semseg"
# MAGIC     WHEN target LIKE "%trailer%" THEN "trailer"
# MAGIC     ELSE target
# MAGIC   END;

# COMMAND ----------

# MAGIC %sql
# MAGIC use catalog viper_dsp_sbx;
# MAGIC
# MAGIC use schema active_learning;
# MAGIC
# MAGIC create OR REPLACE TEMPORARY view my_training_dataset as
# MAGIC (
# MAGIC   SELECT
# MAGIC     split,
# MAGIC     input_data,
# MAGIC     dataset_name,
# MAGIC     dataset_version,
# MAGIC     image_id,
# MAGIC     categorize_target(task) as task
# MAGIC   FROM
# MAGIC     silver.azureml.training_datasets_v2
# MAGIC   where
# MAGIC     dataset_name IN ("PaceGeneralYUV", "PaceGeneralYUV-Japan", "TopView_Multitask_YUV")
# MAGIC     AND task != "compressed_rgb"
# MAGIC )

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMPORARY VIEW dsp_selection_clean AS
# MAGIC (
# MAGIC   SELECT
# MAGIC     file_hash,
# MAGIC     created_by,
# MAGIC     state,
# MAGIC     categorize_target(target) as target
# MAGIC   FROM
# MAGIC     silver.mdd.dsp_selection
# MAGIC )

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMPORARY VIEW selected_frames AS
# MAGIC (
# MAGIC   SELECT
# MAGIC     file_hash,
# MAGIC     created_by,
# MAGIC     state,
# MAGIC     target
# MAGIC   FROM
# MAGIC     dsp_selection_clean
# MAGIC   where
# MAGIC     state = "SELECTED"
# MAGIC )

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMPORARY VIEW my_dsp_sequence_deduplicator AS
# MAGIC (
# MAGIC   SELECT
# MAGIC     file_hash,
# MAGIC     context,
# MAGIC     sequencing_mode,
# MAGIC     input_frame_number,
# MAGIC     input_frame_sha,
# MAGIC     categorize_target(usecase) as usecase
# MAGIC   FROM
# MAGIC     silver.mdd.dsp_sequence_deduplicator
# MAGIC   where
# MAGIC     sequencing_mode = "true"
# MAGIC )

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TABLE training_x_selected_dataset_ns AS
# MAGIC (
# MAGIC   SELECT
# MAGIC     training_data.split,
# MAGIC     training_data.input_data,
# MAGIC     training_data.image_id,
# MAGIC     training_data.task,
# MAGIC     training_data.dataset_name,
# MAGIC     training_data.dataset_version,
# MAGIC     selection.created_by,
# MAGIC     selection.stream_hash,
# MAGIC     selection.file_hash
# MAGIC   FROM
# MAGIC     my_training_dataset as training_data
# MAGIC       LEFT OUTER JOIN (
# MAGIC         SELECT DISTINCT
# MAGIC           file_hash,
# MAGIC           image_id,
# MAGIC           created_by,
# MAGIC           target,
# MAGIC           dsp_de_image_extended.stream_hash
# MAGIC         From
# MAGIC           selected_frames JOIN silver.mdd.dsp_de_image_extended USING (file_hash)
# MAGIC       ) selection
# MAGIC         on training_data.image_id = selection.image_id
# MAGIC         and training_data.task = selection.target
# MAGIC )

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TABLE sequencing_infered -- ex mf_test2
# MAGIC WITH sequencing AS (
# MAGIC   SELECT DISTINCT
# MAGIC     my_dsp_sequence_deduplicator.file_hash AS sequence_file_hash,
# MAGIC     selection.created_by,
# MAGIC     usecase as task
# MAGIC   FROM
# MAGIC     my_dsp_sequence_deduplicator
# MAGIC       JOIN selected_frames selection
# MAGIC         ON my_dsp_sequence_deduplicator.input_frame_sha = selection.file_hash
# MAGIC         AND selection.target = usecase -- TODO: check if this works
# MAGIC ),
# MAGIC -- Training data that did not match on selection namespace does not have a stream_hash -> we need to join it.
# MAGIC training_data AS (
# MAGIC   SELECT
# MAGIC     training_data.image_id,
# MAGIC     training_data.task,
# MAGIC     image.stream_hash,
# MAGIC     -- stream_hash is not set in training data for non-matched entries
# MAGIC     training_data.dataset_name,
# MAGIC     -- Add dataset_name here
# MAGIC     training_data.dataset_version
# MAGIC   FROM
# MAGIC     (
# MAGIC       SELECT
# MAGIC         *
# MAGIC       FROM
# MAGIC         training_x_selected_dataset_ns
# MAGIC       WHERE
# MAGIC         created_by IS NULL
# MAGIC     ) AS training_data
# MAGIC       LEFT JOIN (
# MAGIC         SELECT DISTINCT
# MAGIC           image_id,
# MAGIC           stream_hash
# MAGIC         FROM
# MAGIC           silver.mdd.dsp_de_image_extended
# MAGIC       ) AS image
# MAGIC         USING (image_id)
# MAGIC )
# MAGIC SELECT
# MAGIC   training_data.*,
# MAGIC   sel_seq.created_by
# MAGIC FROM
# MAGIC   training_data
# MAGIC     LEFT JOIN sequencing AS sel_seq
# MAGIC       ON training_data.stream_hash = sel_seq.sequence_file_hash
# MAGIC       AND training_data.task = sel_seq.task;

# COMMAND ----------

# MAGIC %md
# MAGIC ### CONTEXT B LSR huntdown
# MAGIC In the past, only CONTEXT A frames were selected, but based on those, some context B frames were selected 

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE
# MAGIC OR REPLACE TEMPORARY VIEW lights_not_matched_by_seq as -- figure which image_ids from LSR task are context B and dont have created_by value
# MAGIC (
# MAGIC   SELECT
# MAGIC     DISTINCT d.image_id,
# MAGIC     task,
# MAGIC     d.stream_hash,
# MAGIC     created_by,
# MAGIC     d.context,
# MAGIC     d.frame_number,
# MAGIC     d.created_at
# MAGIC   FROM
# MAGIC     sequencing_infered as t
# MAGIC     JOIN silver.mdd.dsp_de_image_extended d ON t.image_id = d.image_id
# MAGIC   WHERE
# MAGIC     created_by IS NULL
# MAGIC     AND task LIKE 'light'
# MAGIC     AND d.context = 'B'
# MAGIC );
# MAGIC
# MAGIC CREATE OR REPLACE Table not_matched_by_seq_context_a AS -- take a look on context A frames that are frame_number -1 to context B frame
# MAGIC (
# MAGIC   SELECT DISTINCT
# MAGIC     t.image_id,
# MAGIC     t.task,
# MAGIC     t.stream_hash,
# MAGIC     created_by,
# MAGIC     t.context,
# MAGIC     t.frame_number,
# MAGIC     d3.recorded_at AS recorded_at_minus1,
# MAGIC     d3.frame_number AS frame_number_minus1,
# MAGIC     d3.image_id AS image_id_minus1
# MAGIC   FROM
# MAGIC     lights_not_matched_by_seq AS t
# MAGIC       JOIN silver.mdd.dsp_de_image_extended d3
# MAGIC         ON t.stream_hash = d3.stream_hash
# MAGIC         AND (t.frame_number = d3.frame_number - 1)
# MAGIC );
# MAGIC
# MAGIC create or replace table sequencing_context_b_not_matched as
# MAGIC (
# MAGIC   select
# MAGIC     not_matched_by_seq_context_a.*
# MAGIC   from
# MAGIC     not_matched_by_seq_context_a
# MAGIC       left outer JOIN my_dsp_sequence_deduplicator dedup
# MAGIC         on not_matched_by_seq_context_a.stream_hash = dedup.file_hash
# MAGIC         and dedup.usecase = not_matched_by_seq_context_a.task
# MAGIC   WHERE
# MAGIC     dedup.sequencing_mode = true -- Group by all: remove duplicates create from join
# MAGIC   GROUP BY
# MAGIC     ALL
# MAGIC );
# MAGIC
# MAGIC create or replace table selection_on_stream as
# MAGIC (
# MAGIC   select distinct
# MAGIC     dex.stream_hash,
# MAGIC     selection.target,
# MAGIC     selection.created_by
# MAGIC   from
# MAGIC     selected_frames as selection join silver.mdd.dsp_de_image_extended dex using (file_hash)
# MAGIC );
# MAGIC
# MAGIC CREATE OR REPLACE TABLE context_b_sequenced_selections as
# MAGIC (
# MAGIC   select
# MAGIC     sequencing.image_id,
# MAGIC     sequencing.stream_hash,
# MAGIC     sequencing.task,
# MAGIC     selections.created_by
# MAGIC   from
# MAGIC     sequencing_context_b_not_matched sequencing
# MAGIC       left join selection_on_stream selections
# MAGIC         on sequencing.task = selections.target
# MAGIC         and sequencing.stream_hash = selections.stream_hash
# MAGIC );
# MAGIC
# MAGIC CREATE OR REPLACE TABLE context_b_sequenced_selections AS -- what can happen is that for some frames in sequencing are more then 1 created_by values, and we cannot distinquish which, so filter them out
# MAGIC WITH unique_images AS (
# MAGIC   SELECT
# MAGIC     image_id,
# MAGIC     COUNT(*) AS cnt
# MAGIC   FROM
# MAGIC     context_b_sequenced_selections
# MAGIC   GROUP BY
# MAGIC     image_id
# MAGIC   HAVING
# MAGIC     COUNT(*) = 1
# MAGIC )
# MAGIC SELECT
# MAGIC   a.*
# MAGIC FROM
# MAGIC   context_b_sequenced_selections a JOIN unique_images b USING (image_id);

# COMMAND ----------

# MAGIC %md
# MAGIC # Available versus used frames

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Distinct frames used in any training dataset version by task and camera stream.
# MAGIC CREATE OR REPLACE temporary view training_latest_by_camera AS
# MAGIC SELECT
# MAGIC   training_data.task,
# MAGIC   COUNT(distinct image_id) AS row_count,
# MAGIC   CASE
# MAGIC     WHEN image_id LIKE '%TV%' THEN 'TV'
# MAGIC     WHEN image_id LIKE '%FC%' THEN 'FC'
# MAGIC     ELSE image_id
# MAGIC   END as camera_stream
# MAGIC FROM
# MAGIC   (
# MAGIC     SELECT distinct
# MAGIC       task,
# MAGIC       image_id
# MAGIC     FROM
# MAGIC       gold.azureml.training_datasets_latest_metadata_v2
# MAGIC     WHERE
# MAGIC       input_data.yuv420 LIKE '%mdm%'
# MAGIC       AND dataset_name IN ("PaceGeneralYUV", "PaceGeneralYUV-Japan", "TopView_Multitask_YUV")
# MAGIC       AND task != "compressed_rgb"
# MAGIC   ) AS training_data
# MAGIC GROUP BY
# MAGIC   training_data.task,
# MAGIC   camera_stream

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE temporary view training_by_camera_n_dataset AS
# MAGIC SELECT DISTINCT
# MAGIC   task,
# MAGIC   image_id,
# MAGIC   CASE
# MAGIC     WHEN image_id LIKE '%TV%' THEN 'TV'
# MAGIC     WHEN image_id LIKE '%FC%' THEN 'FC'
# MAGIC     ELSE image_id
# MAGIC   END as camera_stream,
# MAGIC   dataset_name,
# MAGIC   dataset_version
# MAGIC FROM
# MAGIC   silver.azureml.training_datasets_v2
# MAGIC WHERE
# MAGIC   input_data.yuv420 LIKE '%mdm%'
# MAGIC   AND dataset_name IN ("PaceGeneralYUV", "PaceGeneralYUV-Japan", "TopView_Multitask_YUV")
# MAGIC   AND task != "compressed_rgb";
# MAGIC
# MAGIC select
# MAGIC   *
# MAGIC from
# MAGIC   training_by_camera_n_dataset

# COMMAND ----------

# MAGIC %sql
# MAGIC -- TODO: is labelclient_input the right table to base this on?
# MAGIC -- TODO: do we not want to filter this by dataset? What is the message?
# MAGIC CREATE OR REPLACE TABLE training_vs_available_latest AS
# MAGIC WITH label_client as (
# MAGIC   SELECT
# MAGIC     CASE
# MAGIC       WHEN label_team = 'vru' THEN 'vru'
# MAGIC       WHEN label_team = 'vehicle' THEN 'vehicle'
# MAGIC       WHEN label_team = 'traffic_light' THEN 'traffic_light'
# MAGIC       WHEN label_team = 'semantic_full' THEN 'nothing'
# MAGIC       WHEN label_team = 'lidar' THEN 'nothing'
# MAGIC       WHEN label_team = 'function' THEN 'nothing'
# MAGIC       WHEN label_team = 'Vehicle/TV' THEN 'vehicle'
# MAGIC       WHEN label_team = 'Vehicle' THEN 'vehicle'
# MAGIC       WHEN label_team = 'VRU' THEN 'vru'
# MAGIC       WHEN label_team = 'Trailer' THEN 'trailer'
# MAGIC       WHEN label_team = 'TrafficLight' THEN 'traffic_light'
# MAGIC       WHEN label_team = 'TSR (Traffic sign labeling)' THEN 'traffic_sign'
# MAGIC       WHEN label_team = 'TLD' THEN 'traffic_light'
# MAGIC       WHEN label_team = 'TL (Traffic Light Labeling)' THEN 'traffic_light'
# MAGIC       WHEN label_team = 'SemSeg' THEN 'nothing'
# MAGIC       WHEN label_team = 'RoadSymbol' THEN 'road_symbol'
# MAGIC       WHEN label_team = 'Road Condition' THEN 'road_condition'
# MAGIC       WHEN label_team = 'Pole' THEN 'pole'
# MAGIC       WHEN label_team = 'LostCargo' THEN 'nothing'
# MAGIC       WHEN label_team = 'LightSourceRecognition' THEN 'light'
# MAGIC       WHEN label_team = 'Lidar' THEN 'nothing'
# MAGIC       WHEN label_team = 'Lane_3D' THEN 'nothing'
# MAGIC       WHEN label_team = 'Lane3D' THEN 'nothing'
# MAGIC       WHEN label_team = 'Lane' THEN 'lane'
# MAGIC       WHEN label_team = 'LSR' THEN 'light'
# MAGIC       WHEN label_team = 'FreeSpace' THEN 'nothing'
# MAGIC       WHEN label_team = 'FOG' THEN 'fog_detection'
# MAGIC       WHEN label_team = 'Curb' THEN 'nothing'
# MAGIC       WHEN label_team = 'Blockage' THEN 'blockage'
# MAGIC       ELSE label_team
# MAGIC     END AS task,
# MAGIC     CASE
# MAGIC       WHEN stream_camera_type LIKE 'TV%' THEN 'TV'
# MAGIC       WHEN stream_camera_type LIKE 'FC%' THEN 'FC'
# MAGIC       ELSE stream_camera_type
# MAGIC     END AS camera_stream,
# MAGIC     image_id
# MAGIC   FROM
# MAGIC     silver.mdd.viper_labelclient_input
# MAGIC )
# MAGIC SELECT
# MAGIC   task,
# MAGIC   camera_stream,
# MAGIC   COUNT(DISTINCT image_id) as row_count,
# MAGIC   'available' as type
# MAGIC FROM
# MAGIC   label_client
# MAGIC GROUP BY
# MAGIC   all
# MAGIC UNION
# MAGIC SELECT
# MAGIC   task,
# MAGIC   camera_stream,
# MAGIC   row_count,
# MAGIC   'used' as type
# MAGIC FROM
# MAGIC   training_latest_by_camera;
# MAGIC
# MAGIC select
# MAGIC   *
# MAGIC from
# MAGIC   training_vs_available_latest

# COMMAND ----------

# MAGIC %md
# MAGIC ## Frames in training vs labelled data

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TABLE training_x_selected_dataset_by_task_x_target AS
# MAGIC SELECT
# MAGIC   task,
# MAGIC   COALESCE(created_by, 'sequencing') AS created_by,
# MAGIC   COUNT(*) AS row_count,
# MAGIC   dataset_name,
# MAGIC   -- Include dataset_name in the SELECT clause
# MAGIC   dataset_version
# MAGIC FROM
# MAGIC   training_x_selected_dataset_ns
# MAGIC GROUP BY
# MAGIC   task,
# MAGIC   -- Group by task
# MAGIC   COALESCE(created_by, 'sequencing'),
# MAGIC   -- Group by created_by
# MAGIC   dataset_name,
# MAGIC   -- Group by dataset_name
# MAGIC   dataset_version

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TABLE training_x_selected_dataset AS
# MAGIC SELECT
# MAGIC   task,
# MAGIC   COALESCE(created_by, 'sequencing') AS created_by,
# MAGIC   image_id,
# MAGIC   dataset_name,
# MAGIC   -- Include dataset_name in the SELECT clause
# MAGIC   dataset_version
# MAGIC FROM
# MAGIC   training_x_selected_dataset_ns
# MAGIC GROUP BY
# MAGIC   all;
# MAGIC
# MAGIC select
# MAGIC   *
# MAGIC from
# MAGIC   training_x_selected_dataset

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TABLE training_by_selection_method
# MAGIC SELECT distinct
# MAGIC   created_by,
# MAGIC   task,
# MAGIC   image_id,
# MAGIC   dataset_name,
# MAGIC   dataset_version
# MAGIC FROM
# MAGIC   (
# MAGIC     SELECT
# MAGIC       task,
# MAGIC       created_by,
# MAGIC       image_id,
# MAGIC       dataset_name,
# MAGIC       dataset_version
# MAGIC     FROM
# MAGIC       -- every entry with created_by = "sequencing" has created_by = NULL
# MAGIC       viper_dsp_sbx.active_learning.training_x_selected_dataset
# MAGIC     WHERE
# MAGIC       created_by != "sequencing"
# MAGIC       AND dataset_name IN ("PaceGeneralYUV", "PaceGeneralYUV-Japan", "TopView_Multitask_YUV")
# MAGIC     GROUP BY
# MAGIC       all
# MAGIC     UNION
# MAGIC     select
# MAGIC       task,
# MAGIC       created_by,
# MAGIC       image_id,
# MAGIC       dataset_name,
# MAGIC       dataset_version
# MAGIC     from
# MAGIC       (
# MAGIC         select
# MAGIC           seq_contex_a.image_id,
# MAGIC           seq_contex_a.task,
# MAGIC           seq_contex_a.stream_hash,
# MAGIC           seq_contex_a.dataset_name,
# MAGIC           seq_contex_a.dataset_version,
# MAGIC           COALESCE(
# MAGIC             seq_contex_a.created_by, seq_contex_b.created_by, "missing information"
# MAGIC           ) as created_by
# MAGIC         from
# MAGIC           viper_dsp_sbx.active_learning.sequencing_infered seq_contex_a
# MAGIC             LEFT JOIN viper_dsp_sbx.active_learning.context_b_sequenced_selections seq_contex_b
# MAGIC               on seq_contex_a.image_id = seq_contex_b.image_id
# MAGIC               and seq_contex_a.task = seq_contex_b.task
# MAGIC       )
# MAGIC     WHERE
# MAGIC       dataset_name IN ("PaceGeneralYUV", "PaceGeneralYUV-Japan", "TopView_Multitask_YUV")
# MAGIC     GROUP BY
# MAGIC       All
# MAGIC   )
# MAGIC GROUP BY
# MAGIC   All

# COMMAND ----------

# MAGIC %md
# MAGIC ## Statistics on selected vs rejected

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE table selected_vs_rejected as
# MAGIC select
# MAGIC   state,
# MAGIC   target,
# MAGIC   count(*) as count
# MAGIC from
# MAGIC   dsp_selection_clean
# MAGIC where
# MAGIC   state in ("PROPOSED", "SELECTED", "REJECTED")
# MAGIC group by
# MAGIC   all
