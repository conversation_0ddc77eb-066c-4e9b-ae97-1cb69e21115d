{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3d1eb672-1331-42d1-bc67-f66302bc5487", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "USE CATALOG viper_dsp_sbx;\n", "USE SCHEMA vru_splits;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "ea65ed7f-c5d2-445d-80f2-c1efacef1f47", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Preparation in SQL only\n", "\n", "The following cells collect new data (for selection) and old data (for boosting). They are quite time intense, therefore it is recommended to run them in sql-cluster and only run the cells afterwards on a personal compute, which is python compatible."]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "a39a5527-409a-45f2-ae72-5989636a1656", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Load new data\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "92ac8cfd-4c35-4acd-b41b-6cdd228bb7c3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["###Load the new data for _VRU_"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7432574b-b4e0-4e47-b5d6-a5ba5c22a7b4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "\n", "-- Creates viper_dsp_sbx.vru_splits.scaled_split_meta_complete_vru that puts together the new splits, the scores of the split frames and its metadata\n", "\n", "CREATE\n", "OR REPLACE TABLE scaled_split_meta_complete_vru\n", "WITH mapinfo_meta AS (\n", "  SELECT \n", "    file_hash, \n", "    MIN(positions__iso_country_code_alpha2) AS positions__iso_country_code_alpha2\n", "  FROM \n", "    silver.mdd.mapinfo\n", "  GROUP BY \n", "    file_hash\n", "),\n", "time_meta AS (\n", "  SELECT\n", "    file_hash,\n", "    MIN(time_of_day) AS time_of_day\n", "  FROM\n", "    silver.mdd.time_of_day\n", "  GROUP BY\n", "    file_hash\n", "),\n", "weather_meta AS (\n", "  SELECT\n", "    file_hash,\n", "    MIN(tag_uid) as tag_uid\n", "  FROM\n", "    silver.mdd.datamanagement_co_driver_tags\n", "  WHERE\n", "    tag_group = \"Precipitation\"\n", "  GROUP BY\n", "    file_hash\n", "),\n", "road_meta AS (\n", "  SELECT\n", "    split_hash,\n", "    road_category,\n", "    lane_count,\n", "    speed_limit\n", "  FROM\n", "    viper_dsp_sbx.mm_bev.recorded_splits_w_metadata\n", ")\n", "SELECT \n", "  scaled_agg.*,\n", "  mapinfo_meta.file_hash, \n", "  COALESCE(mapinfo_meta.positions__iso_country_code_alpha2, 'None_country') AS positions__iso_country_code_alpha2,\n", "  COALESCE(time_meta.time_of_day, 'Day') AS time_of_day,\n", "  COALESCE(weather_meta.tag_uid, 'None') AS tag_uid,\n", "  COALESCE(road_meta.road_category, 'rural') AS road_category,\n", "  road_meta.lane_count,\n", "  road_meta.speed_limit\n", "FROM \n", "  (WITH score_name_filter AS (\n", "    select\n", "    active_learning.*,\n", "    image.split_hash,\n", "    image.camera_stream\n", "    from\n", "    silver.mdd.active_learning\n", "    join (\n", "      SELECT\n", "        file_hash,\n", "        split_hash,\n", "        camera_stream\n", "      from\n", "        silver.mdd.dsp_de_image\n", "    ) as image on active_learning.file_hash == image.file_hash\n", "    where\n", "    scores__name LIKE \"vru_%\"\n", "  ),\n", "  already_selected AS (\n", "    SELECT \n", "      split_hash, \n", "      start_time_date, \n", "      vin,\n", "      gmdn_hash\n", "    FROM \n", "      viper_dsp_sbx.mm_bev.recorded_splits_active\n", "    WHERE \n", "      (considered_for_selection == FALSE)\n", "  )\n", "  SELECT \n", "    score_name_filter.split_hash, \n", "    score_name_filter.scores__name,\n", "    score_name_filter.scores__value,\n", "    already_selected.vin,\n", "    already_selected.start_time_date,\n", "    already_selected.gmdn_hash\n", "  FROM \n", "    score_name_filter\n", "  JOIN \n", "    already_selected\n", "  ON \n", "    score_name_filter.split_hash = already_selected.split_hash) scaled_agg\n", "LEFT JOIN \n", "  mapinfo_meta\n", "ON \n", "  scaled_agg.split_hash = mapinfo_meta.file_hash\n", "LEFT JOIN\n", "  time_meta\n", "ON\n", "  scaled_agg.split_hash = time_meta.file_hash\n", "LEFT JOIN\n", "  weather_meta\n", "ON\n", "  scaled_agg.split_hash = weather_meta.file_hash\n", "LEFT JOIN\n", "  road_meta\n", "ON\n", "  scaled_agg.split_hash = road_meta.split_hash"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "0096f6fd-66af-413b-a203-af004d9fbe6f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["###Load the new data for _VEHICLES_"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f82566bb-8f27-477a-931f-1a1a47a249fd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "\n", "-- Creates viper_dsp_sbx.vru_splits.scaled_split_meta_complete_vehicle that puts together the new splits, the scores of the split frames and its metadata\n", "\n", "CREATE\n", "OR REPLACE TABLE scaled_split_meta_complete_vehicle\n", "WITH mapinfo_meta AS (\n", "  SELECT \n", "    file_hash, \n", "    MIN(positions__iso_country_code_alpha2) AS positions__iso_country_code_alpha2\n", "  FROM \n", "    silver.mdd.mapinfo\n", "  GROUP BY \n", "    file_hash\n", "),\n", "time_meta AS (\n", "  SELECT\n", "    file_hash,\n", "    MIN(time_of_day) AS time_of_day\n", "  FROM\n", "    silver.mdd.time_of_day\n", "  GROUP BY\n", "    file_hash\n", "),\n", "weather_meta AS (\n", "  SELECT\n", "    file_hash,\n", "    MIN(tag_uid) as tag_uid\n", "  FROM\n", "    silver.mdd.datamanagement_co_driver_tags\n", "  WHERE\n", "    tag_group = \"Precipitation\"\n", "  GROUP BY\n", "    file_hash\n", "),\n", "road_meta AS (\n", "  SELECT\n", "    split_hash,\n", "    road_category,\n", "    lane_count,\n", "    speed_limit\n", "  FROM\n", "    viper_dsp_sbx.mm_bev.recorded_splits_w_metadata\n", ")\n", "SELECT \n", "  scaled_agg.*,\n", "  mapinfo_meta.file_hash, \n", "  COALESCE(mapinfo_meta.positions__iso_country_code_alpha2, 'None_country') AS positions__iso_country_code_alpha2,\n", "  COALESCE(time_meta.time_of_day, 'Day') AS time_of_day,\n", "  COALESCE(weather_meta.tag_uid, 'None') AS tag_uid,\n", "  COALESCE(road_meta.road_category, 'rural') AS road_category,\n", "  road_meta.lane_count,\n", "  road_meta.speed_limit\n", "FROM \n", "  (WITH score_name_filter AS (\n", "    select\n", "    active_learning.*,\n", "    image.split_hash,\n", "    image.camera_stream\n", "    from\n", "    silver.mdd.active_learning\n", "    join (\n", "      SELECT\n", "        file_hash,\n", "        split_hash,\n", "        camera_stream\n", "      from\n", "        silver.mdd.dsp_de_image\n", "    ) as image on active_learning.file_hash == image.file_hash\n", "    where\n", "    scores__name LIKE \"vehicle_%\"\n", "  ),\n", "  already_selected AS (\n", "    SELECT \n", "      split_hash, \n", "      start_time_date, \n", "      vin,\n", "      gmdn_hash\n", "    FROM \n", "      viper_dsp_sbx.mm_bev.recorded_splits_active\n", "    WHERE \n", "      (considered_for_selection == FALSE)\n", "  )\n", "  SELECT \n", "    score_name_filter.split_hash, \n", "    score_name_filter.scores__name,\n", "    score_name_filter.scores__value,\n", "    already_selected.vin,\n", "    already_selected.start_time_date,\n", "    already_selected.gmdn_hash\n", "  FROM \n", "    score_name_filter\n", "  JOIN \n", "    already_selected\n", "  ON \n", "    score_name_filter.split_hash = already_selected.split_hash) scaled_agg\n", "LEFT JOIN \n", "  mapinfo_meta\n", "ON \n", "  scaled_agg.split_hash = mapinfo_meta.file_hash\n", "LEFT JOIN\n", "  time_meta\n", "ON\n", "  scaled_agg.split_hash = time_meta.file_hash\n", "LEFT JOIN\n", "  weather_meta\n", "ON\n", "  scaled_agg.split_hash = weather_meta.file_hash\n", "LEFT JOIN\n", "  road_meta\n", "ON\n", "  scaled_agg.split_hash = road_meta.split_hash"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "99ed6b1e-9ac7-4bc8-98ea-6537e6919f78", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Collect selected data used for boosting"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "66b2c585-9d2d-4deb-8cd4-e4e490a2d24f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "\n", "-- Create viper_dsp_sbx.vru_splits.already_selected_meta that consists of the splits that were already selected (that made it to the dataset) and connects it to the metadata.\n", "\n", "CREATE\n", "OR REPLACE TABLE already_selected_meta\n", "WITH mapinfo_meta AS (\n", "  SELECT \n", "    file_hash, \n", "    MIN(positions__iso_country_code_alpha2) AS positions__iso_country_code_alpha2\n", "  FROM \n", "    silver.mdd.mapinfo\n", "  GROUP BY \n", "    file_hash\n", "),\n", "time_meta AS (\n", "  SELECT\n", "    split_hash,\n", "    MIN(time_of_day) AS time_of_day\n", "  FROM\n", "    viper_dsp_sbx.mm_bev.recorded_splits_w_metadata\n", "  GROUP BY\n", "    split_hash\n", "),\n", "weather_meta AS (\n", "  SELECT\n", "    file_hash,\n", "    MIN(tag_uid) as tag_uid\n", "  FROM\n", "    silver.mdd.datamanagement_co_driver_tags\n", "  WHERE\n", "    tag_group = \"Precipitation\"\n", "  GROUP BY\n", "    file_hash\n", "),\n", "road_meta AS (\n", "  SELECT\n", "    split_hash,\n", "    road_category,\n", "    lane_count,\n", "    speed_limit\n", "  FROM\n", "    viper_dsp_sbx.mm_bev.recorded_splits_w_metadata\n", ")\n", "SELECT \n", "  already_selected.*,\n", "  mapinfo_meta.file_hash, \n", "  COALESCE(mapinfo_meta.positions__iso_country_code_alpha2, 'None') AS positions__iso_country_code_alpha2,\n", "  time_meta.time_of_day,\n", "  COALESCE(weather_meta.tag_uid, 'None') AS tag_uid,\n", "  road_meta.road_category,\n", "  road_meta.lane_count,\n", "  road_meta.speed_limit\n", "FROM \n", "  (SELECT \n", "    split_hash, \n", "    start_time_date, \n", "    vin,\n", "    selection_version\n", "  FROM \n", "    viper_dsp_sbx.mm_bev.selected_split) already_selected\n", "JOIN \n", "  mapinfo_meta\n", "ON \n", "  already_selected.split_hash = mapinfo_meta.file_hash\n", "JOIN\n", "  time_meta\n", "ON\n", "  already_selected.split_hash = time_meta.split_hash --file_hash\n", "LEFT JOIN\n", "  weather_meta\n", "ON\n", "  already_selected.split_hash = weather_meta.file_hash\n", "JOIN\n", "  road_meta\n", "ON\n", "  already_selected.split_hash = road_meta.split_hash"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "589910d9-77e1-4d10-ab1f-b77a40a0d058", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Python based section -> switch to personal compute"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "375eace8-5376-4ed1-a77e-c258e817ddfb", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["BEV_SCHEMA = \"viper_dsp_sbx.vru_splits.\"\n", "\"\"\"Schema for all bev selections.\"\"\""]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "5bab308a-a0a3-4422-8073-664764b11ead", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Data preparation\n", "\n", "`road_category` is not filled for all frames -> we fit a decision tree to predict it based on `lane_count` and `speed_limit`. This approach achieved a >90% accuracy in an initial evaluation\n", "\n", "## Predict road_category for the unknown values based on lane_count and speed_limit"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "448cb000-2808-426b-af17-4c2fa29852c3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "-- Training/testing dat for the decision tree\n", "\n", "SELECT\n", "  lane_count,\n", "  speed_limit,\n", "  road_category\n", "FROM\n", "  viper_dsp_sbx.mm_bev.recorded_splits_w_metadata\n", "WHERE\n", "  road_category ILIKE '%rural%'\n", "  OR road_category ILIKE '%highway%'"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1030a620-55c6-4b9d-ae9a-f3f1512ecd7a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Training/testing of the decision tree that replaces the unknown road_category metadata with either \"rural\" of \"highway\" based on the number of lanes and the max speed\n", "\n", "import pandas as pd\n", "from sklearn.model_selection import train_test_split\n", "from sklearn.tree import DecisionTreeClassifier, export_text\n", "from sklearn.metrics import accuracy_score\n", "from sklearn.preprocessing import MinMaxScaler\n", "\n", "tree_df = _sqldf.toPandas()\n", "\n", "X = tree_df.iloc[:, :-1]  # Select all columns except the last one as features\n", "y = tree_df.iloc[:, -1]   # Select the last column as the label\n", "\n", "X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)\n", "\n", "dec_tree = DecisionTreeClassifier()\n", "dec_tree.fit(X_train, y_train)\n", "\n", "y_pred = dec_tree.predict(X_test)\n", "\n", "accuracy = accuracy_score(y_test, y_pred)\n", "print(f'Accuracy: {accuracy}')"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "ed38a0f6-5d32-435b-9f39-756ecd99cdb9", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## The following process is done separately for _VRU_ and _VEHICLES_\n", "- get score value for each frame from each new split\n", "- group the frames by split and take the max score value of the frames as the split score value\n", "- select top-k percent of the new splits"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "7ce1f4bb-32a0-4c34-bbf3-b0286290b4d8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Get the data into Pandas"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "collapsed": true, "inputWidgets": {}, "nuid": "f972956c-b5db-4054-a8a7-845b3c96aaa8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "SELECT *\n", "FROM scaled_split_meta_complete_vru"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "968dd22d-9837-431c-8031-5ac1cbd24c6b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["curr_df_vru = _sqldf.toPandas()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "collapsed": true, "inputWidgets": {}, "nuid": "*************-4714-b9fa-f3a54b92e7b6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "SELECT *\n", "FROM scaled_split_meta_complete_vehicle"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0a5bb622-d634-4a45-a232-22788ad3c54a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["curr_df_vehicle = _sqldf.toPandas()"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "065e1c31-79f1-4ea5-9baa-3788a54a6cb6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Group based on the split_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7914d80a-c958-4b28-b8dc-2febaf5baf28", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Groups the frames by the split_hash. Each split will have the score equal to the max score of its frames and the metadata of its frames (first frame is taken as reference here since it is assumed that the metadata don't change during the split)\n", "\n", "def group_by_splits(curr_df):\n", "    scaler = MinMaxScaler()\n", "    curr_df['scores__value'] = scaler.fit_transform(curr_df[['scores__value']])\n", "    grouped_curr_df = curr_df.groupby('split_hash').agg({\n", "        'scores__value': 'max',\n", "        'road_category': 'first',\n", "        'lane_count': 'first',\n", "        'speed_limit': 'first',\n", "        'time_of_day': 'first',\n", "        'tag_uid': 'first',\n", "        'positions__iso_country_code_alpha2': 'first',\n", "        'vin': 'first',\n", "        'start_time_date': 'first',\n", "        'gmdn_hash': 'first'\n", "    }).reset_index()\n", "    return grouped_curr_df\n", "\n", "\n", "grouped_curr_df_vru = group_by_splits(curr_df_vru)\n", "grouped_curr_df_vehicle = group_by_splits(curr_df_vehicle)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2cb612cb-2d07-46e0-aeae-bac22ecfd380", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["grouped_curr_df_vru"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "40634eef-269b-40b2-ac60-707affd818d7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["grouped_curr_df_vehicle"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "29f0f257-3634-415e-aba0-01bbab01060e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Estimate the unknowns in road_category"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b0d2ff8f-1cf5-4232-ba68-4c3e91786933", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Replaces the unknowns in the road_category (by rural or highway) using the decision tree trained above\n", "\n", "def estimate_road_category(grouped_curr_df):\n", "    for index, row in grouped_curr_df.iterrows():\n", "        if row['road_category'] == 'unknown':\n", "            pred = dec_tree.predict(pd.DataFrame({'lane_count': [row['lane_count']], 'speed_limit': [row['speed_limit']]}) )\n", "            grouped_curr_df.loc[index, 'road_category'] = pred[0]\n", "    return grouped_curr_df\n", "\n", "\n", "grouped_curr_df_vru = estimate_road_category(grouped_curr_df_vru)\n", "grouped_curr_df_vehicle = estimate_road_category(grouped_curr_df_vehicle)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4655d092-a556-430d-a4b3-7e0de146fc25", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["grouped_curr_df_vru"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8abd7b16-0bcb-46ad-a128-b2b1ab19a92b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["grouped_curr_df_vehicle"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "0aab229d-377c-4d17-a0cd-bf6c0bc5e4f3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# BOOSTED SAMPLING"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "f05c278d-e26f-4015-b1fd-1ec10209c6e9", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Calculate the first factor\n", "- All of the data should have the _VRU_ and the _VEHICLE_ score. Therefore, both grouped_curr_df_vru and grouped_curr_df_vehicle should ideally include the same data and lead to the same first factors. Therefore, only one will be used to compute the first factors for both. "]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "daa92ff7-9b93-4994-b64b-2885dd7c9642", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Connect both scores into one df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4dbf7344-b6f8-406a-a2fe-486e2b208795", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Merge the two dataframes (VRU, Vehicle) together. Basically, add the vehicle scores to the correct splits in the VRU dataframe (the metadata are the same).\n", "\n", "grouped_curr_df_vehicle_subset = grouped_curr_df_vehicle[['split_hash', 'scores__value']]\n", "merged_vru_vehicle_df = pd.merge(grouped_curr_df_vru, grouped_curr_df_vehicle_subset, on='split_hash', suffixes=('', '_vehicle'))\n", "merged_vru_vehicle_df.rename(columns={'scores__value': 'scores__value_vru'}, inplace=True)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "51a10881-2d9c-46fa-9c5c-73b0cdf435c9", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["merged_vru_vehicle_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ed796e9e-a5ef-45b6-a384-58ea31382383", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import itertools\n", "from copy import deepcopy\n", "import numpy as np\n", "import pandas as pd\n", "import matplotlib.pyplot as plt"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "e447c5dd-70c9-4cf3-9594-57881fd55af4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Check metadata \n", "- Check that all metadata values (included in the merged_vru_vehicle_df) are covered by the mapping (MAPPING_REAL_PROXY, MAPPING_PROXY_REAL) \n", "- If some are missing then that may lead to samples being left out\n", "- Country metadata are not in use (deliberately)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bbbc5aba-33a7-4e88-93dc-d94dadf69e3d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["print(set(merged_vru_vehicle_df['tag_uid']))\n", "print(set(merged_vru_vehicle_df['time_of_day']))\n", "print(set(merged_vru_vehicle_df['road_category']))\n", "# print(set(merged_vru_vehicle_df['positions__iso_country_code_alpha2']))"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c7269d01-1386-4921-8456-b31f96dcb3c4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Mappings between the real metadata categories/values and categories/values that the DYO team demands. Whatever is not mentioned here won't be considered for selection.\n", "\n", "MAPPING_REAL_PROXY = {\"road_category\": {'urban':'urban', 'rural':'rural', 'highway':'highway'},\n", "                      #\"positions__iso_country_code_alpha2\": {'PT':'countryother', 'NL':'countryother', 'BE':'countryother', 'FR':'countryother', 'AT':'countryother', 'DE':'DE', 'LU':'countryother', 'ES':'countryother', 'IT':'countryother', 'GR':'countryother', 'None':'countryother'},\n", "                      \"time_of_day\": {'Day':'Day', 'Night':'Night', 'Dawn':'<PERSON>D<PERSON>', 'Dusk':'<PERSON><PERSON><PERSON>'},\n", "                      \"tag_uid\": {'HeavyRain':'Rainy', 'Fog':'Foggy', 'HeavySnow':'HeavySnow', 'Snow':'HeavySnow', 'Rain':'Rainy', 'DenseFog':'Foggy', 'None':'weatherother', 'Hai<PERSON><PERSON><PERSON><PERSON>':'Rainy'}}\n", "MAPPING_PROXY_REAL = {\"road_category\": {'urban':['urban'], 'rural':['rural'], 'highway':['highway']},\n", "                      #\"positions__iso_country_code_alpha2\": {'countryother':['PT', 'NL', 'BE', 'FR', 'AT', 'LU', 'ES', 'IT', 'GR', 'None'], 'DE':['DE']}, \n", "                      \"time_of_day\": {'Day':['Day'], 'Night':['Night'], 'DawnDusk':['Dawn', 'Dusk']},\n", "                      \"tag_uid\": {'Rainy':['<PERSON><PERSON>ain', 'Rain', 'Hail<PERSON>lee<PERSON>'], 'Foggy':['DenseFog', 'Fog'], 'HeavySnow':['HeavySnow', 'Snow'], 'weatherother':['None']}}"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c4a8997d-1fd4-4eb2-9bca-a80dd4e8872a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Checking whether all values are present in the mappings.\n", "\n", "for category in MAPPING_REAL_PROXY:\n", "    for value in merged_vru_vehicle_df[category]:\n", "        assert value in MAPPING_REAL_PROXY[category], \"Value {value} is missing in the mapping MAPPING_REAL_PROXY\"\n", "\n", "for category in MAPPING_REAL_PROXY:\n", "    for value in MAPPING_REAL_PROXY[category]:\n", "        assert value in MAPPING_PROXY_REAL[category][MAPPING_REAL_PROXY[category][value]], \"Value {value} is missing in the mapping MAPPPING_PROXY_REAL\"\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "04465938-9b75-4515-a4e5-3f3388c8c19f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Target distribution as specified by the DYO team.\n", "\n", "TARGET_DISTRIBUTION_RELATIVE = {\"road_category\": {'urban':0.5, 'rural': 0.2, 'highway':0.3},\n", "                                #\"positions__iso_country_code_alpha2\": {'DE': 0.3, 'countryother': 0.7},\n", "                                \"time_of_day\": {'Day': 0.6, 'Night': 0.3, 'DawnDusk': 0.1},\n", "                                \"tag_uid\": {'Rainy': 0.25, 'Foggy': 0.1, 'HeavySnow': 0.1, 'weatherother': 0.55}}"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0aa128a5-2229-4b4a-9944-5bfb9ba0d515", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Create supercategory names (i.e. \"urban_Day_Rainy\", \"urban_Day_Foggy\" etc.).\n", "\n", "categories = list(TARGET_DISTRIBUTION_RELATIVE.keys())\n", "\n", "values = [list(subdict.keys()) for subdict in TARGET_DISTRIBUTION_RELATIVE.values()]\n", "combinations = list(itertools.product(*values))\n", "supercategories = [\"_\".join(combination) for combination in combinations]"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d926cb77-320e-4f3a-bcea-2e74c41838e7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Create dataframe for each supercategory using the corresponding data.\n", "\n", "supcat_dfs = {}\n", "for supcat in supercategories:\n", "    cat_values = supcat.split(\"_\")\n", "    conditions = [merged_vru_vehicle_df[key].isin(MAPPING_PROXY_REAL[key][value]) for key, value in zip(categories, cat_values)]\n", "    combined_conditions = conditions[0]\n", "    for condition in conditions[1:]:\n", "        combined_conditions &= condition\n", "    supcat_dfs[supcat] = merged_vru_vehicle_df[combined_conditions]"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "2600c0ad-d959-4185-a35e-4dd1a53375e2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### First Factor"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "33e14d0d-d07e-4c5b-b82f-00969b300647", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Compute the first factor for each supercategory.\n", "\n", "# counts\n", "supcat_cnt = {}\n", "for key in supcat_dfs:\n", "    supcat_cnt[key] = supcat_dfs[key].shape[0]\n", "\n", "# first factor\n", "first_factor = {}\n", "supcat_max_cnt = max(supcat_cnt.values())\n", "for key in supercategories:\n", "    if supcat_cnt[key] != 0:\n", "        first_factor[key] = supcat_max_cnt / supcat_cnt[key]\n", "    else:\n", "        first_factor[key] = 0\n", "\n", "for supcat in first_factor:\n", "    print(supcat, \"<>\", first_factor[supcat])"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "339bb0b0-76e8-456b-9cf4-e1f81ab94f7f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Supercategory distribution visualization"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bb27bb5c-932e-4002-8fb2-57a1a0bc5381", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Visualisation\n", "\n", "names = [supcat for supcat in supercategories]\n", "counts = [supcat_cnt[supcat] for supcat in supercategories]\n", "plt.figure(figsize=(10, 12))\n", "bars = plt.barh(names, counts, zorder=3)\n", "for bar in bars:\n", "    width = bar.get_width()\n", "    plt.text(width + 10, bar.get_y() + bar.get_height() / 2, str(width), ha='left', va='center', zorder=3)\n", "plt.gca().invert_yaxis()\n", "plt.subplots_adjust(left=0.3, right=0.9, top=0.9, bottom=0.1)\n", "plt.xlim(0,27000)\n", "plt.ylim(0-1,len(names))\n", "plt.xlabel('Number of Samples')\n", "plt.grid(zorder=0, alpha=0.5)\n", "plt.title('Number of Samples per Supercategory (Collected Data)')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "31beaccb-4f6c-4f00-82b1-b8a30b9ae66d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Second Factor"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "772766d4-10b6-4c4e-813a-6ca741f45ece", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Get the data"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "74fb3c05-9ed6-40e2-b9a8-5ee8835e535f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Get the data to Pandas"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6df4d101-01ba-4c9c-80d4-7dac005a035b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "SELECT *\n", "FROM already_selected_meta"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4bb1e32e-a36b-423a-82e7-f45e84225a0d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["sf_df = _sqldf.toPandas()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "25c84950-75d2-43bd-b7c3-8ef38a882ee9", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Groups the frames by the split_hash. Each split will have the metadata of its frames (first frame is taken as reference here since it is assumed that the metadata don't change during the split)\n", "\n", "grouped_sf_df = sf_df.groupby('split_hash').agg({\n", "    'road_category': 'first',\n", "    'lane_count': 'first',\n", "    'speed_limit': 'first',\n", "    'time_of_day': 'first',\n", "    'tag_uid': 'first',\n", "    'positions__iso_country_code_alpha2': 'first'\n", "}).reset_index()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d04f920e-d19e-459f-8fa6-e110ca8032d3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["grouped_sf_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f9287ef4-2838-4c25-96ae-63675e1bd3d3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Replaces the unknowns in the road_category (by rural or highway) using the decision tree trained above\n", "\n", "for index, row in grouped_sf_df.iterrows():\n", "    if row['road_category'] == 'unknown':\n", "        pred = dec_tree.predict(pd.DataFrame({'lane_count': [row['lane_count']], 'speed_limit': [row['speed_limit']]}) )\n", "        grouped_sf_df.loc[index, 'road_category'] = pred[0]"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "063faccf-c52a-47b7-aaa7-dc6abf3d3a53", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["grouped_sf_df"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b22a13fd-f29e-4dd7-9fb6-e923a0cc24b4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Second factor precomputation.\n", "\n", "CURRENT_DISTRIBUTION = deepcopy(TARGET_DISTRIBUTION_RELATIVE)\n", "for key in CURRENT_DISTRIBUTION:\n", "    for nested_key in CURRENT_DISTRIBUTION[key]:\n", "        CURRENT_DISTRIBUTION[key][nested_key] = 0\n", "\n", "CURRENT_DISTRIBUTION_ABSOLUTE = deepcopy(CURRENT_DISTRIBUTION)\n", "sample_cnt = grouped_sf_df.shape[0]\n", "for i in range(sample_cnt):\n", "    for category in CURRENT_DISTRIBUTION:\n", "        CURRENT_DISTRIBUTION[category][MAPPING_REAL_PROXY[category][grouped_sf_df.loc[i][category]]] += (1/sample_cnt)\n", "        CURRENT_DISTRIBUTION_ABSOLUTE[category][MAPPING_REAL_PROXY[category][grouped_sf_df.loc[i][category]]] += 1\n", "\n", "progress_value = deepcopy(CURRENT_DISTRIBUTION)\n", "for category in CURRENT_DISTRIBUTION:\n", "    for value in CURRENT_DISTRIBUTION[category]:\n", "        print(category, value, CURRENT_DISTRIBUTION[category][value])\n", "        progress_value[category][value] = TARGET_DISTRIBUTION_RELATIVE[category][value] / CURRENT_DISTRIBUTION[category][value]\n", "\n", "print(progress_value)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b190e6cb-95b2-4846-8ae4-154a584bc655", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Second factor computation.\n", "\n", "#second factor\n", "second_factor = {}\n", "for supcat in supercategories:\n", "    values = supcat.split(\"_\")\n", "    sf_sum = 0\n", "    for cat_idx, category in enumerate(progress_value):\n", "        sf_sum += progress_value[category][values[cat_idx]]\n", "    second_factor[supcat] = sf_sum/len(values)\n", "#print(second_factor)\n", "\n", "SECOND_FACTOR_LB = 0.005\n", "\n", "# sum second factor to one\n", "sf_sum = np.sum(list(second_factor.values()))\n", "for supcat in second_factor:\n", "    second_factor[supcat] /= sf_sum\n", "\n", "# account for the second factor LB\n", "num_of_factors = len(second_factor)\n", "percent_to_distribute = 1 - num_of_factors * SECOND_FACTOR_LB\n", "for supcat in second_factor:\n", "    second_factor[supcat] = SECOND_FACTOR_LB + percent_to_distribute * second_factor[supcat]\n", "\n", "for supcat in second_factor:\n", "    print(supcat, second_factor[supcat])"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3dc1ec2c-f82b-46a4-a2df-e9f083262840", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Visualisation\n", "\n", "names = [supcat for supcat in second_factor]\n", "counts = [second_factor[supcat] for supcat in second_factor]\n", "plt.figure(figsize=(10, 12))  \n", "bars = plt.barh(names, counts, zorder=3)\n", "for bar in bars:\n", "    width = np.round(bar.get_width(), 3)\n", "    plt.text(width + 0, bar.get_y() + bar.get_height() / 2, str(width), ha='left', va='center', zorder=3)\n", "plt.gca().invert_yaxis()  \n", "plt.subplots_adjust(left=0.3, right=0.9, top=0.9, bottom=0.1) \n", "#plt.xlim(0,0.07)\n", "#plt.ylim(0-1,len(names))\n", "plt.xlabel('Number of Samples')\n", "plt.grid(zorder=0, alpha=0.5)\n", "plt.title('Number of Samples per Supercategory (Collected Data)')\n", "plt.show()"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "c5137816-0393-472c-a40c-48ac34842a50", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Uncertainty Boosting"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "a7bc0a55-80ec-4182-841e-3841a7eb8cb3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Selection for _VRU_"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6019ea02-a08d-4f1b-b96e-0bcf28df15ca", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Transforms/boosts the uncertainty scores and selects the top-k (adjust the TOP_K parameter).\n", "\n", "pd.options.mode.chained_assignment = None\n", "\n", "TRADE_OFF = 1.0\n", "\n", "max_score = max(list(merged_vru_vehicle_df['scores__value_vru']))\n", "\n", "for supcat in supercategories:\n", "    \n", "    # sort the scores for the current supcat\n", "    supcat_dfs[supcat].sort_values(by='scores__value_vru', inplace=True, ascending=True)\n", "    \n", "    if first_factor[supcat] == 0 or second_factor[supcat] == 0:\n", "        continue\n", "\n", "    # get the PIT intervals based on the desired ratio\n", "    second_factor_new = second_factor[supcat]/min(second_factor.values())\n", "    a, b = max_score-(1/first_factor[supcat])*(1/second_factor_new)*max_score, max_score\n", "\n", "    # create the rescaled PIT projection\n", "    supcat_dfs[supcat]['i_vru'] = a + (np.arange(1, len(supcat_dfs[supcat])+1)-0.5) * (b-a) / len(supcat_dfs[supcat])\n", "\n", "    # calculate the distance along the projection\n", "    supcat_dfs[supcat]['score_i_vru'] = np.array(supcat_dfs[supcat]['scores__value_vru'])+((np.array(supcat_dfs[supcat]['i_vru']) - np.array(supcat_dfs[supcat]['scores__value_vru']))*TRADE_OFF)\n", "\n", "    # add the supercategory tag\n", "    supcat_dfs[supcat]['supercategory'] = [supcat] * supcat_dfs[supcat].shape[0]\n", "\n", "# pick the k-frames\n", "TOP_K = 0.2 # 0.3 = top 30%\n", "transformed_df = pd.concat([supcat_dfs[key] for key in supcat_dfs], axis=0)\n", "transformed_df.sort_values(by='score_i_vru', inplace=True, ascending=False)\n", "k_frames = int(len(transformed_df)*TOP_K)\n", "chosen_df_vru = transformed_df.head(k_frames)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bea9a8b7-aa1b-4486-8c66-3208ebad5e79", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["chosen_df_vru"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "9ee8a423-e2df-408c-877e-bc1faf8a9a07", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Selection for _VEHICLE_"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d4766cb9-70c4-426c-9e7b-644bd62201bc", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Transforms/boosts the uncertainty scores and selects the top-k (adjust the TOP_K parameter).\n", "\n", "pd.options.mode.chained_assignment = None\n", "\n", "TRADE_OFF = 1.0\n", "\n", "max_score = max(list(merged_vru_vehicle_df['scores__value_vehicle']))\n", "\n", "for supcat in supercategories:\n", "    \n", "    # sort the scores for the current supcat\n", "    supcat_dfs[supcat].sort_values(by='scores__value_vehicle', inplace=True, ascending=True)\n", "    \n", "    if first_factor[supcat] == 0 or second_factor[supcat] == 0:\n", "        continue\n", "\n", "    # get the PIT intervals based on the desired ratio\n", "    second_factor_new = second_factor[supcat]/min(second_factor.values())\n", "    a, b = max_score-(1/first_factor[supcat])*(1/second_factor_new)*max_score, max_score\n", "\n", "    # create the rescaled PIT projection\n", "    supcat_dfs[supcat]['i_vehicle'] = a + (np.arange(1, len(supcat_dfs[supcat])+1)-0.5) * (b-a) / len(supcat_dfs[supcat])\n", "\n", "    # calculate the distance along the projection\n", "    supcat_dfs[supcat]['score_i_vehicle'] = np.array(supcat_dfs[supcat]['scores__value_vehicle'])+((np.array(supcat_dfs[supcat]['i_vehicle']) - np.array(supcat_dfs[supcat]['scores__value_vehicle']))*TRADE_OFF)\n", "\n", "    # add the supercategory tag\n", "    supcat_dfs[supcat]['supercategory'] = [supcat] * supcat_dfs[supcat].shape[0]\n", "\n", "# pick the k-frames\n", "TOP_K = 0.2 # 0.3 = top 30%\n", "transformed_df = pd.concat([supcat_dfs[key] for key in supcat_dfs], axis=0)\n", "transformed_df.sort_values(by='score_i_vehicle', inplace=True, ascending=False)\n", "k_frames = int(len(transformed_df)*TOP_K)\n", "chosen_df_vehicle = transformed_df.head(k_frames)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2181c3a7-cc1f-4b34-9fa7-9e112836564d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["chosen_df_vehicle"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "dd841d44-55ff-4fe8-b341-df84ed630b09", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Check the overlap so that there isn't too few unique splits. "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1a7cd20f-ab1e-4b5b-b68d-c003191a54c2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Adjust the TOP_K and rerun the transform/selection if there is too much or too few unique splits.\n", "\n", "common_values_set = set(chosen_df_vehicle['split_hash']).intersection(set(chosen_df_vru['split_hash']))\n", "\n", "num_common_values = len(common_values_set)\n", "\n", "print(f\"Number of common values: {num_common_values}\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "0dad682a-9b56-44bf-ada5-19c1cfe855b7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Combine the samples and pick the important data"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d55552fd-4666-4dac-af71-7998e3dc47e1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["all_chosen_df = pd.concat([chosen_df_vehicle, chosen_df_vru], ignore_index=True)\n", "\n", "selected_columns = all_chosen_df[['split_hash', 'vin', 'start_time_date', 'gmdn_hash']]\n", "\n", "split_hashes_df = selected_columns.drop_duplicates(subset=['split_hash'])\n", "\n", "spark_df = spark.createDataFrame(split_hashes_df)\n", "spark_df.write.mode(\"overwrite\").saveAsTable(BEV_SCHEMA + \"temp30perc\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bd85f3e5-160d-4496-9ec7-a1053406d7c2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "select count(*) from temp30perc"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "b4f37d9c-70c6-41e0-a055-24447a9d7f01", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Use this query at the very end to finalize the selection\n", "- Change the selection_version according to the number of the current selection"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "554f09a6-2fdb-4614-965b-af3f94bf9d94", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "select count(*), selection_version from viper_dsp_sbx.mm_bev.selected_split group by all"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "54ec75c8-eed4-49eb-a843-a25eafa47c73", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "\n", "-- TODO: execute at the end to persist selection and adapt selection version\n", "\n", "-- INSERT INTO viper_dsp_sbx.mm_bev.selected_split (split_hash, VIN, start_time_date, selection_version, gmdn_hash)\n", "-- SELECT split_hash, VIN, start_time_date, 7, gmdn_hash\n", "-- FROM temp30perc;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "02081d51-b1ea-403f-ab72-a4e2606e974e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### 1% subselection for _VRU_ and _VEHICLE_ manual labeling\n", "- Sampled from the selected e.g. 30% and only based on the uncertainty scores"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7ad3e6e1-b1af-47e1-b29c-93c746ed877c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_30_vru_sorted = chosen_df_vru.sort_values(by='scores__value_vru', ascending=False)\n", "k_frames = int(len(df_30_vru_sorted)*0.01)\n", "df_30_vru_sorted_chosen = df_30_vru_sorted.head(k_frames)\n", "\n", "selected_columns = df_30_vru_sorted_chosen[['split_hash']]\n", "split_hashes_df = selected_columns.drop_duplicates(subset=['split_hash'])\n", "spark_df = spark.createDataFrame(split_hashes_df)\n", "# spark_df.createOrReplaceTempView(\"temp1perc_vru\")\n", "spark_df.write.mode(\"overwrite\").saveAsTable(BEV_SCHEMA + \"temp1perc_vru\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9d3c30e8-8b75-4c46-ae02-961c1df6987f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "select * from temp1perc_vru"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3aaef29a-605d-4e6e-a392-548d0f43b26a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "\n", "-- TODO: execute at the end to persist selection and adapt target tables\n", "\n", "-- CREATE\n", "-- OR REPLACE TABLE viper_dsp_sbx.mm_bev.selected_split_hashes_1percent_vru_selection7\n", "-- select *\n", "-- from temp1perc_vru"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "77a2218f-48d2-49ea-9ae7-d7fa7cd79b63", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_30_vehicle_sorted = chosen_df_vehicle.sort_values(by='scores__value_vehicle', ascending=False)\n", "k_frames = int(len(df_30_vehicle_sorted)*0.01)\n", "df_30_vehicle_sorted_chosen = df_30_vehicle_sorted.head(k_frames)\n", "\n", "selected_columns = df_30_vehicle_sorted_chosen[['split_hash']]\n", "split_hashes_df = selected_columns.drop_duplicates(subset=['split_hash'])\n", "spark_df = spark.createDataFrame(split_hashes_df)\n", "#spark_df.createOrReplaceTempView(\"temp1perc_vehicle\")\n", "\n", "spark_df.write.mode(\"overwrite\").saveAsTable(BEV_SCHEMA + \"temp1perc_vehicle\")\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f1701354-5711-4698-8d5f-c18cafa61dbe", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "select * from temp1perc_vehicle"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e999df0e-5b40-44c3-9904-d4f47523c92d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "\n", "-- TODO: execute at the end to persist selection and adapt target tables\n", "\n", "-- Save the selected split_hashes somewhere.\n", "\n", "-- CREATE\n", "-- OR REPLACE TABLE viper_dsp_sbx.mm_bev.selected_split_hashes_1percent_vehicle_selection7\n", "-- select *\n", "-- from temp1perc_vehicle"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "90212f88-de93-4da3-bc9c-17a0dd0c057e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "-- TODO: Run this at the end of the execution\n", "\n", "-- SHOULD B<PERSON> DONE AT THE VERY END FOR BOOKKEEPING. Updates that the new frames were used for selection (so they are not used again next time)\n", "\n", "-- UPDATE viper_dsp_sbx.mm_bev.recorded_splits_active\n", "-- SET considered_for_selection = true\n", "-- WHERE EXISTS (\n", "--    SELECT 1\n", "--    FROM scaled_split_meta_complete_vehicle\n", "--    WHERE scaled_split_meta_complete_vehicle.split_hash = viper_dsp_sbx.mm_bev.recorded_splits_active.split_hash\n", "-- );"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "3b971fb8-6dfe-42ba-9c46-c6b4379dc7a7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 704428819829339, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "MMBEV AL manual selection", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}