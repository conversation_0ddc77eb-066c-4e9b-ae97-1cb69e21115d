# Databricks notebook source
# MAGIC %sql
# MAGIC USE CATALOG viper_dsp_sbx;
# MAGIC USE SCHEMA mm_bev;

# COMMAND ----------

# MAGIC %md
# MAGIC Setup a continuous table of all input splits, taking the current archiving into account.

# COMMAND ----------

# MAGIC %sql
# MAGIC -- This table was run initially to set up the first version of recorded_splits_active and should not be rerun, as splits are archieved on a continuous basis.
# MAGIC -- But we want to have the set of splits the were active during selection.
# MAGIC --
# MAGIC -- CREATE
# MAGIC -- OR REPLACE TABLE recorded_splits_pre
# MAGIC -- SELECT
# MAGIC --   splitinfo.file_hash AS split_hash
# MAGIC --   ,date(splitinfo.start_time_utc) AS start_time_date
# MAGIC --   ,driveinfo.VIN AS VIN
# MAGIC -- FROM
# MAGIC --   silver.mdd.`datamanagement-split-info` as splitinfo
# MAGIC --   LEFT JOIN silver.tds.parents USING (file_hash)
# MAGIC --   LEFT JOIN silver.mdd.`datamanagement-drive-info` AS driveinfo ON silver.tds.parents.parent_file_hash = driveinfo.file_hash
# MAGIC -- where (VIN == "WAUZZZF16PD000067" and splitinfo.start_time_utc >= "2024-01-19" and splitinfo.start_time_utc < "2024-08-18"
# MAGIC --  or VIN == "WV1ZZZEB7PH000462"
# MAGIC --  or VIN == "WAUZZZF19LD017469" and splitinfo.start_time_utc >= "2024-06-16")

# COMMAND ----------

# MAGIC %md
# MAGIC Do a continuous update of `recorded_splits_active`. This table can be extended to add additional vehicles

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE
# MAGIC OR REPLACE TABLE viper_dsp_sbx.mm_bev.common_splits
# MAGIC SELECT
# MAGIC   splitinfo.file_hash AS split_hash,
# MAGIC   date(splitinfo.start_time_utc) AS start_time_date,
# MAGIC   parents.parent_file_hash as gmdn_hash,
# MAGIC   driveinfo.VIN AS VIN
# MAGIC FROM
# MAGIC   silver.mdd.`datamanagement-split-info` as splitinfo
# MAGIC   LEFT JOIN silver.tds.parents USING (file_hash)
# MAGIC   LEFT JOIN silver.mdd.`datamanagement-drive-info` AS driveinfo ON silver.tds.parents.parent_file_hash = driveinfo.file_hash
# MAGIC WHERE
# MAGIC   (
# MAGIC     VIN = "WAUZZZF16PD000067"
# MAGIC     AND (
# MAGIC       splitinfo.start_time_utc >= "2024-01-19"
# MAGIC       AND splitinfo.start_time_utc < "2024-08-18"
# MAGIC       OR splitinfo.start_time_utc > "2024-09-30"
# MAGIC     )
# MAGIC     OR VIN = "WV1ZZZEB7PH000462"
# MAGIC     OR VIN = "WAUZZZF19LD017469"
# MAGIC     AND splitinfo.start_time_utc >= "2024-06-16"
# MAGIC     OR (VIN = "WV1ZZZEB4PH000631" AND splitinfo.start_time_utc > "2024-12-03")
# MAGIC     OR (VIN = "WAUZZZF1XPD000055" AND splitinfo.start_time_utc > "2024-12-03")
# MAGIC     OR (VIN = "WAUZZZF1XPD000069" AND splitinfo.start_time_utc > "2024-12-03")
# MAGIC     OR (VIN = "WAUZZZF26LN021711" AND splitinfo.start_time_utc > '2025-01-22')
# MAGIC   )

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Create a temp view of the splits for the tracked vehicles
# MAGIC CREATE OR REPLACE TEMPORARY VIEW recorded_splits_pre AS (
# MAGIC   SELECT *
# MAGIC   FROM common_splits
# MAGIC   WHERE split_hash NOT IN (
# MAGIC     SELECT split_hash FROM recorded_splits_active
# MAGIC   )
# MAGIC )

# COMMAND ----------

# MAGIC %sql
# MAGIC -- Determine the active splits and join them into recorded_splits_active
# MAGIC INSERT INTO 
# MAGIC   recorded_splits_active (split_hash, start_time_date, VIN, gmdn_hash, considered_for_selection) (
# MAGIC     With temp1 AS (
# MAGIC       SELECT
# MAGIC         recorded_splits_pre.*,
# MAGIC         silver.tds.parents.file_hash as child_hash
# MAGIC       FROM
# MAGIC         recorded_splits_pre
# MAGIC         LEFT JOIN silver.tds.parents ON recorded_splits_pre.split_hash == silver.tds.parents.parent_file_hash
# MAGIC     )
# MAGIC     SELECT
# MAGIC       split_hash,
# MAGIC       start_time_date,
# MAGIC       VIN,
# MAGIC       gmdn_hash,
# MAGIC       FALSE AS considered_for_selection
# MAGIC     FROM
# MAGIC       temp1
# MAGIC       LEFT JOIN silver.tds.file_entries ON child_hash = silver.tds.file_entries.file_hash
# MAGIC     WHERE
# MAGIC       silver.tds.file_entries.content_type = 'video/x-msvideo'
# MAGIC     GROUP BY
# MAGIC       ALL
# MAGIC     HAVING
# MAGIC       COUNT(*) = SUM(
# MAGIC         case
# MAGIC           when silver.tds.file_entries.file_state = 'ACTIVE' then 1
# MAGIC           else 0
# MAGIC         end
# MAGIC       )
# MAGIC   )

# COMMAND ----------

# MAGIC %sql
# MAGIC select
# MAGIC   count(*)
# MAGIC from
# MAGIC   recorded_splits_active

# COMMAND ----------

# MAGIC %md
# MAGIC We enhance the recorded splits by the selected splits

# COMMAND ----------

# MAGIC %sql
# MAGIC -- enhance active recorded & active splits with selections
# MAGIC CREATE
# MAGIC OR REPLACE TABLE recorded_splits_w_selection AS
# MAGIC SELECT
# MAGIC   recorded_splits_active.split_hash,
# MAGIC   recorded_splits_active.start_time_date,
# MAGIC   recorded_splits_active.VIN,
# MAGIC   recorded_splits_active.gmdn_hash,
# MAGIC   IF(selected.split_hash IS NOT NULL, True, False) AS selected
# MAGIC FROM
# MAGIC   recorded_splits_active
# MAGIC   LEFT JOIN selected_split as selected using (split_hash);
# MAGIC SELECT
# MAGIC   count(*),
# MAGIC   selected
# MAGIC FROM
# MAGIC   recorded_splits_w_selection
# MAGIC group by
# MAGIC   all

# COMMAND ----------

# MAGIC %md
# MAGIC We enhance that data with metadata

# COMMAND ----------

# MAGIC %sql
# MAGIC -- enhance active recorded & active splits with selections and metadata
# MAGIC CREATE
# MAGIC OR REPLACE TABLE recorded_splits_w_metadata
# MAGIC SELECT
# MAGIC   splits.start_time_date,
# MAGIC   splits.selected,
# MAGIC   splits.gmdn_hash,
# MAGIC   gold_trajectory_frames.*
# MAGIC from
# MAGIC   recorded_splits_w_selection as splits
# MAGIC   join (
# MAGIC     SELECT
# MAGIC       *
# MAGIC     FROM
# MAGIC       dd_leadership_pub_sbx.timeseries.gold_trajectory_frames QUALIFY ROW_NUMBER() OVER(
# MAGIC         PARTITION BY split_hash
# MAGIC         ORDER BY
# MAGIC           frame_number
# MAGIC       ) = 1
# MAGIC   ) as gold_trajectory_frames using (split_hash);
# MAGIC select
# MAGIC   count(*)
# MAGIC FROM
# MAGIC   recorded_splits_w_metadata

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE
# MAGIC OR REPLACE TABLE viper_dsp_sbx.mm_bev.extraction_status with selected_splits as (
# MAGIC   select
# MAGIC     *
# MAGIC   from
# MAGIC     viper_dsp_sbx.mm_bev.selected_split
# MAGIC ),
# MAGIC selected_extractions as (
# MAGIC   select
# MAGIC     silver.mdd.dsp_de_split_extracted.*,
# MAGIC     file_hash as split_hash
# MAGIC   from
# MAGIC     silver.mdd.dsp_de_split_extracted
# MAGIC     join selected_splits on selected_splits.split_hash = file_hash
# MAGIC   where
# MAGIC     entry_state == "FINISHED"
# MAGIC ),
# MAGIC selected_hesai_front as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as hesai_front
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "lidar_qt_front"
# MAGIC ),
# MAGIC selected_hesai_left as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as hesai_left
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "lidar_qt_left"
# MAGIC ),
# MAGIC selected_hesai_right as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as hesai_right
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "lidar_qt_right"
# MAGIC ),
# MAGIC selected_hesai_back as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as hesai_back
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "lidar_qt_back"
# MAGIC ),
# MAGIC selected_velodyne as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as velodyne
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "lidar_velodyne"
# MAGIC ),
# MAGIC selected_innoviz as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as innoviz
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "lidar_innoviz"
# MAGIC ),
# MAGIC selected_can as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as can
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "can"
# MAGIC ),
# MAGIC selected_flexray as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as flexray
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "flexray"
# MAGIC ),
# MAGIC selected_fc1 as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as fc1
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "image_fc1"
# MAGIC     and entry :settings :frequency = 15
# MAGIC     and entry :settings :rgb = True
# MAGIC ),
# MAGIC selected_tv_front as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as tv_front
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "image_tv_front"
# MAGIC     and entry :settings :frequency = 30
# MAGIC     and entry :settings :rgb = True
# MAGIC ),
# MAGIC selected_tv_left as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as tv_left
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "image_tv_left"
# MAGIC     and entry :settings :frequency = 30
# MAGIC     and entry :settings :rgb = True
# MAGIC ),
# MAGIC selected_tv_right as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as tv_right
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "image_tv_right"
# MAGIC     and entry :settings :frequency = 30
# MAGIC     and entry :settings :rgb = True
# MAGIC ),
# MAGIC selected_tv_rear as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as tv_rear
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "image_tv_rear"
# MAGIC     and entry :settings :frequency = 30
# MAGIC     and entry :settings :rgb = True
# MAGIC ),
# MAGIC selected_cr_front_left as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as cr_front_left
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "radar_cr_front_left"
# MAGIC ),
# MAGIC selected_cr_front_right as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as cr_front_right
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "radar_cr_front_right"
# MAGIC ),
# MAGIC selected_cr_rear_left as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as cr_rear_left
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "radar_cr_rear_left"
# MAGIC ),
# MAGIC selected_cr_rear_right as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as cr_rear_right
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "radar_cr_rear_right"
# MAGIC ),
# MAGIC selected_lrr5_front as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as lrr5_front
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "radar_lrr5_front"
# MAGIC ),
# MAGIC selected_lrr5_rear as (
# MAGIC   select
# MAGIC     distinct split_hash,
# MAGIC     True as lrr5_rear
# MAGIC   from
# MAGIC     selected_extractions
# MAGIC   where
# MAGIC     stream = "radar_lrr5_rear"
# MAGIC )
# MAGIC select
# MAGIC   *,
# MAGIC   CASE
# MAGIC     WHEN hesai_front = True
# MAGIC     and hesai_left = True
# MAGIC     and hesai_right = True
# MAGIC     and hesai_back = True
# MAGIC     AND velodyne = True
# MAGIC     AND innoviz = True
# MAGIC     AND can = True
# MAGIC     AND flexray = True
# MAGIC     AND fc1 = True
# MAGIC     AND tv_front = True
# MAGIC     and tv_left = True
# MAGIC     and tv_right = True
# MAGIC     and tv_rear = True THEN CASE
# MAGIC       WHEN cr_front_left = True
# MAGIC       AND cr_front_right = True
# MAGIC       AND cr_rear_left = True
# MAGIC       AND cr_rear_right = True
# MAGIC       AND lrr5_front = True
# MAGIC       AND lrr5_rear = True THEN "FINISHED"
# MAGIC       ELSE "RADAR_MISSING"
# MAGIC     END
# MAGIC     ELSE "NOT_FINISHED"
# MAGIC   END as status
# MAGIC from
# MAGIC   selected_splits
# MAGIC   left join selected_hesai_front using (split_hash)
# MAGIC   left join selected_hesai_left using (split_hash)
# MAGIC   left join selected_hesai_right using (split_hash)
# MAGIC   left join selected_hesai_back using (split_hash)
# MAGIC   left join selected_velodyne using (split_hash)
# MAGIC   left join selected_innoviz using (split_hash)
# MAGIC   left join selected_can using (split_hash)
# MAGIC   left join selected_flexray using (split_hash)
# MAGIC   left join selected_fc1 using (split_hash)
# MAGIC   left join selected_tv_front using (split_hash)
# MAGIC   left join selected_tv_left using (split_hash)
# MAGIC   left join selected_tv_right using (split_hash)
# MAGIC   left join selected_tv_rear using (split_hash)
# MAGIC   left join selected_cr_front_left using (split_hash)
# MAGIC   left join selected_cr_front_right using (split_hash)
# MAGIC   left join selected_cr_rear_left using (split_hash)
# MAGIC   left join selected_cr_rear_right using (split_hash)
# MAGIC   left join selected_lrr5_front using (split_hash)
# MAGIC   left join selected_lrr5_rear using (split_hash);
# MAGIC SELECT
# MAGIC   *
# MAGIC FROM
# MAGIC   viper_dsp_sbx.mm_bev.extraction_status

# COMMAND ----------

# MAGIC %sql
# MAGIC SELECT
# MAGIC   status,
# MAGIC   VIN,
# MAGIC   selection_version,
# MAGIC   count(*)
# MAGIC from
# MAGIC   viper_dsp_sbx.mm_bev.extraction_status
# MAGIC GROUP BY
# MAGIC   ALL

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE
# MAGIC OR REPLACE TABLE viper_dsp_sbx.mm_bev.hitl_corrected_splits AS (
# MAGIC   WITH extracted_splits AS (
# MAGIC     SELECT
# MAGIC       DISTINCT dsp_de_image_extended.split_hash
# MAGIC     FROM
# MAGIC       viper_dsp_sbx.mm_bev.extraction_status
# MAGIC       JOIN silver.mdd.dsp_de_image_extended using (split_hash)
# MAGIC     WHERE
# MAGIC       status = 'FINISHED'
# MAGIC   )
# MAGIC   SELECT
# MAGIC     split_hash,
# MAGIC     parent_file_hash,
# MAGIC     CASE
# MAGIC       WHEN corrections_flattened.recording_sha IS NOT NULL THEN 'Corrected'
# MAGIC       ELSE 'Not Corrected'
# MAGIC     END AS hash_existence
# MAGIC   FROM
# MAGIC     extracted_splits
# MAGIC     JOIN silver.tds.parents ON extracted_splits.split_hash = parents.file_hash
# MAGIC     LEFT JOIN (
# MAGIC       select
# MAGIC         distinct recording_sha
# MAGIC       from
# MAGIC         viper_dsp_sbx.hitl_corrections_prod.corrections_flattened
# MAGIC     ) corrections_flattened ON parent_file_hash = corrections_flattened.recording_sha
# MAGIC );

# COMMAND ----------

# MAGIC %md
# MAGIC # upload first selection
# MAGIC

# COMMAND ----------

# MAGIC %sql
# MAGIC select
# MAGIC   count(*),
# MAGIC   vin
# MAGIC   
# MAGIC from
# MAGIC   viper_dsp_sbx.mm_bev.recorded_splits_active
# MAGIC   group by all

# COMMAND ----------

# MAGIC %sql
# MAGIC select
# MAGIC   count(*),
# MAGIC   vin,
# MAGIC   selection_version
# MAGIC from
# MAGIC   viper_dsp_sbx.mm_bev.selected_split
# MAGIC group by
# MAGIC   all

# COMMAND ----------

# MAGIC %md
# MAGIC ## BEV Autolabel Dashboard Updates
# MAGIC
# MAGIC Produces two tables at the moment: 
# MAGIC - viper_dsp_sbx.mm_bev.recorded_extracted_diff
# MAGIC - viper_dsp_sbx.mm_bev.CERO_MDM_diff
# MAGIC
# MAGIC (cell-specific description is in respective cells)

# COMMAND ----------

# MAGIC %sql
# MAGIC
# MAGIC -- Counts the number of recorded splits.
# MAGIC -- Where recorded means that they are present in silver.cero.file_events.
# MAGIC -- The fc1_avi_file_count is used to count the splits since there is only one avi file per camera (fc1) per split.
# MAGIC
# MAGIC CREATE OR REPLACE TEMP VIEW CERO_per_veh_per_day AS
# MAGIC SELECT
# MAGIC   recorded.vehicle_identification_number,
# MAGIC   recorded.event_date,
# MAGIC   recorded.fc1_avi_file_count,
# MAGIC   mapping.vehicle_name
# MAGIC FROM
# MAGIC   (
# MAGIC     SELECT
# MAGIC       `vehicle_identification_number`,
# MAGIC       DATE(`event_registered_at`) AS `event_date`,
# MAGIC       COUNT(*) AS `fc1_avi_file_count`
# MAGIC     FROM
# MAGIC       `silver`.`cero`.`file_events`
# MAGIC     WHERE
# MAGIC       (   `vehicle_identification_number` = 'WAUZZZF16PD000067' 
# MAGIC       OR  `vehicle_identification_number` = 'WV1ZZZEB7PH000462' 
# MAGIC       OR  `vehicle_identification_number` = 'WAUZZZF19LD017469'
# MAGIC       OR (`vehicle_identification_number` = 'WV1ZZZEB4PH000631' AND `event_registered_at` > '2024-12-03')
# MAGIC       OR (`vehicle_identification_number` = 'WAUZZZF1XPD000055' AND `event_registered_at` > '2024-12-03')
# MAGIC       OR (`vehicle_identification_number` = 'WAUZZZF1XPD000069' AND `event_registered_at` > '2024-12-03')
# MAGIC       OR (`vehicle_identification_number` = "WAUZZZF26LN021711" AND `event_registered_at` > '2025-01-22'))
# MAGIC       AND `event` = 'FileCreated'
# MAGIC       AND `item_full_path` ILIKE '%fc1.avi'
# MAGIC     GROUP BY
# MAGIC       `vehicle_identification_number`,
# MAGIC       `event_date`
# MAGIC   ) recorded
# MAGIC LEFT JOIN
# MAGIC   (SELECT DISTINCT VIN, vehicle_name FROM silver.calstore.devices_installed) mapping
# MAGIC ON
# MAGIC   recorded.vehicle_identification_number = mapping.VIN;

# COMMAND ----------

# MAGIC %sql
# MAGIC
# MAGIC -- Preparation for update of viper_dsp_sbx.mm_bev.recorded_splits_active_inactive (all uploaded splits including archived)
# MAGIC
# MAGIC CREATE OR REPLACE TEMPORARY VIEW recorded_splits_pre AS (
# MAGIC   SELECT *
# MAGIC   FROM common_splits
# MAGIC   WHERE split_hash NOT IN (
# MAGIC     SELECT split_hash FROM recorded_splits_active_inactive
# MAGIC   )
# MAGIC )

# COMMAND ----------

# MAGIC %sql
# MAGIC
# MAGIC -- Update of viper_dsp_sbx.mm_bev.recorded_splits_active_inactive.
# MAGIC -- It follows the same logic as viper_dsp_sbx.mm_bev.recorded_splits_active that is used for selection excepts that it does not filter out the archived splits.
# MAGIC -- Where archived splits are those that don't satisfy the followinf condition
# MAGIC     -- HAVING
# MAGIC       -- COUNT(*) = SUM(
# MAGIC         -- case
# MAGIC           -- when silver.tds.file_entries.file_state = 'ACTIVE' then 1
# MAGIC           -- else 0
# MAGIC         -- end
# MAGIC       -- )
# MAGIC
# MAGIC INSERT INTO 
# MAGIC recorded_splits_active_inactive (split_hash, start_time_date, VIN, gmdn_hash, considered_for_selection) (
# MAGIC     With temp1 AS (
# MAGIC       SELECT
# MAGIC         recorded_splits_pre.*,
# MAGIC         silver.tds.parents.file_hash as child_hash
# MAGIC       FROM
# MAGIC         recorded_splits_pre
# MAGIC         LEFT JOIN silver.tds.parents ON recorded_splits_pre.split_hash == silver.tds.parents.parent_file_hash
# MAGIC     )
# MAGIC     SELECT
# MAGIC       split_hash,
# MAGIC       start_time_date,
# MAGIC       VIN,
# MAGIC       gmdn_hash,
# MAGIC       FALSE AS considered_for_selection
# MAGIC     FROM
# MAGIC       temp1
# MAGIC       LEFT JOIN silver.tds.file_entries ON child_hash = silver.tds.file_entries.file_hash
# MAGIC     WHERE
# MAGIC       silver.tds.file_entries.content_type = 'video/x-msvideo'
# MAGIC     GROUP BY
# MAGIC       ALL
# MAGIC   )

# COMMAND ----------

# MAGIC %sql
# MAGIC
# MAGIC -- Counts the number of uploaded splits including archived (viper_dsp_sbx.mm_bev.recorded_splits_active_inactive).
# MAGIC -- Where uploaded means they are already present in viper_dsp_sbx.mm_bev.recorded_splits_active_inactive.
# MAGIC
# MAGIC CREATE OR REPLACE TEMP VIEW MDM_per_veh_per_day AS
# MAGIC SELECT
# MAGIC   recorded.VIN,
# MAGIC   recorded.start_time_date,
# MAGIC   COUNT(*) AS count,
# MAGIC   mapping.vehicle_name
# MAGIC FROM
# MAGIC   viper_dsp_sbx.mm_bev.recorded_splits_active_inactive recorded
# MAGIC LEFT JOIN
# MAGIC   (SELECT DISTINCT VIN, vehicle_name FROM silver.calstore.devices_installed) mapping
# MAGIC ON
# MAGIC   recorded.VIN = mapping.VIN
# MAGIC GROUP BY
# MAGIC   recorded.VIN,
# MAGIC   recorded.start_time_date,
# MAGIC   mapping.vehicle_name

# COMMAND ----------

# MAGIC %sql
# MAGIC
# MAGIC -- Updates the table that shows the difference between recorded and uploaded splits.
# MAGIC -- Where recorded means that they are present in CERO_per_veh_per_day and uploaded means they are already present in MDM_per_veh_per_day.
# MAGIC -- The fc1_avi_file_count is used to count the splits since there is only one avi file per camera (fc1) per split.
# MAGIC
# MAGIC CREATE
# MAGIC OR REPLACE TABLE viper_dsp_sbx.mm_bev.CERO_MDM_diff
# MAGIC SELECT 
# MAGIC     c.vehicle_name,
# MAGIC     c.fc1_avi_file_count,
# MAGIC     c.event_date,
# MAGIC     COALESCE(m.count, 0) AS count, 
# MAGIC     (c.fc1_avi_file_count - COALESCE(m.count, 0)) AS difference
# MAGIC FROM CERO_per_veh_per_day AS c
# MAGIC LEFT JOIN MDM_per_veh_per_day AS m
# MAGIC ON c.event_date = m.start_time_date AND c.vehicle_name = m.vehicle_name
# MAGIC WHERE 
# MAGIC     (c.vehicle_name = 'Q816' AND (c.event_date BETWEEN '2024-01-19' AND '2024-08-18' OR c.event_date > '2024-09-30'))
# MAGIC     OR (c.vehicle_name = 'Q807' AND c.event_date > '2024-06-16')
# MAGIC     OR (c.vehicle_name = 'B114')
# MAGIC     OR (c.vehicle_name = 'B116' AND c.event_date > '2024-12-03')
# MAGIC     OR (c.vehicle_name = 'Q817 ' AND c.event_date > '2024-12-03')
# MAGIC     OR (c.vehicle_name = 'Q818' AND c.event_date > '2024-12-03')
# MAGIC     OR (c.vehicle_name = "A602" AND c.event_date > '2025-01-22')

# COMMAND ----------

# MAGIC %sql
# MAGIC
# MAGIC -- Updates the table that tracks the number of non-extracted splits per car each day. 
# MAGIC -- Non-extracted split is defined as a split that is found in viper_dsp_sbx.mm_bev.recorded_splits_active but not found in silver.mdd.dsp_de_image
# MAGIC -- (since the extraction was likely run but failed or wasn't extracted at all).
# MAGIC
# MAGIC CREATE
# MAGIC OR REPLACE TABLE viper_dsp_sbx.mm_bev.recorded_extracted_diff
# MAGIC SELECT 
# MAGIC     FIRST(viper_dsp_sbx.mm_bev.recorded_splits_active.VIN) AS VIN,
# MAGIC     viper_dsp_sbx.mm_bev.recorded_splits_active.start_time_date, 
# MAGIC     mapping.vehicle_name,
# MAGIC     COUNT(DISTINCT viper_dsp_sbx.mm_bev.recorded_splits_active.split_hash) AS unique_split_hash_count
# MAGIC FROM 
# MAGIC     viper_dsp_sbx.mm_bev.recorded_splits_active
# MAGIC LEFT JOIN 
# MAGIC     silver.mdd.dsp_de_image
# MAGIC ON 
# MAGIC     viper_dsp_sbx.mm_bev.recorded_splits_active.split_hash = silver.mdd.dsp_de_image.split_hash
# MAGIC LEFT JOIN 
# MAGIC     (SELECT DISTINCT VIN, vehicle_name FROM silver.calstore.devices_installed) mapping
# MAGIC ON 
# MAGIC     viper_dsp_sbx.mm_bev.recorded_splits_active.VIN = mapping.VIN
# MAGIC WHERE 
# MAGIC     silver.mdd.dsp_de_image.split_hash IS NULL
# MAGIC     AND viper_dsp_sbx.mm_bev.recorded_splits_active.considered_for_selection = false
# MAGIC GROUP BY 
# MAGIC     viper_dsp_sbx.mm_bev.recorded_splits_active.start_time_date, 
# MAGIC     mapping.vehicle_name;
