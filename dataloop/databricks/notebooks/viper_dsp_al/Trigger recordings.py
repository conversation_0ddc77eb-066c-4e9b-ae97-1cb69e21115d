# Databricks notebook source
# MAGIC %md
# MAGIC # create table only when new data are extracted (takes 2 minutes for shared M compute)

# COMMAND ----------

# MAGIC %sql
# MAGIC -- create table matching datamanagement_co_driver_tags with dsp_de_image_extended to be able find most accurate images in next step
# MAGIC CREATE
# MAGIC OR REPLACE TABLE viper_dsp_sbx.active_learning.al_triggers_X_de_image AS
# MAGIC SELECT
# MAGIC   de.file_hash AS de_file_hash,
# MAGIC   de.split_hash,
# MAGIC   de.image_id,
# MAGIC   de.recorded_at,
# MAGIC   dt.file_hash AS dt_file_hash,
# MAGIC   dt.tag_start_at,
# MAGIC   es.start_time_utc as sequence_start_at
# MAGIC FROM
# MAGIC   (
# MAGIC     SELECT
# MAGIC       *
# MAGIC     FROM
# MAGIC       silver.mdd.dsp_de_image_extended
# MAGIC     WHERE
# MAGIC       rectification__type IS NULL
# MAGIC       AND context = 'B'
# MAGIC       AND color_space = 'RGB'
# MAGIC   ) AS de
# MAGIC   JOIN silver.mdd.datamanagement_co_driver_tags AS dt ON de.split_hash = dt.file_hash
# MAGIC   JOIN silver.mdd.`datamanagement-split-info` AS es ON de.split_hash = es.file_hash
# MAGIC WHERE
# MAGIC   dt.tag_uid = 'ViperModel-HighUncertainty-TL';

# COMMAND ----------

# MAGIC %md
# MAGIC #### switch to python compute and select those within  - 1000 millisecod interval to match appropriate frame, since trigger time is not synced

# COMMAND ----------

!pip install azure-identity

# COMMAND ----------

dbutils.library.restartPython()

# COMMAND ----------

# MAGIC %sql
# MAGIC WITH RankedImages AS (
# MAGIC   SELECT
# MAGIC     image_id,
# MAGIC     de_file_hash,
# MAGIC     split_hash,
# MAGIC     recorded_at,
# MAGIC     dt_file_hash,
# MAGIC     tag_start_at,
# MAGIC     sequence_start_at,
# MAGIC     ROW_NUMBER() OVER (PARTITION BY dt_file_hash ORDER BY recorded_at ASC) AS rn
# MAGIC   FROM
# MAGIC     viper_dsp_sbx.active_learning.al_triggers_X_de_image
# MAGIC   WHERE
# MAGIC     recorded_at BETWEEN
# MAGIC       tag_start_at - INTERVAL 999 milliseconds
# MAGIC     AND
# MAGIC       tag_start_at + INTERVAL 900 milliseconds
# MAGIC )
# MAGIC SELECT
# MAGIC   t.tds_file_url,
# MAGIC   image_id,
# MAGIC   de_file_hash,
# MAGIC   split_hash,
# MAGIC   recorded_at,
# MAGIC   dt_file_hash,
# MAGIC   tag_start_at,
# MAGIC   sequence_start_at
# MAGIC FROM
# MAGIC   RankedImages JOIN silver.tds.file_entries as t on RankedImages.de_file_hash = t.file_hash
# MAGIC WHERE
# MAGIC   rn = 1; -- Select only the earliest record for each dt_file_hash

# COMMAND ----------

from io import BytesIO

import matplotlib.pyplot as plt
import pandas as pd
import requests
from azure.identity import DeviceCodeCredential
from azure.storage.blob import BlobServiceClient
from PIL import Image

AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AZURE_STORAGE_AUDIENCE = "https://storage.azure.com/.default"

AAD_CREDENTIAL = DeviceCodeCredential(tenant_id=AAD_TENANT_ID)


def download_file_from_tds(tds_file_link):
    headers = {
        "Authorization": f"Bearer {AAD_CREDENTIAL.get_token(AZURE_STORAGE_AUDIENCE).token}",
        "x-ms-version": "2019-07-07",
    }
    response = requests.get(tds_file_link, headers=headers)
    response.raise_for_status()
    return response


def display_img(url):
    response = download_file_from_tds(url)
    img = Image.open(BytesIO(response.content))
    img.thumbnail((800, 600), Image.Resampling.LANCZOS)  # Increase size of the image
    return img


def display_img_from_df(ax, img, info):
    # Prepare text information to display without URL
    text_info = (
        f"Image ID: {info['image_id']}\n"
        f"DE File Hash: {info['de_file_hash']}\n"
        f"Split Hash: {info['split_hash']}\n"
        f"Recorded At: {info['recorded_at']}\n"
        f"DT File Hash: {info['dt_file_hash']}\n"
        f"Tag Start At: {info['tag_start_at']}\n"
        f"Sequence start at: {info['sequence_start_at']}\n"
    )

    ax.text(
        0.5,
        1.0,
        text_info,
        transform=ax.transAxes,
        fontsize=12,
        verticalalignment="bottom",
        horizontalalignment="center",
        bbox=dict(facecolor="white", alpha=0.7),
    )

    ax.imshow(img)
    ax.axis("off")


def display_images_in_plot(df):
    images = []

    for index, row in df.iterrows(): # for each
        url = row["tds_file_url"]
        try:
            img = display_img(url)
            images.append((img, row))  # Store image along with its corresponding row data
        except Exception as e:
            print(f"Error downloading {url}: {e}")
            blank_img = Image.new("RGB", (800, 600), (255, 255, 255))  # White placeholder
            images.append((blank_img, row))  # Store blank image with row data

    num_images = len(images)

    fig, axes = plt.subplots(num_images, 1, figsize=(10, 7 * num_images))

    if num_images == 1:
        display_img_from_df(axes, images[0][0], images[0][1])
    else:
        for ax, (img, info) in zip(axes, images):
            display_img_from_df(ax, img, info)

    plt.tight_layout()
    plt.show()

# COMMAND ----------

import matplotlib.pyplot as plt
import pandas as pd

# Hint: use _sqldf to use the result of the last sql query in python
df = _sqldf.toPandas()
# image_ids = df['tds_file_url'][0:10].to_list() # select the first 70

# COMMAND ----------

split_sha = "b93cc23a9c54375a8b1c48e9b5cd20407a8a859aa2063238fc52fe9cb1e98fb9"
display_images_in_plot(df[df["split_hash"] == split_sha])

# COMMAND ----------

display_images_in_plot(df[:5])

# COMMAND ----------

import pandas as pd

# Convert to datetime and ensure both are timezone-naive
df["sequence_start_at"] = pd.to_datetime(df["sequence_start_at"], utc=True).dt.tz_localize(None)
df["tag_start_at"] = pd.to_datetime(df["tag_start_at"], utc=True).dt.tz_localize(None)

# Calculate the time difference
df["time_diff"] = df["sequence_start_at"] - df["tag_start_at"]

# Convert time difference to total seconds
df["time_diff_seconds"] = df["time_diff"].dt.total_seconds()

# Plot histogram of time difference in seconds
df["time_diff_seconds"].hist(bins=100)
