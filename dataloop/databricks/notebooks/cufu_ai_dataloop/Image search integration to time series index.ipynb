{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "283e65dd-946b-45de-a343-eb403929b29c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "WITH dsp_ai_tags_with_split_hash AS (\n", "  SELECT dsp_ai_tags.tag_name, dsp_de_image.split_hash AS dsp_de_image_split_hash FROM silver.mdd.dsp_ai_tags\n", "  JOIN silver.mdd.dsp_de_image as dsp_de_image ON dsp_ai_tags.file_hash = dsp_de_image.file_hash\n", "  WHERE dsp_ai_tags.tag_name LIKE 'ios%'\n", ")\n", "\n", "SELECT trajectory_signals.file_hash, trajectory_signals.split_hash, trajectory_signals.recorded_at, trajectory_signals.vin, dsp_ai_tags_with_split_hash.tag_name FROM gold.drive_time_series.trajectory_signals_full AS trajectory_signals\n", "JOIN dsp_ai_tags_with_split_hash ON trajectory_signals.split_hash = dsp_ai_tags_with_split_hash.dsp_de_image_split_hash\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b58bc6f9-2a4e-494c-88b0-dfb5d014d1ea", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "from pyspark.sql.functions import col, when, max\n", "\n", "df = _sqldf.dropDuplicates()\n", "unique_identifier_columns = [\"vin\", \"file_hash\", \"recorded_at\", \"split_hash\"]\n", "\n", "pivot_df = df.groupBy(unique_identifier_columns).pivot(\"tag_name\").agg(max(when(col(\"tag_name\").isNotNull(), True).otherwise(False)))\n", "pivot_df = pivot_df.fillna(False)\n", "pivot_df = pivot_df.drop(\"tag_name\")\n", "pivot_df.write.mode(\"overwrite\").saveAsTable(\"cufu_ai_data_loop_sbx.image_search_integration_poc.image_search_ios_tags_new\")\n", "\n", "display(pivot_df)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 8977215938080036, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "Image search integration to time series index", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}