{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "71a9307e-6529-4faf-a580-1d4b612f8918", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import col\n", "\n", "# Define the columns to check\n", "columns_to_check = [\"ins_status\", \"pounds_status\", \"lane_map_alignment_status\", \"semseg_status\"]\n", "\n", "# Load the scene_candidates_duplicate2 table\n", "scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")\n", "\n", "# Create a condition to check if all specified columns are \"available\"\n", "condition = (col(columns_to_check[0]) == \"available\")\n", "for column in columns_to_check[1:]:\n", "    condition = condition & (col(column) == \"available\")\n", "\n", "# Filter out rows where the condition is met\n", "filtered_df = scene_candidates_df.filter(~condition)\n", "\n", "display(filtered_df)\n", "\n", "# Overwrite the table with the filtered DataFrame\n", "filtered_df.write.mode(\"overwrite\").format(\"delta\").saveAsTable(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "clean_scene_candidates", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}