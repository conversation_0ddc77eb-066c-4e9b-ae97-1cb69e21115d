{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "eb59867e-cc7c-480b-baa7-f9f49c942b80", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Find split candidates\n", "This notebook finds split candidates for the OMG dataset.\n", "\n", "Split candidates need to be recorded by a DataColection(DC) vehicle and located on a HD map."]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9592251b-cb93-41c5-87b4-7c309d763b47", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Parameter"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d443f6a0-1431-4152-b895-78f64641cc34", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Index resolution\n", "The index resolution for the H3 index generation of the positions.\n", "> **Warning:**  The H3 index resolution of the (GPS) positions, which will be checked if they are inside the polygon musst be the same as for the polygon."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a98a29df-7a9e-4444-ba91-9e205f9e0b1b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["optimal_resolution = 9"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6177bce9-4cb3-4df4-8c59-5926e276f0ec", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Imports and configs"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "42543d74-acbd-4eb1-a340-a4e9426ef89a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from delta.tables import DeltaTable\n", "from pyspark.sql.functions import *\n", "import mosaic as mos\n", "mos.enable_mosaic(spark, dbutils)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c4121da2-fd17-4449-aa9c-b183ee833897", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Find newest DC data\n", "Each day we want to check if new data is available in the `gold.drive_time_series.situation_dataset_full` table and therefore search for all data created or modified yesterday. \n", "\n", "> **Hint:** Right now the columns `created_at` and `modified_at` are not filled and therefore we use the way over the `emitted_at` column. \n", "\n", "\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "53b53935-36ae-4db9-b16c-0c1cef1bd83e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_timeseries_newest = (\n", "    spark.read\n", "    .table(\"gold.drive_time_series.situation_dataset_full\")\n", "    .select(\n", "        col(\"emitted_at\"), \n", "        col(\"drive_hash\"), \n", "        col(\"split_hash\"), \n", "        col(\"latitude\"), \n", "        col(\"longitude\"), \n", "        col(\"created_at\"), \n", "        col(\"modified_at\")\n", "    )\n", "    .filter(\n", "        col(\"emitted_at\") >= date_sub(current_date(), 40))\n", "    )\n", "\n", "# display(df_timeseries_newest)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4b551bb6-acec-4763-98b8-840cd8597ea7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["<PERSON><PERSON> found entries by a set `recording_sw_version` that starts with `DC`."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "59693c21-5ed6-451c-9ef9-7ddb6b7da20c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_drive_info_dc = (\n", "    spark.read\n", "    .table(\"silver.mdd.datamanagement_drive_info\")\n", "    .filter(\n", "        col(\"recording_sw_version\").isNotNull() \n", "        & col(\"recording_sw_version\").startswith(\"DC\")))"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "475d8bef-7694-434d-944f-8087535e6157", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_relevant_positions = (\n", "    df_timeseries_newest.join(df_drive_info_dc, col(\"drive_hash\") == col(\"file_hash\"), \"inner\")\n", "    .select(\n", "        col(\"emitted_at\"),\n", "        col(\"drive_hash\"),\n", "        col(\"split_hash\"),\n", "        col(\"latitude\"),\n", "        col(\"longitude\"),\n", "        col(\"recording_sw_version\"),\n", "    )\n", "    .orderBy(col(\"drive_hash\"), col(\"emitted_at\"))\n", ")\n", "\n", "# display(df_relevant_positions.limit(20))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9ae7d608-e915-46c0-81b4-c0f344533592", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Calculate Index of found positions"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ad1c292d-e8bc-4963-bc45-311fa6663c5f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_indexed_positions = (\n", "    df_relevant_positions\n", "    .withColumn(\"gps_geom\", mos.st_astext(mos.st_point(\n", "        col(\"longitude\").cast(\"double\"), \n", "        col(\"latitude\").cast(\"double\"))))\n", "    .withColumn(\"h3_index_id\", mos.grid_pointascellid(col(\"gps_geom\"), lit(optimal_resolution)))\n", "    .orderBy(col(\"h3_index_id\"))\n", "    )\n", "\n", "# display(df_indexed_positions.limit(20))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "674cfe4f-90da-442f-ba5d-69b5fbe0e995", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Find positions within our polygons"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e0179d67-0f31-4883-bafd-7c426142c1b7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_found_on_map = (\n", "    df_indexed_positions\n", "    .join\n", "    (\n", "        spark.read\n", "        .table(f\"viper_lane_sbx.hd_map_polygons.hd_map_polygons_resolution_{optimal_resolution}\"),\n", "        \"h3_index_id\",\n", "        \"inner\",\n", "    )\n", "    .where\n", "    (\n", "        # If the borough is a core chip (the chip is fully contained within the geometry), then we do not need\n", "        # to perform any intersection, because any point matching the same index will certainly be contained in\n", "        # the borough. Otherwise we need to perform an st_contains operation on the chip geometry.\n", "        col(\"h3_is_core\")\n", "        | mos.st_contains(col(\"h3_wkb\"), col(\"gps_geom\"))\n", "    )\n", ")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "39b5dc63-4340-4737-9ba6-07791da6a36d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Visualizations"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4363cc1f-063b-4234-ad8f-9489bd8b67ce", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["> **Hint:** Uncomment the following cells within databricks for actual visualization results. "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4b5a11e1-d184-45dc-9fb7-c273812aed91", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["to_display_count = df_found_on_map.groupBy(\"h3_index_id\").count()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1d77f290-45d3-478e-b20c-3c6514685452", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# %%mosaic_kepler\n", "# to_display_count h3_index_id h3"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ffeb1ffc-89f5-4d47-b808-9603ec6cb24b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# %%mosaic_kepler\n", "# df_found_on_map \"h3_wkb\" \"geometry\" 15000"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c515dcc2-a221-4664-9d42-e07c63e6fcda", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Update scene_candidates "]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2e901edf-28f1-47bf-ab0a-4373b51627ea", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Find distinct `split_hash` and calculate `started_at` and `ended_at` for that split. Add `drive_hash` and `map_url` and set `insereted_at` to current_timestamp(). At the end order it be `drive_hash` and `started_at`"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bc119d93-322d-450f-aea5-77cc9e81eeb7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["df_scene_candidates_new = (df_found_on_map\n", "                           .groupBy(\"split_hash\", \"drive_hash\", \"map_url\")\n", "                           .agg(min(\"emitted_at\").alias(\"started_at\"),\n", "                                max(\"emitted_at\").alias(\"ended_at\"))\n", "                           .withColumn(\"inserted_at\", current_timestamp())\n", "                           .orderBy(col(\"drive_hash\"), col(\"started_at\")))\n", "\n", "display(df_scene_candidates_new)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "09b4bd56-e895-4e45-8f24-3a408e71bfd3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Load `scene_candidates` Delta table  "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ba40c577-a3e7-45fb-8c47-305b63a1c0d0", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["delta_table = DeltaTable.forName(spark, \"viper_lane_sbx.atlatec_datapool.scene_candidates\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d63515c4-d8fe-49f3-bcf1-7ea2b6b72042", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["set dafault values of `scene_candidates` columns before merge with existing table"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1e27738c-0f2f-4394-bff8-0bdf218b1ba4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["for column in delta_table.toDF().columns:\n", "    if column not in df_scene_candidates_new.columns:\n", "        if column.endswith(\"_status\"):\n", "            df_scene_candidates_new = df_scene_candidates_new.withColumn(column, lit(\"unavailable\"))\n", "        elif column.endswith(\"_trigger_dates\"):\n", "            df_scene_candidates_new = df_scene_candidates_new.withColumn(column, array().cast(\"array<timestamp>\"))\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d55b8934-1e53-43ee-8460-8ef4ddb740ea", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Perform the merge operation"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ad4f066a-77d4-4aec-a02e-725a689dc635", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["count_before = delta_table.toDF().count()\n", "\n", "delta_table.alias(\"target\").merge(\n", "    df_scene_candidates_new.alias(\"source\"),\n", "    \"target.split_hash = source.split_hash AND target.map_url = source.map_url\"\n", ").whenMatchedUpdateAll(\n", ").whenNotMatchedInsertAll(\n", ").execute()\n", "\n", "count_after = delta_table.toDF().count()\n", "rows_inserted = count_after - count_before\n", "print(f\"Inserted {rows_inserted} rows\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5f970700-86bd-4be7-85d7-38b8691ba928", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Optimize table on split_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "21e3c308-b976-482b-a500-eb21c4641e07", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"OPTIMIZE viper_lane_sbx.atlatec_datapool.scene_candidates ZORDER BY split_hash\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": -1, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "find_scene_candidates", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}