{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "07b59565-e4a0-4129-af14-8f9572dda989", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import current_timestamp, col\n", "\n", "# Define the columns to check\n", "columns_to_check = [\"ins_status\", \"pounds_status\", \"lane_map_alignment_status\", \"semseg_status\", \"hitl_status\"]\n", "\n", "# Load the scene_candidates_duplicate2 table\n", "scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")\n", "\n", "# Create a condition to check if all specified columns are \"available\"\n", "condition = (col(columns_to_check[0]) == \"available\")\n", "for column in columns_to_check[1:]:\n", "    condition = condition & (col(column) == \"available\")\n", "filtered_df = scene_candidates_df.filter(condition)\n", "\n", "# Load the lane_map_alignment_imported table\n", "lane_map_alignment_df = spark.table(\"viper_lane_sbx.hackathon.lane_map_alignment_imported\")\n", "\n", "# Perform the join operation\n", "joined_df = filtered_df.join(lane_map_alignment_df, \"split_hash\",\"inner\")\n", "\n", "# Select the relevant columns from the joined DataFrame\n", "result_df = joined_df.select(\n", "    filtered_df[\"split_hash\"],\n", "    lane_map_alignment_df[\"timestamp\"],\n", "    current_timestamp().alias(\"inserted_at\"),\n", "    lane_map_alignment_df[\"map_alignment_pose_ecef_quat\"],\n", "    lane_map_alignment_df[\"map_alignment_pose_ecef_trans\"],\n", "    lane_map_alignment_df[\"map_alignment_pose_stdev_euler_angles\"],\n", "    lane_map_alignment_df[\"map_alignment_pose_stdev_rotation_angle\"],\n", "    lane_map_alignment_df[\"map_alignment_pose_stdev_translation\"],\n", "    lane_map_alignment_df[\"map_alignment_pose_stdev_translation_norm\"],\n", "    lane_map_alignment_df[\"map_reference_sha\"],\n", "    lane_map_alignment_df[\"map_reference_tds_url\"],\n", "    lane_map_alignment_df[\"map_alignment_version_version\"],\n", "    lane_map_alignment_df[\"map_alignment_version_hash\"]\n", ")\n", "\n", "# Append the results to the labels_lma table\n", "result_df.write.mode(\"append\").format(\"delta\").saveAsTable(\"viper_lane_sbx.atlatec_datapool.labels_lma\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "update_labels_lma", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}