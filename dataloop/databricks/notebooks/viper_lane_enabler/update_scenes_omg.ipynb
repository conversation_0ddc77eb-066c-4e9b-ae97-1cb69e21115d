{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5aafe444-6163-4d2c-85dc-33d4d3dfd72b", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import lit, col, current_timestamp\n", "\n", "# Define the columns to check\n", "columns_to_check = [\"ins_status\", \"pounds_status\", \"lane_map_alignment_status\", \"semseg_status\", \"hitl_status\"]\n", "\n", "# Load the scene_candidates_duplicate2 table\n", "scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")\n", "\n", "# Create a condition to check if all specified columns are \"available\"\n", "condition = (col(columns_to_check[0]) == \"available\")\n", "for column in columns_to_check[1:]:\n", "    condition = condition & (col(column) == \"available\")\n", "\n", "filtered_df = scene_candidates_df.filter(condition)\n", "\n", "# Select the relevant columns and set the dataset_split column to \"train\"\n", "updated_df = filtered_df.select(\n", "    \"split_hash\",\n", "    \"drive_hash\",\n", "    \"started_at\",\n", "    \"ended_at\",\n", "    current_timestamp().alias(\"inserted_at\"),\n", "    lit(\"train\").alias(\"dataset_split\")\n", ")\n", "\n", "# Write the updated DataFrame to the scenes_omg2 table\n", "updated_df.write.mode(\"append\").format(\"delta\").saveAsTable(\"viper_lane_sbx.atlatec_datapool.scenes_omg\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "update_scenes_omg", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}