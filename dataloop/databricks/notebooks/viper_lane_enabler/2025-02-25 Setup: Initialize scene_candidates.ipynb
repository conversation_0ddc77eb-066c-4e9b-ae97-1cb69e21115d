{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "95c911a4-ae62-4981-8fcd-1e6a7c729541", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Create the initial table for scene candidates for the omg use case and fill example data"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "26fb7c68-e913-4568-aa3e-2465cfd85d65", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE viper_lane_sbx.atlatec_datapool.scene_candidates (\n", "    split_hash STRING COMMENT 'Scene (can occure multiple times with different start/end times)',\n", "    started_at TIMESTAMP COMMENT 'Relevant start time',\n", "    ended_at TIMESTAMP COMMENT 'Relevant end time',\n", "    inserted_at TIMESTAMP COMMENT 'Timestamp of insertion',\n", "    ins_status STRING COMMENT 'Status of INS processing',\n", "    ins_trigger_dates ARRAY<TIMESTAMP> COMMENT 'Processing triggered at',\n", "    pounds_status STRING COMMENT 'Status of POUNDS processing',\n", "    pounds_trigger_dates ARRAY<TIMESTAMP> COMMENT 'Processing triggered at',\n", "    lane_map_alignment_status STRING COMMENT 'Status of lane_map_alignment processing',\n", "    lane_map_alignment_trigger_dates ARRAY<TIMESTAMP> COMMENT 'Processing triggered at',\n", "    semseg_status STRING COMMENT 'Status of SemSeg processing',\n", "    semseg_trigger_dates ARRAY<TIMESTAMP> COMMENT 'Processing triggered at',\n", "    hitl_status STRING COMMENT 'Status of HITL processing (Is extrinsic calibration available)',\n", "    hitl_trigger_dates ARRAY<TIMESTAMP> COMMENT 'Processing triggered at',\n", "    drive_hash STRING COMMENT 'Hash of drive used for processing',\n", "    map_url STRING COMMENT 'URL to map for lane_map_alignment processing'\n", ");"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8aed61e8-d3a9-4269-9bf6-b8fdfdcadc8f", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "-- Store example data\n", "WITH file_hashes AS (\n", "    SELECT explode(array(\n", "        '881d262092111162b08746bc59bf7c1fd5206958d5b8c813ee3359083666ac11',\n", "        'ebe4042e6f78f0ddd83007352d4879fab13769a2de2076d77bdbd67df74737a2',\n", "        'd0f21b824ecd5c10c75d4f51ab719e9c05946480116512e3d0c769ed54562151',\n", "        'd549607a5281b74139b4be5a0951da9b51e11efec5ea9fa7954ecbe1ac3265f7',\n", "        'ae578d760b96e50695df47c3132caf1dc22e1cfe43bfc52fc2e2323bfbc6836f',\n", "        'a38fdb4e79a51a458dede5597fe87647a91ec243f81e0502df17b0aaf01247c4',\n", "        'a391a77c6bb2d8399e4cb4cdf4a6ba4e2a607df27be38e8a04f22a06bf99b07b',\n", "        '510df963f6630f6f4cbf03970921775aa9ba34581f63bf1262a9b146fc001e12',\n", "        'd7f96db3b7a0a8198c1b222e2e196080161a84db9cea50ddae7e5ec6ed2e4389',\n", "        '3760e865ac75486e83005bc923deaf79a9c88f092440b54e1cc0df7ab9baea75',\n", "        '09967f69e31fdd9476c753967530041d957e3d07244f250ebad9f40b989456fb', -- Has no INS, no POUNDS, no lane_map_alignment, no semseg\n", "        'aed066fe4e70f061389a258e9babcccc912c657e757b2bc12e2d2b406bb724d1' -- Only lane_map_alignment and semseg missing\n", "    )) AS file_hash\n", ")\n", "INSERT INTO viper_lane_sbx.atlatec_datapool.scene_candidates\n", "SELECT \n", "    file_hash AS split_hash, \n", "    start_time_utc AS started_at, \n", "    end_time_utc AS ended_at, \n", "    current_timestamp() AS inserted_at,\n", "    'unavailable' AS ins_status,\n", "    array() AS ins_trigger_dates,\n", "    'unavailable' AS pounds_status,\n", "    array() AS pounds_trigger_dates,\n", "    'unavailable' AS lane_map_alignment_status,\n", "    array() AS lane_map_alignment_trigger_dates,\n", "    'unavailable' AS semseg_status,\n", "    array() AS semseg_status_trigger_dates,\n", "    'unavailable' AS hitl_status,\n", "    array() AS hitl_status_trigger_dates,\n", "    NULL AS drive_hash,\n", "    NULL AS map_url\n", "FROM \n", "    file_hashes\n", "INNER JOIN \n", "    silver.mdd.datamanagement_split_info\n", "USING (file_hash);"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 5110014480106479, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "2025-02-25 Setup: Initialize scene_candidates", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}