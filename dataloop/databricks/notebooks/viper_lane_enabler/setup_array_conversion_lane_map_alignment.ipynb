{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "886914ff-3bc1-4ea4-b88c-054e991eaab1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE viper_lane_sbx.hackathon.lane_map_alignment_imported (\n", "    split_hash STRING COMMENT 'Scene (can occur multiple times with different start/end times)',\n", "    timestamp BIGINT COMMENT 'Exact timestamp of the position',\n", "    map_alignment_pose_ecef_quat ARRAY<DOUBLE>,\n", "    map_alignment_pose_ecef_trans ARRAY<DOUBLE>,\n", "    map_alignment_pose_stdev_euler_angles ARRAY<DOUBLE>,\n", "    map_alignment_pose_stdev_rotation_angle DOUBLE,\n", "    map_alignment_pose_stdev_translation ARRAY<DOUBLE>,\n", "    map_alignment_pose_stdev_translation_norm DOUBLE,\n", "    map_reference_name STRING COMMENT 'Name of the map used for alignment',\n", "    map_reference_sha STRING COMMENT 'SHA pointing to the map in MDM',\n", "    map_reference_tds_url STRING COMMENT 'URL to the map in TDS',\n", "    map_alignment_version_version STRING COMMENT 'Version of the map used for alignment',\n", "    map_alignment_version_hash STRING COMMENT 'SHA of the map used for alignment',\n", "    map_alignment_pounds_sha STRING COMMENT 'SHA of the pounds LIDAR data used for alignment'\n", ");"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8fe4b24c-156a-42a1-95ff-485cb1964ce4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import array, col\n", "\n", "# Load the data from the original table\n", "orig_df = spark.table(\"viper_lane_sbx.hackathon.lane_map_alignment_imported_orig\")\n", "\n", "# Load the data from the new table to understand its structure\n", "new_df = spark.table(\"viper_lane_sbx.hackathon.lane_map_alignment_imported\")\n", "\n", "# Assuming the new structure has arrays, transform the original data to match the new structure\n", "# This is a placeholder transformation. You need to adjust it based on the actual structure of the new table.\n", "\n", "transformed_df = orig_df.select(\n", "    \"split_hash\",\n", "    \"timestamp\",\n", "    array(col(\"map_alignment_pose_ecef_quat_w\"), \n", "          col(\"map_alignment_pose_ecef_quat_x\"),\n", "          col(\"map_alignment_pose_ecef_quat_y\"),\n", "          col(\"map_alignment_pose_ecef_quat_z\")).alias(\"map_alignment_pose_ecef_quat\"),\n", "    array(col(\"map_alignment_pose_ecef_trans_0\"),\n", "          col(\"map_alignment_pose_ecef_trans_1\"),\n", "          col(\"map_alignment_pose_ecef_trans_2\")).alias(\"map_alignment_pose_ecef_trans\"),\n", "    array(col(\"map_alignment_pose_stdev_euler_angles_yaw\"),\n", "          col(\"map_alignment_pose_stdev_euler_angles_pitch\"),\n", "          col(\"map_alignment_pose_stdev_euler_angles_roll\")).alias(\"map_alignment_pose_stdev_euler_angles\"),\n", "    \"map_alignment_pose_stdev_rotation_angle\",\n", "    array(col(\"map_alignment_pose_stdev_translation_x\"),\n", "          col(\"map_alignment_pose_stdev_translation_y\"),\n", "          col(\"map_alignment_pose_stdev_translation_z\")).alias(\"map_alignment_pose_stdev_translation\"),\n", "    \"map_alignment_pose_stdev_translation_norm\",\n", "    \"map_reference_name\",\n", "    \"map_reference_sha\",\n", "    \"map_reference_tds_url\",\n", "    \"map_alignment_version_version\",\n", "    \"map_alignment_version_hash\",\n", "    \"map_alignment_pounds_sha\"\n", ")\n", "\n", "# Write the transformed data to the new table\n", "transformed_df.write.mode(\"append\").format(\"delta\").saveAsTable(\"viper_lane_sbx.hackathon.lane_map_alignment_imported\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 6913880669083368, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "setup_array_conversion_lane_map_alignment", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}