{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "dd2617b7-2657-4e70-8ea4-278da209c57a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import col, current_timestamp\n", "\n", "# Define the columns to check\n", "columns_to_check = [\"ins_status\", \"pounds_status\", \"lane_map_alignment_status\", \"semseg_status\", \"hitl_status\"]\n", "\n", "# Load the scene_candidates_duplicate2 table\n", "scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")\n", "\n", "# Create a condition to check if all specified columns are \"available\"\n", "condition = (col(columns_to_check[0]) == \"available\")\n", "for column in columns_to_check[1:]:\n", "    condition = condition & (col(column) == \"available\")\n", "\n", "filtered_df = scene_candidates_df.filter(condition)\n", "\n", "# Load the semai_imported table\n", "semai_imported_df = spark.table(\"viper_lane_sbx.hackathon.semai_imported\")\n", "\n", "# Perform the join operation\n", "joined_df = filtered_df.join(semai_imported_df, \"split_hash\",\"inner\")\n", "\n", "# Select the relevant columns from the joined DataFrame\n", "result_df = joined_df.select(\n", "    filtered_df[\"split_hash\"],\n", "    semai_imported_df[\"timestamp\"],\n", "    current_timestamp().alias(\"inserted_at\"),\n", "    semai_imported_df[\"stream_name\"],\n", "    semai_imported_df[\"semai_url\"].alias(\"semseg_url\")\n", ")\n", "\n", "# Append the results to the labels_semai2 table\n", "result_df.write.mode(\"append\").format(\"delta\").saveAsTable(\"viper_lane_sbx.atlatec_datapool.labels_semseg\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "update_labels_semseg", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}