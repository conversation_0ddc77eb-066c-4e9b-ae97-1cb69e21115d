{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "820f559a-1392-460d-a0ca-ea5f4b71d935", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["# Create search polygon table\n", "This notebook downloads the generated search polygons as a geojson file and adds it to a table in viper_lane_sbx.hd_map_polygons"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6d4d6ed4-0d8e-4135-9807-1880bf01b064", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["## Parameter"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "021d0bb3-13a0-4f36-b9f8-b88f3a0ea806", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Index resolution\n", "The index resolution for the H3 index generation of the polygon.\n", "> **Warning:**  The H3 index resolution of the (GPS) points, which will be checked if they are inside the polygon musst be the same as for the polygon."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ab6ad5d9-6fd5-4177-ba59-5071ea14d3e9", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["index_resolution = 9"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "965bf307-18ac-49b7-894a-29a09c495bc8", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["The geojson files that we load are stored in `https://github.com/PACE-INT/adas_predev/tree/main/resources/search_polygons`. To create download the geojson files it is needed to copy a valid token. This can be done be selecting the Raw output in the named repository and copy the link out of the browser."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9b7f395c-7fd7-4eb1-95b9-11e33eaefbed", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["\n", "polygon_dicts = [\n", "    {\n", "        \"tag\":\"FeuerbachBenchmark01\",\n", "        \"polygon_url\":\"https://raw.githubusercontent.com/PACE-INT/adas_predev/refs/heads/main/resources/search_polygons/FeuerbachBenchmark01_2024-11-07_lanelet2_simplified.geojson?token=GHSAT0AAAAAAC42W4SRKHOVKISAC3KWUOXUZ6FYZJQ\",\n", "        \"sha\":\"0a2afe7a18ecacd6966ac1830b7ed32c000c810ca92e373031a1f2c393f1864b\",\n", "        \"url\":\"https://stg5boatlatec02euw.blob.core.windows.net/default/dc140116-ef4c-464a-a850-0af1cc9603d5/FeuerbachBenchmark01_2024-11-07_lanelet2.osm\",\n", "    },\n", "    {\n", "        \"tag\":\"IngolstadtTestRoute\",\n", "        \"polygon_url\":\"https://raw.githubusercontent.com/PACE-INT/adas_predev/refs/heads/main/resources/search_polygons/IngolstadtTestRoute_2024-08-19_lanelet2_simplified.geojson?token=GHSAT0AAAAAAC42W4SQJCV5HB6CKN7VYOYYZ6FY2GQ\",\n", "        \"sha\":\"77fe919810ffc6d10dd1861a46604a1f86d54868f164f4d00d5d7707c0fca1f6\",\n", "        \"url\":\"https://stg5boatlatec01euw.blob.core.windows.net/default/aed191bf-f32a-4d51-b0f8-f01be630e58b/IngolstadtTestRoute_2024-08-19_lanelet2.osm\",\n", "    },\n", "    {\n", "        \"tag\":\"BerlinDowntown01\",\n", "        \"polygon_url\":\"https://raw.githubusercontent.com/PACE-INT/adas_predev/refs/heads/main/resources/search_polygons/BerlinDowntown01_2024-09-12_lanelet2_simplified.geojson?token=GHSAT0AAAAAAC42W4SQ2POEADBBTVV3CB5SZ6FYZZQ\",\n", "        \"sha\":\"0d2d17f0591c17ac6ccf7a1ca760b0ef371f15a2faec8ec3fe02bb780b4bc92d\",\n", "        \"url\":\"https://stg5boatlatec00euw.blob.core.windows.net/default/2323c6ee-22ae-4894-8f4d-3f3ec23d101f/BerlinDowntown01_2024-09-12_lanelet2.osm\",\n", "    },\n", "]"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4bb7834a-7604-4046-ac84-1f0bba1b8af1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Download a file given the URL and store it as temporary file."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d27207d2-5142-4af8-b71d-d55e5adcf38e", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["import tempfile\n", "from urllib.error import HTTPError\n", "import urllib.request\n", "\n", "def download_geojson(url):\n", "    try:\n", "        temp_file = tempfile.NamedTemporaryFile(delete=False)\n", "        with urllib.request.urlopen(url) as response, open(temp_file.name, 'wb') as out_file:\n", "            data = response.read()\n", "            out_file.write(data)\n", "        return temp_file\n", "    except HTTPError as e:\n", "        print(f\"HTTPError: {e.code} - {e.reason} for URL: {url}\")\n", "        return None"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "adb5d10f-51ba-4683-a064-eeacfdc125c4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Load the geo<PERSON><PERSON> file and create the H3 index for that polygon. "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f3f22d36-99b4-467d-90d0-f0eb497578d7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import explode, col, to_json, lit, current_timestamp\n", "import mosaic as mos\n", "mos.enable_mosaic(spark, dbutils)\n", "\n", "def load_geojson(path, map_tag, sha, url):\n", "    return (\n", "        spark.read\n", "            .option(\"multiline\", \"true\")\n", "            .format(\"json\")\n", "            .load(f\"file://{path}\")\n", "            .select(explode(col(\"features\")).alias(\"feature\"))\n", "            .select(mos.st_geomf<PERSON><PERSON><PERSON><PERSON><PERSON>(to_json(col(\"feature.geometry\"))).alias(\"geometry\"))\n", "            .select(mos.grid_tessellateexplode(col(\"geometry\"), lit(index_resolution)).alias(\"h3_index\"))\n", "            .select(col(\"h3_index.is_core\").alias(\"h3_is_core\"), col(\"h3_index.index_id\").alias(\"h3_index_id\"), col(\"h3_index.wkb\").alias(\"h3_wkb\"))\n", "            .withColumn(\"map_hash\", lit(sha))\n", "            .withColumn(\"map_url\", lit(url))\n", "            .withColumn(\"map_tag\", lit(map_tag))\n", "            .withColumn(\"inserted_at\", lit(current_timestamp()))\n", "    )"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b6a453be-3836-4b92-8cac-157bbfeed2ca", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Run over all tag url tuple stored in the first cell download the geojson load it as data frame and generate the h3 index "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "09b39ec4-8e95-44bc-9728-76df35a5ace6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["search_polygons = None\n", "\n", "for polygon in polygon_dicts:\n", "    temp_file = download_geojson(polygon[\"polygon_url\"])\n", "    if temp_file is not None:\n", "        df = load_geojson(temp_file.name, polygon[\"tag\"], polygon[\"sha\"], polygon[\"url\"])\n", "        if search_polygons is None:\n", "            search_polygons = df\n", "        else:\n", "            search_polygons = search_polygons.union(df)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1471c693-12b2-48a3-b34f-8817dc48be39", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Save created table in `viper_lane_sbx.hd_map_polygons` with resolution in table name "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "db33d6c2-2b52-45ce-bf33-af74e37ad3c6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["search_polygons.write.mode(\"overwrite\").saveAsTable(f\"viper_lane_sbx.hd_map_polygons.hd_map_polygons_resolution_{index_resolution}\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "436e5070-f5fe-457d-b7e7-bb0634ee60a6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Optimize created table over the H3 ida of the H3 index. H3 ids on a given resolution have index values close to each other if they are in close real-world proximity. [High Scale Geospatial Processing With Mosaic](https://www.databricks.com/blog/2022/05/02/high-scale-geospatial-processing-with-mosaic.html)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1c506a7a-5f80-41a5-a21a-27a9bd082d5c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["spark.sql(f\"OPTIMIZE viper_lane_sbx.hd_map_polygons.hd_map_polygons_resolution_{index_resolution} ZORDER BY h3_index_id\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "create_search_polygon_table", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}