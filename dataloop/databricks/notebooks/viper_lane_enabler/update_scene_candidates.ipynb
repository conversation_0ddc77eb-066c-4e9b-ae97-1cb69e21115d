{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "7cc6786a-e637-40b2-b82e-3e0d763bcfb1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["### Updates scene_candidats\n", "Notebook searches for all scenes inside scene_candidates if all required information is available.<br>\n", "E.g. INS, POUNDS, SemSeg, Lane Map Alignment, Offline Calibration, ...<br>\n", "Depending on available data the state is updated to:\n", "- unavailable (default)\n", "- triggered (data not found)\n", "- available (data found)\n", "- failed (after 3 attemps to get the data (triggered))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "8de84d4f-1079-40f1-92a8-d6e538a4bbce", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Initialize Spark"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a7f28dca-36c5-4ef4-aa2d-6f4dcc68657a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "24d86de9-ba7f-414f-a8d7-c4cd6d5954e3", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Search all candidates for which INS is available."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "7f5f2f60-587f-4fa5-9150-c9dd4597cdfa", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "# Because only new scenes (2025) have split_hash stored inside ground_truth this function has to iterate through all children children of each scene candidate.\n", "query = \"\"\"\n", "WITH relevant_scenes AS (\n", "  SELECT split_hash\n", "  FROM viper_lane_sbx.atlatec_datapool.scene_candidates\n", "  WHERE ins_status IN ('unavailable', 'triggered', 'failed')\n", ")\n", "SELECT r.split_hash\n", "FROM relevant_scenes r\n", "JOIN silver.mdd.children c1 ON r.split_hash = c1.parent_file_hash\n", "JOIN silver.mdd.children c2 ON c1.file_hash = c2.parent_file_hash\n", "JOIN silver.mdd.ground_truth gt ON gt.file_hash = c2.file_hash\n", "WHERE ground_truth_type = 'odometry' AND\n", "      (is_invalid IS NULL OR is_invalid = False)\n", "\"\"\"\n", "\n", "scenes = spark.sql(query).select(\"split_hash\").distinct()\n", "scenes_with_ins = [row.split_hash for row in scenes.collect()]\n", "\n", "display(scenes)\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "173e6fef-825f-4ea6-b659-7c856a89d3ff", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Search all candidates for which POUNDS is available."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "51decf05-3b3e-43c2-b822-906a2fa29a81", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "query = \"\"\"\n", "WITH relevant_scenes AS (\n", "  SELECT split_hash\n", "  FROM viper_lane_sbx.atlatec_datapool.scene_candidates\n", "  WHERE pounds_status IN ('unavailable', 'triggered', 'failed')\n", ")\n", "SELECT split_hash, COUNT(L.file_hash) AS pcd_count\n", "FROM relevant_scenes\n", "INNER JOIN silver.mdd.dsp_lidar L USING(split_hash)\n", "INNER JOIN silver.mdd.dsp_lidar_pcd ON L.file_hash = source_pcd\n", "GROUP BY split_hash\n", "HAVING pcd_count > 100\n", "\"\"\"\n", "\n", "scenes = spark.sql(query).select(\"split_hash\")\n", "scenes_with_pounds = [row.split_hash for row in scenes.collect()]\n", "\n", "display(scenes)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "4d3e8518-0e7d-42f4-84c9-aae57578384c", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Search all candidates were lane_map_aligmnet is available."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "06dd6300-952a-4e56-9db2-2d0ca92a9be6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import col, when\n", "\n", "lane_map_al_df = spark.table(\"viper_lane_sbx.hackathon.lane_map_alignment_imported\").select(\"split_hash\").distinct()\n", "filtered_scene_candidates_df = scene_candidates_df.filter(col(\"lane_map_alignment_status\").isin(\"unavailable\", \"triggered\", \"failed\"))\n", "\n", "result_df = filtered_scene_candidates_df.join(lane_map_al_df, \"split_hash\", \"inner\").select(\"split_hash\").distinct()\n", "scenes_with_lane_map_al = [row.split_hash for row in result_df.collect()]\n", "\n", "# TODO: currently ignores different map_url (solve: split_hash and map_url is a key. The same split_hash can occure twice with different map_url's)\n", "display(result_df)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "35f34550-f342-4998-84da-ff106f01a306", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Search all candidates were SemSeg is available."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ce020247-38e6-4f76-af4d-d332a0eb2560", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import col, when\n", "\n", "semseg_df = spark.table(\"viper_lane_sbx.hackathon.semai_imported\").select(\"split_hash\").distinct()\n", "filtered_scene_candidates_df = scene_candidates_df.filter(col(\"semseg_status\").isin(\"unavailable\", \"triggered\", \"failed\"))\n", "\n", "result_df = filtered_scene_candidates_df.join(semseg_df, \"split_hash\", \"inner\").select(\"split_hash\").distinct()\n", "scenes_with_semseg = [row.split_hash for row in result_df.collect()]\n", "\n", "display(result_df)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "fe25c82b-ddf7-491d-8035-4b0e5ffb8ef7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Search all candidates for which offline calibraiton (HITL) is available"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6d8e6896-b4dc-4537-a854-67161f349246", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "# Get the required scenes and attach vin and calcstorage_uid\n", "\n", "# Step 1: Get relevant scenes\n", "query1 = \"\"\"\n", "WITH relevant_scenes AS (\n", "  SELECT split_hash, started_at\n", "  FROM viper_lane_sbx.atlatec_datapool.scene_candidates\n", "  WHERE hitl_status IN ('unavailable', 'triggered', 'failed')\n", ")\n", "\"\"\"\n", "\n", "# Step 2: <PERSON><PERSON><PERSON> vin to relevant scenes\n", "query2 = \"\"\"\n", ", scenes_with_vin AS (\n", "  SELECT split_hash, any_value(started_at) AS started_at, any_value(vin) AS vin\n", "  FROM relevant_scenes\n", "  LEFT JOIN gold.drive_time_series.situation_dataset_full USING (split_hash)\n", "  GROUP BY split_hash\n", ")\n", "\"\"\"\n", "\n", "# Step 3: Attach calstore_syscal_name to scenes\n", "# See: https://adb-505904006080631.11.azuredatabricks.net/jobs/740288358217498/tasks/recordings?o=505904006080631\n", "query3 = \"\"\"\n", ", scenes_with_syscal AS (\n", "  SELECT split_hash, started_at, cal.vin, name AS calstore_syscal_name\n", "  FROM scenes_with_vin\n", "  LEFT JOIN viper_coreai_sbx.loomy_poc.calstore_syscals cal ON scenes_with_vin.vin = cal.vin AND started_at >= cal.valid_from AND (started_at <= cal.valid_until OR cal.valid_until IS NULL) \n", ")\n", "\"\"\"\n", "\n", "# Step 4: Get all available corrections (offline calibrations for FC1) and rank corrections based on time difference to the scene start time\n", "# See: https://adb-505904006080631.11.azuredatabricks.net/jobs/740288358217498/tasks/streams_offline_calibration?o=505904006080631\n", "query4 = \"\"\"\n", ", scenes_with_ranked_corrections AS (\n", "    SELECT split_hash,\n", "        ABS(CAST(cal.image_timestamp AS BIGINT)/(1000*1000*1000) - CAST(to_unix_timestamp(scenes_with_syscal.started_at) AS BIGINT)) AS time_difference_secs,\n", "        ROW_NUMBER() OVER\n", "            (PARTITION BY split_hash, cal.camera_stream\n", "              ORDER BY ABS(CAST(cal.image_timestamp AS BIGINT) - CAST(to_unix_timestamp(scenes_with_syscal.started_at) AS BIGINT)*1000*1000*1000))\n", "        AS rank\n", "    FROM scenes_with_syscal\n", "    JOIN viper_dsp_sbx.hitl_corrections_prod.corrections_output cal on cal.syscal_name = calstore_syscal_name\n", "    WHERE cal.status = 'ACCEPTED' AND camera_stream = 'FC1'\n", ")\n", "\"\"\"\n", "\n", "# Step 5: Return only the scenes that have a correction within 12 hours (past or future)\n", "query5 = \"\"\"\n", "SELECT split_hash, time_difference_secs\n", "FROM scenes_with_ranked_corrections\n", "WHERE rank = 1 AND time_difference_secs < 12 * 60 * 60\n", "\"\"\"\n", "\n", "result_df = spark.sql(query1 + query2 + query3 + query4 + query5).select(\"split_hash\").distinct()\n", "scenes_with_hitl = [row.split_hash for row in result_df.collect()]\n", "\n", "display(result_df)"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "4484bdb1-03e1-4d58-a9bc-e5fe18013471", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Update ins_trigger_dates"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "54ea3556-f307-450c-83fb-7712ba428296", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import col, when, array_append, current_timestamp, size\n", "\n", "def update_status(df, available_list, status_col):\n", "    return df.withColumn(\n", "        # Change status: unavailable => triggered, or if inside available_list => available\n", "        status_col, \n", "        when(col(\"split_hash\").isin(available_list), \"available\")\n", "        #.when(col(status_col) == \"unavailable\", \"triggered\")\n", "        .otherwise(col(status_col))\n", "    )\n", "    ## TODO: This code has to be performed by each Trigger script (INS, POUNDS, ...)\n", "    #.withColumn(\n", "    #    # Set status to failed if retry_count is exceeded\n", "    #    status_col,\n", "    #    when(\n", "    #        size(col(trigger_dates_col)) >= retry_count,\n", "    #        \"failed\"\n", "    #    ).otherwise(col(status_col))\n", "    #).withColumn(\n", "    #    # Append current timestamp for all triggered entries\n", "    #    trigger_dates_col,\n", "    #    when(\n", "    #        col(status_col) == \"triggered\",\n", "    #        array_append(col(trigger_dates_col), current_timestamp())\n", "    #    ).otherwise(\n", "    #        col(trigger_dates_col)\n", "    #    )\n", "    #)\n", "\n", "scene_candidates_df = update_status(scene_candidates_df, scenes_with_ins, \"ins_status\")\n", "scene_candidates_df = update_status(scene_candidates_df, scenes_with_pounds, \"pounds_status\")\n", "scene_candidates_df = update_status(scene_candidates_df, scenes_with_lane_map_al, \"lane_map_alignment_status\")\n", "scene_candidates_df = update_status(scene_candidates_df, scenes_with_semseg, \"semseg_status\")\n", "scene_candidates_df = update_status(scene_candidates_df, scenes_with_hitl, \"hitl_status\")\n", "\n", "# Pyspark is bad :(\n", "# Timestamps and arrays behave strangely\n", "# 1. If the display is placed below the write operation, a mysterious entry occures in trigger_dates (isn't stored in the real table)\n", "# 2. Executing array_append(..., current_timestamp()) mutliple times changes all entries not only the last, even if all entries are cast to string before!\n", "#    To prevent this the data is written to the real table.\n", "# 3. Executing this cell twice leads to 3 appended entries instead of 2. (The third execution appends 3 entries and so on)\n", "#    To prevent this scene_candidates_df is read again at the end so that the second execution starts with a clean table.\n", "\n", "display(scene_candidates_df)\n", "scene_candidates_df.write.mode(\"overwrite\").saveAsTable(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")\n", "scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.scene_candidates\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 7692639631513232, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "update_scene_candidates", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}