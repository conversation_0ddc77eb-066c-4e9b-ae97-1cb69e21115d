{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2b784545-73d7-40fe-8218-ea3665c97bfd", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE viper_lane_sbx.atlatec_datapool.scenes_omg (\n", "    split_hash STRING COMMENT 'Scene (can occure multiple times with different start/end times)',\n", "    drive_hash STRING COMMENT 'SHA pointing to the drive in MDM',\n", "    started_at TIMESTAMP COMMENT 'Relevant start time',\n", "    ended_at TIMESTAMP COMMENT 'Relevant end time',\n", "    inserted_at TIMESTAMP COMMENT 'Timestamp of insertion',\n", "    dataset_split STRING COMMENT 'If this belongs to the training or test data'\n", ");"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b07d123b-759f-4662-9995-5d84b9808560", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE viper_lane_sbx.atlatec_datapool.labels_semseg (\n", "    split_hash STRING COMMENT 'Scene hash',\n", "    timestamp BIGINT COMMENT 'Exact timestamp of the label',\n", "    inserted_at TIMESTAMP COMMENT 'Timestamp of insertion',\n", "    stream_name STRING COMMENT 'Stream name of the sensor, e.g. FC1, etc.',\n", "    semseg_url STRING COMMENT 'Download link to sem AI data (currently not MDM, but in the future)'\n", ");"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5a97030d-c371-4d45-9b6c-dfea604dc7ae", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE viper_lane_sbx.atlatec_datapool.labels_lma (\n", "    split_hash STRING COMMENT 'Scene (can occur multiple times with different start/end times)',\n", "    timestamp BIGINT COMMENT 'Exact timestamp of the position',\n", "    inserted_at TIMESTAMP COMMENT 'Timestamp of insertion',\n", "    map_alignment_pose_ecef_quat ARRAY<DOUBLE>,\n", "    map_alignment_pose_ecef_trans ARRAY<DOUBLE>,\n", "    map_alignment_pose_stdev_euler_angles ARRAY<DOUBLE>,\n", "    map_alignment_pose_stdev_translation ARRAY<DOUBLE>,\n", "    map_alignment_pose_stdev_rotation_angle DOUBLE,\n", "    map_alignment_pose_stdev_translation_norm DOUBLE,\n", "    map_reference_sha STRING COMMENT 'SHA pointing to the map in MDM',\n", "    map_reference_tds_url STRING COMMENT 'URL to the map in TDS',\n", "    map_alignment_version_version STRING COMMENT 'Version of the map used for alignment',\n", "    map_alignment_version_hash STRING COMMENT 'SHA of the map used for alignment'\n", ");"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 6913880669083357, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "setup_initialize_result_tables", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}