{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3b30c9c8-e2eb-4d6c-8205-69ce6a9e55e6", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Searches inside scene candidates for all scenes that should get a HITL calibration.<br>\n", "Each car should get a calibration at each day and two calibrations if recording is longer than 8h.<br>\n", "Currently only a list of all scenes that should be calibrated is printed at the end, because no automated pipeline exists for the calibration."]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7073e74b-b71a-4b2c-85fb-066b3bc177fc", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Select the initial scene collection"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9950537c-307f-403d-a934-a108b1ee8f28", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["%python\n", "from pyspark.sql.functions import col, to_date, lit, coalesce\n", "\n", "### HINT: Select the scene candidates collection that should be used. Result has to include [split_hash]\n", "### HINT: Some lines are excluded below which are used to easily run this script for other datasets.\n", "#scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec.selected_splits\").filter(\"deleted_at IS NULL\")\n", "#scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.temp_bev_lane_fc_1_dataset_update_berlin_2025_01\").select(col(\"value\").alias(\"split_hash\"))\n", "scene_candidates_df = spark.table(\"viper_lane_sbx.atlatec_datapool.scene_candidates\").filter(col(\"hitl_status\").isin([\"unavailable\", \"triggered\"]))\n", "\n", "# Append start time of the scene\n", "split_info_df = spark.table(\"silver.mdd.datamanagement_split_info\")\n", "scene_candidates_df = scene_candidates_df.join(\n", "    split_info_df,\n", "    scene_candidates_df.split_hash == split_info_df.file_hash\n", ").select(\n", "    coalesce(scene_candidates_df.hitl_status, lit(\"unavailable\")).alias(\"hitl_status\"),\n", "    \"split_hash\",\n", "    split_info_df.start_time_utc.cast(\"timestamp\").alias(\"started_at\"),\n", "    to_date(split_info_df.start_time_utc.cast(\"timestamp\")).alias(\"scene_day\")\n", ")\n", "\n", "# Append VIN for each scene\n", "situation_dataset_df = spark.table(\"gold.drive_time_series.situation_dataset_full\")\n", "scene_candidates_df = scene_candidates_df.join(\n", "    situation_dataset_df,\n", "    \"split_hash\",\n", "    \"left\"\n", ").select(\n", "    \"hitl_status\",\n", "    \"split_hash\",\n", "    \"started_at\",\n", "    \"scene_day\",\n", "    \"vin\"\n", ").dropDuplicates([\"split_hash\"])\n", "\n", "# Remove scenes for which a HITL calibration is triggered near by (same day, same vehicle).\n", "triggered_scenes_df = scene_candidates_df.filter(col(\"hitl_status\") == \"triggered\")\n", "scene_candidates_df = scene_candidates_df.join(\n", "    triggered_scenes_df,\n", "    [\"vin\", \"scene_day\"],\n", "    \"left_anti\"\n", ").select(\n", "    \"split_hash\",\n", "    \"started_at\",\n", "    \"vin\"\n", ")\n", "\n", "print(f\"Scenes in the original collection: {scene_candidates_df.count()}\")\n", "display(scene_candidates_df.limit(100))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "353851fc-a230-4895-acf7-7bf0dba12107", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Append calstore_syscal_name for each scene"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0b5e2f38-6f99-46b1-8c94-764259a10db4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["# Table contains the calstore_syscal_name for vin + time combination (can be used to combine with scene)\n", "### HINT: This table should be part of silver layer. Until then use the following table\n", "calstore_syscals_df = spark.table(\"viper_coreai_sbx.loomy_dev.calstore_syscals\")\n", "scenes_with_syscal_df = scene_candidates_df.join(\n", "    calstore_syscals_df,\n", "    (scene_candidates_df.vin == calstore_syscals_df.vin) &\n", "    (scene_candidates_df.started_at >= calstore_syscals_df.valid_from) &\n", "    ((scene_candidates_df.started_at <= calstore_syscals_df.valid_until) | calstore_syscals_df.valid_until.isNull()),\n", "    \"inner\"\n", ").select(\n", "    scene_candidates_df.split_hash,\n", "    scene_candidates_df.started_at,\n", "    calstore_syscals_df.vin,\n", "    calstore_syscals_df.name.alias(\"calstore_syscal_name\")\n", ")\n", "print(f\"Scenes with syscal: {scenes_with_syscal_df.count()} (Should be the same amount as before)\")\n", "display(scenes_with_syscal_df.limit(100))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7d783c37-4744-4461-a02c-b3e8d87cc9f2", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Get all scenes with an offline calibration +-12h"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "527f6715-be78-4c4b-af94-90014101be75", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "from pyspark.sql.window import Window\n", "\n", "# Join scenes with corrections and rank the corrections by time difference between the scene and the image time used for calibration\n", "corrections_output_df = spark.table(\"viper_dsp_sbx.hitl_corrections_prod.corrections_output\").filter((col(\"status\") == \"ACCEPTED\") & (col(\"camera_stream\") == \"FC1\"))\n", "scenes_with_ranked_corrections_df = scenes_with_syscal_df.join(\n", "    corrections_output_df,\n", "    scenes_with_syscal_df.calstore_syscal_name == corrections_output_df.syscal_name\n", ").withColumn(\n", "    \"time_difference_secs\",\n", "    F.abs(col(\"image_timestamp\").cast(\"bigint\") / (1000 * 1000 * 1000) - F.unix_timestamp(scenes_with_syscal_df.started_at).cast(\"bigint\"))\n", ").withColumn(\n", "    \"rank\",\n", "    F.row_number().over(\n", "        Window.partitionBy(\"split_hash\", \"camera_stream\").orderBy(\n", "            F.abs(F.col(\"image_timestamp\").cast(\"bigint\") / (1000 * 1000 * 1000) - F.unix_timestamp(scenes_with_syscal_df.started_at).cast(\"bigint\"))\n", "        )\n", "    )\n", ")\n", "\n", "# Filter only the closest correction and also withing (+-)12h\n", "scenes_with_hitl_df = scenes_with_ranked_corrections_df.filter(\n", "    (col(\"rank\") == 1) & (col(\"time_difference_secs\") < 12 * 60 * 60)\n", ").select(\n", "    \"split_hash\",\n", "    \"time_difference_secs\"\n", ")\n", "\n", "print(f\"Scenes with corrections: {scenes_with_hitl_df.count()}\")\n", "display(scenes_with_hitl_df.limit(100))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "63bd3617-ceab-4ebb-a34a-71c770b9c6d7", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Generate a table for each scene in selected_scenes (that don't have hitl nearby) and mark scenes which include stand sill"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "077c6052-f821-4ada-aafa-17308dcbadba", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "\n", "# Step 1: scenes without hitl\n", "scenes_without_hitl_df = scene_candidates_df.join(scenes_with_hitl_df, \"split_hash\", \"left_anti\").select(\"split_hash\", \"vin\", \"started_at\")\n", "\n", "# Step 2: scenes with speed histogram\n", "vehicle_metadata_df = spark.table(\"silver.mdd.dsp_de_vehicle_metadata\")\n", "scenes_with_speed_df = scenes_without_hitl_df.join(\n", "    vehicle_metadata_df,\n", "    scenes_without_hitl_df.split_hash == vehicle_metadata_df.file_hash,\n", "    \"left\"\n", ").select(\n", "    \"split_hash\",\n", "    \"vin\",\n", "    \"started_at\",\n", "    \"speed__stats__min\",\n", "    \"speed__stats__max\",\n", "    # Return the speed histrogram and the sum of the histogram\n", "    F.col(\"speed__histogram__buckets__count\").alias(\"speed_histogram\"),\n", "    F.expr(\"AGGREGATE(speed__histogram__buckets__count, 0, (acc, x) -> acc + x)\").alias(\"speed_histogram_sum\")\n", ")\n", "\n", "missing_data = scenes_with_speed_df.filter(F.col(\"speed_histogram\").isNull()).count()\n", "print(f\"Warning: {missing_data} scenes have no min/max speed and no speed histogram\")\n", "\n", "# Step 3: scenes_stand_still_percentage\n", "scenes_stand_still_percentage_df = scenes_with_speed_df.select(\n", "    \"split_hash\",\n", "    \"vin\",\n", "    \"started_at\",\n", "    (F.col(\"speed_histogram\")[0] / (F.col(\"speed_histogram_sum\") + 0.001)).alias(\"percentage_stand_still\"), # Prevent division by zero\n", "    F.when(\n", "        (F.col(\"speed_histogram\")[0] > 0.2 * F.col(\"speed_histogram_sum\")) & # 20% stand still required -> Prevents a lot of false positives.\n", "        (<PERSON><PERSON>col(\"speed__stats__min\") < 0.0001) & \n", "        (<PERSON><PERSON>col(\"speed__stats__max\") > -0.0001), \n", "        1\n", "    ).otherwise(0).alias(\"has_stand_still\")\n", ").orderBy(\"vin\", \"started_at\")\n", "\n", "total_has_stand_still = scenes_stand_still_percentage_df.filter(F.col(\"has_stand_still\") == 1).count()\n", "print(f\"{total_has_stand_still} scenes have stand still out of {scenes_stand_still_percentage_df.count()} scenes that require calibration\")\n", "display(scenes_stand_still_percentage_df.limit(100))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "df077a9a-8e02-44ce-8de6-1a6a7ee9e8c1", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Generate a table at which time points calibrations are required for each vehicle"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "098572b9-d3d2-4ad7-9aa3-6e4603f239e4", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "\n", "# Step 1: Scenes per day with driven hours, day begin and day end\n", "scenes_per_day_df = scenes_stand_still_percentage_df.groupBy(\n", "    \"vin\",\n", "    F.to_date(\"started_at\").alias(\"day\")\n", ").agg(\n", "    F.count(\"*\").alias(\"scenes\"),\n", "    F.sum(F.col(\"has_stand_still\")).alias(\"scenes_with_stand_still\"),\n", "    (F.hour(F.max(\"started_at\")) - F.hour(F.min(\"started_at\"))).alias(\"hours\"),\n", "    F.min(\"started_at\").alias(\"day_begin\"),\n", "    F.max(\"started_at\").alias(\"day_end\")\n", ").orderBy(\"vin\", \"day\")\n", "\n", "# Step 2: Generate the times (middle_time and middle_time2) for which calibration is required\n", "required_hitl_times_df = scenes_per_day_df.withColumn(\n", "    \"middle_time\",\n", "    F.when(\n", "        <PERSON>.col(\"hours\") < 8,\n", "        F.from_unixtime((F.unix_timestamp(\"day_begin\") + F.unix_timestamp(\"day_end\")) / 2)\n", "    ).otherwise(\n", "        F.from_unixtime(F.unix_timestamp(\"day_begin\") + ((F.unix_timestamp(\"day_end\") - F.unix_timestamp(\"day_begin\")) / 4))\n", "    )\n", ").withColumn(\n", "    \"middle_time2\",\n", "    F.when(\n", "        <PERSON>.col(\"hours\") < 8,\n", "        F.lit(None)\n", "    ).otherwise(\n", "        F.from_unixtime(F.unix_timestamp(\"day_begin\") + ((F.unix_timestamp(\"day_end\") - F.unix_timestamp(\"day_begin\")) * 3 / 4))\n", "    )\n", ")\n", "\n", "total_days = required_hitl_times_df.count()\n", "calibratable_days = required_hitl_times_df.filter(F.col(\"scenes_with_stand_still\") > 0).count()\n", "print(f\"Calibration required for {total_days} days and vehicles. (Can be 2 kalibrations per day)\")\n", "print(f\"Days that can be calibrated: {calibratable_days} ({int(calibratable_days/total_days*100)}%)\")\n", "display(required_hitl_times_df.limit(100))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "10065f20-1495-4ac1-a143-188a68201b2d", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "source": ["Generate the final list with scenes that should get a calibration"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d13dc35b-88b8-4cf4-9684-b88983b6d69a", "showTitle": false, "tableResultSettingsMap": {}, "title": ""}}, "outputs": [], "source": ["from pyspark.sql import functions as F\n", "from pyspark.sql.window import Window\n", "\n", "# Step 1: Generate list of all target times (Times for which a HITL correction is required)\n", "required_target_times_df = required_hitl_times_df.filter(F.col(\"scenes_with_stand_still\") > 0).select(\n", "    \"*\",\n", "    F.col(\"middle_time\").alias(\"target_time\")\n", ").union(\n", "    # Include the second calibration only if multiple stand still scenes are available\n", "    required_hitl_times_df.filter((<PERSON>.col(\"scenes_with_stand_still\") > 5) & (F.col(\"middle_time2\").isNotNull())).select(\n", "        \"*\",\n", "        F.col(\"middle_time2\").alias(\"target_time\")\n", "    )\n", ")\n", "\n", "# Step 2: Calculate how close each scene is to the required target times\n", "ranked_scenes_df = scenes_stand_still_percentage_df.filter(<PERSON>.col(\"has_stand_still\") == 1).alias(\"a\").join(\n", "    required_target_times_df.alias(\"b\"),\n", "    (F.col(\"a.vin\") == F.col(\"b.vin\")) & (F.to_date(F.col(\"a.started_at\")) == F.col(\"b.day\")),\n", "    \"left\"\n", ").select(\n", "    \"a.split_hash\",\n", "    \"a.percentage_stand_still\",\n", "    \"a.vin\",\n", "    \"a.started_at\",\n", "    \"b.day\",\n", "    \"b.target_time\",\n", "    (F.abs(F.unix_timestamp(F.col(\"a.started_at\")) - F.unix_timestamp(F.col(\"b.target_time\"))) / 3600).alias(\"hour_distance\"),\n", "    F.row_number().over(\n", "        Window.partitionBy(<PERSON><PERSON>col(\"b.day\"), <PERSON><PERSON>col(\"b.vin\"), <PERSON><PERSON>col(\"b.target_time\")).orderBy(F.abs(F.unix_timestamp(F.col(\"a.started_at\")) - F.unix_timestamp(F.col(\"b.target_time\"))))\n", "    ).alias(\"rank_time\")\n", ")\n", "\n", "# Step 3: Select the 3 closest scenes for each target time and rank them by the stand still percentage.\n", "ranked_top_3_df = ranked_scenes_df.filter(F.col(\"rank_time\") < 4).select(\n", "    \"*\",\n", "    F.row_number().over(\n", "        Window.partitionBy(<PERSON><PERSON>col(\"day\"), <PERSON><PERSON>col(\"vin\"), <PERSON><PERSON>col(\"target_time\")).orderBy(F.col(\"percentage_stand_still\").desc())\n", "    ).alias(\"rank_stand_still\")\n", ").orderBy(<PERSON><PERSON>col(\"day\"), <PERSON><PERSON>col(\"vin\"), <PERSON><PERSON>col(\"target_time\"), <PERSON><PERSON>col(\"rank_time\"))\n", "\n", "# Step 4: Final selection\n", "final_df = ranked_top_3_df.filter(<PERSON>.col(\"rank_stand_still\") == 1)\n", "\n", "final_df.write.mode(\"overwrite\").saveAsTable(\"viper_lane_sbx.atlatec_datapool.temp_get_hitl_for_scenes\")\n", "display(final_df)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"computePreferences": null, "dashboards": [], "environmentMetadata": {"base_environment": "", "environment_version": "2"}, "language": "python", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "trigger_hitl_calibration", "widgets": {}}, "language_info": {"name": "python"}}, "nbformat": 4, "nbformat_minor": 0}