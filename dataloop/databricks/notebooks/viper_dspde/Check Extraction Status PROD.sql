-- Databricks notebook source
-- MAGIC %md
-- MAGIC # Check dsp_de_split_extracted

-- COMMAND ----------

CREATE OR REPLACE TABLE viper_dsp_sbx.dex_dev.standard_extractions_prod
WITH std_extractions AS (
  SELECT
    *
  FROM
    silver.mdd.dsp_de_split_extracted
),
test_streams AS (
  SELECT
    DISTINCT(file_hash),
    explode(
      ARRAY(
        "image_fc1",
        "image_fc2",
        "image_tv_left",
        "image_tv_front",
        "image_tv_right",
        "image_tv_rear",
        "flexray",
        "can",
        "gps",
        "metadata",
        "lidar_velodyne",
        "lidar_innoviz"
        -- "image_japan_fc1"
      )
    ) as stream
  FROM
    std_extractions
),
finished AS (
  SELECT
    file_hash,
    stream,
    True as Finished
  FROM
    std_extractions
  WHERE
    COALESCE(entry_state, 'FINISHED') = 'FINISHED'
  GROUP BY
    file_hash,
    stream
),
invalid AS (
  SELECT
    file_hash,
    stream,
    True as Invalid,
    collect_set(entry :details) as errors
  FROM
    std_extractions
  WHERE
    entry_state = 'INVALID'
  GROUP BY
    file_hash,
    stream
),
keep_on_hot_storage AS (
  SELECT
    file_hash,
    True as HotStorage
  FROM
    dd_leadership_pub_sbx.data_tiering_service.keep_on_hotstorage
),
tds_scenes AS (
  SELECT
    file_hash,
    time_partition,
    file_state
  FROM
    silver.tds.file_entries
  WHERE
    file_extension = "scene" -- OR file_extension = "bag"
)

SELECT
  *
FROM
  test_streams
  LEFT JOIN finished USING (file_hash, stream)
  LEFT JOIN invalid USING (file_hash, stream)
  LEFT JOIN keep_on_hot_storage USING (file_hash)
  LEFT JOIN tds_scenes USING (file_hash);


-- COMMAND ----------

SELECT
  *
FROM
  viper_dsp_sbx.dex_dev.standard_extractions_prod

-- COMMAND ----------

SELECT
  stream,
  Finished,
  Invalid,
  count(*)
FROM
  viper_dsp_sbx.dex_dev.standard_extractions_prod
WHERE
  file_state = "ACTIVE"
GROUP BY
  stream,
  Finished,
  Invalid
SORT BY
  stream,
  Finished
  ASC

-- COMMAND ----------

WITH mapped_status AS (
  SELECT
    stream,
    CASE
      WHEN Invalid = True THEN "Invalid"
      WHEN Finished = True THEN "Finished"
      ELSE "Missed or Failed"
    END AS status
  FROM
    viper_dsp_sbx.dex_dev.standard_extractions_prod
)
SELECT
  stream,
  status,
  COUNT(*)
FROM
  mapped_status
GROUP BY
  stream,
  status
