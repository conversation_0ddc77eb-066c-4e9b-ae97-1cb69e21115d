# Databricks notebook source
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

# MAGIC
# MAGIC %md
# MAGIC # Execute the deletion of the data selected in previous steps

# COMMAND ----------

# Databricks Tables
CATALOG_FILES_TO_BE_DELETED = "dd_leadership_pub_sbx"
SCHEMA_FILES_TO_BE_DELETED = "ab233021_rtc_deletion"
TABLE_FILES_TO_BE_DELETED = "deprecated_yuv"
FULLPATH_TO_BE_DELETED_TABLE = "viper_dsp_sbx.dex_dev.deprecated_yuv"
NAMESPACES_TO_DELETE = ["dsp_de_image"]
FULLPATH_ALREADY_DELETED_TABLE = (
    f"{CATALOG_FILES_TO_BE_DELETED}.{SCHEMA_FILES_TO_BE_DELETED}.__bookkeeping_{TABLE_FILES_TO_BE_DELETED}"
)
SLICE_SIZE=1000000
SUB_SLICE_SIZE=6000
N_PARALLEL_CONNECTIONS=500

spark.conf.get("spark.driver.maxResultSize")

# COMMAND ----------

# configuring logger
import logging

logging.basicConfig(
    level=logging.INFO,
    format="[%(levelname)s][%(asctime)s] %(message)s  ",
)
log = logging.getLogger("Data Deletion")
log.info("Starting")
logging.getLogger("py4j.clientserver").setLevel(logging.WARN)
logging.getLogger("azure").setLevel(logging.WARN)
logging.getLogger("httpx").setLevel(logging.WARN)
logging.getLogger("urllib3.connectionpool").setLevel(logging.WARN)

import asyncio
import datetime
import random
import uuid

import aiohttp
import pyspark.sql.functions as F
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_mdm_accessor import AsyncMddAccessor, AsyncMdmAccessor, MdmException, MdmFileEntryNotFoundException
from pyspark.errors import AnalysisException
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

from mdm.lib.mdm_search_module.v2.constants import Environment

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_DELETION_APPID = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--appid"
)
AAD_SP_DELETION_SECRET = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--secret"
)
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential_deletion = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_DELETION_APPID, client_secret=AAD_SP_DELETION_SECRET
)
aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)

aad_token_cache_deletion = AadTokenCache(credential_provider=aad_credential_deletion)
aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# APIM Setup
apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
apim_key = kv_client.get_secret("ddleadershipdatabricks-primary-key")

# MDM Accessor setup
environment = Environment.PROD
mdm_accessor = AsyncMdmAccessor(
    credential_provider=aad_token_cache,
    environment=environment,
    use_api_gateway=True,
    credential_provider_deletion=aad_token_cache_deletion,
    apim_key=apim_key.value,
)
mdd_accessor = AsyncMddAccessor(
    credential_provider=aad_token_cache,
    environment=environment,
    use_api_gateway=True,
    apim_key=apim_key.value,
)

# COMMAND ----------

spark.sql(f"""CREATE TABLE IF NOT EXISTS {FULLPATH_ALREADY_DELETED_TABLE} (
  file_hash STRING
  ,tds_fid STRING
  ,tds_state STRING
  ,deleted_at TIMESTAMP
  ,deletion_process_uuid STRING
)""")

# COMMAND ----------

try:
    spark.sql(f"""ALTER TABLE {FULLPATH_ALREADY_DELETED_TABLE} ADD COLUMN deletion_error STRING COMMENT 'Error encountered while deleting file in TDS'""")
    spark.sql(f"""ALTER TABLE {FULLPATH_ALREADY_DELETED_TABLE} ADD COLUMN mdd_entry_deletion_error STRING COMMENT 'Error encountered while deleting MDD namespace'""")
    spark.sql(f"""ALTER TABLE {FULLPATH_ALREADY_DELETED_TABLE} ADD COLUMN mdd_entry_deleted_at TIMESTAMP COMMENT 'When was the mdd namespace dleeted'""")
except AnalysisException as e:
    print(logging.warn("Failed to add column(s) to table with exception %s: %s", type(e).__name__, e))

# COMMAND ----------

print(FULLPATH_ALREADY_DELETED_TABLE)

# COMMAND ----------


def get_file_hashes_to_delete(include_files_in_error: bool = False):
    # df_already_deleted = spark.read.format("delta").table(FULLPATH_ALREADY_DELETED_TABLE)
    # df_to_be_deleted = spark.read.format("delta").table(FULLPATH_TO_BE_DELETED_TABLE)
    df = spark.read.format("delta").table(FULLPATH_ALREADY_DELETED_TABLE)
    if not include_files_in_error:
        df = df.where(df.deletion_error.isNull())
    
    return (
        df
        .where(df.deleted_at.isNull())
        .select(col("file_hash"), col("tds_fid"))
        .limit(SLICE_SIZE)
        # .distinct()
    )

# COMMAND ----------

# display(get_file_hashes_to_delete().limit(10))
# print(f"Going to delete {file_deletion_list.count()} files")

# COMMAND ----------

deletion_correlation_id = uuid.uuid4()
print(f"All deletions are executed with correlation id {deletion_correlation_id}")

# COMMAND ----------

async def delete_file(
    session: aiohttp.ClientSession, tds_client: AsyncMdmAccessor, mdd_client: AsyncMddAccessor, this_sha: str, correlation_id: uuid.UUID, this_fid: Optional[str]=None
):
    error_msg = ""
    tds_fids = None

    # to not start all requests at the same time, add a small startup delay
    start_up_delay = random.random()
    await asyncio.sleep(start_up_delay)

    # resolve sha to TDS FIDs
    if this_fid is None:
        try:
            tds_fids_raw = await tds_client.get_fid_by_sha(session=session, sha=this_sha, request_correlation_id=correlation_id)            
            tds_fids = [x['fid'] for x in tds_fids_raw if x['state'].lower() == 'ok' ]
            logging.info(f"[{this_sha}] Maps to TDS file ids: {tds_fids}")
            if len(tds_fids) == 0:
                    error_msg = f"[{this_sha}] has no fids in state ok"
        except MdmFileEntryNotFoundException:
            error_msg = "File not found"
            logging.error(f"[{this_sha}] no file with hash in TDS")
        except MdmException as e:
            error_msg = f"[{this_sha}] Curious error caught file fetching TDS FID: {e}"
            logging.error(error_msg)
    else:
        tds_fids = [this_fid]

    # Delete namespace documents for SHA
    mdd_error_msg = ""
    # for this_namespace in NAMESPACES_TO_DELETE:
    #     try:
    #         await mdd_client.delete_namespace_document(session, request_correlation_id=correlation_id, sha=this_sha, namespace_name=this_namespace)
    #     except Exception as e:
    #         mdd_error_msg = f"[{this_sha}/{this_namespace}] Failed to delete namespace ({type(e).__name__}): {e}"
    #         logging.error(mdd_error_msg)

    # Delete FIDs in TDS
    if tds_fids is not None:
        for tds_fid in tds_fids:
            try:
                await tds_client.delete_file_by_fid(
                    session, fid=tds_fid, request_correlation_id=correlation_id
                )
                # logging.info(f"[{this_sha}] Success")
            except Exception as e:
                logging.error(f"[{this_sha}] Caught exception on deletion({type(e).__name__}): {e}")
                error_msg = str(e)

    return {"sha": this_sha, "fid": tds_fids, "error_msg": error_msg, "mdd_error_msg": mdd_error_msg}


# COMMAND ----------

async def gather_dictionary(tasks: dict):
    async def mark(key, coroutine):
        return key, await coroutine

    try:
        loop = asyncio.get_running_loop()
        return {
            key: result
            for key, result in await asyncio.gather(*(mark(key, coroutine) for key, coroutine in tasks.items()))
        }
    except RuntimeError:
        loop = asyncio.new_event_loop()
        return {
            key: result
            for key, result in await asyncio.gather(*(mark(key, coroutine) for key, coroutine in tasks.items()))
        }


# COMMAND ----------

def convert_resultset_to_dataset(resultset):
    rows = []
    if not isinstance(resultset, dict):
        logging.error("convert_resultset_to_dataset(...): the resultset is not a dictionary")
        return None
    
    for _key, _value in resultset.items():
        if _value is None:
            logging.error("convert_resultset_to_dataset(...): Skipping None object in resultset dictionary")
            continue

        is_failed = len(_value.get("error_msg", "")) > 0
        is_mdd_failed = len(_value.get("mdd_error_msg", "")) > 0

        fid_obj = _value.get("fid", [])
        if len(fid_obj) == 0:
            rows.append(
                {
                    "file_hash": _key,
                    "tds_fid": None,
                    "tds_state": "UNKNOWN",
                    "deleted_at": None,
                    "deletion_error": _value.get("error_msg"),
                    "deletion_process_uuid": str(deletion_correlation_id),
                }
            )
        else:
            for fid_entry in fid_obj:
                if isinstance(fid_entry, str):
                    fid = fid_entry
                    fid_state = "UNKNOWN"
                else:
                    fid = fid_entry.get("fid", "")
                    fid_state = fid_entry.get("state", "")
  
                
                if is_failed:
                    rows.append(
                        {
                            "file_hash": _key,
                            "tds_fid": fid,
                            "tds_state": fid_state,
                            "deleted_at": None,
                            "deletion_error": _value.get("error_msg"),
                            "deletion_process_uuid": str(deletion_correlation_id),
                        }
                    )
                else:
                    rows.append(
                        {
                            "file_hash": _key,
                            "tds_fid": fid,
                            "tds_state": fid_state,
                            "deleted_at": datetime.datetime.now(),
                            "deletion_error": None,
                            "deletion_process_uuid": str(deletion_correlation_id),
                        }
                    )            

    logging.info("Number of rows %i", len(rows))
    if len(rows) > 0:
        return spark.createDataFrame(rows, schema=StructType([
            StructField("file_hash", StringType(), True),
            StructField("tds_fid", StringType(), True),
            StructField("tds_state", StringType(), True),
            StructField("deleted_at", TimestampType(), True),
            StructField("deletion_error", StringType(), True),
            StructField("deletion_process_uuid", StringType(), True),
        ]))
    else:
        return None


# COMMAND ----------

def update_state(df):
    df.createOrReplaceTempView("source")

    spark.sql(f"""MERGE INTO {FULLPATH_ALREADY_DELETED_TABLE} AS target
              USING source AS source
              ON
                target.file_hash=source.file_hash
                AND target.tds_fid=source.tds_fid
              WHEN MATCHED THEN UPDATE SET target.deleted_at=source.deleted_at, target.deletion_process_uuid=source.deletion_process_uuid, target.deletion_error=source.deletion_error
              """)

# COMMAND ----------

import traceback

aiohttp_connector = aiohttp.TCPConnector(limit=N_PARALLEL_CONNECTIONS)
aiohttp_timeout = aiohttp.ClientTimeout(total=10) #, connect=5, sock_connect=5, sock_read=5)

async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    iteration = 0
    while True:
        # if iteration > 10: break
        logging.info("Retrieve dataframe of upto %i files to delete", SLICE_SIZE)
        file_deletion_list = [x.asDict() for x in get_file_hashes_to_delete().limit(SLICE_SIZE).collect()]
        if len(file_deletion_list) == 0:
            logging.info("It's done all files are deleted")
            break
        if len(file_deletion_list) < 100:
            for row in file_deletion_list:
                logging.info(row)
       
        logging.info(f"Going to delete {len(file_deletion_list)} files")

        deletion_state = {}
        i_sub_slice = 0
        while True:
            if i_sub_slice >= SLICE_SIZE or i_sub_slice > len(file_deletion_list): break
            logging.info("Processing sub slice %i..%i", i_sub_slice, i_sub_slice+SUB_SLICE_SIZE)
            try:
                deletion_state_interim = await gather_dictionary(
                    {
                        this_entry['file_hash']: delete_file(session, mdm_accessor, mdd_accessor, this_entry['file_hash'], this_fid=this_entry['tds_fid'],correlation_id=deletion_correlation_id)
                        for this_entry in file_deletion_list[i_sub_slice:i_sub_slice+SUB_SLICE_SIZE]
                    }
                )
                i_sub_slice += SUB_SLICE_SIZE
                deletion_state.update(deletion_state_interim)
            except Exception as e:
                print(f"An exception occured {e}")
                traceback.print_exc()

        # save last deleted entries
        if len(deletion_state) > 0:
            if len(deletion_state) < 100:
                for _key, _value in deletion_state.items():
                    logging.info("State for %s: %s", _key, _value)
            df_result = convert_resultset_to_dataset(deletion_state)
            if df_result is not None:
                logging.info("Write Results")
                for i_retry_write in range(10):
                    try:
                        update_state(df_result)
                        break
                    except Exception as e:
                        logging.error(e)
            else:
                logging.warning("No results to write back after conversion")
        else:
            logging.warning("Deletion states is empty")

        # statistics
        total_http_calls = 0
        for _key, _value in mdm_accessor._stats_http_call_count.items():
            logging.info("Statistics http_calls to %s: %i", _key, _value )
            total_http_calls += _value

        total_method_calls = 0
        for _key, _value in mdm_accessor._stats_method_invocation_count.items():
            logging.info("Statistics method_calls to %s: %i", _key, _value )
            total_method_calls += _value
        logging.info("Statistics summary total_http_calls=%i / total_method_calls=%i / avg_number_of_retries=%f", total_http_calls, total_method_calls, total_http_calls/total_method_calls)

        # DEBUGGING
        # break
        iteration += 1


# COMMAND ----------

# display(spark.sql(f"""SELECT * FROM {FULLPATH_ALREADY_DELETED_TABLE} LIMIT 100"""))

# COMMAND ----------

# MAGIC %md
# MAGIC # Execute namespace deletion

# COMMAND ----------

# aiohttp_connector = aiohttp.TCPConnector(limit=20)
# aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=10)
# async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
#     print(await mdm_accessor.get_tds_entry_by_sha(session=session, sha="a7122579c4548a84df6f4d929b3f346885fbbbd85b37dea5f7dc39648f997db4", request_correlation_id=deletion_correlation_id))
#     #print(await mdm_accessor.get_fid_by_sha(session=session, sha="a7122579c4548a84df6f4d929b3f346885fbbbd85b37dea5f7dc39648f997db4", request_correlation_id=deletion_correlation_id))
