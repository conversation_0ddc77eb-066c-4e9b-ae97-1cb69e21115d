"""Asynchronous acessor for MDM API."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
# ============================================================================================================
# C O P Y R I G H T                                                                                          \
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2023 Robert Bosch GmbH and Cariad SE. All rights reserved.                                  \
# ============================================================================================================

import asyncio
import random
import time
import uuid
from datetime import datetime
from typing import Dict, List, Literal, Optional

import aiohttp
from aiohttp.client_exceptions import ClientOSError, ServerTimeoutError
from modules.aad_token_cacher import AadTokenCache
from pydantic import BaseModel

from mdm.lib.mdm_search_module.v2.constants import Environment


class TDSFileEntry(BaseModel):
    """Structure of a TDS file descriptor."""

    fid: str
    """ file id, a uuid"""
    sha: str
    """ SHA256 hash sum over file"""
    pool: str
    """ storage pool """
    container: str
    """ container within storage pool """
    state: Literal["new", "processing", "ok", "error", "expired", "deleted"]
    name: str
    size: int
    content_type: str
    blob_url: str
    upload_user_id: str
    upload_expiry: datetime
    created: datetime
    modified: datetime
    parents: List[str]


class TooManyRequestsException(Exception):
    """Exception generated on http status 429."""

    pass


class MdmException(Exception):
    """A general error happened while calling MDM service."""

    pass


class MdmFileEntryNotFoundException(MdmException):
    """File entry not found in MDM."""

    pass


class AsyncMdmAccessor:
    """MDM Accessor for data deletion."""

    def __init__(
        self,
        credential_provider: AadTokenCache,
        environment: Environment,
        use_api_gateway: bool = True,
        credential_provider_deletion: Optional[AadTokenCache] = None,
        apim_key: str = None,
    ):
        """Create MDM accessor with all needed dependencies.

        Args:
            credential_provider (AadTokenCache): AAD Token Cacher instance.
            environment (Environment): Envronment (DEV, QA, PROD) to execute against
            use_api_gateway (bool, optional): Shall Data Delivery API Gateway be used. Defaults to True, i.e. API gateway is used by default.
            credential_provider_deletion (Optional[AadTokenCache], optional): Optionally, you can provide a dedicated credential provider for deletion requests. If no dedicated deletion credential is provided, the default credential_provider is used.
            apim_key (str, optional): In case your service has an APIM key, use it. Otherwise you fall into the default layer of 10req/s.

        Raises:
            Exception: _description_
        """
        self._credential_provider = credential_provider

        self._apim_key = apim_key

        if credential_provider_deletion is None:
            self._credential_provider_deletion = credential_provider
        else:
            self._credential_provider_deletion = credential_provider_deletion

        if environment == Environment.PROD:
            if use_api_gateway:
                self._base_url = "https://data-delivery-api.ad-alliance.biz/mdtds"
            else:
                self._base_url = "https://app-mdtds-pace-westeurope.azurewebsites.net"
            self._aad_audience = "api://sp-pace-mdtds-pace-westeurope/.default"
        elif environment == Environment.QA:
            if use_api_gateway:
                self._base_url = "https://data-delivery-api-qa.ad-alliance.biz/mdtds"
            else:
                self._base_url = "https://app-mdtds-paceqa-westeurope.azurewebsites.net"
            self._aad_audience = "api://sp-paceqa-mdtds-pace-westeurope/.default"
        else:
            raise Exception(f"Unknown MDM environment '{environment}")

        self._max_retries = 10
        self._min_wait_time_seconds = 0.5

        # statistics
        self._stats_lock = asyncio.Lock()
        self._stats_http_call_max_wait_time: Dict[str, float] = {}
        self._stats_http_call_count: Dict[str, int] = {}
        self._stats_method_invocation_count: Dict[str, int] = {}

    async def _increment_http_call_max_wait_time(self, method_name: str, this_wait_time_seconds: float):
        async with self._stats_lock:
            if method_name in self._stats_http_call_count:
                self._stats_http_call_max_wait_time[method_name] = this_wait_time_seconds
            else:
                self._stats_http_call_max_wait_time[method_name] = max(self._stats_http_call_max_wait_time[method_name], this_wait_time_seconds)

    async def _increment_http_call_count(self, method_name: str, increment: int = 1):
        async with self._stats_lock:
            if method_name in self._stats_http_call_count:
                self._stats_http_call_count[method_name] += increment
            else:
                self._stats_http_call_count[method_name] = increment

    async def _increment_method_invocation_count(self, method_name: str, increment: int = 1):
        async with self._stats_lock:
            if method_name in self._stats_method_invocation_count:
                self._stats_method_invocation_count[method_name] += increment
            else:
                self._stats_method_invocation_count[method_name] = increment

    def _get_default_headers(self, correlation_id: uuid.UUID, token_provider: AadTokenCache) -> Dict[str, str]:
        bearer_token = token_provider.get_token(self._aad_audience).token
        result = {
            "Authorization": f"Bearer {bearer_token}",
            "'x-client-request-id'": str(correlation_id),
        }
        if self._apim_key is not None:
            result["apikey"] = self._apim_key
        return result

    async def get_tds_entry_by_sha(
        self, session: aiohttp.ClientSession, sha: str, request_correlation_id: uuid.UUID
    ) -> List[TDSFileEntry]:
        """Get TDS entries for a SHA2 256bit.

        A hash can return multiple entries in case of special situations during ingest.

        Args:
            session (aiohttp.ClientSession): aiohttp session to use
            sha (str): SHA2 256bit of file to find
            request_correlation_id (uuid.UUID): request correlation id to use

        Raises:
            MdmFileEntryNotFoundException: file not found in TDS

        Returns:
            List[TDSFileEntry]: List of entries matching the hash
        """

        request_params = {"sha": sha}

        await self._increment_method_invocation_count("get_tds_entry_by_sha")

        headers = self._get_default_headers(request_correlation_id, token_provider=self._credential_provider)

        for i_try in range(self._max_retries):
            try:
                async with session.get(
                    f"{self._base_url}/v1/files", params=request_params, headers=headers
                ) as response:
                    if response.status == 429 or response.status == 500:
                        # Besides 429 (retry later), the Azure Backend sometimes returns 
                        # 500 where a retry helps or even a 
                        retry_wait_time = float(response.headers.get("Retry-After", str(self._min_wait_time_seconds)))
                        actual_wait_time = retry_wait_time * (1 + random.random())
                        await self._increment_http_call_max_wait_time("get_tds_entry_by_sha", actual_wait_time)
                        time.sleep(actual_wait_time)
                        raise TooManyRequestsException()

                    response.raise_for_status()
                    json_response = await response.json()

                    results = [TDSFileEntry(**item) for item in json_response.get("items")]

                    if len(results) == 0:
                        await self._increment_http_call_count("get_tds_entry_by_sha", i_try + 1)
                        raise MdmFileEntryNotFoundException(f"Hash '{sha}' not found in MDM system.")

                    await self._increment_http_call_count("get_tds_entry_by_sha", i_try + 1)
                    return results
            except TooManyRequestsException:
                continue
            except (TimeoutError, ServerTimeoutError, ClientOSError):
                retry_wait_time = float(self._min_wait_time_seconds)
                actual_wait_time = retry_wait_time * (1 + random.random())
                await asyncio.sleep(actual_wait_time)
                continue

        # final one
        await self._increment_http_call_count("get_tds_entry_by_sha", i_try + 1)
        raise MdmException(f"Finally failed after {self._max_retries}")

    async def get_fid_by_sha(
        self, session: aiohttp.ClientSession, sha: str, request_correlation_id: uuid.UUID
    ) -> List[str]:
        """Get TDS file id (fid) for a SHA2 256bit value.

        Args:
            session (aiohttp.ClientSession): aiohttp session to use
            sha (str): SHA2 256bit of searched for file
            request_correlation_id (uuid.UUID): correlation id for request

        Raises:
            MdmFileEntryNotFoundException: File not found in MDM
            MdmException: Sha2 256 doesn't match uniquely. An error that should be impossible

        Returns:
            str: TDS file id
        """

        tds_entries = await self.get_tds_entry_by_sha(session, sha=sha, request_correlation_id=request_correlation_id)
        return [{"fid": x.fid, "state": x.state} for x in tds_entries]

    async def delete_file_by_fid(
        self, session: aiohttp.ClientSession, fid: str, request_correlation_id: uuid.UUID
    ) -> Dict[str, str]:
        """Delete file in TDS by fid.

        The file identified by the file identifier (fid) is deleted from TDS.
        All attached MDD namespaces are untouched by this process.

        Args:
            session (aiohttp.ClientSession): aiohttp session to use for calls
            fid (str): TDS file descriptor (a uuid)
            request_correlation_id (uuid.UUID): correletion id to use for requests

        Raises:
            MdmException: Any non success http status leads to this exception.

        Returns:
            Dict[str, str]: the raw json returned by TDS in case of success
        """
        headers = self._get_default_headers(request_correlation_id, token_provider=self._credential_provider_deletion)

        await self._increment_method_invocation_count("delete_file_by_fid")

        for i_try in range(self._max_retries):
            try:
                async with session.delete(f"{self._base_url}/v1/files/{fid}", headers=headers) as response:
                    if response.status == 429 or response.status == 500 or response.status == 507:
                        # Besides 429 (retry later), the Azure Backend sometimes returns 
                        # 500 where a retry helps or even a 
                        # 507 if the storage account itself has an issue
                        retry_wait_time = float(response.headers.get("Retry-After", str(self._min_wait_time_seconds)))
                        actual_wait_time = retry_wait_time * (1 + random.random())
                        await asyncio.sleep(actual_wait_time)
                        await self._increment_http_call_max_wait_time("delete_file_by_fid", actual_wait_time)
                        raise TooManyRequestsException()


                    if response.status in (200, 201, 400, 401, 403, 404, 422):
                        json_response = await response.json()
                        if response.status < 300:
                            await self._increment_http_call_count("delete_file_by_fid", i_try + 1)
                            return json_response
                        else:
                            await self._increment_http_call_count("delete_file_by_fid", i_try + 1)
                            raise MdmException(json_response.get("detail", []))
                    
                    await self._increment_http_call_count("delete_file_by_fid", i_try + 1)
                    response.raise_for_status()
            except TooManyRequestsException:
                continue
            except (TimeoutError, ServerTimeoutError, ClientOSError):
                retry_wait_time = float(self._min_wait_time_seconds)
                actual_wait_time = retry_wait_time * (1 + random.random())
                await asyncio.sleep(actual_wait_time)
                continue

            # final one
            await self._increment_http_call_count("delete_file_by_fid", i_try + 1)
            raise MdmException(f"Finally failed after {self._max_retries}")

class AsyncMddAccessor():
    def __init__(
        self,
        credential_provider: AadTokenCache,
        environment: Environment,
        use_api_gateway: bool = True,
        apim_key: str = None,
    ):
        """Create MDD accessor with all needed dependencies.

        Args:
            credential_provider (AadTokenCache): AAD Token Cacher instance.
            environment (Environment): Envronment (DEV, QA, PROD) to execute against
            use_api_gateway (bool, optional): Shall Data Delivery API Gateway be used. Defaults to True, i.e. API gateway is used by default.
            apim_key (str, optional): In case your service has an APIM key, use it. Otherwise you fall into the default layer of 10req/s.

        Raises:
            Exception: _description_
        """
        self._credential_provider = credential_provider

        self._apim_key = apim_key

        self._credential_provider_deletion = credential_provider

        if environment == Environment.PROD:
            if use_api_gateway:
                self._base_url = "https://data-delivery-api.ad-alliance.biz/mddump"
            else:
                self._base_url = "https://app-mdtds-pace-westeurope.azurewebsites.net"
            self._aad_audience = "api://sp-pace-mddump-pace/.default"
        elif environment == Environment.QA:
            if use_api_gateway:
                self._base_url = "https://data-delivery-api-qa.ad-alliance.biz/mddump"
            else:
                self._base_url = "https://app-mdtds-paceqa-westeurope.azurewebsites.net"
            self._aad_audience = "api://sp-pace-mddump-paceqa/.default"
        else:
            raise Exception(f"Unknown MDM environment '{environment}")

        self._max_retries = 10
        self._min_wait_time_seconds = 1.0

    def _get_default_headers(self, correlation_id: uuid.UUID, token_provider: AadTokenCache) -> Dict[str, str]:
        bearer_token = token_provider.get_token(self._aad_audience).token
        result = {
            "Authorization": f"Bearer {bearer_token}",
            "'x-client-request-id'": str(correlation_id),
        }
        if self._apim_key is not None:
            result["apikey"] = self._apim_key
        return result
    
    async def delete_namespace_document(
        self, session: aiohttp.ClientSession, request_correlation_id: uuid.UUID, sha: str, namespace_name: str
    ):
        """Delete namespace document

        The namespace document by the file's SHA2 256bit and the namespace name is deleted

        Args:
            session (aiohttp.ClientSession): aiohttp session to use for calls
            request_correlation_id (uuid.UUID): correletion id to use for requests
            sha (str): SHA2-256bit of the file
            namespace_name (str): name of the namespace document

        Raises:
            MdmException: Any non success http status leads to this exception.
        """
        headers = self._get_default_headers(request_correlation_id, token_provider=self._credential_provider)

        for i_try in range(self._max_retries):
            try:
                async with session.delete(f"{self._base_url}/v2/entries/{sha}/namespaces/{namespace_name}", headers=headers) as response:
                    if response.status == 429:
                        retry_wait_time = float(response.headers.get("Retry-After", str(self._min_wait_time_seconds)))
                        actual_wait_time = retry_wait_time * (1 + random.random())
                        await asyncio.sleep(actual_wait_time)
                        raise TooManyRequestsException()

                    if response.status in (200, 201, 401, 403, 404, 422):
                        json_response = await response.json()
                        if response.status < 300:
                            return
                        else:
                            raise MdmException(json_response.get("detail", []))
                    response.raise_for_status()
            except TooManyRequestsException:
                continue

            # final one
            raise MdmException(f"Finally failed after {self._max_retries}")