"""Caching layer for Azure AAD/Entra ID tokens."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
# ============================================================================================================
# C O P Y R I G H T                                                                                          \
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2023 Robert Bosch GmbH and Cariad SE. All rights reserved.                                  \
# ============================================================================================================
import logging
import time
from multiprocessing import Lock
from typing import Dict

from azure.core.credentials import AccessToken
from azure.identity import AzureCliCredential


class AadTokenCache:
    """Caching of tokens created by Azure AAD/Entra Id libraries."""

    def __init__(self, credential_provider: any = AzureCliCredential()):
        """Create a cache around the provided Azure credential provider.

        Args:
            credential_provider (any, optional): An object implementing the Azure credential provider API. Defaults to AzureCliCredential().
        """
        self._minimum_token_lifetime_in_seconds: int = 600
        self._logging: logging.Logger = logging.getLogger("CachedAadTokenRetriever")
        self._jwt_cache: Dict[str, AccessToken] = {}
        self._cache_lock: Lock = Lock()
        self._credential_provider = credential_provider

    def get_token(self, token_audience: str) -> AccessToken:
        """Get jwt for audience.

        Args:
            token_audience (str): the audience the token is acquired for. Usually, this is an app registration url.

        Returns:
            AccessToken: returns the AccessToken object
        """
        if token_audience not in self._jwt_cache:
            return self._fetch_token_from_aad(token_audience)
        else:
            return self._get_token_from_cache(token_audience)

    def _fetch_token_from_aad(self, token_audience: str) -> str:
        credential = self._credential_provider
        access_token = credential.get_token(token_audience)
        self._logging.debug("Retrieved access token from aad: %s", access_token.token)
        self._save_token_to_cache(token_audience, access_token)
        return access_token

    def _save_token_to_cache(self, token_audience: str, access_token: AccessToken) -> None:
        with self._cache_lock:
            self._jwt_cache[token_audience] = access_token

    def _get_token_from_cache(self, token_audience: str) -> str:
        access_token = self._jwt_cache[token_audience]

        if self._auth_token_expired(access_token):
            return self._fetch_token_from_aad(token_audience)
        else:
            return access_token

    def _auth_token_expired(self, access_token: AccessToken) -> bool:
        self._logging.debug("JWT expiration time stamp %s", access_token.expires_on)
        self._logging.debug("Current timestamp         %s", time.time())
        if time.time() + self._minimum_token_lifetime_in_seconds < access_token.expires_on:
            self._logging.debug(
                "OAuth2.0 bearer token valid for at least %i seconds", self._minimum_token_lifetime_in_seconds
            )
            return False

        self._logging.debug(
            "OAuth2.0 bearer token will expire soon, i.e. less than %i seconds", self._minimum_token_lifetime_in_seconds
        )
        return True
