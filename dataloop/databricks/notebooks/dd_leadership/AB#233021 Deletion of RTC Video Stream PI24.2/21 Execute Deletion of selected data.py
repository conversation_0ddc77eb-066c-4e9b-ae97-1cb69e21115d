# Databricks notebook source

# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------


# MAGIC %md
# MAGIC # Execute the deletion of the data selected in previous steps

# COMMAND ----------

import asyncio
import datetime
import logging
import uuid

import aiohttp
import backoff
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_mdm_accessor import AsyncMdmAccessor, MdmException, MdmFileEntryNotFoundException
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

from mdm.lib.mdm_search_module.v2.constants import Environment

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_DELETION_APPID = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--appid"
)
AAD_SP_DELETION_SECRET = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--secret"
)
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential_deletion = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_DELETION_APPID, client_secret=AAD_SP_DELETION_SECRET
)
aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)

aad_token_cache_deletion = AadTokenCache(credential_provider=aad_credential_deletion)
aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# APIM Setup
apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
apim_key = kv_client.get_secret("ddleadershipdatabricks-primary-key")

# MDM Accessor setup
environment = Environment.PROD
mdm_accessor = AsyncMdmAccessor(
    credential_provider=aad_token_cache,
    environment=environment,
    use_api_gateway=True,
    credential_provider_deletion=aad_token_cache_deletion,
    apim_key=apim_key.value,
)

# Databricks Tables
CATALOG_FILES_TO_BE_DELETED = dbutils.widgets.get("catalog_files_to_be_deleted")
SCHEMA_FILES_TO_BE_DELETED = dbutils.widgets.get("schema_files_to_be_deleted")
TABLE_FILES_TO_BE_DELETED = dbutils.widgets.get("tbl_files_to_be_deleted")
FULLPATH_TO_BE_DELETED_TABLE = f"{CATALOG_FILES_TO_BE_DELETED}.{SCHEMA_FILES_TO_BE_DELETED}.{TABLE_FILES_TO_BE_DELETED}"

FULLPATH_ALREADY_DELETED_TABLE = (
    f"{CATALOG_FILES_TO_BE_DELETED}.{SCHEMA_FILES_TO_BE_DELETED}.__bookkeeping_{TABLE_FILES_TO_BE_DELETED}"
)

# COMMAND ----------

# MAGIC %sql
# MAGIC -- CREATE OR REPLACE TABLE
# MAGIC CREATE TABLE IF NOT EXISTS ${catalog_files_to_be_deleted}.${schema_files_to_be_deleted}.__bookkeeping_${tbl_files_to_be_deleted} (
# MAGIC   file_hash STRING
# MAGIC   ,tds_fid STRING
# MAGIC   ,tds_state STRING
# MAGIC   ,deleted_at TIMESTAMP
# MAGIC   ,deletion_process_uuid STRING
# MAGIC );

# COMMAND ----------

# MAGIC %sql
# MAGIC SELECT * FROM ${catalog_files_to_be_deleted}.${schema_files_to_be_deleted}.${tbl_files_to_be_deleted} LIMIT 10

# COMMAND ----------


def get_file_hashes_to_delete():
    df_already_deleted = spark.read.format("delta").table(FULLPATH_ALREADY_DELETED_TABLE)

    df_files_to_be_deleted = (
        spark.read.format("delta")
        .table(FULLPATH_TO_BE_DELETED_TABLE)
        .join(df_already_deleted, on="file_hash", how="left")
        .where(col("deleted_at").isNull())
        .select(col("file_hash"))
        .distinct()
        .collect()
    )

    return [x.asDict()["file_hash"] for x in df_files_to_be_deleted]


# COMMAND ----------

file_deletion_list = get_file_hashes_to_delete()
print(f"Going to delete {len(file_deletion_list)} files")

# COMMAND ----------

deletion_correlation_id = uuid.uuid4()
print(f"All deletions are executed with correlation id {deletion_correlation_id}")

# COMMAND ----------


@backoff.on_exception(
    backoff.expo,
    max_tries=3,
    max_time=30,
    exception=[aiohttp.ClientResponseError, aiohttp.ClientConnectionError, MdmException],
)
async def delete_file(
    session: aiohttp.ClientSession, tds_client: AsyncMdmAccessor, this_sha: str, correlation_id: uuid.UUID
):
    error_msg = ""
    tds_fids = None

    try:
        tds_fids = await tds_client.get_fid_by_sha(session=session, sha=this_sha, request_correlation_id=correlation_id)
        logging.debug(f"[{this_sha}] Maps to TDS file ids: {tds_fids}")
    except MdmFileEntryNotFoundException:
        error_msg = "File not found"
        logging.error(f"[{this_sha}] no file with hash in TDS")
    except MdmException as e:
        error_msg = f"[{this_sha}] Curious error caught file fetching TDS FID: {e}"
        logging.error(error_msg)

    if tds_fids is not None:
        for tds_fid in tds_fids:
            if tds_fid["state"].lower() == "ok":
                try:
                    await tds_client.delete_file_by_fid(
                        session, fid=tds_fid["fid"], request_correlation_id=correlation_id
                    )
                except Exception as e:
                    logging.error(f"[{this_sha}] Caught exception on deletion: {e}")
                    error_msg = str(e)

    return {"sha": this_sha, "fid": tds_fids, "error_msg": error_msg}


# COMMAND ----------


async def gather_dictionary(tasks: dict):
    async def mark(key, coroutine):
        return key, await coroutine

    try:
        loop = asyncio.get_running_loop()
        return {
            key: result
            for key, result in await asyncio.gather(*(mark(key, coroutine) for key, coroutine in tasks.items()))
        }
    except RuntimeError:
        loop = asyncio.new_event_loop()
        return {
            key: result
            for key, result in await asyncio.gather(*(mark(key, coroutine) for key, coroutine in tasks.items()))
        }


# COMMAND ----------


def convert_resultset_to_dataset(resultset):
    rows = []
    for x in resultset.items():
        for fid_entry in x[1].get("fid", []):
            fid = fid_entry.get("fid", "")
            fid_state = fid_entry.get("state", "")
            is_failed = len(fid_entry.get("error_msg", "")) > 0
            if not is_failed:
                rows.append(
                    {
                        "file_hash": x[0],
                        "tds_fid": fid,
                        "tds_state": fid_state,
                        "deleted_at": datetime.datetime.now(),
                        "deletion_process_uuid": str(deletion_correlation_id),
                    }
                )
    return spark.createDataFrame(rows)


# COMMAND ----------

import traceback

slice_size = 10000
aiohttp_connector = aiohttp.TCPConnector(limit=500)
aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)

async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    while True:
        file_deletion_list = get_file_hashes_to_delete()
        if len(file_deletion_list) == 0:
            print("It's done all files are deleted")
            break
        print(f"Going to delete {len(file_deletion_list)} files")

        deletion_state = {}
        try:
            deletion_state = await gather_dictionary(
                {
                    this_sha: delete_file(session, mdm_accessor, this_sha, correlation_id=deletion_correlation_id)
                    for this_sha in file_deletion_list[:slice_size]
                }
            )
        except Exception as e:
            print(f"An exception occured {e}")
            traceback.print_exc()

        # save last deleted entries
        if len(deletion_state) > 0:
            print("Write Results")
            df_result = convert_resultset_to_dataset(deletion_state)
            (
                df_result.write.format("delta")
                .option("mergeSchema", "true")
                .saveAsTable(FULLPATH_ALREADY_DELETED_TABLE, mode="append")
            )
        else:
            print("Deletion states is empty")


# COMMAND ----------

# with open(f"/Volumes/dd_leadership_pub_sbx/ab233021_rtc_deletion/csv_files/result_{deletion_correlation_id}.json", "w", encoding="utf-8") as dst_file:
#     json.dump(deletion_state, dst_file)

# COMMAND ----------

# aiohttp_connector = aiohttp.TCPConnector(limit=20)
# aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=10)
# async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
#     print(await mdm_accessor.get_tds_entry_by_sha(session=session, sha="a7122579c4548a84df6f4d929b3f346885fbbbd85b37dea5f7dc39648f997db4", request_correlation_id=deletion_correlation_id))
#     #print(await mdm_accessor.get_fid_by_sha(session=session, sha="a7122579c4548a84df6f4d929b3f346885fbbbd85b37dea5f7dc39648f997db4", request_correlation_id=deletion_correlation_id))
