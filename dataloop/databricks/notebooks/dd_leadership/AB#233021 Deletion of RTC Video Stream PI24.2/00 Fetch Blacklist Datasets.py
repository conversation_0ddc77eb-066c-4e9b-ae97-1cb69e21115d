# Databricks notebook source

# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

import asyncio
import random
import time

import aiohttp
import backoff
import pandas as pd
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache

from mdm.lib.mdm_search_module.v2.constants import Environment

AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)

aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# APIM Setup
APIM_KV_URL = "https://prdpacegatewaykyv.vault.azure.net/"
APIM_KV_SECRET_NAME = "ddleadershipdatabricks-primary-key"
kv_client = SecretClient(vault_url=APIM_KV_URL, credential=aad_credential)
APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

# MDM Accessor setup
ENVIRONMENT = Environment.PROD

DATASETS_BASE_URL = "https://data-delivery-api.ad-alliance.biz/datasets-service/api/v2"
DATASETS_AAD_AUDIENCE = "api://sp-pace-datasets-api-dll-prod/.default"

DATASETS_TAGS_TO_FETCH = "to_be_retained"


# Databricks Tables
CATALOG_FILES_TO_BE_DELETED = "dd_leadership_pub_sbx"
SCHEMA_FILES_TO_BE_DELETED = "ab233021_rtc_deletion"
TABLE_DATASET_DELETION_BLACKLIST = "dataset_deletion_blacklist"
TABLE_DATASET_DELETION_BLACKLIST_ENTRIES = "dataset_deletion_blacklist_entries"
FULLPATH_DELETION_BLACKLIST = (
    f"{CATALOG_FILES_TO_BE_DELETED}.{SCHEMA_FILES_TO_BE_DELETED}.{TABLE_DATASET_DELETION_BLACKLIST}"
)
FULLPATH_DELETION_BLACKLIST_ENTRIES = (
    f"{CATALOG_FILES_TO_BE_DELETED}.{SCHEMA_FILES_TO_BE_DELETED}.{TABLE_DATASET_DELETION_BLACKLIST_ENTRIES}"
)

# COMMAND ----------

# MAGIC %md
# MAGIC # Fetching datasets via REST API

# COMMAND ----------


class RetryFailedException(Exception):
    pass


# COMMAND ----------


@backoff.on_exception(
    backoff.expo,
    max_tries=10,
    max_time=120,
    exception=[aiohttp.ClientResponseError, aiohttp.ClientConnectionError, RetryFailedException],
)
async def fetch_dataset_entries_async(
    dataset_id, page_number: int, page_size: int, aad_credential: ClientSecretCredential, session: aiohttp.ClientSession
) -> dict:
    try:
        max_retries = 10
        retry_after_seconds_default = 1.0
        bearer_token = aad_credential.get_token(DATASETS_AAD_AUDIENCE).token
        headers = {
            "Authorization": f"Bearer {bearer_token}",
            "Content-Type": "application/json",
            "apikey": APIM_KEY,
        }

        params = {
            "pageNumber": page_number,
            "pageSize": page_size,
        }

        for i_retry in range(max_retries):
            async with session.get(
                f"{DATASETS_BASE_URL}/datasets/{dataset_id}/entries", params=params, headers=headers
            ) as response:
                if response.status == 429:
                    retry_after_seconds = response.headers.get("Retry-After")
                    if retry_after_seconds is None:
                        retry_after_seconds = retry_after_seconds_default
                    else:
                        retry_after_seconds = float(retry_after_seconds)
                    retry_after_seconds += retry_after_seconds * random.random()
                    print(
                        f"[{dataset_id}] WARNING: running into throttling. waiting for {retry_after_seconds} seconds. try {i_retry+1} of {max_retries}"
                    )
                    time.sleep(retry_after_seconds)
                    continue
                response.raise_for_status()
                json_response = await response.json()
                return json_response
    except Exception as e:
        print(f"[{dataset_id}] ERROR: failed with exception {e}")
        time.sleep(retry_after_seconds_default * (1 + random.random()))
        raise RetryFailedException()

    raise RetryFailedException()


# COMMAND ----------


async def fetch_dataset_all_entries_async(
    dataset_id, aad_credential: ClientSecretCredential, session: aiohttp.ClientSession
) -> dict:
    page_number = 1
    page_size = 10000
    result = []
    while True:
        this_page = await fetch_dataset_entries_async(dataset_id, page_number, page_size, aad_credential, session)
        result.extend(this_page["entries"])

        pagination = this_page["pagination"]
        if pagination["totalPages"] > page_number:
            page_number += 1
        else:
            return result


# COMMAND ----------


async def fetch_datasets_async(
    tags, page_number: int, page_size: int, aad_credential: ClientSecretCredential, session: aiohttp.ClientSession
) -> dict:
    bearer_token = aad_credential.get_token(DATASETS_AAD_AUDIENCE).token
    headers = {
        "Authorization": f"Bearer {bearer_token}",
        "Content-Type": "application/json",
        "apikey": APIM_KEY,
    }

    params = {
        "pageNumber": page_number,
        "pageSize": page_size,
        "tags": tags,
    }

    async with session.get(f"{DATASETS_BASE_URL}/datasets", params=params, headers=headers) as response:
        response.raise_for_status()
        json_response = await response.json()
        return json_response


# COMMAND ----------


async def fetch_all_datasets_async(
    tags, aad_credential: ClientSecretCredential, session: aiohttp.ClientSession
) -> dict:
    page_number = 1
    page_size = 100
    result = []
    while True:
        this_page = await fetch_datasets_async(tags, page_number, page_size, aad_credential, session)
        result.extend(this_page["items"])

        pagination = this_page["pagination"]
        if pagination["totalPages"] > page_number:
            page_number += 1
        else:
            return result


# COMMAND ----------

# fetch descriptors of all matching datasets
request_connector = aiohttp.TCPConnector(limit=100)
request_timeouts = aiohttp.ClientTimeout(sock_connect=10, sock_read=10)
async with aiohttp.ClientSession(trust_env=True, timeout=request_timeouts, connector=request_connector) as session:
    dataset_descriptors = await fetch_all_datasets_async(DATASETS_TAGS_TO_FETCH, aad_token_cache, session)

# COMMAND ----------

len(dataset_descriptors)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Fetch datasets without or incomplete entries

# COMMAND ----------


async def create_entries_record(dataset, aad_credential: ClientSecretCredential, session: aiohttp.ClientSession):
    dataset["entries"] = await fetch_dataset_all_entries_async(dataset["id"], aad_credential, session)
    return dataset


# COMMAND ----------


def validate_entries_of_dataset(this_dataset) -> bool:
    if "entries" not in this_dataset:
        print(f"[{this_dataset['id']}][ERROR] Dataset does not contains entries")
        return False

    if len(this_dataset["entries"]) != int(this_dataset["count"]):
        print(
            f"[{this_dataset['id']}][ERROR] Dataset '{this_dataset['id']}' number of entries does not match the count field"
        )
        return False
    return True


# COMMAND ----------


def remove_entries_of_dataset(this_dataset):
    if "entries" in this_dataset:
        del this_dataset["entries"]
    return this_dataset


# COMMAND ----------


def remove_entries_of_all_datasets(list_of_datasets):
    result = []
    for this_dataset in list_of_datasets:
        if not validate_entries_of_dataset(this_dataset):
            this_dataset = remove_entries_of_dataset(this_dataset)
        result.append(this_dataset)
    return result


# COMMAND ----------

dataset_descriptors = sorted(dataset_descriptors, key=lambda x: x["count"])

request_connector = aiohttp.TCPConnector(limit=100)
request_timeouts = aiohttp.ClientTimeout(sock_connect=10, sock_read=30)
async with aiohttp.ClientSession(trust_env=True, timeout=request_timeouts, connector=request_connector) as session:
    tasks = [
        asyncio.ensure_future(create_entries_record(x, aad_token_cache, session))
        for x in remove_entries_of_all_datasets(dataset_descriptors)
        # only fetch data where we dont have entries
        if "entries" not in x
    ]

    all_relevant_datasets = await asyncio.gather(*tasks)

# COMMAND ----------

for this_dataset in dataset_descriptors:
    validate_entries_of_dataset(this_dataset)

# COMMAND ----------

# MAGIC %md
# MAGIC # Write data to databricks table?

# COMMAND ----------

# MAGIC %md
# MAGIC ## Dataset Table

# COMMAND ----------

df = pd.DataFrame(data=dataset_descriptors)
# column renaming
df = df.rename(
    columns={
        "id": "dataset_id",
        "count": "entries_count",
        "createTime": "created_at",
        "updateTime": "modified_at",
    }
)

# extract into dedicated columns
df["author_users"] = df["author"].apply(lambda x: x["users"])
df["author_applications"] = df["author"].apply(lambda x: x["applications"])
df["dataset_name"] = df["metadata"].apply(lambda x: x["dataset_name"])

# data type conversions
df["dataset_id"] = df["dataset_id"].astype("str")
df["catalog"] = df["catalog"].astype("str")
df["tags"] = df["tags"].astype("str")
df["metadata"] = df["metadata"].astype("str")
df["description"] = df["description"].astype("str")
df["author_users"] = df["author_users"].astype("str")
df["author_applications"] = df["author_applications"].astype("str")
df["created_at"] = pd.to_datetime(df["created_at"])
df["modified_at"] = pd.to_datetime(df["modified_at"])

# remove index
df = df.reset_index(drop=True)

# drop unnecessary column
df = df.drop(columns=["major_version", "minor_version", "type", "author", "entries", "metadata"])

# COMMAND ----------

(
    spark.createDataFrame(df)
    .write.format("delta")
    .option("mergeSchema", "true")
    .saveAsTable(FULLPATH_DELETION_BLACKLIST, mode="overwrite")
)

# COMMAND ----------

# MAGIC %md
# MAGIC ## Dataset Entries Table

# COMMAND ----------

df_entries = pd.DataFrame(
    data=[
        {"dataset_id": dataset["id"], "entry": entry} for dataset in dataset_descriptors for entry in dataset["entries"]
    ]
)

# COMMAND ----------

(
    spark.createDataFrame(df_entries)
    .write.format("delta")
    .option("mergeSchema", "true")
    .saveAsTable(FULLPATH_DELETION_BLACKLIST_ENTRIES, mode="overwrite")
)

# COMMAND ----------
