-- Databricks notebook source
-- ===================================================================================
--  C O P Y R I G H T
-- -----------------------------------------------------------------------------------
--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- ===================================================================================

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Collect all drives and linked data

-- COMMAND ----------

USE CATALOG ${target_catalog};
USE SCHEMA ${target_schema};

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Recording level

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_0
  AS
  SELECT
    entries.file_hash         AS recording_hash
    ,entries.created_at       AS recording_created_at
    ,entries.tds_pool         AS recording_tds_pool
    -- entries.file_hash         AS split_hash
    -- ,entries.created_at       AS split_created_at
    ,entries.file_hash        AS file_hash
    ,entries.file_name        AS file_name
    ,entries.tds_pool         AS file_tds_pool
    ,entries.created_at       AS file_created_at
    ,entries.file_size_bytes  AS file_size_bytes
    ,entries.content_type     AS file_content_type
  FROM silver.tds.file_entries AS entries
  WHERE
    entries.file_name = 'gmdm.json'
    --AND entries.tds_pool = 'g3vprdaq'
;
SELECT * FROM tmp_recording_level_0 LIMIT 10;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Split level

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_1
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,child_entries.file_hash        AS split_hash
    ,child_entries.created_at       AS split_created_at
    ,child_entries.tds_pool         AS split_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
  FROM tmp_recording_level_0 AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM tmp_recording_level_1 LIMIT 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Stream level

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_2
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,parents.split_hash             AS split_hash
    ,parents.split_created_at       AS split_created_at
    ,parents.split_tds_pool         AS split_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
  FROM tmp_recording_level_1 AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM tmp_recording_level_2 LIMIT 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Sub stream levels

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_3
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,parents.split_hash             AS split_hash
    ,parents.split_created_at       AS split_created_at
    ,parents.split_tds_pool         AS split_tds_pool
    ,parents.file_hash              AS stream_hash
    ,parents.file_created_At        AS stream_created_at
    ,parents.file_tds_pool          AS stream_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
  FROM tmp_recording_level_2 AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM tmp_recording_level_3 LIMIT 10

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_4
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,parents.split_hash             AS split_hash
    ,parents.split_created_at       AS split_created_at
    ,parents.split_tds_pool         AS split_tds_pool
    ,parents.stream_hash            AS stream_hash
    ,parents.stream_created_at      AS stream_created_at
    ,parents.stream_tds_pool        AS stream_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
  FROM tmp_recording_level_3 AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM tmp_recording_level_4 LIMIT 10

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_5
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,parents.split_hash             AS split_hash
    ,parents.split_created_at       AS split_created_at
    ,parents.split_tds_pool         AS split_tds_pool
    ,parents.stream_hash            AS stream_hash
    ,parents.stream_created_at      AS stream_created_at
    ,parents.stream_tds_pool        AS stream_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
  FROM tmp_recording_level_4 AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM tmp_recording_level_5 LIMIT 10

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_6
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,parents.split_hash             AS split_hash
    ,parents.split_created_at       AS split_created_at
    ,parents.split_tds_pool         AS split_tds_pool
    ,parents.stream_hash            AS stream_hash
    ,parents.stream_created_at      AS stream_created_at
    ,parents.stream_tds_pool        AS stream_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
  FROM tmp_recording_level_5 AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM tmp_recording_level_6 LIMIT 10

-- COMMAND ----------

CREATE OR REPLACE TABLE tmp_recording_level_7
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,parents.split_hash             AS split_hash
    ,parents.split_created_at       AS split_created_at
    ,parents.split_tds_pool         AS split_tds_pool
    ,parents.stream_hash            AS stream_hash
    ,parents.stream_created_at      AS stream_created_at
    ,parents.stream_tds_pool        AS stream_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
  FROM tmp_recording_level_6 AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM tmp_recording_level_7 LIMIT 10;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Build single big table

-- COMMAND ----------

CREATE OR REPLACE TABLE all_recordings
  AS
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,NULL                   AS split_hash
    ,NULL                   AS split_created_at
    ,NULL                   AS split_tds_pool
    ,NULL                   AS stream_hash
    ,NULL                   AS stream_created_at
    ,NULL                   AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type    
  FROM tmp_recording_level_0 AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,NULL                   AS stream_hash
    ,NULL                   AS stream_created_at
    ,NULL                   AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type   
  FROM tmp_recording_level_1 AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,file_hash              AS stream_hash
    ,file_created_at        AS stream_created_at
    ,file_tds_pool          AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type   
  FROM tmp_recording_level_2 AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,stream_hash            AS stream_hash
    ,stream_created_at      AS stream_created_at
    ,stream_tds_pool        AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type   
  FROM tmp_recording_level_3 AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,stream_hash            AS stream_hash
    ,stream_created_at      AS stream_created_at
    ,stream_tds_pool        AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type   
  FROM tmp_recording_level_4 AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,stream_hash            AS stream_hash
    ,stream_created_at      AS stream_created_at
    ,stream_tds_pool        AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type   
  FROM tmp_recording_level_5 AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,stream_hash            AS stream_hash
    ,stream_created_at      AS stream_created_at
    ,stream_tds_pool        AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type   
  FROM tmp_recording_level_6 AS x
  UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,stream_hash            AS stream_hash
    ,stream_created_at      AS stream_created_at
    ,stream_tds_pool        AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type   
  FROM tmp_recording_level_7 AS x
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Clean-up

-- COMMAND ----------

DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_0;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_1;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_2;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_3;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_4;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_5;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_6;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_recording_level_7;

-- COMMAND ----------

OPTIMIZE ${target_catalog}.${target_schema}.all_recordings;

-- COMMAND ----------

VACUUM ${target_catalog}.${target_schema}.all_recordings

-- COMMAND ----------

ANALYZE TABLE ${target_catalog}.${target_schema}.all_recordings COMPUTE STATISTICS FOR ALL COLUMNS;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Verify the overall data

-- COMMAND ----------

USE CATALOG ${target_catalog};
USE SCHEMA ${target_schema};

-- COMMAND ----------

SELECT * FROM all_recordings LIMIT 10;

-- COMMAND ----------

SELECT 
  COUNT(DISTINCT recording_hash) AS recording_count
  ,COUNT(DISTINCT split_hash) AS split_count
  ,COUNT(DISTINCT file_hash) AS file_count
  ,CAST(SUM(file_size_bytes)/1024/1024/1024/1024 AS BIGINT) AS file_size_tbytes
FROM all_recordings;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Enrich overall data

-- COMMAND ----------

USE CATALOG ${target_catalog};
USE SCHEMA ${target_schema};

CREATE OR REPLACE TABLE all_recordings_with_enrichments AS
  WITH 
  -- Add Stream Names
  STREAM_FILES_WITH_STREAMNAMES AS 
    (
      SELECT
        split_hash
        ,stream_hash
        ,trim(upper(regexp_extract(file_name, '(\\d{6})\\_(\\d{6})\\_([\\w\\_]+)\\.(avi|bin|recbin)$', 3))) AS stream_name
      FROM all_recordings
      WHERE
        ENDSWITH(file_name, '.avi')
        OR ENDSWITH(file_name, '.bin')
        OR ENDSWITH(file_name, '.recbin')
    ),
    STREAMS_PER_SPLIT AS (
      SELECT
        split_hash
        ,collect_list(DISTINCT stream_name) AS streams
      FROM STREAM_FILES_WITH_STREAMNAMES
      WHERE
        stream_name IS NOT NULL
        AND stream_name <> ""
      GROUP BY split_hash
    ),
    -- enrich with recording start time
    RECORDING_INFO AS (
      SELECT
        file_hash                     AS recording_hash
        ,json_document:start_time_utc AS recording_start_time_utc
      FROM unicorn_dev.mdm_flattened.namespace_latest
      WHERE
        namespace_name='datamanagement-recording-info'
    ),
    -- enrich with split start time
    SPLIT_INFO AS (
      SELECT
        file_hash                     AS split_hash
        ,json_document:start_time_utc AS split_start_time_utc
      FROM unicorn_dev.mdm_flattened.namespace_latest
      WHERE
        namespace_name='datamanagement-split-info'
    ),    
    -- enrich with gt_flag
    IS_GT_FILE AS (
      SELECT
        split_hash
        ,CASE
          WHEN
            endswith(file_name, '_gt_trajectory_all.txt')
            OR endswith(file_name, '_gt_trajectory.txt')
          THEN
            1
          ELSE
            0
        END AS includes_gt_trajectory
      FROM all_recordings
    ),
    SPLITS_WITH_WITH_GT_FILE AS (
      SELECT
        split_hash
        ,CASE
          WHEN 
            SUM(includes_gt_trajectory) > 0
          THEN 
            true
          ELSE 
            false
        END AS gt_trajectory_files_in_split
      FROM IS_GT_FILE
      GROUP BY split_hash
    )
    --------------------------------------------------
    -- Bring everything together
    SELECT
      all_files.*
      ,single_stream.stream_name                        AS stream_name
      ,split_level_streamlist.streams                   AS streams_in_split
      ,COALESCE(gt.gt_trajectory_files_in_split, false) AS gt_trajectory_files_in_split
      ,recording_info.recording_start_time_utc          AS recording_start_time_utc
      ,split_info.split_start_time_utc                  AS split_start_time_utc
    FROM all_recordings AS all_files
    -- join recording information
    LEFT JOIN RECORDING_INFO AS recording_info
      ON
        recording_info.recording_hash = all_files.recording_hash
    -- join split infformation
    LEFT JOIN SPLIT_INFO AS split_info
      ON
        split_info.split_hash = all_files.split_hash
    -- join stream information
    LEFT JOIN STREAMS_PER_SPLIT AS split_level_streamlist
      ON
        split_level_streamlist.split_hash=all_files.split_hash
    LEFT JOIN STREAM_FILES_WITH_STREAMNAMES AS single_stream
      ON
        single_stream.stream_hash=all_files.stream_hash
    -- join ground_truth info
    LEFT JOIN SPLITS_WITH_WITH_GT_FILE AS gt
      ON
        gt.split_hash=all_files.file_hash
  ;

-- COMMAND ----------

OPTIMIZE ${target_catalog}.${target_schema}.all_recordings_with_enrichments;

-- COMMAND ----------

VACUUM ${target_catalog}.${target_schema}.all_recordings_with_enrichments;

-- COMMAND ----------

ANALYZE TABLE ${target_catalog}.${target_schema}.all_recordings_with_enrichments COMPUTE STATISTICS FOR COLUMNS
  recording_hash
  ,recording_created_at
  ,recording_tds_pool
  ,split_hash
  ,split_created_at
  ,split_tds_pool
  ,stream_hash
  ,stream_created_at
  ,stream_Tds_pool
  ,file_hash
  ,file_name
  ,file_created_at
  ,file_tds_pool
  ,file_size_bytes
  ,file_content_type
  ,stream_name
  ,recording_start_time_utc
  ,split_start_time_utc

-- COMMAND ----------

USE CATALOG ${target_catalog};
USE SCHEMA ${target_schema};

SELECT
  'all_recordings' AS `Table`
  ,COUNT(*)
  ,COUNT(DISTINCT file_hash)
  ,SUM(file_size_bytes)
FROM all_recordings
UNION
SELECT
  'all_recordings_with_enrichments' AS `Table`
  ,COUNT(*)
  ,COUNT(DISTINCT file_hash)
  ,SUM(file_size_bytes)
FROM all_recordings_with_enrichments;

-- SELECT
--   'all_recordings' AS `Table`
--   ,COUNT(DISTINCT recording_hash)
--   ,COUNT(DISTINCT recording_created_at)
--   ,COUNT(DISTINCT split_hash)
--   ,COUNT(DISTINCT split_created_at)
--   ,COUNT(DISTINCT stream_hash)
--   ,COUNT(DISTINCT stream_created_at)
--   ,COUNT(DISTINCT file_hash)
--   ,COUNT(DISTINCT file_name)
--   ,COUNT(DISTINCT file_created_at)
--   ,SUM(file_size_bytes)
--   ,COUNT(DISTINCT file_content_type)
-- FROM all_recordings
-- UNION
-- SELECT
--   'all_recordings_with_enrichments' AS `Table`
--   ,COUNT(DISTINCT recording_hash)
--   ,COUNT(DISTINCT recording_created_at)
--   ,COUNT(DISTINCT split_hash)
--   ,COUNT(DISTINCT split_created_at)
--   ,COUNT(DISTINCT stream_hash)
--   ,COUNT(DISTINCT stream_created_at)
--   ,COUNT(DISTINCT file_hash)
--   ,COUNT(DISTINCT file_name)
--   ,COUNT(DISTINCT file_created_at)
--   ,SUM(file_size_bytes)
--   ,COUNT(DISTINCT file_content_type)
-- FROM all_recordings_with_enrichments

-- COMMAND ----------

SELECT * FROM all_recordings_with_enrichments
--WHERE gt_trajectory_files_in_split=true
LIMIT 10

-- COMMAND ----------

SELECT * FROM all_recordings_with_enrichments


-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Starting analysis

-- COMMAND ----------

USE CATALOG ${target_catalog};
USE SCHEMA ${target_schema};

-- COMMAND ----------

SELECT
  YEAR(split_created_at) AS year
  ,MONTH(split_created_at) AS month
  ,COUNT(distinct split_hash) AS number_splits
  ,COUNT(distinct split_hash)*30/3600 AS number_recorded_hours_upper_limit
  ,MAX(streams_in_split) AS streams_in_split
FROM all_recordings_with_enrichments
WHERE
  split_hash IS NOT NULL
GROUP BY
  YEAR(split_created_at)
  ,MONTH(split_created_at)
LIMIT 100

-- COMMAND ----------


