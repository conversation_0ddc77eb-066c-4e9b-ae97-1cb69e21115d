# Databricks notebook source

# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

from pyspark.sql.functions import col, from_json, to_timestamp_ntz
from pyspark.sql.types import ArrayType, StringType

# COMMAND ----------

# MAGIC %md
# MAGIC # Transfer dataset base information to databricks unity catalog

# COMMAND ----------

df_datasets = (
    spark.read.format("json")
    # .options(header='true', inferSchema='true')
    .load("/Volumes/unicorn_dev/tfm4fe_test/csv_files/fetch_all_datasets_not_to_delete_dataset.json")
    .withColumn("modified_at", to_timestamp_ntz(col("modified_at")))
    .withColumn("created_at", to_timestamp_ntz(col("created_at")))
    .withColumn("tags", from_json(col("tags"), schema=ArrayType(StringType())))
    .withColumn("author_applications", from_json(col("author_applications"), schema=ArrayType(StringType())))
    .withColumn("author_users", from_json(col("author_users"), schema=ArrayType(StringType())))
    .select(
        col("dataset_id"),
        col("created_at"),
        col("modified_at"),
        col("state"),
        col("version"),
        col("entries_count"),
        col("tags"),
        col("dataset_name"),
        col("description"),
        col("checksum"),
        col("catalog"),
        col("author_users"),
        col("author_applications"),
    )
)

# COMMAND ----------

display(df_datasets.limit(10))

# COMMAND ----------

spark.catalog.setCurrentCatalog("dd_leadership_pub_sbx")
spark.catalog.setCurrentDatabase("ab233021_rtc_deletion")
df_datasets.write.format("delta").option("mergeSchema", "true").saveAsTable(
    "dataset_deletion_blacklist", mode="overwrite"
)

# COMMAND ----------

# MAGIC %sql
# MAGIC OPTIMIZE dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist;
# MAGIC VACUUM dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist;

# COMMAND ----------

# MAGIC %md
# MAGIC # Transfer entries of datasets to databricks unity catalog

# COMMAND ----------

df_dataset_entries = (
    spark.read.format("json")
    # .options(header='true', inferSchema='true')
    .load(
        "/Volumes/dd_leadership_pub_sbx/ab233021_rtc_deletion/csv_files/fetch_all_datasets_not_to_delete_entries.json"
    )
)
display(df_dataset_entries.limit(10))

# COMMAND ----------

spark.catalog.setCurrentCatalog("dd_leadership_pub_sbx")
spark.catalog.setCurrentDatabase("ab233021_rtc_deletion")
df_dataset_entries.write.format("delta").option("mergeSchema", "true").saveAsTable(
    "dataset_deletion_blacklist_entries", mode="overwrite"
)

# COMMAND ----------

# MAGIC %sql
# MAGIC OPTIMIZE dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist_entries;
# MAGIC VACUUM dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist_entries;

# COMMAND ----------
