-- Databricks notebook source
-- ===================================================================================
--  C O P Y R I G H T
-- -----------------------------------------------------------------------------------
--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- ===================================================================================

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Prologue
-- MAGIC
-- MAGIC As part of cost down measures, certain camera streams are considered as no longer relevant. Therefore, this data is to be deleted.
-- MAGIC
-- MAGIC This notebook covers:
-- MAGIC 1. What is the amount of data in total for the given set of sensors
-- MAGIC 2. Apply filtering to only remove the data that is agreed on to be deleted.
-- MAGIC
-- MAGIC What data is considered for deletion?
-- MAGIC - Data on TDS pool ``g3vprdaq``
-- MAGIC - Only data uploaded before 2024/03/01
-- MAGIC - Only stream of **r**ear **t**ele **c**amera (RTC) and their children
-- MAGIC
-- MAGIC What data is excluded from deletion?
-- MAGIC - Datasets with the tag ``to_be_retained`` are excluded from the deletion included all children
-- MAGIC
-- MAGIC #### Literature
-- MAGIC - [Confluence Documentation for data stream deletion](https://pace-project.atlassian.net/wiki/spaces/CROSSD/pages/636781808/Decision+233021+-+Delete+data+of+unsued+camera+streams+recorded+by+data+acquisition+fleet)
-- MAGIC - [**Decision 233021**: Delete data of unsued camera streams recorded by data acquisition fleet](https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/233021)

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # How much data is stored per stream on g3vprdaq pool?
-- MAGIC
-- MAGIC ## Overall recorded data

-- COMMAND ----------

-- DBTITLE 1,All time data recorded per stream
SELECT
  stream_name
  ,count(distinct file_hash) AS number_of_files
  ,round(sum(file_size_bytes)/1024/1024/1024/1024,2) AS file_size_terabytes
FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments
WHERE
  recording_tds_pool = 'g3vprdaq'
GROUP BY stream_name
HAVING
  stream_name IS NOT NULL
  AND COALESCE(stream_name, '') <> ''

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Check if some of the files have a missing recording date.

-- COMMAND ----------

SELECT
  count(distinct file_hash) AS number_of_files
  ,round(sum(file_size_bytes)/1024/1024/1024/1024/1024,1) AS file_size_petabytes
FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments
WHERE
  ((recording_created_at is NULL) OR (recording_created_at = ''))
  AND recording_tds_pool = 'g3vprdaq'

-- COMMAND ----------

-- MAGIC %md
-- MAGIC We are talking in total of 2.3 PB recorded by RTC recorded until now.
-- MAGIC
-- MAGIC ## Data recorded before 2024/03/01

-- COMMAND ----------

-- DBTITLE 1,Data recorded before 2024/03/01
SELECT
  stream_name
  ,count(distinct file_hash) AS number_of_files
  ,round(sum(file_size_bytes)/1024/1024/1024/1024/1024,1) AS file_size_petabytes
FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments
WHERE
  recording_created_at < '2024-03-01'
  AND recording_tds_pool = 'g3vprdaq'
GROUP BY stream_name
HAVING
  stream_name IS NOT NULL
  AND COALESCE(stream_name, '') <> ''

-- COMMAND ----------

-- MAGIC %md
-- MAGIC Before 2023/03/01, the RTC recorded 1.6 PB.

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Analyse the deletion exclusion list

-- COMMAND ----------

SELECT 
  state
  ,sum(entries_count)
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist
WHERE
  array_contains(tags, 'to_be_retained')
GROUP BY state

-- COMMAND ----------

-- DBTITLE 1,Users created datasets in state archived
;WITH CTE AS 
(
  SELECT 
    explode(author_users) AS author_user
    ,*
  FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist
  WHERE
    array_contains(tags, 'to_be_retained')
    and state='archived'
)
SELECT
  author_user:name AS user_name
  ,author_user:object_id AS user_object_id
  ,count(distinct dataset_id)
FROM CTE
GROUP BY
  user_name
  ,user_object_id

-- COMMAND ----------

;WITH CTE AS (
  SELECT 
    author_users[0]:object_id AS user_object_id
    ,author_users[0]:name AS user_name
    ,*
    --476f2e55-a7a7-416d-aacd-8993a5614503
  FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist
)
SELECT 
  state
  ,user_name
  ,sum(entries_count) AS number_of_entries
FROM CTE
WHERE
  array_contains(tags, 'to_be_retained')
  -- and state='archived'
GROUP BY
  state
  ,user_name


-- COMMAND ----------

-- MAGIC %md
-- MAGIC On 2024/04/23, I requested from users creating the datasets in state ``archived`` what it means. Until an answer, we consider this data to be meant excluded from deletion.

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Exclude splits with any overlap with the datasets from deletion table

-- COMMAND ----------

-- MAGIC %md
-- MAGIC As noted in [Decision 233021: Delete data of unsued camera streams recorded by data acquisition fleet](https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/233021):
-- MAGIC - the archived datasets of _Hsieh, Sin-Yu (SH-T)_ (object id ``476f2e55-a7a7-416d-aacd-8993a5614503``) must **not** be considered as the creator considers them as deleted.
-- MAGIC - the archived datasets of _Backfisch Niels_ (object id ``bd723024-6714-4306-a277-c749e0aef396``) must be considered

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW deletion_exclusion AS
WITH datasets_including_autor AS (
    SELECT
      author_users[0]:object_id AS user_object_id
      ,author_users[0]:name     AS user_name
      ,*
    FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist
  )
  SELECT
    dataset.dataset_id        AS dataset_id
    ,dataset.dataset_name     AS dataset_name
    ,dataset_entries.entry    AS file_hash
  FROM datasets_including_autor AS dataset
  INNER JOIN dd_leadership_pub_sbx.ab233021_rtc_deletion.dataset_deletion_blacklist_entries AS dataset_entries
    ON
      dataset_entries.dataset_id=dataset.dataset_id
  WHERE
    array_contains(dataset.tags, 'to_be_retained')
    -- exclude archived datasets from Hsieh, Sin-Yu (SH-T)
    AND NOT (
      dataset.user_object_id = '476f2e55-a7a7-416d-aacd-8993a5614503'
      AND dataset.state = 'archived'
    )
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Analyze what the users put on no-deletion list

-- COMMAND ----------

-- get a view on what is put into the no delete datasets
CREATE OR REPLACE TABLE dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list AS
  SELECT
    exclusions.dataset_id AS excluded_by_dataset_id
    ,exclusions.dataset_name AS excluded_by_dataset_name
    ,exclusions.file_hash as excluded_file_hash
    ,files.*
  FROM deletion_exclusion AS exclusions
  LEFT JOIN silver.tds.file_entries AS files
    ON
      exclusions.file_hash=files.file_hash
  ;

-- COMMAND ----------

SELECT
  (SELECT COUNT(distinct file_hash) FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list) AS files_found_in_tds_tables
  ,(SELECT COUNT(distinct file_hash) FROM deletion_exclusion) AS deletion_exclusion_file_count
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC Same number of files in both datasets.

-- COMMAND ----------

SELECT
  *
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list
LIMIT 10

-- COMMAND ----------

SELECT
  file_extension, count(distinct file_hash)
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list
GROUP by file_extension
limit 100

-- COMMAND ----------

SELECT
  (
     SELECT
      count(DISTINCT files.file_hash)
    FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS files
    INNER JOIN dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list as exclusion_list
    ON
      exclusion_list.file_hash=files.file_hash
    WHERE
      array_size(exclusion_list.parents) > 0 AND
      exclusion_list.tds_pool not in ('pacetestpace')
  ) AS join_test,
  (
    SELECT count(DISTINCT file_hash) FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list as exclusion_list
    WHERE
      array_size(exclusion_list.parents) > 0 AND
      exclusion_list.tds_pool not in ('pacetestpace')
  )  AS ref_value,
  (join_test - ref_value) as diff_value,
  (diff_value < -10) as passed 
  -- a difference of 8 is allowed due to broken parent child relations


-- COMMAND ----------

/** That's a very long running query **/
-- WITH CTE AS (
--   SELECT *
--     FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS files
--     INNER JOIN dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list as exclusion_list
--       ON exclusion_list.file_hash=files.file_hash
-- )
-- SELECT
--   *
--   FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list
--   LEFT JOIN CTE using (file_hash)
--   WHERE 
--     CTE.file_hash IS NULL
--     AND array_size(ab233021_files_on_no_deletion_list.parents) > 0
--     AND ab233021_files_on_no_deletion_list.tds_pool not in ('pacetestpace')
-- LIMIT 100

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Create list of all splits that must be retained aka deletion black list

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ### List of full recordings on blacklist with their linked splits

-- COMMAND ----------

-- create list of full recordings not to be deleted aka a recording blacklist
CREATE OR REPLACE TABLE dd_leadership_pub_sbx.ab233021_rtc_deletion.recordings_not_to_delete AS 
    SELECT
      recording_hash
      ,split_hash
      -- ,collect_list(split_tds_pool)        AS split_tds_pools
      ,collect_list(deletion_exclusion.file_hash) AS file_hashes
      ,collect_list(dataset_id)   AS dataset_ids
      ,collect_list(dataset_name) AS dataset_names
    FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS files
    INNER JOIN deletion_exclusion
      ON
        deletion_exclusion.file_hash=files.recording_hash
    WHERE
      -- exclude the gmdm.json itself
      deletion_exclusion.file_hash<>files.file_hash
    GROUP BY
      recording_hash
      ,split_hash
;

-- COMMAND ----------

SELECT
  COUNT(distinct split_hash) AS splits_count
  ,COUNT(distinct recording_hash) AS recording_count
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.recordings_not_to_delete

-- COMMAND ----------

-- ,X AS (
-- SELECT
--   recording_hash
--   ,explode(file_hashes) AS file_hash
-- FROM CTE WHERE split_hash IS NULL
-- )
-- SELECT
--   X.recording_hash
--   ,entries.*
-- FROM X
-- JOIN silver.tds.file_entries AS entries
-- ON
--   entries.file_hash = X.file_hash
SELECT
  *
  -- COUNT(DISTINCT split_hash)
  -- ,count(distinct recording_hash)
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.recordings_not_to_delete
LIMIT 10


-- COMMAND ----------

-- MAGIC %md
-- MAGIC ### Create list of all splits that must be retained including those from full recordings table

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.ab233021_rtc_deletion.prelim_splits_not_to_delete AS 
    SELECT
      recording_hash
      ,split_hash
      -- ,max(split_tds_pool)        AS split_tds_pool
      ,collect_list(deletion_exclusion.file_hash) AS file_hashes
      ,collect_list(dataset_id)   AS dataset_ids
      ,collect_list(dataset_name) AS dataset_names
    FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS files
    INNER JOIN deletion_exclusion
      ON
        deletion_exclusion.file_hash=files.file_hash
    WHERE
      -- exclude recording level gmdm.json
      files.file_hash <> files.recording_hash
    GROUP BY
      recording_hash
      ,split_hash
;

-- COMMAND ----------

SELECT
  COUNT(distinct split_hash) AS splits_count
  ,COUNT(distinct recording_hash) AS recording_count
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.prelim_splits_not_to_delete

-- COMMAND ----------

SELECT
  *
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.prelim_splits_not_to_delete
WHERE split_hash IS NULL
LIMIT 100

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ### Union of single splits and full recordings not to delete

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete AS
  WITH CTE AS (
    -- the up-aggregated splits
    SELECT
      recording_hash
      ,split_hash
      ,file_hashes
      ,dataset_ids
      ,dataset_names
      ,'SPLIT' AS source
    FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.prelim_splits_not_to_delete
    UNION
    -- search for files that reference a full recording
    SELECT
      recording_hash
      ,split_hash
      ,file_hashes
      ,dataset_ids
      ,dataset_names
      ,'RECORDING' AS source
    FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.recordings_not_to_delete
  )
  SELECT
    split_hash
    ,collect_list(source) AS sources
    ,collect_list(file_hashes) AS file_hashes
    ,collect_list(dataset_ids)   AS dataset_ids
    ,collect_list(dataset_names) AS dataset_name
  FROM CTE
  GROUP BY
    split_hash
  ;


-- COMMAND ----------

SELECT * FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete LIMIT 10

-- COMMAND ----------

SELECT * FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete
WHERE
  array_size(sources) > 1
LIMIT 10

-- COMMAND ----------

SELECT
  count(distinct split_hash) AS splits_excluded_from_deletion
  ,(SELECT count(distinct file_hash) FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list WHERE file_extension='scene') AS scene_files_included_in_deletion_exclusion_tbl
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete

-- COMMAND ----------

;WITH CTE AS (
  SELECT
  *
  FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.files_on_no_deletion_list AS xxx
  LEFT JOIN dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete AS excluded_splits
    ON
      excluded_splits.split_hash=xxx.file_hash
  WHERE 
    xxx.file_extension='scene'
    AND excluded_splits.split_hash IS NULL
)
--SELECT COUNT(DISTINCT file_hash) FROM CTE
SELECT * FROM CTE
WHERE array_size(parents) > 0
LIMIT 10000

-- COMMAND ----------

SELECT
  ROUND(SUM(file_size_bytes)/1024/1024/1024/1024, 1) AS file_size_terabyte
FROM silver.tds.file_entries
WHERE
  file_name LIKE '%_RTC%'
  AND array_size(parents) = 0
limit 10

-- COMMAND ----------

SELECT 
  array_size(file_hashes)
  ,*
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete
WHERE split_hash IS NULL
LIMIT 100

-- COMMAND ----------

WITH CTE AS (
  SELECT 
    explode(file_hashes) AS file_hashes
  FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete
  WHERE split_hash IS NULL
),
CTE2 AS (
  SELECT
    explode(file_hashes) AS file_hash
  FROM CTE
)
SELECT
  entries.*
FROM CTE2
LEFT JOIN silver.tds.file_entries AS entries
  ON
    entries.file_hash=CTE2.file_hash
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC #### Number of splits excluded from deletion overall

-- COMMAND ----------

SELECT
  count(distinct split_hash) AS number_of_splits_excluded_from_deletion
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete

-- COMMAND ----------

-- MAGIC %md
-- MAGIC #### Terabytes excluded from deletion overall not limited to RTC

-- COMMAND ----------

SELECT
  'Total Data excluded from deletion' AS `What?`
  ,ROUND(SUM(entries.file_size_bytes)/1024/1024/1024/1024, 1) AS file_size_terabytes
FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS entries
INNER JOIN dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete AS split_exclusion
  ON
    entries.split_hash=split_exclusion.split_hash
WHERE
  entries.recording_created_at < '2024-03-01'
  AND entries.recording_tds_pool = 'g3vprdaq'

-- COMMAND ----------

-- MAGIC %md
-- MAGIC #### RTC Data excluded from deletion

-- COMMAND ----------

SELECT
  'RTC Data excluded from deletion by exclusion' AS `What?`
  ,ROUND(SUM(entries.file_size_bytes)/1024/1024/1024/1024, 1) AS file_size_terabytes
FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS entries
INNER JOIN dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete AS split_exclusion
  ON
    entries.split_hash=split_exclusion.split_hash
WHERE
  entries.recording_created_at < '2024-03-01'
  AND entries.recording_tds_pool = 'g3vprdaq'
  AND entries.stream_name = 'RTC'

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Calculate list of files to be deleted
-- MAGIC ## Get all RTC streams matching the overall deletion criteria without applying deletion blacklist

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files AS 
  SELECT
    entries.recording_hash
    ,entries.recording_start_time_utc
    ,entries.split_hash
    ,entries.split_start_time_utc
    ,entries.stream_name
    ,entries.stream_hash
    ,entries.file_hash
    ,entries.file_name
    ,entries.file_created_at
    ,regexp_extract(entries.file_name, '\\.(\\w+)$', 1) AS file_extension
    ,entries.file_size_bytes
  FROM dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS entries
  WHERE
    entries.recording_created_at < '2024-03-01'
    AND entries.recording_tds_pool = 'g3vprdaq'
    AND entries.stream_name = 'RTC'
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## What files are hanging in RTC stream?

-- COMMAND ----------

SELECT
  count(distinct file_hash) AS files_to_delete
  ,round(sum(file_size_bytes)/1024/1024/1024/1024, 0) AS volume_to_delete_terabyte
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files

-- COMMAND ----------

SELECT
  file_extension
  ,min(recording_start_time_utc) AS oldest_recording_date
  ,max(recording_start_time_utc) AS youngest_recording_date
  ,min(file_created_at) AS oldest_file
  ,max(file_created_at) AS youngest_file
  ,count(distinct file_hash) AS file_count
  ,round(sum(file_size_bytes)/1024/1024/1024, 0) AS total_file_size_gigabyte
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files
group by file_extension

-- COMMAND ----------

-- MAGIC %md
-- MAGIC **TODO**: Check if any elements sent to labeling.

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Exclude splits from blacklist

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_exclusion_datasets AS
--without_excluded_splits AS 
  SELECT
    all_files.*
    ,CASE WHEN array_size(exclusion_list.dataset_ids) > 0 THEN 1 ELSE 0 END AS is_in_exclusion_dataset
    ,exclusion_list.dataset_ids
    ,exclusion_list.dataset_name
  FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files  AS all_files
  LEFT JOIN dd_leadership_pub_sbx.ab233021_rtc_deletion.splits_not_to_delete AS exclusion_list
    ON
      all_files.split_hash=exclusion_list.split_hash
;

-- COMMAND ----------

SELECT * FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_exclusion_datasets limit 10

-- COMMAND ----------

SELECT
  is_in_exclusion_dataset
  ,COUNT(DISTINCT file_hash)                          AS file_count
  ,round(sum(file_size_bytes)/1024/1024/1024/1024, 0) AS total_file_size_terabyte
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_exclusion_datasets
GROUP BY
  is_in_exclusion_dataset

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Create final list of files to delete

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits AS 
  SELECT
    *
  FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_exclusion_datasets
  WHERE
    is_in_exclusion_dataset = 0

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Check if a file from the blacklist is accidentially on the data to delete list

-- COMMAND ----------

-- -- TODO: Join with blacklist to check if something from blacklist is on the deletion list
SELECT
*
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits AS data_to_delete
INNER JOIN deletion_exclusion 
  ON
    data_to_delete.file_hash=deletion_exclusion.file_hash
limit 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Nicola Pirlo's test

-- COMMAND ----------

SELECT
  (
     SELECT
      count(DISTINCT file_hash)
    FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
  ) AS join_test,
  (
    SELECT count(DISTINCT file_hash) FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files
  )  AS all_files,
  (
    SELECT count(DISTINCT file_hash) FROM deletion_exclusion
  )  AS excluded_files,
  (all_files - excluded_files) as ref_value,
  (join_test = ref_value) as passed

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Final review point of files to be deleted

-- COMMAND ----------

;WITH CTE AS (
  SELECT
    file_hash
    ,max(file_size_bytes) AS file_size_bytes
  FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
  GROUP BY file_hash
)
SELECT
  COUNT(distinct file_hash) AS `number of files to be deleted`
  ,ROUND(SUM(file_size_bytes)/1024/1024/1024/1024, 0) AS `total Terabytes to be deleted`
FROM CTE

-- COMMAND ----------

SELECT
  *
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
limit 1000

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ### Verify deletion set is valid
-- MAGIC #### Check file types in dataset
-- MAGIC
-- MAGIC We only expect files of type ``avi``, ``recbin``, ``png`` and ``webm``.

-- COMMAND ----------

SELECT
  file_extension
  ,count(distinct file_hash) AS file_count
  ,round(sum(file_size_bytes)/1024/1024/1024/1024, 0) AS total_file_size_terabyte
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
GROUP BY file_extension

-- COMMAND ----------

-- MAGIC %md
-- MAGIC #### Check if stream files, i.e. avi and recbin, not matching RTC are included

-- COMMAND ----------

SELECT
  file_hash
  ,file_extension
  ,file_size_bytes
  ,file_name
  ,file_created_at
  ,recording_start_time_utc
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
WHERE
  (
  not endswith(file_name, '_RTC.avi')
  and not endswith(file_name, '_RTC.recbin')
  and not file_name LIKE '%_RTC_%.png'
  and not file_name LIKE '%_RTC_%.webm'
  )

-- COMMAND ----------

-- MAGIC %md
-- MAGIC #### Check that PNGs are only from RTC

-- COMMAND ----------

SELECT
  file_hash
  ,file_extension
  ,file_size_bytes
  ,file_name
  ,file_created_at
  ,recording_start_time_utc
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
WHERE
  file_extension='png'
  AND NOT (file_name LIKE '%_RTC_%')
limit 1000

-- COMMAND ----------

-- MAGIC %md
-- MAGIC #### Check that PNG file sizes are in expected file size_range

-- COMMAND ----------

SELECT
  file_hash
  ,file_extension
  ,round(file_size_bytes/1024/1024, 1) as file_size_kilobytes
  ,file_name
  ,file_created_at
  ,recording_start_time_utc
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
WHERE
  file_extension='png'
  AND file_size_bytes/1024/1024 > 10
limit 1000

-- COMMAND ----------

SELECT
  file_hash
  ,file_extension
  ,file_size_bytes
  ,file_name
  ,file_created_at
  ,recording_start_time_utc
FROM dd_leadership_pub_sbx.ab233021_rtc_deletion.rtc_stream_related_files_without_excluded_splits
WHERE
  not startswith(file_name, 'ALLIANCE-')
