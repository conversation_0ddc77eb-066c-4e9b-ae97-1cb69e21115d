"""Import logs from storage account to databricks table."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
# Databricks notebook source
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from pyspark.sql import Row

# COMMAND ----------

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_APPID = "368b4089-c423-46d3-943e-791fb7d8a3e0"
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)
# aad_token_cache         = AadTokenCache(credential_provider=aad_credential)

# APIM Setup
apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
apim_key = kv_client.get_secret("ddleadershipdatabricks-primary-key")

# COMMAND ----------

configs_abfss = {
    "fs.azure.account.auth.type": "OAuth",
    "fs.azure.account.oauth.provider.type": "org.apache.hadoop.fs.azurebfs.oauth2.ClientCredsTokenProvider",
    "fs.azure.account.oauth2.client.id": AAD_SP_READ_APPID,
    "fs.azure.account.oauth2.client.secret": AAD_SP_READ_SECRET,
    "fs.azure.account.oauth2.client.endpoint": f"https://login.microsoftonline.com/{AAD_TENANT_ID}/oauth2/token",
}

# COMMAND ----------

STORAGE_ACCOUNT = "stmdmstaticpaceeuw"
STORAGE_CONTAINER = "insights-logs-storageread"
dbutils.fs.mount(
    source=f"abfss://{STORAGE_CONTAINER}@{STORAGE_ACCOUNT}.dfs.core.windows.net/",
    mount_point=f"/mnt/{STORAGE_ACCOUNT}",
    extra_configs=configs_abfss,
)

# COMMAND ----------

from azure.storage.blob import BlobServiceClient

# COMMAND ----------

blob_client = BlobServiceClient(f"https://{STORAGE_ACCOUNT}.blob.core.windows.net", credential=aad_credential)

# COMMAND ----------

container_client = blob_client.get_container_client(STORAGE_CONTAINER)
# for x in blob_client.list_containers():
#     print(x)

# COMMAND ----------

for x in container_client.list_blobs():
    print(x)

# COMMAND ----------
