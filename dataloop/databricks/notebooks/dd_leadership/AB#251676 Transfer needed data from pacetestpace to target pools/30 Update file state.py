# Databricks notebook source
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

# MAGIC %md
# MAGIC # Documentation
# MAGIC
# MAGIC ## Parameters
# MAGIC
# MAGIC |Parameter Name|Description|Default QA|Default Prod|
# MAGIC |--|--|--|--|
# MAGIC |``schema_name``|Databricks schema to operate on|ab251676_pacetestpace_copy_qa|ab251676_pacetestpace_copy|

# COMMAND ----------

# MAGIC %md
# MAGIC # Copy files from input table to the target pool

# COMMAND ----------

from mdm.lib.mdm_search_module.v2.constants import Environment

SCHEMA_NAME = dbutils.widgets.get("schema_name")

environment = Environment.PROD

SLICE_SIZE = 10000
# for testing
# SLICE_SIZE = 100
N_PARALLEL_CONNECTIONS = 100

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup

# COMMAND ----------

import asyncio
import datetime
import logging
import uuid

import aiohttp
from azure.identity import ClientSecretCredential
from azure.identity.aio import ClientSecretCredential as ClientSecretCredentialAsync
from azure.keyvault.secrets.aio import SecretClient
from mdm.tds.aio import TrustedDataStorageClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_mdm_accessor import (
    AsyncMdmAccessor,
    MdmException,
    MdmFileEntryNotFoundException,
    TDSFileCopyResponse,
    TDSFileEntry,
)
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# logging
logger = logging.getLogger("Data Copying")
logger.setLevel(logging.ERROR)

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_DELETION_APPID = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--appid"
)
AAD_SP_DELETION_SECRET = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--secret"
)
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential_deletion_async = ClientSecretCredentialAsync(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_DELETION_APPID, client_secret=AAD_SP_DELETION_SECRET
)
aad_credential_deletion = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_DELETION_APPID, client_secret=AAD_SP_DELETION_SECRET
)
aad_credential_async = ClientSecretCredentialAsync(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)
aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)

aad_token_cache_deletion = AadTokenCache(credential_provider=aad_credential_deletion)
aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# Environment dependent setup
if environment == Environment.QA:
    print("Using QA instance")
    # APIM Setup
    apim_kv_url = "https://qapacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential_async)
    APIM_KEY = (await kv_client.get_secret("ddleadershipdatabricks-primary-key")).value

    # TDS
    TDS_BASE_URL = "https://data-delivery-api-qa.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-paceqa-westeurope/.default"
elif environment == Environment.PROD:
    print("Using PROD instance")
    # APIM Setup
    apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential_async)
    APIM_KEY = (await kv_client.get_secret("ddleadershipdatabricks-primary-key")).value
    
    # TDS
    TDS_BASE_URL = "https://data-delivery-api.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-pace-westeurope/.default"

# TDS
TDS_HEADERS = {"apikey": APIM_KEY}

mdm_accessor = AsyncMdmAccessor(
    credential_provider=aad_token_cache,
    environment=environment,
    use_api_gateway=True,
    credential_provider_deletion=aad_token_cache_deletion,
    apim_key=APIM_KEY,
)

# COMMAND ----------

# MAGIC %md
# MAGIC # Bookkeeping table

# COMMAND ----------

BOOKKEEPING_TABLE=f"dd_leadership_pub_sbx.{SCHEMA_NAME}.__bookkeeping"
print(BOOKKEEPING_TABLE)

# COMMAND ----------

class Bookkeeping():
    def __init__(self, logger: logging, table: str, push_n_entries: int=1000):
        self._max_entries = push_n_entries
        self._table = table
        self._log = logger
        self._entries = []

    def _spark_struct(self):
        return StructType([
            StructField("file_hash", StringType(), True),
            StructField("file_name", StringType(), True),
            StructField("file_size", LongType(), True),
            StructField("content_type", StringType(), True),
            StructField("tds_source_fid", StringType(), True),
            StructField("tds_source_fid_state", StringType(), True),
            StructField("tds_destination_fid", StringType(), True),
            StructField("tds_destination_state", StringType(), True),
            StructField("started_copy_at", TimestampType(), True),
            StructField("tds_copy_id", StringType(), True),
            StructField("copy_process_uuid", StringType(), True),
            StructField("deletion_process_uuid", StringType(), True)
        ])

    def flush_to_db(self, force: bool = False):
        if force or len(self._entries) > self._max_entries:
            self._log.info("Flushing %i entries to bookkeeping table %s", len(self._entries), self._table)
            df = spark.createDataFrame(self._entries, self._spark_struct())
            df.write.format("delta").option("mergeSchema", "true").saveAsTable(self._table, mode="append")
            # clear buffer
            self._entries.clear()


    def add_file_to_bookkeeping(self, src_entry: TDSFileEntry, destination_entry: TDSFileCopyResponse):
        record = {
            'file_hash': src_entry.sha,
            'file_name': src_entry.name,
            'file_size': src_entry.size,
            'content_type': src_entry.content_type,
            'tds_source_fid': str(src_entry.fid),
            'tds_source_fid_state': src_entry.state,
            'tds_destination_fid': None,
            'tds_destination_state': None,
            'started_copy_at': None,
            'tds_copy_id': None,
            'copy_process_uuid': None,
            'deletion_process_uuid': None
        }
        if destination_entry is not None:
            record['tds_destination_fid'] = destination_entry.file.fid
            record['tds_destination_state'] = destination_entry.file.state
            record['started_copy_at'] = destination_entry.file.created
            record['tds_copy_id'] = destination_entry.copy_id
            record['copy_process_uuid'] = destination_entry.file.upload_client_request_id
        self._entries.append(record)
        self.flush_to_db()

# COMMAND ----------

bookkeeping = Bookkeeping(logger, BOOKKEEPING_TABLE, push_n_entries=SLICE_SIZE)

# COMMAND ----------

# MAGIC %md
# MAGIC # Load Dataframe

# COMMAND ----------

def get_file_copy_state():
    df = (spark
          .read
          .format("delta")
          .table(BOOKKEEPING_TABLE)
        #   .select(
        #       col('file_hash')
        #       ,col('tds_destination_fid')
        #       ,col('tds_copy_id')
        #       ,col('tds_destination_state')
        #   )
        #   .collect()
    )

    return df

# df_copyed_nok = get_file_copy_state().where(col('tds_destination_state') != lit('ok'))
# x = df_copyed_nok.collect()
# display(df_copyed_nok.limit(10))

    #     .collect()
    # )

    # return [x.asDict()["file_hash"] for x in df_files_to_be_copied]

# COMMAND ----------

async def get_fid_state(session, file_hash, fid, request_correlation_id):
    try:
        response = await mdm_accessor.get_tds_entry_by_fid(session, fid, request_correlation_id)
        return {
            "file_hash": file_hash,
            "call_state": "ok",
            "fid": fid,
            "fid_state": response.state,
            }
    except MdmFileEntryNotFoundException:
            return {
                "file_hash": file_hash,
                "call_state": "not_found",
                "fid": fid,
                "fid_state": None,
            }

# COMMAND ----------

def update_target_fid_states(new_fid_states):
    df_result_updates = spark.createDataFrame(new_fid_states)
    df_result_updates.createOrReplaceTempView("results")
    merge_query = f"""MERGE INTO {BOOKKEEPING_TABLE} AS target
        USING results AS source
        ON
            source.file_hash=target.file_hash
            AND source.fid=target.tds_destination_fid
        WHEN MATCHED THEN UPDATE SET target.tds_destination_state=source.fid_state"""
    spark.sql(merge_query)

# COMMAND ----------

#import traceback

aiohttp_connector = aiohttp.TCPConnector(limit=N_PARALLEL_CONNECTIONS)
aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)

async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    this_correlation_id = uuid.uuid4()

    df_copyed_nok = get_file_copy_state().where(col('tds_destination_state') != lit('ok'))
    entries_to_update = df_copyed_nok.collect()
    #### DEBUG !!!!!!!!!!!!!!!!!
    # entries_to_update = entries_to_update[:2*SLICE_SIZE+10]
    ####
    number_of_files_to_check = len(entries_to_update)
    n_processed_elements = 0
    slice_start = 0

    while True:
        print(f"n_processed_elements={n_processed_elements} >= number_of_files_to_check={number_of_files_to_check}")
        if n_processed_elements >= number_of_files_to_check:
            # if all files are checked, stop processing
            break

        results = []
        try:
            logging.debug("Starting copies")

            results = await asyncio.gather(
                *[
                    get_fid_state(session, file_hash=this_entry['file_hash'], fid=this_entry['tds_destination_fid'], request_correlation_id=this_correlation_id)
                    for this_entry in entries_to_update[slice_start:slice_start+SLICE_SIZE]
                ]
            )

            n_processed_elements += len(results)

            # write update
            update_target_fid_states(results)

            # update slice_start pointer
            slice_start += SLICE_SIZE
        except Exception as e:
            logging.warning(f"An exception occured {e}")
            #traceback.print_exc()

# COMMAND ----------

# MAGIC %md
# MAGIC # Statistics

# COMMAND ----------

# MAGIC %sql
# MAGIC SELECT * FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping` LIMIT 10;
# MAGIC SELECT 
# MAGIC   'Total Rows' AS `What?`
# MAGIC   ,count(distinct file_hash) AS `Count`
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC UNION
# MAGIC SELECT 
# MAGIC   'Target FID ok' AS `What?`
# MAGIC   ,count(distinct file_hash) AS `Count`
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC WHERE tds_destination_state='ok'
# MAGIC UNION
# MAGIC SELECT 
# MAGIC   'Target FID new' AS `What?`
# MAGIC   ,count(distinct file_hash) AS `Count`
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC WHERE tds_destination_state='new'
# MAGIC UNION
# MAGIC SELECT 
# MAGIC   'Target FID processing' AS `What?`
# MAGIC   ,count(distinct file_hash) AS `Count`
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC WHERE tds_destination_state='processing'
# MAGIC UNION
# MAGIC SELECT 
# MAGIC   'Target FID ok' AS `What?`
# MAGIC   ,count(distinct file_hash) AS `Count`
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC WHERE tds_destination_state='ok'
# MAGIC UNION
# MAGIC SELECT 
# MAGIC   'Target FID error' AS `What?`
# MAGIC   ,count(distinct file_hash) AS `Count`
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC WHERE tds_destination_state='error'
# MAGIC UNION
# MAGIC SELECT 
# MAGIC   'Target FID null' AS `What?`
# MAGIC   ,count(distinct file_hash) AS `Count`
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC WHERE tds_destination_state IS NULL
# MAGIC ;

# COMMAND ----------

# MAGIC %sql
# MAGIC SELECT 
# MAGIC   *
# MAGIC FROM dd_leadership_pub_sbx.${schema_name}.`__bookkeeping`
# MAGIC WHERE tds_destination_state <> 'ok'

# COMMAND ----------


