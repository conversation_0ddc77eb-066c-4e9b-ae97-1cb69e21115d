# Databricks notebook source
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

# MAGIC %md
# MAGIC # Documentation
# MAGIC
# MAGIC ## Parameters
# MAGIC
# MAGIC |Parameter Name|Description|Default QA|Default Prod|
# MAGIC |--|--|--|--|
# MAGIC |``schema_name``|Databricks schema to operate on|ab251676_pacetestpace_copy_qa|ab251676_pacetestpace_copy|
# MAGIC |``files_to_keep_table``|Table holding all files to copy over|hashes_to_save_unique|hashes_to_save_unique|
# MAGIC |``tds_source_pool``|Pool to copy data from|pacetestpaceqa|pacetestpace|
# MAGIC |``tds_target_pool``|Pool to copy data to|clrecompqa|g3closedloop|

# COMMAND ----------

# MAGIC %md
# MAGIC # Copy files from input table to the target pool

# COMMAND ----------

from mdm.lib.mdm_search_module.v2.constants import Environment

SCHEMA_NAME = dbutils.widgets.get("schema_name")
TABLE_FILE_TO_KEEP = f"dd_leadership_pub_sbx.{SCHEMA_NAME}.hashes_to_save_unique" # f"dd_leadership_pub_sbx.{SCHEMA_NAME}.{dbutils.widgets.get('files_to_keep_table')}"
TDS_TARGET_POOL = dbutils.widgets.get("tds_target_pool")
TDS_SOURCE_POOL = dbutils.widgets.get("tds_source_pool")

environment = Environment.PROD

SLICE_SIZE = 10000
N_PARALLEL_CONNECTIONS = 100

# COMMAND ----------

# MAGIC %md
# MAGIC ## Setup

# COMMAND ----------

import asyncio
import datetime
import logging
import uuid

import aiohttp
from azure.identity import ClientSecretCredential
from azure.identity.aio import ClientSecretCredential as ClientSecretCredentialAsync
from azure.keyvault.secrets.aio import SecretClient
from mdm.tds.aio import TrustedDataStorageClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_mdm_accessor import (
    AsyncMdmAccessor,
    MdmException,
    MdmFileEntryNotFoundException,
    TDSFileCopyResponse,
    TDSFileEntry,
)
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# logging
logger = logging.getLogger("Data Copying")
logger.setLevel(logging.ERROR)

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_DELETION_APPID = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--appid"
)
AAD_SP_DELETION_SECRET = dbutils.secrets.get(
    scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--secret"
)
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential_deletion_async = ClientSecretCredentialAsync(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_DELETION_APPID, client_secret=AAD_SP_DELETION_SECRET
)
aad_credential_deletion = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_DELETION_APPID, client_secret=AAD_SP_DELETION_SECRET
)
aad_credential_async = ClientSecretCredentialAsync(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)
aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)

aad_token_cache_deletion = AadTokenCache(credential_provider=aad_credential_deletion)
aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# Environment dependent setup
if environment == Environment.QA:
    print("Using QA instance")
    # APIM Setup
    apim_kv_url = "https://qapacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential_async)
    APIM_KEY = (await kv_client.get_secret("ddleadershipdatabricks-primary-key")).value

    # TDS
    TDS_BASE_URL = "https://data-delivery-api-qa.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-paceqa-westeurope/.default"
elif environment == Environment.PROD:
    print("Using PROD instance")
    # APIM Setup
    apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential_async)
    APIM_KEY = (await kv_client.get_secret("ddleadershipdatabricks-primary-key")).value
    
    # TDS
    TDS_BASE_URL = "https://data-delivery-api.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-pace-westeurope/.default"

# TDS
TDS_HEADERS = {"apikey": APIM_KEY}

mdm_accessor = AsyncMdmAccessor(
    credential_provider=aad_token_cache,
    environment=environment,
    use_api_gateway=True,
    credential_provider_deletion=aad_token_cache_deletion,
    apim_key=APIM_KEY,
)

# COMMAND ----------

# MAGIC %md
# MAGIC # Bookkeeping table

# COMMAND ----------

BOOKKEEPING_TABLE=f"dd_leadership_pub_sbx.{SCHEMA_NAME}.__bookkeeping"
print(BOOKKEEPING_TABLE)

# COMMAND ----------

# MAGIC %sql
# MAGIC -- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.${schema_name}.__bookkeeping (
# MAGIC CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.${schema_name}.__bookkeeping (
# MAGIC   file_hash STRING
# MAGIC   ,file_name STRING
# MAGIC   ,file_size BIGINT
# MAGIC   ,content_type STRING
# MAGIC   ,tds_source_fid STRING
# MAGIC   ,tds_source_fid_state STRING
# MAGIC   ,tds_destination_fid STRING
# MAGIC   ,tds_destination_state STRING
# MAGIC   ,started_copy_at TIMESTAMP
# MAGIC   ,tds_copy_id STRING
# MAGIC   ,copy_process_uuid STRING
# MAGIC   ,deletion_process_uuid STRING
# MAGIC );

# COMMAND ----------

class Bookkeeping():
    def __init__(self, logger: logging, table: str, push_n_entries: int=1000):
        self._max_entries = push_n_entries
        self._table = table
        self._log = logger
        self._entries = []

    def _spark_struct(self):
        return StructType([
            StructField("file_hash", StringType(), True),
            StructField("file_name", StringType(), True),
            StructField("file_size", LongType(), True),
            StructField("content_type", StringType(), True),
            StructField("tds_source_fid", StringType(), True),
            StructField("tds_source_fid_state", StringType(), True),
            StructField("tds_destination_fid", StringType(), True),
            StructField("tds_destination_state", StringType(), True),
            StructField("started_copy_at", TimestampType(), True),
            StructField("tds_copy_id", StringType(), True),
            StructField("copy_process_uuid", StringType(), True),
            StructField("deletion_process_uuid", StringType(), True)
        ])

    def flush_to_db(self, force: bool = False):
        if force or len(self._entries) > self._max_entries:
            self._log.info("Flushing %i entries to bookkeeping table %s", len(self._entries), self._table)
            spark.sql("DROP VIEW IF EXISTS data_to_flush")
            df = spark.createDataFrame(self._entries, self._spark_struct())
            df.createTempView("data_to_flush")
            spark.sql(f"""MERGE INTO {self._table} AS target
                      USING data_to_flush AS source
                      ON
                        source.file_hash=target.file_hash
                      WHEN MATCHED THEN UPDATE SET *
                      WHEN NOT MATCHED BY TARGET THEN INSERT *
                      """)
            # df.write.format("delta").option("mergeSchema", "true").saveAsTable(self._table, mode="append")
            # clear buffer
            self._entries.clear()


    def add_file_to_bookkeeping(self, src_entry: TDSFileEntry, destination_entry: TDSFileCopyResponse):
        record = {
            'file_hash': src_entry.sha,
            'file_name': src_entry.name,
            'file_size': src_entry.size,
            'content_type': src_entry.content_type,
            'tds_source_fid': str(src_entry.fid),
            'tds_source_fid_state': src_entry.state,
            'tds_destination_fid': None,
            'tds_destination_state': None,
            'started_copy_at': None,
            'tds_copy_id': None,
            'copy_process_uuid': None,
            'deletion_process_uuid': None
        }
        if destination_entry is not None:
            record['tds_destination_fid'] = destination_entry.file.fid
            record['tds_destination_state'] = destination_entry.file.state
            record['started_copy_at'] = destination_entry.file.created
            record['tds_copy_id'] = destination_entry.copy_id
            record['copy_process_uuid'] = destination_entry.file.upload_client_request_id
        self._entries.append(record)
        self.flush_to_db()

# COMMAND ----------

bookkeeping = Bookkeeping(logger, BOOKKEEPING_TABLE, push_n_entries=SLICE_SIZE)

# COMMAND ----------

# bookkeeping.add_file_to_bookkeeping(good_files[0], copy_response)
# # bookkeeping._entries
# bookkeeping.flush_to_db(True)
# display(spark.sql(f"SELECT * FROM {BOOKKEEPING_TABLE}"))

# COMMAND ----------

# MAGIC %md
# MAGIC # Load Dataframe

# COMMAND ----------

display(
    spark.read.format("delta").table(BOOKKEEPING_TABLE).where(
        col('tds_source_fid_state') != lit('ok')
    )
    .limit(10)
)

# COMMAND ----------

df_already_copied = spark.read.format("delta").table(BOOKKEEPING_TABLE)
        
df = (spark.read.format("delta")
        .table(TABLE_FILE_TO_KEEP)
        .join(df_already_copied, on="file_hash", how="fullouter")
        .where(
            df_already_copied.tds_source_fid_state.isNull()
            | (df_already_copied.tds_source_fid_state != lit('ok'))
        )
)
display(df)

# COMMAND ----------

def get_file_hashes_to_copy():
    df_already_copied = spark.read.format("delta").table(BOOKKEEPING_TABLE)

    df_files_to_be_copied = (
        spark.read.format("delta")
        .table(TABLE_FILE_TO_KEEP)
        .join(df_already_copied, on="file_hash", how="fullouter")
        .where(
            df_already_copied.tds_source_fid_state.isNull()
            | (df_already_copied.tds_source_fid_state != lit('ok'))
        )
        .select(col("file_hash"))
        .distinct()
        .collect()
    )

    return [x.asDict()["file_hash"] for x in df_files_to_be_copied]

# COMMAND ----------

files_to_copy = get_file_hashes_to_copy()
print(f"Bookkeeping table: {BOOKKEEPING_TABLE}")
print(f"Files to keep table: {TABLE_FILE_TO_KEEP}")
print(f"Going to copy {len(files_to_copy)} files from '{TDS_SOURCE_POOL}' to '{TDS_TARGET_POOL}'")

# COMMAND ----------

def has_role(pool_config, role_name):
    acls = pool_config['acl']
    if len(acls) != 1:
        raise ValueError(f"Found more than once ACL on pool '{pool_config['name']}'.")
    roles = next(iter(pool_config['acl'].values()))['roles']
    return role_name in roles

def validate_pool_access(list_of_pools, pool_name, role_name):
    tds_target_pool_config = [x for x in list_of_pools['items'] if x['name'] == pool_name]
    if len(tds_target_pool_config) == 0:
        raise Exception(f"TDS pool '{pool_name}' does not exist")
    if not has_role(tds_target_pool_config[0], "uploader"):
        raise Exception(f"Does not have `{role_name}` role at TDS pool '{pool_name}'")

async def check_permissions_on_tds_pools():
    aiohttp_connector = aiohttp.TCPConnector(limit=1)
    aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)
    async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
        tds_pools = await mdm_accessor.list_pools(session, uuid.uuid4())
        validate_pool_access(tds_pools, TDS_TARGET_POOL, "uploader")
        validate_pool_access(tds_pools, TDS_SOURCE_POOL, "reader")

await check_permissions_on_tds_pools()

# COMMAND ----------

async def copy_file_by_hash(session, this_hash, source_pool, destination_pool, request_correlation_id):

    response = await mdm_accessor.get_tds_entry_by_sha(session, this_hash, request_correlation_id=this_correlation_id)

    # find best source file
    bad_files = [x for x in response if x.state in ('error', 'deleted', 'new', 'processing') and x.pool == source_pool]
    if len(bad_files) > 0:
        logging.warning("Got %i TDS file ids for hash '%s' in bad state.", len(bad_files), this_hash)
    
    good_files = [x for x in response if x.state in ('ok') and x.pool == source_pool]
    if len(good_files) > 1:
        logging.warning("Got more than one TDS file entries, i.e. %i, for hash '%s' at source pool. Picking first one.", len(good_files), this_hash)
    elif len(good_files) < 1:
        logging.error("There were no good files for hash '%s' at source pool.", this_hash)
        return {
            "source": bad_files[0],
            "destination": None
        }
        # raise RuntimeError("There were no good files for hash '%s' at source pool." % (this_hash,) )
    source_fid = good_files[0]

    # checking if file already exists on target
    already_on_dst_pool = [x for x in response if x.state in ('ok', 'new', 'processing') and x.pool == destination_pool]
    if len(already_on_dst_pool) > 0:
        # file already on destination pool
        logging.info("Found '%s' already on target pool. Skipping copying", this_hash)
        return {
            "source": source_fid,
            "destination": TDSFileCopyResponse(copy_id="UNKNOWN", file=already_on_dst_pool[0])
        }

    else:
        # need to copy file
        copy_response = await mdm_accessor.copy_file_by_fid(session, fid=source_fid.fid, destination_pool=destination_pool, request_correlation_id=this_correlation_id)
        return {
            "source": source_fid,
            "destination": copy_response
        }

# COMMAND ----------

import traceback

aiohttp_connector = aiohttp.TCPConnector(limit=N_PARALLEL_CONNECTIONS)
aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)

async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    this_correlation_id = uuid.uuid4()
    while True:
        files_to_copy = get_file_hashes_to_copy()
        if len(files_to_copy) == 0:
            logging.info("No more files to process")
            break

        logging.info("Going to copy %i files from '%s' to '%s'", len(files_to_copy), TDS_SOURCE_POOL, TDS_TARGET_POOL)

        results = []
        try:
            logging.debug("Starting copies")
            results = await asyncio.gather(
                *[
                    copy_file_by_hash(session, this_hash, source_pool=TDS_SOURCE_POOL, destination_pool=TDS_TARGET_POOL, request_correlation_id=this_correlation_id)
                    for this_hash in files_to_copy[:SLICE_SIZE]
                ]
            )
        except Exception as e:
            logging.warning(f"An exception occured {e}")
            traceback.print_exc()
        
        # save log entries
        if len(results) > 0:
            # print(results)
            [
                bookkeeping.add_file_to_bookkeeping(src_entry=this_result["source"], destination_entry=this_result["destination"])
                for this_result in results
            ]
            bookkeeping.flush_to_db(True)
        else:
            logging.warning("No results in list")

        # break

# COMMAND ----------

display(spark.sql(f"SELECT * FROM {BOOKKEEPING_TABLE} WHERE tds_source_fid_state IN ('error')"))

# COMMAND ----------

query = f"""SELECT * FROM {BOOKKEEPING_TABLE} WHERE file_hash in (
    '6b023aa3544c16c980f4004bc20ffd6916b2eec3b1d10dfc34b14a8839168f5b',
    '3b88eb9a4c26584de14076cd62cc1d3f1d0d869027b13f55886f157239c1473f',
    'fcf94c35563ddbfd0222efa512c351f13583df9dd1031b777d447ed179e78e2e')"""
display(spark.sql(query).limit(100))

# COMMAND ----------


