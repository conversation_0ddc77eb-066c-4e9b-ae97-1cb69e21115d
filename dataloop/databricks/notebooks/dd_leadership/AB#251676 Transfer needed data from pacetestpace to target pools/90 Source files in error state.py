# Databricks notebook source
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

# MAGIC %md
# MAGIC # Documentation
# MAGIC
# MAGIC ## Parameters
# MAGIC
# MAGIC |Name|What?|QA|PROD|
# MAGIC |--|--|--|--|
# MAGIC |``schema_name``|Databricks schema to operate on|ab251676_pacetestpace_copy_qa|ab251676_pacetestpace_copy|

# COMMAND ----------

# MAGIC %md
# MAGIC # Program

# COMMAND ----------

SCHEMA_NAME = dbutils.widgets.get("schema_name")

# COMMAND ----------

BOOKKEEPING_TABLE=f"dd_leadership_pub_sbx.{SCHEMA_NAME}.__bookkeeping"
print(BOOKKEEPING_TABLE)

# COMMAND ----------

from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

df = (spark
        .read
        .format("delta")
        .table(BOOKKEEPING_TABLE)
        .where(
            col('tds_source_fid_state') != lit('ok')
        )
        .select(
            col('file_hash')
            ,col('tds_source_fid')
            ,col('tds_source_fid_state')
        )
)
display(df.limit(10))


# COMMAND ----------

df.coalesce(1).write.option("header", "true").mode('overwrite').csv(f"/Volumes/dd_leadership_pub_sbx/{SCHEMA_NAME}/external_files/output_failed_source_files.csv")

# COMMAND ----------


