# Databricks notebook source
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

# MAGIC %md
# MAGIC # Documentation
# MAGIC
# MAGIC ## Parameters
# MAGIC
# MAGIC |Parameter Name|Description|Default QA|Default Prod|
# MAGIC |--|--|--|--|
# MAGIC |``schema_name``|Databricks schema to operate on|ab251676_pacetestpace_copy_qa|ab251676_pacetestpace_copy|

# COMMAND ----------

from pyspark.sql.functions import col, from_json, lit, to_timestamp_ntz
from pyspark.sql.types import ArrayType, StringType

# COMMAND ----------

schema_name = dbutils.widgets.get("schema_name")

# COMMAND ----------

print(f"dd_leadership_pub_sbx.{schema_name}.files_to_keep_mono_repo")

# COMMAND ----------

# MAGIC %md
# MAGIC # List of MDM file hashes used in PACE mono repo as provided by Martin Goth

# COMMAND ----------

df = (
    spark
        .read
        .format("csv")
        .load(f"/Volumes/dd_leadership_pub_sbx/{schema_name}/external_files/ab251676_mdm_files_in_monorepo.csv")
        .select(
            col("_c0").alias("file_hash")
        )
        .withColumn("aad_oid_provided_by", lit("fe6a91fe-73bd-46e3-a54e-661033f01331"))
        .withColumn("aad_display_name_provided_by", lit("Goth Martin (XC-DX/PJ-PACE-F21) (Bosch)"))
)
display(df)

# COMMAND ----------

df.write.format("delta").option("mergeSchema", "true").saveAsTable(f"dd_leadership_pub_sbx.{schema_name}.files_to_keep_mono_repo", mode="overwrite")

# COMMAND ----------


