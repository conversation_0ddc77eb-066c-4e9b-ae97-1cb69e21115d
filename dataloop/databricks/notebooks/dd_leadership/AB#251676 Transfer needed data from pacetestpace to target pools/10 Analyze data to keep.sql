-- Databricks notebook source
-- ===================================================================================
--  C O P Y R I G H T
-- -----------------------------------------------------------------------------------
--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- ===================================================================================

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Documentation
-- MAGIC
-- MAGIC ## Parameters
-- MAGIC
-- MAGIC |Parameter Name|Description|Default QA|Default Prod|
-- MAGIC |--|--|--|--|
-- MAGIC |``schema_name``|Databricks schema to operate on|ab251676_pacetestpace_copy_qa|ab251676_pacetestpace_copy|
-- MAGIC |``pool_name``|The TDS pool to filter on|pacetestpaceqa|pacetestpace|
-- MAGIC |``catalog_name_tds``|Databricks catalog where to find the TDS data|silver_qa|silver|

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Data referenced by the ADA mono repo

-- COMMAND ----------

USE CATALOG dd_leadership_pub_sbx;
USE SCHEMA ${schema_name};

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.${schema_name}.files_to_keep_mono_repo_only_relevant_files AS
  SELECT
    entries.*
    ,keepers.file_hash AS src_file_hash
    ,keepers.aad_display_name_provided_by
    ,keepers.aad_oid_provided_by
  FROM dd_leadership_pub_sbx.${schema_name}.files_to_keep_mono_repo AS keepers
  LEFT JOIN ${catalog_name_tds}.tds.file_entries AS entries
    ON
      entries.file_hash=keepers.file_hash
  -- filtering happens later to better validate list
  -- WHERE
  --   tds_pool IN ('${pool_name}')
  --   OR tds_pool IS NULL
--LEFT JOIN dd_leadership_pub_sbx.recording_views.all_recordings_with_enrichments AS structures

-- COMMAND ----------

SELECT * FROM dd_leadership_pub_sbx.${schema_name}.files_to_keep_mono_repo_only_relevant_files

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW START_FILES AS 
  SELECT
    file_hash
    ,file_name
    ,tds_pool
  FROM files_to_keep_mono_repo_only_relevant_files
  WHERE
    tds_pool = '${pool_name}'

-- COMMAND ----------

SELECT * FROM START_FILES LIMIT 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Full file hierarchy resolving

-- COMMAND ----------

USE CATALOG dd_leadership_pub_sbx;
USE SCHEMA ${schema_name};

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l0 AS
SELECT
  entries.file_hash     AS l0_file_hash
  ,entries.file_name    AS l0_file_name
  ,entries.content_type AS l0_content_type
  ,entries.created_at   AS l0_created_at
  ,entries.tds_pool     AS l0_tds_pool
  ,entries.parents      AS l0_parents
  ,entries.file_hash    AS file_hash
  ,entries.file_name    AS file_name
  ,entries.content_type AS content_type
  ,entries.created_at   AS created_at
  ,entries.tds_pool     AS tds_pool
FROM ${catalog_name_tds}.tds.file_entries AS entries
WHERE array_size(entries.parents)=0
;
SELECT * FROM full_recursion_l0 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

SELECT COUNT(distinct l0_file_hash) FROM full_recursion_l0

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l1 AS
SELECT
  parent.l0_file_hash     AS l0_file_hash
  ,parent.l0_file_name    AS l0_file_name
  ,parent.l0_content_type AS l0_content_type
  ,parent.l0_created_at   AS l0_created_at
  ,parent.l0_tds_pool     AS l0_tds_pool
  ,child.file_hash        AS l1_file_hash
  ,child.file_name        AS l1_file_name
  ,child.content_type     AS l1_content_type
  ,child.created_at       AS l1_created_at
  ,child.tds_pool         AS l1_tds_pool
  ,child.file_hash        AS file_hash
  ,child.file_name        AS file_name
  ,child.content_type     AS content_type
  ,child.created_at       AS created_at
  ,child.tds_pool         AS tds_pool
FROM full_recursion_l0 AS parent
LEFT JOIN ${catalog_name_tds}.tds.parents AS relations
  ON
    relations.parent_file_hash=parent.file_hash
LEFT JOIN ${catalog_name_tds}.tds.file_entries AS child
  ON
    child.file_hash=relations.file_hash
;
SELECT * FROM full_recursion_l1 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l2 AS
SELECT
  parent.l0_file_hash     AS l0_file_hash
  ,parent.l0_file_name    AS l0_file_name
  ,parent.l0_content_type AS l0_content_type
  ,parent.l0_created_at   AS l0_created_at
  ,parent.l0_tds_pool     AS l0_tds_pool
  ,parent.l1_file_hash    AS l1_file_hash
  ,parent.l1_file_name    AS l1_file_name
  ,parent.l1_content_type AS l1_content_type
  ,parent.l1_created_at   AS l1_created_at
  ,parent.l1_tds_pool     AS l1_tds_pool 
  ,child.file_hash        AS l2_file_hash
  ,child.file_name        AS l2_file_name
  ,child.content_type     AS l2_content_type
  ,child.created_at       AS l2_created_at
  ,child.tds_pool         AS l2_tds_pool
  ,child.file_hash        AS file_hash
  ,child.file_name        AS file_name
  ,child.content_type     AS content_type
  ,child.created_at       AS created_at
  ,child.tds_pool         AS tds_pool
FROM full_recursion_l1 AS parent
LEFT JOIN ${catalog_name_tds}.tds.parents AS relations
  ON
    relations.parent_file_hash=parent.file_hash
LEFT JOIN ${catalog_name_tds}.tds.file_entries AS child
  ON
    child.file_hash=relations.file_hash
;
SELECT * FROM full_recursion_l2 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l3 AS
SELECT
  parent.l0_file_hash     AS l0_file_hash
  ,parent.l0_file_name    AS l0_file_name
  ,parent.l0_content_type AS l0_content_type
  ,parent.l0_created_at   AS l0_created_at
  ,parent.l0_tds_pool     AS l0_tds_pool
  ,parent.l1_file_hash    AS l1_file_hash
  ,parent.l1_file_name    AS l1_file_name
  ,parent.l1_content_type AS l1_content_type
  ,parent.l1_created_at   AS l1_created_at
  ,parent.l1_tds_pool     AS l1_tds_pool 
  ,parent.l2_file_hash    AS l2_file_hash
  ,parent.l2_file_name    AS l2_file_name
  ,parent.l2_content_type AS l2_content_type
  ,parent.l2_created_at   AS l2_created_at
  ,parent.l2_tds_pool     AS l2_tds_pool 
  ,child.file_hash        AS l3_file_hash
  ,child.file_name        AS l3_file_name
  ,child.content_type     AS l3_content_type
  ,child.created_at       AS l3_created_at
  ,child.tds_pool         AS l3_tds_pool
  ,child.file_hash        AS file_hash
  ,child.file_name        AS file_name
  ,child.content_type     AS content_type
  ,child.created_at       AS created_at
  ,child.tds_pool         AS tds_pool
FROM full_recursion_l2 AS parent
LEFT JOIN ${catalog_name_tds}.tds.parents AS relations
  ON
    relations.parent_file_hash=parent.file_hash
LEFT JOIN ${catalog_name_tds}.tds.file_entries AS child
  ON
    child.file_hash=relations.file_hash
;
SELECT * FROM full_recursion_l3 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l4 AS
SELECT
  parent.l0_file_hash     AS l0_file_hash
  ,parent.l0_file_name    AS l0_file_name
  ,parent.l0_content_type AS l0_content_type
  ,parent.l0_created_at   AS l0_created_at
  ,parent.l0_tds_pool     AS l0_tds_pool
  ,parent.l1_file_hash    AS l1_file_hash
  ,parent.l1_file_name    AS l1_file_name
  ,parent.l1_content_type AS l1_content_type
  ,parent.l1_created_at   AS l1_created_at
  ,parent.l1_tds_pool     AS l1_tds_pool 
  ,parent.l2_file_hash    AS l2_file_hash
  ,parent.l2_file_name    AS l2_file_name
  ,parent.l2_content_type AS l2_content_type
  ,parent.l2_created_at   AS l2_created_at
  ,parent.l2_tds_pool     AS l2_tds_pool 
  ,parent.l3_file_hash    AS l3_file_hash
  ,parent.l3_file_name    AS l3_file_name
  ,parent.l3_content_type AS l3_content_type
  ,parent.l3_created_at   AS l3_created_at
  ,parent.l3_tds_pool     AS l3_tds_pool 
  ,child.file_hash        AS l4_file_hash
  ,child.file_name        AS l4_file_name
  ,child.content_type     AS l4_content_type
  ,child.created_at       AS l4_created_at
  ,child.tds_pool         AS l4_tds_pool
  ,child.file_hash        AS file_hash
  ,child.file_name        AS file_name
  ,child.content_type     AS content_type
  ,child.created_at       AS created_at
  ,child.tds_pool         AS tds_pool
FROM full_recursion_l3 AS parent
LEFT JOIN ${catalog_name_tds}.tds.parents AS relations
  ON
    relations.parent_file_hash=parent.file_hash
LEFT JOIN ${catalog_name_tds}.tds.file_entries AS child
  ON
    child.file_hash=relations.file_hash
;
SELECT * FROM full_recursion_l4 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l5 AS
SELECT
  parent.l0_file_hash     AS l0_file_hash
  ,parent.l0_file_name    AS l0_file_name
  ,parent.l0_content_type AS l0_content_type
  ,parent.l0_created_at   AS l0_created_at
  ,parent.l0_tds_pool     AS l0_tds_pool
  ,parent.l1_file_hash    AS l1_file_hash
  ,parent.l1_file_name    AS l1_file_name
  ,parent.l1_content_type AS l1_content_type
  ,parent.l1_created_at   AS l1_created_at
  ,parent.l1_tds_pool     AS l1_tds_pool 
  ,parent.l2_file_hash    AS l2_file_hash
  ,parent.l2_file_name    AS l2_file_name
  ,parent.l2_content_type AS l2_content_type
  ,parent.l2_created_at   AS l2_created_at
  ,parent.l2_tds_pool     AS l2_tds_pool 
  ,parent.l3_file_hash    AS l3_file_hash
  ,parent.l3_file_name    AS l3_file_name
  ,parent.l3_content_type AS l3_content_type
  ,parent.l3_created_at   AS l3_created_at
  ,parent.l3_tds_pool     AS l3_tds_pool 
  ,parent.l4_file_hash    AS l4_file_hash
  ,parent.l4_file_name    AS l4_file_name
  ,parent.l4_content_type AS l4_content_type
  ,parent.l4_created_at   AS l4_created_at
  ,parent.l4_tds_pool     AS l4_tds_pool 
  ,child.file_hash        AS l5_file_hash
  ,child.file_name        AS l5_file_name
  ,child.content_type     AS l5_content_type
  ,child.created_at       AS l5_created_at
  ,child.tds_pool         AS l5_tds_pool
  ,child.file_hash        AS file_hash
  ,child.file_name        AS file_name
  ,child.content_type     AS content_type
  ,child.created_at       AS created_at
  ,child.tds_pool         AS tds_pool
FROM full_recursion_l4 AS parent
LEFT JOIN ${catalog_name_tds}.tds.parents AS relations
  ON
    relations.parent_file_hash=parent.file_hash
LEFT JOIN ${catalog_name_tds}.tds.file_entries AS child
  ON
    child.file_hash=relations.file_hash
;
SELECT * FROM full_recursion_l5 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l6 AS
SELECT
  parent.l0_file_hash     AS l0_file_hash
  ,parent.l0_file_name    AS l0_file_name
  ,parent.l0_content_type AS l0_content_type
  ,parent.l0_created_at   AS l0_created_at
  ,parent.l0_tds_pool     AS l0_tds_pool
  ,parent.l1_file_hash    AS l1_file_hash
  ,parent.l1_file_name    AS l1_file_name
  ,parent.l1_content_type AS l1_content_type
  ,parent.l1_created_at   AS l1_created_at
  ,parent.l1_tds_pool     AS l1_tds_pool 
  ,parent.l2_file_hash    AS l2_file_hash
  ,parent.l2_file_name    AS l2_file_name
  ,parent.l2_content_type AS l2_content_type
  ,parent.l2_created_at   AS l2_created_at
  ,parent.l2_tds_pool     AS l2_tds_pool 
  ,parent.l3_file_hash    AS l3_file_hash
  ,parent.l3_file_name    AS l3_file_name
  ,parent.l3_content_type AS l3_content_type
  ,parent.l3_created_at   AS l3_created_at
  ,parent.l3_tds_pool     AS l3_tds_pool 
  ,parent.l4_file_hash    AS l4_file_hash
  ,parent.l4_file_name    AS l4_file_name
  ,parent.l4_content_type AS l4_content_type
  ,parent.l4_created_at   AS l4_created_at
  ,parent.l4_tds_pool     AS l4_tds_pool 
  ,parent.l5_file_hash    AS l5_file_hash
  ,parent.l5_file_name    AS l5_file_name
  ,parent.l5_content_type AS l5_content_type
  ,parent.l5_created_at   AS l5_created_at
  ,parent.l5_tds_pool     AS l5_tds_pool 
  ,child.file_hash        AS l6_file_hash
  ,child.file_name        AS l6_file_name
  ,child.content_type     AS l6_content_type
  ,child.created_at       AS l6_created_at
  ,child.tds_pool         AS l6_tds_pool
  ,child.file_hash        AS file_hash
  ,child.file_name        AS file_name
  ,child.content_type     AS content_type
  ,child.created_at       AS created_at
  ,child.tds_pool         AS tds_pool
FROM full_recursion_l5 AS parent
LEFT JOIN ${catalog_name_tds}.tds.parents AS relations
  ON
    relations.parent_file_hash=parent.file_hash
LEFT JOIN ${catalog_name_tds}.tds.file_entries AS child
  ON
    child.file_hash=relations.file_hash
;
SELECT * FROM full_recursion_l6 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

SELECT * FROM full_recursion_l6
WHERE file_hash IS NOT NULL
LIMIT 10;

-- COMMAND ----------

CREATE OR REPLACE TABLE full_recursion_l7 AS
SELECT
  parent.l0_file_hash     AS l0_file_hash
  ,parent.l0_file_name    AS l0_file_name
  ,parent.l0_content_type AS l0_content_type
  ,parent.l0_created_at   AS l0_created_at
  ,parent.l0_tds_pool     AS l0_tds_pool
  ,parent.l1_file_hash    AS l1_file_hash
  ,parent.l1_file_name    AS l1_file_name
  ,parent.l1_content_type AS l1_content_type
  ,parent.l1_created_at   AS l1_created_at
  ,parent.l1_tds_pool     AS l1_tds_pool 
  ,parent.l2_file_hash    AS l2_file_hash
  ,parent.l2_file_name    AS l2_file_name
  ,parent.l2_content_type AS l2_content_type
  ,parent.l2_created_at   AS l2_created_at
  ,parent.l2_tds_pool     AS l2_tds_pool 
  ,parent.l3_file_hash    AS l3_file_hash
  ,parent.l3_file_name    AS l3_file_name
  ,parent.l3_content_type AS l3_content_type
  ,parent.l3_created_at   AS l3_created_at
  ,parent.l3_tds_pool     AS l3_tds_pool 
  ,parent.l4_file_hash    AS l4_file_hash
  ,parent.l4_file_name    AS l4_file_name
  ,parent.l4_content_type AS l4_content_type
  ,parent.l4_created_at   AS l4_created_at
  ,parent.l4_tds_pool     AS l4_tds_pool 
  ,parent.l5_file_hash    AS l5_file_hash
  ,parent.l5_file_name    AS l5_file_name
  ,parent.l5_content_type AS l5_content_type
  ,parent.l5_created_at   AS l5_created_at
  ,parent.l5_tds_pool     AS l5_tds_pool 
  ,parent.l6_file_hash    AS l6_file_hash
  ,parent.l6_file_name    AS l6_file_name
  ,parent.l6_content_type AS l6_content_type
  ,parent.l6_created_at   AS l6_created_at
  ,parent.l6_tds_pool     AS l6_tds_pool 
  ,child.file_hash        AS l7_file_hash
  ,child.file_name        AS l7_file_name
  ,child.content_type     AS l7_content_type
  ,child.created_at       AS l7_created_at
  ,child.tds_pool         AS l7_tds_pool
  ,child.file_hash        AS file_hash
  ,child.file_name        AS file_name
  ,child.content_type     AS content_type
  ,child.created_at       AS created_at
  ,child.tds_pool         AS tds_pool
FROM full_recursion_l6 AS parent
LEFT JOIN ${catalog_name_tds}.tds.parents AS relations
  ON
    relations.parent_file_hash=parent.file_hash
LEFT JOIN ${catalog_name_tds}.tds.file_entries AS child
  ON
    child.file_hash=relations.file_hash
;
SELECT * FROM full_recursion_l7 WHERE file_hash IS NOT NULL LIMIT 10;

-- COMMAND ----------

SELECT * FROM full_recursion_l7 
WHERE file_hash IS NOT NULL
LIMIT 10;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC So we have only upto 6 level of recursion

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Create full recursion table

-- COMMAND ----------

USE CATALOG dd_leadership_pub_sbx;
USE SCHEMA ${schema_name};

CREATE OR REPLACE TABLE full_recursion AS
  SELECT
    l0_file_hash
    ,l0_file_name
    ,l0_content_type
    ,l0_created_at
    ,l0_tds_pool
    ,NULL AS l1_file_hash
    ,NULL AS l1_file_name
    ,NULL AS l1_content_type
    ,NULL AS l1_created_at
    ,NULL AS l1_tds_pool
    ,NULL AS l2_file_hash
    ,NULL AS l2_file_name
    ,NULL AS l2_content_type
    ,NULL AS l2_created_at
    ,NULL AS l2_tds_pool
    ,NULL AS l3_file_hash
    ,NULL AS l3_file_name
    ,NULL AS l3_content_type
    ,NULL AS l3_created_at
    ,NULL AS l3_tds_pool
    ,NULL AS l4_file_hash
    ,NULL AS l4_file_name
    ,NULL AS l4_content_type
    ,NULL AS l4_created_at
    ,NULL AS l4_tds_pool
    ,NULL AS l5_file_hash
    ,NULL AS l5_file_name
    ,NULL AS l5_content_type
    ,NULL AS l5_created_at
    ,NULL AS l5_tds_pool
    ,NULL AS l6_file_hash
    ,NULL AS l6_file_name
    ,NULL AS l6_content_type
    ,NULL AS l6_created_at
    ,NULL AS l6_tds_pool
    ,file_hash
    ,file_name
    ,content_type
    ,created_at
    ,tds_pool
  FROM full_recursion_l0
  WHERE file_hash IS NOT NULL
  UNION
  SELECT
    l0_file_hash
    ,l0_file_name
    ,l0_content_type
    ,l0_created_at
    ,l0_tds_pool
    ,l1_file_hash
    ,l1_file_name
    ,l1_content_type
    ,l1_created_at
    ,l1_tds_pool
    ,NULL AS l2_file_hash
    ,NULL AS l2_file_name
    ,NULL AS l2_content_type
    ,NULL AS l2_created_at
    ,NULL AS l2_tds_pool
    ,NULL AS l3_file_hash
    ,NULL AS l3_file_name
    ,NULL AS l3_content_type
    ,NULL AS l3_created_at
    ,NULL AS l3_tds_pool
    ,NULL AS l4_file_hash
    ,NULL AS l4_file_name
    ,NULL AS l4_content_type
    ,NULL AS l4_created_at
    ,NULL AS l4_tds_pool
    ,NULL AS l5_file_hash
    ,NULL AS l5_file_name
    ,NULL AS l5_content_type
    ,NULL AS l5_created_at
    ,NULL AS l5_tds_pool
    ,NULL AS l6_file_hash
    ,NULL AS l6_file_name
    ,NULL AS l6_content_type
    ,NULL AS l6_created_at
    ,NULL AS l6_tds_pool
    ,file_hash
    ,file_name
    ,content_type
    ,created_at
    ,tds_pool
  FROM full_recursion_l1
  WHERE file_hash IS NOT NULL
  UNION
  SELECT
    l0_file_hash
    ,l0_file_name
    ,l0_content_type
    ,l0_created_at
    ,l0_tds_pool
    ,l1_file_hash
    ,l1_file_name
    ,l1_content_type
    ,l1_created_at
    ,l1_tds_pool
    ,l2_file_hash
    ,l2_file_name
    ,l2_content_type
    ,l2_created_at
    ,l2_tds_pool
    ,NULL AS l3_file_hash
    ,NULL AS l3_file_name
    ,NULL AS l3_content_type
    ,NULL AS l3_created_at
    ,NULL AS l3_tds_pool
    ,NULL AS l4_file_hash
    ,NULL AS l4_file_name
    ,NULL AS l4_content_type
    ,NULL AS l4_created_at
    ,NULL AS l4_tds_pool
    ,NULL AS l5_file_hash
    ,NULL AS l5_file_name
    ,NULL AS l5_content_type
    ,NULL AS l5_created_at
    ,NULL AS l5_tds_pool
    ,NULL AS l6_file_hash
    ,NULL AS l6_file_name
    ,NULL AS l6_content_type
    ,NULL AS l6_created_at
    ,NULL AS l6_tds_pool
    ,file_hash
    ,file_name
    ,content_type
    ,created_at
    ,tds_pool
  FROM full_recursion_l2
  WHERE file_hash IS NOT NULL
  UNION
  SELECT
    l0_file_hash
    ,l0_file_name
    ,l0_content_type
    ,l0_created_at
    ,l0_tds_pool
    ,l1_file_hash
    ,l1_file_name
    ,l1_content_type
    ,l1_created_at
    ,l1_tds_pool
    ,l2_file_hash
    ,l2_file_name
    ,l2_content_type
    ,l2_created_at
    ,l2_tds_pool
    ,l3_file_hash
    ,l3_file_name
    ,l3_content_type
    ,l3_created_at
    ,l3_tds_pool
    ,NULL AS l4_file_hash
    ,NULL AS l4_file_name
    ,NULL AS l4_content_type
    ,NULL AS l4_created_at
    ,NULL AS l4_tds_pool
    ,NULL AS l5_file_hash
    ,NULL AS l5_file_name
    ,NULL AS l5_content_type
    ,NULL AS l5_created_at
    ,NULL AS l5_tds_pool
    ,NULL AS l6_file_hash
    ,NULL AS l6_file_name
    ,NULL AS l6_content_type
    ,NULL AS l6_created_at
    ,NULL AS l6_tds_pool
    ,file_hash
    ,file_name
    ,content_type
    ,created_at
    ,tds_pool
  FROM full_recursion_l3
  WHERE file_hash IS NOT NULL
  UNION
  SELECT
    l0_file_hash
    ,l0_file_name
    ,l0_content_type
    ,l0_created_at
    ,l0_tds_pool
    ,l1_file_hash
    ,l1_file_name
    ,l1_content_type
    ,l1_created_at
    ,l1_tds_pool
    ,l2_file_hash
    ,l2_file_name
    ,l2_content_type
    ,l2_created_at
    ,l2_tds_pool
    ,l3_file_hash
    ,l3_file_name
    ,l3_content_type
    ,l3_created_at
    ,l3_tds_pool
    ,l4_file_hash
    ,l4_file_name
    ,l4_content_type
    ,l4_created_at
    ,l4_tds_pool
    ,NULL AS l5_file_hash
    ,NULL AS l5_file_name
    ,NULL AS l5_content_type
    ,NULL AS l5_created_at
    ,NULL AS l5_tds_pool
    ,NULL AS l6_file_hash
    ,NULL AS l6_file_name
    ,NULL AS l6_content_type
    ,NULL AS l6_created_at
    ,NULL AS l6_tds_pool
    ,file_hash
    ,file_name
    ,content_type
    ,created_at
    ,tds_pool
  FROM full_recursion_l4
  WHERE file_hash IS NOT NULL
  UNION
  SELECT
    l0_file_hash
    ,l0_file_name
    ,l0_content_type
    ,l0_created_at
    ,l0_tds_pool
    ,l1_file_hash
    ,l1_file_name
    ,l1_content_type
    ,l1_created_at
    ,l1_tds_pool
    ,l2_file_hash
    ,l2_file_name
    ,l2_content_type
    ,l2_created_at
    ,l2_tds_pool
    ,l3_file_hash
    ,l3_file_name
    ,l3_content_type
    ,l3_created_at
    ,l3_tds_pool
    ,l4_file_hash
    ,l4_file_name
    ,l4_content_type
    ,l4_created_at
    ,l4_tds_pool
    ,l5_file_hash
    ,l5_file_name
    ,l5_content_type
    ,l5_created_at
    ,l5_tds_pool
    ,NULL AS l6_file_hash
    ,NULL AS l6_file_name
    ,NULL AS l6_content_type
    ,NULL AS l6_created_at
    ,NULL AS l6_tds_pool
    ,file_hash
    ,file_name
    ,content_type
    ,created_at
    ,tds_pool
  FROM full_recursion_l5
  WHERE file_hash IS NOT NULL
  UNION
  SELECT
    l0_file_hash
    ,l0_file_name
    ,l0_content_type
    ,l0_created_at
    ,l0_tds_pool
    ,l1_file_hash
    ,l1_file_name
    ,l1_content_type
    ,l1_created_at
    ,l1_tds_pool
    ,l2_file_hash
    ,l2_file_name
    ,l2_content_type
    ,l2_created_at
    ,l2_tds_pool
    ,l3_file_hash
    ,l3_file_name
    ,l3_content_type
    ,l3_created_at
    ,l3_tds_pool
    ,l4_file_hash
    ,l4_file_name
    ,l4_content_type
    ,l4_created_at
    ,l4_tds_pool
    ,l5_file_hash
    ,l5_file_name
    ,l5_content_type
    ,l5_created_at
    ,l5_tds_pool
    ,l6_file_hash
    ,l6_file_name
    ,l6_content_type
    ,l6_created_at
    ,l6_tds_pool
    ,file_hash
    ,file_name
    ,content_type
    ,created_at
    ,tds_pool
  FROM full_recursion_l6
  WHERE file_hash IS NOT NULL
;

-- COMMAND ----------

SELECT * FROM full_recursion LIMIT 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## clean-up temporary tables

-- COMMAND ----------

DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recorsion_l0;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l0;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l1;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l2;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l3;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l4;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l5;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l6;
DROP TABLE IF EXISTS dd_leadership_pub_sbx.${schema_name}.full_recursion_l7;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Check for parents and ancestors of relevant files

-- COMMAND ----------

USE CATALOG dd_leadership_pub_sbx;
USE SCHEMA ${schema_name};

CREATE OR REPLACE TEMPORARY VIEW START_FILES AS 
  SELECT
    file_hash
    ,file_name
    ,tds_pool
  FROM files_to_keep_mono_repo_only_relevant_files
  WHERE
    tds_pool = '${pool_name}'
;

-- COMMAND ----------

CREATE OR REPLACE TABLE files_to_keep_full_recursion AS 
  SELECT
    START_FILES.file_hash AS whitelist_file_hash
    ,full_recursion.*
  FROM START_FILES
  LEFT JOIN full_recursion
    ON
      full_recursion.file_hash=START_FILES.file_hash

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Table of files that must be copied

-- COMMAND ----------

CREATE OR REPLACE TABLE hashes_to_save (
  whitelist_file_hash STRING
  ,file_hash STRING
  ,file_name STRING
);

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Match on L0

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l0_file_hash

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l0_file_hash
  AND e.l1_file_hash IS NOT NULL

-- COMMAND ----------

-- MAGIC %md
-- MAGIC All files on this level are on ``pacetestpace`` pool and must be moved to pool ``clrecompute``. There are no children.

-- COMMAND ----------

INSERT INTO hashes_to_save SELECT e.whitelist_file_hash, e.l0_file_hash, e.l0_file_name FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l0_file_hash

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Match on L1

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l1_file_hash

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l1_file_hash
  AND e.l2_file_hash IS NOT NULL

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW MATCHES_L1_PARENTS AS
SELECT
  e.whitelist_file_hash
  ,recursion.file_hash
  ,recursion.file_name
FROM files_to_keep_full_recursion AS e
LEFT JOIN full_recursion AS recursion
  ON
    recursion.l0_file_hash=e.l0_file_hash
    AND recursion.tds_pool='${pool_name}'
WHERE
  e.whitelist_file_hash=e.l1_file_hash
  AND e.l0_tds_pool='${pool_name}'

-- COMMAND ----------

-- find all parents that are on pacetestpace pool and select their children
SELECT COUNT(distinct file_hash), count(*) FROM MATCHES_L1_PARENTS

-- COMMAND ----------

SELECT * FROM MATCHES_L1_PARENTS LIMIT 1000

-- COMMAND ----------

INSERT INTO hashes_to_save SELECT whitelist_file_hash, file_hash, file_name FROM MATCHES_L1_PARENTS

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Match on L2

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l2_file_hash

-- COMMAND ----------

-- check if any of these files has children
SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l2_file_hash
  AND e.l3_file_hash IS NOT NULL

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ### Direct parents and their children being on pacetestpace pool

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW MATCHES_L2_PARENTS AS SELECT
  e.whitelist_file_hash
  ,recursion.*
FROM files_to_keep_full_recursion AS e
LEFT JOIN full_recursion AS recursion
  ON
    recursion.l1_file_hash=e.l1_file_hash
    -- only children of the parent that are on pacetestpace pool
    AND recursion.tds_pool='${pool_name}'
WHERE
  -- only consider whitelist files on l2
  e.whitelist_file_hash=e.l2_file_hash
  -- parents on any pool

-- COMMAND ----------

-- find all direct parents that are on pacetestpace pool and select their children on pacetestpace pool
SELECT
  '*' AS `Parent Location`
  ,'${pool_name}' AS `Child Location`
  ,COUNT(distinct file_hash)
FROM MATCHES_L2_PARENTS

-- COMMAND ----------

SELECT * FROM MATCHES_L2_PARENTS

-- COMMAND ----------

-- find all direct parents that are on pacetestpace pool and select their children on pacetestpace pool
INSERT INTO hashes_to_save SELECT whitelist_file_hash, file_hash, file_name FROM MATCHES_L2_PARENTS

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ### Grand parents and their children being on pacetestpace pool

-- COMMAND ----------


CREATE OR REPLACE TEMPORARY VIEW MATCHES_L2_GRAND_PARENTS AS SELECT
  e.whitelist_file_hash
  ,recursion.*
FROM files_to_keep_full_recursion AS e
LEFT JOIN full_recursion AS recursion
  ON
    -- go to grandparent
    recursion.l0_file_hash=e.l0_file_hash
    -- only children of the parent that are on pacetestpace pool
    AND recursion.tds_pool='${pool_name}'
WHERE
  -- only consider whitelist files on l2
  e.whitelist_file_hash=e.l2_file_hash
  -- garndparents on any pool
  -- AND e.l0_tds_pool='${pool_name}'

-- COMMAND ----------

SELECT
  '*' AS `Grand parent location`
  ,'*' AS `Parent Location`
  ,'${pool_name}' AS `Child Location`
  ,COUNT(distinct file_hash)
FROM MATCHES_L2_GRAND_PARENTS

-- COMMAND ----------

SELECT * FROM MATCHES_L2_GRAND_PARENTS

-- COMMAND ----------

INSERT INTO hashes_to_save 
SELECT 
  whitelist_file_hash
  ,file_hash
  ,file_name
FROM MATCHES_L2_GRAND_PARENTS

-- COMMAND ----------



-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Follow-up level / all empty

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l3_file_hash

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l4_file_hash

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l5_file_hash

-- COMMAND ----------

SELECT * FROM files_to_keep_full_recursion AS e
WHERE
  e.whitelist_file_hash=e.l6_file_hash

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Filter on unique file hashes

-- COMMAND ----------

SELECT
  COUNT(*)
  ,COUNT(distinct file_hash)
FROM hashes_to_save 

-- COMMAND ----------

CREATE OR REPLACE TABLE hashes_to_save_unique AS
  SELECT
    file_hash
    ,MAX(file_name) AS file_name
    ,collect_list(whitelist_file_hash) AS whitelist_file_hashes
  FROM hashes_to_save 
  GROUP BY
    file_hash
;

-- COMMAND ----------

SELECT * FROM hashes_to_save_unique

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Ensure that all files on initial list are included

-- COMMAND ----------

USE CATALOG dd_leadership_pub_sbx;
USE SCHEMA ${schema_name};

-- COMMAND ----------

SELECT 
  * 
-- the table of hashes to keep from mono repo
FROM dd_leadership_pub_sbx.${schema_name}.files_to_keep_mono_repo AS keepers
-- the table of hashes to keep from mono repo found on MDM prod
LEFT JOIN files_to_keep_mono_repo_only_relevant_files AS final_list
ON
  keepers.file_hash=final_list.file_hash
WHERE
  -- that's only true if the file is not found on MDM prod
  final_list.file_hash IS NULL

-- COMMAND ----------

-- MAGIC %md
-- MAGIC I checked the missing 81 files with TDS. These are files stored on QA pool.

-- COMMAND ----------


