# Databricks notebook source
# MAGIC %md
# MAGIC ```python
# MAGIC # ===================================================================================
# MAGIC #  C O P Y R I G H T
# MAGIC # -----------------------------------------------------------------------------------
# MAGIC #  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# MAGIC # ===================================================================================
# MAGIC ```

# COMMAND ----------

# MAGIC %md
# MAGIC # Create table with splits
# MAGIC
# MAGIC Search query provided by AI Fusion (<PERSON> Lombacher):
# MAGIC > All scenes where these Images belong to should not be archived [Search & Find (ad-alliance.biz)](https://www.search.ad-alliance.biz/search-result?l=name:%22ALLIANCE-DC02_%2a_TVfront_f00007_RGB_ContextA_rectified.png%22&rv=grid&rpp=24&ap=5&sb=createdDate&so=asc). 
# MAGIC > Streams which are needed are FC1, TV cameras, reference lidar and if available radar, imu data, and CAN/Flexray imu data. 
# MAGIC
# MAGIC SQL Notation:
# MAGIC ```sql
# MAGIC SELECT 
# MAGIC   *
# MAGIC FROM silver.tds.file_entries
# MAGIC WHERE
# MAGIC   file_name LIKE 'ALLIANCE-DC02_%_TVfront_f00007_RGB_ContextA_rectified.png'
# MAGIC ```
# MAGIC
# MAGIC On the selected splits, we remove the streams:
# MAGIC - FC2
# MAGIC - FC2STEREO
# MAGIC - FSCRIGHT
# MAGIC - FSCLEFT
# MAGIC - RSCRIGHT
# MAGIC - RSCLEFT
# MAGIC - RTC

# COMMAND ----------

# MAGIC %md
# MAGIC ## Verify that counts between Search & Find and Databricks SQL query matches
# MAGIC
# MAGIC Search & Find report 40849 files back.

# COMMAND ----------

# MAGIC %sql
# MAGIC select count(distinct file_hash) from silver.tds.file_entries
# MAGIC where file_name LIKE 'ALLIANCE-DC02_%_TVfront_f00007_RGB_ContextA_rectified.png'

# COMMAND ----------

# MAGIC %md
# MAGIC Databricks Result set size on 2024/09/06 at 17:55 CEST:

# COMMAND ----------

# MAGIC %md
# MAGIC ## Create Views

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMPORARY VIEW fusion_image_selection AS
# MAGIC   SELECT
# MAGIC     images.file_name AS image_file_name
# MAGIC     ,images.file_hash AS image_file_hash
# MAGIC     ,concat(substring_index(images.file_name, "_TVfront_", 1), ".scene") AS scene_file_name
# MAGIC     ,scene_files.file_hash AS scene_file_hash
# MAGIC   FROM silver.tds.file_entries AS images
# MAGIC   INNER JOIN silver.tds.file_entries AS scene_files
# MAGIC     ON
# MAGIC       scene_files.file_name=concat(substring_index(images.file_name, "_TVfront_", 1), ".scene")
# MAGIC   WHERE 
# MAGIC     images.file_name LIKE 'ALLIANCE-DC02_%_TVfront_f00007_RGB_ContextA_rectified.png'

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMPORARY VIEW fusion_image_split_set AS
# MAGIC   SELECT
# MAGIC     scene_file_hash AS split_file_hash
# MAGIC   FROM fusion_image_selection
# MAGIC   GROUP BY split_file_hash

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMPORARY VIEW fusion_all_split_data AS
# MAGIC   SELECT
# MAGIC     fusion_image_selection.scene_file_hash AS fusion_image_scene_hash
# MAGIC     ,all_recordings.file_hash AS file_hash
# MAGIC     ,all_recordings.stream_name AS stream_name
# MAGIC     ,all_recordings.file_size_bytes AS file_size_bytes
# MAGIC   FROM fusion_image_selection
# MAGIC   LEFT JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings
# MAGIC     ON
# MAGIC       fusion_image_selection.scene_file_hash = all_recordings.split_hash

# COMMAND ----------

# MAGIC %md
# MAGIC ## Analyze selected dataset

# COMMAND ----------

# MAGIC %md
# MAGIC ### Total dataset size in gigabytes

# COMMAND ----------

# MAGIC %sql
# MAGIC SELECT
# MAGIC   ROUND(SUM(file_size_bytes)/1024/1024/1024,0) AS file_size_gigabytes
# MAGIC FROM fusion_all_split_data

# COMMAND ----------

# MAGIC %md
# MAGIC Result on 2024/09/06:
# MAGIC   - 1.4 PiB

# COMMAND ----------

# MAGIC %md
# MAGIC ### Storage size distribution by stream

# COMMAND ----------

# MAGIC %sql
# MAGIC WITH cte AS (
# MAGIC   SELECT
# MAGIC     stream_name
# MAGIC     ,MAX(file_size_bytes) AS file_size_bytes
# MAGIC   FROM fusion_all_split_data
# MAGIC   GROUP BY file_hash, stream_name
# MAGIC )
# MAGIC SELECT
# MAGIC   stream_name
# MAGIC   ,ROUND(SUM(file_size_bytes)/1024/1024/1024,0) AS file_size_gigabytes
# MAGIC   ,COUNT(*) AS file_count
# MAGIC FROM cte
# MAGIC GROUP BY stream_name
# MAGIC ORDER BY stream_name ASC

# COMMAND ----------

# MAGIC %md
# MAGIC The FC2*, FSC* and RSC* camera streams take significant amount of data but are not requested by the team, so we remove them in the next step.

# COMMAND ----------

# MAGIC %md
# MAGIC ## Remove unneeded camera streams

# COMMAND ----------

# MAGIC %sql
# MAGIC CREATE OR REPLACE TEMPORARY VIEW fusion_all_split_data_no_unused_streams AS
# MAGIC   SELECT
# MAGIC       *
# MAGIC   FROM fusion_all_split_data
# MAGIC   WHERE
# MAGIC     stream_name NOT IN (
# MAGIC       'FC2'
# MAGIC       ,'FC2STEREO'
# MAGIC       ,'FSCRIGHT'
# MAGIC       ,'FSCLEFT'
# MAGIC       ,'RSCRIGHT'
# MAGIC       ,'RSCLEFT'
# MAGIC       ,'RTC'
# MAGIC     )

# COMMAND ----------

# MAGIC %sql
# MAGIC WITH CTE AS (
# MAGIC   SELECT
# MAGIC     file_hash
# MAGIC     ,MAX(file_size_bytes) AS file_size_bytes
# MAGIC   FROM fusion_all_split_data_no_unused_streams
# MAGIC   GROUP BY file_hash
# MAGIC )
# MAGIC SELECT
# MAGIC   ROUND(SUM(file_size_bytes)/1024/1024/1024,0) AS file_size_gigabytes
# MAGIC   ,count(distinct file_hash) AS file_count
# MAGIC FROM fusion_all_split_data_no_unused_streams

# COMMAND ----------

# MAGIC %md
# MAGIC Result on 2024/09/06:
# MAGIC   - 0,6 PiB

# COMMAND ----------

# MAGIC %md
# MAGIC # Create dataset from table

# COMMAND ----------

import asyncio
import datetime
import itertools
import json
import logging
import traceback
import uuid

import aiohttp
import pyspark.sql.functions as sf
from azure.identity import ClientSecretCredential, DeviceCodeCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# logging
logger = logging.getLogger("Dataset Creation")
logger.setLevel(logging.ERROR)

# COMMAND ----------

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"

AZURE_STORAGE_AUDIENCE = "https://storage.azure.com/.default"

aad_credential = DeviceCodeCredential(tenant_id=AAD_TENANT_ID)
aad_credential.get_token(AZURE_STORAGE_AUDIENCE)

# COMMAND ----------

aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# COMMAND ----------

# APIM Setup

AAD_SP_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

sp_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_APPID, client_secret=AAD_SP_SECRET
)

apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
kv_client = SecretClient(vault_url=apim_kv_url, credential=sp_credential)
APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

# COMMAND ----------

from modules.async_dataset_accessor import AsyncDatasetAccessor

# COMMAND ----------

dataset = spark.sql("""SELECT DISTINCT file_hash FROM fusion_all_split_data_no_unused_streams""")

# COMMAND ----------

dataset_shas = dataset.collect()

# COMMAND ----------

print(len(dataset_shas))

# COMMAND ----------

from itertools import chain

# COMMAND ----------

dataset_request = {
    "state": "public",
    "type": "image/jpeg",
    "catalog": {"service": {"platform": "mdm"}, "tenant": "neutral tenant", "url": "https://data-delivery-api.ad-alliance.biz/mddump/"},
    "entries": [x[0] for x in dataset_shas],
    "author": {
        "users": [
        {
            "object_id": "291163e6-95b3-4fe7-870f-3f92455fe88b",
            "name": "Lombacher, Jakob (T1-45) (Cariad)"
        }
        ],
        "applications": [
        ]
    },
    "tags": ["keep_on_hot_storage"],
    "metadata": {
        "dataset_name": "AB#305070 / AI Fusion Dataset to be kept on hot storage"
    },
    "description": "AI Fusion Dataset as defined in Decision 305070: Blacklisting Fusion Datasets for data archiving consideration"
    }

# COMMAND ----------

# import json
# request_body = json.dumps(dataset_request)
# print(request_body)

# COMMAND ----------

dataset_base_url = "https://data-delivery-api.ad-alliance.biz/datasets-service"
dataset_aad_audience = "api://sp-pace-datasets-api-dll-prod/.default"

# COMMAND ----------

request_correlation_id: uuid.UUID = uuid.uuid4()

# COMMAND ----------

request_header = {
    "Authorization": f"Bearer {aad_token_cache.get_token(dataset_aad_audience).token}",
    "x-client-request-id": str(request_correlation_id),
    "apikey": APIM_KEY,
}

# COMMAND ----------

import requests

# COMMAND ----------

# response = requests.post(f"{dataset_base_url}/api/v2/datasets", headers=request_header, json=dataset_request)
# print(response.status_code)
# print(response.text)
# print(response.raise_for_status())

# COMMAND ----------

# MAGIC %md
# MAGIC ## Result
# MAGIC Dataset was created with id: ba0332d4-6a3e-4ece-9b39-8a7b1c9576a8

# COMMAND ----------

# MAGIC %md
# MAGIC # Validate that files in the new dataset are not on the tiering list
# MAGIC
# MAGIC Before you can execute this test, the full storage tiering workflow must be executed.

# COMMAND ----------

# MAGIC %sql
# MAGIC select * from fusion_all_split_data_no_unused_streams limit 10

# COMMAND ----------

# MAGIC %sql
# MAGIC SELECT 
# MAGIC   * 
# MAGIC FROM dd_leadership_pub_sbx.data_tiering_service.tiering_decision
# MAGIC INNER JOIN fusion_all_split_data_no_unused_streams AS this_dataset
# MAGIC   ON this_dataset.file_hash = tiering_decision.file_hash
# MAGIC WHERE
# MAGIC   tiering_decision.target_storage_tier = 'ARCHIVE'
# MAGIC limit 10

# COMMAND ----------
