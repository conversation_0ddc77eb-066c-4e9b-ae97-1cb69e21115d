-- Databricks notebook source
-- MAGIC %md
-- MAGIC # Create time binned signal table
-- MAGIC We'll keep the earliest value for each signal per second.

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.events_flexray_time_binned
WITH CTE AS (
  SELECT
    recording_hash
    ,split_hash
    ,date_trunc('SECOND', started_at) AS started_at
    ,started_at AS _started_at
    ,name
    ,value
    ,value_extra
  FROM dd_leadership_pub_sbx.timeseries.events_flexray
),
ORDERED_DATA AS (
  SELECT
    row_number() OVER (PARTITION BY recording_hash, started_at, name ORDER BY _started_at ASC) AS _row_number
    ,*
  FROM CTE
)
SELECT
  *
  EXCEPT(_row_number, _started_at)
FROM ORDERED_DATA
WHERE
  _row_number = 1

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Optimize table and clean-up

-- COMMAND ----------

OPTIMIZE dd_leadership_pub_sbx.timeseries.events_flexray_time_binned ZORDER BY (recording_hash, started_at)
