-- Databricks notebook source
-- MAGIC %md
-- MAGI<PERSON> ```python
-- MAGIC # ===================================================================================
-- MAGIC #  C O P Y R I G H T
-- MAGIC # -----------------------------------------------------------------------------------
-- MAGIC #  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- MAGIC # ===================================================================================
-- MAGIC ```

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Define dataset

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW dataset AS 
  SELECT
    file_entries.file_hash
    ,file_entries.tds_file_url
    ,regexp_extract(file_entries.tds_file_url, 'https://[^/]+/[^/]+/([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})/', 1) AS file_source_tds_fid
    ,file_entries.file_size_bytes
    ,file_entries.file_state
    ,'ACTIVE' AS target_storage_tier
  FROM silver.tds.file_entries
  WHERE
    file_extension='recbin'
    AND file_state='ARCHIVED'

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Analytics of the data set including restore cost

-- COMMAND ----------

SELECT
  COUNT(distinct dataset.file_hash) AS file_count
  ,sum(dataset.file_size_bytes)/1024/1024/1024 AS total_size_gigabytes
  ,sum(restore_costs.restore_low_prio_total_euro) AS costs_high_prio_euro
  ,sum(restore_costs.restore_high_prio_total_euro) AS costs_high_prio_euro
FROM dataset
LEFT JOIN dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs AS restore_costs
  ON
    restore_costs.file_hash=dataset.file_hash

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Persist dataset

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.data_tiering_service.__98_dataset_restore_recbins
  select 
    *  
  from dataset
;

-- COMMAND ----------

OPTIMIZE dd_leadership_pub_sbx.data_tiering_service.__98_dataset_restore_recbins;

-- COMMAND ----------

VACUUM dd_leadership_pub_sbx.data_tiering_service.__98_dataset_restore_recbins;

-- COMMAND ----------

ANALYZE TABLE dd_leadership_pub_sbx.data_tiering_service.__98_dataset_restore_recbins COMPUTE STATISTICS FOR ALL COLUMNS

-- COMMAND ----------


