{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2b9efe38-f799-4280-b155-33cb134dc32c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- ===================================================================================\n", "--  <PERSON> O P Y R I G H T\n", "-- -----------------------------------------------------------------------------------\n", "--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.\n", "-- =================================================================================== "]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b9334597-80d7-4b18-9166-07fd8b0388bf", "showTitle": false, "title": ""}}, "source": ["# Parameters\n", "\n", "|Name|Description|QA|Prod|\n", "|--|--|--|--|\n", "|target_catalog|||dd_leadership_pub_sbx|\n", "|target_schema|||data_tiering_service|"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e05563dc-bd3b-47d1-811a-0aca14fdd56f", "showTitle": false, "title": ""}}, "source": ["# Variables"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8a770557-32f7-4b46-895a-6e27d11a664f", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- min file age before tiering\n", "DECLARE OR REPLACE min_file_age_days_before_tiering INT;\n", "SET VARIABLE min_file_age_days_before_tiering=(SELECT CAST(value AS INT) FROM dd_leadership_pub_sbx.data_tiering_service.__configuration WHERE key='min_file_age_days_before_tiering');\n", "-- dataset tag name\n", "DECLARE OR REPLACE dataset_tag_keep_on_hotstorage STRING;\n", "SET VARIABLE dataset_tag_keep_on_hotstorage=(SELECT value FROM dd_leadership_pub_sbx.data_tiering_service.__configuration WHERE key='dataset_tag_keep_on_hotstorage');\n", "-- ignore some pools\n", "DECLARE OR REPLACE tds_pools_to_consider ARRAY<STRING>;\n", "SET VARIABLE tds_pools_to_consider=(SELECT from_json(value, \"ARRAY<STRING>\") FROM dd_leadership_pub_sbx.data_tiering_service.__configuration WHERE key='tds_pools_to_consider');"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6699f7c2-9584-4adf-b5cb-a6f6bfb57918", "showTitle": false, "title": ""}}, "source": ["# Check keep_on_hotstorage dataset entries structure"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2350d83f-5b18-477c-b08b-d6ae4fec294c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM ${target_catalog}.${target_schema}.keep_on_hotstorage LIMIT 10"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "97b905eb-ff21-4af6-9353-36b59df5dda0", "showTitle": false, "title": ""}}, "source": ["# Check which files on dataset are not found"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fe19cb12-e8b4-4b47-a525-c6b6be5c70e5", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.keep_on_hotstorage_notfound COMMENT 'This table contains all files in a `keep_on_hotstorage` dataset that could not be resolved to a stream.' AS\n", "  WITH CTE AS (\n", "    SELECT \n", "      dataset_entries.dataset_id\n", "      ,dataset_entries.dataset_name\n", "      ,dataset_entries.file_hash\n", "      ,entries.tds_pool\n", "      ,entries.file_extension\n", "      ,entries.content_type\n", "      ,entries.file_name\n", "      ,entries.file_size_bytes\n", "      ,entries.file_state\n", "      ,from_json(dataset_entries.dataset_author:users, 'array<struct<name:string, object_id:string>>') AS dataset_users\n", "    FROM ${target_catalog}.${target_schema}.keep_on_hotstorage AS dataset_entries\n", "    LEFT JOIN ${target_catalog}.${target_schema}.streams_with_extraction_info AS streams\n", "    ON\n", "      dataset_entries.file_hash=streams.file_hash\n", "      OR dataset_entries.file_hash=streams.recording_hash\n", "      OR dataset_entries.file_hash=streams.split_hash\n", "    LEFT JOIN silver.tds.file_entries AS entries\n", "      ON\n", "        dataset_entries.file_hash=entries.file_hash\n", "    WHERE\n", "      streams.file_hash IS NULL\n", "      AND array_contains(tds_pools_to_consider, tds_pool)\n", "  ),\n", "  CTE_EXPLODED_USER AS (\n", "      SELECT\n", "        dataset_id\n", "        ,dataset_name\n", "        ,file_hash\n", "        ,tds_pool\n", "        ,file_extension\n", "        ,content_type\n", "        ,file_name\n", "        ,file_size_bytes\n", "        ,file_state\n", "        ,explode(dataset_users) AS dataset_user\n", "      FROM CTE\n", "  ),\n", "  CTE_USER_NUMBERED AS (\n", "      SELECT\n", "        row_number() OVER (PARTITION BY dataset_id, file_hash ORDER BY dataset_user.object_id ASC) as _row_number\n", "        ,dataset_id\n", "        ,dataset_name\n", "        ,file_hash\n", "        ,tds_pool\n", "        ,file_extension\n", "        ,content_type\n", "        ,file_name\n", "        ,file_size_bytes\n", "        ,file_state\n", "        ,dataset_user.object_id AS dataset_user_object_id\n", "        ,aad_users.display_name AS dataset_user_name\n", "        ,aad_users.ada_domain   AS dataset_domain\n", "        ,aad_users.ada_cluster  AS dataset_cluster\n", "        ,aad_users.ada_team     AS dataset_team\n", "        ,aad_users.ada_role     AS dataset_role\n", "      FROM CTE_EXPLODED_USER\n", "      LEFT JOIN dd_leadership_pub_sbx.data_tiering_service.aad_users AS aad_users\n", "          ON\n", "              aad_users.object_id=CTE_EXPLODED_USER.dataset_user.object_id\n", "  ),\n", "  CTE_SELECTED AS (\n", "      SELECT\n", "        dataset_id\n", "        ,dataset_name\n", "        ,file_hash\n", "        ,tds_pool\n", "        ,file_extension\n", "        ,content_type\n", "        ,file_name\n", "        ,file_size_bytes\n", "        ,file_state\n", "        ,dataset_user_object_id\n", "        ,dataset_user_name\n", "        ,dataset_domain\n", "        ,dataset_cluster\n", "        ,dataset_team\n", "        ,dataset_role\n", "      FROM CTE_USER_NUMBERED\n", "      WHERE _row_number=1\n", "  )\n", "  SELECT * FROM CTE_SELECTED"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "67ca4a7d-3b79-40ca-93fd-92f2c830b8c7", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE ${target_catalog}.${target_schema}.keep_on_hotstorage_notfound ZORDER BY (dataset_id, tds_pool, file_hash)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "31d166ec-01e3-4215-8138-8ea000631c14", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM ${target_catalog}.${target_schema}.keep_on_hotstorage_notfound"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "583515c6-9f25-42d0-889e-caab11a99fe5", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE ${target_catalog}.${target_schema}.keep_on_hotstorage_notfound COMPUTE STATISTICS FOR ALL COLUMNS"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "eaa9080f-e50e-4350-9efd-f4e735d16815", "showTitle": false, "title": ""}}, "source": ["# Analyse results"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5b25f2c1-b7ba-4bf5-82df-9d88a599df8d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM ${target_catalog}.${target_schema}.keep_on_hotstorage_notfound LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4c47aba0-700d-4834-9589-46848ad9a7d1", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM ${target_catalog}.${target_schema}.keep_on_hotstorage_notfound \n", "WHERE file_name IS NULL\n", "LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a45dc9d5-f7c2-4953-9259-af9c39757b6b", "showTitle": false, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "environmentMetadata": {"base_environment": "", "client": "1"}, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "14 Log unmatched dataset entries", "widgets": {"target_catalog": {"currentValue": "dd_leadership_pub_sbx", "nuid": "bc297589-162d-4f97-8cde-ec66e6866c86", "typedWidgetInfo": null, "widgetInfo": {"widgetType": "text", "defaultValue": "dd_leadership_pub_sbx", "label": "", "name": "target_catalog", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "target_schema": {"currentValue": "data_tiering_service", "nuid": "86c0a4d4-0500-4c8a-86f8-398da1cfc205", "typedWidgetInfo": null, "widgetInfo": {"widgetType": "text", "defaultValue": "data_tiering_service", "label": "", "name": "target_schema", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}}}}, "nbformat": 4, "nbformat_minor": 0}