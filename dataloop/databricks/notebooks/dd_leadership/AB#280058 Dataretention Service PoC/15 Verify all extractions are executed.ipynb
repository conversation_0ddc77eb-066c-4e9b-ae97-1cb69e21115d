{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "999ec07e-d200-4752-bfcd-8131e7110166", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- ===================================================================================\n", "--  <PERSON> O P Y R I G H T\n", "-- -----------------------------------------------------------------------------------\n", "--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.\n", "-- =================================================================================== "]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "afe36f97-ef16-4c45-8076-d1ef03a0db5a", "showTitle": false, "title": ""}}, "source": ["# Parameters\n", "\n", "|Name|Description|QA|Prod|\n", "|--|--|--|--|\n", "|catalog|||dd_leadership_pub_sbx|\n", "|schema|||data_tiering_service|"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d52fbcac-2406-4255-a5d2-425a629faf20", "showTitle": false, "title": ""}}, "source": ["# Analyse the Data Extraction Requests"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c005959f-a633-4921-abb3-a7170ac3a922", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT COUNT(*) FROM ${catalog}.${schema}.ds_extraction_requests"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6b67bd86-50f6-4467-92df-90193e1fd24c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM ${catalog}.${schema}.ds_extraction_requests LIMIT 10"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5eb8f920-5f4d-4bf2-956c-a55fce8326a6", "showTitle": false, "title": ""}}, "source": ["# Create stream level extraction request\n", "\n", "The output of this step only includes files that must not be forcefully kept on hotstorage e.g. because they are part of a dataset or are tagged like that in the decision table"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2716efb2-ec7a-4b66-9c77-22e068393d70", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- -- CREATE OR REPLACE TABLE ${catalog}.${schema}._tmp_stream_extraction_request_v2 AS\n", "-- CREATE OR REPLACE TEMPORARY VIEW _tmp_stream_extraction_request_v2 AS\n", "-- SELECT\n", "--   streams.recording_hash\n", "--   ,streams.split_hash\n", "--   ,splits.is_candidate_for_archiving AS split_is_candidate_for_archiving\n", "--   ,streams.stream_hash\n", "--   ,streams.stream_name\n", "--   ,streams.file_hash\n", "--   ,streams.file_source_tds_pool\n", "--   ,streams.file_name\n", "--   ,streams.file_size_bytes\n", "--   ,streams.file_state\n", "--   ,streams.file_source_url\n", "--   ,streams.file_source_tds_fid\n", "--   ,requests.needs_burst_mode_6x5\n", "--   ,requests.needs_burst_mode_1x100\n", "--   ,COALESCE(requests.needs_full_extraction, false) AS needs_full_extraction\n", "-- FROM ${catalog}.${schema}.base_archiving_info_streams AS streams\n", "-- -- only include splits that ar candidates for archiving\n", "-- INNER JOIN ${catalog}.${schema}.base_archiving_info_splits AS splits\n", "--   ON\n", "--     splits.split_hash=streams.split_hash\n", "--     -- AND splits.is_candidate_for_archiving\n", "-- LEFT JOIN ${catalog}.${schema}.ds_extraction_requests AS requests\n", "--   ON\n", "--     requests.file_hash=streams.split_hash\n", "-- -- remove all streams that are not on the valid stream list\n", "-- INNER JOIN ${catalog}.${schema}.valid_streams AS valid_streams\n", "--   ON\n", "--     streams.stream_name=valid_streams.stream_name\n", "-- -- WHERE stream_hash='6f3f9d599c5082ad72940d3e414a379621da66029e9eff42e927c6163c54dff6'\n", "-- -- LIMIT 100"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cc39c189-8e54-4f15-8550-627272ec8609", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${catalog}.${schema}._tmp_stream_extraction_request AS\n", "SELECT \n", "  streams.recording_hash\n", "  ,streams.split_hash\n", "  ,streams.stream_hash\n", "  ,streams.stream_name\n", "  ,streams.file_hash\n", "  ,streams.file_source_tds_pool\n", "  ,streams.file_name\n", "  ,streams.file_size_bytes\n", "  ,streams.file_state\n", "  ,streams.file_source_url\n", "  ,streams.file_source_tds_fid\n", "  ,requests.needs_burst_mode_6x5\n", "  ,requests.needs_burst_mode_1x100\n", "  ,COALESCE(requests.needs_full_extraction, false) AS needs_full_extraction\n", "  -- <PERSON> adds needs_1hz_extraction\n", "FROM ${catalog}.${schema}.ds_extraction_requests AS requests\n", "INNER JOIN ${catalog}.${schema}.base_archiving_info_splits AS splits\n", "  ON\n", "    splits.split_hash=requests.file_hash\n", "    AND splits.is_candidate_for_archiving\n", "INNER JOIN ${catalog}.${schema}.base_archiving_info_streams AS streams\n", "  ON\n", "    requests.file_hash=streams.split_hash\n", "INNER JOIN ${catalog}.${schema}.valid_streams AS valid_streams\n", "  ON\n", "    streams.stream_name=valid_streams.stream_name\n", "WHERE\n", "  -- these files must not be tiered\n", "  NOT requests.additional_keep_on_hot"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5f678732-e2a3-4148-b0ea-0b40c4d2567d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT \n", "  needs_burst_mode_6x5\n", "  ,needs_burst_mode_1x100\n", "  ,COUNT(DISTINCT file_hash)\n", "FROM ${catalog}.${schema}.ds_extraction_requests\n", "GROUP BY \n", "  needs_burst_mode_6x5\n", "  ,needs_burst_mode_1x100\n", "LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8182635e-fb89-417b-b427-d2f4303fea21", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT 'v2' AS What, COUNT(DISTINCT file_hash) AS file_count FROM _tmp_stream_extraction_request_v2 WHERE split_is_candidate_for_archiving=false\n", "-- UNION\n", "-- SELECT 'v1' AS What, COUNT(DISTINCT file_hash) AS file_count FROM ${catalog}.${schema}._tmp_stream_extraction_request"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "542c6559-7490-417b-8280-4d9ac97b663c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE ${catalog}.${schema}._tmp_stream_extraction_request ZORDER BY (split_hash, stream_hash, file_hash);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d9b50f40-3fcc-4387-b22e-4bdae7ca1188", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM ${catalog}.${schema}._tmp_stream_extraction_request"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "16c59847-7261-4986-a648-772ca78957b2", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT COUNT(*) FROM ${catalog}.${schema}._tmp_stream_extraction_request"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "60111162-a028-4333-b106-99894fc4db8e", "showTitle": false, "title": ""}}, "source": ["## Analyze for plausibility"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ac2c0dff-a57a-4b47-980e-6f7c2b4e43d8", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  ROUND(SUM(file_size_bytes)/1024/1024/1024/1024, 0) file_size_terabytes\n", "FROM ${catalog}.${schema}._tmp_stream_extraction_request"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fec83ead-f1b6-408d-a497-7d63e63eedbd", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM ${catalog}.${schema}._tmp_stream_extraction_request LIMIT 10"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "36b988a7-269d-4633-9f3f-aa0c10d1d919", "showTitle": false, "title": ""}}, "source": ["# Collect information about current extraction progress for files"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1699e67d-bbe9-4c2a-862e-d448e9d3e166", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${catalog}.${schema}._tmp_stream_extraction_request_and_state AS\n", "  SELECT\n", "    requests.*\n", "    ,COALESCE(state.has_successful_raw_extraction, false) AS has_successful_raw_extraction\n", "    ,COALESCE(state.has_raw_burst_extraction_6x5, false) AS has_raw_burst_extraction_6x5\n", "    ,COALESCE(state.has_raw_burst_extraction_3x10, false) AS has_raw_burst_extraction_3x10\n", "    ,COALESCE(state.has_raw_burst_extraction_1x100, false) AS has_raw_burst_extraction_1x100\n", "    ,COALESCE(state.has_raw_burst_extraction_1x200, false) AS has_raw_burst_extraction_1x200\n", "    ,COALESCE(state.has_raw_extraction_1hz, false) AS has_raw_extraction_1hz\n", "    ,COALESCE(state.has_raw_extraction_full_rate, false) AS has_raw_extraction_full_rate\n", "  FROM ${catalog}.${schema}._tmp_stream_extraction_request AS requests\n", "  LEFT JOIN ${catalog}.${schema}.extraction_settings_all AS state\n", "    ON\n", "      requests.split_hash=state.split_hash\n", "      AND requests.stream_name=state.stream_name"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8e79ad0d-7774-4820-b53c-6050637f98bc", "showTitle": false, "title": ""}}, "source": ["## Analyze for plausibility"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a908aa62-caf5-4c17-b8c9-a6ed283bb579", "showTitle": false, "title": ""}}, "source": ["### Has at least one successful raw extraction"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e1272732-8ad9-4e6e-9904-df5e070a225c", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"catalog": "dd_leadership_pub_sbx", "schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  has_successful_raw_extraction\n  ,ROUND(SUM(file_size_bytes)/1024/1024/1024/1024, 0) file_size_terabytes\nFROM ${catalog}.${schema}._tmp_stream_extraction_request_and_state \nGROUP BY has_successful_raw_extraction) SELECT `has_successful_raw_extraction`,SUM(`file_size_terabytes`) `column_8a9120ee16` FROM q GROUP BY `has_successful_raw_extraction`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "has_successful_raw_extraction", "id": "column_8a9120ee15"}, "y": [{"column": "file_size_terabytes", "id": "column_8a9120ee16", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"column_8a9120ee16": {"name": "file_size_terabytes", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": true, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "type": "-"}, "yAxis": [{"type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "30c06d95-4c92-4e8b-96f3-df2e2bb508ba", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 5.755859375, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "has_successful_raw_extraction", "type": "column"}], "selects": [{"column": "has_successful_raw_extraction", "type": "column"}, {"alias": "column_8a9120ee16", "args": [{"column": "file_size_terabytes", "type": "column"}], "function": "SUM", "type": "function"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  has_successful_raw_extraction\n", "  ,ROUND(SUM(file_size_bytes)/1024/1024/1024/1024, 0) file_size_terabytes\n", "FROM ${catalog}.${schema}._tmp_stream_extraction_request_and_state \n", "GROUP BY has_successful_raw_extraction"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6d21e056-4103-429d-ad40-fd5ede95093f", "showTitle": false, "title": ""}}, "source": ["### Has at least one successful raw extraction by stream"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fdbc7d76-1599-4f32-8d28-9cf1d489ded4", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"catalog": "dd_leadership_pub_sbx", "schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  stream_name\n  ,has_successful_raw_extraction\n  ,ROUND(SUM(file_size_bytes)/1024/1024/1024/1024, 0) file_size_terabytes\nFROM ${catalog}.${schema}._tmp_stream_extraction_request_and_state \nGROUP BY\n  stream_name\n  ,has_successful_raw_extraction) SELECT `stream_name`,SUM(`file_size_terabytes`) `column_8a9120ee13`,`has_successful_raw_extraction` FROM q GROUP BY `has_successful_raw_extraction`,`stream_name`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"series": {"column": "has_successful_raw_extraction", "id": "column_8a9120ee19"}, "x": {"column": "stream_name", "id": "column_8a9120ee12"}, "y": [{"column": "file_size_terabytes", "id": "column_8a9120ee13", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "isAggregationOn": true, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": "stack"}, "seriesOptions": {"column_8a9120ee13": {"name": "file_size_terabytes", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": true, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "type": "-"}, "yAxis": [{"type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "3d6c0cb5-101d-488b-9ca8-35e4949820ad", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 5.7802734375, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "stream_name", "type": "column"}, {"column": "has_successful_raw_extraction", "type": "column"}], "selects": [{"column": "stream_name", "type": "column"}, {"alias": "column_8a9120ee13", "args": [{"column": "file_size_terabytes", "type": "column"}], "function": "SUM", "type": "function"}, {"column": "has_successful_raw_extraction", "type": "column"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  stream_name\n", "  ,has_successful_raw_extraction\n", "  ,ROUND(SUM(file_size_bytes)/1024/1024/1024/1024, 0) file_size_terabytes\n", "FROM ${catalog}.${schema}._tmp_stream_extraction_request_and_state \n", "GROUP BY\n", "  stream_name\n", "  ,has_successful_raw_extraction"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2f56a2fb-8f33-4bd8-8f7b-4c6a6f2c7242", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM ${catalog}.${schema}._tmp_stream_extraction_request_and_state LIMIT 5"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1de08f38-19c5-4211-b4fa-dee6da1b4f97", "showTitle": false, "title": ""}}, "source": ["# Derive decision of target storage tier of file\n", "\n", "> **WARNING**: The created table can and will contain multiple rows per file hash. This is caused by files with more than two entries within TDS. This happened either due to errors within the ingest or the TDS itself."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ad4753bf-0c9c-4487-ad38-831dab57b123", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${catalog}.${schema}.tiering_decision AS\n", "  WITH CTE_SINGLE_DECISION AS (\n", "    SELECT\n", "      recording_hash\n", "      ,split_hash\n", "      ,stream_hash\n", "      ,stream_name\n", "      ,file_hash\n", "      ,file_source_tds_fid\n", "      ,needs_burst_mode_6x5\n", "      ,has_raw_burst_extraction_6x5\n", "      ,needs_burst_mode_1x100\n", "      ,has_raw_burst_extraction_1x100\n", "      ,has_raw_extraction_1hz\n", "      ,(needs_burst_mode_6x5 = has_raw_burst_extraction_6x5) AS raw_burst_extraction_6x5_fulfilled\n", "      ,(needs_burst_mode_1x100 = has_raw_burst_extraction_1x100) AS raw_burst_extraction_1x100_fulfilled\n", "      ,(has_raw_extraction_1hz) AS raw_extraction_1hz_fulfilled\n", "    FROM ${catalog}.${schema}._tmp_stream_extraction_request_and_state\n", "  ),\n", "  CTE_FINAL_TIERING_DECISION AS (\n", "    SELECT\n", "      *\n", "      ,CASE WHEN (\n", "        raw_burst_extraction_1x100_fulfilled\n", "        AND raw_burst_extraction_6x5_fulfilled\n", "        AND raw_extraction_1hz_fulfilled\n", "      ) THEN 'ARCHIVE' ELSE 'HOT' END AS target_storage_tier\n", "    FROM CTE_SINGLE_DECISION\n", "  )\n", "  SELECT\n", "    *\n", "  FROM CTE_FINAL_TIERING_DECISION\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "be7e6e77-a183-4ae2-b2db-c25d2c2576c0", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  needs_burst_mode_6x5\n", "  ,has_raw_burst_extraction_6x5\n", "  ,COUNT(distinct stream_hash)\n", "FROM ${catalog}.${schema}.tiering_decision\n", "WHERE\n", "  target_storage_tier='HOT'\n", "  and needs_burst_mode_1x100\n", "  and has_raw_burst_extraction_1x100\n", "GROUP BY\n", "  needs_burst_mode_6x5\n", "  ,has_raw_burst_extraction_6x5\n", "LIMIT 100"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d6f6ce59-55d0-46db-b782-b95ace371e13", "showTitle": false, "title": ""}}, "outputs": [], "source": ["COMMENT ON TABLE ${catalog}.${schema}.tiering_decision IS 'This table includes the target storage tier per stream file. The files are referenced by their TDS fid, i.e. ``file_source_tds_fid``. The decision is provided in the column ``target_storage_tier``. The target storage tier is either ``ARCHIVE`` or ``HOT``. Beware that per ``file_hash`` multiple files can exist in TDS. '"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c1e13b95-a16f-47e1-9b96-00b5554b7991", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE ${catalog}.${schema}.tiering_decision ZORDER BY (file_hash);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5de0a4af-90e8-43e6-a656-8dfc4bd1387e", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM ${catalog}.${schema}.tiering_decision;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "63f11f83-d01a-41d9-b253-b41eec394f10", "showTitle": false, "title": ""}}, "source": ["# Execute sanity checks on tiering decision table"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b0f1fe25-3f68-4aae-8b8a-e5137950684f", "showTitle": false, "title": ""}}, "source": ["### Check for duplicates"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b5149b23-bd34-46eb-b4e8-5ab6a0d90915", "showTitle": false, "title": ""}}, "outputs": [], "source": [";WITH CTE AS (\n", "  SELECT\n", "    file_hash, file_source_tds_fid, COUNT(*)\n", "  FROM ${catalog}.${schema}.tiering_decision\n", "  GROUP BY file_hash, file_source_tds_fid\n", "  HAVING COUNT(*) > 1\n", ")\n", "SELECT assert_true(count(*)=0) FROM CTE"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fa5ca698-1daa-4f36-b6f9-3283c827fbd2", "showTitle": false, "title": ""}}, "source": ["### Check if file was previously noted to be within a keep_on_hot_storage dataset"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ee1ccca1-5f71-4b16-b0a0-87ac8c0389d9", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW verify_tiering_decision AS\n", "  SELECT\n", "    tiering_decision.recording_hash, tiering_decision.split_hash, tiering_decision.stream_hash, tiering_decision.file_hash, tiering_decision.file_source_tds_fid\n", "    ,tiering_decision.target_storage_tier\n", "    ,base_archiving_info_streams.file_in_hotstorage_dataset\n", "    ,base_archiving_info_streams.is_candidate_for_archiving AS stream_is_candidate_for_archiving\n", "    ,base_archiving_info_splits.is_candidate_for_archiving AS split_is_candidate_for_archiving\n", "  FROM ${catalog}.${schema}.tiering_decision\n", "  LEFT JOIN ${catalog}.${schema}.base_archiving_info_streams\n", "    ON\n", "      base_archiving_info_streams.file_hash=tiering_decision.file_hash\n", "  LEFT JOIN ${catalog}.${schema}.base_archiving_info_splits\n", "    ON\n", "      tiering_decision.split_hash=base_archiving_info_splits.split_hash"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e7a07186-1881-4cff-a9bc-6d94bfe474cc", "showTitle": false, "title": ""}}, "source": ["#### Check if the files are not a match on any of the previously used tiering candidate tables\n", "\n", "The result should be empty."]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "42ee016a-0c90-48ec-b9df-54476d60465e", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT assert_true(count(*) = 0) FROM verify_tiering_decision\n", "WHERE\n", "  stream_is_candidate_for_archiving IS NULL\n", "  OR split_is_candidate_for_archiving IS NULL"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f86ef3d7-0084-4426-b478-509b7b1f6151", "showTitle": false, "title": ""}}, "source": ["#### Check if files that are on the HOT list are kept on HOT"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6cdf0702-14a7-4992-bba9-1906f630f56b", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT assert_true(count(*) = 0) FROM verify_tiering_decision\n", "WHERE\n", "  target_storage_tier='ARCHIVE'\n", "  AND file_in_hotstorage_dataset\n", "  AND NOT stream_is_candidate_for_archiving\n", "  AND NOT split_is_candidate_for_archiving\n", "LIMIT 10"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7f3a02f5-3078-4290-90ba-df34d0f5e3c2", "showTitle": false, "title": ""}}, "source": ["# Execute analysis"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d91ff243-ce8c-4ad3-be19-9f9af8be835a", "showTitle": false, "title": ""}}, "source": ["## Number of files per storage tier"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2934c7d2-01f6-4f65-98af-551d5583cb2c", "showTitle": false, "title": ""}, "jupyter": {"source_hidden": true}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"catalog": "dd_leadership_pub_sbx", "schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  target_storage_tier\n  ,COUNT(DISTINCT file_source_tds_fid) AS number_of_files\nFROM ${catalog}.${schema}.tiering_decision\nGROUP BY\n  target_storage_tier) SELECT `target_storage_tier`,`number_of_files` FROM q", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "target_storage_tier", "id": "column_8a9120ee6"}, "y": [{"column": "number_of_files", "id": "column_8a9120ee7"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "pie", "isAggregationOn": false, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"number_of_files": {"name": "Number of files", "type": "pie", "yAxis": 0}}, "showDataLabels": true, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": false, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "type": "-"}, "yAxis": [{"type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "435ccb06-3cb9-4a19-bd6d-c2bdf1e6c0d9", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 15.375, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"selects": [{"column": "target_storage_tier", "type": "column"}, {"column": "number_of_files", "type": "column"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  target_storage_tier\n", "  ,COUNT(DISTINCT file_source_tds_fid) AS number_of_files\n", "FROM ${catalog}.${schema}.tiering_decision\n", "GROUP BY\n", "  target_storage_tier"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "22d22269-b350-4852-9119-66a80d5e647a", "showTitle": false, "title": ""}}, "source": ["## Number of files per storage tier and stream name"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a2bae48e-47b5-4bcd-8454-061ff3b35854", "showTitle": false, "title": ""}, "jupyter": {"source_hidden": true}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"catalog": "dd_leadership_pub_sbx", "schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  stream_name\n  ,target_storage_tier\n  ,COUNT(DISTINCT file_source_tds_fid) AS number_of_files\nFROM ${catalog}.${schema}.tiering_decision\nGROUP BY\n  stream_name\n  ,target_storage_tier) SELECT `stream_name`,`target_storage_tier`,`number_of_files` FROM q", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": false, "coefficient": 1, "columnConfigurationMap": {"series": {"column": "target_storage_tier", "id": "column_8a9120ee2"}, "x": {"column": "stream_name", "id": "column_8a9120ee1"}, "y": [{"column": "number_of_files", "id": "column_8a9120ee3"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "isAggregationOn": false, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "percentValues": false, "stacking": "stack"}, "seriesOptions": {"column_8a9120ee3": {"name": "number_of_files", "type": "column", "yAxis": 0}, "number_of_files": {"name": "number_of_files", "type": "column", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": false, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "title": {"text": "Stream Name"}, "type": "-"}, "yAxis": [{"rangeMin": 0, "title": {"text": "Number of TDS files"}, "type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "9d5efe1b-5680-496f-9fae-9a973e0541d6", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 16.0, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"selects": [{"column": "stream_name", "type": "column"}, {"column": "target_storage_tier", "type": "column"}, {"column": "number_of_files", "type": "column"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  stream_name\n", "  ,target_storage_tier\n", "  ,COUNT(DISTINCT file_source_tds_fid) AS number_of_files\n", "FROM ${catalog}.${schema}.tiering_decision\n", "GROUP BY\n", "  stream_name\n", "  ,target_storage_tier\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "dd522025-5aa7-4b2b-b732-add3ec5897b6", "showTitle": false, "title": ""}}, "source": ["## Data volume to per storage tier"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fe29f0e2-30e8-4b50-b3b9-5e63888ad052", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"catalog": "dd_leadership_pub_sbx", "schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  tiering_decision.target_storage_tier AS `Target Storage Tier`\n  ,ROUND(SUM(file_entries.file_size_bytes)/1024/1024/1024/1024, 0) AS TiB\nFROM ${catalog}.${schema}.tiering_decision\nLEFT JOIN silver.tds.file_entries\n  ON\n    file_entries.file_hash=tiering_decision.file_hash\nGROUP BY\n  tiering_decision.target_storage_tier) SELECT `Target Storage Tier`,SUM(`TiB`) `column_8a9120ee10` FROM q GROUP BY `Target Storage Tier`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "Target Storage Tier", "id": "column_8a9120ee9"}, "y": [{"column": "TiB", "id": "column_8a9120ee10", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"column_8a9120ee10": {"name": "TiB", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": true, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "type": "-"}, "yAxis": [{"type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "942680f3-2605-469c-ac87-6b71fab18910", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 18.0, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "Target Storage Tier", "type": "column"}], "selects": [{"column": "Target Storage Tier", "type": "column"}, {"alias": "column_8a9120ee10", "args": [{"column": "TiB", "type": "column"}], "function": "SUM", "type": "function"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  tiering_decision.target_storage_tier AS `Target Storage Tier`\n", "  ,ROUND(SUM(file_entries.file_size_bytes)/1024/1024/1024/1024, 0) AS TiB\n", "FROM ${catalog}.${schema}.tiering_decision\n", "LEFT JOIN silver.tds.file_entries\n", "  ON\n", "    file_entries.file_hash=tiering_decision.file_hash\n", "GROUP BY\n", "  tiering_decision.target_storage_tier"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b41b642f-4e5a-41bd-af4d-68d4ac487496", "showTitle": false, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "environmentMetadata": {"base_environment": "", "client": "1"}, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "15 Verify all extractions are executed", "widgets": {"catalog": {"currentValue": "dd_leadership_pub_sbx", "nuid": "da3c3b5d-65d7-4ff4-97ad-863780cd5df6", "typedWidgetInfo": null, "widgetInfo": {"widgetType": "text", "defaultValue": "dd_leadership_pub_sbx", "label": "", "name": "catalog", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "schema": {"currentValue": "data_tiering_service", "nuid": "b169b6ba-aaf0-493d-a21e-d6e7fea5abf2", "typedWidgetInfo": null, "widgetInfo": {"widgetType": "text", "defaultValue": "data_tiering_service", "label": "", "name": "schema", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}}}}, "nbformat": 4, "nbformat_minor": 0}