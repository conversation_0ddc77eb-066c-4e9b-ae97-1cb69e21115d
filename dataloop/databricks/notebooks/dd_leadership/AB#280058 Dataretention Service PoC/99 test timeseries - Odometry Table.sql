-- Databricks notebook source
-- MAGIC %md
-- MAGIC # Create dedicated table for odometry support

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.events_odometry AS
  WITH TIME_BINNING AS (
    SELECT
      recording_hash, split_hash, started_at
      ,min(value) FILTER (WHERE name = 'KBI_Kilometerstand') AS KBI_Kilometerstand
      ,min(value) FILTER (WHERE name = 'ESP_v_ref') AS ESP_v_ref
      ,min(value) FILTER (WHERE name = 'ESP_v_ref_Fahrtrichtung') AS ESP_v_ref_Fahrtrichtung
      ,min(value_extra) FILTER (WHERE name = 'ESP_v_ref_Fahrtrichtung') AS ESP_v_ref_Fahrtrichtung_extra
      ,min(value) FILTER (WHERE name = 'BR_Eingriffsmoment') AS BR_Eingriffsmoment
      ,min(value) FILTER (WHERE name = 'EML_GierRate') AS EML_GierRate
      ,min(value) FILTER (WHERE name = 'EML_LenkWnklVL') AS EML_LenkWnklVL
      ,min(value) FILTER (WHERE name = 'EML_LenkWnklVR') AS EML_LenkWnklVR

    FROM dd_leadership_pub_sbx.timeseries.events_flexray_time_binned
    GROUP BY recording_hash, split_hash, started_at
  ),
  FIX_ODOMETRY_JOIN_NEXT_BEST AS (
    SELECT
      row_number() OVER (PARTITION BY base.recording_hash, base.started_at ORDER BY next.started_at ASC) AS _row_number_odo_fix
      ,base.*
      ,next.started_at AS next_started_at
      ,next.KBI_Kilometerstand AS next_KBI_Kilometerstand
    FROM TIME_BINNING AS base
    LEFT JOIN TIME_BINNING AS next
      ON
        next.recording_hash=base.recording_hash
        AND next.started_at > base.started_at
        -- at maximum look 10 seconds into the future to make the execution predictable
        AND next.started_at <= TIMESTAMPADD(SECOND, 10, base.started_at)
        AND COALESCE(next.KBI_Kilometerstand,0) > 0
  ),
  CLEANSED_BASE_TABLE AS (
    SELECT
      row_number() OVER (PARTITION BY recording_hash ORDER BY started_at ASC) AS _row_number
      ,recording_hash
      ,split_hash
      ,started_at
      ,CASE WHEN KBI_Kilometerstand > 0 THEN KBI_Kilometerstand ELSE next_KBI_Kilometerstand END AS KBI_Kilometerstand
      ,ESP_v_ref
      ,ESP_v_ref_Fahrtrichtung
      ,ESP_v_ref_Fahrtrichtung_extra
      ,(ESP_v_ref / 3.6) AS v
      ,BR_Eingriffsmoment
      ,EML_GierRate
      ,EML_LenkWnklVL
      ,EML_LenkWnklVR
    FROM FIX_ODOMETRY_JOIN_NEXT_BEST
    WHERE
      _row_number_odo_fix=1
  ),
  NEXT_TIME_STAMP AS (
    SELECT
      LEAD(started_at) IGNORE NULLS OVER (PARTITION BY recording_hash ORDER BY `_row_number` ASC) AS started_at_next
      ,*
    FROM CLEANSED_BASE_TABLE
  ), 
  ADD_TIME_DELTA AS (
    SELECT
      *
      ,CASE WHEN started_at_next IS NULL THEN NULL ELSE (DATEDIFF(MILLISECOND, started_at, started_at_next) / 1000.) END AS dt
    FROM NEXT_TIME_STAMP
  ),
  TIMEBIN_DISTANCE_DRIVEN AS (
    SELECT
      *
      ,dt * v AS ds
    FROM ADD_TIME_DELTA
  ),
  DISTANCE_INTEGRAL AS (
    SELECT
      SUM(ds) OVER (PARTITION BY recording_hash ORDER BY `_row_number` ASC) AS distance
      ,*
    FROM TIMEBIN_DISTANCE_DRIVEN
  )
  SELECT
    recording_hash
    ,split_hash
    ,started_at
    ,KBI_Kilometerstand
    ,ESP_v_ref
    ,ESP_v_ref_Fahrtrichtung
    ,ESP_v_ref_Fahrtrichtung_extra
    ,BR_Eingriffsmoment
    ,EML_GierRate
    ,EML_LenkWnklVL
    ,EML_LenkWnklVR
    ,v
    ,dt
    ,ds
    ,distance AS recording_distance
  FROM DISTANCE_INTEGRAL

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Anotate units

-- COMMAND ----------

-- Comment on table
COMMENT ON TABLE dd_leadership_pub_sbx.timeseries.events_odometry IS 'Contains odometry data for a recording within a 1 second binning. This contains the raw bus signals of the odometer and the esp as well as a derived finer granular distance based on the velocity measured by the ESP system.';

-- Comment on single columns
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN recording_hash COMMENT 'Unique SHA2-256bit of the recording file (gmdm.json) the data belongs to.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN split_hash COMMENT 'Unique SHA2-256bit of the split file (*.scene) the data belongs to.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN started_at COMMENT 'Timestamp of the measurement contained in this row';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN KBI_Kilometerstand COMMENT 'Ego-Vehicle odometer value provided by the Kombiinstrument as recorded from the bus.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ESP_v_ref COMMENT 'Ego-Vehicle velocity provided by ESP as recorded from the bus.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ESP_v_ref_Fahrtrichtung COMMENT 'Numerical representation of the ego-mehicle movement direction provided by ESP as recorded from the bus.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ESP_v_ref_Fahrtrichtung_extra COMMENT 'Human readable representation of the ego-vehicle movement direction provided by ESP as recorded from the bus.';

ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN BR_Eingriffsmoment COMMENT 'Ego-Vehicle brake torque provided by ESP as recorded from the bus.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN EML_GierRate COMMENT 'Ego-Vehicle yaw rate provided by EML as recorded from the bus.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN EML_LenkWnklVL COMMENT 'Ego-Vehicle steering angle of left front wheel provided by EML as recorded from the bus.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN EML_LenkWnklVR COMMENT 'Ego-Vehicle steering angle of right front wheel provided by EML as recorded from the bus.';

ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN v COMMENT 'Ego-vehicle velocity in m/s derived from the best available data source.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN dt COMMENT 'Time difference between the last measurement and this measurement. Should be one second.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ds COMMENT 'Ego-vehicle distance moved since last measurement.';
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN recording_distance COMMENT 'Ego-vehicle total distance travelled during this recording.';

-- Set responsibility tags for table
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry SET TAGS ('responsible_domain'='Data Delivery', 'responsible_team'='Domain Leadership');
-- Set unity/measurement system defining tags
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN started_at SET TAGS ('timesystem' = 'TAI');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN KBI_Kilometerstand SET TAGS ('units' = 'km','source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ESP_v_ref SET TAGS ('units' = 'km/h','source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ESP_v_ref_Fahrtrichtung SET TAGS ('units'='enum', 'source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ESP_v_ref_Fahrtrichtung_extra SET TAGS ('units'='enum', 'source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN BR_Eingriffsmoment SET TAGS ('units'='UNKNOWN', 'source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN EML_GierRate SET TAGS ('units'='rad/s', 'source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN EML_LenkWnklVL SET TAGS ('units'='rad', 'source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN EML_LenkWnklVR SET TAGS ('units'='rad', 'source'='Flexray');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN v SET TAGS ('units' = 'm/s','source'='computed');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN dt SET TAGS ('units' = 's','source'='computed');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN ds SET TAGS ('units' = 'm','source'='computed');
ALTER TABLE dd_leadership_pub_sbx.timeseries.events_odometry ALTER COLUMN recording_distance SET TAGS ('units' = 'm','source'='computed');

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Optimize and clean-up table

-- COMMAND ----------

OPTIMIZE dd_leadership_pub_sbx.timeseries.events_odometry ZORDER BY (recording_hash, split_hash, started_at)

-- COMMAND ----------

ANALYZE TABLE dd_leadership_pub_sbx.timeseries.events_odometry COMPUTE STATISTICS FOR ALL COLUMNS

-- COMMAND ----------

VACUUM dd_leadership_pub_sbx.timeseries.events_odometry
