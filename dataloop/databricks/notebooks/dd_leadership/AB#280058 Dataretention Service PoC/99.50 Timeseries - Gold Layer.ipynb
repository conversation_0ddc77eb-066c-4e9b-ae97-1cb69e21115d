{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c555c2c1-3633-411e-a271-328a4af257a2", "showTitle": false, "title": ""}}, "source": ["# Timeseries Recording Database\n", "\n", "<PERSON><PERSON> howto create a time series recording database"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "43fc24cc-7539-4276-91c6-f258bb5dc426", "showTitle": false, "title": ""}}, "source": ["## The Recording Database"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a63cc706-0424-4176-be2d-6d66cc40f793", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.recordings (\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.recordings (\n", "  recording_id BIGINT GENERATED ALWAYS AS IDENTITY ( START WITH 0 INCREMENT BY 1 ) COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  started_at TIMESTAMP NOT NULL COMMENT 'UTC timestamp of the recording start',\n", "  ended_at TIMESTAMP COMMENT 'UTC timestamp of the recording end',\n", "  recording_mdm_hash STRING NOT NULL COMMENT 'MDM hash to the head of the recording',\n", "  recording_tds_pool STRING NOT NULL COMMENT 'TDS Pool the recording is stored at, e.g. g3vprdaq for the data acquistion drived or g3closedloop for the SIPA drives.',\n", "  valid BOOLEAN COMMENT 'Show the result of the ingest validation service. In case the ingest is invalid, i.e. valid==false, most if not all data enrichments are not executed. If no ingest validation information is available or an unknown status was returned, this field is null.'\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8b91e843-6fa7-4e11-bdc1-a6c99f5c6967", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- alter table dd_leadership_pub_sbx.timeseries.recordings add columns (recording_tds_pool STRING COMMENT 'TDS Pool the recording is stored at, e.g. g3vprdaq for the data acquistion drived or g3closedloop for the SIPA drives.');\n", "-- alter table dd_leadership_pub_sbx.timeseries.recordings alter column valid comment 'Show the result of the ingest validation service. In case the ingest is invalid, i.e. valid==false, most if not all data enrichments are not executed. If no ingest validation information is available or an unknown status was returned, this field is null.';"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c3d9af2c-0835-4829-8a85-42fb33b0fdac", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW ingest_validation_result AS\n", "select\n", "  file_hash AS recording_hash\n", "  ,UPPER(json_document:status) AS ingest_status\n", "from bronze.mdm.mdd_namespaces_latest\n", "where namespace_name='ingest-validation-result'\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ff299270-76cb-4402-b27e-42ee88a4d273", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW recordings AS\n", "  WITH RECORDINGS (\n", "    SELECT\n", "      recording_hash\n", "      ,max(recording_tds_pool) as recording_tds_pool\n", "    FROM dd_leadership_pub_sbx.data_tiering_service.all_recordings\n", "    GROUP BY recording_hash\n", "  ),\n", "  ENRICHED AS (\n", "    SELECT\n", "      drive_info.json_document:vehicle_model:VIN::STRING AS vin\n", "      ,drive_info.json_document:vehicle_model:plate::STRING AS license_plate\n", "      ,recording_info.json_document:start_time_utc::TIMESTAMP AS started_at\n", "      ,recording_info.json_document:end_time_utc::TIMESTAMP AS ended_at\n", "      ,recordings.recording_hash AS recording_mdm_hash\n", "      ,recordings.recording_tds_pool AS recording_tds_pool\n", "      ,CASE \n", "        WHEN ingest_validation_result.ingest_status IS NULL THEN NULL\n", "        WHEN ingest_validation_result.ingest_status='VALID' THEN true\n", "        WHEN ingest_validation_result.ingest_status='INVALID' THEN false\n", "        ELSE NULL\n", "      END AS valid\n", "    FROM RECORDINGS AS recordings\n", "    LEFT JOIN bronze.mdm.mdd_namespaces_latest AS drive_info\n", "      ON\n", "        drive_info.file_hash=recordings.recording_hash\n", "        AND drive_info.namespace_name='datamanagement-drive-info'\n", "    LEFT JOIN bronze.mdm.mdd_namespaces_latest AS recording_info\n", "      ON\n", "        recording_info.file_hash=recordings.recording_hash\n", "        AND recording_info.namespace_name='datamanagement-recording-info'\n", "    LEFT JOIN ingest_validation_result\n", "      ON\n", "        ingest_validation_result.recording_hash=recordings.recording_hash\n", "  )\n", "  SELECT\n", "    DAY(started_at) + 100*MONTH(started_at) + 100*100*YEAR(started_at) AS partition_date\n", "    ,*\n", "  FROM ENRICHED\n", "  WHERE\n", "    -- these records should be put into a quarantain log\n", "    vin IS NOT NULL\n", "    AND license_plate IS NOT NULL\n", "    AND started_at IS NOT NULL\n", "    AND recording_mdm_hash IS NOT NULL\n", "  ;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a0768a6e-73e1-41be-8276-77791a214b54", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT * FROM recordings \n", "-- LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "89aae7a7-fe06-43f4-a2ec-ae7e6cc3286e", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.recordings AS target\n", "  USING recordings AS source\n", "  ON\n", "    source.partition_date=target.partition_date\n", "    AND source.vin=target.vin\n", "    AND source.started_at=target.started_at\n", "    AND source.recording_mdm_hash=target.recording_mdm_hash\n", "  WHEN MATCHED THEN UPDATE SET \n", "    target.license_plate=source.license_plate\n", "    ,target.ended_at=source.ended_at\n", "    ,target.valid=source.valid\n", "    ,target.recording_tds_pool=source.recording_tds_pool\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT \n", "    (partition_date, vin, license_plate, started_at, ended_at, recording_mdm_hash) \n", "    VALUES (source.partition_date, source.vin, source.license_plate,\n", "      source.started_at, source.ended_at, source.recording_mdm_hash\n", "      )"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b161dcc6-525f-42b6-bc7b-2adf177e8fa5", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.recordings"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bed01614-b59b-468f-80d3-024a47458baa", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.recordings;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3d15f87f-a69d-431a-8547-e582e6aa805d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.recordings COMPUTE STATISTICS FOR ALL COLUMNS;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a7119072-62e4-4716-87c4-8a703328ab1f", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {}, "collapsed": false, "command": "%sql WITH q AS (with cte as (\n  select\n    recording_tds_pool\n    ,sum(case when valid then 1 else 0 end) AS valid_recordings\n    ,sum(case when not valid then 1 else 0 end) as invalid_recordings\n    ,sum(case when valid is null then 1 else 0 end) as non_checked_recordings\n  from dd_leadership_pub_sbx.timeseries.recordings\n  group by recording_tds_pool\n)\nselect\n*\nfrom cte UNPIVOT INCLUDE NULLS (\n  value FOR what IN (\n      valid_recordings,\n      invalid_recordings,\n      non_checked_recordings)\n)) SELECT `recording_tds_pool`,`what`,SUM(`value`) `column_1db4eab63` FROM q GROUP BY `recording_tds_pool`,`what`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"series": {"column": "what", "id": "column_1db4eab62"}, "x": {"column": "recording_tds_pool", "id": "column_1db4eab61"}, "y": [{"column": "value", "id": "column_1db4eab63", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "isAggregationOn": true, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": "stack"}, "seriesOptions": {"column_1db4eab63": {"name": "value", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": true, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "type": "-"}, "yAxis": [{"type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "c6c82b44-44fe-40e8-9926-f5305a4d6ce9", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 4.5, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "recording_tds_pool", "type": "column"}, {"column": "what", "type": "column"}], "selects": [{"column": "recording_tds_pool", "type": "column"}, {"column": "what", "type": "column"}, {"alias": "column_1db4eab63", "args": [{"column": "value", "type": "column"}], "function": "SUM", "type": "function"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["with cte as (\n", "  select\n", "    recording_tds_pool\n", "    ,sum(case when valid then 1 else 0 end) AS valid_recordings\n", "    ,sum(case when not valid then 1 else 0 end) as invalid_recordings\n", "    ,sum(case when valid is null then 1 else 0 end) as non_checked_recordings\n", "  from dd_leadership_pub_sbx.timeseries.recordings\n", "  group by recording_tds_pool\n", ")\n", "select\n", "*\n", "from cte UNPIVOT INCLUDE NULLS (\n", "  value FOR what IN (\n", "      valid_recordings,\n", "      invalid_recordings,\n", "      non_checked_recordings)\n", ")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b7f27188-38f7-4e09-8bf8-e9b9b4450484", "showTitle": false, "title": ""}}, "source": ["## Database of files in a recording"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "82dbcc1e-d490-4b74-94f3-ca5323a6b1e3", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.recording_files (\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.recording_files (\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  stream STRING NOT NULL COMMENT 'File stream name',\n", "  file_started_at TIMESTAMP NOT NULL COMMENT 'UTC timestamp of the recording start',\n", "  file_ended_at TIMESTAMP COMMENT 'UTC timestamp of the recording end',\n", "  file_hash STRING NOT NULL COMMENT 'MDM file hash',\n", "  file_name STRING NOT NULL COMMENT 'File name',\n", "  file_content_type STRING NOT NULL COMMENT 'Content type of file',\n", "  file_size_bytes BIGINT NOT NULL COMMENT 'File size',\n", "  file_sources array<struct<name:string,url:string,contentType:string,createdTime:string,provider:string,location:string,type:string,state:string>> NOT NULL COMMENT 'File sources',\n", "  file_state STRING NOT NULL COMMENT 'State of the file: An **ACTIVE** file is on hot storage and can be accessed, an **ARCHIVED** file is on archive and cannot directly be accessed (please contact storage admin and bring money)',\n", "  tds_file_url STRING NOT NULL COMMENT 'A direct link to the file on TDS blob storage'\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "325e1171-051a-4453-a867-e867f14f77cc", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- ALTER TABLE dd_leadership_pub_sbx.timeseries.recording_files ADD COLUMNS (\n", "--   file_state STRING COMMENT 'State of the file: An **ACTIVE** file is on hot storage and can be accessed, an **ARCHIVED** file is on archive and cannot directly be accessed (please contact storage admin and bring money)',\n", "--   tds_file_url STRING COMMENT 'A direct link to the file on TDS blob storage'\n", "-- )"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0338745f-c099-495e-abc9-5691b3bcdfc9", "showTitle": false, "title": ""}}, "outputs": [], "source": ["  -- SELECT\n", "  --   *\n", "  -- FROM dd_leadership_pub_sbx.data_tiering_service.all_recordings\n", "  -- WHERE stream_hash IS NULL AND split_hash IS NOT NULL\n", "  -- LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "de920a81-f02f-4a24-acd4-1c92f3e18bdf", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW splits AS\n", "  WITH SPLITS AS (\n", "    SELECT\n", "      recording_hash, split_hash\n", "    FROM dd_leadership_pub_sbx.data_tiering_service.all_recordings\n", "    WHERE endswith(file_name, \".scene\")\n", "    GROUP BY recording_hash, split_hash\n", "  ),\n", "  SPLIT_TIME AS (\n", "    SELECT\n", "      splits.recording_hash\n", "      ,splits.split_hash\n", "      ,split_info.json_document:start_time_utc::TIMESTAMP   AS split_started_at\n", "      ,split_info.json_document:end_time_utc::TIMESTAMP     AS split_ended_at\n", "    FROM SPLITS\n", "    LEFT JOIN bronze.mdm.mdd_namespaces_latest AS split_info\n", "      ON\n", "        split_info.file_hash=splits.split_hash\n", "        AND split_info.namespace_name='datamanagement-split-info'\n", "  ),\n", "  SPLIT_TIME_CLEANSED AS (\n", "    SELECT\n", "      row_number() OVER (PARTITION BY current_split.recording_hash, current_split.split_hash ORDER BY COALESCE(current_split.split_ended_at,next_split.split_started_at) ASC) AS _row_number\n", "      ,current_split.recording_hash\n", "      ,current_split.split_hash\n", "      ,current_split.split_started_at\n", "      ,COALESCE(current_split.split_ended_at, next_split.split_started_at) AS split_ended_at\n", "      ,current_split.split_ended_at AS c_ended_at\n", "      ,next_split.split_started_at AS n_started_at\n", "    FROM SPLIT_TIME AS current_split\n", "    LEFT JOIN SPLIT_TIME AS next_split\n", "      ON\n", "        next_split.recording_hash=current_split.recording_hash\n", "        AND next_split.split_started_at > COALESCE(current_split.split_ended_at, current_split.split_Started_at)\n", "  )\n", "  SELECT\n", "    recording_hash\n", "    ,split_hash\n", "    ,split_started_at\n", "    ,split_ended_at\n", "  FROM SPLIT_TIME_CLEANSED\n", "  WHERE\n", "    _row_number=1\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7b21d34d-8d2c-452f-bbc7-7d4e378b4378", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW recording_files AS \n", "  WITH RECORDINGS AS (\n", "    SELECT\n", "      * \n", "    FROM dd_leadership_pub_sbx.timeseries.recordings\n", "  ),\n", "  CTE AS (\n", "    SELECT\n", "    recordings.recording_id                                 AS recording_id\n", "      ,recordings.partition_date                            AS partition_date\n", "      ,recordings.vin                                       AS vin\n", "      ,recordings.license_plate                             AS license_plate\n", "      ,files.stream_name                                    AS stream\n", "      ,splits.split_started_at                              AS file_started_at\n", "      ,splits.split_ended_at                                AS file_ended_at\n", "      ,files.file_hash                                      AS file_hash\n", "      ,files.file_name                                      AS file_name\n", "      ,files.file_extension                                 AS file_extension\n", "      ,files.file_content_type                              AS file_content_type\n", "      ,files.file_size_bytes                                AS file_size_bytes\n", "      ,files.file_sources                                   AS file_sources\n", "      ,file_entries.file_state                              AS file_state\n", "      ,file_entries.tds_file_url                            AS tds_file_url\n", "    FROM RECORDINGS AS recordings\n", "    INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings AS files\n", "      ON\n", "        files.recording_hash=RECORDINGS.recording_mdm_hash\n", "        -- AND files.stream_hash IS NOT NULL\n", "    -- ensure that badly hanging lapi labeling requests are removed\n", "    LEFT OUTER JOIN bronze.mdm.mdd_namespaces_latest AS send_to_lapi\n", "      ON\n", "        files.file_hash=send_to_lapi.file_hash\n", "        AND send_to_lapi.namespace_name='lapi'\n", "    -- get time information from splits\n", "    INNER JOIN splits\n", "      ON\n", "        splits.recording_hash=files.recording_hash\n", "        AND splits.split_hash=files.split_hash\n", "    -- get file state\n", "    INNER JOIN silver.tds.file_entries AS file_entries\n", "      ON\n", "        file_entries.file_hash=files.file_hash\n", "  )\n", "  SELECT\n", "    *\n", "  FROM CTE\n", "  WHERE\n", "    -- these files must be in\n", "    CTE.file_extension IN ('split', 'cseq')\n", "    OR CTE.file_name IN ('gmdm.json')\n", "    OR (\n", "      -- should go to a deadletter/review table\n", "      NOT (stream IS NULL OR trim(stream) = '')\n", "      AND NOT (file_started_at IS NULL)\n", "    )\n", "  -- WHERE namespace.namespace_name NOT IN ('lapi', 'dsp_sequence_deduplicator', 'ns-reference', 'deep-execution', 'datamanagement-split-info', 'datamanagement-recording-info', 'datamanagement-drive-info', 'dsp_de_split_extracted', 'dsp_selection', 'dsp_sequence_info', 'goc_top_dense_range_map')\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3c93733b-873b-4a2a-bd14-a600170b0961", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT * FROM dd_leadership_pub_sbx.data_tiering_service.all_recordings WHERE file_name='gmdm.json' LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3ef0976e-fd2f-4bc1-8021-3ed256111089", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT * FROM recording_files WHERE endswith(file_name, '.scene') LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "477b416b-500f-4386-8e49-5bc64c2203d3", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.recording_files AS target\n", "  USING recording_files  AS source\n", "  ON\n", "    source.recording_id=target.recording_id\n", "    -- that is redundant to recording_id and does not help for faster joining\n", "    -- AND source.partition_date=target.partition_date\n", "    -- AND source.vin=target.vin\n", "    AND source.file_started_at=target.file_started_at\n", "    AND source.file_hash=target.file_hash\n", "  WHEN MATCHED \n", "    AND (\n", "      source.tds_file_url<>target.tds_file_url\n", "      OR source.file_state<>target.file_state\n", "    )\n", "    THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "58dad773-0f9b-4a49-a981-6d11653dd707", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.recording_files ZORDER BY (recording_id, file_started_at);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "09afbf37-c01e-4157-b335-8ae8d7c92b51", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.recording_files;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0344280b-c12b-4e78-a3e6-397beae96ee1", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.recording_files COMPUTE STATISTICS FOR COLUMNS\n", "  recording_id,\n", "  partition_date,\n", "  vin,\n", "  license_plate,\n", "  stream,\n", "  file_started_at,\n", "  file_ended_at,\n", "  file_hash,\n", "  file_name,\n", "  file_content_type,\n", "  file_size_bytes,\n", "  file_state\n", ";"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "563759e7-d6cd-4832-b6b0-d04d11e06f29", "showTitle": false, "title": ""}}, "source": ["## Create Location Event table"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bc58fc67-2f62-487c-bc41-f00e3afc0c29", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW gps_coordinates AS\n", "  WITH RECORDINGS AS (\n", "      SELECT\n", "        * \n", "      FROM dd_leadership_pub_sbx.timeseries.recordings\n", "    ),\n", "    GPS_RAW AS (\n", "      SELECT\n", "        recordings.recording_id                               AS recording_id\n", "        ,recordings.partition_date                            AS partition_date\n", "        ,recordings.vin                                       AS vin\n", "        ,recordings.license_plate                             AS license_plate\n", "        ,recordings.started_at                                AS recording_started_at\n", "        ,explode(from_json(gps.json_document:gps:content, 'ARRAY<STRUCT<lat:float, lon:float, time:timestamp>>'))\n", "                                                              AS gps\n", "        ,gps.json_document:gps:app_version::STRING            AS gps_extractor_version\n", "        ,files.stream_hash                                    AS raw_gps_file_hash\n", "      FROM RECORDINGS AS recordings\n", "      INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings AS files\n", "        ON\n", "          files.recording_hash=RECORDINGS.recording_mdm_hash\n", "          AND files.stream_name='GPS'\n", "      INNER JOIN silver.tds.parents AS gps_extraction\n", "        ON\n", "          gps_extraction.parent_file_hash=files.stream_hash\n", "      INNER JOIN bronze.mdm.mdd_namespaces_latest AS gps\n", "        ON\n", "          gps.file_hash=gps_extraction.file_hash\n", "          AND gps.namespace_name='dsp_de_gps'\n", "    ),\n", "    GPS_UNCORRECTED AS (\n", "      SELECT\n", "        row_number() OVER (PARTITION BY recording_id ORDER BY gps.time ASC) AS _row_number\n", "        ,recording_id\n", "        ,recording_started_at\n", "        ,partition_date\n", "        ,vin\n", "        ,license_plate\n", "        ,gps.time AS started_at\n", "        ,null AS ended_at\n", "        ,gps_extractor_version\n", "        ,gps.lat AS latitude\n", "        ,gps.lon AS longitude\n", "        ,raw_gps_file_hash\n", "      FROM GPS_RAW\n", "    ),\n", "    GPS_RECORDING_OFFSET AS (\n", "      SELECT\n", "        recording_id\n", "        ,(MIN(started_at)-MIN(recording_started_at)) AS time_offset\n", "      FROM GPS_UNCORRECTED\n", "      GROUP BY recording_id\n", "    )\n", "    SELECT\n", "      gps.recording_id\n", "      ,gps.partition_date\n", "      ,gps.vin\n", "      ,gps.license_plate\n", "      ,(gps.started_at-GPS_RECORDING_OFFSET.time_offset) AS started_at\n", "      ,(gps_next.started_at-GPS_RECORDING_OFFSET.time_offset) AS ended_at\n", "      ,gps.gps_extractor_version\n", "      ,gps.latitude\n", "      ,gps.longitude\n", "      ,gps.raw_gps_file_hash\n", "    FROM GPS_UNCORRECTED AS gps\n", "    INNER JOIN GPS_RECORDING_OFFSET\n", "      ON\n", "        gps.recording_id=GPS_RECORDING_OFFSET.recording_id\n", "    LEFT JOIN GPS_UNCORRECTED AS gps_next\n", "      ON\n", "        gps.recording_id=gps_next.recording_id\n", "        AND gps._row_number+1=gps_next._row_number\n", "\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "df745e39-85a7-4b88-b54b-b7dbe49750d6", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT * FROM gps_coordinates LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "17bcfb24-5720-42dd-b079-12aad8af5b7a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.events_gps\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.events_gps\n", "(\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  started_at TIMESTAMP NOT NULL COMMENT 'GPS Event start',\n", "  ended_at TIMESTAMP COMMENT 'GPS Event end',\n", "  latitude FLOAT NOT NULL,\n", "  longitude FLOAT NOT NULL,\n", "  gps_extractor_version STRING,\n", "  raw_gps_file_hash STRING NOT NULL COMMENT 'MDM file hash for raw gps data source'\n", ")\n", "  -- SELECT * FROM gps_coordinates"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "786fa93a-978c-4ee4-8dfc-7101187ca3f4", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.events_gps AS target\n", "  USING gps_coordinates  AS source\n", "  ON\n", "    source.recording_id=target.recording_id\n", "    AND source.started_at=target.started_at\n", "    AND source.ended_at=target.ended_at\n", "  WHEN MATCHED AND source.gps_extractor_version <> target.gps_extractor_version THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "70ce0eb9-6c46-439f-95c1-051d7cfa4c0a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.events_gps ZORDER BY (recording_id, started_at);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3c828309-cc5f-4ecd-acdf-09d618e4c9a3", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.events_gps;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0d167af3-5ec1-4b79-a4e7-f118e09d288c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.events_gps COMPUTE STATISTICS FOR ALL COLUMNS;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "58b98127-3df8-47d0-94d6-19809d14a141", "showTitle": false, "title": ""}}, "source": ["## Create co-driver tag events"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "28ca39b5-849d-4f40-9be6-7c5be8331582", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW codriver_tags AS\n", "  WITH RECORDINGS AS (\n", "      SELECT\n", "        * \n", "      FROM dd_leadership_pub_sbx.timeseries.recordings\n", "  ),\n", "  SPLITS AS (\n", "    SELECT\n", "      recordings.*\n", "      ,files.split_hash\n", "    FROM RECORDINGS AS recordings\n", "    INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings AS files\n", "      ON\n", "        files.recording_hash=RECORDINGS.recording_mdm_hash\n", "        AND files.stream_name='GPS'\n", "  ),\n", "  CODRIVER_TAGS_CLEANSED AS (\n", "    SELECT\n", "      file_hash\n", "      ,created_at\n", "      ,modified_at\n", "      ,json_document\n", "    FROM bronze.mdm.mdd_namespaces_latest_v2\n", "    WHERE namespace_name='datamanagement-co-driver-tags'\n", "  ),\n", "  CODRIVER_TAGS AS (\n", "    SELECT\n", "      file_hash AS split_hash\n", "      ,json_document:fileStartTimeStamp::BIGINT AS file_started_at\n", "      ,json_document:fileStopTimeStamp::BIGINT AS file_stopped_at\n", "      -- ,json_document:tags\n", "      ,explode(from_json(json_document:tags, 'ARRAY<STRUCT<group:STRING, startTimestamp:BIGINT, stopTimestamp:BIGINT, type:STRING, uid:STRING, value:STRING>>')) AS tag\n", "    FROM CODRIVER_TAGS_CLEANSED\n", "  )\n", "  ------------------------------------------\n", "  -- BRING IT TOGETHER\n", "  ------------------------------------------\n", "  SELECT\n", "      splits.recording_id\n", "      ,splits.partition_date\n", "      ,splits.vin\n", "      ,splits.license_plate\n", "      ,CASE WHEN tags.tag.startTimestamp > 946681200000000 THEN from_unixtime(tags.tag.startTimestamp/**********) ELSE from_unixtime(tags.tag.startTimestamp) END AS started_at\n", "      ,CASE WHEN tags.tag.stopTimestamp > 946681200000000  THEN from_unixtime(tags.tag.stopTimestamp/**********) ELSE from_unixtime(tags.tag.stopTimestamp) END AS ended_at\n", "      ,tags.tag.startTimestamp AS started_raw_at\n", "      ,tags.tag.stopTimestamp AS ended_raw_at\n", "      ,tags.tag.group AS group\n", "      ,tags.tag.type AS type\n", "      ,tags.tag.uid AS uid\n", "      ,tags.tag.value AS value\n", "      -- ,gps.started_at\n", "      -- ,gps_next.started_at AS ended_at\n", "  FROM splits\n", "  INNER JOIN CODRIVER_TAGS AS tags\n", "    ON\n", "      tags.split_hash=splits.split_hash\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "dee045f3-f2f1-4b55-b131-9a85900009fc", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT * FROM codriver_tags LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b77db753-880c-4ced-a992-20f1accb7469", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.events_codriver_tags\n", "CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.events_codriver_tags\n", "(\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  started_at TIMESTAMP NOT NULL COMMENT 'GPS Event start',\n", "  ended_at TIMESTAMP COMMENT 'GPS Event end',\n", "  group STRING,\n", "  type STRING,\n", "  uid STRING,\n", "  value STRING\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "61e1444d-33b0-4855-a498-7d2d49fbf91e", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.events_codriver_tags AS target\n", "  USING codriver_tags AS source\n", "  ON\n", "    -- index\n", "    source.recording_id=target.recording_id\n", "    -- AND source.partition_date=target.partition_date\n", "    -- AND source.vin=target.vin\n", "    AND source.started_at=target.started_at\n", "    AND source.ended_at=target.ended_at\n", "    -- content\n", "    AND source.group=target.group\n", "    AND source.type=target.type\n", "    AND source.uid=target.uid\n", "    AND source.value=target.value\n", "  -- WHEN MATCHED THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "66b60be2-917f-448d-b57d-e56f029ad0b9", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.events_codriver_tags ZORDER BY (recording_id, started_at);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e834ff82-3463-4b07-a6d3-aaecd5698efa", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.events_codriver_tags"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "524df729-8110-4992-8a98-02b219da0541", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.events_codriver_tags COMPUTE STATISTICS FOR ALL COLUMNS;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "556d0064-5b43-4a7d-9128-90cc050dff84", "showTitle": false, "title": ""}}, "source": ["## Create be-close events"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "182feeaf-8c56-41a4-926f-6ffa50797af7", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW events_mapinfo AS\n", "  WITH RECORDINGS AS (\n", "      SELECT\n", "        * \n", "      FROM dd_leadership_pub_sbx.timeseries.recordings\n", "  ),\n", "  SPLITS AS (\n", "    SELECT\n", "      recordings.*\n", "      ,files.split_hash\n", "    FROM RECORDINGS AS recordings\n", "    INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings AS files\n", "      ON\n", "        files.recording_hash=RECORDINGS.recording_mdm_hash\n", "        AND (\n", "          files.file_extension='scene'\n", "          OR files.file_content_type='plain/text;split=true'\n", "        )\n", "  ),\n", "  MAPINFO AS (\n", "    SELECT\n", "        splits.recording_id\n", "        ,splits.partition_date\n", "        ,splits.vin\n", "        ,splits.license_plate\n", "        ,splits.started_at AS recording_started_at\n", "        ,mapinfo.positions__timestamp AS started_at\n", "        ,NULL AS ended_at\n", "        ,mapinfo.positions__coordinate_lat AS latitude\n", "        ,mapinfo.positions__coordinate_lon AS longitude\n", "        ,mapinfo.positions__country AS country\n", "        ,mapinfo.positions__state AS state\n", "        ,mapinfo.positions__city AS city\n", "        ,mapinfo.positions__street AS street\n", "        ,mapinfo.positions__is_urban AS is_urban\n", "        ,mapinfo.positions__is_tunnel AS is_tunnel\n", "        ,mapinfo.positions__is_roundabout AS is_roundabout\n", "        ,mapinfo.positions__is_ramp AS is_ramp\n", "        ,mapinfo.positions__is_paved AS is_paved\n", "        ,mapinfo.positions__is_private AS is_private\n", "        ,mapinfo.positions__has_public_access AS has_public_access\n", "        ,mapinfo.positions__has_limited_access AS has_limited_access\n", "        ,mapinfo.positions__slopes AS slopes\n", "        ,mapinfo.positions__divider AS divider\n", "        ,mapinfo.positions__curvatures AS curvatures\n", "        ,mapinfo.positions__speed_limit AS speed_limit\n", "        ,mapinfo.positions__speed_unit AS speed_unit\n", "        ,mapinfo.positions__confidence_value AS confidence_value\n", "        ,mapinfo.positions__lane_count AS lane_count\n", "        ,mapinfo.positions__route_type_ids AS route_type_ids\n", "        ,mapinfo.positions__route_types AS route_types\n", "        ,mapinfo.positions__iso_country_code_alpha2 AS iso_country_code_alpha2\n", "        ,mapinfo.positions__iso_country_code_alpha3 AS iso_country_code_alpha3\n", "        ,mapinfo.positions__iso_country_code_numeric AS iso_country_code_numeric\n", "    FROM SPLITS\n", "    INNER JOIN silver.mdd.mapinfo AS mapinfo\n", "      ON\n", "        mapinfo.file_hash=SPLITS.split_hash\n", "    WHERE\n", "      -- filter for reasonable times\n", "      mapinfo.positions__timestamp > '2010-01-01'\n", "  ),\n", "  MAPINFO_TIME_ORDERED AS (\n", "    SELECT\n", "      row_number() OVER (PARTITION BY recording_id ORDER BY started_at ASC) AS _row_number\n", "      ,*\n", "    FROM MAPINFO\n", "  ),\n", "  MAPINFO_RECORDING_OFFSET AS (\n", "    SELECT\n", "      recording_id\n", "      ,(MIN(started_at)-MIN(recording_started_at)) AS time_offset\n", "    FROM MAPINFO\n", "    GROUP BY recording_id\n", "  )\n", "  SELECT\n", "    mapinfo.recording_id\n", "    ,mapinfo.partition_date\n", "    ,mapinfo.vin\n", "    ,mapinfo.license_plate\n", "    ,(mapinfo.started_at-offset.time_offset) AS started_at\n", "    ,(mapinfo_next.started_at-offset.time_offset) AS ended_at\n", "    ,mapinfo.latitude\n", "    ,mapinfo.longitude\n", "    ,mapinfo.country\n", "    ,mapinfo.state\n", "    ,mapinfo.city\n", "    ,mapinfo.street\n", "    ,mapinfo.is_urban\n", "    ,mapinfo.is_tunnel\n", "    ,mapinfo.is_roundabout\n", "    ,mapinfo.is_ramp\n", "    ,mapinfo.is_paved\n", "    ,mapinfo.is_private\n", "    ,mapinfo.has_public_access\n", "    ,mapinfo.has_limited_access\n", "    ,mapinfo.slopes\n", "    ,mapinfo.divider\n", "    ,mapinfo.curvatures\n", "    ,mapinfo.speed_limit\n", "    ,mapinfo.speed_unit\n", "    ,mapinfo.confidence_value\n", "    ,mapinfo.lane_count\n", "    ,mapinfo.route_type_ids\n", "    ,mapinfo.route_types\n", "    ,mapinfo.iso_country_code_alpha2\n", "    ,mapinfo.iso_country_code_alpha3\n", "    ,mapinfo.iso_country_code_numeric\n", "  FROM MAPINFO_TIME_ORDERED AS mapinfo\n", "  INNER JOIN MAPINFO_RECORDING_OFFSET AS offset\n", "    ON\n", "      offset.recording_id=mapinfo.recording_id\n", "  LEFT JOIN MAPINFO_TIME_ORDERED AS mapinfo_next\n", "  ON\n", "    mapinfo.recording_id=mapinfo_next.recording_id\n", "    AND mapinfo._row_number+1=mapinfo_next._row_number\n", "  WHERE\n", "    mapinfo.started_at IS NOT NULL\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "56b42759-f5aa-4541-aa1b-edf96e78d21c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.events_mapinfo\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.events_mapinfo\n", "(\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  started_at TIMESTAMP NOT NULL COMMENT 'Mapinfo event start',\n", "  ended_at TIMESTAMP COMMENT 'Mapinfo event end. In case of null value, the event end could not be determined. This most likely means that it was the last recorded event of the drive.',\n", "  latitude FLOAT NOT NULL,\n", "  longitude FLOAT NOT NULL,\n", "  country STRING,\n", "  state STRING,\n", "  city STRING,\n", "  street STRING,\n", "  is_urban BOOLEAN,\n", "  is_tunnel BOOLEAN,\n", "  is_roundabout BOOLEAN,\n", "  is_ramp BOOLEAN,\n", "  is_paved BOOLEAN,\n", "  is_private BOOLEAN,\n", "  has_public_access BOOLEAN,\n", "  has_limited_access BOOLEAN,\n", "  slopes ARRAY<DOUBLE>,\n", "  divider STRING,\n", "  curvatures ARRAY<DOUBLE>,\n", "  speed_limit STRING,\n", "  speed_unit STRING,\n", "  confidence_value DOUBLE,\n", "  lane_count STRING,\n", "  route_type_ids ARRAY<STRING>,\n", "  route_types ARRAY<STRING>,\n", "  iso_country_code_alpha2 STRING,\n", "  iso_country_code_alpha3 STRING,\n", "  iso_country_code_numeric STRING\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0d0375ba-853d-412d-834f-e726ad00c871", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.events_mapinfo AS target\n", "  USING events_mapinfo  AS source\n", "  ON\n", "    source.recording_id=target.recording_id\n", "    AND source.started_at=target.started_at\n", "    AND source.ended_at=target.ended_at\n", "  -- WHEN MATCHED THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cd81244d-6fee-4b3d-ac1e-4611a839f50b", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.events_mapinfo ZORDER BY (recording_id, started_at)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "52ed6d5d-f205-433d-a95a-867b33227c97", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.events_mapinfo"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8edc575f-90f9-4274-8188-79d064e11b0a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.events_mapinfo COMPUTE STATISTICS FOR COLUMNS\n", "  recording_id,\n", "  partition_date,\n", "  vin,\n", "  license_plate,\n", "  started_at,\n", "  ended_at,\n", "  latitude,\n", "  longitude,\n", "  country,\n", "  state,\n", "  city,\n", "  street,\n", "  is_urban,\n", "  is_tunnel,\n", "  is_roundabout,\n", "  is_ramp,\n", "  is_paved,\n", "  is_private,\n", "  has_public_access,\n", "  has_limited_access,\n", "  divider,\n", "  speed_limit,\n", "  speed_unit,\n", "  confidence_value,\n", "  lane_count,\n", "  iso_country_code_alpha2,\n", "  iso_country_code_alpha3,\n", "  iso_country_code_numeric\n", ";"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1ac10b44-6550-4f99-af39-5347103cf4ff", "showTitle": false, "title": ""}}, "source": ["## Create Image data"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "30b8299e-f1e7-47be-8c73-65d889f2a317", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.image_files\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.image_files\n", "(\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  stream STRING NOT NULL COMMENT 'File stream name',\n", "  file_started_at TIMESTAMP NOT NULL COMMENT 'UTC timestamp of the recording start',\n", "  file_ended_at TIMESTAMP COMMENT 'UTC timestamp of the recording end',\n", "  file_hash STRING NOT NULL COMMENT 'MDM file hash',\n", "  file_name STRING NOT NULL COMMENT 'File name',\n", "  file_content_type STRING NOT NULL COMMENT 'Content type of file',\n", "  file_size_bytes BIGINT NOT NULL COMMENT 'File size',\n", "  file_sources array<struct<name:string,url:string,contentType:string,createdTime:string,provider:string,location:string,type:string,state:string>> NOT NULL COMMENT 'File sources',\n", "  file_state STRING NOT NULL COMMENT 'State of the file: An **ACTIVE** file is on hot storage and can be accessed, an **ARCHIVED** file is on archive and cannot directly be accessed (please contact storage admin and bring money)',\n", "  tds_file_url STRING NOT NULL COMMENT 'A direct link to the file on TDS blob storage',\n", "  recorded_at_unix BIGINT,\n", "  project STRING,\n", "  frame_number INT,\n", "  view STRING,\n", "  view_type STRING,\n", "  is_raw_image BOOLEAN,\n", "  color_space STRING COMMENT 'Color space of the image, i.e. RGB or YUV.',\n", "  context STRING COMMENT 'Context of the image, i.e. either A or B. These are different exposures of the same image.',\n", "  bit_depth INT,\n", "  is_compressed BOOLEAN,\n", "  compression__algorithm STRING,\n", "  isp STRING,\n", "  anonymizer__git_commit STRING,\n", "  anonymizer__algorithm_used STRING,\n", "  anonymizer__configuration MAP<STRING,STRING>,\n", "  rectification__type STRING,\n", "  rectification__warper_version STRING,\n", "  rectification__calstorage_uid STRING,\n", "  extractor_version STRING,\n", "  extractor_version_int INT,\n", "  extractor_git_hash STRING,\n", "  extractor_created_at STRING\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "collapsed": true, "inputWidgets": {}, "nuid": "e0f0b419-930f-4fb2-8730-f27b1ee92d4c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW image_files AS\n", "  WITH \n", "    SPLITS_RAW AS (\n", "      SELECT\n", "        all_recordings.recording_hash, all_recordings.split_hash\n", "      FROM silver.mdd.dsp_de_image\n", "      INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings\n", "        ON\n", "          dsp_de_image.split_hash=all_recordings.split_hash\n", "      WHERE dsp_de_image.split_hash IS NOT NULL\n", "      GROUP BY all_recordings.recording_hash, all_recordings.split_hash\n", "    ),\n", "    RECORDINGS AS (\n", "        SELECT\n", "          * \n", "        FROM dd_leadership_pub_sbx.timeseries.recordings\n", "    ),\n", "    SPLITS AS (\n", "      SELECT\n", "        recordings.*\n", "        ,splits.split_hash\n", "      FROM SPLITS_RAW AS splits\n", "      INNER JOIN dd_leadership_pub_sbx.timeseries.recordings AS recordings\n", "        ON\n", "          recordings.recording_mdm_hash=splits.recording_hash\n", "\n", "    ),\n", "    IMAGE_FILES AS (\n", "      SELECT\n", "          splits.recording_id\n", "          ,splits.partition_date\n", "          ,splits.vin\n", "          ,splits.license_plate\n", "          ,images.camera_stream AS stream\n", "          ,images.recorded_at AS file_started_at\n", "          ,NULL AS file_ended_at\n", "          -- Link to file\n", "          ,images.file_hash\n", "          ,file_entries.file_name\n", "          ,file_entries.content_type AS file_content_type\n", "          ,file_entries.file_size_bytes\n", "          ,file_entries.sources AS file_sources\n", "          ,file_entries.file_state AS file_state\n", "          ,file_entries.tds_file_url AS tds_file_url\n", "          -- Additional information\n", "          ,images.recorded_at_unix\n", "          ,images.project\n", "          ,images.frame_number\n", "          ,images.view\n", "          ,images.view_type\n", "          ,images.is_raw_image\n", "          ,images.color_space\n", "          ,images.context\n", "          ,images.bit_depth\n", "          ,images.is_compressed\n", "          ,images.compression__algorithm\n", "          ,images.isp\n", "          ,images.anonymizer__git_commit\n", "          ,images.anonymizer__algorithm_used\n", "          ,images.anonymizer__configuration\n", "          ,images.rectification__type\n", "          ,images.rectification__warper_version\n", "          ,images.rectification__calstorage_uid\n", "          ,images.extractor_version\n", "          ,images.extractor_version_int\n", "          ,images.extractor_git_hash\n", "          ,images.extractor_created_at\n", "      FROM silver.mdd.dsp_de_image AS images\n", "      INNER JOIN SPLITS as splits\n", "        ON\n", "          splits.split_hash=images.split_hash\n", "      INNER JOIN silver.tds.file_entries AS file_entries\n", "        ON\n", "          file_entries.file_hash=images.file_hash\n", "    )\n", "    SELECT\n", "    *\n", "    FROM IMAGE_FILES\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "605f0226-2549-4b7d-a497-2b27cb0df447", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- select * from image_files limit 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7fd96ae1-4722-4020-9b2d-a0e991fea9e4", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.image_files AS target\n", "  USING image_files AS source\n", "  ON\n", "    source.recording_id=target.recording_id\n", "    AND source.file_started_at=target.file_started_at\n", "    AND source.file_ended_at=target.file_ended_at\n", "    AND source.file_hash=target.file_hash\n", "  WHEN MATCHED AND source.extractor_version <> target.extractor_version THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7a20d012-97c5-4506-9c06-227e92e60510", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.image_files ZORDER BY (recording_id, file_started_at)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ad847a66-737c-427e-a1d4-62ebb697b33d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.image_files"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "58a21b7c-44ec-4889-8d89-2b97877d9bde", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.image_files COMPUTE STATISTICS FOR COLUMNS\n", "  recording_id,\n", "  partition_date,\n", "  vin,\n", "  license_plate,\n", "  stream,\n", "  file_started_at,\n", "  file_ended_at,\n", "  file_hash,\n", "  file_name,\n", "  file_content_type,\n", "  file_size_bytes,\n", "  file_state,\n", "  recorded_at_unix,\n", "  project,\n", "  frame_number,\n", "  view,\n", "  view_type,\n", "  is_raw_image,\n", "  color_space,\n", "  context,\n", "  bit_depth,\n", "  is_compressed,\n", "  compression__algorithm,\n", "  isp,\n", "  anonymizer__git_commit,\n", "  anonymizer__algorithm_used,\n", "  rectification__type,\n", "  rectification__warper_version,\n", "  rectification__calstorage_uid,\n", "  extractor_version,\n", "  extractor_version_int,\n", "  extractor_git_hash,\n", "  extractor_created_at\n", ";"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "a7fb4e49-4561-417b-b25c-d3147e860a89", "showTitle": false, "title": ""}}, "source": ["## Create Image Thumbnail Data"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "30145375-5ca4-4757-82c9-f35925f0a9be", "showTitle": false, "title": ""}}, "source": ["### Create thumbnail namespace view"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "68c1aa9e-a4b9-4986-a658-3ff71a98e63d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.__thumbnail_namespace (\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.__thumbnail_namespace (\n", "  file_hash STRING NOT NULL COMMENT 'file hash of the thumbnail'\n", "  ,created_at TIMESTAMP NOT NULL COMMENT 'first creation of this namespace entry'\n", "  ,modified_at TIMESTAMP NOT NULL COMMENT 'timestamp of last modification'\n", "  ,height INTEGER COMMENT 'Height dimension of the thumbnail image'\n", "  ,width INTEGER  COMMENT 'Width dimension of the thumbnail image'\n", "  ,stream STRING NOT NULL COMMENT 'sensor stream name'\n", "  ,image_captured_at TIMESTAMP NOT NULL COMMENT 'Timestamp of the original image capture, i.e. recording time'\n", "  ,version STRING NOT NULL COMMENT 'version of the creator of the thumbnail'\n", "  ,base_file_name STRING NOT NULL COMMENT 'base file name of the thumbnail that allows easy finding of the full size image'\n", "  ,parent_file_hash STRING NOT NULL COMMENT 'link to the parent file, that was te base for the thumbnail creation'\n", "  ,content_type STRING NOT NULL\n", "  ,file_size_bytes BIGINT NOT NULL COMMENT 'File size'\n", "  ,file_name STRING NOT NULL COMMENT 'file name of the thumbnail'\n", "  ,tds_file_url STRING NOT NULL COMMENT 'TDS download url of the thumbnail'\n", "  ,file_state STRING NOT NULL COMMENT 'State of the file: An **ACTIVE** file is on hot storage and can be accessed, an **ARCHIVED** file is on archive and cannot directly be accessed (please contact storage admin and bring money)'\n", "  ,file_sources array<struct<name:string,url:string,contentType:string,createdTime:string,provider:string,location:string,type:string,state:string>> NOT NULL COMMENT 'File sources'\n", ")\n", "COMMENT 'Helper table keeping prepared thumbnail namespace metadata. Only on hot storage available thumbnails are included.'"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9378e2fc-0f8c-4e65-9ec3-f434b01b59c8", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW thumbnail_namespace AS\n", "  WITH CTE AS (\n", "    SELECT\n", "      ns.file_hash\n", "      ,ns.created_at\n", "      ,ns.modified_At\n", "      ,ns.json_document:height::INTEGER AS height\n", "      ,ns.json_document:width::INTEGER AS width\n", "      ,UPPER(ns.json_document:stream::STRING) AS stream\n", "      ,ns.json_document:timestamp::TIMESTAMP AS image_captured_at\n", "      ,ns.json_document:version:version::STRING AS version\n", "      ,substring_index(file_entries.file_name, \"_thumbnail_\", 1) AS base_file_name\n", "      ,file_entries.parents[0] as parent_file_hash\n", "      ,file_entries.content_type\n", "      ,file_entries.file_size_bytes\n", "      ,file_entries.file_state\n", "      ,file_entries.sources AS file_sources\n", "      ,file_entries.file_name\n", "      ,file_entries.tds_file_url\n", "    FROM bronze.mdm.mdd_namespaces_latest_v2 AS ns\n", "    INNER JOIN silver.tds.file_entries\n", "      ON\n", "        file_entries.file_hash=ns.file_hash\n", "        AND endswith(file_entries.content_type, \";thumbnail=true\")\n", "    WHERE\n", "      namespace_name='thumbnails'\n", "      AND file_state='ACTIVE'\n", "      AND parents IS NOT NULL\n", "      AND array_size(parents) > 0\n", "  )\n", "  SELECT\n", "    file_hash, created_at, modified_at, \n", "    height, width, stream, image_captured_at, version,\n", "    base_file_name, parent_file_hash, content_type, file_name, file_size_bytes, file_state, tds_file_url, file_sources\n", "  FROM CTE\n", "  WHERE\n", "    stream IS NOT NULL\n", "    AND image_captured_at IS NOT NULL\n", "    AND height IS NOT NULL\n", "    AND width IS NOT NULL"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "37dc0698-188f-46b9-8645-9f2eb5e092ce", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.__thumbnail_namespace AS target\n", "  USING thumbnail_namespace AS source\n", "  ON\n", "    source.file_hash=target.file_hash\n", "  WHEN MATCHED AND source.modified_at > target.modified_at THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0a347149-dc09-4aa6-9257-cd0047f374ad", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.__thumbnail_namespace ZORDER BY parent_file_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "57d0baac-d79e-42f2-a5b2-185fd719b5fb", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.__thumbnail_namespace;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "af8351ef-f2a2-48b5-bca6-c8797c2bd719", "showTitle": false, "title": ""}}, "source": ["### Create Thumbnail Data table"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "df1a0546-510f-48fa-878a-158a043bdfe0", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.image_thumbnails_files (\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.image_thumbnails_files (\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  stream STRING NOT NULL COMMENT 'File stream name',\n", "  file_started_at TIMESTAMP NOT NULL COMMENT 'UTC timestamp of the recording start',\n", "  file_ended_at TIMESTAMP COMMENT 'UTC timestamp of the recording end',\n", "  file_hash STRING NOT NULL COMMENT 'MDM file hash',\n", "  file_name STRING NOT NULL COMMENT 'File name',\n", "  file_content_type STRING NOT NULL COMMENT 'Content type of file',\n", "  file_size_bytes BIGINT NOT NULL COMMENT 'File size',\n", "  file_sources array<struct<name:string,url:string,contentType:string,createdTime:string,provider:string,location:string,type:string,state:string>> NOT NULL COMMENT 'File sources',\n", "  file_state STRING NOT NULL COMMENT 'State of the file: An **ACTIVE** file is on hot storage and can be accessed, an **ARCHIVED** file is on archive and cannot directly be accessed (please contact storage admin and bring money)',\n", "  tds_file_url STRING NOT NULL COMMENT 'A direct link to the file on TDS blob storage',\n", "  frame_number INT COMMENT 'Frame number within the source stream file, i.e. not necessarily for the whole recording',\n", "  thumbnail_height INT NOT NULL COMMENT 'Height of thumbnail',\n", "  thumbnail_width INT NOT NULL COMMENT 'Width of thumbnail',\n", "  thumbnail_version STRING COMMENT 'Version of the thumbnail creator',\n", "  recorded_at_unix BIGINT,\n", "  project STRING,\n", "  view STRING,\n", "  view_type STRING,\n", "  is_raw_image BOOLEAN,\n", "  color_space STRING COMMENT 'Color space of the image, i.e. RGB or YUV.',\n", "  context STRING COMMENT 'Context of the image, i.e. either A or B. These are different exposures of the same image.',\n", "  bit_depth INT,\n", "  is_compressed BOOLEAN,\n", "  compression__algorithm STRING,\n", "  isp STRING,\n", "  anonymizer__git_commit STRING,\n", "  anonymizer__algorithm_used STRING,\n", "  anonymizer__configuration MAP<STRING,STRING>,\n", "  rectification__type STRING,\n", "  rectification__warper_version STRING,\n", "  rectification__calstorage_uid STRING,\n", "  extractor_version STRING,\n", "  extractor_version_int INT,\n", "  extractor_git_hash STRING,\n", "  extractor_created_at STRING\n", ")\n", "COMMENT 'This table sorts the available thumbnails of the image files'"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b484f1bd-3bab-45bc-b36e-54b8f032da10", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW image_thumbnails_files  AS\n", "  WITH IMAGE_INFO AS (\n", "    SELECT \n", "      *\n", "    FROM dd_leadership_pub_sbx.timeseries.image_files\n", "  )\n", "  SELECT\n", "    image_files.recording_id\n", "    ,image_files.partition_date\n", "    ,image_files.vin\n", "    ,image_files.license_plate\n", "    ,image_files.stream\n", "    ,image_files.file_started_at\n", "    ,image_files.file_ended_at\n", "    ,thumbnail.file_hash\n", "    ,thumbnail.file_name\n", "    ,thumbnail.file_size_bytes\n", "    ,thumbnail.file_sources\n", "    ,thumbnail.file_state\n", "    ,thumbnail.content_type AS file_content_type\n", "    ,thumbnail.tds_file_url\n", "    ,thumbnail.height AS thumbnail_height\n", "    ,thumbnail.width AS thumbnail_width\n", "    ,thumbnail.version AS thumbnail_version\n", "    ,image_files.recorded_at_unix\n", "    ,image_files.project\n", "    ,image_files.frame_number\n", "    ,image_files.view\n", "    ,image_files.view_type\n", "    ,image_files.is_raw_image\n", "    ,image_files.color_space\n", "    ,image_files.context\n", "    ,image_files.bit_depth\n", "    ,image_files.is_compressed\n", "    ,image_files.compression__algorithm\n", "    ,image_files.isp\n", "    ,image_files.anonymizer__git_commit\n", "    ,image_files.anonymizer__algorithm_used\n", "    ,image_files.anonymizer__configuration\n", "    ,image_files.rectification__type\n", "    ,image_files.rectification__warper_version\n", "    ,image_files.rectification__calstorage_uid\n", "    ,image_files.extractor_version\n", "    ,image_files.extractor_version_int\n", "    ,image_files.extractor_git_hash\n", "    ,image_files.extractor_created_at\n", "  FROM IMAGE_INFO AS image_files\n", "  INNER JOIN dd_leadership_pub_sbx.timeseries.`__thumbnail_namespace` AS thumbnail\n", "    ON\n", "      thumbnail.parent_file_hash=image_files.file_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e918b6fd-958b-40e0-aa8a-b9e70f30f207", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.image_thumbnails_files AS target\n", "  USING image_thumbnails_files AS source\n", "  ON\n", "        source.recording_id=target.recording_id\n", "    AND source.file_started_at=target.file_started_at\n", "    AND source.file_ended_at=target.file_ended_at\n", "    AND source.file_hash=target.file_hash\n", "  WHEN MATCHED AND source.extractor_version_int > target.extractor_version_int THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bc67770a-3fe3-42b4-9883-2878476ee5f4", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.image_thumbnails_files ZORDER BY (recording_id, file_started_at)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7105850c-d69e-4dc5-a230-bd65d57563e0", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.image_thumbnails_files"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a15de3a3-1116-4098-90d6-7cb8c667e510", "showTitle": false, "title": ""}}, "source": ["## Create Lidar data"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "65b4d0b2-367d-4acf-bbe6-a3d58cc75746", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.lidar_files\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.lidar_files\n", "(\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  stream STRING NOT NULL COMMENT 'File stream name',\n", "  file_started_at TIMESTAMP NOT NULL COMMENT 'UTC timestamp of the recording start',\n", "  file_ended_at TIMESTAMP COMMENT 'UTC timestamp of the recording end',\n", "  file_hash STRING NOT NULL COMMENT 'MDM file hash',\n", "  file_name STRING NOT NULL COMMENT 'File name',\n", "  file_content_type STRING NOT NULL COMMENT 'Content type of file',\n", "  file_size_bytes BIGINT NOT NULL COMMENT 'File size',\n", "  file_sources array<struct<name:string,url:string,contentType:string,createdTime:string,provider:string,location:string,type:string,state:string>> NOT NULL COMMENT 'File sources',\n", "  file_state STRING NOT NULL COMMENT 'State of the file: An **ACTIVE** file is on hot storage and can be accessed, an **ARCHIVED** file is on archive and cannot directly be accessed (please contact storage admin and bring money)',\n", "  tds_file_url STRING NOT NULL COMMENT 'A direct link to the file on TDS blob storage',\n", "  image_hash STRING COMMENT 'TDS hash for time Synchronized image',\n", "  source_pcd_hash STRING COMMENT ' TDS hash of the original PCD file',\n", "  odometry_source STRING,\n", "  coordinate_system STRING,\n", "  compensation_version_created_date TIMESTAMP,\n", "  compensation_version_git_hash STRING,\n", "  compensation_version_number STRING\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ca8042c9-dc97-4915-ac96-a423466cf993", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW lidar_files AS\n", "SELECT\n", "  recordings.recording_id\n", "  ,recordings.partition_date\n", "  ,recordings.vin\n", "  ,recordings.license_plate\n", "  ,UPPER(dsp_lidar_pcd.stream) AS stream\n", "  ,dsp_de_image.split_hash\n", "  ,dsp_lidar_pcd.recorded_at AS file_started_at\n", "  ,null AS file_ended_at\n", "  ,dsp_lidar_pcd.file_hash\n", "  ,file_entries.file_name\n", "  ,file_entries.content_type AS file_content_type\n", "  ,file_entries.file_size_bytes\n", "  ,file_entries.sources AS file_sources\n", "  ,file_entries.file_state\n", "  ,file_entries.tds_file_url\n", "  ,dsp_lidar_pcd.image  AS image_hash\n", "  ,dsp_lidar_pcd.source_pcd AS source_pcd_hash\n", "  ,dsp_lidar_pcd.odometry_source\n", "  ,dsp_lidar_pcd.coordinate_system\n", "  ,dsp_lidar_pcd.compensation_version_created_date\n", "  ,dsp_lidar_pcd.compensation_version_git_hash\n", "  ,dsp_lidar_pcd.compensation_version_number\n", "FROM silver.mdd.dsp_lidar_pcd\n", "INNER JOIN silver.mdd.dsp_de_image\n", "  ON\n", "    dsp_de_image.file_hash=dsp_lidar_pcd.image\n", "INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings\n", "  ON\n", "    all_recordings.stream_hash=dsp_de_image.stream_hash\n", "INNER JOIN dd_leadership_pub_sbx.timeseries.recordings\n", "  ON\n", "    recordings.recording_mdm_hash=all_recordings.recording_hash\n", "INNER JOIN silver.tds.file_entries\n", "  ON\n", "    file_entries.file_hash=dsp_lidar_pcd.file_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2af9f0c6-b244-4f0c-892f-1e8db1e69f50", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.lidar_files AS target\n", "  USING lidar_files AS source\n", "  ON\n", "    source.recording_id=target.recording_id\n", "    AND source.file_started_at=target.file_started_at\n", "    AND source.file_ended_at=target.file_ended_at\n", "    AND source.file_hash=target.file_hash\n", "  WHEN MATCHED AND source.compensation_version_created_date > target.compensation_version_created_date THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0a344057-e1c7-4864-9478-7357c370153d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.lidar_files ZORDER BY (recording_id, file_started_at);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a519a32f-af8e-4a6c-b8b4-5f04ca5d6923", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.lidar_files;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "38002d7e-a961-4638-adb2-4b4f36e8348b", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.lidar_files COMPUTE STATISTICS FOR COLUMNS\n", "  recording_id,\n", "  partition_date,\n", "  vin,\n", "  license_plate,\n", "  stream,\n", "  file_started_at,\n", "  file_ended_at,\n", "  file_hash,\n", "  file_name,\n", "  file_content_type,\n", "  file_size_bytes,\n", "  file_state,\n", "  tds_file_url,\n", "  image_hash,\n", "  source_pcd_hash,\n", "  odometry_source,\n", "  coordinate_system,\n", "  compensation_version_created_date,\n", "  compensation_version_git_hash,\n", "  compensation_version_number\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c46e6119-c1b6-4a32-9844-4b17df0d7d8c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- select stream, count(*) from dd_leadership_pub_sbx.timeseries.lidar_files group by stream"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b241df47-f830-450f-9da4-40858e84907a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT file_hash, count(*) FROM dd_leadership_pub_sbx.timeseries.lidar_files\n", "-- group by file_hash\n", "-- having count(*) > 1\n", "-- LIMIT 1000"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cb7c66c7-db14-4617-9bff-ac143692c3e4", "showTitle": false, "title": ""}}, "source": ["## Object DB meets time series index"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d372fe9b-0ec6-41ca-8f0d-064932c92f88", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.timeseries.events_label\n", "-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.events_label\n", "(\n", "  recording_id BIGINT COMMENT 'Unique id for a recording',\n", "  partition_date INT NOT NULL COMMENT 'Date partition the recording belongs to, too speed up related data lookups',\n", "  vin STRING NOT NULL COMMENT 'VIN of vehicle used for recording',\n", "  stream STRING NOT NULL COMMENT 'Name of the annotated sensor stream',\n", "  stream_type STRING NOT NULL COMMENT 'Type of sensor stream, i.e. CAMERA or LIDAR',\n", "  license_plate STRING NOT NULL COMMENT 'License plate',\n", "  started_at TIMESTAMP NOT NULL COMMENT 'Mapinfo event start',\n", "  ended_at TIMESTAMP COMMENT 'Mapinfo event end. In case of null value, the event end could not be determined. This most likely means that it was the last recorded event of the drive.',\n", "  frame_number INT COMMENT 'The frame number in video stream. Is not available for LIDAR data',\n", "  label_task_id STRING,\n", "  class STRING,\n", "  object_id STRING,\n", "  annotation_type STRING,\n", "  annotation_name STRING,\n", "  interpolated STRING,\n", "  attributes MAP<STRING,STRING>,\n", "  label_result_file_hash STRING,\n", "  labeled_frame_hash STRING\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "53fbf8d3-e294-4551-8071-027d49263fb4", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW events_label AS\n", "  WITH SANITIZED_OBJECT_DB AS (\n", "    SELECT\n", "      frame_sha AS frame_hash\n", "      ,CASE \n", "        WHEN UPPER(stream_type) IN ('CAMERA', 'CAMERA/FRAME/PNG') THEN 'CAMERA'\n", "        WHEN UPPER(stream_type) IN ('LIDAR') THEN 'LIDAR'\n", "        ELSE 'UNKNOWN'\n", "       END AS stream_type\n", "      ,label_task_id\n", "      ,class\n", "      ,object_id\n", "      ,annotation_type\n", "      ,annotation_name\n", "      ,interpolated\n", "      ,attributes\n", "      ,file_hash AS label_result_file_hash\n", "      ,frame_sha AS labeled_frame_hash      \n", "    FROM silver.autoqc.label_file_objects\n", "    WHERE\n", "      frame_sha IS NOT NULL\n", "  ),\n", "  RECORDING_LINKING_DONE AS (\n", "    SELECT\n", "      COALESCE(image_files.recording_id,lidar_files.recording_id) AS recording_id\n", "      ,COALESCE(image_files.partition_date,lidar_files.partition_date) AS partition_date\n", "      ,COALESCE(image_files.vin,lidar_files.vin) AS vin\n", "      ,COALESCE(image_files.license_plate,lidar_files.license_plate) AS license_plate\n", "      ,UPPER(COALESCE(image_files.stream,lidar_files.stream)) AS stream\n", "      ,label_file_objects.stream_type\n", "      ,COALESCE(image_files.file_started_at,lidar_files.file_started_at) AS started_at\n", "      ,COALESCE(image_files.file_ended_at,lidar_files.file_ended_at) AS ended_at\n", "      ,COALESCE(image_files.frame_number,NULL) AS frame_number\n", "      ,label_file_objects.label_task_id\n", "      ,label_file_objects.class\n", "      ,label_file_objects.object_id\n", "      ,label_file_objects.annotation_type\n", "      ,label_file_objects.annotation_name\n", "      ,label_file_objects.interpolated\n", "      ,label_file_objects.attributes\n", "      ,label_file_objects.label_result_file_hash\n", "      ,label_file_objects.labeled_frame_hash\n", "    FROM SANITIZED_OBJECT_DB AS label_file_objects \n", "    LEFT JOIN dd_leadership_pub_sbx.timeseries.image_files AS image_files\n", "      ON\n", "        image_files.file_hash=label_file_objects.frame_hash\n", "        AND label_file_objects.stream_type = 'CAMERA'\n", "    LEFT JOIN dd_leadership_pub_sbx.timeseries.lidar_files AS lidar_files\n", "      ON\n", "        lidar_files.file_hash=label_file_objects.frame_hash\n", "        AND label_file_objects.stream_type = 'LIDAR'\n", "  )\n", "  SELECT\n", "    *\n", "  FROM RECORDING_LINKING_DONE\n", "  WHERE\n", "    -- only take those that can be linked to a recording\n", "    recording_id IS NOT NULL\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9e373e81-eee4-4b62-bd44-2dcf5a8ea36b", "showTitle": false, "title": ""}}, "outputs": [], "source": ["MERGE INTO dd_leadership_pub_sbx.timeseries.events_label AS target\n", "  USING events_label AS source\n", "  ON\n", "    source.recording_id=target.recording_id\n", "    -- AND source.partition_date=target.partition_date\n", "    -- AND source.vin=target.vin\n", "    AND source.started_at=target.started_at\n", "    AND source.ended_at=target.ended_at\n", "    AND source.stream=target.stream\n", "    AND source.label_task_id=target.label_task_id\n", "    AND source.object_id=target.object_id\n", "  -- WHEN MATCHED THEN UPDATE SET *\n", "  WHEN NOT MATCHED BY TARGET THEN INSERT *"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9fedff3f-3383-4eab-b128-b30a6cf007ec", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.events_label ZORDER BY (recording_id, started_at);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "25ba1156-6f2b-40d0-81de-ea8cc5c142e1", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.events_label;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "97dd2a27-6256-484f-8a7f-cdb2bc31100c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE dd_leadership_pub_sbx.timeseries.events_label COMPUTE STATISTICS FOR COLUMNS\n", "  recording_id,\n", "  partition_date,\n", "  vin,\n", "  stream,\n", "  stream_type,\n", "  license_plate,\n", "  started_at,\n", "  ended_at,\n", "  frame_number,\n", "  label_task_id,\n", "  class,\n", "  object_id,\n", "  annotation_type,\n", "  annotation_name,\n", "  interpolated,\n", "  label_result_file_hash,\n", "  labeled_frame_hash\n", ";"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "44254286-774e-4de4-b3a5-7ac19a04ea6d", "showTitle": false, "title": ""}}, "source": ["### Analyse"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6f5c4a3e-60f5-4599-b16d-9b06917e8950", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select 'object_db_entries' as what, count(*) as value from silver.autoqc.label_file_objects\n", "union\n", "select 'events_label_entries' as what, count(*) as value from dd_leadership_pub_sbx.timeseries.events_label"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "0d5d4bdb-631f-41cd-a8f3-f21e35036619", "showTitle": false, "title": ""}}, "source": ["## Create ego vehicle data"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a7d51a37-736e-4034-a7ff-7b3a353af176", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE dd_leadership_pub_sbx.timeseries.events_ego_state AS\n", "WITH RECORDINGS AS (\n", "    SELECT\n", "      * \n", "    FROM dd_leadership_pub_sbx.timeseries.recordings\n", "),\n", "SPLITS AS (\n", "  SELECT\n", "    recordings.*\n", "    ,files.split_hash\n", "    ,split_info.start_time_utc\n", "    ,split_info.end_time_utc\n", "  FROM RECORDINGS AS recordings\n", "  INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings AS files\n", "    ON\n", "      files.recording_hash=RECORDINGS.recording_mdm_hash\n", "      AND (\n", "        files.file_extension='scene'\n", "        OR files.file_content_type='plain/text;split=true'\n", "      )\n", "  INNER JOIN silver.mdd.`datamanagement-split-info` AS split_info\n", "    ON\n", "      split_info.file_hash=files.split_hash\n", ")\n", "select\n", "  splits.recording_id, splits.partition_date, splits.vin, splits.license_plate\n", "  ,splits.start_time_utc AS started_at\n", "  ,splits.end_time_utc AS ended_at\n", "  ,ego.environment__precipitation__present\n", "  ,ego.environment__precipitation__intensity\n", "  ,ego.environment__temperature\n", "  ,ego.environment__lighting__intensity\n", "  ,ego.wipers__front__max_use\n", "  ,ego.wipers__rear__on\n", "  ,ego.wipers__rear__off\n", "  ,ego.lights__front__off\n", "  ,ego.lights__front__regular\n", "  ,ego.lights__front__low_beam\n", "  ,ego.lights__front__high_beam\n", "  ,ego.lights__fog_frontlight__off\n", "  ,ego.lights__fog_frontlight__on\n", "  ,ego.lights__fog_backlight__off\n", "  ,ego.lights__fog_backlight__on\n", "  ,ego.lights__turn_indicators__left__off\n", "  ,ego.lights__turn_indicators__left__on\n", "  ,ego.lights__turn_indicators__right__off\n", "  ,ego.lights__turn_indicators__right__on\n", "  ,ego.speed__stats__min\n", "  ,ego.speed__stats__max\n", "  ,ego.speed__stats__mean\n", "  ,ego.speed__direction\n", "  ,ego.brake_signal\n", "from SPLITS as splits\n", "inner join silver.mdd.dsp_de_vehicle_metadata as ego on ego.file_hash=splits.split_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "431d8ba5-8f0a-46ec-9369-e08f5d194899", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE dd_leadership_pub_sbx.timeseries.events_ego_state ZORDER BY (recording_id, started_at)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "36d26963-80ad-445d-8f3b-5de95efef754", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM dd_leadership_pub_sbx.timeseries.events_ego_state"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4dcc6e0d-247c-48cd-9431-1751fad12a80", "showTitle": false, "title": ""}}, "source": ["# Use Timeseries Database to solve actual user tasks"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "308d439d-718f-4500-9d6c-feed869e9f8a", "showTitle": false, "title": ""}}, "source": ["## 30 second snippet with all information available"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "5733d12c-4def-4840-81f8-946c285094d1", "showTitle": false, "title": ""}}, "source": ["### Create silver layer of aggregated recording locations and labelled data"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6326ad99-af56-4a2b-8a01-4ac3e92c855c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["create or replace table dd_leadership_pub_sbx.timeseries.silver_recording_info as\n", "  with map_enriched (\n", "    select\n", "      recordings.recording_id, max(recordings.recording_mdm_hash) as recording_mdm_hash, max(recordings.vin) as vin, max(recordings.started_at) AS recording_started_at, max(events_mapinfo.ended_at) as recording_ended_at\n", "      ,datediff(minute, min(events_mapinfo.started_at), max(events_mapinfo.ended_at)) as drive_duration_minutes\n", "      ,collect_set(events_mapinfo.route_types) as driven_route_types\n", "      ,collect_set(events_mapinfo.country) as driven_countries\n", "      ,collect_set(events_mapinfo.city) as driven_cities\n", "      ,sum(CAST(events_mapinfo.is_roundabout AS INT)) as driven_roundabout_seconds\n", "      ,sum(CAST(events_mapinfo.is_tunnel AS INT)) as driven_tunnel_seconds\n", "      ,collect_set(events_mapinfo.is_urban) as is_urban\n", "    from dd_leadership_pub_sbx.timeseries.recordings\n", "    inner join dd_leadership_pub_sbx.timeseries.events_mapinfo\n", "      on\n", "        events_mapinfo.recording_id=recordings.recording_id\n", "    where\n", "      recordings.valid\n", "      and recordings.recording_tds_pool='g3vprdaq'\n", "      and recordings.started_at > '2024-01-01'\n", "      and events_mapinfo.started_at > '2000-01-01'\n", "    group by recordings.recording_id\n", "  ),\n", "  start_location (\n", "    select\n", "      map_enriched.recording_id\n", "      ,min(event_gps.latitude) as latitude\n", "      ,min(event_gps.longitude) as longitude\n", "    from map_enriched\n", "    inner join dd_leadership_pub_sbx.timeseries.events_gps as event_gps\n", "      on\n", "        event_gps.recording_id=map_enriched.recording_id\n", "        and event_gps.started_at=map_enriched.recording_started_at\n", "    group by map_enriched.recording_id\n", "  ),\n", "  end_location (\n", "    select\n", "      map_enriched.recording_id\n", "      ,min(event_gps.latitude) as latitude\n", "      ,min(event_gps.longitude) as longitude\n", "    from map_enriched\n", "    inner join dd_leadership_pub_sbx.timeseries.events_gps as event_gps\n", "      on\n", "        event_gps.recording_id=map_enriched.recording_id\n", "        and event_gps.started_at=map_enriched.recording_ended_at\n", "    group by map_enriched.recording_id\n", "  ),\n", "  labeling_enriched (\n", "    select\n", "     events_label.recording_id\n", "      ,events_label.label_task_id\n", "      ,count(distinct events_label.labeled_frame_hash) as labeled_unique_frames\n", "      ,collect_set(upper(stream)) as labeled_sensor_streams\n", "      ,collect_set(stream_type) as labeled_sensor_stream_types\n", "    from dd_leadership_pub_sbx.timeseries.events_label\n", "    group by events_label.recording_id, events_label.label_task_id\n", "  ),\n", "  available_sensors (\n", "    select\n", "      recording_id, collect_set(stream) as sensor_stream_names\n", "    from dd_leadership_pub_sbx.timeseries.recording_files\n", "    group by recording_id\n", "  ),\n", "  recording_file_sizes (\n", "    select\n", "      recording_id\n", "      ,round(sum(file_size_bytes)/1024/1024/1024, 0) as recording_size_gb\n", "      ,count(distinct file_hash) as recording_file_count\n", "    from dd_leadership_pub_sbx.timeseries.recording_files\n", "    group by recording_id\n", "  ),\n", "  recording_single_images (\n", "    select\n", "      recording_id\n", "      ,round(sum(file_size_bytes)/1024/1024/1024, 0) as image_file_size_gb\n", "      ,count(distinct file_hash) as image_file_count\n", "    from dd_leadership_pub_sbx.timeseries.image_files\n", "    group by recording_id\n", "  ),\n", "  ego_vehicle (\n", "    select\n", "      recording_id\n", "      ,collect_set(environment__temperature) AS env_temperature\n", "      ,collect_set(environment__precipitation__intensity) AS env_precipitation_intensity\n", "      ,collect_set(speed__stats__mean) AS speed_stats_mean\n", "      ,MAX(wipers__front__max_use) AS wipers_front_max_use\n", "      ,collect_set(lights__front__off) AS lights_front_off\n", "    from dd_leadership_pub_sbx.timeseries.events_ego_state as ego\n", "    group by recording_id\n", "  )\n", "  select\n", "    map_enriched.*\n", "    ,start_location.latitude as start_latitude\n", "    ,start_location.longitude as start_longitude\n", "    ,end_location.latitude as end_latitude\n", "    ,end_location.longitude as end_longitude\n", "    ,ego_vehicle.speed_stats_mean\n", "    ,ego_vehicle.wipers_front_max_use\n", "    ,ego_vehicle.lights_front_off\n", "    ,ego_vehicle.env_temperature\n", "    ,ego_vehicle.env_precipitation_intensity \n", "    ,recording_file_sizes.recording_size_gb\n", "    ,recording_single_images.image_file_size_gb\n", "    ,available_sensors.sensor_stream_names\n", "    ,labeling_enriched.label_task_id\n", "    ,labeling_enriched.labeled_unique_frames\n", "    ,labeling_enriched.labeled_sensor_streams\n", "    ,labeling_enriched.labeled_sensor_stream_types\n", "  from map_enriched\n", "  inner join start_location on start_location.recording_id=map_enriched.recording_id\n", "  inner join end_location on end_location.recording_id=map_enriched.recording_id\n", "  inner join ego_vehicle on ego_vehicle.recording_id=map_enriched.recording_id \n", "  inner join recording_file_sizes on recording_file_sizes.recording_id=map_enriched.recording_id\n", "  inner join recording_single_images on recording_single_images.recording_id=map_enriched.recording_id\n", "  inner join labeling_enriched on labeling_enriched.recording_id=map_enriched.recording_id\n", "  inner join available_sensors on available_sensors.recording_id=map_enriched.recording_id\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e6d102a5-4c13-460e-857c-bed2862fd64e", "showTitle": false, "title": ""}}, "outputs": [], "source": [" select * from dd_leadership_pub_sbx.timeseries.silver_recording_info\n", " where label_task_id like '%VRU%'\n", " order by labeled_unique_frames desc\n", " limit 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f79ce2a5-2536-4dcc-99fd-e0c91f1b48d4", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select * from dd_leadership_pub_sbx.timeseries.silver_recording_info\n", "-- order by unique_labeled_frame desc\n", "limit 10\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "a02bac49-aa55-4585-a645-64a716cd8374", "showTitle": false, "title": ""}}, "source": ["### Dig into recording 34613 with its 780 labeled frames"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "25e6dd37-6521-409a-b230-2fff3b9c8795", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select\n", "  *\n", "from dd_leadership_pub_sbx.timeseries.recordings\n", "where\n", "  recordings.recording_id=34613"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "b1451a62-c550-491c-a41f-57e87a11a477", "showTitle": false, "title": ""}}, "source": ["## Raw RGB Images as output from sequence extraction (non-anonymized)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "865bc492-32e3-406f-a137-30461aee8543", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select \n", "    view, view_type, count(*)\n", "from dd_leadership_pub_sbx.timeseries.image_files\n", "where\n", "    anonymizer__algorithm_used is null\n", "    AND file_state='ACTIVE'\n", "    AND color_space='RGB'\n", "group by \n", "  view,\n", "  view_type\n", "order by view asc, view_type asc\n", "limit 1000"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "f9e33f2c-fdbf-42b4-aacf-e2bbfa4e28ee", "showTitle": false, "title": ""}}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "3d758c8c-defb-4303-a35f-f14cb9904686", "showTitle": false, "title": ""}}, "source": ["## Rectified PNGs as input for Labeling (non-anonymized)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c4c58505-ed61-4ad9-b9f0-ef9b7d85730f", "showTitle": false, "title": ""}}, "outputs": [], "source": ["with labeled_images as (\n", "  select \n", "    recording_id, partition_date, stream, started_at, label_task_id, label_result_file_hash\n", "  from dd_leadership_pub_sbx.timeseries.events_label\n", "  where\n", "    stream_type='CAMERA'\n", "    and partition_date=20240603\n", "  group by recording_id, partition_date, stream, started_at, label_task_id, label_result_file_hash\n", ")\n", "select\n", "  labeled_images.*\n", "  ,image_files.color_space\n", "  ,image_files.view\n", "  ,image_files.view_type\n", "  ,image_files.context\n", "  ,image_files.isp\n", "  ,(image_files.anonymizer__algorithm_used is not null) AS is_anonymized\n", "  ,image_files.rectification__type\n", "  ,image_files.rectification__warper_version\n", "  ,image_files.rectification__calstorage_uid\n", "  ,image_files.extractor_version_int AS extractor_version\n", "  ,image_files.tds_file_url AS image_tds_file_url\n", "  ,image_files.file_hash AS image_file_hash\n", "from labeled_images\n", "inner join dd_leadership_pub_sbx.timeseries.image_files\n", "  on\n", "    image_files.recording_id=labeled_images.recording_id\n", "    and image_files.partition_date=labeled_images.partition_date\n", "    and image_files.stream=labeled_images.stream\n", "    and image_files.file_started_at=labeled_images.started_at\n", "    -- and image_files.frame_number=labeled_images.frame_number\n", "where\n", "  image_files.file_state='ACTIVE'\n", "  and image_files.is_raw_image=false\n", "order by recording_id asc, labeled_images.started_at asc, label_task_id asc\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "c0c62bc8-a02e-4d13-a3b5-d8a8bb371978", "showTitle": false, "title": ""}}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "f5e07524-71aa-4ee8-9346-a924dfca7749", "showTitle": false, "title": ""}}, "source": ["## Rectified Thumbnails for import to Voxel51"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "132260b3-fae8-4d3e-998f-65bafd11da32", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select view, view_type, count(*) from dd_leadership_pub_sbx.timeseries.image_thumbnails_files\n", "group by view, view_type\n", "limit 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b94278ef-9bb9-4f9a-8be0-a57675b6f54d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select * from dd_leadership_pub_sbx.timeseries.image_thumbnails_files\n", "where \n", "  view is null\n", "  and stream='FC1'\n", "  and (\n", "    thumbnail_height>=1024\n", "    or thumbnail_width>=1024\n", "  )\n", "LIMIT 100"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d4ee40cc-e44c-42ee-ab6d-2005773fc160", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT count(distinct file_hash) FROM dd_leadership_pub_sbx.timeseries.image_files\n", "WHERE file_name LIKE 'ALLIANCE-DC02_%_TVfront_f00007_RGB_ContextA_rectified.png'"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "b210bc35-6d36-4106-a060-77f7c0948717", "showTitle": false, "title": ""}}, "source": ["# Create Dataset for AI Fusion"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f9e8e50f-0e3d-424b-8f5b-3215cb9dc7bc", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select count(distinct file_hash) from silver.tds.file_entries\n", "where file_name LIKE 'ALLIANCE-DC02_%_TVfront_f00007_RGB_ContextA_rectified.png'"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c1e79316-ffe9-414a-8c59-7632016e0c2f", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TEMPORARY VIEW fusion_image_selection AS\n", "  select\n", "    images.file_name AS image_file_name\n", "    ,images.file_hash AS image_file_hash\n", "    ,concat(substring_index(images.file_name, \"_TVfront_\", 1), \".scene\") AS scene_file_name\n", "    ,scene_files.file_hash AS scene_file_hash\n", "  from silver.tds.file_entries as images\n", "  inner join silver.tds.file_entries as scene_files\n", "    on\n", "      scene_files.file_name=concat(substring_index(images.file_name, \"_TVfront_\", 1), \".scene\")\n", "  where images.file_name LIKE 'ALLIANCE-DC02_%_TVfront_f00007_RGB_ContextA_rectified.png'"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ff99bdd4-3a2f-4aa6-9248-7632952a3e3c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE TEMPORARY VIEW fusion_image_split_set AS\n", "  SELECT\n", "    scene_file_hash AS split_file_hash\n", "  FROM fusion_image_selection\n", "  GROUP BY split_file_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6ca0c66f-3187-4feb-928e-bf1007315e48", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select count(distinct image_file_hash), count(distinct scene_file_hash)\n", "from fusion_image_selection"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bccc8f97-9e98-4544-9475-31734e7d150d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select * from fusion_image_selection limit 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "54f03314-b355-4b10-ab30-7c92a05e1577", "showTitle": false, "title": ""}}, "outputs": [], "source": ["create or replace temporary view fusion_all_split_data as\n", "  select \n", "    fusion_image_selection.scene_file_hash AS fusion_image_scene_hash\n", "    ,all_recordings.file_hash AS file_hash\n", "    ,all_recordings.stream_name AS stream_name\n", "    ,all_recordings.file_size_bytes AS file_size_bytes\n", "  from fusion_image_selection\n", "  left join dd_leadership_pub_sbx.data_tiering_service.all_recordings\n", "    on\n", "      fusion_image_selection.scene_file_hash = all_recordings.split_hash"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a24d0213-3f8a-4477-b9ff-85639cd87670", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- detect if there are non matched splits\n", "select \n", "  assert_true(count(*) = 0)\n", "from fusion_all_split_data\n", "where file_hash is null"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "39e8566b-5da6-4083-b353-86f7930aced7", "showTitle": false, "title": ""}}, "outputs": [], "source": ["with cte as (\n", "  select\n", "    stream_name\n", "    ,max(file_size_bytes) as file_size_bytes\n", "  from fusion_all_split_data\n", "  group by file_hash, stream_name\n", ")\n", "select \n", "  stream_name\n", "  ,round(sum(file_size_bytes)/1024/1024/1024,0) as file_size_gigabytes\n", "  ,count(*) as file_count\n", "from cte\n", "group by stream_name"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "22089073-a118-4b9c-81b0-d56c3dca3b6d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["select \n", "  count(distinct split_file_hash) as split_count\n", "from fusion_image_split_set limit 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "89b47a1f-2673-457d-acba-b9a97ef85c9c", "showTitle": false, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "environmentMetadata": {"base_environment": "", "client": "1"}, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "99.50 Timeseries - Gold Layer", "widgets": {}}}, "nbformat": 4, "nbformat_minor": 0}