-- Databricks notebook source
-- MAGIC %md
-- MAGI<PERSON> ```python
-- MAGIC # ===================================================================================
-- MAGIC #  C O P Y R I G H T
-- MAGIC # -----------------------------------------------------------------------------------
-- MAGIC #  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- MAGIC # ===================================================================================
-- MAGIC ```

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Create global configuration table

-- COMMAND ----------

CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.data_tiering_service.__configuration (
-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.data_tiering_service.__configuration (
  key STRING NOT NULL,
  value STRING,
  updated_at TIMESTAMP DEFAULT current_timestamp()
)
TBLPROPERTIES ('delta.feature.allowColumnDefaults' = 'supported')

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Define all parameters

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW parameters AS
  SELECT 'min_file_age_days_before_tiering' AS key, 60 AS value
  UNION SELECT 'dataset_tag_keep_on_hotstorage' AS key, '[\'keep_on_hot_storage\',\'to_be_retained\']' AS value
  UNION SELECT 'tds_pools_to_consider' AS key, '[\'g3vprdaq\']' AS value
  UNION SELECT 'excluded_file_extensions' AS key, '[\'recbin\']' AS value
  ;
SELECT * FROM parameters

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Update parameters

-- COMMAND ----------

MERGE INTO dd_leadership_pub_sbx.data_tiering_service.__configuration AS target
  USING parameters AS source
  ON target.key = source.key
  WHEN MATCHED THEN UPDATE SET value=source.value
  WHEN NOT MATCHED THEN INSERT (key, value) VALUES (source.key, source.value)

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Check parameters

-- COMMAND ----------

SELECT * FROM dd_leadership_pub_sbx.data_tiering_service.__configuration

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Define valid streams to be considered for tiering
-- MAGIC
-- MAGIC All streams that are not mentioned here, must not be tiered down from hot storages.

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.data_tiering_service.valid_streams (stream_name STRING);
INSERT INTO dd_leadership_pub_sbx.data_tiering_service.valid_streams VALUES
  ('FC1')
  ,('FC2')
  ,('FC2STEREO')
  ,('FSCLEFT')
  ,('FSCRIGHT')
  ,('RSCLEFT')
  ,('RSCRIGHT')
  ,('RTC')
  ,('TVLEFT')
  ,('TVFRONT')
  ,('TVRIGHT')
  ,('TVREAR')
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Define streams that exclude a split and all its streams from storage down tiering
-- MAGIC
-- MAGIC If a stream is mentioned in this table, the whole split must be kept on hot storage. It overrides all other criteria that should lead to a down tiering from hot storage.

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.data_tiering_service.tiering_blocking_streams (stream_name STRING);
INSERT INTO dd_leadership_pub_sbx.data_tiering_service.tiering_blocking_streams VALUES
  ('QTLEFT')
  ,('QTRIGHT')
  ,('QTFRONT')
  ,('QTBACK')
;

-- COMMAND ----------


