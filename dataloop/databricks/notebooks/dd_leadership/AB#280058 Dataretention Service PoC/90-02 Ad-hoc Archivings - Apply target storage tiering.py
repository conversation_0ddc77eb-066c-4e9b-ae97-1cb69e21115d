# Databricks notebook source
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

# MAGIC %md
# MAGIC # Documentation

# COMMAND ----------

# MAGIC %md
# MAGIC ## Parameters

# COMMAND ----------

from mdm.lib.mdm_search_module.v2.constants import Environment

# Environment
environment = Environment.PROD

# Databricks datastore configuration
CATALOG_NAME="dd_leadership_pub_sbx"
SCHEMA_NAME="data_tiering_service"
TIERING_INPUT_TABLE="90_adhoc_bringup_measurements_tiering"
TIERING_STATUS_TABLE="__state_tracking_table_90_02"

# Parallicity configuration
SLICE_SIZE = 10000
N_PARALLEL_CONNECTIONS = 500

# COMMAND ----------

# MAGIC %md
# MAGIC ## parameters derived constants

# COMMAND ----------

TIERING_TABLE_FULL_PATH=f"{CATALOG_NAME}.{SCHEMA_NAME}.{TIERING_INPUT_TABLE}"
TIERING_STATUS_TABLE_FULL_PATH=f"{CATALOG_NAME}.{SCHEMA_NAME}.{TIERING_STATUS_TABLE}"

# COMMAND ----------

# MAGIC %md
# MAGIC # Setup

# COMMAND ----------

import asyncio
import datetime
import logging
import uuid

import aiohttp
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_mdm_accessor import (
    AsyncMdmAccessor,
    MdmException,
    MdmFileEntryNotFoundException,
    TDSFileCopyResponse,
    TDSFileEntry,
    TDSStorageTierEnum,
)
from pyspark.sql import Row
from pyspark.sql import functions as F
from pyspark.sql.functions import col, lit
from pyspark.sql.types import *

# COMMAND ----------

# logging
# logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("Data Tiering")

logging.getLogger("azure").setLevel(logging.WARN)
logging.getLogger("CachedAadTokenRetriever").setLevel(logging.WARN)
logging.getLogger("urllib3.connectionpool").setLevel(logging.WARN)
logging.getLogger("msal.token_cache").setLevel(logging.WARN)
logging.getLogger("msal").setLevel(logging.WARN)
logging.getLogger("py4j").setLevel(logging.WARN)

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET
)

aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# Environment dependent setup
if environment == Environment.QA:
    print("Using QA instance")
    # APIM Setup
    apim_kv_url = "https://qapacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

    # TDS
    TDS_BASE_URL = "https://data-delivery-api-qa.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-paceqa-westeurope/.default"
elif environment == Environment.PROD:
    print("Using PROD instance")
    # APIM Setup
    apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value
    
    # TDS
    TDS_BASE_URL = "https://data-delivery-api.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-pace-westeurope/.default"

# TDS
TDS_HEADERS = {"apikey": APIM_KEY}

mdm_accessor = AsyncMdmAccessor(
    credential_provider=aad_token_cache,
    environment=environment,
    use_api_gateway=True,
    apim_key=APIM_KEY,
)

# COMMAND ----------

# MAGIC %md
# MAGIC # Bookkeeping about storage tiering

# COMMAND ----------

spark.sql(f"""CREATE TABLE IF NOT EXISTS {TIERING_STATUS_TABLE_FULL_PATH} (
    file_hash STRING
    ,tds_fid STRING
    ,storage_tier STRING
    ,error_msg STRING
    ,executed_at TIMESTAMP
)""")

# COMMAND ----------

def add_logging_rows(rows):
    data_struct = StructType([
        StructField("file_hash", StringType(), True),
        StructField("tds_fid", StringType(), True),
        StructField("storage_tier", StringType(), True),
        StructField("executed_at", TimestampType(), True),
        StructField("error_msg", StringType(), True),
    ])

    spark.sql("DROP VIEW IF EXISTS logging_rows")
    df = spark.createDataFrame(rows, data_struct)
    df.createTempView("logging_rows")

    try:
        spark.sql(f"""MERGE INTO {TIERING_STATUS_TABLE_FULL_PATH} AS target
                USING logging_rows AS source
                ON
                    source.file_hash=target.file_hash
                    AND source.tds_fid=target.tds_fid
                WHEN MATCHED THEN UPDATE SET *
                WHEN NOT MATCHED BY TARGET THEN INSERT *
                """)
    except Exception as e:
        logging.error(e)
        raise e

# COMMAND ----------

# MAGIC %md
# MAGIC # Methods to get data that needs to be tiered to archive

# COMMAND ----------

def get_files_to_archive():
    df_process_log = (
        spark
            .read
            .format("delta")
            .table(TIERING_STATUS_TABLE_FULL_PATH)
            .select(
                col("file_hash").alias("log_file_hash")
                ,col("tds_fid").alias("log_tds_fid")
                ,col("storage_tier").alias("log_storage_tier")
                ,col("error_msg").alias("log_error_msg")
            )
    )
    df_todo_list = (
        spark
            .read
            .format("delta")
            .table(TIERING_TABLE_FULL_PATH)
            .where(
                (col("file_move_to_archive")==lit(True))
                & (F.lower("file_state") == lit("active"))
                & (col("stream_is_valid_for_tiering") == lit(True))
            )
            .join(
                df_process_log, 
                df_process_log.log_tds_fid == col("file_source_tds_fid"),
                "outer"
            )
            .where(
                df_process_log.log_tds_fid.isNull()
                # storage tier is not yet at archived
                & (
                    (F.lower("log_storage_tier") != lit("archived"))
                    | df_process_log.log_storage_tier.isNull()
                )
                # does not have an error message
                & (
                    (F.trim("log_error_msg") == "")
                    | df_process_log.log_error_msg.isNull()
                )
            )
            .select(
                col("file_hash")
                ,col("file_source_tds_fid").alias("tds_fid")
            )
    )
    return df_todo_list
# display(get_files_to_archive().limit(100))

# COMMAND ----------

total_files_to_archive = get_files_to_archive().count()
print(f"Going to archive {total_files_to_archive} files")

# COMMAND ----------

async def archive_file(
    session: aiohttp.ClientSession, tds_client: AsyncMdmAccessor, file_hash: str, fid: str, correlation_id: uuid.UUID
):
    error_msg = ""

    try:
        await mdm_accessor.change_tier(session, request_correlation_id=correlation_id, fid=fid, tier=TDSStorageTierEnum.ARCHIVE)
    except Exception as e:
        logging.error(f"[{fid}] Caught exception on archiving: {e}")
        error_msg = str(e)

    return {
        "file_hash": file_hash,
        "tds_fid": fid,
        "storage_tier": "archived",
        "executed_at": datetime.datetime.now(),
        "error_msg": error_msg
    }

# COMMAND ----------

aiohttp_connector = aiohttp.TCPConnector(limit=N_PARALLEL_CONNECTIONS)
aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)

async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    this_correlation_id = uuid.uuid4()

    files_archived = 0

    while True:
        files_to_archive = get_files_to_archive().limit(SLICE_SIZE).collect()
        print(f"{files_archived}/{total_files_to_archive} files are archived!")
        if len(files_to_archive) == 0:
            break

        command_state = []
        try:
            command_state = await asyncio.gather(
                *[
                    archive_file(session, mdm_accessor, file_hash=this_file["file_hash"], fid=this_file["tds_fid"], correlation_id=this_correlation_id)
                    for this_file in files_to_archive
                ]
            )
        except Exception as e:
            print(f"An exception occured {e}")
            traceback.print_exc()

        add_logging_rows(command_state)
        files_archived += len(command_state)

# COMMAND ----------

display(spark.sql(f"""SELECT * FROM {TIERING_STATUS_TABLE_FULL_PATH} LIMIT 100"""))

# COMMAND ----------


