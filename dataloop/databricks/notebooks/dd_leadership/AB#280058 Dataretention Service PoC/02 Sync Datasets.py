# Databricks notebook source
import asyncio
import datetime
import itertools
import json
import logging
import traceback
import uuid

import aiohttp
import pyspark.sql.functions as sf
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_dataset_accessor import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ccessor
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

from mdm.lib.mdm_search_module.v2.constants import Environment

ENVIRONMENT = Environment.PROD
N_PARALLEL_CONNECTIONS = 100
SLICE_SIZE = 100

TARGET_CATALOG="dd_leadership_pub_sbx"
TARGET_SCHEMA="data_tiering_service"

# COMMAND ----------

# MAGIC %md
# MAGIC # Setup

# COMMAND ----------

# logging
logger = logging.getLogger("Data Copying")
logger.setLevel(logging.ERROR)

# COMMAND ----------

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"

AAD_SP_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_APPID, client_secret=AAD_SP_SECRET
)
aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# unique run id
run_id = uuid.uuid4()

# Table Names
table_datasets = f"{TARGET_CATALOG}.{TARGET_SCHEMA}.datasets"
table_dataset_entries = f"{TARGET_CATALOG}.{TARGET_SCHEMA}.dataset_entries"

# Environment dependent setup
if ENVIRONMENT == Environment.QA:
    print("Using QA instance")
    # APIM Setup
    apim_kv_url = "https://qapacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

elif ENVIRONMENT == Environment.PROD:
    print("Using PROD instance")
    # APIM Setup
    apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

# COMMAND ----------

# MAGIC %md
# MAGIC # Helper methods

# COMMAND ----------

# MAGIC %md
# MAGIC ## Datasets Table

# COMMAND ----------

spark.sql(f"""CREATE TABLE IF NOT EXISTS {table_datasets} (
  id STRING,
  version STRING,
  state STRING,
  major_version INT,
  minor_version INT,
  author STRING,
  catalog STRING,
  tags ARRAY<STRING>,
  checksum STRING,
  count BIGINT,
  metadata STRING,
  createTime TIMESTAMP,
  updateTime TIMESTAMP,
  description STRING,
  fetcher_run_id STRING,
  fetcher_process_id STRING,
  fetched_at TIMESTAMP
);""")

# COMMAND ----------

def _convert_datasets_to_dataframe(datasets, fetched_at, fetcher_process_id, fetcher_run_id):
    return (
        spark.createDataFrame(
            Row(
                id=x.id,
                version=x.version,
                state=x.state,
                major_version=int(x.major_version),
                minor_version=int(x.minor_version),
                type=x.type,
                author=x.author.json(),
                # author_applications=json.dumps(x.author.applications),
                catalog=x.catalog.json(),
                tags=x.tags,
                checksum=x.checksum,
                count=x.count,
                metadata=x.metadata.json(),
                createTime=x.createTime,
                updateTime=x.updateTime,
                description=x.description,
                fetcher_run_id=str(fetcher_run_id),
                fetcher_process_id=str(fetcher_process_id),
                fetched_at=fetched_at
            )
            for x in datasets
        )
        .withColumn("major_version", col("major_version").cast(IntegerType()))
        .withColumn("minor_version", col("minor_version").cast(IntegerType()))
    )

# COMMAND ----------

def _get_latest_updated_dataset():
    return (
        spark
            .read
            .format("delta")
            .table(table_datasets)
            .select(col("updateTime"))
            .agg(sf.max("updateTime").alias("maxUpdateTime"))
            .select(
                coalesce(
                    col("maxUpdateTime"),
                    lit(datetime.datetime(1900, 1, 1, 0, 0, 0))
                ).alias("maxUpdateTime")
            )
            .collect()
        )[0][0]

# COMMAND ----------

# MAGIC %md
# MAGIC ## Dataset Entries

# COMMAND ----------

spark.sql(f"""CREATE TABLE IF NOT EXISTS {table_dataset_entries} (
  dataset_id STRING,
  dataset_version STRING,
  dataset_checksum STRING,
  entry STRING,
  fetcher_run_id STRING,
  fetcher_process_id STRING,
  fetched_at TIMESTAMP
);""")

# COMMAND ----------

def _convert_datasetentries_to_dataframe(dataset, dataset_entries, fetched_at, fetcher_process_id, fetcher_run_id):
    return spark.createDataFrame(
        Row(
            dataset_id=dataset.id,
            dataset_version=dataset.version,
            dataset_checksum=dataset.checksum,
            entry=x,
            fetcher_run_id=str(fetcher_run_id),
            fetcher_process_id=str(fetcher_process_id),
            fetched_at=fetched_at
        )
        for x in dataset_entries
    )

# COMMAND ----------

# MAGIC %md
# MAGIC # Fetch and persist datasets

# COMMAND ----------

dataset_fetcher_process_id = uuid.uuid4()

aiohttp_connector = aiohttp.TCPConnector(limit=N_PARALLEL_CONNECTIONS)
aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)
async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    dataset_accessor = AsyncDatasetAccessor(session, aad_token_cache, ENVIRONMENT, apim_key=APIM_KEY)

    # the dataset service misses the option to filter by update timestamp, so we need to fetch everything and filter afterwards
    datasets = await dataset_accessor.list_all_datasets(dataset_fetcher_process_id)
    # dataset_entries = await dataset_accessor.get_all_dataset_entries(uuid.uuid4(), dataset_id=datasets[0].id)

# COMMAND ----------

df = _convert_datasets_to_dataframe(datasets=datasets, fetched_at=datetime.datetime.now(), fetcher_process_id=dataset_fetcher_process_id, fetcher_run_id=run_id)

# COMMAND ----------

# MAGIC %md
# MAGIC Identify changed datasets for later

# COMMAND ----------

df_current_datasets = (
    spark
        .read
        .format("delta")
        .table(table_datasets)
)
df_new_or_changed_datasets = (
    df
        .select(
            col("id")
            ,col("count").alias("entry_count")
            ,col("version")
            ,col("major_version")
            ,col("minor_version")
            ,col("checksum")
        )
        .join(df_current_datasets, df.id == df_current_datasets.id, "leftouter")
        .where(
            df_current_datasets.id.isNull()
            | (df_current_datasets.major_version < df.major_version)
            | (df_current_datasets.minor_version < df.minor_version)
        )
        .select(
            df.id
            ,col("entry_count")
            ,df.version
            ,df.checksum
        )
)

# COMMAND ----------

df.createOrReplaceTempView("fetched_datasets")

# COMMAND ----------

display(
    spark.sql(f"""MERGE INTO {table_datasets} AS target
          USING fetched_datasets AS source
          ON
            source.id=target.id
            AND source.version=target.version
          WHEN NOT MATCHED BY TARGET
            THEN
                INSERT *
          """)
)
# (df
#  .write
#  .format("delta")
#  .option("mergeSchema", "true")
#  .saveAsTable(table_datasets, mode="overwrite")
# )

# COMMAND ----------

spark.sql(f"OPTIMIZE {table_datasets}")
spark.sql(f"VACUUM {table_datasets}")

# COMMAND ----------

# MAGIC %md
# MAGIC # Fetch and persist dataset entries

# COMMAND ----------

# MAGIC %md
# MAGIC ## Identify entries to fetch

# COMMAND ----------

# MAGIC %md
# MAGIC ### Missing entries

# COMMAND ----------

df_current_datasets = (
    spark
        .read
        .format("delta")
        .table(table_datasets)
        .select(
            col("id")
            ,col("count").alias("entry_count")
            ,col("version")
            ,col("checksum")
        )
)

# COMMAND ----------

df_current_entries = (
    spark
        .read
        .format("delta")
        .table(table_dataset_entries)
        .select(
            col("dataset_id")
            ,col("entry")
        )
        .groupBy(col("dataset_id"))
        .count()
        .select(
            col("dataset_id")
            ,col("count").alias("entry_count")
        )
)

# COMMAND ----------


df_missing_dataset_entries = (
    df_current_datasets
        .join(df_current_entries, df_current_datasets.id==df_current_entries.dataset_id, "leftouter")
        .where(
            df_current_entries.entry_count.isNull()
            | (df_current_datasets.entry_count != df_current_entries.entry_count)
        )
        .select(
            df_current_datasets.id
            ,df_current_datasets.entry_count
            ,df_current_datasets.version
            ,df_current_datasets.checksum
        )
)

# COMMAND ----------

# MAGIC %md
# MAGIC ### Build dataframe of datasets to fetch by adding also the updated datasets

# COMMAND ----------

df_entries_to_fetch = (
    df_new_or_changed_datasets
    .union(
        df_missing_dataset_entries
    )
    .select(
        col("id")
        ,col("entry_count")
        ,col("version")
        ,col("checksum")
    )
    .groupBy(
        col("id")
        ,col("version")
        ,col("checksum")
        ,col("entry_count")
    )
    .count()
    .withColumnRenamed("count", "duplicate_source_count")
    .withColumnRenamed("entry_count", "count")
).where(col("count") > lit(0))

# COMMAND ----------

entries_to_fetch = df_entries_to_fetch.collect()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Fetch entries

# COMMAND ----------

async def _fetch_dataset_entries(accessor, dataset_row, run_id, process_id):
    logging.info(f"Fetching dataset entries for {dataset_row.id}")
    entries = await dataset_accessor.get_all_dataset_entries(process_id, dataset_id=dataset_row.id, dataset_version=dataset_row.version)
    return [
        {
            "dataset_id": dataset_row.id,
            "dataset_version": dataset_row.version,
            "dataset_checksum": dataset_row.checksum,
            "entry": entry,
            "fetcher_run_id": str(run_id),
            "fetcher_process_id": str(process_id),
            "fetched_at": datetime.datetime.now()
        }
        for entry in entries
    ]

# COMMAND ----------

def update_entries(entries):
    # remove duplicates from entries first
    df_update_entries = (spark
                         .createDataFrame(entries)
                         .groupBy(
                             col("dataset_id")
                             ,col("dataset_version")
                             ,col("dataset_checksum")
                             ,col("entry")
                         )
                         .agg(
                             sf.max("fetcher_run_id").alias("fetcher_run_id")
                             ,sf.max("fetcher_process_id").alias("fetcher_process_id")
                             ,sf.max("fetched_at").alias("fetched_at")
                         )
    )
    df_update_entries.createOrReplaceTempView("entries_update")
    spark.sql(f"""MERGE INTO {table_dataset_entries} AS target
              USING entries_update AS source
              ON
                source.dataset_id=target.dataset_id
                AND source.dataset_version=target.dataset_version
                AND source.entry=target.entry
              WHEN MATCHED THEN UPDATE SET *
              WHEN NOT MATCHED BY TARGET THEN INSERT *
              """)

# COMMAND ----------

entries_fetcher_process_id = uuid.uuid4()

aiohttp_connector = aiohttp.TCPConnector(limit=N_PARALLEL_CONNECTIONS)
aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)
async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    dataset_accessor = AsyncDatasetAccessor(session, aad_token_cache, ENVIRONMENT, apim_key=APIM_KEY)

    n_datasets_to_fetch = len(entries_to_fetch)
    i_dataset = 0

    while True:
        if i_dataset >= n_datasets_to_fetch:
            break

        print(f"Starting with {i_dataset} of {n_datasets_to_fetch}")
        results = []
        try:
            results = await asyncio.gather(
                *[
                    _fetch_dataset_entries(dataset_accessor, this_dataset , run_id=run_id, process_id=entries_fetcher_process_id)
                    for this_dataset in entries_to_fetch[i_dataset:i_dataset+SLICE_SIZE]
                ]
            )
            # flatten
            results = list(itertools.chain.from_iterable(results))

            update_entries(results)

            # if all successful increment start index for next batch
            i_dataset += SLICE_SIZE
        except Exception as e:
            logging.warning(f"An exception occured {e}")
            traceback.print_exc()

# COMMAND ----------

# MAGIC %md
# MAGIC ## Optimize target table

# COMMAND ----------

spark.sql(f"OPTIMIZE {table_dataset_entries}")
spark.sql(f"VACUUM {table_dataset_entries}")

# COMMAND ----------

display(spark.sql(f"ANALYZE TABLE {table_dataset_entries} COMPUTE STATISTICS FOR ALL COLUMNS"))

# COMMAND ----------

# MAGIC %md
# MAGIC # Debugging

# COMMAND ----------

# display(spark.sql(f"""SELECT dataset_id, dataset_version, entry, count(*) FROM {table_dataset_entries} GROUP BY dataset_id, dataset_version, entry HAVING count(*) > 1"""))

# COMMAND ----------

# display(spark.sql(f"""DELETE FROM {table_dataset_entries} WHERE dataset_id='62a24532-4ed8-4919-bb6b-100955d49147' AND dataset_version='6.0'"""))
# display(spark.sql(f"""DELETE FROM {table_dataset_entries}"""))

# COMMAND ----------

# 
