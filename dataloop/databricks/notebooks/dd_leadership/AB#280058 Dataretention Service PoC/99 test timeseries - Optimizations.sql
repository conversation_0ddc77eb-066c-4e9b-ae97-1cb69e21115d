-- Databricks notebook source
-- MAGIC %md
-- MAGIC # Purpose
-- MAGIC Execute table optimizations for better performance in intended use-cases

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # dd_leadership_pub_sbx.timeseries.events_flexray

-- COMMAND ----------

OPTIMIZE dd_leadership_pub_sbx.timeseries.events_flexray ZORDER BY (vin, started_at)

-- COMMAND ----------

VACUUM dd_leadership_pub_sbx.timeseries.events_flexray

-- COMMAND ----------

AN<PERSON><PERSON><PERSON><PERSON> TABLE dd_leadership_pub_sbx.timeseries.events_flexray COMPUTE STATISTICS FOR COLUMNS
  recording_id
  ,partition_date
  ,vin
  ,license_plate
  ,started_at
  ,started_timestamp
  ,frame
  ,name;
