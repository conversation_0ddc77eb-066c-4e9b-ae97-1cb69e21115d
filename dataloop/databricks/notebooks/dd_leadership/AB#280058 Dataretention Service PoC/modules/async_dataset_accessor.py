"""Asynchronous acessor for MDM API."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
# ============================================================================================================
# C O P Y R I G H T                                                                                          \
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.                                  \
# ============================================================================================================

import logging
import random
import time
import uuid
from datetime import datetime
from enum import Enum
from typing import Dict, List, Literal, Optional

import aiohttp
from modules.aad_token_cacher import AadTokenCache
from pydantic import BaseModel

from mdm.lib.mdm_search_module.v2.constants import Environment


class DatasetAuthorEntry(BaseModel):
    object_id: str
    name: Optional[str] = None

class DatasetAuthors(BaseModel):
    users: Optional[List[DatasetAuthorEntry]] = None
    applications: Optional[List[DatasetAuthorEntry]] = None

class DatasetPlatformEnum(Enum):
    MDM = "mdm"
    FML = "fml"

class DataCatalogService(BaseModel):
    platform: DatasetPlatformEnum

class DatasetCatalog(BaseModel):
    service: Optional[DataCatalogService] = None
    tenant: Optional[str] = None
    url: Optional[str] = None

class DatasetMetadata(BaseModel):
    dataset_name: Optional[str] = None
    namespace: Optional[str] = None
    namespace_version: Optional[str] = None
    id: Optional[str] = None

class DatasetDescriptor(BaseModel):

    id: str
    version: str
    state: str
    major_version: int
    minor_version: int
    type: str
    author: DatasetAuthors
    catalog: DatasetCatalog
    tags: Optional[List[str]] = None
    
    checksum: str
    """ Checksum of the dataset """

    count: int
    """ Number of entries in the dataset """

    metadata: Optional[DatasetMetadata] = None
    """ Metadata of the dataset """

    createTime: Optional[datetime] = None
    updateTime: Optional[datetime] = None
    description: Optional[str] = None

class DatasetPagination(BaseModel):
    pageNumber: int
    pageSize: int
    totalCount: int
    totalPages: int

class DatasetLinks(BaseModel):
    first: str
    prev: Optional[str] = None
    next: Optional[str] = None
    last: str

class DatasetListResponse(BaseModel):
    pagination: DatasetPagination
    links: DatasetLinks
    items: List[DatasetDescriptor]

class DatasetEntriesResponse(BaseModel):
    pagination: DatasetPagination
    links: DatasetLinks
    entries: List[str]

### Exceptions

class TooManyRequestsException(Exception):
    """Exception generated on http status 429."""

    pass

class RetriesFailedException(Exception):
    """All retries failed"""

    pass

### Functionality

class AsyncDatasetAccessor:
    """MDM Accessor for data deletion."""

    def __init__(
        self,
        session: aiohttp.ClientSession,
        credential_provider: AadTokenCache,
        environment: Environment,
        apim_key: str = None,
    ):
        """Create MDM accessor with all needed dependencies.

        Args:
            credential_provider (AadTokenCache): AAD Token Cacher instance.
            environment (Environment): Envronment (DEV, QA, PROD) to execute against
            use_api_gateway (bool, optional): Shall Data Delivery API Gateway be used. Defaults to True, i.e. API gateway is used by default.
            credential_provider_deletion (Optional[AadTokenCache], optional): Optionally, you can provide a dedicated credential provider for deletion requests. If no dedicated deletion credential is provided, the default credential_provider is used.
            apim_key (str, optional): In case your service has an APIM key, use it. Otherwise you fall into the default layer of 10req/s.

        Raises:
            Exception: _description_
        """
        self._session = session
        self._credential_provider = credential_provider

        self._apim_key = apim_key

        if environment == Environment.PROD:
            self._base_url = "https://data-delivery-api.ad-alliance.biz/datasets-service"
            self._aad_audience = "api://sp-pace-datasets-api-dll-prod/.default"
        elif environment == Environment.QA:
            self._base_url = "https://data-delivery-api-qa.ad-alliance.biz/datasets-service"
            self._aad_audience = "api://sp-pace-datasets-api-dll-qa/.default"
        else:
            raise Exception(f"Unknown MDM environment '{environment}")

        self._max_retries = 10
        self._min_wait_time_seconds = 1.0

    def _get_default_headers(self, correlation_id: uuid.UUID, token_provider: AadTokenCache) -> Dict[str, str]:
        bearer_token = token_provider.get_token(self._aad_audience).token
        result = {
            "Authorization": f"Bearer {bearer_token}",
            "x-client-request-id": str(correlation_id),
        }
        if self._apim_key is not None:
            result["apikey"] = self._apim_key
        return result

    async def _list_all_datasets(
        self, request_correlation_id: uuid.UUID, page_number: int = 1, page_size: int = 100, tags: List[str] = []
    ) -> DatasetListResponse:
        headers = self._get_default_headers(request_correlation_id, token_provider=self._credential_provider)

        params = {
            "pageNumber": page_number,
            "pageSize": page_size,
            "tags": tags,
        }

        for i_try in range(self._max_retries):
            try:
                async with self._session.get(
                    f"{self._base_url}/api/v2/datasets", params=params, headers=headers
                ) as response:
                    # errors with dedicated error handler
                    if response.status == 429:
                        retry_wait_time = float(response.headers.get("Retry-After", str(self._min_wait_time_seconds)))
                        actual_wait_time = retry_wait_time * (1 + random.random())
                        time.sleep(actual_wait_time)
                        raise TooManyRequestsException()
                    # fail for other errors
                    response.raise_for_status()

                    # process data in case of success
                    json_response = await response.json()
                    results = DatasetListResponse(**json_response)
                    return results
            except TooManyRequestsException:
                continue

            # final one
            raise RetriesFailedException(f"Finally failed after {self._max_retries}")
    
    async def _get_dataset_entries(
            self, request_correlation_id: uuid.UUID, dataset_id: str, page_number: int = 1, page_size: int = 100
    ) -> DatasetEntriesResponse:
        headers = self._get_default_headers(request_correlation_id, token_provider=self._credential_provider)

        params = {
            "pageNumber": page_number,
            "pageSize": page_size,
        }

        for i_try in range(self._max_retries):
            try:
                async with self._session.get(
                    f"{self._base_url}/api/v2/datasets/{dataset_id}/entries", params=params, headers=headers
                ) as response:
                    # errors with dedicated error handler
                    if response.status == 429:
                        retry_wait_time = float(response.headers.get("Retry-After", str(self._min_wait_time_seconds)))
                        actual_wait_time = retry_wait_time * (1 + random.random())
                        time.sleep(actual_wait_time)
                        raise TooManyRequestsException()
                    # fail for other errors
                    response.raise_for_status()

                    # process data in case of success
                    json_response = await response.json()
                    results = DatasetEntriesResponse(**json_response)
                    return results
            except TooManyRequestsException:
                continue

            # final one
            raise RetriesFailedException(f"Finally failed after {self._max_retries}")

    async def _get_entries_of_dataset_version(
            self, request_correlation_id: uuid.UUID, dataset_id: str, version: str, page_number: int = 1, page_size: int = 100
    ) -> DatasetEntriesResponse:
        headers = self._get_default_headers(request_correlation_id, token_provider=self._credential_provider)

        params = {
            "pageNumber": page_number,
            "pageSize": page_size,
        }

        for i_try in range(self._max_retries):
            try:
                async with self._session.get(
                    f"{self._base_url}/api/v2/datasets/{dataset_id}/versions/{version}/entries", params=params, headers=headers
                ) as response:
                    # errors with dedicated error handler
                    if response.status == 429:
                        retry_wait_time = float(response.headers.get("Retry-After", str(self._min_wait_time_seconds)))
                        actual_wait_time = retry_wait_time * (1 + random.random())
                        time.sleep(actual_wait_time)
                        raise TooManyRequestsException()
                    # fail for other errors
                    response.raise_for_status()

                    # process data in case of success
                    json_response = await response.json()
                    results = DatasetEntriesResponse(**json_response)
                    return results
            except TooManyRequestsException:
                continue

            # final one
            raise RetriesFailedException(f"Finally failed after {self._max_retries}")

    async def list_all_datasets(
            self,  request_correlation_id: uuid.UUID, tags: List[str] = []
    ):
        result = []
        page_number = 1
        page_size = 100

        while True:
            this_page = await self._list_all_datasets(request_correlation_id, page_number, page_size, tags)
            result.extend(this_page.items)

            if this_page.pagination.totalPages > page_number:
                page_number += 1
            else:
                return result

    async def get_all_dataset_entries(
            self, request_correlation_id: uuid.UUID, dataset_id: str, dataset_version: str
    ) -> List[str]:
        page_number = 1
        page_size = 10000
        result = []
        while True:
            this_page = await self._get_entries_of_dataset_version(request_correlation_id, dataset_id, dataset_version, page_number, page_size)
            result.extend(this_page.entries)

            if this_page.pagination.totalPages > page_number:
                page_number += 1
            else:
                return result
            
    # async def create_new_dataset(self, request_correlation_id: uuid.UUID, dataset_name: str, author_users: List[str], tags: List[str], description: str = "", state: str = "public", type: str = "image/jpeg"):
    #     headers = self._get_default_headers(request_correlation_id, token_provider=self._credential_provider)

    #     async with self._session.post(
    #         f"{self._base_url}/api/v2/datasets", body=..., headers=headers
    #     ) as response:
    #         pass

# {
#   "state": "public",
#   "type": "image/jpeg",
#   "catalog": {
#     "service": {
#       "platform": "mdm"
#     },
#     "tenant": "string",
#     "url": "string"
#   },
#   "entries": [
#     "8509e7210ca4f7074f06469b8e5562e6cba1aaa4c439d55720f52bf78e0a3bfa"
#   ],
#   "author": {
#     "users": [
#       {
#         "object_id": "fb701143-fab1-48f1-a628-1a9bd6655943",
#         "name": "string"
#       }
#     ],
#     "applications": [
#       {
#         "object_id": "294b21b4-8bfe-4fee-9ba5-1e5871741d89"
#       }
#     ]
#   },
#   "tags": [],
#   "label_entries": {
#     "additionalProp1": [
#       {}
#     ],
#     "additionalProp2": [
#       {}
#     ],
#     "additionalProp3": [
#       {}
#     ]
#   },
#   "metadata": {
#     "dataset_name": "string",
#     "namespace": "string",
#     "namespace_version": "string",
#     "id": "string"
#   },
#   "description": "string"
# }