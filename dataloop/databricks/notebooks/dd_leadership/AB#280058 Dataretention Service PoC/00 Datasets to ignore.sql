-- Databricks notebook source
-- MAGIC %md
-- MAGI<PERSON> ```python
-- MAGIC # ===================================================================================
-- MAGIC #  C O P Y R I G H T
-- MAGIC # -----------------------------------------------------------------------------------
-- MAGIC #  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- MAGIC # ===================================================================================
-- MAGIC ```

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Datasets to ignore
-- MAGIC
-- MAGIC During time some datasets might no longer be relevant. But as these datasets cannot be mutated anymore we must put them on a blacklist.

-- COMMAND ----------

CREATE TABLE IF NOT EXISTS dd_leadership_pub_sbx.data_tiering_service.dataset_blacklist (
-- CREATE OR REPLACE TABLE dd_leadership_pub_sbx.data_tiering_service.dataset_blacklist (
  dataset_id STRING NOT NULL COMMENT 'Unique id of the dataset',
  ticket_system STRING NOT NULL COMMENT 'Ticketing system that keeps the decision to put these datasets onto the blacklist',
  ticket_id STRING NOT NULL COMMENT 'ID within the ticket system to reference the ticket',
  ticket_url STRING COMMENT 'Direct link to the decision ticket',
  created_at TIMESTAMP NOT NULL COMMENT 'Timestamp of the dataset creation',
  updated_at TIMESTAMP DEFAULT current_timestamp()
)
TBLPROPERTIES ('delta.feature.allowColumnDefaults' = 'supported')

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Define all parameters

-- COMMAND ----------

CREATE OR REPLACE TEMPORARY VIEW datasets AS
  SELECT 'b3da44c9-9207-45c6-9186-47352482e563' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT '29652d63-141b-4e46-8b3b-e81a3fd99169' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT '8a3ffec6-26c8-41cb-9ffb-d6f3ec36a66e' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT '458e31be-913d-4f1b-bb2b-c8e48b9c820d' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT 'c8bf6cde-38ba-4e0b-a1c3-82eb5e702a4a' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT '367bc8f2-49c6-419f-b316-34d1104b8d1c' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT '138fd400-8dbd-45ae-9901-351020f96f0b' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT '9dae7a29-ec14-4032-ac50-0afd46ae76dc' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT 'a76f6a1f-7d79-4c8e-8a02-6c3a54030c12' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT '18270a74-9a0a-44bc-8131-101c08f3798a' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  UNION SELECT 'e4ffe09e-4612-477c-ba9c-9ee04dd62b79' AS dataset_id, 'ADA ADO' AS ticket_system, '305070' AS ticket_id, 'https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/305070' AS ticket_url
  ;
SELECT * FROM datasets

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Update parameters

-- COMMAND ----------

MERGE INTO dd_leadership_pub_sbx.data_tiering_service.dataset_blacklist AS target
  USING datasets AS source
  ON target.dataset_id = source.dataset_id
  WHEN MATCHED AND (
    source.ticket_system <> target.ticket_system
    OR source.ticket_id <> target.ticket_id
    OR source.ticket_url <> target.ticket_url
    )
      THEN UPDATE SET ticket_system=source.ticket_system, ticket_id=source.ticket_id, ticket_url=source.ticket_url
  WHEN NOT MATCHED THEN INSERT (dataset_id, ticket_system, ticket_id, ticket_url, created_at) VALUES (source.dataset_id, source.ticket_system, source.ticket_id, source.ticket_url, NOW())

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Check parameters

-- COMMAND ----------

SELECT * FROM dd_leadership_pub_sbx.data_tiering_service.dataset_blacklist
