-- Databricks notebook source
-- ===================================================================================
--  C O P Y R I G H T
-- -----------------------------------------------------------------------------------
--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- =================================================================================== 

-- COMMAND ----------

DECLARE OR REPLACE azure_discount = .75;
DECLARE OR REPLACE si_base = 1000;
-- Azure Blob Storage Archive early deletion/retrival penalty EUR/(month and GB)
DECLARE OR REPLACE restore_penalty_per_month_gb = 0.00167*azure_discount; 
-- Azure Blob storage archive --> hot rehydration costs EUR/GB
DECLARE OR REPLACE restore_hydration_low_prio_per_gb = 0.0222*azure_discount;
DECLARE OR REPLACE restore_hydration_high_prio_per_gb = 0.1202*azure_discount;

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs AS
    WITH ARCHIVED_FILES AS (
        select
            file_hash
            ,modified_at
            ,datediff(DAY, modified_At, now()) AS days_since_tiering
            ,file_size_bytes
        from silver.tds.file_entries
        where file_state='ARCHIVED'
    ),
    PENALTY_MONTHS AS (
        SELECT
            *
            ,ceil((6*(180-days_since_tiering)/180)) AS penalty_months
        FROM ARCHIVED_FILES
    ),
    COMPONENT_COSTS AS (
    select 
        file_hash
        ,modified_at
        ,days_since_tiering
        ,file_size_bytes
        ,penalty_months
        ,CASE WHEN 
            penalty_months > 0 
        THEN 
            (file_size_bytes/si_base/si_base/si_base * restore_penalty_per_month_gb * penalty_months)
        ELSE
            0
        END
        AS restore_penalty_euro
        ,(file_size_bytes/si_base/si_base/si_base * restore_hydration_low_prio_per_gb) AS restore_hydration_euro_low_prio
        ,(file_size_bytes/si_base/si_base/si_base * restore_hydration_high_prio_per_gb) AS restore_hydration_euro_high_prio
    from PENALTY_MONTHS
    )
    SELECT
        *
        ,restore_hydration_euro_low_prio+restore_penalty_euro AS restore_low_prio_total_euro
        ,restore_hydration_euro_high_prio+restore_penalty_euro AS restore_high_prio_total_euro
    FROM COMPONENT_COSTS
;

-- COMMAND ----------

ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs SET TBLPROPERTIES ('comment' = 'Costs for restoring currently archived data including early penalty');
ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ALTER COLUMN days_since_tiering COMMENT 'Number of days since the TDS entry tiered. **WARNING**: It assumes that the modified date of the entry was last changed during the storage tier change.';
ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ALTER COLUMN penalty_months COMMENT 'Number of months we would need to pay a penalty for because of an early restore. After 180 days at archive tier, this penalty is 0.';
ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ALTER COLUMN restore_penalty_euro COMMENT 'Actual early restore penalty cost depending on the months left before 180 days';
ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ALTER COLUMN restore_hydration_euro_low_prio COMMENT 'Cost to restore this amount of data with low priority.';
ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ALTER COLUMN restore_hydration_euro_high_prio COMMENT 'Cost to restore this amount of data with high priority.';
ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ALTER COLUMN restore_low_prio_total_euro COMMENT 'Total cost for restoring this file with low priority';
ALTER TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ALTER COLUMN restore_high_prio_total_euro COMMENT 'Total cost for restoring this file with high priority';

-- COMMAND ----------

OPTIMIZE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs ZORDER BY (file_hash);

-- COMMAND ----------

VACUUM dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs;

-- COMMAND ----------

ANALYZE TABLE dd_leadership_pub_sbx.data_tiering_service.azure_rehydration_costs COMPUTE STATISTICS FOR ALL COLUMNS;

-- COMMAND ----------


