-- Databricks notebook source
-- ===================================================================================
--  C O P Y R I G H T
-- -----------------------------------------------------------------------------------
--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- ===================================================================================

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Parameters
-- MAGIC
-- MAGIC |Name|Description|QA|Prod|
-- MAGIC |--|--|--|--|
-- MAGIC |target_catalog|||dd_leadership_pub_sbx|
-- MAGIC |target_schema|||data_tiering_service|
-- MAGIC |tds_pool|||g3vprdaq|

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Check if splits with unknown image channels exist

-- COMMAND ----------

-- SELECT
--   file_hash
--   ,from_json(json_document, 'STRUCT<image_fc1: ARRAY<STRING>>')
-- FROM bronze.mdm.mdd_namespaces_latest
-- WHERE
--   namespace_name='dsp_de_split_extracted'
-- limit 10

-- COMMAND ----------

-- ;WITH CTE AS (
--   SELECT
--     file_hash
--     ,json_object_keys(json_document) AS top_level_fields
--     -- ,from_json(json_document:image_fc1, 'ARRAY<STRING>') AS image_fc1
--     -- ,from_json(json_document:image_fc2, 'ARRAY<STRING>') AS image_fc2
--     -- ,from_json(json_document:image_fc2_stereo, 'ARRAY<STRING>') AS image_fc2_stereo
--     -- ,from_json(json_document:image_fsc_left, 'ARRAY<STRING>') AS image_fsc_left
--     -- ,from_json(json_document:image_fsc_right, 'ARRAY<STRING>') AS image_fsc_right
--     -- ,from_json(json_document:image_rsc_left, 'ARRAY<STRING>') AS image_rsc_left
--     -- ,from_json(json_document:image_rsc_right, 'ARRAY<STRING>') AS image_rsc_right
--     -- ,from_json(json_document:image_rtc, 'ARRAY<STRING>') AS image_rtc
--     -- ,from_json(json_document:image_tv_left, 'ARRAY<STRING>') AS image_tv_left
--     -- ,from_json(json_document:image_tv_front, 'ARRAY<STRING>') AS image_tv_front
--     -- ,from_json(json_document:image_tv_right, 'ARRAY<STRING>') AS image_tv_right
--     -- ,from_json(json_document:image_tv_rear, 'ARRAY<STRING>') AS image_tv_rear
--   FROM bronze.mdm.mdd_namespaces_latest
--   WHERE
--     namespace_name='dsp_de_split_extracted'
--   limit 10
-- )
-- SELECT
--   file_hash
--   ,array_remove(
--     array_remove(
--       array_remove(
--         array_remove(top_level_fields, 
--           "start_configuration"

-- -- "runtime_parameters"
-- -- "derived_from"
-- -- "image_fc1"
-- -- "image_fc2"
-- -- "image_fc2_stereo"
-- -- "image_fsc_left"
-- -- "image_fsc_right"
-- -- "image_rsc_left"
-- -- "image_rsc_right"
-- -- "image_rtc"
-- -- "image_tv_left"
-- -- "image_tv_front"
-- -- "image_tv_right"
-- -- "image_tv_rear"
-- -- "lidar_velodyne"
-- -- "lidar_innoviz"
-- -- "lidar_qt_left"
-- -- "lidar_qt_front"
-- -- "lidar_qt_right"
-- -- "lidar_qt_back"
-- -- "flexray"
-- -- "can"
-- -- "gps"
-- -- "metadata"
-- -- "image_japan_fc1"
-- -- "odometry_japan"
-- -- "lidar_japan"
--         ), "schema_link"), "created_by"), "schema_version"
--   )
-- FROM CTE
-- -- WHERE 
-- --   (
-- --     array_contains(top_level_fields, 'schema_link')
-- --     AND array_contains(top_level_fields, 'created_by')
-- --     AND array_contains(top_level_fields, 'start_configuration')
-- --     AND array_contains(top_level_fields, 'runtime_parameters')
-- --     AND array_contains(top_level_fields, 'derived_from')
-- --   )

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Create table with latest extraction settings for a split
-- MAGIC
-- MAGIC Documentation of namespace can be found at https://github.com/PACE-INT/mdm-namespace-schemas/blob/main/models/src/pace/mdm_namespaces/split_extracted.py

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.mdd_namespaces_latest_images AS
SELECT
  file_hash
  ,modified_at
  ,from_json(json_document:image_fc1, 'ARRAY<STRING>') AS image_fc1
  ,from_json(json_document:image_fc2, 'ARRAY<STRING>') AS image_fc2
  ,from_json(json_document:image_fc2_stereo, 'ARRAY<STRING>') AS image_fc2_stereo
  ,from_json(json_document:image_fsc_left, 'ARRAY<STRING>') AS image_fsc_left
  ,from_json(json_document:image_fsc_right, 'ARRAY<STRING>') AS image_fsc_right
  ,from_json(json_document:image_rsc_left, 'ARRAY<STRING>') AS image_rsc_left
  ,from_json(json_document:image_rsc_right, 'ARRAY<STRING>') AS image_rsc_right
  ,from_json(json_document:image_rtc, 'ARRAY<STRING>') AS image_rtc
  ,from_json(json_document:image_tv_left, 'ARRAY<STRING>') AS image_tv_left
  ,from_json(json_document:image_tv_front, 'ARRAY<STRING>') AS image_tv_front
  ,from_json(json_document:image_tv_right, 'ARRAY<STRING>') AS image_tv_right
  ,from_json(json_document:image_tv_rear, 'ARRAY<STRING>') AS image_tv_rear
FROM bronze.mdm.mdd_namespaces_latest
WHERE
  namespace_name='dsp_de_split_extracted'

-- COMMAND ----------

SELECT * FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images ORDER BY modified_at DESC LIMIT 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Testing on data

-- COMMAND ----------

-- WITH CTE AS (
--   SELECT
--     file_hash
--     ,'FC1' AS stream_name
--     ,explode(image_fc1) as stream_info
--   FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
-- ),
-- CTE_STRUCTURED AS (
--   SELECT
--     file_hash
--     ,upper(stream_name) AS stream_name
--     ,stream_info:date::TIMESTAMP AS extracted_at
--     ,UPPER(stream_info:state) AS state
--     ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
--     ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
--     ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
--     ,stream_info:settings AS settings
--   FROM CTE
-- )
-- SELECT
--   *
-- FROM CTE_STRUCTURED
-- WHERE burstmode <> 'OFF'
-- LIMIT 100

-- COMMAND ----------

-- WITH CTE AS (
--     SELECT
--       file_hash
--       ,'FC1' AS stream_name
--       ,explode(image_fc1) as stream_info
--     FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
--   ),
--   CTE_STRUCTURED AS (
--     SELECT
--       file_hash
--       ,upper(stream_name) AS stream_name
--       ,stream_info:date::TIMESTAMP AS extracted_at
--       ,UPPER(stream_info:state) AS state
--       ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
--       ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
--       ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
--       ,stream_info:settings AS settings
--     FROM CTE
--   ),
--   CTE_RAW_VIEW AS (
--     SELECT
--       file_hash
--       ,stream_name
--       ,extracted_at
--       ,(state='FINISHED') AS has_successful_raw_extraction
--       ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
--       ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
--       ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
--       ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
--       -- ,settings
--     FROM CTE_STRUCTURED
--     WHERE is_raw_extraction
--   ),
--   CTE_AGGREGATED AS (
--     SELECT
--       file_hash
--       ,stream_name
--       ,collect_list(extracted_at) as latest_extraction_at
--       ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
--       ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
--       ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
--       ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
--       ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
--     FROM CTE_RAW_VIEW
--     WHERE
--       has_successful_raw_extraction
--     GROUP BY file_hash, stream_name
--   ),
--   CTE_CONDENSED AS (
--     SELECT
--       file_hash AS split_hash
--       ,stream_name
--       ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
--       ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
--       ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
--       -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
--       ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
--       ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
--       ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
--       ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
--     FROM CTE_AGGREGATED
--   )
--   SELECT
--     *
--   FROM CTE_CONDENSED
--   -- WHERE
--   --   has_raw_burst_extraction_3x10
--   limit 100
-- ;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Per Camera analysis

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## FC1

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_fc1
  WITH CTE AS (
    SELECT
      file_hash
      ,'FC1' AS stream_name
      ,explode(image_fc1) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## FC2

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_fc2
  WITH CTE AS (
    SELECT
      file_hash
      ,'FC2' AS stream_name
      ,explode(image_fc2) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## FC2 STEREO

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_fc2_stereo
  WITH CTE AS (
    SELECT
      file_hash
      ,'FC2STEREO' AS stream_name
      ,explode(image_fc2_stereo) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## FSC LEFT

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_fsc_left
  WITH CTE AS (
    SELECT
      file_hash
      ,'FSCLEFT' AS stream_name
      ,explode(image_fsc_left) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## FSC RIGHT

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_fsc_right
  WITH CTE AS (
    SELECT
      file_hash
      ,'FSCRIGHT' AS stream_name
      ,explode(image_fsc_right) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## RSC LEFT

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_rsc_left
  WITH CTE AS (
    SELECT
      file_hash
      ,'RSCLEFT' AS stream_name
      ,explode(image_rsc_left) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
   CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## RSC RIGHT

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_rsc_right
  WITH CTE AS (
    SELECT
      file_hash
      ,'RSCRIGHT' AS stream_name
      ,explode(image_rsc_right) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## RTC

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_rtc
  WITH CTE AS (
    SELECT
      file_hash
      ,'RTC' AS stream_name
      ,explode(image_rtc) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## TV LEFT

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_tv_left
  WITH CTE AS (
    SELECT
      file_hash
      ,'TVLEFT' AS stream_name
      ,explode(image_tv_left) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## TV RIGHT

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_tv_right
  WITH CTE AS (
    SELECT
      file_hash
      ,'TVRIGHT' AS stream_name
      ,explode(image_tv_right) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## TV FRONT

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_tv_front
  WITH CTE AS (
    SELECT
      file_hash
      ,'TVFRONT' AS stream_name
      ,explode(image_tv_front) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## TV REAR

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_tv_rear
  WITH CTE AS (
    SELECT
      file_hash
      ,'TVREAR' AS stream_name
      ,explode(image_tv_rear) as stream_info
    FROM ${target_catalog}.${target_schema}.mdd_namespaces_latest_images 
  ),
  CTE_STRUCTURED AS (
    SELECT
      file_hash
      ,upper(stream_name) AS stream_name
      ,stream_info:date::TIMESTAMP AS extracted_at
      ,UPPER(stream_info:state) AS state
      ,COALESCE(stream_info:settings:raw::BOOLEAN, false)             AS is_raw_extraction
      ,stream_info:settings:frequency::FLOAT                          AS extraction_frequency
      ,COALESCE(UPPER(stream_info:settings:burstmode::STRING), 'OFF') AS burstmode
      ,stream_info:settings AS settings
    FROM CTE
  ),
  CTE_RAW_VIEW AS (
    SELECT
      file_hash
      ,stream_name
      ,extracted_at
      ,(state='FINISHED') AS has_successful_raw_extraction
      ,CASE WHEN state='FINISHED' THEN burstmode            ELSE NULL  END              AS has_raw_extraction_burstmode
      ,CASE WHEN state='FINISHED' THEN extraction_frequency ELSE NULL  END              AS has_raw_extraction_frequency
      ,(state='FINISHED' AND extraction_frequency > 0.9 AND extraction_frequency < 1.1) AS has_raw_extraction_1hz
      ,(state='FINISHED' AND extraction_frequency > 14.9 )                              AS has_raw_extraction_full_rate
      -- ,settings
    FROM CTE_STRUCTURED
    WHERE is_raw_extraction
  ),
  CTE_AGGREGATED AS (
    SELECT
      file_hash
      ,stream_name
      ,collect_list(extracted_at) as latest_extraction_at
      ,collect_list(has_successful_raw_extraction) as has_successful_raw_extraction
      ,collect_list(has_raw_extraction_burstmode) as has_raw_extraction_burstmode
      ,collect_list(has_raw_extraction_frequency) as has_raw_extraction_frequency
      ,collect_list(has_raw_extraction_1hz) as has_raw_extraction_1hz
      ,collect_list(has_raw_extraction_full_rate) as has_raw_extraction_full_rate
    FROM CTE_RAW_VIEW
    WHERE
      has_successful_raw_extraction
    GROUP BY file_hash, stream_name
  ),
  CTE_CONDENSED AS (
    SELECT
      file_hash AS split_hash
      ,stream_name
      ,array_contains(has_successful_raw_extraction, true) AS has_successful_raw_extraction
      ,array_contains(has_raw_extraction_burstmode, 'BURST6X5') AS has_raw_burst_extraction_6x5
      ,array_contains(has_raw_extraction_burstmode, 'BURST3X10') AS has_raw_burst_extraction_3x10
      -- private communication with Nicola Pirlo, there's no 1x200 burst mode that must be a typo (2024/07/29)
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X100') OR array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x100
      ,array_contains(has_raw_extraction_burstmode, 'BURST1X200') AS has_raw_burst_extraction_1x200
      ,array_contains(has_raw_extraction_1hz, true) AS has_raw_extraction_1hz
      ,array_contains(has_raw_extraction_full_rate, true) AS has_raw_extraction_full_rate
    FROM CTE_AGGREGATED
  )
  SELECT
    *
  FROM CTE_CONDENSED
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Create unionized table

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.extraction_settings_all AS
  SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_fc1
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_fc2
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_fc2_stereo
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_fsc_left
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_fsc_right
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_rsc_left
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_rsc_right
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_rtc
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_tv_left
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_tv_right
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_tv_front
  UNION SELECT * FROM ${target_catalog}.${target_schema}.extraction_settings_tv_rear

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Optimize

-- COMMAND ----------

OPTIMIZE ${target_catalog}.${target_schema}.extraction_settings_all ZORDER BY (split_hash, stream_name)

-- COMMAND ----------

VACUUM ${target_catalog}.${target_schema}.extraction_settings_all;

-- COMMAND ----------

ANALYZE TABLE ${target_catalog}.${target_schema}.extraction_settings_all COMPUTE STATISTICS FOR COLUMNS split_hash, stream_name;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Clean-up temporary tables

-- COMMAND ----------

DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.mdd_namespaces_latest_images;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_fc1;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_fc2;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_fc2_stereo;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_fsc_left;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_fsc_right;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_rsc_left;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_rsc_right;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_rtc;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_tv_left;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_tv_right;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_tv_front;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.extraction_settings_tv_rear;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Inspect data

-- COMMAND ----------

SELECT
  stream_name
  ,count(distinct split_hash) as split_count
  ,round(count(distinct split_hash)*30/3600) as processed_hours
FROM ${target_catalog}.${target_schema}.extraction_settings_all
GROUP BY stream_name
-- LIMIT 100

-- COMMAND ----------

SELECT
  *
FROM ${target_catalog}.${target_schema}.extraction_settings_all limit 1

-- COMMAND ----------


