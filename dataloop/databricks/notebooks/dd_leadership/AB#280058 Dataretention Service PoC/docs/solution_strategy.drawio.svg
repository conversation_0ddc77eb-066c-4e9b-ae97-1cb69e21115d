<svg host="65bd71144e" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="1771px" height="581px" viewBox="-0.5 -0.5 1771 581" content="&lt;mxfile&gt;&lt;diagram id=&quot;rkAiF7Zx_c9ByNkMfGn7&quot; name=&quot;Page-1&quot;&gt;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&lt;/diagram&gt;&lt;/mxfile&gt;">
    <defs/>
    <g>
        <path d="M 260 260 L 333.63 260" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 338.88 260 L 331.88 263.5 L 333.63 260 L 331.88 256.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 260px; margin-left: 300px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                store as
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="300" y="263" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    store as
                </text>
            </switch>
        </g>
        <path d="M 70 260 L 133.63 260" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 138.88 260 L 131.88 263.5 L 133.63 260 L 131.88 256.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 260px; margin-left: 105px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                does
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="105" y="263" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    does
                </text>
            </switch>
        </g>
        <ellipse cx="55" cy="237.5" rx="7.499999999999999" ry="7.499999999999999" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 55 245 L 55 270 M 55 250 L 40 250 M 55 250 L 70 250 M 55 270 L 40 290 M 55 270 L 70 290" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 297px; margin-left: 55px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Data Scientist
                                <br/>
                                of Business Team
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="55" y="309" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Data...
                </text>
            </switch>
        </g>
        <path d="M 490 275 L 520 275 Q 530 275 530 285 L 530 320 Q 530 330 540 330 L 673.63 330" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 678.88 330 L 671.88 333.5 L 673.63 330 L 671.88 326.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 340 210 L 460 210 L 490 240 L 490 310 L 340 310 L 340 210 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 460 210 L 460 240 L 490 240 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 460 210 L 460 240 L 490 240" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 148px; height: 1px; padding-top: 260px; margin-left: 341px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Dataset
                                <br/>
                                «keep_on_hot_storage»
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="415" y="264" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Dataset...
                </text>
            </switch>
        </g>
        <path d="M 800 329.99 L 1033.63 329.96" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1038.88 329.96 L 1031.88 333.46 L 1033.63 329.96 L 1031.88 326.46 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 330px; margin-left: 920px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                consumes
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="920" y="333" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    consumes
                </text>
            </switch>
        </g>
        <rect x="680" y="300" width="120" height="60" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 330px; margin-left: 681px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                MDM Dataset Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="740" y="334" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MDM Dataset Service
                </text>
            </switch>
        </g>
        <path d="M 70 470 L 133.63 470" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 138.88 470 L 131.88 473.5 L 133.63 470 L 131.88 466.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 470px; margin-left: 105px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                does
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="105" y="473" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    does
                </text>
            </switch>
        </g>
        <ellipse cx="55" cy="447.5" rx="7.499999999999999" ry="7.499999999999999" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <path d="M 55 455 L 55 480 M 55 460 L 40 460 M 55 460 L 70 460 M 55 480 L 40 500 M 55 480 L 70 500" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 507px; margin-left: 55px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: nowrap;">
                                Business Team
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="55" y="519" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Busin...
                </text>
            </switch>
        </g>
        <rect x="140" y="230" width="120" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <path d="M 152 230 L 152 290 M 248 230 L 248 290" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 260px; margin-left: 153px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    Identify important files that must be on hot storage
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="199" y="264" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Identify importa...
                </text>
            </switch>
        </g>
        <path d="M 260 470 L 333.63 470" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 338.88 470 L 331.88 473.5 L 333.63 470 L 331.88 466.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 470px; margin-left: 300px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                consumes
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="300" y="473" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    consumes
                </text>
            </switch>
        </g>
        <rect x="140" y="440" width="120" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <path d="M 152 440 L 152 500 M 248 440 L 248 500" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 94px; height: 1px; padding-top: 470px; margin-left: 153px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    Define Business Value Matrix
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="199" y="474" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Define Business...
                </text>
            </switch>
        </g>
        <path d="M 460 470 L 490 470 Q 500 470 506.82 470 L 513.63 470" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 518.88 470 L 511.88 473.5 L 513.63 470 L 511.88 466.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 470px; margin-left: 491px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                creates
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="491" y="473" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    creates
                </text>
            </switch>
        </g>
        <rect x="340" y="440" width="120" height="60" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 470px; margin-left: 341px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Data Value
                                <br/>
                                Evaluation
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="400" y="474" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Data Value...
                </text>
            </switch>
        </g>
        <path d="M 800 470 L 873.63 470" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 878.88 470 L 871.88 473.5 L 873.63 470 L 871.88 466.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 470px; margin-left: 840px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                publishes
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="840" y="473" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    publishes
                </text>
            </switch>
        </g>
        <rect x="680" y="440" width="120" height="60" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 470px; margin-left: 681px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Viper Data Extraction
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="740" y="474" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Viper Data Extraction
                </text>
            </switch>
        </g>
        <path d="M 600 470 L 673.63 470" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 678.88 470 L 671.88 473.5 L 673.63 470 L 671.88 466.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 470px; margin-left: 640px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                consumes
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="640" y="473" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    consumes
                </text>
            </switch>
        </g>
        <path d="M 560 520 L 560 530 Q 560 540 570 540.01 L 1032.91 540.24" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1038.16 540.24 L 1031.16 543.74 L 1032.91 540.24 L 1031.16 536.74 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 540px; margin-left: 789px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                consumes
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="789" y="543" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    consumes
                </text>
            </switch>
        </g>
        <path d="M 520 420 L 570 420 L 600 450 L 600 520 L 520 520 L 520 420 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 570 420 L 570 450 L 600 450 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 570 420 L 570 450 L 600 450" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 470px; margin-left: 521px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Data Extraction Request
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="560" y="474" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Data Extracti...
                </text>
            </switch>
        </g>
        <path d="M 960 470.36 L 1033.63 471.02" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1038.88 471.07 L 1031.85 474.51 L 1033.63 471.02 L 1031.91 467.51 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 471px; margin-left: 1000px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                consumes
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1000" y="474" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    consumes
                </text>
            </switch>
        </g>
        <path d="M 880 420 L 930 420 L 960 450 L 960 520 L 880 520 L 880 420 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 930 420 L 930 450 L 960 450 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 930 420 L 930 450 L 960 450" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 470px; margin-left: 881px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Data Extraction State
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="920" y="474" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Data Extracti...
                </text>
            </switch>
        </g>
        <path d="M 1161.68 348.72 L 1253.63 349.57" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1258.88 349.62 L 1251.85 353.05 L 1253.63 349.57 L 1251.91 346.05 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1159.52 499.36 L 1253.63 499.96" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1258.88 499.99 L 1251.86 503.45 L 1253.63 499.96 L 1251.9 496.45 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <rect x="1040" y="300" width="120" height="280" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 440px; margin-left: 1041px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Data Tiering Decission Workflow
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1100" y="444" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Data Tiering Decissi...
                </text>
            </switch>
        </g>
        <path d="M 1340 350 L 1403.63 350" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1408.88 350 L 1401.88 353.5 L 1403.63 350 L 1401.88 346.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1260 300 L 1310 300 L 1340 330 L 1340 400 L 1260 400 L 1260 300 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1310 300 L 1310 330 L 1340 330 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 1310 300 L 1310 330 L 1340 330" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 350px; margin-left: 1261px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Storage Tiering Decision
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1300" y="354" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Storage Tieri...
                </text>
            </switch>
        </g>
        <path d="M 1530 350 L 1643.63 350" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1648.88 350 L 1641.88 353.5 L 1643.63 350 L 1641.88 346.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 1px; height: 1px; padding-top: 350px; margin-left: 1590px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); background-color: rgb(255, 255, 255); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 11px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; background-color: rgb(255, 255, 255); white-space: nowrap;">
                                execute operations
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1590" y="353" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="11px" text-anchor="middle">
                    execute operations
                </text>
            </switch>
        </g>
        <rect x="1410" y="320" width="120" height="60" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 350px; margin-left: 1411px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Tiering Decision Executioner
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1470" y="354" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Tiering Decision Exe...
                </text>
            </switch>
        </g>
        <rect x="0" y="0" width="410" height="190" fill="rgb(255, 255, 255)" stroke="rgb(0, 0, 0)" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe flex-start; justify-content: unsafe center; width: 408px; height: 1px; padding-top: 7px; margin-left: 1px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <b>
                                    Legend
                                </b>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="205" y="19" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Legend
                </text>
            </switch>
        </g>
        <rect x="10" y="30" width="100" height="60" fill="#fff2cc" stroke="#d6b656" pointer-events="all"/>
        <path d="M 20 30 L 20 90 M 100 30 L 100 90" fill="none" stroke="#d6b656" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 60px; margin-left: 22px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                <span style="color: rgb(0, 0, 0); font-family: Helvetica; font-size: 12px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; font-weight: 400; letter-spacing: normal; orphans: 2; text-align: center; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; float: none; display: inline !important;">
                                    Business Process
                                </span>
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="61" y="64" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Business Proc...
                </text>
            </switch>
        </g>
        <path d="M 23 100 L 57 100 L 87 130 L 87 180 L 23 180 L 23 100 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 57 100 L 57 130 L 87 130 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 57 100 L 57 130 L 87 130" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 62px; height: 1px; padding-top: 140px; margin-left: 24px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Information
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="55" y="144" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Information
                </text>
            </switch>
        </g>
        <rect x="130" y="30" width="120" height="60" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 60px; margin-left: 131px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                New Databricks Workflow
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="64" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    New Databricks Workf...
                </text>
            </switch>
        </g>
        <rect x="130" y="110" width="120" height="60" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 140px; margin-left: 131px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Existing Service
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="190" y="144" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Existing Service
                </text>
            </switch>
        </g>
        <rect x="1650" y="320" width="120" height="60" fill="#f5f5f5" stroke="#666666" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 350px; margin-left: 1651px;">
                        <div data-drawio-colors="color: #333333; " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(51, 51, 51); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                MDM TDS
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1710" y="354" fill="#333333" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    MDM TDS
                </text>
            </switch>
        </g>
        <path d="M 1340 500 L 1403.63 500" fill="none" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="stroke"/>
        <path d="M 1408.88 500 L 1401.88 503.5 L 1403.63 500 L 1401.88 496.5 Z" fill="rgb(0, 0, 0)" stroke="rgb(0, 0, 0)" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1260 450 L 1310 450 L 1340 480 L 1340 550 L 1260 550 L 1260 450 Z" fill="#d5e8d4" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <path d="M 1310 450 L 1310 480 L 1340 480 Z" fill-opacity="0.05" fill="#000000" stroke="none" pointer-events="all"/>
        <path d="M 1310 450 L 1310 480 L 1340 480" fill="none" stroke="#82b366" stroke-miterlimit="10" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 78px; height: 1px; padding-top: 500px; margin-left: 1261px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Logging and Monitoring Information
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1300" y="504" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Logging and M...
                </text>
            </switch>
        </g>
        <rect x="1410" y="470" width="120" height="60" rx="9" ry="9" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 500px; margin-left: 1411px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Data Tiering Dashboard
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="1470" y="504" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Data Tiering Dashboa...
                </text>
            </switch>
        </g>
        <rect x="270" y="30" width="120" height="60" rx="9" ry="9" fill="#f8cecc" stroke="#b85450" pointer-events="all"/>
        <g transform="translate(-0.5 -0.5)scale(0.9999999999999999)">
            <switch>
                <foreignObject pointer-events="none" width="101%" height="101%" requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility" style="overflow: visible; text-align: left;">
                    <div xmlns="http://www.w3.org/1999/xhtml" style="display: flex; align-items: unsafe center; justify-content: unsafe center; width: 118px; height: 1px; padding-top: 60px; margin-left: 271px;">
                        <div data-drawio-colors="color: rgb(0, 0, 0); " style="box-sizing: border-box; font-size: 0px; text-align: center;">
                            <div style="display: inline-block; font-size: 12px; font-family: Helvetica; color: rgb(0, 0, 0); line-height: 1.2; pointer-events: all; white-space: normal; overflow-wrap: normal;">
                                Databricks Dashboard
                            </div>
                        </div>
                    </div>
                </foreignObject>
                <text x="330" y="64" fill="rgb(0, 0, 0)" font-family="Helvetica" font-size="12px" text-anchor="middle">
                    Databricks Dashboard
                </text>
            </switch>
        </g>
    </g>
    <switch>
        <g requiredFeatures="http://www.w3.org/TR/SVG11/feature#Extensibility"/>
        <a transform="translate(0,-5)" xlink:href="https://www.diagrams.net/doc/faq/svg-export-text-problems" target="_blank">
            <text text-anchor="middle" font-size="10px" x="50%" y="100%">
                Text is not SVG - cannot display
            </text>
        </a>
    </switch>
</svg>