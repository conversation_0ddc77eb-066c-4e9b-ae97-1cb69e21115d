-- Databricks notebook source
-- ===================================================================================
--  C O P Y R I G H T
-- -----------------------------------------------------------------------------------
--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- ===================================================================================

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Parameters
-- MAGIC
-- MAGIC |Name|Description|QA|Prod|
-- MAGIC |--|--|--|--|
-- MAGIC |target_catalog|||dd_leadership_pub_sbx|
-- MAGIC |target_schema|||data_tiering_service|

-- COMMAND ----------

USE CATALOG ${target_catalog};
USE SCHEMA ${target_schema};

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Initialize variables

-- COMMAND ----------

-- TDS Pools to consider
DECLARE OR REPLACE tds_pools_to_consider ARRAY<STRING>;
SET VARIABLE tds_pools_to_consider=(SELECT from_json(value, "ARRAY<STRING>") FROM dd_leadership_pub_sbx.data_tiering_service.__configuration WHERE key='tds_pools_to_consider');

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Create set of all recording, splits and streams for investigation

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Recording Level

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.tmp_level_0_recording
  AS
  SELECT
    entries.file_hash         AS recording_hash
    ,entries.created_at       AS recording_created_at
    ,entries.tds_pool         AS recording_tds_pool
    -- entries.file_hash         AS split_hash
    -- ,entries.created_at       AS split_created_at
    ,entries.file_hash        AS file_hash
    ,entries.file_name        AS file_name
    ,entries.tds_pool         AS file_tds_pool
    ,entries.created_at       AS file_created_at
    ,entries.file_size_bytes  AS file_size_bytes
    ,entries.content_type     AS file_content_type
    ,entries.file_extension   AS file_extension
    ,entries.file_state       AS file_state
    ,entries.sources          AS file_sources
  FROM silver.tds.file_entries AS entries
  WHERE
    entries.file_name = 'gmdm.json'
    -- AND array_contains(tds_pools_to_consider, entries.tds_pool)
;
SELECT * FROM tmp_level_0_recording LIMIT 10;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Split level

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.tmp_level_1_split
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,child_entries.file_hash        AS split_hash
    ,child_entries.created_at       AS split_created_at
    ,child_entries.tds_pool         AS split_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
    ,child_entries.file_extension   AS file_extension
    ,child_entries.file_state       AS file_state
    ,child_entries.sources          AS file_sources
  FROM ${target_catalog}.${target_schema}.tmp_level_0_recording AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM ${target_catalog}.${target_schema}.tmp_level_1_split LIMIT 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Stream level

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.tmp_level_2_stream
  SELECT
    parents.recording_hash          AS recording_hash
    ,parents.recording_created_at   AS recording_created_at
    ,parents.recording_tds_pool     AS recording_tds_pool
    ,parents.split_hash             AS split_hash
    ,parents.split_created_at       AS split_created_at
    ,parents.split_tds_pool         AS split_tds_pool
    ,child_entries.file_hash        AS file_hash
    ,child_entries.file_name        AS file_name
    ,child_entries.tds_pool         AS file_tds_pool
    ,child_entries.created_at       AS file_created_at
    ,child_entries.file_size_bytes  AS file_size_bytes
    ,child_entries.content_type     AS file_content_type
    ,child_entries.file_extension   AS file_extension
    ,child_entries.file_state       AS file_state
    ,child_entries.sources          AS file_sources
  FROM ${target_catalog}.${target_schema}.tmp_level_1_split AS parents
  INNER JOIN silver.tds.parents AS children
    ON
      parents.file_hash = children.parent_file_hash
  INNER JOIN silver.tds.file_entries AS child_entries
    ON child_entries.file_hash = children.file_hash
;
SELECT * FROM ${target_catalog}.${target_schema}.tmp_level_2_stream LIMIT 10

-- COMMAND ----------

-- MAGIC %md
-- MAGIC ## Create recording table

-- COMMAND ----------

CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.all_recordings
  AS
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,NULL                   AS split_hash
    ,NULL                   AS split_created_at
    ,NULL                   AS split_tds_pool
    ,NULL                   AS stream_name
    ,NULL                   AS stream_hash
    ,NULL                   AS stream_created_at
    ,NULL                   AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type  
    ,file_extension         AS file_extension
    ,file_state             AS file_state
    ,file_sources           AS file_sources
  FROM ${target_catalog}.${target_schema}.tmp_level_0_recording AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,NULL                   AS stream_name
    ,NULL                   AS stream_hash
    ,NULL                   AS stream_created_at
    ,NULL                   AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type
    ,file_extension         AS file_extension 
    ,file_state             AS file_state
    ,file_sources           AS file_sources
  FROM ${target_catalog}.${target_schema}.tmp_level_1_split AS x
UNION
  SELECT
    recording_hash          AS recording_hash
    ,recording_created_at   AS recording_created_at
    ,recording_tds_pool     AS recording_tds_pool
    ,split_hash             AS split_hash
    ,split_created_at       AS split_created_at
    ,split_tds_pool         AS split_tds_pool
    ,trim(upper(regexp_extract(file_name, '(\\d{6})\\_(\\d{6})\\_([\\w\\_]+)\\.(avi|bin|recbin|bs|hdf5)$', 3))) AS stream_name
    ,file_hash              AS stream_hash
    ,file_created_at        AS stream_created_at
    ,file_tds_pool          AS stream_tds_pool
    ,file_hash              AS file_hash
    ,file_name              AS file_name
    ,file_created_at        AS file_created_at
    ,file_tds_pool          AS file_tds_pool
    ,file_size_bytes        AS file_size_bytes
    ,file_content_type      AS file_content_type
    ,file_extension         AS file_extension
    ,file_state             AS file_state
    ,file_sources           AS file_sources
  FROM ${target_catalog}.${target_schema}.tmp_level_2_stream AS x

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Clean-up

-- COMMAND ----------

DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_level_0_recording;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_level_1_split;
DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tmp_level_2_stream;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Optimize recording table

-- COMMAND ----------

OPTIMIZE ${target_catalog}.${target_schema}.all_recordings;

-- COMMAND ----------

VACUUM ${target_catalog}.${target_schema}.all_recordings

-- COMMAND ----------

SELECT * FROM ${target_catalog}.${target_schema}.all_recordings limit 10

-- COMMAND ----------

ANALYZE TABLE ${target_catalog}.${target_schema}.all_recordings COMPUTE STATISTICS FOR COLUMNS
    recording_hash
    ,recording_created_at
    ,recording_tds_pool
    ,split_hash
    ,split_created_at
    ,split_tds_pool
    ,stream_name
    ,stream_hash
    ,stream_created_at
    ,stream_tds_pool
    ,file_hash
    ,file_name
    ,file_created_at
    ,file_tds_pool
    ,file_size_bytes
    ,file_content_type
    ,file_state
;

-- COMMAND ----------

-- MAGIC %md
-- MAGIC # Test

-- COMMAND ----------

SELECT stream_name FROM ${target_catalog}.${target_schema}.all_recordings
GROUP BY stream_name
ORDER BY stream_name ASC

-- COMMAND ----------


