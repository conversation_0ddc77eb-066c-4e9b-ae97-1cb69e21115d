# Databricks notebook source
import asyncio
import datetime
import itertools
import json
import logging
import traceback
import uuid

import aiohttp
import msgraph
import pendulum
import pyspark.sql.functions as sf
import requests
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_dataset_accessor import AsyncDatasetAccessor
from msgraph import GraphServiceClient
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

from mdm.lib.mdm_search_module.v2.constants import Environment

ENVIRONMENT = Environment.PROD
N_PARALLEL_CONNECTIONS = 100
SLICE_SIZE = 100

TARGET_CATALOG="dd_leadership_pub_sbx"
TARGET_SCHEMA="data_tiering_service"
TARGET_TABLE_USERS="aad_users"
TARGET_TABLE_GROUPS="aad_groups"

# COMMAND ----------

# MAGIC %md
# MAGIC # Setup

# COMMAND ----------

# logging
logger = logging.getLogger("Data Copying")
logger.setLevel(logging.ERROR)

# COMMAND ----------

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"

AAD_SP_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

MSGRAPH_AUDIENCE = "https://graph.microsoft.com/.default"

aad_credential = ClientSecretCredential(
    tenant_id=AAD_TENANT_ID, client_id=AAD_SP_APPID, client_secret=AAD_SP_SECRET
)
aad_token_cache = AadTokenCache(credential_provider=aad_credential)

# unique run id
run_id = uuid.uuid4()

# Table Names
table_datasets = f"{TARGET_CATALOG}.{TARGET_SCHEMA}.datasets"
table_dataset_entries = f"{TARGET_CATALOG}.{TARGET_SCHEMA}.dataset_entries"

# Environment dependent setup
if ENVIRONMENT == Environment.QA:
    print("Using QA instance")
    # APIM Setup
    apim_kv_url = "https://qapacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

elif ENVIRONMENT == Environment.PROD:
    print("Using PROD instance")
    # APIM Setup
    apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

# COMMAND ----------

# MAGIC %md
# MAGIC # Helper methods

# COMMAND ----------

def does_table_exists(catalog_name: str, schema_name: str, table_name: str) -> bool:
    df = spark.sql(f"""
                   SELECT 1
                   FROM {catalog_name}.information_schema.tables
                   WHERE
                    table_name='{table_name}'
                    AND table_schema='{schema_name}'""")
    return df.count() > 0

# COMMAND ----------

# MAGIC %md
# MAGIC # AAD Connection

# COMMAND ----------

from msgraph.generated.users.users_request_builder import UsersRequestBuilder


class GraphClient():

    def __init__(self, aad_credentials):
        self._client = GraphServiceClient(credentials=aad_credentials, scopes=['https://graph.microsoft.com/.default'])
        self._all_users = None
    
    def attribute_mapping(self):
        return {
            "id":                   { "response": "id",                     "databricks": "object_id",              "databricks_type": StringType()}, 
            "displayName":          { "response": "display_name",           "databricks": "display_name",           "databricks_type": StringType()},
            "surname":              { "response": "surname",                "databricks": "surname",                "databricks_type": StringType()},
            "givenName":            { "response": "given_name",             "databricks": "given_name",             "databricks_type": StringType()},
            "mail":                 { "response": "mail",                   "databricks": "mail",                   "databricks_type": StringType()},
            "accountEnabled":       { "response": "account_enabled",        "databricks": "account_enabled",        "databricks_type": BooleanType()},
            "companyName":          { "response": "company_name",           "databricks": "company_name",           "databricks_type": StringType()},
            "userType":             { "response": "user_type",              "databricks": "user_type",              "databricks_type": StringType()},
            "mobilePhone":          { "response": "mobile_phone",           "databricks": "mobile_phone",           "databricks_type": StringType()},
            "otherMails":           { "response": "other_mails",            "databricks": "other_mails",            "databricks_type": ArrayType(StringType())},
            "userPrincipalName":    { "response": "user_principal_name",    "databricks": "user_principal_name",    "databricks_type": StringType()},
            "createdDateTime":      { "response": "created_date_time",      "databricks": "created_at",             "databricks_type": TimestampType()},
            "deletedDateTime":      { "response": "deleted_date_time",      "databricks": "deleted_at",             "databricks_type": TimestampType()},
        }

    def extension_attribute_mapping(self):
        return {
            "extension_cfd9ec329cd2499782d6092982aa3542_Domain":            { "response": "extension_cfd9ec329cd2499782d6092982aa3542_Domain",          "databricks": "ada_domain",         "databricks_type": StringType()},
            "extension_cfd9ec329cd2499782d6092982aa3542_Cluster":           { "response": "extension_cfd9ec329cd2499782d6092982aa3542_Cluster",         "databricks": "ada_cluster",        "databricks_type": StringType()},
            "extension_cfd9ec329cd2499782d6092982aa3542_Team":              { "response": "extension_cfd9ec329cd2499782d6092982aa3542_Team",            "databricks": "ada_team",           "databricks_type": StringType()},
            "extension_cfd9ec329cd2499782d6092982aa3542_Disabledinactive":  { "response": "extension_cfd9ec329cd2499782d6092982aa3542_Disabledinactive","databricks": "disabled_inactive",  "databricks_type": StringType()},
            "extension_cfd9ec329cd2499782d6092982aa3542_whenDisabled":      { "response": "extension_cfd9ec329cd2499782d6092982aa3542_whenDisabled",    "databricks": "when_disabled",      "databricks_type": StringType()},
            "extension_cfd9ec329cd2499782d6092982aa3542_Role":              { "response": "extension_cfd9ec329cd2499782d6092982aa3542_Role",            "databricks": "ada_role",           "databricks_type": StringType()},
            "extension_cfd9ec329cd2499782d6092982aa3542_isGuestUser":       { "response": "extension_cfd9ec329cd2499782d6092982aa3542_isGuestUser",     "databricks": "is_ada_guest_user",  "databricks_type": StringType()},
        }

    def all_attribute_mappings(self):
        return {**self.attribute_mapping(), **self.extension_attribute_mapping()}

    async def get_all_users(self):
        if self._all_users is None:
            self._all_users = []
            # users = await self._client.users.get()

            query_params = UsersRequestBuilder.UsersRequestBuilderGetQueryParameters(
                select=[x for x in self.all_attribute_mappings()],
                top=100,
            )
            request_config = UsersRequestBuilder.UsersRequestBuilderGetRequestConfiguration(
                query_parameters=query_params
            )

            users = await self._client.users.get(request_configuration=request_config)

            while users:
                self._all_users.extend(users.value)
                next_link = users.odata_next_link
                if next_link is None:
                    break
                users = await self._client.users.with_url(next_link).get()
        
        # answer call from cache
        return self._all_users
    
    @staticmethod
    def _data_type_conversion(value):
        if isinstance(value, pendulum.DateTime):
            return datetime.datetime.fromtimestamp(value.timestamp(), pendulum.tz.UTC)
        else:
            return value

    async def get_all_users_as_dict(self):
        result = []
        for this_user in await self.get_all_users():
            user_record = { _key['databricks']: None for _, _key in graph_client.all_attribute_mappings().items() }

            user_record.update({
                graph_client.extension_attribute_mapping()[_key]['databricks']: GraphClient._data_type_conversion(_value)
                for _key, _value in this_user.additional_data.items()
            })
            user_record.update({
                _value['databricks']: GraphClient._data_type_conversion(getattr(this_user, _value['response']))
                for _key, _value in graph_client.attribute_mapping().items()
                if hasattr(this_user, _value['response'])
            })

            result.append(user_record)
        return result
        
    
graph_client = GraphClient(aad_credential)
users = await graph_client.get_all_users()
print(f"Fetched {len(users)} users")

# COMMAND ----------

df_users = (
    spark.createDataFrame(
        [
            Row(**x)
            for x in (await graph_client.get_all_users_as_dict())
        ]
        ,
        StructType([
            StructField(this_field['databricks'], this_field['databricks_type'])
            for _, this_field in graph_client.all_attribute_mappings().items()
        ])
    )
)

# COMMAND ----------

display(df_users.limit(10))

# COMMAND ----------

if does_table_exists(TARGET_CATALOG, TARGET_SCHEMA, TARGET_TABLE_USERS):
    print("Update existing table")
    df_users.createOrReplaceTempView("aad_users")
    spark.sql(f"""
              MERGE INTO {TARGET_CATALOG}.{TARGET_SCHEMA}.{TARGET_TABLE_USERS} AS target
              USING aad_users AS source
              ON
                target.object_id=source.object_id
              WHEN MATCHED THEN UPDATE SET *
              WHEN NOT MATCHED BY TARGET THEN INSERT *
              WHEN NOT MATCHED BY SOURCE THEN DELETE
              """)

else:
    print("Initial creation")
    (df_users
     .write
     .format("delta")
     .saveAsTable(f"{TARGET_CATALOG}.{TARGET_SCHEMA}.{TARGET_TABLE_USERS}")
    )

# COMMAND ----------

display(spark.sql(f"OPTIMIZE {TARGET_CATALOG}.{TARGET_SCHEMA}.{TARGET_TABLE_USERS}"))

# COMMAND ----------

display(spark.sql(f"VACUUM {TARGET_CATALOG}.{TARGET_SCHEMA}.{TARGET_TABLE_USERS}"))
