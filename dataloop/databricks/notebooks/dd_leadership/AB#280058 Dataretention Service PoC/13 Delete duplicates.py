# Databricks notebook source
# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

# COMMAND ----------

# MAGIC %md
# MAGIC # Documentation

# COMMAND ----------

# MAGIC %md
# MAGIC ## Parameters

# COMMAND ----------

from mdm.lib.mdm_search_module.v2.constants import Environment

# Environment
environment = Environment.PROD

# Databricks datastore configuration
CATALOG_NAME="dd_leadership_pub_sbx"
SCHEMA_NAME="data_tiering_service"
DUPLICATES_TABLE="duplicate_active_tds_fids"
DUPLICATES_DELETION_STATE_TABLE="_state_duplicate_fid_deletion"

# Parallicity configuration
SLICE_SIZE = 10000
N_PARALLEL_CONNECTIONS = 500

# COMMAND ----------

# MAGIC %md
# MAGIC ## parameters derived constants

# COMMAND ----------

DUPLICATES_TABLE_FULL_PATH=f"{CATALOG_NAME}.{SCHEMA_NAME}.{DUPLICATES_TABLE}"
DUPLICATES_DELETION_STATE_TABLE_FULL_PATH=f"{CATALOG_NAME}.{SCHEMA_NAME}.{DUPLICATES_DELETION_STATE_TABLE}"

# COMMAND ----------

print(DUPLICATES_TABLE_FULL_PATH)
print(DUPLICATES_DELETION_STATE_TABLE_FULL_PATH)

# COMMAND ----------

# MAGIC %md
# MAGIC # Setup

# COMMAND ----------

import asyncio
import datetime
import logging
import uuid

import aiohttp
from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from modules.aad_token_cacher import AadTokenCache
from modules.async_mdm_accessor import (
    AsyncMdmAccessor,
    MdmException,
    MdmFileEntryNotFoundException,
    TDSFileCopyResponse,
    TDSFileEntry,
    TDSStorageTierEnum,
)
from pyspark.sql import Row
from pyspark.sql.functions import *
from pyspark.sql.types import *

# COMMAND ----------

# logging
# logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger("Data Tiering")

logging.getLogger("azure").setLevel(logging.WARN)
logging.getLogger("CachedAadTokenRetriever").setLevel(logging.WARN)
logging.getLogger("urllib3.connectionpool").setLevel(logging.WARN)
logging.getLogger("msal.token_cache").setLevel(logging.WARN)
logging.getLogger("msal").setLevel(logging.WARN)
logging.getLogger("py4j").setLevel(logging.WARN)

# AAD Connection Strings and Credentials
AAD_TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
AAD_SP_READ_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
AAD_SP_READ_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")
aad_credential = ClientSecretCredential(tenant_id=AAD_TENANT_ID, client_id=AAD_SP_READ_APPID, client_secret=AAD_SP_READ_SECRET)
aad_token_cache = AadTokenCache(credential_provider=aad_credential)

AAD_SP_DELETION_APPID = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--appid")
AAD_SP_DELETION_SECRET = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-datadeletion-dbx--secret")
aad_credential_deletion = ClientSecretCredential(tenant_id=AAD_TENANT_ID, client_id=AAD_SP_DELETION_APPID, client_secret=AAD_SP_DELETION_SECRET)
aad_token_cache_deletion = AadTokenCache(credential_provider=aad_credential_deletion)



# Environment dependent setup
if environment == Environment.QA:
    print("Using QA instance")
    # APIM Setup
    apim_kv_url = "https://qapacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value

    # TDS
    TDS_BASE_URL = "https://data-delivery-api-qa.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-paceqa-westeurope/.default"
elif environment == Environment.PROD:
    print("Using PROD instance")
    # APIM Setup
    apim_kv_url = "https://prdpacegatewaykyv.vault.azure.net/"
    apim_kv_secret_name = "ddleadershipdatabricks-primary-key"
    kv_client = SecretClient(vault_url=apim_kv_url, credential=aad_credential)
    APIM_KEY = kv_client.get_secret("ddleadershipdatabricks-primary-key").value
    
    # TDS
    TDS_BASE_URL = "https://data-delivery-api.ad-alliance.biz/mdtds"
    TDS_AAD_AUDIENCE = "api://sp-pace-mdtds-pace-westeurope/.default"

# TDS
TDS_HEADERS = {"apikey": APIM_KEY}

mdm_accessor = AsyncMdmAccessor(
    credential_provider=aad_token_cache,
    credential_provider_deletion=aad_token_cache_deletion,
    environment=environment,
    use_api_gateway=True,
    apim_key=APIM_KEY,
)

# COMMAND ----------

# MAGIC %md
# MAGIC # Setup tracing

# COMMAND ----------

spark.sql(f"""CREATE TABLE IF NOT EXISTS {DUPLICATES_DELETION_STATE_TABLE_FULL_PATH} (tds_fid STRING, executed_at TIMESTAMP, error_msg STRING)""")

# COMMAND ----------

def create_logging_record(tds_fid: str, executed_at: datetime.datetime, error_msg: str) -> Dict[str, Any]:
    return {
        "tds_fid": tds_fid,
        "executed_at": executed_at,
        "error_msg": error_msg,
    }

# COMMAND ----------

def add_logging_rows(rows):
    data_struct = StructType([
        StructField("tds_fid", StringType(), True),
        StructField("executed_at", TimestampType(), True),
        StructField("error_msg", StringType(), True),
    ])

    spark.sql("DROP VIEW IF EXISTS logging_rows")
    df = spark.createDataFrame(rows, data_struct)
    df.createTempView("logging_rows")
    spark.sql(f"""MERGE INTO {DUPLICATES_DELETION_STATE_TABLE_FULL_PATH} AS target
            USING logging_rows AS source
            ON
            source.tds_fid=target.tds_fid
            WHEN MATCHED THEN UPDATE SET *
            WHEN NOT MATCHED BY TARGET THEN INSERT *
            """)

# COMMAND ----------

# add_logging_rows([{"tds_fid": "kennwort", "executed_at":datetime.datetime.now()}])

# COMMAND ----------

display(spark.sql(f"""SELECT * FROM {DUPLICATES_DELETION_STATE_TABLE_FULL_PATH}"""))

# COMMAND ----------

# MAGIC %md
# MAGIC # Fetch data to delete

# COMMAND ----------



# COMMAND ----------

def get_files_to_delete():
    df_process_log = (
        spark
            .read
            .format("delta")
            .table(DUPLICATES_DELETION_STATE_TABLE_FULL_PATH)
    )

    df = (
        spark
            .read
            .format("delta")
            .table(DUPLICATES_TABLE_FULL_PATH)
            # let's keep exactly one per entry
            .where(col("_row_number") > lit(1))
            .join(df_process_log, df_process_log.tds_fid == col("file_source_tds_fid"), "outer")
            .where(
                # only fetch data that's not yet processed
                df_process_log.tds_fid.isNull()
            )
            # .orderBy(asc("stream_hash"), asc("_row_number"))
    )
    return df

display(get_files_to_delete().orderBy(col("stream_hash")).limit(100))

# COMMAND ----------

total_duplicates_to_delete = get_files_to_delete().count()
print(f"Going to delete {total_duplicates_to_delete} duplicate files")

# COMMAND ----------

# MAGIC %md
# MAGIC # Delete duplicates

# COMMAND ----------

async def delete_file(
    session: aiohttp.ClientSession, tds_client: AsyncMdmAccessor, this_fid: str, correlation_id: uuid.UUID
):
    error_msg = ""

    try:
        await tds_client.delete_file_by_fid(
            session, fid=this_fid, request_correlation_id=correlation_id
        )
    except Exception as e:
        logging.error(f"[{this_fid}] Caught exception on deletion: {e}")
        error_msg = str(e)

    return {
        "tds_fid": this_fid,
        "executed_at": datetime.datetime.now(),
        "error_msg": error_msg
    }

# COMMAND ----------

import traceback

aiohttp_connector = aiohttp.TCPConnector(limit=N_PARALLEL_CONNECTIONS)
aiohttp_timeout = aiohttp.ClientTimeout(total=None, sock_connect=5, sock_read=30)

async with aiohttp.ClientSession(trust_env=True, connector=aiohttp_connector, timeout=aiohttp_timeout) as session:
    this_correlation_id = uuid.uuid4()

    duplicate_files_deleted = 0
    while True:
        files_to_delete = get_files_to_delete().limit(SLICE_SIZE).select(col("file_source_tds_fid")).collect()
        print(f"{duplicate_files_deleted}/{total_duplicates_to_delete} files deleted")
        if len(files_to_delete) == 0:
            break
        
        deletion_state = []
        try:
            deletion_state = await asyncio.gather(
                # *[
                #     delete_file(session, mdm_accessor, this_tds_fid, correlation_id=this_correlation_id)
                #     for this_tds_fid in files_to_delete
                # ]
            )
        except Exception as e:
            print(f"An exception occured {e}")
            traceback.print_exc()


        add_logging_rows(deletion_state)
        duplicate_files_deleted += len([x for x in deletion_state if len(deletion_state["error_msg"]) == 0])
        break

    # response = await mdm_accessor.change_tier(session, request_correlation_id=this_correlation_id, fid="202ad476-9a3d-4ce4-98b0-565f29492d6e", tier=TDSStorageTierEnum.ARCHIVE)
    # print(response)

# COMMAND ----------


