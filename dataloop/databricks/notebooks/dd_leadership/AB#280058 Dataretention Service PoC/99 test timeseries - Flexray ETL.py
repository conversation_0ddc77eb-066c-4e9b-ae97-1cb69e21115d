# Databricks notebook source
# MAGIC %md
# MAGIC # Purpose
# MAGIC Process flexray json files and store them into a table

# COMMAND ----------

# MAGIC %md
# MAGIC # 0. Setup session

# COMMAND ----------

import asyncio
import itertools
import json
import logging
import random
import time
import traceback
import zlib
from datetime import datetime
from functools import reduce
from typing import Any, Dict, List

import aiohttp
import dateutil.parser
import pyspark
import requests
from azure.core.credentials import AccessToken
from azure.identity import ClientSecretCredential
from pyspark.sql import Row
from pyspark.sql.functions import col
from pyspark.sql.types import ArrayType, IntegerType, LongType, StringType, StructField, StructType, TimestampType

# COMMAND ----------

logger = logging.getLogger("Flexray Fetcher")

logging.getLogger("azure").setLevel(logging.WARN)
logging.getLogger("CachedAadTokenRetriever").setLevel(logging.WARN)
logging.getLogger("urllib3.connectionpool").setLevel(logging.WARN)
logging.getLogger("msal.token_cache").setLevel(logging.WARN)
logging.getLogger("msal").setLevel(logging.WARN)
logging.getLogger("py4j").setLevel(logging.WARN)

# COMMAND ----------

# MAGIC %md
# MAGIC ## 0.1. Configuration

# COMMAND ----------

TABLE_NAME = "dd_leadership_pub_sbx.timeseries.events_flexray"
TESTING_MODE = False
BATCH_SIZE =  40000
REQUEST_TIMEOUT = 5 # seconds
MAX_RETRIES_DOWNLOAD = 10

# COMMAND ----------

relevant_signals = [
    'KBI_Kilometerstand',
    'ESP_v_ref_Fahrtrichtung',
    'ESP_v_ref',
    'BR_Eingriffsmoment',
    'EML_GierRate',
    'EML_LenkWnklVL',
    'EML_LenkWnklVR',
    'BCM1_Aussen_Temp_ungef',
    'DI_Fotosensor',
    'RS_Regenmenge',
    'RS_Wischergeschwindigkeit',
    'Heckwischer_Status',
    'LV_Tagfahrlicht_Anzeige',
    'LV_Abblendlicht_Anzeige',
    'LV_Fernlicht_Anzeige',
    'LV_Nebellicht_Anzeige',
    'LV_Nebelschlusslicht_Anzeige',
    'LV_Blinker_VL_aktiv',
    'LV_Blinker_VR_aktiv'
]

# COMMAND ----------

# MAGIC %md
# MAGIC ## 0.2. Helper methods

# COMMAND ----------

tds_accessor_sp_aad_tenant = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
tds_accessor_sp_aad_appid = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--appid")
tds_accessor_sp_aad_secret = dbutils.secrets.get(scope="ddleadership", key="appreg--sp-pace-dataloop-ddleadership-dbx--secret")

storage_account_aad_audience = "https://storage.azure.com/.default"

sp_credential = ClientSecretCredential(tenant_id=tds_accessor_sp_aad_tenant, client_id=tds_accessor_sp_aad_appid, client_secret=tds_accessor_sp_aad_secret)

# COMMAND ----------

def has_relevant_signals(key_list: List[str]):
    return any([_key in list(key_list) for _key in relevant_signals])

# COMMAND ----------


def keep_only_relevant_fields(dict: Dict[str, any], signal_list: List[str]) -> Dict[str, any]:
    _relevant_fields = ["timestamp", "frame"]
    _relevant_fields.extend(signal_list)
    return {
        k: v
        for k, v in dict.items()
        if k in _relevant_fields
    }

# COMMAND ----------


def safely_split_bus_value(value: str) -> str:
    splitted = value.split(sep=" ", maxsplit=1)
    if len(splitted) == 2:
        return [splitted[0], splitted[1].strip()]
    else:
        return [splitted[0], ""]

# COMMAND ----------

def convert_timestamp_to_datetime(timestamp: int) -> datetime:
    digits = len(str(timestamp))
    # WARNING this conversion is only valid for timestamps until Sat Nov 20 2286 17:46:39 GMT+0000
    if digits < 11:
        # seconds
        return datetime.fromtimestamp(timestamp)
    if digits < 14:
        # milliseconds
        return datetime.fromtimestamp(timestamp / 1e3)
    if digits < 17:
        # microseconds
        return datetime.fromtimestamp(timestamp / 1e6)
    if digits < 20:
        # nanoseconds
        return datetime.fromtimestamp(timestamp / 1e9)
    else:
        raise ValueError(f"Invalid timestamp: {timestamp}")

# COMMAND ----------

def transform_to_normalized_table(data_frame: Dict[str, str], row: Row) -> List[any]:
    started_at = convert_timestamp_to_datetime(int(data_frame['timestamp']))
    ended_at = None
    started_timestamp = int(data_frame['timestamp'])
    frame = data_frame['frame']
    data_frame.pop('timestamp')
    data_frame.pop('frame')

    return [
        (
            row.recording_id, # recording_id
            row.partition_date, # partition_date
            row.vin, # vin
            row.license_plate, # license_plate
            started_at, # started_at
            ended_at, # ended_at
            started_timestamp, # started_timestamp
            frame, # frame
            k, # name
            safely_split_bus_value(v)[0], # value
            safely_split_bus_value(v)[1], # value_extra
            row.app_version,  # extractor_version
            row.channels, # flexray_channels
            row.file_hash, # zlib_file_hash
            row.stream_hash, # stream_hash
            row.split_hash, # split_hash
            row.recording_hash, # recording_hash
        )
        for k, v in data_frame.items()
    ]
    

# COMMAND ----------

# MAGIC %md
# MAGIC # 1. Define target data structure

# COMMAND ----------

def get_dataframe_schema():
    return StructType([
        StructField("recording_id", LongType(), False),
        StructField("partition_date", IntegerType(), False),
        StructField("vin", StringType(), False),
        StructField("license_plate", StringType(), False),
        StructField("started_at", TimestampType(), False),
        StructField("ended_at", TimestampType(), True),
        StructField("started_timestamp", LongType(), False),
        StructField("frame", StringType(), False),
        StructField("name", StringType(), False),
        StructField("value", StringType(), True),
        StructField("value_extra", StringType(), True),
        StructField("extractor_version", StringType(), True),
        StructField("flexray_channels", ArrayType(IntegerType()), True),
        StructField("zlib_file_hash", StringType(), False),
        StructField("stream_hash", StringType(), False),
        StructField("split_hash", StringType(), False),
        StructField("recording_hash", StringType(), False),
    ])

# COMMAND ----------

spark.sql(f"""CREATE TABLE IF NOT EXISTS {TABLE_NAME} (
    recording_id BIGINT,
    partition_date INT,
    vin STRING,
    license_plate STRING,
    started_at TIMESTAMP,
    ended_at TIMESTAMP,
    started_timestamp BIGINT,
    frame STRING,
    name STRING,
    value STRING,
    value_extra STRING,
    extractor_version STRING,
    flexray_channels ARRAY<INT>,
    zlib_file_hash STRING,
    stream_hash STRING,
    split_hash STRING,
    recording_hash STRING
)""")

# COMMAND ----------

# MAGIC %md
# MAGIC # 2. Find files to ingest to databricks

# COMMAND ----------

def get_flexray_files_to_process():
    return spark.sql(f"""
        SELECT 
            recording_files.recording_id
            ,recording_files.partition_date
            ,recording_files.vin
            ,recording_files.license_plate
            --,recording_files.stream
            ,recording_files.file_hash AS stream_hash
            ,ns.file_hash
            ,ns.json_document:flexray:app_version::STRING AS app_version
            ,ns.json_document:flexray:message_count::BIGINT AS message_count
            ,from_json(ns.json_document:flexray:channels::STRING,  'ARRAY<INT>') AS channels
            --,file_entries.content_type
            --,file_entries.file_state
            --,file_entries.file_name
            ,file_entries.tds_file_url
            ,all_recordings.recording_hash
            ,all_recordings.split_hash
        FROM bronze.mdm.mdd_namespaces_latest_v2 AS ns
        INNER JOIN silver.tds.file_entries AS file_entries
            ON
                file_entries.file_hash=ns.file_hash
        INNER JOIN dd_leadership_pub_sbx.timeseries.recording_files AS recording_files
            ON
                recording_files.file_hash=file_entries.parents[0]
        INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings AS all_recordings
            ON
                all_recordings.file_hash=file_entries.parents[0]
        LEFT JOIN {TABLE_NAME} AS target_tbl
            ON
                target_tbl.zlib_file_hash=ns.file_hash
        WHERE
            ns.namespace_name = 'dsp_de_flexray'
            AND NOT ns.is_deleted
            AND UPPER(file_entries.file_state) = 'ACTIVE'
            AND target_tbl.zlib_file_hash IS NULL
        """)
# display(get_flexray_files_to_process().limit(100))

# COMMAND ----------

total_files_to_process = get_flexray_files_to_process().count()
-- 2830107
-- 2830097
print(total_files_to_process)

# COMMAND ----------

# MAGIC %md
# MAGIC # 3. ETL Business Logic

# COMMAND ----------

def get_zlib_file_as_json(url: str):
    headers = {
        "Authorization": f"Bearer {sp_credential.get_token(storage_account_aad_audience).token}",
        "x-ms-version": "2019-07-07"
    }

    for i_try in range(MAX_RETRIES_DOWNLOAD):
        try:
            response = requests.get(url, headers=headers, timeout=REQUEST_TIMEOUT)

            if response.status_code > 299:
                print(f"Failed with response {response.status_code}: {response.text}")
                response.raise_for_status()
            
            decompressed = zlib.decompress(response.content)
            print(f"file size decompressed {round(len(decompressed)/1024/1024, 1)} MiB")
            return json.loads(decompressed.decode("utf-8"))
        except requests.exceptions.Timeout as e:
            if i_try == MAX_RETRIES_DOWNLOAD - 1:
                raise
            
            time.sleep(random.random())
            pass

# COMMAND ----------

def process_flexray_file(row: Row, debug: bool = False):
    initial_wait_time = random.random()*1.
    time.sleep(initial_wait_time)

    try:
        json_obj = get_zlib_file_as_json(row.tds_file_url)
        return list(itertools.chain.from_iterable([
            transform_to_normalized_table(
                keep_only_relevant_fields(x, relevant_signals)
                ,row
            )
            for x in json_obj
            if has_relevant_signals(x.keys())
        ]))
    except Exception as e:
        if debug:
            raise
        else:
            return []

# COMMAND ----------

# MAGIC %md
# MAGIC # 4. Test run to identify setup failures

# COMMAND ----------

rows = get_flexray_files_to_process().limit(1).collect()

# COMMAND ----------

result = process_flexray_file(rows[0], True)

# COMMAND ----------

result[:2]

# COMMAND ----------

# MAGIC %md
# MAGIC # 5. Production run

# COMMAND ----------

i_processed_files = 0
while i_processed_files <= total_files_to_process:

    # repartition next batch for optimal compute use
    n_partitions=sc.defaultParallelism
    partitioned_flexray_file_df = (get_flexray_files_to_process()
        .limit(BATCH_SIZE)
        .repartitionByRange(n_partitions, "file_hash")
        )
    
    n_files_in_batch = partitioned_flexray_file_df.count()
    if n_files_in_batch == 0:
        print(" ---> FINISHED <--- ")
        break
    print(f"Processing next batch of {n_files_in_batch} of {total_files_to_process} / already processed {i_processed_files}")


    # define transformation
    rdd_flexray = partitioned_flexray_file_df.rdd.flatMap(process_flexray_file)

    # execute ETL
    spark.createDataFrame(rdd_flexray, get_dataframe_schema()).write.mode("append").saveAsTable(TABLE_NAME)

    i_processed_files += n_files_in_batch
    if TESTING_MODE:
        break
