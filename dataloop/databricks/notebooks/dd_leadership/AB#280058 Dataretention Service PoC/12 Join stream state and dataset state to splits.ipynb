{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2b9efe38-f799-4280-b155-33cb134dc32c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- ===================================================================================\n", "--  <PERSON> O P Y R I G H T\n", "-- -----------------------------------------------------------------------------------\n", "--  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.\n", "-- =================================================================================== "]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b9334597-80d7-4b18-9166-07fd8b0388bf", "showTitle": false, "title": ""}}, "source": ["# Parameters\n", "\n", "|Name|Description|QA|Prod|\n", "|--|--|--|--|\n", "|target_catalog|||dd_leadership_pub_sbx|\n", "|target_schema|||data_tiering_service|"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e05563dc-bd3b-47d1-811a-0aca14fdd56f", "showTitle": false, "title": ""}}, "source": ["# Variables"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8a770557-32f7-4b46-895a-6e27d11a664f", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- min file age before tiering\n", "DECLARE OR REPLACE min_file_age_days_before_tiering INT;\n", "SET VARIABLE min_file_age_days_before_tiering=(SELECT CAST(value AS INT) FROM ${target_catalog}.${target_schema}.__configuration WHERE key='min_file_age_days_before_tiering');\n", "-- dataset tag name\n", "DECLARE OR REPLACE dataset_tag_keep_on_hotstorage ARRAY<STRING>;\n", "SET VARIABLE dataset_tag_keep_on_hotstorage=(SELECT from_json(value, \"ARRAY<STRING>\") FROM ${target_catalog}.${target_schema}.__configuration WHERE key='dataset_tag_keep_on_hotstorage');\n", "-- TDS Pools to consider\n", "DECLARE OR REPLACE tds_pools_to_consider ARRAY<STRING>;\n", "SET VARIABLE tds_pools_to_consider=(SELECT from_json(value, \"ARRAY<STRING>\") FROM dd_leadership_pub_sbx.data_tiering_service.__configuration WHERE key='tds_pools_to_consider');\n", "-- Files to exclude\n", "DECLARE OR REPLACE excluded_file_extensions ARRAY<STRING>;\n", "SET VARIABLE excluded_file_extensions=(SELECT from_json(value, \"ARRAY<STRING>\") FROM dd_leadership_pub_sbx.data_tiering_service.__configuration WHERE key='excluded_file_extensions');"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "e6a3c038-a465-477c-8d93-babff2fe7895", "showTitle": false, "title": ""}}, "source": ["# Clean-up"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6ccaacaa-69db-4318-918a-8376ade0c8e8", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tiering_decision;\n", "-- DROP TABLE IF EXISTS ${target_catalog}.${target_schema}.tiering_decision_splits;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "38adb76d-444e-4ecb-8263-f7221125b773", "showTitle": false, "title": ""}}, "source": ["# Add stream extraction state to file"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ff1b511a-2ffd-4f3d-b327-b3b316112003", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.streams_with_extraction_info AS\n", "--   SELECT\n", "--     recordings.recording_hash\n", "--     ,recordings.split_hash\n", "--     ,recordings.split_created_at\n", "--     ,recordings.stream_hash\n", "--     ,recordings.stream_name\n", "--     ,recordings.file_created_at\n", "--     ,recordings.file_hash\n", "--     ,recordings.file_name\n", "--     ,recordings.file_size_bytes\n", "--     ,recordings.file_content_type\n", "--     ,recordings.file_state\n", "--     ,recordings.file_sources\n", "--     ,extraction_settings.latest_extraction_at AS extracted_at\n", "--     ,extraction_settings.has_successful_raw_extraction AS has_successful_raw_extraction\n", "--     ,extraction_settings.has_successful_15Hz_rgb_yuv_extraction AS has_successful_15Hz_rgb_yuv_extraction\n", "--     ,extraction_settings.raw_extraction_burstmode AS raw_extraction_burstmode\n", "--     ,extraction_settings.raw_extraction_frequency AS raw_extraction_frequency\n", "--   FROM ${target_catalog}.${target_schema}.all_recordings AS recordings\n", "--   LEFT JOIN ${target_catalog}.${target_schema}.extraction_settings_all AS extraction_settings\n", "--     ON\n", "--       recordings.split_hash=extraction_settings.file_hash\n", "--       AND recordings.stream_name=extraction_settings.stream_name"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6699f7c2-9584-4adf-b5cb-a6f6bfb57918", "showTitle": false, "title": ""}}, "source": ["# Add dataset information"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "af0f1113-9b45-4293-8148-c12c2f53eb79", "showTitle": false, "title": ""}}, "source": ["## Select relevant datasets"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2822ce8f-4fe8-4ccc-9768-7d813352301b", "showTitle": false, "title": ""}}, "outputs": [], "source": ["WITH CTE_ORDER_VERSIONS AS (\n", "  SELECT\n", "    row_number() OVER (PARTITION BY id ORDER BY major_version DESC, minor_version DESC) AS _row_number\n", "    ,*\n", "  FROM ${target_catalog}.${target_schema}.datasets\n", "),\n", "CTE_PICK_CURRENT_VERSION AS (\n", "  SELECT\n", "    datasets.id AS dataset_id\n", "    ,datasets.version AS dataset_version\n", "    ,datasets.metadata:dataset_name as dataset_name\n", "    ,datasets.author AS dataset_author\n", "    ,datasets.tags AS dataset_tags\n", "  FROM CTE_ORDER_VERSIONS AS datasets\n", "  WHERE\n", "    datasets._row_number = 1\n", "),\n", "CTE_KEEP_TAGS AS (\n", "  select explode(dataset_tag_keep_on_hotstorage) AS tag\n", "),\n", "CTE_VALID_DATASETS AS (\n", "  SELECT\n", "    dataset_id, dataset_version\n", "  FROM CTE_KEEP_TAGS\n", "  INNER JOIN CTE_PICK_CURRENT_VERSION\n", "    ON\n", "      array_contains(CTE_PICK_CURRENT_VERSION.dataset_tags, CTE_KEEP_TAGS.tag)\n", "  GROUP BY dataset_id, dataset_version\n", ")\n", "SELECT\n", "    datasets.id AS dataset_id\n", "    ,datasets.version AS dataset_version\n", "    ,datasets.metadata:dataset_name as dataset_name\n", "    ,datasets.author AS dataset_author\n", "    ,datasets.tags AS dataset_tags\n", "    ,entries.entry as file_hash\n", "FROM CTE_VALID_DATASETS\n", "INNER JOIN ${target_catalog}.${target_schema}.datasets\n", "  ON\n", "    CTE_VALID_DATASETS.dataset_id=datasets.id\n", "    AND CTE_VALID_DATASETS.dataset_version=datasets.version\n", "LEFT JOIN ${target_catalog}.${target_schema}.dataset_entries AS entries\n", "  ON\n", "    entries.dataset_id=datasets.id\n", "    AND entries.dataset_version=datasets.version\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "11e13a05-cec3-486b-82ca-f7a05c5570fc", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.keep_on_hotstorage AS\n", "  WITH CTE_ORDER_VERSIONS AS (\n", "    SELECT\n", "      row_number() OVER (PARTITION BY id ORDER BY major_version DESC, minor_version DESC) AS _row_number\n", "      ,*\n", "    FROM ${target_catalog}.${target_schema}.datasets\n", "  ),\n", "  CTE_PICK_CURRENT_VERSION AS (\n", "    SELECT\n", "      datasets.id AS dataset_id\n", "      ,datasets.version AS dataset_version\n", "      ,datasets.metadata:dataset_name as dataset_name\n", "      ,datasets.author AS dataset_author\n", "      ,datasets.tags AS dataset_tags\n", "    FROM CTE_ORDER_VERSIONS AS datasets\n", "    WHERE\n", "      datasets._row_number = 1\n", "  ),\n", "  CTE_KEEP_TAGS AS (\n", "    select explode(dataset_tag_keep_on_hotstorage) AS tag\n", "  ),\n", "  CTE_VALID_DATASETS AS (\n", "    SELECT\n", "      dataset_id, dataset_version\n", "    FROM CTE_KEEP_TAGS\n", "    INNER JOIN CTE_PICK_CURRENT_VERSION\n", "      ON\n", "        array_contains(CTE_PICK_CURRENT_VERSION.dataset_tags, CTE_KEEP_TAGS.tag)\n", "    LEFT ANTI JOIN ${target_catalog}.${target_schema}.dataset_blacklist\n", "      ON\n", "        CTE_PICK_CURRENT_VERSION.dataset_id=dataset_blacklist.dataset_id\n", "    GROUP BY dataset_id, dataset_version\n", "  )\n", "  SELECT\n", "      datasets.id AS dataset_id\n", "      ,datasets.version AS dataset_version\n", "      ,datasets.metadata:dataset_name as dataset_name\n", "      ,datasets.author AS dataset_author\n", "      ,datasets.tags AS dataset_tags\n", "      ,entries.entry as file_hash\n", "  FROM CTE_VALID_DATASETS\n", "  INNER JOIN ${target_catalog}.${target_schema}.datasets\n", "    ON\n", "      CTE_VALID_DATASETS.dataset_id=datasets.id\n", "      AND CTE_VALID_DATASETS.dataset_version=datasets.version\n", "  LEFT JOIN ${target_catalog}.${target_schema}.dataset_entries AS entries\n", "    ON\n", "      entries.dataset_id=datasets.id\n", "      AND entries.dataset_version=datasets.version\n", "  ;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2350d83f-5b18-477c-b08b-d6ae4fec294c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["with cte as (\n", "  SELECT\n", "    dataset_id\n", "    ,dataset_version\n", "    ,max(dataset_tags) as dataset_tags\n", "  FROM ${target_catalog}.${target_schema}.keep_on_hotstorage\n", "  group BY\n", "    dataset_id\n", "    ,dataset_version\n", ")\n", "select\n", "  sum(case when array_contains(dataset_tags, \"to_be_retained\") then 1 else 0 end) AS tagged__to_be_retained\n", "  ,sum(case when array_contains(dataset_tags, \"keep_on_hot_storage\") then 1 else 0 end) AS tagged__keep_on_hot_storage\n", "  ,count(distinct dataset_id) AS count_of_datasets\n", "from cte\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "79dfa29b-1220-4091-a713-db07c035a0e7", "showTitle": false, "title": ""}}, "source": ["# Get ingest validity"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "51eb8090-22da-4c19-bc99-f1c94f837e0d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW ingest_validation_result AS\n", "select\n", "  file_hash AS recording_hash\n", "  ,UPPER(json_document:status) AS ingest_status\n", "from bronze.mdm.mdd_namespaces_latest\n", "where namespace_name='ingest-validation-result'\n", ";\n", "SELECT * FROM ingest_validation_result LIMIT 10"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "17acf8ae-ac6e-44e7-a54e-8dfdf906f4f5", "showTitle": false, "title": ""}}, "source": ["# Derive storage tiering decision"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b379097e-b8ea-4f33-8221-55bd2a964417", "showTitle": false, "title": ""}}, "source": ["## Stream level decision"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "34b22882-bbba-4b23-b384-45a4ad554223", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.base_archiving_info_streams AS\n", "  WITH PRE_DECIDED AS (\n", "    SELECT\n", "      streams.recording_hash\n", "      ,streams.split_hash\n", "      ,streams.split_created_at\n", "      ,streams.stream_hash\n", "      ,streams.stream_name\n", "      -- for analysis add flag about cause\n", "      ,CASE WHEN hotstorage.dataset_id IS NOT NULL THEN true ELSE false END AS file_in_hotstorage_dataset\n", "      -- file is old enough for tiering\n", "      ,streams.file_created_at AS stream_file_created_at\n", "      ,DATE_DIFF(now(), streams.file_created_at) AS stream_file_age_days\n", "      ,(DATE_DIFF(now(), streams.file_created_at) > min_file_age_days_before_tiering) AS file_is_old_enough_for_tiering\n", "      -- stream is valid for tiering\n", "      ,(valid_streams.stream_name IS NOT NULL) AS stream_is_valid_for_tiering\n", "      -- is blocking stream\n", "      ,(tiering_blocking_streams.stream_name IS NOT NULL) AS stream_is_blacklisted_for_tiering\n", "      -- ingest validation format\n", "      ,CASE WHEN ingest_validation_result.ingest_status = 'VALID' THEN true ELSE false END AS valid_ingest_format\n", "      ,streams.file_hash\n", "      ,streams.file_name\n", "      ,streams.file_size_bytes\n", "      ,streams.file_state\n", "      ,streams.file_sources\n", "      ,hotstorage.dataset_id AS hotstorage_dataset_id\n", "      ,hotstorage.dataset_version as hotstorage_dataset_version\n", "      ,hotstorage.dataset_author as hotstorage_dataset_author\n", "      ,hotstorage.dataset_name as hotstorage_dataset_name\n", "      ,hotstorage.dataset_tags as hotstorage_dataset_tags\n", "      ,now() as row_updated_at\n", "    FROM ${target_catalog}.${target_schema}.all_recordings AS streams\n", "    LEFT JOIN ${target_catalog}.${target_schema}.valid_streams AS valid_streams\n", "      ON\n", "        valid_streams.stream_name=streams.stream_name\n", "    LEFT JOIN ${target_catalog}.${target_schema}.tiering_blocking_streams AS tiering_blocking_streams\n", "      ON\n", "        tiering_blocking_streams.stream_name=streams.stream_name\n", "    LEFT JOIN ${target_catalog}.${target_schema}.keep_on_hotstorage AS hotstorage\n", "      ON\n", "        streams.stream_hash = hotstorage.file_hash\n", "        OR streams.split_hash = hotstorage.file_hash\n", "        OR streams.recording_hash = hotstorage.file_hash\n", "    LEFT JOIN ingest_validation_result\n", "      ON\n", "        ingest_validation_result.recording_hash = streams.recording_hash\n", "    WHERE\n", "      LENGTH(TRIM(COALESCE(streams.stream_name, \"\"))) > 0\n", "      -- only consider some TDS pools\n", "      AND array_contains(tds_pools_to_consider, streams.recording_tds_pool)\n", "      -- exclude some file extensions\n", "      AND NOT array_contains(excluded_file_extensions, streams.file_extension)\n", "  ),\n", "  DECIDED AS (\n", "    SELECT\n", "      *\n", "      -- final decision\n", "      ,CASE WHEN\n", "          (\n", "            file_is_old_enough_for_tiering\n", "            AND stream_is_valid_for_tiering\n", "            AND NOT stream_is_blacklisted_for_tiering\n", "            AND NOT file_in_hotstorage_dataset\n", "            AND valid_ingest_format\n", "          )\n", "          OR (\n", "            NOT valid_ingest_format\n", "            AND NOT file_in_hotstorage_dataset\n", "          )\n", "        THEN \n", "          true\n", "        ELSE\n", "          false\n", "        END\n", "        AS is_candidate_for_archiving\n", "    FROM PRE_DECIDED\n", "  ),\n", "  FILE_SOURCES_EXPLODED AS (\n", "    SELECT\n", "      recording_hash\n", "      ,split_hash\n", "      ,split_created_at\n", "      ,stream_hash\n", "      ,stream_name\n", "      ,file_in_hotstorage_dataset\n", "      ,file_is_old_enough_for_tiering\n", "      ,stream_file_created_at\n", "      ,stream_file_age_days\n", "      ,stream_is_valid_for_tiering\n", "      ,stream_is_blacklisted_for_tiering\n", "      ,valid_ingest_format\n", "      ,is_candidate_for_archiving\n", "      ,file_hash\n", "      ,file_name\n", "      ,file_size_bytes\n", "      ,file_state\n", "      ,explode(file_sources) AS file_source\n", "      ,hotstorage_dataset_id\n", "      ,hotstorage_dataset_version\n", "      ,hotstorage_dataset_author\n", "      ,hotstorage_dataset_name\n", "      ,hotstorage_dataset_tags\n", "      ,row_updated_at\n", "    FROM DECIDED\n", "    WHERE \n", "      stream_hash IS NOT NULL\n", "  ),\n", "  FILE_SOURCE_PARSED AS (\n", "    SELECT\n", "      recording_hash\n", "      ,split_hash\n", "      ,split_created_at\n", "      ,stream_hash\n", "      ,stream_name\n", "      ,file_in_hotstorage_dataset\n", "      ,file_is_old_enough_for_tiering\n", "      ,stream_file_created_at\n", "      ,stream_file_age_days\n", "      ,stream_is_valid_for_tiering\n", "      ,stream_is_blacklisted_for_tiering\n", "      ,valid_ingest_format\n", "      ,is_candidate_for_archiving\n", "      ,file_hash\n", "      ,file_name\n", "      ,file_size_bytes\n", "      ,file_state\n", "      ,file_source.url AS file_source_url\n", "      ,regexp_extract(file_source.url, 'https://[^/]+/[^/]+/([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})/', 1)  as file_source_tds_fid\n", "      ,regexp_extract(file_source.url, 'https:\\\\/\\\\/st(\\\\w+)\\\\d{2}(euw)',1) AS file_source_tds_pool\n", "      ,file_source.provider AS file_source_provider\n", "      ,file_source.state AS file_source_state\n", "      ,hotstorage_dataset_id\n", "      ,hotstorage_dataset_version\n", "      ,hotstorage_dataset_author\n", "      ,hotstorage_dataset_name\n", "      ,hotstorage_dataset_tags\n", "      ,row_updated_at\n", "    FROM FILE_SOURCES_EXPLODED\n", "  )\n", "  SELECT\n", "    recording_hash\n", "    ,split_hash\n", "    ,split_created_at\n", "    ,stream_hash\n", "    ,stream_name\n", "    ,file_in_hotstorage_dataset\n", "    ,stream_file_created_at\n", "    ,stream_file_age_days\n", "    ,file_is_old_enough_for_tiering\n", "    ,stream_is_valid_for_tiering\n", "    ,stream_is_blacklisted_for_tiering\n", "    ,valid_ingest_format\n", "    ,is_candidate_for_archiving\n", "    ,file_hash\n", "    ,file_name\n", "    ,file_size_bytes\n", "    ,file_state\n", "    ,file_source_url\n", "    ,file_source_tds_fid\n", "    ,file_source_tds_pool\n", "    ,file_source_provider\n", "    ,file_source_state\n", "    ,hotstorage_dataset_id\n", "    ,hotstorage_dataset_version\n", "    ,hotstorage_dataset_author\n", "    ,hotstorage_dataset_name\n", "    ,hotstorage_dataset_tags\n", "    ,row_updated_at\n", "  FROM FILE_SOURCE_PARSED\n", "  WHERE\n", "    -- lower(provider)='azure'\n", "    lower(file_source_state) in ('active', 'archived')\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c98bbea6-874b-4d11-b51c-8c0e118396ad", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE ${target_catalog}.${target_schema}.base_archiving_info_streams;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "cea252ec-b408-4c02-98f5-edc6588dee16", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM ${target_catalog}.${target_schema}.base_archiving_info_streams;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a62ff752-3c07-42b4-adaa-add7209cbd83", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE ${target_catalog}.${target_schema}.base_archiving_info_streams COMPUTE STATISTICS FOR COLUMNS\n", "    recording_hash\n", "    ,split_hash\n", "    ,split_created_at\n", "    ,stream_hash\n", "    ,stream_name\n", "    ,file_hash\n", "    ,file_name\n", "    ,file_size_bytes\n", "    ,file_state\n", "    ,file_source_tds_fid\n", "    ,file_source_tds_pool\n", "    ,file_source_provider\n", "    ,file_source_state\n", "    ,hotstorage_dataset_id\n", "    ,hotstorage_dataset_version\n", "    ,hotstorage_dataset_author\n", "    ,hotstorage_dataset_name"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "9fcd4b51-d2f3-445f-ba18-96f71749098f", "showTitle": false, "title": ""}}, "source": ["### Validate"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "42a952a2-41d3-4c91-8c70-b2c2352ed918", "showTitle": false, "title": ""}}, "source": ["#### If a recording is in a keep_on_hot_storage dataset it must be excluded from archiving"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6dc23069-4612-493e-8782-02454700643f", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*) = 0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "WHERE\n", "  is_candidate_for_archiving = true\n", "  AND file_in_hotstorage_dataset = true"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "628813e0-3e55-491b-93b2-cdf9b82b6701", "showTitle": false, "title": ""}}, "source": ["#### If a recording is invalid, it is immediately flagged as is_candidate_for_archiving besides it is on keep_on_hotstorage _list_"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "61225bdd-03e0-46df-bd2d-fbee09d8944a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*) = 0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "WHERE\n", "  is_candidate_for_archiving = false\n", "  AND valid_ingest_format = false\n", "  AND file_in_hotstorage_dataset = false"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3682beba-7881-4a21-beea-2457b8961eb7", "showTitle": false, "title": ""}}, "source": ["#### Only files old enough for tiering and invalid ingests have is_candidate_for_archiving set `true`"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "92af7d92-135d-4302-8318-cc5498951d0a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*) = 0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "WHERE\n", "  is_candidate_for_archiving\n", "  AND valid_ingest_format\n", "  AND NOT file_is_old_enough_for_tiering"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7863d5a7-6697-468e-96d5-f1cfeffd03d7", "showTitle": false, "title": ""}}, "source": ["#### A stream on the blacklist for tiering of a valid recording must not be a candidate for archiving"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0c905b11-67d5-4ae2-92e7-d72e7e4ced53", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*)=0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "WHERE\n", "  stream_is_blacklisted_for_tiering=true\n", "  AND valid_ingest_format=true\n", "  AND is_candidate_for_archiving"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "bd1c8093-a020-4711-84bc-04ef5678afa3", "showTitle": false, "title": ""}}, "source": ["## Aggregate on split level"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "98aa3290-11bc-4d75-970f-f3761de7c41c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.base_archiving_info_splits AS\n", "  WITH CTE AS (\n", "    SELECT\n", "      recording_hash\n", "      ,split_hash\n", "      ,MAX(split_created_at) AS split_created_at\n", "      ,MAX(file_in_hotstorage_dataset) AS has_files_in_hotstorage_dataset\n", "      ,array_distinct(flatten(collect_list(hotstorage_dataset_tags))) AS hotstorage_dataset_tags\n", "      -- replace by checking on split file created\n", "      ,(DATE_DIFF(now(), MAX(split_created_at)) > min_file_age_days_before_tiering) AS split_is_old_enough_for_tiering\n", "      ,SUM(CAST(stream_is_valid_for_tiering AS INT)) > 0 AS has_streams_valid_for_tiering\n", "      ,SUM(CAST(stream_is_blacklisted_for_tiering AS INT)) > 0 AS has_streams_blacklisted_for_tiering\n", "      ,MAX(valid_ingest_format) AS valid_ingest_format\n", "      -- ,SUM(CAST(is_candidate_for_archiving AS INT)) > 0 AS is_candidate_for_archiving\n", "      ,SUM(file_size_bytes) AS sum_stream_file_size_bytes\n", "      ,MAX(row_updated_at) AS row_updated_at\n", "      ,collect_list(hotstorage_dataset_id) AS hoststorage_dataset_ids\n", "      ,COUNT(DISTINCT hotstorage_dataset_id) AS hoststorage_dataset_id_counts\n", "    FROM ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "    GROUP BY recording_hash, split_hash \n", "  )\n", "  SELECT\n", "    *\n", "    ,CASE WHEN\n", "      (\n", "        split_is_old_enough_for_tiering\n", "        AND has_streams_valid_for_tiering\n", "        AND NOT has_streams_blacklisted_for_tiering\n", "        AND NOT has_files_in_hotstorage_dataset\n", "        AND valid_ingest_format\n", "      )\n", "      OR (\n", "        NOT valid_ingest_format\n", "        AND NOT has_files_in_hotstorage_dataset\n", "      )\n", "    THEN \n", "      true\n", "    ELSE\n", "      false\n", "    END\n", "    AS is_candidate_for_archiving\n", "  FROM CTE\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "00d51068-2bb9-40ea-b94a-63f847fd666d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["OPTIMIZE ${target_catalog}.${target_schema}.base_archiving_info_splits;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0c183db7-9c7e-414b-9500-10f23a218bc4", "showTitle": false, "title": ""}}, "outputs": [], "source": ["VACUUM ${target_catalog}.${target_schema}.base_archiving_info_splits;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "219f7131-bd42-45ae-b7c9-d4193b5dda8f", "showTitle": false, "title": ""}}, "outputs": [], "source": ["ANALYZE TABLE ${target_catalog}.${target_schema}.base_archiving_info_splits COMPUTE STATISTICS FOR COLUMNS\n", "  recording_hash\n", "  ,split_hash\n", "  ,split_created_at\n", "  ,has_files_in_hotstorage_dataset\n", "  ,split_is_old_enough_for_tiering\n", "  ,has_streams_valid_for_tiering\n", "  ,has_streams_blacklisted_for_tiering\n", "  ,valid_ingest_format\n", "  ,is_candidate_for_archiving\n", "  ,sum_stream_file_size_bytes\n", "  ,row_updated_at\n", ";"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "af34ba03-6576-41c0-99e8-e7030cc530e4", "showTitle": false, "title": ""}}, "source": ["### Validate"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5d323199-4a0e-453e-8266-88dab916c0a6", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM ${target_catalog}.${target_schema}.base_archiving_info_splits where array_size(hotstorage_dataset_tags) > 0 LIMIT 10"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4fa3f628-5c82-4657-842d-1cfce5046a2b", "showTitle": false, "title": ""}}, "source": ["#### If a recording is in a keep_on_hot_storage dataset it must be excluded from archiving"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "61989730-cc1e-4d5f-b99a-d16bda8e31e4", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*) = 0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE\n", "  is_candidate_for_archiving = true\n", "  AND has_files_in_hotstorage_dataset = true\n", "limit 10"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5eb494b7-829a-4a50-bbe7-c76843a15b7f", "showTitle": false, "title": ""}}, "source": ["#### If a recording is invalid, it is immediately flagged as is_candidate_for_archiving besides it is on keep_on_hotstorage _list_"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c92b970f-43c4-4bec-a18a-06aff7317a2d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*) = 0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE\n", "  is_candidate_for_archiving = false\n", "  AND valid_ingest_format = false\n", "  AND has_files_in_hotstorage_dataset = false"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5261b3ae-6a01-4e4e-ac40-006ff5c14f07", "showTitle": false, "title": ""}}, "source": ["#### Only files old enough for tiering and invalid ingests have is_candidate_for_archiving set `true`"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7cd01795-b2e4-4451-8b34-43c57267b166", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*) = 0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE\n", "  is_candidate_for_archiving = true\n", "  AND valid_ingest_format = true\n", "  AND split_is_old_enough_for_tiering = false"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b512c405-54ac-4d34-b388-9c8b2f0bccc6", "showTitle": false, "title": ""}}, "source": ["#### A stream on the blacklist for tiering of a valid recording must not be a candidate for archiving"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2e221184-ab03-4e00-b7d9-b3ea9f705fc9", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  assert_true(count(*)=0)\n", "FROM ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE\n", "  is_candidate_for_archiving=true\n", "  AND valid_ingest_format=true\n", "  AND has_streams_blacklisted_for_tiering=true"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "484dab1e-fcb8-4ac9-800b-8f17465e592f", "showTitle": false, "title": ""}}, "source": ["## Analyse tiering decision on split level"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "348d454d-2b40-4e0f-b946-7ac1c2bbf0ce", "showTitle": false, "title": ""}}, "source": ["### Check final decision case of is in hotstorage dataset, ingest format is not valid"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f0baa27d-23ae-43df-9499-a8e479e6ed4c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- Expection set is empty as a file in hotstorage must not be tiered under any circumstances\n", "SELECT * FROM ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE \n", "  has_files_in_hotstorage_dataset\n", "  AND is_candidate_for_archiving\n", "LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8b25b971-55d5-4627-a955-5796597e4a0b", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  CASE WHEN has_files_in_hotstorage_dataset THEN 'Hot' ELSE 'Archive' END AS storage_tier\n", "  ,count(distinct split_hash) AS is_in_hotstorage_dataset\n", "FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE\n", "  split_is_old_enough_for_tiering\n", "GROUP BY has_files_in_hotstorage_dataset"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "3331c40b-3096-48c8-8589-dedce0c34e29", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"target_catalog": "dd_leadership_pub_sbx", "target_schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n  ,count(distinct split_hash) AS splits_ready_for_archiving\nFROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\nWHERE\n  split_is_old_enough_for_tiering\nGROUP BY is_candidate_for_archiving) SELECT `storage_tier`,SUM(`splits_ready_for_archiving`) `column_e4d6a9c52` FROM q GROUP BY `storage_tier`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "storage_tier", "id": "column_e4d6a9c51"}, "y": [{"column": "splits_ready_for_archiving", "id": "column_e4d6a9c52", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "pie", "isAggregationOn": true, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"column_e4d6a9c52": {"name": "Number of files eligible for storage tiering", "type": "pie", "yAxis": 0}}, "showDataLabels": true, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": false, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "type": "-"}, "yAxis": [{"type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "4f9d46fd-d565-41b6-912f-4ca091b5020a", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 12.998046875, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "storage_tier", "type": "column"}], "selects": [{"column": "storage_tier", "type": "column"}, {"alias": "column_e4d6a9c52", "args": [{"column": "splits_ready_for_archiving", "type": "column"}], "function": "SUM", "type": "function"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n", "  ,count(distinct split_hash) AS splits_ready_for_archiving\n", "FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE\n", "  split_is_old_enough_for_tiering\n", "GROUP BY is_candidate_for_archiving"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "dfdca9b1-7aa8-407b-a94b-33093d0ad2d1", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"target_catalog": "dd_leadership_pub_sbx", "target_schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n  ,ROUND(SUM(sum_stream_file_size_bytes)/1024/1024/1024/1024,0) AS split_storage_size_terabytes\nFROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\nWHERE\n  split_is_old_enough_for_tiering\nGROUP BY is_candidate_for_archiving) SELECT `storage_tier`,`split_storage_size_terabytes` FROM q", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "storage_tier", "id": "column_bf108ef01"}, "y": [{"column": "split_storage_size_terabytes", "id": "column_bf108ef02"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "pie", "isAggregationOn": false, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"split_storage_size_terabytes": {"name": "Data Volume in TiB per target tier after successful extraction", "type": "pie", "yAxis": 0}}, "showDataLabels": true, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": false, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "type": "-"}, "yAxis": [{"type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "362619d0-efc0-4039-a0ac-79d670e8c427", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 12.99853515625, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"selects": [{"column": "storage_tier", "type": "column"}, {"column": "split_storage_size_terabytes", "type": "column"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n", "  ,ROUND(SUM(sum_stream_file_size_bytes)/1024/1024/1024/1024,0) AS split_storage_size_terabytes\n", "FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "WHERE\n", "  split_is_old_enough_for_tiering\n", "GROUP BY is_candidate_for_archiving"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8823abf0-f1e8-4c85-a09e-b841b1af9254", "showTitle": false, "title": ""}}, "source": ["# Analyse tiering decision"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f07aea4a-68f9-4de9-8ab0-b0e4b9a6b3aa", "showTitle": false, "title": ""}}, "source": ["### Split Level decision sankey"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "52fca62e-4e6b-494f-92e7-d3b5467488a7", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"target_catalog": "dd_leadership_pub_sbx", "target_schema": "data_tiering_service"}, "collapsed": false, "command": "WITH OLD_ENOUGH_SPLITS AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n),\nTOO_YOUNG_SPLITS AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    NOT split_is_old_enough_for_tiering\n),\n-- Level 2\nOLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_KEEP_ON_HOT_STORAGE AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n    AND has_files_in_hotstorage_dataset\n    AND array_contains(hotstorage_dataset_tags, 'keep_on_hot_storage')\n),\nOLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_NO_DELETE AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n    AND has_files_in_hotstorage_dataset\n    AND array_contains(hotstorage_dataset_tags, 'to_be_retained')\n),\nOLD_ENOUGH_SPLITS_NOT_ON_HOTSTORAGE AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n    AND NOT has_files_in_hotstorage_dataset\n),\n-- Level 3\nOLD_ENOUGH_NOT_ON_HOTSTORAGE_BLACKLISTED AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n    AND NOT has_files_in_hotstorage_dataset\n    AND has_streams_blacklisted_for_tiering\n),\nOLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n    AND NOT has_files_in_hotstorage_dataset\n    AND NOT has_streams_blacklisted_for_tiering\n),\n-- Final\nOLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_CANDIDATE AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n    AND NOT has_files_in_hotstorage_dataset\n    AND NOT has_streams_blacklisted_for_tiering\n    AND is_candidate_for_archiving\n),\nOLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_NO_CANDIDATE AS (\n  SELECT\n    count(distinct split_hash) AS `value`\n  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n  WHERE\n    split_is_old_enough_for_tiering\n    AND NOT has_files_in_hotstorage_dataset\n    AND NOT has_streams_blacklisted_for_tiering\n    AND NOT is_candidate_for_archiving\n)\n-- ////////////////// BUILD VISUALIZATION\n-- Level 1\nSELECT\n  'Total' as stage1\n  ,'Too young for tiering' as stage2\n  ,NULL as stage3\n  ,NULL as stage4\n  ,null AS stage5\n  ,value\nFROM TOO_YOUNG_SPLITS\n-- Level 2\nUNION\nSELECT\n  'Total' as stage1\n  ,'Old enough for tiering' as stage2\n  ,'In keep_on_hotstorage dataset' AS stage3\n  ,NULL as stage4\n  ,null AS stage5\n  ,value\nFROM OLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_KEEP_ON_HOT_STORAGE\nUNION\nSELECT 'Total' as stage1\n  ,'Old enough for tiering' as stage2\n  ,'In to_be_retained dataset' AS stage3\n  ,NULL as stage4\n  ,null AS stage5\n  ,value\nFROM OLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_NO_DELETE\nUNION\n-- Level 3\nSELECT\n  'Total' as stage1\n  ,'Old enough for tiering' as stage2\n  ,'Not in keep_on_hotstorage_dataset' AS stage3\n  ,'Contains blacklisted stream' as stage4\n  ,null AS stage5\n  ,value\nFROM OLD_ENOUGH_NOT_ON_HOTSTORAGE_BLACKLISTED\n-- Level 4\nUNION\nSELECT\n  'Total' as stage1\n  ,'Old enough for tiering' as stage2\n  ,'Not in keep_on_hotstorage_dataset' AS stage3\n  ,'No blacklisted streams' as stage4\n  ,'Is tiering candidate' as stage5\n  ,value\nFROM OLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_CANDIDATE\nUNION\nSELECT\n  'Total' as stage1\n  ,'Old enough for tiering' as stage2\n  ,'Not in keep_on_hotstorage_dataset' AS stage3\n  ,'No blacklisted streams' as stage4\n  ,'Is no tiering candidate' as stage5\n  ,value\nFROM OLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_NO_CANDIDATE", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "SANKEY"}, {"key": "options", "value": {}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "ec80ee09-2141-4262-8d28-0118927f3ce5", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 12.************, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["WITH OLD_ENOUGH_SPLITS AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "),\n", "TOO_YOUNG_SPLITS AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    NOT split_is_old_enough_for_tiering\n", "),\n", "-- Level 2\n", "OLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_KEEP_ON_HOT_STORAGE AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "    AND has_files_in_hotstorage_dataset\n", "    AND array_contains(hotstorage_dataset_tags, 'keep_on_hot_storage')\n", "),\n", "OLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_NO_DELETE AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "    AND has_files_in_hotstorage_dataset\n", "    AND array_contains(hotstorage_dataset_tags, 'to_be_retained')\n", "),\n", "OLD_ENOUGH_SPLITS_NOT_ON_HOTSTORAGE AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "    AND NOT has_files_in_hotstorage_dataset\n", "),\n", "-- Level 3\n", "OLD_ENOUGH_NOT_ON_HOTSTORAGE_BLACKLISTED AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "    AND NOT has_files_in_hotstorage_dataset\n", "    AND has_streams_blacklisted_for_tiering\n", "),\n", "OLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "    AND NOT has_files_in_hotstorage_dataset\n", "    AND NOT has_streams_blacklisted_for_tiering\n", "),\n", "-- Final\n", "OLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_CANDIDATE AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "    AND NOT has_files_in_hotstorage_dataset\n", "    AND NOT has_streams_blacklisted_for_tiering\n", "    AND is_candidate_for_archiving\n", "),\n", "OLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_NO_CANDIDATE AS (\n", "  SELECT\n", "    count(distinct split_hash) AS `value`\n", "  FROM  ${target_catalog}.${target_schema}.base_archiving_info_splits\n", "  WHERE\n", "    split_is_old_enough_for_tiering\n", "    AND NOT has_files_in_hotstorage_dataset\n", "    AND NOT has_streams_blacklisted_for_tiering\n", "    AND NOT is_candidate_for_archiving\n", ")\n", "-- ////////////////// BUILD VISUALIZATION\n", "-- Level 1\n", "SELECT\n", "  'Total' as stage1\n", "  ,'Too young for tiering' as stage2\n", "  ,NULL as stage3\n", "  ,NULL as stage4\n", "  ,null AS stage5\n", "  ,value\n", "FROM TOO_YOUNG_SPLITS\n", "-- Level 2\n", "UNION\n", "SELECT\n", "  'Total' as stage1\n", "  ,'Old enough for tiering' as stage2\n", "  ,'In keep_on_hotstorage dataset' AS stage3\n", "  ,NULL as stage4\n", "  ,null AS stage5\n", "  ,value\n", "FROM OLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_KEEP_ON_HOT_STORAGE\n", "UNION\n", "SELECT 'Total' as stage1\n", "  ,'Old enough for tiering' as stage2\n", "  ,'In to_be_retained dataset' AS stage3\n", "  ,NULL as stage4\n", "  ,null AS stage5\n", "  ,value\n", "FROM OLD_ENOUGH_SPLITS_ON_HOTSTORAGE_TAG_NO_DELETE\n", "UNION\n", "-- Level 3\n", "SELECT\n", "  'Total' as stage1\n", "  ,'Old enough for tiering' as stage2\n", "  ,'Not in keep_on_hotstorage_dataset' AS stage3\n", "  ,'Contains blacklisted stream' as stage4\n", "  ,null AS stage5\n", "  ,value\n", "FROM OLD_ENOUGH_NOT_ON_HOTSTORAGE_BLACKLISTED\n", "-- Level 4\n", "UNION\n", "SELECT\n", "  'Total' as stage1\n", "  ,'Old enough for tiering' as stage2\n", "  ,'Not in keep_on_hotstorage_dataset' AS stage3\n", "  ,'No blacklisted streams' as stage4\n", "  ,'Is tiering candidate' as stage5\n", "  ,value\n", "FROM OLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_CANDIDATE\n", "UNION\n", "SELECT\n", "  'Total' as stage1\n", "  ,'Old enough for tiering' as stage2\n", "  ,'Not in keep_on_hotstorage_dataset' AS stage3\n", "  ,'No blacklisted streams' as stage4\n", "  ,'Is no tiering candidate' as stage5\n", "  ,value\n", "FROM OLD_ENOUGH_NOT_ON_HOTSTORAGE_NOT_BLACKLISTED_IS_NO_CANDIDATE"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4caa5b3e-c1e1-4de9-a44b-a894628dc41c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT * FROM  ${target_catalog}.${target_schema}.base_archiving_info_streams LIMIT 10 "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "fb465c86-4d52-4bad-9f27-897c47a3c9d8", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"target_catalog": "dd_leadership_pub_sbx", "target_schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n  ,count(distinct file_hash) AS files_to_be_moved_to_archive\nFROM  ${target_catalog}.${target_schema}.base_archiving_info_streams\nGROUP BY is_candidate_for_archiving) SELECT `storage_tier`,SUM(`files_to_be_moved_to_archive`) `column_71afd75212` FROM q GROUP BY `storage_tier`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "storage_tier", "id": "column_71afd75218"}, "y": [{"column": "files_to_be_moved_to_archive", "id": "column_71afd75212", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "isAggregationOn": true, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"column_71afd75212": {"name": "files_to_be_moved_to_archive", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": false, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "title": {"text": "Move file to Archive?"}, "type": "-"}, "yAxis": [{"title": {"text": "Number of files at storage tier"}, "type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "2fe764c2-4216-4a9f-af87-9f7adff55ccb", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 14.0, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "storage_tier", "type": "column"}], "selects": [{"column": "storage_tier", "type": "column"}, {"alias": "column_71afd75212", "args": [{"column": "files_to_be_moved_to_archive", "type": "column"}], "function": "SUM", "type": "function"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n", "  ,count(distinct file_hash) AS files_to_be_moved_to_archive\n", "FROM  ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "GROUP BY is_candidate_for_archiving"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6bb39805-c8ab-48e9-b521-b045dbb47f7f", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"target_catalog": "dd_leadership_pub_sbx", "target_schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n  ,round(sum(file_size_bytes)/1024/1024/1024/1024, 0) AS data_volume_terabyte\nFROM  ${target_catalog}.${target_schema}.base_archiving_info_streams\nGROUP BY is_candidate_for_archiving) SELECT `storage_tier`,SUM(`data_volume_terabyte`) `column_71afd75215` FROM q GROUP BY `storage_tier`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "storage_tier", "id": "column_71afd75221"}, "y": [{"column": "data_volume_terabyte", "id": "column_71afd75215", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "isAggregationOn": true, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"column_71afd75215": {"name": "data_volume_terabyte", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": false, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "title": {"text": "Storage Tier"}, "type": "-"}, "yAxis": [{"title": {"text": "Data volume at storage tier / TiB"}, "type": "-"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "729313cc-0dff-4de7-ae4a-f6eba11d127c", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 15.0, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "storage_tier", "type": "column"}], "selects": [{"column": "storage_tier", "type": "column"}, {"alias": "column_71afd75215", "args": [{"column": "data_volume_terabyte", "type": "column"}], "function": "SUM", "type": "function"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  CASE WHEN is_candidate_for_archiving THEN 'Archive' ELSE 'Hot' END AS storage_tier\n", "  ,round(sum(file_size_bytes)/1024/1024/1024/1024, 0) AS data_volume_terabyte\n", "FROM  ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "GROUP BY is_candidate_for_archiving"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "a0205aca-c8a7-42ad-a033-65d758d9a351", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["Databricks visualization. Run in Databricks to view."]}, "metadata": {"application/vnd.databricks.v1.subcommand+json": {"baseErrorDetails": null, "bindings": {"target_catalog": "dd_leadership_pub_sbx", "target_schema": "data_tiering_service"}, "collapsed": false, "command": "%sql WITH q AS (SELECT\n  CASE WHEN file_in_hotstorage_dataset THEN 'Yes' ELSE 'No' END AS in_hotstorage_dataset\n  ,round(sum(file_size_bytes)/1024/1024/1024/1024, 0) AS data_volume_terabyte\nFROM  ${target_catalog}.${target_schema}.base_archiving_info_streams\nGROUP BY file_in_hotstorage_dataset) SELECT `in_hotstorage_dataset`,SUM(`data_volume_terabyte`) `column_71afd75223` FROM q GROUP BY `in_hotstorage_dataset`", "commandTitle": "Visualization 1", "commandType": "auto", "commandVersion": 0, "commentThread": [], "commentsVisible": false, "contentSha256Hex": null, "customPlotOptions": {"redashChart": [{"key": "type", "value": "CHART"}, {"key": "options", "value": {"alignYAxesAtZero": true, "coefficient": 1, "columnConfigurationMap": {"x": {"column": "in_hotstorage_dataset", "id": "column_71afd75222"}, "y": [{"column": "data_volume_terabyte", "id": "column_71afd75223", "transform": "SUM"}]}, "dateTimeFormat": "DD/MM/YYYY HH:mm", "direction": {"type": "counterclockwise"}, "error_y": {"type": "data", "visible": true}, "globalSeriesType": "column", "isAggregationOn": true, "legend": {"traceorder": "normal"}, "missingValuesAsZero": true, "numberFormat": "0,0.[00000]", "percentFormat": "0[.]00%", "series": {"error_y": {"type": "data", "visible": true}, "stacking": null}, "seriesOptions": {"column_71afd75223": {"name": "data_volume_terabyte", "type": "column", "yAxis": 0}}, "showDataLabels": false, "sizemode": "diameter", "sortX": true, "sortY": true, "swappedAxes": false, "textFormat": "", "useAggregationsUi": true, "valuesOptions": {}, "version": 2, "xAxis": {"labels": {"enabled": true}, "title": {"text": "In Hotstorage Dataset?"}, "type": "-"}, "yAxis": [{"title": {"text": "Data Volume / TiB"}, "type": "linear"}, {"opposite": true, "type": "-"}]}}]}, "datasetPreviewNameToCmdIdMap": {}, "diffDeletes": [], "diffInserts": [], "displayType": "redashChart", "error": null, "errorDetails": null, "errorSummary": null, "errorTraceType": null, "finishTime": 0, "globalVars": {}, "guid": "", "height": "auto", "hideCommandCode": false, "hideCommandResult": false, "iPythonMetadata": null, "inputWidgets": {}, "isLockedInExamMode": false, "latestUser": "a user", "latestUserId": null, "listResultMetadata": null, "metadata": {"byteLimit": 2048000, "rowLimit": 10000}, "nuid": "354381ad-a1df-445b-bb93-1e658caeccac", "origId": 0, "parentHierarchy": [], "pivotAggregation": null, "pivotColumns": null, "position": 16.0, "resultDbfsErrorMessage": null, "resultDbfsStatus": "INLINED_IN_TREE", "results": null, "showCommandTitle": false, "startTime": 0, "state": "input", "streamStates": {}, "subcommandOptions": {"queryPlan": {"groups": [{"column": "in_hotstorage_dataset", "type": "column"}], "selects": [{"column": "in_hotstorage_dataset", "type": "column"}, {"alias": "column_71afd75223", "args": [{"column": "data_volume_terabyte", "type": "column"}], "function": "SUM", "type": "function"}]}}, "submitTime": 0, "subtype": "tableResultSubCmd.visualization", "tableResultIndex": 0, "tableResultSettingsMap": null, "useConsistentColors": false, "version": "CommandV1", "width": "auto", "workflows": null, "xColumns": null, "yColumns": null}}}], "source": ["SELECT\n", "  CASE WHEN file_in_hotstorage_dataset THEN 'Yes' ELSE 'No' END AS in_hotstorage_dataset\n", "  ,round(sum(file_size_bytes)/1024/1024/1024/1024, 0) AS data_volume_terabyte\n", "FROM  ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "GROUP BY file_in_hotstorage_dataset\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "617738ce-87bc-4498-936e-ac4ce9b77322", "showTitle": false, "title": ""}}, "source": ["# Create table of duplicate TDS fids"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0de51071-26d9-4ca1-83bf-c8eb28b900e0", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TEMPORARY VIEW duplicate_stream_hashes AS \n", "  SELECT\n", "    stream_hash\n", "    ,count(distinct file_source_tds_fid) AS number_of_tds_entries_per_file\n", "    ,max(file_size_bytes) as file_size_bytes\n", "    -- ,sum(file_size_bytes) as sum_file_size_bytes\n", "  FROM ${target_catalog}.${target_schema}.base_archiving_info_streams\n", "  WHERE\n", "    lower(file_source_state)='active'\n", "  GROUP BY stream_hash\n", "  HAVING number_of_tds_entries_per_file > 1\n", ";"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1b58a6d6-09ab-43b8-b9cf-0cd75f948a30", "showTitle": false, "title": ""}}, "outputs": [], "source": ["CREATE OR REPLACE TABLE ${target_catalog}.${target_schema}.duplicate_active_tds_fids AS\n", "  WITH CTE AS (\n", "    SELECT \n", "      duplicates.stream_hash\n", "      ,files.file_source_tds_fid\n", "      ,MAX(duplicates.file_size_bytes) AS file_size_bytes\n", "      ,MAX(duplicates.number_of_tds_entries_per_file) AS number_of_tds_entries_per_hash\n", "      ,MAX(files.file_source_url) AS file_source_url\n", "      ,MAX(files.file_source_tds_pool) AS file_source_tds_pool\n", "      ,MAX(files.file_source_provider) AS file_source_provider\n", "      ,MAX(files.file_source_state) AS file_source_state\n", "    FROM duplicate_stream_hashes AS duplicates\n", "    INNER JOIN ${target_catalog}.${target_schema}.base_archiving_info_streams AS files\n", "      ON\n", "        files.stream_hash=duplicates.stream_hash    \n", "    GROUP BY\n", "      duplicates.stream_hash\n", "      ,files.file_source_tds_fid\n", "  )\n", "  SELECT \n", "    -- include pool to avoid that data being currently moved between pools is accidentially deleted\n", "    row_number() OVER (PARTITION BY stream_hash, file_source_tds_pool ORDER BY file_source_tds_fid) AS _row_number\n", "    ,stream_hash\n", "    ,file_size_bytes\n", "    ,number_of_tds_entries_per_hash\n", "    ,file_source_url\n", "    ,file_source_tds_pool\n", "    ,file_source_tds_fid\n", "    ,file_source_provider\n", "    ,file_source_state\n", "  FROM CTE"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7feefcb5-e2b1-4475-bed1-aadc856b2a41", "showTitle": false, "title": ""}}, "source": ["## Are there any files from different pools in it?"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f10ad0a0-e7be-4e0f-aaa0-a0969a169b17", "showTitle": false, "title": ""}}, "source": ["## Analyze how many duplicates we have"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "abf57c70-3ef7-47a6-80b9-557ea9b31f63", "showTitle": false, "title": ""}}, "outputs": [], "source": [";WITH DUPLICATED_DATA_VOLUME AS (\n", "  SELECT\n", "    stream_hash\n", "    ,number_of_tds_entries_per_file-1 AS duplicate_file_count\n", "    ,(number_of_tds_entries_per_file-1)*file_size_bytes AS duplicated_data_bytes\n", "  FROM duplicate_stream_hashes\n", ")\n", "SELECT\n", "  SUM(duplicate_file_count)\n", "  ,ROUND(SUM(duplicated_data_bytes)/1024/1024/1024, 1) duplicated_data_gib\n", "FROM DUPLICATED_DATA_VOLUME\n", "limit 100"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "7da73655-f75a-453c-be7d-bd85a1783dc8", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  _row_number\n", "  ,stream_hash\n", "  ,file_source_tds_fid\n", "  ,file_source_tds_pool\n", "  ,file_source_url\n", "FROM ${target_catalog}.${target_schema}.duplicate_active_tds_fids \n", "-- WHERE\n", "--   number_of_tds_entries_per_hash=(SELECT MAX(number_of_tds_entries_per_hash) FROM ${target_catalog}.${target_schema}.duplicate_active_tds_fids )\n", "limit 1000"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d63f63f7-b9cf-41f0-a97d-77a998fa98ad", "showTitle": false, "title": ""}}, "outputs": [], "source": ["SELECT\n", "  file_source_tds_fid, count(*) AS count_\n", "FROM ${target_catalog}.${target_schema}.duplicate_active_tds_fids \n", "GROUP BY file_source_tds_fid\n", "HAVING count_ > 1\n", "LIMIT 100"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "d93d715d-c9f2-4201-8900-924d2659da59", "showTitle": false, "title": ""}}, "source": ["# Experiments"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "03aeab3b-10f5-4064-8974-7fdb1864258d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["-- SELECT * FROM ${target_catalog}.${target_schema}.base_archiving_info_streams LIMIT 10"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "e91a2100-9786-4c29-8536-b9a8bced481f", "showTitle": false, "title": ""}}, "outputs": [], "source": []}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "environmentMetadata": {"base_environment": "", "client": "1"}, "language": "sql", "notebookMetadata": {"pythonIndentUnit": 4}, "notebookName": "12 Join stream state and dataset state to splits", "widgets": {"target_catalog": {"currentValue": "dd_leadership_pub_sbx", "nuid": "bc297589-162d-4f97-8cde-ec66e6866c86", "typedWidgetInfo": null, "widgetInfo": {"widgetType": "text", "defaultValue": "dd_leadership_pub_sbx", "label": "", "name": "target_catalog", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}, "target_schema": {"currentValue": "data_tiering_service", "nuid": "86c0a4d4-0500-4c8a-86f8-398da1cfc205", "typedWidgetInfo": null, "widgetInfo": {"widgetType": "text", "defaultValue": "data_tiering_service", "label": "", "name": "target_schema", "options": {"widgetType": "text", "autoCreated": false, "validationRegex": null}}}}}}, "nbformat": 4, "nbformat_minor": 0}