-- Databricks notebook source
-- MAGIC %md
-- MA<PERSON><PERSON> ```python
-- MAGIC # ===================================================================================
-- MAGIC #  C O P Y R I G H T
-- MAGIC # -----------------------------------------------------------------------------------
-- MAGIC #  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
-- MAGIC # ===================================================================================
-- MAGIC ```

-- COMMAND ----------

-- MAGIC %md
-- MAGIC There's a bunch of measurements on ``g3vprdaq`` TDS pool that is not usable following the file name pattern ``BRINGUP*NO-WARRANTIES*``.
-- MAGIC These files are not in use.

-- COMMAND ----------

DECLARE OR REPLACE min_file_age_days_before_tiering INT;
SET VARIABLE min_file_age_days_before_tiering=(SELECT CAST(value AS INT) FROM dd_leadership_pub_sbx.data_tiering_service.__configuration WHERE key='min_file_age_days_before_tiering');

-- COMMAND ----------

CREATE OR REPLACE TABLE dd_leadership_pub_sbx.data_tiering_service.90_adhoc_bringup_measurements_tiering AS
  WITH CTE AS (
    SELECT
      scene_files.tds_pool
      ,scene_files.file_hash
      ,scene_files.content_type
      ,scene_files.created_at AS file_created_at
      ,scene_files.file_name
      ,split_namespace.json_document
      ,split_namespace.created_at AS namespace_created_at
    FROM silver.tds.file_entries AS scene_files
    LEFT JOIN bronze.mdm.mdd_namespaces_latest_v2 AS split_namespace
      ON
        split_namespace.namespace_name='split-ingest'
        AND split_namespace.file_hash=scene_files.file_hash
    WHERE
      scene_files.file_extension='scene'
      AND scene_files.file_name LIKE 'BRINGUP%NO-WARRANTIES%'
      AND scene_files.tds_pool='g3vprdaq'
      AND split_namespace.file_hash IS NULL
  ),
  DECISION AS (
    SELECT
      recordings.recording_hash
      ,recordings.split_hash
      ,recordings.stream_hash
      ,recordings.stream_name
      -- for analysis add flag about cause
      ,CASE WHEN hotstorage.dataset_id IS NOT NULL THEN true ELSE false END AS file_in_hotstorage_dataset
      -- file is old enough for tiering
      ,(DATE_DIFF(now(), recordings.file_created_at)  > min_file_age_days_before_tiering) AS file_is_old_enough_for_tiering
      -- stream is valid for tiering
      ,(valid_streams.stream_name IS NOT NULL) AS stream_is_valid_for_tiering
      ,CASE WHEN 
        -- file must not be in a dataset
        hotstorage.dataset_id IS NULL
        -- must be older than .. days
        AND (DATE_DIFF(now(), recordings.file_created_at) > min_file_age_days_before_tiering)
        -- must be on the valid stream list
        AND valid_streams.stream_name IS NOT NULL
      THEN 
        true
      ELSE
        false
      END
      AS file_move_to_archive
      ,recordings.file_hash
      ,recordings.file_name
      ,recordings.file_size_bytes
      ,recordings.file_state
      ,explode(file_sources) AS file_source
      ,hotstorage.dataset_id AS hotstorage_dataset_id
      ,hotstorage.dataset_version AS hotstorage_dataset_version
      ,hotstorage.dataset_author AS  hotstorage_dataset_author
      ,hotstorage.dataset_name AS hotstorage_dataset_name
    FROM CTE
    INNER JOIN dd_leadership_pub_sbx.data_tiering_service.all_recordings AS recordings
      ON
        recordings.split_hash=cte.file_hash
    LEFT JOIN dd_leadership_pub_sbx.data_tiering_service.keep_on_hotstorage AS hotstorage
      ON
        recordings.stream_hash = hotstorage.file_hash
        OR recordings.split_hash = hotstorage.file_hash
        OR recordings.recording_hash = hotstorage.file_hash
    LEFT JOIN dd_leadership_pub_sbx.data_tiering_service.valid_streams AS valid_streams
      ON
        valid_streams.stream_name=recordings.stream_name
    WHERE
      recordings.stream_hash IS NOT NULL
  )
  SELECT
      recording_hash
      ,split_hash
      ,stream_hash
      ,stream_name
      ,file_in_hotstorage_dataset
      ,file_is_old_enough_for_tiering
      ,stream_is_valid_for_tiering
      ,file_move_to_archive
      ,file_hash
      ,file_name
      ,file_size_bytes
      ,file_state
      ,file_source.url AS file_source_url
      ,regexp_extract(file_source.url, 'https://[^/]+/[^/]+/([0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12})/', 1)  as file_source_tds_fid
      ,regexp_extract(file_source.url, 'https:\\/\\/st(\\w+)\\d{2}(euw)',1) AS file_source_tds_pool
      ,file_source.provider AS file_source_provider
      ,file_source.state AS file_source_state
      ,hotstorage_dataset_id
      ,hotstorage_dataset_version
      ,hotstorage_dataset_author
      ,hotstorage_dataset_name
  FROM DECISION
  WHERE
    stream_is_valid_for_tiering

-- COMMAND ----------

SELECT hotstorage_dataset_name, hotstorage_dataset_id, max(hotstorage_dataset_author), ROUND(SUM(file_size_bytes)/1024/1024/1024, 1) file_size_gigabytes
FROM dd_leadership_pub_sbx.data_tiering_service.90_adhoc_bringup_measurements_tiering
WHERE file_in_hotstorage_dataset
GROUP BY hotstorage_dataset_id, hotstorage_dataset_name

-- COMMAND ----------

SELECT
  ROUND(SUM(
    CASE WHEN file_in_hotstorage_dataset 
      THEN 
        file_size_bytes 
      ELSE 
        0
      END
  )/1024/1024/1024, 1) AS file_size_keep_on_hot_gigabyte
  ,ROUND(SUM(
    CASE WHEN NOT file_in_hotstorage_dataset 
      THEN 
        file_size_bytes 
      ELSE 
        0
      END
  )/1024/1024/1024, 1) AS file_size_archive_gigabyte
FROM dd_leadership_pub_sbx.data_tiering_service.90_adhoc_bringup_measurements_tiering

-- COMMAND ----------

SELECT * FROM dd_leadership_pub_sbx.data_tiering_service.90_adhoc_bringup_measurements_tiering WHERE file_move_to_archive LIMIT 10

-- COMMAND ----------


