# AB#280058 Data retention PoC

[AB#280058](https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/280058) is about creating a first draft of how we can do business value driven storage tiering.

# 1. Introduction & Goals

The Data retention PoC is about understanding how we can do storage tiering driven by the value of the data for the customer.

For the evaluation, it is important to have a running PoC that is accepted by the business, i.e. ADA Viper domain.

# 2. Constraints

- Only files stored within the MDM sub-system TDS are considered for storage tiering.
- No new micro-services shall be introduced.
- Reuse of existing services
- Reuse of existing data and metadata. No new data sources possible.
- Must integrate with the existing Viper Data Extraction
- Must integrate with the MDM system for data management
- Business teams must be able to excempt data from tiering
- We need to identify how much data is excluded from storage tiering and who is excluding it.


# 3. Context & Scope

This PoC works in the context of the Data Storage System, i.e. Measurement Data Management System (MDM), responsible for providing data and the Business Domains, i.e. Viper, defining the value of the data.

![image](docs/context.drawio.svg)

As of the start of the PoC we had more than 100 PB of active data in Azure BLOB storages. On the one hand, this incured high cloud consumption costs. On the other hand, it was unclear which of this data is actually of value for the customer.


# 4. Solution Strategy

## 4.1. Overview

![image](docs/solution_strategy.drawio.svg)

- Excluding data from storage tiering
  - MDM Datasets are used to build bundles of files stored in MDM TDS system that must be kept in on hot storage aka ``ACTIVE``.
  - The dataset mut be tagged with ``keep_on_hot_storage``
  - Only recording, split and stream files are allowed in the dataset. All other files are ignored.
- Business decission
  - Aggregating existing data to derive a decission on tiering, shall happen with Azure Databricks as all data is available as well as the necessary functionality is provided.
  - Viper DSP (Nicola Pirlo) takes care of implementing the business decission
  - Business decissions are provided as one or more databricks tables.
- Storage Tiering execution
  - MDM Datasets are consumed
  - Consumes tables with business decisions 


## 4.2. Deriving business decission

tbd (Nicola Pirlo)


## 4.3. Storage Tiering decission

The whole storage tiering decission is implemented as a multi step Databricks Workflow.
The data transformations are implemented as SQL.

### 4.3.1. Overview

![image](docs/tiering_decision_db_workflow.png)

The tiering decision process creates several tables within the schema ``dd_leadership_pub_sbx.data_tiering_service``:

|Table Name|Created by|Used by|Purpose|
|--|--|--|--|
|``__configuration``|``00 Configuration``|all|Contains configuration parameters for the storage tiering, e.g. how old files must be before considering them for archiving|
|``__state_tracking_table``|``20 Apply target storage tiering``|``20 Apply target storage tiering``|Keeps track of the archiving execution.|
|``aad_users``|``01 Sync Users via DUDA``|tbd|Contains all users from active directory to enable mapping of datasets to ADA domains and teams.
|``all_recordings``|``10 Create base set of recording, splits and streams``|tbd|Resolves the recording hierarchy from the ``gmdm.json`` over split to stream level to derive decision on ADA drive ontology relevant entities like the split level.
|``azure_rehydration_costs``|``90 Update rehydration costs``|support team|Contains the approximational costs for rehydration of archived files on a per file base |
|``dataset_blacklist``|``00 Datasets to ignore``|``12 Join stream state and dataset state to splits``|This table contains a list of datasets that must be ignored for the storage tiering decision, e.g. because some teams have whitelisted too many files. As it is against the team autonomy, it also includes mandatory fields to tie the blacklisting to a decission ticket for process documentation.
|``dataset_entries``|``02 Sync Datasets``|``12 Join stream state and dataset state to splits``|A Databricks mirror of datasets from the MDM datasets service. Needs to move to use the official tables as soon as possible.
|``datasets``|``02 Sync Datasets``|``12 Join stream state and dataset state to splits``|A Databricks mirror of datasets from the MDM datasets service. Needs to move to use the official tables as soon as possible.
|``extraction_settings_all``|``11 Create table of already processed stream``|``15 Verify all extractions are executed``|Contains the current extraction status for a stream file. It uses the ``dsp_de_split_extracted`` namespace to determin which extractions have been executed for the streams of a split.
|``keep_on_hotstorage``|``12 Join stream state and dataset state to splits``|``12 Join stream state and dataset state to splits``|Contains all files hashes that must be kept on hot storage as they are in a relevant dataset.|
|``keep_on_hotstorage_notfound``|``12 Join stream state and dataset state to splits``|``12 Join stream state and dataset state to splits``|These files belong to a ``keep_on_hot_storage`` dataset, however we failed to map them to a _split_ or _stream_ file. Usually, this happens because the file is actual an image or a preview video which are not supported.
|``tiering_blocking_streams``|``00 Configuration``|``12 Join stream state and dataset state to splits``|Splits that contain a stream listed in this table are kept on hot storage|
|``tiering_decision``|``00 Configuration``|
|``valid_streams``|``00 Configuration``|``15 Verify all extractions are executed``|Table holds the data streams that are valid to tier. All other streams are ignored|

### 4.3.2. Configuration

The ``__configuration`` table holds the following configuration parameters:

|parameter name|dat type|value|meaning|
|--|--|--|--|
|dataset_tag_keep_on_hotstorage|array<string>|['keep_on_hot_storage','to_be_retained']|Only datasets that have one of these tags are considered for the 
|min_file_age_days_before_tiering|int|60|Minimum age of a file before it is even considered for moving to archive tier. The age is measured in days relative to the ``file_created_at`` attribute.
|tds_pools_to_consider|array<string>|['g3vprdaq']|Only pools in this list are included in the storage tiering.
|excluded_file_extensions|array<string>|['recbin']|File extensions, i.e. after the last ``.`` in the file name, that are excluded from moving to archive tier.|


## 4.4. Tiering decision execution

The execution of the tiering decission is part of the overall Databricks Workflow.
This part is written in Python as it interacts with the data, i.e. tiering decision and logging tables, as well as HTTPS services for execution.