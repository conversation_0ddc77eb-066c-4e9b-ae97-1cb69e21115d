{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f73eb45a-4b71-4afc-9a79-bc4bc2736961", "showTitle": false, "title": ""}}, "source": ["### read data for the namespaces:\n", "- datamanagement-drive-info\n", "- datamanagement-recording-info\n", "- datamanagement-split-info\n", "- datamanagement-co-driver-tags"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5fdd47a3-f826-498d-8762-1dc5e4c517a7", "showTitle": false, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE IF NOT EXISTS dd_unicorn_sbx.data_quality.level_0_ns_check(file_hash string, namespaces_present boolean, ns_present_info MAP<string, boolean>, namespaces_valid boolean, namespace_drive_info_valid string, namespace_recording_info_valid string) USING DELTA CLUSTER BY (file_hash);\n", "\n", "CREATE TABLE IF NOT EXISTS dd_unicorn_sbx.data_quality.level_1_ns_check(file_hash string, namespaces_present boolean, ns_present_info MAP<string, boolean>, namespaces_valid boolean, namespace_split_info_valid string, namespace_codriver_info_valid string) USING DELTA CLUSTER BY (file_hash);\n", "\n", "CREATE TABLE IF NOT EXISTS dd_unicorn_sbx.data_quality.level_1_ns_check_sample(file_hash string, namespaces_present boolean, ns_present_info MAP<string, boolean>, namespaces_valid boolean, namespace_split_info_valid string, namespace_codriver_info_valid string) USING DELTA CLUSTER BY (file_hash);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d83343a2-adf9-4cb1-8380-e3156178e0c5", "showTitle": false, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import explode, col, str_to_map, from_json, when, json_tuple, get_json_object, expr,udf, regexp_replace\n", "from pyspark.sql.types import StructField, ArrayType, StringType, StructType, List, DateType, MapType, BooleanType, TimestampType\n", "from pyspark.sql import DataFrame\n", "import re, json\n", "from pydantic import ValidationError, validator"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ac0b1dcd-a0a4-449b-8282-2882ffcc22a1", "showTitle": false, "title": ""}}, "outputs": [], "source": ["BRONZE_NS_TABLE = \"bronze.mdm.mdd_namespaces_latest\"\n", "SILVER_TDS_ENTRY_TABLE = \"silver.tds.file_entries\"\n", "\n", "DRIVE_INFO_NS = \"datamanagement-drive-info\"\n", "RECORDING_INFO_NS = \"datamanagement-recording-info\"\n", "SPLIT_INFO_NS = \"datamanagement-split-info\"\n", "CO_DRIVER_TAGS_NS = \"datamanagement-co-driver-tags\"\n", "\n", "# TDS_POOLS = [\"g3vprdaq\", \"clrecompute\", \"g3closedloop\", \"pacetestpace\", \"g5cariadebdl01\"]\n", "TDS_POOLS = [\"g3vprdaq\", \"clrecompute\", \"g3closedloop\", \"g5cariadebdl01\"]\n", "RECORDING_FILE_NAME = \"gmdm.json\"\n", "SPLIT_CONTENT_TYPE = \"plain/text;split=true\""]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "86b78a6b-6f1e-4fdb-92dd-8a56faf23fe9", "showTitle": false, "title": ""}}, "outputs": [], "source": ["recording_df: DataFrame = (\n", "    spark.read.table(SILVER_TDS_ENTRY_TABLE)\n", "    .filter(col(\"tds_pool\").isin(TDS_POOLS))\n", "    .where(col(\"file_name\") == RECORDING_FILE_NAME)\n", "    .where(col(\"file_state\") == \"ACTIVE\")\n", "    .select(\"file_hash\", \"file_name\")\n", "    .repartition(\"file_hash\")\n", ")\n", "split_df: DataFrame = (\n", "    spark.read.table(SILVER_TDS_ENTRY_TABLE)\n", "    .filter(col(\"tds_pool\").isin(TDS_POOLS))\n", "    .where(col(\"content_type\") == SPLIT_CONTENT_TYPE)\n", "    .where(col(\"file_state\") == \"ACTIVE\")\n", "    .select(\"file_hash\", \"file_name\")\n", "    .repartition(\"file_hash\")\n", ")\n", "drive_info_df: DataFrame = (\n", "    spark.read.table(BRONZE_NS_TABLE)\n", "    .where(col(\"namespace_name\") == DRIVE_INFO_NS)\n", "    .select(\"file_hash\", \"json_document\")\n", "    .repartition(\"file_hash\")\n", ")\n", "recording_info_df: DataFrame = (\n", "    spark.read.table(BRONZE_NS_TABLE)\n", "    .where(col(\"namespace_name\") == RECORDING_INFO_NS)\n", "    .select(\"file_hash\", \"json_document\")\n", "    .repartition(\"file_hash\")\n", ")\n", "split_info_df: DataFrame = (\n", "    spark.read.table(BRONZE_NS_TABLE)\n", "    .where(col(\"namespace_name\") == SPLIT_INFO_NS)\n", "    .select(col(\"file_hash\"), \"json_document\")\n", "    .repartition(\"file_hash\")\n", ")\n", "codriver_info_df: DataFrame = (\n", "    spark.read.table(BRONZE_NS_TABLE)\n", "    .where(col(\"namespace_name\") == CO_DRIVER_TAGS_NS)\n", "    .select(\"file_hash\", \"json_document\")\n", "    .repartition(\"file_hash\")\n", ")\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "1cb4b1d3-0c8f-40ff-a0b1-cdfa375ae11f", "showTitle": false, "title": ""}}, "source": ["#### check how many recordings have the namespaces recording-info and drive-info attached"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "892bed36-0edd-4e41-a921-845cd106e883", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m\n", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)\n", "File \u001b[0;32m<command-2189547538909003>, line 4\u001b[0m\n", "\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mpyspark\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msql\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfunctions\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pandas_udf, PandasUDFType\n", "\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mpyspark\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msql\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BooleanType, StringType\n", "\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mnamespace_validation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m validate_recording_info\n", "\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mnamespace_validation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m validate_drive_info\n", "\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mnamespace_validation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m validate_split_info\n", "\n", "File \u001b[0;32m/Workspace/Shared/[Unicorn] Data Structural Checks/namespace_validation.py:2\u001b[0m\n", "\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n", "\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmdm_namespaces\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mrecording_base\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DataManagementRecordingInfoPayload\n", "\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmdm_namespaces\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcodriver_tags\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DataManagementCoDriverTagsPayload\n", "\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON>\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmdm_namespaces\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdrive\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdrive\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DataManagementDrivePayload\n", "\n", "File \u001b[0;32m/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py:33\u001b[0m\n", "\u001b[1;32m     30\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01mdatetime\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m datetime\n", "\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01m<PERSON>ping\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON>rt\u001b[39;00m Any, Dict, List, Optional\n", "\u001b[0;32m---> 33\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01mpydantic\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mv1\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m BaseModel \u001b[38;5;28;01mas\u001b[39;00m _BaseModel\n", "\u001b[1;32m     34\u001b[0m \u001b[38;5;28;01m<PERSON><PERSON>\u001b[39;00m \u001b[38;5;21;01<PERSON>ydantic\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mv1\u001b[39;00m \u001b[38;5;28;01<PERSON><PERSON><PERSON>\u001b[39;00m Extra, StrictStr, constr\n", "\u001b[1;32m     36\u001b[0m \u001b[38;5;28;01m<PERSON>rom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Action, Namespace\n", "\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'pydantic.v1'"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "datasetInfos": [], "jupyterProps": {"ename": "ModuleNotFoundError", "evalue": "No module named 'pydantic.v1'"}, "metadata": {"errorSummary": "<span class='ansi-red-fg'>ModuleNotFoundError</span>: No module named 'pydantic.v1'"}, "removedWidgets": [], "sqlProps": null, "stackFrames": ["\u001b[0;31m---------------------------------------------------------------------------\u001b[0m", "\u001b[0;31mModuleNotFoundError\u001b[0m                       <PERSON><PERSON> (most recent call last)", "File \u001b[0;32m<command-2189547538909003>, line 4\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpyspark\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msql\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mfunctions\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m pandas_udf, PandasUDFType\n\u001b[1;32m      2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpyspark\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01msql\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mtypes\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BooleanType, StringType\n\u001b[0;32m----> 4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mnamespace_validation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m validate_recording_info\n\u001b[1;32m      5\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mnamespace_validation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m validate_drive_info\n\u001b[1;32m      6\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mnamespace_validation\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m validate_split_info\n", "File \u001b[0;32m/Workspace/Shared/[Unicorn] Data Structural Checks/namespace_validation.py:2\u001b[0m\n\u001b[1;32m      1\u001b[0m \u001b[38;5;28;01mimport\u001b[39;00m \u001b[38;5;21;01<PERSON><PERSON><PERSON>\u001b[39;00m\n\u001b[0;32m----> 2\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpace\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmdm_namespaces\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mrecording_base\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DataManagementRecordingInfoPayload\n\u001b[1;32m      3\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpace\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmdm_namespaces\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcodriver_tags\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DataManagementCoDriverTagsPayload\n\u001b[1;32m      4\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpace\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mmdm_namespaces\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdrive\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mdrive\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m DataManagementDrivePayload\n", "File \u001b[0;32m/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py:33\u001b[0m\n\u001b[1;32m     30\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mdatetime\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m datetime\n\u001b[1;32m     31\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mtyping\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Any, Dict, List, Optional\n\u001b[0;32m---> 33\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpydantic\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mv1\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m BaseModel \u001b[38;5;28;01mas\u001b[39;00m _BaseModel\n\u001b[1;32m     34\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01mpydantic\u001b[39;00m\u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mv1\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Extra, StrictStr, constr\n\u001b[1;32m     36\u001b[0m \u001b[38;5;28;01mfrom\u001b[39;00m \u001b[38;5;21;01m.\u001b[39;00m\u001b[38;5;21;01mcore\u001b[39;00m \u001b[38;5;28;01mimport\u001b[39;00m Action, Namespace\n", "\u001b[0;31mModuleNotFoundError\u001b[0m: No module named 'pydantic.v1'"], "type": "baseError"}}}], "source": ["from pyspark.sql.functions import pandas_udf, PandasUDFType\n", "from pyspark.sql.types import BooleanType, StringType\n", "\n", "from namespace_validation import validate_recording_info\n", "from namespace_validation import validate_drive_info\n", "from namespace_validation import validate_split_info\n", "from namespace_validation import validate_codriver\n", "\n", "validate_recording_info = udf(validate_recording_info, StringType())\n", "validate_drive_info = udf(validate_drive_info, StringType())\n", "validate_split_info = udf(validate_split_info, StringType())\n", "validate_codriver = udf(validate_codriver, StringType())\n"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "de4716c4-b170-45fa-88f0-f963f9f14aa6", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 173.0 failed 4 times, most recent failure: Lost task 0.3 in stage 173.0 (TID 2953) (************* executor 0): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n", "    from pydantic.v1 import BaseModel as _BaseModel\n", "ModuleNotFoundError: No module named 'pydantic.v1'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n", "    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n", "    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n", "    f, return_type = read_command(pickleSer, infile)\n", "  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n", "    command = serializer._read_with_length(file)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n", "    raise SerializationError(\"Caused by \" + traceback.format_exc())\n", "pyspark.serializers.SerializationError: Caused by <PERSON><PERSON> (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n", "    from pydantic.v1 import BaseModel as _BaseModel\n", "ModuleNotFoundError: No module named 'pydantic.v1'\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n", "\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n", "\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n", "\tat scala.util.Using$.resource(Using.scala:269)\n", "\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n", "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n", "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n", "\tat java.lang.Thread.run(Thread.java:750)\n", "\n", "Driver stacktrace:\n", "\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:3900)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:3822)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:3809)\n", "\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\n", "\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\n", "\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\n", "\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:3809)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1685)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1670)\n", "\tat scala.Option.foreach(Option.scala:407)\n", "\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1670)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:4146)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4058)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4046)\n", "\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:54)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$runJob$1(DAGScheduler.scala:1348)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:1336)\n", "\tat org.apache.spark.SparkContext.runJobInternal(SparkContext.scala:3005)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$runSparkJobs$1(Collector.scala:355)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.collect.Collector.runSparkJobs(Collector.scala:299)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$collect$1(Collector.scala:384)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.collect.Collector.collect(Collector.scala:381)\n", "\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:122)\n", "\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:131)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:94)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:90)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:78)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$1(ResultCacheManager.scala:546)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.collectResult$1(ResultCacheManager.scala:540)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$2(ResultCacheManager.scala:555)\n", "\tat org.apache.spark.sql.execution.adaptive.ResultQueryStageExec.$anonfun$doMaterialize$1(QueryStageExec.scala:663)\n", "\tat org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:1175)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$6(SQLExecution.scala:769)\n", "\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$5(SQLExecution.scala:769)\n", "\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$4(SQLExecution.scala:769)\n", "\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$3(SQLExecution.scala:768)\n", "\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$2(SQLExecution.scala:767)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.withOptimisticTransaction(SQLExecution.scala:789)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$1(SQLExecution.scala:766)\n", "\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.$anonfun$run$1(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.IdentityClaim$.withClaim(IdentityClaim.scala:48)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.$anonfun$runWithCaptured$4(SparkThreadLocalForwardingThreadPoolExecutor.scala:91)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:90)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured$(SparkThreadLocalForwardingThreadPoolExecutor.scala:67)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:131)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.run(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n", "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n", "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n", "\tat java.lang.Thread.run(Thread.java:750)\n", "Caused by: org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n", "    from pydantic.v1 import BaseModel as _BaseModel\n", "ModuleNotFoundError: No module named 'pydantic.v1'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n", "    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n", "    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n", "    f, return_type = read_command(pickleSer, infile)\n", "  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n", "    command = serializer._read_with_length(file)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n", "    raise SerializationError(\"Caused by \" + traceback.format_exc())\n", "pyspark.serializers.SerializationError: Caused by <PERSON><PERSON> (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n", "    from pydantic.v1 import BaseModel as _BaseModel\n", "ModuleNotFoundError: No module named 'pydantic.v1'\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n", "\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n", "\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n", "\tat scala.util.Using$.resource(Using.scala:269)\n", "\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n", "\t... 3 more\n"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "datasetInfos": [], "jupyterProps": null, "metadata": {"errorSummary": "PythonException: Traceback (most recent call last):"}, "removedWidgets": [], "sqlProps": {"errorClass": null, "pysparkCallSite": null, "pysparkFragment": null, "sqlState": null, "startIndex": null, "stopIndex": null}, "stackFrames": ["org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 173.0 failed 4 times, most recent failure: Lost task 0.3 in stage 173.0 (TID 2953) (************* executor 0): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n    from pydantic.v1 import BaseModel as _BaseModel\nModuleNotFoundError: No module named 'pydantic.v1'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n    f, return_type = read_command(pickleSer, infile)\n  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n    command = serializer._read_with_length(file)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n    raise SerializationError(\"Caused by \" + traceback.format_exc())\npyspark.serializers.SerializationError: Caused by Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n    from pydantic.v1 import BaseModel as _BaseModel\nModuleNotFoundError: No module named 'pydantic.v1'\n\n\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n\tat scala.util.Using$.resource(Using.scala:269)\n\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:750)\n\nDriver stacktrace:\n\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:3900)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:3822)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:3809)\n\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\n\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\n\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\n\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:3809)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1685)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1670)\n\tat scala.Option.foreach(Option.scala:407)\n\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1670)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:4146)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4058)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4046)\n\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:54)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$runJob$1(DAGScheduler.scala:1348)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:1336)\n\tat org.apache.spark.SparkContext.runJobInternal(SparkContext.scala:3005)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$runSparkJobs$1(Collector.scala:355)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.collect.Collector.runSparkJobs(Collector.scala:299)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$collect$1(Collector.scala:384)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.collect.Collector.collect(Collector.scala:381)\n\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:122)\n\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:131)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:94)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:90)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:78)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$1(ResultCacheManager.scala:546)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.collectResult$1(ResultCacheManager.scala:540)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$2(ResultCacheManager.scala:555)\n\tat org.apache.spark.sql.execution.adaptive.ResultQueryStageExec.$anonfun$doMaterialize$1(QueryStageExec.scala:663)\n\tat org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:1175)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$6(SQLExecution.scala:769)\n\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$5(SQLExecution.scala:769)\n\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$4(SQLExecution.scala:769)\n\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$3(SQLExecution.scala:768)\n\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$2(SQLExecution.scala:767)\n\tat org.apache.spark.sql.execution.SQLExecution$.withOptimisticTransaction(SQLExecution.scala:789)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$1(SQLExecution.scala:766)\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.$anonfun$run$1(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.IdentityClaim$.withClaim(IdentityClaim.scala:48)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.$anonfun$runWithCaptured$4(SparkThreadLocalForwardingThreadPoolExecutor.scala:91)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:90)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured$(SparkThreadLocalForwardingThreadPoolExecutor.scala:67)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:131)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.run(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n    from pydantic.v1 import BaseModel as _BaseModel\nModuleNotFoundError: No module named 'pydantic.v1'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n    f, return_type = read_command(pickleSer, infile)\n  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n    command = serializer._read_with_length(file)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n    raise SerializationError(\"Caused by \" + traceback.format_exc())\npyspark.serializers.SerializationError: Caused by Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pace/mdm_namespaces/__init__.py\", line 33, in <module>\n    from pydantic.v1 import BaseModel as _BaseModel\nModuleNotFoundError: No module named 'pydantic.v1'\n\n\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n\tat scala.util.Using$.resource(Using.scala:269)\n\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n\t... 3 more\n"], "type": "baseError"}}}], "source": ["from pyspark.sql.functions import create_map, lit\n", "# recording_sample = recording_df.limit(100)\n", "recording_sample = recording_df\n", "recording_result_df = (\n", "    recording_sample\n", "    .join(drive_info_df, recording_sample[\"file_hash\"] == drive_info_df[\"file_hash\"], \"left_outer\")\n", "    .withColumn(\"namespace_attached_drive_info\", drive_info_df[\"file_hash\"].isNotNull())\n", "    .join(recording_info_df, recording_sample[\"file_hash\"] == recording_info_df[\"file_hash\"], \"left_outer\")\n", "    .withColumn(\"namespace_attached_recording_info\", recording_info_df[\"file_hash\"].isNotNull())\n", "    .select(recording_sample[\"file_hash\"], \"file_name\", \"namespace_attached_drive_info\", \"namespace_attached_recording_info\", \n", "            drive_info_df[\"json_document\"].alias(\"drive_info_content\"), recording_info_df[\"json_document\"].alias(\"recording_info_content\"))\n", "    .withColumn(\"namespaces_present\", col(\"namespace_attached_drive_info\") & col(\"namespace_attached_recording_info\"))\n", "    .withColumn(\"ns_present_info\", create_map(lit(\"drive_info\"), col(\"namespace_attached_drive_info\"), lit(\"recording_info\"), col(\"namespace_attached_recording_info\")))\n", "    .drop(\"namespace_attached_drive_info\", \"namespace_attached_recording_info\")\n", "    # validate namespaces\n", "    .withColumn(\"namespace_drive_info_valid\", validate_drive_info(col(\"drive_info_content\")))\n", "    .withColumn(\"namespace_recording_info_valid\", validate_recording_info(col(\"recording_info_content\")))\n", "    # .drop(\"drive_info_content\", \"recording_info_content\")    \n", "    # .withColumn(\"namespaces_valid\", col(\"drive_info_valid\") & col(\"recording_info_valid\"))\n", "    .withColumn(\"namespaces_valid\", when((col(\"namespace_drive_info_valid\") == \"Valid\") & (col(\"namespace_recording_info_valid\") == \"Valid\"), True).otherwise(False))\n", "    # .withColumn(\"ns_valid_info\", create_map(lit(\"drive_info\"), col(\"drive_info_valid\"), lit(\"recording_info\"), col(\"recording_info_valid\")))\n", "    .drop(\"file_name\", \"drive_info_content\", \"recording_info_content\")\n", ")\n", "display(recording_result_df)\n", "recording_result_df.write.format(\"delta\").mode(\"overwrite\").saveAsTable(\"dd_unicorn_sbx.data_quality.level_0_ns_check\")\n"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "6a15b3e3-6113-44b6-9577-09e00b19a670", "showTitle": false, "title": ""}}, "source": ["#### check how many splits have the namespaces split-info and co-driver-tags attached"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d26a7556-34fd-4035-aaa8-1d4ee786ad82", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 18.0 failed 4 times, most recent failure: Lost task 0.3 in stage 18.0 (TID 111) (************* executor 0): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n", "    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n", "    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n", "    f, return_type = read_command(pickleSer, infile)\n", "  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n", "    command = serializer._read_with_length(file)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n", "    raise SerializationError(\"Caused by \" + traceback.format_exc())\n", "pyspark.serializers.SerializationError: Caused by <PERSON><PERSON> (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n", "\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n", "\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n", "\tat scala.util.Using$.resource(Using.scala:269)\n", "\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n", "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n", "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n", "\tat java.lang.Thread.run(Thread.java:750)\n", "\n", "Driver stacktrace:\n", "\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:3897)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:3819)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:3806)\n", "\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\n", "\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\n", "\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\n", "\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:3806)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1685)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1670)\n", "\tat scala.Option.foreach(Option.scala:407)\n", "\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1670)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:4143)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4055)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4043)\n", "\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:54)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$runJob$1(DAGScheduler.scala:1348)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:1336)\n", "\tat org.apache.spark.SparkContext.runJobInternal(SparkContext.scala:3005)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$runSparkJobs$1(Collector.scala:355)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.collect.Collector.runSparkJobs(Collector.scala:299)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$collect$1(Collector.scala:384)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.collect.Collector.collect(Collector.scala:381)\n", "\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:122)\n", "\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:131)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:94)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:90)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:78)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$1(ResultCacheManager.scala:546)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.collectResult$1(ResultCacheManager.scala:540)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$2(ResultCacheManager.scala:555)\n", "\tat org.apache.spark.sql.execution.adaptive.ResultQueryStageExec.$anonfun$doMaterialize$1(QueryStageExec.scala:663)\n", "\tat org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:1175)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$6(SQLExecution.scala:769)\n", "\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$5(SQLExecution.scala:769)\n", "\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$4(SQLExecution.scala:769)\n", "\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$3(SQLExecution.scala:768)\n", "\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$2(SQLExecution.scala:767)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.withOptimisticTransaction(SQLExecution.scala:789)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$1(SQLExecution.scala:766)\n", "\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.$anonfun$run$1(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.IdentityClaim$.withClaim(IdentityClaim.scala:48)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.$anonfun$runWithCaptured$4(SparkThreadLocalForwardingThreadPoolExecutor.scala:91)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:90)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured$(SparkThreadLocalForwardingThreadPoolExecutor.scala:67)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:131)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.run(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n", "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n", "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n", "\tat java.lang.Thread.run(Thread.java:750)\n", "Caused by: org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n", "    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n", "    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n", "    f, return_type = read_command(pickleSer, infile)\n", "  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n", "    command = serializer._read_with_length(file)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n", "    raise SerializationError(\"Caused by \" + traceback.format_exc())\n", "pyspark.serializers.SerializationError: Caused by <PERSON><PERSON> (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n", "\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n", "\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n", "\tat scala.util.Using$.resource(Using.scala:269)\n", "\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n", "\t... 3 more\n"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "datasetInfos": [], "jupyterProps": null, "metadata": {"errorSummary": "Command skipped"}, "removedWidgets": [], "sqlProps": {"errorClass": null, "pysparkCallSite": null, "pysparkFragment": null, "sqlState": null, "startIndex": null, "stopIndex": null}, "stackFrames": ["org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 18.0 failed 4 times, most recent failure: Lost task 0.3 in stage 18.0 (TID 111) (************* executor 0): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n    f, return_type = read_command(pickleSer, infile)\n  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n    command = serializer._read_with_length(file)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n    raise SerializationError(\"Caused by \" + traceback.format_exc())\npyspark.serializers.SerializationError: Caused by Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\n\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n\tat scala.util.Using$.resource(Using.scala:269)\n\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:750)\n\nDriver stacktrace:\n\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:3897)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:3819)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:3806)\n\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\n\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\n\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\n\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:3806)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1685)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1670)\n\tat scala.Option.foreach(Option.scala:407)\n\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1670)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:4143)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4055)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4043)\n\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:54)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$runJob$1(DAGScheduler.scala:1348)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:1336)\n\tat org.apache.spark.SparkContext.runJobInternal(SparkContext.scala:3005)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$runSparkJobs$1(Collector.scala:355)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.collect.Collector.runSparkJobs(Collector.scala:299)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$collect$1(Collector.scala:384)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.collect.Collector.collect(Collector.scala:381)\n\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:122)\n\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:131)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:94)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:90)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:78)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$1(ResultCacheManager.scala:546)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.collectResult$1(ResultCacheManager.scala:540)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$2(ResultCacheManager.scala:555)\n\tat org.apache.spark.sql.execution.adaptive.ResultQueryStageExec.$anonfun$doMaterialize$1(QueryStageExec.scala:663)\n\tat org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:1175)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$6(SQLExecution.scala:769)\n\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$5(SQLExecution.scala:769)\n\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$4(SQLExecution.scala:769)\n\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$3(SQLExecution.scala:768)\n\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$2(SQLExecution.scala:767)\n\tat org.apache.spark.sql.execution.SQLExecution$.withOptimisticTransaction(SQLExecution.scala:789)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$1(SQLExecution.scala:766)\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.$anonfun$run$1(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.IdentityClaim$.withClaim(IdentityClaim.scala:48)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.$anonfun$runWithCaptured$4(SparkThreadLocalForwardingThreadPoolExecutor.scala:91)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:90)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured$(SparkThreadLocalForwardingThreadPoolExecutor.scala:67)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:131)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.run(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n    f, return_type = read_command(pickleSer, infile)\n  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n    command = serializer._read_with_length(file)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n    raise SerializationError(\"Caused by \" + traceback.format_exc())\npyspark.serializers.SerializationError: Caused by Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\n\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n\tat scala.util.Using$.resource(Using.scala:269)\n\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n\t... 3 more\n"], "type": "baseError"}}}], "source": ["# split_sample = split_df.limit(100)\n", "split_sample = split_df\n", "split_result_df = (split_sample\n", "    .join(split_info_df, split_sample[\"file_hash\"] == split_info_df[\"file_hash\"], \"left_outer\")\n", "    .withColumn(\"namespace_attached_split_info\", split_info_df[\"file_hash\"].isNotNull())\n", "    .join(codriver_info_df, split_sample[\"file_hash\"] == codriver_info_df[\"file_hash\"], \"left_outer\")\n", "    .withColumn(\"namespace_attached_codriver_info\", codriver_info_df[\"file_hash\"].isNotNull())\n", "    .select(split_sample[\"file_hash\"], \"file_name\", \"namespace_attached_split_info\", \"namespace_attached_codriver_info\", \n", "            split_info_df[\"json_document\"].alias(\"split_info_content\"), codriver_info_df[\"json_document\"].alias(\"codriver_info_content\"))\n", "    .withColumn(\"namespaces_present\", col(\"namespace_attached_split_info\") & col(\"namespace_attached_codriver_info\"))\n", "    .withColumn(\"ns_present_info\", create_map(lit(\"split_info\"), col(\"namespace_attached_split_info\"), lit(\"codriver_info\"), col(\"namespace_attached_codriver_info\")))\n", "    .drop(\"namespace_attached_split_info\", \"namespace_attached_codriver_info\")\n", "    # validate namespaces\n", "    .withColumn('namespace_split_info_valid', validate_split_info(col(\"split_info_content\")))\n", "    .withColumn('namespace_codriver_info_valid', validate_codriver(col(\"codriver_info_content\")))\n", "    # .drop(\"split_info_content\", \"codriver_info_content\")\n", "    # .withColumn(\"namespaces_valid\", when(col(\"split_info_valid\") & col(\"codriver_info_valid\"))\n", "    .withColumn(\"namespaces_valid\", when((col(\"namespace_split_info_valid\") == \"Valid\") & (col(\"namespace_codriver_info_valid\") == \"Valid\"), True).otherwise(False))\n", "    # .withColumn(\"ns_valid_info\", create_map(lit(\"split_info\"), col(\"split_info_valid\"), lit(\"codriver_info\"), col(\"codriver_info_valid\")))\n", "    .drop(\"file_name\", \"split_info_content\", \"codriver_info_content\")\n", ")\n", "display(split_result_df)\n", "split_result_df.write.format(\"delta\").mode(\"overwrite\").saveAsTable(\"dd_unicorn_sbx.data_quality.level_1_ns_check_sample\")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "94f8331f-f752-4bbd-a7ab-548b2b223434", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/plain": ["org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 18.0 failed 4 times, most recent failure: Lost task 0.3 in stage 18.0 (TID 111) (************* executor 0): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n", "    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n", "    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n", "    f, return_type = read_command(pickleSer, infile)\n", "  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n", "    command = serializer._read_with_length(file)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n", "    raise SerializationError(\"Caused by \" + traceback.format_exc())\n", "pyspark.serializers.SerializationError: Caused by <PERSON><PERSON> (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n", "\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n", "\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n", "\tat scala.util.Using$.resource(Using.scala:269)\n", "\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n", "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n", "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n", "\tat java.lang.Thread.run(Thread.java:750)\n", "\n", "Driver stacktrace:\n", "\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:3897)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:3819)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:3806)\n", "\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\n", "\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\n", "\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\n", "\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:3806)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1685)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1670)\n", "\tat scala.Option.foreach(Option.scala:407)\n", "\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1670)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:4143)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4055)\n", "\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4043)\n", "\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:54)\n", "\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$runJob$1(DAGScheduler.scala:1348)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:1336)\n", "\tat org.apache.spark.SparkContext.runJobInternal(SparkContext.scala:3005)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$runSparkJobs$1(Collector.scala:355)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.collect.Collector.runSparkJobs(Collector.scala:299)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$collect$1(Collector.scala:384)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.collect.Collector.collect(Collector.scala:381)\n", "\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:122)\n", "\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:131)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:94)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:90)\n", "\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:78)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$1(ResultCacheManager.scala:546)\n", "\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.collectResult$1(ResultCacheManager.scala:540)\n", "\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$2(ResultCacheManager.scala:555)\n", "\tat org.apache.spark.sql.execution.adaptive.ResultQueryStageExec.$anonfun$doMaterialize$1(QueryStageExec.scala:663)\n", "\tat org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:1175)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$6(SQLExecution.scala:769)\n", "\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$5(SQLExecution.scala:769)\n", "\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$4(SQLExecution.scala:769)\n", "\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$3(SQLExecution.scala:768)\n", "\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$2(SQLExecution.scala:767)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.withOptimisticTransaction(SQLExecution.scala:789)\n", "\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$1(SQLExecution.scala:766)\n", "\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.$anonfun$run$1(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.IdentityClaim$.withClaim(IdentityClaim.scala:48)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.$anonfun$runWithCaptured$4(SparkThreadLocalForwardingThreadPoolExecutor.scala:91)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:90)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured$(SparkThreadLocalForwardingThreadPoolExecutor.scala:67)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:131)\n", "\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.run(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n", "\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n", "\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n", "\tat java.lang.Thread.run(Thread.java:750)\n", "Caused by: org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n", "    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n", "    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n", "  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n", "    f, return_type = read_command(pickleSer, infile)\n", "  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n", "    command = serializer._read_with_length(file)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n", "    raise SerializationError(\"Caused by \" + traceback.format_exc())\n", "pyspark.serializers.SerializationError: Caused by <PERSON><PERSON> (most recent call last):\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n", "    return self.loads(obj)\n", "  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n", "    return cloudpickle.loads(obj, encoding=encoding)\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n", "    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n", "  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n", "    from typing_extensions import deprecated\n", "ImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n", "\n", "\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n", "\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n", "\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n", "\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n", "\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n", "\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n", "\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n", "\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n", "\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n", "\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n", "\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n", "\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n", "\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n", "\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n", "\tat scala.util.Using$.resource(Using.scala:269)\n", "\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n", "\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n", "\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n", "\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n", "\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n", "\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n", "\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n", "\t... 3 more\n"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "arguments": {}, "datasetInfos": [], "jupyterProps": null, "metadata": {"errorSummary": "Command skipped"}, "removedWidgets": [], "sqlProps": {"errorClass": null, "pysparkCallSite": null, "pysparkFragment": null, "sqlState": null, "startIndex": null, "stopIndex": null}, "stackFrames": ["org.apache.spark.SparkException: Job aborted due to stage failure: Task 0 in stage 18.0 failed 4 times, most recent failure: Lost task 0.3 in stage 18.0 (TID 111) (************* executor 0): org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n    f, return_type = read_command(pickleSer, infile)\n  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n    command = serializer._read_with_length(file)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n    raise SerializationError(\"Caused by \" + traceback.format_exc())\npyspark.serializers.SerializationError: Caused by Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\n\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n\tat scala.util.Using$.resource(Using.scala:269)\n\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:750)\n\nDriver stacktrace:\n\tat org.apache.spark.scheduler.DAGScheduler.failJobAndIndependentStages(DAGScheduler.scala:3897)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2(DAGScheduler.scala:3819)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$abortStage$2$adapted(DAGScheduler.scala:3806)\n\tat scala.collection.mutable.ResizableArray.foreach(ResizableArray.scala:62)\n\tat scala.collection.mutable.ResizableArray.foreach$(ResizableArray.scala:55)\n\tat scala.collection.mutable.ArrayBuffer.foreach(ArrayBuffer.scala:49)\n\tat org.apache.spark.scheduler.DAGScheduler.abortStage(DAGScheduler.scala:3806)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1(DAGScheduler.scala:1685)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$handleTaskSetFailed$1$adapted(DAGScheduler.scala:1670)\n\tat scala.Option.foreach(Option.scala:407)\n\tat org.apache.spark.scheduler.DAGScheduler.handleTaskSetFailed(DAGScheduler.scala:1670)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.doOnReceive(DAGScheduler.scala:4143)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4055)\n\tat org.apache.spark.scheduler.DAGSchedulerEventProcessLoop.onReceive(DAGScheduler.scala:4043)\n\tat org.apache.spark.util.EventLoop$$anon$1.run(EventLoop.scala:54)\n\tat org.apache.spark.scheduler.DAGScheduler.$anonfun$runJob$1(DAGScheduler.scala:1348)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.scheduler.DAGScheduler.runJob(DAGScheduler.scala:1336)\n\tat org.apache.spark.SparkContext.runJobInternal(SparkContext.scala:3005)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$runSparkJobs$1(Collector.scala:355)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.collect.Collector.runSparkJobs(Collector.scala:299)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$collect$1(Collector.scala:384)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.collect.Collector.collect(Collector.scala:381)\n\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:122)\n\tat org.apache.spark.sql.execution.collect.Collector$.collect(Collector.scala:131)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:94)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:90)\n\tat org.apache.spark.sql.execution.qrc.InternalRowFormat$.collect(cachedSparkResults.scala:78)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$1(ResultCacheManager.scala:546)\n\tat com.databricks.spark.util.FrameProfiler$.record(FrameProfiler.scala:94)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.collectResult$1(ResultCacheManager.scala:540)\n\tat org.apache.spark.sql.execution.qrc.ResultCacheManager.$anonfun$computeResult$2(ResultCacheManager.scala:555)\n\tat org.apache.spark.sql.execution.adaptive.ResultQueryStageExec.$anonfun$doMaterialize$1(QueryStageExec.scala:663)\n\tat org.apache.spark.sql.SparkSession.withActive(SparkSession.scala:1175)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$6(SQLExecution.scala:769)\n\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$5(SQLExecution.scala:769)\n\tat com.databricks.util.LexicalThreadLocal$Handle.runWith(LexicalThreadLocal.scala:63)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$4(SQLExecution.scala:769)\n\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$3(SQLExecution.scala:768)\n\tat scala.util.DynamicVariable.withValue(DynamicVariable.scala:62)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$2(SQLExecution.scala:767)\n\tat org.apache.spark.sql.execution.SQLExecution$.withOptimisticTransaction(SQLExecution.scala:789)\n\tat org.apache.spark.sql.execution.SQLExecution$.$anonfun$withThreadLocalCaptured$1(SQLExecution.scala:766)\n\tat java.util.concurrent.CompletableFuture$AsyncSupply.run(CompletableFuture.java:1604)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.$anonfun$run$1(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.IdentityClaim$.withClaim(IdentityClaim.scala:48)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.$anonfun$runWithCaptured$4(SparkThreadLocalForwardingThreadPoolExecutor.scala:91)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:90)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingHelper.runWithCaptured$(SparkThreadLocalForwardingThreadPoolExecutor.scala:67)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.runWithCaptured(SparkThreadLocalForwardingThreadPoolExecutor.scala:131)\n\tat org.apache.spark.util.threads.SparkThreadLocalCapturingRunnable.run(SparkThreadLocalForwardingThreadPoolExecutor.scala:134)\n\tat java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1149)\n\tat java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:624)\n\tat java.lang.Thread.run(Thread.java:750)\nCaused by: org.apache.spark.api.python.PythonException: Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1860, in main\n    func, profiler, deserializer, serializer = read_udfs(pickleSer, infile, eval_type)\n  File \"/databricks/spark/python/pyspark/worker.py\", line 1750, in read_udfs\n    udfs.append(read_single_udf(pickleSer, infile, eval_type, runner_conf, udf_index=i))\n  File \"/databricks/spark/python/pyspark/worker.py\", line 737, in read_single_udf\n    f, return_type = read_command(pickleSer, infile)\n  File \"/databricks/spark/python/pyspark/worker_util.py\", line 68, in read_command\n    command = serializer._read_with_length(file)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 197, in _read_with_length\n    raise SerializationError(\"Caused by \" + traceback.format_exc())\npyspark.serializers.SerializationError: Caused by Traceback (most recent call last):\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 193, in _read_with_length\n    return self.loads(obj)\n  File \"/databricks/spark/python/pyspark/serializers.py\", line 571, in loads\n    return cloudpickle.loads(obj, encoding=encoding)\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/__init__.py\", line 30, in <module>\n    from .core_schema import CoreConfig, CoreSchema, CoreSchemaType, ErrorType\n  File \"/local_disk0/.ephemeral_nfs/cluster_libraries/python/lib/python3.10/site-packages/pydantic_core/core_schema.py\", line 15, in <module>\n    from typing_extensions import deprecated\nImportError: cannot import name 'deprecated' from 'typing_extensions' (/databricks/python3/lib/python3.10/site-packages/typing_extensions.py)\n\n\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.handlePythonException(PythonRunner.scala:550)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:115)\n\tat org.apache.spark.sql.execution.python.BasePythonUDFRunner$$anon$2.read(PythonUDFRunner.scala:98)\n\tat org.apache.spark.api.python.BasePythonRunner$ReaderIterator.hasNext(PythonRunner.scala:506)\n\tat org.apache.spark.InterruptibleIterator.hasNext(InterruptibleIterator.scala:37)\n\tat scala.collection.Iterator$$anon$11.hasNext(Iterator.scala:491)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat scala.collection.Iterator$$anon$10.hasNext(Iterator.scala:460)\n\tat org.apache.spark.sql.catalyst.expressions.GeneratedClass$GeneratedIteratorForCodegenStage2.processNext(Unknown Source)\n\tat org.apache.spark.sql.execution.BufferedRowIterator.hasNext(BufferedRowIterator.java:43)\n\tat org.apache.spark.sql.execution.WholeStageCodegenEvaluatorFactory$WholeStageCodegenPartitionEvaluator$$anon$1.hasNext(WholeStageCodegenEvaluatorFactory.scala:43)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$5(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$3(UnsafeRowBatchUtils.scala:88)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.$anonfun$encodeUnsafeRows$1(UnsafeRowBatchUtils.scala:68)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.sql.execution.collect.UnsafeRowBatchUtils$.encodeUnsafeRows(UnsafeRowBatchUtils.scala:62)\n\tat org.apache.spark.sql.execution.collect.Collector.$anonfun$processFunc$2(Collector.scala:214)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$3(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.$anonfun$runTask$1(ResultTask.scala:82)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.ResultTask.runTask(ResultTask.scala:62)\n\tat org.apache.spark.TaskContext.runTaskWithListeners(TaskContext.scala:201)\n\tat org.apache.spark.scheduler.Task.doRunTask(Task.scala:186)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$5(Task.scala:151)\n\tat com.databricks.unity.UCSEphemeralState$Handle.runWith(UCSEphemeralState.scala:45)\n\tat com.databricks.unity.HandleImpl.runWith(UCSHandle.scala:103)\n\tat com.databricks.unity.HandleImpl.$anonfun$runWithAndClose$1(UCSHandle.scala:108)\n\tat scala.util.Using$.resource(Using.scala:269)\n\tat com.databricks.unity.HandleImpl.runWithAndClose(UCSHandle.scala:107)\n\tat org.apache.spark.scheduler.Task.$anonfun$run$1(Task.scala:145)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.scheduler.Task.run(Task.scala:99)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$9(Executor.scala:958)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally(SparkErrorUtils.scala:64)\n\tat org.apache.spark.util.SparkErrorUtils.tryWithSafeFinally$(SparkErrorUtils.scala:61)\n\tat org.apache.spark.util.Utils$.tryWithSafeFinally(Utils.scala:105)\n\tat org.apache.spark.executor.Executor$TaskRunner.$anonfun$run$3(Executor.scala:961)\n\tat scala.runtime.java8.JFunction0$mcV$sp.apply(JFunction0$mcV$sp.java:23)\n\tat com.databricks.spark.util.ExecutorFrameProfiler$.record(ExecutorFrameProfiler.scala:110)\n\tat org.apache.spark.executor.Executor$TaskRunner.run(Executor.scala:853)\n\t... 3 more\n"], "type": "baseError"}}}], "source": ["# %sql\n", "# OPTIMIZE dd_unicorn_sbx.data_quality.level_0_ns_check;\n", "# OPTIMIZE dd_unicorn_sbx.data_quality.level_1_ns_check;"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "environmentMetadata": {"base_environment": "", "client": "1"}, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 954961223862650, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "[Draft] Namespace checks exploration", "widgets": {}}}, "nbformat": 4, "nbformat_minor": 0}