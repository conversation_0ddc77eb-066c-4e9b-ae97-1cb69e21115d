{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "26b37c5f-0a44-44ec-bd5e-2d76504b64fd", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["['application/json;format=Cassandra', 'application/json', 'plain/text', 'application/bytesoup', 'application/bytesoup', 'application/x-hdf;bytesoup=true', 'plain/text;split=true', 'velodyne/octet-stream', 'gps/octet-stream', 'application/octet-stream', 'video/x-msvideo', 'application/kev']\n"]}], "source": ["import dataloop.schemas.content_type as type_resolver\n", "import logging\n", "\n", "def get_known_content_types(target_format: str = \"cassandra\") -> list:\n", "    known_content_types = list()\n", "    for definition_list in type_resolver.load_content_type_definitions(target_format).values():\n", "        known_content_types.extend(map(lambda definition: definition[\"content-type\"], definition_list))\n", "    return known_content_types\n", "\n", "known_content_types = get_known_content_types()\n", "print(known_content_types)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "af51f5e9-aeee-43b3-9b1a-d398085e3edc", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "execute_result", "data": {"text/plain": ["117578"]}, "execution_count": 7, "metadata": {}}], "source": ["from pyspark.sql.functions import col, udf\n", "from pyspark.sql.types import StringType\n", "\n", "\n", "def extract_content_type(file_name: str) -> str:\n", "    return type_resolver.safe_get_content_type(file_name)\n", "\n", "def extract_file_format(file_name: str) -> str:\n", "    return type_resolver.safe_get_file_format(file_name)\n", "\n", "extract_content_type_udf = udf(extract_content_type, StringType())\n", "extract_file_format_udf = udf(extract_file_format, StringType())\n", "\n", "TDS_POOLS = [\"g3vprdaq\", \"clrecompute\", \"g3closedloop\", \"pacetestpace\", \"g5cariadebdl01\"]\n", "BIG_FOUR_TYPES = [\"image/png\", \"application/pcd\", \"image/png;thumbnail=true\", \"image/jpg;thumbnail=true\"]\n", "\n", "df = (spark.read.table(\"silver.tds.file_entries\")\n", ".filter(col(\"tds_pool\").isin(TDS_POOLS))\n", ".filter(~col(\"content_type\").isin(BIG_FOUR_TYPES))\n", ".sample(0.001) # approx. 4,3 mio\n", ".select(\"created_at\", \"file_hash\", \"file_name\", \"content_type\", \"file_extension\")\n", ".withColumn(\"derived_content_type\", extract_content_type_udf(col(\"file_name\")))\n", "#.withColumn(\"derived_file_format\", extract_file_format_udf(col(\"file_name\")))\n", ".withColumn(\"explicit_content_type\", col(\"derived_content_type\").isin(known_content_types))\n", ")\n", "#df.write.format(\"delta\").mode(\"overwrite\").saveAsTable(\"dd_unicorn_sbx.data_quality.content_type_sample\")\n", "df.count()"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "bea18dda-20ba-4c1b-a8ca-59460135c69a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["%sql\n", "select * from silver.tds.file_entries where tds_pool in (\"g3vprdaq\", \"clrecompute\", \"g3closedloop\", \"pacetestpace\", \"g5cariadebdl01\") and \n", "content_type in (\"image/png;thumbnail=true\") and file_name not like \"%_thumbnail_%.png\";"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "5aaa495b-81fb-47d8-acd2-c01c2dda6881", "showTitle": false, "title": ""}}, "source": ["### Initial check for all files\n", "**Total Speed (8 Nodes cluster):**\n", "0.1% - 3,5 minutes; 100% - 3500 minutes = 58 hours = 2,43 days\n", "\n", "**Speed excluding images and .pcd files:**\n", "0.1% - 10 seconds; 100% - 2,7 hours\n", "\n", "**Checking images and .pcd files with SQL query: 15 seconds * 4** = 1 minute"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "5f493aee-71a4-4b94-874f-b5946cf8f250", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>num_of_files</th><th>same_content_type</th><th>content_type</th><th>derived_content_type</th></tr></thead><tbody><tr><td>1980658</td><td>true</td><td>image/png</td><td>image/png</td></tr><tr><td>763173</td><td>false</td><td>application/pcd</td><td>application/octet-stream</td></tr><tr><td>741830</td><td>false</td><td>image/png;thumbnail=true</td><td>image/png</td></tr><tr><td>693622</td><td>false</td><td>image/jpg;thumbnail=true</td><td>image/jpeg</td></tr><tr><td>40599</td><td>true</td><td>application/octet-stream</td><td>application/octet-stream</td></tr><tr><td>28767</td><td>true</td><td>video/x-msvideo</td><td>video/x-msvideo</td></tr><tr><td>23857</td><td>false</td><td>video/webm;preview=true</td><td>video/webm</td></tr><tr><td>5825</td><td>true</td><td>application/json</td><td>application/json</td></tr><tr><td>3769</td><td>false</td><td>application/zlib</td><td>application/octet-stream</td></tr><tr><td>2408</td><td>true</td><td>plain/text;split=true</td><td>plain/text;split=true</td></tr><tr><td>2389</td><td>false</td><td>application/csv</td><td>text/csv</td></tr><tr><td>2367</td><td>false</td><td>application/octet-stream</td><td>gps/octet-stream</td></tr><tr><td>2273</td><td>false</td><td>application/json;label_type=olf</td><td>application/json</td></tr><tr><td>780</td><td>false</td><td>application/json;label_type=gaf</td><td>application/json</td></tr><tr><td>602</td><td>false</td><td>application/octet-stream</td><td>velodyne/octet-stream</td></tr><tr><td>522</td><td>false</td><td>application/json;label_type=jlf</td><td>application/json</td></tr><tr><td>460</td><td>false</td><td>image/raw</td><td>application/octet-stream</td></tr><tr><td>327</td><td>false</td><td>application/zlib</td><td>application/json</td></tr><tr><td>84</td><td>false</td><td>text/plain</td><td>plain/text;split=true</td></tr><tr><td>59</td><td>true</td><td>text/plain</td><td>text/plain</td></tr><tr><td>50</td><td>false</td><td>application/x-recbin</td><td>application/octet-stream</td></tr><tr><td>45</td><td>false</td><td>text/plain;charset=UTF-8</td><td>text/plain</td></tr><tr><td>41</td><td>false</td><td>application/octet-stream</td><td>video/x-msvideo</td></tr><tr><td>41</td><td>false</td><td>image/png</td><td>image/jpeg</td></tr><tr><td>36</td><td>true</td><td>plain/text</td><td>plain/text</td></tr><tr><td>30</td><td>true</td><td>application/bytesoup</td><td>application/bytesoup</td></tr><tr><td>29</td><td>true</td><td>application/json;format=Cassandra</td><td>application/json;format=Cassandra</td></tr><tr><td>16</td><td>false</td><td>application/octet-stream</td><td>application/bytesoup</td></tr><tr><td>15</td><td>false</td><td>text/plain;charset=UTF-8</td><td>application/x-hdf;bytesoup=true</td></tr><tr><td>12</td><td>false</td><td>text/plain;charset=UTF-8</td><td>application/bytesoup</td></tr><tr><td>10</td><td>true</td><td>application/x-hdf;bytesoup=true</td><td>application/x-hdf;bytesoup=true</td></tr><tr><td>10</td><td>false</td><td>application/octet-stream</td><td>text/plain</td></tr><tr><td>5</td><td>false</td><td>text/plain;charset=UTF-8</td><td>application/octet-stream</td></tr><tr><td>4</td><td>false</td><td>application/x-hdf</td><td>application/x-hdf;bytesoup=true</td></tr><tr><td>3</td><td>false</td><td>application/bag</td><td>application/octet-stream</td></tr><tr><td>2</td><td>false</td><td>application/octet-stream</td><td>application/json</td></tr><tr><td>1</td><td>false</td><td>application/hdf5</td><td>application/x-hdf;bytesoup=true</td></tr><tr><td>1</td><td>false</td><td>application/csts</td><td>application/octet-stream</td></tr><tr><td>1</td><td>false</td><td>cassandra/scene</td><td>plain/text;split=true</td></tr><tr><td>1</td><td>false</td><td>text/plain</td><td>plain/text</td></tr><tr><td>1</td><td>true</td><td>application/zip</td><td>application/zip</td></tr><tr><td>1</td><td>false</td><td>cassandra/recbin</td><td>application/octet-stream</td></tr><tr><td>1</td><td>false</td><td>application/json</td><td>application/json;format=Cassandra</td></tr><tr><td>1</td><td>false</td><td>application/rosbag</td><td>application/octet-stream</td></tr><tr><td>1</td><td>false</td><td>application/scene</td><td>plain/text;split=true</td></tr><tr><td>1</td><td>false</td><td>application/rosbag</td><td>application/x-xz</td></tr><tr><td>1</td><td>false</td><td>application/mkv</td><td>video/x-matroska</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [[1980658, true, "image/png", "image/png"], [763173, false, "application/pcd", "application/octet-stream"], [741830, false, "image/png;thumbnail=true", "image/png"], [693622, false, "image/jpg;thumbnail=true", "image/jpeg"], [40599, true, "application/octet-stream", "application/octet-stream"], [28767, true, "video/x-msvideo", "video/x-msvideo"], [23857, false, "video/webm;preview=true", "video/webm"], [5825, true, "application/json", "application/json"], [3769, false, "application/zlib", "application/octet-stream"], [2408, true, "plain/text;split=true", "plain/text;split=true"], [2389, false, "application/csv", "text/csv"], [2367, false, "application/octet-stream", "gps/octet-stream"], [2273, false, "application/json;label_type=olf", "application/json"], [780, false, "application/json;label_type=gaf", "application/json"], [602, false, "application/octet-stream", "velodyne/octet-stream"], [522, false, "application/json;label_type=jlf", "application/json"], [460, false, "image/raw", "application/octet-stream"], [327, false, "application/zlib", "application/json"], [84, false, "text/plain", "plain/text;split=true"], [59, true, "text/plain", "text/plain"], [50, false, "application/x-recbin", "application/octet-stream"], [45, false, "text/plain;charset=UTF-8", "text/plain"], [41, false, "application/octet-stream", "video/x-msvideo"], [41, false, "image/png", "image/jpeg"], [36, true, "plain/text", "plain/text"], [30, true, "application/bytesoup", "application/bytesoup"], [29, true, "application/json;format=Cassandra", "application/json;format=Cassandra"], [16, false, "application/octet-stream", "application/bytesoup"], [15, false, "text/plain;charset=UTF-8", "application/x-hdf;bytesoup=true"], [12, false, "text/plain;charset=UTF-8", "application/bytesoup"], [10, true, "application/x-hdf;bytesoup=true", "application/x-hdf;bytesoup=true"], [10, false, "application/octet-stream", "text/plain"], [5, false, "text/plain;charset=UTF-8", "application/octet-stream"], [4, false, "application/x-hdf", "application/x-hdf;bytesoup=true"], [3, false, "application/bag", "application/octet-stream"], [2, false, "application/octet-stream", "application/json"], [1, false, "application/hdf5", "application/x-hdf;bytesoup=true"], [1, false, "application/csts", "application/octet-stream"], [1, false, "cassandra/scene", "plain/text;split=true"], [1, false, "text/plain", "plain/text"], [1, true, "application/zip", "application/zip"], [1, false, "cassandra/recbin", "application/octet-stream"], [1, false, "application/json", "application/json;format=Cassandra"], [1, false, "application/rosbag", "application/octet-stream"], [1, false, "application/scene", "plain/text;split=true"], [1, false, "application/rosbag", "application/x-xz"], [1, false, "application/mkv", "video/x-matroska"]], "datasetInfos": [], "dbfsResultPath": null, "isJsonSchema": true, "metadata": {}, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": null, "xColumns": null, "yColumns": null}, "removedWidgets": [], "schema": [{"metadata": "{}", "name": "num_of_files", "type": "\"long\""}, {"metadata": "{}", "name": "same_content_type", "type": "\"boolean\""}, {"metadata": "{}", "name": "content_type", "type": "\"string\""}, {"metadata": "{}", "name": "derived_content_type", "type": "\"string\""}], "type": "table"}}}], "source": ["%sql\n", "select count(*) as num_of_files, (content_type == derived_content_type) as same_content_type, content_type, derived_content_type\n", "from dd_unicorn_sbx.data_quality.content_type_sample group by content_type, derived_content_type\n", "order by num_of_files desc;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "ad3872f5-4808-443d-a884-8950970ab641", "showTitle": false, "title": ""}}, "source": ["### Data Exploration:\n", "- **image/png | image/png** - 46% of all files; content type is correct.\n", "- **application/pcd | application/octet-stream** - 18% of all files; content type is correct, library should be extended to recognize .pcd files\n", "- **image/png;thumbnail=true | image/png** - 17% of all files; content type is correct, library should be extended to add _;thumbnail=true_ type parameter, based on the file name: ALLIANCE-DC01_INMC5947-D-006_20230512_091543_FC1_f00466_RGB_ContextA_thumbnail_240.png\n", "- **image/jpg;thumbnail=true | image/jpeg** - 16% of all files; content type is correct, library should be extended to add _;thumbnail=true_ type parameter, based on the file name (see above). \n", "  - image/jpeg is the corrent mime type for .jpg files, as listed in [IANA media types](https://www.iana.org/assignments/media-types/media-types.xhtml). \n", "\n", "Four content types above make up 97% of all files.\n", "\n", "- **application/octet-stream | application/octet-stream** - file names like '%_centerRefLidar.bin', '%_imu.bin', '%_rectified.pcd'.\n", "- **video/x-msvideo | video/x-msvideo** - content type is correct.\n", "- **video/webm;preview=true | video/webm** - content type is correct; library should be extended to add _;preview=true_ type parameter. \n", "  - Unclear, how to determine if file contains preview. Currently all .webm files are marked as preview. Example file name: ALLIANCE-DC02_INMC5945-D-001_20231212_102707_TVleft_video_240p.webm\n", "- **application/json | application/json** - content type is correct.\n", "- **application/zlib | application/octet-stream** - content type is correct, library should be extended to recognize .zlib files. Moct files contain _can or _flexray in their name.\n", "- **plain/text;split=true | plain/text;split=true** - content type is correct.\n", "- **application/csv | text/csv** - library recognizes .csv files as text/csv implicitly; we need wither to add .csv file explicitly to the mapping, or change content type (text/csv is a correct content type according to [IANA](https://www.iana.org/assignments/media-types/media-types.xhtml).\n", "- **application/octet-stream | gps/octet-stream** - content type is set incorrectly, must be fixed according to the value determined by the library.\n", "- **application/octet-stream | velodyne/octet-stream** - content type is set incorrectly, must be fixed according to the value determined by the library.\n", "- **application/json;label_type=olf | application/json** - content type is correct, library should be extended to add _;label_type=olf_ type parameter, based on the file name: xc_adda_Pace_Lane_polyline_2D_202307191441_50124_297365.olf.json.\n", "-**application/json;label_type=gaf | application/json** - content type is correct, library should be extended to add _;label_type=gaf_ type parameter, see above.\n", "- **application/json;label_type=jlf | application/json** - content type is correct, library should be extended to add _;label_type=jlf_ type parameter, see above.\n", "- **image/raw | application/octet-stream** - content type is correct; library should be extended to reconize .raw image files coming from Pioneering Fleet: CARIAD-EBDL_IN-M-1239_20230420_175849_FC1_f00008.raw\n", "- **application/zlib | application/json** - wrong content type: seems like it was not changed after unpacking. All files were created between 2024-02-02 and 2024-04-26, one time migration might be sufficient.\n", "- **text/plain | plain/text;split=true** - wrong content type. All files were created between 2023-10-18 and 2023-12-03, one time migration might be sufficient. Total 105037 files like that.\n", "- **text/plain | text/plain** - content type is correct; all .txt files.\n", "- **application/x-recbin | application/octet-stream** - content type is correct; library should be extended to recognize .raw recbin files: PACE-ISP-Tuning-Lab-PACE-ISP-Tuning-Lab-TV_Grau_Xe_400_20230405_162351_00000.raw (might be challenging to quanitfy file naming rule to differentiate from PF images). All files were created from 2023-01-25 to 2023-05-04.\n", "- **text/plain;charset=UTF-8 | text/plain** - content type is correct; .txt files; \n", "  - I don't think library can be adjusted for this case, we need to deal with abcence of charset in the type derived from library.\n", "- **application/octet-stream | video/x-msvideo** - content type is set incorrectly, must be fixed according to the value determined by the library.\n", "- **image/png | image/jpeg** - content type is set incorrectly, must be fixed according to the value determined by the library. Files name like '%_stitched_thumbnail_%.jpg'; I checked one image, it's truly jpg: contains JFIF in the beginning of file bytes.\n", "- **plain/text | plain/text** - content type is derived explicitly by the library, as it's not a standard mime type (correct one is text/plain); .cseq files.\n", "- **application/bytesoup | application/bytesoup** - content type is correct.\n", "- **application/json;format=Cassandra | application/json;format=Cassandra** - content type is correct.\n", "- **application/octet-stream | application/bytesoup** - content type is set incorrectly, must be fixed according to the value determined by the library; .bs files.\n", "- **text/plain;charset=UTF-8 | application/x-hdf;bytesoup=true** - content type is set incorrectly, must be fixed according to the value determined by the library; .hdf5 files.\n", "- **text/plain;charset=UTF-8 | application/bytesoup** - content type is set incorrectly, must be fixed according to the value determined by the library; .bs files.\n", "- **application/x-hdf;bytesoup=true | application/x-hdf;bytesoup=true** - content type is correct.\n", "- **application/octet-stream | text/plain** - content type is set incorrectly, must be fixed according to the value determined by the library; .txt files.\n", "- **text/plain;charset=UTF-8 | application/octet-stream** - content type is (probably) correct, but can also be a JSON based on a couple files I've checked; it's total of 5104 files without extencion, created from 2023-05-24 to 2023-06-26. File name: 45a1ab4a18f91fa4fdc42d896988921b1ddefdc8147dc7eb75d08df35cdc286c_Pace_TLD_bbox_2D_20230124_1a051443_5059_4649_9b80_c413f6dd1600\n", "- **application/x-hdf | application/x-hdf;bytesoup=true** - files seems to be derived from other hdf5 files, so maybe bytesoup=true is justified here; in this case library is wrong.\n", "- **application/bag | application/octet-stream** - content type is correct; library should be extended to recognize .bag files.\n", "- **application/octet-stream | application/json** - content type is set incorrectly, must be fixed according to the value determined by the library.\n", "- **application/hdf5 | application/x-hdf;bytesoup=true** - content type is set incorrectly, must be fixed according to the value determined by the library. Total 182 files.\n", "- **application/csts | application/octet-stream** - content type is correct; not sure if library should be extended, it's total 76 files, all were created between 2023-05-31 and 2023-09-11. Type also can't be found in PACE-INT organization. Maybe it's better to just delete these files.\n", "- **cassandra/scene | plain/text;split=true** - content type is set incorrectly, must be fixed according to the value determined by the library. Total 2668 files.\n", "- **text/plain | plain/text** - ???\n", "- **application/zip | application/zip** - content type is correct.\n", "- **cassandra/recbin | application/octet-stream** - content type is correct; library should be extended (??? or not) to recognize .recbin files. Total 3133 files, created between 2023-02-17 and 2023-07-01.\n", "- **application/json | application/json;format=Cassandra** - content type is set incorrectly, must be fixed according to the value determined by the library; gmdm.json files\n", "- **application/rosbag | application/octet-stream** content type is correct; library should be extended (??? or not) to recognize .recbin files. Total 746 files, created between 2023-03-07 and 2024-04-11.\n", "- **application/scene | plain/text;split=true** - content type is set incorrectly, must be fixed according to the value determined by the library; .scene files\n", "- **application/rosbag | application/x-xz** - 86 files; compressed (.xz) rosbag files, so which content type is correct? TBD\n", "- **application/mkv | video/x-matroska** - 112 .mkv files; content type by library is implicitly derived; application/mkv is not used anywhere in PACE-INT organization. TBD\n"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "environmentMetadata": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 483481557516866, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "Content Type", "widgets": {}}}, "nbformat": 4, "nbformat_minor": 0}