{"cells": [{"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "aa3e25c1-fe99-4505-9b13-ef645f732de1", "showTitle": false, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import explode, col, str_to_map, from_json, when, json_tuple, get_json_object, expr,udf, regexp_replace, lit, date_sub, current_date\n", "from pyspark.sql.types import StructField, ArrayType, StringType, StructType, List, DateType, MapType, BooleanType, TimestampType\n", "import json"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "ab15d4b0-f029-41aa-977f-31eb841d2a6c", "showTitle": false, "title": ""}}, "outputs": [], "source": ["%sql\n", "CREATE TABLE IF NOT EXISTS dd_unicorn_sbx.data_quality.distribution_parameters(file_hash string, created_at timestamp, namespace_present boolean, weather string, road_condition string, road_type string, geography string, road_curvature string, daytime string, traffic_density string) USING DELTA CLUSTER BY (file_hash);"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "62d333ed-82af-4f77-a8f0-ba103de5487f", "showTitle": false, "title": ""}, "jupyter": {"source_hidden": true}}, "outputs": [], "source": ["# %sql\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters SET TBLPROPERTIES (\n", "#    'delta.columnMapping.mode' = 'name',\n", "#    'delta.minReaderVersion' = '2',\n", "#    'delta.minWriterVersion' = '5'\n", "# );\n", "# -- ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters DROP COLUMN created_at;\n", "# -- ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN created_at timestamp;\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN weather string;\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN road_condition string;\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN road_type string;\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN geography string;\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN road_curvature string;\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN daytime string;\n", "# ALTER TABLE dd_unicorn_sbx.data_quality.distribution_parameters ADD COLUMN traffic_density string;"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "eb383ef1-5229-497f-acd4-3867327c7e45", "showTitle": false, "title": ""}}, "source": ["####get all the scenes created in the last n days"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "4d4ca59d-4c69-454d-82a8-74fd2e7419ea", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>time_partition</th><th>tds_pool</th><th>content_type</th><th>file_extension</th><th>created_at</th><th>modified_at</th><th>file_hash</th><th>file_size_bytes</th><th>file_name</th><th>file_location</th><th>file_provider</th><th>file_state</th><th>tds_file_url</th><th>parents</th><th>tags</th><th>sources</th></tr></thead><tbody><tr><td>20230626</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2023-06-26T17:00:19.405Z</td><td>2024-06-28T18:04:53.553Z</td><td>814bd9c03014991fba761ca1aad04251738c6448784f1376f86cd53b2d815ce4</td><td>3427</td><td>ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq00euw.blob.core.windows.net/default/9e5bc759-d11e-41c8-9f0e-48789325ee8a/ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene</td><td>List(0dba82eafcf3157637eacc397b561a9f20426a988864fd36392feac76c7dd6f2)</td><td>List()</td><td>List(List(ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene, https://stg3vprdaq00euw.blob.core.windows.net/default/9e5bc759-d11e-41c8-9f0e-48789325ee8a/ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene, plain/text;split=true, 2023-06-26T17:00:12.354Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20240319</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2024-03-19T00:27:44.956Z</td><td>2024-06-12T19:29:14.549Z</td><td>62ee6ed236ecb483b504a99d84c66fd1875b66a15a1d226a5916af8292597014</td><td>3672</td><td>ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq02euw.blob.core.windows.net/default/03a18d51-0025-451e-b538-a120a2840d7e/ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene</td><td>List(17b780d0ad3da434a6358615eef0adf6d0a45f1b454f1d8b4803ed1c7d29f56f)</td><td>List()</td><td>List(List(ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene, https://stg3vprdaq02euw.blob.core.windows.net/default/03a18d51-0025-451e-b538-a120a2840d7e/ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene, plain/text;split=true, 2024-03-19T00:27:40.199Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20240417</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2024-04-17T12:13:47.423Z</td><td>2024-04-26T04:15:04.711Z</td><td>62d722d418e5dcbd4badd36b5bc2ce0640ee7c278ef11c4398ee0ecfa5e9801f</td><td>3672</td><td>ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq02euw.blob.core.windows.net/default/4c9514a3-68b9-422f-a28d-9f223c6b3948/ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene</td><td>List(48ce419681186439cae67c9cd5122b6930af78efdd52a7ffc8b110a97cb0e2eb)</td><td>List()</td><td>List(List(ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene, https://stg3vprdaq02euw.blob.core.windows.net/default/4c9514a3-68b9-422f-a28d-9f223c6b3948/ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene, plain/text;split=true, 2024-04-17T12:13:42.499Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20240405</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2024-04-05T21:16:29.092Z</td><td>2024-06-12T13:20:03.465Z</td><td>62f7dfbb2c02bb7d1271183d0fd0e59514c3c165e93f037a2a9f2b7dfcf0d1e4</td><td>3672</td><td>ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq01euw.blob.core.windows.net/default/7dbed036-55e5-44f8-97a8-d4b22b055593/ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene</td><td>List(6d327b2e19e5391d154c2c031c864d9457514f0fe44b599fa8917a4e2d0424f8)</td><td>List()</td><td>List(List(ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene, https://stg3vprdaq01euw.blob.core.windows.net/default/7dbed036-55e5-44f8-97a8-d4b22b055593/ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene, plain/text;split=true, 2024-04-05T21:16:24.591Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20240610</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2024-06-10T11:32:31.243Z</td><td>2024-06-14T02:27:19.797Z</td><td>62e4cddefcffd62a847de9352c34f4e7b89384b6626647363951e17895d2caae</td><td>3672</td><td>ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq02euw.blob.core.windows.net/default/009bd239-65dd-4ac1-b526-10dc498c8d0a/ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene</td><td>List(2741760adedd7096f3da9068bfa41044e194ccab1da0279f67e75a80477eb246)</td><td>List()</td><td>List(List(ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene, https://stg3vprdaq02euw.blob.core.windows.net/default/009bd239-65dd-4ac1-b526-10dc498c8d0a/ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene, plain/text;split=true, 2024-06-10T11:32:27.696Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20240522</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2024-05-22T01:28:53.813Z</td><td>2024-06-05T09:07:51.152Z</td><td>62d74dba505373a613687ddff65ba657cf69f59489574199faceda694c23e6a8</td><td>3672</td><td>ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq00euw.blob.core.windows.net/default/3ead6fee-5776-42ee-82a4-ee2ceb51b532/ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene</td><td>List(9443a665605af8d0df8287c670990cb95cbc02fe125e321615f6bfba7d1f13df)</td><td>List()</td><td>List(List(ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene, https://stg3vprdaq00euw.blob.core.windows.net/default/3ead6fee-5776-42ee-82a4-ee2ceb51b532/ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene, plain/text;split=true, 2024-05-22T01:28:50.094Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20230829</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2023-08-29T00:55:25.992Z</td><td>2024-05-09T02:56:50.087Z</td><td>62fba345d5dc365404d44cc55fc72b4efe66ddf9874d3c1eef6d4ff4c6dd4957</td><td>3427</td><td>ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq01euw.blob.core.windows.net/default/ef754158-6ac6-4c81-a5fe-100a4785d405/ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene</td><td>List(6e6052922dfdfe96d13d3b2c38cf25504d07b879379c40f126de072b3b459d6d)</td><td>List()</td><td>List(List(ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene, https://stg3vprdaq01euw.blob.core.windows.net/default/ef754158-6ac6-4c81-a5fe-100a4785d405/ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene, plain/text;split=true, 2023-08-29T00:55:22.446Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20240521</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2024-05-21T14:37:25.458Z</td><td>2024-06-05T09:07:54.671Z</td><td>62e4bf6987d867095fc8a520a222eaadea9a3bdb863f06f4c2c35855ed9de235</td><td>3672</td><td>ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq01euw.blob.core.windows.net/default/bce6e0f4-d157-4b9b-a80e-16898911b76e/ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene</td><td>List(c09db5ee94956a551467c4fced2a7463fca1509ddc02902d8454e88eb9c2dd90)</td><td>List()</td><td>List(List(ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene, https://stg3vprdaq01euw.blob.core.windows.net/default/bce6e0f4-d157-4b9b-a80e-16898911b76e/ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene, plain/text;split=true, 2024-05-21T14:37:21.036Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20231116</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2023-11-16T14:17:03.153Z</td><td>2024-04-19T19:01:22.276Z</td><td>62de1a378e860fe4f3caf4896bd248627d132a56604d63b0c6325fbb6c623707</td><td>3652</td><td>ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq02euw.blob.core.windows.net/default/d7391572-3a53-42a2-9cd7-c80f422d77cc/ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene</td><td>List(355a75282f5637ce41c2eb44a670f7bd216f6654cf906a9ce11d35c3c3a53202)</td><td>List()</td><td>List(List(ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene, https://stg3vprdaq02euw.blob.core.windows.net/default/d7391572-3a53-42a2-9cd7-c80f422d77cc/ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene, plain/text;split=true, 2023-11-16T14:16:59.553Z, azure, westeurope, HTTPS, Active))</td></tr><tr><td>20240515</td><td>g3vprdaq</td><td>plain/text;split=true</td><td>scene</td><td>2024-05-15T23:46:31.76Z</td><td>2024-06-05T09:08:06.517Z</td><td>62ef695e5af7917a032713375bdbb4a8e4f62350b9c0205799168c19fb34b11b</td><td>3672</td><td>ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene</td><td>westeurope</td><td>azure</td><td>ACTIVE</td><td>https://stg3vprdaq01euw.blob.core.windows.net/default/412f1569-0afd-4840-8af2-6e01a734275d/ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene</td><td>List(b838db5a3c3fac55ce9fbfdb77420b29c3179bd220f7a395e89c7756badfe91e)</td><td>List()</td><td>List(List(ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene, https://stg3vprdaq01euw.blob.core.windows.net/default/412f1569-0afd-4840-8af2-6e01a734275d/ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene, plain/text;split=true, 2024-05-15T23:46:27.681Z, azure, westeurope, HTTPS, Active))</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [[20230626, "g3vprdaq", "plain/text;split=true", "scene", "2023-06-26T17:00:19.405Z", "2024-06-28T18:04:53.553Z", "814bd9c03014991fba761ca1aad04251738c6448784f1376f86cd53b2d815ce4", 3427, "ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq00euw.blob.core.windows.net/default/9e5bc759-d11e-41c8-9f0e-48789325ee8a/ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene", ["0dba82eafcf3157637eacc397b561a9f20426a988864fd36392feac76c7dd6f2"], [], [["ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene", "https://stg3vprdaq00euw.blob.core.windows.net/default/9e5bc759-d11e-41c8-9f0e-48789325ee8a/ALLIANCE-DC01_INMC5944-D-002_20230619_104050.scene", "plain/text;split=true", "2023-06-26T17:00:12.354Z", "azure", "westeurope", "HTTPS", "Active"]]], [20240319, "g3vprdaq", "plain/text;split=true", "scene", "2024-03-19T00:27:44.956Z", "2024-06-12T19:29:14.549Z", "62ee6ed236ecb483b504a99d84c66fd1875b66a15a1d226a5916af8292597014", 3672, "ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq02euw.blob.core.windows.net/default/03a18d51-0025-451e-b538-a120a2840d7e/ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene", ["17b780d0ad3da434a6358615eef0adf6d0a45f1b454f1d8b4803ed1c7d29f56f"], [], [["ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene", "https://stg3vprdaq02euw.blob.core.windows.net/default/03a18d51-0025-451e-b538-a120a2840d7e/ALLIANCE-DC02_LBXO215-D-001_20240228_211143.scene", "plain/text;split=true", "2024-03-19T00:27:40.199Z", "azure", "westeurope", "HTTPS", "Active"]]], [20240417, "g3vprdaq", "plain/text;split=true", "scene", "2024-04-17T12:13:47.423Z", "2024-04-26T04:15:04.711Z", "62d722d418e5dcbd4badd36b5bc2ce0640ee7c278ef11c4398ee0ecfa5e9801f", 3672, "ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq02euw.blob.core.windows.net/default/4c9514a3-68b9-422f-a28d-9f223c6b3948/ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene", ["48ce419681186439cae67c9cd5122b6930af78efdd52a7ffc8b110a97cb0e2eb"], [], [["ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene", "https://stg3vprdaq02euw.blob.core.windows.net/default/4c9514a3-68b9-422f-a28d-9f223c6b3948/ALLIANCE-DC02_HNRG724-D-001_20240410_154219.scene", "plain/text;split=true", "2024-04-17T12:13:42.499Z", "azure", "westeurope", "HTTPS", "Active"]]], [20240405, "g3vprdaq", "plain/text;split=true", "scene", "2024-04-05T21:16:29.092Z", "2024-06-12T13:20:03.465Z", "62f7dfbb2c02bb7d1271183d0fd0e59514c3c165e93f037a2a9f2b7dfcf0d1e4", 3672, "ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq01euw.blob.core.windows.net/default/7dbed036-55e5-44f8-97a8-d4b22b055593/ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene", ["6d327b2e19e5391d154c2c031c864d9457514f0fe44b599fa8917a4e2d0424f8"], [], [["ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene", "https://stg3vprdaq01euw.blob.core.windows.net/default/7dbed036-55e5-44f8-97a8-d4b22b055593/ALLIANCE-DC02_BSZA848-D-001_20240325_061912.scene", "plain/text;split=true", "2024-04-05T21:16:24.591Z", "azure", "westeurope", "HTTPS", "Active"]]], [20240610, "g3vprdaq", "plain/text;split=true", "scene", "2024-06-10T11:32:31.243Z", "2024-06-14T02:27:19.797Z", "62e4cddefcffd62a847de9352c34f4e7b89384b6626647363951e17895d2caae", 3672, "ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq02euw.blob.core.windows.net/default/009bd239-65dd-4ac1-b526-10dc498c8d0a/ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene", ["2741760adedd7096f3da9068bfa41044e194ccab1da0279f67e75a80477eb246"], [], [["ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene", "https://stg3vprdaq02euw.blob.core.windows.net/default/009bd239-65dd-4ac1-b526-10dc498c8d0a/ALLIANCE-DC02_LBXO174-D-001_20240524_044545.scene", "plain/text;split=true", "2024-06-10T11:32:27.696Z", "azure", "westeurope", "HTTPS", "Active"]]], [20240522, "g3vprdaq", "plain/text;split=true", "scene", "2024-05-22T01:28:53.813Z", "2024-06-05T09:07:51.152Z", "62d74dba505373a613687ddff65ba657cf69f59489574199faceda694c23e6a8", 3672, "ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq00euw.blob.core.windows.net/default/3ead6fee-5776-42ee-82a4-ee2ceb51b532/ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene", ["9443a665605af8d0df8287c670990cb95cbc02fe125e321615f6bfba7d1f13df"], [], [["ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene", "https://stg3vprdaq00euw.blob.core.windows.net/default/3ead6fee-5776-42ee-82a4-ee2ceb51b532/ALLIANCE-DC02_LBXO174-D-001_20240507_091108.scene", "plain/text;split=true", "2024-05-22T01:28:50.094Z", "azure", "westeurope", "HTTPS", "Active"]]], [20230829, "g3vprdaq", "plain/text;split=true", "scene", "2023-08-29T00:55:25.992Z", "2024-05-09T02:56:50.087Z", "62fba345d5dc365404d44cc55fc72b4efe66ddf9874d3c1eef6d4ff4c6dd4957", 3427, "ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq01euw.blob.core.windows.net/default/ef754158-6ac6-4c81-a5fe-100a4785d405/ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene", ["6e6052922dfdfe96d13d3b2c38cf25504d07b879379c40f126de072b3b459d6d"], [], [["ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene", "https://stg3vprdaq01euw.blob.core.windows.net/default/ef754158-6ac6-4c81-a5fe-100a4785d405/ALLIANCE-DC01_INMC5944-D-004_20230817_172234.scene", "plain/text;split=true", "2023-08-29T00:55:22.446Z", "azure", "westeurope", "HTTPS", "Active"]]], [20240521, "g3vprdaq", "plain/text;split=true", "scene", "2024-05-21T14:37:25.458Z", "2024-06-05T09:07:54.671Z", "62e4bf6987d867095fc8a520a222eaadea9a3bdb863f06f4c2c35855ed9de235", 3672, "ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq01euw.blob.core.windows.net/default/bce6e0f4-d157-4b9b-a80e-16898911b76e/ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene", ["c09db5ee94956a551467c4fced2a7463fca1509ddc02902d8454e88eb9c2dd90"], [], [["ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene", "https://stg3vprdaq01euw.blob.core.windows.net/default/bce6e0f4-d157-4b9b-a80e-16898911b76e/ALLIANCE-DC02_LBXO174-D-001_20240511_135545.scene", "plain/text;split=true", "2024-05-21T14:37:21.036Z", "azure", "westeurope", "HTTPS", "Active"]]], [20231116, "g3vprdaq", "plain/text;split=true", "scene", "2023-11-16T14:17:03.153Z", "2024-04-19T19:01:22.276Z", "62de1a378e860fe4f3caf4896bd248627d132a56604d63b0c6325fbb6c623707", 3652, "ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq02euw.blob.core.windows.net/default/d7391572-3a53-42a2-9cd7-c80f422d77cc/ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene", ["355a75282f5637ce41c2eb44a670f7bd216f6654cf906a9ce11d35c3c3a53202"], [], [["ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene", "https://stg3vprdaq02euw.blob.core.windows.net/default/d7391572-3a53-42a2-9cd7-c80f422d77cc/ALLIANCE-DC01-IMU_LBXR268-D-001_20231111_180508.scene", "plain/text;split=true", "2023-11-16T14:16:59.553Z", "azure", "westeurope", "HTTPS", "Active"]]], [20240515, "g3vprdaq", "plain/text;split=true", "scene", "2024-05-15T23:46:31.76Z", "2024-06-05T09:08:06.517Z", "62ef695e5af7917a032713375bdbb4a8e4f62350b9c0205799168c19fb34b11b", 3672, "ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene", "westeurope", "azure", "ACTIVE", "https://stg3vprdaq01euw.blob.core.windows.net/default/412f1569-0afd-4840-8af2-6e01a734275d/ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene", ["b838db5a3c3fac55ce9fbfdb77420b29c3179bd220f7a395e89c7756badfe91e"], [], [["ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene", "https://stg3vprdaq01euw.blob.core.windows.net/default/412f1569-0afd-4840-8af2-6e01a734275d/ALLIANCE-DC02_LBXO174-D-001_20240503_171545.scene", "plain/text;split=true", "2024-05-15T23:46:27.681Z", "azure", "westeurope", "HTTPS", "Active"]]]], "datasetInfos": [], "dbfsResultPath": null, "isJsonSchema": true, "metadata": {}, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": null, "xColumns": null, "yColumns": null}, "removedWidgets": [], "schema": [{"metadata": "{\"comment\":\"Key for temporal partitioning by year, month and day of entry creation time\"}", "name": "time_partition", "type": "\"integer\""}, {"metadata": "{\"comment\":\"TDS Storage Pool Name of latest active source in TDS\"}", "name": "tds_pool", "type": "\"string\""}, {"metadata": "{\"comment\":\"Media type according to RFC 6838 of latest active source in TDS\"}", "name": "content_type", "type": "\"string\""}, {"metadata": "{\"comment\":\"File extension of latest active source in TDS\"}", "name": "file_extension", "type": "\"string\""}, {"metadata": "{\"comment\":\"Creation timestamp of the TDS file entry\"}", "name": "created_at", "type": "\"timestamp\""}, {"metadata": "{\"comment\":\"Modified timestamp of the TDS file entry\"}", "name": "modified_at", "type": "\"timestamp\""}, {"metadata": "{\"comment\":\"SHA2 256bit of the file entry\"}", "name": "file_hash", "type": "\"string\""}, {"metadata": "{\"comment\":\"Size of the file on BLOB storage in bytes\"}", "name": "file_size_bytes", "type": "\"long\""}, {"metadata": "{\"comment\":\"Filename of the referenced file of latest active source in TDS\"}", "name": "file_name", "type": "\"string\""}, {"metadata": "{\"comment\":\"Location of file, e.g. westeurope, of latest active source in TDS\"}", "name": "file_location", "type": "\"string\""}, {"metadata": "{\"comment\":\"Storage backend provider of file, e.g. Azure, of latest active source in TDS\"}", "name": "file_provider", "type": "\"string\""}, {"metadata": "{\"comment\":\"State of the file in storage backend, i.e. Active or Deleted, of latest active source in TDS\"}", "name": "file_state", "type": "\"string\""}, {"metadata": "{\"comment\":\"Reference to file for downloading of latest active source in TDS\"}", "name": "tds_file_url", "type": "\"string\""}, {"metadata": "{\"comment\":\"List of parent hashes of this entry\"}", "name": "parents", "type": "{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true}"}, {"metadata": "{\"comment\":\"List of tags of this entry\"}", "name": "tags", "type": "{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true}"}, {"metadata": "{\"comment\":\"All sources of this entry independent of state\"}", "name": "sources", "type": "{\"type\":\"array\",\"elementType\":{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"url\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"contentType\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"createdTime\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"provider\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"location\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"type\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"state\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]},\"containsNull\":true}"}], "type": "table"}}}], "source": ["split_df = (\n", "      # spark.read.table(\"unicorn_dev.mdm_flattened.tds_file_entries_v2\")\n", "      spark.read.table(\"silver.tds.file_entries\")\n", "      .where(\"tds_pool IN ('g3vprdaq', 'clrecompute', 'g3closedloop', 'pacetestpace', 'g5cariadebdl01')\")\n", "      .filter(col(\"content_type\") == \"plain/text;split=true\")\n", "      # .filter(col(\"created_at\") > lit(date_sub(current_date(),30)))\n", "      .repartition(\"file_hash\")\n", ")\n", "display(split_df.limit(10))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c70dfe3d-c936-4551-8e67-65299c1a4890", "showTitle": false, "title": ""}}, "source": ["####get records with namespace == data_management_data_distribution"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "29fee786-ead5-4a37-978e-c96a258b1bf9", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>file_hash</th><th>namespace</th><th>customFields</th></tr></thead><tbody></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [], "datasetInfos": [], "dbfsResultPath": null, "isJsonSchema": true, "metadata": {}, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": null, "xColumns": null, "yColumns": null}, "removedWidgets": [], "schema": [{"metadata": "{}", "name": "file_hash", "type": "\"string\""}, {"metadata": "{}", "name": "namespace", "type": "\"string\""}, {"metadata": "{}", "name": "customFields", "type": "\"string\""}], "type": "table"}}}], "source": ["distribution_df = (\n", "      spark.read.table(\"bronze.mdm.mdd_namespaces\")\n", "      .filter(col(\"namespace\") == \"datamanagement-distribution-parameters\")\n", "      # .filter(col(\"createdDate\") > lit(date_sub(current_date(),30)))\n", "      .select(col(\"sha\").alias(\"file_hash\"), \"namespace\",\"customFields\")\n", ")\n", "display(distribution_df.limit(10))"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "0aba68a8-464a-4afb-85c2-c1b2bf47f7a1", "showTitle": false, "title": ""}}, "source": ["#### check how many namespaces attached on the scenes created"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "c7c365a5-1d67-4781-bf42-73fce953df69", "showTitle": false, "title": ""}}, "outputs": [], "source": ["split_result_df = (\n", "    split_df\n", "    .join(distribution_df, split_df[\"file_hash\"] == distribution_df[\"file_hash\"], \"left_outer\")\n", "    .withColumn(\"namespace_present\", distribution_df[\"file_hash\"].isNotNull())\n", "    .withColumn(\"weather\", lit(None))\n", "    .withColumn(\"road_condition\", lit(None))\n", "    .withColumn(\"road_type\", lit(None))\n", "    .withColumn(\"geography\", lit(None))\n", "    .withColumn(\"road_curvature\", lit(None))\n", "    .withColumn(\"daytime\", lit(None))\n", "    .withColumn(\"traffic_density\", lit(None))\n", "    .select(split_df[\"file_hash\"], split_df[\"created_at\"],\"namespace_present\", \"weather\",\"road_condition\",\"road_type\",\"geography\",\"road_curvature\",\"daytime\",\"traffic_density\")\n", ")"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "618b67b2-b659-4334-8915-10409b0445f6", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>file_hash</th><th>created_at</th><th>namespace_present</th><th>weather</th><th>road_condition</th><th>road_type</th><th>geography</th><th>road_curvature</th><th>daytime</th><th>traffic_density</th></tr></thead><tbody><tr><td>260829cf10be311c91c4cbeabf46c62970aeeda1993693d0514660aedc1919d0</td><td>2024-02-10T13:07:34.987Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>2619668fda36935987b478b9e31d6efebe656a1c075b35f86c3f3c46966deb4a</td><td>2024-01-30T10:25:53.678Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>260986346a5a2b34f0a935159e9d6675342a6aa36143368f9caca8867a12937b</td><td>2024-02-07T02:54:48.8Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>cd676ebf52bcbbca8f7a125b21c92a8cedb509c35fd24c8a5c52f883263dd5a9</td><td>2024-02-07T22:28:17.915Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>cd8404e5fb92f86be2a17dafa8059ef9c0ffad2c5907400d78b55ce55ec70804</td><td>2024-01-13T17:09:33.203Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>cd76a6e09b91848499acb54214b4719592d64974fac7d48f5570e963585965a0</td><td>2023-12-26T13:33:13.665Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>cd76a84b705233c2853eee077655d4f94bbdfebd137360fa713d74e064c48ff5</td><td>2024-01-17T22:00:04.935Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>cd82d749e6acbd07019766b37cce78524af9d1c92021da8fd9b91cb1ded5c800</td><td>2023-12-26T23:05:05.624Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>a7c7a2654ad4714b70404fb1cbe6b9b7f17d484ad972a34afea5625a2edd636b</td><td>2024-03-06T20:08:29.095Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr><tr><td>a7d8e285c81ad6a526954edf7d346c1323696d6ea6c279155c5af174ad573720</td><td>2024-03-02T03:51:44.284Z</td><td>false</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td><td>null</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [["260829cf10be311c91c4cbeabf46c62970aeeda1993693d0514660aedc1919d0", "2024-02-10T13:07:34.987Z", false, null, null, null, null, null, null, null], ["2619668fda36935987b478b9e31d6efebe656a1c075b35f86c3f3c46966deb4a", "2024-01-30T10:25:53.678Z", false, null, null, null, null, null, null, null], ["260986346a5a2b34f0a935159e9d6675342a6aa36143368f9caca8867a12937b", "2024-02-07T02:54:48.8Z", false, null, null, null, null, null, null, null], ["cd676ebf52bcbbca8f7a125b21c92a8cedb509c35fd24c8a5c52f883263dd5a9", "2024-02-07T22:28:17.915Z", false, null, null, null, null, null, null, null], ["cd8404e5fb92f86be2a17dafa8059ef9c0ffad2c5907400d78b55ce55ec70804", "2024-01-13T17:09:33.203Z", false, null, null, null, null, null, null, null], ["cd76a6e09b91848499acb54214b4719592d64974fac7d48f5570e963585965a0", "2023-12-26T13:33:13.665Z", false, null, null, null, null, null, null, null], ["cd76a84b705233c2853eee077655d4f94bbdfebd137360fa713d74e064c48ff5", "2024-01-17T22:00:04.935Z", false, null, null, null, null, null, null, null], ["cd82d749e6acbd07019766b37cce78524af9d1c92021da8fd9b91cb1ded5c800", "2023-12-26T23:05:05.624Z", false, null, null, null, null, null, null, null], ["a7c7a2654ad4714b70404fb1cbe6b9b7f17d484ad972a34afea5625a2edd636b", "2024-03-06T20:08:29.095Z", false, null, null, null, null, null, null, null], ["a7d8e285c81ad6a526954edf7d346c1323696d6ea6c279155c5af174ad573720", "2024-03-02T03:51:44.284Z", false, null, null, null, null, null, null, null]], "datasetInfos": [], "dbfsResultPath": null, "isJsonSchema": true, "metadata": {}, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": null, "xColumns": null, "yColumns": null}, "removedWidgets": [], "schema": [{"metadata": "{\"comment\":\"SHA2 256bit of the file entry\"}", "name": "file_hash", "type": "\"string\""}, {"metadata": "{\"comment\":\"Creation timestamp of the TDS file entry\"}", "name": "created_at", "type": "\"timestamp\""}, {"metadata": "{}", "name": "namespace_present", "type": "\"boolean\""}, {"metadata": "{}", "name": "weather", "type": "\"void\""}, {"metadata": "{}", "name": "road_condition", "type": "\"void\""}, {"metadata": "{}", "name": "road_type", "type": "\"void\""}, {"metadata": "{}", "name": "geography", "type": "\"void\""}, {"metadata": "{}", "name": "road_curvature", "type": "\"void\""}, {"metadata": "{}", "name": "daytime", "type": "\"void\""}, {"metadata": "{}", "name": "traffic_density", "type": "\"void\""}], "type": "table"}}}], "source": ["display(split_result_df.limit(10))"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "f3e6af68-2ea7-408d-bd48-45f683b6c712", "showTitle": false, "title": ""}}, "outputs": [], "source": ["split_result_df.select(\"file_hash\",\"created_at\",\"namespace_present\",\"weather\",\"road_condition\",\"road_type\",\"geography\",\"road_curvature\",\"daytime\",\"traffic_density\").write.format(\"delta\").mode(\"overwrite\").saveAsTable(\"dd_unicorn_sbx.data_quality.distribution_parameters\")"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "environmentMetadata": null, "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 3150797565617873, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "Distribution namespace", "widgets": {}}}, "nbformat": 4, "nbformat_minor": 0}