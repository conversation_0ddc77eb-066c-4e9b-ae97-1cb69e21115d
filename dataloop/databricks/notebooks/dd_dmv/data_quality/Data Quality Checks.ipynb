{"cells": [{"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "8773ca20-7686-4974-ba84-d9bbef9fccc4", "showTitle": false, "title": ""}}, "source": ["###Add inferred_type and split data based on that "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "15cd8223-8bd5-42ec-a348-abdd16a05d77", "showTitle": false, "title": ""}}, "outputs": [], "source": ["%sql\n", "USE CATALOG dd_unicorn_sbx;\n", "USE SCHEMA data_quality;"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "d5da4a36-ce7b-47c9-a365-80c9f15ad51b", "showTitle": false, "title": ""}}, "outputs": [], "source": ["from pyspark.sql.functions import col, when, size, collect_list, first\n", "\n", "df = spark.read.table(\"unicorn_dev.mdm_flattened.tds_file_entries_v2\").where(\n", "    \"tds_pool IN ('g3vprdaq', 'clrecompute', 'g3closedloop', 'pacetestpace', 'g5cariadebdl01')\"\n", ")\n", "\n", "# filter criteria\n", "# 1. If file_name is gmdm.json it's a recording\n", "# 2. If content_type is \"plain/text;split=true\" it's a split\n", "# 3. If content_type is \"application/octet-stream\" it's a stream\n", "# 4. For Now Keep others as unknown\n", "df = df.withColumn(\n", "    \"inferred_type\",\n", "    # when(col(\"file_name\") == \"gmdm.json\", \"recording\")\n", "    when(col(\"content_type\") == \"application/json;format=Cassandra\", \"recording\")\n", "    .when(col(\"content_type\") == \"plain/text;split=true\", \"split\")\n", "    .when(\n", "        col(\"content_type\") == \"application/octet-stream\", \"stream\"\n", "    )  # this is allegedly not a correct way to identify a stream\n", "    .otherwise(\"unknown\"),\n", ")\n", "\n", "recording_df = df.filter(col(\"inferred_type\") == \"recording\")\n", "\n", "split_df = df.filter(col(\"inferred_type\") == \"split\")\n", "split_df = split_df.withColumn(\"parent_file_hash\", split_df[\"parents\"].getItem(0))\n", "split_df = split_df.select(\n", "    col(\"tds_pool\").alias(\"split_tds_pool\"),\n", "    col(\"content_type\").alias(\"split_content_type\"),\n", "    col(\"file_hash\").alias(\"split_file_hash\"),\n", "    col(\"file_name\").alias(\"split_file_name\"),\n", "    col(\"parent_file_hash\").alias(\"split_parent_file_hash\"),\n", "    col(\"inferred_type\").alias(\"split_inferred_type\"),\n", ")\n", "# split_df = split_df.select([col(c).alias(f\"split_{c}\") for c in df.columns])\n", "\n", "stream_df = df.filter(col(\"inferred_type\") == \"stream\")\n", "stream_df = stream_df.withColumn(\"parent_file_hash\", stream_df[\"parents\"].getItem(0))\n", "stream_df = stream_df.select(\n", "    col(\"tds_pool\").alias(\"stream_tds_pool\"),\n", "    col(\"content_type\").alias(\"stream_content_type\"),\n", "    col(\"file_hash\").alias(\"stream_file_hash\"),\n", "    col(\"file_name\").alias(\"stream_file_name\"),\n", "    col(\"parent_file_hash\").alias(\"stream_parent_file_hash\"),\n", "    col(\"inferred_type\").alias(\"stream_inferred_type\"),\n", ")\n", "\n", "unknown_df = df.filter(col(\"inferred_type\") == \"unknown\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "2b45f9c3-0db1-4ec7-9524-ed7bb644e251", "showTitle": false, "title": ""}}, "source": ["## Implement Checks for Level 0 (For Recordings)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "62ec3969-8959-4f15-a2d0-4ebf4a4a6a2d", "showTitle": false, "title": ""}}, "outputs": [], "source": ["# Check if the recording has parent\n", "recording_df = recording_df.withColumn(\n", "    \"parent_check_on_recording\",\n", "    when(col(\"parents\").isNull() | (size(col(\"parents\")) == 0), True).otherwise(False),\n", ")\n", "# this check right now will always give True because we use content type to identify recordings\n", "# TODO replace this with a name check (all recordings must be named 'gmdm.json')\n", "recording_df = recording_df.withColumn(\n", "    \"is_content_type_correct\",\n", "    when(col(\"content_type\") == \"application/json;format=Cassandra\", True).otherwise(\n", "        False\n", "    ),\n", ")\n", "\n", "# check if recording has split\n", "join_recording_split_df = recording_df.join(\n", "    split_df, recording_df.file_hash == split_df.split_parent_file_hash, \"left\"\n", ")\n", "distinct_recording_df = join_recording_split_df.groupBy(\"file_hash\").agg(\n", "    collect_list(\"split_file_hash\").alias(\"split_file_hash\"),\n", "    first(\"tds_pool\").alias(\"tds_pool\"),\n", "    first(\"content_type\").alias(\"content_type\"),\n", "    first(\"file_name\").alias(\"file_name\"),\n", "    first(\"parents\").alias(\"parents\"),\n", "    first(\"inferred_type\").alias(\"inferred_type\"),\n", "    first(\"parent_check_on_recording\").alias(\"parent_check_on_recording\"),\n", "    first(\"is_content_type_correct\").alias(\"is_content_type_correct\"),\n", ")\n", "\n", "distinct_recording_df = distinct_recording_df.withColumn(\n", "    \"has_attached_scene\",\n", "    when(\n", "        col(\"split_file_hash\").isNull() | (size(col(\"split_file_hash\")) == 0), False\n", "    ).otherwise(True),\n", ")\n", "table_name = \"level_0_checks\"\n", "distinct_recording_df.write.mode(\"overwrite\").saveAsTable(f\"{table_name}\")\n", "\n", "\n", "# TODO check country code\n", "# TODO check that the information in MDM matches in gmdm.json\n", "# TODO check that videos\n", "\n", "# lvl1: MIME type must be existing mime types - purpose is to enable browser to view the files, https://www.iana.org/assignments/media-types/media-types.xhtml\n", "# lvl2: Encoding: UTF8\n", "\n", "# lvl1 and lvl2 are combined in Content-Type with ;\n", "\n", "# lvl3 (type): drive, recording, split, (video, radar, lidar, gps, can), video-preview, video-raw, label, image-raw, image-anon, image-yuv\n", "# lvl3 should be a dict of enums\n", "\n", "\n", "# Next steps\n", "# Fix Content-Type\n", "#   fix gmdm.json writing in embedded, it's the ground truth, it \n", "#   fix Cassandra workflow (it should not be a replacement for the correct ground truth)\n", "#   backfill old metadata\n", "\n", "# Design and add the lvl3 (dict of enums)\n", "#   make it extensible\n", "#   aim for 80% in the first iteration"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "2b1d14e1-7ed4-45fc-9934-3dd6f869c65e", "showTitle": false, "title": ""}}, "source": ["## Implement Checks for Level 1 (For Splits)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "59467865-c72e-416a-9f79-838294f5481a", "showTitle": false, "title": ""}}, "outputs": [], "source": ["# check the content type of split\n", "# this check as of now will always result in True because we identify splits by content type\n", "split_df = split_df.withColumn(\n", "    \"split_content_type_correct\",\n", "    when(col(\"split_content_type\") == \"plain/text;split=true\", True).otherwise(False),\n", ")\n", "\n", "# check split has parent or not\n", "split_df = split_df.withColumn(\n", "    \"parent_check_on_splits\",\n", "    when(col(\"split_parent_file_hash\").isNull(), False).otherwise(True),\n", ")\n", "\n", "join_split_stream_df = split_df.join(\n", "    stream_df, split_df.split_file_hash == stream_df.stream_parent_file_hash, \"left\"\n", ")\n", "\n", "distinct_split_df = join_split_stream_df.groupBy(\"split_file_hash\").agg(\n", "    collect_list(\"stream_file_hash\").alias(\"stream_file_hash\"),\n", "    first(\"split_tds_pool\").alias(\"split_tds_pool\"),\n", "    first(\"split_content_type\").alias(\"split_content_type\"),\n", "    first(\"split_file_name\").alias(\"split_file_name\"),\n", "    first(\"split_parent_file_hash\").alias(\"split_parent_file_hash\"),\n", "    first(\"split_inferred_type\").alias(\"split_inferred_type\"),\n", "    first(\"split_content_type_correct\").alias(\"split_content_type_correct\"),\n", "    first(\"parent_check_on_splits\").alias(\"parent_check_on_splits\"),\n", ")\n", "\n", "\n", "distinct_split_df = distinct_split_df.withColumn(\n", "    \"split_has_children\",\n", "    when(\n", "        col(\"stream_file_hash\").isNull() | (size(col(\"stream_file_hash\")) == 0), False\n", "    ).otherwise(True),\n", ")\n", "table_name = \"level_1_checks\"\n", "distinct_split_df.write.mode(\"overwrite\").saveAsTable(f\"{table_name}\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "09d5a233-4340-4c8c-b734-455ba9e3cce6", "showTitle": false, "title": ""}}, "source": ["## Implement Checks for Level 2 (For Streams) "]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "73860e7a-4aec-4fb3-9c38-92aeeb8d24ec", "showTitle": false, "title": ""}}, "outputs": [], "source": ["stream_df = stream_df.withColumn(\n", "    \"stream_content_type_correct\",\n", "    when(\n", "        (\n", "            col(\"stream_file_name\").endswith(\".hdf5\")\n", "            & (col(\"stream_content_type\") == \"application/x-hdf;bytesoup=true\")\n", "        )\n", "        | (\n", "            col(\"stream_file_name\").endswith(\".bs\")\n", "            & (col(\"stream_content_type\") == \"application/bytesoup\")\n", "        )\n", "        | (col(\"stream_content_type\") == \"application/octet-stream\"),\n", "        True,\n", "    ).otherwise(False),\n", ")\n", "\n", "\n", "stream_df = stream_df.withColumn(\n", "    \"stream_file_extension_check\",\n", "    when(col(\"stream_file_name\").rlike(\"\\\\.(avi|bin|recbin|hdf5|bs)$\"), True).otherwise(\n", "        False\n", "    ),\n", ")\n", "\n", "join_stream_split_df = stream_df.join(\n", "    split_df, split_df.split_file_hash == stream_df.stream_parent_file_hash, \"left\"\n", ")\n", "\n", "distinct_stream_df = join_stream_split_df.groupBy(\"stream_file_hash\").agg(\n", "    collect_list(\"split_file_hash\").alias(\"split_file_hash\"),\n", "    first(\"stream_tds_pool\").alias(\"stream_tds_pool\"),\n", "    first(\"stream_content_type\").alias(\"stream_content_type\"),\n", "    first(\"stream_file_name\").alias(\"stream_file_name\"),\n", "    first(\"split_parent_file_hash\").alias(\"split_parent_file_hash\"),\n", "    first(\"stream_inferred_type\").alias(\"stream_inferred_type\"),\n", "    first(\"stream_content_type_correct\").alias(\"stream_content_type_correct\"),\n", "    first(\"stream_file_extension_check\").alias(\"stream_file_extension_check\"),\n", ")\n", "\n", "\n", "distinct_stream_df = distinct_stream_df.withColumn(\n", "    \"stream_has_parent\",\n", "    when(\n", "        col(\"split_file_hash\").isNull() | (size(col(\"split_file_hash\")) == 0), False\n", "    ).otherwise(True),\n", ")\n", "table_name = \"level_2_checks\"\n", "distinct_stream_df.write.mode(\"overwrite\").saveAsTable(f\"{table_name}\")"]}, {"cell_type": "markdown", "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {}, "inputWidgets": {}, "nuid": "7ca75e65-2a28-4270-a2b0-e0e2f2e973b7", "showTitle": false, "title": ""}}, "source": ["## KPI Calculation\n", "\n", "95% of all entries in data management system follow the ADA ontology  \n", "Goal: Good Entries/All Entries > 95%\n", "\n", "Analysis doc: https://pace-project.atlassian.net/wiki/spaces/DDD/pages/630817634/Structural+checks+for+data+metadata"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "b31d0146-8e96-441b-86d2-1efd5eabef86", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Total all_entries count: 38842488\nTotal good_entries count :  36988916\nKPI Calculation : 95.22797818718513\n"]}], "source": ["# get all the entries which we have identified as recordings,splits,streams\n", "\n", "recordings = spark.sql(\n", "    \"select count(*) from dd_unicorn_sbx.data_quality.level_0_checks\"\n", ").collect()[0][0]\n", "splits = spark.sql(\n", "    \"select count(*) from dd_unicorn_sbx.data_quality.level_1_checks\"\n", ").collect()[0][0]\n", "streams = spark.sql(\n", "    \"select count(*) from dd_unicorn_sbx.data_quality.level_2_checks\"\n", ").collect()[0][0]\n", "\n", "all_entries = recordings + splits + streams\n", "print(\"Total all_entries count:\", all_entries)\n", "\n", "\n", "# get all the good entries\n", "\n", "good_recordings = spark.sql(\n", "    \"select count(*) from dd_unicorn_sbx.data_quality.level_0_checks where parent_check_on_recording is true and is_content_type_correct is true and has_attached_scene is true\"\n", ").collect()[0][0]\n", "\n", "good_splits = spark.sql(\n", "    \"select count(*) from dd_unicorn_sbx.data_quality.level_1_checks where split_content_type_correct is true and parent_check_on_splits is true and split_has_children is true\"\n", ").collect()[0][0]\n", "\n", "good_streams = spark.sql(\n", "    \"select count(*) from dd_unicorn_sbx.data_quality.level_2_checks where stream_content_type_correct is true and stream_file_extension_check is true and stream_has_parent is true\"\n", ").collect()[0][0]\n", "\n", "good_entries = good_recordings + good_splits + good_streams\n", "\n", "print(\"Total good_entries count : \", good_entries)\n", "\n", "goal = (good_entries / all_entries) * 100\n", "\n", "print(\"KPI Calculation :\", goal)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2cb76286-c8b3-4146-8065-24345b65f91d", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>(scalarsubquery() / scalarsubquery())</th></tr></thead><tbody><tr><td>0.9878349498602663</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [[0.9878349498602663]], "datasetInfos": [{"name": "_sqldf", "schema": {"fields": [{"metadata": {"__autoGeneratedAlias": "true"}, "name": "(scalarsubquery() / scalarsubquery())", "nullable": true, "type": "double"}], "type": "struct"}, "tableIdentifier": null, "typeStr": "pyspark.sql.connect.dataframe.DataFrame"}], "dbfsResultPath": null, "isJsonSchema": true, "metadata": {"dataframeName": "_sqldf", "executionCount": 1}, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": null, "xColumns": null, "yColumns": null}, "removedWidgets": [], "schema": [{"metadata": "{\"__autoGenerated<PERSON><PERSON><PERSON>\":\"true\"}", "name": "(scalarsubquery() / scalarsubquery())", "type": "\"double\""}], "type": "table"}}}], "source": ["%sql\n", "-- only recordings\n", "WITH good_recordings AS (\n", "  select * from dd_unicorn_sbx.data_quality.level_0_checks\n", "  where parent_check_on_recording is true\n", "    and is_content_type_correct is true\n", "    and has_attached_scene is true\n", ")\n", "SELECT \n", "  (SELECT COUNT(*) FROM good_recordings)\n", "  /\n", "  (SELECT COUNT(*) FROM dd_unicorn_sbx.data_quality.level_0_checks)"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "2d23f1a2-d451-4162-aa3d-800061cce245", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>(scalarsubquery() / scalarsubquery())</th></tr></thead><tbody><tr><td>0.9953326340989301</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [[0.9953326340989301]], "datasetInfos": [{"name": "_sqldf", "schema": {"fields": [{"metadata": {"__autoGeneratedAlias": "true"}, "name": "(scalarsubquery() / scalarsubquery())", "nullable": true, "type": "double"}], "type": "struct"}, "tableIdentifier": null, "typeStr": "pyspark.sql.connect.dataframe.DataFrame"}], "dbfsResultPath": null, "isJsonSchema": true, "metadata": {"dataframeName": "_sqldf", "executionCount": 4}, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": null, "xColumns": null, "yColumns": null}, "removedWidgets": [], "schema": [{"metadata": "{\"__autoGenerated<PERSON><PERSON><PERSON>\":\"true\"}", "name": "(scalarsubquery() / scalarsubquery())", "type": "\"double\""}], "type": "table"}}}], "source": ["%sql\n", "-- only splits\n", "WITH good_splits AS (\n", "  select * from dd_unicorn_sbx.data_quality.level_1_checks\n", "  where\n", "    split_content_type_correct is true -- this is currently always true becase we identify splits by content type\n", "    and parent_check_on_splits is true\n", "    and split_has_children is true\n", ")\n", "SELECT \n", "  (SELECT COUNT(*) FROM good_splits)\n", "  /\n", "  (SELECT COUNT(*) FROM dd_unicorn_sbx.data_quality.level_1_checks)\n", "\n", "-- every direct descendant of gmdm.json must inherit all the metadata from it\n", "-- OR\n", "-- every known direct descendant gmdm.json automatically gets metadata from the parent"]}, {"cell_type": "code", "execution_count": 0, "metadata": {"application/vnd.databricks.v1+cell": {"cellMetadata": {"byteLimit": 2048000, "implicitDf": true, "rowLimit": 10000}, "inputWidgets": {}, "nuid": "8b070c74-b806-4f72-bc23-7964482ccd5f", "showTitle": false, "title": ""}}, "outputs": [{"output_type": "display_data", "data": {"text/html": ["<style scoped>\n", "  .table-result-container {\n", "    max-height: 300px;\n", "    overflow: auto;\n", "  }\n", "  table, th, td {\n", "    border: 1px solid black;\n", "    border-collapse: collapse;\n", "  }\n", "  th, td {\n", "    padding: 5px;\n", "  }\n", "  th {\n", "    text-align: left;\n", "  }\n", "</style><div class='table-result-container'><table class='table-result'><thead style='background-color: white'><tr><th>(scalarsubquery() / scalarsubquery())</th></tr></thead><tbody><tr><td>0.9498100398824585</td></tr></tbody></table></div>"]}, "metadata": {"application/vnd.databricks.v1+output": {"addedWidgets": {}, "aggData": [], "aggError": "", "aggOverflow": false, "aggSchema": [], "aggSeriesLimitReached": false, "aggType": "", "arguments": {}, "columnCustomDisplayInfos": {}, "data": [[0.9498100398824585]], "datasetInfos": [{"name": "_sqldf", "schema": {"fields": [{"metadata": {"__autoGeneratedAlias": "true"}, "name": "(scalarsubquery() / scalarsubquery())", "nullable": true, "type": "double"}], "type": "struct"}, "tableIdentifier": null, "typeStr": "pyspark.sql.connect.dataframe.DataFrame"}], "dbfsResultPath": null, "isJsonSchema": true, "metadata": {"dataframeName": "_sqldf", "executionCount": 38}, "overflow": false, "plotOptions": {"customPlotOptions": {}, "displayType": "table", "pivotAggregation": null, "pivotColumns": null, "xColumns": null, "yColumns": null}, "removedWidgets": [], "schema": [{"metadata": "{\"__autoGenerated<PERSON><PERSON><PERSON>\":\"true\"}", "name": "(scalarsubquery() / scalarsubquery())", "type": "\"double\""}], "type": "table"}}}], "source": ["%sql\n", "-- only streams\n", "WITH good_streams AS (\n", "  select * from dd_unicorn_sbx.data_quality.level_2_checks\n", "   where stream_content_type_correct is true\n", "     and stream_file_extension_check is true\n", "     and stream_has_parent is true\n", ")\n", "SELECT \n", "  (SELECT COUNT(*) FROM good_streams)\n", "  /\n", "  (SELECT COUNT(*) FROM dd_unicorn_sbx.data_quality.level_2_checks)"]}], "metadata": {"application/vnd.databricks.v1+notebook": {"dashboards": [], "language": "python", "notebookMetadata": {"mostRecentlyExecutedCommandWithImplicitDF": {"commandId": 954961223862444, "dataframes": ["_sqldf"]}, "pythonIndentUnit": 4}, "notebookName": "[Draft] Data Quality Checks", "widgets": {}}}, "nbformat": 4, "nbformat_minor": 0}