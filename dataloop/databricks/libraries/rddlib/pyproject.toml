[build-system]
requires = ["setuptools~=75.0.0"]
build-backend = "setuptools.build_meta"

# General project metadata
[project]
name = "rddlib"
dynamic = ["version", "readme"]
requires-python = ">=3.10"
dependencies = [
    "dbxlib~=0.4.0",
    "azure-identity~=1.17.0",
    "azure-keyvault-secrets~=4.8.0",
    "msal~=1.30.0",
    "python-dateutil~=2.9.0",
    "click~=8.1.0",
]

# Metadata dynamically loaded during build time
[tool.setuptools.dynamic]
version = { file = "VERSION" }
readme = { file = ["README.md"], content-type = "text/markdown" }

# Optional dependencies that are installed when providing the name
# of the corresponding extras, e.g.:
#   pip install rddlib[dbx,stardog]
[project.optional-dependencies]
dbx = ["dbxlib[dbx]"]
spark = ["dbxlib[spark]"]
stardog = ["pystardog~=0.17.0"]
needs = ["dohq-artifactory~=0.10.0", "rdflib~=7.0.0"]

[project.scripts]
rddcli = "rddlib.cli:main"
rddcli-dbx = "rddlib.cli:main_dbx"

# coverage.py configuration
[tool.coverage.run]
branch = true
command_line = "-m pytest tests/"
source = ["src"]
omit = [
    "src/rddlib/cli/**/*.py", # CLI is not tested
    "__init__.py",
    # Temporary exclude files that are not tested
    "src/rddlib/auth_utils.py",
    "src/rddlib/dbx_utils.py",
    "src/rddlib/delta_table_utils.py",
    "src/rddlib/quality_check.py",
    "src/rddlib/stream_utils.py",
]

[tool.coverage.report]
format = "text"
show_missing = true
