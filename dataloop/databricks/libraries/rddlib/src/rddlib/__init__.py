"""Initialization file for the rddlib.

This file imports various modules and functions for use within the package.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from dbxlib.databricks import DBX_ENVIRONMENT, DBX_WORKSPACE_URL, DatabricksEnvironment, get_dbx_env_catalog
from dbxlib.names import FullSchemaName, FullTableName
from rddlib._logging import setup_databricks_logging
from rddlib.auth_utils import (
    AzureTokenAuth,
    get_databricks_bearer_token,
    get_databricks_pat,
    get_rdd_secret,
    get_stardog_access_token,
)
from rddlib.dbx_utils import DBX_APP_SCHEMA_MAP, get_dbx_sql_warehouse_url
from rddlib.quality_check import (
    datetime_consistency_check,
    deduplicate,
    missing_value_detection,
    numeric_outlier_detection,
    outlier_detection,
    schema_validation,
    text_pattern_check,
    unique_value_validation,
)

__all__ = [
    "AzureTokenAuth",
    "get_databricks_bearer_token",
    "get_databricks_pat",
    "get_rdd_secret",
    "get_stardog_access_token",
    "setup_databricks_logging",
    "datetime_consistency_check",
    "deduplicate",
    "missing_value_detection",
    "numeric_outlier_detection",
    "outlier_detection",
    "schema_validation",
    "text_pattern_check",
    "unique_value_validation",
    "DatabricksEnvironment",
    "DBX_WORKSPACE_URL",
    "DBX_ENVIRONMENT",
    "get_dbx_env_catalog",
    "get_dbx_sql_warehouse_url",
    "DBX_APP_SCHEMA_MAP",
    "FullSchemaName",
    "FullTableName",
]
