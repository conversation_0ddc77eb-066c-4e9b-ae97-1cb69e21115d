"""Module containing the definition of the delta tables command group."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import click
from rddlib import delta_table_utils as dtu


@click.group(help="Commands related to delta tables.")
def delta_table() -> None:
    """Definition of delta_table command group."""
    pass


@delta_table.command(name="drop", help="Drops one or more delta tables if they exist.")
@click.option("-c", "--catalog", type=str, required=True)
@click.option("-s", "--schema", type=str, required=True)
@click.option("-t", "--table", "tables", type=str, multiple=True, required=True)
@click.option("--run_id", type=str, required=False)
def delta_table_drop(catalog: str, schema: str, tables: list[str], run_id: str | None) -> None:
    """Definition of delta_table drop command."""
    for table in tables:
        dtu.drop(f"`{catalog}`.`{schema}`.`{table}`")


if __name__ == "__main__":
    raise SystemError("Call the main CLI instead of this file.")
