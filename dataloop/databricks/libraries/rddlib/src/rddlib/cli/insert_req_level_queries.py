"""This module contains SPARQL queries used for inserting req-levels to needs data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

INSERT_REQ_LEVEL_QUERIES = {
    # Insert edge for level 1 requirements
    "level_1": """
    INSERT {
        ?systemReqLevel1 gold:reqLevel gold:Level1 .
        ?systemReqLevel1 gold:hasFeatureIncrement ?feature .
    }
    WHERE
    {
        ?feature a pace:Feature ;
                pace:id ?feature_increment_id .
        FILTER CONTAINS(?feature_increment_id, "FEATURE_INCREMENT")

        ?systemReqLevel1 pace:belongsTo ?feature ;
            a pace:SystemRequirement.
    }
    """,
    # Insert edge for level 3 requirements
    "level_3": """
    INSERT {
        ?systemReqLevel3 gold:reqLevel gold:Level3 .
        ?systemReqLevel3 gold:hasFeatureIncrement ?feature .
    }
    WHERE
    {
        ?feature a pace:Feature ;
                pace:id ?feature_increment_id .
        FILTER CONTAINS(?feature_increment_id, "FEATURE_INCREMENT")

        ?systemReqLevel3 pace:isDerivedFrom/pace:belongsTo ?feature;
            a pace:SystemRequirement.
    }
    """,
    # Insert edge for level 4 requirements
    "level_4": """
    INSERT {
        ?systemReqLevel4 gold:reqLevel gold:Level4 .
        ?systemReqLevel4 gold:hasFeatureIncrement ?feature .
    }
    WHERE
    {
        ?feature a pace:Feature ;
                pace:id ?feature_increment_id .
        FILTER CONTAINS(?feature_increment_id, "FEATURE_INCREMENT")

        ?systemReqLevel4 pace:isDerivedFrom+/pace:isDerivedFrom/pace:belongsTo ?feature;
            a pace:SystemRequirement.
    }
    """,
    # Insert edge for software requirements
    "level_sw": """
    INSERT {
        ?softwareReq gold:reqLevel gold:LevelSw .
        ?softwareReq gold:hasFeatureIncrement ?feature.
    }
    WHERE
    {
        ?feature a pace:Feature ;
                pace:id ?feature_increment_id .
        FILTER CONTAINS(?feature_increment_id, "FEATURE_INCREMENT")

        ?softwareReq a pace:SoftwareRequirement;
            pace:isDerivedFrom+/pace:belongsTo ?feature;
        .
    }
    """,
    # Add labels
    "label": """
    INSERT {
        ?l1 rdfs:label 'l1'.
        ?l3 rdfs:label 'l3'.
        ?l4 rdfs:label 'l4'.
        ?lsw rdfs:label 'sw'.
    } WHERE {
        BIND(gold:Level1 as ?l1)
        BIND(gold:Level3 as ?l3)
        BIND(gold:Level4 as ?l4)
        BIND(gold:LevelSW as ?lsw)
    }
    """,
}
