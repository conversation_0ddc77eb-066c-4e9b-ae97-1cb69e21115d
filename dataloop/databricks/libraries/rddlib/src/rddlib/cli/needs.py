"""Module containing the definition of the needs command group."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from contextlib import contextmanager
from typing import TYPE_CHECKING, Any, Generator

import click
from rddlib import get_rdd_secret

if TYPE_CHECKING:
    from rddlib.stardog_utils import StardogUtils


SCHEMA_URL = "https://jfrog.ad-alliance.biz/artifactory/shared-generic-dev-local/sphinx_needs_turtle/main/schema.ttl"


@click.group(help="Commands related to needs.json.")
def needs() -> None:
    """Definition of needs command group."""
    pass


@contextmanager
def _setup_art_path(url: str) -> Generator["StardogUtils", Any, None]:
    """Creates an ArtifactoryPath object to be used in commands."""
    from artifactory import ArtifactoryPath

    art_name = get_rdd_secret("alliance-artifactory-username")
    art_key = get_rdd_secret("alliance-artifactory-token")
    return ArtifactoryPath(url, auth=(art_name, art_key))


@needs.command(name="print-mappings", help="Prints the mapping dict generated from a PACE schema.")
@click.option("-u", "--url", type=str, required=False)
def needs_print_mapping(url: str | None) -> None:
    """Definition of stardog drop command."""
    import json

    from rddlib.needs_utils import get_mapping_dict

    art_path = _setup_art_path(url if url is not None else SCHEMA_URL)
    mapping_dict = get_mapping_dict(art_path)
    print(json.dumps(mapping_dict, indent=4))


if __name__ == "__main__":
    raise SystemError("Call the main CLI instead of this file.")
