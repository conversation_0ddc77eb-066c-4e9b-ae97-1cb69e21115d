"""Module containing the definition of the stardog command group."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import TYPE_CHECKING

import click
from rddlib import get_rdd_secret, get_stardog_access_token

if TYPE_CHECKING:
    from rddlib.stardog_utils import StardogUtils


@click.group(help="Commands related to stardog databases.")
def stardog() -> None:
    """Definition of stardog command group."""
    pass


def _setup_sdu() -> "StardogUtils":
    """Creates a StardogUtils object to be used as contextmanager in commands."""
    from rddlib.stardog_utils import StardogUtils

    conn_details = {
        "endpoint": get_rdd_secret("rdd-stardog-url"),
        "auth": get_stardog_access_token(),
    }
    return StardogUtils(conn_details)


@stardog.command(name="drop", help="Drops one or more databases if they exist")
@click.option("-d", "--database", "databases", type=str, multiple=True, required=True)
def drop(databases: list[str]) -> None:
    """Definition of drop command."""
    sdu = _setup_sdu()
    for db in databases:
        sdu.drop_database(db)


@stardog.command(name="delete-edges", help="Delete edges from a stardog databse.")
@click.option("-d", "--database", type=str, required=True)
@click.option("-s", "--source", type=click.Choice(["github_pr", "ado", "jira"]), required=True)
def delete_edges(database: str, source: str) -> None:
    """Definition of delete-edges command."""
    from rddlib.cli.delete_edges_queries import DELETE_EDGES_QUERIES

    sdu = _setup_sdu()
    # Allow missing database
    if not sdu.database_exists(database):
        return  # exit without error

    delete_query = DELETE_EDGES_QUERIES[source]
    sdu.run_query(database, delete_query)


@stardog.command(name="add_req_levels", help="Add requirement levels to legacy graph")
@click.option("-d", "--database", type=str, required=True)
def add_req_levels(database: str) -> None:
    """Definition of add_req_levels command."""
    from rddlib.cli.insert_req_level_queries import INSERT_REQ_LEVEL_QUERIES

    sdu = _setup_sdu()
    # Allow missing database
    if not sdu.database_exists(database):
        return  # exit without error

    insert_query_level_1 = INSERT_REQ_LEVEL_QUERIES["level_1"]
    insert_query_level_3 = INSERT_REQ_LEVEL_QUERIES["level_3"]
    insert_query_level_4 = INSERT_REQ_LEVEL_QUERIES["level_4"]
    insert_query_level_sw = INSERT_REQ_LEVEL_QUERIES["level_sw"]
    insert_query_label = INSERT_REQ_LEVEL_QUERIES["label"]
    sdu.run_query(database, insert_query_level_1)
    sdu.run_query(database, insert_query_level_3)
    sdu.run_query(database, insert_query_level_4)
    sdu.run_query(database, insert_query_level_sw)
    sdu.run_query(database, insert_query_label)


@stardog.command(name="load-from-databricks", help="Load data from databricks into a stardog databse using a mapping.")
@click.option("-d", "--database", type=str, required=True)
@click.option("--mapping", type=str, required=True)
@click.option("--catalog", type=str, required=True)
@click.option("--schema", type=str, required=True)
def load_from_databricks(database: str, mapping: str, catalog: str, schema: str) -> None:
    """Definition of load-from-databricks command."""
    sdu = _setup_sdu()
    sdu.load_from_databricks(database, mapping, catalog, schema)


@stardog.command(name="load-from-databricks", help="Load data from databricks into a stardog databse using a mapping.")
@click.option("-d", "--database", type=str, required=True)
@click.option("--turtle", type=str, required=True)
def load_from_turtle(database: str, turtle: str) -> None:
    """Definition of load-from-databricks command."""
    sdu = _setup_sdu()
    sdu.load_from_turtle(database, turtle)


if __name__ == "__main__":
    raise SystemError("Call the main CLI instead of this file.")
