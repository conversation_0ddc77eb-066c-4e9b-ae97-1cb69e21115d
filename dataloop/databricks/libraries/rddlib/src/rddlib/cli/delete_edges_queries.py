"""This module contains SPARQL queries used for deleting edges in different data sources."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

DELETE_EDGES_QUERIES = {
    # Delete certain attributes from ADO workitems to prevent duplicate edges.
    "ado": """
    DELETE {
            ?workitem ado:hasTitle ?old_title .
            ?workitem ado:hasStatus ?old_status .
            ?workitem ado:hasChangeDate ?old_change_date .
            ?workitem ado:hasTags ?old_tags .
            ?workitem ado:hasSeverity ?old_severity .
            ?workitem ado:hasAreaPath ?old_area_path .
            ?workitem ado:hasIteration ?old_iteration .
            ?workitem ado:hasReason ?old_reason .
            ?workitem ado:needsLink ?old_feature_link .
        }
        WHERE {
            ?workitem a ?workitemType ;
                    ado:hasTitle ?old_title ;
                    ado:hasStatus ?old_status ;
                    ado:hasChangeDate ?old_change_date .

            OPTIONAL { ?workitem ado:hasTags ?old_tags . }
            OPTIONAL { ?workitem ado:hasSeverity ?old_severity . }
            OPTIONAL { ?workitem ado:hasAreaPath ?old_area_path . }
            OPTIONAL { ?workitem ado:hasIteration ?old_iteration . }
            OPTIONAL { ?workitem ado:hasReason ?old_reason . }
            OPTIONAL { ?workitem ado:needsLink ?old_feature_link . }

            FILTER (?workitemType IN (
                ado:UserStory,
                ado:Bug,
                ado:Decision,
                ado:KeyResult,
                ado:Objective,
                ado:TeamEpic,
                ado:Impediment,
                ado:Risk,
                ado:RequestForFeature,
                ado:Milestone
            ))
        }
    """,
    # Delete certain attributes from Github Pull Request to prevent duplicate edges
    "github_pr": """
    DELETE {
        ?pr github:hasTitle ?old_title .
        ?pr github:hasStatus ?old_status .
        ?pr github:updatedAt ?old_updated_date .
        ?pr github:hasLabel ?old_label .
        ?pr github:isDraft ?old_draft_status .
        ?pr github:isLocked ?old_locked_status .
        }
        WHERE {
            ?pr a github:PullRequest ;
                    github:hasTitle ?old_title ;
                    github:hasStatus ?old_status ;
                    github:updatedAt ?old_updated_date .
            OPTIONAL { ?pr github:hasLabel ?old_label . }
            OPTIONAL { ?pr github:isDraft ?old_draft_status . }
            OPTIONAL { ?pr github:isLocked ?old_locked_status . }
    }
    """,
    # Delete certain attributes from Jira issues data to prevent duplicate edges
    "jira": """
    DELETE {
        ?issue a ?issueType .
        ?issue jira:hasSummary ?old_summary .
        ?issue jira:hasStatus ?old_status .
        ?issue jira:hasLabel ?old_label .
        ?issue jira:hasComponent ?old_component .
        ?issue jira:hasFixVersion ?old_fix_version .
        ?issue jira:hasSafetyRelevance ?old_safety_relevance .
        ?issue jira:hasSeverity ?old_severity .
        ?issue jira:hasTeamName ?old_team_name .
        ?issue jira:hasDescription ?old_description .
        ?issue jira:hasProblemType ?old_problem_type .
        ?issue jira:hasBranchName ?old_branch_name .
        ?issue jira:hasIssueType ?old_issue_type .
        ?issue jira:hasResolution ?old_resolution .
    }
    WHERE {
        ?issue a ?issueType .
        OPTIONAL { ?issue jira:hasSummary ?old_summary . }
        OPTIONAL { ?issue jira:hasStatus ?old_status . }
        OPTIONAL { ?issue jira:hasLabel ?old_label . }
        OPTIONAL { ?issue jira:hasComponent ?old_component . }
        OPTIONAL { ?issue jira:hasFixVersion ?old_fix_version . }
        OPTIONAL { ?issue jira:hasSafetyRelevance ?old_safety_relevance . }
        OPTIONAL { ?issue jira:hasSeverity ?old_severity . }
        OPTIONAL { ?issue jira:hasTeamName ?old_team_name . }
        OPTIONAL { ?issue jira:hasDescription ?old_description . }
        OPTIONAL { ?issue jira:hasProblemType ?old_problem_type . }
        OPTIONAL { ?issue jira:hasBranchName ?old_branch_name . }
        OPTIONAL { ?issue jira:hasIssueType ?old_issue_type . }
        OPTIONAL { ?issue jira:hasResolution ?old_resolution . }
        FILTER (?issueType IN (
            jira:Activity,
            jira:Story,
            jira:Task,
            jira:Epic,
            jira:Problem,
            jira:Bugfix,
            jira:Subtask,
            jira:ProjectFeature,
            jira:ChangeRequest,
            jira:OpenItem,
            jira:Risk
        ))
    }
    """,
}
