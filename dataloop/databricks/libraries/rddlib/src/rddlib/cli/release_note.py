"""Module containing the definition of the generate release note command group."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

import click
from rddlib import release_note_utils as rnu

# Configure logging
logger = logging.getLogger(__name__)


@click.group(help="Commands related to release note.")
def release_note() -> None:
    """Definition of release_note command group."""
    pass


@release_note.command(name="generate", help="Generates a release note and upload to artifactory.")
@click.option("--template_name", required=True, help="Template name for the release note.")
@click.option("--config_name", required=True, help="Config file name for the release note.")
@click.option("--external_data_version", required=True, help="External data version (SHA) for connecting to GitHub.")
@click.option("--software_version", required=True, help="Software version (SHA) for connecting to Stardog.")
@click.option("--query_parameters", required=True, help="Query Parameters for Stardog queries.")
def generate_release_note(template_name, config_name, external_data_version, software_version, query_parameters):
    """Main function to trigger release note generation, download PDF, and upload to Artifactory."""
    logger.info("Starting the release note generation process...")

    # Get access token
    token = rnu.get_rng_access_token()

    # Trigger release note API
    sas_url = rnu.trigger_release_note(
        template_name, config_name, external_data_version, software_version, query_parameters, token
    )

    # Download PDF
    pdf_file = rnu.download_pdf(sas_url, template_name)

    # Upload PDF to Artifactory
    rnu.upload_to_artifactory(pdf_file)
    logger.info("Release note process completed successfully!")


if __name__ == "__main__":
    raise SystemError("Call the main CLI instead of this file.")
