#!/usr/bin/python
"""Module containing the definition of the RDD CLI."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

import click
import colorlog
from rddlib.cli.delta_table import delta_table
from rddlib.cli.needs import needs
from rddlib.cli.release_note import release_note
from rddlib.cli.stardog import stardog


@click.group(
    help="This is the command line interface utility of the RDD team.",
    context_settings={"max_content_width": 120},
)
def main() -> None:
    """Definition of main command group."""
    # Configure logging
    colorlog.basicConfig(
        stream=click.get_text_stream("stdout"),
        format="%(log_color)s%(asctime)s | %(levelname)s | %(name)s | %(message)s",
        level=logging.DEBUG,
    )


# Add command groups
main.add_command(delta_table)
main.add_command(needs)
main.add_command(stardog)
main.add_command(release_note)


def main_dbx() -> None:
    """Wrapper method for main command group to be used for Databricks workflows."""
    main(standalone_mode=False)


if __name__ == "__main__":
    main()
