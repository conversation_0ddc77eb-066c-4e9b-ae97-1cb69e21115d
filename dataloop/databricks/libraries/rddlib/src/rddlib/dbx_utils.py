"""Helper module that contains utility functions related to databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from dbxlib.databricks import (
    DBX_ENVIRONMENT,
    DBX_WORKSPACE_URL,
    RUNS_IN_DATABRICKS,
    DatabricksEnvironment,
    get_dbx_env_catalog,
)
from dbxlib.names import FullSchemaName, FullTableName

DBX_APP_SCHEMA_MAP = {
    "release": "ada_release_graph",
    "rng": "rdd_release_graph",
    "ingest": "dataloop_release_graph",
}


def get_dbx_sql_warehouse_url(workspace_url: str | None = DBX_WORKSPACE_URL, name: str | None = "shared") -> str:
    """Retrieve the JDBC URL of the serverless warehouse from Databricks using a service principal.

    Args:
        workspace_url (str): The URL of the Databricks workspace. Defaults to the current workspace.
        name (str | None): An optional name to filter for. Is ignored if no matching warehouse is found.
            Defaults to "shared".

    Returns:
        str: The JDBC URL of the serverless warehouse if successful, otherwise None.
    """
    from rddlib.auth_utils import get_databricks_bearer_token

    # Get the bearer token
    access_token = get_databricks_bearer_token()

    # Use the acquired Azure AD token to initialize the Databricks SDK client
    from databricks.sdk import WorkspaceClient

    databricks_client = WorkspaceClient(host=workspace_url, token=access_token)

    # List data sources to find the serverless warehouse
    data_sources = list(databricks_client.data_sources.list())

    # Filter for the serverless warehouse
    for data_source in data_sources:
        if data_source.name == name:
            warehouse = data_source
            break
    else:
        # Pick first one if name not found
        warehouse = data_sources[0]

    # Get the serverless warehouse details
    warehouse_details = databricks_client.warehouses.get(id=warehouse.warehouse_id)  # type: ignore[arg-type]

    # Extract the JDBC URL
    jdbc_url = warehouse_details.jdbc_url

    return jdbc_url  # type: ignore[return-value]


__all__ = [
    "get_dbx_sql_warehouse_url",
    "get_dbx_env_catalog",
    "RUNS_IN_DATABRICKS",
    "DBX_WORKSPACE_URL",
    "DBX_ENVIRONMENT",
    "DatabricksEnvironment",
    "FullTableName",
    "FullSchemaName",
]
