"""Helper module for delta table operations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

from dbxlib.names import FullSchemaName, FullTableName
from dbxlib.tables import append, drop, merge, merge_or_overwrite, overwrite, table_exists, table_has_schema
from dbxlib.tables import update_table_metadata as _update_table_metadata

logger = logging.getLogger(__name__)


def update_table_metadata(
    table: FullTableName | str,
    description: str,
    responsible_team: str = "Release Driven Development",
    responsible_domain: str = "Data Delivery",
    refresh_interval: str = "P1D",
    additional_tags: dict[str, str] | None = None,
    column_descriptions: dict[str, str] | None = None,
    column_timesystems: dict[str, str] | None = None,
    column_units: dict[str, str] | None = None,
    additional_column_tags: dict[str, dict[str, str]] | None = None,
    force_update: bool = False,
) -> None:
    """Sets the metadata of a table if necessary.

    If force_update is set, the metadata will be updated even if it has not changed.

    Args:
        table (FullTableName | str): The full name of the delta table.
        description (str): The description of the table.
        responsible_team (str): The responsible team. Defaults to "Release Driven Development".
        responsible_domain (str): The responsible domain. Defaults to "Data Delivery".
        refresh_interval (str): The refresh interval in ISO 8601 duration format. Defaults to "P1D".
        additional_tags (dict[str, str], optional): Additional tags to set on the table
            as a mapping of tag name to tag value.
        column_descriptions (dict[str, str], optional): A mapping of column names to their descriptions.
            Defaults to None.
        column_timesystems (dict[str, str], optional): A mapping of timestamp columns to their timesystems.
            Any columns not in the mapping will be assumed to be in UTC. Defaults to None.
        column_units (dict[str, str], optional): A mapping of columns to their units. Defaults to None.
        additional_column_tags (dict[str, dict[str, str]], optional): Additional tags to set on the columns
            as a mapping of column name to a mapping of tag name to tag value. Defaults to None.
        force_update (bool, optional): Whether to update even if no changes were made.
            Defaults to False.
    """
    _update_table_metadata(
        table=table,
        description=description,
        responsible_team=responsible_team,
        responsible_domain=responsible_domain,
        refresh_interval=refresh_interval,
        additional_tags=additional_tags,
        column_descriptions=column_descriptions,
        column_timesystems=column_timesystems,
        column_units=column_units,
        additional_column_tags=additional_column_tags,
        force_update=force_update,
    )


__all__ = [
    "update_table_metadata",
    "merge",
    "overwrite",
    "append",
    "merge_or_overwrite",
    "drop",
    "table_exists",
    "table_has_schema",
    "FullSchemaName",
    "FullTableName",
]
