"""Helper module that contains all sorts of utility functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import os
import sys
from pathlib import Path
from types import FunctionType, MethodType
from typing import Any, Iterator

from pyspark.sql import Column
from pyspark.sql.functions import when


def _get_bundle_dir_databricks() -> Path:
    """Returns the bundle root directory of the currently executed script in Databricks."""
    script_dir = Path.cwd()
    # Look for files folder to find bundle root
    for parent in script_dir.parents:
        if parent.name == "files":
            return parent

    raise FileNotFoundError("No bundle directory found.")


def _get_bundle_dir_local() -> Path:
    """Returns the bundle root directory of the currently executed script on local."""
    # Script arg is at a later position in sys.argv when running in debug mode
    if bool(os.environ.get("DEBUGPY_RUNNING")):
        script_arg = next((arg for arg in sys.orig_argv[::-1] if arg.endswith(".py")))
    else:
        script_arg = sys.argv[0]

    script = Path(script_arg).resolve()
    # Scan parent dirs for databricks.yml to find bundle root
    for parent in script.parents:
        if (parent / "databricks.yml").exists():
            return parent
    else:
        raise FileNotFoundError("No bundle directory found.")


def get_bundle_dir() -> Path:
    """Returns the bundle root directory of the currently executed script."""
    if "DATABRICKS_RUNTIME_VERSION" in os.environ:
        bundle_dir = _get_bundle_dir_databricks()
    else:
        bundle_dir = _get_bundle_dir_local()

    return bundle_dir


def add_bundle_dir_to_path() -> None:
    """Adds the bundle root directory of the currently executed script to the Python path."""
    bundle_dir = get_bundle_dir()
    sys.path.append(str(bundle_dir))


def full_qualname(cls_or_func: type | FunctionType) -> str:
    """Returns the full qualified name of a class or function."""
    return f"{cls_or_func.__module__}.{cls_or_func.__qualname__}"


def method_self_name(method: MethodType) -> tuple[object, str]:
    """Returns the object a method is bound to as well as its name.

    This is required as arguments when patching methods in unittests.
    """
    return (method.__self__, method.__name__)


def batch(iterable: list[Any], batch_size: int = 1) -> Iterator[list[Any]]:
    """Yields batches of fixed size from a given list."""
    length = len(iterable)
    for start_idx in range(0, length, batch_size):
        yield iterable[start_idx : start_idx + batch_size]


def nullif_blank(col: Column) -> Column:
    """Replaces all blank string values in `col` with `null`."""
    return when(col == "", None).otherwise(col)
