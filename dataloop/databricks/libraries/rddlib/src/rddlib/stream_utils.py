"""Helper module for Spark structured streaming operations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import tempfile
from contextlib import contextmanager
from pathlib import Path
from typing import Generator

from databricks.sdk.errors import ResourceDoesNotExist
from pyspark.dbutils import DBUtils
from pyspark.sql import DataFrame, SparkSession

logger = logging.getLogger(__name__)

# Initialize Spark and dbutils
spark = SparkSession.builder.getOrCreate()
dbutils = DBUtils(spark)


def backup_checkpoint(checkpoint_path: str) -> str | None:
    """Backup the current checkpoint to a temporary location using dbutils."""
    backup_path = f"{checkpoint_path}_backup"

    try:
        dbutils.fs.rm(backup_path, recurse=True)
    except Exception:
        pass

    # Copy the entire checkpoint directory to the backup path
    dbutils.fs.cp(checkpoint_path, backup_path, recurse=True)
    return backup_path


def restore_checkpoint(checkpoint_path: str, backup_path: str) -> None:
    """Restore the checkpoint from the backup using dbutils."""
    try:
        dbutils.fs.rm(checkpoint_path, recurse=True)
    except Exception:
        pass

    # Restore from the backup
    dbutils.fs.cp(backup_path, checkpoint_path, recurse=True)


@contextmanager
def checkpoint_backup(checkpoint_path: str) -> Generator[None, None, None]:
    """Context manager to handle checkpoint backup and restore."""
    backup_path = None
    try:
        backup_path = backup_checkpoint(checkpoint_path)
        yield
    except Exception as e:
        # If exception occurs, restore the checkpoint
        if backup_path:
            restore_checkpoint(checkpoint_path, backup_path)
        raise e
    finally:
        # Clean up the backup after successful execution
        if backup_path and dbutils.fs.ls(backup_path):
            dbutils.fs.rm(backup_path, recurse=True)


def stream_updates_to_dataframe(stream_df: DataFrame, checkpoint_path: str) -> DataFrame | None:
    """Stream updates to a standard DataFrame.

    This function writes the updates from a streaming DataFrame to a temporary directory
    and then reads them back into a standard DataFrame.

    Args:
        stream_df: The streaming DataFrame to read updates from.
        checkpoint_path: The path to the checkpoint directory.

    Returns:
        A DataFrame containing the updates, or None if no updates were found.
    """
    with tempfile.TemporaryDirectory() as tmp_dir:
        query = (
            stream_df.writeStream.trigger(availableNow=True)
            .option("checkpointLocation", checkpoint_path)
            .foreachBatch(lambda df, batch_id: df.write.parquet(f"{tmp_dir}/{batch_id}", mode="error"))
            .start()
        )
        query.awaitTermination()

        # Collect all parquet files
        try:
            parquet_files = [Path(file.path) for file in dbutils.fs.ls(tmp_dir)]
            parquet_files.sort(key=lambda file: int(file.stem))
        except ResourceDoesNotExist:
            # Return early if no files found
            logger.warning("Caught a ResourceDoesNotExist")
            return None
        except Exception as e:
            if "java.io.FileNotFoundException" in str(e):
                # Return early if no files found
                logger.warning("Caught a java.io.FileNotFoundException")
                return None
            else:
                # Handle other exceptions or re-raise them if needed
                raise e

        result_df = spark.read.parquet(*[str(file) for file in parquet_files])
        return result_df
