"""Logging utilities for Databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any

from dbxlib._logging import DEFAULT_LOG_TABLE, DEFAULT_LOGGING_CONFIG
from dbxlib._logging import setup_databricks_logging as _setup_databricks_logging
from dbxlib.names import FullSchemaName
from pyspark.sql import SparkSession


def setup_databricks_logging(  # pragma: no cover
    schema_full: FullSchemaName,
    context: str,
    spark: SparkSession | None = None,
    table: str = DEFAULT_LOG_TABLE,
    run_id: str | None = None,
    log_level: str = "INFO",
    max_queue_size: int = 5,
    basic_config: dict[str, Any] = DEFAULT_LOGGING_CONFIG,
    enabled_loggers: list[str] | None = None,
) -> None:
    """Set up logging to Databricks tables.

    Args:
        spark (SparkSession): The Spark session.
        schema_full (FullSchemaName): The full schema name of the table.
        context (str): The context of the log message.
        table (str | None, optional): The name of the table. Defaults to DEFAULT_LOG_TABLE.
        run_id (str | None, optional): The run ID of the executing job. Defaults to None.
        log_level (str, optional): The log level to use. Defaults to "INFO".
        max_queue_size (int, optional): The maximum size of the log queue. Defaults to 5.
        basic_config (dict[str, Any], optional): The basic logging configuration.
            This should be a dictionary that can be passed to logging.config.dictConfig.
            It must include a "databricks" handler that has the class DatabricksHandler
            and a "databricks_context" filter that has the class DatabricksContextFilter.
            The object is deep copied and then modified to include the custom configuration.
            Defaults to DEFAULT_LOGGING_CONFIG.
        enabled_loggers (list[str] | None, optional): A list of logger names to enable.
            The rddlib logger is added automatically. Defaults to None.
    """
    if enabled_loggers is None:
        enabled_loggers = []
    # Add rddlib to the enabled loggers
    enabled_loggers.append("rddlib")

    _setup_databricks_logging(
        schema_full,
        context,
        spark=spark,
        table=table,
        run_id=run_id,
        log_level=log_level,
        max_queue_size=max_queue_size,
        basic_config=basic_config,
        enabled_loggers=enabled_loggers,
    )
