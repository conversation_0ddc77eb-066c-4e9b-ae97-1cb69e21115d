"""Helper module for generating release note."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import time
from pathlib import Path

import requests
from azure.identity import ClientSecretCredential
from dbxlib import get_dbx_env_catalog
from pyspark.sql import SparkSession
from rddlib.auth_utils import get_rdd_secret

# Set up logging
logger = logging.getLogger(__name__)

ARTIFACTORY_BASE_URL = "https://jfrog.ad-alliance.biz/shared-generic-prod-local/rdd/ingest/release_notes"
DBX_SCHEMA = "ada_release_graph"
TABLE_NAME = "ingest_commits"


def get_rng_access_token() -> str:  # pragma: no cover
    """Authenticate with Azure AD and obtain an access token.

    Returns:
        str: Access token.
    """
    from pyspark.dbutils import DBUtils

    dbutils = DBUtils(SparkSession.builder.getOrCreate())
    client_id = dbutils.secrets.get(scope="rdd", key="job-spn-client-id")
    client_secret = dbutils.secrets.get(scope="rdd", key="job-spn-client-secret")
    tenant_id = get_rdd_secret("azure-tenant-id")

    credential = ClientSecretCredential(tenant_id=tenant_id, client_id=client_id, client_secret=client_secret)
    scope = get_rdd_secret("rdd-rng-api-scope")
    token = credential.get_token(scope).token
    logger.info("Successfully obtained Azure access token.")
    return token


def trigger_release_note(
    template_name: str,
    config_name: str,
    external_data_version: str,
    software_version: str,
    query_parameters: str,
    token: str,
) -> str:
    """Trigger the RNG API to generate a release note.

    Args:
        template_name (str): Name of the release note template.
        config_name (str): Config file name for release note.
        external_data_version (str): External data version (SHA) for GitHub.
        software_version (str): Software version (SHA) for Stardog.
        query_parameters (str): Query parameters for Stardog queries.
        token (str): Bearer token for authentication.

    Returns:
        str: SAS URL.
    """
    headers = {"Authorization": f"Bearer {token}"}

    # Retrieve latest commit SHA if either version is None
    latest_commit_sha = get_latest_commit_sha()
    logger.info(f"The latest commit SHA retrieved from the ingest_commits table is: {latest_commit_sha}")
    external_data_version = external_data_version or latest_commit_sha
    software_version = software_version or latest_commit_sha

    params = {
        "ExternalDataVersion": external_data_version,
        "SoftwareVersion": software_version,
        "QueryParameters": query_parameters,
        "Draft": str(False),
    }

    api_base_url = get_rdd_secret("rdd-rng-base-url")
    api_endpoint = f"{api_base_url}/api/generate-release-note?Config={config_name}&TemplateID={template_name}"

    response = requests.get(api_endpoint, headers=headers, params=params)
    response.raise_for_status()

    sas_url = response.json()["sasUrl"]  # KeyError will be raised if 'sasUrl' is missing
    logger.info("Successfully received SAS URL for the release note PDF.")
    return sas_url


def download_pdf(sas_url: str, template_name: str) -> Path:
    """Download the release note PDF using the SAS URL.

    Args:
        sas_url (str): SAS URL to fetch the release note PDF.
        template_name (str): Name of the release note template.

    Returns:
        str: Path to the downloaded PDF file.
    """
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    output_file = Path.cwd() / f"release_note_{template_name}_{timestamp}.pdf"

    response = requests.get(sas_url)
    response.raise_for_status()

    with output_file.open("wb") as fp:
        fp.write(response.content)

    logger.info(f"PDF downloaded successfully: {output_file}")
    return output_file


def upload_to_artifactory(file: Path) -> None:
    """Upload the generated PDF to Artifactory with the correct filename.

    Args:
        file (Path): Path to the generated release note PDF.
    """
    art_name = get_rdd_secret("alliance-artifactory-username")
    art_key = get_rdd_secret("alliance-artifactory-token")

    artifactory_url = f"{ARTIFACTORY_BASE_URL}/{file.name}"

    with file.open("rb") as fp:
        response = requests.put(
            artifactory_url, auth=(art_name, art_key), data=fp, headers={"Content-Type": "application/pdf"}
        )
        response.raise_for_status()

    logger.info(f"File uploaded successfully to Artifactory: {artifactory_url}")


def get_latest_commit_sha() -> str:
    """Retrieve the latest commit SHA from the ingest_commits table.

    Returns:
        str: Latest commit SHA.
    """
    gold_dbx_catalog = get_dbx_env_catalog("gold")
    ingest_commits_table = f"{gold_dbx_catalog}.{DBX_SCHEMA}.{TABLE_NAME}"
    query = f"SELECT commit_sha FROM {ingest_commits_table} ORDER BY processed_at DESC LIMIT 1"
    result = SparkSession.builder.getOrCreate().sql(query).collect()
    return result[0]["commit_sha"]
