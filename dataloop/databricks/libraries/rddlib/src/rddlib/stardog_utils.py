"""Helper module for stardog operations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging

# standard imports
from pathlib import Path
from typing import Any

from rddlib.auth_utils import get_databricks_pat
from rddlib.dbx_utils import get_dbx_sql_warehouse_url
from stardog import Connection
from stardog.admin import Admin
from stardog.content import File, MappingRaw, Raw
from stardog.exceptions import StardogException

logger = logging.getLogger(__name__)


class StardogUtils:
    """Class for Stardog database operations."""

    connection_details: Connection

    def __init__(self, con_details: dict[str, Any]) -> None:
        """Initialize DeleteDeltaTable class."""
        self.connection_details = con_details

    def create_database(self, db_name: str, ignore_exists: bool = True) -> None:
        """Create a database if it does not exist."""
        with Admin(**self.connection_details) as admin:
            if not self.database_exists(db_name):
                admin.new_database(db_name)
                logger.info(f"Database {db_name} has been created.")
            else:
                if ignore_exists:
                    logger.info(f"Database {db_name} already exists.")
                else:
                    raise StardogException(f"Could not create database {db_name} as it already exists.")

    def drop_database(self, db_name: str, ignore_missing: bool = True) -> None:
        """Drop the database if it exists."""
        with Admin(**self.connection_details) as admin:
            if self.database_exists(db_name):
                logger.info(f"Database {db_name} exists. Dropping database.")
                admin.database(db_name).drop()
                logger.info(f"Database {db_name} has been dropped.")
            else:
                if ignore_missing:
                    logger.info(f"Database {db_name} does not exist.")
                else:
                    raise StardogException(f"Could not drop database {db_name} as it does not exist.")

    def database_exists(self, db_name: str) -> bool:
        """Check if the database exists."""
        with Admin(**self.connection_details) as admin:
            return any(db.name == db_name for db in admin.databases())

    def run_query(self, db_name: str, query: str) -> None:
        """Execute a SPARQL query against a database."""
        with Connection(db_name, **self.connection_details) as conn:
            conn.begin()
            conn.update(query)
            conn.commit()

    def load_from_databricks(
        self,
        db_name: str,
        mapping: str,
        dbx_catalog: str,
        dbx_schema: str,
        dbx_jdbc_url: str | None = None,
        dbx_jdbc_user: str | None = None,
        dbx_jdbc_password: str | None = None,
    ) -> None:  # pragma: no cover
        """Load data from databricks into a database using a mapping file."""
        if dbx_jdbc_url is None:
            dbx_jdbc_url = get_dbx_sql_warehouse_url().replace("jdbc:spark", "jdbc:databricks")
        if (dbx_jdbc_user is None) or (dbx_jdbc_password is None):
            from pyspark.dbutils import DBUtils
            from pyspark.sql import SparkSession

            dbutils = DBUtils(SparkSession.builder.getOrCreate())
            dbx_jdbc_user = dbutils.secrets.get(scope="rdd", key="stardog-jdbc-spn-client-id")
            dbx_jdbc_password = get_databricks_pat()

        # Collect databricks connection options
        dbx_options = {
            "jdbc.url": dbx_jdbc_url,
            "jdbc.username": dbx_jdbc_user,
            "jdbc.password": dbx_jdbc_password,
            "query.translation": "DEFAULT",
            "jdbc.driver": "com.databricks.client.jdbc.Driver",
            "sql.catalog": dbx_catalog,
            "sql.schemas": f"{dbx_catalog}.{dbx_schema}",
        }

        with Admin(**self.connection_details) as admin:
            admin.materialize_virtual_graph(db=db_name, mappings=MappingRaw(mapping), options=dbx_options)
            # self.log.materialized(
            #     f'{{"schema": "{src_schema}",' + f'"target_db": "{self.db}",' + f'"mapping_file": "{mapping_file}"}}'
            # )

    def load_from_turtle(self, db_name: str, turtle: str) -> None:
        """Load the contents of a turtle file into a database."""
        with Connection(db_name, **self.connection_details) as conn:
            conn.begin()
            conn.add(Raw(turtle, content_type="text/turtle"))
            conn.commit()

    def load_namespaces(self, db_name: str, turtle_file: Path) -> None:
        """Load namespace prefixes from a turtle file in a database."""
        with Admin(**self.connection_details) as admin:
            admin.database(db_name).import_namespaces(File(str(turtle_file), content_type="text/turtle"))
