"""Helper module to access rdd secrets from key vault and getting stardog token."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any

from azure.identity import ClientSecretCredential
from azure.keyvault.secrets import SecretClient
from dbxlib.databricks import DBX_WORKSPACE_URL
from msal import ConfidentialClientApplication
from pyspark.sql import SparkSession
from requests.auth import AuthBase

# Constants
DATABRICKS_PAT_VALIDITY = 3000  # Validity in seconds


class AzureTokenAuth(AuthBase):
    """Attaches Azure Token to the given Request object.

    This class provides authentication by attaching an Azure token to the provided Request object.
    """

    credential: Any | None
    scope: str | None
    token: str | None

    def __init__(
        self,
        credential: Any | None = None,
        scope: str | None = None,
        token: str | None = None,
    ):
        """Initialize the AzureTokenAuth class with credential and scope.

        Args:
            credential: The Azure credential object.
            scope: The scope for the token.
            token: The Azure token string.
        """
        self.credential = credential
        self.scope = scope
        self.token = token

    def __eq__(self, other: Any) -> bool:
        """Check if two AzureTokenAuth instances are equal.

        Args:
            other: Another AzureTokenAuth instance to compare.

        Returns:
            bool: True if equal, False otherwise.
        """
        if not isinstance(other, AzureTokenAuth):
            return False

        return all(
            [
                self.credential == getattr(other, "credential", None),
                self.scope == getattr(other, "scope", None),
            ]
        )

    def __ne__(self, other: Any) -> bool:
        """Check if two AzureTokenAuth instances are not equal.

        Args:
            other: Another AzureTokenAuth instance to compare.

        Returns:
            bool: True if not equal, False otherwise.
        """
        return self != other

    def __call__(self, r: Any) -> Any:
        """Attach Azure token to the request headers.

        This method is called to attach the Azure token to the headers of the given request.

        Args:
            r: The request object to attach the token to.

        Returns:
            Request: The modified request object.
        """
        if self.token:
            token = self.token
        else:
            token = self.credential.get_token(self.scope).token  # type: ignore[union-attr]
        r.headers["Authorization"] = "Bearer " + token
        return r


def get_rdd_secret(key: str) -> str:
    """Get secret from rdd keyvault."""
    from pyspark.dbutils import DBUtils

    spark = SparkSession.builder.getOrCreate()
    dbutils = DBUtils(spark)

    client_id = dbutils.secrets.get(scope="rdd", key="job-spn-client-id")
    client_secret = dbutils.secrets.get(scope="rdd", key="job-spn-client-secret")
    tenant_id = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
    key_vault_name = dbutils.secrets.get(scope="rdd", key="rdd-keyvault-name")

    credential = ClientSecretCredential(tenant_id=tenant_id, client_id=client_id, client_secret=client_secret)

    key_vault_uri = f"https://{key_vault_name}.vault.azure.net"
    client = SecretClient(vault_url=key_vault_uri, credential=credential)
    return client.get_secret(key).value  # type: ignore[return-value]


def get_stardog_access_token() -> AzureTokenAuth:
    """Get an access token for Stardog using Azure credentials."""
    client_id = get_rdd_secret("sp-databricks-client-id")
    client_secret = get_rdd_secret("sp-databricks-client-secret")
    auth_scope = get_rdd_secret("rdd-stardog-api-scope")
    tenant_id = get_rdd_secret("azure-tenant-id")
    credential = ClientSecretCredential(tenant_id=tenant_id, client_id=client_id, client_secret=client_secret)
    return AzureTokenAuth(credential=credential, scope=auth_scope)


def get_databricks_bearer_token() -> str:
    """Retrieve a bearer token using a service principal.

    Returns:
        str: The bearer token if successful, otherwise None.
    """
    from pyspark.dbutils import DBUtils

    spark = SparkSession.builder.getOrCreate()
    dbutils = DBUtils(spark)

    # Retrieve service principal credentials from Azure Key Vault
    app_id = dbutils.secrets.get(scope="rdd", key="stardog-jdbc-spn-client-id")
    secret = dbutils.secrets.get(scope="rdd", key="stardog-jdbc-spn-client-secret")
    tenant_id = "a6c60f0f-76aa-4f80-8dba-092771d439f0"
    authority = f"https://login.microsoftonline.com/{tenant_id}"
    scopes = ["2ff814a6-3304-4ab8-85cb-cd0e6f879c1d/.default"]

    # Authenticate using the service principal credentials to obtain an Azure AD token
    client = ConfidentialClientApplication(client_id=app_id, authority=authority, client_credential=secret)
    result = client.acquire_token_for_client(scopes=scopes)

    if not result or "access_token" not in result:
        raise Exception("Failed to acquire access token")

    return result["access_token"]


def get_databricks_pat(databricks_workspace_url: str = DBX_WORKSPACE_URL) -> str:  # type: ignore[assignment]
    """Retrieve a Databricks personal access token using a service principal.

    This function authenticates a service principal using Microsoft Authentication Library (MSAL)
    to acquire an Azure AD token, and then uses the Databricks SDK to create a personal access token.

    Args:
        databricks_workspace_url (str): The URL of the Databricks workspace. Defaults to the current workspace.

    Returns:
        str: The Databricks personal access token if successful, otherwise None.
    """
    # Get the bearer token
    access_token = get_databricks_bearer_token()

    # Use the acquired Azure AD token to initialize the Databricks SDK client
    from databricks.sdk import WorkspaceClient

    databricks_client = WorkspaceClient(host=databricks_workspace_url, token=access_token)

    # Create a Databricks personal access token
    response = databricks_client.tokens.create(
        comment="Token for load script", lifetime_seconds=DATABRICKS_PAT_VALIDITY
    )

    return response.token_value  # type: ignore[return-value]
