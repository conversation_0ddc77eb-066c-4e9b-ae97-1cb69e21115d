"""Generate dictionary with mappings for needs ingestion."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from artifactory import ArtifactoryPath
from rddlib.auth_utils import get_rdd_secret

SCHEMA_URL = "https://jfrog.ad-alliance.biz/artifactory/shared-generic-dev-local/sphinx_needs_turtle/main/schema.ttl"


def get_latest_pace_ontology() -> ArtifactoryPath:
    """Returns an ArtifactoryPath to the latest version of the PACE ontology."""
    # Get artifactory path to the ontology
    art_name = get_rdd_secret("alliance-artifactory-username")
    art_key = get_rdd_secret("alliance-artifactory-token")

    return ArtifactoryPath(SCHEMA_URL, auth=(art_name, art_key))
