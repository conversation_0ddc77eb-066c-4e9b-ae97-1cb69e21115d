"""Helper module for data quality checks.

This module provides functions to perform data quality checks and manipulations
on Spark DataFrames, including the ability to deduplicate data based on various
criteria such as column subsets, time windows, and optional hashing for more
efficient deduplication over large datasets.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
import logging
from datetime import datetime, timedelta
from typing import Any

from dateutil.relativedelta import relativedelta
from pyspark.sql import DataFrame, Row
from pyspark.sql import SparkSession as spark
from pyspark.sql import functions as F
from pyspark.sql.functions import avg, col, lag, length
from pyspark.sql.functions import sum as sum_
from pyspark.sql.types import StructType
from pyspark.sql.window import Window

logger = logging.getLogger(__name__)


def log_quality_check_result(passed: bool, qc_type: str, content: Any) -> None:
    """Log the result of a quality check.

    Args:
        passed (bool): Indicates if the quality check passed.
        qc_type (str): The type of quality check performed.
        content (str | None): Details of the quality check results.
    """
    result_dict = {"passed": passed, "qc_type": qc_type, "content": str(content) if content is not None else None}
    logger_meth = logger.info if passed else logger.warning

    dict_str = json.dumps(result_dict, indent=None)
    logger_meth(f"[quality_check]:{dict_str}")


def deduplicate(df, subset, time_window, use_hashing, just_test):
    """Performs deduplication of a Spark DataFrame based on a subset of columns.

    Optionally, this function can handle deduplication within a specified time window for a datetime
    column and use hashing for efficiency. It returns a list containing the indices of all
    duplicate elements in the original DataFrame.

    Args:
        df (DataFrame): The input Spark DataFrame.
        subset (list): Columns used as the basis for deduplication.
        time_window (dict): Specifies the time window for deduplication, including the datetime
            column, start, and end dates.
        use_hashing (bool): Indicates if hashing should be used for deduplication.
        just_test (bool): Specifies if this is a test run or actual deduplication.

    Returns:
        tuple: A tuple containing the deduplicated DataFrame and a list of indices of all
        duplicate elements in the original DataFrame.
    """
    # Take care of array and map type columns
    for col_name, col_type in df.dtypes:
        # Check if the column is of type Array<Struct>
        if "array<struct" in col_type.lower():
            df = df.withColumn(col_name, F.to_json(F.col(col_name)))
        elif "map" in col_type.lower():
            # Assuming you want to handle MapType columns as well, perhaps with a different logic
            df = df.withColumn(col_name, F.map_values(F.col(col_name)).cast("string"))
    if not use_hashing:
        # Do deduplication without hashing
        if just_test:
            df.dropDuplicates(subset=subset).show()
        else:
            window_spec = Window.partitionBy(subset).orderBy(subset)
            # Assign unique IDs to each row
            df_with_id = df.withColumn("row_id", F.monotonically_increasing_id())
            # Select only the rows with the lowest ID in each partition
            output_df = (
                df_with_id.withColumn("duplicated", F.row_number().over(window_spec))
                .filter("duplicated = 1")
                .drop("duplicated")
            )
            # Collect the duplicate IDs
            duplicate_ids = [row.row_id for row in df_with_id.subtract(output_df).collect()]
    else:
        # Do deduplication with hashing
        datetime_column = time_window["column"]
        if datetime_column not in df.columns:
            raise ValueError(f"Column '{datetime_column}' not found in the DataFrame.")

        start_date, end_date = map(F.to_timestamp, time_window["dates"])

        window_spec = Window.partitionBy(subset).orderBy(datetime_column)

        hash_column = "hash_column"
        df_hashed = df.withColumn(hash_column, F.hash(*subset))

        count_window = (
            Window.partitionBy(subset, hash_column).orderBy(datetime_column).rangeBetween(Window.unboundedPreceding, 0)
        )
        df_filtered = df_hashed.filter(
            (F.col(datetime_column).between(start_date, end_date))
            & (F.approx_count_distinct(hash_column).over(count_window) == 1)
        )

        if just_test:
            df_filtered.show()
        else:
            # Convert MapType columns to StringType
            for col_name, col_type in df_filtered.dtypes:
                if "map" in col_type.lower():
                    df_filtered = df_filtered.withColumn(col_name, F.map_values(F.col(col_name)).cast("string"))
            output_df = df_filtered.drop(hash_column)
            # Assign unique IDs to each row
            df_with_id = df.withColumn("row_id", F.monotonically_increasing_id())
            # Collect the duplicate IDs
            duplicate_ids = [row.row_id for row in df_with_id.subtract(output_df).collect()]

    return output_df, duplicate_ids


def unique_value_validation(df: DataFrame, column: str, values_to_match: list, methods: list = ["baseline"]):
    """Checks if a given column of a Spark DataFrame matches a set of unique values.

    This function can use different methods to validate if the column values match the provided
    unique values list. The default method is 'baseline', which directly checks for matches.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The categorical column to be validated.
        values_to_match (list): Suggested unique values that the column should contain.
        methods (list of str): Methods to use for validation, default is ["baseline"].

    Returns:
        DataFrame: A DataFrame containing rows that do not match the suggested values.
    """
    # Ensure the column exists in the DataFrame
    if column not in df.columns:
        raise ValueError(f"Column '{column}' not found in the DataFrame.")

    # Start to process for each method given
    for method in methods:
        # first will be baseline, so check the conformity for each suggested unique value
        if method == "baseline":
            non_matching_rows = df.filter(~F.col(column).isin(values_to_match))

        else:
            # now we have to check the other methods and load other function corresponding to this
            # method. If the method is not implemented, then an error is thrown.
            # An example method could be an nlp function that encodes each value of row and the
            # corresponding suggested values to vectors and then it is easier to find matching
            # through vector distance.
            print("Not implemented yet!")

    return non_matching_rows


def missing_value_detection(df: DataFrame, column: str):
    """Checks for missing values in a specified column of a Spark DataFrame.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to check for missing values.

    Returns:
        DataFrame: A DataFrame containing rows where the specified column has missing values.
    """
    # Ensure the column exists in the DataFrame
    if column not in df.columns:
        raise ValueError(f"Column '{column}' not found in the DataFrame.")

    # Filter rows based on the missing value condition in the given column
    missing_value_rows = df.filter(F.col(column).isNull())

    # Return the list
    return missing_value_rows


def numeric_outlier_detection(df: DataFrame, column: str, threshold_multiplier: float = 3.0):
    """Performs numeric outlier detection based on the standard deviation.

    This method identifies outliers by calculating the mean and standard deviation and using these
    to define a threshold for outlier detection.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The numeric column to analyze for outliers.
        threshold_multiplier (float): Multiplier for the standard deviation to define outliers.

    Returns:
        list: Indices of rows containing outliers.
    """
    # Calculate mean and standard deviation of the column
    mean_val, stddev_val = df.select(F.mean(F.col(column)), F.stddev(F.col(column))).first()

    # Define the lower and upper bounds for outliers based on the standard deviation
    lower_bound = mean_val - threshold_multiplier * stddev_val
    upper_bound = mean_val + threshold_multiplier * stddev_val

    # Filter rows where the column value is outside the bounds
    outlier_rows = (
        df.filter((F.col(column) < lower_bound) | (F.col(column) > upper_bound))
        .select("index")
        .rdd.flatMap(lambda x: x)
        .collect()
    )

    return outlier_rows


def outlier_detection(df: DataFrame, column: str, method: str = "numeric", **kwargs):
    """Detects outliers in a given column of a Spark DataFrame using a specified method.

    Currently supports a numeric method based on statistical deviation.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to analyze for outliers.
        method (str): Outlier detection method to use; defaults to "numeric".
        **kwargs (dict): Additional keyword arguments specific to the outlier detection method.

    Returns:
        list: Indices of the rows identified as outliers.
    """
    # Ensure the column exists in the DataFrame
    if column not in df.columns:
        raise ValueError(f"Column '{column}' not found in the DataFrame.")

    if method == "numeric":
        return numeric_outlier_detection(df, column, **kwargs)
    else:
        raise ValueError(f"Unsupported outlier detection method: {method}")


def schema_validation(df: DataFrame, desired_schema: StructType):
    """Checks if a PySpark DataFrame's schema matches a specified desired schema.

    This function compares the actual schema of the DataFrame with a provided StructType schema
    and identifies any discrepancies.

    Args:
        df (DataFrame): The PySpark DataFrame to validate.
        desired_schema (StructType): The desired pyspark.sql.types.StructType to compare against.

    Returns:
        tuple: A tuple containing a boolean indicating if the schemas match and a dictionary with
               details of any discrepancies.
    """
    actual_schema = df.schema
    actual_schema_str = str(actual_schema)
    desired_schema_str = str(desired_schema)

    if actual_schema_str == desired_schema_str:
        return True, {}
    else:
        # Find the specific struct fields and types that don't match
        actual_fields = {field.name: field.dataType for field in actual_schema.fields}
        desired_fields = {field.name: field.dataType for field in desired_schema.fields}

        mismatch_details = {
            field: {
                "actual_type": actual_fields.get(field),
                "desired_type": desired_fields.get(field),
            }
            for field in set(actual_fields) | set(desired_fields)
            if actual_fields.get(field) != desired_fields.get(field)
        }

        return False, mismatch_details


def datetime_consistency_check(df: DataFrame, column: str, desired_pattern: str):
    """Ensures datetime values in a specified column of a Spark DataFrame conform to a desired pattern.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column containing datetime values to check.
        desired_pattern (str): The desired datetime format pattern (e.g., 'yyyy-MM-dd').

    Returns:
        DataFrame: A DataFrame with the datetime column values converted to the desired pattern.
    """
    # Ensure the column exists in the DataFrame
    if column not in df.columns:
        raise ValueError(f"Column '{column}' not found in the DataFrame.")

    # Convert the column to the desired datetime pattern
    output_df = df.withColumn(column, F.to_timestamp(F.col(column), desired_pattern))

    return output_df


def text_pattern_check(df: DataFrame, column: str, desired_pattern: str):
    """Checks if text values in a specified column of a DataFrame match a given pattern.

    This function evaluates each text entry in the specified column against a regular expression
    pattern to identify non-conforming rows.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The text column to be checked.
        desired_pattern (str): The regex pattern that the text values should match.

    Returns:
        DataFrame: A DataFrame containing rows where the text does not match the desired pattern.
    """
    # Check for each row of the column if the value matches the desired pattern
    invalid_rows = df.filter(~F.col(column).rlike(desired_pattern))
    return invalid_rows


def null_check(df: DataFrame) -> DataFrame:
    """Identify and count null values in each column of the DataFrame.

    Args:
        df (DataFrame): The input Spark DataFrame.

    Returns:
        DataFrame: A DataFrame with the count of null values per column.
    """
    return df.select([(sum_(col(c).isNull().cast("int")).alias(c)) for c in df.columns])


def value_range_check(df: DataFrame, column: str, min_val: float, max_val: float) -> int:
    """Ensure values in a numeric column fall within a specified range.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to check.
        min_val (float): Minimum value allowed.
        max_val (float): Maximum value allowed.

    Returns:
        int: The count of values outside the specified range.
    """
    return df.filter((col(column) < min_val) | (col(column) > max_val)).count()


def value_distribution_check(df: DataFrame, column: str) -> DataFrame:
    """Check the distribution of values in a column, especially for categorical variables.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to check.

    Returns:
        DataFrame: A DataFrame showing the count of each unique value in the column.
    """
    return df.groupBy(column).count().orderBy("count", ascending=False)


def data_type_check(df: DataFrame, column: str, data_type: str) -> int:
    """Ensure a column contains data of the specified type.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to check.
        data_type (str): The expected data type (e.g., 'int', 'float', 'string').

    Returns:
        int: The count of rows where the column does not contain the specified data type.
    """
    return df.filter(col(column).cast(data_type).isNull()).count()


def consistency_check(df: DataFrame, greater_col: str, lower_col: str) -> int:
    """Ensure that related columns follow logical consistency (e.g., start date should be earlier than end date).

    Args:
        df (DataFrame): The input Spark DataFrame.
        greater_col (str): The start column to be greater.
        lower_col (str): The end column to be lower.

    Returns:
        int: The count of rows where consistency is violated.
    """
    return df.filter(col(greater_col) > col(lower_col)).count()


def length_check(df: DataFrame, column: str, max_len: int) -> int:
    """Ensure string columns do not exceed the specified length.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to check.
        max_len (int): The maximum allowed length.

    Returns:
        int: The count of values exceeding the specified length.
    """
    return df.filter(length(col(column)) > max_len).count()


def referential_integrity_check(df: DataFrame, fk_column: str, pk_df: DataFrame, pk_column: str) -> int:
    """Ensure foreign key columns match primary key columns in another DataFrame.

    Args:
        df (DataFrame): The input DataFrame containing the foreign key.
        fk_column (str): The foreign key column in the first DataFrame.
        pk_df (DataFrame): The DataFrame containing the primary key.
        pk_column (str): The primary key column in the second DataFrame.

    Returns:
        int: The count of unmatched foreign key values.
    """
    unmatched = df.join(pk_df, df[fk_column] == pk_df[pk_column], how="left_anti")
    return unmatched.count()


def constant_value_check_dict(df: DataFrame) -> dict:
    """Identify columns where all values are the same and return a dictionary with the column name and the constant value as key/value pairs.

    Args:
        df (DataFrame): The input Spark DataFrame.

    Returns:
        dict: A dictionary indicating whether each column contains a single unique value.
    """
    result = {}
    for c in df.columns:
        distinct_values = df.select(c).distinct().collect()
        if len(distinct_values) == 1:
            result[c] = distinct_values[0][0]

    return result


def co_occurrence_check(df: DataFrame, column1: str, value1: str, column2: str, value2: str) -> int:
    """Check if certain values in one column correspond to specific values in another column.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column1 (str): The first column to check.
        value1 (str): The specific value to check in the first column.
        column2 (str): The second column to check.
        value2 (str): The specific value to check in the second column.

    Returns:
        int: The count of rows where the co-occurrence condition is violated.
    """
    return df.filter((col(column1) == value1) & (col(column2) != value2)).count()


def imbalanced_data_check(df: DataFrame, column: str) -> DataFrame:
    """Identify heavily imbalanced classes in a categorical column.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to check.

    Returns:
        DataFrame: A DataFrame showing the ratio of each unique value in the column.
    """
    # Group by the given column and count occurrences of each unique value.
    # This will also produce a Dataframe with the additional count column.
    value_counts = df.groupBy(column).count()

    # Calculate the total number of rows in the DataFrame
    total_rows = df.count()

    # Add a new column 'ratio' that represents the ratio of each count to the total number of rows
    return value_counts.withColumn("ratio", col("count") / total_rows)


def cross_dataset_consistency_check(df1: DataFrame, df2: DataFrame, column: str) -> int:
    """Ensure consistency of certain values across multiple DataFrames.

    Args:
        df1 (DataFrame): The first DataFrame.
        df2 (DataFrame): The second DataFrame.
        column (str): The column to check for consistency.

    Returns:
        int: The count of discrepancies between the two DataFrames in the specified column.
    """
    df1_values = df1.select(column).distinct()
    df2_values = df2.select(column).distinct()
    discrepancies = df1_values.join(df2_values, column, how="outer").filter(
        col(df1_values[column]).isNull() | col(df2_values[column]).isNull()
    )
    return discrepancies.count()


def aggregation_consistency_check(df: DataFrame, column: str, expected_sum: float, expected_avg: float) -> bool:
    """Verify that aggregations (e.g., sum, average) make logical sense.

    Args:
        df (DataFrame): The input DataFrame.
        column (str): The column to check.
        expected_sum (float): The expected sum of the column values.
        expected_avg (float): The expected average of the column values.

    Returns:
        bool: True if the actual values match the expected values, False otherwise.
    """
    agg_result = df.agg(sum_(col(column)).alias("sum"), avg(col(column)).alias("avg")).collect()[0]
    return agg_result["sum"] == expected_sum and agg_result["avg"] == expected_avg


def time_series_continuity_check(df: DataFrame, timestamp_column: str, time_difference: int) -> tuple[int, DataFrame]:
    """Ensure time series data does not have significant gaps.

    Args:
        df (DataFrame): The input Spark DataFrame.
        timestamp_column (str): The timestamp column to check.
        time_difference (int): The allowed time difference between consecutive timestamps in seconds.

    Returns:
        (int, DataFrame): A tuple containing the count of rows where the time difference exceeds the allowed limit
                          and a DataFrame with rows where the check failed.
    """
    window_spec = Window.orderBy(timestamp_column)
    df = df.withColumn("prev_time", lag(col(timestamp_column)).over(window_spec))
    failed_rows = df.filter(col(timestamp_column).cast("long") - col("prev_time").cast("long") > time_difference)
    count = failed_rows.count()
    return count, failed_rows


def aggregated_metrics_consistency_check(
    df: DataFrame, column: str, time_column: str, expected_metrics: dict
) -> DataFrame:
    """Ensure aggregated metrics (e.g., sum, average) are consistent over time or match specific known values.

    Args:
        df (DataFrame): The input Spark DataFrame.
        column (str): The column to aggregate.
        time_column (str): The column representing time (e.g., 'date').
        expected_metrics (dict): A dictionary with expected aggregated metrics (e.g., {'2023-08-13': {'sum': 30.0, 'avg': 15.0}, ...}).

    Returns:
        DataFrame: A DataFrame with rows where the aggregated metrics do not match the expected values.
    """
    # Convert expected_metrics dict to DataFrame
    expected_metrics_list = [
        Row(**{time_column: k, "expected_sum": v["sum"], "expected_avg": v["avg"]}) for k, v in expected_metrics.items()
    ]
    expected_metrics_df = df.sparkSession.createDataFrame(expected_metrics_list)

    # Calculate actual metrics
    actual_metrics = df.groupBy(time_column).agg(F.sum(column).alias("sum"), F.avg(column).alias("avg"))

    # Join actual metrics with expected metrics
    metrics_with_expectations = actual_metrics.join(expected_metrics_df, on=time_column, how="left")

    # Identify inconsistencies
    inconsistencies = metrics_with_expectations.filter(
        (metrics_with_expectations["sum"] != metrics_with_expectations["expected_sum"])
        | (metrics_with_expectations["avg"] != metrics_with_expectations["expected_avg"])
    )

    return inconsistencies


def time_series_completeness_check(df: DataFrame, time_column: str, frequency: str) -> DataFrame:
    """Ensure that there are no missing time periods in the dataset.

    Args:
        df (DataFrame): The input Spark DataFrame.
        time_column (str): The column representing time.
        frequency (str): The expected frequency of time periods (e.g., 'daily', 'monthly').

    Returns:
        DataFrame: A DataFrame with the time periods where data is missing.
    """
    # Generate expected time periods based on frequency
    min_time = df.select(F.min(time_column)).first()[0]
    max_time = df.select(F.max(time_column)).first()[0]

    if frequency == "daily":
        expected_dates = [min_time + timedelta(days=x) for x in range((max_time - min_time).days + 1)]
    elif frequency == "monthly":
        expected_dates = [
            min_time + relativedelta(months=x)
            for x in range((max_time.year - min_time.year) * 12 + max_time.month - min_time.month + 1)
        ]
    else:
        raise ValueError("Unsupported frequency: {}".format(frequency))

    expected_df = spark.createDataFrame([(date,) for date in expected_dates], [time_column])
    missing_dates = expected_df.join(df, expected_df[time_column] == df[time_column], "left_anti")
    return missing_dates


def foreign_key_integrity_check(df: DataFrame, fk_column: str, parent_df: DataFrame, pk_column: str) -> DataFrame:
    """Ensure that foreign key values exist in the corresponding parent table.

    Args:
        df (DataFrame): The input DataFrame containing the foreign key.
        fk_column (str): The foreign key column in the first DataFrame.
        parent_df (DataFrame): The DataFrame containing the primary key.
        pk_column (str): The primary key column in the parent DataFrame.

    Returns:
        DataFrame: A DataFrame containing rows with unmatched foreign key values.
    """
    unmatched = df.join(parent_df, df[fk_column] == parent_df[pk_column], how="left_anti")
    return unmatched


def data_freshness_check(df: DataFrame, update_timestamp_column: str, max_age_hours: int) -> bool:
    """Ensure the Gold table is being updated as expected by checking the timestamp of the most recent data update.

    Args:
        df (DataFrame): The input Spark DataFrame.
        update_timestamp_column (str): The column containing the timestamp of the most recent update.
        max_age_hours (int): The maximum acceptable age of the most recent update in hours.

    Returns:
        bool: True if the data is fresh, False otherwise.
    """
    most_recent_update = df.agg(F.max(update_timestamp_column)).first()[0]
    current_time = datetime.now()
    age = (current_time - most_recent_update).total_seconds() / 3600
    return age <= max_age_hours


def derived_metric_correlation_check(
    df: DataFrame, col1: str, col2: str, expected_correlation: float, tolerance: float
) -> bool:
    """Ensure that derived metrics make sense by checking their correlation with other relevant metrics.

    Args:
        df (DataFrame): The input Spark DataFrame.
        col1 (str): The first column to use for correlation.
        col2 (str): The second column to use for correlation.
        expected_correlation (float): The expected correlation coefficient.
        tolerance (float): The acceptable deviation from the expected correlation coefficient.

    Returns:
        bool: True if the correlation is within the acceptable range, False otherwise.
    """
    actual_correlation = df.stat.corr(col1, col2)
    return abs(actual_correlation - expected_correlation) <= tolerance


def aggregation_completeness_check(df: DataFrame, group_by_columns: list, agg_column: str) -> DataFrame:
    """Verify that aggregations in the Gold layer are complete by ensuring every group is present in the aggregated results.

    Args:
        df (DataFrame): The input Spark DataFrame.
        group_by_columns (list): The columns to group by.
        agg_column (str): The column that has been aggregated.

    Returns:
        DataFrame: A DataFrame containing missing groups.
    """
    original_groups = df.select(group_by_columns).distinct()
    aggregated_groups = df.groupBy(group_by_columns).agg(F.count(agg_column).alias("count"))
    missing_groups = original_groups.join(aggregated_groups, on=group_by_columns, how="left_anti")
    return missing_groups


def redundancy_check(df: DataFrame, subset: list) -> DataFrame:
    """Ensure that there are no redundant rows in the Gold layer resulting from the aggregation of Silver layers.

    Args:
        df (DataFrame): The input Spark DataFrame.
        subset (list): Columns to consider for identifying redundancy.

    Returns:
        DataFrame: A DataFrame containing redundant rows.
    """
    redundant_rows = df.groupBy(subset).count().filter(col("count") > 1)
    return redundant_rows
