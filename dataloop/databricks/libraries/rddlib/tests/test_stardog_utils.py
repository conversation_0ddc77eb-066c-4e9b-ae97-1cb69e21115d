"""Tests for the stardog_utils module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from rddlib.stardog_utils import StardogUtils
from stardog.content import File, Raw
from stardog.exceptions import StardogException


@pytest.fixture
def stardog_utils() -> StardogUtils:
    """Return a StardogUtils instance with mocked connection details."""
    connection_details = {"username": "test_user", "password": "test_password", "endpoint": "http://localhost:1234"}
    return StardogUtils(connection_details)


# Test create_database method


@patch("rddlib.stardog_utils.Admin")
def test_create_database_success(mock_admin: MagicMock, stardog_utils: StardogUtils) -> None:
    """Test create_database method with a successful creation."""
    mock_admin_instance = MagicMock()
    mock_admin.return_value.__enter__.return_value = mock_admin_instance
    mock_admin_instance.databases.return_value = []

    stardog_utils.create_database("test_db")

    mock_admin_instance.new_database.assert_called_once_with("test_db")


@patch("rddlib.stardog_utils.Admin")
@pytest.mark.parametrize("ignore_exists", [True, False])
def test_create_database_already_exists(
    mock_admin: MagicMock, stardog_utils: StardogUtils, ignore_exists: bool
) -> None:
    """Test create_database method when the database already exists."""
    mock_admin_instance = MagicMock()
    mock_admin.return_value.__enter__.return_value = mock_admin_instance

    db_mock = MagicMock()
    db_mock.name = "test_db"
    mock_admin_instance.databases.return_value = [db_mock]

    if ignore_exists:
        stardog_utils.create_database("test_db", ignore_exists=True)
    else:
        with pytest.raises(StardogException):
            stardog_utils.create_database("test_db", ignore_exists=False)


# Test drop_database method


@patch("rddlib.stardog_utils.Admin")
def test_drop_database_success(mock_admin: MagicMock, stardog_utils: StardogUtils) -> None:
    """Test drop_database method with a successful drop."""
    mock_admin_instance = MagicMock()
    mock_admin.return_value.__enter__.return_value = mock_admin_instance

    db_mock = MagicMock()
    db_mock.name = "test_db"
    mock_admin_instance.databases.return_value = [db_mock]

    stardog_utils.drop_database("test_db")

    mock_admin_instance.database.return_value.drop.assert_called_once()


@patch("rddlib.stardog_utils.Admin")
@pytest.mark.parametrize("ignore_missing", [True, False])
def test_drop_database_not_exists(mock_admin: MagicMock, stardog_utils: StardogUtils, ignore_missing: bool) -> None:
    """Test drop_database method when the database does not exist."""
    mock_admin_instance = MagicMock()
    mock_admin.return_value.__enter__.return_value = mock_admin_instance
    mock_admin_instance.databases.return_value = []

    if ignore_missing:
        stardog_utils.drop_database("test_db", ignore_missing=True)
    else:
        with pytest.raises(StardogException):
            stardog_utils.drop_database("test_db", ignore_missing=False)


# Test run_query method


@patch("rddlib.stardog_utils.Connection")
def test_run_query(mock_connection: MagicMock, stardog_utils: StardogUtils) -> None:
    """Test run_query method."""
    mock_connection_instance = MagicMock()
    mock_connection.return_value.__enter__.return_value = mock_connection_instance

    stardog_utils.run_query("test_db", "SELECT * WHERE { ?s ?p ?o }")

    mock_connection_instance.begin.assert_called_once()
    mock_connection_instance.update.assert_called_once_with("SELECT * WHERE { ?s ?p ?o }")
    mock_connection_instance.commit.assert_called_once()


# Test load_from_turtle method


@patch("rddlib.stardog_utils.Connection")
def test_load_from_turtle(mock_connection: MagicMock, stardog_utils: StardogUtils) -> None:
    """Test load_from_turtle method."""
    mock_connection_instance = MagicMock()
    mock_connection.return_value.__enter__.return_value = mock_connection_instance

    stardog_utils.load_from_turtle("test_db", "path/to/turtle/file")

    mock_connection_instance.begin.assert_called_once()
    mock_connection_instance.add.assert_called_once()
    content = mock_connection_instance.add.call_args[0][0]
    assert isinstance(content, Raw)
    mock_connection_instance.commit.assert_called_once()


# Test load_namespaces method


@patch("rddlib.stardog_utils.Admin")
def test_load_namespaces(mock_admin: MagicMock, stardog_utils: StardogUtils) -> None:
    """Test load_namespaces method."""
    mock_admin_instance = MagicMock()
    mock_admin.return_value.__enter__.return_value = mock_admin_instance
    mock_database_instance = MagicMock()
    mock_admin_instance.database.return_value = mock_database_instance

    stardog_utils.load_namespaces("test_db", Path("path/to/turtle/file.ttl"))

    mock_database_instance.import_namespaces.assert_called_once()
    content = mock_database_instance.import_namespaces.call_args[0][0]
    assert isinstance(content, File)
