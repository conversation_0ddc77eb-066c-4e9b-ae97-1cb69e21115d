"""Tests for the release_note_utils module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import tempfile
from pathlib import Path
from unittest.mock import MagicMock, patch

from rddlib.release_note_utils import ARTIFACTORY_BASE_URL, download_pdf, trigger_release_note, upload_to_artifactory


def _get_rdd_secret(key: str) -> str:
    return {
        "azure-tenant-id": "tenant_id",
        "rdd-rng-api-scope": "scope",
        "rdd-rng-base-url": "http://api.base.url",
        "alliance-artifactory-username": "username",
        "alliance-artifactory-token": "token",
    }[key]


@patch("requests.get")
@patch("rddlib.release_note_utils.get_rdd_secret", side_effect=_get_rdd_secret)
@patch("rddlib.release_note_utils.SparkSession")
def test_trigger_release_note(mock_spark: MagicMock, mock_get_rdd_secret: MagicMock, mock_get: MagicMock) -> None:
    """Test trigger_release_note function."""
    mock_spark.sql.return_value.collect.return_value = [{"commit_sha": "mocked_sha"}]
    mock_response = MagicMock()
    mock_response.json.return_value = {"sasUrl": "http://sas.url"}
    mock_get.return_value = mock_response
    mock_get.return_value.raise_for_status = MagicMock()

    sas_url = trigger_release_note(
        "template_name", "config_name", "external_data_version", "software_version", "query_parameters", "token"
    )
    assert sas_url == "http://sas.url"
    mock_get.assert_called_once()


@patch("rddlib.release_note_utils.Path.open")
@patch("time.strftime")
@patch("requests.get")
def test_download_pdf(mock_get: MagicMock, mock_strftime: MagicMock, mock_open: MagicMock) -> None:
    """Test download_pdf function."""
    mock_response = MagicMock()
    mock_response.content = b"PDF content"
    mock_get.return_value = mock_response
    mock_get.return_value.raise_for_status = MagicMock()

    mock_strftime.return_value = "20250101_120000"

    output_file = download_pdf("http://sas.url", "template_name")

    result_path = Path.cwd() / "release_note_template_name_20250101_120000.pdf"
    mock_open.assert_called_once_with("wb")
    assert output_file == result_path


@patch("requests.put")
@patch("rddlib.release_note_utils.get_rdd_secret", side_effect=_get_rdd_secret)
def test_upload_to_artifactory(mock_get_rdd_secret: MagicMock, mock_put: MagicMock) -> None:
    """Test upload_to_artifactory function."""
    with tempfile.NamedTemporaryFile() as tmp_file:
        path = Path(tmp_file.name)
        upload_to_artifactory(path)

        mock_put.assert_called_once()
        put_args, put_kwargs = mock_put.call_args
        assert put_args[0] == f"{ARTIFACTORY_BASE_URL}/{path.name}"
        assert put_kwargs["auth"] == ("username", "token")
        assert put_kwargs["data"].name == str(path)
        assert put_kwargs["headers"]["Content-Type"] == "application/pdf"
