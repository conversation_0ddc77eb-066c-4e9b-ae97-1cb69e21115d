"""Tests for the needs_utils module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import patch

from rddlib.needs_utils import SCHEMA_URL, get_latest_pace_ontology


def mock_get_rdd_secret(secret_name: str) -> str:
    """Mock get_rdd_secret function."""
    if secret_name == "alliance-artifactory-username":
        return "test_user"
    elif secret_name == "alliance-artifactory-token":
        return "test_token"
    else:
        raise NotImplementedError(f"Secret {secret_name} not implemented in mock.")


@patch("rddlib.needs_utils.get_rdd_secret", mock_get_rdd_secret)
def test_get_latest_pace_ontology() -> None:
    """Test get_latest_pace_ontology function."""
    art_path = get_latest_pace_ontology()

    assert str(art_path) == SCHEMA_URL
    assert art_path.auth == ("test_user", "test_token")
