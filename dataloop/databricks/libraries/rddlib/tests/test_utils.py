"""Tests for the utils module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import sys
from pathlib import Path
from unittest.mock import MagicMock, patch

import pytest
from pyspark.sql import Column, Row, SparkSession
from rddlib.utils import (
    _get_bundle_dir_databricks,
    _get_bundle_dir_local,
    add_bundle_dir_to_path,
    batch,
    full_qualname,
    get_bundle_dir,
    method_self_name,
    nullif_blank,
)

# Test _get_bundle_dir_databricks function


@patch.dict("os.environ", DATABRICKS_RUNTIME_VERSION="42.0")
@patch("pathlib.Path.cwd", return_value=Path("/some/bundle/files/subdir"))
def test_get_bundle_dir_databricks_success(mock_cwd: MagicMock) -> None:
    """Test get_bundle_dir in Databricks environment."""
    result = _get_bundle_dir_databricks()
    assert result == Path("/some/bundle/files")
    mock_cwd.assert_called_once()


@patch.dict("os.environ", DATABRICKS_RUNTIME_VERSION="42.0")
@patch("pathlib.Path.cwd", return_value=Path("/some/other/dir/subdir"))
def test_get_bundle_dir_databricks_non_bundle(mock_cwd: MagicMock) -> None:
    """Test get_bundle_dir in non-bundle folder in Databricks environment."""
    with pytest.raises(FileNotFoundError):
        _get_bundle_dir_databricks()


# Test _get_bundle_dir_local function


@patch("sys.argv", ["/some/bundle/subdir/script.py"])
@patch("pathlib.Path.exists", autospec=True, side_effect=lambda path: path == Path("/some/bundle/databricks.yml"))
def test_get_bundle_dir_local_success(mock_exists: MagicMock) -> None:
    """Test get_bundle_dir in local environment."""
    result = _get_bundle_dir_local()
    assert result == Path("/some/bundle")
    assert mock_exists.call_count == 2


@patch("sys.argv", ["/some/other/dir/script.py"])
@patch("pathlib.Path.exists", return_value=False)
def test_get_bundle_dir_local_non_bundle(mock_exists: MagicMock) -> None:
    """Test get_bundle_dir in non-bundle folder in local environment."""
    with pytest.raises(FileNotFoundError):
        _get_bundle_dir_local()


@patch.dict("os.environ", DEBUGPY_RUNNING="true")
@patch(
    "sys.orig_argv",
    [
        "debugger.py",
        "1",
        "2",
        "3",
        "4",
        "5",
        "6",
        "7",
        "8",
        "9",
        "10",
        "/some/bundle/script.py",
    ],
)
@patch("pathlib.Path.exists", autospec=True, side_effect=lambda path: path == Path("/some/bundle/databricks.yml"))
def test_get_bundle_dir_local_debugging(mock_exists: MagicMock) -> None:
    """Test get_bundle_dir in local environment when debugging."""
    result = _get_bundle_dir_local()
    assert result == Path("/some/bundle")


# Test get_bundle_dir function


@pytest.mark.parametrize("on_databricks", [True, False])
@patch("rddlib.utils._get_bundle_dir_local")
@patch("rddlib.utils._get_bundle_dir_databricks")
def test_get_bundle_dir(
    mock_get_bundle_dir_databricks: MagicMock,
    mock_get_bundle_dir_local: MagicMock,
    on_databricks: bool,
    monkeypatch: pytest.MonkeyPatch,
) -> None:
    """Test get_bundle_dir function."""
    if on_databricks:
        monkeypatch.setattr("os.environ", {"DATABRICKS_RUNTIME_VERSION": "42.0"})
        get_bundle_dir()
        mock_get_bundle_dir_databricks.assert_called_once()
        mock_get_bundle_dir_local.assert_not_called()
    else:
        get_bundle_dir()
        mock_get_bundle_dir_databricks.assert_not_called()
        mock_get_bundle_dir_local.assert_called_once()


# Test add_bundle_dir_to_path function


@patch("sys.path", ["/usr/lib/bin"])
@patch("rddlib.utils.get_bundle_dir", return_value=Path("/some/bundle"))
def test_add_bundle_dir_to_path(mock_get_bundle_dir: MagicMock) -> None:
    """Test add_bundle_dir_to_path."""
    add_bundle_dir_to_path()
    assert "/some/bundle" in sys.path
    assert "/usr/lib/bin" in sys.path


# Test full_qualname function


def sample_function() -> None:
    """Sample function."""
    pass


def test_full_qualname() -> None:
    """Test full_qualname function."""

    result = full_qualname(sample_function)  # type: ignore[arg-type]
    assert result == "tests.test_utils.sample_function"


# Test method_self_name


class SampleClass:
    """Sample class for testing."""

    def sample_method(self) -> None:
        """Sample method."""
        pass


def test_method_self_name() -> None:
    """Test method__name function."""
    instance = SampleClass()
    method = instance.sample_method
    result = method_self_name(method)  # type: ignore[arg-type]
    assert result == (instance, "sample_method")


# Test batch function


def test_batch() -> None:
    """Test batch function."""
    iterable = [1, 2, 3, 4, 5]
    result = list(batch(iterable, 2))
    assert result == [[1, 2], [3, 4], [5]]


# Test pyspark functions


@pytest.mark.skip
def test_nullif_blank() -> None:
    """Test nullif_blank function."""
    spark = SparkSession.builder.master("local").appName("test").getOrCreate()
    df = spark.createDataFrame([("",), ("non-empty",)], ["col"])
    result_df = df.select(nullif_blank(Column("col")).alias("result"))
    result = result_df.collect()
    assert result == [Row(result=None), Row(result="non-empty")]
