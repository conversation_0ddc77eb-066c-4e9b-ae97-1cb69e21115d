# dbxlib - Common Databricks library

A library providing common functionality for working in Databricks

## Install from wheel

```bash
pip install ./dist/dbxlib-X.X.X-py3-none-any.whl[EXTRAS]
```

You need to replace `EXTRAS` with a comma separated list of extra dependencies you want to install:

- `dbx`: Usage in Databricks or with Databricks Connect
- `spark`: Local usage of Spark only, not all features from `dbx` supported

## Install locally in editable mode

```bash
pip install -e .[EXTRAS]
```

For `EXTRAS` see section above.

## Build as wheel

```bash
pip install build wheel
python3 -m build --wheel
```

The resulting `.whl` file can be found in the `dist/` folder.

## Install on DBX job cluster

Add the following configuration to your job YAML. Dependencies are
added at task level.

```bash
libraries:
    - pypi:
        package: dbxlib[EXTRAS]==<VERSION>
```
