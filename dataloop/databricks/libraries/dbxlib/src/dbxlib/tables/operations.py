"""This module provides operations for Databricks tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

from dbxlib.names import FullTableName
from dbxlib.pyspark import compare_struct_types
from delta import DeltaTable
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import StructType

logger = logging.getLogger(__name__)


def merge(df: DataFrame, table: FullTableName | str, condition: str) -> None:
    """Perform a merge operation on delta table.

    Args:
        df (DataFrame): The data to update the delta table with.
        table (FullTableName | str): The full name of the delta table.
        condition (str): The merge condition.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    delta_table = DeltaTable.forName(SparkSession.builder.getOrCreate(), str(table))
    count_before = delta_table.toDF().count()
    (
        delta_table.alias("target")
        .merge(df.alias("source"), condition)
        .whenMatchedUpdateAll()
        .whenNotMatchedInsertAll()
        .execute()
    )

    count_after = delta_table.toDF().count()
    logger.info(f"Data merged into {table} ({count_after - count_before} rows added)")


def overwrite(df: DataFrame, table: FullTableName | str, overwrite_schema: bool = False) -> None:
    """Perform an overwrite operation on a delta table.

    Args:
        df (DataFrame): The data to update the delta table with.
        table (FullTableName | str): The full name of the delta table.
        overwrite_schema (bool, optional): Whether to overwrite the schema of the table.
            Defaults to False.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    # Set overwriteSchema option if requested
    if overwrite_schema:
        write = df.write.option("overwriteSchema", "true")
    else:
        write = df.write.option("mergeSchema", "true")

    write.saveAsTable(str(table), format="delta", mode="overwrite")
    logger.info(f"Overwrote {table} with data ({df.count()} rows updated)")


def append(df: DataFrame, table: FullTableName | str) -> None:
    """Perform an append operation on a delta table.

    Args:
        df (DataFrame): The data to append to the delta table.
        table (FullTableName | str): The full name of the delta table.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    df.write.option("mergeSchema", "true").saveAsTable(str(table), format="delta", mode="append")
    logger.info(f"Data appended to {table} ({df.count()} rows added)")


def merge_or_overwrite(df: DataFrame, table: FullTableName | str, merge_condition: str) -> None:
    """Tries to merge data into a delta table.

    Falls back to overwriting it if the schema is different.
    Creates the table if it does not exist.

    Args:
        df (DataFrame): The data to update the delta table with.
        table (FullTableName | str): The full name of the delta table.
        merge_condition (str): The merge condition.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    if table_exists(table):
        logger.info(f"Table '{table}' already exists")
        # Check if schema is consistent
        if table_has_schema(table, df.schema):
            logger.info(f"Schema of table '{table}' matches. The table will be merged.")
            merge(df, table, merge_condition)
        else:
            logger.info(f"Schema of table '{table}' changed. The table will be overwritten.")
            overwrite(df, table, overwrite_schema=True)
    else:
        logger.info(f"Table '{table}' does not exist")
        overwrite(df, table)


def table_exists(table: FullTableName | str) -> bool:
    """Checks whether a delta table exists.

    Args:
        table (FullTableName | str): The full name of the delta table.

    Returns:
        bool: True if the table exists, False otherwise.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    return SparkSession.builder.getOrCreate().catalog.tableExists(str(table))


def table_has_schema(table: FullTableName | str, schema: StructType) -> bool:
    """Checks whether a table has a specific schema.

    Args:
        table (FullTableName | str): The full name of the delta table.
        schema (StructType): The schema to check against.

    Returns:
        bool: True if the table has the specified schema, False otherwise.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    table_df = SparkSession.builder.getOrCreate().read.table(str(table))
    return compare_struct_types(table_df.schema, schema)


def drop(table: FullTableName | str) -> None:
    """Drops the specified delta table if it exists.

    Args:
        table (FullTableName | str): The full name of the delta table.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    logger.info(f"Processing table: {table}")
    if table_exists(table):
        SparkSession.builder.getOrCreate().sql(f"DROP TABLE {table}")
        logger.info(f"Table {table} has been dropped.")
    else:
        logger.info(f"Table {table} does not exist.")
