"""This module provides functions to set metadata on Databricks tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import os
import re
from collections import defaultdict

from dbxlib.names import FullTableName
from delta.exceptions import MetadataChangedException
from pyspark.sql import SparkSession
from pyspark.sql.functions import coalesce, col, lit

logger = logging.getLogger(__name__)

# Validation regex
RESPONSIBLE_DOMAIN_CPATTERN = re.compile(r"^[A-Z][a-zA-Z0-9\- ]+$")
RESPONSIBLE_TEAM_CPATTERN = re.compile(r"^[A-Z][a-zA-Z0-9\- ]+$")
TAG_NAME_CPATTERN = re.compile(r"^[a-zA-Z][a-zA-Z0-9_\-\. ]+$")
TAG_VALUE_CPATTERN = re.compile(r"^.*$")
ISO8601_DURATION_CPATTERN = re.compile(
    r"^(?:P(?=\d|T\d)(?:(\d+)Y)?(?:(\d+)M)?(?:(\d+)([DW]))?(?:T(?:(\d+)H)?(?:(\d+)M)?(?:(\d+(?:\.\d+)?)S)?)?|none)$"
)

# Update functions


def update_table_metadata(
    table: FullTableName | str,
    description: str,
    responsible_team: str,
    responsible_domain: str,
    refresh_interval: str = "P1D",
    additional_tags: dict[str, str] | None = None,
    column_descriptions: dict[str, str] | None = None,
    column_timesystems: dict[str, str] | None = None,
    column_units: dict[str, str] | None = None,
    additional_column_tags: dict[str, dict[str, str]] | None = None,
    force_update: bool = False,
) -> None:
    """Sets the metadata of a table if necessary.

    If force_update is set, the metadata will be updated even if it has not changed.

    Args:
        table (FullTableName | str): The full name of the delta table.
        description (str): The description of the table.
        responsible_team (str): The responsible team.
        responsible_domain (str): The responsible domain.
        refresh_interval (str): The refresh interval in ISO 8601 duration format. Defaults to "P1D".
        additional_tags (dict[str, str], optional): Additional tags to set on the table
            as a mapping of tag name to tag value.
        column_descriptions (dict[str, str], optional): A mapping of column names to their descriptions.
            Defaults to None.
        column_timesystems (dict[str, str], optional): A mapping of timestamp columns to their timesystems.
            Any columns not in the mapping will be assumed to be in UTC. Defaults to None.
        column_units (dict[str, str], optional): A mapping of columns to their units. Defaults to None.
        additional_column_tags (dict[str, dict[str, str]], optional): Additional tags to set on the columns
            as a mapping of column name to a mapping of tag name to tag value. Defaults to None.
        force_update (bool, optional): Whether to update even if no changes were made.
            Defaults to False.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    # Update description if necessary
    update_table_description(table, description, force_update)

    # Update tags if necessary
    update_table_tags(table, responsible_team, responsible_domain, refresh_interval, additional_tags, force_update)

    # Update column descriptions if necessary
    update_columns_description(table, column_descriptions, force_update)

    # Update column tags if necessary
    update_columns_tags(table, column_timesystems, column_units, additional_column_tags, force_update)


def update_table_description(
    table: FullTableName | str,
    description: str,
    force_update: bool = False,
) -> None:
    """Sets the description of a table if necessary.

    If force_update is set, the description will be updated even if it has not changed.

    Args:
        table (FullTableName | str): The full name of the delta table.
        description (str): The description of the table.
        force_update (bool, optional): Whether to update even if no changes were made.
            Defaults to False.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    # Set description only if it changed
    if force_update or (get_table_description(table) != description):
        set_table_description(table, description)
    else:
        logger.debug(f"Description of {table} is up to date")


def update_table_tags(
    table: FullTableName | str,
    responsible_team: str,
    responsible_domain: str,
    refresh_interval: str = "P1D",
    additional_tags: dict[str, str] | None = None,
    force_update: bool = False,
) -> None:
    """Sets the tags of a table if necessary.

    If force_update is set, the tags will be updated even if they have not changed.

    Args:
        table (FullTableName | str): The full name of the delta table.
        responsible_team (str): The responsible team.
        responsible_domain (str): The responsible domain.
        refresh_interval (str): The refresh interval in ISO 8601 duration format. Defaults to "P1D".
        additional_tags (dict[str, str], optional): Additional tags to set on the table
            as a mapping of tag name to tag value.
        force_update (bool, optional): Whether to update even if no changes were made.
            Defaults to False.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    # Validate input
    assert RESPONSIBLE_DOMAIN_CPATTERN.match(responsible_domain), "Invalid responsible domain"
    assert RESPONSIBLE_TEAM_CPATTERN.match(responsible_team), "Invalid responsible team"
    assert ISO8601_DURATION_CPATTERN.match(refresh_interval), "Invalid refresh interval"

    # Check if tags need to be updated
    new_tags = {
        "responsible_domain": responsible_domain,
        "responsible_team": responsible_team,
        "refresh_interval": refresh_interval,
        **(additional_tags or {}),
    }
    if not force_update:
        update_tags = get_table_tags(table) != new_tags
    else:
        update_tags = True
    # Update tags if necessary
    if update_tags:
        set_table_tags(table, new_tags)
    else:
        logger.debug(f"Tags of {table} are up to date")


def update_columns_tags(
    table: FullTableName | str,
    timesystems: dict[str, str] | None = None,
    units: dict[str, str] | None = None,
    additional_tags: dict[str, dict[str, str]] | None = None,
    force_update: bool = False,
) -> None:
    """Sets the tags of all columns of a table if necessary.

    If force_update is set, the tags will be updated even if they have not changed.

    Args:
        table (FullTableName | str): The full name of the delta table.
        timesystems (dict[str, str] | None): A mapping of timestamp columns to their timesystems.
            Any columns not in the mapping will be assumed to be in UTC. Defaults to None.
        units (dict[str, str] | None): A mapping of columns to their units. Defaults to None.
        additional_tags (dict[str, dict[str, str]] , optional): Additional tags to set on the columns
            as a mapping of column name to a mapping of tag name to tag value. Defaults to None.
        force_update (bool, optional): Whether to update even if no changes were made.
            Defaults to False.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    # Set all time columns to UTC by default
    full_timesystems = {col: "UTC" for col in get_timestamp_columns(table)}
    # Overwrite timesystems if provided
    if timesystems is not None:
        full_timesystems.update(timesystems)

    # Gather all tags
    tags: dict[str, dict[str, str]] = defaultdict(dict)
    if additional_tags is not None:
        tags.update(additional_tags)
    for column, timesystem in full_timesystems.items():
        tags[column].update({"timesystem": timesystem})
    if units is not None:
        for column, unit in units.items():
            tags[column].update({"unit": unit})

    # Get all existing tags (to avoid multiple reads)
    all_existing_tags = get_all_column_tags(table)

    # Set tags for each column
    for column in get_table_columns(table):
        column_tags = tags.get(column, {})
        # Set column tags only if they changed
        if force_update or (all_existing_tags[column] != column_tags):
            set_column_tags(table, column, column_tags)
        else:
            logger.debug(f"Tags of column {column} in {table} are up to date")


def update_columns_description(
    table: FullTableName | str,
    descriptions: dict[str, str] | None = None,
    force_update: bool = False,
) -> None:
    """Sets the description of all columns of a table if necessary.

    If force_update is set, the descriptions will be updated even if they have not changed.

    Args:
        table (FullTableName | str): The full name of the delta table.
        descriptions (dict[str, str], optional): A mapping of column names to their descriptions.
            Defaults to None.
        force_update (bool, optional): Whether to update even if no changes were made.
            Defaults to False.
    """
    if isinstance(table, str):  # pragma: no cover
        table = FullTableName.parse(table)

    if descriptions is None:  # pragma: no cover
        descriptions = {}

    # Get all existing descriptions (to avoid multiple reads)
    exist_descriptions = get_all_column_descriptions(table)

    # Set descriptions for each column
    for column in get_table_columns(table):
        column_description = descriptions.get(column, "")
        # Set column description only if it changed
        if force_update or (exist_descriptions[column] != column_description):
            set_column_description(table, column, column_description)
        else:
            logger.debug(f"Description of column {column} in {table} is up to date")


# Table tags functions


def get_table_tags(table: FullTableName) -> dict[str, str]:  # pragma: no cover
    """Returns the tags of a table.

    Args:
        table (FullTableName): The full name of the table.

    Returns:
        dict[str, str]: The tags of the table as a mapping of tag name to tag value.
    """
    tags_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.table_tags")
    tag_rows = (
        tags_df.where((col("schema_name") == table.schema) & (col("table_name") == table.table))
        .select("tag_name", "tag_value")
        .collect()
    )

    return {row["tag_name"]: row["tag_value"] for row in tag_rows}


def set_table_tags(table: FullTableName, tags: dict[str, str]) -> None:  # pragma: no cover
    """Sets the tags of a table.

    Args:
        table (FullTableName): The full name of the table.
        tags (dict[str, str]): The tags to set as a mapping of tag name to tag value.
    """
    # Validate input
    for tag_name, tag_value in tags.items():
        assert TAG_NAME_CPATTERN.match(tag_name), f"Invalid tag name '{tag_name}'"
        assert TAG_VALUE_CPATTERN.match(tag_value), f"Invalid tag value '{tag_value}' for tag '{tag_name}'"

    tag_strs = [f"'{tag_name}' = '{_escape_single_quotes(tag_value)}'" for tag_name, tag_value in tags.items()]
    joined_tag_strs = ",".join(tag_strs)
    # Set table tags
    if len(tags) > 0:
        _sql_ignore_metadata_changed_exception(f"ALTER TABLE {table} SET TAGS ({joined_tag_strs})")

    # Remove other tags
    old_tags = get_table_tags(table)
    remove_tags = set(old_tags.keys()) - set(tags.keys())
    if len(remove_tags) > 0:
        joined_remove_tags_str = ",".join([f"'{tag}'" for tag in remove_tags])
        _sql_ignore_metadata_changed_exception(f"ALTER TABLE {table} UNSET TAGS ({joined_remove_tags_str})")

    logger.info(f"Set tags of {table} to {joined_tag_strs}")


# Column tags functions


def get_column_tags(table: FullTableName, column: str) -> dict[str, str]:  # pragma: no cover
    """Returns the tags of a column.

    Args:
        table (FullTableName): The full name of the table.
        column (str): The name of the column.

    Returns:
        dict[str, str]: The tags of the column as a mapping of tag name to tag value.
    """
    tags_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.column_tags")
    tag_rows = (
        tags_df.where(
            (col("schema_name") == table.schema) & (col("table_name") == table.table) & (col("column_name") == column)
        )
        .select("tag_name", "tag_value")
        .collect()
    )

    return {row["tag_name"]: row["tag_value"] for row in tag_rows}


def get_all_column_tags(table: FullTableName) -> dict[str, dict[str, str]]:  # pragma: no cover
    """Returns the tags of all columns of a table.

    Args:
        table (FullTableName): The full name of the table.

    Returns:
        dict[str, dict[str, str]]: A mapping of column names to their tags as a mapping of tag name to tag value.
    """
    tags_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.column_tags")
    tag_rows = (
        tags_df.where((col("schema_name") == table.schema) & (col("table_name") == table.table))
        .select("column_name", "tag_name", "tag_value")
        .collect()
    )

    column_tags: dict[str, dict[str, str]] = defaultdict(dict)
    for row in tag_rows:
        column_tags[row["column_name"]][row["tag_name"]] = row["tag_value"]

    return column_tags


def set_column_tags(table: FullTableName, column: str, tags: dict[str, str]) -> None:  # pragma: no cover
    """Sets the tags of a column.

    Args:
        table (FullTableName): The full name of the table.
        column (str): The name of the column.
        tags (dict[str, str]): The tags to set as a mapping of tag name to tag value.
    """
    # Validate input
    for tag_name, tag_value in tags.items():
        assert TAG_NAME_CPATTERN.match(tag_name), f"Invalid tag name '{tag_name}'"
        assert TAG_VALUE_CPATTERN.match(tag_value), f"Invalid tag value '{tag_value}' for tag '{tag_name}'"

    tag_strs = [f"'{tag_name}' = '{_escape_single_quotes(tag_value)}'" for tag_name, tag_value in tags.items()]
    joined_tag_strs = ",".join(tag_strs)
    # Set column tags
    if len(tags) > 0:
        _sql_ignore_metadata_changed_exception(
            f"ALTER TABLE {table} ALTER COLUMN `{column}` SET TAGS ({joined_tag_strs})"
        )

    # Remove other tags
    old_tags = get_column_tags(table, column)
    remove_tags = set(old_tags.keys()) - set(tags.keys())
    if len(remove_tags) > 0:
        joined_remove_tags_str = ",".join([f"'{tag}'" for tag in remove_tags])
        _sql_ignore_metadata_changed_exception(
            f"ALTER TABLE {table} ALTER COLUMN `{column}` UNSET TAGS ({joined_remove_tags_str})"
        )

    logger.info(f"Set tags of column {column} in {table} to {joined_tag_strs}")


# Table description functions


def get_table_description(table: FullTableName) -> str:  # pragma: no cover
    """Returns the description of a table.

    Args:
        table (FullTableName): The full name of the table.

    Returns:
        str: The description of the table.
    """
    meta_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.tables")
    comment_rows = (
        meta_df.where((col("table_schema") == table.schema) & (col("table_name") == table.table))
        .select(coalesce(col("comment"), lit("")).alias("comment"))
        .collect()
    )

    return comment_rows[0]["comment"]


def set_table_description(table: FullTableName, description: str) -> None:  # pragma: no cover
    """Sets the description of a table.

    Args:
        table (FullTableName): The full name of the table.
        description (str): The description to set.
    """
    # Escape unescaped quotes in description otherwise they break the SQL query
    description_escaped = _escape_single_quotes(description)

    _sql_ignore_metadata_changed_exception(f"COMMENT ON TABLE {table} IS '{description_escaped}'")
    logger.info(f"Set description of {table} to '{description}'")


# Column description functions


def get_column_description(table: FullTableName, column: str) -> str:  # pragma: no cover
    """Returns the description of a column.

    Args:
        table (FullTableName): The full name of the table.
        column (str): The name of the column.

    Returns:
        str: The description of the column.
    """
    meta_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.columns")
    comment_rows = (
        meta_df.where(
            (col("table_schema") == table.schema) & (col("table_name") == table.table) & (col("column_name") == column)
        )
        .select(coalesce(col("comment"), lit("")).alias("comment"))
        .collect()
    )

    return comment_rows[0]["comment"]


def get_all_column_descriptions(table: FullTableName) -> dict[str, str]:  # pragma: no cover
    """Returns the descriptions of all columns of a table.

    Args:
        table (FullTableName): The full name of the table.

    Returns:
        dict[str, str]: A mapping of column names to their descriptions.
    """
    meta_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.columns")
    comment_rows = (
        meta_df.where((col("table_schema") == table.schema) & (col("table_name") == table.table))
        .select("column_name", coalesce(col("comment"), lit("")).alias("comment"))
        .collect()
    )

    return {row["column_name"]: row["comment"] for row in comment_rows}


def set_column_description(table: FullTableName, column: str, description: str) -> None:  # pragma: no cover
    """Sets the description of a column.

    To set the description of a column in a view, COMMENT ON COLUMN is used which needs at least DBR 16.1.

    Args:
        table (FullTableName): The full name of the table.
        column (str): The name of the column.
        description (str): The description to set.
    """
    # Escape unescaped quotes in description otherwise they break the SQL query
    description_escaped = _escape_single_quotes(description)

    if get_table_type(table) == "VIEW":
        dbr_version = os.environ.get("DATABRICKS_RUNTIME_VERSION", None)
        if not dbr_version:
            raise ValueError("Script is running outside of Databricks. Cannot determine DBR version.")
        if float(dbr_version) >= 16.1:
            _sql_ignore_metadata_changed_exception(f"COMMENT ON COLUMN {table}.{column} IS '{description_escaped}'")
        else:
            raise ValueError(
                f"Need at least DBR of 16.1 to alter columns in views. Used DBR version is: {dbr_version}."
            )
    else:
        _sql_ignore_metadata_changed_exception(
            f"ALTER TABLE {table} CHANGE COLUMN `{column}` COMMENT '{description_escaped}'"
        )
    logger.info(f"Set description of column {column} in {table} to '{description}'")


# Other functions


def get_table_columns(table: FullTableName) -> list[str]:  # pragma: no cover
    """Returns the columns of a table.

    Args:
        table (FullTableName): The full name of the table.

    Returns:
        list[str]: The names of the columns.
    """
    meta_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.columns")
    column_names = (
        meta_df.where((col("table_schema") == table.schema) & (col("table_name") == table.table))
        .select("column_name")
        .collect()
    )

    return [row["column_name"] for row in column_names]


def get_timestamp_columns(table: FullTableName) -> list[str]:  # pragma: no cover
    """Returns the timestamp columns of a table.

    Args:
        table (FullTableName): The full name of the table.

    Returns:
        list[str]: The names of the timestamp columns.
    """
    meta_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.columns")
    timestamp_columns = (
        meta_df.where(
            (col("table_schema") == table.schema)
            & (col("table_name") == table.table)
            & (col("data_type") == "TIMESTAMP")
        )
        .select("column_name")
        .collect()
    )

    return [row["column_name"] for row in timestamp_columns]


def get_table_type(table: FullTableName) -> str:
    """Returns the type of the table.

    Returns:
        str: The type of the table.
    """
    meta_df = SparkSession.builder.getOrCreate().read.table(f"{table.catalog}.information_schema.tables")
    table_type = (
        meta_df.where((col("table_schema") == table.schema) & (col("table_name") == table.table))
        .select("table_type")
        .collect()
    )
    return table_type[0]["table_type"]


def _escape_single_quotes(description: str) -> str:
    """Escapes unescaped quotes in a string.

    Args:
        description (str): The string to escape quotes in.

    Returns:
        str: The string with quotes escaped.
    """
    return re.sub(r"(?<!\\)'", r"\\'", description)


def _sql_ignore_metadata_changed_exception(sql_query: str) -> None:  # pragma: no cover
    """Executes an SQL query and ignores MetadataChangedException.

    Args:
        sql_query (str): The SQL query to execute.
    """
    try:
        SparkSession.builder.getOrCreate().sql(sql_query)
    except MetadataChangedException as e:
        logger.warning(f"Ignoring concurrent metadata update exception: {e}")
