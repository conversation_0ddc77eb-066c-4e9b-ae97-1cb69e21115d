"""This module provides functions to work with Databricks tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbxlib.tables.metadata import (
    update_columns_description,
    update_columns_tags,
    update_table_description,
    update_table_metadata,
    update_table_tags,
)
from dbxlib.tables.operations import append, drop, merge, merge_or_overwrite, overwrite, table_exists, table_has_schema

__all__ = [
    "merge",
    "overwrite",
    "append",
    "drop",
    "merge_or_overwrite",
    "table_exists",
    "table_has_schema",
    "update_table_metadata",
    "update_table_description",
    "update_columns_tags",
    "update_table_tags",
    "update_columns_description",
]
