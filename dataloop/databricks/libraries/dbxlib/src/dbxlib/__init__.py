"""Main module of Databricks library."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbxlib._logging import DEFAULT_LOGGING_CONFIG, setup_databricks_logging
from dbxlib.databricks import (
    DBX_ENVIRONMENT,
    DBX_WORKSPACE_URL,
    RUNS_IN_DATABRICKS,
    DatabricksEnvironment,
    get_dbx_env_catalog,
)
from dbxlib.names import FullSchemaName, FullTableName

__all__ = [
    "setup_databricks_logging",
    "DEFAULT_LOGGING_CONFIG",
    "get_dbx_env_catalog",
    "get_dbx_sql_warehouse_url",
    "DBX_WORKSPACE_URL",
    "DBX_ENVIRONMENT",
    "DatabricksEnvironment",
    "RUNS_IN_DATABRICKS",
    "FullSchemaName",
    "FullTableName",
]
