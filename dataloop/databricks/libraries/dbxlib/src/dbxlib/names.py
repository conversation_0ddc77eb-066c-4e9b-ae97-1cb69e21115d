"""This module provides classes for working with Databricks schema and table names."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import re
from functools import cache
from typing import ClassVar

from dbxlib.databricks import get_dbx_env_catalog


class FullSchemaName:
    """Represents the full name of a schema in Databricks."""

    # Class fields
    _NON_DELIMITED_PATTERN: ClassVar[str] = r"([a-zA-Z_][a-zA-Z0-9_]*)"
    _PART_PATTERN: ClassVar[str] = rf"(?:{_NON_DELIMITED_PATTERN}|`(.+)`)"
    _NON_DELIMITED_CPATTERN: ClassVar[re.Pattern[str]] = re.compile(rf"^{_NON_DELIMITED_PATTERN}$")
    _FULL_CPATTERN: ClassVar[re.Pattern[str]] = re.compile(rf"^{_PART_PATTERN}\.{_PART_PATTERN}$")
    _NON_ALPHANUMERIC_CPATTERN: ClassVar[re.Pattern[str]] = re.compile(r"[^a-zA-Z0-9_]")

    _raw_catalog: str
    catalog: str
    """The Databricks catalog."""
    schema: str
    """The Databricks schema."""

    __slots__ = ("_raw_catalog", "catalog", "schema")

    def __init__(self, catalog: str, schema: str, is_generic_catalog: bool = True) -> None:
        """Creates a new full schema name.

        Args:
            catalog (str): The Databricks catalog.
            schema (str): The Databricks schema.
            is_generic_catalog (bool): The whether the catalog is generic and shall be replaced with
                the Databricks environment specific version (e.g. "gold" on DEV becomes "gold_dev").
        """
        if is_generic_catalog:
            env_catalog = get_dbx_env_catalog(catalog)
        else:
            env_catalog = catalog

        self._raw_catalog = catalog
        self.catalog = env_catalog
        self.schema = schema

    @cache
    def as_str(self) -> str:
        """Returns a string representation of the schema name.

        This can be safely used in SQL queries as all parts of the name are quoted.
        """
        catalog_str = self.catalog
        # No match with non-delimited pattern means quoting is required
        if self._NON_DELIMITED_CPATTERN.match(catalog_str) is None:
            catalog_str = f"`{catalog_str}`"
        schema_str = self.schema
        # No match with non-delimited pattern means quoting is required
        if self._NON_DELIMITED_CPATTERN.match(schema_str) is None:
            schema_str = f"`{schema_str}`"

        return f"{catalog_str}.{schema_str}"

    @cache
    def as_str_unquoted(self) -> str:
        """Returns a unquoted string representation of the schema name.

        This may not be safely used in SQL queries!
        """
        return f"{self.catalog}.{self.schema}"

    @cache
    def as_str_snake_case(self) -> str:
        """Returns a snake cased string representation of the schema name.

        The dots separating the parts of the name as well as all non-alphanumeric
        characters in the parts are replaced by underscores.
        """
        result = f"{self.catalog}_{self.schema}"
        # Replace invalid characters with underscores
        return self._NON_ALPHANUMERIC_CPATTERN.sub("_", result)

    def __str__(self) -> str:  # pragma: no cover
        """Returns the result of the 'as_str' method."""
        return self.as_str()

    def __repr__(self) -> str:  # pragma: no cover
        """Returns the result of the '__str__' method."""
        return str(self)

    def __eq__(self, value: object) -> bool:
        """Returns whether this schema name is equal to another object.

        Args:
            value (object): The object to compare to.
        """
        if not isinstance(value, FullSchemaName):
            return False

        return self.catalog == value.catalog and self.schema == value.schema

    def __hash__(self) -> int:
        """Returns a hash value for this schema name."""
        return hash((self.catalog, self.schema))

    def copy_with(
        self,
        catalog: str | None = None,
        schema: str | None = None,
    ) -> "FullSchemaName":
        """Creates a copy of this schema name with the given parts replaced.

        Args:
            catalog (str | None, optional): The new value for catalog. Defaults to None.
            schema (str | None, optional): The new value for schema. Defaults to None.
        """
        if catalog is None:
            catalog = self.catalog
            is_generic_catalog = False
        else:
            catalog = catalog
            is_generic_catalog = True

        schema = self.schema if schema is None else schema
        return FullSchemaName(catalog, schema, is_generic_catalog)

    @classmethod
    def parse(cls, value: str, is_generic_catalog: bool = True) -> "FullSchemaName":
        """Parses a full schema name from string.

        See https://docs.databricks.com/en/sql/language-manual/sql-ref-names.html#schema-name
        for a definition of valid names.

        Args:
            value (str): _description_
            is_generic_catalog (bool, optional): _description_. Defaults to True.
        """
        match = cls._FULL_CPATTERN.match(value)
        if match is None:
            raise ValueError("Invalid schema name.")

        catalog, schema = (part for part in match.groups() if part is not None)
        return cls(catalog, schema, is_generic_catalog)


class FullTableName:
    """Represents the full name of a table in Databricks."""

    # Class fields
    _FULL_PATTERN: ClassVar[re.Pattern[str]] = re.compile(
        rf"^{FullSchemaName._FULL_CPATTERN.pattern[1:-1]}\.{FullSchemaName._PART_PATTERN}$"
    )

    _schema_full: FullSchemaName

    @property
    def catalog(self) -> str:
        """The Databricks catalog."""
        return self._schema_full.catalog

    @property
    def schema(self) -> str:
        """The Databricks schema."""
        return self._schema_full.schema

    table: str
    """The Databricks table."""

    __slots__ = ("_schema_full", "table")

    def __init__(self, schema_full: FullSchemaName, table: str) -> None:
        """Creates a new full table name.

        Args:
            schema_full (FullSchemaName): The full Databricks schema.
            table (str): The table name.
        """
        self._schema_full = schema_full
        self.table = table

    @cache
    def as_str(self) -> str:
        """Returns a string representation of the table name.

        This can be safely used in SQL queries as all parts of the name are quoted.
        """
        table_str = self.table
        # No match with non-delimited pattern means quoting is required
        if FullSchemaName._NON_DELIMITED_CPATTERN.match(table_str) is None:
            table_str = f"`{table_str}`"

        return f"{self._schema_full.as_str()}.{table_str}"

    @cache
    def as_str_unquoted(self) -> str:
        """Returns a unqoted string representation of the table name.

        This may not be safely used in SQL queries!
        """
        return f"{self._schema_full.as_str_unquoted()}.{self.table}"

    @cache
    def as_str_snake_case(self) -> str:
        """Returns a snake cased string representation of the table name.

        The dots separating the parts of the name as well as all non-alphanumeric
        characters in the parts are replaced by underscores.
        """
        # Replace invalid characters with underscores
        table = FullSchemaName._NON_ALPHANUMERIC_CPATTERN.sub("_", self.table)
        return f"{self._schema_full.as_str_snake_case()}_{table}"

    def __str__(self) -> str:  # pragma: no cover
        """Returns the result of the 'as_str' method."""
        return self.as_str()

    def __repr__(self) -> str:  # pragma: no cover
        """Returns the result of the '__str__' method."""
        return str(self)

    def __eq__(self, value: object) -> bool:
        """Returns whether this table name is equal to another object.

        Args:
            value (object): The object to compare to.
        """
        if not isinstance(value, FullTableName):
            return False

        return self._schema_full == value._schema_full and self.table == value.table

    def __hash__(self) -> int:
        """Returns a hash value for this table name."""
        return hash((self._schema_full, self.table))

    def copy_with(
        self,
        catalog: str | None = None,
        schema: str | None = None,
        table: str | None = None,
    ) -> "FullTableName":
        """Creates a copy of this table name with the given parts replaced.

        Args:
            catalog (str | None, optional): The new value for catalog. Defaults to None.
            schema (str | None, optional): The new value for schema. Defaults to None.
            table (str | None, optional): The new value for table. Defaults to None.
        """
        schema_full = self._schema_full.copy_with(catalog, schema)
        table = self.table if table is None else table
        return FullTableName(schema_full, table)

    @classmethod
    def parse(cls, value: str, is_generic_catalog: bool = True) -> "FullTableName":
        """Parses a full table name from string.

        See https://docs.databricks.com/en/sql/language-manual/sql-ref-names.html#schema-name
        for a definition of valid names.

        Args:
            value (str): _description_
            is_generic_catalog (bool, optional): Whether the catalog is generic and shall be replaced with
                the Databricks environment specific version (e.g. "gold" on DEV becomes "gold_dev").
                Defaults to True.
        """
        match = cls._FULL_PATTERN.match(value)
        if match is None:
            raise ValueError("Invalid table name.")

        catalog, schema, table = (part for part in match.groups() if part is not None)
        return cls(FullSchemaName(catalog, schema, is_generic_catalog), table)
