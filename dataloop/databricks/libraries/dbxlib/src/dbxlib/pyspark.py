"""Utility functions and constants for working with pyspark."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import pyspark.sql.functions as F
from pyspark.sql import Column
from pyspark.sql.types import ArrayType, DataType, StructField, StructType


def compare_struct_fields(
    field1: StructField, field2: StructField, ignore_nullable: bool = False, ignore_metadata: bool = True
) -> bool:
    """Compare two pyspark struct fields.

    Args:
        field1: The first field.
        field2: The second field.
        ignore_nullable: Whether to ignore the nullable flag.
        ignore_metadata: Whether to ignore the metadata.
    """
    if field1.name != field2.name:
        return False
    if not ignore_nullable and (field1.nullable != field2.nullable):
        return False
    if not ignore_metadata and (field1.metadata != field2.metadata):
        return False

    return compare_data_types(field1.dataType, field2.dataType)


def compare_struct_types(
    st1: StructType, st2: StructType, ignore_nullable: bool = False, ignore_metadata: bool = True
) -> bool:
    """Compare two pyspark struct types.

    Args:
        st1: The first struct type.
        st2: The second struct type.
        ignore_nullable: Whether to ignore the nullable flag.
        ignore_metadata: Whether to ignore the metadata.
    """
    # Return early if lengths are different
    if len(st1) != len(st2):
        return False

    # Compare all fields
    for field1, field2 in zip(st1, st2, strict=True):
        if not compare_struct_fields(field1, field2, ignore_nullable, ignore_metadata):
            return False

    return True


def compare_array_types(
    at1: ArrayType, at2: ArrayType, ignore_nullable: bool = False, ignore_metadata: bool = True
) -> bool:
    """Compare two pyspark array types.

    Args:
        at1: The first array type.
        at2: The second array type.
        ignore_nullable: Whether to ignore the nullable flag.
        ignore_metadata: Whether to ignore the metadata.
    """
    if not ignore_nullable and (at1.containsNull != at2.containsNull):
        return False
    return compare_data_types(at1.elementType, at2.elementType, ignore_nullable, ignore_metadata)


def compare_data_types(
    dt1: DataType, dt2: DataType, ignore_nullable: bool = False, ignore_metadata: bool = True
) -> bool:
    """Compare two pyspark data types.

    Args:
        dt1: The first data type.
        dt2: The second data type.
        ignore_nullable: Whether to ignore the nullable flag.
        ignore_metadata: Whether to ignore the metadata
    """
    # Compare the types
    if dt1.__class__ is not dt2.__class__:
        return False

    # Compare the type parameters for arrays
    if isinstance(dt1, ArrayType):
        return compare_array_types(dt1, dt2, ignore_nullable, ignore_metadata)  # type: ignore[arg-type]

    # Compare the fields for structs
    elif isinstance(dt1, StructType):
        return compare_struct_types(dt1, dt2, ignore_nullable, ignore_metadata)  # type: ignore[arg-type]

    return True


# Dataframe API functions


def nullable(col: Column) -> Column:
    """Ensures the given column is nullable."""
    return F.when(F.lit(True), col)


def array_nullable(col: Column) -> Column:
    """Ensures the elements of the arrays as well as the arrays themselves in the given column are nullable."""
    return nullable(F.transform(col, lambda x: nullable(x)))
