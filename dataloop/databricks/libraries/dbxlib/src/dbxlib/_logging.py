"""Logging utilities for Databricks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import logging.config
import uuid
from copy import deepcopy
from datetime import datetime, timezone
from typing import Any, Callable

from dbxlib.names import FullSchemaName, FullTableName
from pyspark.sql import DataFrame, SparkSession
from pyspark.sql.types import DoubleType, StringType, StructField, StructType, TimestampType

logger = logging.getLogger(__name__)

DEFAULT_LOG_TABLE = "workflow_logs"

LOG_SCHEMA = StructType(
    [
        StructField("instance_id", StringType(), True),
        StructField("timestamp", TimestampType(), True),
        StructField("reltime", DoubleType(), True),
        StructField("level", StringType(), True),
        StructField("context", StringType(), True),
        StructField("message", StringType(), True),
    ]
)


def _get_instance_id(run_id: str | None) -> str:
    """Get the instance ID for the log message.

    Returns a string that is either "run-" followed by the run ID if it is given
    or "uuid-" followed by a new UUID.

    Args:
        run_id (str | None): The run ID of the executing job.

    Returns:
        str: The instance ID.
    """
    if run_id is not None:
        return "run-" + run_id
    return "uuid-" + str(uuid.uuid4())


def _level_filter_maker(level: str) -> Callable[[logging.LogRecord], bool]:
    """Create a filter that only allows records at or below a given level.

    Args:
        level (str): The level to filter at or below.

    Returns:
        Callable: A filter function that returns True for records at or below the given level.
    """
    log_level = getattr(logging, level)

    def level_filter(record: logging.LogRecord) -> bool:
        return record.levelno <= log_level

    return level_filter


class DatabricksContextFilter(logging.Filter):
    """A logging filter that adds context to log records."""

    instance_id: str
    context: str

    def __init__(self, run_id: str | None, context: str):
        """Create a new DatabricksContextFilter.

        Args:
            run_id (str | None): The run ID of the executing job.
            context (str): The context of the log messages.
        """
        super().__init__()
        self.instance_id = _get_instance_id(run_id)
        self.context = context

    def filter(self, record: logging.LogRecord) -> bool:
        """Add context to the log record.

        Args:
            record (LogRecord): The log record.

        Returns:
            bool: Always True.
        """
        record.instance_id = self.instance_id
        record.context = self.context
        return True


class DatabricksHandler(logging.Handler):
    """A logging handler that writes log records to a Databricks table."""

    spark: SparkSession
    table_full: FullTableName

    _log_queue: list[dict[str, Any]]
    _max_queue_size: int

    def __init__(
        self,
        spark: SparkSession,
        schema_full: FullSchemaName,
        table: str = DEFAULT_LOG_TABLE,
        max_queue_size: int = 5,
    ):
        """Create a new DatabricksHandler.

        Args:
            spark (SparkSession): The Spark session.
            schema_full (FullSchemaName): The full schema name of the table.
            table (str, optional): The name of the table. Defaults to "ingest_logs".
            max_queue_size (int, optional): The maximum size of the log queue. Defaults to 5.
        """
        super().__init__()
        self.spark = spark
        self.table_full = FullTableName(schema_full, table)

        self._log_queue = []
        self._max_queue_size = max_queue_size

    def emit(self, record: logging.LogRecord) -> None:
        """Emit a record to the Databricks table.

        This will add the record to the log queue and flush the queue if it is full.

        Args:
            record (LogRecord): The log record to emit.
        """
        try:
            row = self._row_from_record(record)
            self._log_queue.append(row)
            if len(self._log_queue) >= self._max_queue_size:
                self.flush()
        except Exception:
            self.handleError(record)

    def flush(self) -> None:
        """Flush the log queue to the Databricks table."""
        # End early if the queue is empty
        if len(self._log_queue) == 0:
            return

        self.acquire()
        try:
            log_df: DataFrame = self.spark.createDataFrame(self._log_queue, LOG_SCHEMA)
            log_df.write.saveAsTable(str(self.table_full), format="delta", mode="append")

            self._log_queue.clear()
        finally:
            self.release()

    def close(self) -> None:
        """Close the handler and flush the log queue."""
        try:
            self.flush()
        finally:
            super().close()

    def _row_from_record(self, record: logging.LogRecord) -> dict[str, Any]:
        """Create a row from a log record.

        The log records must have been processed by the DatabricksContextFilter.

        Args:
            record (LogRecord): The log record.

        Returns:
            dict[str, Any]: A dictionary representing the row.
        """
        return {
            "instance_id": record.instance_id,  # type: ignore[attr-defined]
            "timestamp": datetime.fromtimestamp(record.created, tz=timezone.utc),
            "reltime": record.relativeCreated / 1000,
            "level": record.levelname,
            "context": record.context,  # type: ignore[attr-defined]
            "message": record.getMessage(),
        }


DEFAULT_LOGGING_CONFIG = {
    "version": 1,
    "disable_existing_loggers": False,
    "formatters": {
        "console": {
            "class": "colorlog.ColoredFormatter",
            "format": "%(log_color)s%(asctime)s | %(levelname)s | %(context)s | %(name)s | %(message)s",
            "log_colors": {
                "DEBUG": "white",
                "INFO": "green",
                "WARNING": "yellow",
                "ERROR": "red",
                "CRITICAL": "red,bg_white",
            },
        },
    },
    "filters": {
        "warnings_and_below": {
            "()": _level_filter_maker,
            "level": "WARNING",
        },
        "databricks_context": {
            "()": DatabricksContextFilter,
        },
    },
    "handlers": {
        "stdout": {
            "class": "logging.StreamHandler",
            "level": "DEBUG",
            "formatter": "console",
            "stream": "ext://sys.stdout",
            "filters": ["databricks_context", "warnings_and_below"],
        },
        "stderr": {
            "class": "logging.StreamHandler",
            "level": "ERROR",
            "formatter": "console",
            "stream": "ext://sys.stderr",
            "filters": ["databricks_context"],
        },
        "databricks": {
            "class": f"{__name__}.{DatabricksHandler.__name__}",
            "level": "DEBUG",
            "filters": ["databricks_context"],
        },
    },
    "root": {  # Disable all loggers by default
        "level": "CRITICAL",
        "handlers": ["stdout", "stderr", "databricks"],
    },
    "loggers": {
        "__main__": {"level": "DEBUG"},
        "dbxlib": {"level": "DEBUG"},
    },
}


def setup_databricks_logging(
    schema_full: FullSchemaName,
    context: str,
    spark: SparkSession | None = None,
    table: str = DEFAULT_LOG_TABLE,
    run_id: str | None = None,
    log_level: str = "INFO",
    max_queue_size: int = 5,
    basic_config: dict[str, Any] = DEFAULT_LOGGING_CONFIG,
    enabled_loggers: list[str] | None = None,
) -> None:
    """Set up logging to Databricks tables.

    Args:
        spark (SparkSession): The Spark session.
        schema_full (FullSchemaName): The full schema name of the table.
        context (str): The context of the log message.
        table (str | None, optional): The name of the table. Defaults to DEFAULT_LOG_TABLE.
        run_id (str | None, optional): The run ID of the executing job. Defaults to None.
        log_level (str, optional): The log level to use. Defaults to "INFO".
        max_queue_size (int, optional): The maximum size of the log queue. Defaults to 5.
        basic_config (dict[str, Any], optional): The basic logging configuration.
            This should be a dictionary that can be passed to logging.config.dictConfig.
            It must include a "databricks" handler that has the class DatabricksHandler
            and a "databricks_context" filter that has the class DatabricksContextFilter.
            The object is deep copied and then modified to include the custom configuration.
            Defaults to DEFAULT_LOGGING_CONFIG.
        enabled_loggers (list[str] | None, optional): A list of logger names to enable.
    """
    if spark is None:
        spark = SparkSession.builder.getOrCreate()

    basic_config = deepcopy(basic_config)
    # Configure the Databricks context filter
    dbx_context_filter = basic_config["filters"]["databricks_context"]
    dbx_context_filter["run_id"] = run_id
    dbx_context_filter["context"] = context
    # Configure the Databricks handler
    dbx_config = basic_config["handlers"]["databricks"]
    dbx_config["spark"] = spark
    dbx_config["schema_full"] = schema_full
    dbx_config["table"] = table
    dbx_config["max_queue_size"] = max_queue_size
    dbx_config["level"] = log_level
    # Configure stdout handler
    stdout_config = basic_config["handlers"]["stdout"]
    stdout_config["level"] = log_level
    # Enable additional loggers
    if enabled_loggers is not None:
        for logger_name in enabled_loggers:
            basic_config["loggers"][logger_name] = {"level": "DEBUG"}

    # Configure logging
    logging.config.dictConfig(basic_config)

    # Log successful setup
    logger.info('Setup logging to table %s.%s with context "%s"', schema_full, table, context)
