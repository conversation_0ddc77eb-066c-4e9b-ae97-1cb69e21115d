"""Utility functions for working with Databricks environments."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import os
from enum import Enum

from py4j.protocol import Py4JJavaError
from pyspark.sql import SparkSession


class DatabricksEnvironment(Enum):
    """Enumeration of databricks environments."""

    DEV = 1
    QA = 2
    PROD = 3


DBX_WORKSPACE_URL: str | None
"""The URL of the current databricks workspace or None if not running in databricks."""
DBX_ENVIRONMENT: DatabricksEnvironment | None
"""The current databricks environment or None if not running in databricks."""
RUNS_IN_DATABRICKS: bool
"""Whether the code is executed in Databricks."""

DBX_WORKSPACE_URL_ENV_MAP = {
    "adb-505904006080631.11.azuredatabricks.net": DatabricksEnvironment.DEV,
    "adb-1833128652588029.9.azuredatabricks.net": DatabricksEnvironment.QA,
    "adb-8617216030703889.9.azuredatabricks.net": DatabricksEnvironment.PROD,
}

DBX_ENV_CATALOG_MAP = {
    DatabricksEnvironment.DEV: {
        "bronze": "bronze_dev",
        "silver": "silver_dev",
        "gold": "gold_dev",
    },
    DatabricksEnvironment.QA: {
        "bronze": "bronze_qa",
        "silver": "silver_qa",
        "gold": "gold_qa",
    },
    DatabricksEnvironment.PROD: {
        "bronze": "bronze",
        "silver": "silver",
        "gold": "gold",
    },
}


def get_dbx_env_catalog(generic_catalog: str) -> str:
    """Returns the name of a given generic catalog in the current Databricks environment.

    If not running in Databricks or no mapping for the catalog exists, returns the input catalog.
    """
    if DBX_ENVIRONMENT is None:
        return generic_catalog

    return DBX_ENV_CATALOG_MAP[DBX_ENVIRONMENT].get(generic_catalog, generic_catalog)


def _get_dbx_workspace_url() -> str | None:  # pragma: no cover
    """Return the current Databricks workspace URL or None if not executed in Databricks.

    As a special case if executed in a non-test debugging session, the DEV workspace URL is returned.
    """
    # Assume DEV workspace if debugging
    if bool(os.getenv("DEBUGPY_RUNNING")) and (os.getenv("PYTEST_VERSION") is None):
        return [url for url, env in DBX_WORKSPACE_URL_ENV_MAP.items() if env == DatabricksEnvironment.DEV][0]

    # Check if running in Databricks
    if os.getenv("DATABRICKS_RUNTIME_VERSION") is None:
        return None

    try:
        workspace_url = SparkSession.builder.getOrCreate().conf.get("spark.databricks.workspaceUrl")
        return workspace_url
    except RuntimeError:
        return None
    except Py4JJavaError:  # Does not inherit from BaseException, therefore needs own except block
        return None


def _get_dbx_environment() -> DatabricksEnvironment | None:
    """Return the current Databricks environment or None if not executed in Databricks.

    As a special case if executed in a non-test debugging session, the DEV environment is returned.
    """
    if DBX_WORKSPACE_URL is None:
        return None

    env = DBX_WORKSPACE_URL_ENV_MAP.get(DBX_WORKSPACE_URL)
    assert (
        env is not None
    ), f'Unexpected databricks workspace URL "{DBX_WORKSPACE_URL}" is not associated to an environment.'
    return env


def __init_module__() -> None:
    """Initializes any runtime-valued constants of the module."""
    global DBX_WORKSPACE_URL, DBX_ENVIRONMENT, RUNS_IN_DATABRICKS
    DBX_WORKSPACE_URL = _get_dbx_workspace_url()
    DBX_ENVIRONMENT = _get_dbx_environment()
    RUNS_IN_DATABRICKS = DBX_WORKSPACE_URL is not None


# This has to be called before using any of the runtime constants in other utility functions below!
__init_module__()
