[build-system]
requires = ["setuptools~=75.0.0"]
build-backend = "setuptools.build_meta"

# General project metadata
[project]
name = "dbxlib"
description = "A library providing common functionality for working in Databricks."
dynamic = ["version", "readme"]
requires-python = ">=3.10"
dependencies = [
    "colorlog~=6.9.0",
]

# Metadata dynamically loaded during build time
[tool.setuptools.dynamic]
version = { file = "VERSION" }
readme = { file = ["README.md"], content-type = "text/markdown" }

[project.optional-dependencies]
dbx = [
    "databricks-connect~=14.3.0;python_version=='3.10'",
    "databricks-connect~=15.4.0;python_version=='3.11'",
    "databricks-sdk~=0.32.0"
]
spark = ["pyspark~=3.5.0", "delta-spark==3.2.0"]

# coverage.py configuration
[tool.coverage.run]
branch = true
command_line = "-m pytest tests/"
source = ["src"]
omit = [
    "__init__.py",
]

[tool.coverage.report]
format = "text"
show_missing = true
