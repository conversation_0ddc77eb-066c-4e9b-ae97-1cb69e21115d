"""Unit tests for the dbxlib.tables.operations module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Generator
from unittest.mock import MagicMock, patch

import pytest
from dbxlib import FullSchemaName, FullTableName
from dbxlib.tables.operations import append, drop, merge, merge_or_overwrite, overwrite, table_exists, table_has_schema
from pyspark.sql import DataFrame
from pyspark.sql.types import IntegerType, StringType, StructField, StructType

TABLE = FullTableName(FullSchemaName("test_catalog", "test_schema"), "test_table")


@pytest.fixture(scope="function")
def mock_dataframe() -> MagicMock:
    """Fixture to create a mock DataFrame."""
    df = MagicMock(spec=DataFrame)
    df.count.return_value = 10  # Mock count method
    return df


@pytest.fixture
def mock_spark() -> Generator[MagicMock, None, None]:
    """Fixture to create a mock Spark session."""
    spark = MagicMock()
    with patch("pyspark.sql.SparkSession.Builder.getOrCreate", return_value=spark):
        yield spark


# Test merge function


@patch("dbxlib.tables.operations.DeltaTable")
def test_merge(mock_delta_table: MagicMock, mock_dataframe: MagicMock) -> None:
    """Test the merge function."""
    merge(mock_dataframe, TABLE, "source.id = target.id")
    mock_delta_table.forName.return_value.alias.return_value.merge.assert_called_once()


# Test overwrite function


def test_overwrite(mock_dataframe: MagicMock) -> None:
    """Test the overwrite function."""
    overwrite(mock_dataframe, TABLE, overwrite_schema=False)

    mock_write = mock_dataframe.write

    mock_write.option.assert_called_with("mergeSchema", "true")
    mock_write = mock_write.option.return_value

    mock_write.saveAsTable.assert_called_once_with(str(TABLE), format="delta", mode="overwrite")


def test_overwrite_with_schema(mock_dataframe: MagicMock) -> None:
    """Test the overwrite function with schema overwrite."""
    overwrite(mock_dataframe, TABLE, overwrite_schema=True)

    mock_write = mock_dataframe.write

    mock_write.option.assert_called_with("overwriteSchema", "true")
    mock_write = mock_write.option.return_value

    mock_write.saveAsTable.assert_called_once_with(str(TABLE), format="delta", mode="overwrite")


# Test append function


def test_append(mock_dataframe: MagicMock) -> None:
    """Test the append function."""
    append(mock_dataframe, TABLE)

    mock_write = mock_dataframe.write

    mock_write.option.assert_called_with("mergeSchema", "true")
    mock_write = mock_write.option.return_value

    mock_write.saveAsTable.assert_called_once_with(str(TABLE), format="delta", mode="append")


# Test merge_or_overwrite function


@pytest.mark.parametrize("table_exists, schema_matches", [(True, True), (True, False), (False, False)])
def test_merge_or_overwrite(table_exists: bool, schema_matches: bool, mock_dataframe: MagicMock) -> None:
    """Test merge_or_overwrite for different scenarios."""
    merge_condition = "source.id = target.id"

    with (
        patch("dbxlib.tables.operations.table_exists", return_value=table_exists),
        patch("dbxlib.tables.operations.table_has_schema", return_value=schema_matches),
        patch("dbxlib.tables.operations.merge") as mock_merge,
        patch("dbxlib.tables.operations.overwrite") as mock_overwrite,
    ):
        merge_or_overwrite(mock_dataframe, TABLE, merge_condition)

        if table_exists:
            if schema_matches:
                mock_merge.assert_called_once_with(mock_dataframe, TABLE, merge_condition)
            else:
                mock_overwrite.assert_called_once_with(mock_dataframe, TABLE, overwrite_schema=True)
        else:
            mock_overwrite.assert_called_once_with(mock_dataframe, TABLE)


# Test table exists function


def test_table_exists_existing_table(mock_spark: MagicMock) -> None:
    """Test table_exists function for existing table.."""
    mock_spark.catalog.tableExists.return_value = True

    assert table_exists(TABLE)

    mock_spark.catalog.tableExists.assert_called_once_with(str(TABLE))


def test_table_exists_non_existing_table(mock_spark: MagicMock) -> None:
    """Test table_exists function for non-existing table."""
    mock_spark.catalog.tableExists.return_value = False

    assert not table_exists(TABLE)

    mock_spark.catalog.tableExists.assert_called_once_with(str(TABLE))


# Test table_has_schema function


def test_table_has_schema_matching(mock_spark: MagicMock) -> None:
    """Test table_has_schema function for matching schema."""
    schema = StructType([StructField("id", IntegerType())])
    mock_spark.read.table.return_value.schema = schema

    assert table_has_schema(TABLE, StructType([StructField("id", IntegerType())]))

    mock_spark.read.table.assert_called_once_with(str(TABLE))


def test_table_has_schema_non_matching(mock_spark: MagicMock) -> None:
    """Test table_has_schema function for non-matching schema."""
    schema = StructType([StructField("id", IntegerType())])  # Define a mock schema
    mock_spark.read.table.return_value.schema = schema

    assert not table_has_schema(TABLE, StructType([StructField("id", StringType())]))
    mock_spark.read.table.assert_called_once_with(str(TABLE))


# Test drop function


def test_drop_existing_table(mock_spark: MagicMock) -> None:
    """Test drop function for existing table."""
    mock_spark.catalog.tableExists.return_value = True

    drop(TABLE)

    mock_spark.sql.assert_called_once_with(f"DROP TABLE {TABLE}")


def test_drop_non_existing_table(mock_spark: MagicMock) -> None:
    """Test drop function for non-existing table."""
    mock_spark.catalog.tableExists.return_value = False

    drop(TABLE)

    mock_spark.sql.assert_not_called()  # Ensure DROP TABLE was not called
