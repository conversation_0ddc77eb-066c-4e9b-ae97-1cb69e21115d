"""Unit tests for the dbxlib.tables.metadata module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from unittest.mock import MagicMock, patch

import pytest
from dbxlib import FullSchemaName, FullTableName
from dbxlib.tables.metadata import (
    ISO8601_DURATION_CPATTERN,
    RESPONSIBLE_DOMAIN_CPATTERN,
    RESPONSIBLE_TEAM_CPATTERN,
    TAG_NAME_CPATTERN,
    TAG_VALUE_CPATTERN,
    update_columns_description,
    update_columns_tags,
    update_table_description,
    update_table_metadata,
    update_table_tags,
)

# Regex tests


def test_responsible_domain_pattern() -> None:
    """Test the RESPONSIBLE_DOMAIN_CPATTERN regex."""
    valid_domains: list[str] = ["Example-domain", "Example domain", "Example123"]
    invalid_domains: list[str] = [
        "<EMAIL>",
        "example_domain",
        "example#domain",
        "example",
        "1Example",
        "example123",
    ]

    for domain in valid_domains:
        assert RESPONSIBLE_DOMAIN_CPATTERN.match(domain) is not None

    for domain in invalid_domains:
        assert RESPONSIBLE_DOMAIN_CPATTERN.match(domain) is None


def test_responsible_team_pattern() -> None:
    """Test the RESPONSIBLE_TEAM_CPATTERN regex."""
    valid_teams: list[str] = ["Team-alpha", "Team beta", "Team123"]
    invalid_teams: list[str] = ["team@alpha", "team_alpha", "team#beta", "team", "1Team", "team123"]

    for team in valid_teams:
        assert RESPONSIBLE_TEAM_CPATTERN.match(team) is not None

    for team in invalid_teams:
        assert RESPONSIBLE_TEAM_CPATTERN.match(team) is None


def test_tag_name_pattern() -> None:
    """Test the TAG_NAME_CPATTERN regex."""
    valid_tags: list[str] = ["Tag-name", "tag.name", "tag name", "tag_name", "tag name 123"]
    invalid_tags: list[str] = ["1tag@name", "tag#name", "tag name!", "123tag", "_tagname"]

    for tag in valid_tags:
        assert TAG_NAME_CPATTERN.match(tag) is not None

    for tag in invalid_tags:
        assert TAG_NAME_CPATTERN.match(tag) is None


def test_tag_value_pattern() -> None:
    """Test the TAG_VALUE_CPATTERN regex."""
    valid_values = ["value", "value with spaces", "123", "", " "]

    for value in valid_values:
        assert TAG_VALUE_CPATTERN.match(value) is not None


def test_ISO8601_duration_pattern() -> None:
    """Test the ISO8601_DURATION_CPATTERN regex."""
    valid_durations = [
        "P1Y",
        "P1M",
        "P1D",
        "PT1H",
        "PT1M",
        "PT1S",
        "P1Y2M3DT4H5M6S",
        "P1Y2M",
        "P1DT2H",
        "PT3M4S",
        "none",
    ]
    invalid_durations = [
        "1Y",
        "P1Y2M3D4H5M6S",
        "P1Y2M3D4H5M6S7",
        "P1Y2M3D4H5M6S7T",
        "P1Y2M3D4H5M6S7T8H",
        "PT",
        "P",
        "P1Y2M3D4H5M6S7T8H9M",
        "P1Dnone",
        "-P1D",
    ]

    for duration in valid_durations:
        assert ISO8601_DURATION_CPATTERN.match(duration) is not None

    for duration in invalid_durations:
        assert ISO8601_DURATION_CPATTERN.match(duration) is None


TABLE = FullTableName(FullSchemaName("test_catalog", "test_schema"), "test_table")

# Test update_table_description


@patch("dbxlib.tables.metadata.get_table_description")
@patch("dbxlib.tables.metadata.set_table_description")
def test_update_table_description_changes(
    mock_set_table_description: MagicMock, mock_get_table_description: MagicMock
) -> None:
    """Test the update_table_description function for changes."""
    mock_get_table_description.return_value = "Old description"

    update_table_description(
        table=TABLE,
        description="New description",
        force_update=False,
    )

    # Check that set_table_description was called with the new description
    mock_set_table_description.assert_called_once_with(TABLE, "New description")


@patch("dbxlib.tables.metadata.get_table_description")
@patch("dbxlib.tables.metadata.set_table_description")
def test_update_table_description_no_changes(
    mock_set_table_description: MagicMock, mock_get_table_description: MagicMock
) -> None:
    """Test the update_table_description function when no changes are needed."""
    mock_get_table_description.return_value = "Same description with 'quotes'"

    update_table_description(
        table=TABLE,
        description="Same description with 'quotes'",
        force_update=False,
    )

    # Check that set_table_description was not called
    mock_set_table_description.assert_not_called()


@patch("dbxlib.tables.metadata.get_table_description")
@patch("dbxlib.tables.metadata.set_table_description")
def test_update_table_description_no_changes_force(
    mock_set_table_description: MagicMock, mock_get_table_description: MagicMock
) -> None:
    """Test the update_table_description function with force_update set to True."""
    mock_get_table_description.return_value = "Same description"

    update_table_description(
        table=TABLE,
        description="Same description",
        force_update=True,
    )

    # Check that set_table_description was called due to force_update
    mock_set_table_description.assert_called_once_with(TABLE, "Same description")


# Test update_table_tags


@patch("dbxlib.tables.metadata.get_table_tags")
@patch("dbxlib.tables.metadata.set_table_tags")
def test_update_table_tags_changes(mock_set_table_tags: MagicMock, mock_get_table_tags: MagicMock) -> None:
    """Test the update_table_tags function for changes."""
    mock_get_table_tags.return_value = {
        "responsible_domain": "Old Domain",
        "responsible_team": "Old Team",
        "refresh_interval": "PT1H",
    }

    update_table_tags(
        table=TABLE,
        responsible_team="New Team",
        responsible_domain="New Domain",
        refresh_interval="PT1H",
        additional_tags={"tag1": "value1"},
        force_update=False,
    )

    # Check that set_table_tags was called with the new tags
    mock_set_table_tags.assert_called_once_with(
        TABLE,
        {
            "responsible_domain": "New Domain",
            "responsible_team": "New Team",
            "refresh_interval": "PT1H",
            "tag1": "value1",
        },
    )


@patch("dbxlib.tables.metadata.get_table_tags")
@patch("dbxlib.tables.metadata.set_table_tags")
def test_update_table_tags_no_changes(mock_set_table_tags: MagicMock, mock_get_table_tags: MagicMock) -> None:
    """Test the update_table_tags function when no changes are needed."""
    mock_get_table_tags.return_value = {
        "responsible_domain": "New Domain",
        "responsible_team": "New Team",
        "refresh_interval": "PT1H",
    }

    update_table_tags(
        table=TABLE,
        responsible_team="New Team",
        responsible_domain="New Domain",
        refresh_interval="PT1H",
        force_update=False,
    )

    # Check that set_table_tags was not called
    mock_set_table_tags.assert_not_called()


@patch("dbxlib.tables.metadata.get_table_tags")
@patch("dbxlib.tables.metadata.set_table_tags")
def test_update_table_tags_no_changes_force(mock_set_table_tags: MagicMock, mock_get_table_tags: MagicMock) -> None:
    """Test the update_table_tags function with force_update set to True."""
    mock_get_table_tags.return_value = {
        "responsible_domain": "New Domain",
        "responsible_team": "New Team",
        "refresh_interval": "PT1H",
    }

    update_table_tags(
        table=TABLE,
        responsible_team="New Team",
        responsible_domain="New Domain",
        refresh_interval="PT1H",
        force_update=True,
    )

    # Check that set_table_tags was called due to force_update
    mock_set_table_tags.assert_called_once_with(
        TABLE, {"responsible_domain": "New Domain", "responsible_team": "New Team", "refresh_interval": "PT1H"}
    )


# Test update_columns_description


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_all_column_descriptions")
@patch("dbxlib.tables.metadata.set_column_description")
def test_update_columns_description_changes(
    mock_set_column_description: MagicMock,
    mock_get_all_column_descriptions: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_description function for changes."""
    mock_get_all_column_descriptions.return_value = {"col1": "Old description", "col2": "Another old description"}

    descriptions = {"col1": "New description for col1", "col2": "New description for col2"}

    update_columns_description(
        table=TABLE,
        descriptions=descriptions,
        force_update=False,
    )

    # Check that set_column_description was called for col1 and col2
    mock_set_column_description.assert_any_call(TABLE, "col1", "New description for col1")
    mock_set_column_description.assert_any_call(TABLE, "col2", "New description for col2")


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_all_column_descriptions")
@patch("dbxlib.tables.metadata.set_column_description")
def test_update_columns_description_no_changes(
    mock_set_column_description: MagicMock,
    mock_get_all_column_descriptions: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_description function when no changes are needed."""
    # All columns have the same description
    mock_get_all_column_descriptions.return_value = {"col1": "New description", "col2": "New description with 'quotes'"}

    descriptions = {"col1": "New description", "col2": "New description with 'quotes'"}

    update_columns_description(
        table=TABLE,
        descriptions=descriptions,
        force_update=False,
    )

    # Check that set_column_description was not called
    mock_set_column_description.assert_not_called()


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_all_column_descriptions")
@patch("dbxlib.tables.metadata.set_column_description")
def test_update_columns_description_no_changes_force(
    mock_set_column_description: MagicMock,
    mock_get_all_column_descriptions: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_description function with force_update set to True."""
    mock_get_all_column_descriptions.return_value = {"col1": "Old description", "col2": "Old description"}

    descriptions = {"col1": "New description for col1", "col2": "New description for col2"}

    update_columns_description(
        table=TABLE,
        descriptions=descriptions,
        force_update=True,
    )

    # Check that set_column_description was called for col1 and col2 due to force_update
    mock_set_column_description.assert_any_call(TABLE, "col1", "New description for col1")
    mock_set_column_description.assert_any_call(TABLE, "col2", "New description for col2")


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_all_column_descriptions")
@patch("dbxlib.tables.metadata.set_column_description")
def test_update_columns_description_partial_updates(
    mock_set_column_description: MagicMock,
    mock_get_all_column_descriptions: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_description function with partial updates."""
    mock_get_all_column_descriptions.return_value = {"col1": "Old description", "col2": "New description for col2"}

    descriptions = {"col1": "New description for col1", "col2": "New description for col2"}

    update_columns_description(
        table=TABLE,
        descriptions=descriptions,
        force_update=False,
    )

    # Check that set_column_description was called for col1 but not for col2
    mock_set_column_description.assert_called_once_with(TABLE, "col1", "New description for col1")


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1"])
@patch("dbxlib.tables.metadata.get_all_column_descriptions")
@patch("dbxlib.tables.metadata.get_table_type", return_value="VIEW")
@patch("dbxlib.tables.metadata._sql_ignore_metadata_changed_exception")
def test_update_columns_description_for_view_no_dbr(
    mock_sql_ignore_metadata_changed_exception: MagicMock,
    mock_get_table_type: MagicMock,
    mock_get_all_column_descriptions: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_description function with force_update set to True and a view."""
    mock_get_all_column_descriptions.return_value = {"col1": "Old description"}

    descriptions = {"col1": "New description for col1"}
    with pytest.raises(ValueError, match="Script is running outside of Databricks. Cannot determine DBR version."):
        update_columns_description(
            table=TABLE,
            descriptions=descriptions,
            force_update=True,
        )


@patch("os.environ.get", return_value="16.1")
@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1"])
@patch("dbxlib.tables.metadata.get_all_column_descriptions")
@patch("dbxlib.tables.metadata.get_table_type", return_value="VIEW")
@patch("dbxlib.tables.metadata._sql_ignore_metadata_changed_exception")
def test_update_columns_description_for_view_valid_dbr(
    mock_sql_ignore_metadata_changed_exception: MagicMock,
    mock_get_table_type: MagicMock,
    mock_get_all_column_descriptions: MagicMock,
    mock_get_table_columns: MagicMock,
    mock_os_environ: MagicMock,
) -> None:
    """Test the update_columns_description function with force_update set to True and a view."""
    mock_get_all_column_descriptions.return_value = {"col1": "Old description"}

    descriptions = {"col1": "New description for col1"}

    update_columns_description(
        table=TABLE,
        descriptions=descriptions,
        force_update=True,
    )
    mock_sql_ignore_metadata_changed_exception.assert_called_once_with(
        f"COMMENT ON COLUMN {TABLE}.col1 IS 'New description for col1'"
    )


@patch("os.environ.get", return_value="15.4")
@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1"])
@patch("dbxlib.tables.metadata.get_all_column_descriptions")
@patch("dbxlib.tables.metadata.get_table_type", return_value="VIEW")
@patch("dbxlib.tables.metadata._sql_ignore_metadata_changed_exception")
def test_update_columns_description_for_view_valid_invalid_dbr(
    mock_sql_ignore_metadata_changed_exception: MagicMock,
    mock_get_table_type: MagicMock,
    mock_get_all_column_descriptions: MagicMock,
    mock_get_table_columns: MagicMock,
    mock_os_environ: MagicMock,
) -> None:
    """Test the update_columns_description function with force_update set to True and a view."""
    mock_get_all_column_descriptions.return_value = {"col1": "Old description"}
    descriptions = {"col1": "New description for col1"}

    with pytest.raises(
        ValueError, match="Need at least DBR of 16.1 to alter columns in views. Used DBR version is: 15.4."
    ):
        update_columns_description(
            table=TABLE,
            descriptions=descriptions,
            force_update=True,
        )


# Test update_columns_tags


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_timestamp_columns")
@patch("dbxlib.tables.metadata.get_all_column_tags")
@patch("dbxlib.tables.metadata.set_column_tags")
def test_update_columns_tags_changes(
    mock_set_column_tags: MagicMock,
    mock_get_all_column_tags: MagicMock,
    mock_get_timestamp_columns: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_tags function."""
    # Mock the return values of the functions
    mock_get_timestamp_columns.return_value = ["col1", "col2"]
    mock_get_all_column_tags.return_value = {"col1": {"timesystem": "UTC"}, "col2": {}}

    # Test case 1: Update tags with no force_update
    update_columns_tags(
        table=TABLE,
        timesystems={"col1": "PST"},
        units={"col1": "seconds"},
        additional_tags={"col2": {"description": "A column"}},
        force_update=False,
    )

    # Check that set_column_tags was called for col1 and col2
    mock_set_column_tags.assert_any_call(TABLE, "col1", {"timesystem": "PST", "unit": "seconds"})
    mock_set_column_tags.assert_any_call(TABLE, "col2", {"timesystem": "UTC", "description": "A column"})


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_timestamp_columns")
@patch("dbxlib.tables.metadata.get_all_column_tags")
@patch("dbxlib.tables.metadata.set_column_tags")
def test_update_columns_tags_no_changes(
    mock_set_column_tags: MagicMock,
    mock_get_all_column_tags: MagicMock,
    mock_get_timestamp_columns: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_tags function."""
    # Mock the return values of the functions
    mock_get_timestamp_columns.return_value = ["col1", "col2"]
    mock_get_all_column_tags.return_value = {
        "col1": {"timesystem": "PST", "unit": "seconds"},
        "col2": {"timesystem": "UTC"},
    }

    update_columns_tags(
        table=TABLE,
        timesystems={"col1": "PST"},
        units={"col1": "seconds"},
        force_update=False,
    )

    # Check that set_column_tags was not called for col1
    mock_set_column_tags.assert_not_called()


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_timestamp_columns")
@patch("dbxlib.tables.metadata.get_all_column_tags")
@patch("dbxlib.tables.metadata.set_column_tags")
def test_update_columns_tags_no_changes_force(
    mock_set_column_tags: MagicMock,
    mock_get_all_column_tags: MagicMock,
    mock_get_timestamp_columns: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_tags function."""
    # Mock the return values of the functions
    mock_get_timestamp_columns.return_value = ["col1", "col2"]
    mock_get_all_column_tags.return_value = {
        "col1": {"timesystem": "PST", "unit": "seconds"},
        "col2": {"timesystem": "UTC"},
    }

    update_columns_tags(
        table=TABLE,
        timesystems={"col1": "CST"},
        units={"col1": "seconds"},
        force_update=True,
    )

    # Check that set_column_tags was called for col1 due to force_update
    mock_set_column_tags.assert_any_call(TABLE, "col1", {"timesystem": "CST", "unit": "seconds"})


@patch("dbxlib.tables.metadata.get_table_columns", return_value=["col1", "col2"])
@patch("dbxlib.tables.metadata.get_timestamp_columns")
@patch("dbxlib.tables.metadata.get_all_column_tags")
@patch("dbxlib.tables.metadata.set_column_tags")
def test_update_columns_tags_defaults(
    mock_set_column_tags: MagicMock,
    mock_get_all_column_tags: MagicMock,
    mock_get_timestamp_columns: MagicMock,
    mock_get_table_columns: MagicMock,
) -> None:
    """Test the update_columns_tags function."""
    # Mock the return values of the functions
    mock_get_timestamp_columns.return_value = ["col1", "col2"]
    mock_get_all_column_tags.return_value = {
        "col1": {"timesystem": "PST", "unit": "seconds"},
        "col2": {},
    }

    update_columns_tags(table=TABLE, force_update=False)

    # Check that set_column_tags was called for col1 and col2 due to defaults
    mock_set_column_tags.assert_any_call(TABLE, "col1", {"timesystem": "UTC"})
    mock_set_column_tags.assert_any_call(TABLE, "col2", {"timesystem": "UTC"})


# Test update_table_metadata


@patch("dbxlib.tables.metadata.update_columns_tags")
@patch("dbxlib.tables.metadata.update_columns_description")
@patch("dbxlib.tables.metadata.update_table_tags")
@patch("dbxlib.tables.metadata.update_table_description")
def test_update_table_metadata(
    mock_update_table_description: MagicMock,
    mock_update_table_tags: MagicMock,
    mock_update_columns_description: MagicMock,
    mock_update_columns_tags: MagicMock,
) -> None:
    """Test the update_table_metadata function."""
    update_table_metadata(
        table=TABLE,
        description="New description",
        responsible_team="New Team",
        responsible_domain="New Domain",
        refresh_interval="PT1H",
        column_descriptions={"col1": "New description for col1", "col2": "New description for col2"},
        column_timesystems={"col1": "PST"},
        column_units={"col1": "seconds"},
        force_update=False,
    )

    # Check that all the update functions were called
    mock_update_table_description.assert_called_once_with(TABLE, "New description", False)
    mock_update_table_tags.assert_called_once_with(TABLE, "New Team", "New Domain", "PT1H", None, False)
    mock_update_columns_description.assert_called_once_with(
        TABLE, {"col1": "New description for col1", "col2": "New description for col2"}, False
    )
    mock_update_columns_tags.assert_called_once_with(TABLE, {"col1": "PST"}, {"col1": "seconds"}, None, False)
