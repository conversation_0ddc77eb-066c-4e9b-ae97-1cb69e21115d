"""Tests for the logging module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import itertools
import logging
from datetime import datetime, timezone
from types import SimpleNamespace
from unittest.mock import MagicMock, patch

import pytest
from dbxlib import FullSchemaName, FullTableName
from dbxlib._logging import (
    DEFAULT_LOG_TABLE,
    DatabricksContextFilter,
    DatabricksHandler,
    _get_instance_id,
    _level_filter_maker,
    setup_databricks_logging,
)
from pyspark.sql import SparkSession

# Test _get_instance_id function


def test_get_instance_id() -> None:
    """Test the _get_instance_id function."""
    assert _get_instance_id(None).startswith("uuid-")
    assert _get_instance_id("foo").startswith("run-foo")


# Test _level_filter_maker function


def test_level_filter_maker() -> None:
    """Test the _level_filter_maker function."""
    filter_fn = _level_filter_maker("WARNING")

    assert filter_fn(SimpleNamespace(levelno=logging.INFO))  # type: ignore[arg-type]
    assert filter_fn(SimpleNamespace(levelno=logging.WARNING))  # type: ignore[arg-type]
    assert not filter_fn(SimpleNamespace(levelno=logging.ERROR))  # type: ignore[arg-type]


# Test setup_databricks_logging function


@pytest.mark.parametrize(
    "spark, table, run_id, log_level, max_queue_size, enabled_loggers",
    [
        *itertools.product(
            [None, MagicMock()],
            [None, "test_table"],
            [None, "test_run_id"],
            [None, "WARNING"],
            [None, 42],
            [None, ["test.logging"]],
        ),
        (None, None, None, None, None, ["test.logging", "test.logging2"]),
    ],
)
@pytest.mark.usefixtures("spark")
@patch("pyspark.sql.SparkSession.Builder.getOrCreate", return_value=MagicMock())
@patch("logging.config.dictConfig")
def test_setup_databricks_logging(  # noqa: C901
    dictConfig_mock: MagicMock,
    getOrCreate_mock: MagicMock,
    spark: SparkSession | None,
    table: str | None,
    run_id: str | None,
    log_level: str | None,
    max_queue_size: int | None,
    enabled_loggers: list[str] | None,
) -> None:
    """Test the setup_databricks_logging function."""
    # Construct the kwargs for the function
    kwargs = {"schema_full": FullSchemaName("test_catalog", "test_schema"), "context": "Test context"}
    if spark is not None:
        kwargs["spark"] = spark
    if table is not None:
        kwargs["table"] = table
    if run_id is not None:
        kwargs["run_id"] = run_id
    if log_level is not None:
        kwargs["log_level"] = log_level
    if max_queue_size is not None:
        kwargs["max_queue_size"] = max_queue_size
    if enabled_loggers is not None:
        kwargs["enabled_loggers"] = enabled_loggers

    setup_databricks_logging(**kwargs)  # type: ignore[arg-type]

    assert dictConfig_mock.call_count == 1
    config = dictConfig_mock.call_args[0][0]

    dbx_context_filter = config["filters"]["databricks_context"]
    assert dbx_context_filter["context"] == "Test context"
    assert dbx_context_filter["run_id"] == (run_id if run_id else None)

    dbx_config = config["handlers"]["databricks"]
    assert dbx_config["schema_full"] == FullSchemaName("test_catalog", "test_schema")
    assert dbx_config["spark"] is (spark if spark else getOrCreate_mock.return_value)
    assert dbx_config["table"] == (table if table else DEFAULT_LOG_TABLE)
    assert dbx_config["max_queue_size"] == (max_queue_size if max_queue_size else 5)
    assert dbx_config["level"] == (log_level if log_level else "INFO")

    stdout_config = config["handlers"]["stdout"]
    assert stdout_config["level"] == (log_level if log_level else "INFO")

    for i, (logger_name, logger) in enumerate(config["loggers"].items()):
        assert logger["level"] == "DEBUG"
        if i == 0:
            assert logger_name == "__main__"
        elif i == 1:
            assert logger_name == "dbxlib"
        elif i == 2:
            enabled_logger = enabled_loggers[0]  # type: ignore[index]
            assert logger_name == enabled_logger


# Test DatabricksContextFilter class


def test_databricks_context_filter_with_run_id() -> None:
    """Test DatabricksContextFilter with a provided run ID."""
    run_id = "12345"
    context = "test_context"
    filter_instance = DatabricksContextFilter(run_id, context)

    assert filter_instance.instance_id == "run-12345"
    assert filter_instance.context == context

    # Create a mock log record for testing
    record = MagicMock()
    assert filter_instance.filter(record) is True
    assert record.instance_id == "run-12345"
    assert record.context == context


def test_databricks_context_filter_without_run_id() -> None:
    """Test DatabricksContextFilter without a provided run ID."""
    context = "test_context"
    filter_instance = DatabricksContextFilter(None, context)

    assert filter_instance.instance_id.startswith("uuid-")
    assert filter_instance.context == context

    # Create a mock log record for testing
    record = MagicMock()
    assert filter_instance.filter(record) is True
    assert record.instance_id.startswith("uuid-")
    assert record.context == context


# Test DatabricksHandler class


@pytest.fixture(scope="module")
def log_record() -> logging.LogRecord:
    """Create a mock log record for testing."""
    record = MagicMock(spec=logging.LogRecord)
    record.instance_id = "run-12345"
    record.levelname = "INFO"
    record.getMessage = MagicMock(return_value="This is a test log message.")
    record.relativeCreated = 5000  # 5 seconds
    record.created = datetime.now(tz=timezone.utc).timestamp()  # Current time in seconds
    record.context = "test_context"
    return record


@pytest.mark.parametrize(
    "table, max_queue_size",
    itertools.product(
        [None, "test_table"],
        [None, 42],
    ),
)
@pytest.mark.usefixtures("spark")
def test_databricks_handler_init(spark: SparkSession, table: str | None, max_queue_size: int | None) -> None:
    """Test initialization of DatabricksHandler."""
    kwargs = {
        "spark": spark,
        "schema_full": FullSchemaName("test_catalog", "test_schema"),
    }
    if table is not None:
        kwargs["table"] = table
    if max_queue_size is not None:
        kwargs["max_queue_size"] = max_queue_size
    handler = DatabricksHandler(**kwargs)  # type: ignore[arg-type]

    assert handler.spark == spark
    assert handler.table_full == FullTableName(
        FullSchemaName("test_catalog", "test_schema"),
        table if table else DEFAULT_LOG_TABLE,
    )
    assert handler._max_queue_size == (max_queue_size if max_queue_size else 5)
    assert handler._log_queue == []


@pytest.mark.usefixtures("spark")
def test_databricks_handler_emit(spark: SparkSession, log_record: logging.LogRecord) -> None:
    """Test emit method of DatabricksHandler."""
    handler = DatabricksHandler(spark, FullSchemaName("test_catalog", "test_schema"), max_queue_size=2)

    with patch.object(handler, "flush") as flush_mock, patch.object(handler, "handleError") as handle_error_mock:
        # Test that log records are added to the queue but not flushed if max_queue_size is not reached
        handler.emit(log_record)
        flush_mock.assert_not_called()
        assert len(handler._log_queue) == 1
        assert handler._log_queue[0] == handler._row_from_record(log_record)

        # Test that the queue is flushed when max_queue_size is reached
        handler.emit(log_record)
        flush_mock.assert_called_once()

        # Test that errors are handled
        flush_mock.side_effect = Exception
        handler.emit(log_record)
        handle_error_mock.assert_called_once()


@pytest.mark.usefixtures("spark")
def test_flush_empty_queue(spark: SparkSession) -> None:
    """Test flush method when the log queue is empty."""
    handler = DatabricksHandler(spark, FullSchemaName("test_catalog", "test_schema"), max_queue_size=2)

    # Call the flush method
    handler.flush()

    # Assert that the log queue is still empty
    assert len(handler._log_queue) == 0


@pytest.mark.usefixtures("spark")
def test_flush_with_logs(spark: SparkSession, log_record: logging.LogRecord) -> None:
    """Test flush method when the log queue has entries."""
    handler = DatabricksHandler(spark, FullSchemaName("test_catalog", "test_schema"), max_queue_size=2)
    # Set table_full to single-part name
    handler.table_full = "test_table"  # type: ignore[assignment]
    # Add a log record to the queue
    handler.emit(log_record)

    with patch("pyspark.sql.DataFrameWriter.saveAsTable") as save_as_table_mock:
        # Call the flush method
        handler.flush()

        # Assert that the log records are written to the table
        save_as_table_mock.assert_called_once_with("test_table", format="delta", mode="append")

    # Assert that the log queue is cleared
    assert len(handler._log_queue) == 0


@pytest.mark.usefixtures("spark")
def test_databricks_handler_close(spark: SparkSession) -> None:
    """Test close method of DatabricksHandler."""
    handler = DatabricksHandler(spark, FullSchemaName("test_catalog", "test_schema"))

    with (
        patch.object(handler, "flush") as flush_mock,
        patch("logging.Handler.close") as super_close_mock,
    ):
        handler.close()
        flush_mock.assert_called_once()
        super_close_mock.assert_called_once()


@pytest.mark.usefixtures("spark")
def test_databricks_handler_close_with_exception(spark: SparkSession) -> None:
    """Test close method of DatabricksHandler with an exception."""
    handler = DatabricksHandler(spark, FullSchemaName("test_catalog", "test_schema"))

    with (
        patch.object(handler, "flush", side_effect=Exception) as flush_mock,
        patch("logging.Handler.close") as super_close_mock,
    ):
        with pytest.raises(Exception):
            handler.close()
        flush_mock.assert_called_once()
        super_close_mock.assert_called_once()


@pytest.mark.usefixtures("spark")
def test_databricks_handler_row_from_record(spark: SparkSession, log_record: logging.LogRecord) -> None:
    """Test _row_from_record method of DatabricksHandler."""
    handler = DatabricksHandler(spark, FullSchemaName("test_catalog", "test_schema"))

    # Call the _row_from_record method
    row = handler._row_from_record(log_record)

    # Assertions
    assert row["instance_id"] == "run-12345"
    assert row["level"] == "INFO"
    assert row["context"] == "test_context"
    assert row["message"] == "This is a test log message."
    assert row["reltime"] == 5.0  # 5000 ms converted to seconds
    assert row["timestamp"] == datetime.fromtimestamp(log_record.created, tz=timezone.utc)
