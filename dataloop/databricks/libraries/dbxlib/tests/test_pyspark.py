"""Unit tests for the pyspark module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from dbxlib.pyspark import compare_array_types, compare_data_types, compare_struct_fields, compare_struct_types
from pyspark.sql.types import ArrayType, IntegerType, StringType, StructField, StructType

# Test compare_struct_fields


def test_compare_struct_fields_equal() -> None:
    """Test comparing equal struct fields."""
    field1 = StructField("name", StringType(), True)
    field2 = StructField("name", StringType(), True)
    assert compare_struct_fields(field1, field2)


def test_compare_struct_fields_different_name() -> None:
    """Test comparing struct fields with different names."""
    field1 = StructField("name", StringType(), True)
    field2 = StructField("age", IntegerType(), True)
    assert not compare_struct_fields(field1, field2)


def test_compare_struct_fields_different_nullable() -> None:
    """Test comparing struct fields with different nullable flags."""
    field1 = StructField("name", StringType(), True)
    field2 = StructField("name", StringType(), False)
    assert not compare_struct_fields(field1, field2)


def test_compare_struct_fields_ignore_nullable() -> None:
    """Test comparing struct fields ignoring nullable flag."""
    field1 = StructField("name", StringType(), True)
    field2 = StructField("name", StringType(), False)
    assert compare_struct_fields(field1, field2, ignore_nullable=True)


def test_compare_struct_fields_different_metadata() -> None:
    """Test comparing struct fields with different metadata."""
    field1 = StructField("name", StringType(), True, metadata={"key": "value"})
    field2 = StructField("name", StringType(), True, metadata={"key": "different_value"})
    assert not compare_struct_fields(field1, field2, ignore_metadata=False)


def test_compare_struct_fields_ignore_metadata() -> None:
    """Test comparing struct fields ignoring metadata."""
    field1 = StructField("name", StringType(), True, metadata={"key": "value"})
    field2 = StructField("name", StringType(), True, metadata={"key": "different_value"})
    assert compare_struct_fields(field1, field2, ignore_metadata=True)


# Test compare_struct_types


def test_compare_struct_types_equal() -> None:
    """Test comparing equal struct types."""
    st1 = StructType([StructField("name", StringType(), True)])
    st2 = StructType([StructField("name", StringType(), True)])
    assert compare_struct_types(st1, st2)


def test_compare_struct_types_different_length() -> None:
    """Test comparing struct types of different lengths."""
    st1 = StructType([StructField("name", StringType(), True)])
    st2 = StructType([StructField("name", StringType(), True), StructField("age", IntegerType(), True)])
    assert not compare_struct_types(st1, st2)


def test_compare_struct_types_different_fields() -> None:
    """Test comparing struct types with different fields."""
    st1 = StructType([StructField("name", StringType(), True)])
    st2 = StructType([StructField("age", IntegerType(), True)])
    assert not compare_struct_types(st1, st2)


# Test compare_array_types


def test_compare_array_types_equal() -> None:
    """Test comparing equal array types."""
    at1 = ArrayType(StringType(), containsNull=True)
    at2 = ArrayType(StringType(), containsNull=True)
    assert compare_array_types(at1, at2)


def test_compare_array_types_different_contains_null() -> None:
    """Test comparing array types with different containsNull flags."""
    at1 = ArrayType(StringType(), containsNull=True)
    at2 = ArrayType(StringType(), containsNull=False)
    assert not compare_array_types(at1, at2)


# Test compare_data_types


def test_compare_data_types_equal() -> None:
    """Test comparing equal data types."""
    dt1 = StringType()
    dt2 = StringType()
    assert compare_data_types(dt1, dt2)


def test_compare_data_types_different_types() -> None:
    """Test comparing different data types."""
    dt1 = StringType()
    dt2 = IntegerType()
    assert not compare_data_types(dt1, dt2)


def test_compare_data_types_array_types() -> None:
    """Test comparing array data types."""
    dt1 = ArrayType(StringType(), containsNull=True)
    dt2 = ArrayType(StringType(), containsNull=True)
    assert compare_data_types(dt1, dt2)


def test_compare_data_types_struct_types() -> None:
    """Test comparing struct data types."""
    dt1 = StructType([StructField("name", StringType(), True)])
    dt2 = StructType([StructField("name", StringType(), True)])
    assert compare_data_types(dt1, dt2)
