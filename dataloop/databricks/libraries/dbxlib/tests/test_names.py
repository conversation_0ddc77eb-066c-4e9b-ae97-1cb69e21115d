"""Unit tests for the names module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import itertools
from unittest.mock import patch

import dbxlib.databricks
import pytest
from dbxlib.names import FullSchemaName, FullTableName

# Test FullSchemaName class


def test_full_schema_name_init_without_env() -> None:
    """Test FullSchemaName initialization without environment."""
    # Generic catalog
    schema_name = FullSchemaName("test_catalog", "test_schema", True)
    assert schema_name.catalog == "test_catalog"
    assert schema_name.schema == "test_schema"

    # Non-generic catalog
    schema_name = FullSchemaName("test_catalog", "test_schema", False)
    assert schema_name.catalog == "test_catalog"
    assert schema_name.schema == "test_schema"


def test_full_schema_name_init_with_env() -> None:
    """Test FullSchemaName initialization with environment."""
    with patch.object(dbxlib.databricks, "DBX_ENVIRONMENT", dbxlib.databricks.DatabricksEnvironment.QA):
        # Generic catalog with mapping
        schema_name = FullSchemaName("gold", "test_schema", True)
        assert schema_name.catalog == "gold_qa"
        assert schema_name.schema == "test_schema"

        # Non-generic catalog
        schema_name = FullSchemaName("test_catalog", "test_schema", False)
        assert schema_name.catalog == "test_catalog"
        assert schema_name.schema == "test_schema"

        # Generic catalog without mapping
        schema_name = FullSchemaName("test_catalog", "test_schema", True)
        assert schema_name.catalog == "test_catalog"
        assert schema_name.schema == "test_schema"


@pytest.mark.parametrize(
    "catalog, catalog_needs_quotes, schema, schema_needs_quotes",
    [
        (item[0], "_" not in item[0], item[1], "_" not in item[1])
        for item in itertools.product(["test_catalog", "test catalog"], ["test_schema", "test schema"])
    ],
)
def test_full_schema_name_as_str(
    catalog: str, catalog_needs_quotes: bool, schema: str, schema_needs_quotes: bool
) -> None:
    """Test FullSchemaName.as_str method."""
    schema_name = FullSchemaName(catalog, schema)

    # Construct expected result
    if catalog_needs_quotes:
        expected_result = f"`{catalog}`"
    else:
        expected_result = catalog
    expected_result += "."
    if schema_needs_quotes:
        expected_result += f"`{schema}`"
    else:
        expected_result += schema

    assert schema_name.as_str() == expected_result


@pytest.mark.parametrize(
    "catalog, schema",
    itertools.product(["test_catalog", "test catalog"], ["test_schema", "test schema"]),
)
def test_full_schema_name_as_str_unquoted(catalog: str, schema: str) -> None:
    """Test FullSchemaName.as_str_unquoted method."""
    schema_name = FullSchemaName(catalog, schema)

    # Construct expected result
    expected_result = f"{catalog}.{schema}"

    assert schema_name.as_str_unquoted() == expected_result


def test_full_schema_name_as_str_snake_case() -> None:
    """Test FullSchemaName.as_str_snake_case method."""
    schema_name = FullSchemaName("test_catalog", "test sch.ema")
    assert schema_name.as_str_snake_case() == "test_catalog_test_sch_ema"


def test_full_schema_name_eq() -> None:
    """Test FullSchemaName.__eq__ method."""
    schema_name1 = FullSchemaName("test_catalog", "test_schema", True)
    schema_name2 = FullSchemaName("test_catalog", "test_schema", False)
    schema_name3 = FullSchemaName("other_catalog", "test_schema")
    assert schema_name1 == schema_name2
    assert schema_name1 != schema_name3
    assert schema_name1 != "test_catalog.test_schema"


@pytest.mark.parametrize(
    "new_catalog, new_schema",
    itertools.product(["other_catalog", None], ["other_schema", None]),
)
def test_full_schema_name_copy_with(new_catalog: str | None, new_schema: str | None) -> None:
    """Test FullSchemaName.copy_with method."""
    schema_name = FullSchemaName("test_catalog", "test_schema")
    new_schema_name = schema_name.copy_with(catalog=new_catalog, schema=new_schema)

    assert new_schema_name.catalog == (new_catalog or "test_catalog")
    assert new_schema_name.schema == (new_schema or "test_schema")


def test_full_schema_name_parse() -> None:
    """Test FullSchemaName.parse method."""
    schema_name = FullSchemaName.parse("test_catalog.test_schema")
    assert schema_name.catalog == "test_catalog"
    assert schema_name.schema == "test_schema"

    with pytest.raises(ValueError):
        FullSchemaName.parse("invalid_schema_name")


# Test FullTableName class


@pytest.fixture(scope="function")
def schema_name() -> FullSchemaName:
    """Return a FullSchemaName instance."""
    return FullSchemaName("test_catalog", "test_schema")


def test_full_table_name_init(schema_name: FullSchemaName) -> None:
    """Test FullTableName initialization."""
    table_name = FullTableName(schema_name, "test_table")
    assert table_name.catalog == schema_name.catalog
    assert table_name.schema == schema_name.schema
    assert table_name.table == "test_table"
    assert table_name._schema_full == schema_name


@pytest.mark.parametrize(
    "catalog, catalog_needs_quotes, schema, schema_needs_quotes, table, table_needs_quotes",
    [
        (item[0], "_" not in item[0], item[1], "_" not in item[1], item[2], "_" not in item[2])
        for item in itertools.product(
            ["test_catalog", "test catalog"],
            ["test_schema", "test schema"],
            ["test_table", "test table"],
        )
    ],
)
def test_full_table_name_as_str(
    catalog: str,
    catalog_needs_quotes: bool,
    schema: str,
    schema_needs_quotes: bool,
    table: str,
    table_needs_quotes: bool,
) -> None:
    """Test FullTableName.as_str method."""
    table_name = FullTableName(FullSchemaName(catalog, schema, False), table)

    # Construct expected result
    if catalog_needs_quotes:
        expected_result = f"`{catalog}`"
    else:
        expected_result = catalog
    expected_result += "."
    if schema_needs_quotes:
        expected_result += f"`{schema}`"
    else:
        expected_result += schema
    expected_result += "."
    if table_needs_quotes:
        expected_result += f"`{table}`"
    else:
        expected_result += table

    assert table_name.as_str() == expected_result


@pytest.mark.parametrize(
    "catalog, schema, table",
    itertools.product(["test_catalog", "test catalog"], ["test_schema", "test schema"], ["test_table", "test table"]),
)
def test_full_table_name_as_str_unquoted(catalog: str, schema: str, table: str) -> None:
    """Test FullTableName.as_str_unquoted method."""
    table_name = FullTableName(FullSchemaName(catalog, schema, False), table)

    # Construct expected result
    expected_result = f"{catalog}.{schema}.{table}"

    assert table_name.as_str_unquoted() == expected_result


def test_full_table_name_as_str_snake_case() -> None:
    """Test FullTableName.as_str_snake_case method."""
    table_name = FullTableName(FullSchemaName("test_catalog", "test schema", False), "test.ta-ble")
    assert table_name.as_str_snake_case() == "test_catalog_test_schema_test_ta_ble"


def test_full_table_name_eq(schema_name: FullSchemaName) -> None:
    """Test FullTableName.__eq__ method."""
    table_name1 = FullTableName(schema_name, "test_table")
    table_name2 = FullTableName(schema_name, "other_table")
    table_name3 = FullTableName(schema_name, "test_table")
    assert table_name1 != table_name2
    assert table_name1 == table_name3
    assert table_name1 != "test_catalog.test_schema.test_table"


@pytest.mark.parametrize(
    "new_catalog, new_schema, new_table",
    itertools.product(["other_catalog", None], ["other_schema", None], ["other_table", None]),
)
def test_full_table_name_copy_with(new_catalog: str | None, new_schema: str | None, new_table: str | None) -> None:
    """Test FullTableName.copy_with method."""
    table_name = FullTableName(FullSchemaName("test_catalog", "test_schema", False), "test_table")
    new_table_name = table_name.copy_with(catalog=new_catalog, schema=new_schema, table=new_table)

    assert new_table_name.catalog == (new_catalog or "test_catalog")
    assert new_table_name.schema == (new_schema or "test_schema")
    assert new_table_name.table == (new_table or "test_table")


def test_full_table_name_parse() -> None:
    """Test FullTableName.parse method."""
    table_name = FullTableName.parse("test_catalog.test_schema.test_table")
    assert table_name.catalog == "test_catalog"
    assert table_name.schema == "test_schema"
    assert table_name.table == "test_table"

    with pytest.raises(ValueError):
        FullTableName.parse("invalid_table_name")
