"""Unit tests for the dbxlib.databricks module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Generator
from unittest.mock import MagicMock, patch

import dbxlib.databricks as dbx
import pytest


@pytest.fixture
def mock_spark() -> Generator[MagicMock, None, None]:
    """Fixture to create a mock Spark session."""
    spark = MagicMock()
    with patch("pyspark.sql.SparkSession.Builder.getOrCreate", return_value=spark):
        yield spark


# Test get_dbx_env_catalog function


def test_get_dbx_env_catalog_dev() -> None:
    """Test get_dbx_env_catalog for the DEV environment."""
    with patch.object(dbx, "DBX_ENVIRONMENT", dbx.DatabricksEnvironment.DEV):
        assert dbx.get_dbx_env_catalog("bronze") == "bronze_dev"
        assert dbx.get_dbx_env_catalog("silver") == "silver_dev"
        assert dbx.get_dbx_env_catalog("gold") == "gold_dev"
        assert dbx.get_dbx_env_catalog("unknown") == "unknown"


def test_get_dbx_env_catalog_qa() -> None:
    """Test get_dbx_env_catalog for the QA environment."""
    with patch.object(dbx, "DBX_ENVIRONMENT", dbx.DatabricksEnvironment.QA):
        assert dbx.get_dbx_env_catalog("bronze") == "bronze_qa"
        assert dbx.get_dbx_env_catalog("silver") == "silver_qa"
        assert dbx.get_dbx_env_catalog("gold") == "gold_qa"
        assert dbx.get_dbx_env_catalog("unknown") == "unknown"


def test_get_dbx_env_catalog_prod() -> None:
    """Test get_dbx_env_catalog for the PROD environment."""
    with patch.object(dbx, "DBX_ENVIRONMENT", dbx.DatabricksEnvironment.PROD):
        assert dbx.get_dbx_env_catalog("bronze") == "bronze"
        assert dbx.get_dbx_env_catalog("silver") == "silver"
        assert dbx.get_dbx_env_catalog("gold") == "gold"
        assert dbx.get_dbx_env_catalog("unknown") == "unknown"


def test_get_dbx_env_catalog_no_environment() -> None:
    """Test get_dbx_env_catalog when no environment is set."""
    with patch.object(dbx, "DBX_ENVIRONMENT", None):
        assert dbx.get_dbx_env_catalog("bronze") == "bronze"
        assert dbx.get_dbx_env_catalog("silver") == "silver"
        assert dbx.get_dbx_env_catalog("gold") == "gold"
        assert dbx.get_dbx_env_catalog("unknown") == "unknown"


# Test _get_dbx_environment function


def test_get_dbx_environment_no_workspace_url() -> None:
    """Test _get_dbx_environment when no workspace URL is set."""
    with patch.object(dbx, "DBX_WORKSPACE_URL", None):
        assert dbx._get_dbx_environment() is None


def test_get_dbx_environment_invalid_url() -> None:
    """Test _get_dbx_environment with an invalid workspace URL."""
    with patch.object(dbx, "DBX_WORKSPACE_URL", "invalid_url"):
        with pytest.raises(AssertionError):
            dbx._get_dbx_environment()


def test_get_dbx_environment_success() -> None:
    """Test _get_dbx_environment for an existing environment."""
    with patch.object(dbx, "DBX_WORKSPACE_URL", "adb-505904006080631.11.azuredatabricks.net"):
        assert dbx._get_dbx_environment() == dbx.DatabricksEnvironment.DEV


# Test _get_dbx_workspace_url function


def test_get_dbx_workspace_url_no_databricks_runtime(monkeypatch: pytest.MonkeyPatch) -> None:
    """Test _get_dbx_workspace_url when not running in Databricks."""
    monkeypatch.delenv("DATABRICKS_RUNTIME_VERSION", raising=False)
    assert dbx._get_dbx_workspace_url() is None


def test_get_dbx_workspace_url(monkeypatch: pytest.MonkeyPatch, mock_spark: MagicMock) -> None:
    """Test _get_dbx_workspace_url when running in Databricks."""
    monkeypatch.setenv("DATABRICKS_RUNTIME_VERSION", "SomeVersion")
    mock_spark.conf.get.return_value = "some_url"
    assert dbx._get_dbx_workspace_url() == "some_url"


@pytest.mark.parametrize("debugging, testing", [(True, True), (True, False), (False, True), (False, False)])
def test_get_dbx_workspace_url_debugging(monkeypatch: pytest.MonkeyPatch, debugging: bool, testing: bool) -> None:
    """Test _get_dbx_workspace_url when debugging."""
    if debugging:
        monkeypatch.setenv("DEBUGPY_RUNNING", "true")
    if not testing:  # Env var is already set when executing tests
        monkeypatch.delenv("PYTEST_VERSION")

    monkeypatch.setattr(dbx, "DBX_WORKSPACE_URL_ENV_MAP", {"some_url": dbx.DatabricksEnvironment.DEV})

    result = dbx._get_dbx_workspace_url()
    if debugging and not testing:
        assert result == "some_url"
    else:
        assert result is None


# Test module initialization


@patch.object(dbx, "_get_dbx_workspace_url", return_value="adb-505904006080631.11.azuredatabricks.net")
def test_init_module(mock_get_url: MagicMock) -> None:
    """Test module initialization to ensure constants are set correctly."""
    dbx.__init_module__()

    assert dbx.DBX_WORKSPACE_URL == "adb-505904006080631.11.azuredatabricks.net"
    assert dbx.DBX_ENVIRONMENT == dbx.DatabricksEnvironment.DEV
    assert dbx.RUNS_IN_DATABRICKS is True
