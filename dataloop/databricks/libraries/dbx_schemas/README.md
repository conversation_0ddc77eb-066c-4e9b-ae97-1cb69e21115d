# dbx_schemas - Databricks Public Schemas Library

A library providing public schemas for tables in Databricks using pyspark's
park.sql.functions.StructType and spark.sql.functions.StructField.

The goal of this package is to let teams publish their table schemas to enable data contract tests
and enable re-use of schema fragments to ensure a consistent usage of naming conventions and
tagging.

## Install from wheel

```bash
pip install ./dist/dbx_schemas-X.X.X-py3-none-any.whl[EXTRAS]
```

You need to replace `EXTRAS` with a comma separated list of extra dependencies you want to install:

- `dbx`: Usage in Databricks or with Databricks Connect
- `spark`: Local usage of Spark only, not all features from `dbx` supported

NOTE: The dbx_schemas package does NOT use the `dbx` optional dependencies for now to keep the package lean.

## Install locally in editable mode

```bash
pip install -e .[EXTRAS]
```

For `EXTRAS` see section above.

## Build as wheel

```bash
pip install build wheel
python3 -m build --wheel
```

The resulting `.whl` file can be found in the `dist/` folder.

## Install on DBX job cluster

Add the following configuration to your job YAML. Dependencies are
added at task level.

```bash
libraries:
    - pypi:
        package: dbx_schemas[EXTRAS]==<VERSION>
```

## Define Table Schemas
Package names follow strictly the unity catalog naming conventions for our production data.
If your table is located in `gold.drive_time_series.your_table` then you need to place it in the
`gold.drive_time_series` package and re-export it in the `__init__.py` file.
"Private" Tables do not have to be re-exported, but should also be placed in the correct package.
Example:
```
src/dbx_schemas/gold/drive_time_series/__init__.py              :  re-exports MULTISENSOR_FRAMES_SCHEMA from .mutlisensor_frames
src/dbx_schemas/gold/drive_time_series/multisensor_frames.py    :  contains MULTISENSOR_FRAMES_SCHEMA
src/dbx_schemas/gold/drive_time_series/_private_tables.py       :  contains _YOUR_PRIVATE_TABLE_SCHEMA
```

this enables the following imports:
```python
from dbx_schema.gold.drive_time_series import MULTISENSOR_FRAMES_SCHEMA
```

Other parties are encourage to write tests against the schema to ensure that the fields they expect are present and
have the correct data types. These tests however should live in the consuming package and not in the schema package.
Meaning:
If you have a table gold.your_schema.your_table which is based on silver.mdd.file_entries, you should add a test to
`tests/dbx_schemas/gold/your_schema/test_your_table.py`.
