"""Utility functions for schema managing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import datetime

from dbx_schemas.common.functions import Table
from pyspark.sql import SparkSession


def apply_column_tags(*, spark_session: SparkSession, uc_catalog: str, table: Table) -> None:
    """Apply all tags defines in the schema metadata['tags'] to the table columns."""
    for field in table.fields:
        if "tags" in field.metadata:
            all_tags = ", ".join([f"'{k}'='{v}'" for k, v in field.metadata["tags"].items()])
            if all_tags:
                spark_session.sql(
                    f"ALTER TABLE {table.full_table_name(uc_catalog)} ALTER COLUMN {field.name} SET TAGS ({all_tags})"
                )


def apply_column_descriptions(*, spark_session: SparkSession, uc_catalog: str, table: Table) -> None:
    """Alter the table columns to have the correct description."""
    for field in table.fields:
        if "comment" in field.metadata:
            comment = field.metadata["comment"]
            spark_session.sql(
                f"ALTER TABLE {table.full_table_name(uc_catalog)} ALTER COLUMN {field.name} COMMENT '{comment}'"
            )


def _timedelta_isoformat(td: datetime.timedelta) -> str:
    """ISO 8601 encoding for Python timedelta object."""
    if td < datetime.timedelta(0):
        raise ValueError("we do not support negative timedeltas for table refresh times.")
    if td.microseconds:
        raise ValueError("we do not support sub-second timedeltas for table refresh times.")

    minutes, seconds = divmod(td.seconds, 60)
    hours, minutes = divmod(minutes, 60)
    result = "P"
    if td.days:
        result += f"{td.days}D"
    if td.seconds or not td.days:
        result += "T"
    if hours:
        result += f"{hours}H"
    if minutes:
        result += f"{minutes}M"
    if seconds or len(result) == 2:  # edge case for 0 second duration
        result += f"{seconds}S"
    return result


def apply_table_tags(*, spark_session: SparkSession, uc_catalog: str, table: Table) -> None:
    """Apply the table tags to the table."""
    tags = table.tags
    query = f"""
        ALTER TABLE {table.full_table_name(uc_catalog)} SET TAGS (
            'responsible_domain'='{tags.responsible_domain}',
            'responsible_team'='{tags.responsible_team}'"""
    if tags.refresh_interval:
        query += f""",
            'refresh_interval'='{_timedelta_isoformat(tags.refresh_interval)}'"""
    query += """
        )"""
    spark_session.sql(query)


def apply_all_tags_and_comments(*, spark_session: SparkSession, uc_catalog: str, table: Table) -> None:
    """Apply all tags and descriptions to the table."""
    apply_table_tags(spark_session=spark_session, uc_catalog=uc_catalog, table=table)
    apply_column_descriptions(spark_session=spark_session, uc_catalog=uc_catalog, table=table)
    apply_column_tags(spark_session=spark_session, uc_catalog=uc_catalog, table=table)
