"""Multisensor Frames Schema."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from datetime import timedelta
from typing import Final

from dbx_schemas.common import RECORDING_STARTED_AT_FIELD, VIN_FIELD
from dbx_schemas.common.files import file_hash_field, file_url_field
from dbx_schemas.common.functions import Table, field
from dbx_schemas.common.tags import ANALYTICS_PLATFORM
from dbx_schemas.common.time_series import emitted_at_field
from pyspark.sql.types import IntegerType, StringType, StructField


def context(camera_name: str) -> StructField:
    """Context Field."""
    return field(
        name=f"{camera_name}_context",
        data_type=StringType(),
        nullable=True,
        description=f"Context of the {camera_name} camera",
    )


def rectification_warper_version(camera_name: str) -> StructField:
    """Rectification warper version field."""
    return field(
        name=f"{camera_name}_rectification_warper_version",
        data_type=StringType(),
        nullable=True,
        description=f"Rectification warper version for {camera_name} frame",
    )


def rectification_calstorage_uid(camera_name: str) -> StructField:
    """Rectification calstorage UID field."""
    return field(
        name=f"{camera_name}_rectification_calstorage_uid",
        data_type=StringType(),
        nullable=True,
        description=f"Rectification calstorage UID for {camera_name} frame",
    )


def extractor_version(camera_name: str) -> StructField:
    """Extractor version field."""
    return field(
        name=f"{camera_name}_extractor_version",
        data_type=StringType(),
        nullable=True,
        description=f"Extractor version for {camera_name} frame",
    )


def frame_number(camera_name: str) -> StructField:
    """Frame number field."""
    return field(
        name=f"{camera_name}_frame_number",
        data_type=IntegerType(),
        nullable=True,
        description=f"Frame number of the {camera_name} frame",
    )


EMITTED_AT_MIN_FIELD = emitted_at_field(name="emitted_at_min", description="Minimum time of the emitted frames")
EMITTED_AT_MAX_FIELD = emitted_at_field(name="emitted_at_max", description="Maximum time of the emitted frames")


def camera_fields_fragment(camera: str) -> list[StructField]:
    """Fields for a camera in the multisensor_frames_camera table."""
    return [
        # not all cameras have to exist, so fields can be null
        emitted_at_field(name=f"{camera}_emitted_at", nullable=True),
        file_hash_field(
            col_name=f"{camera}_distorted_rgb_file_hash",
            description=f"SHA2-256 hash of the distorted rgb file for the {camera} camera",
            nullable=True,
        ),
        file_url_field(
            col_name=f"{camera}_distorted_rgb_file_url",
            description=f"Blob Storage URL for the distorted rgb file for the {camera} camera",
            nullable=True,
        ),
        file_hash_field(
            col_name=f"{camera}_label_image_file_hash",
            description=f"SHA2-256 hash of the label image file for the {camera} camera",
            nullable=True,
        ),
        file_url_field(
            col_name=f"{camera}_label_image_file_url",
            description=f"Blob Storage URL for the label image file for the {camera} camera",
            nullable=True,
        ),
        frame_number(camera),
        context(camera),
        rectification_warper_version(camera),
        rectification_calstorage_uid(camera),
        extractor_version(camera),
    ]


_CAMERAS: Final = ["fc1", "tv_front", "tv_left", "tv_right", "tv_rear", "rcw"]
MULTISENSOR_FRAMES_CAMERA_SCHEMA: Final = Table(
    category="gold",
    uc_schema="drive_time_series",
    name="multisensor_frames_camera",
    tags=ANALYTICS_PLATFORM.with_refresh_interval(timedelta(days=1)),
    fields=[
        VIN_FIELD,
        RECORDING_STARTED_AT_FIELD,
        EMITTED_AT_MIN_FIELD,
        EMITTED_AT_MAX_FIELD,
    ]
    + sum([camera_fields_fragment(cam) for cam in _CAMERAS], []),
    cluster_by=["vin", "emitted_at_min"],
)
