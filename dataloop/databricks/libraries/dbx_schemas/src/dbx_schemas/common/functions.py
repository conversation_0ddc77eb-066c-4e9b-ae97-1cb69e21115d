"""Common functions for the DBX schemas."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import datetime
from dataclasses import dataclass
from typing import Iterable, List, Literal

from pyspark.sql.types import DataType, StructField, StructType


def _comment(value: str) -> dict[str, str]:
    """Produces the dict entries for a comment for the metadata field."""
    return {"comment": value}


def _tags(value: dict[str, str]) -> dict[str, dict[str, str]]:
    """Produces the dict entries for tags for the metadata field."""
    return {"tags": value}


TIME_DOMAIN = Literal["TAI", "UTC"]
CATALOG_CATEGORY = Literal["bronze", "silver", "gold"]


@dataclass(frozen=True)
class TableTags:
    """TableTags defines the tags for a table."""

    responsible_domain: str
    responsible_team: str
    refresh_interval: datetime.timedelta | None = None
    """refresh interval is optional as some tables are not regularly updated."""

    def with_refresh_interval(self, refresh_interval: datetime.timedelta) -> "TableTags":
        """Create a clone with a different refresh interval."""
        return TableTags(
            responsible_domain=self.responsible_domain,
            responsible_team=self.responsible_team,
            refresh_interval=refresh_interval,
        )


class Table:
    """Table defines a table including the table tags and its schema."""

    def __init__(
        self,
        category: CATALOG_CATEGORY,
        uc_schema: str,
        name: str,
        tags: TableTags,
        fields: List[StructField] = [],
        cluster_by: None | List[str] = None,
    ):
        """Initializes a Table object.

        Args:
            category: The category of the table (bronze, silver, gold). This is not automatically the uc_catalog as
                there can be dev or qa catalogs for those categories.
            uc_schema: the unity catalog schema name. Not the DDL schema/structure of the table.
            name: the unity catalog table name.
            tags: Tags for the table.
            fields: the schema of the table, i.e. a list of columns.
            cluster_by: the fields to cluster the table by for better performance.

        """
        field_names = [field.name for field in fields]
        unique_names = set(field_names)
        assert len(unique_names) == len(
            field_names
        ), f"Duplicate fields in table {name} schema {fields}: {[i for i in unique_names if field_names.count(i) > 1]}"

        self.fields = fields
        self.category = category
        self.uc_schema = uc_schema
        self.name = name
        self.tags = tags
        self.cluster_by = cluster_by
        if cluster_by:
            for field in cluster_by:
                assert field in self.field_names(), f"Field {field} is not in the schema, cannot cluster_by it."

    def __len__(self) -> int:
        """Return the number of fields in the schema."""
        return len(self.fields)

    def __getitem__(self, key: str | int) -> StructField:
        """Return the field with the given name."""
        if isinstance(key, str):
            for field in self.fields:
                if field.name == key:
                    return field
            raise KeyError(f"Field {key} not found in the schema.")
        elif isinstance(key, int):
            try:
                return self.fields[key]
            except IndexError:
                raise IndexError("Table index out of range")
        raise KeyError(f"Field {key} not found in the schema.")

    def field_names(self) -> Iterable[str]:
        """Return the field names in the schema."""
        return (field.name for field in self.fields)

    def as_spark_schema(self) -> StructType:
        """Return the schema as a spark schema."""
        return StructType(self.fields)

    def with_table_name(self, name: str) -> "Table":
        """Create a clone with a different name."""
        return Table(
            category=self.category,
            uc_schema=self.uc_schema,
            name=name,
            tags=self.tags,
            fields=self.fields,
            cluster_by=self.cluster_by,
        )

    def full_table_name(self, catalog: str) -> str:
        """Return a full table name with catalog, schema and table name in '.' separated form."""
        return f"{catalog}.{self.uc_schema}.{self.name}"


def field(
    *, name: str, data_type: DataType, nullable: bool = True, description: str, time_domain: None | TIME_DOMAIN = None
) -> StructField:
    """Define a single field/column in a schema."""
    return StructField(
        name,
        data_type,
        nullable,
        metadata=_comment(description) | _tags({"time_domain": time_domain} if time_domain else {}),
    )
