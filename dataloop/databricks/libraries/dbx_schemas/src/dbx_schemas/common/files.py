"""Common file related fields."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import IntegerType, StringType, StructField

from .functions import field


def file_hash_field(
    *, col_name: str, description: str = "SHA2-256 hash of the file", nullable: bool = False
) -> StructField:
    """File Hash field.

    Args:
        col_name: column name for the field. Must end with _hash.
        description: description of the field.
        nullable: whether the field is nullable.
    """
    assert col_name.endswith("_hash")
    return field(name=col_name, data_type=StringType(), nullable=nullable, description=description)


def file_url_field(*, col_name: str, description: str = "Blob Storage URL.", nullable: bool = True) -> StructField:
    """File Url field.

    Args:
        col_name: column name for the field. Must end with _file_url.
        description: description of the field.
        nullable: whether the field is nullable.
    """
    assert col_name.endswith("file_url"), "all file url columns must end with file_url"
    return field(name=col_name, data_type=StringType(), nullable=nullable, description=description)


FILE_HASH_FIELD = file_hash_field(col_name="file_hash")

SPLIT_HASH_FIELD = file_hash_field(col_name="split_hash", description="Split hash (.scene file)")
STREAM_HASH_FIELD = file_hash_field(col_name="stream_hash", description="Stream hash (.avi file")
TDS_FILE_URL_FIELD = file_url_field(col_name="tds_file_url")
FILE_URL_FIELD = file_url_field(col_name="file_url")

FILE_SIZE_BYTES_FIELD = field(
    name="file_size_bytes", data_type=IntegerType(), description="Size of the file in bytes", nullable=False
)
