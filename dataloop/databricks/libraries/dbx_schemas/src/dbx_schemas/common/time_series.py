"""Common fields for time series data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import StringType, StructField, TimestampType

from .functions import field


def emitted_at_field(
    *, name: str, description: str = "Point in time the record was emitted", nullable: bool = False
) -> StructField:
    """Emitted At Field in TAI Time Domain, by default not null."""
    return field(name=name, data_type=TimestampType(), nullable=nullable, description=description, time_domain="TAI")


VIN_FIELD = field(name="vin", data_type=StringType(), nullable=False, description="Vehicle Identification Number")
"""Vehicle Identification Number"""

RECORDING_STARTED_AT_FIELD = field(
    name="recording_started_at",
    data_type=TimestampType(),
    nullable=False,
    description="Point in time the recording started",
    time_domain="TAI",
)
"""Point in time the recording started in TAI time domain"""

EMITTED_AT_FIELD = emitted_at_field(name="emitted_at")
"""'Emitted at' in TAI time domain. Almost all data from vehicles is in TAI."""

STARTED_AT_FIELD = field(
    name="started_at",
    data_type=TimestampType(),
    nullable=False,
    description="Point in time the split/time-span started",
    time_domain="TAI",
)
"""Point in time the split/time-span started in TAI time domain"""

ENDED_AT_FIELD = field(
    name="ended_at",
    data_type=TimestampType(),
    nullable=True,
    description="Point in time the split/time-span ended",
    time_domain="TAI",
)
"""Point in time the split/time-span ended in TAI time domain. For legacy reasons nullable."""
