"""Databricks schemas module for common schema fragments.

These common schem fragments are mainained by the Analytics Platform Team.
Other data product specific fragments shall be placed relative to their tables e.g.
./src/dbx_schemas/gold/my_product/_fragments.py.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from .basic import CREATED_AT_FIELD, MODIFIED_AT_FIELD  # noqa: F401
from .files import (  # noqa: F401
    FILE_HASH_FIELD,
    FILE_SIZE_BYTES_FIELD,
    FILE_URL_FIELD,
    SPLIT_HASH_FIELD,
    STREAM_HASH_FIELD,
    TDS_FILE_URL_FIELD,
)
from .time_series import ENDED_AT_FIELD  # noqa: F401
from .time_series import EMITTED_AT_FIELD, RECORDING_STARTED_AT_FIELD, STARTED_AT_FIELD, VIN_FIELD  # noqa: F401
