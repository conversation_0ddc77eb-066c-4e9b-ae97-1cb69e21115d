"""Common basic fields."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import TimestampType

from .functions import field

CREATED_AT_FIELD = field(
    name="created_at",
    data_type=TimestampType(),
    nullable=False,
    description="Point in time the row was created",
    time_domain="UTC",
)
MODIFIED_AT_FIELD = field(
    name="modified_at",
    data_type=TimestampType(),
    nullable=False,
    description="Point in time the row was last updated",
    time_domain="UTC",
)
