"""Schema for the files_scenes table in the Ada Ontology."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from dbx_schemas.common import (
    CREATED_AT_FIELD,
    ENDED_AT_FIELD,
    FILE_HASH_FIELD,
    FILE_SIZE_BYTES_FIELD,
    FILE_URL_FIELD,
    RECORDING_STARTED_AT_FIELD,
    STARTED_AT_FIELD,
    VIN_FIELD,
)
from dbx_schemas.common.functions import Table
from dbx_schemas.common.tags import ANALYTICS_PLATFORM

FILES_SCENES_SCHEMA = Table(
    category="silver",
    uc_schema="ada_ontology",
    name="files_scenes",
    tags=ANALYTICS_PLATFORM,
    fields=[
        VIN_FIELD,
        RECORDING_STARTED_AT_FIELD,
        STARTED_AT_FIELD,
        ENDED_AT_FIELD,
        FILE_URL_FIELD,
        FILE_SIZE_BYTES_FIELD,
        FILE_HASH_FIELD,
        CREATED_AT_FIELD,
    ],
)
