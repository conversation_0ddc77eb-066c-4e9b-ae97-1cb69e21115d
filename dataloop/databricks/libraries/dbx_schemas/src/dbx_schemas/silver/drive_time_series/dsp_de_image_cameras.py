"""Stripped versions of the dsp_de_image namespace per camera for faster access."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from datetime import timedelta
from typing import Final

from dbx_schemas.common import CREATED_AT_FIELD, EMITTED_AT_FIELD, MODIFIED_AT_FIELD
from dbx_schemas.common.files import FILE_HASH_FIELD, SPLIT_HASH_FIELD, STREAM_HASH_FIELD
from dbx_schemas.common.functions import Table, field
from dbx_schemas.common.tags import ANALYTICS_PLATFORM
from pyspark.sql.types import IntegerType, StringType

DSP_DE_IMAGE_CAMERA_FRAGMENT: Final = [
    EMITTED_AT_FIELD,
    CREATED_AT_FIELD,
    MODIFIED_AT_FIELD,
    FILE_HASH_FIELD,
    SPLIT_HASH_FIELD,
    STREAM_HASH_FIELD,
    field(name="view", data_type=StringType(), nullable=False, description="View (e.g. Distorted, Label, Wide...)"),
    field(
        name="view_type",
        data_type=StringType(),
        nullable=False,
        description="View type (e.g. TRIFOCAL, BIFOCAL, ...)",
    ),
    field(name="frame_number", data_type=IntegerType(), nullable=False, description="Frame number"),
    field(name="context", data_type=StringType(), nullable=False, description="Context"),
    field(name="rectification__warper_version", data_type=StringType(), nullable=True, description="Warper version"),
    field(name="rectification__calstorage_uid", data_type=StringType(), nullable=True, description="Calstorage UID"),
    field(
        name="extractor_version",
        data_type=StringType(),
        nullable=True,
        description="Semantic version of the extractor (Zeno)",
    ),
]
"""Schema for all dsp_de_image_{camera} tables"""

DSP_DE_IMAGE_CAMERA_FC1_SCHEMA = Table(
    category="silver",
    uc_schema="drive_time_series",
    name="dsp_de_image_fc1",
    fields=DSP_DE_IMAGE_CAMERA_FRAGMENT,
    tags=ANALYTICS_PLATFORM.with_refresh_interval(timedelta(days=1)),
    cluster_by=["file_hash"],
)
DSP_DE_IMAGE_CAMERA_TV_FRONT_SCHEMA = DSP_DE_IMAGE_CAMERA_FC1_SCHEMA.with_table_name("dsp_de_image_tv_front")
DSP_DE_IMAGE_CAMERA_TV_LEFT_SCHEMA = DSP_DE_IMAGE_CAMERA_FC1_SCHEMA.with_table_name("dsp_de_image_tv_left")
DSP_DE_IMAGE_CAMERA_TV_RIGHT_SCHEMA = DSP_DE_IMAGE_CAMERA_FC1_SCHEMA.with_table_name("dsp_de_image_tv_right")
DSP_DE_IMAGE_CAMERA_TV_REAR_SCHEMA = DSP_DE_IMAGE_CAMERA_FC1_SCHEMA.with_table_name("dsp_de_image_tv_rear")
DSP_DE_IMAGE_CAMERA_RCW_SCHEMA = DSP_DE_IMAGE_CAMERA_FC1_SCHEMA.with_table_name("dsp_de_image_rcw")
