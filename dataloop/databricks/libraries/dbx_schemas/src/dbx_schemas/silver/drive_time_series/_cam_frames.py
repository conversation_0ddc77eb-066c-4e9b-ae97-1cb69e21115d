__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from datetime import timedelta

from dbx_schemas.common import EMITTED_AT_FIELD, RECORDING_STARTED_AT_FIELD, VIN_FIELD
from dbx_schemas.common.files import file_hash_field, file_url_field
from dbx_schemas.common.functions import Table, field
from dbx_schemas.common.tags import ANALYTICS_PLATFORM
from pyspark.sql.types import IntegerType, StringType

_CAM_FRAMES_FRAGMENT = [
    VIN_FIELD,
    RECORDING_STARTED_AT_FIELD,
    EMITTED_AT_FIELD,
    file_hash_field(col_name="distorted_rgb_file_hash", nullable=True),
    file_url_field(col_name="distorted_rgb_file_url", nullable=True),
    file_hash_field(col_name="label_image_file_hash", nullable=True),
    file_url_field(col_name="label_image_file_url", nullable=True),
    field(name="frame_number", data_type=IntegerType(), nullable=False, description="Frame number"),
    field(name="context", data_type=StringType(), nullable=False, description="Context"),
    field(
        name="rectification__warper_version",
        data_type=StringType(),
        nullable=True,
        description="Rectification warper version",
    ),
    field(
        name="rectification__calstorage_uid",
        data_type=StringType(),
        nullable=True,
        description="Rectification calstorage UID",
    ),
    field(name="extractor_version", data_type=StringType(), nullable=True, description="Extractor version"),
]

_CAM_FRAMES_FC1_SCHEMA = Table(
    category="silver",
    uc_schema="drive_time_series",
    name="cam_frames_fc1",
    tags=ANALYTICS_PLATFORM.with_refresh_interval(timedelta(days=1)),
    fields=_CAM_FRAMES_FRAGMENT,
    cluster_by=["vin", "emitted_at"],
)
_CAM_FRAMES_TV_FRONT_SCHEMA = _CAM_FRAMES_FC1_SCHEMA.with_table_name("cam_frames_tv_front")
_CAM_FRAMES_TV_LEFT_SCHEMA = _CAM_FRAMES_FC1_SCHEMA.with_table_name("cam_frames_tv_left")
_CAM_FRAMES_TV_REAR_SCHEMA = _CAM_FRAMES_FC1_SCHEMA.with_table_name("cam_frames_tv_rear")
_CAM_FRAMES_TV_RIGHT_SCHEMA = _CAM_FRAMES_FC1_SCHEMA.with_table_name("cam_frames_tv_right")
_CAM_FRAMES_RCW_SCHEMA = _CAM_FRAMES_FC1_SCHEMA.with_table_name("cam_frames_rcw")
