"""Files Images schemas for Drive Time Series Silver."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from datetime import timedelta

from dbx_schemas.common import (
    CREATED_AT_FIELD,
    EMITTED_AT_FIELD,
    FILE_HASH_FIELD,
    MODIFIED_AT_FIELD,
    RECORDING_STARTED_AT_FIELD,
    SPLIT_HASH_FIELD,
    STREAM_HASH_FIELD,
    TDS_FILE_URL_FIELD,
    VIN_FIELD,
)
from dbx_schemas.common.functions import Table, field
from dbx_schemas.common.tags import ANALYTICS_PLATFORM
from pyspark.sql.types import IntegerType, StringType

FILES_IMAGES_FRAGMENT = [
    VIN_FIELD,
    RECORDING_STARTED_AT_FIELD,
    FILE_HASH_FIELD,
    CREATED_AT_FIELD,
    MODIFIED_AT_FIELD,
    SPLIT_HASH_FIELD,
    STREAM_HASH_FIELD,
    field(name="view", data_type=StringType(), nullable=False, description="View (e.g. Distorted, Label, Wide...)"),
    field(
        name="view_type",
        data_type=StringType(),
        nullable=False,
        description="View type (e.g. TRIFOCAL, BIFOCAL, ...)",
    ),
    field(name="frame_number", data_type=IntegerType(), nullable=False, description="Frame number"),
    EMITTED_AT_FIELD,
    field(name="context", data_type=StringType(), nullable=False, description="Context"),
    field(name="rectification__warper_version", data_type=StringType(), nullable=True, description="Warper version"),
    field(name="rectification__calstorage_uid", data_type=StringType(), nullable=True, description="Calstorage UID"),
    field(
        name="extractor_version",
        data_type=StringType(),
        nullable=True,
        description="Semantic version of the extractor (Zeno)",
    ),
    TDS_FILE_URL_FIELD,
]

FILES_IMAGES_FC1_SCHEMA = Table(
    category="silver",
    uc_schema="drive_time_series",
    name="files_images_fc1",
    tags=ANALYTICS_PLATFORM.with_refresh_interval(timedelta(days=1)),
    fields=FILES_IMAGES_FRAGMENT,
    cluster_by=["vin", "emitted_at"],
)
FILES_IMAGES_TV_FRONT_SCHEMA = FILES_IMAGES_FC1_SCHEMA.with_table_name("files_images_tv_front")
FILES_IMAGES_TV_LEFT_SCHEMA = FILES_IMAGES_FC1_SCHEMA.with_table_name("files_images_tv_left")
FILES_IMAGES_TV_RIGHT_SCHEMA = FILES_IMAGES_FC1_SCHEMA.with_table_name("files_images_tv_right")
FILES_IMAGES_TV_REAR_SCHEMA = FILES_IMAGES_FC1_SCHEMA.with_table_name("files_images_tv_rear")
FILES_IMAGES_RCW_SCHEMA = FILES_IMAGES_FC1_SCHEMA.with_table_name("files_images_rcw")
