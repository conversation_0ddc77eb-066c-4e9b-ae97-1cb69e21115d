"""Private schemas for file_entries_{camera} tables. Only for analytics platform."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Final

from dbx_schemas.common.files import FILE_HASH_FIELD, TDS_FILE_URL_FIELD
from dbx_schemas.common.functions import Table
from dbx_schemas.common.tags import ANALYTICS_PLATFORM

_FILE_ENTRIES_CAMERA_FRAGMENT: Final = [
    FILE_HASH_FIELD,
    TDS_FILE_URL_FIELD,
]
"""Schema for all file_entries_{camera} tables"""

_FILE_ENTRIES_FC1_SCHEMA: Final = Table(
    category="silver",
    uc_schema="drive_time_series",
    name="_file_entries_fc1",
    tags=ANALYTICS_PLATFORM,
    fields=_FILE_ENTRIES_CAMERA_FRAGMENT,
    cluster_by=["file_hash"],
)
_FILE_ENTRIES_TV_FRONT_SCHEMA: Final = _FILE_ENTRIES_FC1_SCHEMA.with_table_name("_file_entries_tv_front")
_FILE_ENTRIES_TV_LEFT_SCHEMA: Final = _FILE_ENTRIES_FC1_SCHEMA.with_table_name("_file_entries_tv_left")
_FILE_ENTRIES_TV_RIGHT_SCHEMA: Final = _FILE_ENTRIES_FC1_SCHEMA.with_table_name("_file_entries_tv_right")
_FILE_ENTRIES_TV_REAR_SCHEMA: Final = _FILE_ENTRIES_FC1_SCHEMA.with_table_name("_file_entries_tv_rear")
_FILE_ENTRIES_RCW_SCHEMA: Final = _FILE_ENTRIES_FC1_SCHEMA.with_table_name("_file_entries_rcw")
