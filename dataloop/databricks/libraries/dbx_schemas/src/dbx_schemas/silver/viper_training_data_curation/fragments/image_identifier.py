"""Module containing the common image identifier schema to be shared for dbx dataset curation tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import (
    StringType,
    StructField,
    StructType,
)

IMAGE_IDENTIFIER_SCHEMA = StructType(
    [
        # image identifier
        StructField(
            "image_id",
            StringType(),
            True,
            metadata={"comment": "Unique identifier of an image composed of stream_file_name and frame_number"},
        ),
    ]
)
