"""Module containing the common metadata schema to be shared for dbx dataset curation tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import (
    FloatType,
    StringType,
    StructField,
    StructType,
)

METADATA_SCHEMA = StructType(
    [
        # Metadata
        StructField("project", StringType(), True, metadata={"comment": "Project name (e.g. ALLIANCE, JAPAN)"}),
        StructField(
            "time_of_day",
            StringType(),
            True,
            metadata={
                "comment": "Time of day calculated based on gps location and time (e.g. 'Night', 'Day', 'Dawn', 'Dusk')"
            },
        ),
        StructField("country_code", StringType(), True, metadata={"comment": "County code (ISO 3166-1 alpha-2)"}),
        StructField("gps_elevation", FloatType(), True, metadata={"comment": "GPS elevation"}),
        StructField("gps_latitude", FloatType(), True, metadata={"comment": "GPS latitude"}),
        StructField("gps_longitude", FloatType(), True, metadata={"comment": "GPS longitude"}),
    ]
)
