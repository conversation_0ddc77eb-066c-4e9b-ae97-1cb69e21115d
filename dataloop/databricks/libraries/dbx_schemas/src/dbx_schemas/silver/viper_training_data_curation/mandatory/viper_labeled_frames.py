"""Define common schema viper labeled frames tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbx_schemas.silver.viper_training_data_curation.fragments.image_configurations import IMAGE_CONFIGURATION_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.image_identifier import IMAGE_IDENTIFIER_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.label_metadata import LABEL_METADATA_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.metadata import METADATA_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.storage_info import STORAGE_INFO_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.stream_information import STREAM_INFORMATION_SCHEMA
from pyspark.sql.types import (
    StructType,
)

# Combine all fields from all common schemas
combined_fields = (
    IMAGE_CONFIGURATION_SCHEMA.fields
    + METADATA_SCHEMA.fields
    + STORAGE_INFO_SCHEMA.fields
    + LABEL_METADATA_SCHEMA.fields
    + IMAGE_IDENTIFIER_SCHEMA.fields
    + STREAM_INFORMATION_SCHEMA.fields
)

# Create a new StructType with the combined fields from all common schemas
VIPER_LABELED_FRAMES_MANDATORY_SCHEMA = StructType(combined_fields)
