"""Module containing the common mandatory schema for the training table in databricks shared by all feature teams."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from copy import deepcopy

from dbx_schemas.silver.viper_training_data_curation.mandatory.viper_training import VIPER_TRAINING_MANDATORY_SCHEMA
from pyspark.sql.types import StringType, StructField

# Add traffic sign specific fields to the mandatory schema without modifying the original schema
TS_TRAINING_SCHEMA = deepcopy(VIPER_TRAINING_MANDATORY_SCHEMA)
TS_TRAINING_SCHEMA = TS_TRAINING_SCHEMA.add(
    StructField(
        "comment",
        StringType(),
        True,
        metadata={
            "comment": "E.g. explanation why a sample got assigned to a certain split, oversampled, blacklisted, etc."
        },
    ),
)
