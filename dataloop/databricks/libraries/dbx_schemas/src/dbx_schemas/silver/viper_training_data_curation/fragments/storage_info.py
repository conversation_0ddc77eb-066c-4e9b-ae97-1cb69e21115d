"""Module containing the common storage info schema to be shared for dbx dataset curation tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import (
    StringType,
    StructField,
    StructType,
)

STORAGE_INFO_SCHEMA = StructType(
    [
        # Storage information (both label and image)
        StructField(
            "label_hash",
            StringType(),
            True,
            metadata={"comment": "SHA2-256bit of the label file the element belongs to"},
        ),
        StructField(
            "label_file_url",
            StringType(),
            True,
            metadata={"comment": "Reference to label file for downloading of latest active source in TDS"},
        ),
        StructField(
            "label_file_state",
            StringType(),
            True,
            metadata={
                "comment": "State of the latest active source of the label in TDS storage, i.e. Active or Deleted"
            },
        ),
        StructField("image_hash", StringType(), True, metadata={"comment": "SHA2-256bit of the image"}),
        StructField(
            "image_file_url",
            StringType(),
            True,
            metadata={"comment": "Reference to image file for downloading of latest active source in TDS"},
        ),
        StructField(
            "image_file_state",
            StringType(),
            True,
            metadata={
                "comment": "State of the latest active source of the image in TDS storage, i.e. Active or Deleted"
            },
        ),
    ]
)
