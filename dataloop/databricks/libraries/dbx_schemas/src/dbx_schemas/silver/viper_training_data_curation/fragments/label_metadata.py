"""Module containing the common label metadata schema to be shared for dbx dataset curation tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import (
    IntegerType,
    StringType,
    StructField,
    StructType,
    TimestampType,
)

LABEL_METADATA_SCHEMA = StructType(
    [
        # Label metadata (incl. reference image metadata)
        StructField(
            "label_guide_url",
            StringType(),
            True,
            metadata={"comment": "URL of the label guide used to label this element"},
        ),
        StructField("label_pipeline_id", StringType(), True, metadata={"comment": "Lapi label pipeline ID"}),
        StructField("label_batch_id", StringType(), True, metadata={"comment": "Lapi label batch id"}),
        StructField("label_latest_version", StringType(), True, metadata={"comment": "Lapi label latest_version"}),
        StructField(
            "label_modified_at",
            TimestampType(),
            True,
            metadata={"comment": "Point in time when the label was last modified"},
        ),
        StructField(
            "label_content_type",
            StringType(),
            True,
            metadata={"comment": "Media type according to RFC 6838 of latest active source in TDS"},
        ),
        StructField(
            "ref_img_file_hash",
            StringType(),
            True,
            metadata={"comment": "SHA2-256bit of the image that went to labeling ('reference image')"},
        ),
        StructField(
            "ref_img_extractor_version",
            IntegerType(),
            True,
            metadata={"comment": "Reference (labeled) image extractor version"},
        ),
        StructField(
            "ref_img_ex_rectification_target",
            StringType(),
            True,
            metadata={"comment": "Reference (labeled) image extraction rectification target"},
        ),
        StructField(
            "ref_img_rectification_calstorage_uid",
            StringType(),
            True,
            metadata={"comment": "Reference (labeled) image rectification calstorage UID"},
        ),
    ]
)
