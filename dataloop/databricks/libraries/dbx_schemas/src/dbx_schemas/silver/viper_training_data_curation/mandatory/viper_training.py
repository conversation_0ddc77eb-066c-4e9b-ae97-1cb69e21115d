"""Module containing the common mandatory schema for the training table in databricks shared by Viper feature teams."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from dbx_schemas.silver.viper_training_data_curation.fragments.image_configurations import IMAGE_CONFIGURATION_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.image_identifier import IMAGE_IDENTIFIER_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.label_metadata import LABEL_METADATA_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.metadata import METADATA_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.storage_info import STORAGE_INFO_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.fragments.stream_information import STREAM_INFORMATION_SCHEMA
from pyspark.sql.types import IntegerType, StringType, StructField, StructType

ADDITIONAL_TRAINING_SCHEMA = StructType(
    [
        # additional fields that are not defined in shared label_metadata subschema
        StructField(
            "label_pipeline_version", StringType(), True, metadata={"comment": "Version of the LAPI label pipeline"}
        ),
        # dataset configuration (not defined in any shared subschemas)
        StructField(
            "oversample_ratio",
            IntegerType(),
            True,
            metadata={"comment": "Oversample ratio i.e. number of times the sample is repeated"},
        ),
        StructField(
            "usage", StringType(), True, metadata={"comment": "Usage of the sample (e.g. train, val, test, unused)"}
        ),
    ]
)
# Combine all fields from shared subschemas and training table specific schema
combined_fields = (
    IMAGE_CONFIGURATION_SCHEMA.fields
    + METADATA_SCHEMA.fields
    + STORAGE_INFO_SCHEMA.fields
    + LABEL_METADATA_SCHEMA.fields
    + IMAGE_IDENTIFIER_SCHEMA.fields
    + STREAM_INFORMATION_SCHEMA.fields
    + ADDITIONAL_TRAINING_SCHEMA.fields
)

# Create a new StructType with the combined fields from all schemas
VIPER_TRAINING_MANDATORY_SCHEMA = StructType(combined_fields)
