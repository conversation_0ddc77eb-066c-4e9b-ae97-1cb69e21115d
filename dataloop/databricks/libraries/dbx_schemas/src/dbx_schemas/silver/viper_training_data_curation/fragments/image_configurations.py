"""Module containing the common image configuration schema to be shared for dbx dataset curation tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import (
    IntegerType,
    StringType,
    StructField,
    StructType,
)

IMAGE_CONFIGURATION_SCHEMA = StructType(
    [
        # Image config
        StructField("image_extractor_version", IntegerType(), True, metadata={"comment": "Image extractor version"}),
        StructField(
            "image_ex_rectification_target",
            StringType(),
            True,
            metadata={"comment": "Image extraction rectification target"},
        ),
        StructField("image_color_space", StringType(), True, metadata={"comment": "Image color space"}),
        StructField(
            "image_rectification_calstorage_uid",
            StringType(),
            True,
            metadata={"comment": "Image rectification calstorage UID"},
        ),
        StructField(
            "image_compression_algorithm",
            StringType(),
            True,
            metadata={"comment": "Compression algorithm used on the image"},
        ),
        StructField(
            "image_view_type",
            StringType(),
            True,
            metadata={"comment": "View type of the image (e.g. Bifocal, Trifocal)"},
        ),
        StructField(
            "image_view", StringType(), True, metadata={"comment": "View of the image (e.g. Far, Mid, Wide, Label)"}
        ),
    ]
)
