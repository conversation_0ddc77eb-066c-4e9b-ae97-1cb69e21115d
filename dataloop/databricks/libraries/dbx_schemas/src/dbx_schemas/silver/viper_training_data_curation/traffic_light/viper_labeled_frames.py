"""Define schema for viper tl labeled frames tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from copy import deepcopy

from dbx_schemas.silver.viper_training_data_curation.mandatory.viper_labeled_frames import (
    VIPER_LABELED_FRAMES_MANDATORY_SCHEMA,
)
from pyspark.sql.types import (
    StringType,
    StructField,
)

mandatory_schema_copy = deepcopy(VIPER_LABELED_FRAMES_MANDATORY_SCHEMA)
VIPER_LABELED_FRAMES_TRAFFIC_LIGHT_SCHEMA = mandatory_schema_copy.add(
    StructField(
        "defective_frame", StringType(), True, metadata={"comment": "Defective frame property from manual labeling"}
    ),
)
