"""Module containing the common stream information schema to be shared for dbx dataset curation tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pyspark.sql.types import (
    IntegerType,
    LongType,
    StringType,
    StructField,
    StructType,
)

STREAM_INFORMATION_SCHEMA = StructType(
    [
        # Stream information
        StructField(
            "split_hash",
            StringType(),
            True,
            metadata={"comment": "SHA2-256bit of the split file (cf. ADA drive ontology) the element belongs to"},
        ),
        StructField(
            "stream_hash",
            StringType(),
            True,
            metadata={"comment": "SHA2-256bit of the stream file (cf. ADA drive ontology) the element belongs to"},
        ),
        StructField(
            "stream_file_name",
            StringType(),
            True,
            metadata={"comment": "Filename of the stream file (cf. ADA drive ontology) the element belongs to"},
        ),
        StructField(
            "stream_camera_type",
            StringType(),
            True,
            metadata={"comment": "Type of the camera capturing the stream (e.g. FC1, TV)"},
        ),
        StructField("frame_number", IntegerType(), True, metadata={"comment": "Number of frame in the stream"}),
        StructField("frame_context", StringType(), True, metadata={"comment": "Context of frame (e.g. A, B)"}),
        StructField(
            "frame_recorded_unix", LongType(), True, metadata={"comment": "Unix timestamp of the recorded frame"}
        ),
    ]
)
