"""Tests for the _cam_frames tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from dbx_schemas.common.functions import field
from dbx_schemas.silver.drive_time_series._cam_frames import (
    _CAM_FRAMES_FC1_SCHEMA,
    _CAM_FRAMES_RCW_SCHEMA,
    _CAM_FRAMES_TV_FRONT_SCHEMA,
    _CAM_FRAMES_TV_LEFT_SCHEMA,
    _CAM_FRAMES_TV_REAR_SCHEMA,
    _CAM_FRAMES_TV_RIGHT_SCHEMA,
)
from helpers import assert_schema_match, must_contain
from pyspark.sql.types import StringType


def test__cam_frames_fc1_schema() -> None:
    """Test the _cam_frames_fc1 schema."""
    assert_schema_match(_CAM_FRAMES_FC1_SCHEMA, _CAM_FRAMES_RCW_SCHEMA)
    assert_schema_match(_CAM_FRAMES_FC1_SCHEMA, _CAM_FRAMES_TV_FRONT_SCHEMA)
    assert_schema_match(_CAM_FRAMES_FC1_SCHEMA, _CAM_FRAMES_TV_LEFT_SCHEMA)
    assert_schema_match(_CAM_FRAMES_FC1_SCHEMA, _CAM_FRAMES_TV_REAR_SCHEMA)
    assert_schema_match(_CAM_FRAMES_FC1_SCHEMA, _CAM_FRAMES_TV_RIGHT_SCHEMA)


def test__cam_frames_fragment() -> None:
    """Test the _cam_frames_fragment."""
    assert must_contain(
        _CAM_FRAMES_FC1_SCHEMA.as_spark_schema(),
        field(name="vin", data_type=StringType(), nullable=False, description=""),
    )
