"""Tests for the _file_entries tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from dbx_schemas.common.functions import field
from dbx_schemas.silver.drive_time_series._file_entries import _FILE_ENTRIES_FC1_SCHEMA, _FILE_ENTRIES_RCW_SCHEMA
from helpers import must_contain
from pyspark.sql.types import StringType


def test_file_entries_tables() -> None:
    """Test the _FILES_ENTRIES_CAMERA_FRAGMENT."""
    assert must_contain(
        _FILE_ENTRIES_FC1_SCHEMA,
        field(name="file_hash", data_type=StringType(), nullable=False, description="Hash of the file"),
    )
    assert must_contain(
        _FILE_ENTRIES_FC1_SCHEMA,
        field(name="tds_file_url", data_type=StringType(), nullable=True, description="don't care"),
    )
    assert _FILE_ENTRIES_FC1_SCHEMA.as_spark_schema() == _FILE_ENTRIES_RCW_SCHEMA.as_spark_schema()
