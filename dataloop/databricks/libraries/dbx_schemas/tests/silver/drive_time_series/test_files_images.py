"""Tests for the files_images tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbx_schemas.common.time_series import VIN_FIELD
from dbx_schemas.silver.drive_time_series import FILES_IMAGES_FC1_SCHEMA
from helpers import must_contain

"""Tests for the files_images tables."""


def test_files_images() -> None:
    """Test that the files_images table contains the necessary fields."""
    assert must_contain(FILES_IMAGES_FC1_SCHEMA, VIN_FIELD)
