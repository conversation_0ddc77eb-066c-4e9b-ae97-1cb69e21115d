"""Tests for the dsp_de_image_camera schemas."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from dbx_schemas.common.functions import field
from dbx_schemas.silver.drive_time_series import DSP_DE_IMAGE_CAMERA_FC1_SCHEMA
from helpers import must_contain
from pyspark.sql.types import StringType


def test_dsp_de_image_camera_schema() -> None:
    """Test the dsp_de_image_camera schema."""
    assert must_contain(
        DSP_DE_IMAGE_CAMERA_FC1_SCHEMA, field(name="file_hash", data_type=StringType(), nullable=False, description="")
    )
