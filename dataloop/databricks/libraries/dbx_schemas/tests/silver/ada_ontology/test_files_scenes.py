"""Tests for the files_scenes schema."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from dbx_schemas.common import (
    ENDED_AT_FIELD,
    FILE_HASH_FIELD,
    FILE_SIZE_BYTES_FIELD,
    FILE_URL_FIELD,
    RECORDING_STARTED_AT_FIELD,
    STARTED_AT_FIELD,
    VIN_FIELD,
)
from dbx_schemas.silver.ada_ontology import FILES_SCENES_SCHEMA
from helpers import must_contain


def test_files_scenes_schema() -> None:
    """Test the files_scenes schema."""
    must_contain(FILES_SCENES_SCHEMA, VIN_FIELD)
    must_contain(FILES_SCENES_SCHEMA, RECORDING_STARTED_AT_FIELD)
    must_contain(FILES_SCENES_SCHEMA, STARTED_AT_FIELD)
    must_contain(FILES_SCENES_SCHEMA, ENDED_AT_FIELD)
    must_contain(FILES_SCENES_SCHEMA, FILE_URL_FIELD)
    must_contain(FILES_SCENES_SCHEMA, FILE_HASH_FIELD)
    must_contain(FILES_SCENES_SCHEMA, FILE_SIZE_BYTES_FIELD)
