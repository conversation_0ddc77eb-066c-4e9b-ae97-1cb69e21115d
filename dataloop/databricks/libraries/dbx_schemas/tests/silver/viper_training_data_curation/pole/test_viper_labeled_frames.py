"""Contract test for pole labeled frames schema."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbx_schemas.silver.viper_training_data_curation.mandatory.viper_labeled_frames import (
    VIPER_LABELED_FRAMES_MANDATORY_SCHEMA,
)
from dbx_schemas.silver.viper_training_data_curation.pole.viper_labeled_frames import VIPER_LABELED_FRAMES_POLE_SCHEMA
from tests.helpers import must_contain


def test_pole_viper_labeled_frames_schema() -> None:
    """Test for the pole specific labelled frames schema."""
    # GIVEN a pole specfic labelled frames table schema and a mandatory labelled frames table schema

    # WHEN checking the pole specific schema against the mandatory labelled frames table
    for field in VIPER_LABELED_FRAMES_MANDATORY_SCHEMA.fields:
        # THEN each field of the mandatory schema must be contained in the pole specific schema
        assert must_contain(VIPER_LABELED_FRAMES_POLE_SCHEMA, field)

    # NOTE: the pole labelled frames table usecase does not define additional fields yet to the mandatory schema
