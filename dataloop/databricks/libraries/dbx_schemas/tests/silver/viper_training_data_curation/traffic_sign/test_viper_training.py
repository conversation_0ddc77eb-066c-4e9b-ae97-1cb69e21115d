"""Contract test for traffic sign training schema."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbx_schemas.silver.viper_training_data_curation.mandatory.viper_training import VIPER_TRAINING_MANDATORY_SCHEMA
from dbx_schemas.silver.viper_training_data_curation.traffic_sign.viper_training import TS_TRAINING_SCHEMA
from helpers import must_contain
from pyspark.sql.types import StringType, StructField


def test_traffic_sign_viper_training_schema() -> None:
    """Test for the traffic sign specific training schema."""
    # GIVEN a traffic sign specfic training table schema and a mandatory training table schema

    # WHEN checking the traffic sign specific schema against the mandatory training table
    for field in VIPER_TRAINING_MANDATORY_SCHEMA.fields:
        # THEN each field of the mandatory schema must be contained in the traffic sign specific schema
        assert must_contain(TS_TRAINING_SCHEMA, field)

    # and the traffic signs training table usecase must also contain the "comment" field of the string type to not break
    assert must_contain(TS_TRAINING_SCHEMA, StructField("comment", StringType(), nullable=True))
