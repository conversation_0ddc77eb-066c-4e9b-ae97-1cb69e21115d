"""Contract test for traffic light labeled frames schema."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbx_schemas.silver.viper_training_data_curation.mandatory.viper_labeled_frames import (
    VIPER_LABELED_FRAMES_MANDATORY_SCHEMA,
)
from dbx_schemas.silver.viper_training_data_curation.traffic_light.viper_labeled_frames import (
    VIPER_LABELED_FRAMES_TRAFFIC_LIGHT_SCHEMA,
)
from pyspark.sql.types import StringType, StructField
from tests.helpers import must_contain


def test_traffic_light_viper_labeled_frames_schema() -> None:
    """Test for the traffic light specific labelled frames schema."""
    # GIVEN a traffic light specfic labelled frames table schema and a mandatory labelled frames table schema

    # WHEN checking the traffic light specific schema against the mandatory labelled frames table
    for field in VIPER_LABELED_FRAMES_MANDATORY_SCHEMA.fields:
        # THEN each field of the mandatory schema must be contained in the traffic light specific schema
        assert must_contain(VIPER_LABELED_FRAMES_TRAFFIC_LIGHT_SCHEMA, field)

    # and the traffic light labelled frames table usecase must also contain the "defective_frame" field of the string
    #   type to not break
    assert must_contain(
        VIPER_LABELED_FRAMES_TRAFFIC_LIGHT_SCHEMA, StructField("defective_frame", StringType(), nullable=True)
    )
