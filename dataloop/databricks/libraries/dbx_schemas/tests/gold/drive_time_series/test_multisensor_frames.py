"""Test for the multi_sensor frames tables."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dbx_schemas.common.functions import field
from dbx_schemas.gold.drive_time_series.multisensor_frames import camera_fields_fragment
from helpers import must_contain
from pyspark.sql.types import StructType, TimestampType


def test_camera_frames() -> None:
    """Test the camera_frames fragment."""
    schema = StructType(camera_fields_fragment("fc1"))
    assert must_contain(
        schema=schema,
        target_field=field(
            name="fc1_emitted_at",
            data_type=TimestampType(),
            nullable=True,
            description="Time when the data was emitted",
            time_domain="TAI",
        ),
    )
