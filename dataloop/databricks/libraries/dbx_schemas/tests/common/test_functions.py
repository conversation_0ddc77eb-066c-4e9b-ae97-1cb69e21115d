"""Tests for functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from dbx_schemas.common.functions import Table, field
from dbx_schemas.common.tags import ANALYTICS_PLATFORM
from pyspark.sql.types import StringType
from pytest import raises


def test_table() -> None:
    """Tests for the Table class."""

    table = Table(
        category="bronze",
        uc_schema="schema",
        name="name",
        tags=ANALYTICS_PLATFORM,
        fields=[field(name="a", data_type=StringType(), description="desc")],
    )
    assert len(table) == 1
    assert table["a"].dataType == StringType()
    assert table[0].dataType == StringType()

    with raises(KeyError):
        print(table["does_not_exist"])
    with raises(IndexError):
        print(table[3])
    with raises(KeyError):
        print(table[0.2])
