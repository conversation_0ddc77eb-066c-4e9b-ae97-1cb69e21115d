"""Tests for the utils module."""

from datetime import timedelta
from unittest.mock import MagicMock

from dbx_schemas.common.functions import Table, TableTags, field
from dbx_schemas.common.tags import ANALYTICS_PLATFORM
from dbx_schemas.utils import _timedelta_isoformat, apply_column_descriptions, apply_column_tags, apply_table_tags
from pyspark.sql.types import TimestampType

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


def test_timedelta_isoformat() -> None:
    """Test for the timedelta isoformat function."""
    assert _timedelta_isoformat(timedelta(days=1, seconds=2)) == "P1DT2S"
    assert _timedelta_isoformat(timedelta(days=0, seconds=0)) == "PT0S"
    assert _timedelta_isoformat(timedelta(days=0, seconds=3601)) == "PT1H1S"
    assert _timedelta_isoformat(timedelta(days=0, seconds=3600)) == "PT1H"
    assert _timedelta_isoformat(timedelta(days=0, seconds=24 * 60 * 60)) == "P1D"
    assert _timedelta_isoformat(timedelta(days=0, seconds=24 * 60)) == "PT24M"
    assert _timedelta_isoformat(timedelta(days=0, seconds=24 * 60 + 61)) == "PT25M1S"
    assert _timedelta_isoformat(timedelta(days=20, seconds=12 * 60 * 60 + 24 * 60 + 1)) == "P20DT12H24M1S"


def _simply_sql(input: str) -> str:
    """Strips unnecessary whitespace from SQL queries to simplify testing."""
    import re

    return re.sub("\\s+", " ", input).lstrip(" ").rstrip(" ")


def test_apply_table_tags() -> None:
    """Test the apply_table_tags function."""
    spark_session = MagicMock()
    table = Table(
        tags=TableTags(responsible_domain="domain", responsible_team="team", refresh_interval=timedelta(days=1)),
        category="bronze",
        uc_schema="schema",
        name="name",
    )
    apply_table_tags(spark_session=spark_session, uc_catalog="foo", table=table)
    assert spark_session.sql.call_count == 1
    query = _simply_sql(spark_session.sql.call_args[0][0])
    assert query == (
        "ALTER TABLE foo.schema.name SET TAGS ( 'responsible_domain'='domain', "
        "'responsible_team'='team', 'refresh_interval'='P1D' )"
    )


def test_apply_column_tags() -> None:
    """Test the apply_column_tags function."""
    spark_session = MagicMock()
    schema = Table(
        category="bronze",
        uc_schema="schema",
        name="table",
        tags=ANALYTICS_PLATFORM,
        fields=[
            field(name="a", data_type=TimestampType(), description="", time_domain="TAI"),
            field(name="b", data_type=TimestampType(), description="", time_domain="UTC"),
            field(name="c", data_type=TimestampType(), description="", time_domain=None),
        ],
    )
    apply_column_tags(spark_session=spark_session, uc_catalog="foo", table=schema)
    assert spark_session.sql.call_count == 2
    print(spark_session.sql.call_args_list)
    tai_query = _simply_sql(spark_session.sql.call_args_list[0].args[0])
    utc_query = _simply_sql(spark_session.sql.call_args_list[1].args[0])
    assert tai_query == ("ALTER TABLE foo.schema.table ALTER COLUMN a SET TAGS ('time_domain'='TAI')")
    assert utc_query == ("ALTER TABLE foo.schema.table ALTER COLUMN b SET TAGS ('time_domain'='UTC')")


def test_apply_column_descriptions() -> None:
    """Test the apply_column_descriptions function."""
    spark_session = MagicMock()
    schema = [
        field(
            name="a",
            data_type=TimestampType(),
            description="some a",
        ),
        field(
            name="b",
            data_type=TimestampType(),
            description="some b",
        ),
        field(
            name="c",
            data_type=TimestampType(),
            description="",
        ),
    ]
    table = Table(category="bronze", uc_schema="schema", name="table", tags=ANALYTICS_PLATFORM, fields=schema)
    apply_column_descriptions(spark_session=spark_session, uc_catalog="foo", table=table)
    assert spark_session.sql.call_count == 3
    print(spark_session.sql.call_args_list)
    a_query = _simply_sql(spark_session.sql.call_args_list[0].args[0])
    b_query = _simply_sql(spark_session.sql.call_args_list[1].args[0])
    c_query = _simply_sql(spark_session.sql.call_args_list[2].args[0])
    assert a_query == "ALTER TABLE foo.schema.table ALTER COLUMN a COMMENT 'some a'"
    assert b_query == "ALTER TABLE foo.schema.table ALTER COLUMN b COMMENT 'some b'"
    assert c_query == "ALTER TABLE foo.schema.table ALTER COLUMN c COMMENT ''"
