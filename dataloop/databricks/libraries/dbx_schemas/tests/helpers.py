"""Commom test helpers for the dbx_schemas tests."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pyspark.sql.types import StructField, StructType


def must_contain(schema: StructType, target_field: StructField) -> bool:
    """Helper for checking that a table must contain a certain field.

    Args:
        schema: The schema of the table to test against.
        target_field: The field that must be contained in the schema.

    Returns:
        bool: True if the target field is contained in the schema, False otherwise.
    """
    for field in schema.fields:
        if (
            field.name == target_field.name
            and field.dataType == target_field.dataType
            and field.nullable == target_field.nullable
        ):
            return True
    return False


def assert_schema_match(expected: StructType, actual: StructType) -> None:
    """Asserts that the schema of a and b are a match by comparing their field names and data types."""
    assert len(expected) == len(
        actual
    ), f"Number of fields does not match. Expected: {len(expected)}, got: {len(actual)}"
    for a_field in expected:
        assert a_field.name == actual[a_field.name].name
        assert (
            a_field.dataType == actual[a_field.name].dataType
        ), f"type of expected {a_field.name}:{a_field.dataType} does not match actual {actual[a_field.name].dataType}"
