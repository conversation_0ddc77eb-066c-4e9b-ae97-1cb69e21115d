[build-system]
requires = ["setuptools~=75.0.0"]
build-backend = "setuptools.build_meta"

# General project metadata
[project]
name = "dbx_schemas"
description = "A library providing pyspark table schemas for working in Databricks."
dynamic = ["version", "readme"]
requires-python = ">=3.10"
dependencies = []

# Metadata dynamically loaded during build time
[tool.setuptools.dynamic]
version = { file = "VERSION" }
readme = { file = ["README.md"], content-type = "text/markdown" }

[project.optional-dependencies]
dbx = []    # define dbx option to satisfy gatekeeper (databricks-connect left out for lean package)
spark = ["pyspark~=3.5.0", "delta-spark==3.2.0"]
test = [
    "pytest>=8.3.0",
    "coverage>=7.6.0",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
pythonpath = "./src"
log_cli = "True"
log_level = "DEBUG"

# coverage.py configuration
[tool.coverage.run]
branch = true
command_line = "-m pytest tests/"
source = ["src"]
omit = [
    "__init__.py",
]

[tool.coverage.report]
format = "text"
show_missing = true
