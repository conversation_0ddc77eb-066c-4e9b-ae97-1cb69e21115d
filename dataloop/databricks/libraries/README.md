# Databricks libraries

This folder contains shared libraries that may be used in Databricks pipelines and notebooks.

## Using a library in Databricks

In order to use a library in Databricks it must have been deployed to Artifactory first.
This is usually done automatically by the [deployment workflow](#deployment-workflow).
Then you can install them on a Databricks compute cluster.
There might be some additional configuration necessary so `pip` can access Artifactory.
See the following sections for more information.

### Personal cluster

Upon creation of a personal compute cluster, the default values should already define an
initialization script that handles the configuration of `pip`.
If you wan to be sure, expand the *Advanced options* at the bottom of the creation dialog,
select the *Init Scripts* tab and check if there is a file called `init-pip-conf-datalake-*.sh` listed.

After the creation of the cluster, you can install the library either via the *Libraries* tab in its
settings or directly in the [notebook](https://docs.databricks.com/en/libraries/notebooks-python-libraries.html).

### Job cluster

For job clusters, we recommend using the `Job Compute` cluster policy which includes the correct `pip` configuration.
In order to retrieve the correct cluster policy ID, you must first define a variable in the Databricks bundle configuration:

```yaml
variables:
  job_cluster_policy_id:
    description: "Cluster Policy ID for job clusters"
    lookup:
      cluster_policy: "Job Compute"
```

Then you can use this to define your job and its executing job cluster:

```yaml
resources:
  jobs:
    example_job:
      tasks:
        - task_key: example_task
          spark_python_task:
            python_file: /Workspace/Users/<USER>/.bundle/${bundle.target}/${bundle.name}/files/transform.py
            parameters: ["--arg", "example_table"]
          job_cluster_key: example_job_cluster
          libraries:
            - pypi:
                package: exlib~=0.1.2

      job_clusters:
        - job_cluster_key: example_job_cluster
          new_cluster:
            instance_pool_id: example-pool-id-000
            policy_id: ${var.job_cluster_policy_id}
            spark_version: "15.4"

```


## Implementation pattern

All libraries must be valid Python projects defined by a `pyproject.toml` file.

The version must be defined in a separate file `VERSION` which contains a valid version identifier according to [PEP 440](https://packaging.python.org/en/latest/specifications/version-specifiers/#version-specifiers).
As Databricks appears to not treat them correctly, we do not allow dev, pre- or post-release versions.
It is read at built-time using [dynamic metadata](https://packaging.python.org/en/latest/guides/writing-pyproject-toml/#static-vs-dynamic-metadata).

Every library must define at least the `command_line` setting for [coverage.py](https://coverage.readthedocs.io/en/latest/) under `[tool.coverage.run]` in `pyproject.toml`.
This is required as we enforce a minimum test coverage in the [deployment workflow](#deployment-workflow).
It is of course recommended to cover as much functionality as possible with unit tests.

In order to execute the unit tests locally, we use local Spark instead of Databricks.
As the related `pyspark` and `databricks-connect` Python dependencies are mutually exclusive,
each library must define `spark` and `dbx` extras under `[project.optional-dependencies]`.

## Deployment workflow

A common deployment workflow for all libraries is provided.
Its definition can be found in [.github/workflows/deploy_libraries.yml](../../.github/workflows/deploy_libraries.yml).

It gets triggered whenever there is a pull request event or a push to `main` (usually after a successful pull request merge) with any changes to the libraries directory.

First, all existing libraries are collected and validated against the [implementation pattern](#implementation-pattern).
Then the workflow runs unit tests for all changed libraries and libraries that depend on them and makes sure that a minimum test coverage is reached.
Note that the tests get run for each supported Python version, which are `3.10` and `3.11` at the moment.

The workflow then checks whether the version of a library changed compared to its version on the `main` branch.
If so, the library is built as a wheel and deployed to Artifactory.

> Note: The library will only be deployed if its version got incremented.
        Therefore, it is recommended to increment the version once at the start of a pull request
        that will make changes to the library.
        Take a look below for why this works.

The deployment triggered by a pull request event works differently to one triggered by a push to `main`:

- On a pull request event, the library gets deployed to `shared-pypi-dev` which allows existing versions of packages to be overridden.
  Therefore, you must only increase the version of your library once and it will override the package in the repository
  after each pull request event.
- On a push to main, the library gets deployed to `shared-pypi-prod` and will not be able to be overridden again.
