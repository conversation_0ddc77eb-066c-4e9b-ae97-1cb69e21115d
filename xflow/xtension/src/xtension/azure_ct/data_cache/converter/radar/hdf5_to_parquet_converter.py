"""Conversion of hdf5 data to parquet format."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from collections.abc import Sequence
from typing import TYPE_CHECKING, Any

import numpy as np
import numpy.typing as npt
from dataloop_constants import MDM_CONSTANTS
from dataloop_data_formats.joint_box_format.utils.transforms import Transformation
from mdm.mdd import MetaDataDumpClient

from azure_tools.credential import get_default_azure_credential, get_mdd_apikey
from data_formats.radar.pointcloud_accumulation import (
    MAX_TIME_DIFFERENCE_TO_REF_SENSOR_IN_MS,
)
from data_formats.radar.processing import process_radar_data
from xtension.azure_ct.data_cache.converter.radar.hdf5.hdf5_helper import (
    ALLIANCE_RADAR_NAMES,
)
from xtension.azure_ct.data_cache.data_converter import DataConverter
from xtension.azure_ct.data_cache.data_types import Parquet
from xtension.azure_ct.data_cache.mdm_data.data_source import get_active_source_url
from xtension.azure_ct.data_cache.mdm_data.data_types import RadarHdf5
from xtension.azure_ct.data_cache.sample import Sample, SampleElement

_LOGGER = logging.getLogger(__name__)


class Hdf5ToParquetAllRadarsConverter(DataConverter):
    """Convert hdf5 in hdf5 format to parquet format."""

    version = "2.3.4"  # type: ignore[reportAssignmentType]

    def __init__(
        self,
        reference_radar: str,
        reference_sensor: str = "image_fc1",
        radars_to_combined: list[str] = ALLIANCE_RADAR_NAMES,
        perform_radar_accumulation_over_time: bool = True,  # noqa: FBT001, FBT002
        max_time_for_accumulation_in_ms: np.timedelta64 = MAX_TIME_DIFFERENCE_TO_REF_SENSOR_IN_MS,
        mdd_url: str = MDM_CONSTANTS["prod"]["mdd"]["url"],
        mdd_api_scopes: Sequence[str] = (MDM_CONSTANTS["prod"]["mdd"]["scope"],),
        org_id: str = "pace",
    ) -> None:
        """Converter for pointclouds from all radars hdf5 to parquet format.

        Args:
            reference_radar: reference leading radar name.
            reference_sensor: reference sensor name. Timestamps of this sensor are used as leader/madter for
                              ego motion compensatin, aggregations, etc.
            radars_to_combined: list of radar names to be combined.
            perform_radar_accumulation_over_time: flag to enable/disable radar accumulation over time.
            max_time_for_accumulation_in_ms: maximum allowed delta time to be used for radar accumulation over time
                                             in milliseconds.
            mdd_url: URL of the MDD service, defaults to the ALLIANCE (PACE) production URL.
            mdd_api_scopes: List of API scopes, required for MDD authentication, defaults to
                the ALLIANCE (PACE) production API scope.
            org_id: Organization ID for MDM services, defaults to "pace".
        """
        super().__init__()
        self.reference_radar = reference_radar
        self.reference_sensor = reference_sensor
        self.radars_to_combined = radars_to_combined
        self.perform_radar_accumulation_over_time = perform_radar_accumulation_over_time
        self.max_time_for_accumulation_in_ns: np.timedelta64 = max_time_for_accumulation_in_ms.astype("timedelta64[ns]")

        self.credential = get_default_azure_credential()
        self.mdd_client = MetaDataDumpClient(
            credential=self.credential,
            base_url=mdd_url,
            credential_scopes=mdd_api_scopes,
            org_id=org_id,
            gateway=True,
            headers={"apikey": get_mdd_apikey(self.credential)},
        )

    def get_source_url_from_sha(self, sha: str) -> str | None:
        """Get the source URL on mdm using SHA.

        Args:
            sha: SHA of the file on mdm.

        Returns:
            Source URL on mdm or None.
        """
        return get_active_source_url(self.mdd_client, sha)

    def convert(self, sample_element: SampleElement, sample_readonly: Sample) -> SampleElement:
        """Converts a pointcloud in pcd format to tfrecord format."""
        assert isinstance(sample_element.data, RadarHdf5)
        ref_sensor_timestamp_in_ns = int(sample_readonly.meta_data["timestamp_ns"][self.reference_sensor])
        ref_sensor_timestamp_in_ns = np.datetime64(ref_sensor_timestamp_in_ns, "ns")

        # # Get ego odometry related data from IMU trajectory file on MDM
        ego_poses, ego_pose_timestamps_in_ns, ego_velocities, ego_angular_rates = sample_readonly.odometry.data.read()  # pyright: ignore[reportAttributeAccessIssue]

        # Get info about reference radar...
        ref_radar_name = sample_element.name
        (
            ref_radar_features,
            ref_radar_timestamps_in_ns,
            ref_radar_extrinsics_calibration,
        ) = sample_element.data.read()

        if TYPE_CHECKING:
            assert ref_radar_name is not None

        # Update the radar data with the closest timestamp
        radars_data: list[
            tuple[
                str,
                dict[str, npt.NDArray[Any]],
                npt.NDArray[np.datetime64],
                Transformation,
            ]
        ] = [
            (
                ref_radar_name,
                ref_radar_features,
                ref_radar_timestamps_in_ns,
                ref_radar_extrinsics_calibration,
            )
        ]

        # Get auxiliary radars data
        for radar_name in self.radars_to_combined:
            if f"radar_{radar_name}" != self.reference_radar:
                radar_sha = sample_readonly.meta_data["sha"]["radar_" + radar_name]

                source_url = self.get_source_url_from_sha(radar_sha)
                if source_url is None:
                    _LOGGER.warning(
                        f"Skipping loading of {radar_name} hdf5 with sha {radar_sha} since it couldnt be found on MDM."
                    )
                    continue

                radar_data = RadarHdf5(source_url=source_url, credential=self.credential)
                radar_features, radar_timestamps, radar_extrinsics_calibration = radar_data.read()
                radars_data.append(
                    (
                        radar_name,
                        radar_features,
                        radar_timestamps,
                        radar_extrinsics_calibration,
                    )
                )

        # Processes data for all the selected/available radar sensors
        combined_radar_parquet = Parquet(
            data=process_radar_data(
                radars_data=radars_data,  # type: ignore[reportArgumentType]
                ref_sensor_timestamp_in_ns=ref_sensor_timestamp_in_ns,
                ego_poses=ego_poses,
                ego_velocities=ego_velocities,
                ego_pose_timestamps_in_ns=ego_pose_timestamps_in_ns,
                ego_angular_rates=ego_angular_rates,
                perform_radar_accumulation_over_time=self.perform_radar_accumulation_over_time,
                max_time_for_accumulation_in_ns=self.max_time_for_accumulation_in_ns,
            )
        )

        sample_element.data = combined_radar_parquet

        return sample_element
