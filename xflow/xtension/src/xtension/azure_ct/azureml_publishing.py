"""This module contains functions to parse and publish experiment results to AzureML."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON>sch GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import datetime
import json
import logging
import os
import re
import shutil
import subprocess as sp
import sys
import tempfile
from pathlib import Path
from typing import Any

import msrest.exceptions
from azure.ai.ml import MLClient
from azure.ai.ml.constants import AssetTypes
from azure.ai.ml.entities import Job, Model
from azureml.core.run import Run
from tenacity import retry, retry_if_exception_type, stop_after_attempt, wait_exponential_jitter

from azure_tools.job import get_job, get_ml_client

LOGGER = logging.getLogger(__name__)

# Name of the base folder for each model in the model store
MODEL_STORE_FOLDER = "model"
# Official limit of AzureML metric name length
MAX_METRIC_NAME_LENGTH = 255


def publish_experiment_results(
    documentation_path: str,
    metrics: dict[str, Any],
    metrics_version: int = -1,
    ignore_patterns: list[str] | None = None,
    include_patterns: list[str] | None = None,
) -> Model | None:
    """Publish given data as results of an experiment to the model store.

    Args:
        documentation_path: Path to documentation which should be published.
        metrics: Dictionary with key-value pairs of metric name and metric value.
        metrics_version: Version number of extracted metrics and paths/filenames.
        ignore_patterns: List of regex patterns to exclude files from copying. Note: files matching these patterns
            will not be copied even if they match include_patterns.
        include_patterns: List of regex patterns to include files for copying.

    Note: If either ignore_patterns or include_patterns is set, the documentation_path is copied to a temporary
        folder before publishing. If both are None, the documentation_path is used directly.

    Returns:
        Model if successfully published else None
    """
    ml_client = get_ml_client()
    if ml_client is None:
        LOGGER.warning("No MLClient available, not publishing experiment results.")
        return None

    # extract information about the experiment
    meta_data = extract_meta_data(ml_client)

    # check if metrics version is set correctly. If not, don't publish metrics and model
    # Note: model will still be available in pipeline outputs, but not in model store
    if metrics_version <= 0:
        # warn the user that metrics version is not set correctly and that it is assumed that this goes together
        # with not implemented metrics
        LOGGER.warning("Warning: Invalid metrics version, not publishing model!")
        return None

    LOGGER.info("Publishing model")
    # copy documentation to 'outputs' such that azureml shows it in the browser
    if ignore_patterns or include_patterns:
        release_folder_path = str(Path(tempfile.gettempdir()) / "release")
        copy_data(
            input_folder=documentation_path,
            output_folder=release_folder_path,
            ignore_patterns=ignore_patterns,
            include_patterns=include_patterns,
            allow_overwrite=True,
        )
    else:
        release_folder_path = documentation_path

    model = publish_model(
        metrics=metrics,
        metrics_version=metrics_version,
        meta_data=meta_data,
        documentation_path=release_folder_path,  # type: ignore[arg-type]
        ml_client=ml_client,
    )
    LOGGER.info("Pipeline metadata/tags:")
    for key, value in meta_data.items():
        LOGGER.info(f"    {key}: {value}")

    return model


def _copy_with_shutil(
    input_folder: str,
    output_folder: str,
    include_patterns: list[str] | None = None,
    ignore_patterns: list[str] | None = None,
    *,
    allow_overwrite: bool,
) -> None:
    """Copy files using shutil, filtering by regex patterns."""

    include_patterns = include_patterns or []
    compiled_include_patterns = [re.compile(p) for p in include_patterns]
    ignore_patterns = ignore_patterns or []
    compiled_ignore_patterns = [re.compile(p) for p in ignore_patterns]

    def matches_patterns(filename: str, patterns: list[re.Pattern]) -> bool:  # type: ignore  # noqa: PGH003
        return any(p.search(filename) for p in patterns)

    os.makedirs(output_folder, exist_ok=True)

    for root, _, files in os.walk(input_folder):
        rel_path = os.path.relpath(root, input_folder)
        dest_dir = os.path.join(output_folder, rel_path) if rel_path != "." else output_folder
        os.makedirs(dest_dir, exist_ok=True)

        for file in files:
            # Check ignore patterns first
            if compiled_ignore_patterns and matches_patterns(os.path.join(root, file), compiled_ignore_patterns):
                LOGGER.debug(f"Ignoring file {file} due to ignore_patterns from {root}")
                continue
            # Then check include patterns
            if compiled_include_patterns and not matches_patterns(os.path.join(root, file), compiled_include_patterns):
                LOGGER.debug(f"Skipping file {file} as it does not match include_patterns from {root}")
                continue

            src_file = os.path.join(root, file)
            dst_file = os.path.join(dest_dir, file)

            if os.path.exists(dst_file) and not allow_overwrite:
                LOGGER.warning(f"File exists and overwrite not allowed: {dst_file}")
                continue

            LOGGER.info(f"Copying file {src_file} to {dst_file}")
            shutil.copy2(src_file, dst_file)


def _copy_with_rsync(
    input_folder: str,
    output_folder: str,
    include_patterns: list[str] | None,
    ignore_patterns: list[str] | None = None,
    *,
    allow_overwrite: bool,
) -> None:
    """Copy files using rsync, filtering by include and ignore patterns."""

    rsync_call = ["rsync", "-rav"]

    ignore_patterns = ignore_patterns or []
    # 1. Exclude ignore_patterns first (highest priority)
    for pattern in ignore_patterns:
        rsync_call += ["--exclude", pattern]

    # 2. Include include_patterns
    if include_patterns:
        for pattern in include_patterns:
            rsync_call += ["--include", pattern]
    else:
        # If no include patterns, include everything
        rsync_call += ["--include", "*"]

    # 3. Include all directories to allow recursion
    rsync_call += ["--include", "*/"]

    # 4. Exclude everything else
    rsync_call += ["--exclude", "*"]

    if not allow_overwrite:
        rsync_call.append("--ignore-existing")

    src_rsync = input_folder.rstrip(os.sep) + os.sep
    dst_rsync = output_folder

    rsync_call += [src_rsync, dst_rsync]

    LOGGER.info(f"Running rsync command: {' '.join(rsync_call)}")
    sp.check_call(rsync_call)


def copy_data(
    input_folder: str,
    output_folder: str,
    include_patterns: list[str] | None = None,
    ignore_patterns: list[str] | None = None,
    *,
    allow_overwrite: bool = False,
) -> None:
    """Copy files from input_folder to output_folder.

    If include_patterns is empty or None, copies everything.
    Otherwise, copies only files matching any of the regex patterns.

    Args:
        input_folder: Source directory to copy from.
        output_folder: Destination directory to copy to.
        include_patterns: List of regex patterns to include. If empty or None, copy all files.
        ignore_patterns: List of regex patterns to exclude. Files matching these are excluded even if included.
        allow_overwrite: If True, allows overwriting existing files/folders.
    """
    try:
        _copy_with_shutil(
            input_folder, output_folder, include_patterns, ignore_patterns, allow_overwrite=allow_overwrite
        )
    except Exception:
        LOGGER.exception("shutil copy failed, falling back to rsync")
        _copy_with_rsync(
            input_folder, output_folder, include_patterns, ignore_patterns, allow_overwrite=allow_overwrite
        )


def parse_copy_params(arg_name: str = "--cp", help_msg_prefix: str = "") -> argparse.Namespace:
    """Parse command line arguments to parametrize copy function.

    Argparsing for copy arguments with copy and ignore patterns. Parses arguments and removes them from sys.argv

    Args:
        arg_name: Name of argument to be parsed in command line, e.g. --cp
        help_msg_prefix: Message to be displayed prior to argument usage in command line help string

    Returns:
        argparse return value with respective copy arguments parsed
    """
    help_msg = (
        f"{help_msg_prefix}ARG order is SRC DST [IGNORE_PATTERN ...]. Copy data from SRC to DST, ignoring all files "
        "matching any IGNORE_PATTERN"
    )
    # parse arguments for copy
    parser = argparse.ArgumentParser()
    parser.add_argument(arg_name, type=str, nargs="+", metavar="ARG", help=help_msg, action="append")
    parser.add_argument(
        f"{arg_name}_allow_overwrite",
        help="Allow to overwrite existing files during copy using rsync",
        action="store_true",
        default=False,
    )
    args, unparsed = parser.parse_known_args()
    # reset sys args for what's to follow
    sys.argv = [sys.argv[0]] + unparsed
    return args


@retry(
    stop=stop_after_attempt(10),
    wait=wait_exponential_jitter(),
    retry=retry_if_exception_type(msrest.exceptions.ClientRequestError),
    reraise=True,
)
def get_aml_pipeline_run() -> Run | None:
    """Get the current Azure ML pipeline run object, or None if not running on Azure.

    If the current service context is a step/stage, the returned run object corresponds to the top level parent run.

    Returns:
        Pipeline run object
    """
    from xtension.logging import azure  # noqa: PLC0415

    run = azure.get_azure_run()
    if run is None:
        return None
    while run.parent:
        run = run.parent
        LOGGER.info(f"Parent run found, using parent run. Stage name: {run.name}")
    return run


def get_aml_pipeline_job(ml_client: MLClient) -> Job | None:
    """Get the current Azure ML pipeline job object, or None if not running on Azure.

    If the current service context is a step/stage, the returned run object corresponds to the top level parent run.

    Returns:
        Pipeline run object
    """
    job = get_job(ml_client)
    if job is None:
        return None

    LOGGER.info(f"Job name: {job.name}")

    while (root_pipeline_run_id := job.properties.get("azureml.rootpipelinerunid")) is not None:
        job = ml_client.jobs.get(name=root_pipeline_run_id)
        LOGGER.info(f"Parent run found, using parent run. Job name: {job.name}")

    return job


def print_aml_run_info(aml_run: Job) -> None:
    """Print detailed run information to console."""
    print(f"Run id (pipeline): {aml_run.name}")
    print(f"Experiment name: {aml_run.experiment_name}")
    print("Tags:")
    [print(f"    {key}: {value}") for key, value in parse_tags(aml_run).items()]


def parse_tags(aml_run: Job) -> dict[str, Any]:
    """Parse the tags of the pipeline run.

    In particular, convert strings to desired data types for known tags,
    e.g. tag 'short_run' is converted to bool.

    Args:
        aml_run: AML run object of the pipeline run where tags are parsed from.

    Returns:
        Dictionary of tags with converted values
    """
    tags = dict(aml_run.tags) if aml_run.tags is not None else {}
    # convert bool
    for key in ["short_run", "productive_run"]:
        if key in tags:
            val_str = tags[key].lower()
            val_bool = val_str in ["true", "1"]
            tags[key] = val_bool

    return tags


def extract_meta_data(ml_client: MLClient) -> dict[str, Any]:
    """Extract meta data of the run.

    Meta data is the workspace name, the run id of the pipeline, and the timestamp/date
    of the pipeline start.

    Adds a version number of the meta data that needs to be increased if things change.

    Returns:
        Meta data as dictionary.
    """
    # increase version number whenever something is changed
    meta_data_version = "1.0"
    aml_run = get_aml_pipeline_job(ml_client)
    if aml_run is None or aml_run.name is None:
        msg = "Azure run was not detected, code is not running on azure."
        raise RuntimeError(msg)
    print_aml_run_info(aml_run)
    start_time = aml_run.creation_context.created_at if aml_run.creation_context is not None else None
    meta_data = {
        "date": start_time,
        "workspace": ml_client.workspace_name,
        "pipeline_run_id": aml_run.name,
        "experiment_name": aml_run.experiment_name,
        "version": meta_data_version,
        "training_stage_id": get_training_stage_id(ml_client, aml_run.name),
    }
    tags = parse_tags(aml_run)
    return {**meta_data, **tags}


def get_training_stage_id(ml_client: MLClient, root_run_id: str) -> None | str:
    """Get the Training's stage id."""

    LOGGER.debug(f"Parent run name: {root_run_id}")
    children = ml_client.jobs.list(parent_job_name=root_run_id)

    for job in children:
        if job.display_name in ("Training", "long_run_train", "train"):
            LOGGER.info(f"Stage: {job.display_name} has stage id: {job.name}")
            return job.name

    return None


def log_metrics_to_pipeline(metrics: dict[str, Any], metrics_version: int) -> None:
    """Log metrics to the pipeline run.

    Args:
        metrics: Dictionary of metrics names and values
        metrics_version: Version number of extracted metrics and paths/filesnames
    """
    LOGGER.info("Log metrics to pipeline.")
    aml_run = get_aml_pipeline_run()
    if aml_run is None:
        msg = "Azure run was not detected, code is not running on azure."
        raise RuntimeError(msg)

    aml_run.log("metrics_version", metrics_version)

    for metric_name, value in metrics.items():
        if len(metric_name) > MAX_METRIC_NAME_LENGTH:
            LOGGER.warning(
                f"The metric name '{metric_name}' exceeds the maximum of {MAX_METRIC_NAME_LENGTH} characters! The "
                f"'{metric_name[MAX_METRIC_NAME_LENGTH:]}' part will be cut."
            )
            metric_name = metric_name[:MAX_METRIC_NAME_LENGTH]  # noqa: PLW2901

        aml_run.log(metric_name, value)


def write_metrics_to_storage(
    metrics_paths: list[str],
    metrics_version: int,
    meta_data: dict[str, Any],
    documentation_path: str,
    metrics_output_storage: str,
    ml_client: MLClient,
) -> None:
    """Write metrics and meta data storage.

    The data is intended for reports by functional testing, etc.

    Args:
        metrics_paths: List of paths, relative to documentation dir, that are copied
        metrics_version: Version number of extracted metrics and paths/filesnames
        meta_data: Dictionary of meta data names and values
        documentation_path: Path to documentation.
        metrics_output_storage: Root output path for writing metrics.
        ml_client: Azure ML client to use for publishing the model.
    """
    if not metrics_paths:
        return

    LOGGER.info("Write metrics to storage.")
    aml_run = get_aml_pipeline_job(ml_client)
    if aml_run is None or aml_run.name is None:
        msg = "Azure run was not detected, code is not running on azure."
        raise RuntimeError(msg)

    start_time: datetime.datetime = aml_run.creation_context.created_at  # type: ignore[reportAssignmentType ]

    metrics_output_path = os.path.join(
        metrics_output_storage,
        parse_tags(aml_run)["work_package"],
        aml_run.experiment_name,  # type: ignore[reportArgumentType]
        f"v{metrics_version}",
        str(start_time.year),
        str(start_time.month),
        str(start_time.day),
        aml_run.name,
    )

    # prepend absolute path to documentation
    metrics_paths = [os.path.join(documentation_path, p) for p in metrics_paths]
    # copy
    for p in metrics_paths:
        copy_data(input_folder=p, output_folder=metrics_output_path)

    # write meta data
    meta_data_path = os.path.join(metrics_output_path, "meta.json")
    with open(meta_data_path, "w") as f:
        json.dump(meta_data, f, indent=4, sort_keys=True)


def publish_model(
    metrics: dict[str, Any],
    metrics_version: int,
    meta_data: dict[str, Any],
    documentation_path: str,
    ml_client: MLClient,
) -> Model:
    """Publish report and model with metrics and meta data as tags to the azureml model registry.

    Args:
        metrics: Dictionary of metrics names and values
        metrics_version: Version number of extracted metrics and paths/filesnames
        meta_data: Dictionary of meta data names and values
        documentation_path: Path to documentation as, e.g., produced by report step.
        ml_client: Azure ML client to use for publishing the model.

    Returns:
        Azure ml registered Model.
    """
    if not os.path.isdir(documentation_path):
        msg = f"Model path '{documentation_path}' is not a directory! Only a directory can be published as a model."
        raise OSError(msg)

    LOGGER.info(f"Publish model from {documentation_path} with metrics version {metrics_version}.")

    tags_and_props: dict[str, Any] = {**meta_data, **metrics, "metrics_version": metrics_version}

    job = get_job(ml_client)

    model_asset = Model(
        path=documentation_path,
        name=job.experiment_name if job is not None else None,
        type=AssetTypes.CUSTOM_MODEL,
        tags=tags_and_props,
        properties=tags_and_props,
    )

    registered = ml_client.models.create_or_update(model_asset)
    return registered
