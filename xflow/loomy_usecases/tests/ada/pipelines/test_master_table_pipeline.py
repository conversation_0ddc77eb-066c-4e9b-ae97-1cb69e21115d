"""Integration test for the ADA master table pipeline."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path

import pyarrow as pa
import pytest
from pytest_mock import MockerFixture

from loomy.core.storage_locations.file_location import FileLocation
from loomy.dataset_indices.parquet_dataset_index import ParquetDatasetIndex
from loomy_usecases.ada.definitions import StreamName
from loomy_usecases.ada.pipelines.config.config import AdaPipelineConfig
from loomy_usecases.ada.pipelines.master_table_pipeline import run_pipeline
from loomy_usecases.common.warehouses.duckdb import DuckDBWarehouse

EXPECTED_STREAMS = {
    StreamName.IMAGE_FC1_A_TRIFOCAL_WIDE,
    StreamName.IMAGE_FC1_B_TRIFOCAL_WIDE,
    StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_FAR,
    StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_MID,
    StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_WIDE,
    StreamName.IMAGE_TV_FRONT,
    StreamName.IMAGE_TV_LEFT,
    StreamName.IMAGE_TV_REAR,
    StreamName.IMAGE_TV_RIGHT,
    StreamName.RADAR_COMBINED,
    StreamName.LIDAR_VELODYNE_EMC,
    StreamName.GT_ODOMETRY,
    StreamName.GT_AUTO_LABEL,
    StreamName.LMA_LABEL,
    StreamName.OCCUPANCY_LABEL,
    StreamName.OMG_SEMSEG_FC1,
    StreamName.OMG_SEMSEG_TV_LEFT,
    StreamName.OMG_SEMSEG_TV_RIGHT,
    StreamName.OMG_SEMSEG_TV_FRONT,
    StreamName.OMG_SEMSEG_TV_REAR,
}


@pytest.mark.slow
@pytest.mark.lfs_dependencies([r"%ada_test_data_dir%"])
def test_master_table_pipeline(
    ada_master_tables: dict[str, pa.Table], config: AdaPipelineConfig, tmp_path: Path, mocker: MockerFixture
) -> None:
    """Test for the ADA master table pipeline."""

    # GIVEN a set of local master tables in a DuckDB warehouse
    mocker.patch(
        "loomy_usecases.ada.pipelines.master_table_pipeline.DatabricksWarehouse",
        return_value=DuckDBWarehouse(tables=ada_master_tables),
    )
    # WHEN running the pipeline
    num_recordings = ada_master_tables["recordings"].num_rows
    run_pipeline(config=config)

    # THEN the pipeline should run without errors and create a dataset
    dataset_index = ParquetDatasetIndex(FileLocation(file_path=tmp_path / "dataset_index"))
    dataset = dataset_index.get_dataset(name="ada_test")

    # WHEN iterating over the recordings in the dataset
    recordings = list(dataset.iterate_recordings())
    # THEN the dataset should contain the expected number of recordings
    assert len(recordings) == num_recordings

    # WHEN checking the streams in each recording
    # THEN each recording should have the expected streams and samples
    for recording in recordings:
        assert set(recording.streams.keys()) == EXPECTED_STREAMS
        for stream in recording.streams.values():
            # NOTE: The radar processor heavily relies on the fact that the streams it operates on have a relationship
            # which is not the case for the test data. Therefore, the radar stream will have no samples.
            if stream.name != StreamName.RADAR_COMBINED:
                assert len(stream.samples) > 0
