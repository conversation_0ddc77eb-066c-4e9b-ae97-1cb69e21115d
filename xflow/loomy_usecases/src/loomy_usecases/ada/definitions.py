"""Definitions used across the ADA use cases."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Iterable, Mapping
from enum import Enum

ADA_DATA_LAKE_LOCATION = "https://vdeeptrainingprod.blob.core.windows.net/loomy-data-lake/ada"
ADA_CATALOG_LOCATION = "https://vdeephierarchicalprod.blob.core.windows.net/loomy-catalogs/ada"

TEST_DATA_LAKE_LOCATION = "https://vdeeptrainingprod.blob.core.windows.net/loomy-data-lake/testing"
TEST_CATALOG_LOCATION = "https://vdeephierarchicalprod.blob.core.windows.net/loomy-catalogs/testing"


class StreamName(str, Enum):
    """An enumeration of the stream names of the ADA Usecase."""

    IMAGE_FC1_A = "image_fc1_A"
    IMAGE_FC1_B = "image_fc1_B"
    IMAGE_TV_LEFT = "image_tv_left"
    IMAGE_TV_RIGHT = "image_tv_right"
    IMAGE_TV_FRONT = "image_tv_front"
    IMAGE_TV_REAR = "image_tv_rear"
    OMG_SEMSEG_FC1 = "omg_semseg_fc1"
    OMG_SEMSEG_TV_LEFT = "omg_semseg_tv_left"
    OMG_SEMSEG_TV_RIGHT = "omg_semseg_tv_right"
    OMG_SEMSEG_TV_FRONT = "omg_semseg_tv_front"
    OMG_SEMSEG_TV_REAR = "omg_semseg_tv_rear"
    RADAR_LRR5_FRONT = "radar_lrr5_front"
    RADAR_LRR5_REAR = "radar_lrr5_rear"
    RADAR_CR_FRONT_LEFT = "radar_cr_front_left"
    RADAR_CR_FRONT_RIGHT = "radar_cr_front_right"
    RADAR_CR_REAR_LEFT = "radar_cr_rear_left"
    RADAR_CR_REAR_RIGHT = "radar_cr_rear_right"
    LIDAR_VELODYNE = "lidar_velodyne"
    LIDAR_VELODYNE_EMC = "lidar_motion_compensated_center"
    GT_AUTO_LABEL = "gt_auto_label"
    GT_ODOMETRY = "gt_odometry"
    LMA_LABEL = "lma_label"
    OCCUPANCY_LABEL = "occupancy_label"

    # output stream names of the processors that don't match the input streams
    IMAGE_FC1_A_TRIFOCAL_WIDE = "image_fc1_A_trifocal_wide"
    IMAGE_FC1_B_TRIFOCAL_WIDE = "image_fc1_B_trifocal_wide"
    IMAGE_FC1_A_TRIFOCAL_CNN_FAR = "image_fc1_A_trifocal_cnn_far"
    IMAGE_FC1_A_TRIFOCAL_CNN_MID = "image_fc1_A_trifocal_cnn_mid"
    IMAGE_FC1_A_TRIFOCAL_CNN_WIDE = "image_fc1_A_trifocal_cnn_wide"
    RADAR_COMBINED = "radar_combined"


class TaskName(str, Enum):
    """An enumeration of the task names of the ADA Usecase."""

    DYO = "dyo"
    OCC = "occ"
    OMG = "omg"


DEBUG_MODE_SAMPLE_LIMIT_ENV_VAR = "DEBUG_MODE_SAMPLE_LIMIT"  # env var used when debugging the loomy pipeline

_MULTIMODAL_IMAGE_STREAMS = [
    StreamName.IMAGE_FC1_A_TRIFOCAL_WIDE,
    StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_FAR,
    StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_MID,
    StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_WIDE,
    StreamName.IMAGE_TV_LEFT,
    StreamName.IMAGE_TV_RIGHT,
    StreamName.IMAGE_TV_FRONT,
    StreamName.IMAGE_TV_REAR,
]

# This variable defines a set of output streams that must be available for a certain task. A recording will be
# considered invalid if not at least all output streams of one stream group are present.
REQUIRED_STREAM_GROUPS: Mapping[str, Iterable[str]] = {
    TaskName.DYO: [
        *_MULTIMODAL_IMAGE_STREAMS,
        StreamName.RADAR_COMBINED,
        StreamName.GT_AUTO_LABEL,
        StreamName.GT_ODOMETRY,
    ],
    TaskName.OCC: [
        *_MULTIMODAL_IMAGE_STREAMS,
        StreamName.OCCUPANCY_LABEL,
    ],
}
