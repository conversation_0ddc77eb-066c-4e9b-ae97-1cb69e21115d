"""Implementation of the sensor stream generator."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import functools
import json
import logging
import operator
from enum import Enum
from types import TracebackType
from typing import Any

import numpy as np
import pyarrow as pa
import pyarrow.compute as pc
from sqlalchemy.sql.expression import ColumnExpressionArgument, and_, join, literal_column, select
from typing_extensions import Self

from loomy.core.basic_elements.arrow import build_arrow_expr, iterate_arrow_table_as_pydict
from loomy.core.basic_elements.stored_datum import StoredDatum
from loomy.core.basic_elements.synchronization import find_closest_sample_timestamps_for
from loomy.core.data_types import PngImageData
from loomy.core.data_types.hdf5_data import Hdf5Data
from loomy.core.storage_locations import AzureBlobLocation
from loomy.processors.identity_processor import IdentityProcessor
from loomy.processors.interface import ProcessorSpec
from loomy_usecases.ada.datasources.master_table.schema.alchemy.image_frames import IMAGE_FRAMES_TABLE
from loomy_usecases.ada.datasources.master_table.schema.alchemy.lidar_scans_motion_compensated import (
    LIDAR_SCANS_MOTION_COMPENSATED_TABLE,
)
from loomy_usecases.ada.datasources.master_table.schema.alchemy.recordings import RECORDINGS_TABLE
from loomy_usecases.ada.datasources.master_table.schema.alchemy.streams import STREAMS_TABLE
from loomy_usecases.ada.datasources.master_table.stream_generators.interface import StreamGenerator
from loomy_usecases.ada.definitions import StreamName, TaskName
from loomy_usecases.ada.helpers import limit_if_in_debug_mode
from loomy_usecases.ada.processors.image_conversion_tv_processor import (
    ImageConversionTVfrontProcessor,
    ImageConversionTVleftProcessor,
    ImageConversionTVrearProcessor,
    ImageConversionTVrightProcessor,
)
from loomy_usecases.ada.processors.image_warping_fc1_processor import (
    ImageWarpingFC1AToTrifocalCNNFarViewProcessor,
    ImageWarpingFC1AToTrifocalCNNMidViewProcessor,
    ImageWarpingFC1AToTrifocalCNNWideViewProcessor,
    ImageWarpingFC1ATrifocalWideProcessor,
    ImageWarpingFC1BTrifocalWideProcessor,
)
from loomy_usecases.ada.processors.radar_processor import RadarAccumulationProcessor
from loomy_usecases.ada.schema.recording import AdaRecording
from loomy_usecases.ada.schema.streams.image import (
    HW,
    AdaImageSample,
    AdaImageStream,
    ColorFormat,
    CylinderCameraModel,
    DeformedCylinderCameraModel,
    ImageContext,
    ImageView,
)
from loomy_usecases.ada.schema.streams.lidar import (
    AdaLidarSample,
    AdaLidarStream,
    LidarSensorType,
)
from loomy_usecases.ada.schema.streams.radar import AdaRadarSample, AdaRadarStream
from loomy_usecases.ada.schema.types.basic import Vector2d
from loomy_usecases.ada.schema.types.pose import Rotation, Translation
from loomy_usecases.common.data_types.point_cloud_data import PointCloudData
from loomy_usecases.common.helper import milliseconds_to_nanoseconds
from loomy_usecases.common.warehouses.interface import DataWarehouse

_LOGGER = logging.getLogger(__name__)


class SensorStreamGenerator(StreamGenerator):
    """Stream generator for sensor streams."""

    def __init__(self, warehouse: DataWarehouse, frequency: int | None = None) -> None:
        """Create a new sensor stream generator.

        Args:
            warehouse: Data warehouse to read from
            frequency: The frequency to downsample the image streams. This means that all streams depending on any
                       of the image streams will be implicitly downsampled too.
        """
        super().__init__(warehouse)
        self._frequency = frequency

        self._streams: pa.Table | None = None
        self._image_frames: pa.Table | None = None
        self._radar_streams: pa.Table | None = None  # radar comes in HDF5 files for whole stream and has no frames
        self._lidar_frames: pa.Table | None = None

        self.processor_specs = [
            ProcessorSpec(
                processor_type=ImageWarpingFC1ATrifocalWideProcessor,
                input_streams=[StreamName.IMAGE_FC1_A],
                output_stream=StreamName.IMAGE_FC1_A_TRIFOCAL_WIDE,
            ),
            ProcessorSpec(
                processor_type=ImageWarpingFC1BTrifocalWideProcessor,
                input_streams=[StreamName.IMAGE_FC1_B],
                output_stream=StreamName.IMAGE_FC1_B_TRIFOCAL_WIDE,
            ),
            ProcessorSpec(
                processor_type=ImageWarpingFC1AToTrifocalCNNFarViewProcessor,
                input_streams=[StreamName.IMAGE_FC1_A],
                output_stream=StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_FAR,
            ),
            ProcessorSpec(
                processor_type=ImageWarpingFC1AToTrifocalCNNMidViewProcessor,
                input_streams=[StreamName.IMAGE_FC1_A],
                output_stream=StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_MID,
            ),
            ProcessorSpec(
                processor_type=ImageWarpingFC1AToTrifocalCNNWideViewProcessor,
                input_streams=[StreamName.IMAGE_FC1_A],
                output_stream=StreamName.IMAGE_FC1_A_TRIFOCAL_CNN_WIDE,
            ),
            ProcessorSpec(
                processor_type=ImageConversionTVleftProcessor,
                input_streams=[StreamName.IMAGE_TV_LEFT],
                output_stream=StreamName.IMAGE_TV_LEFT,
            ),
            ProcessorSpec(
                processor_type=ImageConversionTVrightProcessor,
                input_streams=[StreamName.IMAGE_TV_RIGHT],
                output_stream=StreamName.IMAGE_TV_RIGHT,
            ),
            ProcessorSpec(
                processor_type=ImageConversionTVfrontProcessor,
                input_streams=[StreamName.IMAGE_TV_FRONT],
                output_stream=StreamName.IMAGE_TV_FRONT,
            ),
            ProcessorSpec(
                processor_type=ImageConversionTVrearProcessor,
                input_streams=[StreamName.IMAGE_TV_REAR],
                output_stream=StreamName.IMAGE_TV_REAR,
            ),
            ProcessorSpec(
                processor_type=RadarAccumulationProcessor,
                input_streams=[
                    StreamName.RADAR_CR_REAR_LEFT,
                    StreamName.RADAR_CR_REAR_RIGHT,
                    StreamName.RADAR_CR_FRONT_RIGHT,
                    StreamName.RADAR_CR_FRONT_LEFT,
                    StreamName.RADAR_LRR5_FRONT,
                    StreamName.RADAR_LRR5_REAR,
                    StreamName.IMAGE_FC1_A,
                    StreamName.GT_ODOMETRY,
                ],
                output_stream=StreamName.RADAR_COMBINED,
            ),
            ProcessorSpec(
                IdentityProcessor,
                input_streams=[StreamName.LIDAR_VELODYNE_EMC],
                output_stream=StreamName.LIDAR_VELODYNE_EMC,
            ),
        ]

    def __enter__(self, filter_expr: ColumnExpressionArgument[bool] | None = None) -> Self:
        """Enter the context manager and cache warehouse data matching the filter.

        Args:
            filter_expr: Optional SQL Alchemy filter expression for the recordings

        Returns:
            The stream generator instance
        """

        recording_streams = join(
            left=RECORDINGS_TABLE,
            right=STREAMS_TABLE,
            onclause=RECORDINGS_TABLE.c.split_hash == STREAMS_TABLE.c.split_hash,
        )
        streams_query = select(literal_column("streams.*")).select_from(recording_streams)
        if filter_expr is not None:
            streams_query = streams_query.where(filter_expr)
        self._streams = self._warehouse.query(streams_query)

        self._image_frames = self._query_images_frames(recording_streams, filter_expr)
        self._lidar_frames = self._query_lidar_frames(recording_streams, filter_expr)

        return self

    def _query_images_frames(
        self, recording_streams: pa.Table, filter_expr: ColumnExpressionArgument[bool] | None = None
    ) -> pa.Table:
        """Query image frames from the data warehouse.

        Args:
            recording_streams: Table containing the recording and stream data
            filter_expr: Optional SQL Alchemy filter expression for the recordings

        Returns:
            PyArrow Table containing the image frames
        """
        # Get distinct stream types for sample queries
        streams_images = join(
            left=recording_streams,
            right=IMAGE_FRAMES_TABLE,
            onclause=and_(
                STREAMS_TABLE.c.split_hash == IMAGE_FRAMES_TABLE.c.split_hash,
                STREAMS_TABLE.c.stream_hash == IMAGE_FRAMES_TABLE.c.stream_hash,
            ),
        )
        images_query = select(IMAGE_FRAMES_TABLE).select_from(streams_images)

        # For now we only select non-anonymized RGB label-views
        images_query = images_query.where(
            IMAGE_FRAMES_TABLE.c.view == ImageView.LABEL.value,
            IMAGE_FRAMES_TABLE.c.color_space == ColorFormat.RGB.value,
            IMAGE_FRAMES_TABLE.c.anonymizer__git_commit.is_(None),
        )
        if filter_expr is not None:
            images_query = images_query.where(filter_expr)
        images_query = images_query.order_by(IMAGE_FRAMES_TABLE.c.stream_hash)
        return self._warehouse.query(images_query)

    def _query_lidar_frames(
        self, recording_streams: pa.Table, filter_expr: ColumnExpressionArgument[bool] | None = None
    ) -> pa.Table:
        """Query lidar scans from the data warehouse.

        Args:
            recording_streams: Table containing the recording and stream data
            filter_expr: Optional SQL Alchemy filter expression for the recordings

        Returns:
            PyArrow Table containing the lidar scans
        """

        lidar_frames = join(
            left=recording_streams,
            right=LIDAR_SCANS_MOTION_COMPENSATED_TABLE,
            onclause=and_(
                STREAMS_TABLE.c.split_hash == LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.split_hash,
                STREAMS_TABLE.c.stream_hash == LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.stream_hash,
            ),
        )
        lidar_frames_query = select(LIDAR_SCANS_MOTION_COMPENSATED_TABLE).select_from(lidar_frames)

        if filter_expr is not None:
            lidar_frames_query = lidar_frames_query.where(filter_expr)
        return self._warehouse.query(lidar_frames_query)

    def __exit__(
        self, exc_type: type[BaseException] | None, exc_val: BaseException | None, exc_tb: TracebackType | None
    ) -> None:
        """Exit the context manager."""
        # Clear cached data
        self._streams = None
        self._image_frames = None
        self._lidar_frames = None

    def _apply_downsampling(self, recording: AdaRecording) -> bool:
        return self._frequency is not None and TaskName.OCC not in recording.task

    def add_streams_to_recording(self, recording: AdaRecording) -> None:
        """Add streams to a recording.

        Args:
            recording: Recording to add streams to
        """

        assert self._streams is not None, "Stream Generator must be used inside a context manager"
        assert self._image_frames is not None, "Stream Generator must be used inside a context manager"

        recording_streams = self._streams.filter(
            build_arrow_expr(RECORDINGS_TABLE.c.split_hash.name, recording.split_hash)
        )

        # First make FC1 streams
        for stream_data in iterate_arrow_table_as_pydict(recording_streams):
            if stream_data[STREAMS_TABLE.c.stream.name] == "image_fc1":
                for stream in self._make_image_streams(recording, stream_data):
                    recording.add_stream(stream)

        for stream_data in iterate_arrow_table_as_pydict(recording_streams):
            if stream_data[STREAMS_TABLE.c.stream.name].startswith("image_tv"):
                for stream in self._make_image_streams(recording, stream_data):
                    recording.add_stream(stream)
            elif stream_data[STREAMS_TABLE.c.stream.name].startswith("radar"):
                stream = self._make_radar_stream(recording, stream_data)
                recording.add_stream(stream)
            elif stream_data[STREAMS_TABLE.c.stream.name].startswith(
                StreamName.LIDAR_VELODYNE_EMC
            ):  # for now: only compensated velodyne
                stream = self._make_lidar_stream(recording, stream_data)
                recording.add_stream(stream)

    def _get_common_stream_params(self, recording: AdaRecording, stream_data: dict[str, Any]) -> dict[str, Any]:
        """Get common stream parameters from the data.

        Args:
            recording: The recording for which to create the stream
            stream_data: Data of the stream read from the data warehouse

        Returns:
            Common stream parameters
        """
        return {
            "name": stream_data[STREAMS_TABLE.c.stream.name],
            "sensor_name": stream_data[STREAMS_TABLE.c.stream.name],
            "recording_id": recording.recording_id,
            "drive_hash": stream_data["drive_hash"],
            "split_hash": stream_data[STREAMS_TABLE.c.split_hash.name],
            "sensor_stream_hash": stream_data["stream_hash"],
            "extractor_version": stream_data["extractor_version"],
            "syscal_rotation": _get_default_rotation(stream_data["syscal_rotation"]),
            "syscal_translation": _get_default_translation(stream_data["syscal_translation"]),
            "offline_raw_rotation": _get_rotation(stream_data["offline_raw_rotation"]),
            "offline_raw_translation": _get_translation(stream_data["offline_raw_translation"]),
        }

    def _make_image_streams(self, recording: AdaRecording, stream_data: dict[str, Any]) -> list[AdaImageStream]:
        """Create an image stream object from the data.

        Args:
            recording: The recording for which to create the stream
            stream_data: Data of the stream read from the data warehouse

        Returns:
            A new image stream object with samples added. None if the stream is empty.
        """

        stream_params = self._get_common_stream_params(recording, stream_data)
        stream_name = stream_params.pop("name")
        streams: list[AdaImageStream] = []
        context_samples = self._make_image_samples(stream_data, recording)
        for context, samples in context_samples.items():
            # Add context suffix to stream name (if there is more than a single context)
            name = f"{stream_name}_{context.value}" if len(context_samples) > 1 else stream_name
            stream = AdaImageStream(
                name=name,
                sample_type=AdaImageSample,
                samples=samples,  # type: ignore[reportArgumentType]
                **stream_params,
                container=None,
                context=context,
                view=ImageView.LABEL,
                color_format=ColorFormat.RGB,
                intrinsics=self._camera_intrinsics_from_string(stream_data["rectified_intrinsics"]),
                offline_rectified_rotation=_get_rotation(stream_data["offline_rectified_rotation"]),
                offline_rectified_translation=_get_translation(stream_data["offline_rectified_translation"]),
            )
            streams.append(stream)
        return streams

    def _make_image_samples(
        self, stream_data: dict[str, Any], recording: AdaRecording
    ) -> dict[ImageContext, list[AdaImageSample]]:
        """Create image samples from the data.

        Args:
            stream_data: Data of the stream read from the data warehouse
            recording: The recording that can be used to access timestamps of other streams

        Returns:
            List of image samples for the stream
        """
        assert self._image_frames is not None

        filter_expr = [
            build_arrow_expr(IMAGE_FRAMES_TABLE.c.stream_hash.name, stream_data[STREAMS_TABLE.c.stream_hash.name]),
        ]
        stream_frames = self._image_frames.filter(functools.reduce(operator.and_, filter_expr))

        # We return a group of samples for each context, as the streams need to be split by context
        context_values = pc.unique(stream_frames[IMAGE_FRAMES_TABLE.c.context.name]).to_pylist()  # type: ignore[reportAttributeAccessIssue]
        context_samples: dict[ImageContext, list[AdaImageSample]] = {}
        for ctx in context_values:
            context_frames = stream_frames.filter(build_arrow_expr(IMAGE_FRAMES_TABLE.c.context.name, ctx)).sort_by(
                IMAGE_FRAMES_TABLE.c.recorded_at_unix.name
            )

            if self._apply_downsampling(recording):
                assert self._frequency is not None
                context_frames = downsample_camera_stream(context_frames, self._frequency, recording)

            context_frames = limit_if_in_debug_mode(context_frames)  # limit number of samples if in debug mode

            samples = [
                AdaImageSample(
                    name=frame[IMAGE_FRAMES_TABLE.c.image_id.name],
                    timestamp=frame[IMAGE_FRAMES_TABLE.c.recorded_at_unix.name],
                    frame_number=frame[IMAGE_FRAMES_TABLE.c.frame_number.name],
                    image=StoredDatum(
                        storage_location=AzureBlobLocation.from_blob_url(
                            blob_url=frame[IMAGE_FRAMES_TABLE.c.tds_file_url.name]
                        ),
                        data_type=PngImageData,
                        digest=frame[IMAGE_FRAMES_TABLE.c.file_hash.name],
                        digest_type="sha256",
                    ),
                )
                for frame in iterate_arrow_table_as_pydict(context_frames)
            ]

            context_samples[ImageContext(ctx)] = samples
        return context_samples

    @staticmethod
    def _camera_intrinsics_from_string(intrinsics: str) -> CylinderCameraModel | DeformedCylinderCameraModel:
        """Parse camera intrinsics from a string.

        Args:
            intrinsics: Intrinsics as a string

        Returns:
            Camera intrinsics model
        """
        data = json.loads(intrinsics)
        assert "type" in data, "Missing camera model type"
        if data["type"] == "CYLINDER":
            return CylinderCameraModel(
                size=HW(
                    width=int(data[CameraModelFields.TARGET_DIMENSIONS][0]),
                    height=int(data[CameraModelFields.TARGET_DIMENSIONS][1]),
                ),
                focal_length=Vector2d.from_list(data[CameraModelFields.FOCAL_LENGTH]),
                principal_point=Vector2d.from_list(data[CameraModelFields.PRINCIPAL_POINT]),
            )
        if data["type"] == "DEFORMEDCYLINDER":
            return DeformedCylinderCameraModel(
                size=HW(
                    width=int(data[CameraModelFields.TARGET_DIMENSIONS][0]),
                    height=int(data[CameraModelFields.TARGET_DIMENSIONS][1]),
                ),
                focal_length=Vector2d.from_list(data[CameraModelFields.FOCAL_LENGTH]),
                principal_point=Vector2d.from_list(data[CameraModelFields.PRINCIPAL_POINT]),
                cut_angle_lower=data[CameraModelFields.CUT_ANGLE_LOWER],
                cut_angle_upper=data[CameraModelFields.CUT_ANGLE_UPPER],
            )
        err_msg = f"Unknown camera model type: {data['model_type']}"
        raise ValueError(err_msg)

    def _make_radar_stream(self, recording: AdaRecording, stream_data: dict[str, Any]) -> AdaRadarStream:
        """Create a AdaRadarStream object from the data.

        Args:
            recording: The recording for which to create the stream
            stream_data: Data of the stream read from the data warehouse

        Returns:
            A new AdaRadarStream object without samples as the data for the full recording is stored in a container
        """

        stream = AdaRadarStream(
            sample_type=AdaRadarSample,
            samples=[],
            **self._get_common_stream_params(recording, stream_data),
            container=StoredDatum(
                storage_location=AzureBlobLocation.from_blob_url(
                    blob_url=stream_data[STREAMS_TABLE.c.container_file_url.name]
                ),
                data_type=Hdf5Data,
                digest=stream_data[STREAMS_TABLE.c.container_file_hash.name],
            ),
        )

        return stream

    def _make_lidar_stream(self, recording: AdaRecording, stream_data: dict[str, Any]) -> AdaLidarStream:
        """Create lidar stream from the data.

        Args:
            recording: The recording for which to create the stream
            stream_data: Data of the stream read from the data warehouse

        Returns:
            Lidar stream object with lidar scans samples
        """

        assert self._lidar_frames is not None

        # stream_hash is different due to "_motion_compensated" suffix, so we filter only by split_hash
        stream_lidar_frames = self._lidar_frames.filter(
            build_arrow_expr(STREAMS_TABLE.c.split_hash.name, stream_data[STREAMS_TABLE.c.split_hash.name])
        ).sort_by(LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.recorded_at_unix.name)

        assert len(stream_lidar_frames) > 0

        if self._apply_downsampling(recording):  # downsample lidar stream
            lidar_timestamps = (
                stream_lidar_frames[LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.recorded_at_unix.name]
                .to_numpy()
                .astype(np.int64)
            )
            camera_timestamps = [sample.timestamp for sample in recording.streams[StreamName.IMAGE_FC1_A].samples]

            # Cameras have rolling shutter of ca. 25 ms and camera timestamps correspond to the first row of the image.
            # Lidar scans are compensated to image center --> expected time delay for the lidar scans should be
            # at most ~13 ms, including a robustness margin of 3 ms --> 16 ms time delta.
            indices = find_closest_sample_timestamps_for(
                target_timestamps=np.array(camera_timestamps),
                sample_timestamps=lidar_timestamps,
                max_timedelta_prev=milliseconds_to_nanoseconds(0),
                max_timedelta_next=milliseconds_to_nanoseconds(16),
            )
            stream_lidar_frames = stream_lidar_frames.take(np.unique(indices[indices != -1]))

        stream = AdaLidarStream(
            sample_type=AdaLidarSample,
            samples=self._make_lidar_samples(stream_lidar_frames),  # type: ignore[reportArgumentType]
            **self._get_common_stream_params(recording, stream_data),
            container=None,
            type=LidarSensorType.VELODYNE,
            egomotion_compensated=True,
            undistorted=True,
        )

        return stream

    def _make_lidar_samples(self, stream_lidar_frames: pa.Table) -> list[AdaLidarSample]:
        """Create lidar samples from the data.

        Args:
            stream_lidar_frames: pa.Table with data for all lidar samples in a stream. The table is assumed to
                                 be already be sorted by min_timestamp.

        Returns:
            List of lidar samples for the stream
        """

        stream_lidar_frames = limit_if_in_debug_mode(stream_lidar_frames)  # limit number of samples if in debug mode

        samples = [
            AdaLidarSample(
                name=frame[LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.file_hash.name],
                timestamp=frame[LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.recorded_at_unix.name],
                min_timestamp=frame[LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.recorded_at_unix.name],
                max_timestamp=frame[LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.recorded_at_unix.name],
                point_cloud=StoredDatum(
                    storage_location=AzureBlobLocation.from_blob_url(
                        blob_url=frame[LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.tds_file_url.name]
                    ),
                    data_type=PointCloudData,
                    digest=frame[LIDAR_SCANS_MOTION_COMPENSATED_TABLE.c.file_hash.name],
                    digest_type="sha256",
                ),
            )
            for frame in iterate_arrow_table_as_pydict(stream_lidar_frames)
        ]

        return samples


class CameraModelFields(str, Enum):
    """Enum to define the fields of the camera model embedded in a json."""

    TARGET_DIMENSIONS = "target_dimensions"
    FOCAL_LENGTH = "focal_length"
    PRINCIPAL_POINT = "principal_point"
    CUT_ANGLE_LOWER = "cut_angle_lower"
    CUT_ANGLE_UPPER = "cut_angle_upper"


def _get_rotation(data: list[float] | None) -> Rotation | None:
    """Get rotation from list of floats."""
    return Rotation.from_list(data) if data is not None else None


def _get_default_rotation(data: list[float] | None) -> Rotation:
    """Get default rotation from list of floats when no data is available."""
    return Rotation.from_list(data) if data is not None else Rotation.from_list([0.0, 0.0, 0.0, 1.0])


def _get_translation(data: list[float] | None) -> Translation | None:
    """Get translation from list of floats."""
    return Translation.from_list(data) if data is not None else None


def _get_default_translation(data: list[float] | None) -> Translation:
    """Get default translation from list of floats when no data is available."""
    return Translation.from_list(data) if data is not None else Translation.from_list([0.0, 0.0, 0.0])


def downsample_camera_stream(frames_table: pa.Table, target_frequency: int, recording: AdaRecording) -> pa.Table:
    """Downsample a camera stream to a target frequency.

    Args:
        frames_table: Table containing the camera frames sorted by timestamp
        target_frequency: Target frequency to downsample to
        recording: Recording which can be used to access already existing timestamps from other streams

    Returns:
        Downsampled frames table.
    """

    stream_name = frames_table[IMAGE_FRAMES_TABLE.c.stream.name].unique().to_pylist()[0]

    # Downsample FC1 streams
    if stream_name == "image_fc1":
        timestamps = frames_table[IMAGE_FRAMES_TABLE.c.recorded_at_unix.name].to_numpy()
        stream_frequency = 1e9 / np.mean(np.diff(timestamps))
        if np.isclose(stream_frequency, np.round(stream_frequency), atol=0.01):
            stream_frequency = int(np.round(stream_frequency))  # convert to int if possible

        assert target_frequency <= stream_frequency, f"Target frequency can be {stream_frequency} Hz max."
        # The stream has a frequency of N Hz, so there are N samples per second. To achieve the target
        # frequency we calculate how many frames need to be skipped.
        frame_offset = int(stream_frequency // target_frequency)
        if not np.isclose(stream_frequency % target_frequency, 0):
            _LOGGER.info(
                f"Specified target frequency of {target_frequency} is not a divisor of the FC1 frame rate "
                f"({stream_frequency} hz). Using closest possible frequency "
                f"{stream_frequency / frame_offset:.2f} hz instead."
            )

        indices = list(range(len(frames_table)))[::frame_offset]  # take every nth frame
        return frames_table.take(indices)

    # Downsample TV streams based on timestamps from IMAGE_FC1_A stream
    tv_timestamps = frames_table[IMAGE_FRAMES_TABLE.c.recorded_at_unix.name].to_numpy().astype(np.int64)
    fc1_a_timestamps = [sample.timestamp for sample in recording.streams[StreamName.IMAGE_FC1_A].samples]
    indices = find_closest_sample_timestamps_for(
        target_timestamps=np.array(fc1_a_timestamps),
        sample_timestamps=np.array(tv_timestamps),
        max_timedelta_prev=milliseconds_to_nanoseconds(5),
        max_timedelta_next=milliseconds_to_nanoseconds(5),
    )
    frames_table = frames_table.take(np.unique(indices[indices != -1]))
    assert len(frames_table) == len(fc1_a_timestamps)
    return frames_table
