"""Implementation of the Image Warping processor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import json
from functools import cache
from pathlib import Path
from typing import Any, final

import numpy as np
from PIL import Image
from warper_package.image_warper import ImageWarper

from data_formats.warping.alliance import get_calib_config_path as get_alliance_calib_config_path
from loomy.core.accessors import RecordingAccessor, RecordingRequest
from loomy.core.stream import Stream
from loomy_usecases.ada.processors.image_conversion_processor import ImageConversionProcessor, ImageFileFormat
from loomy_usecases.ada.schema.streams.image import (
    HW,
    CylinderCameraModel,
    DeformedCylinderCameraModel,
    Vector2d,
)


class ImageWarpingProcessor(ImageConversionProcessor):
    """Processor that warps the image samples in the stream."""

    version = ImageConversionProcessor.version + "_0.1"

    def __init__(
        self,
        stream_name: str,
        target_format: ImageFileFormat,
        view: str,
        warping_config: str,
        jpeg_quality: int | None = None,
    ) -> None:
        """Initialize the ImageWarpingProcessor.

        Args:
            stream_name: The name of the stream to process.
            target_format: The target format for the image conversion ("png", "jpeg", etc.).
            view: The view type for the warping processor.
            warping_config: The configuration file for the warping process.
            jpeg_quality: The quality of the JPEG image (0-100).
        """
        super().__init__(stream_name=stream_name, target_format=target_format, jpeg_quality=jpeg_quality)
        self._view = view
        self._new_stream_name = self._stream_name + "_" + self._view
        self._warp_config_file_path = get_alliance_calib_config_path(warping_config)
        self._image_warper = _get_image_warper(self._warp_config_file_path)

    def get_input_request(self) -> RecordingRequest:
        """Get input data request for the processor."""
        return self._get_input_request(self._new_stream_name)

    @final
    def get_stream(self, recording_accessor: RecordingAccessor) -> Stream:
        """Get the stream that this processor will create.

        Args:
            recording_accessor: The recording data to process

        Returns:
            Stream object created through `self._make_stream`.
        """
        stream_accessor = recording_accessor.streams[self._stream_name]
        stream_data = stream_accessor.model_dump(exclude={"samples"})
        stream_data.update(
            {
                "name": self._new_stream_name,
                "intrinsics": _get_warp_intrinsics(self._warp_config_file_path),
            }
        )
        stream_data.pop("stream_type")
        stream = self.make_stream(
            recording_accessor,
            **stream_data,
        )
        return stream

    def _convert_image(self, image: Image.Image) -> Image.Image:
        """Convert the image to the target format.

        Args:
            image: The image to convert.

        Returns:
            The converted image.
        """
        warped_image_array = self._image_warper.warp_image(np.array(image))
        return Image.fromarray(warped_image_array.astype(np.uint8))


@cache
def _get_image_warper(warp_config_file_path: str) -> ImageWarper:
    """Get an ImageWarper instance for the given configuration file path.

    Instantiation of the ImageWarper is rather expensive and spams the logs, so we cache it.
    """
    return ImageWarper(warp_config_file_path)


@cache
def _get_warp_intrinsics(warp_config_file_path: str) -> CylinderCameraModel | DeformedCylinderCameraModel:
    """Get the intrinsics of from the warper configuration.

    Args:
        warp_config_file_path: The path to the warping configuration file.

    Returns:
        The intrinsics of the warped image configuration.
    """
    with Path(warp_config_file_path).open() as file:
        warp_config = json.load(file)

    if not warp_config.get("target", None) or not warp_config["target"].get("intrinsics", None):
        error_message = "No target intrincs found in warp config file"
        raise ValueError(error_message)

    target_intrinsics = warp_config["target"]["intrinsics"]
    target_type = target_intrinsics["type"]
    assert target_type in ("Cylinder", "DeformedCylinder")
    camera_model_type = CylinderCameraModel if target_type == "Cylinder" else DeformedCylinderCameraModel

    additional_params: dict[str, Any] = {}
    if target_type == "DeformedCylinder":
        additional_params = {
            "cut_angle_upper": target_intrinsics["cut_angle_upper"],
            "cut_angle_lower": target_intrinsics["cut_angle_lower"],
        }

    return camera_model_type(
        size=HW(
            height=int(target_intrinsics["image_dimensions"][0]),
            width=int(target_intrinsics["image_dimensions"][1]),
        ),
        focal_length=Vector2d.from_list(target_intrinsics["focal_length"]),
        principal_point=Vector2d.from_list(target_intrinsics["principal_point"]),
        **additional_params,
    )
