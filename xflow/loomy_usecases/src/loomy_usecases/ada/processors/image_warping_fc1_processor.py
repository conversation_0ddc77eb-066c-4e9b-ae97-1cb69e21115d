"""Implementation of the FC1 Image Warping processors."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from loomy_usecases.ada.definitions import StreamName
from loomy_usecases.ada.processors.image_conversion_processor import ImageFileFormat
from loomy_usecases.ada.processors.image_warping_processor import ImageWarpingProcessor


class ImageWarpingFC1ATrifocalWideProcessor(ImageWarpingProcessor):
    """Processor that warps the image samples in the stream."""

    version = ImageWarpingProcessor.version + "_jpeg80"

    def __init__(self) -> None:
        """Initialize the FC1TrifocalWideWarpingProcessor."""
        super().__init__(
            stream_name=StreamName.IMAGE_FC1_A,
            target_format=ImageFileFormat.JPEG,
            view="trifocal_wide",
            warping_config="fc1_label_view_to_trifocal_wide_view",
            jpeg_quality=80,
        )


class ImageWarpingFC1BTrifocalWideProcessor(ImageWarpingProcessor):
    """Processor that warps the image samples in the stream."""

    version = ImageWarpingProcessor.version + "_jpeg80"

    def __init__(self) -> None:
        """Initialize the FC1TrifocalWideWarpingProcessor."""
        super().__init__(
            stream_name=StreamName.IMAGE_FC1_B,
            target_format=ImageFileFormat.JPEG,
            view="trifocal_wide",
            warping_config="fc1_label_view_to_trifocal_wide_view",
            jpeg_quality=80,
        )


class ImageWarpingFC1AToTrifocalCNNFarViewProcessor(ImageWarpingProcessor):
    """Processor that warps the FC1_A label view samples to the trifocal cnn L0 far view."""

    version = ImageWarpingProcessor.version + "_jpeg80"

    def __init__(self) -> None:
        """Initialize the ImageWarpingFC1AToTrifocalCNNFarViewProcessor."""
        super().__init__(
            stream_name=StreamName.IMAGE_FC1_A,
            target_format=ImageFileFormat.JPEG,
            view="trifocal_cnn_far",
            warping_config="fc1_label_view_to_trifocal_l0_cnn_far_view_v2",
            jpeg_quality=80,
        )


class ImageWarpingFC1AToTrifocalCNNMidViewProcessor(ImageWarpingProcessor):
    """Processor that warps the FC1_A label view samples to the trifocal cnn L1 mid view."""

    version = ImageWarpingProcessor.version + "_jpeg80"

    def __init__(self) -> None:
        """Initialize the ImageWarpingFC1AToTrifocalCNNMidViewProcessor."""
        super().__init__(
            stream_name=StreamName.IMAGE_FC1_A,
            target_format=ImageFileFormat.JPEG,
            view="trifocal_cnn_mid",
            warping_config="fc1_label_view_to_trifocal_l1_cnn_mid_view_v2",
            jpeg_quality=80,
        )


class ImageWarpingFC1AToTrifocalCNNWideViewProcessor(ImageWarpingProcessor):
    """Processor that warps the FC1_A label view samples to the trifocal cnn L2 wide view."""

    version = ImageWarpingProcessor.version + "_jpeg80"

    def __init__(self) -> None:
        """Initialize the ImageWarpingFC1AToTrifocalCNNWideViewProcessor."""
        super().__init__(
            stream_name=StreamName.IMAGE_FC1_A,
            target_format=ImageFileFormat.JPEG,
            view="trifocal_cnn_wide",
            warping_config="fc1_label_view_to_trifocal_l2_cnn_wide_view_v2",
            jpeg_quality=80,
        )
