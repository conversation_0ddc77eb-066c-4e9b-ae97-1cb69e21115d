"""Module with common constants."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON>sch GmbH. All rights reserved.
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


def get_shm_size(compute_target: str, security_factor: float = 1.5, default_shm_size: float = 2.0) -> str:
    """Resolve the shared memory size for the compute target.

    Args:
        compute_target: The name of the compute target.
        security_factor: The factor to multiply the shm size with.
        default_shm_size: The default shm size.

    Returns the shm size for the compute target in the format '<size>g', e.g., '2g' for 2 GB.
    """

    # This hardcoded dictionary contains the pre-defined shm sizes for the different node types
    # for the PyTorch and Pyper data loading. The required shm size scales 1) with the current batch size and 2)
    # with the number of workers.
    # For Pyper:
    #   Regarding 1) the current batch size in the MTL setup is 10 which equals 323 MB of data per batch,
    #   i.e. 323 MB per worker.
    #   Regarding 2) the number of workers is defined by the number of CPUs per node and is currently calculated as
    #   max((cpu_count if cpu_count is not None else 1) // 3, 1)`
    #   Thus, roughly the number of cpu cores divided by 3, resulting in eg 32 workers for the 4a100 node
    #   2GB is set as smallest shm size, which is the usual default.
    # For PyTorch:
    #   We can be generous with shm for all node types which are primarily in use for xtorch, because nothing beyond
    #   data loading consumes significant amounts of RAM.
    shm_size_per_node = {
        "gpupoola100": 16.0,
        "gpupool1a100": 60.0,
        "gpupool2a100": 120.0,
        "gpupool4a100": 240.0,
        "gpupool8A100": 480.0,  # out of 900GB
        "gpupool4coret4": 2.0,
        "gpupoolt4": 8.0,
        "gpupoolv100": 4.0,
        "gpupool2v100": 8.0,
        "gpupoolh100": 192.0,  # out of 320GB
        "gpupool2h100": 384.0,  # out of 640GB
        "gpupool4t4": 32.0,
        "gpupool4t4-lp": 32.0,
    }

    shm_size = shm_size_per_node.get(compute_target, default_shm_size)

    # Multiply the shm size by the security factor
    shm_size = round(shm_size * security_factor, 1)

    # Convert the shm size to string and append 'g'
    shm_size_str = f"{shm_size}g"

    return shm_size_str
