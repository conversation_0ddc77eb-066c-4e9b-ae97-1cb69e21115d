"""Functions based on the arrow library.

For type and schema handling, the following nomenclature conventions are used:
- Field: The name of a field in a struct-like object with its type annotation (for python or arrow)
- (Python) Type: A (Python) type, e.g. a class or scalar
- (Type) Annotation: The type annotation of a field, e.g. a single type, a Union of types, typing.Annotated or similar
- Arrow Type: The type of a field in an arrow schema, e.g. a string, int, list, struct, etc.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import hashlib
from collections.abc import Callable, Generator
from dataclasses import dataclass
from enum import Enum, IntEnum
from functools import cache
from pathlib import Path
from types import UnionType
from typing import Annotated, Any, TypeVar, get_args, get_origin

import numpy as np
import pyarrow as pa
import pyarrow.compute as pc
from pydantic import BaseModel
from pydantic.fields import ComputedFieldInfo, FieldInfo
from pydantic.main import IncEx


@dataclass
class ArrowSerializeAs:
    """Annotation for pydantic fields.

    Used as annotation to indicate how the type of a pydantic field should be
    treated in an arrow schema.
    """

    type: pa.DataType


_PYTHON_TYPE_TO_ARROW_TYPE = {
    bool: pa.bool_(),
    str: pa.string(),
    int: pa.int64(),
    float: pa.float64(),
    bytes: pa.binary(),
    Path: pa.string(),
    np.float64: pa.float64(),
    np.float32: pa.float32(),
    np.float16: pa.float16(),
    np.int64: pa.int64(),
    np.int32: pa.int32(),
    np.int16: pa.int16(),
    np.int8: pa.int8(),
    np.uint8: pa.uint8(),
}


class ArrowSchemaBaseModel(BaseModel, frozen=True, extra="forbid"):
    """Base class for pydantic models that provide an interface for the generation of an Arrow schema."""

    @classmethod
    @cache
    def model_all_fields(cls) -> dict[str, FieldInfo | ComputedFieldInfo]:
        """Get both defined and computed fields of the BaseModel."""
        return {**cls.model_fields, **cls.model_computed_fields}

    @classmethod
    def arrow_schema(cls, exclude: IncEx | None = None) -> pa.Schema:
        """Get the arrow schema of the Model."""
        return arrow_schema_from_type(cls, exclude)

    @classmethod
    def arrow_schema_hash(cls, exclude: IncEx | None = None) -> str:
        """Get hash of the BaseModel's arrow schema.

        This hash is used to decide if two models have the same schema and can be stored in the same
        pyarrow table. Since a pa.Schema is sensitive to the order of the fields, we
        do not sort them for hashing.
        """
        schema = cls.arrow_schema(exclude=exclude)
        return hashlib.sha256(schema.to_string().encode()).hexdigest()

    @classmethod
    def get_field_annotations(cls) -> dict[str, type[Any]]:
        """Get a dictionary of field names with corresponding type annotations of the Model.

        This method can be overriden by subclasses to dynamically generate the fields and annotations at runtime,
        e.g. for properties that are generated by custom serializers which are not picked up automatically by pydantic
        in the __annotations__ property.

        By default, this method returns the model_fields and model_computed_fields with corresponding type annotations.

        Returns:
            A dictionary with the field names as keys and the type annotations as values.
        """
        annotations: dict[str, type[Any]] = {}
        for name, field_info in cls.model_fields.items():
            assert field_info.annotation is not None
            if not field_info.metadata:
                annotations[name] = field_info.annotation
            else:
                assert len(field_info.metadata) == 1, "Unpack operator in subscript requires Python 3.11+"
                annotations[name] = Annotated[field_info.annotation, field_info.metadata[0]]  # type: ignore[valid-type]

        for name, field_info in cls.model_computed_fields.items():
            annotations[name] = field_info.return_type

        return annotations


def arrow_schema_from_type(type_: type[Any], exclude: IncEx | None = None) -> pa.Schema:
    """Generate an arrow schema from a struct-like object type."""
    fields = arrow_fields_from_struct_type(type_, exclude)
    return pa.schema(fields)


def python_types_from_type_annotation(
    type_annotation: type[Any] | UnionType, *, exclude_none: bool = False
) -> list[type[Any]]:
    """Extract the python types from a type annotation.

    Args:
        type_annotation: A python type annotation of a single type, or Union of types.
        exclude_none: Whether to exclude the None type from the extracted types.

    Returns:
        A list of python types contained in the type annotation
    """
    if not isinstance(type_annotation, UnionType):
        python_types = [type_annotation]
    else:
        python_types = list(get_args(type_annotation))

    if exclude_none:
        return [type_ for type_ in python_types if type_ is not type(None)]
    return python_types


def python_fields_from_struct_type(type_: type[Any], exclude: IncEx | None = None) -> dict[str, type[Any]]:
    """Extract a dict of field names with corresponding type annotations for a struct-like object type.

    This function is used to extract the field annotations from a class, Pydantic BaseModel or NamedTuple.
    It has additional support to override the default __annotations__ field with a get_field_annotations classmethod
    in custom objects to dynamically generate the annotations at runtime.

    Args:
        type_: The type of a struct-like object (class, Pydantic Model, NamedTuple, ...).
        exclude: A list of field names to exclude from the extracted fields.

    Returns:
        A dictionary with the field names as keys and the type annotations as values.
    """
    if hasattr(type_, "get_field_annotations"):
        annotations = type_.get_field_annotations()
    else:
        annotations = type_.__annotations__

    if exclude is not None:
        return {key: value for key, value in annotations.items() if key not in exclude}
    return annotations


def arrow_fields_from_struct_type(type_: type[Any], exclude: IncEx | None = None) -> list[pa.Field]:
    """Extract a list of arrow fields for a struct-like object type.

    This function is used to extract the field annotations from a class, Pydantic BaseModel or NamedTuple and
    parse them into a list of arrow fields.

    Args:
        type_: The type of a struct-like object (class, Pydantic Model, NamedTuple, ...).
        exclude: A list of field names to exclude from the extracted fields.

    Returns:
        A list of arrow fields.
    """
    fields = []

    for field_name, field_annotation in python_fields_from_struct_type(type_, exclude).items():
        fields.append(
            pa.field(
                field_name,
                type=arrow_type_from_type_annotation(field_annotation),
                nullable=is_optional(field_annotation),
                metadata=None,
            )
        )

    return fields


def arrow_type_from_type_annotation(type_annotation: type[Any]) -> pa.DataType:
    """Convert a python type annotation to an arrow data type.

    Args:
        type_annotation: A python type annotation, e.g. a single type, a Union of types, typing.Annotated or similar.

    Returns:
        The arrow data type.
    """

    # Check if the type is using typing.Annotated for additional metadata
    if get_origin(type_annotation) is Annotated:
        type_metadata = type_annotation.__metadata__
        type_annotation = type_annotation.__args__[0]
    else:
        type_metadata = []

    # Check if the field has a custom serialization type
    for annotation in type_metadata:
        if isinstance(annotation, ArrowSerializeAs):
            return annotation.type

    # Extract the python types from the type annotation
    field_types = python_types_from_type_annotation(type_annotation, exclude_none=True)
    if len(field_types) > 1:
        # Union types need to be serialized as (json) string as otherwise the arrow schema would be ambiguous
        return pa.string()

    python_type = field_types[0]

    arrow_type = arrow_type_from_python_type(python_type)
    return arrow_type


def arrow_type_from_python_type(python_type: type[Any]) -> pa.DataType:  # noqa: PLR0911, C901
    """Convert a singular python type to an arrow data type.

    Args:
        python_type: A singular python type (not a Union or Annotated type).

    Returns:
        The arrow data type corresponding to the python type.
    """
    origin = get_origin(python_type)  # get base type of a generic type if applicable
    args = get_args(python_type)  # get args of a generic type if applicable
    underlying_type = origin or python_type

    if origin is None and python_type in _PYTHON_TYPE_TO_ARROW_TYPE:
        return _PYTHON_TYPE_TO_ARROW_TYPE[python_type]
    if issubclass(underlying_type, Enum):
        if issubclass(underlying_type, str):
            return pa.string()
        if issubclass(underlying_type, IntEnum):
            return pa.int64()
        err_msg = f"Enum type must be IntEnum or derive from str, got '{underlying_type}'"
        raise ValueError(err_msg)
    if is_structured_type(underlying_type):
        fields = arrow_fields_from_struct_type(underlying_type)
        if len(fields) == 0:
            error_msg = f"Type '{underlying_type}' does not have any fields"
            raise ValueError(error_msg)
        return pa.struct(fields)
    if origin in (list, set):
        return pa.list_(arrow_type_from_python_type(args[0]))
    if origin is tuple:
        assert (len(args) == 2 and args[1] is Ellipsis) or len(set(args)) == 1, "Only homogeneous tuples are supported."
        return pa.list_(arrow_type_from_python_type(args[0]))
    if origin is dict:
        return pa.map_(arrow_type_from_python_type(args[0]), arrow_type_from_python_type(args[1]))
    if origin is type:
        return pa.string()
    error_msg = f"Field type '{python_type}' is currently not supported"
    raise ValueError(error_msg)


def is_optional(field_type_annotation: type[Any] | UnionType) -> bool:
    """Check if a field is optional."""
    return field_type_annotation is type(None) or type(None) in get_args(field_type_annotation)


def is_structured_type(type_: type[Any]) -> bool:
    """Check if a type is a structured type."""
    return hasattr(type_, "__annotations__") or hasattr(type_, "get_field_annotations")


def build_arrow_expr(key: str, value: list[Any] | Any) -> pc.Expression:
    """Build an arrow filter expression for a key-value pair."""
    if isinstance(value, list):
        return pc.field(key).isin(value)
    return pc.field(key) == pc.scalar(value)


def iterate_arrow_table_as_pydict(table: pa.Table) -> Generator[dict[str, Any], None, None]:
    """Iterate an arrow table row by row and return the data as a generator returning dicts."""
    for row_batch in table.to_batches():
        for row_data in row_batch.to_pylist():
            assert isinstance(row_data, dict)
            yield row_data


_T = TypeVar("_T", bound=ArrowSchemaBaseModel)


def iterate_arrow_table_deserialized(
    table: pa.Table,
    obj_type: type[_T],
    callback: Callable[[_T, dict[str, Any]], None] | None = None,
) -> Generator[_T, None, None]:
    """Deserialize the rows of an arrow table into a pydantic model of type "ArrowSchemaBaseModel".

    Args:
        table: The arrow table whose rows should be deserialized.
        obj_type: The subclass type of "ArrowSchemaBaseModel" to deserialize the rows into.
        callback: An optional callback function that is called for each deserialized object given the row data.

    Returns:
        A generator yielding the deserialized objects.
    """
    for row_data in iterate_arrow_table_as_pydict(table):
        data = {key: value for key, value in row_data.items() if key in obj_type.model_all_fields()}
        obj = obj_type.model_validate(data)
        if callback is not None:
            callback(obj, row_data)
        yield obj
