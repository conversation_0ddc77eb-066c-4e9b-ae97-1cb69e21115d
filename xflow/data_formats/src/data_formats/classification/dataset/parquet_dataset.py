"""This module provides the ParquetDataset class for handling Parquet files."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import datetime
import logging
from collections.abc import Generator
from enum import Enum
from io import BytesIO

import numpy as np
import numpy.typing as npt
import pyarrow as pa
import pyarrow.compute as pc
import pyarrow.dataset as ds
import pyarrow.dataset as pda
from PIL import Image
from pydantic import ConfigDict, Field

from data_formats.classification.dataset.schema.arrow import ArrowSchemaBaseModel


class ClassifierDatasetSchema(ArrowSchemaBaseModel, frozen=True):
    """Pydantic schema for the dataset parquet file with optional fields."""

    model_config = ConfigDict(frozen=True, arbitrary_types_allowed=True)
    id: int = Field(..., description="Unique identifier for the record")
    snippet_data_without_schema: str = Field(..., description="Ground truth data")
    snippet_image_data: bytes = Field(..., description="Image data")
    snippet_image_type: str = Field(..., description="Type of image data, RGB or YUV")
    snippet_location: list[np.float32] = Field(..., description="Location of the snippet")
    box_width: np.float32 = Field(..., description="Width of the snippet")
    box_height: np.float32 = Field(..., description="Height of the snippet")
    split: str = Field(..., description="Type of dataset")
    object_id: str = Field(..., description="Unique identifier for the object")
    track_id: str = Field(..., description="Unique identifier for the object")
    view: str | None = Field(None, description="View of the object")
    stream_camera: str | None = Field(None, description="Camera stream")
    classifier_class_name: str = Field(..., description="Name of the classifier class")
    trafficSignClass: str = Field(..., description="Name of the traffic sign class")  # noqa: N815
    value: np.float32 = Field(..., description="Value attribute of the traffic sign")
    text: str = Field(..., description="Text data")
    country_code: str = Field(..., description="Country code")
    blacklisted: bool | None = Field(None, description="Blacklisted status")
    blacklisted_reason: str | None = Field(None, description="Reason for blacklisting")
    frame_image_path: str | None = Field(None, description="Path to the frame image")
    frame_label_path: str | None = Field(None, description="Path to the frame label")
    shape: str = Field(..., description="Shape of the object")
    hierarchy_class: str = Field(..., description="Hierarchy class of the object")
    is_inverted: bool = Field(..., description="LED/Inverted status")
    time_of_day: str | None = Field(None, description="Time of the day")
    isYawRotated: str | None = Field(None, description="Image Yaw rotation")  # noqa: N815
    isRollRotated: str | None = Field(None, description="Image Roll rotation")  # noqa: N815
    isDamaged: bool | None = Field(None, description="Image damage flag")  # noqa: N815
    isPartiallyOutOfImage: bool | None = Field(None, description="Snippet out of image flag")  # noqa: N815
    isOccluded: str | None = Field(None, description="Snippet occluded flag")  # noqa: N815
    job_id: str | None = Field(None, description="Job ID")
    is_on_vehicle: str | None = Field(None, description="Snippet on vehicle flag")


# Define a logger for this module
_LOGGER = logging.getLogger(__name__)


class ClassifierParquetDataset:
    """Class for handling Parquet files."""

    class TrainColumns(str, Enum):
        """Enum for classifier parquet column names."""

        CLASS_NAME = "classifier_class_name"
        IMAGE_DATA = "snippet_image_data"
        OBJECT_ID = "object_id"

    class EvilGTColumns(str, Enum):
        """Enum for classifier evil ground truth column names."""

        BOX_WIDTH = "box_width"
        BOX_HEIGHT = "box_height"
        CLASS_NAME = "classifier_class_name"
        VIEW = "view"
        COUNTRY_CODE = "country_code"
        SHAPE = "shape"
        HIERARCHY_CLASS = "hierarchy_class"
        IS_INVERTED = "is_inverted"
        TIME_OF_DAY = "time_of_day"
        IS_YAW_ROTATED = "isYawRotated"
        IS_ROLL_ROTATED = "isRollRotated"
        IS_DAMAGED = "isDamaged"
        IS_PARTIALLY_OUT_OF_IMAGE = "isPartiallyOutOfImage"
        IS_OCCLUDED = "isOccluded"

    def __init__(self, partition_count: int = 500) -> None:
        """Initialize the ParquetDataset object."""
        self._partition_count = partition_count

        # Convert existing pydantic schema to the pyarrow
        self.schema = ClassifierDatasetSchema.arrow_schema()
        # Ensure binary is large_binary type
        snippet_image_data_idx = self.schema.get_field_index("snippet_image_data")
        self.schema = self.schema.set(
            snippet_image_data_idx,
            pa.field("snippet_image_data", pa.large_binary(), nullable=False),
        )

        # Define the column names for the classifier parquet file
        self.column_names: list[str] = self.schema.names
        # derive column datatypes in pandas dataframe from the schema
        self.column_dtypes: list[str] = [self.schema.field(i).type for i in range(len(self.schema.names))]
        self.required_columns: list[str] = [i for i in self.schema.names if not self.schema.field(i).nullable]
        self.partition_columns: list[str] = ["country_code"]

    @classmethod
    def get_column_names(cls, group: type[Enum]) -> list[str]:
        """Get the list of column names from the provided Enum group.

        Args:
            group: Enum group containing column name values.

        Returns:
            List of column names as strings.
        """
        return [member.value for member in group]

    @staticmethod
    def encode_image_to_bytes(image_data: npt.NDArray[np.uint8], image_format: str = "PNG") -> bytes:
        """Encode image data to PNG in bytes encoded.

        Args:
            image_data: Image data as a NumPy array.
            image_format: Image format (e.g., "PNG", "JPEG"). Default is "PNG".

        Returns:
            A PNG image in bytes encoded format.
        """
        image = Image.fromarray(image_data)
        buf = BytesIO()
        image.save(buf, format="PNG")  # or 'JPEG'
        image_bytes = buf.getvalue()
        return image_bytes

    @staticmethod
    def decode_image_from_bytes(encoded_image: bytes) -> npt.NDArray[np.uint8]:
        """Decode an Image object from the Parquet column back to image data.

        Args:
            encoded_image: A PNG image in bytes encoded format.

        Returns:
            Decoded image data as a NumPy array.
        """
        img = Image.open(BytesIO(encoded_image))
        return np.array(img)

    def save_to_parquet(self, table: pa.Table, output_dir: str) -> None:
        """Save the PyArrow Table to a Parquet file."""
        # Ensure required columns are present in the PyArrow Table
        missing_columns = [column for column in self.required_columns if column not in table.column_names]
        if missing_columns:
            msg = f"Required columns are missing in the PyArrow Table: {missing_columns}"
            raise ValueError(msg)

        # Add optional fields with None if they are not present in the Table
        for field in self.schema.names:
            if field not in table.column_names:
                table = table.append_column(
                    field,
                    pa.array([None] * table.num_rows, type=self.schema.field(field).type),
                )

        # Reorder the table columns to match the schema's field order
        table = table.select(self.schema.names)

        # Ensure the table conforms to the schema's nullability constraints
        table = table.cast(self.schema)

        # Write the PyArrow Table to Parquet files
        unique_timestamp = datetime.datetime.now(datetime.timezone.utc).strftime("%Y%m%d%H%M%S%f")[:-3]
        unique_id = f"{unique_timestamp}{np.random.default_rng().integers(0, 1_000):03d}"
        pda.write_dataset(
            table,
            base_dir=output_dir,
            basename_template=f"part-{unique_id}-{{i}}.parquet",
            partitioning=self.partition_columns,
            schema=self.schema,
            format="parquet",
            max_rows_per_file=self._partition_count,
            max_rows_per_group=self._partition_count,
            existing_data_behavior="overwrite_or_ignore",
            partitioning_flavor="hive",
        )

    def load_parquet_dataset(
        self,
        parquet_files: list[str],
        filter_expressions: (list[tuple[str, str, str]] | pc.Expression | None) = None,
        selected_columns: list[str] | None = None,
        *,
        shuffle: bool = False,
    ) -> pda.Dataset:
        """Function to read parquet files as pyarrow datasets.

        Args:
            parquet_files: List of paths to the parquet files.
            filter_expressions: Filter expressions to apply to the dataset.
            selected_columns: List of columns to select from the dataset.
            shuffle: Whether to shuffle the parquet files for interleaving.

        Yields:
            dict: A dictionary containing the data from the parquet files.
        """

        if shuffle:
            np.random.default_rng().shuffle(parquet_files)

        dataset = ds.dataset(parquet_files, format="parquet", partitioning="hive")
        return dataset

    def load_parquet_to_data_batches(
        self,
        parquet_files: list[str],
        filter_expressions: (list[tuple[str, str, str]] | pc.Expression | None) = None,
        selected_columns: list[str] | None = None,
        *,
        shuffle: bool = False,
    ) -> Generator[pa.RecordBatch, None, None]:
        """Generator function to yield data batches from pyarrow datasets from the Parquet files.

        It yields data batches as RecordBatches, which can be used directly with PyArrow or converted to other formats.

        Args:
            parquet_files: List of paths to the parquet files.
            filter_expressions: Filter expressions to apply to the dataset.
            selected_columns: List of columns to select from the dataset.
            shuffle: Whether to shuffle the parquet files for interleaving.

        Returns:
            pa.RecordBatch: A RecordBatch containing the data from the parquet files.
        """

        dataset = self.load_parquet_dataset(
            parquet_files=parquet_files,
            filter_expressions=filter_expressions,
            selected_columns=selected_columns,
            shuffle=shuffle,
        )
        yield from dataset.to_batches(filter=filter_expressions, columns=selected_columns, use_threads=True)
