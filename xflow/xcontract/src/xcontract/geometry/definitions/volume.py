"""Volume data structures."""

from __future__ import annotations

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import NamedTuple


class VolumeRange(NamedTuple):
    """3D volume range."""

    x_min: float
    y_min: float
    z_min: float
    x_max: float
    y_max: float
    z_max: float

    @property
    def x_length(self) -> float:
        """Length of the volume in x direction."""
        return self.x_max - self.x_min

    @property
    def y_length(self) -> float:
        """Length of the volume in y direction."""
        return self.y_max - self.y_min

    @property
    def z_length(self) -> float:
        """Length of the volume in z direction."""
        return self.z_max - self.z_min
