"""Metadata definition for model deployment to monorepository."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import json
import logging
import pathlib
import re
from dataclasses import asdict, dataclass, field, fields
from typing import Any

import numpy as np
from numpy.typing import DTypeLike, NDArray

LOGGER = logging.getLogger(__name__)

DUMMY_METADATA_TASK_NAME = "dummy_task"


@dataclass
class TensorMetadata:
    """Metadata about a tensor."""

    shape: tuple[int, ...] = field(default_factory=tuple)
    datatype: str = "float32"
    transpose: tuple[int, ...] = field(default_factory=tuple)
    quantization_param_offset: float = 0.0
    quantization_param_scale: float = 1.0  # for QNN, in case a tensor is not quantized, this is set to 0
    quantized: bool = False
    attributes: str = ""
    order: int = -1

    @property
    def np_datatype(self) -> DTypeLike:
        """Returns the numpy dtype of the tensor."""

        if self.datatype == "bool":
            return np.bool_

        return getattr(np, self.datatype)

    def is_quantizable(self) -> bool:
        """Ensure inputs are fulfilling the requirements or constraints to be quantized.

        Checks if the tensor is a quantized tensor, has a valid scale, its quantization type is uint8 and the shape of
        the meta data and the actual tensor are matching.

        Args:
            tensor: a tensor which has the same shape as in the tensor_meta_data.
        """
        if self.quantized is False:
            # tensor is not quantized
            return False

        if self.quantization_param_scale == 0.0:
            # Wrong scale factor for quantization
            return False

        if self.datatype not in ("uint8", "uint16"):  # noqa: SIM103
            # unsupported data type of the quantized tensor
            return False

        return True

    def quantize_tensor(
        self, tensor: NDArray[np.float32]
    ) -> NDArray[np.uint8] | NDArray[np.uint16] | NDArray[np.float32]:
        """Quantize a tensor.

        Note right now we stick to the current QNN implementation which generates the quantized
        tensor using uint8 and not int8. Some other frameworks might behave differently, e.g. latest
        TF-Lite version.

        Note that QNN could also quantize tensors to uint16. If we would use that, then this function
        shall be adapted.

        Args:
            tensor: a float32 tensor which has the same shape as in the tensor_meta_data.

        Returns:
            tensor that is quantized to uint8 or uint16. If the tensor is not quantized, then it is returned as is.
        """
        if not self.is_quantizable():
            return tensor
        type_info = np.iinfo(self.np_datatype)
        unclipped = np.round(tensor / self.quantization_param_scale - self.quantization_param_offset)

        return np.clip(unclipped, type_info.min, type_info.max).astype(self.np_datatype)

    def dequantize_tensor(self, tensor: NDArray[Any]) -> NDArray[np.float32 | np.int32]:
        """De-Quantize a tensor.

        Note right now we stick to the current QNN implementation which generates the quantized
        tensor using uint8 and not int8. Some other frameworks might behave differently, e.g. latest
        TF-Lite version.

        Note that QNN could also quantize tensors to uint16. If we would use that, then this function
        shall be adapted.

        Args:
            tensor: a uint8 tensor which has the same shape as in the tensor_meta_data.

        Returns:
            tensor that is de-quantized to float32.
        """
        if self.is_quantizable():
            # Note: sometimes float32 + int results in a float64.
            return ((tensor.astype(np.int64) + self.quantization_param_offset) * self.quantization_param_scale).astype(
                np.float32
            )
        if self.np_datatype == np.float16:
            return tensor.astype(np.float32)
        return tensor


@dataclass
class TaskMetadata:
    """Task metadata with tensors."""

    tensors: dict[str, TensorMetadata] = field(default_factory=dict)

    # Per task list of output positions. [2,3,4] means task contains 3 output tensors
    # and are returned by inference application as 2nd, 3rd and 4th tensors.
    order: list[int] = field(default_factory=list)

    # some parts of metadata will be filled after evaluation
    metadata: dict[str, Any] = field(default_factory=dict)

    def finalize(self) -> None:
        """Calculate order of tensors in a head."""
        self.order.clear()
        for tensor in self.tensors.values():
            self.order.append(tensor.order)


@dataclass
class Metadata:
    """General model metadata."""

    model_name: str = ""
    model_version: str = ""
    conversion_date: str = ""
    azure_run_id: str = ""
    azure_experiment_name: str = ""
    azure_workspace_name: str = ""
    conversion_job_url: str = ""
    xflow_commit_hash: str = ""
    model_format: str = ""
    aimet_version: str | None = None
    qnn_custom_ops_version: str = ""
    qnn_sdk_version: str = ""
    qnn_hexagon_version: str = ""
    attributes: dict[str, Any] = field(default_factory=dict)


@dataclass
class ModelMetadata:
    """Aggregation of metadata used for deployment of a nvidia and qualcomm models to pace monorepository."""

    metadata: Metadata = field(default_factory=Metadata)

    # {input_name: TensorMetadata} mapping
    inputs: dict[str, TensorMetadata] = field(default_factory=dict)

    # {input_name: input_name_pattern} mapping rule, for grouping specific input tensors together.
    _input_groups_def: dict[str, str] = field(default_factory=dict)

    # {input_name: input_group} mapping, added for inputs which are/to-be grouped
    _input_group_mapping: dict[str, str] = field(default_factory=dict)

    # variables are intermediate tensors
    variables: dict[str, TensorMetadata] = field(default_factory=dict)

    # {output_task: TaskMetadata} mapping
    tasks: dict[str, TaskMetadata] = field(default_factory=dict)

    # Dictionary of profiling metrics
    profiling: dict[str, Any] = field(default_factory=dict)

    @classmethod
    def from_dict(cls, metadata_dict: dict[str, Any]) -> "ModelMetadata":
        """Construct ModelMetadata out of dict."""
        model_metadata = cls()
        model_metadata.metadata = Metadata(**metadata_dict["metadata"])
        # Parse inputs and input-groupings
        model_metadata.inputs = {}
        for input_name, input_metadata_or_grouped_inputs in metadata_dict["inputs"].items():
            if "tensors" not in input_metadata_or_grouped_inputs:
                model_metadata.inputs[input_name] = TensorMetadata(**input_metadata_or_grouped_inputs)
            else:
                # Incase, there are grouped inputs present, extract them
                for sub_input_name, sub_input_metadata in input_metadata_or_grouped_inputs["tensors"].items():
                    model_metadata.inputs[sub_input_name] = TensorMetadata(**sub_input_metadata)
                    model_metadata._input_group_mapping[sub_input_name] = input_name

        model_metadata.variables = {
            name: TensorMetadata(**tensor) for name, tensor in metadata_dict["variables"].items()
        }
        # Task is double nested into TaskMetadata -> TensorMetadata
        tasks: dict[str, TaskMetadata] = {}
        for task_name, task_metadata in metadata_dict["tasks"].items():
            task_tensors: dict[str, Any] = {"tensors": {}, "order": [], "metadata": {}}
            task_tensors["metadata"] = task_metadata.get("metadata", {})
            for tensor_name, tensor in task_metadata["tensors"].items():
                task_tensors["tensors"][tensor_name] = TensorMetadata(**tensor)
            task_tensors["order"] = task_metadata["order"]
            tasks[task_name] = TaskMetadata(**task_tensors)
        model_metadata.tasks = tasks
        return model_metadata

    def to_dict(self) -> dict[str, Any]:
        """Convert the metadata object to dictionary.

        Due to customization in model_metadata json's dictionary, specifically the
        input grouping, dataclass's asdict() couldn't be used.
        """
        model_metadata_dict = asdict(self)
        del model_metadata_dict["_input_groups_def"]
        del model_metadata_dict["_input_group_mapping"]

        model_inputs = model_metadata_dict["inputs"]

        # Add/Update first _input_group_mapping for all inputs
        for inp_name in model_inputs:
            for grp_name, grp_pattern in self._input_groups_def.items():
                grp_regex = re.compile(grp_pattern)
                if grp_regex.match(inp_name):
                    self._input_group_mapping[inp_name] = grp_name
                    break

        # Now, start grouping the inputs
        model_inputs_grouped = {}
        for inp_name, inp_meta in model_inputs.items():
            if inp_name in self._input_group_mapping:
                grp_name = self._input_group_mapping[inp_name]
                if grp_name not in model_inputs_grouped:
                    model_inputs_grouped[grp_name] = {}
                    model_inputs_grouped[grp_name]["tensors"] = {}
                model_inputs_grouped[grp_name]["tensors"][inp_name] = inp_meta
            else:
                # For non-grouped inputs, simply place in dictionary as it is.
                model_inputs_grouped[inp_name] = inp_meta

        # [IMPORTANT FOR DEPLYOMENT, specially around splitted models]
        # Sort the grouped inputs inside a group, by tensor-names, so that they are consistent between splitted models
        # and connected to each other via single port in correct order.
        for inp_or_grouped_tensors in model_inputs_grouped.values():
            if "tensors" not in inp_or_grouped_tensors:
                continue
            inp_or_grouped_tensors["tensors"] = dict(sorted(inp_or_grouped_tensors["tensors"].items()))

        # Replace with grouped inputs
        model_metadata_dict["inputs"] = model_inputs_grouped
        return model_metadata_dict

    def add_task(self, task_name: str, task_metadata: TaskMetadata | None = None) -> None:
        """Add empty output task.

        Args:
            task_name: Name of task to be added to the metadata
            task_metadata: Task metadata to be added. If None, then default is used
        """
        if task_metadata is None:
            task_metadata = TaskMetadata()
        self.tasks[task_name] = task_metadata

    def add_output_tensor(
        self, output_tensor_name: str, task_name: str | None = None, tensor_metadata: TensorMetadata | None = None
    ) -> None:
        """Find task corresponding output_tensor and add the tensor metadata to the task.

        Args:
            output_tensor_name: Name of the output tensor
            task_name: If provided, then the output tensor is added to the specified task.
                If None, then a suitable task is search via the tensor name.
            tensor_metadata: metadata for the output tensor. If None, then default TensorMetadata is used.
        """
        if tensor_metadata is None:
            tensor_metadata = TensorMetadata()
        if task_name is None:
            task_name = self.get_task_for_output_tensor(output_tensor_name)
        if task_name is not None:
            if task_name not in self.tasks:
                self.add_task(task_name)
            self.tasks[task_name].tensors[output_tensor_name] = tensor_metadata
        else:
            if DUMMY_METADATA_TASK_NAME not in self.tasks:
                self.add_task(DUMMY_METADATA_TASK_NAME)
            self.tasks[DUMMY_METADATA_TASK_NAME].tensors[output_tensor_name] = tensor_metadata
            LOGGER.warning(
                f"Adding {output_tensor_name} output tensor to metadata with {DUMMY_METADATA_TASK_NAME} task name."
            )

    def update_output_tensor(
        self,
        output_tensor_name: str,
        tensor_metadata: TensorMetadata,
        task_name: str | None = None,
    ) -> None:
        """Update the metadata of the output tensor.

        All the non default values in the TensorMetadata from the tensor_metadata attributes are
        copied to the output tensor metadata.

        Args:
            output_tensor_name: Name of the output tensor to be updated
            tensor_metadata: metadata with the updated values.
            task_name: Optional task name
        """
        to_be_updated_metadata = self.get_output_tensor_metadata(output_tensor_name, task_name)
        if to_be_updated_metadata is None:
            msg = (
                f"Could not find {output_tensor_name} output tensor in the metadata. Tensor was probably added "
                "during the conversion stage. Trying to add the tensor to the metadata."
            )
            LOGGER.info(msg)
            self.add_output_tensor(output_tensor_name, tensor_metadata=tensor_metadata)
            return
        default_tensor_metadata = TensorMetadata()
        for tensor_attrs in fields(TensorMetadata):
            # Update only attributes which differ from default values.
            if getattr(tensor_metadata, tensor_attrs.name) != getattr(default_tensor_metadata, tensor_attrs.name):
                setattr(to_be_updated_metadata, tensor_attrs.name, getattr(tensor_metadata, tensor_attrs.name))

    def get_task_for_output_tensor(self, output_tensor_name: str) -> str | None:
        """Get task for output tensor.

        Args:
            output_tensor_name: Name of the output tensor

        Returns:
            task name
        """
        found_tasks = [task_name for task_name in self.tasks if output_tensor_name.startswith(f"{task_name}_")]
        if len(found_tasks) == 1:
            return found_tasks[0]

        # If the output-name doesn't contain a task name, then try looking into already added outputs under each tasks.
        found_tasks = [task_name for task_name in self.tasks if output_tensor_name in self.tasks[task_name].tensors]
        if len(found_tasks) == 1:
            return found_tasks[0]
        return None

    def add_input(self, tensor_name: str, tensor_metadata: TensorMetadata | None = None) -> None:
        """Add input tensor metadata."""
        if tensor_metadata is None:
            tensor_metadata = TensorMetadata()
        self.inputs[tensor_name] = tensor_metadata

    def add_input_grouping(self, group_name: str, tensor_name_pattern: str) -> None:
        """To group specific inputs together, while converting to dictions/writing to Json."""
        self._input_groups_def[group_name] = tensor_name_pattern

    def add_variable(self, tensor_name: str, tensor_metadata: TensorMetadata) -> None:
        """Add tensor metadata for other variables (intermediate tensors)."""
        self.variables[tensor_name] = tensor_metadata

    def add_profiling(self, profiling_metrics: dict[str, Any]) -> None:
        """Add profiling metrics."""
        self.profiling = profiling_metrics

    def get_output_tensor_metadata(
        self, output_tensor_name: str, task_name: str | None = None
    ) -> TensorMetadata | None:
        """Get metadata and task name for given output tensor name.

        Args:
            output_tensor_name: Find the tensor metadata for this output tensor
            task_name: Task of the output tensor
        Return
            Tensor metadata of the output tensor
        """

        if task_name is not None:
            # Use the task hint to search only tensors from that task
            task = self.tasks[task_name]
            for tensor_name, metadata in task.tensors.items():
                if output_tensor_name == tensor_name:
                    return metadata
        else:
            # Search over all tasks
            for task in self.tasks.values():
                for tensor_name, metadata in task.tensors.items():
                    if output_tensor_name == tensor_name:
                        return metadata

        return None

    def _convert_inputs(self, inputs: dict[str, NDArray[Any]], conversion_func: str) -> dict[str, NDArray[Any]]:
        """Convert (e.g. quantize or dequantize) inputs of a model.

        Only the quantized inputs are converted. Non-quantized tensors, which are already in integer types, are copied.

        Args:
            inputs: a dict containing the input values to be converted
            conversion_func: name of the conversion function, e.g. "dequantize_tensor" or "quantize_tensor"

        Returns:
            inputs which are converted
        """
        inputs_converted = {}
        for input_name, input_val in inputs.items():
            tensor_meta_data = self.inputs[input_name]
            if tensor_meta_data.quantized:
                inputs_converted[input_name] = getattr(tensor_meta_data, conversion_func)(input_val)
            else:
                inputs_converted[input_name] = input_val

        return inputs_converted

    def quantize_inputs(self, inputs: dict[str, NDArray[Any]]) -> dict[str, NDArray[Any]]:
        """Quantize input tensors of a model.

        Args:
            inputs: a dict containing the input values to be quantized

        Returns:
            inputs of a model which are quantized
        """
        return self._convert_inputs(inputs, "quantize_tensor")

    def dequantize_inputs(self, inputs: dict[str, NDArray[Any]]) -> dict[str, NDArray[Any]]:
        """De-Quantize input tensors of a model.

        Args:
            inputs: a dict containing the input values to be de-quantized

        Returns:
            inputs of a model which are in float32 or integer (which were not quantized)
        """
        return self._convert_inputs(inputs, "dequantize_tensor")

    def finalize(self, remove_unused_outputs: bool = False) -> None:  # noqa: FBT001, FBT002
        """Finalize preparation of metadata.

        Args:
            remove_unused_outputs: If True, then remove output tensors which are not used in the model.
        """
        if remove_unused_outputs:
            for output_tensor, output_metadata in self.outputs.items():
                if output_metadata.order == -1:
                    LOGGER.warning(
                        f"Output tensor {output_tensor} order is not set. Output tensor is missing in the model."
                        "This might be expected when the output tensor was removed by graph rewriters."
                    )
                    task_name = self.get_task_for_output_tensor(output_tensor)
                    if task_name is not None:
                        self.tasks[task_name].tensors.pop(output_tensor)
                    else:
                        LOGGER.error(f"Error in metadata handling, could not find task for {output_tensor}.")

        for task in self.tasks.values():
            task.finalize()

    def get_all_tensors(self) -> dict[str, TensorMetadata]:
        """Get all tensors (inputs, intermediate, outputs).

        Returns:
            {tensor_name} : TensorMetadata
        """
        tensors = {}
        tensors.update(self.variables)
        tensors.update(self.inputs)
        tensors.update(self.outputs)
        return tensors

    @property
    def outputs(self) -> dict[str, TensorMetadata]:
        """Get all output tensors.

        Returns:
            Mapping of output tensor name and it's TensorMetadata
        """
        tensors = {}
        for task in self.tasks.values():
            tensors.update(task.tensors)
        return tensors

    @property
    def sorted_outputs(self) -> dict[str, TensorMetadata]:
        """Returns outputs sorted by their order field."""
        result = self.outputs
        return dict(sorted(result.items(), key=lambda item: item[1].order))

    @property
    def sorted_inputs(self) -> dict[str, TensorMetadata]:
        """Returns inputs sorted by their order field."""
        result = self.inputs
        return dict(sorted(result.items(), key=lambda item: item[1].order))


def save_model_metadata_to_json(
    model_metadata: ModelMetadata,
    save_to_path: str | pathlib.Path,
    remove_unused_outputs: bool = False,  # noqa: FBT001, FBT002
) -> None:
    """Save the metadata dataclass to a json.

    Args:
        model_metadata: Metadata to save
        save_to_path: Path where to save the json
        remove_unused_outputs: If True, then remove output tensors which are not found in the model.
            Unused outputs are the ones which have order = -1.
    """
    model_metadata.finalize(remove_unused_outputs)
    with open(save_to_path, "w", encoding="utf-8") as json_fp:
        json.dump(model_metadata.to_dict(), json_fp, indent=4, sort_keys=False, default=lambda value: repr(value))


def update_model_metadata_with_output_layers_attribute_mapping(
    pace_metadata: ModelMetadata,
    output_layer_mapping: dict[str, str],
) -> None:
    """Update ModelMetadata with mapping of output tensor name to attributes.

    Args:
        pace_metadata: ModelMetadata to update
        output_layer_mapping: {output_tensor_name: attributes}
    """
    for output_layer_name, attributes in output_layer_mapping.items():
        pace_metadata.add_output_tensor(output_layer_name, tensor_metadata=TensorMetadata(attributes=attributes))
