"""Rewriter Rules for graph surgery.

The Graph rewriter is called in the beginning of the surgery and after all the
node matched rules are applied.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import pathlib
import re
import tempfile
import typing
from collections import defaultdict
from collections.abc import Sequence
from dataclasses import dataclass
from itertools import groupby
from typing import Any, Final, cast

import numpy as np
import numpy.typing as npt
import onnx
import onnx.shape_inference as osi
import onnx_graphsurgeon as gs

from conversion.helpers import convert_image_shape_from_420_to_444_sampling
from conversion.onnx.constants import ATHENA_DOMAIN
from conversion.qnn.onnx_surgery.common import (
    append_node,
    convert_node_to_identity,
    delete_node,
    find_first_node,
    find_node_with_output_name,
    find_start_and_end,
    fuse_end_points,
    fuse_start_points,
    get_first_non_constant_input_node,
    get_name_of_tensor,
    get_nms_node,
    is_next_node_present,
    is_node_in_graph,
    replace_outputs_of_endpoints,
)
from conversion.qnn.onnx_surgery.create_centernet_replacement import (
    CenterNetFarviewParams,
    CenterNetMidviewParams,
    CenterNetParams,
    CenterNetWideviewParams,
    MaskedCenternetMixin,
    ViewMasks,
    create_center_net,
)
from conversion.qnn.onnx_surgery.template_replacement import load_and_replace
from conversion.qnn.quantization_overrides import Encoding, QuantizationEncodings, merge_encodings
from conversion.qnn.yuv_config import YuvQnnConfig
from conversion_contract import contract

LOGGER = logging.getLogger(__name__)
UNEXPECTED_NUM_OUT_MSG: Final[str] = "Unexpected number of outputs"


def find_largest_divisor(shape: list[int], power_of_n: int = 3) -> int:
    """Get largest possible divisor from 2^power_of_n of max(shape).

    Args:
        shape: Shape of node output
        power_of_n: Power of n to factorize the largest dimension

    Returns:
        largest factor
    """
    max_value = max(shape)
    factors = sorted([2**n for n in range(power_of_n + 1)], reverse=True)
    for factor in factors:
        if max_value % factor == 0:
            return factor
    msg = f"Unreachable code. {max_value} % 1 is not 0!"
    raise AssertionError(msg)


def reshape_factorize_largest_dimension(shape: list[int], class_axis: int = -1) -> tuple[list[int], int]:
    """Given shape, factorize its largest dimension by the largest 2^n. Where n=5...0.

    The rationale is to maximize the saturation of the qualcomm 1x8x8x32 crouton data structure.
    shape = [1, 53600, 3] => reshape_shape = [1, 6700, 8, 3]
    shape = [1, 3, 53600] => reshape_shape = [1, 3, 1675, 32]
    It is assumed that class_axis is different from the largest dimension axis.

    Args:
        shape: Shape of the output node
        class_axis: index of axis which holds the classes

    Returns:
        Shape for the Reshape operation, and the updated axis of the classes
    """
    largest_dimension = max(shape)
    if largest_dimension == shape[class_axis]:
        # Currently there is no need to handle this case.
        msg = (
            f"reshape_factorize_largest_dimension not implemented for case that {class_axis=} is the largest dimension."
        )
        raise NotImplementedError(msg)
    axis_largest_dimension = shape.index(largest_dimension)
    if axis_largest_dimension == len(shape) - 1:  # e.g. [1, 3, 53600]
        divisor = find_largest_divisor(shape, power_of_n=5)
        reshape_shape = shape[:axis_largest_dimension] + [shape[axis_largest_dimension] // divisor, divisor]
    else:  # e.g. [1, 53600, 3]
        divisor = find_largest_divisor(shape, power_of_n=3)
        reshape_shape = (
            shape[:axis_largest_dimension]
            + [shape[axis_largest_dimension] // divisor, divisor]
            + shape[axis_largest_dimension + 1 :]
        )
        if class_axis > -1:
            class_axis = class_axis + 1
    if divisor == 1:
        msg = f"Could not determine shape for Reshape operation. {shape=}."
        raise ValueError(msg)
    return reshape_shape, class_axis


def shape_to_list(gs_shape: Sequence[int | str]) -> list[int]:
    """Get the shape of the gs shape.

    This is basically needed to fix some pyright issues.

    Args:
        gs_shape: A shape from GS

    Returns:
        A checked shape list.
    """
    shape = []
    for d in gs_shape:
        if isinstance(d, str):
            msg = f"unknown dim in {gs_shape}"
            raise TypeError(msg)
        shape.append(d)

    return shape


def set_dim(tensor: gs.Tensor, index: int, value: int) -> None:
    """Set the dimension to a value.

    This is basically needed to fix some pyright issues.

    Args:
        tensor: The tensor to set the dimension.
        index: The dimension to set.
        value: The value to set for the dimension.

    """
    assert tensor.shape is not None
    assert isinstance(tensor.shape, list)
    tensor.shape[index] = value


class AbstractGraphRewriter:
    """Interface of a GraphRewriter."""

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Rewrite the graph before node surgery.

        Args:
            graph: the onnx graph
            quant_overrides: not used

        Returns:
            Rewritten graph
        """
        return graph

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Rewrite the graph after the node surgery.

           For example to correct the shape of the model after the node
           surgery, etc...

        Args:
            graph: the onnx graph
            quant_overrides: not used

        Returns:
            Rewritten graph
        """
        return graph


class OnnxSubgraphFinder:
    """Locate onnx replacement subgrapht for a given model input resolution."""

    def get_replacement_subgraph_from_model_res(
        self, graph: gs.Graph, folder_path: str, replacements_name: str
    ) -> pathlib.Path | None:
        """Get path to the replacement graph for given graph resolution and replacement name.\

        Args:
            graph: graph under surgery
            folder_path: path to the folder with replacement subgraphs
            replacements_name: name of the replacement subgraph

        Returns:
            Path to the replacement subgraph or None if not found
        """
        input_1_shape = shape_to_list(graph.inputs[0].shape)
        if input_1_shape[1] == 1:
            input_1_shape = convert_image_shape_from_420_to_444_sampling(input_1_shape)
        if len(input_1_shape) == 4:
            height, width = input_1_shape[2:]
            subgraph_replacement_path = (
                pathlib.Path(__file__).parent / folder_path / f"{replacements_name}_{width}x{height}_subgraph.onnx"
            )
            if subgraph_replacement_path.exists():
                return subgraph_replacement_path
        return None


class MergeAnchorLayers(AbstractGraphRewriter):
    """Remove box predictor Mul and Add nodes before NMS and merge them into NMS."""

    def __init__(self, heads_to_output_bounding_box_name_mapping: dict[str, str]) -> None:
        """Initialize the rewriter with mapping of head name and the output tensors.

        Note:
            Currently is done for traffic_light, vru, vehicles and traffic_sign
            for the classes, boxes and coordinates output layers.

        Args:
            heads_to_output_bounding_box_name_mapping: Mapping between {head_name: bounding_box_output_name}
                eg. {traffic_light: [tl_coordinates]}
        """
        self.heads_to_output_mapping = heads_to_output_bounding_box_name_mapping

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Remove box predictor Mul and Add nodes before NMS and merge them into NMS.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings for the qnn-onnx-converter
        """
        for head_name, head_output in self.heads_to_output_mapping.items():
            graph = self._merge_anchor_layers(graph, head_name, head_output)

        return graph

    def _merge_anchor_layers(self, graph: gs.Graph, head: str, head_output: str) -> gs.Graph:  # noqa: C901, PLR0912, PLR0915
        """Remove box predictor Mul and Add nodes before NMS and merge them into NMS.

            The pattern matched is Mul->Add->Concat box_predictor.
            Only one Mul->Add->Concat box_predictor per head is supported
        Args:
            graph: Graph to be manipulated
            head: Head to checked
            head_output: The substring of the output name to be modified
        """
        tensors = graph.tensors()
        head = head.removesuffix("_trifocal")
        concat_node = None
        # look for type Concat->Add->Mul
        #      and name  concat->box_predictor->box_predictor
        for node in graph.nodes:
            # "_head"/.*/"concatenate".*/"concat" # (Concat)
            if head in node.name and "concat" in node.name and node.op == "Concat":
                # we only look for on path, concat will have multiple
                # if one path matches, the other paths are assumed to be the same
                # and will be collected below
                prev_node = None
                prev_prev_node = None

                prev_node = get_first_non_constant_input_node(node)
                if prev_node:
                    prev_prev_node = get_first_non_constant_input_node(prev_node)

                if (
                    prev_node
                    and prev_prev_node
                    and (
                        head in prev_prev_node.name
                        and "box_predictor" in prev_prev_node.name
                        and prev_prev_node.op == "Mul"
                    )
                    and (head in prev_node.name and "box_predictor" in prev_node.name and prev_node.op == "Add")
                ):
                    concat_node = node
                    break

        # check if Concat was found
        if not concat_node:
            LOGGER.warning(f"No Concat->Add->Mul found for merging in head {head}")
            return graph

        layer_list_node: dict[str, Any] = {"offsets": [], "scales": []}
        layer_list_inputs: dict[str, Any] = {"offsets": [], "scales": []}
        # go through concat inputs and collect scale and offset constants in this order
        for inp in concat_node.inputs:
            n_add = inp.inputs[0]
            # "_head"/.*/"box_predictor".*/"decoded" # (add)
            if head in n_add.name and "box_predictor" in n_add.name and n_add.op == "Add":
                for inp_add in n_add.inputs:
                    if isinstance(inp_add, gs.Constant):
                        layer_list_inputs["offsets"].append(inp_add.name)
                        layer_list_node["offsets"].append(n_add)
                    else:
                        n_mul = inp_add.inputs[0]
                        # "_head"/.*/"box_predictor".*/"scale" # (mul)
                        if head in n_mul.name and "box_predictor" in n_mul.name and n_mul.op == "Mul":
                            for inp_mul in n_mul.inputs:
                                if isinstance(inp_mul, gs.Constant):
                                    layer_list_inputs["scales"].append(inp_mul.name)
                                    layer_list_node["scales"].append(n_mul)

        # check if Mul was found
        if len(layer_list_inputs["scales"]) == 0:
            LOGGER.warning(f"No anchor scales found for merging in head {head}")
            return graph

        # check if Add was found
        if len(layer_list_inputs["offsets"]) == 0:
            LOGGER.warning(f"No anchor scales found for merging in head {head}")
            return graph

        nms_node = get_nms_node(head, graph)

        # check if NMS was found
        if nms_node is None:
            LOGGER.warning(f"No nms found in head {head}")
            return graph

        # Search for Gather node
        gather_node = None
        gathers = nms_node.outputs[0].outputs
        for node in gathers:
            if node.op == "GatherPlugin" and head_output in node.outputs[0].name:
                gather_node = node
                break

        # check if Gather was found
        if gather_node is None:
            LOGGER.warning(f"No gather found in head {head}")
            return graph

        # Merge anchor scales into one constant
        anchor_scales = []
        for layer in layer_list_inputs["scales"]:
            anchor_scales.append(np.array(tensors[layer].values))  # noqa: PERF401

        anchor_scales_constant = gs.Constant(
            name=f"{head}_anchor_scales_const",
            values=np.concatenate(anchor_scales, axis=1).round().astype(np.int32).squeeze(),
        )

        anchor_scales_var = gs.Variable(
            name=f"{head}_anchor_scales",
            dtype=np.int32,
        )

        anchor_scales_identity = gs.Node(
            op="Identity",
            name=f"{gather_node.name}_scale_Identity",
            inputs=[anchor_scales_constant],
            outputs=[anchor_scales_var],
        )
        append_node(graph, anchor_scales_identity)

        # merge anchor offsets into one constant
        anchor_offsets = []
        for layer in layer_list_inputs["offsets"]:
            anchor_offsets.append(np.array(tensors[layer].values))  # noqa: PERF401

        anchor_offsets_const = gs.Constant(
            name=f"{head}_anchor_offsets_const",
            values=np.concatenate(anchor_offsets, axis=1).round().astype(np.int32).squeeze(),
        )

        anchor_offsets_var = gs.Variable(
            name=f"{head}_anchor_offsets",
            dtype=np.int32,
        )
        anchor_offsets_identity = gs.Node(
            op="Identity",
            name=f"{gather_node.name}_offset_Identity",
            inputs=[anchor_offsets_const],
            outputs=[anchor_offsets_var],
        )
        append_node(graph, anchor_offsets_identity)

        # remove per scale mul and add layers
        for node in layer_list_node["offsets"]:
            delete_node(node)
        for node in layer_list_node["scales"]:
            delete_node(node)

        LOGGER.info(f"Modify NMS {head}, {nms_node.name}")

        nms_node.inputs.append(anchor_scales_var)
        nms_node.inputs.append(anchor_offsets_var)

        # remove gather on the boxes and its output to the nms output
        nms_node.outputs.append(gather_node.outputs[0])
        gather_node.outputs.clear()

        graph.cleanup(remove_unused_node_outputs=False).toposort()
        return graph


class ObjectnessNMSRewriter(AbstractGraphRewriter):
    """Rewrite NMS in the case if used with objectness filter."""

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Connect NMS to anchorgenerator directly and output boxes from NMS.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings for the qnn-onnx-converter
        """
        for node in graph.nodes:
            if node.op == "NMS":
                # Find add/offset and mul/scale layers/tensors
                id = node.i()  # noqa: A001
                add = node.i(0).i()
                offset = 0 if add.i(1).op == "Mul" else 1
                mul = add.i(0) if offset else add.i(1)
                if add.op == "Add" and mul.op == "Mul":
                    offset = add.inputs[offset]
                    if isinstance(offset, gs.Variable):
                        if "anchor_generator" in mul.i(0).i().name:
                            predictions = mul.inputs[1]
                            scale = mul.inputs[0]
                        elif "anchor_generator" in mul.i(1).i().name:
                            predictions = mul.inputs[0]
                            scale = mul.inputs[1]
                        else:
                            msg = "AnchorGenerator not found in NMS"
                            raise ValueError(msg)
                        # Add offset and scale tensor as inputs to NMS
                        node.inputs.append(scale)
                        node.inputs.append(offset)
                        node.inputs[0] = predictions
                        # Output boxes directly from NMS -> no separate Gather needed
                        gather = id.o(0) if id.o(0).op == "GatherPlugin" else id.o(1)
                        node.outputs.append(gather.outputs[0])
                        gather.outputs = []

        graph.cleanup(remove_unused_node_outputs=False).toposort()

        return graph


class InsertFilter2D(AbstractGraphRewriter):
    """Replace Filter2D layers with Filter2D custom op."""

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Replace Filter2D layers with Filter2D custom op.

        Delete transposes before this op as they are not needed for the custom op.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings

        Returns:
            Rewritten graph
        """
        # find first node in graph
        current = find_first_node(graph)
        if current is None:
            msg = "no first node found"
            raise ValueError(msg)

        reg = re.compile("Filter2D")
        paths = find_start_and_end(current, reg, [])
        for i, (start, end) in enumerate(paths):
            out_shape = end.outputs[0].shape
            prefix = reg.split(end.name)[0]
            var = gs.Variable(
                name=f"{prefix}Filter2D_{i}",
                dtype=np.int32,
                shape=[1, out_shape[0]],
            )
            filter_node = gs.Node(
                op="Filter2D",
                name=f"{prefix}Filter2D_{i}",
                attrs={"threshold": start.inputs[1].values.item()},
                inputs=[start.inputs[0]],
                outputs=[var],
                domain=ATHENA_DOMAIN,
            )
            replace_outputs_of_endpoints(graph, [end], filter_node)
        graph.cleanup().toposort()

        return graph


class InsertGather2D(AbstractGraphRewriter):
    """Replace Gather2D layers with Gather2D custom op."""

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Replace Gather2D layers with Gather2D custom op.

        Delete transposes before this op as they are not needed for the custom op.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings

        Returns:
            Rewritten graph
        """
        first_node = find_first_node(graph)
        if first_node is None:
            msg = "no first node found"
            raise ValueError(msg)

        reg = re.compile("Gather_2D")
        paths = find_start_and_end(first_node, reg, [])
        paths = fuse_start_points(paths)
        for i, (starts, end) in enumerate(paths):
            assert len(starts) == 3, "Gather2D is expected to have 3 input node"
            for start in starts:
                # If there were multiple Gather2D applied on the same data, tf2onnx will optimize multiple
                # transposes of the same tensor into a single transpose and while doing so also change the
                # name of the transpose, breaking the naming scheme.
                if start.op == "GatherND":
                    starts.append(start.i())
            # identify data and ids node
            data = ids = None
            for start in starts:
                if start.op == "ReduceMax":
                    ids = start
                if start.op == "Transpose":
                    data = start
            assert data is not None, "Could not identify the data node -> should be a Transpose node"
            assert ids is not None, "Could not identify the ids node -> should be a GatherND node"
            in_shape = data.inputs[0].shape
            out_shape = end.outputs[0].shape
            prefix = reg.split(end.name)[0]
            var = gs.Variable(
                name=f"{prefix}Gather2D_{i}",
                dtype=np.float32,
                shape=[1, out_shape[0], in_shape[1]],
            )
            gather_node = gs.Node(
                op="Gather2D",
                name=f"{prefix}Gather2D_{i}",
                inputs=[data.inputs[0], ids.inputs[0]],
                outputs=[var],
                domain=ATHENA_DOMAIN,
            )
            replace_outputs_of_endpoints(graph, [end], gather_node)

        graph.cleanup().toposort()

        return graph


class ReplaceQNNIndexEncoder(AbstractGraphRewriter):
    """Replace QNNIndexEncoder layers from the graph."""

    def __init__(self, encoder_node_name: str) -> None:
        """Init QNNIndexEncoder rewriter.

        Args:
            encoder_node_name: Name of the QNNIndexEncoder nodes in the graph
        """
        self.encoder_node_name = encoder_node_name

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Remove QNNIndexEncoder layers from the graph.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings

        Returns:
            Rewritten graph
        """
        first_node = find_first_node(graph)
        if first_node is None:
            msg = "no first node found"
            raise ValueError(msg)
        reg = re.compile(self.encoder_node_name)
        paths = find_start_and_end(first_node, reg, [])

        for i, (start, end) in enumerate(paths):
            out_shape = end.outputs[0].shape
            prefix = reg.split(end.name)[0]
            var = gs.Variable(
                name=f"{prefix}{self.encoder_node_name}_{i}",
                dtype=np.int32,
                shape=[1, out_shape[0]],
            )
            filter_node = gs.Node(
                op="Identity",
                name=f"{prefix}{self.encoder_node_name}_{i}",
                inputs=[start.inputs[0]],
                outputs=[var],
            )
            replace_outputs_of_endpoints(graph, [end], filter_node)
        graph.cleanup().toposort()

        return graph


class InsertAnchorGenerator(AbstractGraphRewriter):
    """Replace AnchorGenerator layers with AnchorGenerator custom op."""

    def __init__(self, preceding_yuv_plugin: bool = False) -> None:  # noqa: FBT001, FBT002
        """Init AnchorGenerator rewriter.

        Args:
            preceding_yuv_plugin: If the YUV plugin is used before the AnchorGenerator
        """
        self.preceding_yuv_plugin = preceding_yuv_plugin

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:  # noqa: PLR0915, C901
        """Replace AnchorGenerator layers with AnchorGenerator custom op.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings

        Returns:
            Rewritten graph
        """
        first_node = find_first_node(graph)
        assert first_node is not None, "Could not find first node in graph"
        reg = re.compile("anchor_generator")
        paths = find_start_and_end(first_node, reg, [])
        paths = fuse_end_points(paths)
        for i, (start, ends) in enumerate(paths):
            scales = offsets = None
            end_node = ends[-1]
            for end in ends:
                if end.op == "Concat":
                    scales = end
                if end.op == "Reshape":
                    offsets = end
            assert len(ends) == 2, "Anchorgenerator is expected to have 2 output nodes"
            assert scales is not None, "Could not identify the scales node -> should be a concat node"
            assert offsets is not None, "Could not identify the offsets node -> should be a reshape node"
            assert start.op == "GatherND", "Anchorgenerator start node should be GatherND"
            out_shape = start.outputs[0].shape
            prefix = reg.split(end_node.name)[0]
            anchors = out_shape[1] // 4
            var_anchor_sizes = gs.Variable(
                name=f"{prefix}AnchorGenerator_{i}/anchor_sizes",
                dtype=np.int32,
                shape=[anchors, 2],
            )
            anchor_sizes = np.reshape(start.inputs[0].values[0, 0, 0, :], [anchors, 4])
            anchor_sizes = anchor_sizes[:, 2:] - anchor_sizes[:, :2]
            const_anchor_sizes = gs.Constant(
                f"{prefix}AnchorGenerator_{i}/const_anchor_sizes", np.array(anchor_sizes, dtype=np.int32)
            )
            id_node = gs.Node(
                op="Identity",
                name=f"{prefix}AnchorGenerator_{i}/anchor_sizes",
                inputs=[const_anchor_sizes],
                outputs=[var_anchor_sizes],
            )
            append_node(graph, id_node)

            var_scales = gs.Variable(
                name=f"{prefix}AnchorGenerator_{i}/scales",
                dtype=np.int32,
                shape=[out_shape[0] * anchors, 4],
            )
            var_offsets = gs.Variable(
                name=f"{prefix}AnchorGenerator_{i}/offsets",
                dtype=np.int32,
                shape=[out_shape[0] * anchors, 4],
            )

            def extract_stride(name: str) -> int | None:
                """Extract anchor generator stride from node name."""
                match = re.search(r"anchor_generator_stride_(\d+)", name)
                if match:
                    return int(match.group(1))
                return None

            stride = extract_stride(start.name)
            if not stride:
                LOGGER.info("Could not extract stride from node name. Calculate from input shape instead...")
                stride = graph.inputs[0].shape[3] // start.inputs[0].shape[2]
                if self.preceding_yuv_plugin:
                    # If the YUV Input Plugin was added then the input shape was adapted
                    stride = (graph.inputs[0].shape[3] * 16) // start.inputs[0].shape[2]
            anchor_node = gs.Node(
                op="AnchorGenerator",
                name=f"{prefix}AnchorGenerator_{i}",
                inputs=[start.inputs[1], var_anchor_sizes],
                outputs=[var_offsets, var_scales],
                attrs={"stride": stride},
                domain=ATHENA_DOMAIN,
            )
            replace_outputs_of_endpoints(graph, [offsets], anchor_node)
            concat_offsets = None
            for out in anchor_node.outputs[0].outputs:
                if out.op == "Add":
                    concat_offsets = out.o()
                    out.outputs[0].shape = anchor_node.outputs[0].shape
                    out.outputs[0].dtype = anchor_node.outputs[0].dtype
                    out.inputs[1] = gs.Constant(
                        out.inputs[1].name,
                        np.array(
                            np.int32(out.inputs[1].values[0, ...]),
                            dtype=np.int32,
                        ),
                    )
                if out.op == "Concat":
                    concat_offsets = out
            assert concat_offsets, "Could not find concat node"
            concat_offsets.outputs[0].shape = concat_offsets.outputs[0].shape[-2:]
            concat_offsets.outputs[0].dtype = anchor_node.outputs[0].dtype
            concat_offsets.attrs["axis"] = -2
            replace_outputs_of_endpoints(graph, [scales], anchor_node, 1)
            concat_scales = None
            for out in anchor_node.outputs[1].outputs:
                if out.op == "Concat":
                    concat_scales = out
            assert concat_scales, "Could not find concat node"
            concat_scales.outputs[0].shape = concat_scales.outputs[0].shape[-2:]
            concat_scales.outputs[0].dtype = np.int32
            concat_scales.attrs["axis"] = -2
        graph.cleanup().toposort()

        return graph


class FixTransposeIntoGather(AbstractGraphRewriter):
    """Remove redundant transpose and correct shape into gather plugin."""

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Remove redundant transpose and correct shape into gather plugin.

        Delete transposes before gather to match expected shape.
        The graph needs to contain shape information to detect the extra
        transpose.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings

        Returns:
            Rewritten graph
        """
        for node in graph.nodes:
            if "Transpose__" in node.name:
                output_nodes = [nn for n in node.outputs for nn in n.outputs]

                if any(item.op == "GatherPlugin" for item in output_nodes):
                    branch_name = node.o().name
                    bn_list = branch_name.split("/")
                    _ = bn_list.pop()
                    bn_list.append(node.name)
                    node.name = "/".join(bn_list)
                    node.outputs[0].name = "/".join(bn_list)
                    LOGGER.info(f"Rename Transpose {node.name}, to {node.outputs[0].name}")

                    search_node = node
                    for _ in range(5):
                        if search_node:
                            prev_node = get_first_non_constant_input_node(search_node)
                            if prev_node is not None and prev_node.op == "Softmax":
                                LOGGER.info(f"{self.__class__.__name__} applied to {node.name}.")
                                convert_node_to_identity(node)
                                break
                            search_node = prev_node
        graph.cleanup(remove_unused_node_outputs=False).toposort()

        return graph


class RewriteYUVInputPlugin(AbstractGraphRewriter):
    """Replace the RGB2YUV with the YUV plugin input.

    The rgb input_1 is replaced by 2 inputs: input_y and input_uv.
    """

    channel_idx = 1
    height_idx = 2
    width_idx = 3

    def __init__(
        self,
        yuv_node_pattern: str,
        vcrop_node_pattern: str,
        yuv_subsequent_conv_node_pattern: str,
        split_input: bool = True,  # noqa: FBT001, FBT002
        yuv10: bool = True,  # noqa: FBT001, FBT002
        multifocal_target_shapes: list[tuple[int, int]] | None = None,
        multifocal_target_crops: list[tuple[float, float, float, float]] | None = None,
    ) -> None:
        """Init YUV plugin rewriter to replace RGB2YUV420 with the YUV plugin.

        During multifocal deployment, the shapes of the camera inputs must be adapted and an additional
        cropping applied, since the network expects the same multifocal crop format used during training.

        Args:
            yuv_node_pattern: Name pattern of the YUV2YUV420 node.
            vcrop_node_pattern: Name pattern of the preceding node for vertical cropping.
            yuv_subsequent_conv_node_pattern: Name pattern of the subsequent Conv2D after space2depth.
            split_input: flag for using the YUV Plugin directly.
            yuv10: flag for 10bit YUV
            multifocal_target_shapes: List of target shapes for multifocal deployment,
                specified as (height, width) tuples.
            multifocal_target_crops: List of target crops defined as (top, left, height, width) tuples,
                used to adjust the target images to match the expected network input shapes.
        """
        self.yuv_node_pattern = yuv_node_pattern
        self.vcrop_node_pattern = vcrop_node_pattern
        self.yuv_subsequent_conv_node_pattern = yuv_subsequent_conv_node_pattern
        self.split_input = split_input
        self.yuv10 = yuv10

        self.multifocal_target_shapes = multifocal_target_shapes
        self.multifocal_target_crops = multifocal_target_crops
        if (
            self.multifocal_target_shapes
            and self.multifocal_target_crops
            and len(self.multifocal_target_shapes) != len(self.multifocal_target_crops)
        ):
            error_msg = "The number of multifocal input shapes must match the number of multifocal input crops."
            raise ValueError(error_msg)

    def _find_subsequent_node(
        self, nodes_by_input: dict[str, list[gs.Node]], starting_input_name: str, pattern: str
    ) -> gs.Node | None:
        backlog = {starting_input_name}
        walked = set()

        while backlog:
            node_input = backlog.pop()

            for n in nodes_by_input.get(node_input, []):
                if re.match(pattern, n.name):
                    return n
                if n.name in walked:
                    continue
                for o in n.outputs:
                    backlog.add(o.name)
                walked.add(n.name)

        return None

    def _find_subsequent_convs(
        self, nodes_by_input: dict[str, list[gs.Node]], graph: gs.Graph
    ) -> dict[str, gs.Node | None]:
        return {
            get_name_of_tensor(name): self._find_subsequent_node(
                nodes_by_input, get_name_of_tensor(name), self.yuv_subsequent_conv_node_pattern
            )
            for name in graph.inputs
            if contract.CAMERA_TO_IMAGE_LUT_NAME not in get_name_of_tensor(name)
        }

    def _with_suffix(self, *, is_multi_input: bool, name: str, i: int) -> str:
        if is_multi_input:
            return f"{name}_{i}"
        return name

    def _add_vcrop(
        self,
        *,
        graph: gs.Graph,
        yuv_out: gs.Variable,
        input_rgb_like_height: int,
        i: int,
        output_dim: tuple[int, int, int],
        space_to_depth_factor: int,
        is_multi_input: bool,
    ) -> gs.Variable:
        # Create vcrop output variable
        (output_channels, output_height, output_width) = output_dim
        cropped_rgb_like_height = input_rgb_like_height // 2
        cropped_height = cropped_rgb_like_height // space_to_depth_factor
        vcrop_out = gs.Variable(
            name=self._with_suffix(is_multi_input=is_multi_input, name="vcrop_out", i=i),
            dtype=np.float32,
            shape=(1, output_channels, cropped_height, output_width),
        )

        # Create vcrop slicing node
        starts_vcrop_height = output_height - cropped_height
        starts_vcrop = gs.Constant(
            self._with_suffix(is_multi_input=is_multi_input, name="starts_vcrop", i=i),
            np.array([0, 0, starts_vcrop_height, 0], dtype=np.int64),
        )
        max64_int = np.iinfo(np.int64).max
        ends_vcrop = gs.Constant(
            self._with_suffix(is_multi_input=is_multi_input, name="ends_vcrop", i=i),
            np.array([max64_int, max64_int, max64_int, max64_int], dtype=np.int64),
        )
        axes_vcrop = gs.Constant(
            self._with_suffix(is_multi_input=is_multi_input, name="axes_vcrop", i=i),
            np.array([0, 1, 2, 3], dtype=np.int64),
        )
        vcrop_node = gs.Node(
            op="Slice",
            name=self._with_suffix(is_multi_input=is_multi_input, name="SliceVCrop", i=i),
            inputs=[yuv_out, starts_vcrop, ends_vcrop, axes_vcrop],
            outputs=[vcrop_out],
        )
        append_node(graph, vcrop_node)

        return vcrop_out

    def _setup_slice_dims(
        self,
        *,
        yuv420_input_shape: gs.Tensor,
        input_rgb_like_height: int,
        input_rgb_like_width: int,
        is_multi_input: bool,
        nodes_by_input: dict[str, list[gs.Node]],
        multifocal_crop: tuple[float, float, float, float] | None,
    ) -> tuple[Any, ...]:
        # Get max value for int64 for slice nodes to use it as slice ends value
        max64_int = np.iinfo(np.int64).max
        yuv420_input_shape_name = get_name_of_tensor(yuv420_input_shape)
        assert yuv420_input_shape.shape is not None  # please type checkers
        slice_y_shape = shape_to_list(yuv420_input_shape.shape)
        slice_y_shape[self.height_idx] = input_rgb_like_height
        starts_y_np = np.array([0, 0, 0, 0], dtype=np.int64)
        ends_y_np = np.array([max64_int, max64_int, input_rgb_like_height, max64_int], dtype=np.int64)
        starts_uv_np = np.array([0, 0, input_rgb_like_height, 0], dtype=np.int64)
        ends_uv_np = np.array([max64_int, max64_int, max64_int, max64_int], dtype=np.int64)

        if is_multi_input:
            if multifocal_crop is not None:
                # xtorch version
                starts_y_np[self.height_idx] = multifocal_crop[0] * input_rgb_like_height
                ends_y_np[self.height_idx] = (multifocal_crop[0] + multifocal_crop[2]) * input_rgb_like_height
                starts_uv_np[self.height_idx] = input_rgb_like_height + multifocal_crop[0] * input_rgb_like_height // 2
                ends_uv_np[self.height_idx] = (
                    input_rgb_like_height + (multifocal_crop[0] + multifocal_crop[2]) * input_rgb_like_height // 2
                )

                bytes_per_pixel = YuvQnnConfig.byte_per_pixel if self.yuv10 else 1
                starts_y_np[self.width_idx] = (multifocal_crop[1] * input_rgb_like_width) // 32 * bytes_per_pixel
                ends_y_np[self.width_idx] = (
                    ((multifocal_crop[1] + multifocal_crop[3]) * input_rgb_like_width) // 32 * bytes_per_pixel
                )
                starts_uv_np[self.width_idx] = (multifocal_crop[1] * input_rgb_like_width) // 32 * bytes_per_pixel
                ends_uv_np[self.width_idx] = (
                    ((multifocal_crop[1] + multifocal_crop[3]) * input_rgb_like_width) // 32 * bytes_per_pixel
                )

                slice_y_shape[self.width_idx] = int(slice_y_shape[self.width_idx] * multifocal_crop[3])
                slice_y_shape[self.height_idx] = int(slice_y_shape[self.height_idx] * multifocal_crop[2])
                input_rgb_like_height = slice_y_shape[self.height_idx]
                input_rgb_like_width = int(input_rgb_like_width * multifocal_crop[3])

            else:
                # tensorflow version, which can be removed as soon as TF models are not supported anymore
                crop_y = self._find_subsequent_node(nodes_by_input, yuv420_input_shape_name, ".*FarViewCropY.*")
                mid_crop_y = self._find_subsequent_node(nodes_by_input, yuv420_input_shape_name, ".*MidViewCropY.*")
                if mid_crop_y is not None:
                    crop_y = mid_crop_y
                    mid_crop_uv = self._find_subsequent_node(
                        nodes_by_input, yuv420_input_shape_name, ".*MidViewCropUV.*"
                    )
                    assert mid_crop_uv is not None, "MidViewCropUV node not found"
                    slice_y_shape[self.height_idx] = slice_y_shape[self.height_idx] // 2
                    starts_y_np[self.height_idx] = input_rgb_like_height + mid_crop_y.inputs[1].values[self.height_idx]
                    starts_uv_np[self.height_idx] = mid_crop_uv.inputs[1].values[self.height_idx]
                if crop_y is not None:
                    bytes_per_pixel = YuvQnnConfig.byte_per_pixel if self.yuv10 else 1
                    slice_y_shape[self.width_idx] = slice_y_shape[self.width_idx] // 2
                    starts_y_np[self.width_idx] = crop_y.inputs[1].values[self.width_idx] // 32 * bytes_per_pixel
                    ends_y_np[self.width_idx] = crop_y.inputs[2].values[self.width_idx] // 32 * bytes_per_pixel
                    starts_uv_np[self.width_idx] = crop_y.inputs[1].values[self.width_idx] // 32 * bytes_per_pixel
                    ends_uv_np[self.width_idx] = crop_y.inputs[2].values[self.width_idx] // 32 * bytes_per_pixel
                    input_rgb_like_height = slice_y_shape[self.height_idx]
                    input_rgb_like_width = input_rgb_like_width // 2

        return (
            slice_y_shape,
            starts_y_np,
            ends_y_np,
            starts_uv_np,
            ends_uv_np,
            input_rgb_like_height,
            input_rgb_like_width,
        )

    def _process_each_input(
        self,
        *,
        graph: gs.Graph,
        input_rgb_like: gs.Tensor,
        quant_overrides: QuantizationEncodings,
        vcrop: bool,
        is_multi_input: bool,
        input_cnt: int,
        nodes_by_input: dict[str, list[gs.Node]],
    ) -> tuple[gs.Variable, int, int]:
        input_rgb_like_name = get_name_of_tensor(input_rgb_like)

        if "_" in input_rgb_like_name:
            sp = input_rgb_like_name.split("_")[-1]
            if sp.isdigit():
                i = int(sp)
            else:
                i = input_cnt + 1
        else:
            i = input_cnt + 1

        LOGGER.info(f"RewriteYUVInputPlugin: Processing input {input_rgb_like_name} with shape {input_rgb_like.shape}")
        assert input_rgb_like.shape
        assert len(input_rgb_like.shape) == 4
        assert (input_rgb_like.shape[self.width_idx] % YuvQnnConfig.width_as_channels) == 0

        # Save values needed later
        input_rgb_like_height, input_rgb_like_width, yuv420_input_shape = self.__calculate_new_input_shape(
            input_rgb_like,
            multifocal_input_shape=self.multifocal_target_shapes[i] if self.multifocal_target_shapes else None,
        )

        (
            slice_y_shape,
            starts_y_np,
            ends_y_np,
            starts_uv_np,
            ends_uv_np,
            input_rgb_like_height,
            input_rgb_like_width,
        ) = self._setup_slice_dims(
            yuv420_input_shape=yuv420_input_shape,
            input_rgb_like_height=input_rgb_like_height,
            input_rgb_like_width=input_rgb_like_width,
            is_multi_input=is_multi_input,
            nodes_by_input=nodes_by_input,
            multifocal_crop=self.multifocal_target_crops[i] if self.multifocal_target_crops else None,
        )

        # Create output variables of the slice nodes for YUV to Y and UV separation
        input_y = gs.Variable(
            name=self._with_suffix(is_multi_input=is_multi_input, name="input_y", i=i),
            dtype=np.float32,
            shape=slice_y_shape,
        )
        assert input_y.shape is not None  # please type checkers
        input_uv = gs.Variable(
            name=self._with_suffix(is_multi_input=is_multi_input, name="input_uv", i=i),
            dtype=np.float32,
            shape=input_y.shape[:],
        )
        assert input_uv.shape is not None  # please type checkers
        set_dim(input_uv, self.height_idx, shape_to_list(input_uv.shape)[self.height_idx] // YuvQnnConfig.uv_factor)

        # Create slice node for YUV to Y separation
        starts_y = gs.Constant(self._with_suffix(is_multi_input=is_multi_input, name="starts_y", i=i), starts_y_np)
        ends_y = gs.Constant(self._with_suffix(is_multi_input=is_multi_input, name="ends_y", i=i), ends_y_np)
        axes_y = gs.Constant(
            self._with_suffix(is_multi_input=is_multi_input, name="axes_y", i=i), np.array([0, 1, 2, 3], dtype=np.int64)
        )
        ysplice_node = gs.Node(
            op="Slice",
            name=self._with_suffix(is_multi_input=is_multi_input, name="SliceY", i=i),
            inputs=[yuv420_input_shape, starts_y, ends_y, axes_y],
            outputs=[input_y],
        )
        append_node(graph, ysplice_node)

        # Create slice node for YUV to UV separation
        starts_uv = gs.Constant(self._with_suffix(is_multi_input=is_multi_input, name="starts_uv", i=i), starts_uv_np)
        ends_uv = gs.Constant(self._with_suffix(is_multi_input=is_multi_input, name="ends_uv", i=i), ends_uv_np)
        axes_uv = gs.Constant(
            self._with_suffix(is_multi_input=is_multi_input, name="axes_uv", i=i),
            np.array([0, 1, 2, 3], dtype=np.int64),
        )
        uvsplice_node = gs.Node(
            op="Slice",
            name=self._with_suffix(is_multi_input=is_multi_input, name="SliceUV", i=i),
            inputs=[yuv420_input_shape, starts_uv, ends_uv, axes_uv],
            outputs=[input_uv],
        )
        append_node(graph, uvsplice_node)

        # Create YUV output variable
        space_to_depth_factor = 4
        output_width = input_rgb_like_width // space_to_depth_factor
        output_height = input_rgb_like_height // space_to_depth_factor
        yuv_op = "YUV10Input" if self.yuv10 else "YUVInput"
        output_channels = YuvQnnConfig.width_as_channels * (YuvQnnConfig.byte_per_pixel if self.yuv10 else 1)

        yuv_out = gs.Variable(
            name=self._with_suffix(is_multi_input=is_multi_input, name=yuv_op, i=i),
            dtype=np.float32,
            shape=(1, output_channels, output_height, output_width),
        )

        # Create YUV conversion node
        yuvinput_node = gs.Node(
            op=yuv_op,
            name=self._with_suffix(is_multi_input=is_multi_input, name=yuv_op, i=i),
            inputs=[input_y, input_uv],
            outputs=[yuv_out],
            domain=ATHENA_DOMAIN,
        )
        append_node(graph, yuvinput_node)

        if vcrop:
            yuv_out = self._add_vcrop(
                graph=graph,
                yuv_out=yuv_out,
                input_rgb_like_height=input_rgb_like_height,
                i=i,
                output_dim=(output_channels, output_height, output_width),
                space_to_depth_factor=space_to_depth_factor,
                is_multi_input=is_multi_input,
            )
        quant_overrides.add_activation_encoding(
            get_name_of_tensor(yuv420_input_shape), Encoding(bitwidth=8, scale=1.0, offset=0)
        )
        quant_overrides.add_activation_encoding(
            self._with_suffix(is_multi_input=is_multi_input, name="input_y", i=i),
            Encoding(
                bitwidth=8,
                scale=1.0,
                offset=0,
            ),
        )
        quant_overrides.add_activation_encoding(
            self._with_suffix(is_multi_input=is_multi_input, name="input_uv", i=i),
            Encoding(
                bitwidth=8,
                scale=1.0,
                offset=0,
            ),
        )
        quant_overrides.add_activation_encoding(
            self._with_suffix(is_multi_input=is_multi_input, name=yuv_op, i=i),
            Encoding(
                bitwidth=8,
                scale=1.0,
                offset=0,
            ),
        )

        if vcrop:
            quant_overrides.add_activation_encoding(
                self._with_suffix(is_multi_input=is_multi_input, name="vcrop_out", i=i),
                Encoding(
                    bitwidth=8,
                    scale=1.0,
                    offset=0,
                ),
            )
        return yuv_out, output_channels, i

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Match the YUV input in model inputs."""

        # If YUV2YUV420 node is not present in the graph, return the graph as it is
        if not is_node_in_graph(graph, self.yuv_node_pattern):
            return graph

        # Check if the graph has the vcrop node
        vcrop = bool(self.vcrop_node_pattern) and is_node_in_graph(graph, self.vcrop_node_pattern)

        # Depending on input type (split or merged), inject the YUV plugin the appropriate way and return the graph
        if self.split_input:
            if vcrop:
                msg = "Vertical cropping is not implemented for split input yet"
                raise NotImplementedError(msg)
            self.__inject_yuv_plugin_with_distinct_inputs(graph, quant_overrides)
        else:
            self.__inject_yuv_plugin_with_merged_inputs(graph, quant_overrides, vcrop)
        return graph

    def __inject_yuv_plugin_with_merged_inputs(
        self,
        graph: gs.Graph,
        quant_overrides: QuantizationEncodings,
        vcrop: bool,  # noqa: FBT001
    ) -> None:
        """Inject the YUV plugin with two slice layer and a single input tensor.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings
            vcrop: Flag for vertical cropping
        """

        nodes_by_input = defaultdict(list)

        for n in graph.nodes:
            for i in n.inputs:
                nodes_by_input[i.name].append(n)

        is_multi_input = len(graph.inputs) > 1
        input_cnt = -1

        adjusted_weight_names: set[str] = set()
        subsequent_convs = self._find_subsequent_convs(nodes_by_input, graph)

        some_input_found = False

        for input_rgb_like in graph.inputs:
            input_rgb_like_name = get_name_of_tensor(input_rgb_like)

            # Check, if the input node is YUV2YUV420 compatible
            # by checking the yuv_node_pattern. Skip, if not.
            input_node = nodes_by_input[input_rgb_like_name]
            if re.match(self.yuv_node_pattern, input_node[0].name) is None:
                LOGGER.info(f"RewriteYUVInputPlugin: Skipping input {input_rgb_like_name}")
                continue

            some_input_found = True

            yuv_out, output_channels, input_cnt = self._process_each_input(
                graph=graph,
                input_rgb_like=input_rgb_like,
                quant_overrides=quant_overrides,
                vcrop=vcrop,
                is_multi_input=is_multi_input,
                input_cnt=input_cnt,
                nodes_by_input=nodes_by_input,
            )

            subsequent_conv = subsequent_convs.get(input_rgb_like_name)

            if subsequent_conv:
                LOGGER.info(f"RewriteYUVInputPlugin: Modify YUV conv weights for node {subsequent_conv.name}")
                # modify node to include padding
                # modify weights to work with 10bit YUV
                subsequent_conv.inputs[0] = yuv_out
                weights = subsequent_conv.inputs[1]
                assert weights.shape is not None  # please type checkers
                if weights.name in adjusted_weight_names:
                    LOGGER.info(
                        f"RewriteYUVInputPlugin: Weights {weights.name} (shape= {weights.shape}) already adjusted."
                    )
                else:
                    LOGGER.info(f"RewriteYUVInputPlugin: Adjusting weights: {weights.name} (shape= {weights.shape}).")

                    subsequent_conv.inputs[1].values = _expand_yuv_conv_weights(
                        weights.values, output_channels, self.yuv10
                    )
                    adjusted_weight_names.add(weights.name)

        if not some_input_found:
            LOGGER.warning(
                f"RewriteYUVInputPlugin: All inputs have been skipped; no input matched with subsequent node pattern"
                f"'{self.yuv_subsequent_conv_node_pattern}'."
                "Maybe you forgot to update the node pattern?"
            )

        LOGGER.info("RewriteYUVInputPlugin:  Done.")

        graph.cleanup().toposort()

        onnx_graph = gs.export_onnx(graph)
        onnx_graph = osi.infer_shapes(onnx_graph, check_type=True, strict_mode=True, data_prop=True)
        onnx.checker.check_model(onnx_graph)
        graph = gs.import_onnx(onnx_graph)

    def __calculate_new_input_shape(
        self, input_tensor: gs.Tensor, multifocal_input_shape: tuple[int, int] | None = None
    ) -> tuple[int, int, gs.Tensor]:
        """Calculate new input shape for YUV plugin based on the current shape of the input tensor in the graph.

        Args:
            input_tensor: Current input tensor in the graph
            multifocal_input_shape: Shape of the input tensor for multifocal deployment

        Returns:
            rgb_like height, rgb_like width, new input tensor with shape adjusted for YUV plugin.
        """
        # Test RGB like or YUV like
        # Save values needed later
        rgb_like_num_channels = 3
        height_factor = 1.0 + 1.0 / YuvQnnConfig.uv_factor  # Height consists of input_y + subsampled input_uv

        if multifocal_input_shape is not None:
            # For multifocal the input shapes in the onnx graph are the train shapes, since the deployment
            # shapes require additional cropping, which are added in the YUVInputPlugin. Therefore, the multifocal
            # deployment shapes are explicitely set here.
            set_dim(input_tensor, self.height_idx, multifocal_input_shape[0])
            set_dim(input_tensor, self.width_idx, multifocal_input_shape[1])

        input_tensor_shape = shape_to_list(input_tensor.shape)
        input_rgb_like_width = input_tensor_shape[self.width_idx]
        input_rgb_like_height = -1

        if input_tensor.shape[1] == rgb_like_num_channels:
            input_rgb_like_height = input_tensor_shape[self.height_idx]
            # Change input_rgb_like to YUV contiguous format. We have NCHW with N=C=1, H=1.5*rgb_h and W=rgb_w*2 (16bit)
            set_dim(input_tensor, self.channel_idx, 1)
            set_dim(input_tensor, self.height_idx, int(input_rgb_like_height * height_factor))
        elif input_tensor.shape[1] == 1:
            # YUV420 like
            input_rgb_like_height = int(input_tensor_shape[self.height_idx] / height_factor)

        input_tensor_shape = shape_to_list(input_tensor.shape)
        if self.yuv10:
            set_dim(input_tensor, self.width_idx, input_tensor_shape[self.width_idx] * YuvQnnConfig.byte_per_pixel)

        input_tensor_shape = shape_to_list(input_tensor.shape)
        set_dim(input_tensor, self.width_idx, input_tensor.shape[self.width_idx] // YuvQnnConfig.width_as_channels)
        set_dim(input_tensor, self.channel_idx, YuvQnnConfig.width_as_channels)

        return input_rgb_like_height, input_rgb_like_width, input_tensor

    def __inject_yuv_plugin_with_distinct_inputs(
        self,
        graph: gs.Graph,
        quant_overrides: QuantizationEncodings,
    ) -> None:
        """Inject the YUV plugin directly after two inputs."""

        input_y = graph.inputs[0]
        assert input_y.shape is not None  # please type checkers
        assert len(input_y.shape) == 4
        assert (input_y.shape[3] % YuvQnnConfig.width_as_channels) == 0
        space_to_depth_factor = 4
        channels = YuvQnnConfig.width_as_channels
        input_y_shape = shape_to_list(input_y.shape)
        original_width = input_y_shape[3]
        set_dim(input_y, 3, input_y_shape[3] // channels)
        set_dim(input_y, 1, channels)
        input_width = original_width // space_to_depth_factor

        input_y.name = "input_y"

        if self.yuv10:
            input_y_shape = shape_to_list(input_y.shape)
            set_dim(input_y, 3, input_y_shape[3] * YuvQnnConfig.byte_per_pixel)

        input_uv = gs.Variable(
            name="input_uv",
            dtype=np.float32,
            shape=input_y.shape[:],
        )
        assert input_uv.shape is not None  # please type checkers
        set_dim(input_uv, 2, shape_to_list(input_uv.shape)[2] // YuvQnnConfig.uv_factor)
        graph.inputs.append(input_uv)

        if self.yuv10:
            yuv_op = "YUV10Input"
            output_channels = channels * YuvQnnConfig.byte_per_pixel
        else:
            yuv_op = "YUVInput"
            output_channels = channels

        yuv_out = gs.Variable(
            name=yuv_op,
            dtype=np.float32,
            shape=(1, output_channels, input_y.shape[2] // space_to_depth_factor, input_width),
        )

        yuvinput_node = gs.Node(
            op=yuv_op, name=yuv_op, inputs=[input_y, input_uv], outputs=[yuv_out], domain=ATHENA_DOMAIN
        )
        append_node(graph, yuvinput_node)

        graph.nodes[0].inputs.clear()
        graph.nodes[0].outputs.clear()
        for node in graph.nodes:
            if re.match(self.yuv_subsequent_conv_node_pattern, node.name):
                # modify node to include padding
                # modify weights to work with 10bit YUV
                node.inputs[0] = yuv_out
                # modify weights to work with 10bit YUV
                weights = node.inputs[1]
                node.inputs[1].values = _expand_yuv_conv_weights(weights.values, output_channels, self.yuv10)

        quant_overrides.add_activation_encoding(yuv_op, Encoding(bitwidth=8, scale=1.0, offset=0))
        quant_overrides.add_activation_encoding("input_y", Encoding(bitwidth=8, scale=1.0, offset=0))
        quant_overrides.add_activation_encoding("input_uv", Encoding(bitwidth=8, scale=1.0, offset=0))
        graph.cleanup().toposort()
        onnx_graph = gs.export_onnx(graph)
        onnx_graph = osi.infer_shapes(onnx_graph, check_type=True, strict_mode=True, data_prop=True)
        onnx.checker.check_model(onnx_graph)
        graph = gs.import_onnx(onnx_graph)


class RewriteYUV420InputWithPlugin(AbstractGraphRewriter):
    """Replaces input yuv image consumer nodes with the YUV plugin.

    The input yuv image consumer nodes combination containing space2depth, concat and conv2d are replaced
    with the YUV 10bit plugin, along with required adaptations to the conv2d layer weights & biases.
    Additionally, image inputs could be set to be normalized, in-case required. This is done by incorporating
    scale and offset into the conv2d weights & biases as well.
    """

    def __init__(
        self,
        yuv_node_pattern: str,
        yuv_subsequent_conv_node_pattern: str,
        input_y_name: str,
        input_uv_name: str,
        yuv_plugin_name: str,
        normalize_input: bool = False,  # noqa: FBT001, FBT002
        split_yuv_input: bool = True,  # noqa: FBT001, FBT002
    ) -> None:
        """Init YUV plugin rewriter to replace yuv image consumer nodes.

        Args:
            yuv_node_pattern: Name pattern of the YUV2YUV420 node.
            yuv_subsequent_conv_node_pattern: Name pattern of the subsequent Conv2D after space2depth.
            input_y_name: Model Y input name.
            input_uv_name: Model UV input name.
            yuv_plugin_name: YUV plugin name to be set.
            normalize_input: Flag for applying normalization to the input.
            split_yuv_input: Flag for having separate inputs for y & uv.
        """
        self.yuv_node_pattern = yuv_node_pattern
        self.yuv_subsequent_conv_node_pattern = yuv_subsequent_conv_node_pattern
        self.input_y_name = input_y_name
        self.input_uv_name = input_uv_name
        self.yuv_plugin_name = yuv_plugin_name
        self.normalize_input = normalize_input
        self.split_yuv_input = split_yuv_input

    def apply_rule_on_start(  # noqa: C901, PLR0912, PLR0915
        self,
        graph: gs.Graph,
        quant_overrides: QuantizationEncodings,
        yuv10: bool = True,  # noqa: FBT001, FBT002
    ) -> gs.Graph:
        """Match the YUV input in model inputs."""

        # If YUV2YUV420 node is not present in the graph, return the graph as is
        if not is_node_in_graph(graph, self.yuv_node_pattern):
            return graph

        input_y = None
        input_uv = None
        for g_inp in graph.inputs:
            name = get_name_of_tensor(g_inp)
            if name == self.input_y_name:
                input_y = g_inp
            if name == self.input_uv_name:
                input_uv = g_inp

        assert input_y is not None
        assert input_uv is not None

        assert len(input_y.shape) == 4
        assert (input_y.shape[3] % YuvQnnConfig.width_as_channels) == 0
        space_to_depth_factor = 4
        channels = YuvQnnConfig.width_as_channels
        input_y_shape = shape_to_list(input_y.shape)
        original_width = input_y_shape[3]
        set_dim(input_y, 3, input_y_shape[3] // channels)
        set_dim(input_y, 1, channels)
        input_width = original_width // space_to_depth_factor

        if yuv10:
            input_y_shape = shape_to_list(input_y.shape)
            set_dim(input_y, 3, input_y_shape[3] * YuvQnnConfig.byte_per_pixel)

        input_y_shape = shape_to_list(input_y.shape)
        input_uv.shape = input_y_shape
        set_dim(input_uv, 2, input_y_shape[2] // YuvQnnConfig.uv_factor)

        if yuv10:
            yuv_op = "YUV10Input"
            output_channels = channels * YuvQnnConfig.byte_per_pixel
        else:
            yuv_op = "YUVInput"
            output_channels = channels

        yuv_out = gs.Variable(
            name=f"{self.yuv_plugin_name}_out",
            dtype=np.float32,
            shape=(1, output_channels, input_y.shape[2] // space_to_depth_factor, input_width),
        )
        yuv_out_name = get_name_of_tensor(yuv_out)

        yuvinput_node = gs.Node(
            op=yuv_op, name=self.yuv_plugin_name, inputs=[input_y, input_uv], outputs=[yuv_out], domain=ATHENA_DOMAIN
        )
        append_node(graph, yuvinput_node)

        input_y_name = get_name_of_tensor(input_y)
        input_uv_name = get_name_of_tensor(input_uv)
        # If split input not set, merge the inputs and add slice node for splitting later
        yuv_merged_input_name = ""
        if not self.split_yuv_input:
            yuv_merged_input_shape = shape_to_list(input_y.shape)
            yuv_merged_input_shape[2] += shape_to_list(input_uv.shape)[2]

            yuv_merged_input = gs.Variable(
                name=input_y_name.replace("_y", "_yuv"),
                dtype=np.float32,
                shape=yuv_merged_input_shape,
            )
            yuv_merged_input_name = yuv_merged_input.name

            max_int = np.iinfo(np.int64).max
            # y
            starts_y = gs.Constant(f"starts_y_{input_y_name}", np.array([0, 0, 0, 0], dtype=np.int64))
            ends_y = gs.Constant(
                f"ends_y_{input_y_name}", np.array([max_int, max_int, input_y.shape[2], max_int], dtype=np.int64)
            )
            axes_y = gs.Constant(f"axes_y_{input_y_name}", np.array([0, 1, 2, 3], dtype=np.int64))

            ysplice_node = gs.Node(
                op="Slice",
                name=f"SliceY_{input_y_name}",
                inputs=[yuv_merged_input, starts_y, ends_y, axes_y],
                outputs=[input_y],
            )
            append_node(graph, ysplice_node)

            # uv
            starts_uv = gs.Constant(f"starts_uv_{input_uv_name}", np.array([0, 0, input_y.shape[2], 0], dtype=np.int64))
            ends_uv = gs.Constant(
                f"ends_uv_{input_uv_name}", np.array([max_int, max_int, max_int, max_int], dtype=np.int64)
            )
            axes_uv = gs.Constant(f"axes_uv_{input_uv_name}", np.array([0, 1, 2, 3], dtype=np.int64))

            uvsplice_node = gs.Node(
                op="Slice",
                name=f"SliceUV_{input_uv_name}",
                inputs=[yuv_merged_input, starts_uv, ends_uv, axes_uv],
                outputs=[input_uv],
            )
            append_node(graph, uvsplice_node)

            graph.inputs.remove(input_y)
            graph.inputs.remove(input_uv)
            graph.inputs.append(yuv_merged_input)

        # Adapt the follow-up conv layer according to yuv_plugin
        for node in graph.nodes:
            if re.match(self.yuv_subsequent_conv_node_pattern, node.name):
                # Normalize the inputs, if enabled
                # modify node to include padding
                # modify weights to work with 10bit YUV

                # Normalize inputs to Conv layer if enabled
                # ------------------------------------------
                if self.normalize_input:
                    biases_val = node.inputs[2].values
                    weights_val = node.inputs[1].values
                    bias_adj = []
                    for out_filter in weights_val:
                        bias_adj.append(out_filter.sum())  # noqa: PERF401
                    biases_val -= np.asarray(bias_adj).astype(np.float32)

                    # As, both the y and uv are scaled to 10 bits by yuv plugin,
                    # however, yuv plugin sets the the number range to between 0-255
                    # (8 bits + 2 fractional bits)
                    y_scale = uv_scale = ((2.0**8) - 1.0) / 2
                    weights_val[:, 0:16, :, :] *= 1.0 / y_scale
                    weights_val[:, 16:24, :, :] *= 1.0 / uv_scale
                    node.inputs[2].values = biases_val
                    node.inputs[1].values = weights_val

                node.inputs[0] = yuv_out
                # modify weights to work with 10bit YUV
                weights = node.inputs[1]
                node.inputs[1].values = _expand_yuv_conv_weights(weights.values, output_channels, yuv10)

        # Fill-in required default quantization overrides, setting no-quantization for inputs
        # and node related to inputs, as they are already in 8-bits.
        if not self.split_yuv_input:
            quant_overrides.add_activation_encoding(yuv_merged_input_name, Encoding(bitwidth=8, scale=1.0, offset=0))
        quant_overrides.add_activation_encoding(input_y_name, Encoding(bitwidth=8, scale=1.0, offset=0))
        quant_overrides.add_activation_encoding(input_uv_name, Encoding(bitwidth=8, scale=1.0, offset=0))
        quant_overrides.add_activation_encoding(yuv_out_name, Encoding(bitwidth=8, scale=1.0, offset=0))
        graph.cleanup().toposort()
        onnx_graph = gs.export_onnx(graph)
        onnx_graph = osi.infer_shapes(onnx_graph, check_type=True, strict_mode=True, data_prop=True)
        onnx.checker.check_model(onnx_graph)
        graph = gs.import_onnx(onnx_graph)

        return graph


def _expand_yuv_conv_weights(
    weights: npt.NDArray[np.float32],
    output_channels: int,
    yuv10: bool,  # noqa: FBT001
) -> npt.NDArray[np.float32]:
    """Exapands/pads the weights of Conv layer in the model following the yuv nodes/plugin.

    As, during inserting the plugin, the 16bits input is transformed to contain MSB and LSBs
    separately. The resulting data is doubled, and conv layer weights should be padded
    accordingly to take in the data accordingly.

    Args:
        weights: Original Conv layer weights, to be extended.
        output_channels: Resulting outputs channels of yuv plugin.
        yuv10: Flag, if it is yuv 10bit input.

    Returns:
        numpy.ndarray: Expanded/padded data.
    """
    # W OIHW
    w_padded = np.pad(
        weights,
        [(0, 0), (0, output_channels - weights.shape[1]), (0, 0), (0, 0)],
        mode="constant",
        constant_values=0,
    )
    if yuv10:
        for k in range(weights.shape[2]):
            for t in range(weights.shape[3]):
                for co in range(weights.shape[0]):
                    for ci in range(weights.shape[1]):
                        newidx = (ci // 4) % 2 + (ci % 4) * 2 + (ci // 8) * 8
                        w_padded[co, newidx, k, t] = weights[co, ci, k, t]
                        w_padded[co, newidx + 32, k, t] = weights[co, ci, k, t] / 4
    return w_padded


class RewriteYUV444Plugin(AbstractGraphRewriter):
    """Replace the RGB input with YUV444 layer.

    This plugin, for QNN, replaces the input RGB layer with two inputs: 8 bit y and uv in a byte packed format.
    """

    def __init__(self, yuv444_node_pattern: str) -> None:
        """Init YUV plugin rewriter to remove the RGB to YUV444 conversion.

        Args:
            yuv444_node_pattern: Name pattern of the RGB2YUV444 node.
        """
        self.yuv444_node_pattern = yuv444_node_pattern

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Match the YUV input in model inputs.

        Args:
            graph: Onnx graph surgeon of the model that surgery should be performed on.
            quant_overrides: Not used, but required to satisfy common interface with vision usecase.

        Returns:
            onnx graph after surgery/removal of RGB2YUV444 conversion.
        """
        delete_node = None
        delete_node_index = -1
        # Return same graph early if node pattern did not match
        for index, node in enumerate(graph.nodes):
            if re.match(self.yuv444_node_pattern, node.name):
                delete_node = node
                delete_node_index = index
                break
        else:
            return graph

        # Redirect inputs from the layer following the delete layer
        graph.nodes[delete_node_index + 1].inputs[0] = graph.inputs[0]
        # Disconnect delete node from graph
        delete_node.outputs.clear()
        # Automatically removes nodes which don't contribute (delete node in this case)
        graph.cleanup()

        return graph


class FixTransposeReshapeSoftmax(AbstractGraphRewriter):
    """Add additional no-op transpose to patterns of transpose-reshape-softmax."""

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Add additional no-op transpose to patterns of transpose-reshape-softmax.

        Add additional transpose, as in this constellation QNN currently (2.10.4) does not optimize the
        transpose out which leads to wrong outputs and therefore non-functioning tasks which have this
        pattern.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings

        Returns:
            Rewritten graph
        """
        onnx_graph = gs.export_onnx(graph)
        onnx_graph = osi.infer_shapes(onnx_graph, check_type=True, strict_mode=True, data_prop=True)
        graph = gs.import_onnx(onnx_graph)
        for node in graph.nodes:
            if node.op == "Transpose" and is_next_node_present(node):
                next_node = node.o()
                rank = len(node.outputs[0].shape)
                noop_perm = np.arange(rank).tolist()
                if (
                    next_node.op == "Reshape"
                    and is_next_node_present(next_node)
                    and next_node.o().op == "Softmax"
                    and node.attrs["perm"] != noop_perm
                ):
                    transpose_out = gs.Variable(name=f"{node.outputs[0].name}", dtype=np.float32)
                    noop_node = gs.Node(
                        op="Transpose",
                        name=f"{node.name}_noop",
                        attrs={"perm": noop_perm},
                        inputs=[transpose_out],
                        outputs=node.outputs,
                    )
                    noop_node.outputs[0].name += "_noop"
                    append_node(graph, noop_node)
                    node.outputs.clear()
                    node.outputs.append(transpose_out)
                    LOGGER.info(f"{self.__class__.__name__} applied to {node.name}.")

        graph.cleanup().toposort()

        onnx_graph = gs.export_onnx(graph)
        onnx_graph = osi.infer_shapes(onnx_graph, check_type=True, strict_mode=True, data_prop=True)
        graph = gs.import_onnx(onnx_graph)

        return graph


class InjectLSRCenternetXTorchPlugin(AbstractGraphRewriter):
    """Inject Centernet plugin for LSR head."""

    HEAD_PREFIX: typing.Final[str] = "light_head_"

    @dataclass
    class ScalingConfig:
        """Config for inserting scaling nodes."""

        # Where to insert the scaling node
        pre_concat_pos: int
        post_centernet_mul_name: str

        scale: float = 1.0
        scaling_node_name: str = "PreCenternetName"

    def __init__(
        self,
        start_replacement_tensor_name_pattern: str,
    ) -> None:
        """Init the LSR centernet plugin rewriter.

        Args:
            start_replacement_tensor_name_pattern: Name of the tensor where the replacement starts
        """

        self.start_replacement_tensor_name_pattern = start_replacement_tensor_name_pattern

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:  # noqa: PLR0915 C901
        """Inject the Centernet plugin for the LSR head."""

        source_graph = graph.copy()

        cutoff_nodes = {
            int(match_.group(1)): n
            for n in graph.nodes
            if (match_ := re.fullmatch(self.start_replacement_tensor_name_pattern, n.name)) and match_
        }

        if not cutoff_nodes:
            return graph

        @dataclass
        class LightHeadConfig:
            """Config for the light head."""

            cutoff_node: gs.Node
            parameters: CenterNetParams
            branch_prefix: str

        light_head_configs = []
        for stride, cutoff_node in cutoff_nodes.items():
            params_objects = [
                CenterNetFarviewParams(),
                CenterNetMidviewParams(),
                CenterNetWideviewParams(),
                CenterNetParams(),
            ]

            params = next(
                (params for params in params_objects if params.stride == stride),
                CenterNetParams(),  # Default if no match
            )

            light_head_configs.append(LightHeadConfig(cutoff_node, params, f"stride_{stride}_"))

        multiple_branches = len(light_head_configs) > 1
        replacement_super_model = None

        for config in light_head_configs:
            replacement_sub_graph, replacement_root_tensor = create_center_net(
                config.parameters,
                config.cutoff_node.outputs[0].name,
                name_prefix=f"{config.branch_prefix}{self.HEAD_PREFIX}",
                opset=graph.opset,
            )
            typing.cast(gs.Variable, replacement_sub_graph.inputs[0]).dtype = np.dtype(np.float32)
            replacement_sub_graph.cleanup().toposort()

            if not replacement_super_model:
                replacement_super_model = gs.export_onnx(replacement_sub_graph)
            else:
                replacement_super_model = onnx.compose.merge_models(
                    replacement_super_model, gs.export_onnx(replacement_sub_graph), io_map=[]
                )
        assert replacement_super_model
        replacement_super_graph = gs.import_onnx(replacement_super_model)

        assert isinstance(replacement_super_graph, gs.Graph)
        if multiple_branches:
            outputs = sorted(
                replacement_super_graph.outputs, key=lambda x: typing.cast(gs.Variable, x).name.startswith("stride")
            )
            light_index = next(
                x for x, val in enumerate(outputs) if typing.cast(gs.Variable, val).name.startswith("stride")
            )

            def split_after_stride(s: str) -> list[str]:
                return re.split(r"(stride_\d+_)", s, maxsplit=1)[1:]

            # put light outputs last such that we can easily remove them and append the new ones
            light_outputs = outputs[light_index:]
            # groupby needs prior sorting
            light_outputs = sorted(light_outputs, key=lambda x: split_after_stride(typing.cast(gs.Variable, x).name)[1])
            new_light_outputs = []
            for name, output_iter in groupby(
                light_outputs, lambda x: split_after_stride(typing.cast(gs.Variable, x).name)[1]
            ):
                output_group = list(output_iter)

                trifocal_output_name = name.removeprefix(self.HEAD_PREFIX)
                # trifocal_output_name = trifocal_output_name.replace("light", "light_trifocal")
                concat_out = gs.Variable(f"{trifocal_output_name}", dtype=np.dtype(np.float32))

                concat_node = gs.Node(
                    name=f"{trifocal_output_name}_concat",
                    op="Concat",
                    inputs=output_group,
                    outputs=[concat_out],
                    attrs={"axis": 1},
                )
                assert isinstance(replacement_super_graph.nodes, list)
                replacement_super_graph.nodes.append(concat_node)

                assert isinstance(output_group[0].shape, list)
                concat_shape = output_group[0].shape.copy()
                concat_shape[1] = len(cutoff_nodes) * concat_shape[1]
                concat_out.shape = concat_shape

                new_light_outputs.append(concat_out)

            outputs[light_index:] = new_light_outputs
            replacement_super_graph.outputs = outputs
        else:
            for output in replacement_super_graph.outputs:
                output.name = typing.cast(gs.Variable, output).name.removeprefix(self.HEAD_PREFIX)

        replacement_super_model = gs.export_onnx(replacement_super_graph)

        with tempfile.TemporaryDirectory() as temp_dir:
            onnx.save(replacement_super_model, pathlib.Path(temp_dir) / "replacement_model.onnx")

            updated_graph = load_and_replace(
                source_model=source_graph,
                template_model_path=pathlib.Path(__file__).parent
                / "replacement_subgraphs"
                / "lsr_template_subgraph_trifocal_xtorch.onnx",
                replacement_model_path=pathlib.Path(temp_dir) / "replacement_model.onnx",
            )
            assert updated_graph, (
                "Failed to replace the light postprocessing subgraph for qnn conversion. This might be due "
                "to changes in the source model."
            )

        for _i, config in enumerate(light_head_configs):
            score_scaling = self.ScalingConfig(
                pre_concat_pos=3,
                post_centernet_mul_name=f"{config.branch_prefix}{self.HEAD_PREFIX}feature_scale_Mul",
                scale=2,
                scaling_node_name=f"{config.branch_prefix}{self.HEAD_PREFIX}PreScoreScaling",
            )
            centernet_node = self.get_centernet_node(updated_graph, f"{config.branch_prefix}{self.HEAD_PREFIX}")
            updated_graph = self.add_scaling_for_center_net_plugin(
                updated_graph,
                score_scaling,
                centernet_node,
            )

            # TODO: Add accuracy extension for centernet  # noqa: TD003
            updated_graph = self.add_accuracy_extension_for_center_net(
                updated_graph, f"{config.branch_prefix}{self.HEAD_PREFIX}", config.parameters, centernet_node
            )

            if isinstance(config.parameters, MaskedCenternetMixin):
                updated_graph = self.add_masking_for_center_net_plugin(
                    updated_graph,
                    centernet_node,
                    config.parameters.view_mask,
                    config.branch_prefix,
                )
                with_masking = True
            else:
                with_masking = False

            output_prefix = "light"  # light_trifocal" if multiple_branches else "light"
            self.update_quant_overrides(
                quant_overrides,
                centernet_node,
                config.cutoff_node,
                config.branch_prefix,
                output_prefix=output_prefix,
                with_masking=with_masking,
            )

        updated_graph.cleanup().toposort()

        return updated_graph

    def update_quant_overrides(
        self,
        quant_overrides: QuantizationEncodings,
        centernet_node: gs.Node,
        cutoff_node: gs.Node,
        branch: str,
        *,
        output_prefix: str,
        with_masking: bool = False,
    ) -> None:
        """Force input and output tensors of Centernet plugin encodings to be the same.

        Args:
            quant_overrides: Quantization override dict to be updated.
            centernet_node: centernet node.
            cutoff_node: cutoff node where the CenternetPP Plugin is inserted
            branch: branch_name for trifocal head
            with_masking: Flag to indicate if masking is used
            output_prefix: Prefix for the output tensor names
        """

        # Quantization params must be the same in the input and output tensor of the Centernet Plugin.
        # Scale and offset taken from Azure conversion run with full calibration dataset.
        quant_overrides.add_activation_encoding(cutoff_node.outputs[0].name, Encoding(bitwidth=8, min=0.0, max=3.0))
        quant_overrides.add_activation_encoding(
            f"{branch}light_head_centernet_coords_int", Encoding(bitwidth=8, min=0.0, max=3.0)
        )

        # TODO: Add accuracy extension for centernet  # noqa: TD003
        for node in ["wh_conv", "wh_conv_relu"]:
            quant_overrides.add_activation_encoding(
                f"{branch}light_head_{node}",
                Encoding(bitwidth=8, min=0.0, max=3.0),
            )

        # Insert quantization overrides to last Conv layer of the Light logits
        # Reason is that detections smaller than -2 are filtered out after sigmoid activation

        if with_masking:
            mask_offset_add_node = centernet_node.i().i()
            if mask_offset_add_node.op == "Add":
                mask_offset_add_node_name = mask_offset_add_node.outputs[0].name
                quant_overrides.add_activation_encoding(
                    mask_offset_add_node_name, Encoding(bitwidth=8, min=-2.0, max=4.0)
                )

            filter_offset_mul_node = mask_offset_add_node.i()
            if filter_offset_mul_node.op == "Mul":
                filter_offset_mul_node_name = filter_offset_mul_node.outputs[0].name
                quant_overrides.add_activation_encoding(
                    filter_offset_mul_node_name, Encoding(bitwidth=8, min=-2.0, max=4.0)
                )
            conv_node = filter_offset_mul_node.i()
        else:
            conv_node = centernet_node.i().i()

        if conv_node.op == "Conv":
            conv_output_name = conv_node.outputs[0].name
            quant_overrides.add_activation_encoding(conv_output_name, Encoding(bitwidth=8, min=-2.0, max=4.0))
        else:
            msg = "Could not find last Conv layer."
            raise RuntimeError(msg)

        # Override classes to be 0-255 integers
        classes_layes_to_override = [
            f"{branch}light_head_cast",
            f"{branch}light_head_cast___3",
            f"{output_prefix}_classes",
        ]
        for layer_name in classes_layes_to_override:
            quant_overrides.add_activation_encoding(layer_name, Encoding(bitwidth=8, scale=1.0, offset=0))

        # Box coordinates needs to be overridden to 16 bit due it predicting pixel coordinates
        coords_layers_to_override = [
            f"{branch}light_head_cast___1",  # Cast from int32 to uint16
            f"{branch}light_head_wh",  # Width
            f"{branch}light_head_wh_small",  # Width
            f"{branch}light_head_wh_large",  # Width
            f"{branch}light_head_relu_size",  # Width
            f"{branch}light_head_size2",  # Width
            f"{branch}light_head_xy2",  # Add offset
            f"{branch}light_head_box1",  # Sub box dim
            f"{branch}light_head_box2",  # Add box dim
            f"{branch}light_head_box",  # Concat
            f"{branch}light_head_squeeze___3",  # Squeeze
            f"{branch}light_head_transpose___1",
            f"{output_prefix}_boxes",
            f"{output_prefix}_group_boxes",
        ]
        for layer_name in coords_layers_to_override:
            quant_overrides.add_activation_encoding(layer_name, Encoding(bitwidth=16))

        params_to_override = [
            f"{branch}light_head_wh_threshold",  # Width
        ]
        for layer_name in params_to_override:
            quant_overrides.add_param_encoding(layer_name, Encoding(bitwidth=16))

    def get_centernet_node(self, graph: gs.Graph, name_filter: str) -> gs.Node:
        """Find CenterNetPP node in the graph.

        Args:
            graph: Graph to search for the node
            name_filter: get first centernet node that matches the filter in case of multiple centernet nodes
        Returns:
            CenterNetNode
        """
        for node in graph.nodes:
            if node.op == "CenterNetPP" and name_filter in node.name:
                return node
        msg = "Centernet node not found in the graph."
        raise RuntimeError(msg)

    def add_masking_for_center_net_plugin(
        self, graph: gs.Graph, centernet_node: gs.Node, masks: ViewMasks, branch: str
    ) -> gs.Graph:
        """Inserts masking nodes before and after CenterNetPP."""
        concat_node = centernet_node.i()
        heatmap_node = centernet_node.i().i()
        heatmap_variable = heatmap_node.outputs[0]

        # Multiply masked values with 0
        heat_map_filtered = gs.Variable(f"{branch}heatmap_filtered", dtype=np.dtype(np.float32))
        filter_mask = gs.Constant(name=f"{branch}view_filter_mask", values=masks.filter_mask)

        filter_node = gs.Node(
            op="Mul",
            name=f"{branch}apply_filter_mask",
            inputs=[heatmap_variable, filter_mask],
            outputs=[heat_map_filtered],
        )

        # Add offsets do zeroed values
        heat_map_filtered_with_offsets = gs.Variable(
            f"{branch}heatmap_filtered_with_offsets", dtype=np.dtype(np.float32)
        )
        offset_mask = gs.Constant(name=f"{branch}view_offset_mask", values=masks.offset_mask)

        offset_node = gs.Node(
            op="Add",
            name=f"{branch}apply_offset_mask",
            inputs=[heat_map_filtered, offset_mask],
            outputs=[heat_map_filtered_with_offsets],
        )

        for i, input_ in enumerate(concat_node.inputs):
            if input_.name == heatmap_variable.name:
                heatmap_input_index = i
                break
        else:
            msg = "Could not find heatmap input index."
            raise RuntimeError(msg)

        concat_node.inputs[heatmap_input_index] = heat_map_filtered_with_offsets

        assert isinstance(graph.nodes, list)
        graph.nodes.append(filter_node)
        graph.nodes.append(offset_node)

        return graph

    def add_accuracy_extension_for_center_net(
        self,
        graph: gs.Graph,
        name_prefix: str,
        params: CenterNetParams,
        centernet_node: gs.Node,
    ) -> gs.Graph:
        """Modifies Width and Height Convolution for improved dynamic range."""
        scale_large = params.box_size_scale / params.box_size_scale_large
        scale_small = params.box_size_scale / params.box_size_scale_small

        conv_node = centernet_node.i().i(params.box_size_position).i()
        conv_node.outputs[0].name = f"{name_prefix}wh_conv"
        conv_node.o().outputs[0].name = f"{name_prefix}wh_conv_relu"
        weights_large = np.array(conv_node.inputs[1].values) * scale_large
        weights_small = np.array(conv_node.inputs[1].values) * scale_small
        weights = np.concatenate([weights_large, weights_small], axis=0)
        conv_node.inputs[1].values = weights
        if len(conv_node.inputs) > 2:
            bias_large = np.array(conv_node.inputs[2].values) * scale_large
            bias_small = np.array(conv_node.inputs[2].values) * scale_small
            bias = np.concatenate([bias_large, bias_small], axis=0)
            conv_node.inputs[2].values = bias
        conv_output = conv_node.outputs[0]
        leaky_relu_output = conv_node.o().outputs[0]
        concat_output = conv_node.o().o().outputs[0]

        conv_output.shape = [conv_output.shape[0], 4, conv_output.shape[2], conv_output.shape[3]]
        leaky_relu_output.shape = [
            leaky_relu_output.shape[0],
            4,
            leaky_relu_output.shape[2],
            leaky_relu_output.shape[3],
        ]
        concat_output.shape = [
            concat_output.shape[0],
            concat_output.shape[1] + 2,
            concat_output.shape[2],
            concat_output.shape[3],
        ]

        return graph

    def add_scaling_for_center_net_plugin(
        self, graph: gs.Graph, config: ScalingConfig, centernet_node: gs.Node
    ) -> gs.Graph:
        """Inserts Mul and Div scaling nodes before and after CenterNetPP."""
        factor = gs.Constant(
            name=f"{config.scaling_node_name}_factor",
            values=np.ones([1], dtype=np.float32) * config.scale,
        )
        pre_centernet_node = centernet_node.i().name
        scaling_inserted = [False, False]
        for node in graph.nodes:
            if node.name == pre_centernet_node:
                # Insert it before
                pre_centernet_tensor = node.inputs[config.pre_concat_pos]

                div_out = gs.Variable(f"tensor_{config.scaling_node_name}_div_out", dtype=np.dtype(np.float32))
                pre_scale = gs.Node(
                    op="Div",
                    name=f"node_{config.scaling_node_name}_scale",
                    inputs=[pre_centernet_tensor, factor],
                    outputs=[div_out],
                )
                node.inputs[config.pre_concat_pos] = div_out
                assert isinstance(graph.nodes, list)
                graph.nodes.append(pre_scale)
                scaling_inserted[0] = True

            if config.post_centernet_mul_name in node.name:
                # Adjust the scaling of the Mul node
                current_scale = float(node.inputs[1].values)
                node.inputs[1].values = np.ones([1], dtype=np.float32) * current_scale * config.scale
                scaling_inserted[1] = True
        if not all(scaling_inserted):
            msg = "Feature scaling for the light task wasn't inserted correctly."
            raise RuntimeError(msg)

        return graph


class InjectLSRCenternetPlugin(AbstractGraphRewriter):
    """Inject Centernet plugin for LSR head."""

    HEAD_PREFIX: typing.Final[str] = "light_head_"

    @dataclass
    class ScalingConfig:
        """Config for inserting scaling nodes."""

        # Where to insert the scaling node
        pre_concat_pos: int
        post_centernet_mul_name: str

        scale: float = 1.0
        scaling_node_name: str = "PreCenternetName"

    def __init__(
        self,
        start_replacement_tensor_name_pattern: str,
    ) -> None:
        """Init the LSR centernet plugin rewriter.

        Args:
            start_replacement_tensor_name_pattern: Name of the tensor where the replacement starts
        """

        self.start_replacement_tensor_name_pattern = start_replacement_tensor_name_pattern

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:  # noqa: PLR0915, C901, PLR0912
        """Inject the Centernet plugin for the LSR head."""

        source_graph = graph.copy()

        cutoff_nodes = sorted(
            [
                n
                for n in graph.nodes
                if re.match(self.start_replacement_tensor_name_pattern, n.name) and len(n.inputs) == 4
            ],
            key=lambda x: x.name,
        )

        if not cutoff_nodes:
            return graph

        @dataclass
        class LightHeadConfig:
            """Config for the light head."""

            cutoff_node: gs.Node
            parameters: CenterNetParams
            branch_prefix: str

        light_head_configs = []
        for cutoff_node in cutoff_nodes:

            def _follow_inputs(n: gs.Node) -> typing.Generator[gs.Node, None, None]:
                """Follow inputs of a node."""
                try:
                    while n := n.i():
                        yield n
                except IndexError:
                    return

            for n in _follow_inputs(cutoff_node):
                if "_far/" in n.name:
                    params = CenterNetFarviewParams()
                    branch_prefix = "BranchFarview/"
                    break
                if "_mid/" in n.name:
                    params = CenterNetMidviewParams()
                    branch_prefix = "BranchMidview/"
                    break
                if "_wide/" in n.name:
                    params = CenterNetWideviewParams()
                    branch_prefix = "BranchWideview/"
                    break
            else:
                params = CenterNetParams()
                branch_prefix = ""
            light_head_configs.append(LightHeadConfig(cutoff_node, params, branch_prefix))

        multiple_branches = len(light_head_configs) > 1
        replacement_super_model = None

        for config in light_head_configs:
            replacement_sub_graph, replacement_root_tensor = create_center_net(
                config.parameters,
                config.cutoff_node.name,
                opset=graph.opset,
                name_prefix=f"{config.branch_prefix}{self.HEAD_PREFIX}",
            )
            typing.cast(gs.Variable, replacement_sub_graph.inputs[0]).dtype = np.dtype(np.float32)
            replacement_sub_graph.cleanup().toposort()

            if not replacement_super_model:
                replacement_super_model = gs.export_onnx(replacement_sub_graph)
            else:
                replacement_super_model = onnx.compose.merge_models(
                    replacement_super_model, gs.export_onnx(replacement_sub_graph), io_map=[]
                )
        assert replacement_super_model
        replacement_super_graph = gs.import_onnx(replacement_super_model)

        assert isinstance(replacement_super_graph, gs.Graph)
        if multiple_branches:
            outputs = sorted(
                replacement_super_graph.outputs, key=lambda x: typing.cast(gs.Variable, x).name.startswith("Branch")
            )
            light_index = next(
                x for x, val in enumerate(outputs) if typing.cast(gs.Variable, val).name.startswith("Branch")
            )

            light_outputs = outputs[light_index:]
            light_outputs = sorted(light_outputs, key=lambda x: typing.cast(gs.Variable, x).name.split("/")[1])
            new_light_outputs = []
            for name, output_iter in groupby(light_outputs, lambda x: typing.cast(gs.Variable, x).name.split("/")[1]):
                output_group = list(output_iter)

                trifocal_output_name = name.removeprefix(self.HEAD_PREFIX)
                trifocal_output_name = trifocal_output_name.replace("light", "light_trifocal")
                concat_out = gs.Variable(f"{trifocal_output_name}", dtype=np.dtype(np.float32))

                concat_node = gs.Node(
                    name=f"{trifocal_output_name}_concat",
                    op="Concat",
                    inputs=output_group,
                    outputs=[concat_out],
                    attrs={"axis": 1},
                )
                assert isinstance(replacement_super_graph.nodes, list)
                replacement_super_graph.nodes.append(concat_node)

                assert isinstance(output_group[0].shape, list)
                concat_shape = output_group[0].shape.copy()
                concat_shape[1] = len(cutoff_nodes) * concat_shape[1]
                concat_out.shape = concat_shape

                new_light_outputs.append(concat_out)

            outputs[light_index:] = new_light_outputs
            replacement_super_graph.outputs = outputs
        else:
            for output in replacement_super_graph.outputs:
                output.name = typing.cast(gs.Variable, output).name.removeprefix(self.HEAD_PREFIX)

        replacement_super_model = gs.export_onnx(replacement_super_graph)

        with tempfile.TemporaryDirectory() as temp_dir:
            onnx.save(replacement_super_model, pathlib.Path(temp_dir) / "replacement_model.onnx")

            template_file_name = (
                "lsr_template_subgraph_mono.onnx" if not multiple_branches else "lsr_template_subgraph_trifocal.onnx"
            )

            updated_graph = load_and_replace(
                source_model=source_graph,
                template_model_path=pathlib.Path(__file__).parent / "replacement_subgraphs" / template_file_name,
                replacement_model_path=pathlib.Path(temp_dir) / "replacement_model.onnx",
            )
            assert updated_graph, (
                "Failed to replace the light postprocessing subgraph for qnn conversion. This might be due "
                "to changes in the source model."
            )

        for _i, config in enumerate(light_head_configs):
            score_scaling = self.ScalingConfig(
                pre_concat_pos=3,
                post_centernet_mul_name=f"{config.branch_prefix}{self.HEAD_PREFIX}feature_scale_Mul",
                scale=2,
                scaling_node_name=f"{config.branch_prefix}{self.HEAD_PREFIX}PreScoreScaling",
            )
            centernet_node = self.get_centernet_node(updated_graph, f"{config.branch_prefix}{self.HEAD_PREFIX}")
            updated_graph = self.add_scaling_for_center_net_plugin(
                updated_graph,
                score_scaling,
                centernet_node,
            )

            updated_graph = self.add_accuracy_extension_for_center_net(
                updated_graph, f"{config.branch_prefix}{self.HEAD_PREFIX}", config.parameters, centernet_node
            )

            if isinstance(config.parameters, MaskedCenternetMixin):
                updated_graph = self.add_masking_for_center_net_plugin(
                    updated_graph,
                    centernet_node,
                    config.parameters.view_mask,
                    config.branch_prefix,
                )
                with_masking = True
            else:
                with_masking = False

            output_prefix = "light_trifocal" if multiple_branches else "light"
            self.update_quant_overrides(
                quant_overrides,
                centernet_node,
                config.cutoff_node,
                config.branch_prefix,
                output_prefix=output_prefix,
                with_masking=with_masking,
            )

        updated_graph.cleanup().toposort()

        return updated_graph

    def update_quant_overrides(
        self,
        quant_overrides: QuantizationEncodings,
        centernet_node: gs.Node,
        cutoff_node: gs.Node,
        branch: str,
        *,
        output_prefix: str,
        with_masking: bool = False,
    ) -> None:
        """Force input and output tensors of Centernet plugin encodings to be the same.

        Args:
            quant_overrides: Quantization override dict to be updated.
            centernet_node: centernet node.
            cutoff_node: cutoff node where the CenternetPP Plugin is inserted
            branch: branch_name for trifocal head
            with_masking: Flag to indicate if masking is used
            output_prefix: Prefix for the output tensor names
        """

        # Quantization params must be the same in the input and output tensor of the Centernet Plugin.
        # Scale and offset taken from Azure conversion run with full calibration dataset.
        quant_overrides.add_activation_encoding(centernet_node.outputs[0].name, Encoding(bitwidth=8, min=0.0, max=3.0))

        quant_overrides.add_activation_encoding(
            f"{branch}light_head_centernet_coords_int",
            Encoding(bitwidth=8, min=0.0, max=3.0),
        )

        for node in ["wh_conv", "wh_conv_relu"]:
            quant_overrides.add_activation_encoding(
                f"{branch}light_head_{node}", Encoding(bitwidth=8, min=0.0, max=3.0)
            )

        # Insert quantization overrides to last Conv layer of the Light logits
        # Reason is that detections smaller than -2 are filtered out after sigmoid activation

        if with_masking:
            mask_offset_add_node = centernet_node.i().i()
            if mask_offset_add_node.op == "Add":
                mask_offset_add_node_name = mask_offset_add_node.outputs[0].name
                quant_overrides.add_activation_encoding(
                    mask_offset_add_node_name, Encoding(bitwidth=8, min=-2.0, max=4.0)
                )

            filter_offset_mul_node = mask_offset_add_node.i()
            if filter_offset_mul_node.op == "Mul":
                filter_offset_mul_node_name = filter_offset_mul_node.outputs[0].name
                quant_overrides.add_activation_encoding(
                    filter_offset_mul_node_name, Encoding(bitwidth=8, min=-2.0, max=4.0)
                )
            conv_node = filter_offset_mul_node.i()
        else:
            conv_node = centernet_node.i().i()

        if conv_node.op == "Conv":
            conv_output_name = conv_node.outputs[0].name
            quant_overrides.add_activation_encoding(conv_output_name, Encoding(bitwidth=8, min=-2.0, max=4.0))
        else:
            msg = "Could not find last Conv layer."
            raise RuntimeError(msg)

        # Override classes to be 0-255 integers
        classes_layes_to_override = [
            f"{branch}light_head_cast",
            f"{branch}light_head_cast___3",
            f"{output_prefix}_classes",
        ]
        for layer_name in classes_layes_to_override:
            quant_overrides.add_activation_encoding(layer_name, Encoding(bitwidth=8, scale=1.0, offset=0))

        # Box coordinates needs to be overridden to 16 bit due it predicting pixel coordinates
        coords_layers_to_override = [
            f"{branch}light_head_cast___1",  # Cast from int32 to uint16
            f"{branch}light_head_wh",  # Width
            f"{branch}light_head_wh_small",  # Width
            f"{branch}light_head_wh_large",  # Width
            f"{branch}light_head_relu_size",  # Width
            f"{branch}light_head_size2",  # Width
            f"{branch}light_head_xy2",  # Add offset
            f"{branch}light_head_box1",  # Sub box dim
            f"{branch}light_head_box2",  # Add box dim
            f"{branch}light_head_box",  # Concat
            f"{branch}light_head_squeeze___3",  # Squeeze
            f"{branch}light_head_transpose___1",
            f"{output_prefix}_boxes",
            f"{output_prefix}_group_boxes",
        ]
        for layer_name in coords_layers_to_override:
            quant_overrides.add_activation_encoding(layer_name, Encoding(bitwidth=16))

        params_to_override = [
            f"{branch}light_head_wh_threshold",  # Width
        ]
        for layer_name in params_to_override:
            quant_overrides.add_param_encoding(layer_name, Encoding(bitwidth=16))

    def get_centernet_node(self, graph: gs.Graph, name_filter: str) -> gs.Node:
        """Find CenterNetPP node in the graph.

        Args:
            graph: Graph to search for the node
            name_filter: get first centernet node that matches the filter in case of multiple centernet nodes
        Returns:
            CenterNetNode
        """
        for node in graph.nodes:
            if node.op == "CenterNetPP" and name_filter in node.name:
                return node
        msg = "Centernet node not found in the graph."
        raise RuntimeError(msg)

    def add_masking_for_center_net_plugin(
        self, graph: gs.Graph, centernet_node: gs.Node, masks: ViewMasks, branch: str
    ) -> gs.Graph:
        """Inserts masking nodes before and after CenterNetPP."""
        concat_node = centernet_node.i()
        heatmap_node = centernet_node.i().i()
        heatmap_variable = heatmap_node.outputs[0]

        # Multiply masked values with 0
        heat_map_filtered = gs.Variable(f"{branch}heatmap_filtered", dtype=np.dtype(np.float32))
        filter_mask = gs.Constant(name=f"{branch}view_filter_mask", values=masks.filter_mask)

        filter_node = gs.Node(
            op="Mul",
            name=f"{branch}apply_filter_mask",
            inputs=[heatmap_variable, filter_mask],
            outputs=[heat_map_filtered],
        )

        # Add offsets do zeroed values
        heat_map_filtered_with_offsets = gs.Variable(
            f"{branch}heatmap_filtered_with_offsets", dtype=np.dtype(np.float32)
        )
        offset_mask = gs.Constant(name=f"{branch}view_offset_mask", values=masks.offset_mask)

        offset_node = gs.Node(
            op="Add",
            name=f"{branch}apply_offset_mask",
            inputs=[heat_map_filtered, offset_mask],
            outputs=[heat_map_filtered_with_offsets],
        )

        for i, input_ in enumerate(concat_node.inputs):
            if input_.name == heatmap_variable.name:
                heatmap_input_index = i
                break
        else:
            msg = "Could not find heatmap input index."
            raise RuntimeError(msg)

        concat_node.inputs[heatmap_input_index] = heat_map_filtered_with_offsets

        assert isinstance(graph.nodes, list)
        graph.nodes.append(filter_node)
        graph.nodes.append(offset_node)

        return graph

    def add_accuracy_extension_for_center_net(
        self,
        graph: gs.Graph,
        name_prefix: str,
        params: CenterNetParams,
        centernet_node: gs.Node,
    ) -> gs.Graph:
        """Modifies Width and Height Convolution for improved dynamic range."""
        scale_large = params.box_size_scale / params.box_size_scale_large
        scale_small = params.box_size_scale / params.box_size_scale_small

        conv_node = centernet_node.i().i(params.box_size_position).i()
        conv_node.outputs[0].name = f"{name_prefix}wh_conv"
        conv_node.o().outputs[0].name = f"{name_prefix}wh_conv_relu"
        weights_large = np.array(conv_node.inputs[1].values) * scale_large
        weights_small = np.array(conv_node.inputs[1].values) * scale_small
        weights = np.concatenate([weights_large, weights_small], axis=0)
        conv_node.inputs[1].values = weights
        if len(conv_node.inputs) > 2:
            bias_large = np.array(conv_node.inputs[2].values) * scale_large
            bias_small = np.array(conv_node.inputs[2].values) * scale_small
            bias = np.concatenate([bias_large, bias_small], axis=0)
            conv_node.inputs[2].values = bias
        return graph

    def add_scaling_for_center_net_plugin(
        self, graph: gs.Graph, config: ScalingConfig, centernet_node: gs.Node
    ) -> gs.Graph:
        """Inserts Mul and Div scaling nodes before and after CenterNetPP."""
        factor = gs.Constant(
            name=f"{config.scaling_node_name}_factor",
            values=np.ones([1], dtype=np.float32) * config.scale,
        )
        pre_centernet_node = centernet_node.i().name
        scaling_inserted = [False, False]
        for node in graph.nodes:
            if node.name == pre_centernet_node:
                # Insert it before
                pre_centernet_tensor = node.inputs[config.pre_concat_pos]

                div_out = gs.Variable(f"tensor_{config.scaling_node_name}_div_out", dtype=np.dtype(np.float32))
                pre_scale = gs.Node(
                    op="Div",
                    name=f"node_{config.scaling_node_name}_scale",
                    inputs=[pre_centernet_tensor, factor],
                    outputs=[div_out],
                )
                node.inputs[config.pre_concat_pos] = div_out
                assert isinstance(graph.nodes, list)
                graph.nodes.append(pre_scale)
                scaling_inserted[0] = True

            if config.post_centernet_mul_name in node.name:
                # Adjust the scaling of the Mul node
                current_scale = float(node.inputs[1].values)
                node.inputs[1].values = np.ones([1], dtype=np.float32) * current_scale * config.scale
                scaling_inserted[1] = True
        if not all(scaling_inserted):
            msg = "Feature scaling for the light task wasn't inserted correctly."
            raise RuntimeError(msg)

        return graph


class InjectLaneHeadCustomOps(OnnxSubgraphFinder, AbstractGraphRewriter):
    """Inject custom ops for lane head."""

    def __init__(self) -> None:
        """Init the Lane custom ops rewriter."""
        self.subgraph_template_path = (
            pathlib.Path(__file__).parent / "replacement_subgraphs" / "lane_template_subgraph.onnx"
        )
        if not self.subgraph_template_path.exists():
            msg = f"{self.subgraph_template_path} not found."
            raise FileNotFoundError(msg)
        self.prefix = "lane_keypoint_head"

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Inject custom ops for lane head."""
        subgraph_replacement_path = self.get_replacement_subgraph_from_model_res(
            graph, "replacement_subgraphs", "lane_replacement"
        )
        if subgraph_replacement_path is not None:
            graph_with_lane_custom_ops = load_and_replace(
                source_model=graph,
                template_model_path=self.subgraph_template_path,
                replacement_model_path=subgraph_replacement_path,
                name_prefix=self.prefix,
            )
            if graph_with_lane_custom_ops is not None:
                self.update_quant_overrides(quant_overrides)
                LOGGER.info(f"{self.__class__.__name__} replacement was applied.")
                return graph_with_lane_custom_ops
        return graph

    def update_quant_overrides(self, quant_overrides: QuantizationEncodings) -> None:
        """Force input and output tensors of Centernet plugin encodings to be the same.

        Args:
            quant_overrides: Quantization override dict to be updated.
        """

        # *** Force same quant Override for MaxPoolNMS, class logits ***#
        quant_overrides.add_activation_encoding(
            "StatefulPartitionedCall/MultitaskModel/lane_keypoint_head/ClassificationHead/OutputConv/BiasAdd",
            Encoding(
                bitwidth=8,
                min=-67.0,
                max=10.0,
            ),
        )
        quant_overrides.add_activation_encoding(
            "StatefulPartitionedCall/MultitaskModel/lane_keypoint_head/strided_slice",
            Encoding(
                bitwidth=8,
                min=-67.0,
                max=10.0,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{self.prefix}_maxpoolnms_out", Encoding(bitwidth=8, min=-67.0, max=10.0)
        )
        quant_overrides.add_activation_encoding(
            f"{self.prefix}_gather_logit", Encoding(bitwidth=8, min=-67.0, max=10.0)
        )

        # Rewrite 2nd gather quantization
        quant_overrides.add_activation_encoding(
            "lane_keypoint_startpoint_offsets", Encoding(bitwidth=8, min=-900, max=900)
        )

        # *** Force same quant override for Gather2D for Startpoint offset ***
        quant_overrides.add_activation_encoding(
            "StatefulPartitionedCall/MultitaskModel/lane_keypoint_head/StartpointOffsetHead/OutputConv/BiasAdd",
            Encoding(
                bitwidth=8,
                min=-0.5,
                max=1.5,
            ),
        )
        quant_overrides.add_activation_encoding(f"{self.prefix}_gather_offset", Encoding(bitwidth=8, min=-0.5, max=1.5))


class InjectLaneExpHeadCustomOps(OnnxSubgraphFinder, AbstractGraphRewriter):
    """Inject custom ops for lane EXP head."""

    def __init__(self) -> None:
        """Init the Lane custom ops rewriter."""
        self.subgraph_template_path = (
            pathlib.Path(__file__).parent / "replacement_subgraphs" / "fslane_template_subgraph.onnx"
        )
        if not self.subgraph_template_path.exists():
            msg = f"{self.subgraph_template_path} not found."
            raise FileNotFoundError(msg)
        self.prefix = "lane_keypoint_head"

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Inject custom ops for lane head."""
        subgraph_replacement_path = self.get_replacement_subgraph_from_model_res(
            graph, "replacement_subgraphs", "fslane_replacement"
        )
        if subgraph_replacement_path is not None:
            graph_with_lane_custom_ops = load_and_replace(
                source_model=graph,
                template_model_path=self.subgraph_template_path,
                replacement_model_path=subgraph_replacement_path,
                name_prefix=self.prefix,
            )
            if graph_with_lane_custom_ops is not None:
                self.update_quant_overrides(quant_overrides)
                LOGGER.info(f"{self.__class__.__name__} replacement was applied.")
                return graph_with_lane_custom_ops
        return graph

    def update_quant_overrides(self, quant_overrides: QuantizationEncodings) -> None:
        """Force input and output tensors of Lane keypoint EXP head.

        Args:
            quant_overrides: Quantization override dict to be updated.
        """

        # *** Force same quant Override for MaxPoolNMS, class logits ***#
        for layer in [
            "StatefulPartitionedCall/MultitaskModel/lane_keypoint_head/ClassificationHead/OutputConv/BiasAdd",
            "StatefulPartitionedCall/MultitaskModel/lane_keypoint_head/strided_slice",
            f"{self.prefix}_maxpoolnms_out",
            f"{self.prefix}_gather_logit",
        ]:
            quant_overrides.add_activation_encoding(layer, Encoding(bitwidth=8, min=-67.0, max=10.0))

        # *** Force same quant override for Gather2D for Startpoint offset ***
        for layer in [
            "StatefulPartitionedCall/MultitaskModel/lane_keypoint_head/InstanceEmbeddingHead/OutputConv/BiasAdd",
            "lane_keypoint_instance_embeddings",
        ]:
            quant_overrides.add_activation_encoding(layer, Encoding(bitwidth=8, min=-0.5, max=1.5))


class InjectMultiStixelHeadCustomOps(OnnxSubgraphFinder, AbstractGraphRewriter):
    """Inject custom ops for MultiStixel head."""

    def __init__(self) -> None:
        """Init the MultiStixel custom ops rewriter."""
        self.subgraph_template_path = (
            pathlib.Path(__file__).parent / "replacement_subgraphs" / "multistixel_template_subgraph.onnx"
        )
        if not self.subgraph_template_path.exists():
            msg = f"{self.subgraph_template_path} not found."
            raise FileNotFoundError(msg)
        self.prefix = "multi_stixel"
        self.onnx_replacements_folder_path = "replacement_subgraphs"
        self.onnx_replacements_name = "multistixel_replacement"

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Inject custom ops for Multistixel head."""
        subgraph_replacement_path = self.get_replacement_subgraph_from_model_res(
            graph, self.onnx_replacements_folder_path, self.onnx_replacements_name
        )
        if subgraph_replacement_path is not None:
            graph_with_multistixel_custom_ops = load_and_replace(
                source_model=graph,
                template_model_path=self.subgraph_template_path,
                replacement_model_path=subgraph_replacement_path,
                name_prefix=self.prefix,
            )
            if graph_with_multistixel_custom_ops is not None:
                self.update_quant_overrides(quant_overrides)
                LOGGER.info(f"{self.__class__.__name__} replacement was applied.")
                return graph_with_multistixel_custom_ops

        return graph

    def update_quant_overrides(self, quant_overrides: QuantizationEncodings) -> None:
        """Force input and output tensors of MultiStixel head.

        Args:
            quant_overrides: Quantization override dict to be updated.
        """
        return


class AddTypeInformation(AbstractGraphRewriter):
    """Add type information to the onnx graph node names."""

    def __init__(self) -> None:
        """Init type information rewriter."""
        self.Ops: list[str] = []

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Add type information to the onnx graph node names."""

        LOGGER.info("Adding type information to onnx graph")

        for node in graph.nodes:
            name = node.name
            if name == "" and len(node.inputs) > 1:
                name = node.inputs[0].name
            node.name = f"{name}_OpType_{node.op}"
            if f"OpType_{node.op}" not in self.Ops:
                self.Ops.append(f"OpType_{node.op}")

        return graph


class InjectDepthThresholding(AbstractGraphRewriter):
    """Inject thresholding custom op for the depth head."""

    def __init__(
        self, uncertainty_lower_bound: float, uncertainty_upper_bound: float, onnx_opset_version: int = 11
    ) -> None:
        """Init the thresholding rewriter.

        Args:
            uncertainty_lower_bound: lower bound for thresholding uncertainty
            uncertainty_upper_bound: upper bound for thresholding uncertainty
            onnx_opset_version: ONNX opset version
        """
        self.uncertainty_lower_bound = uncertainty_lower_bound
        self.uncertainty_upper_bound = uncertainty_upper_bound

        self._onnx_opset_version = onnx_opset_version

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Inject thresholding custom op for the depth head."""
        less_nodes = [node for node in graph.nodes if node.op == "Less" and "depth" in node.name]
        if len(less_nodes) != 3:
            return graph
        thrs_input = less_nodes[0].inputs[0]
        depth_outputs = {}
        for output in graph.outputs:
            output = cast(gs.Variable, output)
            if output.name.startswith("depth"):
                depth_outputs[output.name.split("_")[-1]] = output

        thrs_output = gs.Variable(
            "depth_and_uncertainty", dtype=np.float32, shape=[1, 3] + depth_outputs["depth"].shape[2:]
        )

        depth_threshold_constant = gs.Constant("depth_threshold_const", values=np.array([0.0], dtype=np.float32))
        depth_threshold_out = gs.Variable("depth_threshold", dtype=np.float32)
        depth_threshold = gs.Node(
            op="Identity", name="depth_threshold", inputs=[depth_threshold_constant], outputs=[depth_threshold_out]
        )
        append_node(graph, depth_threshold)

        uncertainty_lower_bound_constant = gs.Constant(
            "depth_uncertainty_lower_bound_const",
            values=np.array([self.uncertainty_lower_bound], dtype=np.float32),
        )
        uncertainty_lower_bound_out = gs.Variable("depth_uncertainty_lower_bound", dtype=np.float32)
        uncertainty_lower_bound = gs.Node(
            op="Identity",
            name="depth_uncertainty_lower_bound",
            inputs=[uncertainty_lower_bound_constant],
            outputs=[uncertainty_lower_bound_out],
        )
        append_node(graph, uncertainty_lower_bound)

        uncertainty_upper_bound_constant = gs.Constant(
            "depth_uncertainty_upper_bound_const",
            values=np.array([self.uncertainty_upper_bound], dtype=np.float32),
        )
        uncertainty_upper_bound_out = gs.Variable("depth_uncertainty_upper_bound", dtype=np.float32)
        uncertainty_upper_bound = gs.Node(
            op="Identity",
            name="depth_uncertainty_upper_bound",
            inputs=[uncertainty_upper_bound_constant],
            outputs=[uncertainty_upper_bound_out],
        )
        append_node(graph, uncertainty_upper_bound)

        thrs_input.outputs.clear()
        thrs_node = gs.Node(
            op="DepthFromMonoThresholding",
            name="depth_head_DepthFromMonoThresholding",
            inputs=[thrs_input, depth_threshold_out, uncertainty_lower_bound_out, uncertainty_upper_bound_out],
            outputs=[thrs_output],
            domain=ATHENA_DOMAIN,
        )
        append_node(graph, thrs_node)

        for output in depth_outputs.values():
            cast(gs.Variable, output).inputs.clear()

        if self._onnx_opset_version >= 13:
            split = gs.Constant("depth_split", values=np.array([1, 2], dtype=np.int64))
            split_node = gs.Node(
                op="Split",
                name="depth_head_output_split",
                attrs={"axis": 1},
                inputs=[thrs_output, split],
                outputs=[depth_outputs["depth"], depth_outputs["uncertainty"]],
            )
        else:
            split_node = gs.Node(
                op="Split",
                name="depth_head_output_split",
                attrs={"axis": 1, "split": [1, 2]},
                inputs=[thrs_output],
                outputs=[depth_outputs["depth"], depth_outputs["uncertainty"]],
            )
        append_node(graph, split_node)

        quant_overrides.add_activation_encoding("depth_and_uncertainty", Encoding(bitwidth=8, scale=1.0, offset=0))

        LOGGER.info(f"{self.__class__.__name__} applied to depth head")
        graph.cleanup().toposort()
        return graph


class InsertTopK2DSum(AbstractGraphRewriter):
    """Add TopK2DSum custom op /ReduceSum with Reshape/TopK2DSum layers using the TopK2DSum custom op."""

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Replace TopK layers with TopK2DSum custom op.

        Delete transposes before this op as they are not needed for the custom op.

        Args:
            graph: Graph to be manipulated
            quant_overrides: Convenience functionality to modify quantization encodings

        Returns:
            Rewritten graph
        """
        onnx_graph = gs.export_onnx(graph)
        onnx_graph = osi.infer_shapes(onnx_graph, check_type=True, strict_mode=True, data_prop=True)
        graph = gs.import_onnx(onnx_graph)

        current = find_first_node(graph)
        if current is None:
            msg = "no first node found"
            raise ValueError(msg)
        reg = re.compile(contract.TOPK_2D_SUM_BASE_NAME)
        paths = find_start_and_end(current, reg, [])
        for _, (start, end) in enumerate(paths):
            assert isinstance(start, gs.Node)
            assert isinstance(end, gs.Node)
            LOGGER.info("Inserting TopK2DSum between %s and %s", start.name, end.name)
            prefix = reg.split(end.name)[1].replace("/", "")
            unsqueeze_output = gs.Variable(
                name=prefix + "/unsqueeze_output",
                dtype=start.inputs[0].dtype,
            )
            unsqueeze = gs.Node(
                op="Unsqueeze",
                name=prefix + "/unsqueeze",
                inputs=[start.inputs[0]],
                outputs=[unsqueeze_output],
                attrs={"axes": [3]},
            )
            append_node(graph, unsqueeze)
            topk = gs.Node(
                op="TopK2DSum",
                name=prefix + "/TopK2DSum",
                inputs=[unsqueeze_output],
                outputs=[end.outputs[0]],
                domain=ATHENA_DOMAIN,
            )
            append_node(graph, topk)

            end.outputs.clear()

        graph.cleanup().toposort()

        return graph


class ReattachSumHardEntropy(AbstractGraphRewriter):
    """Reattach sum hard entropy trigger to specified task output.

    Ensures that the active learning sum hard entropy task is correctly attached after
    the conversion step.
    """

    def __init__(self, pattern: str, output_name: str) -> None:
        """Initialize search patterns.

        Args:
            pattern: Patterns to match the input names of the trigger.
            output_name: Name of the head output to reattach the trigger to.
        """
        super().__init__()
        self.pattern = pattern
        self.output_name = output_name

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Reattach subgraph to the score output.

        Args:
            graph: Graph to be manipulated
            quant_overrides: Convenience functionality to modify quantization encodings

        Returns:
            Rewritten graph
        """
        _ = (quant_overrides,)
        onnx_graph = gs.export_onnx(graph)
        graph = gs.import_onnx(onnx_graph)

        output_node = find_node_with_output_name(graph, self.output_name)
        al_input_add_node = find_node_with_output_name(graph, rf"{self.pattern}/Add")
        al_input_mul_node = find_node_with_output_name(graph, rf"{self.pattern}/Mul")

        if al_input_mul_node is None or al_input_add_node is None or output_node is None:
            LOGGER.info("No active learning sum hard entropy nodes found, skipping reattachment.")
            return graph

        prefix = al_input_add_node.name.rsplit("/", maxsplit=1)[0]
        softmax_output = gs.Variable(
            name=prefix + "/softmax_output",
            dtype=output_node.outputs[0].dtype,
        )
        softmax = gs.Node(
            op="Softmax",
            name=prefix + "/softmax",
            inputs=[output_node.outputs[0]],
            outputs=[softmax_output],
        )
        append_node(graph, softmax)

        al_input_add_node.inputs[0] = softmax.outputs[0]
        al_input_mul_node.inputs[0] = softmax.outputs[0]

        graph.cleanup().toposort()

        return graph


class OptimizeSumHardEntropy(AbstractGraphRewriter):
    """Replace ReduceSum and TopK ops with Conv (1x1) and TopK2DSum custom op."""

    def __init__(self, pattern: str) -> None:
        """Initialize pattern.

        Args:
            pattern: List of patterns to match the output names.
        """
        self.pattern = pattern

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Replace ReduceSum ops and TopK with Conv (1x1) and TopK2DSum custom op.

        i.e. ReduceSum -> TopK -> ReduceSum
        is replaced with
        Reshape -> Transpose -> Conv (1x1) -> Transpose -> TopK2DSum

        Args:
            graph: Graph to be manipulated
            quant_overrides: Convenience functionality to modify quantization encodings
        Returns:
            Rewritten graph
        """

        _ = (quant_overrides,)
        onnx_graph = gs.export_onnx(graph)
        graph = gs.import_onnx(onnx_graph)

        pattern = re.compile(self.pattern)

        for node in graph.nodes:
            if node.op != "Mul" or not pattern.match(node.name):
                continue

            mul = node
            assert isinstance(mul, gs.Node)

            namespace = mul.name.split("/")[-2]
            next_node = node.o()
            if next_node.op != "ReduceSum" or next_node.o().op != "TopK" or next_node.o().o().op != "ReduceSum":
                LOGGER.warning(f"Pattern Mul -> ReduceSum -> TopK -> ReduceSum not found in namespace {namespace}.")
                continue

            prefix = mul.name.rsplit("/", maxsplit=1)[0]
            output_node = find_node_with_output_name(graph, rf"{namespace}.*")
            if output_node is None:
                msg = f"Output node with name {namespace} not found."
                raise ValueError(msg)

            LOGGER.info(
                "Inserting Reshape -> Transpose -> Conv (1x1) -> Transpose -> TopK2DSum after %s with output %s",
                mul.name,
                output_node.name,
            )

            dtype = mul.outputs[0].dtype

            # Reshape (for crouton memory efficiency)
            shape_list, _ = reshape_factorize_largest_dimension(mul.outputs[0].shape)
            shape = gs.Constant(prefix + "/reshape_shape", np.array(shape_list, dtype=np.dtype(np.int64)))
            reshape_output = gs.Variable(name=prefix + "/reshape_output", shape=shape.values.tolist(), dtype=dtype)
            reshape = gs.Node(
                op="Reshape",
                name=prefix + "/reshape",
                inputs=[mul.outputs[0], shape],
                outputs=[reshape_output],
            )
            append_node(graph, reshape)

            # Transpose to NCHW format
            transpose_to_nchw_output = gs.Variable(name=prefix + "/transpose_to_nchw_output", dtype=dtype)
            transpose_to_nchw = gs.Node(
                op="Transpose",
                name=prefix + "/transpose_NHWC_to_NCHW",
                inputs=[reshape_output],
                outputs=[transpose_to_nchw_output],
                attrs={"perm": [0, 3, 1, 2]},
            )
            append_node(graph, transpose_to_nchw)

            # Conv2D
            # Create weight initializer: shape (out_channels, in_channels, kernel_height, kernel_width)
            weight_values = np.ones((1, shape_list[-1], 1, 1), dtype=dtype)
            weight_tensor = gs.Constant(name=prefix + "/conv1x1_weight", values=weight_values)
            conv_output = gs.Variable(name=prefix + "/conv1x1_output", dtype=dtype)
            conv = gs.Node(
                op="Conv",
                name=prefix + "/conv1x1",
                inputs=[transpose_to_nchw_output, weight_tensor],
                outputs=[conv_output],
                attrs={"kernel_shape": [1, 1]},
            )
            append_node(graph, conv)

            # Transpose back to NHWC format
            transpose_to_nhwc_output = gs.Variable(name=prefix + "/transpose_NCHW_to_NHWC_output", dtype=dtype)
            transpose_to_nhwc = gs.Node(
                op="Transpose",
                name=prefix + "/transpose_NCHW_to_NHWC",
                inputs=[conv_output],
                outputs=[transpose_to_nhwc_output],
                attrs={"perm": [0, 2, 3, 1]},
            )
            append_node(graph, transpose_to_nhwc)

            # TopK2DSum
            topk = gs.Node(
                op="TopK2DSum",
                name=prefix,
                inputs=[transpose_to_nhwc_output],
                outputs=[output_node.outputs[0]],
                domain=ATHENA_DOMAIN,
            )
            append_node(graph, topk)
            output_node.outputs.clear()

        graph.cleanup().toposort()

        return graph


class AddRgb2Yuv420Converter(AbstractGraphRewriter):
    """Adds set of nodes for RGB2YUV conversion.

    Intended for model present in only Onnx form and taking Y & UV as inputs and
    for which, RGB input is to be used. For that, RGB2YUV converter is added with
    this surgery.
    """

    def __init__(
        self,
        input_y_name: str,
        input_uv_name: str,
        rgb2yuv_conv_param: tuple[npt.NDArray[Any], npt.NDArray[Any]],
    ) -> None:
        """Init the AddRgb2Yuv420Converter rewriter.

        Args:
            input_y_name: Model Y input name.
            input_uv_name: Model UV input name.
            rgb2yuv_conv_param: Calculated weights and biases for the RGB2YUV conversion
        """
        self.input_y_name = input_y_name
        self.input_uv_name = input_uv_name
        self.rgb2yuv_conv_param = rgb2yuv_conv_param

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Applies the set of new nodes for RGB2YUV conversion and replacing the model inputs.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings

        Returns:
            Rewritten graph
        """

        input_y = None
        input_uv = None
        for g_inp in graph.inputs:
            name = get_name_of_tensor(g_inp)
            if name == self.input_y_name:
                input_y = g_inp
            if name == self.input_uv_name:
                input_uv = g_inp

        assert input_y is not None
        assert input_uv is not None

        assert input_y.shape is not None
        assert len(input_y.shape) == 4

        yuv_merged_input_name = cast(gs.Variable, input_y).name.replace("_y", "_rgb")
        yuv_merged_input_shape = shape_to_list(input_y.shape)
        yuv_merged_input_shape[1] = 3
        yuv_merged_input = gs.Variable(
            name=yuv_merged_input_name,
            dtype=np.float32,
            shape=yuv_merged_input_shape,
        )

        # Add conv layer for RGB2YUV conversion
        yuv_conv_out = gs.Variable(
            name=yuv_merged_input_name + "_conv",
            dtype=np.float32,
        )
        yuv_weight, yuv_bias = self.rgb2yuv_conv_param
        yuv_weight = np.expand_dims(yuv_weight, axis=-1)
        yuv_weight = np.expand_dims(yuv_weight, axis=-1)
        conv_weights = gs.Constant(yuv_merged_input_name + "_w", values=yuv_weight)
        conv_bias = gs.Constant(yuv_merged_input_name + "_b", values=yuv_bias)
        yuv_conv_node = gs.Node(
            op="Conv",
            name=yuv_merged_input_name,
            attrs={"dilations": (1, 1), "group": 1, "kernel_shape": (1, 1), "pads": (0, 0, 0, 0), "strides": (1, 1)},
            inputs=[yuv_merged_input, conv_weights, conv_bias],
            outputs=[yuv_conv_out],
        )
        append_node(graph, yuv_conv_node)

        input_y_name = get_name_of_tensor(input_y)
        input_uv_name = get_name_of_tensor(input_uv)

        # Add slice for Y
        max_int = np.iinfo(np.int64).max
        starts_y = gs.Constant(f"starts_y_{input_y_name}", np.array([0, 0], dtype=np.int64))
        ends_y = gs.Constant(f"ends_y_{input_y_name}", np.array([max_int, 1], dtype=np.int64))
        axes_y = gs.Constant(f"axes_y_{input_y_name}", np.array([0, 1], dtype=np.int64))
        yslice_node = gs.Node(
            op="Slice",
            name=f"SliceY_{input_y_name}",
            inputs=[yuv_conv_out, starts_y, ends_y, axes_y],
            outputs=[input_y],
        )
        append_node(graph, yslice_node)

        # Add slice for UV
        uv_slice_out = gs.Variable(name=f"SliceUV_{input_uv_name}_out", dtype=np.float32)
        starts_uv = gs.Constant(f"starts_uv_{input_uv_name}", np.array([0, 1], dtype=np.int64))
        ends_uv = gs.Constant(f"ends_uv_{input_uv_name}", np.array([max_int, max_int], dtype=np.int64))
        axes_uv = gs.Constant(f"axes_uv_{input_uv_name}", np.array([0, 1], dtype=np.int64))
        uvslice_node = gs.Node(
            op="Slice",
            name=f"SliceUV_{input_uv_name}",
            inputs=[yuv_conv_out, starts_uv, ends_uv, axes_uv],
            outputs=[uv_slice_out],
        )
        append_node(graph, uvslice_node)

        # Add AveragePool for UV
        averagepool_node = gs.Node(
            op="AveragePool",
            name=f"AveragePool_{input_uv_name}",
            attrs={"kernel_shape": (2, 1), "pads": (0, 0, 0, 0), "strides": (2, 2)},
            inputs=[uv_slice_out],
            outputs=[input_uv],
        )
        append_node(graph, averagepool_node)

        # Update graph inputs
        graph.inputs.remove(input_y)
        graph.inputs.remove(input_uv)
        graph.inputs.append(yuv_merged_input)

        graph.cleanup(remove_unused_node_outputs=False).toposort()
        return graph


class AlTriggerQuantOverrides(AbstractGraphRewriter):
    """Add quantization overrides for active learning trigger Ops."""

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Add quantization overrides for active learning trigger Ops.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings
        Returns:
            Rewritten graph
        """
        tl_namespace = "dsp_al_traffic_light_hard_entropy"
        ts_namespace = "dsp_al_traffic_sign_hard_entropy"
        if self.is_namespace_in_graph(graph, tl_namespace):
            # based on entropy 209.58 +/- 20.0
            # python quantization_ranges.py --min_entropy 189.58 --max_entropy 229.28
            # --num_classes 3 --num_entropies 300 --num_samples 100000
            quantization_ranges = {
                "Add": [5.676498325206457e-07, 0.8042564110434841],
                "Log": [-14.381761100375734, -0.21783714143877805],
                "Mul": [-0.36787934101929937, -6.72562816994265e-06],
                "Neg": [6.72562816994265e-06, 0.36787934101929937],
                "Reshape": [6.72562816994265e-06, 0.36787934101929937],
                "ReduceSum": [0.6299961504802977, 0.7652323741681137],
                "Unsqueeze": [0.6299961504802977, 0.7652323741681137],
                "TopK2DSum": [189.58544100708406, 229.27878724490128],
            }
            self.add_quantization_overrides_hard_entropy(
                quant_overrides,
                namespace=tl_namespace,
                quantization_ranges=quantization_ranges,
            )
        if self.is_namespace_in_graph(graph, ts_namespace):
            # based on entropy 174.77 +/- 20.0
            # python quantization_ranges.py --min_entropy 154.77 --max_entropy 194.77
            # --num_classes 2 --num_entropies 300 --num_samples 100000
            quantization_ranges = {
                "Add": [0.21074936287372886, 0.789250837126271],
                "Log": [-1.557085705242446, -0.23667109088413482],
                "Mul": [-0.3676005414647151, -0.1867928329367821],
                "Neg": [0.1867928329367821, 0.3676005414647151],
                "Reshape": [0.1867928329367821, 0.3676005414647151],
                "ReduceSum": [0.5149474975478479, 0.6496769043023166],
                "Unsqueeze": [0.5149474975478479, 0.6496769043023166],
                "TopK2DSum": [154.77376706366093, 194.7695047245145],
            }
            self.add_quantization_overrides_hard_entropy(
                quant_overrides,
                namespace=ts_namespace,
                quantization_ranges=quantization_ranges,
            )
        return graph

    def is_namespace_in_graph(self, graph: gs.Graph, namespace: str) -> bool:
        """Check if namespace is in one of the node names.

        Args:
            graph: Graph to be manipulated
            namespace: namespace string that is present in the trigger node names
        Returns:
            True if namespace is present, otherwise False
        """
        return any(namespace in node.name for node in graph.nodes)

    def add_quantization_overrides_hard_entropy(
        self, quant_overrides: QuantizationEncodings, namespace: str, quantization_ranges: dict[str, list[float]]
    ) -> None:
        """Add quantization override min and max values to quant_overrides for hard_entropy trigger ops.

        Args:
            quant_overrides: override encodings
            namespace: namespace string that is present in the trigger node names
            quantization_ranges: Dict with quantization ranges for each op
        """
        # consider using a larger bit_width
        quant_overrides.add_activation_encoding(
            f"{namespace}/class_plus_eps/output",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["Add"][0],
                max=quantization_ranges["Add"][1],  # Add
                limit_min_max=True,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{namespace}/log_prob/output",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["Log"][0],
                max=quantization_ranges["Log"][1],  # Log
                limit_min_max=True,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{namespace}/p_log_p/output",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["Mul"][0],
                max=quantization_ranges["Mul"][1],  # Mul
                limit_min_max=True,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{namespace}/neg_p_log_p/output",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["Neg"][0],
                max=quantization_ranges["Neg"][1],  # Neg
                limit_min_max=True,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{namespace}/reshape_for_entropy/output",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["Reshape"][0],
                max=quantization_ranges["Reshape"][1],  # Reshape
                limit_min_max=True,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{namespace}/entropy/output",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["ReduceSum"][0],
                max=quantization_ranges["ReduceSum"][1],  # ReduceSum
                limit_min_max=True,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{namespace}/unsqueeze_output",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["Unsqueeze"][0],
                max=quantization_ranges["Unsqueeze"][1],  # Unsqueeze
                limit_min_max=True,
            ),
        )
        quant_overrides.add_activation_encoding(
            f"{namespace}",
            Encoding(
                bitwidth=8,
                min=quantization_ranges["TopK2DSum"][0],
                max=quantization_ranges["TopK2DSum"][1],  # TopK2DSum
                limit_min_max=True,
            ),
        )


class SplitSumOp(AbstractGraphRewriter):
    """Splits the sum operation for more than 2 operands.

    For the case of summation of more than 2 inputs/operands, QNN splits
    the summation over multiple sum operations. This causes the problem for
    the case, overrides in model are being set, corresponding to onnx. So,
    splitting the sum directly in Onnx would make the ops and quantization
    parameters transparent.
    For example, see the testcase.
    """

    def __init__(
        self,
        split_all: bool = True,  # noqa: FBT001, FBT002
        node_name: str | None = None,
    ) -> None:
        """Init the SplitSumOp rewriter.

        Args:
            split_all: Flag to define, if all sum operations to be splitted.
            node_name: Incase defined, then only that node will be splitted.
        """
        self.split_all = split_all
        self.node_name = node_name

        assert self.split_all or node_name is not None, "Define node_name to split Sum Op!"

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Splits the Sum op with operand > 2.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings
        Returns:
            Rewritten graph
        """
        for node in graph.nodes:
            if node.op == "Sum" and len(node.inputs) > 2 and (self.split_all or node.name == self.node_name):
                orig_node_name = node.name
                # To take over the quantization overrides to splitted/chained sum ops
                orig_node_quant_override = quant_overrides.get_activation_encoding(node.outputs[0].name)
                sum_nodes = []
                sum_operands = node.inputs
                for i in range(len(node.inputs) - 2):
                    sum_chain_node_out = gs.Variable(
                        name=f"{orig_node_name}_chain_{i}_out",
                        dtype=node.outputs[0].dtype,
                        shape=node.outputs[0].shape,
                    )
                    sum_node = gs.Node(
                        op="Sum",
                        name=f"{orig_node_name}_chain_{i}",
                        inputs=[],
                        outputs=[sum_chain_node_out],
                    )
                    sum_nodes.append(sum_node)
                    sum_operands.append(sum_chain_node_out)
                    append_node(graph, sum_node)

                    # Add quantization override to chained nodes as well, incase sum node output overridden
                    if orig_node_quant_override:
                        quant_overrides.add_activation_encoding(sum_chain_node_out.name, orig_node_quant_override)

                sum_nodes.append(node)

                # Sum Ops are tied to inputs or intermediate outputs of previous sum
                for idx, sum_node in enumerate(sum_nodes):
                    sum_node.inputs = sum_operands[idx * 2 : idx * 2 + 2]

                LOGGER.info(f"{self.__class__.__name__} applied to {node.name}")

                if not self.split_all:
                    break

        graph.cleanup(remove_unused_node_outputs=False).toposort()
        return graph


class MergeConcatConstantGraphRewriter(AbstractGraphRewriter):
    """Merge consecutive constants in concat nodes.

    This is a bug in the QNN SDK where if the same constants appears multiple times, the graph construction fails.
    We work around this by merging all consecutive constants into a single one.
    """

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Merge consecutive constants in Concat ops.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings
        Returns:
            Rewritten graph
        """

        node_cnt = 0
        for node in graph.nodes:
            if node.op != "Concat":
                continue

            axis: int = cast(int, node.attrs.get("axis", -1))
            new_inputs: list[gs.Tensor] = []
            constants_to_merge: list[gs.Constant] = []

            for current_input in node.inputs:
                if isinstance(current_input, gs.Constant):
                    constants_to_merge.append(current_input)
                    continue

                node_cnt = self._merge_constants(
                    constants_to_merge=constants_to_merge,
                    new_inputs=new_inputs,
                    axis=axis,
                    node=node,
                    node_cnt=node_cnt,
                    quant_overrides=quant_overrides,
                )
                new_inputs.append(current_input)

            node_cnt = self._merge_constants(
                constants_to_merge=constants_to_merge,
                new_inputs=new_inputs,
                axis=axis,
                node=node,
                node_cnt=node_cnt,
                quant_overrides=quant_overrides,
            )
            node.inputs.clear()
            node.inputs.extend(new_inputs)

        return graph

    def _merge_constants(  # noqa: C901
        self,
        *,
        constants_to_merge: list[gs.Constant],
        new_inputs: list[gs.Tensor],
        axis: int,
        node: gs.Node,
        node_cnt: int,
        quant_overrides: QuantizationEncodings,
    ) -> int:
        if len(constants_to_merge) == 1:
            new_inputs.append(constants_to_merge[0])
            constants_to_merge.clear()
        elif len(constants_to_merge) > 1:
            merged_values = np.concatenate([c.values for c in constants_to_merge], axis=axis)

            merged_const = gs.Constant(name=f"{constants_to_merge[0].name}_{node_cnt}_merged", values=merged_values)

            constants_to_merge_s = [
                f"{c.name}: (shape={c.values.shape}, dtype={c.values.dtype})" for c in constants_to_merge
            ]

            msg = (
                f"MergeConcatConstantGraphRewriter: Merging {constants_to_merge_s} into a single constant "
                f"{merged_const} for Concat node {node}"
            )

            new_inputs.append(merged_const)

            bitwidth = None
            min_value = None
            max_value = None

            for node_input in node.inputs:
                if encoding := (
                    quant_overrides.get_activation_encoding(node_input.name)
                    or quant_overrides.get_param_encoding(node_input.name)
                ):
                    if this_bitwidth := encoding.bitwidth:
                        bitwidth = max(this_bitwidth, bitwidth or 0)
                    if (this_min := encoding.min) is not None:
                        min_value = this_min if min_value is None else min(this_min, min_value)
                    if (this_max := encoding.max) is not None:
                        max_value = this_max if max_value is None else max(this_max, max_value)

            if bitwidth is not None or min_value is not None or max_value is not None:
                encoding = Encoding(bitwidth=bitwidth or 8)
                if min_value is not None:
                    encoding.min = min_value
                if max_value is not None:
                    encoding.max = max_value

                quant_overrides.add_param_encoding(merged_const.name, encoding)

                msg += f" with encoding {encoding}"

            LOGGER.info(msg)

            constants_to_merge.clear()
            node_cnt += 1

        return node_cnt


class FuseMatMulAddOps(AbstractGraphRewriter):
    """[MatMul] or [Matmul + Add] ops are replaced with a single Gemm op for QNN conversion.

    TODO: Refactor FuseMatMulAddOps FuseMatMulMulOps graph surgery
    Usually, MatMul + Add op combination is supposed to optimized and fused together by QNN SDK
    into a QNN specific FullyConnected Op. But, for some cases, it does not fuse them together, resulting
    in a FullyConnected + Add op. Additionally, for a rare case, it crashes, when Add op's constant
    operand has override for 32 bitwidth (to force fuse with MatMul, for which Add is treated as bias
    and Bias is computed with 32 bitwidth.)
    Additionally, for Matmul only, as QNN adds the squeeze/unsqueeze internally, it is better to manage
    the overrides and resulting nodes in Qnn graph, when they are already present in Onnx.
    Note: Onnx supports Gemm op with input rank of 2, so for now input rank 3 is supported with
    Dim0 == Batchsize
    """

    def __init__(
        self,
    ) -> None:
        """Init the FuseMatMulAddOps rewriter."""
        return

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:  # noqa: PLR0915, C901, PLR0912
        """Replace [MatMul] or [Matmul + Add] ops combination with Gemm Op.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings
        Returns:
            Rewritten graph
        """
        for node in graph.nodes:
            if node.op == "MatMul" and isinstance(node.inputs[1], gs.Constant):
                # For Rank >3 or MatMul Weights of Rank >2, not supported
                if len(node.inputs[0].shape) > 3 or len(node.inputs[1].shape) > 2:
                    continue

                # Even for Rank 3, Dim0 should be 1, to be able to remove and add the dimension
                if len(node.inputs[0].shape) == 3 and node.inputs[0].shape[0] > 1:
                    continue

                out_feat_dim = node.inputs[1].shape[1]

                add_node = None

                add_node = node
                add_bias = None
                if node.o().op == "Add":
                    add_node = node.o()
                    add_bias = add_node.inputs[0] if node.outputs[0] == add_node.inputs[1] else add_node.inputs[1]
                    # Verify Add bias/constant operand shape if suitable for fusing
                    # Channels first (onnx) - MatMul op Weights shape = [m, n], Add Op. operand shape = [m]
                    # Channels Last (QC graph) - MatMul op Weights shape = [n, m], Add Op. operand shape = [m]
                    if add_bias.shape != [out_feat_dim]:
                        add_node = node

                merged_encoding = merge_encodings(
                    node.outputs[0].name,
                    quant_overrides.get_activation_encoding(node.outputs[0].name),
                    add_node.outputs[0].name,
                    quant_overrides.get_activation_encoding(add_node.outputs[0].name),
                )

                weight_var = node.inputs[1]
                bias_var = add_bias

                # Optional folding of a subsequent multiplication into the Gemm node.
                mul_node = None
                if isinstance(weight_var, gs.Constant) and isinstance(bias_var, gs.Constant | None):
                    mul_node = add_node.o() if add_node.outputs and add_node.outputs[0].outputs else None
                    if mul_node is not None and not self._is_mul_fusable(mul_node, add_node.outputs[0], out_feat_dim):
                        mul_node = None

                if mul_node is not None:
                    assert mul_node is not None
                    scale_const = (
                        mul_node.inputs[0] if mul_node.inputs[1] is add_node.outputs[0] else mul_node.inputs[1]
                    )
                    scale_vals = scale_const.values.astype(weight_var.values.dtype)
                    weight_var = _fold_scale_into_const(weight_var, scale_vals)
                    if bias_var:
                        bias_var = _fold_scale_into_const(bias_var, scale_vals)

                    merged_encoding = merge_encodings(
                        f"{node.outputs[0].name}_merged",
                        merged_encoding,
                        mul_node.outputs[0].name,
                        quant_overrides.get_activation_encoding(mul_node.outputs[0].name),
                    )

                gem_inputs = [node.inputs[0], weight_var]
                if bias_var:
                    gem_inputs.append(bias_var)
                gemm_out = (
                    mul_node.outputs[0] if mul_node is not None else add_node.outputs[0]
                )  # final tensor name to preserve topology
                gem_outputs = [gemm_out]
                # For Rank 3 input, add squeeze and unsqueeze nodes around Gemm node
                if len(node.inputs[0].shape) == 3:
                    # Add squeeze op before the Gemm layer to make it Rank 2
                    squeeze_axis_const = gs.Constant(name=node.name + "_squeeze_axis", values=np.array([0]))
                    squeeze_out = gs.Variable(name=node.name + "_squeeze_out", dtype=node.inputs[0].dtype)
                    squeeze = gs.Node(
                        op="Squeeze",
                        name=node.name + "_squeeze",
                        inputs=[node.inputs[0], squeeze_axis_const],
                        outputs=[squeeze_out],
                    )
                    if quant_overrides.get_activation_encoding(squeeze_out.name) is None:
                        quant_overrides.add_activation_encoding(
                            squeeze_out.name, quant_overrides.get_activation_encoding(node.inputs[0].name)
                        )

                    gem_inputs[0] = squeeze_out
                    gemm_out = gs.Variable(name=node.name + "_gemm_out", dtype=node.inputs[0].dtype)
                    gem_outputs = [gemm_out]

                    post_node = None
                    if mul_node is not None and mul_node.outputs and mul_node.outputs[0].outputs:
                        post_node = mul_node.o()
                    elif mul_node is None and add_node.outputs and add_node.outputs[0].outputs:
                        post_node = add_node.o()

                    if post_node and post_node.op in {"Clip", "Relu"}:
                        unsqueeze_output = post_node.outputs[0]
                        unsqueeze_in = gs.Variable(name=node.name + "_unsqueeze_in", dtype=node.inputs[0].dtype)
                        if quant_overrides.get_activation_encoding(unsqueeze_in.name) is None:
                            quant_overrides.add_activation_encoding(
                                unsqueeze_in.name, quant_overrides.get_activation_encoding(post_node.outputs[0].name)
                            )
                        post_node.inputs[0] = gemm_out
                        post_node.outputs[0] = unsqueeze_in
                        unsqueeze_input = unsqueeze_in
                    else:
                        unsqueeze_input = gemm_out
                        unsqueeze_output = mul_node.outputs[0] if mul_node is not None else add_node.outputs[0]

                    # Add unsqueeze op after the Gemm layer to make it Rank 3 again
                    unsqueeze_axis_const = gs.Constant(name=node.name + "_unsqueeze_axis", values=np.array([0]))
                    unsqueeze = gs.Node(
                        op="Unsqueeze",
                        name=node.name + "_unsqueeze",
                        inputs=[unsqueeze_input, unsqueeze_axis_const],
                        outputs=[unsqueeze_output],
                    )
                    graph.nodes += [squeeze]
                    graph.nodes += [unsqueeze]

                    if merged_encoding and quant_overrides.get_activation_encoding(gemm_out.name) is None:
                        quant_overrides.add_activation_encoding(gemm_out.name, merged_encoding)

                gemm = gs.Node(
                    op="Gemm",
                    name=node.name + "_gemm",
                    inputs=[*gem_inputs],
                    outputs=[*gem_outputs],
                    attrs={"alpha": 1.0, "beta": 1.0, "transA": 0, "transB": 0},
                )
                LOGGER.info(f"{self.__class__.__name__} applied to {node.name}, folded mul: {mul_node is not None}")
                node.inputs.clear()
                node.outputs.clear()
                add_node.inputs.clear()
                add_node.outputs.clear()
                if mul_node is not None:
                    mul_node.inputs.clear()
                    mul_node.outputs.clear()
                graph.nodes += [gemm]

        graph.cleanup().toposort()
        return graph

    @staticmethod
    def _is_mul_fusable(mul_node: gs.Node, add_output: gs.Variable, out_dim: int) -> bool:
        """Return True if mul_node can be folded.

        Args:
            mul_node: The Mul node to inspect.
            add_output: The output of the preceding Add node.
            out_dim: Number of output channels (‖m‖).
        """
        if mul_node.op != "Mul":
            return False
        # Identify which input is the scaling constant.
        scale_inp = mul_node.inputs[0] if mul_node.inputs[1] is add_output else mul_node.inputs[1]
        if not isinstance(scale_inp, gs.Constant):
            return False
        if scale_inp.values.size == 1:
            return True  # scalar broadcast
        return list(scale_inp.values.shape) == [out_dim]


class FuseMatMulMulOps(AbstractGraphRewriter):
    """MatMul + Mul ops are optimized by folding constant scales into the MatMul weight.

    - If the scaling constant is a scalar and the weight is constant, we multiply the weight by that scalar and
      remove the Mul node.
    - If it's a per-channel vector matching output features and the weight is constant,
      we broadcast-fold it into the weight tensor and drop the Mul.

    This preserves the original MatMul op and supports rank-2 inputs and rank-3 with batch size = 1.
    """

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Replace MatMul + Mul ops combination with a single MatMul Op."""
        for node in list(graph.nodes):
            if node.op != "MatMul":
                continue
            mul_node = node.o()
            if not mul_node or mul_node.op != "Mul":
                continue

            merged_encoding = quant_overrides.get_activation_encoding(
                mul_node.outputs[0].name
            ) or quant_overrides.get_activation_encoding(node.outputs[0].name)

            weight_var = node.inputs[1]
            # We can only fold if the weights are a constant
            if not isinstance(weight_var, gs.Constant):
                continue

            scale_const = mul_node.inputs[0] if mul_node.inputs[1] is node.outputs[0] else mul_node.inputs[1]
            # We can only fold if the scale is a constant
            if not isinstance(scale_const, gs.Constant):
                continue
            scale_vals = scale_const.values

            # Feature dimension at MatMul weight: [k, m] => m = output features
            out_feats = node.inputs[1].shape[-1]
            is_scalar = scale_vals.size == 1
            is_per_channel = list(scale_vals.shape) == [out_feats]
            if not (is_scalar or is_per_channel):
                continue

            # Fold scale into weight
            sv = scale_vals.astype(weight_var.values.dtype)
            weight_var = _fold_scale_into_const(weight_var, sv)
            node.inputs[1] = weight_var

            # Remove Mul: reroute MatMul output directly
            mul_out = mul_node.outputs[0]
            node.outputs[0] = mul_out

            # Clear Mul
            mul_node.inputs.clear()
            mul_node.outputs.clear()

            if merged_encoding:
                quant_overrides.add_activation_encoding(node.outputs[0].name, merged_encoding)

            LOGGER.info(f"{self.__class__.__name__} applied to {node.name}")

        graph.cleanup().toposort()
        return graph


def _fold_scale_into_const(const: gs.Constant, scale: npt.NDArray[np.float32]) -> gs.Constant:
    """Return a new constant whose values are const * scale.

    Broadcast rules follow numpy semantics; otherwise scale is reshaped so that its last dimension matches the
    feature dim.
    """
    # Ensure scale has at least 2 dims so broadcasting works.
    if scale.size != 1 and const.values.ndim == 2 and scale.ndim == 1:
        scale = scale.reshape(1, -1)  # (1, m) so it broadcasts over k
    new_vals = const.values * scale
    return gs.Constant(name=f"{const.name}_scaled", values=new_vals)


def _rewrite_filter_2d(node: gs.Node) -> None:
    """Adapt Filter2D.

    Args:
        node: The Filter2D node to adapt.
    """
    prev_node = node.i()
    if prev_node.op == "Transpose":
        prev_node.outputs[0].shape = prev_node.i().outputs[0].shape
        delete_node(prev_node)


def _rewrite_gather_2d(node: gs.Node) -> None:
    """Adapt Gather2D.

    Args:
        node: The Gather2D node to adapt.
    """
    assert len(node.outputs) == 1, UNEXPECTED_NUM_OUT_MSG

    # transposes can be shared between heads
    transpose_node = node.i()
    if transpose_node.op == "Transpose":
        prev_node = get_first_non_constant_input_node(transpose_node)
        if prev_node is not None:
            for consumer_gather_node in transpose_node.outputs[0].outputs[:]:
                consumer_gather_node.inputs[0] = prev_node.outputs[0]
            transpose_node.inputs.clear()
            transpose_node.outputs.clear()

    node.outputs[0].shape = [1, node.inputs[-1].shape[-1], node.inputs[0].shape[1]]


def _rewrite_anchor_generator(node: gs.Node) -> None:
    """Adapt AnchorGenerator.

    Args:
        node: The AnchorGenerator node to adapt.
    """
    assert "strides" in node.attrs, "strides attribute not present"
    assert isinstance(node.attrs["strides"], list)
    assert len(node.inputs) == 2, "Unexpected number of inputs"
    assert len(node.outputs) == 2, UNEXPECTED_NUM_OUT_MSG
    assert isinstance(node.inputs[1], gs.Constant), "second inputs need to be a constant"

    stride = int(node.attrs["strides"][0])
    node.attrs.clear()
    node.attrs["stride"] = stride

    # filter 2d indices
    if node.inputs[0].dtype != np.int32:
        node.inputs[0].dtype = np.int32

    # prior box sizes
    if node.inputs[1].values.dtype != np.int32:
        node.inputs[1].values = node.inputs[1].values.astype(np.int32)

    num_anchors = node.inputs[0].shape[-1] * node.inputs[1].shape[0]
    # Add correct output dtype/shapes
    node.outputs[0].dtype = np.int32
    node.outputs[0].shape = [num_anchors, 4]
    node.outputs[1].dtype = np.int32
    node.outputs[1].shape = [num_anchors, 4]

    add_offset_node = node.o(0, 0)
    assert add_offset_node.op == "Add", "Expected Add node"
    assert isinstance(add_offset_node.inputs[1], gs.Constant)

    add_offset_node.inputs[1].values = add_offset_node.inputs[1].values.astype(np.int32)
    add_offset_node.outputs[0].dtype = np.int32
    add_offset_node.outputs[0].shape = None


def _collect_box_decoder_nodes(node: gs.Node) -> tuple[gs.Node, list[gs.Tensor], list[gs.Tensor]]:
    """Collect the box decoder nodes."""

    # find concat of decoded boxes
    box_concat = node.i()
    assert box_concat.op == "Concat"

    # the inputs into concat determine the number of scales
    n_scales = len(box_concat.inputs)

    regression_offsets = []
    anchor_scales = []
    new_box_concat_inputs = []
    for scale in range(n_scales):
        box_decoder_add = box_concat.i(scale, 0)
        assert box_decoder_add.op == "Add", f"Expected Add is {box_decoder_add.op}"
        box_decoder_mul = box_decoder_add.i(1, 0)
        assert box_decoder_mul.op == "Mul", f"Expected Add is {box_decoder_mul.op}"

        regression_offsets.append(box_decoder_mul.inputs[0])
        anchor_scale = box_decoder_mul.inputs[1]
        if isinstance(anchor_scale, gs.Constant) and anchor_scale.values.ndim == 3:
            # when objectness filtering is not active, this is the constant anchor scale
            # tensor as used in the torch model which needs to be converted to int32 and
            # squeezed in contrast to the already matching filtered outputs of the
            # AnchorGenerator
            if anchor_scale.values.ndim == 3:
                anchor_scale.values = anchor_scale.values.squeeze(0)
            anchor_scale.values = anchor_scale.values.astype(np.int32)
        anchor_scales.append(anchor_scale)
        box_decoder_mul.outputs.clear()

        box_offset = box_decoder_add.inputs[0]
        box_offset.name = f"{node.name}/anchor_offsets{scale}"
        if isinstance(box_offset, gs.Constant):
            if box_offset.values.ndim == 3:
                box_offset.values = box_offset.values.squeeze(0)
            box_offset.values = box_offset.values.astype(np.int32)
        elif not isinstance(box_offset, gs.Constant):
            box_offset.dtype = np.int32

        new_box_concat_inputs.append(box_decoder_add.inputs[0])

    box_concat.inputs = new_box_concat_inputs
    return box_concat, regression_offsets, anchor_scales


def _rewrite_nms(graph: gs.Graph, node: gs.Node) -> None:
    """Adapt NMS.

    Args:
        graph: The graph to modify
        node: The NMS node to adapt.
    """
    assert len(node.inputs) == 6, f"Unexpected number of inputs {len(node.inputs)}"
    node.attrs["background_id"] = int(node.inputs[-4].values.item())
    node.attrs["confidence_threshold"] = node.inputs[-3].values.item()
    node.attrs["nms_threshold"] = node.inputs[-2].values.item()
    node.attrs["keep_top_k"] = int(node.inputs[-1].values.item())
    node.inputs.pop()
    node.inputs.pop()
    node.inputs.pop()
    node.inputs.pop()

    # filter 2d indices
    if node.inputs[0].dtype != np.int32:
        node.inputs[0].dtype = np.int32

    assert len(node.outputs) == 1, UNEXPECTED_NUM_OUT_MSG

    # remove the box decoder but keep the regression and anchor scales tensors as we need
    # to concat them and pass them to the NMS
    (box_concat, regression_offsets, anchor_scales) = _collect_box_decoder_nodes(node)

    concat_output_shape = None
    if box_concat.outputs[0].shape:
        concat_output_shape = box_concat.outputs[0].shape[1:]
        box_concat.outputs[0].shape = concat_output_shape
    assert concat_output_shape is not None

    box_concat.attrs["axis"] = -2
    box_concat.outputs[0].dtype = np.int32
    box_concat.outputs[0].name = f"{node.name}/anchor_offsets_concat_out"

    var_regression_concat_out = gs.Variable(
        name=f"{node.name}/regression_concat_out",
        dtype=np.float32,
    )
    regression_concat_node = gs.Node(
        op="Concat",
        name=f"{node.name}/regression_concat",
        inputs=regression_offsets,
        attrs={"axis": 1},
        outputs=[var_regression_concat_out],
    )
    append_node(graph, regression_concat_node)

    var_anchor_scales_concat_out = gs.Variable(
        name=f"{node.name}/anchor_scales_concat_out",
        dtype=np.int32,
        shape=concat_output_shape,
    )
    anchor_scales_concat_node = gs.Node(
        op="Concat",
        name=f"{node.name}/anchor_scales_concat",
        inputs=anchor_scales,
        attrs={"axis": -2},
        outputs=[var_anchor_scales_concat_out],
    )
    append_node(graph, anchor_scales_concat_node)
    classes = node.inputs[1]
    node.inputs.clear()
    node.inputs.extend(
        [
            var_regression_concat_out,
            classes,
            var_anchor_scales_concat_out,  # anchor scale
            box_concat.outputs[0],  # anchor offset
        ]
    )
    n_classes = None
    if node.inputs[1].shape is not None:
        n_classes = shape_to_list(node.inputs[1].shape)[2]

    assert n_classes is not None, "n_classes could not be determined"

    index_output = node.outputs[0]
    index_output.shape = [1, node.attrs["keep_top_k"], n_classes, 1]
    index_output.dtype = np.int32

    boxes_output = None
    # Search for GatherPlugin node
    gather_node = None
    gathers = node.outputs[0].outputs
    for n in gathers:
        if n.op == "GatherPlugin" and "box2d" in get_name_of_tensor(n.outputs[0]):
            gather_node = n
            break
    assert gather_node is not None

    boxes_output = gather_node.outputs[0]
    boxes_output.shape = [1, node.attrs["keep_top_k"], n_classes, 4]
    boxes_output.dtype = np.float32
    node.outputs.append(boxes_output)
    gather_node.outputs.clear()


class RewriteSsdObjectness(AbstractGraphRewriter):
    """RewriteSsdObjectness."""

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Adjust Filter2D,Gather2D,AnchorGenerator,NMS and GatherPLugin after torch export.

        Args:
            graph: Graph to be manipulated
            quant_overrides: override encodings
        Returns:
            Rewritten graph
        """
        for node in graph.nodes:
            if node.op == "Filter2D":
                LOGGER.info("RewriteSsdObjectness: change Filter2D")
                _rewrite_filter_2d(node)
            if node.op == "Gather2D":
                LOGGER.info("RewriteSsdObjectness: change Gather2D")
                _rewrite_gather_2d(node)
            if node.op == "AnchorGenerator":
                LOGGER.info("RewriteSsdObjectness: change AnchorGenerator")
                _rewrite_anchor_generator(node)
            if node.op == "NMS":
                LOGGER.info("RewriteSsdObjectness: change NMS")
                _rewrite_nms(graph, node)
        graph.cleanup().toposort()
        return graph


class FuseShapeOps(AbstractGraphRewriter):
    """Fuse consecutive ops like Squeeze, Unsqueeze, Reshape."""

    def __init__(self) -> None:
        """Init the FuseShapeOps rewriter."""
        super().__init__()
        self.SHAPE_OPS = {"Squeeze", "Unsqueeze", "Reshape"}

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Fuse consecutive ops like Squeeze, Unsqueeze, Reshape."""

        tensors_before = {t.name for t in graph.tensors().values()}

        changed = True
        while changed:
            changed = False
            for n in list(graph.nodes):
                if n.op not in self.SHAPE_OPS:
                    continue
                if not n.outputs:
                    continue
                out_t = n.outputs[0]
                for nxt in out_t.outputs:
                    if nxt.op not in self.SHAPE_OPS:
                        continue
                    # n ➜ out_t ➜ nxt   is a shape-op chain
                    changed |= self._fuse_pair(n, nxt, graph, quant_overrides)

            graph.cleanup().toposort()

        tensors_after = {t.name for t in graph.tensors().values()}
        removed_tensors = tensors_before - tensors_after

        quant_overrides.remove_encodings(removed_tensors)

        return graph

    def _fuse_pair(
        self, upper: gs.Node, lower: gs.Node, graph: gs.Graph, quant_overrides: QuantizationEncodings
    ) -> bool:
        """Fuse two shape ops into one.

        Two cases:
            1. if Shape is unchanged before / after ops, remove two ops directly.
            2. if Shape is changed before / after ops, create ONE new Reshape op instead
        """

        inp: gs.Variable = upper.inputs[0]
        shape0 = tuple(int(d) if d is not None else -1 for d in inp.shape)  # type: ignore[reportOptionalIterable]

        shape1 = _apply_op_to_shape(upper, shape0)
        shape2 = _apply_op_to_shape(lower, shape1)

        out_tensor = lower.outputs[0]
        lower.outputs.clear()

        if _is_same_static_shape(shape0, shape2):
            LOGGER.info(f"Removing {upper.name} -> {lower.name}: {shape0} -> {shape1} -> {shape2}")
            return self._handle_no_ops_fusion(graph, inp, out_tensor, quant_overrides)

        LOGGER.info(f"Fusing {upper.name} -> {lower.name}: {shape0} -> {shape1} -> {shape2}")
        return self._handle_reshape_fusion(graph, inp, upper, lower, out_tensor, shape2, quant_overrides)

    def _handle_no_ops_fusion(
        self,
        graph: gs.Graph,
        input_tensor: gs.Variable,
        output_tensor: gs.Variable,
        quant_overrides: QuantizationEncodings,
    ) -> bool:
        """Fuse two shape ops when the tensor shapes are unchanged before / after ops."""
        # No-op -> bypass both nodes
        for c in list(output_tensor.outputs):
            c.inputs = [input_tensor if t is output_tensor else t for t in c.inputs]

        if output_tensor in graph.outputs:
            input_tensor.name = output_tensor.name
            graph.outputs = [input_tensor if t is output_tensor else t for t in graph.outputs]

        return True

    def _handle_reshape_fusion(
        self,
        graph: gs.Graph,
        input_tensor: gs.Variable,
        upper: gs.Node,
        lower: gs.Node,
        output_tensor: gs.Variable,
        output_shape: tuple[int, ...],
        quant_overrides: QuantizationEncodings,
    ) -> bool:
        """Fuse two shape ops when tensor shapes changed before / after ops.

        Create ONE Reshape that realizes two shape ops.
        """
        is_graph_output = output_tensor in graph.outputs
        shape_const = gs.Constant(f"{lower.name}_shape", np.asarray(output_shape, dtype=np.int64))

        if is_graph_output:
            new_out = output_tensor
        else:
            new_out = gs.Variable(f"{lower.name}_out", dtype=upper.outputs[0].dtype, shape=output_shape)
            quant_overrides.add_activation_encoding(
                new_out.name, quant_overrides.get_activation_encoding(output_tensor.name)
            )

        reshape = gs.Node(
            op="Reshape", inputs=[input_tensor, shape_const], outputs=[new_out], name=f"{upper.name}_fused"
        )
        graph.nodes.append(reshape)  # type: ignore[reportAttributeAccessIssue]

        for c in list(output_tensor.outputs):
            c.inputs = [new_out if t is output_tensor else t for t in c.inputs]

        if is_graph_output:
            graph.outputs = [new_out if t is output_tensor else t for t in graph.outputs]

        return True


def _apply_op_to_shape(op_node: gs.Node, in_shape: Sequence[int]) -> tuple[int, ...]:  # noqa: C901, PLR0912
    """Return the static shape tuple after applying op_node.

    Unknown dims stay as -1.
    """
    if op_node.op == "Unsqueeze":
        axes = op_node.inputs[1]
        if not isinstance(axes, gs.Constant):
            msg = f"Node {op_node.name} has non-static axes input {axes.name} which is not supported."
            raise RuntimeError(msg)
        out = list(in_shape)
        for ax in axes.values:
            out.insert(ax if ax >= 0 else len(out) + ax + 1, 1)
        return tuple(out)

    if op_node.op == "Squeeze":
        axes = op_node.inputs[1]
        if not isinstance(axes, gs.Constant):
            msg = f"Node {op_node.name} has non-static axes input {axes.name} which is not supported."
            raise RuntimeError(msg)
        out = [d for i, d in enumerate(in_shape) if i not in axes.values]
        return tuple(out)

    if op_node.op == "Reshape":
        # constant second input is required for static analysis
        shape_const = op_node.inputs[1]
        if not isinstance(shape_const, gs.Constant):
            msg = f"Node {op_node.name} has non-static shape input {shape_const.name} which is not supported."
            raise RuntimeError(msg)

        raw = list(shape_const.values)
        target = []
        unknown_idx = None
        known_product = 1

        # First pass: handle 0, positive dims, and record -1
        for i, x in enumerate(raw):
            if x == 0:
                # copy from input shape
                dim = in_shape[i]
                target.append(dim)
                if dim != -1:
                    known_product *= dim
            elif x == -1:
                if unknown_idx is not None:
                    msg = f"Node {op_node.name} has more than one -1 in reshape: {raw}."
                    raise RuntimeError(msg)
                unknown_idx = len(target)
                target.append(-1)
            else:
                dim = int(x)
                target.append(dim)
                known_product *= dim

        # Compute product of input dims (skip unknowns)
        input_product = 1
        for d in in_shape:
            if d == -1:
                # cannot compute total elements if input is dynamic
                input_product = None
                break
            input_product *= d

        # Infer the -1 dimension if present
        if unknown_idx is not None:
            if input_product is None:
                msg = f"Node {op_node.name}: cannot infer -1 dimension with dynamic input shape {in_shape}"
                raise RuntimeError(msg)
            inferred = input_product // known_product
            target[unknown_idx] = inferred

        return tuple(target)  # type: ignore[reportReturnType]

    return tuple(in_shape)  # type: ignore[reportReturnType]


def _is_same_static_shape(a: Sequence[int], b: Sequence[int]) -> bool:
    """-1 acts like a wildcard (unknown dim)."""
    if len(a) != len(b):
        return False
    return all((x == y) or (x == -1) or (y == -1) for x, y in zip(a, b))  # noqa: PLR1714


class FuseLpNormalization(AbstractGraphRewriter):
    """Detects the classic L2-normalisation sub-graph and replaces it with ONNX LpNormalization.

        X
        ├─►Mul(X,X)──►ReduceSum(axis=k, keepdims=1)
        │                              │
        │        Div◄─Expand◄─Clip◄─Sqrt┘
        └────────────────────────────────┘

    QNN supports L2Norm (https://pace-docs.azurewebsites.net/qnn-docs/v2.32.2/OpDef/MasterOpDef.html#l2norm)
    which is more accurate and faster than a manual sequence of nodes.
    """

    @staticmethod
    def _single_consumer(var: gs.Variable) -> bool:
        """True if `var` is consumed by exactly one node."""
        return len(var.outputs) == 1

    @staticmethod
    def _is_square(mul: gs.Node) -> bool:
        """True if Mul inputs are the *same* tensor (X*X)."""
        return mul.inputs[0] is mul.inputs[1]

    def apply_rule_on_start(  # noqa: C901, PLR0912
        self,
        graph: gs.Graph,
        quant_overrides: QuantizationEncodings,
    ) -> gs.Graph:
        """Detect and replace L2-normalization sub-graph with LpNormalization."""

        for div in list(graph.nodes):
            if div.op != "Div":
                continue

            # identify x and the denominator branch
            x_var, expand = None, None
            if (
                len(div.inputs) > 0
                and div.inputs[0].inputs
                and div.inputs[0].inputs[0]
                and div.inputs[0].inputs[0].op == "Expand"
            ):
                expand = div.inputs[0].inputs[0]
                x_var = div.inputs[1]
            elif (
                len(div.inputs) > 1
                and div.inputs[1].inputs
                and div.inputs[1].inputs[0]
                and div.inputs[1].inputs[0].op == "Expand"
            ):
                expand = div.inputs[1].inputs[0]
                x_var = div.inputs[0]
            else:
                continue
            if not isinstance(x_var, gs.Variable):
                continue

            # Expand -> Clip
            clip = expand.inputs[0].inputs[0]
            if not clip or clip.op != "Clip":
                continue
            if not self._single_consumer(clip.outputs[0]):
                continue

            # Clip -> Sqrt
            sqrt = clip.inputs[0].inputs[0]
            if not sqrt or sqrt.op != "Sqrt" or not self._single_consumer(sqrt.outputs[0]):
                continue

            # Sqrt -> ReduceSum
            reduce = sqrt.inputs[0].inputs[0]
            if not reduce or reduce.op != "ReduceSum" or not self._single_consumer(reduce.outputs[0]):
                continue
            axes = reduce.inputs[1].values
            if axes is None or len(axes) != 1:
                continue
            axis = axes[0]
            if reduce.attrs.get("keepdims", 1) != 1:
                continue

            # ReduceSum -> Mul (square)
            mul = reduce.inputs[0].inputs[0]
            if not mul or mul.op != "Mul" or not self._single_consumer(mul.outputs[0]):
                continue
            if not self._is_square(mul):
                continue
            if mul.inputs[0] is not x_var:
                continue  # X feeding Mul must be exactly our numerator tensor

            # All checks passed → perform the fusion
            lp_out = div.outputs[0]
            lp_node = gs.Node(
                op="LpNormalization",
                name=f"LpNorm_Fused_{div.name}",
                inputs=[x_var],
                outputs=[lp_out],
                attrs={"p": 2, "axis": axis},
            )
            assert isinstance(graph.nodes, list)
            graph.nodes.append(lp_node)

            div.outputs.clear()

            LOGGER.info(f"{self.__class__.__name__} fused into LpNormalization on axis {axis}")

        graph.cleanup().toposort()
        return graph


class FuseUnsqueezeConcatReshape(AbstractGraphRewriter):
    """Rewrites N * Unsqueeze(axis=1) -> Concat(axis=1) -> Reshape into a single Concat(axis=0).

    Conditions that must hold for the fold to be applied:

    * Each input to the Concat is produced by an Unsqueeze inserting axis = 1.
    * The Unsqueeze inputs are tensors whose batch dimension is 1.
    * The Reshape collapses the leading two dimensions into one.
    """

    def _is_valid_unsqueeze_chain(self, unsqueezes: list[gs.Node]) -> bool:
        for u in unsqueezes:
            if u.op != "Unsqueeze":
                return False

            if len(u.inputs) != 2 or not isinstance(u.inputs[1], gs.Constant):
                return False

            axes = u.inputs[1].values.tolist()
            if axes is None or axes != [1]:
                return False

            in_var = u.inputs[0]
            if in_var.shape is None or in_var.shape[0] != 1:
                return False

        return True

    def _reshape_flattens_batch_and_axis1(self, reshape: gs.Node) -> bool:
        """Checks that the supplied Reshape turns [1, n, ...] into [n, ...].

        The second input to Reshape must be a constant specifying the target
        shape; we look for something like [-1, *shape[2:]*].  The -1 is the
        common idiom for 'infer n'.
        """
        if len(reshape.inputs) != 2 or not isinstance(reshape.inputs[1], gs.Constant):
            return False

        target_shape = reshape.inputs[1].values.tolist()
        # At least two dims and first dim is -1 (n) and second dim is omitted
        # because the preceding Concat+Unsqueeze guaranteed it becomes 1xn...
        return len(target_shape) >= 1 and target_shape[0] == -1

    def apply_rule_on_start(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Fold Unsqueeze + Concat + Reshape into a single Concat(axis=0)."""
        # Iterate over a copy because we may delete nodes
        for reshape in list(graph.nodes):
            if reshape.op != "Reshape" or not reshape.inputs or not reshape.inputs[0].inputs:
                continue

            concat: gs.Node = reshape.i(0)
            if not concat or concat.op != "Concat" or concat.attrs.get("axis") != 1:
                continue

            # Inputs must be Unsqueezes that add axis = 1
            unsqueezes: list[gs.Node] = [concat.i(i) for i in range(len(concat.inputs))]
            if not all(unsqueezes) or not self._is_valid_unsqueeze_chain(unsqueezes):
                continue

            if not self._reshape_flattens_batch_and_axis1(reshape):
                continue

            # We have a match!  Build the replacement Concat(axis = 0)
            original_inputs: list[gs.Variable] = [u.inputs[0] for u in unsqueezes]

            new_concat = gs.Node(
                op="Concat",
                name=f"{concat.name}_fused",
                inputs=original_inputs,  # type: ignore[reportArgumentType]
                outputs=list(reshape.outputs),  # keep graph outputs intact
                attrs={"axis": 0},
            )
            assert isinstance(graph.nodes, list)
            graph.nodes.append(new_concat)

            reshape.outputs.clear()

            LOGGER.info(f"{self.__class__.__name__} folded {reshape.name}")

        graph.cleanup().toposort()
        return graph


class RemoveOutput(AbstractGraphRewriter):
    """Remove output according to specified pattern."""

    def __init__(self, pattern_list: list[str]) -> None:
        """Initialize pattern.

        Args:
            pattern_list: List of patterns to match the output names.
        """
        self.pattern_list = pattern_list

    def apply_rule_on_end(self, graph: gs.Graph, quant_overrides: QuantizationEncodings) -> gs.Graph:
        """Remove output according to pattern.

        Returns:
            Rewritten graph
        """

        _ = (quant_overrides,)
        onnx_graph = gs.export_onnx(graph)
        graph = gs.import_onnx(onnx_graph)

        for pattern in self.pattern_list:
            pattern_compiled = re.compile(pattern)
            graph.outputs = [
                output for output in graph.outputs if not pattern_compiled.match(cast(gs.Variable, output).name)
            ]

        return graph
