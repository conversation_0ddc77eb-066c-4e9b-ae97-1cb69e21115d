"""Sparse Detection Training Lightning Module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import math
from collections.abc import Mapping
from pathlib import Path
from typing import TYPE_CHECKING, Any

import torch
from pytorch_lightning.utilities.types import STEP_OUTPUT, LRSchedulerConfigType
from torch import nn, optim
from torch.optim.lr_scheduler import LambdaLR, LinearLR, SequentialLR

import xtorch_usecases.sparse_detection.dataset.field_definitions as fields
from xcontract.data.definitions.image import HW
from xtorch.losses.interface import LossOutput
from xtorch.metrics.tracking.tracking import TrackingMetric, TrackingMetricsGroundTruth, TrackingMetricsPredictions
from xtorch.nn.heads.object_detection.box3d import PredictedBoxes
from xtorch.training import Stage
from xtorch.training.metric_handling import MetricsContainer
from xtorch.training.training_module import StepOutputKey, TrainingModule
from xtorch_usecases.multimodal.tasks.sparse_detection.bbox_utils import build_2d_bboxes, build_gt_boxes_per_frame
from xtorch_usecases.multimodal.tasks.sparse_detection.definitions import (
    SparseDetectionCameraGT,
    SparseDetectionGT,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.focal_head_loss import FocalHeadLoss
from xtorch_usecases.multimodal.tasks.sparse_detection.head import SparseDetectionHeadOutput
from xtorch_usecases.multimodal.tasks.sparse_detection.loss import SparseDetectionLoss
from xtorch_usecases.multimodal.tasks.sparse_detection.memory_manager import MemoryState
from xtorch_usecases.multimodal.tasks.sparse_detection.streampetr_head import StreamPETRModelInput
from xtorch_usecases.multimodal.tasks.sparse_detection.streampetr_loss import (
    StreamPETRLoss,
)
from xtorch_usecases.multimodal.utils.camera_model import get_pinhole_camera_model
from xtorch_usecases.multimodal.utils.occlusion import (
    get_visibility_mask,
)
from xtorch_usecases.sparse_detection.common.visualization.visualization_writer import VisualizationWriter
from xtorch_usecases.sparse_detection.config import SparseDetectionLMConfig
from xtorch_usecases.sparse_detection.model.model import StreamPETRModel
from xtorch_usecases.sparse_detection.utils.nuscenes_detection import NuscenesDetectionMetric

_LOGGER = logging.getLogger(__name__)

Batch = dict[str, Any]
Predictions = SparseDetectionHeadOutput


class SparseDetectionLM(TrainingModule[SparseDetectionLMConfig, Batch, Predictions, StreamPETRModel]):
    """Sparse Detection Training Lightning Module."""

    def __init__(self, config: SparseDetectionLMConfig) -> None:
        """Instantiates all the resources necessary for the module."""
        super().__init__(config=config)
        self._last_video_frames: list[tuple[str, int]] = []
        self._visu_writer = None
        self._memory_state: MemoryState | None = None

    @property
    def loss(self) -> SparseDetectionLoss:
        """Returns sparse detection loss."""
        assert isinstance(self._loss_fn, SparseDetectionLoss)
        return self._loss_fn

    def _compute_prev_exists(self, batch: Batch) -> torch.Tensor:
        """Computes whether the previous frame of each batch is still part of the same sequence.

        This is necessary to reset the model's state when the sequence changes. A simpler alternative might be to
        count batches up to sequence length, but it bears the risk of mis-aligning the model's state with the data.

        TODO(331328): Map scene tokens in preprocessing to integer to avoid device sync.
        """
        if len(self._last_video_frames) == 0:
            self._last_video_frames = [("invalid_scene_token", -10)] * len(batch[fields.SCENE_TOKEN.name])

        frame_indices = batch[fields.FRAME_IDX.name]
        assert isinstance(frame_indices, torch.Tensor)

        curr_video_frames = list(zip(batch[fields.SCENE_TOKEN.name], batch[fields.FRAME_IDX.name].tolist()))
        if len(curr_video_frames) != len(self._last_video_frames):
            prev_exists = torch.tensor([False] * len(curr_video_frames), device=self.device)
        else:
            prev_video_frames = [(scene_token, frame_idx - 1) for scene_token, frame_idx in curr_video_frames]
            prev_exists = torch.tensor(
                [
                    prev_frame == last_frame
                    for prev_frame, last_frame in zip(prev_video_frames, self._last_video_frames)
                ],
                device=self.device,
            )
        self._last_video_frames = curr_video_frames
        return prev_exists

    def _build_input(self, batch: Batch) -> StreamPETRModelInput:
        """Builds the input for the model."""
        gt_labels_per_frame, gt_bboxes_per_frame = None, None
        if self.training and self._config.model.head_params.fusion_head_params.denoising_params.enabled:
            fusion_gt = build_gt_boxes_per_frame(
                label_ids=batch[fields.AMODAL_CUBOID_LABEL_IDS.name],
                centers_xyz=batch[fields.AMODAL_CUBOID_CENTERS.name],
                dimensions_lwh=batch[fields.AMODAL_CUBOID_DIMENSIONS.name],
                rotations_xyzw=torch.cat(  # Convert quaternion convention wxyz -> xyzw
                    (
                        (rotations_wxyz := batch[fields.AMODAL_CUBOID_ROTATIONS_WXYZ.name])[..., 1:],
                        rotations_wxyz[..., :1],
                    ),
                    dim=-1,
                ),
                velocities_xy=batch[fields.AMODAL_CUBOID_VELOCITIES.name],
                masks=batch[fields.CUBOID_PADDING_MASK.name],
            )
            gt_labels_per_frame = fusion_gt.label_ids
            gt_bboxes_per_frame = fusion_gt.bboxes

        imgs = batch[fields.MULTI_CAMERA_INPUT_IMAGES.name]
        return StreamPETRModelInput(
            imgs=imgs,
            radar_points=batch[fields.RADAR_POINTS.name],
            lidar_points=batch.get(fields.LIDAR_POINTS.name),
            intrinsics=batch[fields.MULTI_CAMERA_INTRINSICS_MATRIX.name],
            img2lidar=batch[fields.LIDAR2IMG.name].inverse(),
            original_img_hw=tuple(imgs.shape[-2:]),
            ego_pose=batch[fields.EGO_POSE.name],
            ego_pose_inv=batch[fields.EGO_POSE_INV.name],
            timestamps=batch[fields.TIMESTAMP_S.name],
            prev_exists=self._compute_prev_exists(batch),
            gt_labels_per_frame=gt_labels_per_frame,
            gt_bboxes_per_frame=gt_bboxes_per_frame,
        )

    def forward(self, batch: Batch) -> Predictions:
        """Forward pass through the model."""
        assert isinstance(self.model, StreamPETRModel)
        inputs = self._build_input(batch)
        output, self._memory_state = self.model(inputs, self._memory_state)
        return output

    def build_model(self) -> StreamPETRModel:
        """Instantiate a SingleLayer model."""
        return StreamPETRModel(params=self._config.model)

    def build_optimizer(self) -> optim.Optimizer:
        """Construct the AdamW optimizer used in this experiment."""
        if TYPE_CHECKING:
            assert isinstance(self._model.backbone, nn.Module)
        backbone_param_names = {n for n, _ in self._model.backbone.named_parameters()}
        backbone_params = list(self._model.backbone.parameters())
        # find all params exept those in the backbone; take into account the added prefix
        other_params = [p for n, p in self._model.named_parameters() if n.split(".", 1)[1] not in backbone_param_names]
        # apply a different learning rate to the pretrained backbone
        return optim.AdamW(
            [
                {
                    "params": backbone_params,
                    "lr": self._config.optimizer.lr * self._config.optimizer.img_backbone_lr_multiplier,
                },
                {"params": other_params, "lr": self._config.optimizer.lr},
            ],
            weight_decay=self._config.optimizer.weight_decay,
        )

    def build_lr_scheduler(self, optimizer: optim.Optimizer) -> LRSchedulerConfigType:
        """Construct the learning rate scheduler used in this experiment."""
        scheduler_warmup = LinearLR(
            optimizer,
            start_factor=self._config.lr_scheduler.warmup_ratio,
            total_iters=self._config.lr_scheduler.warmup_iters,
        )
        num_steps_cosine_annealing = (
            int(self.trainer.estimated_stepping_batches) - self._config.lr_scheduler.warmup_iters
        )
        assert num_steps_cosine_annealing > 0, "The number of steps for cosine annealing must be positive."

        # Define LambdaLR scheduler, which multiplies the function output with the initial LR for each group
        min_lr_ratio = self._config.lr_scheduler.min_lr_ratio

        def cosine_annealing_lr_lambda(epoch: int) -> float:
            return min_lr_ratio + 0.5 * (1 - min_lr_ratio) * (
                1 + math.cos(math.pi * epoch / num_steps_cosine_annealing)
            )

        scheduler_cos_annealing = LambdaLR(optimizer, lr_lambda=cosine_annealing_lr_lambda)

        _LOGGER.info(
            f"The LR scheduler has been configured with linear warmup for {self._config.lr_scheduler.warmup_iters} "
            f"steps followed by cosine annealing for the remaining {num_steps_cosine_annealing} steps."
        )

        # combine warmup and cosine annealing; update scheduler at each step (instead of default each epoch)
        return {
            "scheduler": SequentialLR(
                optimizer=optimizer,
                schedulers=[scheduler_warmup, scheduler_cos_annealing],
                milestones=[self._config.lr_scheduler.warmup_iters],
            ),
            "interval": "step",
        }

    def build_loss(self) -> SparseDetectionLoss:
        """Instantiates the sparse detection loss module."""
        sp_loss_config = self._config.loss.fusion_head_loss
        fh_loss_config = self._config.loss.camera_head_loss

        focal_head_losses = None
        if fh_loss_config.enabled:
            focal_head_losses = [
                FocalHeadLoss(
                    stride=fh_loss_config.stride,
                    class_loss_weight=fh_loss_config.class_loss_weight,
                    bbox_giou_loss_weight=fh_loss_config.bbox_giou_loss_weight,
                    bbox_l1_loss_weight=fh_loss_config.bbox_l1_loss_weight,
                    center_loss_weight=fh_loss_config.center_loss_weight,
                    class_cost_weight=fh_loss_config.class_cost_weight,
                    bbox_giou_cost_weight=fh_loss_config.bbox_giou_cost_weight,
                    bbox_l1_cost_weight=fh_loss_config.bbox_l1_cost_weight,
                    center_cost_weight=fh_loss_config.center_cost_weight,
                )
                for _ in range(self._config.loss.num_camera_losses)
            ]

        return SparseDetectionLoss(
            fusion_head_loss=StreamPETRLoss(
                class_loss_weight=sp_loss_config.class_loss_weight,
                bbox_loss_weights=sp_loss_config.bbox_loss_weights,
                dn_loss_weight=sp_loss_config.dn_loss_weight,
            ),
            camera_head_losses=focal_head_losses,
            use_uncertainty_weighting=self._config.loss.use_uncertainty_weighting,
        )

    def build_metrics(self) -> MetricsContainer[Predictions, Batch]:
        """Instantiates the metrics."""

        def tracking_predictions_access_function(predictions: Predictions) -> list[TrackingMetricsPredictions]:
            if TYPE_CHECKING:
                assert isinstance(self.model, StreamPETRModel)
            boxes: list[PredictedBoxes] = self.model.decode(predictions)  # list of len B with variable size preds
            return [
                TrackingMetricsPredictions(
                    pred_obj_ids=pred_boxes.track_ids,
                    pred_boxes=torch.cat(
                        (pred_boxes.boxes[:, :3], pred_boxes.labels.unsqueeze(1), pred_boxes.scores.unsqueeze(1)),
                        dim=-1,
                    ),
                )
                for pred_boxes in boxes
            ]

        # tracking metrics like mota, motp, idf1, etc.
        metrics: MetricsContainer[Predictions, Batch] = MetricsContainer()
        metrics.add(
            "tracking",
            TrackingMetric(
                class_names=[f"class_{i}" for i in range(self._config.model.head_params.num_classes)],
                only_aggregated=True,
                min_score=self._config.evaluation.tracking.min_threshold,
            ),
            group=[Stage.VALIDATE, Stage.TEST],
            predictions_access_function=tracking_predictions_access_function,
            targets_access_function=tracking_target_access_function,
        )
        # nuscenes detection metrics like mAP, NDS, etc
        metrics.add(
            "nuscenes-detection",
            NuscenesDetectionMetric(
                detection_ranges=self._config.evaluation.nuscenes_detection_kpis.nuscenes_map_detection_ranges,
                class_id_to_nuscenes_label=self._config.evaluation.nuscenes_detection_kpis.class_id_to_nuscenes_label,
            ),
            group=[Stage.VALIDATE, Stage.TEST],
            predictions_access_function=lambda predictions: self.model.decode(predictions),
        )
        return metrics

    def on_validation_epoch_start(self) -> None:
        """Initializes the visualization writer at the beginning of validation."""
        assert self._trainer is not None, "Trainer should be initialized before calling this method."

        filename = f"epoch_{self._trainer.current_epoch}.mcap"
        output_file = Path(self._config.visualization.visu_folder) / filename
        output_file.parent.mkdir(parents=True, exist_ok=True)
        self._visu_writer = VisualizationWriter(output_file)

    def on_validation_batch_end(
        self, outputs: STEP_OUTPUT, batch: Any, batch_idx: int, dataloader_idx: int = 0
    ) -> None:
        """After each batch check if the batch index is in the visualization batches and visualize the results."""
        if (
            batch_idx in self._config.visualization.visu_val_batches
            and self._visu_writer is not None
            and isinstance(outputs, Mapping)
        ):
            if TYPE_CHECKING:
                assert isinstance(self.model, StreamPETRModel)
            predictions: Predictions = outputs[StepOutputKey.PREDICTIONS]
            boxes: list[PredictedBoxes] = self.model.decode(predictions)
            self._visu_writer.visualize_and_save(batch, boxes)

    def on_validation_epoch_end(self) -> None:
        """Closes the visualization writer at the end of validation."""
        if self._visu_writer is not None:
            self._visu_writer.close_writer()
            self._visu_writer = None

        super().on_validation_epoch_end()

    def _build_focal_head_gt(self, batch: Batch) -> SparseDetectionCameraGT:
        """Builds class and regression ground truth as expected by the focal head loss.

        In the following, B is the batch size, N is the number of cameras and K is the number of
        annotated 3d bounding boxes including padding.
        `num_gts_i` is the number of annotated 3d bounding boxes without padding for each frame i while `num_coords`
        is the number of coordinates in StreamPETR box format.
        """
        # boxes
        height_width = batch[fields.MULTI_CAMERA_INPUT_IMAGES.name].shape[-2:]
        label_ids = batch[fields.AMODAL_CUBOID_LABEL_IDS.name]
        centers_xyz = batch[fields.AMODAL_CUBOID_CENTERS.name]  # B, K, 3
        dimensions_lwh = batch[fields.AMODAL_CUBOID_DIMENSIONS.name]
        rotations_wxyz = batch[fields.AMODAL_CUBOID_ROTATIONS_WXYZ.name]
        rotations_xyzw = torch.cat((rotations_wxyz[..., 1:], rotations_wxyz[..., :1]), -1)
        # camera model
        intrinsics = batch[fields.MULTI_CAMERA_INTRINSICS_MATRIX.name]  # B, N, 4, 4
        vehicle_T_cam = batch[fields.MULTI_CAMERA_EXTRINSICS_MATRIX.name]  # noqa: N806 B, N, 4, 4
        camera_model = get_pinhole_camera_model(
            intrinsics=intrinsics,
            vehicle_T_cam=vehicle_T_cam,
            training=True,
            image_size=HW(height_width[0], height_width[1]),
        )
        # mask
        mask = batch[fields.CUBOID_PADDING_MASK.name]  # B, K

        bboxes_img, centers_img, projection_mask, _ = build_2d_bboxes(
            dims_lwh=dimensions_lwh[:, None, ...],
            center_xyz=centers_xyz[:, None, ...],
            orientation_xyzw=rotations_xyzw[:, None, ...],
            camera_model=camera_model,
            image_size_wh=rotations_xyzw.new_tensor(height_width[::-1]).reshape(1, 1, 1, 2),
        )  # B, N, K, *
        bboxes_img[~projection_mask] = 0

        label_ids_per_frame_per_camera = []
        bboxes_per_frame_per_camera = []
        centers_per_frame_per_camera = []
        ignore_boxes_per_frame_per_camera = []
        for batch_index in range(centers_xyz.shape[0]):
            mask_bi = mask[batch_index]  # K

            label_ids_per_frame: list[torch.Tensor] = []
            bboxes_per_frame: list[torch.Tensor] = []
            centers_per_frame: list[torch.Tensor] = []
            ignore_boxes_per_frame: list[torch.Tensor] = []
            for camera_index in range(intrinsics.shape[1]):
                bboxes_bci = bboxes_img[batch_index, camera_index]
                centers_bci = centers_img[batch_index, camera_index]
                valid_projection_mask_bci = torch.any(bboxes_bci != 0, dim=-1) & torch.any(
                    centers_bci != 0, dim=-1
                )  # K
                mask_bci = mask_bi & valid_projection_mask_bci

                label_ids_bci_masked = label_ids[batch_index][mask_bci]  # [NUM_OBJS]
                bboxes_bci_masked = bboxes_bci[mask_bci]  # [NUM_OBJS, 4]
                centers_bci_masked = centers_bci[mask_bci]  # [NUM_OBJS, 2]

                # Ignore boxes are received as masked-out regular boxes that have a valid size.
                ignore_box_mask_bci = ~mask_bi & valid_projection_mask_bci
                ignore_boxes_bci_masked = bboxes_bci[ignore_box_mask_bci]  # [NUM_IGNORES, 4]

                # Occlusion handling
                visibility_mask = torch.tensor(get_visibility_mask(bboxes_bci_masked.cpu().numpy(), *height_width))
                bboxes_bci_masked = bboxes_bci_masked[visibility_mask]
                centers_bci_masked = centers_bci_masked[visibility_mask]
                label_ids_bci_masked = label_ids_bci_masked[visibility_mask]

                label_ids_per_frame.append(label_ids_bci_masked)
                bboxes_per_frame.append(bboxes_bci_masked)
                centers_per_frame.append(centers_bci_masked)
                ignore_boxes_per_frame.append(ignore_boxes_bci_masked)

            label_ids_per_frame_per_camera.append(label_ids_per_frame)
            bboxes_per_frame_per_camera.append(bboxes_per_frame)
            centers_per_frame_per_camera.append(centers_per_frame)
            ignore_boxes_per_frame_per_camera.append(ignore_boxes_per_frame)

        # TODO: Migrate to data preprocessing
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/347539

        return SparseDetectionCameraGT(
            label_ids=label_ids_per_frame_per_camera,
            bboxes=bboxes_per_frame_per_camera,
            centers=centers_per_frame_per_camera,
            ignore_boxes=ignore_boxes_per_frame_per_camera,
        )

    def compute_loss(self, batch: Batch, predictions: Predictions) -> LossOutput:
        """Compute the loss for the given prediction and target data.."""
        streampetr_gt = build_gt_boxes_per_frame(
            label_ids=batch[fields.AMODAL_CUBOID_LABEL_IDS.name],
            centers_xyz=batch[fields.AMODAL_CUBOID_CENTERS.name],
            dimensions_lwh=batch[fields.AMODAL_CUBOID_DIMENSIONS.name],
            rotations_xyzw=torch.cat(  # Convert quaternion convention wxyz -> xyzw
                (
                    (rotations_wxyz := batch[fields.AMODAL_CUBOID_ROTATIONS_WXYZ.name])[..., 1:],
                    rotations_wxyz[..., :1],
                ),
                dim=-1,
            ),
            velocities_xy=batch[fields.AMODAL_CUBOID_VELOCITIES.name],
            masks=batch[fields.CUBOID_PADDING_MASK.name],
        )
        focal_head_gt = None if self._loss_fn.camera_head_losses is None else [self._build_focal_head_gt(batch)]
        gt = SparseDetectionGT(fusion=streampetr_gt, camera=focal_head_gt)
        return self._loss_fn(predictions, gt)


def tracking_target_access_function(batch: Batch) -> list[TrackingMetricsGroundTruth]:
    """Access function for the ground truth targets."""
    batch_size = len(batch[fields.AMODAL_CUBOID_CENTERS.name])
    gt_inputs = []
    for i in range(batch_size):
        mask = batch[fields.CUBOID_PADDING_MASK.name][i]
        gt_box_xyz = batch[fields.AMODAL_CUBOID_CENTERS.name][i, mask, :3]
        gt_labels = batch[fields.AMODAL_CUBOID_LABEL_IDS.name][i, mask].unsqueeze(-1)
        gt_inputs.append(
            TrackingMetricsGroundTruth(
                gt_obj_ids=batch[fields.TRACKING_IDS.name][i, mask],
                gt_boxes=torch.cat((gt_box_xyz, gt_labels), dim=-1),
                frame_id=batch[fields.FRAME_IDX.name][i].item(),
                video_name=batch[fields.SCENE_TOKEN.name][i],
            )
        )
    return gt_inputs
