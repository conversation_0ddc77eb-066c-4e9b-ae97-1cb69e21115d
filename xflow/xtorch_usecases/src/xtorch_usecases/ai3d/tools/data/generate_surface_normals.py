#!/usr/bin/env python3

"""Script to generate surface normals maps for a parallax dataset."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
from pathlib import Path

import numpy as np
import torch
import torch.nn.functional as F
from PIL import Image
from pytorch_lightning import Trainer
from tqdm import tqdm

from xcontract.data.definitions.image import HW
from xcontract.data.definitions.usage import ValueKey
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.config.model import AI3DConfig, Config, get_multi_task_config
from xtorch_usecases.ai3d.data_modules.build_pyper import build_pyper_data_module
from xtorch_usecases.ai3d.data_postprocessing.camera.common import compute_camera_geometry_from_calibration
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, SpecificDataSources
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    OUTPUT_SUBSAMPLING_FACTOR,
    UNCROPPED_INPUT_RESOLUTION,
    CameraName,
)
from xtorch_usecases.ai3d.tools.data.common import AITopoCSVDataId
from xtorch_usecases.ai3d.utils.surface_normals import depth_to_surface_normal
from xtorch_usecases.common.helpers import get_gpu_specific_setting
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.build_pyper import load_dataset_as_data_frame


def _create_configuration(camera: CameraName, dataset_name: str | None = None) -> Config:
    """Create dataloader configuration to generate surface normals.

    Args:
        camera: Camera for which the surface normals must be generated.
        dataset_name: Name of the dataset for which the normal generation shall be triggered.

    Returns:
        Dataloader configuration.
    """
    config = get_multi_task_config()

    # Update config according to camera
    config.task_configs.parallax.camera_name = camera
    config = AI3DConfig(config).config

    config.task_configs.parallax.data_augmentation.extrinsic_rotation.enabled = False
    config.task_configs.parallax.data_augmentation.odometry_rotation.enabled = False
    config.task_configs.parallax.data_augmentation.color_augmentation.enabled = False
    config.task_configs.parallax.head_params.enabled_heads.surface_normals = False
    config.task_configs.parallax.head_params.enabled_heads.pose = False
    config.task_configs.parallax.inputs = {CSVDataId.SYS_CALIB: SpecificDataSources.CALIBRATION}
    config.task_configs.parallax.labels = {CSVDataId.GT_DEPTH: SpecificDataSources.DEPTH_MAP}
    if dataset_name is not None:
        config.data.dataset_name = dataset_name

    # TODO: remove unneeded data from config and dataloader
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/401708
    # TODO: optimize batch size/workers for every target node
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/401708
    config.data.batch_size_val = get_gpu_specific_setting(v100=32, a100=64, h100=96, default=2)
    config.data.num_workers_val = get_gpu_specific_setting(v100=16, a100=32, h100=32, default=2)
    return config


def _configure_no_split_dataset(config: Config) -> tuple[Path, int]:
    """Create a temporary CSV file where every split='test' and configure it as dataset.

    Args:
        config: Dataloader configuration.

    Returns:
        dataset_path_no_split: Path to the temporary CSV file.
        total_iterations: Total number of iterations for the dataloader.
    """
    print(f"Input dataset: {config.data.dataset_name}")
    dataset = load_dataset_as_data_frame(
        dataset_base_path=config.data.dataset_base_path,
        dataset_name=config.data.dataset_name,
        dataset_workspace=config.data.dataset_workspace,
    )

    dataset["split"] = "test"
    dataset_path_no_split = config.output_folder / f"{config.data.dataset_name.replace('.csv', '')}_no_split.csv"
    dataset_path_no_split.parent.mkdir(parents=True, exist_ok=True)
    dataset.to_csv(dataset_path_no_split, index=False)
    print(f"Input CSV has been copied to temporary CSV where all frames have split='test': {dataset_path_no_split}")

    # Set dataset to no-split CSV file stored in local path
    config.data.dataset_name = str(dataset_path_no_split.name)
    config.data.dataset_base_path = dataset_path_no_split.parent
    config.data.dataset_workspace = None
    total_iterations = int(np.ceil(len(dataset) / config.data.batch_size_val))
    return dataset_path_no_split, total_iterations


def _get_valid_normal_paths(batch: OnDeviceBatchedTrainingDict) -> tuple[list[Path], torch.Tensor]:
    """Get valid surface normal paths and corresponding indices from the batch.

    Args:
        batch: Batch containing ground truth depth maps and camera calibration data.

    Returns:
        valid_normal_paths: List of paths to valid surface normal files.
        valid_indices: Indices of valid depth maps in the batch.
    """
    depth_paths = batch[CSVDataId.GT_DEPTH][ValueKey.IDENTIFIER]
    is_valid = batch[CSVDataId.GT_DEPTH][ValueKey.VALID]
    assert isinstance(is_valid, torch.Tensor)
    assert isinstance(depth_paths, np.ndarray)

    valid_indices = torch.where(is_valid)[0]
    valid_depth_paths = depth_paths[valid_indices.cpu().numpy().tolist()]
    assert isinstance(valid_depth_paths, np.ndarray)

    # Surface normal paths are consistent with CsvSplitter._add_surface_normals_path()
    valid_normal_paths = [
        Path(path.replace("DEPTH", AITopoCSVDataId.SURFACE_NORMALS.value).replace(".png", ".npy"))
        for path in valid_depth_paths
    ]
    return valid_normal_paths, valid_indices


MIN_DEPTH = 0.6  # m
MAX_DEPTH = 150.0  # m


def _compute_surface_normals(
    batch: OnDeviceBatchedTrainingDict, valid_indices: torch.Tensor, normals_map_shape: HW
) -> torch.Tensor:
    """Compute surface normals from batch.

    Args:
        batch: Batch containing ground truth depth maps and camera calibration data.
        valid_indices: Indices of valid depth maps in the batch.
        normals_map_shape: Shape of the surface normals map to be generated.

    Returns:
        Surface normals for the valid depth maps, as a Tensor[batch_size, height, width, channels].
    """
    depth_map = batch[CSVDataId.GT_DEPTH][ValueKey.DATA].depth_map  # type: ignore[reportAttributeAccessIssue]
    assert isinstance(depth_map, torch.Tensor)
    valid_depth_map = depth_map[valid_indices]
    _, _, height, width = valid_depth_map.shape
    view = ViewParameters(crop_u=0, crop_v=0, crop_height=height, crop_width=width, scale=1 / OUTPUT_SUBSAMPLING_FACTOR)
    camera_geometry_net_output = compute_camera_geometry_from_calibration(
        calibration=batch[CSVDataId.SYS_CALIB], view=view
    )
    if camera_geometry_net_output is None:
        msg = "Camera geometry net output is None. Check calibration data."
        raise ValueError(msg)
    downsampled_depth_map = F.interpolate(
        valid_depth_map, size=(normals_map_shape.height, normals_map_shape.width), mode="nearest"
    )
    surface_normals = depth_to_surface_normal(
        downsampled_depth_map, camera_geometry_net_output.light_rays, MIN_DEPTH, MAX_DEPTH
    )
    return surface_normals.permute(0, 2, 3, 1)  # convert from BCHW to BHWC format for saving as numpy files


def _generate_surface_normals(
    camera: CameraName, dataset_name: str | None = None, *, overwrite: bool = False, rgb_dump: bool = False
) -> None:
    """Generate ground truth surface normals from ground truth depth maps of the AITopo dataset.

    Args:
        camera (CameraName): Camera for which the surface normals must be generated.
        dataset_name (str | None): Name of the dataset for which the normal generation shall be triggered. If not
            provided, the default dataset for the specified camera is used.
        overwrite (bool): If True, overwrite existing surface normals files.
        rgb_dump (bool): If True, dump RGB images of surface normals in addition to numpy files.
    """
    config = _create_configuration(camera, dataset_name)
    dataset_path_no_split, total_iterations = _configure_no_split_dataset(config)
    print(f"Dataloader configuration: {config}")

    data_module = build_pyper_data_module(config, pipeline_step=PipelineStep.EVALUATE_TORCH)
    data_module.trainer = Trainer()
    data_module.setup("")
    normals_map_shape = UNCROPPED_INPUT_RESOLUTION[camera] // OUTPUT_SUBSAMPLING_FACTOR

    print(f"Overwrite existing surface normal files: {overwrite}")
    print(f"RGB dump enabled: {rgb_dump}")

    for batch in tqdm(data_module.test_dataloader(), total=total_iterations):
        valid_normal_paths, valid_indices = _get_valid_normal_paths(batch)
        if not overwrite and all(path.exists() for path in valid_normal_paths):
            print(f"Skipping existing surface normals: {valid_normal_paths}")
            continue

        surface_normals = _compute_surface_normals(batch, valid_indices, normals_map_shape)
        for path, surface_normals_map in zip(valid_normal_paths, surface_normals.cpu().numpy()):
            print(f"Overwriting surface normal: {path}")

            Path.mkdir(path.parent, exist_ok=True)
            np.save(path, surface_normals_map)
            if rgb_dump:
                path_rgb = path.with_suffix(".png")
                # Map surface normals from [-1, 1] to [0, 255] for RGB visualization
                surface_normals_rgb = ((surface_normals_map + 1.0) * 255.0 / 2.0).astype(np.uint8)
                Image.fromarray(surface_normals_rgb).save(path_rgb)

    data_module.teardown("")
    if dataset_path_no_split.exists():
        dataset_path_no_split.unlink()
        print(f"Deleted temporary CSV file: {dataset_path_no_split}")


def main() -> None:
    """Main function to run the surface normals generation script."""
    parser = argparse.ArgumentParser(description=__doc__)
    parser.add_argument(
        "-c",
        "--camera",
        dest="camera",
        type=CameraName.from_str,
        required=True,
        help="Camera for which the surface normals must be generated. Must be consistent with dataset_name.",
    )
    parser.add_argument(
        "-d",
        "--dataset-name",
        dest="dataset_name",
        type=str,
        default=None,
        help="""Name of the dataset for which the normal generation is triggered. For local runs, this must be a CSV
        file contained in config.data.dataset_base_path. For Azure ML runs, this must be the name of a data asset
        in the viper_c3d workspace: https://ml.azure.com/data?wsid=/subscriptions/c4f1c7f3-9206-409f-a333-5b89a516e5dd/resourceGroups/vdeep-ct-prod/providers/Microsoft.MachineLearningServices/workspaces/viper_c3d&tid=a6c60f0f-76aa-4f80-8dba-092771d439f0.
        If not provided, the default dataset for the specified camera is used.""",
    )
    parser.add_argument(
        "-o", "--overwrite", action="store_true", default=False, help="Overwrite existing surface normals files."
    )
    parser.add_argument(
        "-r",
        "--rgb-dump",
        action="store_true",
        default=False,
        help="Dump RGB images of surface normals in addition to numpy files.",
    )
    args = parser.parse_args()

    _generate_surface_normals(args.camera, args.dataset_name, overwrite=args.overwrite, rgb_dump=args.rgb_dump)


if __name__ == "__main__":
    main()
