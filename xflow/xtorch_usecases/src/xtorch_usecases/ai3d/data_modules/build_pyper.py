"""Build PyPerDataModule to load dataset for the adapted parallax task configuration."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import functools
import time
from collections.abc import Mapping
from typing import Any, Protocol, runtime_checkable

import numpy as np
import numpy.typing as npt
import torch

from conversion.qnn.utils.dtype import cast_calib
from data_formats.camera.image_conversion.rgb_to_yuv import rgb8_to_yuv10, rgb_to_yuv_qc_uint16
from data_formats.camera.pyper.conversion import Color, Depth, ImageInputConfig
from data_formats.camera.pyper.ignorer import ignorer, yuv420_nv12_ignorer
from data_formats.camera.pyper.processing import adjuster, inflator
from data_formats.common.size_modification.image import crop_pad, crop_pad_yuv420_nv12
from pyper import dict_pipeline
from pyper.dict_pipeline import OffsetGenerator, TaskProcessingConfig
from pyper.multi_task.dataset_input import DatasetInputIterable
from xcontract.data.definitions.image import HW, AvailableImageTypes, YUV420ImageQC, YUV444Image
from xcontract.data.definitions.usage import Usage
from xtorch_usecases.ai3d.config.schema import Ai3dDataModuleConfig, Config
from xtorch_usecases.ai3d.config.usecase import CameraDataConfig
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import (
    PipelineWithPostProcessingParams,
    PostProcessableDataModule,
    PostProcessingTask,
    PostProcessingTasks,
)
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import ParallaxConfig
from xtorch_usecases.ai3d.tasks.parallax.task import PARALLAX_TASK_ID
from xtorch_usecases.ai3d.utils.pipeline_mappings import PIPELINE_TO_DATA_MODULE_USAGES
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.build_pyper import (
    build_input_iterable,
    load_dataset_as_data_frame,
)

# WORKAROUND: The PyPer azure downloader expects all training data (blobs) to reside in the `cache` container.
# Unfortunately, the data used by the AI3D parallax use case resides in the `datasets` container. So, the internal
# Pyper cache path is overwritten to enable the blob download during training of the ai3d parallax net (single task).
dict_pipeline._TRAINING_CACHE = "datasets"  # noqa: SLF001

_usage_to_iterable_name: dict[Usage, str] = {
    Usage.TRAINING: "train",
    Usage.VALIDATION: "val",
    Usage.TEST: "test",
}


def _configuration_for_image_processing(
    data_id: str, image_data_config: CameraDataConfig
) -> TaskProcessingConfig[npt.NDArray[np.float32]]:
    """Get the pyper processing configuration for loading the image data.

    Args:
        data_id: The ID of the image input.
        image_data_config: The configuration of the image data.

    Returns:
        The pyper processing configuration for loading the image data.
    """
    if image_data_config.stored_image_type == AvailableImageTypes.RGB:
        rgb_to_loaded_type_processing_config = {
            AvailableImageTypes.YUV444: TaskProcessingConfig(
                task_id=data_id,
                inflator=functools.partial(
                    inflator,
                    input_config=ImageInputConfig(
                        color=Color.RGB,
                        channel_0_depth=Depth.EIGHT,
                        channel_1_depth=Depth.EIGHT,
                        channel_2_depth=Depth.EIGHT,
                        postprocessor=rgb8_to_yuv10,
                    ),
                ),
                ignorer=functools.partial(ignorer, shape=image_data_config.ignorers_image_size),
                adjuster=functools.partial(
                    adjuster,
                    crop_pad=crop_pad,
                    padding_values=YUV444Image.padding_values,
                    to_channels_first=True,
                    cast=lambda x: x.astype(YUV444Image.dtype),
                ),
                formatter=lambda x: x[0],
            ),
            AvailableImageTypes.YUV420_QC: TaskProcessingConfig(
                task_id=data_id,
                inflator=functools.partial(
                    inflator,
                    input_config=ImageInputConfig(
                        color=Color.RGB,
                        channel_0_depth=Depth.EIGHT,
                        channel_1_depth=Depth.EIGHT,
                        channel_2_depth=Depth.EIGHT,
                        postprocessor=rgb_to_yuv_qc_uint16,
                    ),
                ),
                ignorer=functools.partial(
                    yuv420_nv12_ignorer,
                    shape=image_data_config.ignorers_image_size,
                ),
                adjuster=functools.partial(
                    adjuster,
                    cast=cast_calib,  # needed for selecting the upper 10bits
                    crop_pad=crop_pad_yuv420_nv12,
                    padding_values=YUV420ImageQC.padding_values,
                    to_channels_first=False,
                ),
                formatter=lambda x: x[0],
            ),
        }

        return rgb_to_loaded_type_processing_config[image_data_config.loaded_image_type]

    error_msg = (
        f"Unsupported image type conversion from {image_data_config.stored_image_type} to "
        f"{image_data_config.loaded_image_type}."
    )
    raise NotImplementedError(error_msg)


@runtime_checkable
class PostProcessable(Protocol):
    """A protocol for a class supporting the data batch post-processing."""

    def post_processing_config(
        self, pipeline_step: PipelineStep, crop_offset: HW, target_size: HW
    ) -> Mapping[Usage, PostProcessingTasks]:
        """Return the post processing task configuration."""
        raise NotImplementedError


def build_pyper_data_module(
    config: Config, pipeline_step: PipelineStep
) -> PostProcessableDataModule[Ai3dDataModuleConfig, Any]:
    """Build a PostProcessableDataModule for the provided configuration and enabled tasks.

    Note:
        This function is a workaround to adapt the AI3D parallax usecase to the PyPerDataModule used by the
    multitask single camera usecase, since it allows only image inputs and only one label entry per task.
    This function shall be removed once the AI3D task is fully integrated into the multi-task single-camera
    framework.
    https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/338526

    Args:
        config: The multitask configuration for which to create the data module.
        pipeline_step: The current step in the pipeline being executed.
    """

    dataset = load_dataset_as_data_frame(
        dataset_base_path=config.data.dataset_base_path,
        dataset_name=config.data.dataset_name,
        dataset_workspace=config.data.dataset_workspace,
    )
    # TODO: Workaround to use IMU data, which is contained in the same file as that for the prior. Remove this
    # workaround once the AI3D dataset contains the correct entries for pose ground-truth.
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/387106
    dataset[CSVDataId.GT_CURRENT_POSE] = dataset[CSVDataId.CURRENT_POSE]
    dataset[CSVDataId.GT_PREVIOUS_POSE] = dataset[CSVDataId.PREVIOUS_POSE]

    additional_inputs = []
    additional_inputs_processing = []
    task_labels = []
    task_labels_processing = []
    input_config = config.inputs[pipeline_step]

    input_size_config = {
        (
            config.cropping_in_post_processing,
            config.ignorers_image_size,
            config.target_image_size,
            config.cropping_offset,
        )
        for config in input_config.values()
    }
    assert len(input_size_config) == 1, "All input configurations must have the same cropping and target size."
    cropping_in_post_processing, ignorer_shape, target_size, crop_offset = input_size_config.pop()

    for task_id, task_config in config.multi_task_collection.enabled_task_to_config.items():
        if task_id == PARALLAX_TASK_ID:
            assert isinstance(task_config, ParallaxConfig)
            data_processing_config = task_config.data_processing_config(ignorer_shape)
            additional_inputs += list(task_config.inputs.keys())
            additional_inputs_processing += [
                data_processing_config[data_type](data_id) for data_id, data_type in task_config.inputs.items()
            ]
            task_labels += list(task_config.labels.keys())
            task_labels_processing += [
                data_processing_config[data_type](data_id) for data_id, data_type in task_config.labels.items()
            ]
        else:
            task_labels.append(task_id)
            task_labels_processing.append(task_config.data_identifier or task_id)

    input_ids = config.input_ids + additional_inputs
    active_tasks = input_ids + task_labels
    processing_configs = (
        [_configuration_for_image_processing(data_id, data_config) for data_id, data_config in input_config.items()]
        + additional_inputs_processing
        + task_labels_processing
    )

    assert len(active_tasks) == len(processing_configs)

    task_id_to_dataset_column = {t: str(t) for t in active_tasks}

    usages = PIPELINE_TO_DATA_MODULE_USAGES[pipeline_step]
    iterables: dict[Usage, DatasetInputIterable] = {}
    for usage in usages:
        iterables[usage] = build_input_iterable(
            dataset,
            active_tasks=active_tasks,
            task_id_to_dataset_column=task_id_to_dataset_column,
            input_ids=input_ids,
            target_data_ratio={"depth": 1.0},
            usage=usage,
            data_base_path=config.data.data_base_path,
            seed=config.data.data_seed or time.time_ns(),
        )

    pyper_offset_generator: OffsetGenerator | None = (
        None
        if cropping_in_post_processing
        else lambda data: (dict.fromkeys(data, crop_offset), dict.fromkeys(data, target_size))
    )
    post_processing_offset = crop_offset if cropping_in_post_processing else HW(0, 0)

    post_processing_configs: dict[Usage, list[PostProcessingTask]] = {usage: [] for usage in usages}
    for task in config.multi_task_collection.enabled_tasks:
        if isinstance(task, PostProcessable):
            task_post_procesing_configs = task.post_processing_config(
                pipeline_step, crop_offset=post_processing_offset, target_size=target_size
            )
            for usage in usages:
                post_processing_configs[usage].extend(task_post_procesing_configs[usage])

    pipeline_params: dict[str, PipelineWithPostProcessingParams[Any]] = {
        f"{_usage_to_iterable_name[usage]}_pipeline_params": PipelineWithPostProcessingParams(
            input_iterable=input_iterable,
            processing_configs=processing_configs,
            post_processing_tasks=post_processing_configs[usage],
            offset_generator=pyper_offset_generator,
            target_device=torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu"),
        )
        for usage, input_iterable in iterables.items()
    }

    if Usage.TEST in usages:
        test_pipeline_params_name = f"{_usage_to_iterable_name[Usage.TEST]}_pipeline_params"
        pipeline_params["predict_pipeline_params"] = pipeline_params[test_pipeline_params_name]

    pyper_data_module = PostProcessableDataModule(
        config=config.data,
        **pipeline_params,
    )
    return pyper_data_module
