"""This module provides the implementation of the BuildHDF5DataModule class."""

from __future__ import annotations

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections.abc import Iterable, MutableMapping, Sequence
from dataclasses import dataclass
from typing import Any, Generic

import numpy as np
import torch
from omegaconf import MISSING
from typing_extensions import Self

from data_formats.camera.image_conversion.yuv420_to_yuv444 import convert_yuv420_2p_to_yuv444
from xcontract.camera_models.definitions import CameraModelType, IntrinsicIndex
from xcontract.data.definitions.usage import Usage, ValueKey
from xtorch.geometry.transformation.pose_transformation import PoseTransformation
from xtorch.geometry.transformation.quaternion import Quaternion
from xtorch.training.data_module.base import DataConfigTypeT, DataModule, DataModuleConfig
from xtorch.training.data_module.pyper import OnDeviceBatchedTrainingDict
from xtorch.training.data_module.pyper.data_module import T
from xtorch.training.data_module.pyper.pipeline import _to_tensors
from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.config.schema import Ai3dDataModuleConfig
from xtorch_usecases.ai3d.data_formats.camera_calibration.data_loading import (
    CameraCalibrationNumpyData,
    CameraMetadataIndex,
)
from xtorch_usecases.ai3d.data_formats.vehicle_odometry.data_loading import VehicleOdometryNumpyData
from xtorch_usecases.ai3d.data_postprocessing.camera.common import (
    ImageMetaData,
    process_camera_height,
    process_masked_warping_grid,
)
from xtorch_usecases.ai3d.data_postprocessing.odometry.common import _IsometryData, process_4d_pose
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import OUTPUT_SUBSAMPLING_FACTOR, DataId
from xtorch_usecases.ai3d.utils.hdf_aos_reader import AOSHDF5Reader


@dataclass(kw_only=True)
class Hdf5DataModuleConfig(Ai3dDataModuleConfig):
    """Config parameters for the HDF5 data module used for inference."""

    # The topics in the HDF5 file that are used for the data loading.
    metadata_topic: str = MISSING  # containing the image pyramid resolutions, timestamps, frame counter etc.
    image_topic: str = MISSING  # containing the image data of the desired pyramid layer
    odometry_topic: str = MISSING  # containing the odometry data
    extrinsic_topic: str = MISSING  # containing the extrinsic transformation from the vehicle to the sensor
    intrinsic_topic: str = MISSING  # containing the intrinsic camera parameters
    pyramid_layer: int = 0  # the pyramid layer to be used for the image data
    camera_type: str = MISSING


class Hdf5InputContainer(OnDeviceBatchedTrainingDict):
    """Container class fulfilling the TransferableContainer protocol."""

    def to(self, device: torch.device | str) -> Self:
        """Transfers the data to the specified device."""
        return Hdf5InputContainer._to_device(self, device)

    @staticmethod
    def _to_device(data: Any, device: torch.device | str) -> Any:
        """Recursively transfer the data to the specified device."""
        if isinstance(data, torch.Tensor):
            return data.to(device)
        if isinstance(data, MutableMapping):
            for key, value in data.items():
                data[key] = Hdf5InputContainer._to_device(value, device)
        elif isinstance(data, tuple):
            if hasattr(data, "_fields"):
                return type(data)(*(Hdf5InputContainer._to_device(ele, device) for ele in data))
            return tuple(Hdf5InputContainer._to_device(value, device) for value in data)
        else:
            msg = f"Unsupported data type {type(data)}"
            raise TypeError(msg)

        return data


class HDF5DataModule(DataModule[Hdf5DataModuleConfig, OnDeviceBatchedTrainingDict], Generic[DataConfigTypeT, T]):
    """DataModule for building an HDF5 dataset usable for inference."""

    def __init__(self, config: Hdf5DataModuleConfig) -> None:
        """Initializes the HDF5 data module."""
        super().__init__(config)

        self._config = config

        # Create instance of the AOS hdf file reader that needs the
        # path to the hdf5 file and the recording topics as arguments.
        # The reader object itself is iterable and returns the images in yuv420
        # and the poses as absolute poses.
        hdf_file_path = config.data_base_path / config.dataset_name
        self._hdf5_reader = AOSHDF5Reader(
            hdf_file_path=hdf_file_path,
            metadata_topic=config.metadata_topic,
            image_topic=config.image_topic,
            odometry_topic=config.odometry_topic,
            extrinsic_topic=config.extrinsic_topic,
            intrinsic_topic=config.intrinsic_topic,
            pyramid_layer=config.pyramid_layer,
        )

        self._target_device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")

    def __iter__(self) -> HDF5DataModule[DataConfigTypeT, T]:
        """Returns the iterator object itself."""
        return self

    def __len__(self) -> int:
        """Returns the number of image frame pairs in the dataset."""
        return len(self._hdf5_reader)

    @property
    def config(self) -> Hdf5DataModuleConfig:
        """Returns the HDF5 specific configuration of the data module."""
        return self._config

    def process_camera_calibration(
        self,
        sample_data: OnDeviceBatchedTrainingDict,
        intrinsic_data_dict: dict[str, Any],
        extrinsic_dict_data: dict[str, Any],
    ) -> None:
        """Creates the camera calibration data from the intrinsic and extrinsic parameters.

        Args:
            sample_data (OnDeviceBatchedTrainingDict): The sample data dictionary.
            intrinsic_data_dict (dict[str, Any]): The intrinsic camera parameters.
            extrinsic_dict_data (dict[str, Any]): The extrinsic camera parameters.

        Returns:
            None
        """
        extrinsic_rotation_data = np.array(extrinsic_dict_data["rotation"], dtype=np.float32)[np.newaxis, ...]
        extrinsic_translation_data = np.array(extrinsic_dict_data["translation"], dtype=np.float32)[np.newaxis, ...]

        intrinsic_data = np.zeros(5, dtype=np.float32)
        intrinsic_data[IntrinsicIndex.fu.value] = intrinsic_data_dict["focal_length"][0]
        intrinsic_data[IntrinsicIndex.fv.value] = intrinsic_data_dict["focal_length"][1]
        intrinsic_data[IntrinsicIndex.u0.value] = intrinsic_data_dict["principal_point"][0]
        intrinsic_data[IntrinsicIndex.v0.value] = intrinsic_data_dict["principal_point"][1]

        camera_type = CameraModelType[self._config.camera_type]
        if camera_type == CameraModelType.DEFORMED_CYLINDER:
            assert abs(intrinsic_data_dict["cut_angle_lower"]) == abs(intrinsic_data_dict["cut_angle_upper"])
            intrinsic_data[IntrinsicIndex.cut_angle.value] = abs(intrinsic_data_dict["cut_angle_lower"])
        # Add batch dimension
        intrinsic_data = np.expand_dims(intrinsic_data, axis=0)

        camera_metadata = np.zeros(3, dtype=np.int32)
        camera_metadata[CameraMetadataIndex.camera_type.value] = camera_type.value
        camera_metadata[CameraMetadataIndex.camera_height.value] = intrinsic_data_dict["image_dimensions"][1]
        camera_metadata[CameraMetadataIndex.camera_width.value] = intrinsic_data_dict["image_dimensions"][0]
        # Add batch dimension
        camera_metadata = np.expand_dims(camera_metadata, axis=0)

        syscalib = CameraCalibrationNumpyData(
            extrinsic_rotation_data,
            extrinsic_translation_data,
            intrinsic_data,
            camera_metadata,
        )

        sample_data[CSVDataId.SYS_CALIB][ValueKey.DATA] = _to_tensors(syscalib, self._target_device)
        sample_data[CSVDataId.SYS_CALIB][ValueKey.VALID] = torch.as_tensor(data=[True])

    def __next__(self) -> OnDeviceBatchedTrainingDict:
        """Returns the next item in the iterator.

        This method computes the relative odometry transformation between the current image and the reference image.
        It also returns the reference and current YUV444 images, as well as the extrinsic transformation from the
        vehicle to the sensor.

        Returns:
            OnDeviceBatchedTrainingDict: A struct containing one data sample for inference
        """

        def process_camera_odometry(
            reference_frame: VehicleOdometryNumpyData,
            current_frame: VehicleOdometryNumpyData,
            vehicle_extrinsic: VehicleOdometryNumpyData,
        ) -> PoseTransformation:
            """Calculates the relative odometry transformation between the current image and the reference image.

            Args:
                reference_frame (VehicleOdometryNumpyData): The odometry data of the reference frame.
                current_frame (VehicleOdometryNumpyData): The odometry data of the current frame.
                vehicle_extrinsic (VehicleOdometryNumpyData): The extrinsic transformation from the vehicle
                to the sensor.

            Returns:
                PoseTransformation: The relative odometry transformation between the
                current image and the reference image.
            """
            # Vehicle odometry of the vehicle at the reference frame
            reference_frame_rotation = Quaternion(torch.as_tensor(reference_frame.rotation))
            reference_frame_translation = torch.as_tensor(reference_frame.translation)
            reference_frame_odometry = PoseTransformation(reference_frame_rotation, reference_frame_translation)

            # Vehicle odometry of the vehicle at the current frame
            current_frame_rotation = Quaternion(torch.as_tensor(current_frame.rotation))
            current_frame_translation = torch.as_tensor(current_frame.translation)
            current_frame_odometry = PoseTransformation(current_frame_rotation, current_frame_translation)

            # Extrinsic transformation from the vehicle to the sensor
            vehicle_from_sensor_extrinsic = PoseTransformation(
                Quaternion(torch.as_tensor(vehicle_extrinsic.rotation)),
                torch.as_tensor(vehicle_extrinsic.translation),
            )

            # Relative pose between the frames in vehicle coordinate system
            current_from_reference_vehicle_transformation = current_frame_odometry.inverse() * reference_frame_odometry

            # Relocate the pose to the camera coordinate system (as defined by the extrinsic transformation)
            camera_previous_from_camera_current = current_from_reference_vehicle_transformation.relocated(
                vehicle_from_sensor_extrinsic
            )
            return camera_previous_from_camera_current

        with torch.device(self._target_device):
            hdf5_data_sample = next(self._hdf5_reader)

            reference_yuv420_image = hdf5_data_sample.reference_yuv420_image
            current_yuv420_image = hdf5_data_sample.current_yuv420_image

            # Extract meta data later used for HDF5 export
            current_frame_meta = ImageMetaData(
                timestamp=torch.tensor([current_yuv420_image.timestamp], dtype=torch.int64).unsqueeze(0),
                frame_number=torch.tensor([current_yuv420_image.counter], dtype=torch.int64).unsqueeze(0),
            )

            reference_frame_meta = ImageMetaData(
                timestamp=torch.tensor([reference_yuv420_image.timestamp], dtype=torch.int64).unsqueeze(0),
                frame_number=torch.tensor([reference_yuv420_image.counter], dtype=torch.int64).unsqueeze(0),
            )

            # Convert the images to yuv444 and make them torch data types
            reference_yuv444_image = torch.as_tensor(
                [convert_yuv420_2p_to_yuv444(reference_yuv420_image.y_plane, reference_yuv420_image.uv_plane)],
                dtype=torch.float32,
            )

            current_yuv444_image = torch.as_tensor(
                [convert_yuv420_2p_to_yuv444(current_yuv420_image.y_plane, current_yuv420_image.uv_plane)],
                dtype=torch.float32,
            )

            # Convert the image from HWC to CHW and add a batch dimension
            reference_yuv444_image = reference_yuv444_image.permute(0, 3, 1, 2)
            current_yuv444_image = current_yuv444_image.permute(0, 3, 1, 2)

            # Calculate the relative odometry between the two frames
            reference_frame_odometry = VehicleOdometryNumpyData(
                timestamp=np.array([reference_yuv420_image.timestamp]),
                rotation=hdf5_data_sample.reference_frame_odometry["rotation"],
                translation=hdf5_data_sample.reference_frame_odometry["translation"],
            )
            current_frame_odometry = VehicleOdometryNumpyData(
                timestamp=np.array([current_yuv420_image.timestamp]),
                rotation=hdf5_data_sample.current_frame_odometry["rotation"],
                translation=hdf5_data_sample.current_frame_odometry["translation"],
            )
            vehicle_from_sensor_extrinsic = VehicleOdometryNumpyData(
                timestamp=np.array([current_yuv420_image.timestamp]),
                rotation=hdf5_data_sample.vehicle_from_sensor_extrinsic["rotation"],
                translation=hdf5_data_sample.vehicle_from_sensor_extrinsic["translation"],
            )
            camera_previous_from_camera_current = process_camera_odometry(
                reference_frame_odometry, current_frame_odometry, vehicle_from_sensor_extrinsic
            )

            # Prepare the data sample with empty dicts for all key we have to fill
            required_keys = [
                DataId.PREVIOUS_IMAGE,
                DataId.CURRENT_IMAGE,
                DataId.CURRENT_IMAGE_META,
                DataId.PREVIOUS_IMAGE_META,
                DataId.PRIOR_POSE_TRANSFORMATION,
                DataId.PRIOR_POSE,
                CSVDataId.SYS_CALIB,
                DataId.CAMERA_HEIGHT,
                DataId.MASKED_WARPING_GRID,
            ]

            sample_data = {key: {ValueKey.DATA: None, ValueKey.VALID: False} for key in required_keys}
            # Set the two images for inference
            sample_data[DataId.PREVIOUS_IMAGE][ValueKey.DATA] = reference_yuv444_image
            sample_data[DataId.PREVIOUS_IMAGE][ValueKey.VALID] = torch.as_tensor(
                data=[reference_yuv444_image is not None]
            )
            sample_data[DataId.CURRENT_IMAGE][ValueKey.DATA] = current_yuv444_image
            sample_data[DataId.CURRENT_IMAGE][ValueKey.VALID] = torch.as_tensor(data=[current_yuv444_image is not None])

            # Set the meta data for the images
            sample_data[DataId.PREVIOUS_IMAGE_META][ValueKey.DATA] = reference_frame_meta
            sample_data[DataId.PREVIOUS_IMAGE_META][ValueKey.VALID] = torch.as_tensor(data=[True])
            sample_data[DataId.CURRENT_IMAGE_META][ValueKey.DATA] = current_frame_meta
            sample_data[DataId.CURRENT_IMAGE_META][ValueKey.VALID] = torch.as_tensor(data=[True])

            # Prepare the camera pose transformation in 6D later used to derive the 4D input pose
            # Ensure that the tensors gets a batch dimension by unsqueezeing it on first dimension.
            sample_data[DataId.PRIOR_POSE_TRANSFORMATION][ValueKey.DATA] = _IsometryData(
                camera_previous_from_camera_current.rotation.normalized().tensor.unsqueeze(0),
                camera_previous_from_camera_current.translation.unsqueeze(0),
            )
            sample_data[DataId.PRIOR_POSE_TRANSFORMATION][ValueKey.VALID] = torch.as_tensor(data=[True])

            sample_data = Hdf5InputContainer(**sample_data)

            # DataId.PRIOR_POSE is the 4D pose (translation, yaw) of the camera really used for the network input
            # The function call updated the sample_data inplace and added the 4D pose to the sample_data
            process_4d_pose(
                sample_data, input_pose_id=DataId.PRIOR_POSE_TRANSFORMATION, output_4d_pose_id=DataId.PRIOR_POSE
            )
            sample_data[DataId.PRIOR_POSE][ValueKey.VALID] = torch.as_tensor(data=[True])

            # Add the warping grid for the homograhy. The function call relies on an already set 6D pose
            # (PRIOR_POSE_TRANSFORMATION), and a camera height (CAMERA_HEIGHT) in the sample_data and
            # the syscal data (CSVDataId.SYS_CALIB). We prepare these inputs and then reuse the function
            # that in-place updates the sample dict accordingly. CAMERA_HEIGHT comes from the SYS_CALIB
            # anyway and is therefore calculated first.
            intrinsic_data = self._hdf5_reader.get_intrinsic_data()
            self.process_camera_calibration(sample_data, intrinsic_data, self._hdf5_reader.get_extrinsic_data())
            process_camera_height(sample_data)
            sample_data[DataId.CAMERA_HEIGHT][ValueKey.VALID] = torch.as_tensor(data=[True])

            warping_grid_view = ViewParameters(
                crop_u=0,
                crop_v=0,
                crop_width=intrinsic_data["image_dimensions"][0],
                crop_height=intrinsic_data["image_dimensions"][1],
                scale=1.0 / (2 * OUTPUT_SUBSAMPLING_FACTOR),
            )
            process_masked_warping_grid(sample_data, warping_grid_view)
            sample_data[DataId.MASKED_WARPING_GRID][ValueKey.VALID] = torch.as_tensor(data=[True])

        # Construct the data sample containing all data later needed for inference
        return sample_data

    def train_dataloader(self) -> Iterable[OnDeviceBatchedTrainingDict]:
        """Constructs the pytorch training iterable based on the train dataset.

        Returns:
            The generic training iterable.
        """
        msg = "Training is not supported for the HDF5 data module."
        raise NotImplementedError(msg)

    def val_dataloader(self) -> Iterable[OnDeviceBatchedTrainingDict]:
        """Constructs the pytorch validation iterable based on the validation dataset.

        Returns:
            The validation data iterable.
        """

        msg = "Validation is not supported for the HDF5 data module."
        raise NotImplementedError(msg)

    def test_dataloader(self) -> Iterable[OnDeviceBatchedTrainingDict]:
        """Constructs the pytorch test iterable based on the test dataset.

        Returns:
            The test data iterable.
        """
        return self

    def predict_dataloader(self) -> Iterable[OnDeviceBatchedTrainingDict]:
        """Constructs the pytorch predict iterable."""
        return self


def build_hdf5_data_module(
    config: Hdf5DataModuleConfig, usages: Sequence[Usage]
) -> HDF5DataModule[DataModuleConfig, OnDeviceBatchedTrainingDict]:
    """Builds the HDF5 data module for inference.

    Args:
        config (Hdf5DataModuleConfig): The configuration for the HDF5 data module.
        usages (Sequence[Usage]): The usages for which the data module is built.

    Returns:
        HDF5DataModule: The HDF5 data module for inference.
    """

    assert len(usages) > 0, "At least one usage must be specified."

    # Raise an error if the usage is not supported
    if not set(usages).issubset({Usage.VALIDATION, Usage.TEST}):
        msg = f"Unsupported usage {usages} for HDF5 data module."
        raise NotImplementedError(msg)

    return HDF5DataModule(config)
