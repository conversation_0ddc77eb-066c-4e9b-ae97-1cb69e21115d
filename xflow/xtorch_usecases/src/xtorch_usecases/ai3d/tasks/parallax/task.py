"""Parallax task implementation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Mapping
from pathlib import Path

import torch
from pytorch_lightning.callbacks import Callback

from xcontract.data.definitions.image import HW
from xcontract.data.definitions.usage import Usage, ValueKey
from xtorch.losses.meta.uncertainty_weighted_loss import SublossConfig, UncertaintyLossType
from xtorch.multi_task.structured_task import StructuredTask, SupportedData
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch.training.metric_handling import MetricRegistry
from xtorch_usecases.ai3d.callbacks.data_plotter_callback import AI3DDataPlotterCallback
from xtorch_usecases.ai3d.callbacks.hdf5_prediction_callback import Hdf5PredictionCallback
from xtorch_usecases.ai3d.config.usecase import UsecaseConfig
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import PostProcessingTask, PostProcessingTasks
from xtorch_usecases.ai3d.data_postprocessing.odometry.normalization import create_normalization_task
from xtorch_usecases.ai3d.data_postprocessing.task_map import setup_post_processing_tasks
from xtorch_usecases.ai3d.model.definitions import LossId
from xtorch_usecases.ai3d.network import ModelInputType
from xtorch_usecases.ai3d.tasks.parallax.data import DataGroupId, DataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    ParallaxConfig,
    ParallaxGroundTruth,
    ParallaxHeadInput,
    ParallaxHeadOutput,
)
from xtorch_usecases.ai3d.tasks.parallax.head import ParallaxHead
from xtorch_usecases.ai3d.tasks.parallax.loss import ParallaxLoss
from xtorch_usecases.common.pipeline import PipelineStep


class ParallaxTask(
    StructuredTask[
        ParallaxConfig,
        UsecaseConfig,
        ParallaxHeadInput,
        ModelInputType,
        ParallaxHeadOutput,
        ParallaxGroundTruth,
    ]
):
    """Implementation for the parallax task."""

    def __init__(self, config: ParallaxConfig, common_config: UsecaseConfig) -> None:
        """Initialize the task with a task-specific task config and common callback config."""

        assert config is not None
        assert common_config is not None

        self._config = config
        self._callback_config = common_config.callback_config

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Method to provide callbacks for the training and evaluation loop."""
        callbacks: list[Callback] = []
        if pipeline_step is PipelineStep.TRAIN:
            # Always plot the two images
            data_ids_to_log = [DataId.CURRENT_IMAGE, DataId.PREVIOUS_IMAGE]

            # If structure parameter head is enabled, also plot the masked structure parameter, depth and height maps
            if self.config.head_params.enabled_heads.structure_parameter:
                data_ids_to_log.extend([DataId.GT_STRUCTURE_PARAMETER_MAP, DataId.VALID_STRUCTURE_PARAMETER_MASK])
                data_ids_to_log.extend([DataId.GT_DEPTH_MAP, DataId.GT_HEIGHT_MAP])

            # If surface normals head is enabled, also plot the masked surface normals map
            if self.config.head_params.enabled_heads.surface_normals:
                data_ids_to_log.extend([DataId.GT_SURFACE_NORMALS_MAP, DataId.VALID_SURFACE_NORMALS_MASK])

            # If pose head is enabled, also plot the pose angles and translation
            if self.config.head_params.enabled_heads.pose:
                data_ids_to_log.extend([DataId.GT_POSE_ANGLE, DataId.GT_POSE_TRANSLATION])

            callbacks.append(
                AI3DDataPlotterCallback(
                    task_id=self.identifier(),
                    camera_name=self.config.camera_name,
                    data_ids_to_log=data_ids_to_log,
                    train_data_logging_frequency=self._callback_config.train_data_logging_frequency,
                    val_data_logging_frequency=self._callback_config.val_data_logging_frequency,
                    output_folder=Path(self._callback_config.output_root_dir / "plots"),
                )
            )

        if pipeline_step is PipelineStep.EVALUATE_TORCH:
            callbacks.append(
                Hdf5PredictionCallback(
                    enabled_heads=self._config.head_params.enabled_heads,
                    inference_source=self.config.inference_source,
                    output_folder=Path(self._callback_config.output_root_dir),
                    task_id=self.identifier(),
                    camera_name=self.config.camera_name,
                    export_parameters=self.config.export_parameters,
                )
            )

        return callbacks

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """Method to provide metrics for the training loop."""
        return []

    @property
    def head(self) -> ParallaxHead:
        """Task head that processes backbone outputs."""
        return ParallaxHead(self._config.head_params)

    @property
    def loss(self) -> ParallaxLoss:
        """Task specific loss."""
        return ParallaxLoss(self._config.loss_params, self._config.head_params.enabled_heads)

    @property
    def config(self) -> ParallaxConfig:
        """Task specific configuration."""
        return self._config

    def subloss_configs(self) -> dict[str, SublossConfig]:
        """Mapping between sublosses to the corresponding subloss configuration.

        Returns:
            A dictionary mapping sublosses to their subloss configuration.
        """
        sub_losses = {}
        if self._config.head_params.enabled_heads.structure_parameter:
            sub_losses[LossId.STRUCTURE_PARAMETER_LOSS] = SublossConfig(UncertaintyLossType.REGRESSION)

        if self._config.head_params.enabled_heads.pose:
            sub_losses[LossId.POSE_LOSS_X] = SublossConfig(UncertaintyLossType.REGRESSION)
            sub_losses[LossId.POSE_LOSS_Y] = SublossConfig(UncertaintyLossType.REGRESSION)
            sub_losses[LossId.POSE_LOSS_Z] = SublossConfig(UncertaintyLossType.REGRESSION)
            sub_losses[LossId.POSE_LOSS_YAW] = SublossConfig(UncertaintyLossType.REGRESSION)
            sub_losses[LossId.POSE_LOSS_PITCH] = SublossConfig(UncertaintyLossType.REGRESSION)
            sub_losses[LossId.POSE_LOSS_ROLL] = SublossConfig(UncertaintyLossType.REGRESSION)

        if self._config.head_params.enabled_heads.surface_normals:
            sub_losses[LossId.SURFACE_NORMALS_LOSS] = SublossConfig(UncertaintyLossType.REGRESSION)

        return sub_losses

    def post_processing_config(
        self, pipeline_step: PipelineStep, crop_offset: HW, target_size: HW
    ) -> Mapping[Usage, PostProcessingTasks]:
        """Return the post processing configuration for the task based on the pipeline step.

        This sequence of configurations processes the raw data after it is transferred to the the device and finally
        groups all required task labels into a single entry in the data batch with the required ground-truth format.

        Args:
            pipeline_step: The current pipeline step that determines the usages to include.
            crop_offset: The offset to be used for cropping the data to obtain the desired target size.
            target_size: The desired target size of the post-processed input data.

        Returns:
            A mapping of Usage to their corresponding post-processing tasks.
        """
        _required_data_groups = [DataGroupId.IMAGES, DataGroupId.PRIOR_POSE, DataGroupId.MASKED_WARPING_GRID]
        _data_labels = []

        if self._config.head_params.enabled_heads.structure_parameter:
            _required_data_groups.append(DataGroupId.GT_STRUCTURE_PARAMETER)
            _data_labels.extend(
                {DataId.GT_STRUCTURE_PARAMETER_MAP, DataId.VALID_STRUCTURE_PARAMETER_MASK, DataId.CURRENT_IMAGE}
            )

        if self._config.head_params.enabled_heads.surface_normals:
            _required_data_groups.append(DataGroupId.GT_SURFACE_NORMALS)
            _data_labels.extend(
                {DataId.GT_SURFACE_NORMALS_MAP, DataId.VALID_SURFACE_NORMALS_MASK, DataId.CURRENT_IMAGE}
            )

        if self._config.head_params.enabled_heads.pose:
            _required_data_groups.append(DataGroupId.GT_POSE)
            _data_labels.extend({DataId.GT_NORMALIZED_POSE_TRANSLATION, DataId.GT_NORMALIZED_POSE_ANGLE})

        _data_labels = list(dict.fromkeys(_data_labels))  # Remove duplicates while preserving order

        def _group_labels(batch: OnDeviceBatchedTrainingDict) -> None:
            task_labels: dict[str, torch.Tensor] = dict.fromkeys(ParallaxGroundTruth._fields, torch.empty(0))
            for label_id in _data_labels:
                label_data = batch[label_id][ValueKey.DATA]
                assert isinstance(label_data, torch.Tensor)
                task_labels[label_id.value] = label_data

            batch[PARALLAX_TASK_ID][ValueKey.DATA] = ParallaxGroundTruth(**task_labels)

        # Pose normalization tasks: defined separately because scales depend on configuration parameters
        pose_head_parameter = self._config.head_params.pose_head_parameter
        gt_pose_normalization_tasks = []
        if self._config.head_params.enabled_heads.pose:
            inverse_angle_scale = [1.0 / scale for scale in pose_head_parameter.angle_scale]
            gt_pose_normalization_tasks.append(
                create_normalization_task(
                    metric_id=DataId.GT_POSE_ANGLE,
                    normalized_id=DataId.GT_NORMALIZED_POSE_ANGLE,
                    inverse_scale=inverse_angle_scale,
                )
            )

            inverse_translation_scale = [1.0 / scale for scale in pose_head_parameter.translation_scale]
            gt_pose_normalization_tasks.append(
                create_normalization_task(
                    metric_id=DataId.GT_POSE_TRANSLATION,
                    normalized_id=DataId.GT_NORMALIZED_POSE_TRANSLATION,
                    inverse_scale=inverse_translation_scale,
                )
            )

        common_final_processing_tasks = (
            gt_pose_normalization_tasks
            + [PostProcessingTask(input_ids=_data_labels, output_id=PARALLAX_TASK_ID, processor=_group_labels)]
            if pipeline_step.is_torch
            else []
        )

        processing_tasks = {
            usage: list(processing_tasks) + common_final_processing_tasks
            for usage, processing_tasks in setup_post_processing_tasks(
                _required_data_groups, self._config.data_augmentation, pipeline_step, crop_offset, target_size
            ).items()
        }
        return processing_tasks


PARALLAX_TASK_ID = ParallaxTask.identifier()
