"""Parallax head definitions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Callable, Generator, Iterator
from dataclasses import dataclass, field, fields
from enum import IntEnum, auto, unique
from functools import partial
from types import MappingProxyType
from typing import Any, Final, NamedTuple

import torch

from pyper.dict_pipeline import TaskProcessingConfig
from xcontract.data.definitions.image import HW
from xtorch.multi_task.task_config import TaskSpecificConfig
from xtorch.nn.backbones.image.yolo_v4_tiny.yolo_v4_tiny import YoloV4TinyOutput as BackboneOutput
from xtorch_usecases.ai3d.data_formats.camera_calibration import processing_config as calibration_processing_config
from xtorch_usecases.ai3d.data_formats.depth_map import processing_config as depth_map_processing_config
from xtorch_usecases.ai3d.data_formats.surface_normals import processing_config as surface_normals_processing_config
from xtorch_usecases.ai3d.data_formats.vehicle_odometry import processing_config as odometry_processing_config
from xtorch_usecases.ai3d.model.decoder.pose_head import PoseHeadParams
from xtorch_usecases.ai3d.model.decoder.pwa_decoder import (
    FeaturemapFusionParams,
    PoseFusionParams,
    PWAttentionDecoderParams,
)
from xtorch_usecases.ai3d.model.decoder.structure_parameter_head import StructureParameterHeadParams
from xtorch_usecases.ai3d.model.decoder.surface_normals_head import SurfaceNormalsHeadParams
from xtorch_usecases.ai3d.model.definitions import POSE_LOSS_DOFS, DecoderId, FeatureFusionId, LossId, PoseFusionId
from xtorch_usecases.ai3d.model.definitions import DecoderPyramidLevel as Level
from xtorch_usecases.ai3d.model.loss.edge_weighted_mae_loss import EdgeWeightedMAELossParams
from xtorch_usecases.ai3d.model.loss.pose_loss import PoseLossParams
from xtorch_usecases.ai3d.model.sub_modules.local_cross_attention import LocalCrossAttentionParams
from xtorch_usecases.common.datasets.alliance.definitions import CameraName

from .data import DataId, SpecificDataSources

_UNCROPPED_INPUT_RESOLUTION_FC1: Final = HW(1280, 2560)
_UNCROPPED_INPUT_RESOLUTION_TV: Final = HW(1536, 1536)
_TV_CAMERA_NAMES: Final = [CameraName.TVFRONT, CameraName.TVREAR, CameraName.TVLEFT, CameraName.TVRIGHT]
UNCROPPED_INPUT_RESOLUTION: Final[MappingProxyType[CameraName, HW]] = MappingProxyType(
    {CameraName.FC1: _UNCROPPED_INPUT_RESOLUTION_FC1} | dict.fromkeys(_TV_CAMERA_NAMES, _UNCROPPED_INPUT_RESOLUTION_TV)
)
"""The uncropped input resolution for the camera images."""

_TRAIN_INPUT_OFFSET_FC1: Final[HW] = HW(544, 384)
_TRAIN_INPUT_RESOLUTION_FC1: Final[HW] = HW(640, 1792)

_TRAIN_INPUT_OFFSET_TV: Final[HW] = HW(64, 64)
_TRAIN_INPUT_RESOLUTION_TV: Final[HW] = HW(1408, 1408)

TRAIN_INPUT_OFFSET: Final[MappingProxyType[CameraName, HW]] = MappingProxyType(
    {CameraName.FC1: _TRAIN_INPUT_OFFSET_FC1} | dict.fromkeys(_TV_CAMERA_NAMES, _TRAIN_INPUT_OFFSET_TV)
)
TRAIN_INPUT_RESOLUTION: Final[MappingProxyType[CameraName, HW]] = MappingProxyType(
    {CameraName.FC1: _TRAIN_INPUT_RESOLUTION_FC1} | dict.fromkeys(_TV_CAMERA_NAMES, _TRAIN_INPUT_RESOLUTION_TV)
)
"""The input offset and resolution of the camera images used during training."""

_INFERENCE_INPUT_RESOLUTION_FC1_WIDE: Final[HW] = HW(1280, 2304)
INFERENCE_INPUT_RESOLUTION: Final[MappingProxyType[CameraName, HW]] = MappingProxyType(
    {CameraName.FC1: _INFERENCE_INPUT_RESOLUTION_FC1_WIDE}
    | dict.fromkeys(_TV_CAMERA_NAMES, _UNCROPPED_INPUT_RESOLUTION_TV)
)
"""The input resolution of the camera images used during inference."""

OUTPUT_SUBSAMPLING_FACTOR: Final[int] = Level.stride_16

SURFACE_NORMALS_MAP_RESOLUTION: Final[MappingProxyType[CameraName, HW]] = MappingProxyType(
    {
        cam: HW(resolution.height // OUTPUT_SUBSAMPLING_FACTOR, resolution.width // OUTPUT_SUBSAMPLING_FACTOR)
        for cam, resolution in UNCROPPED_INPUT_RESOLUTION.items()
    }
)
"""The resolution used to store surface normal maps in the parallax dataset."""

PLANE_NORMAL: Final[torch.Tensor] = torch.tensor([0.0, 0.0, 1.0])
"""Unit normal to the reference plane for the parallax-net expressed in the ISO23150 camera coordinate system."""


@dataclass(kw_only=True)
class RotationAugmentation:
    """Parameters for configuring a rotation augmentation.

    The augmented (delta) rotation is defined by a random yaw-pitch-roll sample in the range
    +/-[delta_deg_yaw, delta_deg_pitch, delta_deg_roll].

    Attributes:
        enabled: Flag indicating whether the rotation augmentation is enabled.
        delta_deg_yaw: Max deviation of the yaw angle in degrees.
        delta_deg_pitch: Max deviation of the pitch angle in degrees.
        delta_deg_roll: Max deviation of the roll angle in degrees.
    """

    enabled: bool = False
    delta_deg_yaw: float = 0
    delta_deg_pitch: float = 0
    delta_deg_roll: float = 0

    def __post_init__(self) -> None:
        """Ensure the delta angles are non-negative."""
        assert self.delta_deg_yaw >= 0, "Yaw delta rotation must be non-negative."
        assert self.delta_deg_pitch >= 0, "Pitch delta rotation must be non-negative."
        assert self.delta_deg_roll >= 0, "Roll delta rotation must be non-negative."


_EPS = 1e-4


@dataclass(kw_only=True)
class ColorAugmentation:
    """Parameters for configuring common color augmentations for images.

    This class defines the properties for color augmentation that
    are applied to images. The default values represent parameters that disable the augmentation.

    Attributes:
        enabled: Flag indicating if the common color augmentation is enabled.
        random_brightness_probability: Probability of applying brightness adjustment to an image.
        random_brightness_max_delta: Max brightness change. The value should be in the range [0, 1].
                                    0 means no change
                                    The actual brightness factor will be
                                    uniform(1-random_brightness_max_delta, 1+random_brightness_max_delta).

        random_hue_probability: Probability of applying hue adjustment to an image.
        random_hue_max_delta: Max hue change. The value should be in the range [-0.5, 0.5].

        random_saturation_probability: Probability of applying saturation adjustment to an image.
        random_saturation_lower: Lower bound for saturation adjustment. Should be a non-negative number.
        random_saturation_upper: Upper bound for saturation adjustment. Should be a non-negative number.

        random_contrast_probability: Probability of applying contrast adjustment to an image.
        random_contrast_lower: Lower bound for contrast adjustment. Should be a non-negative number.
        random_contrast_upper: Upper bound for contrast adjustment. Should be a non-negative number.

        random_gamma_probability: Probability of applying gamma adjustment to an image.
        random_gamma_lower: Lower bound for gamma adjustment. Should be a non-negative number.
        random_gamma_upper: Upper bound for gamma adjustment. Should be a non-negative number.
    """

    enabled: bool = False

    random_brightness_probability: float = 0
    random_brightness_max_delta: float = _EPS

    random_hue_probability: float = 0
    random_hue_max_delta: float = _EPS

    random_saturation_probability: float = 0
    random_saturation_lower: float = 1 - _EPS
    random_saturation_upper: float = 1 + _EPS

    random_contrast_probability: float = 0
    random_contrast_lower: float = 1 - _EPS
    random_contrast_upper: float = 1 + _EPS

    random_gamma_probability: float = 0
    random_gamma_lower: float = 1 - _EPS
    random_gamma_upper: float = 1 + _EPS

    def __post_init__(self) -> None:
        """Ensure the consistency of the color augmentation parameters."""
        assert 0 <= self.random_hue_probability <= 1, "Hue adjustment probability must be in interval [0;1]."
        assert 0 <= self.random_hue_max_delta <= 0.5, "Hue delta must be in interval [0;0.5]."

        assert 0 <= self.random_saturation_probability <= 1, (
            "Saturation adjustment probability must be in interval [0;1]."
        )
        assert 0 <= self.random_saturation_lower < self.random_saturation_upper, (
            "Saturation lower bound must be positive and smaller than upper bound."
        )

        assert 0 <= self.random_brightness_max_delta <= 1, "Brightness delta must be in interval [0;1]."
        assert 0 <= self.random_brightness_probability <= 1, "Brightness probability must be in interval [0;1]."

        assert 0 <= self.random_contrast_probability <= 1, "Contrast adjustment probability must be in interval [0;1]."
        assert 0 <= self.random_contrast_lower < self.random_contrast_upper, "Contrast must be a non-negative interval."

        assert 0 <= self.random_gamma_probability <= 1, "Gamma adjustment probability must be in interval [0;1]."
        assert 0 <= self.random_gamma_lower < self.random_gamma_upper, "Gamma must be a non-negative interval."


@unique
class InferenceDataSource(IntEnum):
    """Type of input data for inference."""

    CSV_FILE = auto()  # Usual csv file containing references to input images etc.
    HDF5_SEQUENCE = auto()  # HDF5 file containing a sequence of images and odometry data.


@dataclass(kw_only=True)
class DataAugmentationParams:
    """Parameters for configuring data augmentation.

    Attributes:
        extrinsic_rotation: rotation to be augmented to the extrinsic, changing the camera orientation.
        odometry_rotation: rotation to be augmented to the odometry, changing the sensor odometry rotation.
        color_augmentation: color augmentations for an image pair.
    """

    extrinsic_rotation: RotationAugmentation = field(default_factory=RotationAugmentation)
    odometry_rotation: RotationAugmentation = field(default_factory=RotationAugmentation)
    color_augmentation: ColorAugmentation = field(default_factory=ColorAugmentation)


@dataclass(kw_only=True)
class StreetMaskParams:
    """Parameters for the street mask.

    Attributes:
        x_min: Minimum x-coordinate of the street mask bounding box.
        y_min: Minimum y-coordinate of the street mask bounding box.
        x_max: Maximum x-coordinate of the street mask bounding box.
        y_max: Maximum y-coordinate of the street mask bounding box.
    """

    x_min: float = 8.8
    y_min: float = -1.3
    x_max: float = 18.0
    y_max: float = 1.3

    def __post_init__(self) -> None:
        """Post init checks."""

        assert self.x_min < self.x_max, "x_min must be less than x_max."
        assert self.y_min < self.y_max, "y_min must be less than y_max."


@dataclass(kw_only=True)
class InferenceResultExportParams:
    """Parameters for exporting inference results.

    Collect parameters here that are only used to export the inference results, e.g. to HDF5 files.

    Attributes:
        wheel_radius: The radius of the wheel in meters used to export data in the VRL-CS [m]
        normal_vector_tolerance: The tolerance in degrees for the normal vector to be considered valid [deg].
        max_depth: The maximum depth value for depth map export [m].
        street_mask_parameters: Parameters for the street mask used for normal vector calculation.
    """

    wheel_radius: float = 0.35  # TODO: https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/403030
    max_angle_of_normal_to_upward_facing_normal: float = 10
    max_depth: float = 200.0
    street_mask_parameters: StreetMaskParams = field(default_factory=StreetMaskParams)

    def __post_init__(self) -> None:
        """Post init checks."""
        assert self.wheel_radius > 0, "Wheel radius must be a positive number."
        assert 0 <= self.max_angle_of_normal_to_upward_facing_normal <= 90, (
            "Normal vector tolerance must be in the range [0;90] degrees."
        )
        assert self.max_depth >= 0, "Maximum depth must be a positive number."


@dataclass(kw_only=True)
class PoseLossParamsContainer:
    """Dataclass to hold pose loss parameters for each DOFs with dictionary-like access."""

    pose_loss_x: PoseLossParams
    pose_loss_y: PoseLossParams
    pose_loss_z: PoseLossParams
    pose_loss_yaw: PoseLossParams
    pose_loss_pitch: PoseLossParams
    pose_loss_roll: PoseLossParams

    def __getitem__(self, loss_id: LossId) -> PoseLossParams:
        """Allow dictionary-like access to the dataclass fields."""
        return getattr(self, loss_id)


# Ensure pose loss parameters for all DOFs are defined in the container.
assert {f.name for f in fields(PoseLossParamsContainer)} == {k.value for k in POSE_LOSS_DOFS}


@dataclass(kw_only=True)
class ParallaxLossParams:
    """Dataclass for parallax loss parameters.

    Attributes:
        structure_parameter_loss_params: Parameters for the structure parameter loss.
        pose_loss_params: Parameters for the pose loss for each DOF.
        surface_normals_loss_params: Parameters for the surface normals loss.
    """

    structure_parameter_loss_params: EdgeWeightedMAELossParams
    pose_loss_params: PoseLossParamsContainer
    surface_normals_loss_params: EdgeWeightedMAELossParams


@dataclass(kw_only=True)
class PyramidalDecoderParams:
    """Decoder model parameters.

    Attributes:
        decoder_id: Enum defining the encoder
        params: Dataclass containing the decoder parameters.
    """

    decoder_id: DecoderId
    params: PWAttentionDecoderParams


@dataclass(kw_only=True)
class EnabledHeads:
    """Enabled heads for the parallax task.

    Attributes:
        structure_parameter: Flag indicating whether the structure parameter head is enabled.
        pose: Flag indicating whether the pose head is enabled.
        surface_normals: Flag indicating whether the surface normals head is enabled.
    """

    structure_parameter: bool = False
    pose: bool = False
    surface_normals: bool = False


@dataclass(kw_only=True)
class ParallaxHeadParams:
    """ParallaxHead model parameters.

    Attributes:
        output_pyramid_level: Feature pyramid level defining the output resolution of the decoder.
        backbone_channels: Number of channels in the backbone at each pyramid level sorted top-down.
        feature_fusion_id: ID defining the feature fusion network.
        feature_fusion_params: Parameters for the feature fusion network at each pyramid level defined in the decoder.
        pose_fusion_id: ID defining the pose fusion network.
        pose_fusion_params: Parameters for the pose fusion network at each pyramid level
                            defined in the decoder.
        decoder_id: ID defining the decoder.
        enabled_heads: Flags indicating which heads are enabled.
        structure_head_parameter: Parameters for the structure parameter head.
        pose_head_parameter: Parameters for the pose head.
        surface_normals_head_parameter: Parameters for the surface normals head.
        decoder_params: Parameter defining the decoder. Initialized in __post_init__() using
                        attributes of the dataclass defined above.
    """

    output_pyramid_level: Level
    backbone_channels: list[int]

    feature_fusion_id: FeatureFusionId
    feature_fusion_params: dict[str, dict[Level, int]]

    pose_fusion_id: PoseFusionId
    pose_fusion_params: dict[str, Any]

    decoder_id: DecoderId

    enabled_heads: EnabledHeads
    structure_head_parameter: StructureParameterHeadParams
    pose_head_parameter: PoseHeadParams
    surface_normals_head_parameter: SurfaceNormalsHeadParams

    # Declare the field but exclude it from __init__(). Initialized in __post_init__()
    decoder_params: PyramidalDecoderParams = field(init=False)

    def __post_init__(self) -> None:
        """Post init checks."""

        pyramid_level_start = self.output_pyramid_level

        if self.feature_fusion_id in [
            FeatureFusionId.LOCAL_CROSS_ATTENTION,
            FeatureFusionId.LOCAL_OPTIMIZED_CROSS_ATTENTION,
        ]:
            # Check that all required levels have entries in feature fusion parameters
            for level in Level:
                if level > pyramid_level_start:
                    assert level in self.feature_fusion_params["patch_lengths"], (
                        f"Level {level} is missing in feature_fusion_params['patch_lengths']"
                    )
                    assert level in self.feature_fusion_params["dim_heads"], (
                        f"Level {level} is missing in feature_fusion_params['dim_heads']"
                    )

        if not any(self.enabled_heads.__dict__.values()):
            msg = "At least one parallax sub-head must be enabled."
            raise ValueError(msg)

        # initialize the PyramidalDecoderParams dataclass
        # for each stage of the pyramidal decoder, the feature map fusion and
        # pose fusion networks are initialized
        map_fusion_net_at_levels: dict[Level, FeaturemapFusionParams] = {}
        pose_fusion_net_at_levels: dict[Level, PoseFusionParams] = {}

        # Ensure the number of backbone channels matches the number of pyramid levels
        assert len(self.backbone_channels) == len(Level), (
            f"Expected {len(Level)} backbone channels, got {len(self.backbone_channels)}"
        )

        pyramid_level_to_backbone_channels = dict(zip(Level, self.backbone_channels))
        for level in Level:
            if level <= pyramid_level_start:
                # skip pyramid levels above and including the start level
                continue

            featuremap_fusion_parameters = FeaturemapFusionParams(
                self.feature_fusion_id,
                LocalCrossAttentionParams(
                    in_channels=pyramid_level_to_backbone_channels[level],
                    dim_head=self.feature_fusion_params["dim_heads"][level],
                    patch_length=self.feature_fusion_params["patch_lengths"][level],
                ),
            )

            map_fusion_net_at_levels[level] = featuremap_fusion_parameters

            pose_fusion_parameters = PoseFusionParams(
                PoseFusionId.CHANNELWISE_CROSS_ATTENTION,
                {
                    "map_channels": self.feature_fusion_params["dim_heads"][level],
                    "do_concatenation": self.pose_fusion_params["do_concatenation"],
                },
            )
            pose_fusion_net_at_levels[level] = pose_fusion_parameters

        # within the ParallaxHead, the decoder is initialized with following parameters
        self.decoder_params = PyramidalDecoderParams(
            decoder_id=self.decoder_id,
            params=PWAttentionDecoderParams(
                self.output_pyramid_level,
                map_fusion_net_at_levels,
                pose_fusion_net_at_levels,
                pyramid_level_to_backbone_channels,
            ),
        )


@dataclass(kw_only=True)
class ParallaxConfig(TaskSpecificConfig):
    """Config parameters specific for the AI3D parallax task.

    Args:
        camera_name: The name of the camera.
        inference_source: The type of input data for inference (csv file or hdf5).
        inputs: Mapping for inputs specific to this task and the corresponding data sources.
        labels: Mapping for labels (ground-truth) specific to this task and the corresponding data sources.
        depth_map_shape: The expected shape for the depth map.
        surface_normal_map_shape: The expected shape for the surface normals map.
        head_params: Parameters for the parallax task head.
    """

    camera_name: CameraName
    inference_source: InferenceDataSource
    inputs: dict[str, SpecificDataSources]
    labels: dict[str, SpecificDataSources]
    head_params: ParallaxHeadParams
    loss_params: ParallaxLossParams
    data_augmentation: DataAugmentationParams
    export_parameters: InferenceResultExportParams

    def data_processing_config(
        self, ignorer_shape: HW
    ) -> dict[SpecificDataSources, Callable[[str], TaskProcessingConfig[Any]]]:
        """Mapping between input data sources and their processing configurations."""
        return {
            SpecificDataSources.CALIBRATION: partial(calibration_processing_config, camera=self.camera_name),
            SpecificDataSources.VEHICLE_ODOMETRY: odometry_processing_config,
            SpecificDataSources.DEPTH_MAP: partial(depth_map_processing_config, target_shape=ignorer_shape),
            SpecificDataSources.SURFACE_NORMAL: partial(
                surface_normals_processing_config,
                target_shape=ignorer_shape // OUTPUT_SUBSAMPLING_FACTOR,
                adjuster_scale_factor=OUTPUT_SUBSAMPLING_FACTOR,
            ),
        }


@dataclass(kw_only=True)
class ParallaxHeadSpecificInputs:
    """Specific input data for the parallax task head.

    Args:
        previous_features: The features pyramid of the previous image.
        relative_pose: The relative camera position used as motion prior. The last dimension contains the concatenation
            of the translation vector [m] (first 3 entries) and rotation euler angles [rad] (remaining entries).
            Currently only the yaw angle of the rotation will be used.
            TODO: https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/364564
        masked_warping_grid: The grid positions for warping the previous feature maps. The grid is defined in the
            same resolution as the input image.
    """

    previous_features: BackboneOutput
    relative_pose: torch.Tensor
    masked_warping_grid: torch.Tensor


ParallaxHeadInput = tuple[BackboneOutput, ParallaxHeadSpecificInputs]


@dataclass(kw_only=True)
class ParallaxHeadOutput:
    """Output data of the parallax task head.

    Args:
        structure_parameter: The predicted structure parameter map.
        surface_normals: The predicted surface normals map.
        pose_translation: The predicted relative translation [m].
        pose_angle: The predicted relative rotation as Euler angles [rad].
        normalized_pose_translation: The predicted relative translation, normalized with the translation scales.
        normalized_pose_angle: The predicted relative rotation as Euler angles, normalized with the angle scales.
    """

    structure_parameter: torch.Tensor | None = None
    surface_normals: torch.Tensor | None = None
    pose_translation: torch.Tensor | None = None
    pose_angle: torch.Tensor | None = None
    normalized_pose_translation: torch.Tensor | None = None
    normalized_pose_angle: torch.Tensor | None = None

    def __iter__(self) -> Iterator[tuple[str, torch.Tensor]]:
        """Iterate over the output data as tuples of (name: str, value: Tensor)."""
        return ((member.name, getattr(self, member.name)) for member in fields(self))

    def items(self) -> Generator[tuple[str, torch.Tensor]]:
        """Provide a method to iterate over the members as with a dict.

        Returns:
            Generator: A generator that yields tuples of (name: str, value: Tensor).
        """
        for item in fields(self):
            yield (item.name, getattr(self, item.name))


class ParallaxGroundTruth(NamedTuple):
    """Ground truth data used in the parallax task loss.

    Args:
        gt_structure_parameter_map: The ground-truth structure parameter map.
        valid_structure_parameter_mask: The mask indicating valid structure parameter entries.
        current_image: The current image, used as reference image to the parallax network.
        gt_pose_translation: The ground-truth relative translation [m].
        gt_pose_angle: The ground-truth relative rotation as Euler angles [rad].
        gt_surface_normals_map: The ground-truth normalized surface normals map.
        valid_surface_normals_mask: The mask indicating valid surface normals entries.
    """

    gt_structure_parameter_map: torch.Tensor
    valid_structure_parameter_mask: torch.Tensor
    current_image: torch.Tensor
    gt_normalized_pose_translation: torch.Tensor
    gt_normalized_pose_angle: torch.Tensor
    gt_surface_normals_map: torch.Tensor
    valid_surface_normals_mask: torch.Tensor


# ensure that all ParallaxGroundTruth fields are defined in the data batch
assert set(ParallaxGroundTruth._fields).issubset([data_id.value for data_id in DataId])
