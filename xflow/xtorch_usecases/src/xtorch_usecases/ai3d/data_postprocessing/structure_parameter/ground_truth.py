"""Sequence of post processing tasks to compute the ground truth structure parameter data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import PostProcessingTask
from xtorch_usecases.ai3d.data_postprocessing.camera.calibration import POST_PROCESSING_TASK_CAMERA_HEIGHT
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId

from .common import process_structure_parameter_map, process_valid_structure_parameter_mask


def _post_processing_task_valid_structure_parameter_mask(target_view: ViewParameters) -> PostProcessingTask:
    """Create a post processing task for the valid structure parameter mask with cropping option."""
    return PostProcessingTask(
        input_ids=[CSVDataId.GT_DEPTH, CSVDataId.SYS_CALIB],
        output_id=DataId.VALID_STRUCTURE_PARAMETER_MASK,
        processor=lambda batch: process_valid_structure_parameter_mask(batch, target_view),
    )


def _post_processing_task_gt_structure_parameter_map(target_view: ViewParameters) -> PostProcessingTask:
    """Create a post processing task for the ground truth structure parameter map with cropping option."""
    return PostProcessingTask(
        input_ids=[
            CSVDataId.GT_DEPTH,
            CSVDataId.SYS_CALIB,
            DataId.CAMERA_HEIGHT,
            DataId.VALID_STRUCTURE_PARAMETER_MASK,
        ],
        output_id=DataId.GT_STRUCTURE_PARAMETER_MAP,
        processor=lambda batch: process_structure_parameter_map(batch, target_view),
    )


def post_processing_tasks_gt_structure_parameter(target_view: ViewParameters) -> list[PostProcessingTask]:
    """Get the post-processing tasks for the ground truth structure parameter data.

    Args:
        target_view: View parameters to be used for cropping and scaling the structure parameter data.
    """
    return [
        POST_PROCESSING_TASK_CAMERA_HEIGHT,
        _post_processing_task_valid_structure_parameter_mask(target_view),
        _post_processing_task_gt_structure_parameter_map(target_view),
    ]
