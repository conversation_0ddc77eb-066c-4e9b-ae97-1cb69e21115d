"""Common functions for ground truth structure parameter post-processing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import torch

from xcontract.data.definitions.usage import ValueKey
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_postprocessing.camera.common import (
    change_image_view,
    compute_camera_geometry_from_calibration,
)
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import PLANE_NORMAL
from xtorch_usecases.ai3d.utils.structure_parameter import depth_to_structure_parameter

# TODO: it must be evaluated whether crop and rescaling should occur within the PyPer data module.
# https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/364250


def process_valid_structure_parameter_mask(batch: OnDeviceBatchedTrainingDict, target_view: ViewParameters) -> None:
    """Process the valid structure parameter mask.

    Args:
        batch: Batch containing input and output data.
        target_view: View parameters to be used for cropping and scaling the mask.
    """
    valid_structure_parameter_mask = getattr(batch[CSVDataId.GT_DEPTH][ValueKey.DATA], "valid_mask", None)
    assert isinstance(valid_structure_parameter_mask, torch.Tensor)
    batch[DataId.VALID_STRUCTURE_PARAMETER_MASK][ValueKey.DATA] = change_image_view(
        image=valid_structure_parameter_mask, calibration=batch[CSVDataId.SYS_CALIB], target_view=target_view
    )


def process_structure_parameter_map(batch: OnDeviceBatchedTrainingDict, target_view: ViewParameters) -> None:
    """Process depth map, calibration data and camera height to compute structure parameter map.

    Assumption: the camera intrinsic is the same for all frames provided by the dataloader during a training, i.e. we
    train a separate model for each camera (FC1, TVfront...) and only the extrinsic can change between frames depending
    on the selected vehicle.

    Args:
        batch: Batch containing input and output data.
        target_view: View parameters to be used for cropping and scaling the map.
    """
    camera_geometry_net_input = compute_camera_geometry_from_calibration(batch[CSVDataId.SYS_CALIB], target_view)

    if camera_geometry_net_input is None:
        # Skip post-processing: to be checked whether this can be a problem during training with invalid batches
        return

    plane_normal = PLANE_NORMAL.to(camera_geometry_net_input.device)
    camera_height = batch[DataId.CAMERA_HEIGHT][ValueKey.DATA]
    assert isinstance(camera_height, torch.Tensor)

    original_depth_map = getattr(batch[CSVDataId.GT_DEPTH][ValueKey.DATA], "depth_map", None)
    assert isinstance(original_depth_map, torch.Tensor)
    depth_map = camera_geometry_net_input.scaled_crop(original_depth_map)

    valid_mask = batch[DataId.VALID_STRUCTURE_PARAMETER_MASK][ValueKey.DATA]
    assert isinstance(valid_mask, torch.Tensor)
    structure_parameter_map = depth_to_structure_parameter(
        depth_map, camera_geometry_net_input.light_rays, camera_height, plane_normal, validity_mask=valid_mask
    )
    batch[DataId.GT_STRUCTURE_PARAMETER_MAP][ValueKey.DATA] = structure_parameter_map
