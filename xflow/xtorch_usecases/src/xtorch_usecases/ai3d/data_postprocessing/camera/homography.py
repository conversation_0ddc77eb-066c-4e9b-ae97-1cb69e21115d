"""Sequence of post processing tasks to compute the masked warping grid induced by the homography."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import PostProcessingTask
from xtorch_usecases.ai3d.data_postprocessing.odometry.prior import POST_PROCESSING_TASK_PRIOR_POSE_TRANSFORMATION
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId

from .calibration import POST_PROCESSING_TASK_CAMERA_HEIGHT
from .common import process_masked_warping_grid


def _post_processing_task_masked_warping_grid(target_view: ViewParameters) -> PostProcessingTask:
    """Create a post processing task for the masked warping grid with cropping option."""
    return PostProcessingTask(
        input_ids=[CSVDataId.SYS_CALIB, DataId.CAMERA_HEIGHT, DataId.PRIOR_POSE_TRANSFORMATION],
        output_id=DataId.MASKED_WARPING_GRID,
        processor=lambda batch: process_masked_warping_grid(batch, target_view),
    )


def post_processing_tasks_masked_warping_grid_homography(target_view: ViewParameters) -> list[PostProcessingTask]:
    """Get the post-processing tasks for the masked warping grid.

    Args:
        target_view: View parameters to be used for computing the warping grid.
    """
    return [
        POST_PROCESSING_TASK_PRIOR_POSE_TRANSFORMATION,
        POST_PROCESSING_TASK_CAMERA_HEIGHT,
        _post_processing_task_masked_warping_grid(target_view),
    ]
