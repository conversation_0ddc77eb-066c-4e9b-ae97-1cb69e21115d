"""Post processing tasks related to camera calibration."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from functools import partial

from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import PostProcessingTask
from xtorch_usecases.ai3d.data_postprocessing.augmentation.color_augmentations import process_color_augmentation
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId

from .common import process_image


def _post_processing_task_image(
    input_id: CSVDataId, output_id: DataId, target_view: ViewParameters
) -> PostProcessingTask:
    """Create a post processing task for the previous image with cropping option."""
    return PostProcessingTask(
        input_ids=[CSVDataId.SYS_CALIB] + [input_id],
        output_id=output_id,
        processor=partial(process_image, input_id=input_id, output_id=output_id, target_view=target_view),
    )


_POST_PROCESSING_TASK_COLOR_AUGMENTATION_PREVIOUS_IMAGE = PostProcessingTask(
    input_ids=[DataId.PREVIOUS_IMAGE],
    output_id=DataId.PREVIOUS_IMAGE,
    processor=partial(process_color_augmentation, input_id=DataId.PREVIOUS_IMAGE, output_id=DataId.PREVIOUS_IMAGE),
)

_POST_PROCESSING_TASK_COLOR_AUGMENTATION_CURRENT_IMAGE = PostProcessingTask(
    input_ids=[DataId.CURRENT_IMAGE],
    output_id=DataId.CURRENT_IMAGE,
    processor=partial(process_color_augmentation, input_id=DataId.CURRENT_IMAGE, output_id=DataId.CURRENT_IMAGE),
)


def post_processing_tasks_images(target_view: ViewParameters) -> list[PostProcessingTask]:
    """Get the post-processing tasks for the images.

    Args:
        target_view: View parameters to be used for cropping and scaling the images.
    """
    return [
        _post_processing_task_image(
            input_id=CSVDataId.PREVIOUS_IMAGE, output_id=DataId.PREVIOUS_IMAGE, target_view=target_view
        ),
        _post_processing_task_image(
            input_id=CSVDataId.CURRENT_IMAGE, output_id=DataId.CURRENT_IMAGE, target_view=target_view
        ),
        _POST_PROCESSING_TASK_COLOR_AUGMENTATION_PREVIOUS_IMAGE,
        _POST_PROCESSING_TASK_COLOR_AUGMENTATION_CURRENT_IMAGE,
    ]
