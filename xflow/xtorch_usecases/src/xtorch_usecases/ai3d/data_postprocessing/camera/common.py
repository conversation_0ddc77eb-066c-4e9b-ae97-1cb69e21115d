"""Common functions for camera-related outputs post-processing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from functools import lru_cache
from typing import Any, NamedTuple

import torch

from xcontract.camera_models.definitions import CameraModelType, IntrinsicIndex
from xcontract.data.definitions.usage import ValueKey
from xtorch.geometry.transformation.pose_transformation import PoseTransformation, Quaternion
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.ai3d.camera import CameraGeometry, CameraIntrinsicParameters, ViewParameters
from xtorch_usecases.ai3d.data_formats.camera_calibration.data_loading import CameraMetadataIndex
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    PLANE_NORMAL,
)
from xtorch_usecases.ai3d.transformation.planar_homography import PlanarHomography


class ImageMetaData(NamedTuple):
    """Image meta data.

        This class will be used as container for image meta data within the
        LightningRunner and shall only contain torch tensors.

    Attributes:
        timestamp: time stamp of the image.
        frame_number: Frame number of the image.
    """

    timestamp: torch.Tensor
    frame_number: torch.Tensor


def compute_intrinsic_parameters(metadata: torch.Tensor, intrinsic: torch.Tensor) -> CameraIntrinsicParameters:
    """Factory function to construct CameraIntrinsicParameters from CSV-loaded data.

    Args:
        metadata (Tensor[len(CameraMetadataIndex)]): Metadata of the camera loaded from CSV.
        intrinsic (Tensor[len(IntrinsicIndex)]): Intrinsic parameters of the camera loaded from CSV.

    Returns:
        CameraIntrinsicParameters: Intrinsic parameters of the camera.
    """
    return CameraIntrinsicParameters(
        camera_type=CameraModelType(metadata[CameraMetadataIndex.camera_type].item()),
        full_image_height=int(metadata[CameraMetadataIndex.camera_height].item()),
        full_image_width=int(metadata[CameraMetadataIndex.camera_width].item()),
        fu=intrinsic[IntrinsicIndex.fu].item(),
        fv=intrinsic[IntrinsicIndex.fv].item(),
        u0=intrinsic[IntrinsicIndex.u0].item(),
        v0=intrinsic[IntrinsicIndex.v0].item(),
        cut_angle_degrees=intrinsic[IntrinsicIndex.cut_angle].item(),
    )


@lru_cache
def compute_camera_geometry(
    camera_intrinsics: CameraIntrinsicParameters, image_view: ViewParameters, device: torch.device
) -> CameraGeometry:
    """Factory function to cache CameraGeometry initialization.

    Args:
            camera_intrinsics: Parameters describing a virtual camera image.
            image_view: Contains cropping and scaling settings of the desired view, relative to a camera_intrinsics
                        image.
            device: Device on which the tensors are stored.

    Returns:
        CameraGeometry: Camera geometry initialized with the provided parameters.
    """
    return CameraGeometry(camera_intrinsics, image_view, device)


def compute_camera_geometry_from_calibration(
    calibration: dict[ValueKey, Any], view: ViewParameters | None
) -> CameraGeometry | None:
    """Wrapper to compute camera_geometry from calibration data.

    Args:
        calibration: Calibration data loaded from CSV.
        view: View parameters to be used for the camera geometry. If not provided, the same view as the defined in the
            intrinsic parameters will be used.

    Returns:
        CameraGeometry: Camera geometry initialized with the provided calibration data for the first valid frame,
            or None if no valid frame was found.
    """
    valid_frames = calibration[ValueKey.VALID]
    assert isinstance(valid_frames, torch.Tensor)
    valid_frame_indices = torch.nonzero(valid_frames, as_tuple=True)

    if valid_frame_indices[0].numel() == 0:
        return None
    first_valid_frame_index = valid_frame_indices[0][0].item()

    metadata = calibration[ValueKey.DATA].camera_metadata[first_valid_frame_index, ...]
    intrinsic = calibration[ValueKey.DATA].intrinsic[first_valid_frame_index, ...]

    intrinsic_parameters = compute_intrinsic_parameters(metadata, intrinsic)
    if not isinstance(view, ViewParameters):
        view = ViewParameters.identity(intrinsic_parameters)
    return compute_camera_geometry(intrinsic_parameters, view, device=intrinsic.device)


def process_camera_height(batch: OnDeviceBatchedTrainingDict) -> None:
    """Process calibration data to obtain the height of the camera wrt. the reference plane (in meters).

    Args:
        batch: Batch containing input and output data.
    """
    translation = batch[CSVDataId.SYS_CALIB][ValueKey.DATA].translation  # type: ignore[reportAttributeAccessIssue]
    plane_normal = PLANE_NORMAL.to(translation.device)
    height = torch.matmul(translation, plane_normal)
    # The height is clamped to a minimum value of 1 mm to avoid a degenerate homography
    batch[DataId.CAMERA_HEIGHT][ValueKey.DATA] = torch.clamp(height, min=1e-3)


def change_image_view(
    image: torch.Tensor, calibration: dict[ValueKey, Any], target_view: ViewParameters
) -> torch.Tensor:
    """Changes the view by optionally cropping and scaling the image tensor based on the calibration data.

    Args:
        image: Image tensor to be cropped.
        calibration: Calibration data loaded from CSV.
        target_view: View parameters to be used for cropping and scaling the image.

    Returns:
        torch.Tensor: Cropped image tensor. In case no valid frame for calibration is available, simply the uncropped
            image is returned.
    """
    camera_geometry_net_input = compute_camera_geometry_from_calibration(calibration, target_view)

    if camera_geometry_net_input is None:
        return image

    return camera_geometry_net_input.scaled_crop(image)


def process_image(
    batch: OnDeviceBatchedTrainingDict, input_id: CSVDataId, output_id: DataId, target_view: ViewParameters
) -> None:
    """Process the image tensor into the batch at the corresponding output ID given the input ID.

    Args:
        batch: Batch containing input and output data.
        input_id: The ID of the original image data.
        output_id: The ID where the processed image shall be stored.
        target_view: View parameters to be used for cropping and scaling the image.
    """
    # TODO: it must be evaluated whether crop and rescaling should occur within the PyPer data module.
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/364250
    input_data = batch[input_id][ValueKey.DATA]
    assert isinstance(input_data, torch.Tensor)
    batch[output_id][ValueKey.DATA] = change_image_view(input_data, batch[CSVDataId.SYS_CALIB], target_view)


def process_masked_warping_grid(batch: OnDeviceBatchedTrainingDict, target_view: ViewParameters) -> None:
    """Process prior pose, camera height and calibration data to obtained warped feature map positions.

    Assumption: the camera intrinsic is the same for all frames provided by the dataloader during a training, i.e. we
    train a separate model for each camera (FC1, TVfront...) and only the extrinsic can change between frames depending
    on the selected vehicle.

    Note: the warped feature map is provided in channel-first format: (B, C, H, W).

    Args:
        batch: Batch containing input and output data.
        crop: type of cropping applied to the image. If CropType.NONE, no cropping is applied.
        target_view: View parameters to be used for cropping and scaling the image.
    """

    camera_geometry_net_input = compute_camera_geometry_from_calibration(batch[CSVDataId.SYS_CALIB], target_view)

    if camera_geometry_net_input is None:
        # Skip post-processing: to be checked whether this can be a problem during training with invalid batches
        return

    plane_normal = PLANE_NORMAL.to(camera_geometry_net_input.device)
    camera_height = batch[DataId.CAMERA_HEIGHT][ValueKey.DATA]
    assert isinstance(camera_height, torch.Tensor)

    prior_pose_translation = getattr(batch[DataId.PRIOR_POSE_TRANSFORMATION][ValueKey.DATA], "translation", None)
    prior_pose_rotation = getattr(batch[DataId.PRIOR_POSE_TRANSFORMATION][ValueKey.DATA], "rotation", None)
    assert isinstance(prior_pose_translation, torch.Tensor)
    assert isinstance(prior_pose_rotation, torch.Tensor)
    camera_previous_from_camera_current = PoseTransformation(Quaternion(prior_pose_rotation), prior_pose_translation)

    homography = PlanarHomography(camera_previous_from_camera_current, camera_height, plane_normal)
    warped_light_rays = homography.apply(camera_geometry_net_input.light_rays)
    masked_warping_grid = camera_geometry_net_input.points_to_masked_warping_grid(warped_light_rays)
    batch[DataId.MASKED_WARPING_GRID][ValueKey.DATA] = masked_warping_grid
