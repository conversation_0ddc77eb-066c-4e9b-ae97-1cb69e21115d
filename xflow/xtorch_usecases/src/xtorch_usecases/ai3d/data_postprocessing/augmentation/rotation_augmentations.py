"""Implementation of rotation augmentations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Final

import torch

from xcontract.data.definitions.usage import ValueKey
from xtorch.geometry.transformation.pose_transformation import PoseTransformation
from xtorch.geometry.transformation.quaternion import Quaternion
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.ai3d.camera import CameraGeometry
from xtorch_usecases.ai3d.camera.pixel_operations import warp_image
from xtorch_usecases.ai3d.data_postprocessing.camera.common import compute_camera_geometry_from_calibration
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId


def _get_camera_geometry_and_warping_grid(
    batch: OnDeviceBatchedTrainingDict, augmented_from_original: Quaternion
) -> tuple[CameraGeometry, torch.Tensor]:
    """Get intrinsic parameters and warping grid for the rotation augmentation.

    The warping grid is constructed considering the camera model for the full-view and assuming that all the batch
    entries have the same intrinsic camera parameters.

    Args:
        batch: Data batch containing the sys calib entry.
        augmented_from_original: Rotation from the original coordinate system to the augmented coordinate system.

    Returns:
        tuple: Tuple containing the camera geometry and the warping grid for the batch.
    """
    camera_metadata = getattr(batch[CSVDataId.SYS_CALIB][ValueKey.DATA], "camera_metadata", None)
    assert isinstance(camera_metadata, torch.Tensor)
    batch_size = camera_metadata.shape[0]

    camera_geometry = compute_camera_geometry_from_calibration(batch[CSVDataId.SYS_CALIB], None)
    assert camera_geometry is not None
    rotated_light_rays = augmented_from_original.rotate(camera_geometry.light_rays)
    masked_warping_grid = camera_geometry.points_to_masked_warping_grid(rotated_light_rays)
    batched_masked_warping_grid = masked_warping_grid.unsqueeze(0).expand(batch_size, -1, -1, -1)

    return (camera_geometry, batched_masked_warping_grid)


def _warp_batched_image_data(
    image_id: CSVDataId, batch: OnDeviceBatchedTrainingDict, batched_masked_warping_grid: torch.Tensor
) -> None:
    """Warp the image data in the batch using the provided warping grid.

    Args:
        image_id: The ID of the image data to be warped.
        batch: The batch containing the image data.
        batched_masked_warping_grid: The warping grid to be used for warping.
    """
    data = batch[image_id][ValueKey.DATA]
    assert isinstance(data, torch.Tensor)
    batch[image_id][ValueKey.DATA] = warp_image(data, batched_masked_warping_grid, mode="nearest")


def extrinsic_rotation_augmentation(
    batch: OnDeviceBatchedTrainingDict,
    extrinsic_rotation_data: torch.Tensor,
    *,
    skip_previous_image_augmentation: bool = False,
) -> None:
    """Apply extrinsic rotation augmentation to the batch.

    The rotation data `cam_R_aug_cam` is used to rotate the optical axis of the camera. Consequently, all
    batch data represented in image space is warped accordingly.
    The extrinsic (`vehicle` from `camera` transformation) batch data is also modified accordingly (i.e, to a `vehicle`
    from `augmented camera` transformation).

    Args:
        batch: Batch to be post-processed on the device.
        extrinsic_rotation_data: Quaternion representation of the extrinsic rotation to be applied to the batch.
        skip_previous_image_augmentation: If True, skip the augmentation of the previous image. This flag must be set
            only when the odometry augmentation is applied, since it will consider the total rotation augmentation
            (extrinsic + odometry) to warp the previous image.
    """
    cam_from_aug_cam_rotation: Final = Quaternion(extrinsic_rotation_data)
    aug_cam_from_cam_rotation: Final = cam_from_aug_cam_rotation.conjugate()
    camera_geometry, batched_masked_warping_grid = _get_camera_geometry_and_warping_grid(
        batch, aug_cam_from_cam_rotation
    )
    batch_size: Final = batched_masked_warping_grid.shape[0]

    ## Image augmentation
    image_data_entries: Final = (
        [CSVDataId.CURRENT_IMAGE, CSVDataId.PREVIOUS_IMAGE]
        if not skip_previous_image_augmentation
        else [CSVDataId.CURRENT_IMAGE]
    )

    for image_entry in image_data_entries:
        _warp_batched_image_data(image_entry, batch, batched_masked_warping_grid)

    ## Depth augmentation
    gt_depth_map, gt_depth_map_validity = batch[CSVDataId.GT_DEPTH][ValueKey.DATA]
    assert isinstance(gt_depth_map, torch.Tensor)
    assert isinstance(gt_depth_map_validity, torch.Tensor)
    gt_depth_map[:] = warp_image(gt_depth_map, batched_masked_warping_grid, mode="nearest")
    gt_depth_map_validity[:] = warp_image(
        gt_depth_map_validity.to(dtype=torch.float32), batched_masked_warping_grid, mode="nearest"
    ).to(dtype=torch.bool)

    ## Surface normal augmentation
    gt_surface_normal_map, gt_surface_normal_map_validity = batch[CSVDataId.GT_SURFACE_NORMAL][ValueKey.DATA]
    assert isinstance(gt_surface_normal_map, torch.Tensor)
    assert isinstance(gt_surface_normal_map_validity, torch.Tensor)

    # Rotate surface normal vectors: map has shape [B, 3, H, W] but quaternion expects [B, H, W, 3]
    rotated_gt_surface_normal_map = aug_cam_from_cam_rotation.rotate(gt_surface_normal_map.permute(0, 2, 3, 1)).permute(
        0, 3, 1, 2
    )

    # Rotate surface normal map
    surface_normal_height_downscaling = rotated_gt_surface_normal_map.shape[-2] / gt_depth_map.shape[-2]
    surface_normal_width_downscaling = rotated_gt_surface_normal_map.shape[-1] / gt_depth_map.shape[-1]
    assert surface_normal_height_downscaling == surface_normal_width_downscaling
    scaled_camera_geometry = camera_geometry.scaled(surface_normal_height_downscaling)
    scaled_rotated_light_rays = aug_cam_from_cam_rotation.rotate(scaled_camera_geometry.light_rays)
    scaled_masked_warping_grid = scaled_camera_geometry.points_to_masked_warping_grid(scaled_rotated_light_rays)
    batched_scaled_masked_warping_grid = scaled_masked_warping_grid.unsqueeze(0).expand(batch_size, -1, -1, -1)
    gt_surface_normal_map[:] = warp_image(
        rotated_gt_surface_normal_map, batched_scaled_masked_warping_grid, mode="nearest"
    )
    gt_surface_normal_map_validity[:] = warp_image(
        gt_surface_normal_map_validity.to(dtype=torch.float32), batched_scaled_masked_warping_grid, mode="nearest"
    ).to(dtype=torch.bool)

    ## Apply the extrinsic rotation augmentation to the nominal ground truth calibration
    extrinsic_calibration_rotation = getattr(batch[DataId.GT_EXTRINSIC_CALIB][ValueKey.DATA], "rotation", None)
    assert isinstance(extrinsic_calibration_rotation, torch.Tensor)
    veh_from_cam_rotation = Quaternion(extrinsic_calibration_rotation)
    veh_from_aug_cam_rotation = veh_from_cam_rotation * cam_from_aug_cam_rotation
    extrinsic_calibration_rotation[:] = veh_from_aug_cam_rotation.tensor


def odometry_rotation_augmentation(
    batch: OnDeviceBatchedTrainingDict,
    odometry_rotation_data: torch.Tensor,
    extrinsic_rotation_data: torch.Tensor | None = None,
) -> None:
    """Apply odometry rotation augmentation to the batch.

    The objective of the odometry rotation augmentation is mainly to ensure that pitch and roll motion is available in
    the data provided to the network. This is achieved by applying the `odometry_rotation_data` perturbation (aka
    `aug_prev_pose_R_prev_pose`) to the image and ground-truth position data corresponding to the previous frame.

    Args:
        batch: Batch to be post-processed on the device.
        odometry_rotation_data: Quaternion representation of the odometry rotation to be applied to the batch.
        extrinsic_rotation_data: Quaternion representation of the extrinsic rotation to be applied to the batch. If not
            provided, only the odometry rotation will be considered for warping the image data.
    """
    aug_prev_cam_from_prev_cam_rotation: Final = Quaternion(odometry_rotation_data)
    prev_cam_from_aug_prev_cam_rotation: Final = aug_prev_cam_from_prev_cam_rotation.conjugate()

    identity_rotation: Final = Quaternion.identity(device=odometry_rotation_data.device)
    aug_cam_from_cam_rotation: Final = (
        Quaternion(extrinsic_rotation_data).conjugate() if extrinsic_rotation_data is not None else identity_rotation
    )
    _, batched_masked_warping_grid = _get_camera_geometry_and_warping_grid(
        batch, aug_prev_cam_from_prev_cam_rotation * aug_cam_from_cam_rotation
    )
    _warp_batched_image_data(CSVDataId.PREVIOUS_IMAGE, batch, batched_masked_warping_grid)

    extrinsic_calibration: Final = batch[DataId.GT_EXTRINSIC_CALIB][ValueKey.DATA]
    extrinsic_calibration_rotation: Final = getattr(extrinsic_calibration, "rotation", None)
    extrinsic_calibration_translation: Final = getattr(extrinsic_calibration, "translation", None)
    assert isinstance(extrinsic_calibration_rotation, torch.Tensor)
    assert isinstance(extrinsic_calibration_translation, torch.Tensor)

    prev_veh_from_prev_cam_trafo = PoseTransformation(
        Quaternion(extrinsic_calibration_rotation), extrinsic_calibration_translation
    )
    zero_translation: Final = torch.zeros(3).to(identity_rotation.tensor.device)
    prev_cam_from_aug_prev_cam_trafo = PoseTransformation(prev_cam_from_aug_prev_cam_rotation, zero_translation)
    prev_veh_from_aug_prev_veh_trafo = (
        prev_veh_from_prev_cam_trafo * prev_cam_from_aug_prev_cam_trafo * prev_veh_from_prev_cam_trafo.inverse()
    )

    previous_pose = batch[CSVDataId.GT_PREVIOUS_POSE][ValueKey.DATA]
    previous_pose_rotation = getattr(previous_pose, "rotation", None)
    previous_pose_translation = getattr(previous_pose, "translation", None)
    assert isinstance(previous_pose_rotation, torch.Tensor)
    assert isinstance(previous_pose_translation, torch.Tensor)
    inertial_from_prev_veh_trafo = PoseTransformation(Quaternion(previous_pose_rotation), previous_pose_translation)

    inertial_from_aug_prev_veh_trafo = inertial_from_prev_veh_trafo * prev_veh_from_aug_prev_veh_trafo
    previous_pose_rotation[:] = inertial_from_aug_prev_veh_trafo.rotation.tensor
    previous_pose_translation[:] = inertial_from_aug_prev_veh_trafo.translation
