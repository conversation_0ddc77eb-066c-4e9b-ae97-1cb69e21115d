"""Utilities to map from DataGroupId to PostProcessingTasks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections.abc import Mapping
from types import MappingProxyType

from xcontract.data.definitions.image import HW
from xcontract.data.definitions.usage import Usage
from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import PostProcessingTask, PostProcessingTasks
from xtorch_usecases.ai3d.tasks.parallax.data import DataGroupId, RequiredDataGroups
from xtorch_usecases.ai3d.tasks.parallax.definitions import OUTPUT_SUBSAMPLING_FACTOR, DataAugmentationParams
from xtorch_usecases.ai3d.utils.pipeline_mappings import PIPELINE_TO_DATA_MODULE_USAGES
from xtorch_usecases.common.pipeline import PipelineStep

from .augmentation.augmentation_registry import AugmentationRegistry
from .camera.homography import post_processing_tasks_masked_warping_grid_homography
from .camera.images import post_processing_tasks_images
from .odometry.ground_truth import POST_PROCESSING_TASK_GT_EXTRINSIC_CALIB, POST_PROCESSING_TASKS_GT_POSE
from .odometry.prior import POST_PROCESSING_TASKS_PRIOR_POSE
from .structure_parameter.ground_truth import post_processing_tasks_gt_structure_parameter
from .surface_normals.ground_truth import post_processing_tasks_gt_surface_normals


def _post_processing_tasks_for_pipeline_step(
    pipeline_step: PipelineStep, target_offset: HW, target_size: HW
) -> Mapping[DataGroupId, PostProcessingTasks]:
    """Get the post-processing tasks for the given pipeline step."""

    input_view = ViewParameters(
        crop_u=target_offset.width,
        crop_v=target_offset.height,
        crop_width=target_size.width,
        crop_height=target_size.height,
        scale=1.0,
    )

    # Warping grid is downsampled to the next lower resolution level of the output feature map level used in the PWA
    # decoder. This is done to avoid unnecessary downsampling operations in the model.
    warping_grid_scaling_factor = 1.0 / (2 * OUTPUT_SUBSAMPLING_FACTOR)
    warping_grid_view = input_view.scaled(warping_grid_scaling_factor)

    surface_normal_scale_factor = 1.0 / OUTPUT_SUBSAMPLING_FACTOR
    surface_normal_view = input_view.scaled(surface_normal_scale_factor)

    if pipeline_step.is_torch:
        return MappingProxyType(
            {
                DataGroupId.IMAGES: post_processing_tasks_images(input_view),
                DataGroupId.PRIOR_POSE: POST_PROCESSING_TASKS_PRIOR_POSE,
                DataGroupId.MASKED_WARPING_GRID: post_processing_tasks_masked_warping_grid_homography(
                    warping_grid_view
                ),
                DataGroupId.GT_STRUCTURE_PARAMETER: post_processing_tasks_gt_structure_parameter(input_view),
                DataGroupId.GT_POSE: POST_PROCESSING_TASKS_GT_POSE,
                DataGroupId.GT_SURFACE_NORMALS: post_processing_tasks_gt_surface_normals(surface_normal_view),
            }
        )

    if pipeline_step.is_qnn:
        # Only post-processing of input data required for QNN-intended data.
        qnn_post_processing_tasks = {key: [] for key in DataGroupId}
        qnn_post_processing_tasks[DataGroupId.PRIOR_POSE] = POST_PROCESSING_TASKS_PRIOR_POSE
        qnn_post_processing_tasks[DataGroupId.MASKED_WARPING_GRID] = (
            post_processing_tasks_masked_warping_grid_homography(warping_grid_view)
        )
        return MappingProxyType(qnn_post_processing_tasks)

    error_msg = f"Post-processing tasks for pipeline step {pipeline_step} are not implemented."
    raise NotImplementedError(error_msg)


def _create_augmentation_processing_configuration(
    augmentation_params: DataAugmentationParams,
) -> PostProcessingTask | None:
    """Register the configuration for data augmentation.

    Note: All the available parameters for data augmentation are registered using the `AugmentationRegistry`.
    Once an augmentation type available in `augmentation_params` is registered, that type of augmentation will be
    applied to all the necessary data entries during the batch post-processing step.

    Args:
        augmentation_params: Data augmentation parameters to register.

    Returns:
        The registered augmentation processing configuration or None if no augmentation is enabled.
    """
    has_augmentation = False
    if augmentation_params.extrinsic_rotation.enabled:
        AugmentationRegistry.register_extrinsic_rotation_augmentation(augmentation_params.extrinsic_rotation)
        has_augmentation = True

    if augmentation_params.odometry_rotation.enabled:
        AugmentationRegistry.register_odometry_rotation_augmentation(augmentation_params.odometry_rotation)
        has_augmentation = True

    if augmentation_params.color_augmentation.enabled:
        AugmentationRegistry.register_color_augmentation(augmentation_params.color_augmentation)
        has_augmentation = True

    if not has_augmentation:
        return None

    return AugmentationRegistry.augmentation_processing_configuration


def setup_post_processing_tasks(
    data_group_ids: RequiredDataGroups,
    augmentation_params: DataAugmentationParams,
    pipeline_step: PipelineStep,
    target_offset: HW,
    target_size: HW,
) -> Mapping[Usage, PostProcessingTasks]:
    """Set up the sequence of post-processing tasks based on the required data groups and pipeline step.

    Args:
        data_group_ids: Required data groups to post-process for AI3D model input.
        augmentation_params: Data augmentation parameters to to be applied during the post-processing.
        pipeline_step: The pipeline step for which the post-processing tasks shall be created.
        target_offset: The offset to be used for cropping the data to obtain the desired target size.
        target_size: The desired target size of the post-processed input data.

    Returns:
        A mapping from usage to a list of unique post-processing tasks, performed in the correct order.
    """
    task_sequence: PostProcessingTasks = []

    data_group_to_post_processing_tasks = _post_processing_tasks_for_pipeline_step(
        pipeline_step, target_offset=target_offset, target_size=target_size
    )
    for data_group_id in data_group_ids:
        if data_group_id not in data_group_to_post_processing_tasks:
            msg = f"Data group {data_group_id.name} is not supported for post-processing."
            raise KeyError(msg)
        task_sequence.extend(data_group_to_post_processing_tasks[data_group_id])

    augmentation_configuration = _create_augmentation_processing_configuration(augmentation_params)

    usages = PIPELINE_TO_DATA_MODULE_USAGES[pipeline_step]
    task_processing_configurations: dict[Usage, list[PostProcessingTask]] = {}
    for usage in usages:
        data_handling_tasks = [POST_PROCESSING_TASK_GT_EXTRINSIC_CALIB]
        # Enable random data augmentation only for training. The validation, testing and prediction stages don't have
        # any augmentation by now.
        if usage == Usage.TRAINING and augmentation_configuration:
            data_handling_tasks.append(augmentation_configuration)

        # Dictionary is used to preserve order while removing duplicate tasks.
        task_processing_configurations[usage] = list(dict.fromkeys(data_handling_tasks + task_sequence))

    return task_processing_configurations
