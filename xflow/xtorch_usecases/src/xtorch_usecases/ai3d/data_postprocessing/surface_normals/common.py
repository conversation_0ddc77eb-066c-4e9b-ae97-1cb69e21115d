"""Common functions for surface normals post-processing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


import torch

from xcontract.data.definitions.usage import ValueKey
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_postprocessing.camera.common import change_image_view
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId


def process_surface_normal_map(batch: OnDeviceBatchedTrainingDict, target_view: ViewParameters) -> None:
    """Process the surface normal map.

    Args:
        batch: Batch containing input and output data.
        target_view: View parameters to be used for cropping and scaling the map.
    """
    surface_normal_data = batch[CSVDataId.GT_SURFACE_NORMAL][ValueKey.DATA]
    surface_normal_map = getattr(surface_normal_data, "surface_normal_map", None)
    assert isinstance(surface_normal_map, torch.Tensor)
    batch[DataId.GT_SURFACE_NORMALS_MAP][ValueKey.DATA] = change_image_view(
        image=surface_normal_map, calibration=batch[CSVDataId.SYS_CALIB], target_view=target_view
    )


def process_valid_surface_normals_mask(batch: OnDeviceBatchedTrainingDict, target_view: ViewParameters) -> None:
    """Process the valid surface normals mask.

    Args:
        batch: Batch containing input and output data.
        target_view: View parameters to be used for cropping and scaling the mask.
    """
    surface_normal_data = batch[CSVDataId.GT_SURFACE_NORMAL][ValueKey.DATA]
    valid_surface_normals_mask = getattr(surface_normal_data, "valid_mask", None)

    assert isinstance(valid_surface_normals_mask, torch.Tensor)
    batch[DataId.VALID_SURFACE_NORMALS_MASK][ValueKey.DATA] = change_image_view(
        image=valid_surface_normals_mask, calibration=batch[CSVDataId.SYS_CALIB], target_view=target_view
    )
