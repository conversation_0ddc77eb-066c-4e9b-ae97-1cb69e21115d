"""Post processing tasks related to GT surface normals."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import PostProcessingTask
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId

from .common import process_surface_normal_map, process_valid_surface_normals_mask


def _post_processing_task_gt_surface_normals_map(target_view: ViewParameters) -> PostProcessingTask:
    """Create a post processing task for the ground truth surface normals map with cropping option."""
    return PostProcessingTask(
        input_ids=[CSVDataId.GT_SURFACE_NORMAL, CSVDataId.SYS_CALIB],
        output_id=DataId.GT_SURFACE_NORMALS_MAP,
        processor=lambda batch: process_surface_normal_map(batch=batch, target_view=target_view),
    )


def _post_processing_task_valid_surface_normals_mask(target_view: ViewParameters) -> PostProcessingTask:
    """Create a post processing task for the valid surface normals mask with cropping option."""
    return PostProcessingTask(
        input_ids=[CSVDataId.GT_SURFACE_NORMAL, CSVDataId.SYS_CALIB],
        output_id=DataId.VALID_SURFACE_NORMALS_MASK,
        processor=lambda batch: process_valid_surface_normals_mask(batch=batch, target_view=target_view),
    )


def post_processing_tasks_gt_surface_normals(target_view: ViewParameters) -> list[PostProcessingTask]:
    """Get the post-processing tasks for the ground truth surface normals.

    Args:
        target_view: View parameters to be used for cropping and scaling the surface normals data.
    """
    return [
        _post_processing_task_gt_surface_normals_map(target_view),
        _post_processing_task_valid_surface_normals_mask(target_view),
    ]
