"""Functions to read the calibration data contained in a sys-calib json file."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import functools
import json
import os
from collections.abc import Sequence
from enum import IntEnum, unique
from pathlib import Path
from typing import NamedTuple

import numpy as np
import numpy.typing as npt

from pyper.dict_pipeline import TaskProcessingConfig
from xcontract.camera_models.definitions import CameraModelType, IntrinsicIndex
from xcontract.data.definitions.image import HW
from xtorch_usecases.common.datasets.alliance.definitions import CAMERA_NAME_TO_SYSCAL_CAM_MAP, CameraName


@unique
class CameraMetadataIndex(IntEnum):
    """Enum for parameter indices within the camera metadata sequence."""

    camera_type = 0
    camera_height = 1
    camera_width = 2


class CameraCalibrationNumpyData(NamedTuple):
    """Camera calibration data in numpy format.

    This format is required to enable the PyPer processing chain.
    The pair (rotation, translation) defines the extrinsic transformation of the camera, i.e., the transformation
    from camera (sensor) coordinates to the vehicle coordinates.
    The different intrinsic and camera metadata parameters are packed into arrays at the indices defined by
    `IntrinsicIndex` and `CameraMetadataIndex`, respectively.

    Attributes:
        rotation: The quaternion ([x,y,z,w]) defining the extrinsic rotation.
        translation: The extrinsic translation vector.
        intrinsic: The intrinsic parameters of the camera.
        camera_metadata: The metadata parameters of the camera.
    """

    rotation: npt.NDArray[np.float32]
    translation: npt.NDArray[np.float32]
    intrinsic: npt.NDArray[np.float32]
    camera_metadata: npt.NDArray[np.int32]


def _get_calibration(raw_data: bytes, camera: CameraName) -> CameraCalibrationNumpyData:
    """Extract the calibration data.

    Args:
        raw_data: The raw data contained in the sys-calib json file following the SysCal data scheme.
            See https://pace-docs.azurewebsites.net/calibration-storage/main/user-doc/parameter-exports/syscal-export.html#interfaces.parameter_sets.syscal_parameters.syscal_params_v1_3.SyscalParametersV1_3
        camera: Camera name for which the calibration data should be extracted.

    Returns:
        The camera calibration data in numpy format.
    """
    data = json.loads(raw_data)

    sys_calib_camera_name = CAMERA_NAME_TO_SYSCAL_CAM_MAP[camera]
    extrinsic_dict_data = data["devices"][sys_calib_camera_name]["extrinsics"]
    extrinsic_rotation_data = np.array(
        [
            extrinsic_dict_data["rotation"]["quaternion_i"],
            extrinsic_dict_data["rotation"]["quaternion_j"],
            extrinsic_dict_data["rotation"]["quaternion_k"],
            extrinsic_dict_data["rotation"]["quaternion_w"],
        ],
        dtype=np.float32,
    )
    extrinsic_translation_data = np.array(
        [
            extrinsic_dict_data["translation"]["x_m"],
            extrinsic_dict_data["translation"]["y_m"],
            extrinsic_dict_data["translation"]["z_m"],
        ],
        dtype=np.float32,
    )

    intrinsic_data_dict = data["devices"][sys_calib_camera_name]["intrinsics"]["models"]["contributed_v01"]
    intrinsic_data = np.zeros(5, dtype=np.float32)
    intrinsic_data[IntrinsicIndex.fu.value] = intrinsic_data_dict["focal_length"][0]
    intrinsic_data[IntrinsicIndex.fv.value] = intrinsic_data_dict["focal_length"][1]
    intrinsic_data[IntrinsicIndex.u0.value] = intrinsic_data_dict["principal_point"][0]
    intrinsic_data[IntrinsicIndex.v0.value] = intrinsic_data_dict["principal_point"][1]

    camera_type_map: dict[str, CameraModelType] = {
        "CYLINDER": CameraModelType.CYLINDER,
        "DEFORMEDCYLINDER": CameraModelType.DEFORMED_CYLINDER,
    }

    camera_type = camera_type_map[intrinsic_data_dict["type"]]
    if camera_type == CameraModelType.DEFORMED_CYLINDER:
        assert abs(intrinsic_data_dict["cut_angle_lower"]) == abs(intrinsic_data_dict["cut_angle_upper"])
        intrinsic_data[IntrinsicIndex.cut_angle.value] = abs(intrinsic_data_dict["cut_angle_lower"])

    camera_dimension_dict = data["devices"][sys_calib_camera_name]["device_specifics_camera"]
    camera_metadata = np.zeros(3, dtype=np.int32)
    camera_metadata[CameraMetadataIndex.camera_type.value] = camera_type.value
    camera_metadata[CameraMetadataIndex.camera_height.value] = camera_dimension_dict["active_pixels_height_px"]
    camera_metadata[CameraMetadataIndex.camera_width.value] = camera_dimension_dict["active_pixels_width_px"]

    return CameraCalibrationNumpyData(
        extrinsic_rotation_data,
        extrinsic_translation_data,
        intrinsic_data,
        camera_metadata,
    )


@functools.lru_cache
def _ignorer() -> CameraCalibrationNumpyData:
    """Returns a placeholder for ignored calibration data."""
    return CameraCalibrationNumpyData(
        np.zeros(4, dtype=np.float32),
        np.zeros(3, dtype=np.float32),
        np.zeros(5, dtype=np.float32),
        np.zeros(3, dtype=np.int32),
    )


def _adjust_calibration(
    calib_data: Sequence[npt.NDArray[np.float32]], offset: HW | None, target_shape: HW | None
) -> CameraCalibrationNumpyData:
    """Adjust the calibration data based on the offset and target shape."""
    assert isinstance(calib_data, CameraCalibrationNumpyData)
    if offset is None and target_shape is None:
        return calib_data

    assert isinstance(offset, HW)
    assert isinstance(target_shape, HW)

    assert calib_data.camera_metadata[CameraMetadataIndex.camera_height] >= target_shape.height
    assert calib_data.camera_metadata[CameraMetadataIndex.camera_width] >= target_shape.width

    calib_data.intrinsic[IntrinsicIndex.v0] -= offset.height
    calib_data.intrinsic[IntrinsicIndex.u0] -= offset.width
    calib_data.camera_metadata[CameraMetadataIndex.camera_height] = target_shape.height
    calib_data.camera_metadata[CameraMetadataIndex.camera_width] = target_shape.width
    return calib_data


def processing_config(task_id: str, camera: CameraName) -> TaskProcessingConfig[CameraCalibrationNumpyData]:
    """Generate a pyper processing configuration for the camera calibration data.

    Args:
        task_id: The task ID.
        camera: The camera name for which the calibration data should be extracted.

    Returns:
        The pyper processing configuration for reading the corresponding camera calibration data.
    """
    return TaskProcessingConfig(
        task_id=task_id,
        inflator=functools.partial(_get_calibration, camera=camera),
        adjuster=_adjust_calibration,
        ignorer=_ignorer,
        formatter=lambda labels: CameraCalibrationNumpyData(*labels),
    )


def read_calibration_data_from_file(
    json_file_path: Path | os.PathLike[str], camera: CameraName
) -> CameraCalibrationNumpyData:
    """Read the camera calibration data from a json file.

    Args:
        json_file_path: The path to the json calibration file.
        camera: The camera name for which the calibration data should be extracted.

    Returns:
        The camera calibration data in numpy format.
    """
    calib_raw_data = Path(json_file_path).read_bytes()
    return _get_calibration(calib_raw_data, camera)
