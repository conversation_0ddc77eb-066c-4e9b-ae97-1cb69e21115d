"""Functions to read the depth map data contained in a png file."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import functools
import os
from collections.abc import Sequence
from pathlib import Path
from typing import Final, NamedTuple

import numpy as np
import numpy.typing as npt

from data_formats.camera.pyper.conversion import Color, Depth, ImageInputConfig
from data_formats.camera.pyper.processing import inflator
from pyper.dict_pipeline import TaskProcessingConfig
from xcontract.data.definitions.image import HW


class DepthNumpyData(NamedTuple):
    """Depth data in numpy format.

    This format is required to enable the PyPer processing chain.

    Attributes:
        depth_map: Map containing the depth in meters for each camera pixel (npt.NDArray(1, H, W)).
        valid_mask: Validity of each vector in the the depth map (npt.NDArray(1, H, W)).
    """

    depth_map: npt.NDArray[np.float32]
    valid_mask: npt.NDArray[np.bool_]


_DEPTH_IMAGE_CONFIGURATION: Final = ImageInputConfig(
    color=Color.Y,
    channel_0_depth=Depth.SIXTEEN,
)

_EPSILON: Final = 1e-6  # minimum positive value for depth map


def _depth_map_loader(raw_data: bytes, target_shape: HW) -> DepthNumpyData:
    """Load the depth data from raw data contained in a png file.

    In addition to the depth map, a validity mask is generated.

    Pixels where the depth map is invalid (i.e. NaN or non-positive) are set to a minimum value _EPSILON, so as to avoid
    NaN or infinite values during the computation of the structure parameter. With a normal camera height of about 1 m,
    the structure parameter for invalid pixels will be approximately 1e6, which is implausible but still numeric.
    This makes it quite easy to spot invalid pixels and avoid inconsistencies when, for example, the validity mask
    is downscaled.

    Args:
        raw_data: The raw data loaded from the png file.
        target_shape: The expected target shape for the depth map.

    Returns:
        Final task label containing the processed depth data expressed in channel-first format [CHW].
    """
    depth_map_cm = inflator(raw_data, _DEPTH_IMAGE_CONFIGURATION)[0]

    assert depth_map_cm.dtype == np.uint16
    assert depth_map_cm.shape[0] >= target_shape.height, (
        f"Depth map height {depth_map_cm.shape[0]} exceeds target height {target_shape.height}."
    )
    assert depth_map_cm.shape[1] >= target_shape.width, (
        f"Depth map width {depth_map_cm.shape[1]} exceeds target width {target_shape.width}."
    )

    cm_to_m = np.float32(1e-2)
    depth_map_m = cm_to_m * depth_map_cm.astype(np.float32)
    assert depth_map_m.dtype == np.float32

    # Ensure that the depth map is valid (i.e. positive and numeric)
    valid_mask = depth_map_m > _EPSILON
    depth_map_m = np.where(valid_mask, depth_map_m, _EPSILON)

    # Convert from HWC to CHW format
    return DepthNumpyData(depth_map_m.transpose((2, 0, 1)), valid_mask.transpose((2, 0, 1)))


@functools.lru_cache
def _depth_ignorer(shape: HW) -> DepthNumpyData:
    """Returns a placeholder for ignored depth data with the desired target shape.

    Args:
        shape: The target shape for the depth data.

    Returns:
        Placeholder for ignored depth data expressed in channel-first format [CHW].
    """
    return DepthNumpyData(
        depth_map=np.zeros((1, shape.height, shape.width), dtype=np.float32),
        valid_mask=np.zeros((1, shape.height, shape.width), dtype=np.bool_),
    )


def _depth_map_adjuster(
    depth_data: Sequence[npt.NDArray[np.float32]], offset: HW | None, target_shape: HW | None
) -> DepthNumpyData:
    """Adjust the depth map data to the desired target shape and offset."""

    assert isinstance(depth_data, DepthNumpyData)
    if offset is None and target_shape is None:
        return depth_data  # No adjustment needed

    assert offset is not None
    assert target_shape is not None

    h_start, h_end = offset.height, offset.height + target_shape.height
    w_start, w_end = offset.width, offset.width + target_shape.width

    return DepthNumpyData(
        depth_data.depth_map[..., h_start:h_end, w_start:w_end],
        depth_data.valid_mask[..., h_start:h_end, w_start:w_end],
    )


def read_depth_map_from_file(depth_file_path: Path | os.PathLike[str], target_shape: HW) -> DepthNumpyData:
    """Read the depth map data from a png file.

    Args:
        depth_file_path: The path to the png file containing the depth map.
        target_shape: The expected target shape for the depth map.

    Returns:
        The depth map data in numpy, channel-first format [CHW].
    """
    return _depth_map_loader(raw_data=Path(depth_file_path).read_bytes(), target_shape=target_shape)


def processing_config(task_id: str, target_shape: HW) -> TaskProcessingConfig[DepthNumpyData]:
    """Generate a pyper processing configuration for the depth map data.

    Args:
        task_id: The task ID.
        target_shape: The target shape for the depth map.

    Returns:
        The pyper processing configuration for loading the depth map with the desired target shape.
    """
    return TaskProcessingConfig(
        task_id=task_id,
        inflator=functools.partial(_depth_map_loader, target_shape=target_shape),
        adjuster=_depth_map_adjuster,
        ignorer=functools.partial(_depth_ignorer, shape=target_shape),
        formatter=lambda labels: DepthNumpyData(*labels),
    )
