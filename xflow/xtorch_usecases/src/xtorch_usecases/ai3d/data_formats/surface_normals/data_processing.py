"""Helper functions for the ADA dataset loader."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import functools
import io
from collections.abc import Sequence
from pathlib import Path
from typing import NamedTuple

import numpy as np
import numpy.typing as npt

from pyper.dict_pipeline import TaskProcessingConfig
from xcontract.data.definitions.image import HW


class SurfaceNormalNumpyData(NamedTuple):
    """Surface normal data in numpy format.

    This format is required to enable the PyPer processing chain.

    Attributes:
        surface_normal_map: Surface normal vector map (npt.NDArray(3, H, W)).
        valid_mask: Validity of each vector in the the surface normal map (npt.NDArray(1, H, W)).
    """

    surface_normal_map: npt.NDArray[np.float32]
    valid_mask: npt.NDArray[np.bool_]


def load_surface_normals_map(surface_normals_map_path: str | Path, target_shape: HW) -> SurfaceNormalNumpyData:
    """Load surface normals (3D vector encoding) and performs post processing.

    1. Normalize by L2-norm
    2. Compute validity mask.

    Args:
        surface_normals_map_path : path to surface normals numpy map
        target_shape: The target shape for the surface normals map.

    Returns:
        SurfaceNormalNumpyData in channel-first format [CHW].
    """
    return _surface_normal_map_loader(Path(surface_normals_map_path).read_bytes(), target_shape)


def _surface_normal_map_loader(raw_data: bytes, target_shape: HW) -> SurfaceNormalNumpyData:
    """Load the surface normal data from raw data contained in a npy file.

    Pixels where the surface normal map is invalid are set to zero.

    Args:
        raw_data: The raw data loaded from the npy file.
        target_shape: The expected target shape of the surface normals map.

    Returns:
        Final task label containing the processed surface normal data expressed in channel-first format [CHW].
    """
    data = io.BytesIO(raw_data)
    surface_normal_map = np.lib.format.read_array(data)

    assert surface_normal_map.dtype == np.float32
    assert surface_normal_map.shape[0] >= target_shape.height
    assert surface_normal_map.shape[1] >= target_shape.width
    assert surface_normal_map.shape[2] == 3

    # Ensure that the surface normals are normalized
    _epsilon = 1e-6
    surface_normal_magnitude = np.linalg.norm(surface_normal_map, axis=-1, keepdims=True)
    valid_mask = surface_normal_magnitude > _epsilon
    surface_normal_map = np.divide(surface_normal_map, surface_normal_magnitude, where=valid_mask)

    # Set invalid normals to zero
    surface_normal_map = np.where(valid_mask, surface_normal_map, np.zeros_like(surface_normal_map))

    # Convert from HWC to CHW format
    return SurfaceNormalNumpyData(surface_normal_map.transpose((2, 0, 1)), valid_mask.transpose((2, 0, 1)))


@functools.lru_cache
def _surface_normal_ignorer(shape: HW) -> SurfaceNormalNumpyData:
    """Returns a placeholder for ignored surface normal data with the desired target shape.

    Args:
        shape: The target shape for the surface normals data.

    Returns:
        Placeholder for ignored surface normal data expressed in channel-first format [CHW].
    """
    return SurfaceNormalNumpyData(
        surface_normal_map=np.zeros((3, shape.height, shape.width), dtype=np.float32),
        valid_mask=np.zeros((1, shape.height, shape.width), dtype=bool),
    )


def _surface_normal_map_adjuster(
    surface_normal_data: Sequence[npt.NDArray[np.float32]],
    offset: HW | None,
    target_shape: HW | None,
    scale_factor: int,
) -> SurfaceNormalNumpyData:
    """Adjust the surface normal map data to the desired target shape and offset."""

    assert isinstance(surface_normal_data, SurfaceNormalNumpyData)

    if offset is None and target_shape is None:
        return surface_normal_data

    assert offset is not None
    assert target_shape is not None
    start = offset // scale_factor
    end = (offset + target_shape) // scale_factor
    surface_normal_map = surface_normal_data.surface_normal_map[..., start.height : end.height, start.width : end.width]
    valid_mask = surface_normal_data.valid_mask[..., start.height : end.height, start.width : end.width]
    return SurfaceNormalNumpyData(surface_normal_map, valid_mask)


def processing_config(
    task_id: str, target_shape: HW, adjuster_scale_factor: int
) -> TaskProcessingConfig[SurfaceNormalNumpyData]:
    """Generate a pyper processing configuration for the surface normal data.

    Note: The pipeline will return the surface normal map and validity mask in a channel-first format.

    Args:
        task_id: The task ID.
        target_shape: The target shape for the surface normals map.
        adjuster_scale_factor: The scale factor for adjusting the surface normal map.

    Returns:
        The pyper processing configuration for loading the surface normal data with the desired target shape.
    """
    return TaskProcessingConfig(
        task_id=task_id,
        inflator=functools.partial(_surface_normal_map_loader, target_shape=target_shape),
        adjuster=functools.partial(_surface_normal_map_adjuster, scale_factor=adjuster_scale_factor),
        ignorer=functools.partial(_surface_normal_ignorer, shape=target_shape),
        formatter=lambda labels: SurfaceNormalNumpyData(*labels),
    )
