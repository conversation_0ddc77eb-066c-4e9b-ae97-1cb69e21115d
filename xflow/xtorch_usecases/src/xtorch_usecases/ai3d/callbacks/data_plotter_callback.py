"""Callback for plotting AI3D data during training and validation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Sequence
from enum import Enum
from pathlib import Path
from typing import Any

import torch
from pytorch_lightning import LightningModule, Trainer
from pytorch_lightning.callbacks import Callback
from pytorch_lightning.utilities.types import STEP_OUTPUT

from xcontract.data.definitions.usage import ValueKey
from xtorch.training import Stage
from xtorch.training.training_module import StepOutputKey
from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.data_postprocessing.camera.common import (
    change_image_view,
    compute_camera_geometry_from_calibration,
)
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId, OutputId
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    OUTPUT_SUBSAMPLING_FACTOR,
    PLANE_NORMAL,
    TRAIN_INPUT_OFFSET,
    TRAIN_INPUT_RESOLUTION,
    ParallaxHeadOutput,
)
from xtorch_usecases.ai3d.utils.data_plotter import ImagePlotter, PosePlotter
from xtorch_usecases.ai3d.utils.structure_parameter import structure_parameter_to_depth
from xtorch_usecases.common.datasets.alliance.definitions import CameraName


class AI3DDataPlotterCallback(Callback):
    """AI3D data/image plotter callback."""

    BATCH_NR = 0  # Defines the index of the sample in each batch that will be plotted.

    def __init__(
        self,
        task_id: str,
        camera_name: CameraName,
        data_ids_to_log: Sequence[DataId],
        train_data_logging_frequency: int = 1,
        val_data_logging_frequency: int = 1,
        output_folder: Path = Path("outputs"),
    ) -> None:
        """Initialize the AI3D data/image plotter callback.

        Args:
            task_id: id of the parallax task.
            camera_name: Name of the camera used for the data.
            data_ids_to_log: List of data ids defining which data are used for the plots.
            output_folder: The output folder for plotting.
            train_data_logging_frequency: Batch number frequency at which training data are plotted.
            val_data_logging_frequency: Batch number frequency at which validation data are plotted.
        """
        super().__init__()
        self._task_id = task_id

        self._data_ids_to_log = data_ids_to_log

        if self._data_ids_to_log is None:
            msg = "List with DataIds (data_ids_to_log) must be provided."
            raise ValueError(msg)

        plot_pose = any(pose_id in data_ids_to_log for pose_id in [DataId.GT_POSE_ANGLE, DataId.GT_POSE_TRANSLATION])

        self._image_plotter = ImagePlotter()
        self._pose_plotter = PosePlotter() if plot_pose else None

        self._data_logging_frequency = {
            Stage.TRAIN: train_data_logging_frequency,
            Stage.VALIDATE: val_data_logging_frequency,
        }

        self._output_folder = output_folder
        self._view_parameters = ViewParameters(
            crop_u=TRAIN_INPUT_OFFSET[camera_name].width,
            crop_v=TRAIN_INPUT_OFFSET[camera_name].height,
            crop_width=TRAIN_INPUT_RESOLUTION[camera_name].width,
            crop_height=TRAIN_INPUT_RESOLUTION[camera_name].height,
            scale=1.0,
        )

        # Will be set during Callback setup
        self._log_dir = {}

    def _on_batch_end(self, stage: Stage, trainer: Trainer, outputs: STEP_OUTPUT, batch: Any, batch_idx: int) -> None:
        """Called at the end of each batch.

        Args:
            stage: The stage of the training process (TRAIN or VALIDATE).
            trainer: PyTorch Lightning trainer instance.
            outputs: Dictionary containing the loss & predictions.
            batch: Dictionary containing the input data to the network read by dataloader.
            batch_idx: Index of the current batch.
        """
        if self._pose_plotter is not None:
            pose_data = self._get_pose_data(outputs, batch)
            self._pose_plotter.store(pose_data, stage)

        if batch_idx % self._data_logging_frequency[stage] == 0:
            image_data = self._process_image_data(outputs, batch)
            current_image_name = self._get_current_image_name(batch)

            log_dir = self._log_dir[stage] / f"epoch_{trainer.current_epoch:03d}"
            figure_filepath = log_dir / f"prediction_epoch_{trainer.current_epoch:03d}_iter_{batch_idx:05d}.png"
            self._image_plotter.dump(image_data, figure_filepath, batch_nr=self.BATCH_NR, title=current_image_name)
            if self._pose_plotter is not None:
                self._pose_plotter.dump(log_dir, stage=stage)

    def _on_epoch_end(self, stage: Stage) -> None:
        """Called at the end of each epoch.

        Args:
            stage: The stage of the training process (TRAIN or VALIDATE).
        """
        if self._pose_plotter is not None:
            self._pose_plotter.clear(stage)

    def on_train_batch_end(
        self, trainer: Trainer, pl_module: LightningModule, outputs: STEP_OUTPUT, batch: Any, batch_idx: int
    ) -> None:
        """Log data during training.

        Args:
            trainer: PyTorch Lightning trainer instance.
            pl_module: The Pytorch Lightning module.
            outputs: Dictionary containing the loss & predictions.
            batch: Dictionary containing the input data to the network read by dataloader.
            batch_idx: Index of the current batch.
        """
        self._on_batch_end(Stage.TRAIN, trainer, outputs, batch, batch_idx)

    def on_train_epoch_end(self, trainer: Trainer, pl_module: LightningModule) -> None:
        """Clear data in plotter at the end of each epoch.

        Args:
            trainer: PyTorch Lightning trainer instance.
            pl_module: The Pytorch Lightning module.
        """
        self._on_epoch_end(Stage.TRAIN)

    def on_validation_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: STEP_OUTPUT,
        batch: Any,
        batch_idx: int,
        dataloader_idx: int = 0,
    ) -> None:
        """Log data during validation.

        Args:
            trainer: PyTorch Lightning trainer instance.
            pl_module: The Pytorch Lightning module.
            outputs: Dictionary containing the loss & predictions.
            batch: Dictionary containing the input data to the network read by dataloader.
            batch_idx: Index of the current batch.
            dataloader_idx: Unknown
        """
        self._on_batch_end(Stage.VALIDATE, trainer, outputs, batch, batch_idx)

    def on_validation_epoch_end(self, trainer: Trainer, pl_module: LightningModule) -> None:
        """Clear data in plotter at the end of each epoch.

        Args:
            trainer: PyTorch Lightning trainer instance.
            pl_module: The Pytorch Lightning module.
        """
        self._on_epoch_end(Stage.VALIDATE)

    def _create_epoch_log_dir(self, trainer: Trainer, stage: Stage) -> None:
        """Create the log directory for the current epoch."""
        self._log_dir[stage] = self._output_folder / stage.name
        log_dir = self._log_dir[stage] / f"epoch_{trainer.current_epoch:03d}"
        Path.mkdir(log_dir, parents=True, exist_ok=True)

    def on_train_epoch_start(self, trainer: Trainer, pl_module: LightningModule) -> None:
        """Called when the train epoch begins."""
        self._create_epoch_log_dir(trainer, Stage.TRAIN)

    def on_validation_epoch_start(self, trainer: Trainer, pl_module: LightningModule) -> None:
        """Called when the val epoch begins."""
        self._create_epoch_log_dir(trainer, Stage.VALIDATE)

    def _get_current_image_name(self, batch: dict[str, Any]) -> str | None:
        """Get the filename of the current image from the batch.

        Args:
            batch: Dictionary containing the input data to the network read by dataloader.

        Returns:
            The name of the current image, or None if not available.
        """
        if (
            DataId.CURRENT_IMAGE in self._data_ids_to_log
            and DataId.CURRENT_IMAGE in batch
            and ValueKey.IDENTIFIER in batch[DataId.CURRENT_IMAGE]
        ):
            image_path = batch[DataId.CURRENT_IMAGE][ValueKey.IDENTIFIER][self.BATCH_NR]
            if image_path and isinstance(image_path, str):
                return Path(image_path).name
        return None

    def _process_image_data(self, outputs: STEP_OUTPUT, batch: dict[str, Any]) -> dict[Enum, torch.Tensor]:
        """Collect and post-process the image data to be dumped to plots.

        Extracts the predictions from the model output and the corresponding input data from the batch.
        For post-processed inputs (i.e. height and depth maps), to save computation time, only the batch index defined
        by self.BATCH_NR is computed, since it will be the only one plotted.

        Args:
            outputs: Dictionary containing the network predictions.
            batch: Dictionary containing the input data to the network read by dataloader.

        Returns:
            Dictionary containing the data to be dumped.
        """
        parallax_output = self._extract_parallax_output(outputs)
        image_data = {}

        for data_id in self._data_ids_to_log:
            self._prepare_batch(batch, data_id)
            if data_id not in [DataId.GT_POSE_ANGLE, DataId.GT_POSE_TRANSLATION]:
                image_data[data_id] = batch[data_id][ValueKey.DATA]  # ground truth
                self._add_predictions(image_data, data_id, parallax_output, batch)
        return image_data

    def _get_pose_data(self, outputs: STEP_OUTPUT, batch: dict[str, Any]) -> dict[Enum, torch.Tensor]:
        """Collect the pose data to be dumped to plots.

        Differently from image data, poses are collected for all samples in the batch and for all batches.
        Correlation plots are updated only upon dumping.

        Args:
            outputs: Dictionary containing the network predictions.
            batch: Dictionary containing the input data to the network read by dataloader.

        Returns:
            Dictionary containing the data to be dumped.
        """
        parallax_output = self._extract_parallax_output(outputs)
        pose_data = {}

        if DataId.GT_POSE_TRANSLATION in self._data_ids_to_log:
            pose_data[DataId.GT_POSE_TRANSLATION] = batch[DataId.GT_POSE_TRANSLATION][ValueKey.DATA]
            assert isinstance(parallax_output.pose_translation, torch.Tensor)
            pose_data[OutputId.PRED_POSE_TRANSLATION] = parallax_output.pose_translation
        if DataId.GT_POSE_ANGLE in self._data_ids_to_log:
            pose_data[DataId.GT_POSE_ANGLE] = batch[DataId.GT_POSE_ANGLE][ValueKey.DATA]
            assert isinstance(parallax_output.pose_angle, torch.Tensor)
            pose_data[OutputId.PRED_POSE_ANGLE] = parallax_output.pose_angle
        return pose_data

    def _extract_parallax_output(self, outputs: STEP_OUTPUT) -> ParallaxHeadOutput:
        """Extract the Parallax head output from the model outputs.

        Args:
            outputs: Dictionary containing the network predictions.

        Returns:
            ParallaxHeadOutput: The output of the Parallax head.
        """
        assert isinstance(outputs, dict)

        model_output = outputs[StepOutputKey.PREDICTIONS]
        assert self._task_id in model_output, f"Task {self._task_id} not found in model output"

        parallax_output = model_output[self._task_id]
        assert isinstance(parallax_output, ParallaxHeadOutput), (
            f"Model output for ParallaxHead needs to be of type ParallaxHeadOutput. Found {type(parallax_output)}"
        )
        return parallax_output

    def _prepare_batch(self, batch: dict[str, Any], data_id: DataId) -> None:
        """Prepare batch with required post-processed ground truth in case it is missing (i.e. height, depth).

        Args:
            batch: Dictionary containing the input data to the network read by dataloader.
            data_id: The ID of the data to be prepared.
        """
        if data_id == DataId.GT_DEPTH_MAP:
            original_depth_map = batch[CSVDataId.GT_DEPTH][ValueKey.DATA].depth_map
            assert isinstance(original_depth_map, torch.Tensor)
            batch[DataId.GT_DEPTH_MAP] = {
                ValueKey.DATA: change_image_view(
                    image=original_depth_map, calibration=batch[CSVDataId.SYS_CALIB], target_view=self._view_parameters
                )
            }
        if data_id == DataId.GT_HEIGHT_MAP:
            assert self._data_ids_to_log.index(DataId.GT_HEIGHT_MAP) > self._data_ids_to_log.index(
                DataId.GT_DEPTH_MAP
            ), "Height map can only post-processed after depth map has been computed."
            batch[DataId.GT_HEIGHT_MAP] = {
                ValueKey.DATA: batch[DataId.GT_STRUCTURE_PARAMETER_MAP][ValueKey.DATA][self.BATCH_NR, ...]
                * batch[DataId.GT_DEPTH_MAP][ValueKey.DATA][self.BATCH_NR, ...]
            }

    def _add_predictions(
        self, image_data: dict[Enum, torch.Tensor], data_id: DataId, output: ParallaxHeadOutput, batch: dict[str, Any]
    ) -> None:
        """Add predictions corresponding to each ground truth data ID to dumped image data.

        Args:
            image_data: Dictionary containing the image data to be dumped to plots.
            data_id: The ID of the ground truth data to be plotted.
            output: The outputs of the Parallax head.
            batch: Dictionary containing the input data to the network read by dataloader.
        """
        # TODO: add image path to image dump
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/371536
        match data_id:
            case DataId.GT_STRUCTURE_PARAMETER_MAP:
                assert isinstance(output.structure_parameter, torch.Tensor)
                image_data[OutputId.PRED_STRUCTURE_PARAMETER_MAP] = output.structure_parameter
            # Depth and height map are not in the network outputs, and are computed from the structure parameter
            case DataId.GT_DEPTH_MAP:
                assert isinstance(output.structure_parameter, torch.Tensor)
                image_data[OutputId.PRED_DEPTH_MAP] = self._process_depth(output.structure_parameter, batch)
            case DataId.GT_HEIGHT_MAP:
                assert isinstance(output.structure_parameter, torch.Tensor)
                image_data[OutputId.PRED_HEIGHT_MAP] = (
                    output.structure_parameter[self.BATCH_NR, ...] * image_data[OutputId.PRED_DEPTH_MAP]
                )
            case DataId.GT_SURFACE_NORMALS_MAP:
                assert isinstance(output.surface_normals, torch.Tensor)
                image_data[OutputId.PRED_SURFACE_NORMALS_MAP] = output.surface_normals
            case _:
                pass

    def _process_depth(self, structure_parameter_map: torch.Tensor, batch: dict[str, Any]) -> torch.Tensor:
        """Process the depth map.

        Args:
            structure_parameter_map: Structure parameter map computed for the batch.
            batch: Dictionary containing the input data to the network read by dataloader.

        Returns:
            Depth map for the sample defined by self.BATCH_NR.
        """
        camera_geometry_net_output = compute_camera_geometry_from_calibration(
            calibration=batch[CSVDataId.SYS_CALIB], view=self._view_parameters.scaled(1 / OUTPUT_SUBSAMPLING_FACTOR)
        )
        if camera_geometry_net_output is None:
            return torch.zeros_like(structure_parameter_map[self.BATCH_NR, ...])

        plane_normal = PLANE_NORMAL.to(camera_geometry_net_output.device)
        camera_height = batch[DataId.CAMERA_HEIGHT][ValueKey.DATA]
        assert isinstance(camera_height, torch.Tensor)

        max_depth = 150  # max plotted depth in m
        return structure_parameter_to_depth(
            structure_parameter_map[self.BATCH_NR, ...],
            camera_geometry_net_output.light_rays,
            camera_height[self.BATCH_NR],
            plane_normal,
            max_depth=max_depth,
        )
