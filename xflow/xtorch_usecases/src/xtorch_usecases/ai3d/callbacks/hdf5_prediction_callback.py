"""Callback for handling prediction phase in AI3D."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import json
from dataclasses import asdict
from pathlib import Path

import numpy as np
import numpy.typing as npt
import torch
from pytorch_lightning import LightningModule, Trainer
from pytorch_lightning.utilities.types import STEP_OUTPUT
from scipy.spatial.transform import Rotation

from xcontract.data.definitions.image import HW
from xcontract.data.definitions.usage import ValueKey
from xtorch.callbacks.savers.prediction_saver import PredictionSaver
from xtorch.geometry.transformation.pose_transformation import PoseTransformation
from xtorch.geometry.transformation.quaternion import Quaternion
from xtorch.training.data_module import DataModuleConfig
from xtorch.training.data_module.base import DataModule
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.ai3d.camera import ViewParameters
from xtorch_usecases.ai3d.camera.camera_geometry import CameraGeometry
from xtorch_usecases.ai3d.camera.camera_intrinsics import CameraIntrinsicParameters
from xtorch_usecases.ai3d.data_postprocessing.camera.common import (
    change_image_view,
    compute_camera_geometry_from_calibration,
    compute_intrinsic_parameters,
)
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId, OutputId
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    INFERENCE_INPUT_RESOLUTION,
    OUTPUT_SUBSAMPLING_FACTOR,
    PLANE_NORMAL,
    UNCROPPED_INPUT_RESOLUTION,
    EnabledHeads,
    InferenceDataSource,
    InferenceResultExportParams,
    ParallaxHeadOutput,
)
from xtorch_usecases.ai3d.utils import (
    StreetPatchParameters,
    generate_static_street_mask,
    structure_parameter_to_depth,
)
from xtorch_usecases.ai3d.utils.hdf_ai3d_writer import Ai3dHdfWriter, GroupDatasets
from xtorch_usecases.ai3d.utils.height_to_plane_surface import compute_height_to_plane_surface
from xtorch_usecases.ai3d.utils.street_normal_estimation import (
    apply_mask,
    calculate_average_normal_vector,
    combine_masks,
    create_outlier_mask,
)
from xtorch_usecases.common.datasets.alliance.definitions import CameraName
from xtorch_usecases.common.pipeline import EVALUATE_EVIL_SUBFOLDER


class Hdf5PredictionCallback(PredictionSaver):
    """Callback for handling prediction phase in AI3D."""

    def __init__(
        self,
        enabled_heads: EnabledHeads,
        inference_source: InferenceDataSource,
        output_folder: Path = Path("outputs"),
        task_id: str = "parallax",
        *,
        camera_name: CameraName,
        delete_predictions_afterwards: bool = False,
        export_parameters: InferenceResultExportParams,
    ) -> None:
        """Initialize the AI3D prediction callback.

        Args:
            enabled_heads: EnabledHeads dataclass with boolean flags for enabled heads
                (structure_parameter, surface_normals, pose).
            output_folder: The base output folder for predictions.
            task_id: ID of the task to extract outputs for. Default is "parallax".
            camera_name: Name of the camera used for inference.
            inference_source: Source of the inference data, either HDF5_SEQUENCE or CSV_FILE.
            delete_predictions_afterwards: If True predictions will be deleted on evaluation end.
            export_parameters: Parameters for exporting inference results, e.g. street mask parameters and wheel radius.
        """
        super().__init__(
            task_id=task_id,
            save_sample_predictions=False,
            evaluation_folder=str(output_folder / EVALUATE_EVIL_SUBFOLDER),
            delete_predictions_afterwards=delete_predictions_afterwards,
        )

        # When running inference from a csv file we expect that only the outputs of the enabled heads are available.
        self._expected_output_ids = set()
        if inference_source == InferenceDataSource.CSV_FILE:
            if enabled_heads.structure_parameter:
                self._expected_output_ids.add(OutputId.PRED_STRUCTURE_PARAMETER_MAP)
            if enabled_heads.surface_normals:
                self._expected_output_ids.add(OutputId.PRED_SURFACE_NORMALS_MAP)
            if enabled_heads.pose:
                self._expected_output_ids.add(OutputId.PRED_POSE_TRANSLATION)
                self._expected_output_ids.add(OutputId.PRED_POSE_ANGLE)
        # For HDF5 sequence inference we expect that all heads are enabled
        else:
            self._expected_output_ids = {
                OutputId.PRED_STRUCTURE_PARAMETER_MAP,
                OutputId.PRED_SURFACE_NORMALS_MAP,
                OutputId.PRED_POSE_TRANSLATION,
                OutputId.PRED_POSE_ANGLE,
            }

        self._hdf_writer = None
        self._camera_name = camera_name
        self._inference_source = inference_source
        self._export_parameters = export_parameters

        # Assume that GT data are availalbe except for HDF5 sequences.
        self._gt_available = inference_source != InferenceDataSource.HDF5_SEQUENCE

        # Input view parameters
        self._view_offset = (
            (UNCROPPED_INPUT_RESOLUTION[camera_name] - INFERENCE_INPUT_RESOLUTION[camera_name]) // 2
            if inference_source != InferenceDataSource.HDF5_SEQUENCE
            else HW(0, 0)
        )

        # Below values are filled from batch data when available
        self.camera_height = None
        self.camera_extrinsic = None
        self.input_intrinsic_parameters = None
        self.output_intrinsic_parameters = None
        self.camera_geometry_net_input = None
        self.camera_geometry_net_output = None

    def _create_hdf_writer(
        self,
        dataset_name: str,
        hdf_output_root: Path,
        input_intrinsics: CameraIntrinsicParameters,
        output_intrinsics: CameraIntrinsicParameters,
        camera_height: float,
    ) -> None:
        """Create the HDF writer for saving predictions.

        Args:
            dataset_name: Name of the dataset being processed.
            hdf_output_root: Root directory for saving the HDF5 output.
            input_intrinsics: Camera intrinsic parameters of the input image.
            output_intrinsics: Camera intrinsic parameters of the output image.
            camera_height: Height of the camera above the street plane.
        """
        if self._hdf_writer:
            msg = "HDF writer is already created. Cannot create it again."
            raise RuntimeError(msg)

        # Camera name as string representation
        camera_name = self._camera_name.name

        export_groups = [
            GroupDatasets.VISUAL_ODOMETRY,
            GroupDatasets.SURFACE_NORMALS,
            GroupDatasets.STREET_SURFACE_NORMAL,
            GroupDatasets.STRUCTURE_PARAMS,
            GroupDatasets.EXTRINSIC,
            GroupDatasets.INFERENCE_INPUT_VISU,
        ]

        # We expect that only for HDF5 sequnces the ground truth data is missing.
        if self._gt_available:
            export_groups += [
                GroupDatasets.VISUAL_ODOMETRY_GT,
                GroupDatasets.SURFACE_NORMALS_GT,
                GroupDatasets.STREET_SURFACE_NORMAL_GT,
                GroupDatasets.STRUCTURE_PARAMS_GT,
            ]

        input_intrinsics_json = json.dumps(asdict(input_intrinsics))
        output_intrinsics_json = json.dumps(asdict(output_intrinsics))

        intrinsics = {
            "model_type": input_intrinsics.camera_type.name,
            "input_intrinsic_data": input_intrinsics_json,
            "output_intrinsic_data": output_intrinsics_json,
        }

        self._hdf_writer = Ai3dHdfWriter(
            filepath=Path(hdf_output_root) / f"{dataset_name}.hdf5",
            sequence_name=dataset_name,
            camera_name=camera_name,
            groups=export_groups,
            intrinsics=intrinsics,
            reference_plane={"reference_plane_normal": PLANE_NORMAL.numpy(), "reference_plane_height": camera_height},
            capacity=500,
        )

    def on_predict_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: STEP_OUTPUT,
        batch: OnDeviceBatchedTrainingDict,
        batch_idx: int,
        dataloader_idx: int = 0,
    ) -> None:
        """Save and validate predictions when the predict batch ends.

        Args:
            trainer: The Trainer instance.
            pl_module: The LightningModule.
            outputs: Dictionary containing the network predictions.
            batch: Dictionary containing the input data to the network read by dataloader.
            batch_idx: Index of the batch.
            dataloader_idx: Index of the dataloader.
        """
        # Extract the parallax head output using the utility function
        assert isinstance(outputs, dict)
        parallax_output = outputs["torch"][self._task_id]

        self._validate_expected_outputs(parallax_output)

        # Ensure the parameters for prediction are avaialble
        # if trainer.predict_dataloaders and hasattr(trainer.predict_dataloaders, "config"):
        self._save_predictions(trainer, parallax_output, batch)

    def _validate_expected_outputs(self, parallax_output: ParallaxHeadOutput) -> None:
        """Validate that the model output contains the expected outputs.

        Args:
            parallax_output: The output of the parallax head.

        Raises:
            ValueError: If expected outputs are missing.
        """
        outputs = {
            OutputId.PRED_STRUCTURE_PARAMETER_MAP: parallax_output.structure_parameter,
            OutputId.PRED_SURFACE_NORMALS_MAP: parallax_output.surface_normals,
            OutputId.PRED_POSE_TRANSLATION: parallax_output.pose_translation,
            OutputId.PRED_POSE_ANGLE: parallax_output.pose_angle,
        }

        available_outputs = {output_id for output_id, value in outputs.items() if value is not None}

        # Find any missing expected outputs
        missing = self._expected_output_ids - available_outputs
        if missing:
            msg = f"Missing expected OutputId(s) in model output: {missing}"
            raise ValueError(msg)

    def _downscale(
        self,
        input_map: torch.Tensor,
        to_shape: tuple[int, ...],
    ) -> torch.Tensor:
        """Downscale the given map by a factor.

        Args:
            input_map: The map to downscale, with shape (B, H, W) or (B, C, H, W).
            to_shape: Target shape (H_new, W_new).

        Returns:
            Downscaled map.
        """
        assert len(to_shape) == 2
        assert len(input_map.shape) in (3, 4)

        # Ensure input_map is in the shape (B, C, H, W)
        input_ndim = input_map.ndim
        if input_ndim == 3:
            input_map = input_map.unsqueeze(1)

        # Downscale the input map using bilinear interpolation or nearest neighbor
        if input_map.dtype == torch.bool:
            input_float = input_map.float()
            downscaled = torch.nn.functional.interpolate(input_float, size=to_shape, mode="nearest")
            downscaled = downscaled.bool()
        else:
            downscaled = torch.nn.functional.interpolate(input_map, size=to_shape, mode="bilinear", align_corners=False)

        # If the input was 3D, we need to squeeze the channel dimension back out
        if input_ndim == 3:
            downscaled = downscaled.squeeze(1)

        return downscaled

    def _get_batch_data(
        self,
        batch: OnDeviceBatchedTrainingDict,
        data_id: DataId | CSVDataId,
        member: str | None = None,
    ) -> torch.Tensor:
        """Get valid data from the batch for a specific data ID.

        Args:
            batch: Dictionary containing the input data to the network read by dataloader.
            data_id: The ID of the data to retrieve from the batch.
            member: Optional member name to access a specific attribute of the data.

        Returns:
            The data tensor.
        """
        validity = batch[data_id][ValueKey.VALID]
        assert isinstance(validity, torch.Tensor)
        data = batch[data_id][ValueKey.DATA]

        if member is not None:
            assert hasattr(data, member), f"Data does not have member '{member}'."
            data = getattr(data, member)

        assert isinstance(data, torch.Tensor)
        return data[validity]

    def _get_output_data(
        self,
        outputs: ParallaxHeadOutput,
        member: str,
        batch: OnDeviceBatchedTrainingDict,
        data_id: DataId | CSVDataId,
    ) -> torch.Tensor:
        """Get output data for valid batch samples.

        Args:
            outputs: Network predictions.
            member: The member name to access a specific attribute of the output.
            batch: Dictionary containing the input data to the network read by dataloader.
            data_id: The ID of the batch data to retrieve validity information.

        Returns:
            The data tensor for the specified member.
        """
        validity = batch[data_id][ValueKey.VALID]
        assert isinstance(validity, torch.Tensor)
        data = getattr(outputs, member)
        assert isinstance(data, torch.Tensor)
        return data[validity]

    def _get_input_visu(self, batch: OnDeviceBatchedTrainingDict) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get input data for visualization.

        Args:
            batch: Dictionary containing the input data to the network read by dataloader.

        Returns:
            Dictionary with input data for visualization.
        """
        # Currently, we only save the current image as input visualization.
        current_image = self._get_batch_data(batch, DataId.CURRENT_IMAGE)
        return {
            GroupDatasets.INFERENCE_INPUT_VISU.group_name(): {
                "current_image": current_image.permute(0, 2, 3, 1).cpu().numpy()
            }
        }

    def _get_camera_extrinsic(self) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get camera extrinsic data for visualization.

        Returns:
            Dictionary with camera extrinsic data.
        """

        assert isinstance(self.camera_extrinsic, PoseTransformation)
        assert isinstance(self.camera_extrinsic.translation, torch.Tensor)
        assert isinstance(self.camera_extrinsic.rotation.tensor, torch.Tensor)

        return {
            GroupDatasets.EXTRINSIC.group_name(): {
                "translation": self.camera_extrinsic.translation.cpu().numpy(),
                "rotation": self.camera_extrinsic.rotation.tensor.cpu().numpy(),
            }
        }

    def _get_structure_parameter(
        self, struct_maps: torch.Tensor, depth_maps: torch.Tensor
    ) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get structure parameter data for visualization.

        Args:
            struct_maps: Structure parameter maps with shape [batch_size, 1, Height, Width].
            depth_maps: Depth map with shape [batch_size, 1, Height, Width].

        Returns:
            A dictionary containing structure parameter data, including:
                - "structure_parameters": Numpy array of structure parameters with shape [batch_size, Height, Width].
                - "depth": Numpy array of depth maps with shape [batch_size, Height, Width].
                - "height": Numpy array of height maps with shape [batch_size, Height, Width].
        """
        # Height is simply the product of structure parameter and depth map
        height_maps = struct_maps * depth_maps  # shape [batch_size, 1, Height, Width]

        return {
            GroupDatasets.STRUCTURE_PARAMS.group_name(): {
                # We want BHWC, but here we don't need channels, so we just "delete" the C axis by indexing with 0
                "structure_parameters": struct_maps.cpu().numpy().squeeze(1),
                # depth and height maps come in BHWC
                "depth": np.asarray(depth_maps.cpu()).squeeze(1),
                "height": np.asarray(height_maps.cpu()).squeeze(1),
            }
        }

    def _get_metadata(self, batch: OnDeviceBatchedTrainingDict) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get metadata for visualization.

        Args:
            batch: Dictionary containing the input data to the network read by dataloader.

        Returns:
            Dictionary with metadata.
        """
        data_source = (
            DataId.CURRENT_IMAGE_META
            if self._inference_source == InferenceDataSource.HDF5_SEQUENCE
            else CSVDataId.CURRENT_POSE
        )
        current_timestamps = self._get_batch_data(batch, data_source, "timestamp").cpu().numpy()
        if self._inference_source == InferenceDataSource.HDF5_SEQUENCE:
            current_frame_numbers = self._get_batch_data(batch, data_source, "frame_number").cpu().numpy()
        else:
            # TODO: Handle frame numbers for CSV data source
            # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/400346
            current_frame_numbers = np.zeros_like(current_timestamps)

        return {
            "metadata": {
                "timestamp": current_timestamps,
                "frame_number": current_frame_numbers,
            }
        }

    def _get_visual_odometry(
        self, outputs: ParallaxHeadOutput, batch: OnDeviceBatchedTrainingDict
    ) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get visual odometry data for visualization.

        Args:
            outputs: Network predictions.
            batch: Dictionary containing the input data to the network read by dataloader.

        Returns:
            Dictionary with visual odometry data.
        """
        pose_translation = self._get_output_data(outputs, "pose_translation", batch, DataId.PRIOR_POSE)
        pose_angle = self._get_output_data(outputs, "pose_angle", batch, DataId.PRIOR_POSE)
        return {
            GroupDatasets.VISUAL_ODOMETRY.group_name(): {
                "relative_translation": pose_translation.cpu().numpy(),
                "relative_rotation": Rotation.from_euler("ZYX", pose_angle.cpu().numpy(), degrees=True).as_quat(),
            }
        }

    def _get_surface_normals(
        self, normal_vectors: npt.NDArray[np.float64]
    ) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get surface normals data for visualization.

        Args:
            normal_vectors: Normalized surface normal vectors.

        Returns:
            Dictionary with surface normals data.
        """
        return {
            GroupDatasets.SURFACE_NORMALS.group_name(): {
                "normal_vector": normal_vectors,
            }
        }

    def _get_street_surface_normal(
        self,
        street_normal_vector_pred_batch: npt.NDArray[np.float64],
        batch_street_points: list[torch.Tensor],
        max_number_of_street_points: int,
    ) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get street surface normal data.

        Args:
            street_normal_vector_pred_batch: Predicted street surface normal vectors.
            batch_street_points: List of street points for each batch entry.
            max_number_of_street_points: Maximum number of street points across all batches.

        Returns:
            Dictionary with street surface normal data.
        """
        # Estimate the camera height from the street surface points points for each batch entry
        camera_height_results = [
            compute_height_to_plane_surface(street_points) for street_points in batch_street_points
        ]
        # Convert None to np.nan if height could not be calculated from the given data
        camera_heights = [height_result[0] if height_result else np.nan for height_result in camera_height_results]

        batch_street_points_padded = [
            np.pad(point_batch.cpu(), ((0, max_number_of_street_points - len(point_batch)), (0, 0)))
            for point_batch in batch_street_points
        ]

        return {
            GroupDatasets.STREET_SURFACE_NORMAL.group_name(): {
                "street_normal_vector": street_normal_vector_pred_batch,
                "camera_height": np.asarray(
                    [
                        camera_height.cpu().numpy() if isinstance(camera_height, torch.Tensor) else np.nan
                        for camera_height in camera_heights
                    ]
                )[..., np.newaxis],
                "street_points": np.array(batch_street_points_padded, dtype=np.float64),
                "number_of_street_points": np.asarray([len(street_points) for street_points in batch_street_points])[
                    ..., np.newaxis
                ],
            },
        }

    def _get_gt_output(
        self,
        batch: OnDeviceBatchedTrainingDict,
        light_rays: torch.Tensor,
        camera_geometry_net_input: CameraGeometry,
        camera_extrinsic: PoseTransformation,
    ) -> dict[str, dict[str, npt.NDArray[np.float64]]]:
        """Get ground truth output data and prepare the for hdf5 export.

        Note:
            The ground truth output dimensions may differ from the network output due to
            interpolation in the loss function.

        Args:
            batch: A dictionary containing input data
                read by the dataloader, including ground truth depth, structure maps,
                and camera parameters.
            light_rays: Tensor representing the light rays used for
                depth calculations.
            camera_geometry_net_input: Input data structure containing
                camera geometry information.
            camera_extrinsic: Transformation data representing the
                camera's extrinsic parameters.

        Returns:
            A dictionary containing ground truth output data, including structure parameters, depth maps,
            height maps, relative translations, relative rotations, surface normals,
            street normal vectors, camera heights, and street points.
        """
        original_depth_map = getattr(batch[CSVDataId.GT_DEPTH][ValueKey.DATA], "depth_map", None)
        assert isinstance(original_depth_map, torch.Tensor)

        batch[DataId.GT_DEPTH_MAP] = {
            ValueKey.DATA: change_image_view(
                image=original_depth_map,
                calibration=batch[CSVDataId.SYS_CALIB],
                target_view=camera_geometry_net_input._image_view,  # noqa: SLF001
            ),
            ValueKey.VALID: batch[CSVDataId.GT_DEPTH][ValueKey.VALID],
        }
        struct_maps = self._get_batch_data(batch, DataId.GT_STRUCTURE_PARAMETER_MAP)
        depth_maps = self._get_batch_data(batch, DataId.GT_DEPTH_MAP)

        batch[DataId.GT_HEIGHT_MAP] = {
            ValueKey.DATA: struct_maps * depth_maps,
            ValueKey.VALID: torch.from_numpy(np.ones(len(depth_maps), dtype=bool)).to(depth_maps.device),
        }

        height_maps = self._get_batch_data(batch, DataId.GT_HEIGHT_MAP)
        relative_translation = self._get_batch_data(batch, DataId.GT_POSE_TRANSLATION)
        relative_rotation = self._get_batch_data(batch, DataId.GT_POSE_ANGLE)
        camera_height = self._get_batch_data(batch, DataId.CAMERA_HEIGHT)
        normal_vector = self._get_batch_data(batch, DataId.GT_SURFACE_NORMALS_MAP)
        gt_surface_normals_maps = np.moveaxis(normal_vector.cpu().numpy(), 1, -1)

        # Probably not necessary
        gt_surface_normals_maps = (
            gt_surface_normals_maps / np.linalg.norm(gt_surface_normals_maps, axis=-1)[..., np.newaxis]
        )

        street_mask = ~generate_static_street_mask(
            camera=camera_geometry_net_input,
            vehicle_rear_axle_from_camera_transformation=camera_extrinsic,
            street_patch_parameters=StreetPatchParameters(**asdict(self._export_parameters.street_mask_parameters)),
            wheel_radius=self._export_parameters.wheel_radius,
        )

        # ground truth surface normal map is in shape [batch_size, height_output, width_output, 3]
        # street_mask is in shape [batch_size, height_input, width_input]
        # therefore downscale the street mask to the output shape
        output_shape = gt_surface_normals_maps.shape[1:-1]
        street_mask_downscaled = self._downscale(street_mask, output_shape).cpu().numpy()

        prior = PLANE_NORMAL.numpy()
        prior_normalized = prior / np.linalg.norm(prior)

        outlier_mask_batch = create_outlier_mask(
            gt_surface_normals_maps,
            prior=prior_normalized,
            tolerance=self._export_parameters.max_angle_of_normal_to_upward_facing_normal,
        )

        # Now only keep the normal vectors that are not outliers and are inside the street mask.
        mask_combined_batch, _ = combine_masks(street_mask_downscaled, outlier_mask_batch=outlier_mask_batch)
        gt_normal_vector_image_masked_batch = apply_mask(gt_surface_normals_maps, mask_combined_batch)

        street_normal_vector_pred_batch, covariance_kartesian, covariance_angular = calculate_average_normal_vector(
            gt_normal_vector_image_masked_batch
        )

        depth_maps = structure_parameter_to_depth(
            struct_maps,
            light_rays,
            camera_height,
            PLANE_NORMAL.to(light_rays.device),
            max_depth=self._export_parameters.max_depth,
        )  # shape [batch_size, 1, Height, Width]

        # combined street mask is given in shape [batch_size, height_output, width_output]
        # light rays map is given in shape [height_input, width_input, 3]
        # depth map is given in shape [batch_size, 1, height_input, width_input]
        # therfore downscale the light rays and depth maps to the output shape
        light_rays_downscaled = self._downscale(light_rays.permute(2, 0, 1), output_shape).permute(1, 2, 0)
        depth_maps_downscaled = self._downscale(depth_maps, output_shape)
        street_positive_mask_batch = np.invert(mask_combined_batch)
        batch_street_points = [
            (depth_map[:, street_positive_mask].view(-1, 1) * light_rays_downscaled[street_positive_mask])
            for depth_map, street_positive_mask in zip(depth_maps_downscaled, street_positive_mask_batch)
        ]

        # Pad each batch entry to have the same size.
        max_number_of_street_points = street_mask_downscaled.size - np.count_nonzero(street_mask_downscaled)

        camera_height_results = [
            compute_height_to_plane_surface(street_points) for street_points in batch_street_points
        ]
        # Convert to numpy array and set None to np.nan if height could not be calculated from the given data
        camera_heights = np.array(
            [
                height_result[0].cpu().numpy()
                if (height_result and isinstance(height_result[0], torch.Tensor))
                else np.nan
                for height_result in camera_height_results
            ]
        )[..., np.newaxis]

        batch_street_points_padded = [
            np.pad(point_batch.cpu(), ((0, max_number_of_street_points - len(point_batch)), (0, 0)))
            for point_batch in batch_street_points
        ]

        return {
            GroupDatasets.STRUCTURE_PARAMS_GT.group_name(): {
                # all map are given in (B, C, H, W) format
                "structure_parameters": struct_maps.squeeze(1).cpu().numpy(),
                "depth": depth_maps.squeeze(1).cpu().numpy(),
                "height": height_maps.squeeze(1).cpu().numpy(),
            },
            GroupDatasets.VISUAL_ODOMETRY_GT.group_name(): {
                # tranlstaion is given in (B, 3) format
                "relative_translation": relative_translation.cpu().numpy(),
                # rotation is given in (B, 3) format, converted to quaternion
                "relative_rotation": Rotation.from_euler(
                    "ZYX", relative_rotation.cpu().numpy(), degrees=True
                ).as_quat(),
            },
            GroupDatasets.SURFACE_NORMALS_GT.group_name(): {
                # surface normal vector is given in (B, 3, H, W) format
                "normal_vector": normal_vector.permute(0, 2, 3, 1).cpu().numpy(),
            },
            GroupDatasets.STREET_SURFACE_NORMAL_GT.group_name(): {
                # street normal is given in (B, 3) format
                "street_normal_vector": street_normal_vector_pred_batch,
                # height is given in (B, 1) format
                "camera_height": camera_heights,
                # street points are given in (B, max_number_of_street_points, 3) format
                "street_points": np.array(batch_street_points_padded, dtype=np.float64),
                # number of points is given in (B, 1) format
                "number_of_street_points": np.asarray([len(street_points) for street_points in batch_street_points])[
                    ..., np.newaxis
                ],
            },
        }

    def _get_camera_parameters(
        self,
        batch: OnDeviceBatchedTrainingDict,
    ) -> None:
        """Set class members to camera parameters from the batch.

        Args:
            batch: Dictionary containing the input data to the network read by dataloader.
        """

        # Get the camera extrinsic and intrinsic
        camera_metadata = self._get_batch_data(batch, CSVDataId.SYS_CALIB, "camera_metadata")
        extrinsic_rotation = self._get_batch_data(batch, CSVDataId.SYS_CALIB, "rotation")
        extrinsic_translation = self._get_batch_data(batch, CSVDataId.SYS_CALIB, "translation")
        camera_intrinsic = self._get_batch_data(batch, CSVDataId.SYS_CALIB, "intrinsic")
        self.camera_height = self._get_batch_data(batch, DataId.CAMERA_HEIGHT)

        self.camera_extrinsic = PoseTransformation(
            translation=extrinsic_translation, rotation=Quaternion(extrinsic_rotation)
        )

        self.input_intrinsic_parameters = compute_intrinsic_parameters(camera_metadata[0], camera_intrinsic[0])

        # Get the camera model from the stored calibration and adjust it to the
        # output resolution/view we use for inference
        view_size = HW(
            self.input_intrinsic_parameters.full_image_height, self.input_intrinsic_parameters.full_image_width
        ) - (self._view_offset * 2)
        input_view = ViewParameters(
            crop_u=self._view_offset.width,
            crop_v=self._view_offset.height,
            crop_width=view_size.width,
            crop_height=view_size.height,
            scale=1.0,
        )

        self.camera_geometry_net_output = compute_camera_geometry_from_calibration(
            calibration=batch[CSVDataId.SYS_CALIB], view=input_view.scaled(1 / OUTPUT_SUBSAMPLING_FACTOR)
        )
        assert self.camera_geometry_net_output is not None

        self.camera_geometry_net_input = compute_camera_geometry_from_calibration(
            calibration=batch[CSVDataId.SYS_CALIB], view=input_view
        )
        assert self.camera_geometry_net_input is not None

        self.output_intrinsic_parameters = self.camera_geometry_net_output.intrinsics_for_view()

    def _save_predictions(
        self,
        trainer: Trainer,
        outputs: ParallaxHeadOutput,
        batch: OnDeviceBatchedTrainingDict,
    ) -> None:
        """Save predictions to the output directory.

        Args:
            trainer: The Trainer instance.
            outputs: Network predictions.
            batch: Dictionary containing the input data to the network read by dataloader.
        """

        # Set class members to camera specific parameters
        self._get_camera_parameters(batch)

        assert isinstance(self.camera_extrinsic, PoseTransformation)
        assert isinstance(self.input_intrinsic_parameters, CameraIntrinsicParameters)
        assert isinstance(self.output_intrinsic_parameters, CameraIntrinsicParameters)
        assert isinstance(self.camera_geometry_net_input, CameraGeometry)
        assert isinstance(self.camera_geometry_net_output, CameraGeometry)
        assert isinstance(self.camera_height, torch.Tensor)

        if not self._hdf_writer:
            # Create the HDF writer during first call
            # to _save_predictions, so we have access to the trainer and batch data
            assert isinstance(trainer.datamodule, DataModule)  # type: ignore[union-attr]
            assert isinstance(trainer.datamodule.config, DataModuleConfig)  # type: ignore[union-attr]
            assert isinstance(trainer.datamodule.config.dataset_name, str)  # type: ignore[union-attr]
            dataset_name = Path(trainer.datamodule.config.dataset_name).stem  # type: ignore[union-attr]
            self._create_hdf_writer(
                dataset_name=dataset_name,
                hdf_output_root=Path(trainer.default_root_dir),
                input_intrinsics=self.input_intrinsic_parameters,
                output_intrinsics=self.output_intrinsic_parameters,
                camera_height=self.camera_height[0].item(),
            )
        assert isinstance(self._hdf_writer, Ai3dHdfWriter)

        # Extract the predicted surface normal vectors and normalize them
        # hint: normal_vector_map_pred has shape: (NumSamples, Height, Width, 3)
        surface_normals = self._get_output_data(outputs, "surface_normals", batch, DataId.CURRENT_IMAGE)
        normal_vector_map_pred = np.moveaxis(surface_normals.cpu().numpy(), 1, -1)
        normal_vector_map_pred = (
            normal_vector_map_pred / np.linalg.norm(normal_vector_map_pred, axis=-1)[..., np.newaxis]
        )
        assert normal_vector_map_pred.shape[-1] == 3

        # The prior is the surface normal vector that we expect, points in z-direction according to DIN ISO 23150
        prior = PLANE_NORMAL.numpy()
        prior_normalized = prior / np.linalg.norm(prior)

        # Given a rectangular region in the xy plane of the vehicle coordinate system (VRL),
        # Use the corresponding camera model and the camera extrinsic to project this region into
        # the image plane and create a binary mask for the street patch.
        street_mask = ~generate_static_street_mask(
            camera=self.camera_geometry_net_output,
            vehicle_rear_axle_from_camera_transformation=self.camera_extrinsic,
            street_patch_parameters=StreetPatchParameters(**asdict(self._export_parameters.street_mask_parameters)),
            wheel_radius=self._export_parameters.wheel_radius,
        )

        # We expect that all relevant normal vectors are pointing upwards,
        # so we can use the prior to detect outliers.
        outlier_mask_batch = create_outlier_mask(
            normal_vector_map_pred,
            prior=prior_normalized,
            tolerance=self._export_parameters.max_angle_of_normal_to_upward_facing_normal,
        )

        # Now only keep the normal vectors that are not outliers and are inside the street mask.
        mask_combined_batch, outlier_ratio = combine_masks(
            street_mask.cpu().numpy(), outlier_mask_batch=outlier_mask_batch
        )
        normal_vector_image_masked_batch = apply_mask(normal_vector_map_pred, mask_combined_batch)

        # The predicted normal vector batch contains one 3D vector for each inference result. Its shape is:
        # [number of samples in the batch, 3]
        # The covariance is currently not used. The code below calculates it anyway for debugging purposes and for
        # later integration.
        # The diagonal elements represent the variance of the individual parameters and can be converted to their
        # standard deviation by taking their square root.
        street_normal_vector_pred_batch, covariance_kartesian, covariance_angular = calculate_average_normal_vector(
            normal_vector_image_masked_batch
        )

        output_light_rays = self.camera_geometry_net_output.light_rays  # shape [height, width, 3]
        input_light_rays = self.camera_geometry_net_input.light_rays

        # shape [batch_size, 1, Height, Width]
        struct_maps = self._get_output_data(outputs, "structure_parameter", batch, DataId.CURRENT_IMAGE)

        # Note: The unit of the depth is the same as the unit of camera_height.
        depth_maps = structure_parameter_to_depth(
            struct_maps,
            output_light_rays,
            self.camera_height,
            PLANE_NORMAL.to(output_light_rays.device),
            max_depth=self._export_parameters.max_depth,
        )  # shape [batch_size, 1, Height, Width]

        street_positive_mask_batch = np.invert(mask_combined_batch)
        batch_street_points = [
            (depth_map[:, street_positive_mask].view(-1, 1) * output_light_rays[street_positive_mask])
            for depth_map, street_positive_mask in zip(depth_maps, street_positive_mask_batch)
        ]

        # Pad each batch entry to have the same size.
        max_number_of_street_points = street_mask.cpu().numpy().size - np.count_nonzero(street_mask.cpu().numpy())

        # Prepare the data to be saved in HDF5 format
        hdf_export_data = {}

        hdf_export_data.update(
            self._get_street_surface_normal(
                street_normal_vector_pred_batch, batch_street_points, max_number_of_street_points
            )
        )
        hdf_export_data.update(self._get_surface_normals(normal_vector_map_pred))
        hdf_export_data.update(self._get_visual_odometry(outputs, batch))
        hdf_export_data.update(self._get_metadata(batch))
        hdf_export_data.update(self._get_structure_parameter(struct_maps, depth_maps))
        hdf_export_data.update(self._get_camera_extrinsic())
        hdf_export_data.update(self._get_input_visu(batch))

        # Add GT output if available
        if self._gt_available:
            hdf_export_data.update(
                self._get_gt_output(
                    batch=batch,
                    light_rays=input_light_rays,
                    camera_geometry_net_input=self.camera_geometry_net_input,
                    camera_extrinsic=self.camera_extrinsic,
                )
            )

        # Add the data to the HDF writer
        self._hdf_writer.add_entry(data=hdf_export_data)
