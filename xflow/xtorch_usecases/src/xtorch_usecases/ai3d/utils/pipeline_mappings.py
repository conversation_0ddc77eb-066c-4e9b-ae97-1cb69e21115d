"""Mapping between pipeline steps to related data usages."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from types import MappingProxyType

from xcontract.data.definitions.usage import Usage
from xtorch_usecases.common.pipeline import PipelineStep

# Mapping of pipeline steps to required usages for data modules
PIPELINE_TO_DATA_MODULE_USAGES = MappingProxyType(
    {
        PipelineStep.TRAIN: [Usage.TRAINING, Usage.VALIDATION],
        PipelineStep.EVALUATE_TORCH: [Usage.TEST],
        PipelineStep.CONVERT: [Usage.TEST],
        PipelineStep.CONVERT_QNN: [Usage.TEST],
        PipelineStep.EVALUATE_QNN: [Usage.TEST],
        PipelineStep.PROFILE_QNN: [Usage.TEST],
        PipelineStep.PUBLISH: [Usage.TEST],
        PipelineStep.EXPORT: [Usage.TEST],
        PipelineStep.VISUALIZATION_TORCH: [Usage.TEST],
    }
)
