"""Input camera data configurations used for creation of the task configuration."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from xcontract.data.definitions.image import AvailableImageTypes
from xtorch_usecases.ai3d.config.usecase import CameraDataConfig
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    INFERENCE_INPUT_RESOLUTION,
    TRAIN_INPUT_OFFSET,
    TRAIN_INPUT_RESOLUTION,
    UNCROPPED_INPUT_RESOLUTION,
    CameraName,
)
from xtorch_usecases.common.pipeline import PipelineStep

InputConfigMap = dict[PipelineStep, dict[str, CameraDataConfig]]


def _get_input_config(camera: CameraName) -> InputConfigMap:
    """Returns the input configuration for the target camera."""
    train_image_data_config = dict.fromkeys(
        [CSVDataId.CURRENT_IMAGE.value, CSVDataId.PREVIOUS_IMAGE.value],
        CameraDataConfig(
            target_image_size=TRAIN_INPUT_RESOLUTION[camera],
            stored_image_type=AvailableImageTypes.RGB,
            loaded_image_type=AvailableImageTypes.YUV444,
            stored_image_size=UNCROPPED_INPUT_RESOLUTION[camera],
            cropping_in_post_processing=True,
            cropping_offset=TRAIN_INPUT_OFFSET[camera],
        ),
    )

    inference_image_data_config = dict.fromkeys(
        [CSVDataId.CURRENT_IMAGE.value, CSVDataId.PREVIOUS_IMAGE.value],
        CameraDataConfig(
            target_image_size=INFERENCE_INPUT_RESOLUTION[camera],
            stored_image_type=AvailableImageTypes.RGB,
            loaded_image_type=AvailableImageTypes.YUV444,
            stored_image_size=UNCROPPED_INPUT_RESOLUTION[camera],
            # TODO: Remove cropping_in_post_processing when cropping is implemented appropriately in the pyper stage.
            # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/346383
            cropping_in_post_processing=True,
            cropping_offset=(UNCROPPED_INPUT_RESOLUTION[camera] - INFERENCE_INPUT_RESOLUTION[camera]) // 2,
        ),
    )

    inference_image_data_config_qc = dict.fromkeys(
        [CSVDataId.CURRENT_IMAGE.value, CSVDataId.PREVIOUS_IMAGE.value],
        CameraDataConfig(
            target_image_size=INFERENCE_INPUT_RESOLUTION[camera],
            stored_image_type=AvailableImageTypes.RGB,
            loaded_image_type=AvailableImageTypes.YUV420_QC,
            stored_image_size=UNCROPPED_INPUT_RESOLUTION[camera],
            cropping_offset=(UNCROPPED_INPUT_RESOLUTION[camera] - INFERENCE_INPUT_RESOLUTION[camera]) // 2,
        ),
    )

    config = {
        PipelineStep.TRAIN: train_image_data_config,
        PipelineStep.EXPORT: inference_image_data_config,
        PipelineStep.EVALUATE_TORCH: inference_image_data_config,
        PipelineStep.VISUALIZATION_TORCH: inference_image_data_config,
        PipelineStep.CONVERT_QNN: inference_image_data_config_qc,
        PipelineStep.EVALUATE_QNN: inference_image_data_config_qc,
    }
    return config


def get_input_config(camera: CameraName) -> InputConfigMap:
    """Constructs an input configuration dictionary for the specified camera.

    Args:
        camera: The camera name for which to get the configuration

    Returns:
        A dictionary mapping pipeline steps to camera view configurations
    """
    supported_cameras = INFERENCE_INPUT_RESOLUTION.keys()
    if camera in supported_cameras:
        return _get_input_config(camera)

    error_msg = f"Unsupported camera name: {camera}. Supported cameras are {supported_cameras}."
    raise ValueError(error_msg)
