"""Configuration for parallax model."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import os
import time
from dataclasses import dataclass, field
from pathlib import Path

from hydra.core.config_store import ConfigStore
from torch.cuda import device_count

from azure_tools.environment import runs_on_aml_node
from xtorch.export import ExportConfig
from xtorch.nn.backbones.image.yolo_v4_tiny.yolo_v4_tiny import MONOFOCAL_BACKBONE_PARAMETERS
from xtorch.training.trainer_config import TrainerConfig
from xtorch_usecases.ai3d.config.input_configs import get_input_config

# This import shall be modified once the AI3D experiment is fully integrated into the multi-task framework
from xtorch_usecases.ai3d.config.schema import (
    Ai3dDataModuleConfig,
    Config,
    TaskConfigs,
)
from xtorch_usecases.ai3d.config.usecase import CallbackConfig
from xtorch_usecases.ai3d.data_modules.build_hdf5_data_module import Hdf5DataModuleConfig
from xtorch_usecases.ai3d.model.decoder.pose_head import PoseHeadParams
from xtorch_usecases.ai3d.model.decoder.pwa_decoder import PWAttentionDecoder
from xtorch_usecases.ai3d.model.decoder.structure_parameter_head import StructureParameterHeadParams
from xtorch_usecases.ai3d.model.decoder.surface_normals_head import SurfaceNormalsHeadParams
from xtorch_usecases.ai3d.model.definitions import (
    DecoderId,
    DecoderPyramidLevel,
    EncoderId,
    FeatureFusionId,
    PoseFusionId,
    PoseLossMetricId,
)
from xtorch_usecases.ai3d.model.loss.edge_weighted_mae_loss import EdgeWeightedMAELossParams
from xtorch_usecases.ai3d.model.loss.pose_loss import PoseLossParams
from xtorch_usecases.ai3d.network import MultiTaskYoloV4Tiny
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, SpecificDataSources
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    CameraName,
    ColorAugmentation,
    DataAugmentationParams,
    EnabledHeads,
    InferenceDataSource,
    InferenceResultExportParams,
    ParallaxConfig,
    ParallaxHeadParams,
    ParallaxLossParams,
    PoseLossParamsContainer,
    RotationAugmentation,
)
from xtorch_usecases.common.helpers import (
    get_env_specific_setting,
    get_gpu_specific_setting,
    get_test_data_folder,
)

# Expected standard deviation of the ground truth pose DOFs, used for normalization during training and denormalization
# during inference. The values are based on the training data and should be adapted to the dataset.
DEFAULT_TRANSLATION_SCALE = (1.518, 0.0342, 0.0158)  # (x, y, z)
DEFAULT_ANGLE_SCALE = (0.00779, 0.00142, 0.00130)  # (yaw, pitch, roll)

BACKBONE_ENCODER = EncoderId.YOLO_BACKBONE_ENCODER
DEFAULT_CAMERA_NAME = CameraName.FC1


def get_multi_task_config() -> Config:
    """Get the configuration for the AI3D parallax task."""

    output_folder = Path(f"outputs/{time.strftime('%Y-%m-%d/%H-%M-%S')}")
    trainer_config = TrainerConfig(
        log_every_n_steps=500,
        default_root_dir=output_folder,
        max_epochs=get_env_specific_setting(local=2, azure=20),
        train_epoch_length=max(get_env_specific_setting(local=50, azure=10_000) // max(device_count(), 1), 1),
        precision=get_gpu_specific_setting(a100="bf16-mixed", h100="bf16-mixed", default="16-mixed"),
    )

    task_configs = TaskConfigs(
        parallax=ParallaxConfig(
            data_weight=1,
            loss_weight=1,
            camera_name=DEFAULT_CAMERA_NAME,
            inference_source=InferenceDataSource.CSV_FILE,
            inputs={
                CSVDataId.SYS_CALIB: SpecificDataSources.CALIBRATION,
                CSVDataId.CURRENT_POSE: SpecificDataSources.VEHICLE_ODOMETRY,
                CSVDataId.PREVIOUS_POSE: SpecificDataSources.VEHICLE_ODOMETRY,
            },
            labels={
                CSVDataId.GT_DEPTH: SpecificDataSources.DEPTH_MAP,
                CSVDataId.GT_CURRENT_POSE: SpecificDataSources.VEHICLE_ODOMETRY,
                CSVDataId.GT_PREVIOUS_POSE: SpecificDataSources.VEHICLE_ODOMETRY,
                CSVDataId.GT_SURFACE_NORMAL: SpecificDataSources.SURFACE_NORMAL,
            },
            head_params=ParallaxHeadParams(
                output_pyramid_level=DecoderPyramidLevel.stride_16,
                backbone_channels=MultiTaskYoloV4Tiny.get_encoder_channels(BACKBONE_ENCODER),
                feature_fusion_id=FeatureFusionId.LOCAL_OPTIMIZED_CROSS_ATTENTION,
                feature_fusion_params={
                    "dim_heads": {
                        DecoderPyramidLevel.stride_32: 8,
                        DecoderPyramidLevel.stride_64: 8,
                        DecoderPyramidLevel.stride_128: 8,
                    },
                    "patch_lengths": {
                        DecoderPyramidLevel.stride_32: 5,
                        DecoderPyramidLevel.stride_64: 3,
                        DecoderPyramidLevel.stride_128: 3,
                    },
                },
                pose_fusion_id=PoseFusionId.CHANNELWISE_CROSS_ATTENTION,
                pose_fusion_params={"do_concatenation": False},
                decoder_id=DecoderId.PWA_DECODER,
                enabled_heads=EnabledHeads(structure_parameter=True, pose=True, surface_normals=True),
                structure_head_parameter=StructureParameterHeadParams(
                    in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3
                ),
                pose_head_parameter=PoseHeadParams(
                    in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3,
                    translation_scale=DEFAULT_TRANSLATION_SCALE,
                    angle_scale=DEFAULT_ANGLE_SCALE,
                ),
                surface_normals_head_parameter=SurfaceNormalsHeadParams(
                    in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3
                ),
            ),
            loss_params=ParallaxLossParams(
                structure_parameter_loss_params=EdgeWeightedMAELossParams(
                    loss_weight=9.0, relative_rows_no_weighting=0.42, edge_weight=10.0, percentile=90.0
                ),
                pose_loss_params=PoseLossParamsContainer(
                    pose_loss_x=PoseLossParams(loss_weight=1.0, metric=PoseLossMetricId.L1),
                    pose_loss_y=PoseLossParams(loss_weight=0.2, metric=PoseLossMetricId.L1),
                    pose_loss_z=PoseLossParams(loss_weight=0.2, metric=PoseLossMetricId.L1),
                    pose_loss_yaw=PoseLossParams(loss_weight=1.0, metric=PoseLossMetricId.L1),
                    pose_loss_pitch=PoseLossParams(loss_weight=1.0, metric=PoseLossMetricId.L1),
                    pose_loss_roll=PoseLossParams(loss_weight=1.0, metric=PoseLossMetricId.L1),
                ),
                surface_normals_loss_params=EdgeWeightedMAELossParams(
                    loss_weight=3.0, relative_rows_no_weighting=0.42, edge_weight=10.0, percentile=90.0
                ),
            ),
            data_augmentation=DataAugmentationParams(
                extrinsic_rotation=RotationAugmentation(
                    enabled=False,
                    delta_deg_yaw=5,
                    delta_deg_pitch=5,
                    delta_deg_roll=5,
                ),
                odometry_rotation=RotationAugmentation(
                    enabled=False,
                    delta_deg_yaw=2,
                    delta_deg_pitch=1,
                    delta_deg_roll=1,
                ),
                color_augmentation=ColorAugmentation(
                    enabled=False,
                    random_brightness_probability=0.75,
                    random_brightness_max_delta=0.2,
                    random_hue_probability=0.75,
                    random_hue_max_delta=0.08,
                    random_saturation_probability=0.75,
                    random_saturation_lower=0.6,
                    random_saturation_upper=1.6,
                    random_contrast_probability=0.75,
                    random_contrast_lower=0.7,
                    random_contrast_upper=1.3,
                    random_gamma_probability=0.75,
                    random_gamma_lower=0.8,
                    random_gamma_upper=1.5,
                ),
            ),
            export_parameters=InferenceResultExportParams(
                wheel_radius=0.35,  # unit [m]
                max_angle_of_normal_to_upward_facing_normal=10,  # unit [deg]
            ),
        ),
    )

    data_config = Ai3dDataModuleConfig(
        prefetch_factor=4,
        data_base_path=get_env_specific_setting(
            local=get_test_data_folder() / "camera/tasks/ai3d/",
            azure=lambda: Path(os.environ["AZUREML_DATAREFERENCE_INGEST_DATASETS"]),
        ),
        dataset_base_path=get_env_specific_setting(local=get_test_data_folder() / "camera/tasks/ai3d/", azure=None),
        dataset_name=get_env_specific_setting(
            local="parallax_dataset.csv", azure=f"parallax_{task_configs.parallax.camera_name.value}"
        ),
        dataset_workspace=get_env_specific_setting(local=None, azure="viper_c3d"),
        num_workers_train=get_gpu_specific_setting(v100=5, a100=22, h100=32, default=2),
        num_workers_val=get_gpu_specific_setting(v100=2, a100=4, h100=4, default=2),
        batch_size_train=get_gpu_specific_setting(v100=16, a100=24, h100=24, default=8),
        batch_size_val=get_gpu_specific_setting(v100=16, a100=24, h100=24, default=8),
    )

    callback_config = CallbackConfig(
        image_logging_frequency=200,
        train_data_logging_frequency=150,
        val_data_logging_frequency=5,
        output_root_dir=output_folder,
    )

    export_config = ExportConfig(export_path=output_folder / "export")

    config = Config(
        trainer=trainer_config,
        backbone_parameters=MONOFOCAL_BACKBONE_PARAMETERS,
        encoder_id=BACKBONE_ENCODER,
        task_configs=task_configs,
        data=data_config,
        inputs=get_input_config(camera=task_configs.parallax.camera_name),
        learning_rate=1e-4,
        weight_decay=0,
        callback_config=callback_config,
        output_folder=output_folder,
        export_config=export_config,
    )

    return config


def _postprocess_hdf5_data_loader_config(
    config: Hdf5DataModuleConfig, camera_name: CameraName = CameraName.FC1
) -> None:
    """Provide camera specific parameters for the HDF5 data loader configuration.

    Args:
        config (Hdf5DataModuleConfig): The configuration to be updated.
        camera_name (CameraName): The camera name for which the configuration should be returned.
    """

    match camera_name:
        case CameraName.FC1:
            config.metadata_topic = "/aos/activities/pyramid_metadata_provider_fc1/outputs/image_pyramid_metadata"
            config.image_topic = (
                "/aos/activities/hwa_viper_stage1/outputs/front_camera_yuv_image_pyramid_layer_1_00payload"
            )
            config.extrinsic_topic = "/aos/activities/warp_lut_generator/outputs/fc1_extrinsic_wide"
            config.intrinsic_topic = "/aos/activities/warp_lut_generator/outputs/fc1_intrinsic_wide"
            config.pyramid_layer = 1
            config.camera_type = "CYLINDER"

        case CameraName.TVFRONT:
            config.metadata_topic = "/aos/activities/pyramid_metadata_provider_tv_front/outputs/image_pyramid_metadata"
            config.image_topic = "/aos/activities/hwa_viper_stage1/outputs/tv_front_yuv_image_pyramid_layer_0_00payload"
            config.extrinsic_topic = "/aos/activities/warp_lut_generator_tv_front/outputs/tv_front_virtual_extrinsic"
            config.intrinsic_topic = "/aos/activities/warp_lut_generator_tv_front/outputs/tv_front_virtual_intrinsic"
            config.pyramid_layer = 0
            config.camera_type = "DEFORMED_CYLINDER"

        case CameraName.TVREAR:
            config.metadata_topic = "/aos/activities/pyramid_metadata_provider_tv_rear/outputs/image_pyramid_metadata"
            config.image_topic = "/aos/activities/hwa_viper_stage1/outputs/tv_rear_yuv_image_pyramid_layer_0_00payload"
            config.extrinsic_topic = "/aos/activities/warp_lut_generator_tv_rear/outputs/tv_rear_virtual_extrinsic"
            config.intrinsic_topic = "/aos/activities/warp_lut_generator_tv_rear/outputs/tv_rear_virtual_intrinsic"
            config.pyramid_layer = 0
            config.camera_type = "DEFORMED_CYLINDER"

        # Raise an error if the camera name is not recognized.
        case _:
            msg = f"Camera {camera_name} is currently not supported. Please add the relevant AOS topics."
            raise ValueError(msg)


def get_hdf5_data_loader_config(camera_name: CameraName = CameraName.FC1) -> Hdf5DataModuleConfig:
    """Factory function to get the default configuration for the HDF5 data loader.

    Args:
        camera_name (CameraName): The camera name for which the configuration should be returned.
        The reason for this argument is that the function can be used in module tests.
        Otherwise the camera specific parameters are handled in the __post_init__ method of the AI3DConfig class
        by update_hdf5_data_loader_config as the camera name is only avaialble after Hydra config parsing is done.

    Returns:
        Hdf5DataModuleConfig: The default configuration for the HDF5 data loader.
    """

    # Odometry is identical for all cameras
    odometry_topic = (
        "/aos/activities/viper_odometry_provider/outputs/synchronized_accumulated_vehicle_odometry_with_reset"
    )

    data_base_path: Path = get_test_data_folder() / "ai3d/utils"
    dataset_name: str = "aos.hdf"

    config = Hdf5DataModuleConfig(
        odometry_topic=odometry_topic, data_base_path=data_base_path, dataset_name=dataset_name
    )

    # Update the configuration based on the camera name
    _postprocess_hdf5_data_loader_config(config, camera_name)

    return config


def _set_camera_specific_config_values(config: Config, camera_name: CameraName) -> None:
    """Set camera specific configuration parameters in the AI3DConfig.

    Args:
        config (Config): The AI3D configuration to be updated.
        camera_name (CameraName): The camera name for which the configuration should be set.
    """
    config.inputs = get_input_config(camera=camera_name)

    # When running on Azure ML, we need to set the dataset to the corresponding data asset
    if runs_on_aml_node():
        config.data.dataset_name = f"parallax_{camera_name.value}"
    # When running locally, we use a separate dataset for TV cameras
    elif camera_name in [CameraName.TVFRONT, CameraName.TVREAR, CameraName.TVLEFT, CameraName.TVRIGHT]:
        config.data.dataset_name = "parallax_dataset_tv.csv"


@dataclass
class AI3DConfig:
    """Wrapper class for hydra."""

    config: Config = field(default_factory=get_multi_task_config)

    # Specific config for inference on hdf5 files
    hdf5_inference: Hdf5DataModuleConfig = field(default_factory=get_hdf5_data_loader_config)

    def __post_init__(self) -> None:
        """Post init function to set the default config in case of command line parameters."""

        # Reinitialize ParallaxHeadParams with updated backbone_channels
        parallax_head_params = {
            key: value
            for key, value in self.config.task_configs.parallax.head_params.__dict__.items()
            if key != "decoder_params"  # Exclude decoder_params, which are initialized in __post_init__()
        }
        parallax_head_params["backbone_channels"] = MultiTaskYoloV4Tiny.get_encoder_channels(self.config.encoder_id)

        self.config.task_configs.parallax.head_params = ParallaxHeadParams(**parallax_head_params)

        # Now we know the camera name as configured on the cli and can override the
        # camera specific parameters for hdf5 inference
        _set_camera_specific_config_values(self.config, self.config.task_configs.parallax.camera_name)
        if self.config.task_configs.parallax.inference_source == InferenceDataSource.HDF5_SEQUENCE:
            _postprocess_hdf5_data_loader_config(self.hdf5_inference, self.config.task_configs.parallax.camera_name)

        output_dir = self.config.trainer.default_root_dir
        assert isinstance(output_dir, Path)
        self.config.callback_config.output_root_dir = output_dir


# Note: we add here our default config object to the ConfigStore from hydra to make it available to it
ConfigStore.instance().store(name="ai3d_parallax", node=AI3DConfig)
