"""DataModule for the combined dataset."""

from __future__ import annotations

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2023 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from collections.abc import Callable, Mapping, Sequence
from dataclasses import dataclass
from functools import partial
from typing import Any, ClassVar, TypeAlias, cast

from torch import distributed
from torch.utils.data import ConcatDataset, DataLoader, Dataset, default_collate

from data_formats.gmd.parking_gt_mapping import GmdLabelConfig
from data_formats.occupancy_25d.data_classes import OccupancyGridLayout
from xcontract.data.definitions.image import HW
from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import Usage
from xtorch.data.datasets.combinable_dataset import CombinableDataset
from xtorch.data.datasets.inflated_dataset import InflatedDataset
from xtorch.data.datasets.interleaved_dataset import InterleavedDataset
from xtorch.data.definitions.bev import BevDataDict
from xtorch.data.transforms.compose import Compose
from xtorch.geometry.volumes.common import BevLayout
from xtorch.io.mono_path import MonoPath
from xtorch.training.data_module import DataLoaderDataModule, DataModuleConfig, supports_teardown
from xtorch_extensions.datasets.csv_dataset import CsvDataset
from xtorch_extensions.datasets.csv_dataset_sequential import CsvDatasetSequential
from xtorch_extensions.datasets.loomy_dataset import LoomyIterableDataset
from xtorch_usecases.common.datasets.alliance.camera_calibration_loader import (
    JbfCameraCalibrationLoader,
    OccupancySyscalCamCalibrationLoader,
)
from xtorch_usecases.common.datasets.alliance.factory import (
    GMD_CALIBRATION_ID,
    GMD_TASK_ID,
    GT_VEHICLE_TASK_ID,
    GT_VRU_TASK_ID,
    OCCUPANCY_MID_RANGE_TASK_ID,
    OCCUPANCY_NEAR_RANGE_TASK_ID,
    VEHICLE_LABEL_ID,
    CsvDatasetConfig,
    build_csv_dataset,
)
from xtorch_usecases.common.datasets.combined.definitions import GROUND_TRUTH_OFFLINE_DATASET_NAME
from xtorch_usecases.common.datasets.common.summary import summarize_data
from xtorch_usecases.common.datasets.common.transformation_config import TransformationConfig
from xtorch_usecases.common.datasets.loomy.factory import build_loomy_dataset

_logger = logging.getLogger(__name__)

BOX_3D_RELEASE_TASK_ID = "multiviewbox3d_embd"

OCCUPANCY_CAM_METADATA_COLUMN = "input_cam_sys_calib"

CALIB_STAGE_NAME = "calib"


def _data_converter_collate_fn(
    data: list[Mapping[str, BevDataDict]], data_converter: Callable[..., Any]
) -> Mapping[str, BevDataDict]:
    """Collate function that applies the data converter to the collated data."""
    return data_converter(default_collate(data))


@dataclass(kw_only=True)
class CombinedDataModuleConfig(DataModuleConfig):
    """Configuration for the CombinedDataModule."""

    environment: str  # abused to pass this to the trainer

    dataset_name: str

    predict_usage: Usage = Usage.TEST

    batch_size_calib: int = 1
    num_workers_calib: int = 1

    alliance_dataset_basepath: str | None
    alliance_data_basepath: str
    alliance_dataset_names: list[str]
    alliance_dataset_workspace: str | None = None

    loomy_dataindex_path: str | None = None
    loomy_dataset_name: str | None = None
    loomy_dataset_version: dict[Usage, int | None] | None = None
    loomy_loading_enabled: bool = False

    occupancy_data_basepath: str = ""
    occupancy_dataset_basepath: str | None = None
    occupancy_dataset_name: str | None = None
    occupancy_dataset_workspace: str | None = None

    enabled_input_modalities: tuple[InputDataId, ...]

    enabled_tasks: tuple[str, ...] = (BOX_3D_RELEASE_TASK_ID,)

    nb_label_boxes: int = 250

    training_input_shapes: dict[str, list[int]]
    bev_layouts: dict[str, BevLayout]  # mapping from dataset name to bev layout
    jpeg_load_ratio: float = 0.0

    radar_accumulation_window_s: tuple[float, float] = (-0.5, 0.0)

    box3d_label_set_name: str
    box3d_label_set_version: str
    box3d_background_class: str = "Label"

    # The range filter settings defining the masking parameters for the cameras (None -> no masking)
    range_filter: dict[str, float] | None = None

    occupancy_mid_range_grid_layout: OccupancyGridLayout | None = None
    occupancy_near_range_grid_layout: OccupancyGridLayout | None = None

    # Lists of Transforms for ...
    # ... all stages
    common_transforms: list[TransformationConfig] | None = None
    # ... additional ones only for the train stage (e.g. train time augmentation)
    train_transforms: list[TransformationConfig] | None = None

    # For temporal data loading only
    sequence_length: int | None = None
    sequence_info_processor: str | None = None
    sampling_step_size: int | None = None
    alliance_splits_json_path: str | None = None

    ignore_loading_errors_threshold_num: int = 0


class CombinedDataModule(DataLoaderDataModule[CombinedDataModuleConfig, Mapping[str, BevDataDict]]):  # type: ignore[reportInvalidTypeArguments]
    """DataModule for the combined dataset."""

    DatasetType: TypeAlias = Dataset[Mapping[str, BevDataDict]]

    ENABLED_INPUT_MODALITIES_MAP: ClassVar = {x.value: x for x in InputDataId}

    DATASET_AVAILABLE_TASKS: ClassVar[dict[str, Sequence[str]]] = {
        "alliance": (BOX_3D_RELEASE_TASK_ID,),
        "loomy": (BOX_3D_RELEASE_TASK_ID,),
        "occupancy": (OCCUPANCY_MID_RANGE_TASK_ID, OCCUPANCY_NEAR_RANGE_TASK_ID),
        GROUND_TRUTH_OFFLINE_DATASET_NAME: (
            BOX_3D_RELEASE_TASK_ID,
            GT_VEHICLE_TASK_ID,
            GT_VRU_TASK_ID,
        ),
        "gmd": (GMD_TASK_ID,),
    }

    def __init__(
        self,
        config: CombinedDataModuleConfig,
        data_converter: Callable[..., Any] | None = None,
    ) -> None:
        """Constructor."""
        super().__init__(config)
        # Not super clean but a quick temporary solution allowing to pass in multiple dataset names without interfaces
        # changes until multitask-loading is properly supported.
        self._dataset_names = config.dataset_name.split(",")

        invalid_names = [name for name in self._dataset_names if name not in self.DATASET_AVAILABLE_TASKS]
        if len(invalid_names) > 0:
            msg = f"Dataset(s) {', '.join(invalid_names)} not supported."
            raise ValueError(msg)

        self._enabled_modalities = [self.ENABLED_INPUT_MODALITIES_MAP[mod] for mod in config.enabled_input_modalities]
        self._training_input_shapes = {
            self.ENABLED_INPUT_MODALITIES_MAP[mod]: HW(*img_size)
            for mod, img_size in config.training_input_shapes.items()
        }

        self._config = config
        self._calib_dataset: Dataset[Mapping[str, BevDataDict]] | None = None

        if data_converter is not None:
            self._collate_fn = partial(_data_converter_collate_fn, data_converter=data_converter)

        self._predict_usage = self._config.predict_usage

    def _build_dataset(self, usage: Usage) -> DatasetType | LoomyIterableDataset[BevDataDict]:
        """Build a dataset."""
        datasets: list[CombinableDataset[BevDataDict] | LoomyIterableDataset[BevDataDict]] = []

        transform_list = []
        if self._config.common_transforms is not None:
            transform_list += [config.make_transformation() for config in self._config.common_transforms]
        if usage == Usage.TRAINING and self._config.train_transforms is not None:
            transform_list += [config.make_transformation() for config in self._config.train_transforms]
        transform = Compose(transform_list) if len(transform_list) > 0 else None

        batch_size, num_workers = self._get_batch_size_and_num_workers(usage)

        datasets = []
        for dataset_name in self._dataset_names:
            if dataset_name not in ["alliance", GROUND_TRUTH_OFFLINE_DATASET_NAME, "gmd"]:
                alliance_dataset_names = [""]
            else:
                alliance_dataset_names = self._config.alliance_dataset_names

            for alliance_dataset_name in alliance_dataset_names:
                datasets.append(
                    self._get_dataset(dataset_name, alliance_dataset_name, usage, transform, num_workers, batch_size)
                )

        if self._config.sequential_loading or (len(datasets) == 1 and isinstance(datasets[0], LoomyIterableDataset)):
            assert len(datasets) == 1, "Sequential loading currently only supports a single dataset."
            return datasets[0]

        ignore_loaders = {}
        for ds in datasets:
            assert not isinstance(ds, LoomyIterableDataset)
            ignore_loaders.update(ds.create_ignore_loaders())

        inflated_datasets = [
            InflatedDataset(cast(CombinableDataset[BevDataDict], ds), ignore_loaders)
            for ds in datasets
            if len(ds) > 0  # type: ignore[reportArgumentType]
        ]

        if usage == Usage.TRAINING:
            return InterleavedDataset(inflated_datasets, length_divisor=self._config.batch_size_train)
        if usage == Usage.VALIDATION and len(self._dataset_names) > 1:
            # For multitask online validation, we interleave the datasets to ensure each dataset is used even if the
            # number of validation batches is limited.
            return InterleavedDataset(inflated_datasets, length_divisor=self._config.batch_size_val)

        return ConcatDataset(inflated_datasets)

    def _get_dataset(
        self,
        dataset_name: str,
        alliance_dataset_name: str,
        usage: Usage,
        transform: Compose | None,
        num_workers: int,
        batch_size: int,
    ) -> CsvDataset[BevDataDict] | CsvDatasetSequential[BevDataDict] | LoomyIterableDataset[BevDataDict]:
        enabled_tasks = tuple(set(self.DATASET_AVAILABLE_TASKS[dataset_name]) & set(self._config.enabled_tasks))
        bev_layout = self._config.bev_layouts[dataset_name]

        if dataset_name in ["alliance", GROUND_TRUTH_OFFLINE_DATASET_NAME]:
            return build_csv_dataset(
                CsvDatasetConfig(
                    usage=usage,
                    data_basepath=MonoPath(self._config.alliance_data_basepath),
                    dataset_basepath=MonoPath(self._config.alliance_dataset_basepath)
                    if self._config.alliance_dataset_basepath
                    else None,
                    bev_layout=bev_layout,
                    enabled_tasks=enabled_tasks,
                    enabled_input_modalities=self._enabled_modalities,
                    training_input_shapes=self._training_input_shapes,
                    csv_dataset_name=alliance_dataset_name,
                    dataset_workspace=self._config.alliance_dataset_workspace,
                    nb_label_boxes=self._config.nb_label_boxes,
                    box3d_label_set_name=self._config.box3d_label_set_name,
                    box3d_label_set_version=self._config.box3d_label_set_version,
                    box3d_background_class=self._config.box3d_background_class,
                    mask_bev_grid_mask_beyond_meters_for_cameras=self._config.range_filter,
                    camera_metadata_column=VEHICLE_LABEL_ID,
                    camera_calibration_loader=JbfCameraCalibrationLoader(),
                    transform=transform,
                    sequence_length=self._config.sequence_length,
                    sequence_info_processor=self._config.sequence_info_processor,
                    sampling_step_size=self._config.sampling_step_size,
                    splits_json_path=MonoPath(self._config.alliance_splits_json_path)
                    if self._config.alliance_splits_json_path
                    else None,
                    ignore_loading_errors_threshold_num=self._config.ignore_loading_errors_threshold_num,
                )
            )

        if dataset_name == "gmd":
            return build_csv_dataset(
                CsvDatasetConfig(
                    usage=usage,
                    data_basepath=MonoPath(self._config.alliance_data_basepath),
                    dataset_basepath=MonoPath(self._config.alliance_dataset_basepath)
                    if self._config.alliance_dataset_basepath
                    else None,
                    bev_layout=bev_layout,
                    enabled_tasks=enabled_tasks,
                    enabled_input_modalities=self._enabled_modalities,
                    training_input_shapes=self._training_input_shapes,
                    csv_dataset_name=alliance_dataset_name,
                    dataset_workspace=self._config.alliance_dataset_workspace,
                    nb_label_boxes=self._config.nb_label_boxes,
                    box3d_label_set_name=self._config.box3d_label_set_name,
                    box3d_label_set_version=self._config.box3d_label_set_version,
                    box3d_background_class=self._config.box3d_background_class,
                    mask_bev_grid_mask_beyond_meters_for_cameras=self._config.range_filter,
                    gmd_label_config=GmdLabelConfig(),
                    camera_metadata_column=GMD_CALIBRATION_ID,
                    camera_calibration_loader=OccupancySyscalCamCalibrationLoader(),
                    transform=transform,
                    sequence_length=self._config.sequence_length,
                    sampling_step_size=self._config.sampling_step_size,
                    splits_json_path=MonoPath(self._config.alliance_splits_json_path)
                    if self._config.alliance_splits_json_path
                    else None,
                    ignore_loading_errors_threshold_num=self._config.ignore_loading_errors_threshold_num,
                )
            )

        if dataset_name == "occupancy":
            if self._config.loomy_loading_enabled:
                assert self._config.loomy_dataindex_path is not None
                assert self._config.loomy_dataset_name is not None

                return build_loomy_dataset(
                    CsvDatasetConfig(
                        usage=usage,
                        csv_dataset_name=self._config.loomy_dataset_name,
                        enabled_input_modalities=self._enabled_modalities,
                        enabled_tasks=enabled_tasks,
                        bev_layout=bev_layout,
                        data_basepath=self._config.loomy_dataindex_path,
                        dataset_basepath=None,
                        dataset_version=self._config.loomy_dataset_version,
                        training_input_shapes=self._training_input_shapes,
                        dataset_workspace=self._config.occupancy_dataset_workspace,
                        nb_label_boxes=self._config.nb_label_boxes,
                        occupancy_mid_range_grid_layout=self._config.occupancy_mid_range_grid_layout,
                        occupancy_near_range_grid_layout=self._config.occupancy_near_range_grid_layout,
                        camera_metadata_column=OCCUPANCY_CAM_METADATA_COLUMN,
                        camera_calibration_loader=OccupancySyscalCamCalibrationLoader(),
                        transform=transform,
                        num_workers=num_workers,
                        batch_size=batch_size,
                        load_from_loomy=True,
                        loomy_ignore_dataset_partitions=True,  # For occupancy single task, load whole dataset at once
                    ),
                    self._collate_fn,
                )
            return build_csv_dataset(
                CsvDatasetConfig(
                    usage=usage,
                    csv_dataset_name=self._config.occupancy_dataset_name or dataset_name,
                    enabled_input_modalities=self._enabled_modalities,
                    enabled_tasks=enabled_tasks,
                    bev_layout=bev_layout,
                    data_basepath=MonoPath(self._config.occupancy_data_basepath),
                    dataset_basepath=MonoPath(self._config.occupancy_dataset_basepath)
                    if self._config.occupancy_dataset_basepath
                    else None,
                    training_input_shapes=self._training_input_shapes,
                    dataset_workspace=self._config.occupancy_dataset_workspace,
                    nb_label_boxes=self._config.nb_label_boxes,
                    occupancy_mid_range_grid_layout=self._config.occupancy_mid_range_grid_layout,
                    occupancy_near_range_grid_layout=self._config.occupancy_near_range_grid_layout,
                    camera_metadata_column=OCCUPANCY_CAM_METADATA_COLUMN,
                    camera_calibration_loader=OccupancySyscalCamCalibrationLoader(),
                    ignore_loading_errors_threshold_num=self._config.ignore_loading_errors_threshold_num,
                )
            )

        if dataset_name in ["loomy"]:
            assert self._config.loomy_dataindex_path is not None
            assert self._config.loomy_dataset_name is not None
            return build_loomy_dataset(
                CsvDatasetConfig(
                    load_from_loomy=True,
                    usage=usage,
                    data_basepath=self._config.loomy_dataindex_path,
                    dataset_version=self._config.loomy_dataset_version,
                    dataset_basepath=None,
                    csv_dataset_name=self._config.loomy_dataset_name,
                    bev_layout=bev_layout,
                    enabled_tasks=enabled_tasks,
                    enabled_input_modalities=self._enabled_modalities,
                    training_input_shapes=self._training_input_shapes,
                    dataset_workspace=self._config.alliance_dataset_workspace,
                    nb_label_boxes=self._config.nb_label_boxes,
                    box3d_label_set_name=self._config.box3d_label_set_name,
                    box3d_label_set_version=self._config.box3d_label_set_version,
                    box3d_background_class=self._config.box3d_background_class,
                    mask_bev_grid_mask_beyond_meters_for_cameras=self._config.range_filter,
                    camera_calibration_loader=JbfCameraCalibrationLoader(),
                    transform=transform,
                    num_workers=num_workers,
                    ignore_loading_errors_threshold_num=self._config.ignore_loading_errors_threshold_num,
                    batch_size=batch_size,
                ),
                self._collate_fn,
            )

        msg = f"Unknown dataset {dataset_name}"
        raise ValueError(msg)

    def build_train_dataset(self) -> Dataset[Mapping[str, BevDataDict]] | None:
        """Build a training dataset."""
        return self._build_dataset(usage=Usage.TRAINING)

    def build_validation_dataset(self) -> Dataset[Mapping[str, BevDataDict]] | None:
        """Build a validation dataset."""
        return self._build_dataset(usage=Usage.VALIDATION)

    def build_test_dataset(self) -> Dataset[Mapping[str, BevDataDict]] | None:
        """Build a test dataset."""
        return self._build_dataset(usage=Usage.TEST)

    def build_calib_dataset(self) -> Dataset[Mapping[str, BevDataDict]] | None:
        """Build a calibration dataset."""
        return self._build_dataset(usage=Usage.CALIBRATION)

    def build_predict_dataset(self) -> Dataset[Mapping[str, BevDataDict]] | None:
        """Build a prediction dataset."""
        return self._build_dataset(usage=self._predict_usage)

    @property
    def calib_dataset(self) -> Dataset[Mapping[str, BevDataDict]] | None:
        """Getter for the calibration dataset."""
        return self._calib_dataset

    def calib_dataloader(self) -> DataLoader[Mapping[str, BevDataDict]]:
        """Constructs the pytorch calib data loader based on the calib dataset.

        Returns:
            The calib data loader.
        """
        if self._calib_dataset is None:
            msg = (
                "The calib dataset was not initialized. Ensure you called `setup()` with the correct stage"
                " and `build_calib_dataset()` returns a dataset."
            )
            raise ValueError(msg)

        return self._create_dataloader(
            self._calib_dataset,
            shuffle=True,
            is_val=True,
            batch_size=self._config.batch_size_calib,
            num_workers=self._config.num_workers_calib,
        )

    def setup(self, stage: str) -> None:
        """Sets up necessary attributes.

        The stage string is a TrainerFn and provided by lightning. We map it to our internal Stage definition
        before forwarding.

        Called on every process in DDP:
            We manually assign num_replicas and rank here if not running in distributed mode. This needs to
            happen inside setup() because it is executed inside the distributed context, while __init__() lives
            outside of it.
        """

        # Perform main setup.
        # Custom stages like calib need to be implemented here; the rest is implemented in the overloaded xtorch
        # function.
        if stage == CALIB_STAGE_NAME:
            if not distributed.is_initialized():
                self._num_replicas = 1
                self._rank = 0

            self._calib_dataset = self.build_calib_dataset()
        else:
            super().setup(stage)

        # Do some logging
        if hasattr(self, "trainer") and self.trainer and self.trainer.global_rank == 0:
            self.summarize_stage_data(stage)

    def teardown(self, stage: str) -> None:
        """Tear down attributes.

        The stage string is a TrainerFn and provided by lightning. We map it to our internal Stage definition
        before forwarding.
        """

        if stage == CALIB_STAGE_NAME:
            if supports_teardown(self._calib_dataset):
                self._calib_dataset.teardown()
        else:
            super().teardown(stage)

    def summarize_stage_data(self, stage: str) -> None:
        """Summarize information about the data of the given stage, if available."""
        if stage == "fit":
            dataset = self._train_dataset
            title = f"Train dataloader summary (batch_size: {self._config.batch_size_train})"
        elif stage == "validate":
            dataset = self._val_dataset
            title = f"Val dataloader summary (batch_size: {self._config.batch_size_val})"
        elif stage == "test":
            dataset = self._test_dataset
            title = f"Test dataloader summary (batch_size: {self._config.batch_size_val})"
        elif stage == "predict":
            dataset = self._predict_dataset
            title = f"Predict dataloader summary (batch_size: {self._config.batch_size_val})"
        elif stage == CALIB_STAGE_NAME:
            dataset = self._calib_dataset
            title = f"Calibration dataloader summary (batch_size: {self._config.batch_size_calib})"
        else:
            _logger.warning("Unknown stage requested to be summarized.")
            return

        try:
            summarize_data(
                cast(Dataset[Mapping[str, BevDataDict]], dataset),
                title=title,
            )
        except AssertionError:
            _logger.warning(f"Could not summarize stage {stage}")

    def _get_batch_size_and_num_workers(self, usage: Usage) -> tuple[int, int]:
        """Get the batch size and number of workers for the given usage."""
        if usage == Usage.TRAINING:
            return self._config.batch_size_train, self._config.num_workers_train
        if usage == Usage.CALIBRATION:
            return self._config.batch_size_calib, self._config.num_workers_calib
        return self._config.batch_size_val, self._config.num_workers_val
