"""Alliance dataset definitions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Final

from dataloop_data_formats.joint_box_format.objects.camera import CameraName

from xtorch.nn.encoders.voxelizers.definitions import ValueRange

SYSCAL_CAM_TO_CAMERA_NAME_MAP = {
    "FrontCameraWide": CameraName.FC1,
    "TopViewCameraFront": CameraName.TVFRONT,
    "TopViewCameraRight": CameraName.TVRIGHT,
    "TopViewCameraLeft": CameraName.TVLEFT,
    "TopViewCameraRear": CameraName.TVREAR,
}

CAMERA_NAME_TO_SYSCAL_CAM_MAP = {v: k for k, v in SYSCAL_CAM_TO_CAMERA_NAME_MAP.items()}

TOP_VIEW_CAMERAS_IDENTIFIER = "TopViewCamera"

# Theoretical max number of points
# (VLS128 ref lidar theoretical max: 460_800)
# (front lidar theoretical max: 576_000) # NOTE: currently unused in our datasets
ALLIANCE_MAX_NUM_LIDAR_POINTS = 460_800
MAX_POINT_CLOUD_DEPTH = 300.0

ALLIANCE_MAX_NUM_RADAR_POINTS = 100_000

DEFAULT_AUTO_LABEL_ALLIANCE_BEV_GRID_CAM_BASED_MASKING_PARAMS = {
    TOP_VIEW_CAMERAS_IDENTIFIER: float("inf"),
    "FrontCameraWide": float("inf"),
}

HIGH_RANGE_ALLIANCE_BEV_GRID_CAM_BASED_MASKING_PARAMS = {
    TOP_VIEW_CAMERAS_IDENTIFIER: 60.0,
    "FrontCameraWide": 150.0,
}

LOW_RANGE_ALLIANCE_BEV_GRID_CAM_BASED_MASKING_PARAMS = {
    TOP_VIEW_CAMERAS_IDENTIFIER: 40.0,
    "FrontCameraWide": 100.0,
}

# Map for simply getting the masking params based on the range_filter parameter value
PARAM_TO_MASKING_PARAMS_DICT = {
    "none": None,
    "no_range_limit": DEFAULT_AUTO_LABEL_ALLIANCE_BEV_GRID_CAM_BASED_MASKING_PARAMS.copy(),
    "high_range": HIGH_RANGE_ALLIANCE_BEV_GRID_CAM_BASED_MASKING_PARAMS.copy(),
    "low_range": LOW_RANGE_ALLIANCE_BEV_GRID_CAM_BASED_MASKING_PARAMS.copy(),
}


RADAR_FEATURES_TO_LOAD: Final[tuple[str, ...]] = (
    "radial_velocity_over_ground",
    "rcs",
    "delta_t",
)

# Accumulation window for radar locations in milliseconds.
# TODO: https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/356997: Change the DELTA_T Feature in the dataset to
# si_units seconds. Then also change it here.
RADAR_ACCUMULATION_WINDOW_MILLI_SECONDS: ValueRange = ValueRange(min=-500, max=0.0)

# note that these values must match the values in PACE mono repo!
# prototype/radar_perception/qnn/include/radar_perception/qnn/radar_model_buffers_params.hpp
ALLIANCE_RADAR_FEATURE_STATISTICS: Final[dict[str, ValueRange]] = {
    "rcs": ValueRange(min=-41.0, max=35.2),  # based on dataset statistics
    "radial_velocity_over_ground": ValueRange(min=-50.0, max=50.0),  # based on dataset statistics
    "delta_t": RADAR_ACCUMULATION_WINDOW_MILLI_SECONDS,  # based on point cloud aggregation
}
