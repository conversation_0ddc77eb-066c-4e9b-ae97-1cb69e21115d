"""Augmentation Global Affine Transformation (rigid body + scaling) for the bev data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dataclasses import dataclass

import cv2
import torch
import torch.nn.functional as F

from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.data.definitions.bev import (
    BevBox3dGTTuple,
    BevDataDict,
    LidarPointCloudData,
    MultiViewFrameData,
    ParkingGTTuple,
    RadarPointCloudData,
)
from xtorch.geometry.transformation.rotation_matrix_3d import from_euler
from xtorch.geometry.volumes.volumetric_grid import VolumetricGrid
from xtorch_usecases.common.datasets.alliance.factory import GMD_TASK_ID, GT_VEHICLE_TASK_ID, GT_VRU_TASK_ID
from xtorch_usecases.common.datasets.common.transformation_config import TransformationConfig
from xtorch_usecases.multimodal.configs.geometry import BevLayout

from .base import (
    BOX_3D_RELEASE_TASK_ID,
    DataAugmentationTransformation,
    DataAugmentationTransformationParameters,
    ParameterMode,
)


@dataclass
class GlobalAffineTransformationParameters(DataAugmentationTransformationParameters):
    """Data class for global transformation parameters."""

    roll: float = 0.0
    pitch: float = 0.0
    yaw: float = 0.0
    trans_x: float = 0.0
    trans_y: float = 0.0
    trans_z: float = 0.0
    scale: float = 1.0


@dataclass
class GlobalAffineTransformationConfig(TransformationConfig):
    """Configuration of a data loader processing CSV data.

    The default parameter configuration primarily focuses on major rotation in the yaw angle to address the issue of
    the model struggling to learn box yaw. Other angles remain small to avoid disrupting the scene's logic.

    The range scale applies to all points and box sizes in the scene; however, it does not affect camera intrinsics.
    Therefore, it is set to 1.0, though a small range scale (0.99, 1.01) is also possible.
    """

    range_roll: tuple[float, float] | None = (-0.01, 0.01)
    range_pitch: tuple[float, float] | None = (-0.01, 0.01)
    range_yaw: tuple[float, float] | None = (-0.1, 0.1)
    range_x: tuple[float, float] | None = (-0.2, 0.2)
    range_y: tuple[float, float] | None = (-0.2, 0.2)
    range_z: tuple[float, float] | None = (-0.2, 0.2)
    range_scale: tuple[float, float] | None = (0.99, 1.01)

    augmentation_probability: float | None = None

    bev_layout: BevLayout | None = None

    def make_transformation(self) -> DataAugmentationTransformation[BevDataDict]:
        """Create a global transformation instance."""
        return GlobalAffineTransformation(self)


class GlobalAffineTransformation(DataAugmentationTransformation[BevDataDict]):
    """A class that represents a global transformation applied to sensor data.

    Global transformation is applied in the following order: scale -> translation -> rotation.

    The global transformation can be imagined as the whole sensor set and the world is
    being rotated around the ego vehicle. Or put differently, the ego coordinate system is
    rotated w.r.t. the world. This implies that e.g. the perceived images are still the same
    (only their extrinsic calibration is transformed) but any point cloud needs to be
    transformed as well. Hence, any 2D image backbone will not benefit from this
    augmentation. Only world related parts of the network (like e.g. BEV computation or
    3D object detection) will benefit. The network will see the world from a different angle.

    Attributes:
        _config: The configuration for the global transformation.
        _volumetric_grid: Grid that is used for BEV-based data, like ignore regions.
        _parameter_mode: The parameter mode for the transformation.

    """

    def __init__(self, config: GlobalAffineTransformationConfig) -> None:
        """Initializes the Augmenter object.

        Args:
            config: The configuration object for global transformations.

        Returns:
            None
        """
        self._config = config

        self._volumetric_grid = None
        if config.bev_layout is not None:
            self._volumetric_grid = VolumetricGrid.from_config(
                config.bev_layout.volume_params, resolution=config.bev_layout.resolutions
            )

        super().__init__(
            parameter_mode=ParameterMode.SAMPLE_PER_FRAME, augmentation_probability=config.augmentation_probability
        )

    @staticmethod
    def supported_sensor_types() -> list[str]:
        """Returns a list of supported sensor types.

        Returns:
            A list of supported sensor types.
        """
        return [
            InputDataId.CAMERA_PINHOLE,
            InputDataId.RADAR,
            InputDataId.REF_LIDAR,
            BOX_3D_RELEASE_TASK_ID,
            GMD_TASK_ID,
            GT_VEHICLE_TASK_ID,
            GT_VRU_TASK_ID,
            # ALLIANCE
            InputDataId.CAMERA_DEF_CYLINDER,
            InputDataId.CAMERA_CYLINDER,
            InputDataId.TV_FRONT,
            InputDataId.TV_LEFT,
            InputDataId.TV_REAR,
            InputDataId.TV_RIGHT,
        ]

    def transform_data(
        self, sensor_data: BevDataDict, sensor_name: str, sampled_params: DataAugmentationTransformationParameters
    ) -> BevDataDict:
        """Transform the measurement data based on the given sensor name and sampled parameters.

        Args:
            sensor_data: The measurement data dictionary.
            sensor_name: The name of the sensor.
            sampled_params: The sampled parameters for augmentation.

        Returns:
            A new dictionary with the augmented data (the input sensor_data is modified).

        Raises:
            NotImplementedError: If augmentation for the given sensor type is not implemented yet.
        """
        assert isinstance(sampled_params, GlobalAffineTransformationParameters), (
            "Sampled parameters should be of type GlobalTransformationParameters"
        )
        rotation = from_euler(torch.tensor([sampled_params.roll, sampled_params.pitch, sampled_params.yaw]))
        t = torch.tensor([sampled_params.trans_x, sampled_params.trans_y, sampled_params.trans_z], dtype=torch.float32)

        sensor_data_casted = sensor_data[ValueKey.DATA]

        if sensor_name == InputDataId.RADAR and isinstance(sensor_data_casted, RadarPointCloudData):
            """
            We assume the following for this transformation:
            - The Doppler velocity in the point cloud is the Doppler velocity over ground (ego motion
              compensated).
            - Due to the above, the velocities are like the ego vehicle would not be moving.
            - Since the Doppler velocities are defined on the sight ray between sensor and target a rotation
              of the world including these sight rays, does not change the Doppler velocity. This is only
              valid, because of the ego motion compensated Doppler velocities.

            Known accepted limitations:
            - The ego motion compensation by itself introduces some error for the Doppler velocity, since it
              is only defined in the sensors coordinate system. It cannot be properly converted into the ego
              coordinate system since the velocities of the targets are not known. The error is acceptably
              small since the sensors are mounted closely to the ego coordinate system origin.
            - The augmentation rotation suffers from the same error between sensor coordinate system and ego
              coordinate system as we are rotating around the ego coordinate system origin.
            - Due to the sensor setup, we might have different sensor field of view (FOV) overlaps. Typically
              sensor have a bigger overlap to the ego vehicle front and thus a point cloud with higher density
              is generated. The rotation augmentation will move such regions with higher point density to
              different position around the ego vehicle.
            - The input radar point cloud is aggregated from ego motion compensated individual point clouds
              from several time steps. For moving objects this leads to a trail of points since the target
              motion cannot be compensated. Consider a moving object in front of the ego vehicle. Its points
              trail will be directed straight to the front. If we rotate such a scene by e.g. 45 degrees the
              object would appear like moving in a curve in front of the ego which normally would lead to a
              curved points trail shape. The augmentation rotation will lead to a rotated but still straight
              points trail. Hence this is not perfect.
            - The points behave like if the ego motion vector is also rotated. Hence the ego vehicle is
              assumed to be sliding through the scene.
            """
            new_point_cloud = (sensor_data_casted.point_cloud[..., 0:3] * sampled_params.scale + t) @ rotation.T
            # sensor_data_casted.point_features part of point features might be forexample speed vectors
            sensor_data[ValueKey.DATA] = RadarPointCloudData(
                point_cloud=new_point_cloud,
                point_features=sensor_data_casted.point_features,
            )
            return sensor_data

        if sensor_name in [
            InputDataId.CAMERA_PINHOLE,
            InputDataId.CAMERA_DEF_CYLINDER,
            InputDataId.CAMERA_CYLINDER,
            InputDataId.TV_FRONT,
            InputDataId.TV_LEFT,
            InputDataId.TV_REAR,
            InputDataId.TV_RIGHT,
        ] and isinstance(sensor_data_casted, MultiViewFrameData):
            """
            Remark w.r.t to the scaling augmentation and questions about the intrinsic calibration:

            The pinhole model is actually
                u = f_px * Y/Z = f_m * d * Y/Z
            with f_px being the focal length in pixels, f_m being the metric focal length (unit m) and d
            being the pixel density (unit px/m). Now with the world scaling, f_m would be scaled by s
            (this matches our gut feeling), but the imaging sensor device would also scale and if we keep
            the image resolution (same image after all) we would now have a higher pixel density of d/s.
            With this we end up at
                u = s*f_m * d/s * s*Y/(s*Z)
            for the scaled world, which is identical to
                u = f_m * d * Y/Z = f_px * Y/Z
            again.
            Hence, no scaling of the intrinsics is necessary.
            """
            cam_rot = sensor_data_casted.extrinsics[:, :, :3, :3]
            cam_trans = sensor_data_casted.extrinsics[:, :, :3, 3]
            r_cam_new = rotation @ cam_rot
            t_cam_new = (
                torch.transpose((rotation @ torch.transpose(cam_trans * sampled_params.scale, -1, -2)), -1, -2) + t
            )
            new_extrinsics = sensor_data_casted.extrinsics.clone()
            new_extrinsics[:, :, :3, :3] = r_cam_new
            new_extrinsics[:, :, :3, 3] = t_cam_new

            sensor_data[ValueKey.DATA] = MultiViewFrameData(
                extrinsics=new_extrinsics,
                intrinsic_params=sensor_data_casted.intrinsic_params,
                images=sensor_data_casted.images,
                camera_types=sensor_data_casted.camera_types,
            )
            return sensor_data

        if sensor_name in InputDataId.REF_LIDAR and isinstance(sensor_data_casted, LidarPointCloudData):
            new_point_cloud = (sensor_data_casted.point_cloud[..., 0:3] * sampled_params.scale + t) @ rotation.T
            sensor_data[ValueKey.DATA] = LidarPointCloudData(
                point_cloud=new_point_cloud, intensity=sensor_data_casted.intensity
            )
            return sensor_data

        if sensor_name in (
            BOX_3D_RELEASE_TASK_ID,
            GT_VEHICLE_TASK_ID,
            GT_VRU_TASK_ID,
        ) and isinstance(sensor_data_casted, BevBox3dGTTuple):
            # Select which boxes to augment.
            #
            # Invalid boxes may be ignore boxes, which we still want to augment.
            # We need to check for a non-zero extent to distinguish ignore boxes from actual invalid dummy boxes.
            valid_boxes = sensor_data_casted.box_mask | (
                ~sensor_data_casted.box_mask & (sensor_data_casted.box_dimension != 0).any(dim=-1)
            )

            # Rotate and translate box center
            new_box_center = sensor_data_casted.box_center
            new_box_center[valid_boxes] = (new_box_center[valid_boxes] * sampled_params.scale + t) @ rotation.T

            # Rotate box rotations
            new_box_rotations = sensor_data_casted.box_rotations
            new_box_rotations[valid_boxes] = new_box_rotations[valid_boxes] @ rotation

            # Scale length, width and height of boxes
            new_boxes_dimension = sensor_data_casted.box_dimension
            new_boxes_dimension[valid_boxes] = new_boxes_dimension[valid_boxes] * sampled_params.scale

            # Rotate and scale box velocity.
            #
            # Note that we don't have a z velocity component, so pitch and roll will introduce slight errors here.
            new_box_velocity = sensor_data_casted.box_velocity_xy * sampled_params.scale
            new_box_velocity = F.pad(new_box_velocity, [0, 1]) @ rotation.T
            new_box_velocity = new_box_velocity[..., :2]

            # Transform BEV grid mask according to scale, translation, and yaw
            # (ignoring the pitch and roll)
            new_bev_grid_mask = self._transform_bev_grid_mask(sensor_data_casted.bev_grid_mask, sampled_params)

            sensor_data[ValueKey.DATA] = BevBox3dGTTuple(
                box_class_ids=sensor_data_casted.box_class_ids,
                box_center=new_box_center,
                box_dimension=new_boxes_dimension,
                box_rotations=new_box_rotations,
                box_mask=sensor_data_casted.box_mask,
                bev_grid_mask=new_bev_grid_mask,
                sequence_name=sensor_data_casted.sequence_name,
                per_sequence_track_id=sensor_data_casted.per_sequence_track_id,
                global_track_id=sensor_data_casted.global_track_id,
                timestamp_ns=sensor_data_casted.timestamp_ns,
                box_motion_mask=sensor_data_casted.box_motion_mask,
                box_velocity_xy=new_box_velocity,
                box_acceleration_xy=sensor_data_casted.box_acceleration_xy,
                box_yaw_rate=sensor_data_casted.box_yaw_rate,
            )
            return sensor_data
            # Augment box attributes
            # Currently we do not have any box attributes, but if we decide to add them,
            # we might have to augment them here, depending on the attributes.
        if sensor_name == GMD_TASK_ID and isinstance(sensor_data_casted, ParkingGTTuple):
            # TODO: https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/375389 add augmentation
            return sensor_data

        raise_msg = f"Augmentation for sensor type {sensor_name} not implemented yet"
        raise NotImplementedError(raise_msg)

    def _transform_bev_grid_mask(
        self, bev_grid_mask: torch.Tensor, sampled_params: GlobalAffineTransformationParameters
    ) -> torch.Tensor:
        """Transform BEV grid mask according to scale, translation, and yaw (ignoring the pitch and roll).

        Args:
            bev_grid_mask: Mask to be augmented.
            sampled_params: The sampled parameters for augmentation.

        Returns:
            Augmented mask.
        """
        assert self._volumetric_grid is not None
        volumetric_grid = self._volumetric_grid.to(bev_grid_mask.device)

        # Note that due to using cv2 this implementation is not differentiable/traceable, so if we need it as part
        # of the training pipeline (as opposed to the dataloader augmentations), a reimplementation via kornia
        # or similar would be needed.

        # Determine the BEV indices of the ISO coordinate origin.
        #
        # This will the center point for the CV2 transformations.
        # Note: The CV2 conventions are the reason for subtracting 0.5 here.
        center_pos = torch.tensor([[0.0, 0.0]])
        center_pos_in_bev_indices = (center_pos - volumetric_grid.volume.lower_coord) / volumetric_grid.cell_size - 0.5
        center_pos_in_bev_indices = center_pos_in_bev_indices.cpu().numpy()
        center_pos_tuple = (
            center_pos_in_bev_indices[0, 1],
            center_pos_in_bev_indices[0, 0],
        )  # Flipping x and y  - x is the SECOND coordinate for the BEV.

        # Calculate transformation matrix for scaling and translation
        scale_and_shift_matrix = cv2.getRotationMatrix2D(center_pos_tuple, 0.0, sampled_params.scale)
        scale_and_shift_matrix[0, 2] += sampled_params.trans_y / volumetric_grid.cell_size[1]
        scale_and_shift_matrix[1, 2] += sampled_params.trans_x / volumetric_grid.cell_size[0]

        # Apply transformation.
        # Note that the mask is multiplied by 255; this helps us to avoid the internal rounding of cv2,
        # which would otherwise mark some partially-valid cells as fully valid.
        rows, cols = bev_grid_mask.shape
        new_bev_grid_mask = cv2.warpAffine(
            (bev_grid_mask.to(torch.uint8) * 255).cpu().numpy(),
            scale_and_shift_matrix,
            (cols, rows),
        )

        # Calculate and apply rotation matrix
        rotation_matrix = cv2.getRotationMatrix2D(center_pos_tuple, sampled_params.yaw * 180.0 / torch.pi, 1.0)
        new_bev_grid_mask = cv2.warpAffine(new_bev_grid_mask, rotation_matrix, (cols, rows))

        # Mark everything as invalid that isn't fully valid.
        new_bev_grid_mask = torch.tensor(new_bev_grid_mask) == 255
        new_bev_grid_mask.to(bev_grid_mask)

        return new_bev_grid_mask

    def sample_parameters(self) -> DataAugmentationTransformationParameters:
        """Generates random augmentation parameters based on the configured ranges.

        Returns:
            A dictionary containing the sampled augmentation parameters.
        """
        params = GlobalAffineTransformationParameters()
        if self._config.range_roll is not None:
            params.roll = self.rng_generator.uniform(self._config.range_roll[0], self._config.range_roll[1])
        if self._config.range_pitch is not None:
            params.pitch = self.rng_generator.uniform(self._config.range_pitch[0], self._config.range_pitch[1])
        if self._config.range_yaw is not None:
            params.yaw = self.rng_generator.uniform(self._config.range_yaw[0], self._config.range_yaw[1])
        if self._config.range_x is not None:
            params.trans_x = self.rng_generator.uniform(self._config.range_x[0], self._config.range_x[1])
        if self._config.range_y is not None:
            params.trans_y = self.rng_generator.uniform(self._config.range_y[0], self._config.range_y[1])
        if self._config.range_z is not None:
            params.trans_z = self.rng_generator.uniform(self._config.range_z[0], self._config.range_z[1])
        if self._config.range_scale is not None:
            params.scale = self.rng_generator.uniform(self._config.range_scale[0], self._config.range_scale[1])
        return params
