"""Augmentation Global Shuffling (the order of data) for the bev data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dataclasses import dataclass

import torch

from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.data.definitions.bev import (
    BevBox3dGTTuple,
    BevDataDict,
    RadarPointCloudData,
)
from xtorch_usecases.common.datasets.common.transformation_config import TransformationConfig

from .base import (
    BOX_3D_RELEASE_TASK_ID,
    DataAugmentationTransformation,
    DataAugmentationTransformationParameters,
    ParameterMode,
)


@dataclass
class GlobalShufflingParameters(DataAugmentationTransformationParameters):
    """Data class for global shuffling parameters."""

    augmentation_probability: float = 0.0


@dataclass
class GlobalShufflingConfig(TransformationConfig):
    """Config for applying global shuffling of data."""

    param: GlobalShufflingParameters

    def make_transformation(self) -> DataAugmentationTransformation[BevDataDict]:
        """Create a global augmentation instance."""
        return GlobalShuffling(self)


class GlobalShuffling(DataAugmentationTransformation[BevDataDict]):
    """A class that represents a global shuffling applied to sensor data."""

    _config: GlobalShufflingConfig

    def __init__(self, config: GlobalShufflingConfig) -> None:
        """Initializes the Augmenter object.

        Args:
            config: The configuration object for global shuffling.

        Returns:
            None
        """
        self._config = config
        super().__init__(
            parameter_mode=ParameterMode.SAMPLE_PER_FRAME,
            augmentation_probability=config.param.augmentation_probability,
        )

    @staticmethod
    def supported_sensor_types() -> list[str]:
        """Returns a list of supported sensor types.

        Returns:
            A list of supported sensor types.
        """
        return [
            BOX_3D_RELEASE_TASK_ID,
            InputDataId.RADAR,
            # InputDataId.REF_LIDAR,
        ]

    def transform_data(
        self, sensor_data: BevDataDict, sensor_name: str, sampled_params: DataAugmentationTransformationParameters
    ) -> BevDataDict:
        """Shuffle the measurement data based on the given sensor name and sampled parameters.

        This will change the order of points in point cloud, and the order of GT bboxes.

        For BevBox3dGTTuple, bev_grid_mask is not permuted, and the following are not tested
        - sequence_name
        - per_sequence_track_id
        - global_track_id

        Args:
            sensor_data: The measurement data dictionary.
            sensor_name: The name of the sensor.
            sampled_params: The sampled parameters for augmentation.

        Returns:
            A new dictionary with the augmented data (the input sensor_data is modified).

        Raises:
            NotImplementedError: If augmentation for the given sensor type is not implemented yet.
        """
        assert isinstance(sampled_params, GlobalShufflingParameters)
        sensor_data_casted = sensor_data[ValueKey.DATA]

        if sensor_name == InputDataId.RADAR and isinstance(sensor_data_casted, RadarPointCloudData):
            # in definition of point cloud and features, the second last dimension is for the point
            index_radar_points = torch.randperm(
                sensor_data_casted.point_cloud.shape[-2], device=sensor_data_casted.point_cloud.device
            )
            if index_radar_points.shape[0] > 1:
                sensor_data[ValueKey.DATA] = RadarPointCloudData(
                    point_cloud=sensor_data_casted.point_cloud[..., index_radar_points, :],
                    point_features=sensor_data_casted.point_features[..., index_radar_points, :],
                )
            return sensor_data

        if sensor_name == BOX_3D_RELEASE_TASK_ID and isinstance(sensor_data_casted, BevBox3dGTTuple):
            # change the order of the bboxes
            index_boxes = torch.randperm(
                sensor_data_casted.box_class_ids.shape[-1], device=sensor_data_casted.box_class_ids.device
            )
            if index_boxes.shape[0] > 1:
                sensor_data[ValueKey.DATA] = BevBox3dGTTuple(
                    box_class_ids=sensor_data_casted.box_class_ids[..., index_boxes],
                    box_center=sensor_data_casted.box_center[..., index_boxes, :],
                    box_dimension=sensor_data_casted.box_dimension[..., index_boxes, :],
                    box_rotations=sensor_data_casted.box_rotations[..., index_boxes, :, :],
                    box_mask=sensor_data_casted.box_mask[..., index_boxes],
                    bev_grid_mask=sensor_data_casted.bev_grid_mask,
                    sequence_name=sensor_data_casted.sequence_name,
                    per_sequence_track_id=sensor_data_casted.per_sequence_track_id[..., index_boxes],
                    global_track_id=sensor_data_casted.global_track_id[..., index_boxes],
                    timestamp_ns=sensor_data_casted.timestamp_ns,
                    box_motion_mask=sensor_data_casted.box_motion_mask[..., index_boxes],
                    box_velocity_xy=sensor_data_casted.box_velocity_xy[..., index_boxes, :],
                    box_acceleration_xy=sensor_data_casted.box_acceleration_xy[..., index_boxes, :],
                    box_yaw_rate=sensor_data_casted.box_yaw_rate[..., index_boxes, :],
                )
            return sensor_data

        raise_msg = f"Augmentation for sensor type {sensor_name} not implemented yet"
        raise NotImplementedError(raise_msg)

    def sample_parameters(self) -> DataAugmentationTransformationParameters:
        """Generates random augmentation parameters based on the configured ranges.

        Returns:
            A dictionary containing the sampled augmentation parameters.
        """
        params = GlobalShufflingParameters()
        return params
