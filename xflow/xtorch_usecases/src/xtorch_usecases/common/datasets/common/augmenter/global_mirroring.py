"""Augmentation Global Mirroring (mirroring in Y direction) for the bev data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dataclasses import dataclass

import torch

from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.data.definitions.bev import (
    BevBox3dGTTuple,
    BevDataDict,
    RadarPointCloudData,
)
from xtorch.geometry.transformation.euler import from_rotation_matrix
from xtorch.geometry.transformation.rotation_matrix_3d import from_euler
from xtorch_usecases.common.datasets.common.transformation_config import TransformationConfig

from .base import (
    BOX_3D_RELEASE_TASK_ID,
    DataAugmentationTransformation,
    DataAugmentationTransformationParameters,
    ParameterMode,
)


@dataclass
class GlobalMirroringParameters(DataAugmentationTransformationParameters):
    """Data class for global mirroring parameters."""

    probability_y_mirroring: float = 0.0


@dataclass
class GlobalMirroringConfig(TransformationConfig):
    """Config for applying global mirroring of data.

    Now mirror only in Y direction, to generate more data and switch the left- and right-hand traffic.
    Mirroring in Z direction is not plausible, and there is still no usecase for mirroring in X direction.
    """

    param: GlobalMirroringParameters

    def make_transformation(self) -> DataAugmentationTransformation[BevDataDict]:
        """Create a global augmentation instance."""
        return GlobalMirroring(self)


class GlobalMirroring(DataAugmentationTransformation[BevDataDict]):
    """A class that represents a global mirroring applied to sensor data."""

    _config: GlobalMirroringConfig

    def __init__(self, config: GlobalMirroringConfig) -> None:
        """Initializes the Augmenter object.

        Args:
            config: The configuration object for global transformations.

        Returns:
            None
        """
        self._config = config
        super().__init__(
            parameter_mode=ParameterMode.SAMPLE_PER_FRAME, augmentation_probability=config.param.probability_y_mirroring
        )

    @staticmethod
    def supported_sensor_types() -> list[str]:
        """Returns a list of supported sensor types.

        Returns:
            A list of supported sensor types.
        """
        return [
            BOX_3D_RELEASE_TASK_ID,
            InputDataId.RADAR,
            # InputDataId.REF_LIDAR,
        ]

    def transform_data(
        self, sensor_data: BevDataDict, sensor_name: str, sampled_params: DataAugmentationTransformationParameters
    ) -> BevDataDict:
        """Mirror the measurement data based on the given sensor name and sampled parameters.

        Args:
            sensor_data: The measurement data dictionary.
            sensor_name: The name of the sensor.
            sampled_params: The sampled parameters for augmentation.

        Returns:
            A new dictionary with the augmented data (the input sensor_data is modified).

        Raises:
            NotImplementedError: If augmentation for the given sensor type is not implemented yet.
        """
        assert isinstance(sampled_params, GlobalMirroringParameters)
        sensor_data_casted = sensor_data[ValueKey.DATA]

        if sensor_name == InputDataId.RADAR and isinstance(sensor_data_casted, RadarPointCloudData):
            new_point_cloud = sensor_data_casted.point_cloud
            new_point_cloud[..., 1] = -new_point_cloud[..., 1]
            # sensor_data_casted.point_features part of point features might be forexample speed vectors
            sensor_data[ValueKey.DATA] = RadarPointCloudData(
                point_cloud=new_point_cloud,
                point_features=sensor_data_casted.point_features,
            )
            return sensor_data

        if sensor_name == BOX_3D_RELEASE_TASK_ID and isinstance(sensor_data_casted, BevBox3dGTTuple):
            # mirror the position Y
            new_box_center = sensor_data_casted.box_center
            new_box_center[..., 1] = -new_box_center[..., 1]

            # mirror the rotation yaw
            # The unused rotation matrices are 0-initialized, then they will cause exception because 0-matrix is not
            # valid rotation matrix. mask_all_zeros in the mask to find zero-paddings. In the augmentation, they will
            # be set as eye matrix (no rotation), then reset to 0 afterwards (in case there is other masking in other
            # components).
            mask_all_zeros = torch.all(sensor_data_casted.box_rotations[..., :, :] == 0, dim=(-1, -2))
            sensor_data_casted.box_rotations[mask_all_zeros] = torch.eye(
                3, device=sensor_data_casted.box_rotations.device
            )
            rotations_euler = from_rotation_matrix(sensor_data_casted.box_rotations)
            rotations_euler[..., -1] = -rotations_euler[..., -1]  # theta_z is the last index
            new_box_rotations = from_euler(rotations_euler)
            new_box_rotations[mask_all_zeros] = 0.0

            # mirror and Y and yaw components of the motion
            new_box_velocity_xy = sensor_data_casted.box_velocity_xy
            new_box_velocity_xy[..., -1] = -new_box_velocity_xy[..., -1]
            new_box_acceleration_xy = sensor_data_casted.box_acceleration_xy
            new_box_acceleration_xy[..., -1] = -new_box_acceleration_xy[..., -1]
            new_box_yaw_rate = -sensor_data_casted.box_yaw_rate

            sensor_data[ValueKey.DATA] = BevBox3dGTTuple(
                box_class_ids=sensor_data_casted.box_class_ids,
                box_center=new_box_center,
                box_dimension=sensor_data_casted.box_dimension,
                box_rotations=new_box_rotations,
                box_mask=sensor_data_casted.box_mask,
                bev_grid_mask=sensor_data_casted.bev_grid_mask,
                sequence_name=sensor_data_casted.sequence_name,
                per_sequence_track_id=sensor_data_casted.per_sequence_track_id,
                global_track_id=sensor_data_casted.global_track_id,
                timestamp_ns=sensor_data_casted.timestamp_ns,
                box_motion_mask=sensor_data_casted.box_motion_mask,
                box_velocity_xy=new_box_velocity_xy,
                box_acceleration_xy=new_box_acceleration_xy,
                box_yaw_rate=new_box_yaw_rate,
            )
            return sensor_data

        raise_msg = f"Augmentation for sensor type {sensor_name} not implemented yet"
        raise NotImplementedError(raise_msg)

    def sample_parameters(self) -> DataAugmentationTransformationParameters:
        """Generates random augmentation parameters based on the configured ranges.

        Returns:
            A dictionary containing the sampled augmentation parameters.
        """
        params = GlobalMirroringParameters()
        return params
