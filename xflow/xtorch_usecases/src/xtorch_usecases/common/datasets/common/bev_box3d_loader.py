"""Common data loader for the bev data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import uuid
from collections.abc import Mapping
from dataclasses import dataclass
from enum import IntEnum, auto
from pathlib import Path

import cv2
import dataloop_data_formats.joint_box_format as jbf
import label_format as lf
import numpy as np
import torch
from cloudpathlib import CloudPath
from dataloop_data_formats.joint_box_format import JointBoxFormat
from dataloop_data_formats.joint_box_format.objects.box3d import Box3d
from label_patcher.hierarchy_patcher import HierarchyPatcher
from label_set import LabelSet, LabelSetLoader
from pyquaternion import Quaternion

from data_formats.dynamic_objects.bev import transform_from_sensor_to_vehicle
from xtorch.data.definitions.bev import (
    BevBox3dGTTuple,
    BevDataDict,
    TrackMappingErrors,
    make_bev_data,
)
from xtorch.geometry.volumes.common import BevLayout
from xtorch.geometry.volumes.volumetric_grid import VolumetricGrid
from xtorch_usecases.common.datasets.common.io import decode_json

_OCCLUSION_UPPER_LIMIT = float("inf")
_MINIMUM_DIMENSION = 0.05
LOGGER = logging.getLogger(__name__)

# Axis indices according the BEV grid
BEV_GRID_AXIS_X = 0
BEV_GRID_AXIS_Y = 1


class LabelDefinition:  # noqa: D101 # pylint: disable=too-few-public-methods
    def __init__(
        self,
        *,
        label_set_name: str,
        label_set_version: str,
        background_class: str,
    ) -> None:
        """Instantiates the label definitions.

        Args:
            label_set_name: The name of the used label set.
            label_set_version: The version of the used label set.
            background_class: The name of the background label in the label set.
        """
        self.label_set: LabelSet = LabelSetLoader.load(label_set_name, label_set_version)

        self.classes = [label.name for label in self.label_set.predicted_labels]
        self.classes_to_id = {label.name: label.id for label in self.label_set.predicted_labels}
        self.id_to_classes = {label.id: label.name for label in self.label_set.predicted_labels}
        self.ids_to_color = {label.id: label.color for label in self.label_set}

        if background_class in self.classes_to_id:
            self.background_id = self.classes_to_id[background_class]
        else:
            msg = (
                f"The label set is expected to contain the class {background_class} as "
                f"background class. But the loaded label set {label_set_name} with version "
                f"{label_set_version} doesn't contain the class {background_class}."
            )
            raise RuntimeError(msg)


class BevBox3dSupportedAnnotationFormats(IntEnum):
    """Types of input data formats supported by the BevBox3dLoader."""

    SINGLE_JLF = auto()
    MULTIPLE_JBFS = auto()


@dataclass(frozen=True)
class BevBox3dLoadingData:
    """Stores all relevant information for loading the 3d boxes in bev."""

    annotation_path: Path | CloudPath | list[Path | CloudPath]
    identifier: str
    sequence_name: str
    timestamp_ms: str


def mask_3d_ignore_area(
    bev_grid_mask: torch.Tensor,
    ignore_box_dimension: torch.Tensor,
    ignore_box_rotation: torch.Tensor,
    ignore_box_center: torch.Tensor,
    volumetric_grid: VolumetricGrid,
) -> torch.Tensor:
    """Mask the 3D ignore area in the BEV grid mask.

    Args:
        bev_grid_mask: The BEV grid mask.
        ignore_box_dimension: Dimensions of the boxes [..., N, 3].
        ignore_box_rotation: Rotation matrices of the boxes [..., N, 3, 3].
        ignore_box_center: Centers of the boxes [..., N, 3].
        volumetric_grid: The layout of the bird's eye view grid.

    Returns:
        The updates BEV grid mask with the 3D ignore area masked.
    """
    num_cells = volumetric_grid.grid_size

    corner_boxes_points = get_box_3d_corners_from_3d_boxes(ignore_box_dimension, ignore_box_rotation, ignore_box_center)
    bev_corner_box_coordinates = volumetric_grid.compute_grid_indices(corner_boxes_points[..., :2])
    bev_corner_box_coordinates = np.array(bev_corner_box_coordinates)

    ignore_bev_box_corners = bev_corner_box_coordinates[:4]
    mask = np.ones((num_cells[BEV_GRID_AXIS_X], num_cells[BEV_GRID_AXIS_Y]), dtype=np.uint8)
    pts = np.fliplr(ignore_bev_box_corners).reshape((-1, 1, 2)).astype(np.int32)
    cv2.fillPoly(mask, [pts], 0)  # type: ignore[reportArgumentType]

    bev_grid_mask = bev_grid_mask & torch.tensor(mask, dtype=torch.bool)
    return bev_grid_mask


class BevBoxCollector:
    """Class to collect and store the parameters of the boxes in the BEV."""

    def __init__(
        self,
        volumetric_grid: VolumetricGrid,
        num_cells: tuple[int, int],
        classes_to_id: dict[str, int],
        global_track_id_mapping: dict[tuple[str, int], int] | None,
        sequence_name: str,
        max_box_count: int,
    ) -> None:
        """Initialize the BevBoxParameters according to input.

        Args:
            volumetric_grid: Volumetric grid of the BEV.
            num_cells: 2d size of grid of the BEV.
            bev_layout: Layout describing the BEV.
            classes_to_id: Mapping of class names to class ids.
            global_track_id_mapping: Mapping of (sequence_name, per_sequence_track_id) to global track id.
            sequence_name: Name of the sequence.
            max_box_count: Maximum number of boxes to store.
        """
        self._volumetric_grid = volumetric_grid
        self._classes_to_id = classes_to_id
        self._global_track_id_mapping = global_track_id_mapping
        self._max_box_count = max_box_count
        # current index in the arrays to store next box
        self._box_idx = 0

        self.sequence_name = sequence_name
        self.num_cells = num_cells
        self.box_class_ids = torch.zeros([max_box_count], dtype=torch.int64)
        self.box_centers = torch.zeros([max_box_count, 3], dtype=torch.float64)
        self.box_dimensions = torch.zeros([max_box_count, 3], dtype=torch.float64)
        self.box_rotations = torch.zeros([max_box_count, 3, 3], dtype=torch.float64)
        self.box_mask = torch.zeros([max_box_count], dtype=torch.bool)
        self.bev_indices = torch.zeros([max_box_count, 2], dtype=torch.int64)
        self.box_per_sequence_track_id = [0] * max_box_count
        self.box_global_track_id = [0] * max_box_count
        self.bev_valid_boxes_grid_mask = torch.ones(self.num_cells, dtype=torch.bool)
        self.num_filtered_boxes = 0

        self.box_motion_mask = torch.zeros([max_box_count], dtype=torch.bool)
        self.box_velocity_xy = torch.zeros([max_box_count, 2], dtype=torch.float32)
        self.box_acceleration_xy = torch.zeros([max_box_count, 2], dtype=torch.float32)
        self.box_yaw_rate = torch.zeros([max_box_count, 1], dtype=torch.float32)

    def _set_motion_properties(self, box: Box3d) -> None:
        """Set the motion properties of the box.

        Args:
            box: The box to set the motion properties for.
        """
        if box.velocity is not None and box.acceleration is not None and box.yaw_rate is not None:
            self.box_motion_mask[self._box_idx] = torch.as_tensor(1, dtype=torch.bool)
            self.box_velocity_xy[self._box_idx] = torch.as_tensor(box.velocity, dtype=torch.float32)
            self.box_acceleration_xy[self._box_idx] = torch.as_tensor(box.acceleration, dtype=torch.float32)
            self.box_yaw_rate[self._box_idx] = torch.as_tensor(box.yaw_rate, dtype=torch.float32)
        elif not (box.velocity is None and box.acceleration is None and box.yaw_rate is None):
            LOGGER.warning("Not all motion GT is available. Check your GT pipeline.")

    def add_box(
        self,
        box: Box3d,
        *,
        box_to_ignore: bool,
    ) -> None:
        """Add box to internal data structure of box collector.

        Args:
            box: The box to add to the box collector.
            box_to_ignore: true -> box is of ignore list (-> gets appended with box_mask false for this index).
                           false -> box is normal box (-> gets appended with box mask true for this index,
                                                          if not filtered out).
        """
        if self._box_idx >= self._max_box_count:
            return
        assert box.iso_vehicle_T_iso_box is not None
        # in case of label errors, we demand truly positive dimension values
        bbox_dimension = torch.tensor(box.dimensions, dtype=torch.float32)
        bbox_dimension = torch.abs(bbox_dimension).clamp_min(_MINIMUM_DIMENSION)
        bbox_rotation = torch.tensor(box.iso_vehicle_T_iso_box.rotation_matrix, dtype=torch.float32)
        bbox_center = torch.tensor(box.iso_vehicle_T_iso_box.center, dtype=torch.float32)
        # Check if boxes are within bounds of BEV
        bev_coordinates = self._volumetric_grid.compute_grid_indices(bbox_center[..., :2])
        bound_x = (bev_coordinates[BEV_GRID_AXIS_X] >= 0) & (
            bev_coordinates[BEV_GRID_AXIS_X] < self.num_cells[BEV_GRID_AXIS_X]
        )
        bound_y = (bev_coordinates[BEV_GRID_AXIS_Y] >= 0) & (
            bev_coordinates[BEV_GRID_AXIS_Y] < self.num_cells[BEV_GRID_AXIS_Y]
        )
        if not (bound_x & bound_y):
            # take care here a lot of boxes are filtered out
            return  # Skip boxes outside of bev
        if box_to_ignore:
            # boxes to ignore are already added priviously to bev_valid_boxes_grid_mask (necessary for filtering boxes),
            # so no need to add them again
            self.box_class_ids[self._box_idx] = -1
            self.box_per_sequence_track_id[self._box_idx] = TrackMappingErrors.K_NO_PER_SEQUENCE_TRACK_ID
            self.box_global_track_id[self._box_idx] = TrackMappingErrors.K_NO_PER_SEQUENCE_TRACK_ID
        else:
            # If the class name is not given or id is None, mark box position on bev grid as invalid/not trainable
            if box.label not in self._classes_to_id or box.label == "Unlabeled" or box.iso_vehicle_T_iso_box is None:
                self.bev_valid_boxes_grid_mask = mask_3d_ignore_area(
                    self.bev_valid_boxes_grid_mask,
                    bbox_dimension,
                    bbox_rotation,
                    bbox_center,
                    self._volumetric_grid,
                )
                self.num_filtered_boxes += 1
                return  # Skips the box but preserves the box position to ignore on the bev grid
            # Check that only one box is located within a bev cell
            # TODO handle overlap https://rb-tracker.bosch.com/tracker08/browse/UAT-20270  # noqa: TD004
            if (self.bev_indices[: self._box_idx] == bev_coordinates).all(dim=1).any():
                return
            self.box_class_ids[self._box_idx] = self._classes_to_id[box.label]
            self.box_per_sequence_track_id[self._box_idx] = (
                box.per_sequence_track_id or TrackMappingErrors.K_NO_PER_SEQUENCE_TRACK_ID
            )
            if self._global_track_id_mapping is None:
                self.box_global_track_id[self._box_idx] = TrackMappingErrors.K_NO_MAPPING_AVAILABLE
            elif not self.box_per_sequence_track_id[self._box_idx] >= 0:
                self.box_global_track_id[self._box_idx] = TrackMappingErrors.K_NO_PER_SEQUENCE_TRACK_ID
            elif not self.sequence_name:
                self.box_global_track_id[self._box_idx] = TrackMappingErrors.K_NO_SEQUENCE_NAME
            elif (
                self.sequence_name,
                self.box_per_sequence_track_id[self._box_idx],
            ) not in self._global_track_id_mapping:
                self.box_global_track_id[self._box_idx] = TrackMappingErrors.K_KEY_MISSING_IN_MAPPING
            else:
                self.box_global_track_id[self._box_idx] = self._global_track_id_mapping[
                    self.sequence_name, self.box_per_sequence_track_id[self._box_idx]
                ]
        self.box_rotations[self._box_idx] = bbox_rotation
        self.box_dimensions[self._box_idx] = bbox_dimension
        self.box_centers[self._box_idx] = bbox_center
        self.bev_indices[self._box_idx] = bev_coordinates
        self.box_mask[self._box_idx] = not box_to_ignore
        self._set_motion_properties(box)

        self._box_idx += 1


def jbf_box3d_to_bev_box3d_gt_tuple(
    annotations: jbf.Box3dWorldAnnotation,
    bev_layout: BevLayout,
    label_definition: LabelDefinition,
    max_box_count: int,
    global_track_id_mapping: dict[tuple[str, int], int] | None = None,
    bev_grid_mask: torch.Tensor | None = None,
) -> tuple[BevBox3dGTTuple, int]:
    """Transforms a jbf annotation into the bev 3d box format.

    Args:
        annotations: Jbf annotation for 3d world boxes.
        bev_layout: Layout describing the BEV.
        label_definition: Box 3d labelset definition.
        max_box_count: Maximum amount of loaded boxes.
        global_track_id_mapping: mapping of (sequence_name, per_sequence_track_id) to globally unique track id
        bev_grid_mask: bev grid mask indicating which grid positions are valid. Defaults to None (i.e. a grid
                        with all valid positions is generated according to the used BEV layout).

    Returns:
        Tuple
        * BevBox3dGTTuple: describing the bev 3d gt boxes.
          ** The box_mask has all the indices with valid box marked by True
          ** The ignore_boes are marked by box_mask by False, but they have valid parameters as box_dimension
          ** All indices with box_mask False and zero box_dimension are just filling to max_box_count
        * number of filtered out boxes
    """
    volumetric_grid = VolumetricGrid.from_config(bev_layout.volume_params, resolution=bev_layout.resolutions)
    sequence_name = annotations.sequence_name or ""

    box_collector = BevBoxCollector(
        volumetric_grid=volumetric_grid,
        num_cells=bev_layout.num_cells,
        classes_to_id=label_definition.classes_to_id,
        global_track_id_mapping=global_track_id_mapping,
        sequence_name=sequence_name,
        max_box_count=max_box_count,
    )

    if annotations.ignore_regions is not None:
        for box in annotations.ignore_regions:
            assert box.iso_vehicle_T_iso_box is not None
            bbox_dimension = torch.tensor(box.dimensions, dtype=torch.float32)
            bbox_rotation = torch.as_tensor(box.iso_vehicle_T_iso_box.rotation_matrix, dtype=torch.float32)
            bbox_center = torch.as_tensor(box.iso_vehicle_T_iso_box.center, dtype=torch.float32)

            box_collector.bev_valid_boxes_grid_mask = mask_3d_ignore_area(
                box_collector.bev_valid_boxes_grid_mask,
                bbox_dimension,
                bbox_rotation,
                bbox_center,
                volumetric_grid,
            )

    if annotations.boxes3d is not None:
        for box in annotations.boxes3d:
            box_collector.add_box(box=box, box_to_ignore=False)

    if annotations.ignore_regions is not None:
        for box in annotations.ignore_regions:
            box_collector.add_box(box=box, box_to_ignore=True)

    # If not provide/None, a bev grid mask with all valid positions is created (and according to the used bev layout)
    if bev_grid_mask is None:
        bev_grid_mask = torch.ones(box_collector.num_cells, dtype=torch.bool)

    # Updates bev grid mask with box valid/ignore bev grid mask
    bev_grid_mask = bev_grid_mask & box_collector.bev_valid_boxes_grid_mask

    timestamp_ns: int = annotations.timestamp_ms * int(1e6) if annotations.timestamp_ms is not None else -1

    return (
        BevBox3dGTTuple(
            box_class_ids=box_collector.box_class_ids.to(torch.int64),
            box_center=box_collector.box_centers.to(torch.float32),
            box_dimension=box_collector.box_dimensions.to(torch.float32),
            box_rotations=box_collector.box_rotations.to(torch.float32),
            box_mask=box_collector.box_mask.to(torch.bool),
            bev_grid_mask=bev_grid_mask.to(torch.bool),
            sequence_name=box_collector.sequence_name,
            per_sequence_track_id=torch.as_tensor(box_collector.box_per_sequence_track_id, dtype=torch.int64),
            global_track_id=torch.as_tensor(box_collector.box_global_track_id, dtype=torch.int64),
            timestamp_ns=torch.as_tensor(timestamp_ns, dtype=torch.int64),
            box_motion_mask=box_collector.box_motion_mask,
            box_velocity_xy=box_collector.box_velocity_xy,
            box_acceleration_xy=box_collector.box_acceleration_xy,
            box_yaw_rate=box_collector.box_yaw_rate,
        ),
        box_collector.num_filtered_boxes,
    )


def get_box_3d_corners_from_3d_boxes(
    box_dimension: torch.Tensor, box_rotations: torch.Tensor, box_center: torch.Tensor
) -> torch.Tensor:
    """Get the 3D box corners coordinates from the provided 3D boxes.

    Args:
        box_dimension: Dimensions of the boxes [..., N, 3].
        box_rotations: Rotation matrices of the boxes [..., N, 3, 3].
        box_center: Centers of the boxes [..., N, 3].

    Returns:
        Corners of the boxes in the shape [..., N, 8, 3].
    """

    length, width, height = (
        box_dimension[..., 0],
        box_dimension[..., 1],
        box_dimension[..., 2],
    )

    # Get the corners of the box in the local coordinate system
    rlb = torch.stack([-length / 2, width / 2, -height / 2], dim=-1)
    rrb = torch.stack([-length / 2, -width / 2, -height / 2], dim=-1)
    frb = torch.stack([length / 2, -width / 2, -height / 2], dim=-1)
    flb = torch.stack([length / 2, width / 2, -height / 2], dim=-1)
    rlt = torch.stack([-length / 2, width / 2, height / 2], dim=-1)
    rrt = torch.stack([-length / 2, -width / 2, height / 2], dim=-1)
    frt = torch.stack([length / 2, -width / 2, height / 2], dim=-1)
    flt = torch.stack([length / 2, width / 2, height / 2], dim=-1)
    corners = torch.stack([rlb, rrb, frb, flb, rlt, rrt, frt, flt], dim=-2)  # Shape: [..., N, 8, 3]

    # Rotate the corners by the orientations of the boxes and add the center
    # [..., N, 8, 3] @ [..., N, 3, 3] -> [..., N, 8, 3]
    corners = torch.einsum("...ij,...kj->...ki", box_rotations, corners) + torch.unsqueeze(box_center, dim=-2)
    return corners


class BevBox3dLoader:
    """Creates data dictionary with the required structures containing BevBox3dGTTuple."""

    def __init__(
        self,
        dataset_name: str,
        bev_layout: BevLayout,
        label_definition: LabelDefinition,
        max_box_count: int,
        annotation_format: BevBox3dSupportedAnnotationFormats,
        global_track_id_mapping: dict[tuple[str, int], int] | None,
        perform_bev_grid_masking_based_on_cameras: bool = False,  # noqa: FBT001, FBT002
        mask_bev_grid_mask_beyond_meters_for_cameras: Mapping[str, float] | None = None,
    ) -> None:
        """Initialize BevBox3dLoader instance.

        Args:
            dataset_name: Name of the dataset.
            bev_layout: Layout describing the BEV.
            label_definition: Box 3d labelset definition.
            max_box_count: Maximum amount of loaded boxes.
            resize_config: A resize configuration for the input image.
            annotation_format: Format in which the input annotations are stored.
            global_track_id_mapping: mapping of (sequence_name, per_sequence_track_id) to globally unique track id
            perform_bev_grid_masking_based_on_cameras: Flag to indicate if the BEV grid masking based on active cameras
                                                       views (where GT is available) should be performed. Default is
                                                       False (i.e. all bev grid positions are valid).
            mask_bev_grid_mask_beyond_meters_for_cameras: A dict containing the camera and the distance in meters
                                                             (radial) beyond which the BEV grid mask should be masked.
                                                             Defaults to None (i.e. no distance masking is applied).

        """
        self.dataset_name = dataset_name
        self.bev_layout = bev_layout
        self.label_definition = label_definition
        self.max_box_count = max_box_count
        self.annotation_format = annotation_format
        self.perform_bev_grid_masking_based_on_cameras = perform_bev_grid_masking_based_on_cameras
        self.mask_bev_grid_mask_beyond_meters_for_cameras = mask_bev_grid_mask_beyond_meters_for_cameras
        self.global_track_id_mapping = global_track_id_mapping

    def map_class_name_to_label_name(self, class_name: str) -> str:
        """Maps a given class name to the corresponding parent label name.

        Args:
            class_name: Class name of an annotation.

        Returns:
            Corresponding label name.
        """
        labelname = "Unlabeled"
        for label in self.label_definition.label_set.labels:
            if label.predicted and class_name in label.label_hierarchy_names:
                labelname = label.name
                break
        return labelname

    def load_box3d_world_annotation_from_single_jlf_file(
        self, annotation_path: Path | CloudPath, timestamp_ms: str, sequence_name: str
    ) -> jbf.Box3dWorldAnnotation:
        """Loads a jbf.Box3dWorldAnnotation object from one jlf label file.

        Args:
            annotation_path: Path to the jlf label file.
            timestamp_ms: Timestamp of the annotation.
            sequence_name: Name of the annotated sequence.

        Returns:
            jbf.Box3dWorldAnnotation containing all boxes from the jlf file.
        """
        # Load and parse the JLF json file
        json_data_dict = decode_json(annotation_path)
        multiview_frame_raw = lf.Loader().decodeJson(json_data_dict)
        patcher = HierarchyPatcher(
            jlf_object=multiview_frame_raw,
            cross_patching_enabled=True,
        )
        patcher.patch()
        multiview_frame = patcher.get_jlf()
        assert multiview_frame is not None

        jbf_boxes = []
        for box_annotation in multiview_frame.object_annotations:
            # Skip phantom boxes
            if box_annotation.attributes.get("isReflection", False):
                continue
            if box_annotation.attributes.get("isDepiction", False):
                continue

            # Skip 2d annotations
            if box_annotation.instance_type not in ("Box3D", "SpatialMultiViewBox3D"):
                continue

            center_pose = box_annotation.center_pos
            rotation = Quaternion(box_annotation.orientation)
            transformation_matrix = np.eye(4)
            transformation_matrix[:3, :3] = rotation.rotation_matrix
            transformation_matrix[:3, 3] = np.transpose(np.array(center_pose))
            iso_vehicle_T_iso_box = jbf.Transformation(list(transformation_matrix))  # noqa: N806
            label = self.map_class_name_to_label_name(box_annotation.hierarchy_class)

            # prepare track id and handle the case that the track id is represented as uuid instead of a simple int
            if box_annotation.track_id.isdecimal():
                track_id = int(box_annotation.track_id)
            else:
                track_id = uuid.UUID(box_annotation.track_id).int
                # track_id = (track_id >> 64) & 0xffffffffffffffff
                # track_id = (track_id >> 32) & 0xffffffff
                track_id = track_id & 0xFFFFFFFFFFFFFFFF
                track_id = track_id & 0xFFFFFFFF

            occlusion = box_annotation.attributes["occlusion"]
            jbf_boxes.append(
                jbf.Box3d(
                    dimensions=box_annotation.box_size,
                    iso_vehicle_T_iso_box=iso_vehicle_T_iso_box,
                    iso_sensor_T_iso_box=None,
                    label=label,
                    has_valid_3d_information=True,
                    modal_sensor_projection=None,
                    amodal_sensor_projection=None,
                    specification=box_annotation.hierarchy_class,
                    occlusion=occlusion,
                    truncation=0.0,
                    instance_id=track_id,
                    score=1,  # score in [0, 1] - Set to 1 for human-labeled GT
                    z_index=box_annotation.z_value,
                    per_sequence_track_id=track_id,  # Same as global instance id
                )
            )

        jbf_boxes = [
            jbf_box for jbf_box in jbf_boxes if jbf_box.occlusion is None or jbf_box.occlusion <= _OCCLUSION_UPPER_LIMIT
        ]

        box3d_world_annotation = jbf.Box3dWorldAnnotation(
            boxes3d=jbf_boxes,
            label_set=self.label_definition.label_set,
            sequence_name=sequence_name,
            sequence_frame_id=int(timestamp_ms),
            timestamp_ms=int(timestamp_ms),
        )
        return box3d_world_annotation

    def load_box3d_world_annotation_from_multiple_jbf_file(  # noqa: C901
        self, annotation_path: list[Path | CloudPath], sequence_name: str
    ) -> tuple[jbf.Box3dWorldAnnotation | None, tuple[str, ...] | None]:
        """Loads a jbf.Box3dWorldAnnotation object from multiple input jbf label file.

        Args:
            annotation_path: List of paths to multiple jbf files.
            timestamp: Timestamp of the annotation.
            sequence_name: Name of the annotated sequence.

        Returns:
            jbf.Box3dWorldAnnotation containing all boxes collected from all given jbf files.
            None if invalid.
        """
        labeled_cameras = None
        jbf_boxes = []
        ignore_regions = []
        ignore_regions_track_ids = []
        track_id_center_pos = {}
        timestamp_ms = 0
        for path in annotation_path:
            json_data = decode_json(path)
            jbf_annotation = JointBoxFormat.parse_annotation(json_data).common_annotation_object

            # check if we have data with only one camera labeled and set `labeled_cameras` accordingly
            if (
                jbf_annotation is not None
                and jbf_annotation.metadata is not None
                and jbf_annotation.metadata.dataset_project_name is not None
                and jbf_annotation.metadata.dataset_project_name == "alliance_tv_single_cam"
                and jbf_annotation.camera is not None
            ):
                raise NotImplementedError
                # camera_name: CameraName = jbf_annotation.camera.camera_name
                # labeled_cameras = tuple(
                #     [key for key, value in SYSCAL_CAM_TO_CAMERA_NAME_MAP.items() if value == camera_name]
                # )

            if (
                jbf_annotation is not None
                and jbf_annotation.boxes3d is not None
                and jbf_annotation.camera is not None
                and jbf_annotation.camera.iso_vehicle_T_iso_sensor is not None
            ):
                timestamp_ms = jbf_annotation.timestamp_ms
                box: jbf.Box3d

                if jbf_annotation.ignore_regions_3d is not None:
                    for ignore_box in jbf_annotation.ignore_regions_3d:
                        if ignore_box.per_sequence_track_id not in ignore_regions_track_ids:
                            ignore_regions_track_ids.append(ignore_box.per_sequence_track_id)
                            transform_from_sensor_to_vehicle(ignore_box, jbf_annotation.camera)
                            ignore_regions.append(ignore_box)

                for box in jbf_annotation.boxes3d:
                    if box.has_valid_3d_information:
                        transform_from_sensor_to_vehicle(box, jbf_annotation.camera)

                        assert box.iso_vehicle_T_iso_box is not None

                        # Add each object to jbf_boxes only once (once per track id).
                        if box.per_sequence_track_id not in track_id_center_pos:
                            track_id_center_pos[box.per_sequence_track_id] = box.iso_vehicle_T_iso_box.center
                            jbf_boxes.append(box)
                        elif not np.allclose(
                            track_id_center_pos[box.per_sequence_track_id],
                            box.iso_vehicle_T_iso_box.center,
                            atol=1e-3,
                            rtol=1e-3,
                        ):
                            # If track id seen before in other cam, compare center pos in vehicle coordinates.
                            center_diff = np.linalg.norm(
                                track_id_center_pos[box.per_sequence_track_id] - box.iso_vehicle_T_iso_box.center
                            )
                            # TODO: think of a way how to handle this case, e.g. with using the mis-placed box  # noqa: E501, TD003
                            #       as an ignore box (instead of just logging and discarding the whole frame)
                            LOGGER.warning(
                                "Center position mismatch of %f for track id %i in %s",
                                center_diff,
                                box.per_sequence_track_id,
                                path,
                            )
                            return (None, labeled_cameras)

        return (
            jbf.Box3dWorldAnnotation(
                boxes3d=jbf_boxes,
                ignore_regions=ignore_regions,
                label_set=self.label_definition.label_set,
                sequence_name=sequence_name,
                sequence_frame_id=timestamp_ms,
                timestamp_ms=timestamp_ms,
            ),
            labeled_cameras,
        )

    def get_label_data(
        self,
        annotation_path: list[Path | CloudPath] | Path | CloudPath,
        timestamp_ms: str,
        sequence_name: str,
        global_track_id_mapping: dict[tuple[str, int], int] | None,
    ) -> BevBox3dGTTuple | None:
        """Loads the box 3d labels from the input file/files for the bev usecase.

        Args:
            annotation_path: Path to the label file or files depending on the input annotation format.
            timestamp_ms: Timestamp of the annotation.
            sequence_name: Name of the annotated sequence.
            global_track_id_mapping: mapping of (sequence_name, per_sequence_track_id) to globally unique track id

        Returns:
            Labels for the mv frame as BevBox3dGTTuple.
            None if invalid.
        """
        labeled_cameras = None
        box3d_world_annotation: jbf.Box3dWorldAnnotation | None
        if self.annotation_format == BevBox3dSupportedAnnotationFormats.SINGLE_JLF and isinstance(
            annotation_path, (Path, CloudPath)
        ):
            box3d_world_annotation = self.load_box3d_world_annotation_from_single_jlf_file(
                annotation_path, timestamp_ms, sequence_name
            )
        elif (
            self.annotation_format == BevBox3dSupportedAnnotationFormats.MULTIPLE_JBFS
            and isinstance(annotation_path, list)
            and all(isinstance(path, (Path, CloudPath)) for path in annotation_path)
        ):
            (box3d_world_annotation, labeled_cameras) = self.load_box3d_world_annotation_from_multiple_jbf_file(
                annotation_path, sequence_name
            )
        else:
            msg = f"{self.annotation_format} is not a supported annotation format for the BevBox3dLoader."
            raise ValueError(msg)

        if box3d_world_annotation is None:
            return None

        bev_grid_mask = None
        if self.perform_bev_grid_masking_based_on_cameras:
            msg = "perform_bev_grid_masking_based_on_cameras is not yet implemented"
            raise NotImplementedError(msg)
            # bev_grid_mask = create_bev_grid_mask_based_on_multiple_cameras_labeled_views(
            #     bev_layout=self.bev_layout,
            #     labeled_cameras=labeled_cameras,
            #     mask_bev_grid_mask_beyond_meters_for_cameras=self.mask_bev_grid_mask_beyond_meters_for_cameras,
            # )

        # box3d_world_annotation already contains bev box labels
        bev_3d_box_tuple, _ = jbf_box3d_to_bev_box3d_gt_tuple(
            bev_layout=self.bev_layout,
            annotations=box3d_world_annotation,
            label_definition=self.label_definition,
            max_box_count=self.max_box_count,
            global_track_id_mapping=global_track_id_mapping,
            bev_grid_mask=bev_grid_mask,
        )
        return bev_3d_box_tuple

    def __call__(self, loading_data: BevBox3dLoadingData) -> BevDataDict:
        """Loads the box 3d labels from the input file/files for the bev usecase."""
        bev_3d_box_tuple = self.get_label_data(
            annotation_path=loading_data.annotation_path,
            timestamp_ms=loading_data.timestamp_ms,
            sequence_name=loading_data.sequence_name,
            global_track_id_mapping=self.global_track_id_mapping,
        )

        if bev_3d_box_tuple is None:
            return make_bev_data(
                valid=False,
                identifier=f"{loading_data.identifier}:{self.dataset_name}",
                data=BevBox3dGTTuple.dummy(
                    self.max_box_count,
                    self.bev_layout.num_cells,
                    loading_data.sequence_name,
                ),
            )

        return make_bev_data(
            valid=True,
            identifier=f"{loading_data.identifier}:{self.dataset_name}",
            data=bev_3d_box_tuple,
        )
