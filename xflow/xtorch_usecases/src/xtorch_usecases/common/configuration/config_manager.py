"""Provides the load_config decorator for easy loading of Python-based config files with pre-defined overrides."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import functools
import logging
from abc import ABC, abstractmethod
from collections.abc import Callable, Mapping
from pathlib import Path
from typing import Any, Generic, NamedTuple, TypeAlias, TypeVar, cast

from omegaconf import OmegaConf

from azure_tools.environment import runs_on_aml_node
from xcontract.training.run_mode import RunMode
from xtorch.training.run_config import RunConfig
from xtorch_usecases.common.environment import RuntimeEnvironment
from xtorch_usecases.common.pipeline import PipelineStep

_LOGGER = logging.getLogger(__name__)


# Config definition, e.g. MultiModalRunConfig or single camera's Config class
ConfigTypeT = TypeVar("ConfigTypeT", bound=RunConfig)


class Overrides(ABC, Generic[ConfigTypeT]):
    """Base class for configuration overrides."""

    @abstractmethod
    def override(self, config: ConfigTypeT) -> ConfigTypeT:
        """Override individual fields of the given configuration."""
        ...


class IOSettings(NamedTuple):
    """Named tuple to hold input and output folder paths."""

    input_folder: Path
    output_folder: Path


OverrideRegistry: TypeAlias = dict[str | RunMode | RuntimeEnvironment, Overrides[ConfigTypeT]]
ConfigBuildFunc: TypeAlias = Callable[[PipelineStep, IOSettings], ConfigTypeT]
ConfigStore: TypeAlias = Mapping[str, tuple[ConfigBuildFunc[ConfigTypeT], OverrideRegistry[ConfigTypeT]]]


def get_arg_parser(default_config_name: str = "release") -> argparse.ArgumentParser:
    """Get the argument parser for config parsing.

    The following defaults are applied:
        * Config Name = release
        * Run Mode = long
        * Environment = Azure whenever running on aml mode, local otherwise

    Args:
        default_config_name: The default config name to use if not specified in the command line arguments.
    """
    parser = argparse.ArgumentParser()

    parser.add_argument(
        "--config_name",
        type=str,
        help="Sets the config name to use. If not set, the default is 'release'.",
        default=default_config_name,
    )

    parser.add_argument(
        "--run_mode",
        type=RunMode,
        choices=list(RunMode),
        help="The run mode to use.",
        default=RunMode.LONG,
    )

    parser.add_argument(
        "--environment",
        type=RuntimeEnvironment,
        choices=list(RuntimeEnvironment),
        help="The environment to run in.",
        default=RuntimeEnvironment.AZURE if runs_on_aml_node() else RuntimeEnvironment.LOCAL,
    )

    parser.add_argument(
        "--input_folder",
        type=Path,
        help="The base input folder path.",
        default=None,
    )

    parser.add_argument(
        "--output_folder",
        type=Path,
        help="The base output folder path.",
        default=Path("outputs"),
    )

    # For compatibility with the existing launcher, parsed value is not used
    parser.add_argument(
        "--aml_node_count", type=int, help="Number of compute nodes which were launched.", required=False, default=None
    )

    parser.add_argument(
        "--overrides",
        nargs="*",
        help="Any key=value arguments to override config values (use dots for.nested=overrides)",
    )

    return parser


def _load_config_with_overrides(
    available_configs: ConfigStore[ConfigTypeT],
    pipeline_step: PipelineStep,
    default_config_name: str = "release",
) -> ConfigTypeT:
    """Load the config and merge pre-defined overrides based on the cli flags as well als individual cli overrides.

    The override hierarchy is as follows: Overrides are applied in a specific order, with later overrides taking
    precedence and remaining in the final configuration. The order of application is:

    1. Pre-defined overrides based on the configuration name and pipeline step.
    2. Overrides based on the runtime environment.
    3. Overrides based on the run mode.
    4. Command line overrides specified as key-value pairs.

    This ensures that command line overrides have the highest precedence.
    """
    args = get_arg_parser(default_config_name=default_config_name).parse_args()

    io_settings = IOSettings(input_folder=args.input_folder, output_folder=args.output_folder)

    # get the initial config and associated pre-defined overrides based on the config name
    config_with_overrides = available_configs.get(args.config_name)
    if config_with_overrides is None:
        msg = f"Config name '{args.config_name}' not found in registry, which contains '{available_configs.keys()}'."
        raise KeyError(msg)

    build_config, overrides = config_with_overrides
    config = build_config(pipeline_step, io_settings)
    config = cast(ConfigTypeT, OmegaConf.structured(config))

    runtime_env_overrides = overrides.get(args.environment, None)
    if runtime_env_overrides is not None:
        _LOGGER.info(f"Applying overrides for runtime environment '{args.environment}'.")
        config = runtime_env_overrides.override(config)

    run_mode_overrides = overrides.get(args.run_mode, None)
    if run_mode_overrides is not None:
        _LOGGER.info(f"Applying overrides for run mode '{args.run_mode}'.")
        config = run_mode_overrides.override(config)

    # merge overrides from the command line arguments last, as they should take precedence
    if args.overrides:
        _LOGGER.info(f"Applying CLI overrides: {args.overrides}.")
        cli_overrides_config = OmegaConf.from_dotlist(args.overrides)
        config = OmegaConf.merge(config, cli_overrides_config)

    config = cast(ConfigTypeT, OmegaConf.to_object(config))

    return config


def load_config(
    available_configs: ConfigStore[ConfigTypeT],
    pipeline_step: PipelineStep,
    default_config_name: str = "release",
) -> Callable[[Callable[[ConfigTypeT], None]], Callable[[], None]]:
    """Decorator to load the config and merge pre-defined overrides before executing the function.

    The pre-defined overrides are enabled through the following command line arguments:
    - `--config_name`: The name of the base config as defined in the `available_configs` mapping that
      is passed to the decorator.
    - `--run_mode`: The run mode to use, which can be one of the `RunMode` enum values.
    - `--environment`: The runtime environment to use, which can be one of the `RuntimeEnvironment` enum values.

    Any additional key=value arguments can be passed as overrides, which will take precedence over the pre-defined
    overrides when merging the configuration.

    The override hierarchy is as follows: Overrides are applied in a specific order, with later overrides taking
    precedence and remaining in the final configuration. The order of application is:

    1. Pre-defined overrides based on the configuration name and pipeline step.
    2. Overrides based on the runtime environment.
    3. Overrides based on the run mode.
    4. Command line overrides specified as key-value pairs.

    This ensures that command line overrides have the highest precedence.

    Args:
        available_configs: Mapping of config names to tuples of (ConfigBuildFunc[ConfigTypeT], OverrideRegistry).
        pipeline_step: The pipeline step for which to apply the overrides.
        default_config_name: The default config name to use if not specified in the command line arguments.
    """

    def decorator(func: Callable[[ConfigTypeT], Any]) -> Callable[[], None]:
        """Inner decorator without additional arguments that is returned."""

        @functools.wraps(func)
        def wrapper() -> None:
            """Wrapper function."""
            config = _load_config_with_overrides(available_configs, pipeline_step, default_config_name)
            func(config)

        return wrapper

    return decorator


def get_config_name(argv: list[str]) -> str | None:
    """Get the config name from the command line arguments.

    Args:
        argv: The command line arguments passed to the script.

    Returns:
        The config name specified in the command line arguments, or None if not found.
    """
    parser = argparse.ArgumentParser()

    parser.add_argument("--config_name", type=str, required=False, default="release")
    parser.add_argument("--model", type=str, required=False, default=None)

    args, _ = parser.parse_known_args(argv)

    return args.model or args.config_name
