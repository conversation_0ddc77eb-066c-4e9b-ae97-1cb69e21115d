"""Contains callback functionality for the SparseLanecpp task."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections.abc import Callable, Mapping, Sequence
from typing import Any

import torch
from torch import Tensor

from data_formats.common.size_modification.image import crop_pad
from xcontract.data.definitions.image import HW
from xtorch_extensions.visualization.callbacks.lane3d import VisuLane3D
from xtorch_usecases.single_camera.size_modification.pyper.offset import fixed_bottom_crop_center
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppCameraDataConfig,
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.lane_postprocessing import (
    post_processing_gts,
    post_processing_preds,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_decoder import SparseLanecppLineDecoder


def extract_visu_lane3d_from_gt(
    ground_truth: SparseLanecppGroundTruth,
) -> Sequence[VisuLane3D]:
    """Extracts the Lane3d ground truth data from the batch.

    Args:
        ground_truth: The ground truth data containing lane information.

    Returns: A list of VisuLane3D objects.
    """
    gt_lines = post_processing_gts(ground_truth.lane_category, ground_truth.lanes_dense)
    return [
        VisuLane3D(
            lanes_category=gt_line.gt_lanes_class,
            lanes_dense_points=gt_line.gt_lanes_coord,
            lanes_vis=gt_line.gt_lanes_vis,
        )
        for gt_line in gt_lines
    ]


def extract_visu_lane3d_from_preds(
    outputs: Mapping[str, Any],
    task_identifier: str,
    get_preds: Callable[[Mapping[str, Any], str], SparseLanecppHeadOutput],
) -> Sequence[VisuLane3D]:
    """Extracts the Lane3d predictions from the outputs.

    Args:
        outputs: The output of the SparseLanecppHead.
        task_identifier: The identifier for the task.
        get_preds: Callable to get the predictions from the outputs.

    Returns:
        A list of VisuLane3D objects containing the predicted lane data.

    """
    preds = get_preds(outputs, task_identifier)
    postprocessed_preds = post_processing_preds(preds.raw_output.class_scores, preds.lanes_dense)
    return [
        VisuLane3D(
            lanes_category=pred.pred_lanes_prob,
            lanes_dense_points=pred.pred_lanes,
            lanes_vis=pred.pred_lanes_vis,
        )
        for pred in postprocessed_preds
    ]


def extract_visu_lane3d_from_inference_preds(
    outputs: Mapping[str, Any], task_identifier: str, line_decoder: SparseLanecppLineDecoder, backend_name: str
) -> Sequence[VisuLane3D]:
    """Extracts the Lane3d predictions from the outputs.

    Args:
        outputs: The output of the SparseLanecppHead.
        task_identifier: The identifier for the task.
        line_decoder: The line decoder used for decoding the lane predictions.
        backend_name: The name of the backend used for inference.

    Returns:
        A list of VisuLane3D objects containing the predicted lane data.

    """
    preds = outputs[backend_name]
    cls_scores = preds["sparse_lanecpp_raw_output_class_scores"]
    line_preds_unscaled = preds["sparse_lanecpp_raw_output_lane_preds_unscaled"]
    batch_size = cls_scores.shape[0]

    all_cls_scores = cls_scores[None, ...]
    all_line_preds_orig, all_lines_vis = line_preds_unscaled[None, ..., :2], line_preds_unscaled[None, ..., 2:]
    _, _, _, all_line_preds = line_decoder(all_line_preds_orig, all_lines_vis, batch_size=batch_size)
    pred_lines_sub = post_processing_preds(all_cls_scores, all_line_preds)
    return [
        VisuLane3D(
            lanes_category=pred_lines_sub[idx].pred_lanes_prob,
            lanes_dense_points=pred_lines_sub[idx].pred_lanes,
            lanes_vis=pred_lines_sub[idx].pred_lanes_vis,
        )
        for idx in range(batch_size)
    ]


def adjust_to_target_shape(images: Tensor, camera_data_config: SparseLanecppCameraDataConfig) -> Tensor:
    """Adjusts the input images to the target shape.

    Args:
        images: The input images to be adjusted.
        camera_data_config: The camera data configuration containing the target shape.

    Returns:
        The adjusted images with the target shape.
    """
    image_shape = HW(*images.shape[2:])

    if image_shape == camera_data_config.original_shape:
        offsets = fixed_bottom_crop_center(
            image_shape=image_shape,
            target_shape=camera_data_config.target_shape,
            bottom_crop=camera_data_config.crop_bottom,
        )

        def _crop_pad(image: Tensor) -> Tensor:
            return image.new_tensor(
                crop_pad(
                    image.permute(1, 2, 0).cpu().numpy(), offsets=offsets, target_shape=camera_data_config.target_shape
                )
            ).permute(2, 0, 1)

        return torch.stack([_crop_pad(image) for image in images.unbind()])
    if image_shape == camera_data_config.target_shape:
        return images

    message = (
        f"Unexpected input shape {image_shape}. Expected "
        f"{camera_data_config.original_shape} or {camera_data_config.target_shape}."
    )
    raise ValueError(message)
