"""Evaluation of 3D lane detections."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import csv
from dataclasses import dataclass, field, fields
from pathlib import Path

import numpy as np
from numpy.typing import NDArray
from torchmetrics import Metric

from xtorch_usecases.single_camera.tasks.sparse_lanecpp.evaluation.distance import (
    IGNORE_DISTANCE_VAL,
    compute_dists_cost_mat,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.evaluation.min_cost_flow import solve_min_cost_flow
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.evaluation.preprocessor import (
    prep_lanes,
    resample_lanes,
    split_line_by_visibility,
    unpack_pred_eval,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.lane_postprocessing import (
    PostprocessedGTs,
    PostprocessedPreds,
)

_DISTANCE_BINS = [10, 20, 30, 50, 70, 200]


@dataclass
class Lane3DEvalStats:
    """Result of evaluation stats for one sample."""

    num_matches_by_distance: NDArray[np.int32]
    x_error_by_distance: NDArray[np.float32]
    z_error_by_distance: NDArray[np.float32]
    recall: float = 0.0
    precision: float = 0.0
    category_matches: float = 0.0
    num_gts: int = 0
    num_preds: int = 0
    num_matches: int = 0

    def __add__(self, other: "Lane3DEvalStats") -> "Lane3DEvalStats":
        """Overwrite the add operator to sum two Lane3DEvalStats objects."""
        return Lane3DEvalStats(**{f.name: getattr(self, f.name) + getattr(other, f.name) for f in fields(self)})


@dataclass
class Lane3DMetrics:
    """Lane3DMetrics class.

    The final metrics and errors that will be saved in the csv file.
    """

    f1_score: float = 0.0
    precision: float = 0.0
    recall: float = 0.0
    cat_accuracy: float = 0.0
    x_error_by_distance: dict[float, float] = field(default_factory=dict)
    z_error_by_distance: dict[float, float] = field(default_factory=dict)

    def get_result_dict(self, prefix: str = "") -> dict[str, float]:
        """Get the result as a flattened dictionary."""
        result_dict = {}
        for f in fields(self):
            value = getattr(self, f.name)
            # Flatten nested dictionaries
            if isinstance(value, dict):
                for key, val in value.items():
                    display_key = f"{key:.2f}" if isinstance(key, float) else key
                    result_dict[f"{prefix}{f.name}_{display_key}"] = val
            else:
                result_dict[f"{prefix}{f.name}"] = value
        return result_dict


class Lane3DEval(Metric):
    """Lane3DEval class."""

    def __init__(
        self,
        pos_threshold: float,
        top_view_region: NDArray[np.float32],
        metric_save_path: Path,
        task_id: str,
        range_extent: int = 10,
        num_y_steps: int = 500,
        dist_threshold: float = 1.5,
        ratio_threshold: float = 0.75,
        distance_bins: list[int] | None = None,
    ) -> None:
        """Initializes the Lane3DEval class.

        Args:
            pos_threshold: The positive threshold for evaluation.
            top_view_region: A 2D array defining the top-view region
                boundaries for evaluation. The array is expected to have shape (3, 2),
                where each row corresponds to a specific boundary point.
            metric_save_path: path to save the metrics in csv.
            task_id: The ID of the evaluated task.
            range_extent: Amount to extend the evaluation region in the x-direction (both sides).
                This expands the default top-view region by ±range_extent units.
            num_y_steps: Number of y steps for resampling lanes.
            dist_threshold: Distance threshold for matching lanes.
            ratio_threshold: Visibility ratio threshold for matching lanes.
            distance_bins: Defines the depth bins for which to evaluate the regression errors in x and z coordinates.

        """
        super().__init__()
        self._pos_threshold = pos_threshold
        self._save_path = metric_save_path
        self._task_id = task_id

        self._x_min = top_view_region[0, 0]
        self._x_max = top_view_region[1, 0]
        self._y_min = top_view_region[2, 1]
        self._y_max = top_view_region[0, 1]

        self._x_min = self._x_min - range_extent
        self._x_max = self._x_max + range_extent
        self._x_limits = (self._x_min, self._x_max)
        self._num_y_steps = num_y_steps
        self._dist_threshold = dist_threshold
        self._ratio_threshold = ratio_threshold

        self._y_steps = np.linspace(self._y_min, self._y_max, num=self._num_y_steps, endpoint=False)
        self._y_limits = (self._y_steps[0], self._y_steps[-1])

        self._distance_bins = distance_bins or _DISTANCE_BINS
        self._num_distance_bins = len(self._distance_bins)
        self._metrics_and_errors = Lane3DMetrics()
        self._stats = Lane3DEvalStats(
            num_matches_by_distance=np.zeros(self._num_distance_bins, dtype=np.int32),
            x_error_by_distance=np.zeros(self._num_distance_bins, dtype=np.float32),
            z_error_by_distance=np.zeros(self._num_distance_bins, dtype=np.float32),
        )

    def update(
        self,
        pred_lines: list[PostprocessedPreds],
        gt_lines: list[PostprocessedGTs],
    ) -> None:
        """This method updates the state variables of the metric class.

        Args:
            pred_lines: a batch of post-processed lane preds.
            gt_lines: a batch of post-processed lane gts.

        Returns:
            output_stats: A dictionary with a list of float as values:
                x_errors_sum: the summed error in x for each interval
                x_errors_count: total count of cost for calculating errors in x, for each interval
                z_errors_sum: the summed error in z for each interval
                z_errors_count: total count of cost for calculating errors in z, for each interval
        """
        for batch_idx, preds in enumerate(pred_lines):
            pred_lanes, pred_category = unpack_pred_eval(preds, confidence_threshold=self._pos_threshold)

            gt_lanes = gt_lines[batch_idx].gt_lanes_coord
            gt_category = gt_lines[batch_idx].gt_lanes_class
            gt_visibility = gt_lines[batch_idx].gt_lanes_vis

            gt_lanes_with_split, gt_visibility_with_split, gt_category_with_split = [], [], []
            for lane_idx, gt_lane in enumerate(gt_lanes):
                gt_lanes_split, gt_vis_split, gt_cat_split = split_line_by_visibility(
                    gt_lane, gt_visibility[lane_idx], int(gt_category[lane_idx])
                )
                gt_lanes_with_split.extend(gt_lanes_split)
                gt_visibility_with_split.extend(gt_vis_split)
                gt_category_with_split.extend(gt_cat_split)
            gt_lanes = gt_lanes_with_split
            gt_category = gt_category_with_split
            gt_visibility = gt_visibility_with_split

            self._stats += self._sample_eval(
                pred_lanes=pred_lanes,
                gt_lanes=gt_lanes,
                gt_visibility=gt_visibility,
                pred_category=pred_category,
                gt_category=gt_category,
            )

    def compute(self) -> dict[str, float]:
        """This method computes the final metric value.

        Returns:
            All metrics and errors as a dictionary.
        """
        recall = _safe_div(self._stats.recall, self._stats.num_gts)
        precision = _safe_div(self._stats.precision, self._stats.num_preds)
        category_accuracy = _safe_div(self._stats.category_matches, self._stats.num_matches)
        f1_score = _safe_div(2 * recall * precision, recall + precision)

        # Divide the x and z errors by the number of matches in each distance bin
        divisor = np.where(self._stats.num_matches_by_distance > 0, self._stats.num_matches_by_distance, 1)
        x_error_by_distance = self._stats.x_error_by_distance / divisor
        z_error_by_distance = self._stats.z_error_by_distance / divisor

        self._metrics_and_errors = Lane3DMetrics(
            f1_score=f1_score,
            recall=recall,
            precision=precision,
            cat_accuracy=category_accuracy,
            x_error_by_distance={
                self._distance_bins[i]: x_error_by_distance[i] for i in range(self._num_distance_bins)
            },
            z_error_by_distance={
                self._distance_bins[i]: z_error_by_distance[i] for i in range(self._num_distance_bins)
            },
        )

        self._add_eval_to_csv(
            self._metrics_and_errors,
            result_dir=self._save_path,
        )

        # Return the metrics and errors as a dictionary for MLFlow logging
        return self._metrics_and_errors.get_result_dict(prefix=f"evaluation/{self._task_id}/")

    def reset(self) -> None:
        """Reset all statistics and prepare for a new evaluation."""
        super().reset()
        self._stats = Lane3DEvalStats(
            num_matches_by_distance=np.zeros(self._num_distance_bins, dtype=np.int32),
            x_error_by_distance=np.zeros(self._num_distance_bins, dtype=np.float32),
            z_error_by_distance=np.zeros(self._num_distance_bins, dtype=np.float32),
        )

    def _compute_eval_stats(
        self,
        average_distances: NDArray[np.float32],
        num_vis_pred_points: NDArray[np.float32],
        num_vis_gt_points: NDArray[np.float32],
        match_results: NDArray[np.float32],
        num_match_mat: NDArray[np.float32],
        pred_cat: list[int],
        gt_cat: list[int],
    ) -> Lane3DEvalStats:
        """Compute evaluation metrics for lane detection.

        This method calculates various evaluation metrics such as recall, precision,
        category match count, and positional errors (x and z) for the defined distance ranges.
        It considers only the matched pair that is below the distance threshold
        (self._dist_threshold * self._y_steps.shape[0])

        Args:
            average_distances: Average distances between matched ground truth and predicted lanes with a shape of
                (num_gt, num_pred, num_distance_bins, 2) where the last dimension contains (x, z) distances.
            num_vis_pred_points: Number of visible points for each predicted lane.
            num_vis_gt_points: Number of visible points for each ground truth lane.
            match_results: Array containing matching results with shape (num_matches, 3), where each row
                contains [gt_id, pred_id, distance].
            num_match_mat: Matrix containing the number of matched points between ground truth and predictions.
            pred_cat: List of predicted lane categories.
            gt_cat: List of ground truth lane categories.

        Returns: The Lane3DEvalStats containing the summed stats for the sample
        """
        recall, precision, category_matches = 0.0, 0.0, 0.0
        num_matches = 0
        total_distance_errors = np.zeros((self._num_distance_bins, 2), dtype=np.float32)
        num_matches_per_distance = np.zeros(self._num_distance_bins, dtype=np.int32)
        if match_results.shape[0] > 0:
            for i in range(len(match_results)):
                if match_results[i, 2] < self._dist_threshold * self._y_steps.shape[0]:
                    num_matches += 1
                    gt_i = match_results[i, 0]
                    pred_i = match_results[i, 1]
                    # consider match when the number of matched, visible points is above a ratio
                    if num_match_mat[gt_i, pred_i] / num_vis_gt_points[gt_i] >= self._ratio_threshold:
                        recall += 1
                    if num_match_mat[gt_i, pred_i] / num_vis_pred_points[pred_i] >= self._ratio_threshold:
                        precision += 1

                    if pred_cat[pred_i] == gt_cat[gt_i]:
                        category_matches += 1  # category matched num

                    # Aggregate x- & z-errors for all ranges
                    for idx, _ in enumerate(self._distance_bins):
                        if average_distances[gt_i, pred_i, idx, 0] != IGNORE_DISTANCE_VAL:
                            total_distance_errors[idx, :] += average_distances[gt_i, pred_i, idx, :]
                            num_matches_per_distance[idx] += 1

        return Lane3DEvalStats(
            recall=recall,
            precision=precision,
            category_matches=category_matches,
            num_gts=num_match_mat.shape[0],
            num_preds=num_match_mat.shape[1],
            num_matches=num_matches,
            num_matches_by_distance=num_matches_per_distance,
            x_error_by_distance=total_distance_errors[:, 0],
            z_error_by_distance=total_distance_errors[:, 1],
        )

    def _sample_eval(
        self,
        pred_lanes: list[NDArray[np.float32]],
        gt_lanes: list[NDArray[np.float32]],
        gt_visibility: list[NDArray[np.bool_]],
        pred_category: list[int],
        gt_category: list[int],
    ) -> Lane3DEvalStats:
        """Compute evaluation stats and errors for 3D lane detections for one sample.

        This method processes predicted and ground truth lanes by resampling, filtering,
        and computing pairwise distances. It then applies a minimum-cost flow algorithm
        to find the best matches and calculates recall, precision, and category match statistics.

        Args:
            pred_lanes: a list of (N_point X 2) or (N_point X 3) lane depending on 2D or 3D, len() = number of pred lane
            gt_lanes: a list of (N_point X 2) or (N_point X 3) lane depending on 2D or 3D, len() = number of gt lane
            gt_visibility: a list of (N_point,) visibility, len() = number of gt lane
            pred_category: a list of class id, len() = number of pred lane
            gt_category: a list of class id, len() = number of gt lane

        Returns: Lane3DEvalStats containing the evaluation statistics for the sample.
        """
        gt_lanes, gt_category, gt_visibility = prep_lanes(
            lanes=gt_lanes,
            lane_categories=gt_category,
            lane_visibilities=gt_visibility,
            x_limits=self._x_limits,
            y_limits=self._y_limits,
        )
        pred_lanes, pred_category, _ = prep_lanes(
            lanes=pred_lanes, lane_categories=pred_category, x_limits=self._x_limits, y_limits=self._y_limits
        )

        num_gts = len(gt_lanes)
        num_preds = len(pred_lanes)

        # resample gt and pred at y_steps
        gt_lanes, gt_visibility_mat = resample_lanes(
            gt_lanes, visibility=gt_visibility, x_limits=self._x_limits, y_steps=self._y_steps
        )
        pred_lanes, pred_visibility_mat = resample_lanes(pred_lanes, x_limits=self._x_limits, y_steps=self._y_steps)

        # at least two-points for both gt and pred
        gt_lanes = [gt_lanes[k] for k in range(num_gts) if np.sum(gt_visibility_mat[k, :]) > 1]
        gt_category = [gt_category[k] for k in range(num_gts) if np.sum(gt_visibility_mat[k, :]) > 1]
        gt_visibility_mat = gt_visibility_mat[np.sum(gt_visibility_mat, axis=-1) > 1, :]
        num_gts = len(gt_lanes)

        pred_lanes = [pred_lanes[k] for k in range(num_preds) if np.sum(pred_visibility_mat[k, :]) > 1]
        pred_category = [pred_category[k] for k in range(num_preds) if np.sum(pred_visibility_mat[k, :]) > 1]
        pred_visibility_mat = pred_visibility_mat[np.sum(pred_visibility_mat, axis=-1) > 1, :]
        num_preds = len(pred_lanes)

        cost_mat, adj_mat, num_match_mat, average_distances = compute_dists_cost_mat(
            pred_lanes,
            pred_visibility_mat,
            gt_lanes,
            gt_visibility_mat,
            y_steps=self._y_steps,
            distance_bins=np.array(self._distance_bins),
            dist_threshold=self._dist_threshold,
        )

        # solve bipartite matching with min cost flow solver
        match_results = solve_min_cost_flow(adj_mat, cost_mat)
        match_results = np.array(match_results)

        return self._compute_eval_stats(
            average_distances=average_distances,
            num_vis_pred_points=np.sum(pred_visibility_mat, axis=1),
            num_vis_gt_points=np.sum(gt_visibility_mat, axis=1),
            match_results=match_results,
            num_match_mat=num_match_mat,
            pred_cat=pred_category,
            gt_cat=gt_category,
        )

    def _add_eval_to_csv(self, metrics: Lane3DMetrics, result_dir: Path) -> None:
        """Append evaluation results to a CSV file.

        This method writes evaluation results, including F1 score, precision, recall,
        category accuracy, and error metrics, to a CSV file.

        Args:
            metrics: An object containing evaluation metrics and errors.
            result_dir: The directory where the CSV file will be saved.

        """
        # Create the directory if it doesn't exist
        result_dir.mkdir(parents=True, exist_ok=True)

        # Define the CSV file path
        csv_file_path = result_dir / "SparseLanecpp_evaluation_results.csv"

        # Prepare the header if the file is new
        file_exists = csv_file_path.is_file()

        # Append the results to the CSV file
        metrics_dict = metrics.get_result_dict()
        with csv_file_path.open("a", newline="") as csvfile:
            csv_writer = csv.writer(csvfile)

            # Write header if the file is new
            if not file_exists:
                csv_writer.writerow([_to_camel_case(key) for key in metrics_dict])

            # Write the evaluation results
            csv_writer.writerow(list(metrics_dict.values()))


def _safe_div(numerator: float, denominator: float) -> float:
    """Safe division returning 0 when dividing 0/0."""
    if denominator != 0.0:
        return numerator / denominator
    if numerator == 0.0:
        return 0.0
    msg = "Dividing non-zero number by 0"
    raise ValueError(msg)


def _to_camel_case(snake_str: str) -> str:
    """Convert a snake_case string to CamelCase."""
    return "".join(x.capitalize() for x in snake_str.lower().split("_"))
