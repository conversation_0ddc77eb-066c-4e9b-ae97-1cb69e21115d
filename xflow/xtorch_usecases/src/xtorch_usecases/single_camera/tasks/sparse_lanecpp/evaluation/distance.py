"""Computes distances between 3D lanes."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import numpy as np
from numpy.typing import NDArray

IGNORE_DISTANCE_VAL = -1.0


def _compute_euclidean_dist(
    gt_lane: NDArray[np.float32],
    pred_lane: NDArray[np.float32],
    gt_visibility: NDArray[np.float32],
    pred_visibility: NDArray[np.float32],
    dist_threshold: float,
) -> tuple[NDArray[np.float32], NDArray[np.float32], NDArray[np.float32], NDArray[np.bool_], NDArray[np.bool_]]:
    """Computes element-wise Euclidean distances and visibility masks.

    Args:
        gt_lane: Ground truth lane points.
        pred_lane: Predicted lane points.
        gt_visibility: Visibility of ground truth lane points.
        pred_visibility: Visibility of predicted lane points.
        dist_threshold: Distance threshold for matching.

    Returns:
        euclidean_dist: Distance matrix adjusted for visibility.
        x_distances, z_distances: Per-axis distance vector.
        both_visible: Mask where both GT and prediction are visible.
        both_invisible: Mask where both are invisible.
    """
    x_distances = np.abs(gt_lane[:, 0] - pred_lane[:, 0])
    z_distances = np.abs(gt_lane[:, 1] - pred_lane[:, 1])
    euclidean_dist = np.sqrt(x_distances**2 + z_distances**2)

    # apply visibility to penalize different partial matching accordingly
    both_visible = np.logical_and(gt_visibility >= 0.5, pred_visibility >= 0.5)
    both_invisible = np.logical_and(gt_visibility < 0.5, pred_visibility < 0.5)
    other_indices = np.logical_not(np.logical_or(both_visible, both_invisible))

    euclidean_dist[both_invisible] = 0
    euclidean_dist[other_indices] = dist_threshold

    return euclidean_dist, x_distances, z_distances, both_visible, both_invisible


def _compute_average_dist(
    distances: NDArray[np.float32],
    both_visible_indices: NDArray[np.bool_],
    distance_bin_idxs: NDArray[np.int16],
) -> NDArray[np.float32]:
    """Computes average distance errors in multiple distance bins.

    Average distance error only using points where both GT and predicted lanes are visible.
    If no such visible points exist in this range, set distance error to -1.0 indicating invalid match.

    Args:
        distances: distances with shape (num_y_steps, 2) representing (x,z).
        both_visible_indices: Mask indicating where both GT and prediction are visible with shape (num_y_steps,).
        distance_bin_idxs: Index where the distance bins start in the y_steps array with shape (num_distance_bins,).

    Returns: The average distances in the defined distance bins.
        - If no visible points exist in the range, returns IGNORE_DISTANCE_VAL.
        - Otherwise, returns the average distance in the range.
    """
    # Prepend 0 as required by np.add.reduceat for the first section
    distance_bin_idxs = np.concatenate((np.zeros((1,), dtype=np.int16), distance_bin_idxs), axis=0)

    # Compute average distance of visible points for each distance bin
    # np.add.reduceat always evaluates the last slice up to the array end
    num_y_steps = distances.shape[0]
    if distance_bin_idxs[-1] < num_y_steps:
        # Drop last entry in case we're not interested
        average_distances = np.add.reduceat(distances * both_visible_indices[:, None], distance_bin_idxs, axis=0)
        average_distances = average_distances[:-1, :]
        num_visible_points_per_bin = np.add.reduceat(both_visible_indices, distance_bin_idxs, axis=0)[:-1, None]
    else:
        # Drop last index in case it points to the array end & keep the last result
        average_distances = np.add.reduceat(distances * both_visible_indices[:, None], distance_bin_idxs[:-1], axis=0)
        num_visible_points_per_bin = np.add.reduceat(both_visible_indices, distance_bin_idxs[:-1], axis=0)[:, None]

    # Divide by number of visible points within each bin. Set IGNORE_DISTANCE_VAL if no visible points are inside.
    average_distances = np.where(
        num_visible_points_per_bin > 0,
        average_distances / num_visible_points_per_bin,
        np.full_like(average_distances, IGNORE_DISTANCE_VAL),
    )
    return average_distances


def compute_dists_cost_mat(
    pred_lanes: list[NDArray[np.float32]],
    pred_visibility_mat: NDArray[np.float32],
    gt_lanes: list[NDArray[np.float32]],
    gt_visibility_mat: NDArray[np.float32],
    y_steps: NDArray[np.float32],
    distance_bins: NDArray[np.float32],
    dist_threshold: float,
) -> tuple[
    NDArray[np.int64],
    NDArray[np.int64],
    NDArray[np.float32],
    NDArray[np.float32],
]:
    """Compute distance cost metrics between predicted and ground truth lanes.

    This method calculates various distance metrics and cost matrices to evaluate
    the alignment between predicted and ground truth lanes. It considers both
    visibility and spatial distances to compute these metrics.

    Args:
        pred_lanes: List of predicted lane coordinates,
            where each lane is represented as a 2D array of shape (N, 2) with x and z coordinates.
        pred_visibility_mat: Visibility matrix for predicted lanes
            of shape (num_pred_lanes, num_y_samples), where each value indicates the visibility of a lane point.
        gt_lanes: List of ground truth lane coordinates,
            where each lane is represented as a 2D array of shape (N, 2) with x and z coordinates.
        gt_visibility_mat: Visibility matrix for ground truth lanes
            of shape (num_gt_lanes, num_y_samples), where each value indicates the visibility of a lane point.
        y_steps: Array of y-coordinates (steps) for resampling lanes
        distance_bins: List of distance bins for categorizing distances
        dist_threshold: The distance threshold for matching lanes.

    Returns:
        - cost_mat: Cost matrix of shape (num_gt_lanes, num_pred_lanes),
            representing the cost of matching each ground truth lane to a predicted lane.
        - adj_mat: Adjacency matrix of shape (num_gt_lanes, num_pred_lanes),
            indicating whether a match is valid (1) or not (0).
        - num_match_mat: Matrix of shape (num_gt_lanes, num_pred_lanes),
            representing the number of matched points between ground truth and predicted lanes.
        - dist_mat: Matrix of shape (num_gt_lanes, num_pred_lanes, num_distance_bins, 2),
            representing the distances in (x,z) in the defined ranges.

    Notes:
        - The method uses visibility information to penalize partial matches differently.
        - Euclidean distance is used to compute the spatial distance between lane points.
        - The close and far ranges are determined based on the `close_range` parameter.
        - Cost values are adjusted to meet the requirements of minimum cost flow optimization.
    """
    # Find the first index where y_steps is > than the distance bin value
    distance_bin_idxs = np.searchsorted(y_steps, distance_bins)
    num_distance_bins = distance_bins.shape[0]
    num_preds = len(pred_lanes)
    num_gts = len(gt_lanes)

    cost_mat = np.full((num_gts, num_preds), 1000, dtype=int)
    adj_mat = np.zeros((num_gts, num_preds), dtype=int)
    num_match_mat = np.zeros((num_gts, num_preds), dtype=float)
    dist_mat = np.full((num_gts, num_preds, num_distance_bins, 2), 1000.0, dtype=float)

    # compute curve to curve distance
    for gt_idx in range(num_gts):
        for pred_idx in range(num_preds):
            # compute element-wise euclidean distance
            euclidean_dist, x_distances, z_distances, both_visible_indices, both_invisible_indices = (
                _compute_euclidean_dist(
                    gt_lanes[gt_idx],
                    pred_lanes[pred_idx],
                    gt_visibility_mat[gt_idx],
                    pred_visibility_mat[pred_idx],
                    dist_threshold,
                )
            )

            # ATTENTION: use the sum as int type to meet the requirements of min cost flow optimization (int type)
            cost_ = np.sum(euclidean_dist)
            cost_mat[gt_idx, pred_idx] = 1 if 0 < cost_ < 1 else (cost_).astype(int)
            num_match_mat[gt_idx, pred_idx] = np.sum(euclidean_dist < dist_threshold).astype(float) - np.sum(
                both_invisible_indices.astype(float)
            )
            adj_mat[gt_idx, pred_idx] = 1

            # compute average distance for all ranges
            dist_mat[gt_idx, pred_idx, :, :] = _compute_average_dist(
                np.stack((x_distances, z_distances), -1), both_visible_indices, distance_bin_idxs
            )

    return cost_mat, adj_mat, num_match_mat, dist_mat
