"""The SparseLanecpp head."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import numpy as np
import torch
from numpy.typing import NDArray
from torch import Tensor

from data_formats.camera.definitions import CameraParameters
from xcontract.camera_models.definitions import CameraModelType
from xcontract.data.definitions.image import HW
from xcontract.geometry.definitions.volume import VolumeRange
from xtorch.multi_task.structured_task import StructuredTaskHead
from xtorch.nn.blocks.conv_norm_act_2d import ConvBnAct2d
from xtorch.nn.blocks.sliced_multihead_attention import AttentionNonlinearityType
from xtorch.nn.heads.lane3d.transformer_decoder_layer import TransformerDecoderLayerParams
from xtorch_usecases.single_camera.network import BIFPN_CHANNELS, ModelInputType
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.anchors import LineRepresentation
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppHeadInput,
    SparseLanecppHeadOutput,
    SparseLanecppHeadRawOutput,
    SparseLanecppHeadScaledOutput,
    SparseLanecppInstanceHeadOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_decoder import SparseLanecppLineDecoder
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.query_generator import (
    QueryGeneratorOutput,
    SparseInstanceDecoderOutput,
    SparseLanecppQueryGenerator,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.transformer import (
    SparseLanecppTransformer,
    SparseLanecppTransformerInput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.transformer_decoder import SparseLanecppTransformerOutput

IGNORE_VALUE = -9.0  # Value used for 0 probability predictions. Equal to 1e-4 after applying sigmoid.


class SparseLanecppHead(StructuredTaskHead[SparseLanecppHeadInput, ModelInputType, SparseLanecppHeadOutput]):
    """SparseLanecppHead for 3D lane detection in images."""

    def __init__(  # noqa: PLR0913
        self,
        target_shape: HW,
        num_queries_lanes_road_edges: int,
        num_classes_lanes_road_edges: int,
        num_queries_holistic_ego_lanes: int,
        num_classes_holistic_ego_lanes: int,
        num_points_per_line: int,
        anchor_y_steps: NDArray[np.float32],
        anchor_y_steps_dense: NDArray[np.float32],
        position_range: VolumeRange,
        attention_nonlinearity: AttentionNonlinearityType,
        line_representation: LineRepresentation,
        *,
        embed_dims: int = 128,
        points_as_query: bool = True,
        with_iterative_refinement: bool = False,
    ) -> None:
        """Initialize the SparseLanecppHead.

        Warning : Setting points_as_query to False currently breaks the training chain. This is already noted as
        Technical debt.

        Args:
            target_shape: Target shape of the image.
            num_queries_lanes_road_edges: Number of queries for lanes and road edges transformer.
            num_classes_lanes_road_edges: Number of lane & road edge classes.
            num_queries_holistic_ego_lanes: Number of queries for the holistic ego lanes transformer.
            num_classes_holistic_ego_lanes: Number of holistic ego lane classes.
            num_points_per_line: Number of points per line.
            anchor_y_steps: The y-steps of the anchors.
            anchor_y_steps_dense: The y-steps of the dense anchors.
            position_range: Range of positions for the anchors.
            attention_nonlinearity: which non-linearity to use in the attention computation,
                                    ReLU, Softmax, HardSwish or ReLU6.
            line_representation: Type of line representation to use.
            embed_dims: Embedding dimension for the transformer.
            points_as_query: Whether to use points as queries.
            with_iterative_refinement: If true each decoder layer predicts relative offsets of control points, if false
                each decoder layer predicts global coordinates.
        """

        super().__init__()
        self._target_shape = target_shape
        num_anchor_per_query = len(anchor_y_steps) if points_as_query else 1
        self._num_points_per_anchor = num_points_per_line // num_anchor_per_query
        self._with_iterative_refinement = with_iterative_refinement
        self._input_adaptation = ConvBnAct2d(
            in_channels=BIFPN_CHANNELS,
            out_channels=embed_dims,
            kernel_size=1,
        )

        self.num_classes_lanes_road_edges = num_classes_lanes_road_edges
        self.num_classes_holistic_ego_lanes = num_classes_holistic_ego_lanes

        self._lane_road_edge_query_generator = SparseLanecppQueryGenerator(
            embed_dims,
            num_queries_lanes_road_edges,
            num_classes_lanes_road_edges,
            num_points_per_line,
            scale_factor=1.0,
        )

        self._hel_query_generator = SparseLanecppQueryGenerator(
            embed_dims,
            num_queries_holistic_ego_lanes,
            num_classes_holistic_ego_lanes,
            num_points_per_line,
            scale_factor=1.0,
        )

        # Currently, lane road edges and HEL share the same parameters, but we can separate them in the future.
        transformer_decoder_layer_params = TransformerDecoderLayerParams(
            operation_order=["self_attn", "norm", "cross_attn", "norm", "ffn", "norm"],
            num_pt_per_query=len(anchor_y_steps),
            image_shape=target_shape,
            point_cloud_range=position_range,
            attention_nonlinearity=attention_nonlinearity,
            num_heads=4,
            num_levels=1,
            num_points=8,
            ffnn_intermediate_dims_multiplier=8,
            dropout=0.1,
            points_as_query=points_as_query,
            export_use_pinhole_model=False,
        )

        self._lane_road_edge_transformer = SparseLanecppTransformer(
            num_classes=num_classes_lanes_road_edges,
            embed_dims=embed_dims,
            feature_dim=target_shape // 8,
            num_query=num_queries_lanes_road_edges,
            num_anchor_per_query=num_anchor_per_query,
            num_points_per_anchor=self._num_points_per_anchor,
            anchor_y_steps=anchor_y_steps,
            num_decoder_layers=6,
            num_output_layers=2,
            transformer_decoder_layer_params=transformer_decoder_layer_params,
            with_iterative_refinement=with_iterative_refinement,
            use_sigmoid_for_classification=True,
        )

        self._hel_transformer = SparseLanecppTransformer(
            num_classes=num_classes_holistic_ego_lanes,
            embed_dims=embed_dims,
            feature_dim=target_shape // 8,
            num_query=num_queries_holistic_ego_lanes,
            num_anchor_per_query=num_anchor_per_query,
            num_points_per_anchor=self._num_points_per_anchor,
            anchor_y_steps=anchor_y_steps,
            num_decoder_layers=6,
            num_output_layers=2,
            transformer_decoder_layer_params=transformer_decoder_layer_params,
            with_iterative_refinement=with_iterative_refinement,
            use_sigmoid_for_classification=True,
        )

        self.line_decoder = SparseLanecppLineDecoder(
            line_representation=line_representation,
            position_range=position_range,
            num_query=num_queries_holistic_ego_lanes + num_queries_lanes_road_edges,
            points_as_query=points_as_query,
            num_pt_per_line=num_points_per_line,
            anchor_y_steps=anchor_y_steps,
            anchor_y_steps_dense=anchor_y_steps_dense,
        )

    def forward(self, inputs: SparseLanecppHeadInput, model_inputs: ModelInputType) -> SparseLanecppHeadOutput:
        """Forward pass for the SparseLanecppHead model.

        Args:
            inputs: The input feature maps
            model_inputs: The model_inputs. Required for this forward function:
                - principal_point: Principal point of the camera.
                - focal_length: Focal length of the camera.
                - camera_model_type: The type of the camera model.
                - extrinsics: Camera extrinsics.

        Returns:
            SparseLanecppHeadOutput: Unified output data from the model, including:
                - all_cls_scores: Classification scores for all layers.
                - line_preds_x, line_preds_z, line_vis: Predicted lane points.
                - all_line_preds_dense: Dense predicted lane points (optional).
                - sparse_ins_scores: Sparse instance scores (optional).
                - sparse_ins_logits: Sparse instance logits (optional).
                - sparse_ins_masks: Sparse instance masks (optional).
        """
        if not isinstance(model_inputs, torch.fx.Proxy) and not all(
            camera_param in model_inputs for camera_param in CameraParameters._fields
        ):
            msg = (
                "The SparseLanecppHead requires the camera parameters in the model inputs. Provdided inputs are:"
                f"{model_inputs.keys()}"
            )
            raise ValueError(msg)

        fused_neck_output = self._input_adaptation(inputs.stride_8)
        queries_lanes_road_edges: QueryGeneratorOutput = self._lane_road_edge_query_generator(fused_neck_output)
        queries_holistic_ego_lanes: QueryGeneratorOutput = self._hel_query_generator(fused_neck_output)

        preds_lanes_road_edges = self._lane_road_edge_transformer(
            SparseLanecppTransformerInput(
                x=fused_neck_output,
                query=torch.zeros_like(queries_lanes_road_edges.query_embeds),
                query_embed=queries_lanes_road_edges.query_embeds,
                pos_embed=None,
                reference_points_x=queries_lanes_road_edges.reference_points_x,
                reference_points_z=queries_lanes_road_edges.reference_points_z,
                principal_point=model_inputs["principal_point"],
                focal_length=model_inputs["focal_length"],
                is_pinhole_model=model_inputs["camera_model_type"] == CameraModelType.PINHOLE,
                extrinsics=model_inputs["extrinsics"],
            )
        )

        preds_holistic_ego_lane = self._hel_transformer(
            SparseLanecppTransformerInput(
                x=fused_neck_output,
                query=torch.zeros_like(queries_holistic_ego_lanes.query_embeds),
                query_embed=queries_holistic_ego_lanes.query_embeds,
                pos_embed=None,
                reference_points_x=queries_holistic_ego_lanes.reference_points_x,
                reference_points_z=queries_holistic_ego_lanes.reference_points_z,
                principal_point=model_inputs["principal_point"],
                focal_length=model_inputs["focal_length"],
                is_pinhole_model=model_inputs["camera_model_type"] == CameraModelType.PINHOLE,
                extrinsics=model_inputs["extrinsics"],
            )
        )

        # Join the outputs of the two transformers.
        # This is done since the PACE decoder expects a single output holding all predicted classes for each query.
        # Predictions are joined along the query dimension and padded with 0 probability where required.
        joint_transformer_output = _join_transformer_outputs(
            preds_lanes_road_edges,
            preds_holistic_ego_lane,
            num_classes_holistic_ego_lanes=self.num_classes_holistic_ego_lanes,
            num_classes_lanes_road_edges=self.num_classes_lanes_road_edges,
        )

        all_line_preds_unscaled = torch.cat(
            [joint_transformer_output.coordinates, joint_transformer_output.visibility], dim=-1
        )

        assert queries_holistic_ego_lanes.instance_decoder_output is not None
        assert queries_lanes_road_edges.instance_decoder_output is not None
        return _select_output(
            lanecpp_head=self,
            inputs=inputs,
            all_line_preds_unscaled=all_line_preds_unscaled,
            transformer_output=joint_transformer_output,
            sparse_output_lanes_road_edges=queries_lanes_road_edges.instance_decoder_output,
            sparse_output_holistic_ego_lanes=queries_holistic_ego_lanes.instance_decoder_output,
        )


torch.fx.wrap("_join_instance_decoder_outputs")


def _join_instance_decoder_outputs(
    lane_road_edge_instances: SparseInstanceDecoderOutput,
    holistic_ego_lane_instances: SparseInstanceDecoderOutput,
    num_classes_lanes_road_edges: int,
    num_classes_holistic_ego_lanes: int,
) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
    """Join the outputs of the lane + road edges and the holistic ego lane instance decoders.

    Args:
        lane_road_edge_instances: The instance decoder output for lanes and road edges.
        holistic_ego_lane_instances: The instance decoder output for holistic ego lanes.
        num_classes_lanes_road_edges: The number of classes for lanes and road edges.
        num_classes_holistic_ego_lanes: The number of classes for holistic ego lanes.

    Returns: A tuple containing:
        - pred_masks: The concatenated instance decoder masks for lanes, road edges & holistic ego lanes
        - pred_logits: The concatenated instance decoder logits for lanes, road edges & holistic ego lanes.
        - pred_scores: The concatenated instance decoder scores for lanes, road edges & holistic ego lanes.
    """
    device = lane_road_edge_instances.pred_logits.device
    pred_masks = torch.cat([lane_road_edge_instances.pred_masks, holistic_ego_lane_instances.pred_masks], dim=1)
    lane_road_edge_pred_logits = torch.cat(
        (
            # Lane & Road edge classes
            lane_road_edge_instances.pred_logits,
            # Ignore Holistic Ego Lane
            torch.full(
                lane_road_edge_instances.pred_logits.shape[:-1] + (num_classes_holistic_ego_lanes - 1,),
                IGNORE_VALUE,
                device=device,
            ),
        ),
        dim=-1,
    )

    hel_pred_logits = torch.cat(
        (
            # Background class
            holistic_ego_lane_instances.pred_logits[..., 0:1],
            # Ignore Lanes and Road edges
            torch.full(
                holistic_ego_lane_instances.pred_logits.shape[:-1] + (num_classes_lanes_road_edges - 1,),
                IGNORE_VALUE,
                device=device,
            ),
            # Holistic Ego Lane class
            holistic_ego_lane_instances.pred_logits[..., 1:],
        ),
        dim=-1,
    )
    pred_logits = torch.cat([lane_road_edge_pred_logits, hel_pred_logits], dim=1)
    pred_scores = torch.cat([lane_road_edge_instances.pred_scores, holistic_ego_lane_instances.pred_scores], dim=1)
    return pred_masks, pred_logits, pred_scores


torch.fx.wrap("_join_transformer_outputs")


def _join_transformer_outputs(
    lanes_road_edges: SparseLanecppTransformerOutput,
    holistic_ego_lanes: SparseLanecppTransformerOutput,
    num_classes_lanes_road_edges: int,
    num_classes_holistic_ego_lanes: int,
    query_dim: int = -2,
) -> SparseLanecppTransformerOutput:
    """Joins the predictions from the separated transformers for lanes + road edges & holistic ego lanes.

    Notes:
        - This is done since the PACE decoder expects a single output holding all predicted classes for each query
        - Predictions are joined along the query dimension
        - Where required, the classes not predicted by a specific transformer are padded with 0 probability

    Args:
        lanes_road_edges: Output of the Lane & Road edge Transformer. Tensors have shape
            [..., num_queries_lane_rb, (num_classes_lanes_rb)] (with optional class dimension).
        holistic_ego_lanes: Output of the Holistic Ego Lane Transformer. Tensors have shape
            [..., num_queries_hel, (num_classes_hel)].
        num_classes_lanes_road_edges: The number of classes for lanes and road edges.
        num_classes_holistic_ego_lanes: The number of classes for holistic ego lanes.
        query_dim: The dimension along which the queries are joined. Default is -2.


    Returns: The transformer output joined along the queries. Tensors have shape
        [..., num_queries_lane_rb + num_queries_hel, (num_classes)].
    """
    device = lanes_road_edges.classes.device
    lanes_road_edges_classes = torch.cat(
        (
            # Lane & Road edge Classes
            lanes_road_edges.classes,
            # Ignore Holistic Ego Lane
            torch.full(
                lanes_road_edges.classes.shape[:-1] + (num_classes_holistic_ego_lanes - 1,),
                IGNORE_VALUE,
                device=device,
            ),
        ),
        dim=-1,
    )

    holistic_ego_lane_classes = torch.cat(
        (
            # Background class
            holistic_ego_lanes.classes[..., 0:1],
            # Ignore Lane & Road edge Classes
            torch.full(
                holistic_ego_lanes.classes.shape[:-1] + (num_classes_lanes_road_edges - 1,), IGNORE_VALUE, device=device
            ),
            # Holistic Ego Lane class
            holistic_ego_lanes.classes[..., 1:],
        ),
        dim=-1,
    )

    # Concatenate the predictions along the query-dimension.
    joint_classes = torch.cat([lanes_road_edges_classes, holistic_ego_lane_classes], dim=query_dim)
    joint_line_preds = torch.cat([lanes_road_edges.coordinates, holistic_ego_lanes.coordinates], dim=query_dim)
    joint_visibility = torch.cat([lanes_road_edges.visibility, holistic_ego_lanes.visibility], dim=query_dim)
    return SparseLanecppTransformerOutput(joint_classes, joint_line_preds, joint_visibility)


torch.fx.wrap("_select_output")


def _select_output(
    lanecpp_head: SparseLanecppHead,
    inputs: SparseLanecppHeadInput,
    all_line_preds_unscaled: Tensor,
    transformer_output: SparseLanecppTransformerOutput,
    sparse_output_lanes_road_edges: SparseInstanceDecoderOutput,
    sparse_output_holistic_ego_lanes: SparseInstanceDecoderOutput,
) -> SparseLanecppHeadOutput:
    """Handles the output for traced models.

    The basic idea is that outputs are passed in and only the desired fields are forwarded.
    This function is treated as leaf node in tracing so its control flow logic will work.

    Caution, line decoding is performed here conditionally. That is okay because line decoding
    is postprocessing. No quantizers have to be added.
    """
    if lanecpp_head.inferencing:
        return SparseLanecppHeadOutput(
            raw_output=SparseLanecppHeadRawOutput(
                class_scores=transformer_output.classes, lane_preds_unscaled=all_line_preds_unscaled
            ),
        )
    # Caution: The default validation step also needs all outputs because it evaluates the loss.
    batch_size = inputs.stride_8.shape[0]
    # TODO : Move the line_decoder to the post-processing step
    # https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt

    line_preds_x, line_preds_z, line_vis, all_line_preds_dense = lanecpp_head.line_decoder(
        transformer_output.coordinates, transformer_output.visibility, batch_size
    )

    pred_masks, pred_logits, pred_scores = _join_instance_decoder_outputs(
        lane_road_edge_instances=sparse_output_lanes_road_edges,
        holistic_ego_lane_instances=sparse_output_holistic_ego_lanes,
        num_classes_lanes_road_edges=lanecpp_head.num_classes_lanes_road_edges,
        num_classes_holistic_ego_lanes=lanecpp_head.num_classes_holistic_ego_lanes,
    )
    return SparseLanecppHeadOutput(
        raw_output=SparseLanecppHeadRawOutput(
            class_scores=transformer_output.classes, lane_preds_unscaled=all_line_preds_unscaled
        ),
        scaled_output=SparseLanecppHeadScaledOutput(
            lanes_x=line_preds_x, lanes_z=line_preds_z, lanes_visibility=line_vis
        ),
        instance_output=SparseLanecppInstanceHeadOutput(
            masks=pred_masks,
            logits=pred_logits,
            scores=pred_scores,
        ),
        lanes_dense=all_line_preds_dense,
    )
