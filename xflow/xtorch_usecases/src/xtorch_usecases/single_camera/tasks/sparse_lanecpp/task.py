"""Sparse Lane Cpp Task implementation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from functools import partial
from pathlib import Path
from typing import Any

import numpy as np
from onnx_graphsurgeon.ir.graph import Graph
from pytorch_lightning.callbacks import Callback

from conversion.qnn.onnx_surgery.onnx_graph_surgery_rules import AbstractGraphRewriter
from conversion.qnn.quantization_overrides import QuantizationEncodings
from data_formats.lane_3d.mpc4_loader import (
    MPC4Loader,
    SparseLanecppGroundTruthNumPy,
)
from pyper.dict_pipeline import TaskProcessingConfig
from xcontract.data.definitions.image import HW
from xcontract.data.definitions.inputs import InputImageView
from xcontract.data.definitions.usage import Usage
from xtorch.losses.meta.uncertainty_weighted_loss import SublossConfig, UncertaintyLossType
from xtorch.multi_task.structured_task import StructuredTask, SupportedData
from xtorch.training.metric_handling import MetricRegistry
from xtorch.training.training_module import StepOutputKey
from xtorch_extensions.visualization.callbacks.lane3d import Lane3DVisualizationCallback
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.config.common_task_config import SingleCameraCommonTaskConfig
from xtorch_usecases.single_camera.network import ModelInputType
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.callbacks import (
    adjust_to_target_shape,
    extract_visu_lane3d_from_gt,
    extract_visu_lane3d_from_inference_preds,
    extract_visu_lane3d_from_preds,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    CLASS_ID_TO_COLOR,
    SparseLanecppConfig,
    SparseLanecppGroundTruth,
    SparseLanecppHeadInput,
    SparseLanecppHeadOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.evaluation.metrics import (
    Lane3DEval,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.head import SparseLanecppHead
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.lane_postprocessing import (
    handle_gts,
    handle_inference_outputs,
    handle_predictions,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.loss import SparseLanecppLoss
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_decoder import SparseLanecppLineDecoder
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.saver import SparseLanecppPredictionSaver


class SparseLanecppTask(
    StructuredTask[
        SparseLanecppConfig,
        SingleCameraCommonTaskConfig,
        SparseLanecppHeadInput,
        ModelInputType,
        SparseLanecppHeadOutput,
        SparseLanecppGroundTruth,
    ]
):
    """Sparse Lane Cpp Task implementation."""

    def __init__(
        self,
        config: SparseLanecppConfig,
        common_config: SingleCameraCommonTaskConfig,
    ) -> None:
        """Initialize the Sparse Lane Cpp Task.

        Args:
            config: The Sparse Lane Cpp Task configuration.
            common_config: Common config shared across tasks.

        """
        self._config = config
        self._callback_config = common_config.callback_config
        self._reference_view_config = common_config.reference_view_config
        self._camera_parameter_task_id = common_config.camera_parameter_task_id
        self._line_decoder = SparseLanecppLineDecoder(
            line_representation=self._config.loader_config.line_representation,
            position_range=self._config.position_range,
            num_query=self._config.num_queries,
            points_as_query=self._config.points_as_query,
            num_pt_per_line=self._config.loader_config.num_points_per_line,
            anchor_y_steps=self._config.loader_config.anchor_y_steps,
            anchor_y_steps_dense=self._config.loader_config.anchor_y_steps_dense,
        )

    @property
    def head(self) -> SparseLanecppHead:
        """Returns the Sparse Lane CPP head."""
        return SparseLanecppHead(
            target_shape=self._config.camera_data_config.target_shape,
            num_queries_lanes_road_edges=self._config.num_queries_lanes_road_edges,
            num_classes_lanes_road_edges=self._config.loader_config.num_classes_lanes_road_edges,
            num_queries_holistic_ego_lanes=self._config.num_queries_hel,
            num_classes_holistic_ego_lanes=self._config.loader_config.num_classes_holistic_ego_lanes,
            num_points_per_line=self._config.loader_config.num_points_per_line,
            anchor_y_steps=self._config.loader_config.anchor_y_steps,
            anchor_y_steps_dense=self._config.loader_config.anchor_y_steps_dense,
            position_range=self._config.position_range,
            attention_nonlinearity=self._config.attention_nonlinearity,
            line_representation=self._config.loader_config.line_representation,
            points_as_query=self._config.points_as_query,
            with_iterative_refinement=self._config.with_iterative_refinement,
            embed_dims=self._config.embedding_dimension,
        )

    @property
    def loss(self) -> SparseLanecppLoss:
        """Returns the Sparse Lane CPP loss."""
        return SparseLanecppLoss(
            num_queries=self._config.num_queries,
            num_y_steps=len(self._config.loader_config.anchor_y_steps),
            num_y_steps_dense=len(self._config.loader_config.anchor_y_steps_dense),
            num_categories=self._config.loader_config.num_classes,
        )

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Returns the callbacks for the lane 3d task."""
        if any(pipeline_step is step for step in [PipelineStep.EVALUATE_TORCH, PipelineStep.VISUALIZATION_TORCH]):
            pred_lane3d_extraction_fn = partial(
                extract_visu_lane3d_from_preds,
                get_preds=lambda outputs, task_identifier: outputs["torch"][task_identifier],
            )
        elif pipeline_step is PipelineStep.EVALUATE_QNN:
            pred_lane3d_extraction_fn = partial(
                extract_visu_lane3d_from_inference_preds,
                line_decoder=self._line_decoder,
                backend_name="qnn",
            )
        else:
            pred_lane3d_extraction_fn = partial(
                extract_visu_lane3d_from_preds,
                get_preds=lambda outputs, task_identifier: outputs[StepOutputKey.PREDICTIONS][task_identifier],
            )

        adjust_image_function = partial(adjust_to_target_shape, camera_data_config=self._config.camera_data_config)

        callbacks: list[Callback] = []
        assert self._camera_parameter_task_id is not None
        callbacks.append(
            Lane3DVisualizationCallback(
                image_shape=self._config.camera_data_config.target_shape,
                foreground_threshold=self._config.visu_threshold,
                adjust_image_fn=adjust_image_function,
                gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
                pred_lane3d_extraction_fn=pred_lane3d_extraction_fn,
                loader_image_type=self._reference_view_config.loaded_image_type.value,
                loader_image_view=InputImageView.SINGLE_VIEW,
                output_folder=self._callback_config.image_root_dir / Path(self.identifier()),
                task_identifier=self.identifier(),
                camera_parameters_id=self._camera_parameter_task_id,
                image_logging_frequency=self._callback_config.image_logging_frequency
                if pipeline_step is PipelineStep.TRAIN
                else 1,
                max_num_images_per_epoch=self._config.num_validation_images,
                background_label=0,
                class_id_to_color=CLASS_ID_TO_COLOR,
            )
        )

        if pipeline_step.is_evaluate or pipeline_step.is_visualization:
            input_key = InputImageView.SINGLE_VIEW
            evaluation_folder = self._callback_config.output_root_dir / self._callback_config.evaluation_subfolder
            callbacks += [
                SparseLanecppPredictionSaver(
                    task_id=self.identifier(),
                    evaluation_folder=evaluation_folder.as_posix(),
                    input_key=input_key,
                    delete_predictions_afterwards=pipeline_step is not PipelineStep.VISUALIZATION_TORCH,
                    line_decoder=self._line_decoder,
                    camera_parameter_task_id=self._camera_parameter_task_id,
                )
            ]

        return callbacks

    def pyper_processing_config(self, usage: Usage) -> TaskProcessingConfig[SparseLanecppGroundTruthNumPy]:
        """Return the Pyper processing configuration for the task."""

        sparse_lanecpp_data_loader = MPC4Loader(
            num_category=self._config.loader_config.num_classes,
            top_view_region=self._config.loader_config.top_view_region,
            max_lanes=self._config.loader_config.max_lanes,
            image_shape=self._config.camera_data_config.original_shape,
            target_shape=self._config.camera_data_config.target_shape,
            cam_height=self._config.loader_config.cam_height,
            intrinsics=self._config.loader_config.intrinsics,
            anchor_y_steps=self._config.loader_config.anchor_y_steps,
            anchor_y_steps_dense=self._config.loader_config.anchor_y_steps_dense,
            lane_width=self._config.loader_config.lane_width,
            latr_constant=self._config.loader_config.latr_constant,
            gt_conf_threshold=self._config.loader_config.gt_conf_threshold,
            use_occlusions=self._config.loader_config.use_occlusions,
            use_fixed_camera_params=self._config.loader_config.use_fixed_camera_params,
            train_hel=self._config.loader_config.train_hel,
        )

        # Regarding SCA: Pypers type annotations demand tuples of ndarrays but
        #                SparseLanecppGroundTruthNumPy works just fine. It's a named tuple.
        return TaskProcessingConfig(
            task_id=self.identifier(),
            inflator=sparse_lanecpp_data_loader.load_sparse_lanecpp_label,  # type: ignore[reportArgumentType]
            ignorer=lambda: sparse_lanecpp_data_loader.dummy_labels,
            formatter=lambda labels: SparseLanecppGroundTruthNumPy(*labels),
            adjuster=sparse_lanecpp_data_loader.convert_to_ground_truth_and_crop,  # type: ignore[reportArgumentType]
        )

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """Returns the metrics for the lane 3d task."""
        metrics: list[MetricRegistry[Any, Any]] = []

        sparse_lanecpp_eval = Lane3DEval(
            task_id=self.identifier(),
            metric_save_path=self._callback_config.output_root_dir
            / self._callback_config.evaluation_subfolder
            / Path(self.identifier()),
            pos_threshold=self._config.eval_foreground_threshold,
            top_view_region=self._config.loader_config.top_view_region,
        )

        metric = MetricRegistry(
            metric_identifier="sparse_lanecpp",
            metric=sparse_lanecpp_eval,
            predictions_access_function=partial(handle_predictions, task_id=self.identifier()),
            targets_access_function=partial(handle_gts, task_id=self.identifier()),
        )
        metrics.append(metric)

        metric = MetricRegistry(
            metric_identifier="sparse_lanecpp_qnn",
            metric=sparse_lanecpp_eval,
            stages=[],
            inference_backends=["qnn"],
            predictions_access_function=partial(handle_inference_outputs, line_decoder=self._line_decoder),
            targets_access_function=partial(handle_gts, task_id=self.identifier()),
        )
        metrics.append(metric)
        return metrics

    def subloss_configs(self) -> dict[str, SublossConfig]:
        """Returns the subloss types for the task."""
        subloss_weights = self._config.post_uncertainty_subloss_weights
        return {
            "xs_loss": SublossConfig(UncertaintyLossType.REGRESSION, post_uw_weight=subloss_weights.xs_loss),
            "zs_loss": SublossConfig(UncertaintyLossType.REGRESSION, post_uw_weight=subloss_weights.zs_loss),
            "vis_loss": SublossConfig(UncertaintyLossType.CLASSIFICATION, post_uw_weight=subloss_weights.vis_loss),
            "cls_loss": SublossConfig(UncertaintyLossType.CLASSIFICATION, post_uw_weight=subloss_weights.cls_loss),
            "objectness_loss": SublossConfig(
                UncertaintyLossType.CLASSIFICATION, post_uw_weight=subloss_weights.objectness_loss
            ),
            "dice_loss": SublossConfig(UncertaintyLossType.CLASSIFICATION, post_uw_weight=subloss_weights.dice_loss),
            "mask_loss": SublossConfig(UncertaintyLossType.REGRESSION, post_uw_weight=subloss_weights.mask_loss),
            "sparse_inst_cls_loss": SublossConfig(
                UncertaintyLossType.CLASSIFICATION, post_uw_weight=subloss_weights.sparse_inst_cls_loss
            ),
        }

    @property
    def conversion_rewriters(self) -> list[AbstractGraphRewriter]:
        """Conversion rewriters to inject DepthThresholding qnn custom ops."""
        return [
            _InjectLaneCppCropToYuvInput(
                orig_img_shape=self._config.camera_data_config.original_shape,
                target_img_shape=self._config.camera_data_config.target_shape,
                crop_bottom=self._config.camera_data_config.crop_bottom,
            )
        ]


class _InjectLaneCppCropToYuvInput(AbstractGraphRewriter):
    """Inject cropping from fc1 image to training crop size."""

    def __init__(self, orig_img_shape: HW, target_img_shape: HW, crop_bottom: int = 0) -> None:
        """Initialize the rewriter.

        Args:
            orig_img_shape: Original image shape before cropping.
            target_img_shape: Target image shape after cropping.
            crop_bottom: The amount of pixels to crop from the bottom of the image.

        Note: The cropping height is implicitly calculated as original_height - target_height.
        """
        super().__init__()

        self._num_channels = 32

        self._orig_img_height = orig_img_shape.height  # 1280
        self._orig_img_width = orig_img_shape.width  # 2304
        self._yuv_total_height = self._orig_img_height + (self._orig_img_height // 2)
        self._yuv_total_width = (self._orig_img_width * 2) // self._num_channels
        self._crop_top_y = self._orig_img_height - target_img_shape.height - crop_bottom
        self._crop_height_y = target_img_shape.height
        self._crop_top_uv = self._crop_top_y // 2  # UV is half the height of Y
        self._crop_height_uv = self._crop_height_y // 2
        self._crop_width = (target_img_shape.width * 2) // self._num_channels  # 2 bytes per pixel
        self._start_width = (self._yuv_total_width - self._crop_width) // 2  # same for y and uv

    def apply_rule_on_end(self, graph: Graph, quant_overrides: QuantizationEncodings) -> Graph:
        """Overridden."""

        input_y_node, input_uv_node = None, None
        for node in graph.nodes:
            if node.name.startswith("SliceY_0"):
                input_y_node = node
            if node.name.startswith("SliceUV_0"):
                input_uv_node = node

        if input_y_node is None or input_uv_node is None:
            message = "Could not find input_y or input_uv node in the graph."
            raise RuntimeError(message)

        if input_y_node.outputs[0].shape != [
            1,
            self._num_channels,
            self._crop_height_y,
            self._crop_width,
        ] or input_uv_node.outputs[0].shape != [1, self._num_channels, self._crop_height_uv, self._crop_width]:
            message = "Unexpected shape for input_y or input_uv node."
            raise RuntimeError(message)

        max64_int = np.iinfo(np.int64).max
        uncropped_input_shape = [1, self._num_channels, self._yuv_total_height, self._yuv_total_width]

        starts_y_np = np.array([0, 0, self._crop_top_y, self._start_width], dtype=np.int64)
        ends_y_np = np.array(
            [max64_int, max64_int, self._crop_top_y + self._crop_height_y, self._start_width + self._crop_width],
            dtype=np.int64,
        )
        starts_uv_np = np.array([0, 0, self._orig_img_height + self._crop_top_uv, self._start_width], dtype=np.int64)
        ends_uv_np = np.array(
            [
                max64_int,
                max64_int,
                self._orig_img_height + self._crop_top_uv + self._crop_height_uv,
                self._start_width + self._crop_width,
            ],
            dtype=np.int64,
        )

        input_y_node.inputs[0].shape = uncropped_input_shape

        input_y_node.inputs[1].values = starts_y_np
        input_y_node.inputs[2].values = ends_y_np

        input_uv_node.inputs[1].values = starts_uv_np
        input_uv_node.inputs[2].values = ends_uv_np

        return graph
