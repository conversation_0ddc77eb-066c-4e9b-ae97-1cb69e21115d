"""Query generator for SparseLanecpp.

Based on: https://github.com/JMoonr/LATR/blob/1c9e62646274037faa4a2ebed3ce61b96baa68f9/models/latr_head.py
          https://github.com/JMoonr/LATR/blob/1c9e62646274037faa4a2ebed3ce61b96baa68f9/models/sparse_ins.py

Paper: https://arxiv.org/pdf/2308.04583
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

# MIT License.

# Copyright (c) 2023 JMoonr

# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:

# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.
#
# Modified by Robert Bosch GmbH and Cariad SE

import math
from dataclasses import dataclass

import torch
import torch.nn.functional as F
from torch import Tensor, nn

# Exclude functions from traceing
# Only free functions are supported
torch.fx.wrap("_expand_tensor_as")
torch.fx.wrap("_reduce_groups_for_training")


def _get_stack_3x3_convs(num_convs: int, in_channels: int, out_channels: int) -> nn.Sequential:
    """Create a stack of 3x3 convolutional layers.

    Args:
        num_convs: Number of 3x3 convolutional layers.
        in_channels: Number of input channels.
        out_channels: Number of output channels.

    Returns:
        Stack with num_convs 3x3 convolutional layers.
    """
    convs = []
    for _ in range(num_convs):
        convs.append(nn.Conv2d(in_channels, out_channels, 3, padding=1))
        convs.append(nn.ReLU(inplace=True))
        in_channels = out_channels
    return nn.Sequential(*convs)


def _expand_tensor_as(x: torch.Tensor, target: torch.Tensor, keep_x_mask: tuple[bool, ...]) -> torch.Tensor:
    """Expand selected dimensions of x to the shape of target.

    Args:
        x: Input to expand. Must already have the same rank as target.
        target: The reference tensor.
        keep_x_mask: Marks dimensions where x's size is kept. Length equal to rank of x.

    Returns:
        Resized x.
    """
    # This function should generate relatively nice onnx graphs.
    shape = torch.as_tensor(target.shape, dtype=torch.long)
    shape = torch.masked_fill(shape, torch.as_tensor(keep_x_mask), -1)
    return x.expand(shape.tolist())


class _MaskBranch(nn.Module):
    """Processes features through conv layers and projects them to a lower-dimensional space."""

    def __init__(self, embedding_dimension: int, kernel_dim: int, num_convs: int = 4) -> None:
        """Initialize the MaskBranch.

        Args:
            embedding_dimension: Embedding dimension.
            kernel_dim: Dimension of the mask kernel.
            num_convs: Number of 3x3 convolutional layers.
        """
        super().__init__()

        input_dimension = embedding_dimension + 2  # 2 for (x, y) image positional encoding

        self._mask_convs = _get_stack_3x3_convs(num_convs, input_dimension, embedding_dimension)
        self._projection = nn.Conv2d(embedding_dimension, kernel_dim, kernel_size=1)
        self._init_weights()

    def _init_weights(self) -> None:
        """Initialize weights and biases for convolution layers."""
        for m in self._mask_convs.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode="fan_out", nonlinearity="relu")
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        nn.init.kaiming_normal_(self._projection.weight, mode="fan_out", nonlinearity="relu")
        if self._projection.bias is not None:
            nn.init.constant_(self._projection.bias, 0)

    def forward(self, features: torch.Tensor) -> torch.Tensor:
        """Forward pass of the MaskBranch.

        Args:
            features: Input feature tensor.

        Returns:
            torch.Tensor: Projected feature tensor.
        """
        features = self._mask_convs(features)
        return self._projection(features)


class _InstanceBranch(nn.Module):
    """Processes features through conv layers and predicts instance activation maps."""

    def __init__(
        self,
        embedding_dimension: int,
        num_queries: int,
        num_classes: int,
        kernel_dim: int,
        num_convs: int = 4,
        sparse_num_group: int = 1,
        num_group: int = 1,
        prior_prob: float = 0.01,
    ) -> None:
        """Initialize the InstanceBranch.

        Args:
            embedding_dimension: Embedding dimension.
            num_queries: Number of instances to predict.
            num_classes: Number of lane classes to predict.
            kernel_dim: Dimension of the mask kernel.
            num_convs: Number of 3x3 convolutional layers.
            sparse_num_group: Number of sparse groups for iam convolution.
            num_group: Number of groups for iam convolution.
            prior_prob: Prior probability for the classification layer.
        """
        super().__init__()

        self._sparse_num_group = sparse_num_group
        self._num_group = num_group
        self._num_masks = num_queries

        input_dimension = embedding_dimension + 2  # 2 for (x, y) image positional encoding
        self._inst_convs = _get_stack_3x3_convs(
            num_convs=num_convs, in_channels=input_dimension, out_channels=embedding_dimension
        )

        self._iam_conv = nn.Conv2d(
            embedding_dimension * self._num_group,
            self._num_group * self._num_masks * self._sparse_num_group,
            3,
            padding=1,
            groups=self._num_group * self._sparse_num_group,
        )
        self._fc = nn.Linear(embedding_dimension * self._sparse_num_group, embedding_dimension)
        # output
        self._mask_kernel = nn.Linear(embedding_dimension, kernel_dim)
        self._cls_score = nn.Linear(embedding_dimension, num_classes)
        self._objectness = nn.Linear(embedding_dimension, 1)
        self._prior_prob = prior_prob
        self._init_weights()

    def _init_weights(self) -> None:
        """Initialize weights and biases for sub-layers."""
        for m in self._inst_convs.modules():
            if isinstance(m, nn.Conv2d):
                nn.init.kaiming_normal_(m.weight, mode="fan_out", nonlinearity="relu")
                if m.bias is not None:
                    nn.init.constant_(m.bias, 0)
        bias_value = -math.log((1 - self._prior_prob) / self._prior_prob)
        for module in [self._iam_conv, self._cls_score]:
            nn.init.constant_(module.bias, bias_value)
        nn.init.normal_(self._iam_conv.weight, std=0.01)
        nn.init.normal_(self._cls_score.weight, std=0.01)

        nn.init.normal_(self._mask_kernel.weight, std=0.01)
        nn.init.constant_(self._mask_kernel.bias, 0.0)
        nn.init.kaiming_uniform_(self._fc.weight, a=1)
        if self._fc.bias is not None:
            nn.init.constant_(self._fc.bias, 0)

    def forward(self, segmentation_features: torch.Tensor) -> dict[str, torch.Tensor]:
        """Forward pass of the InstanceBranch.

        Args:
            segmentation_features: Input segmentation feature tensor. Shape (B,embedding_dimension,H,W)

        Returns:
            dict: Dictionary containing instance activation maps and features.
        """
        segmentation_features = self._inst_convs(segmentation_features)

        # The repetition is not quantized correctly when using AIMET QAT. Scaling is wrong.
        # Fortunately we can leave it away in the default case where num_groups=1.
        tiled_segmentation_features = (
            segmentation_features.repeat((1, self._num_group, 1, 1)) if self._num_group != 1 else segmentation_features
        )

        # predict instance activation maps (IAMs)
        # shape: (B, num_group * num_mask * sparse_num_group, H, W)
        iam = self._iam_conv(tiled_segmentation_features)
        iam, num_group = _reduce_groups_for_training(self, iam)

        batch_size = iam.size(0)

        iam_prob = iam.sigmoid()
        num_instances = iam_prob.size(1)
        num_channels = segmentation_features.size(1)
        # BxNxHxW -> BxNx(HW)
        iam_prob = iam_prob.view(batch_size, num_instances, -1)

        # aggregate features: BxCxHxW -> Bx(HW)xC
        # (B x N x HW) @ (B x HW x C) -> B x N x C
        all_inst_features = torch.matmul(
            iam_prob, segmentation_features.permute(0, 2, 3, 1).view(batch_size, -1, num_channels)
        )  # BxNxC

        # concat sparse group features
        # B x N x C -> B x G x S x M x C
        inst_features = all_inst_features.reshape(batch_size, num_group, self._sparse_num_group, self._num_masks, -1)
        if self._sparse_num_group > 1 and self._num_masks > 1:
            inst_features = inst_features.permute(0, 1, 3, 2, 4)  # -> B x G x M x S x C
        inst_features = inst_features.reshape(batch_size, num_group, self._num_masks, -1)  # -> B x G x M x F

        inst_features = F.relu(self._fc(inst_features))

        # avg over sparse group
        iam_prob = iam_prob.view(batch_size, num_group, self._sparse_num_group, self._num_masks, iam_prob.shape[-1])
        iam_prob = iam_prob.mean(dim=2).flatten(1, 2)
        inst_features = inst_features.flatten(1, 2)

        # Not needed for eval but included anyway for less tracing issues when using AIMET.
        pred_logits = self._cls_score(inst_features)
        pred_kernel = self._mask_kernel(inst_features)
        pred_scores = self._objectness(inst_features)

        return {
            "pred_logits": pred_logits,
            "pred_kernel": pred_kernel,
            "pred_scores": pred_scores,
            "iam_prob": iam_prob,
            "queries": inst_features,
        }


def _reduce_groups_for_training(self: _InstanceBranch, iam: torch.Tensor) -> tuple[torch.Tensor, int]:
    """Reduces the number of groups for evaluation.

    Args:
        self: The module instance.
        iam: Instance activation maps of shape (B, num_group * num_masks * sparse_num_group, H, W).

    Returns:
        Reduced instance activation maps and number of groups.
    """
    # The function is free standing enable wrapping with torch.fx.wrap.
    # By applying wrap() (see above), it is excluded from tracing which enables dynamic switching between training
    # and eval mode even in traced model.
    if not self.training:
        batch_size, height, width = iam.size(0), iam.size(2), iam.size(3)
        iam = iam.view(batch_size, self._num_group, self._num_masks * self._sparse_num_group, height, width)
        # Grab the first group. Result shape will be consistent with training mode.
        # I.e. Shape will be (B, 1 * num_mask * sparse_num_group, H , W)
        iam = iam[:, 0, ...]
        num_group = 1
    else:
        num_group = self._num_group
    return iam, num_group


@dataclass(kw_only=True)
class SparseInstanceDecoderOutput:
    """Data-class holding the output of the SparseInstanceDecoder."""

    pred_logits: torch.Tensor  # instance class logits of shape (B, N, num_classes)
    pred_kernel: torch.Tensor  # instance masks kernel of shape (B, N, kernel_dim)
    pred_scores: torch.Tensor  # instance scores of shape (B, N, 1)
    pred_masks: torch.Tensor  # computed instance masks of shape (B, N, H, W)
    queries: torch.Tensor  # instance queries of shape (B, N, C)
    iam_prob: torch.Tensor  # instance activation maps of shape (B, N, H, W)


class SparseInstanceDecoder(nn.Module):
    """Processes features through instance and mask branches."""

    def __init__(self, embedding_dimension: int, num_queries: int, num_classes: int, scale_factor: float) -> None:
        """Initialize the SparseInstanceDecoder.

        Args:
            embedding_dimension: Embedding dimension.
            num_queries: Number of queries.
            num_classes: Number of lane classes.
            scale_factor: Up/downscale factor for the instance masks.
        """
        super().__init__()
        self._scale_factor = scale_factor
        self._inst_branch = _InstanceBranch(
            embedding_dimension=embedding_dimension,
            num_queries=num_queries,
            num_classes=num_classes,
            kernel_dim=embedding_dimension,
        )
        self._mask_branch = _MaskBranch(embedding_dimension=embedding_dimension, kernel_dim=embedding_dimension)
        # Lazy initialized buffer. In ONNX exports the buffer will be included as constant and thus will only work
        # for the least recent input dimensions!
        self.register_buffer("_lazy_coordinates_buffer", tensor=None, persistent=False)

    @staticmethod
    @torch.no_grad()
    def _compute_coordinates(features: torch.Tensor) -> torch.Tensor:
        """Compute normalized coordinates for the input tensor.

        Args:
            features: Image features (shape: [B, C, H, W]).

        Returns:
            Coordinate grid (shape: 2, H, W)
        """
        _, _, h, w = features.shape
        y_loc = -1.0 + 2.0 * torch.arange(h, device=features.device) / (h - 1)
        x_loc = -1.0 + 2.0 * torch.arange(w, device=features.device) / (w - 1)
        y_loc, x_loc = torch.meshgrid(y_loc, x_loc)
        locations = torch.stack([x_loc, y_loc])
        return locations.to(features)

    def _get_coordinates_cached(self, features: torch.Tensor) -> torch.Tensor:
        """Returns coordinates for the input tensor.

        See also _compute_coordinates.
        This function caches the coordinates in a buffer to be used when the model is exported. The buffer is recomputed
        when H or W changes. The batch size is dynamically adopted from the input.

        Args:
            features: Image features (shape: [B, C, H, W]).

        Returns:
            Normalized coordinates (shape: [B, 2, H, W]).
        """
        if not isinstance(features, torch.fx.Proxy) and (
            self._lazy_coordinates_buffer is None or self._lazy_coordinates_buffer.shape[2:] != features.shape[2:]
        ):
            self._lazy_coordinates_buffer = SparseInstanceDecoder._compute_coordinates(features)  # type: ignore[replortUninitializedInstanceVariable]
        if self._lazy_coordinates_buffer is None:
            msg = "Coordinates should have been initialized already. Perform a warmup pass before fx tracing."
            raise RuntimeError(msg)
        # Adapt to the batch dimension.
        return _expand_tensor_as(self._lazy_coordinates_buffer.data[None, ...], features, (False, True, True, True))

    def forward(self, features: torch.Tensor) -> SparseInstanceDecoderOutput:
        """Forward pass of the SparseInstanceDecoder.

        Args:
            features: Input feature tensor.

        Returns:
            Outputs of the instance decoder and sub-branches.
        """
        coord_features = self._get_coordinates_cached(features)
        features = torch.cat([coord_features, features], dim=1)
        instance_branch_output = self._inst_branch(features)

        # Masks are only needed for training. Included anyway for less tracing issues when using AIMET.
        pred_masks = self._compute_masks(features, instance_branch_output["pred_kernel"])

        return SparseInstanceDecoderOutput(**instance_branch_output, pred_masks=pred_masks)

    def _compute_masks(self, features: Tensor, pred_kernel: Tensor) -> Tensor:
        """Compute instance masks from features and mask kernel.

        Args:
            features: Input feature tensor.
            pred_kernel: Mask kernel from the instance branch.

        Returns:
            Computed instance masks.
        """
        mask_features = self._mask_branch(features)
        num_instances = pred_kernel.shape[1]
        batch_size, num_channels, height, width = mask_features.shape

        pred_masks = torch.matmul(pred_kernel, mask_features.view(batch_size, num_channels, height * width)).view(
            batch_size, num_instances, height, width
        )
        pred_masks = F.interpolate(pred_masks, scale_factor=self._scale_factor, mode="bilinear", align_corners=False)
        return pred_masks


@dataclass(kw_only=True)
class QueryGeneratorOutput:
    """Data-class holding the output of the SparseInstanceDecoder."""

    instance_decoder_output: SparseInstanceDecoderOutput | None
    query_embeds: Tensor  # query embeddings of shape (B, N x num_points_per_line, C)
    reference_points_x: Tensor  # reference points of shape (B, N x num_points_per_line)
    reference_points_z: Tensor  # reference points of shape (B, N x num_points_per_line)


class SparseLanecppQueryGenerator(nn.Module):
    """SparseLanecppQueryGenerator initializes queries for lane detection.

    This class generates queries and embeddings for lane detection tasks,
    optionally using a sparse instance decoder.
    """

    def __init__(
        self,
        embedding_dimension: int,
        num_queries: int,
        num_classes: int,
        num_points_per_line: int,
        scale_factor: float = 1.0,
        *,
        use_instance_decoder: bool = True,
        point_as_query: bool = True,
    ) -> None:
        """Initializes the SparseLanecppQueryGenerator.

        Args:
            embedding_dimension: Embedding dimension.
            num_queries: Number of queries.
            num_classes: Number of lane classes.
            num_points_per_line: Number of points per lane.
            scale_factor: Up/downscale factor for the instance masks.
            use_instance_decoder: If True use the sparse instance decoder, otherwise query embeddings.
            point_as_query: If True generate queries for each point, otherwise for each instance.
        """
        super().__init__()
        self._num_points_per_anchor = 1 if point_as_query else num_points_per_line

        if use_instance_decoder:
            self._sparse_instance_decoder = SparseInstanceDecoder(
                embedding_dimension, num_queries, num_classes, scale_factor
            )
            self._lane_embedding = None
        else:
            self._sparse_instance_decoder = None
            self._lane_embedding = nn.Embedding(num_queries, embedding_dimension)

        self._query_embedding = nn.Sequential(
            nn.Linear(embedding_dimension, embedding_dimension),
            nn.ReLU(),
            nn.Linear(embedding_dimension, embedding_dimension),
        )

        # AIMET generates an error caused by direct access to the weight tensor
        # later in the code. Therefore, Embedding is only used for initialization
        # and the weight is put into a regular parameter.
        self._point_embedding = nn.Parameter(data=nn.Embedding(num_points_per_line, embedding_dimension).weight)

        self._reference_points = nn.Sequential(
            nn.Linear(embedding_dimension, embedding_dimension),
            nn.ReLU(inplace=True),
            nn.Linear(embedding_dimension, embedding_dimension),
            nn.ReLU(inplace=True),
        )

        # separate projection for x and z coordinates for better quantization
        self._ref_proj_x = nn.Linear(embedding_dimension, self._num_points_per_anchor)
        self._ref_proj_z = nn.Linear(embedding_dimension, self._num_points_per_anchor)

        self._init_weights()

    def _init_weights(self) -> None:
        """Initialize weights and biases."""
        for m in self._reference_points.modules():
            if isinstance(m, nn.Linear):
                nn.init.xavier_uniform_(m.weight, gain=1.0)
                if hasattr(m, "bias") and m.bias is not None:
                    nn.init.constant_(m.bias, 0.0)

    def forward(self, img_feats: Tensor) -> QueryGeneratorOutput:
        """Forward pass to initialize queries.

        Args:
            img_feats: List or tuple of image feature tensors.

        Returns:
            Query embeddings, reference points, and instance decoder output (if applicable).
        """
        sparse_output = None
        if self._sparse_instance_decoder:
            sparse_output = self._sparse_instance_decoder(img_feats)
            query = sparse_output.queries
        else:
            if self._lane_embedding is None:
                msg = "Lane embedding is not initialized."
                raise ValueError(msg)
            query = self._lane_embedding.weight[None, ...].repeat(img_feats.shape[0], 1, 1)  # BxNxC

        query = query.unsqueeze(2)
        query = query + self._point_embedding[None, None, ...]

        query_embeds = self._query_embedding(query).flatten(1, 2)
        reference_points = self._reference_points(query_embeds)
        reference_points_x = self._ref_proj_x(reference_points).sigmoid()
        reference_points_z = self._ref_proj_z(reference_points).sigmoid()

        return QueryGeneratorOutput(
            query_embeds=query_embeds,
            reference_points_x=reference_points_x,
            reference_points_z=reference_points_z,
            instance_decoder_output=sparse_output,
        )
