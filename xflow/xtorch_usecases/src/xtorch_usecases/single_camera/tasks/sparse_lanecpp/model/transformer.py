"""This module implements the SparseLanecpp Transformer architecture for lane detection."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from dataclasses import dataclass

import numpy as np
import torch
from numpy.typing import NDArray
from torch import nn

from xcontract.data.definitions.image import HW
from xtorch.nn.encoders.positional import MaskedSinePositionalEncoding2D
from xtorch.nn.functional import expand
from xtorch.nn.heads.lane3d.ms_deformable_attention_3d import (
    MSDeformableAttention3D,
)
from xtorch.nn.heads.lane3d.transformer_decoder_layer import TransformerDecoderLayerParams
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.transformer_decoder import (
    SparseLanecppTransformerDecoder,
    SparseLanecppTransformerDecoderInput,
    SparseLanecppTransformerOutput,
)

torch.fx.wrap("expand")  # must be called here in the calling module


@dataclass
class SparseLanecppTransformerInput:
    """Input configuration for the SparseLanecpp Transformer."""

    x: torch.Tensor
    query: torch.Tensor
    query_embed: torch.Tensor
    pos_embed: torch.Tensor | None
    reference_points_x: torch.Tensor
    reference_points_z: torch.Tensor
    principal_point: torch.Tensor
    focal_length: torch.Tensor
    is_pinhole_model: torch.Tensor
    extrinsics: torch.Tensor


class SparseLanecppTransformer(nn.Module):
    """SparseLanecpp Transformer."""

    def __init__(  # noqa: PLR0913
        self,
        num_classes: int,
        embed_dims: int,
        feature_dim: HW,
        num_query: int,
        num_anchor_per_query: int,
        num_points_per_anchor: int,
        anchor_y_steps: NDArray[np.float32],
        num_decoder_layers: int,
        num_output_layers: int,
        transformer_decoder_layer_params: TransformerDecoderLayerParams,
        *,
        with_iterative_refinement: bool,
        use_sigmoid_for_classification: bool,
    ) -> None:
        """Initialize the SparseLanecpp Transformer.

        Args:
            num_classes: Number of classes.
            embed_dims: Embedding dimensions.
            feature_dim: Feature map dimension.
            num_query: Number of queries.
            num_anchor_per_query: Number of anchors per query.
            num_points_per_anchor: Number of points per anchor.
            anchor_y_steps: Anchor y steps.
            num_decoder_layers: Number of transformer decoder layers.
            num_output_layers: Number of fully connected layers for the output branches.
            transformer_decoder_layer_params: Parameters for the transformer decoder layer.
                operation_order: Order of operations in the layer.
                num_pt_per_query: Number of points per query.
                image_shape: Shape of the image.
                point_cloud_range: Point cloud range.
                attention_nonlinearity: Nonlinearity type for attention.
                num_heads: Number of attention heads.
                num_levels: Number of levels in the attention mechanism.
                num_points: Number of points in the attention mechanism.
                ffnn_intermediate_dims_multiplier: Multiplier for the intermediate dimensions in the FFNN.
                dropout: Dropout rate for transformer decoder.
                points_as_query: Whether to use points as queries.
                export_use_pinhole_model: Whether to export the model as a pinhole model (or cylindrical model).
            with_iterative_refinement: Whether to use iterative refinement.
            use_sigmoid_for_classification: Whether to use sigmoid activation for classification outputs.
        """
        super().__init__()
        self._embeddings = nn.Parameter(torch.zeros((1, embed_dims, 1, 1)))

        # Image positional encoding
        sine_positional_encoding = MaskedSinePositionalEncoding2D(num_feats=embed_dims // 2, normalize=True)
        self._sin_embed = nn.Buffer(
            data=sine_positional_encoding(torch.zeros((1, feature_dim.height, feature_dim.width), dtype=torch.float32)),
            persistent=False,
        )
        self._adapt_pos3d = nn.Sequential(
            nn.Conv2d(embed_dims, embed_dims * 4, kernel_size=1, stride=1, padding=0),
            nn.ReLU(),
            nn.Conv2d(embed_dims * 4, embed_dims, kernel_size=1, stride=1, padding=0),
        )

        self._decoder = SparseLanecppTransformerDecoder(
            num_classes=num_classes,
            num_query=num_query,
            num_anchor_per_query=num_anchor_per_query,
            num_points_per_anchor=num_points_per_anchor,
            embed_dims=embed_dims,
            anchor_y_steps=anchor_y_steps,
            num_decoder_layers=num_decoder_layers,
            num_output_layers=num_output_layers,
            transformer_decoder_layer_params=transformer_decoder_layer_params,
            with_iterative_refinement=with_iterative_refinement,
            use_sigmoid_for_classification=use_sigmoid_for_classification,
        )

        self._init_weights()

    def _init_weights(self) -> None:
        """Initialize the weights of the SparseLanecpp Transformer."""
        # Skip all modules and classes that are already initialized.
        skipped_modules = [
            self._decoder.cls_branches,
            self._decoder.reg_branches,
            self._decoder.vis_branches,
            self._decoder.reg_branch_x,
            self._decoder.reg_branch_z,
        ]
        skipped_classes = [MSDeformableAttention3D]
        skip_children = False  # Flag required to skip all child modules of a skipped parent
        parent_name = ""  # Name of the parent module to skip children of
        for name, m in self.named_modules():
            # Skip already initialized modules & classes
            if m in skipped_modules:
                skip_children = True
                parent_name = name
                continue
            if any(isinstance(m, skipped_class) for skipped_class in skipped_classes):
                skip_children = True
                parent_name = name
                continue
            if skip_children and name.startswith(parent_name):
                continue
            skip_children = False  # Reset skip_children flag if we are not in the parent module anymore

            # Initialize weights of all other modules
            if hasattr(m, "weight") and m.weight.dim() > 1:  # type: ignore[reportCallIssue]
                nn.init.xavier_uniform_(m.weight, gain=1.0)  # type: ignore[reportArgumentType]
                if hasattr(m, "bias") and m.bias is not None:
                    nn.init.constant_(m.bias, 0.0)  # type: ignore[reportArgumentType]
        nn.init.normal_(self._embeddings)

    def forward(self, inputs: SparseLanecppTransformerInput) -> SparseLanecppTransformerOutput:
        """Forward pass of the SparseLanecpp Transformer.

        Args:
            inputs: The input configuration for the SparseLanecpp Transformer.

        Returns:
            Class outputs, coordinate outputs and visibility outputs.
        """
        bs, _, h, w = inputs.x.shape

        # Prepare and adapt sin pos embed and apply 3D adaption
        sin_embed = expand(self._sin_embed, bs, -1, -1, -1)
        sin_embed = self._adapt_pos3d(sin_embed)

        spatial_shapes = (h, w)
        feat_flatten = inputs.x.flatten(2).permute(0, 2, 1)  # Bx(h*w)xC

        sin_embed += self._embeddings

        assert inputs.pos_embed is None

        return self._decoder(
            SparseLanecppTransformerDecoderInput(
                query=inputs.query,
                key=feat_flatten,
                value=[inputs.x],
                query_pos=inputs.query_embed,
                key_pos=inputs.pos_embed,
                principal_point=inputs.principal_point,
                focal_length=inputs.focal_length,
                is_pinhole_model=inputs.is_pinhole_model,
                extrinsics=inputs.extrinsics,
                sin_embed=[sin_embed],
                reference_points_x=inputs.reference_points_x,
                reference_points_z=inputs.reference_points_z,
                spatial_shapes=spatial_shapes,
            )
        )
