"""Sparse LaneCPP line decoder module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import cast

import numpy as np
import torch
from numpy.typing import NDArray

from xcontract.geometry.definitions.volume import VolumeRange
from xtorch.nn.module import StatefulModule
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_representation.splines import (
    BSpline,
    CatmullRomSpline,
    LineRepresentation,
    SplineModel,
)


class SparseLanecppLineDecoder(StatefulModule):
    """A class to decode line representations into dense lines using spline models."""

    def __init__(
        self,
        line_representation: LineRepresentation,
        position_range: VolumeRange,
        num_query: int,
        num_pt_per_line: int,
        anchor_y_steps: NDArray[np.float32],
        anchor_y_steps_dense: NDArray[np.float32],
        *,
        points_as_query: bool,
    ) -> None:
        """Initialize the SparseLanecppLineDecoder.

        Args:
            line_representation: The type of line representation to use.
            position_range: The range of the point cloud.
            num_query: The number of queries.
            num_pt_per_line: The number of points per line.
            anchor_y_steps: The y-steps for anchors.
            anchor_y_steps_dense: The dense y-steps for anchors.
            points_as_query: Whether points are used as queries.
        """
        super().__init__()
        self.register_buffer("position_range", torch.tensor(position_range, dtype=torch.float32))
        self._num_query = num_query
        self.register_buffer("anchor_y_steps", torch.tensor(anchor_y_steps, dtype=torch.float32))
        self._num_y_steps = anchor_y_steps.shape[0]
        self.register_buffer("anchor_y_steps_dense", torch.tensor(anchor_y_steps_dense, dtype=torch.float32))
        self._num_y_steps_dense = anchor_y_steps_dense.shape[0]
        self._num_anchor_per_query = num_pt_per_line if points_as_query else 1
        self._num_points_per_anchor = num_pt_per_line // self._num_anchor_per_query

        if line_representation == LineRepresentation.CATMULL_ROM:
            self._spline_model = CatmullRomSpline(
                use_offset=False,
                num_control_points=self._num_y_steps,
            )
        elif line_representation in [LineRepresentation.B_SPLINE_DEG_1, LineRepresentation.B_SPLINE_DEG_3]:
            degree = 1 if (line_representation == LineRepresentation.B_SPLINE_DEG_1) else 3
            self._spline_model = BSpline(
                degree=degree,
                use_offset=False,
                num_control_points=self._num_y_steps,
            )
        else:
            msg = f"Unsupported line representation: {line_representation}"
            raise ValueError(msg)

    def forward(
        self, line_preds: torch.Tensor, line_vis: torch.Tensor, batch_size: int
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
        """Forward pass to decode line representations into dense lines using spline models.

        Args:
            line_preds: Tensors containing the predicted lanes x and z coordinates.
            line_vis: Tensors containing the visibility of the predicted lanes.
            batch_size: Batch size.

        Returns:
            line predictions x, line prediction z, line prediction visibility,  all dense line predictions.
        """
        pc_range_diff_x = self.position_range[3] - self.position_range[0]  # type:ignore[reportIndexIssue]
        pc_range_diff_z = self.position_range[5] - self.position_range[2]  # type:ignore[reportIndexIssue]
        line_preds_x = line_preds[..., 0] * pc_range_diff_x + self.position_range[0]  # type:ignore[reportIndexIssue]
        line_preds_z = line_preds[..., 1] * pc_range_diff_z + self.position_range[2]  # type:ignore[reportIndexIssue]

        if not isinstance(line_preds_x, torch.fx.Proxy):
            assert line_preds_x.dim() == 3, (
                f"Line Preds shall have Rank3: Num Transformer, B, C. Found shape: {line_preds_x.shape}"
            )
        line_preds_x = cast(torch.Tensor, line_preds_x)

        num_transformer_out = line_preds_x.shape[0]

        # TODO: check later if these reshapes could be optimized  # noqa: TD003
        # For inference send out splitted predictions to avoid bad quantization around concat due to
        # different value ranges
        line_preds_x = line_preds_x.view(
            num_transformer_out * batch_size,
            self._num_query,
            self._num_anchor_per_query,
            self._num_points_per_anchor,
            1,
        )
        line_preds_x = line_preds_x.permute(
            0, 1, 4, 2, 3
        )  # num_transformer_out * batch_size, num_query, 3, num_anchor_per_query, num_points_per_anchor
        line_preds_x = line_preds_x.flatten(2, 4)
        line_preds_x = line_preds_x.view(
            num_transformer_out,
            batch_size,
            self._num_query,
            self._num_anchor_per_query * self._num_points_per_anchor,
        )

        line_preds_z = line_preds_z.view(
            num_transformer_out * batch_size,
            self._num_query,
            self._num_anchor_per_query,
            self._num_points_per_anchor,
            1,
        )
        line_preds_z = line_preds_z.permute(
            0, 1, 4, 2, 3
        )  # num_transformer_out * batch_size, num_query, 3, num_anchor_per_query, num_points_per_anchor
        line_preds_z = line_preds_z.flatten(2, 4)
        line_preds_z = line_preds_z.view(
            num_transformer_out,
            batch_size,
            self._num_query,
            self._num_anchor_per_query * self._num_points_per_anchor,
        )

        line_vis = line_vis.squeeze(-1)
        line_vis = line_vis.view(
            num_transformer_out * batch_size,
            self._num_query,
            self._num_anchor_per_query,
            self._num_points_per_anchor,
            1,
        )
        line_vis = line_vis.permute(
            0, 1, 4, 2, 3
        )  # num_transformer_out * batch_size, num_query, 3, num_anchor_per_query, num_points_per_anchor
        line_vis = line_vis.flatten(2, 4)
        line_vis = line_vis.view(
            num_transformer_out,
            batch_size,
            self._num_query,
            self._num_anchor_per_query * self._num_points_per_anchor,
        )

        # this is only needed for training
        line_preds_3d = torch.stack((line_preds_x, line_preds_z, line_vis), dim=-1)
        num_transformer_out = line_preds_3d.shape[0]
        line_preds_3d = line_preds_3d.view(
            num_transformer_out * batch_size,
            self._num_query,
            self._num_anchor_per_query,
            self._num_points_per_anchor,
            3,  # x,z,vis
        )
        line_preds_3d = line_preds_3d.permute(
            0, 1, 4, 2, 3
        )  # num_transformer_out * batch_size, num_query, 3, num_anchor_per_query, num_points_per_anchor
        line_preds_3d = line_preds_3d.flatten(2, 4)
        line_preds_3d = line_preds_3d.view(
            num_transformer_out,
            batch_size,
            self._num_query,
            3 * self._num_anchor_per_query * self._num_points_per_anchor,
        )
        # Only required for training. Included anyway for less tracing issues when using AIMET.
        line_preds_3d_dense = _line_representation_to_dense_polyline(
            self._spline_model, line_preds_3d, self._num_y_steps_dense
        )

        return line_preds_x, line_preds_z, line_vis, line_preds_3d_dense


def _line_representation_to_dense_polyline(
    spline_model: SplineModel, line_preds_3d: torch.Tensor, num_y_steps_dense: int
) -> torch.Tensor:
    """Calculate the dense polyline representation of the lane predictions using the spline model.

    Note: The spline model function is not traced by AIMET, so it won't be quantized. This is fine because the dense
        output is not used in inference.

    Args:
        spline_model: The spline model to use for the conversion.
        line_preds_3d: The 3D line preds with
            shape [num_transformer_out, batch_size, num_query, 3 * num_anchor_per_query * num_points_per_anchor].
        num_y_steps_dense: The number of y-steps for the dense representation.

    Returns: The dense polyline representation of the lane predictions.

    """
    return spline_model.line_representation_to_dense_polyline(line_preds_3d, num_y_steps_dense)


torch.fx.wrap("_line_representation_to_dense_polyline")
