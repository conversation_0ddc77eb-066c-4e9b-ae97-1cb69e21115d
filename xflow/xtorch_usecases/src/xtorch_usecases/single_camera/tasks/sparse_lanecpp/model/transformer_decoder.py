"""Decoder Implementation for Sparse LaneCPP."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import copy
from collections.abc import Sequence
from dataclasses import dataclass
from typing import NamedTuple

import numpy as np
import torch
from numpy.typing import NDArray
from torch import nn

from xtorch.nn.functional import inverse_sigmoid
from xtorch.nn.heads.lane3d.transformer_decoder_layer import (
    TransformerDecoderLayer,
    TransformerDecoderLayerInput,
    TransformerDecoderLayerParams,
)
from xtorch.nn.module import StatefulModule


@dataclass
class SparseLanecppTransformerDecoderInput:
    """Input of the SparseLanecpp transformer decoder layer."""

    query: torch.Tensor
    key: torch.Tensor
    value: Sequence[torch.Tensor]
    query_pos: torch.Tensor
    key_pos: torch.Tensor | None
    principal_point: torch.Tensor
    focal_length: torch.Tensor
    is_pinhole_model: torch.Tensor
    extrinsics: torch.Tensor
    sin_embed: Sequence[torch.Tensor]
    reference_points_x: torch.Tensor
    reference_points_z: torch.Tensor
    spatial_shapes: tuple[int, int]


class SparseLanecppTransformerOutput(NamedTuple):
    """Output of the SparseLanecpp Transformer.

    Fields:
        classes: The class predictions per lane.
        coordinates: The predicted x- & z-coordinates. Y-coordinates are fixed to the anchor y-steps.
        visibility: The predicted visibility per anchor point.
    """

    classes: torch.Tensor
    coordinates: torch.Tensor
    visibility: torch.Tensor


class SparseLanecppTransformerDecoder(StatefulModule):
    """SparseLanecpp Transformer Decoder."""

    def __init__(  # noqa: PLR0913
        self,
        num_classes: int,
        num_query: int,
        num_anchor_per_query: int,
        num_points_per_anchor: int,
        embed_dims: int,
        anchor_y_steps: NDArray[np.float32],
        num_decoder_layers: int,
        num_output_layers: int,
        transformer_decoder_layer_params: TransformerDecoderLayerParams,
        *,
        with_iterative_refinement: bool,
        use_sigmoid_for_classification: bool,
    ) -> None:
        """Initialize the SparseLanecpp Transformer Decoder.

        Args:
            num_classes : Number of classes.
            num_query : Number of queries.
            num_anchor_per_query : Number of anchors per query.
            num_points_per_anchor : Number of points per anchor.
            embed_dims : Embedding dimensions.
            anchor_y_steps : Anchor y steps.
            num_decoder_layers: Number of transformer decoder layers.
            num_output_layers: Number of fully connected layers for the output branches.
            transformer_decoder_layer_params: Parameters for the transformer decoder layer.
                operation_order: Order of operations in the layer.
                num_pt_per_query: Number of points per query.
                image_shape: Shape of the image.
                point_cloud_range: Point cloud range.
                attention_nonlinearity: Nonlinearity type for attention.
                num_heads: Number of attention heads.
                num_levels: Number of levels in the attention mechanism.
                num_points: Number of points in the attention mechanism.
                ffnn_intermediate_dims_multiplier: Multiplier for the intermediate dimensions in the FFNN.
                dropout: Dropout rate for transformer decoder.
                points_as_query: Whether to use points as queries.
                export_use_pinhole_model: Whether to export the model as a pinhole model (or cylindrical model).
            with_iterative_refinement: Whether to use iterative refinement.
            use_sigmoid_for_classification: Whether to use sigmoid activation for classification outputs.
        """
        super().__init__()

        self._num_classes = num_classes
        self._num_query = num_query
        self._with_iterative_refinement = with_iterative_refinement
        self._num_anchor_per_query = num_anchor_per_query
        self._num_points_per_anchor = num_points_per_anchor
        self._use_sigmoid_for_classification = use_sigmoid_for_classification

        self._decoder_layers = nn.ModuleList(
            [
                TransformerDecoderLayer(
                    embed_dim=embed_dims,
                    num_query=self._num_query,
                    anchor_y_steps=anchor_y_steps,
                    layer_params=transformer_decoder_layer_params,
                )
                for _ in range(num_decoder_layers)
            ]
        )

        # Output Layers
        # Classification
        cls_branch = []
        for _ in range(num_output_layers):
            cls_branch.append(nn.Linear(embed_dims, embed_dims))
            cls_branch.append(nn.LayerNorm(embed_dims))
            cls_branch.append(nn.ReLU(inplace=True))
        cls_branch.append(nn.Linear(embed_dims, self._num_classes))
        fc_cls = nn.Sequential(*cls_branch)
        self.cls_branches = nn.ModuleList([fc_cls for _ in range(num_decoder_layers)])

        # Regression
        reg_branch = []
        for _ in range(num_output_layers):
            reg_branch.append(nn.Linear(embed_dims, embed_dims))
            reg_branch.append(nn.ReLU())
        reg_branch = nn.Sequential(*reg_branch)
        if self._with_iterative_refinement:
            # different mlp per layer
            self.reg_branches = nn.ModuleList([copy.deepcopy(reg_branch) for _ in range(num_decoder_layers)])
        else:
            # Shared mlp
            self.reg_branches = nn.ModuleList([reg_branch for _ in range(num_decoder_layers)])

        self.reg_branch_x = nn.Linear(embed_dims, self._num_points_per_anchor)
        self.reg_branch_z = nn.Linear(embed_dims, self._num_points_per_anchor)

        # Visibility
        vis_branch = []
        for _ in range(num_output_layers):
            vis_branch.append(nn.Linear(embed_dims, embed_dims))
            vis_branch.append(nn.ReLU())
        vis_branch.append(nn.Linear(embed_dims, self._num_points_per_anchor))
        vis_branch = nn.Sequential(*vis_branch)
        self.vis_branches = nn.ModuleList([vis_branch for _ in range(num_decoder_layers)])
        self._init_weights()

    def _init_weights(self) -> None:
        """Initialize the weights of the SparseLanecpp Transformer Decoder."""
        if self._use_sigmoid_for_classification:
            bias_init = bias_init_with_prob(0.01)
            for m in self.cls_branches:
                nn.init.constant_(list(m.children())[-1].bias, bias_init)  # type: ignore[reportArgumentType]

    def forward(self, inputs: SparseLanecppTransformerDecoderInput) -> SparseLanecppTransformerOutput:
        """Forward pass of the SparseLanecpp Transformer Decoder.

        Args:
            inputs: The input configuration for the SparseLanecpp Transformer Decoder.

        Returns:
            Returns:
                Class outputs, coordinate outputs and visibility outputs.
                During training, each transformer layer returns the predictions
                                 resulting in [num_transformer_layers, batch_size, ...].
                During inference, only the last layer is returned resulting in [batch_size, ...]
        """

        query = inputs.query
        reference_points_x = inputs.reference_points_x
        reference_points_z = inputs.reference_points_z

        assert inputs.key_pos is None

        intermediate = []
        outputs_classes = []
        outputs_coords = []
        outputs_vis = []

        # iterate over layers
        for layer_idx, layer in enumerate(self._decoder_layers):
            layer_forward_pass_args = TransformerDecoderLayerInput(
                query=query,
                key=inputs.key,
                value=inputs.value,
                key_pos=inputs.sin_embed,
                reference_points_x=reference_points_x,
                reference_points_z=reference_points_z,
                principal_point=inputs.principal_point,
                focal_length=inputs.focal_length,
                is_pinhole_model=inputs.is_pinhole_model,
                extrinsics=inputs.extrinsics,
                query_pos=inputs.query_pos,
                spatial_shapes=inputs.spatial_shapes,
            )

            query = layer(inputs=layer_forward_pass_args)

            intermediate.append(query)

            reg_branch_output = self.reg_branches[layer_idx](query)
            reg_branch_output_x = self.reg_branch_x(reg_branch_output)
            reg_branch_output_z = self.reg_branch_z(reg_branch_output)

            bs = reg_branch_output.shape[0]

            # Update reference points
            if self._with_iterative_refinement:
                reference_points_x = inverse_sigmoid(reference_points_x)
                new_reference_points_x = reference_points_x + reg_branch_output_x

                reference_points_z = inverse_sigmoid(reference_points_z)
                new_reference_points_z = reference_points_z + reg_branch_output_z
            else:
                new_reference_points_x = reg_branch_output_x
                new_reference_points_z = reg_branch_output_z

            reference_points_x = new_reference_points_x.sigmoid()
            reference_points_z = new_reference_points_z.sigmoid()

            cls_feat = query.view(bs, self._num_query, self._num_anchor_per_query, -1)
            cls_feat = torch.max(cls_feat, dim=2)[0]
            outputs_class = self.cls_branches[layer_idx](cls_feat)

            vis = self.vis_branches[layer_idx](query)

            outputs_classes.append(outputs_class)
            reference_points = torch.cat([reference_points_x, reference_points_z], dim=-1)
            outputs_coords.append(reference_points)
            outputs_vis.append(vis)

            reference_points_x = reference_points_x.detach()
            reference_points_z = reference_points_z.detach()

        return _extract_last_layer(self, outputs_classes, outputs_coords, outputs_vis)


def _extract_last_layer(
    self_: SparseLanecppTransformerDecoder,
    outputs_classes: list[torch.Tensor],
    outputs_coords: list[torch.Tensor],
    outputs_vis: list[torch.Tensor],
) -> SparseLanecppTransformerOutput:
    """Hides control flow from symbolic tracing."""
    if self_.inferencing:
        return SparseLanecppTransformerOutput(outputs_classes[-1], outputs_coords[-1], outputs_vis[-1])

    return SparseLanecppTransformerOutput(
        torch.stack(outputs_classes), torch.stack(outputs_coords), torch.stack(outputs_vis)
    )


torch.fx.wrap("_extract_last_layer")


def bias_init_with_prob(prior_prob: float) -> float:
    """Initialize bias values according to a given prior probability."""
    bias_init = float(-np.log((1 - prior_prob) / prior_prob))
    return bias_init
