"""The SparseLanecpp detection loss."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import NamedTuple

import torch
from torch import nn

from xtorch.losses.classification.focal_loss import MultiLabelFocalLoss
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.matcher.sparse_instance_matcher import MatchedIndices

_DEFAULT_REGRESSION_LOSS_WEIGHTS = {
    "xs_loss": 1.0,
    "zs_loss": 1.0,
    "xs_smooth_loss": 1.0,
    "zs_smooth_loss": 0.5,
}


class DetectionLossPredictions(NamedTuple):
    """Input for the DetectionLoss class."""

    class_scores: torch.Tensor
    lanes_x: torch.Tensor
    lanes_z: torch.Tensor
    lanes_visibility: torch.Tensor
    lanes_dense: torch.Tensor


class DetectionLossTargets(NamedTuple):
    """Input for the DetectionLoss class."""

    lane_category: torch.Tensor
    lanes_dense: torch.Tensor


class DetectionLossOutput(NamedTuple):
    """NamedTuple holding the detection loss outputs."""

    xs_loss: torch.Tensor
    zs_loss: torch.Tensor
    xs_smooth_loss: torch.Tensor
    zs_smooth_loss: torch.Tensor
    vis_loss: torch.Tensor
    cls_loss: torch.Tensor


class DetectionLoss(nn.Module):
    """The SparseLanecpp detection loss.

    Consists of the following sub-losses:
        1. xs_loss: L1 loss for x coordinates
        2. zs_loss: L1 loss for z coordinates
        3. xs_smooth_loss (optional): MSE loss for x coordinates smoothness
        4. zs_smooth_loss (optional): MSE loss for z coordinates smoothness
        5. vis_loss: Binary CrossEntropy loss for visibility
        6. cls_loss: Focal loss for classification
    """

    def __init__(
        self,
        num_y_steps: int,
        num_y_steps_dense: int,
        num_classes: int,
        regression_loss_weights: dict[str, float] | None = None,
    ) -> None:
        """Initialize the DetectionLoss.

        Args:
            num_y_steps: Number of y steps for the lane.
            num_y_steps_dense: Number of y steps for the dense lane.
            num_classes: Number of classes for the classification task.
            regression_loss_weights: Dictionary containing the weights for the regression losses.
        """
        super().__init__()
        self._num_classes = num_classes
        self._num_y_steps = num_y_steps
        self._num_y_steps_dense = num_y_steps_dense
        if regression_loss_weights is None:
            regression_loss_weights = _DEFAULT_REGRESSION_LOSS_WEIGHTS

        # Sub-loss definitions
        self._reg_crit = nn.L1Loss(reduction="none")
        self._use_smooth_loss = (
            regression_loss_weights["xs_smooth_loss"] > 0.0 or regression_loss_weights["zs_smooth_loss"] > 0.0
        )
        if self._use_smooth_loss:
            self._smooth_crit = nn.MSELoss(reduction="none")
        self._cls_crit = MultiLabelFocalLoss(num_classes=self._num_classes)
        self._vis_loss = nn.BCEWithLogitsLoss(reduction="none")

        # Regression sub-losses are weighted and joined before uncertainty weighting to prevent fighting gradients
        # leading to only straight lines being predicted.
        # All other sub-losses are weighted after uncertainty weighting as they would otherwise be un-learned by the
        # uncertainty weights over time.
        self._xs_loss_weight = regression_loss_weights["xs_loss"]
        self._zs_loss_weight = regression_loss_weights["zs_loss"]
        self._xs_smooth_loss_weight = regression_loss_weights["xs_smooth_loss"]
        self._zs_smooth_loss_weight = regression_loss_weights["zs_smooth_loss"]

    def forward(
        self,
        predictions: DetectionLossPredictions,
        targets: DetectionLossTargets,
        matched_indices: list[MatchedIndices],
    ) -> DetectionLossOutput:
        """Compute the SparseLanecpp detection loss.

        Args:
            predictions: The predictions from the model. All entries have shape [num_layers, batch_size, ...]
            targets: The ground truth data.
            matched_indices: The list of matched indices for predictions and ground truth.

        Returns:
            The loss output.
        """
        batch_size = predictions.lanes_x.shape[1]
        device = predictions.lanes_x.device

        loss = self._multi_layer_loss(
            predictions=predictions,
            targets=targets,
            matched_indices=matched_indices,
            batch_size=batch_size,
            device=device,
        )

        return DetectionLossOutput(
            xs_loss=loss.xs_loss * self._xs_loss_weight,
            zs_loss=loss.zs_loss * self._zs_loss_weight,
            xs_smooth_loss=loss.xs_smooth_loss * self._xs_smooth_loss_weight,
            zs_smooth_loss=loss.zs_smooth_loss * self._zs_smooth_loss_weight,
            vis_loss=loss.vis_loss,
            cls_loss=loss.cls_loss,
        )

    def _multi_layer_loss(
        self,
        predictions: DetectionLossPredictions,
        targets: DetectionLossTargets,
        matched_indices: list[MatchedIndices],
        batch_size: int,
        device: torch.device,
    ) -> DetectionLossOutput:
        """Computes the detection loss for a all layers of the SparseLanecpp head.

        NOTE: The loss hierarchy is lane -> sample -> layer. Mean computations respect this hierarchy.

        Args:
            predictions: The predictions from the model. All entries have shape [num_layers, batch_size, ...]
            targets: The ground truth data.
            matched_indices: The list of matched indices for predictions and ground truth.
            batch_size: The batch size of the predictions.
            device: The device to use for the computations.

        Returns:
            The loss output.
        """
        num_preds = predictions.class_scores.shape[2]
        num_layers = predictions.class_scores.shape[0]

        def _scatter_reduce_sample_valid_mean(
            loss: torch.Tensor, index: tuple[torch.Tensor | slice, ...], numel: torch.Tensor
        ) -> torch.Tensor:
            """Scatters loss components back to batch dimension & reduces them to per layer per sample means.

            Args:
                loss: The loss tensor to reduce.
                index: The indices to scatter the loss to.
                numel: The number of valid elements per sample over which to compute the mean.
                        Must have a broadcastable shape to (num_layers, batch_size).

            Returns: The reduced loss tensor.
            """
            scattered_loss = torch.zeros((num_layers, batch_size, num_preds), device=loss.device, dtype=loss.dtype)
            scattered_loss[index] = loss.sum(dim=[-1])
            return scattered_loss.sum(dim=-1) / torch.clamp(numel, min=1)

        def _reg_loss(
            preds: torch.Tensor,
            gts: torch.Tensor,
            all_batch_idx: torch.Tensor,
            all_pred_idx: torch.Tensor,
            valid_pts_mask: torch.Tensor,
        ) -> torch.Tensor:
            """Compute regression loss per layer per sample.

            Args:
                preds: The predicted values of shape [num_layers, num_matched_preds, num_points]
                gts: The ground truth values of shape [num_layers, num_matched_gts, num_points].
                all_batch_idx: The batch indices for the matched predictions/gts.
                all_pred_idx: The prediction indices for the matched predictions/gts.
                valid_pts_mask: A mask indicating which points are valid.

            Returns:
                The reduced regression loss tensor of shape [num_layers, batch_size].
            """
            loss = self._reg_crit(preds, gts)
            valid_points_count = valid_pts_mask.sum(dim=-1)
            scattered_points_count = torch.zeros(
                (num_layers, batch_size, num_preds), device=loss.device, dtype=valid_points_count.dtype
            )
            scattered_points_count[:, all_batch_idx, all_pred_idx] = valid_points_count
            return _scatter_reduce_sample_valid_mean(
                loss=torch.where(valid_pts_mask, loss, 0.0),
                index=(slice(None), all_batch_idx, all_pred_idx),
                numel=scattered_points_count.sum(dim=-1),
            )

        def _smooth_loss(
            preds: torch.Tensor,
            all_batch_idx: torch.Tensor,
            all_pred_idx: torch.Tensor,
        ) -> torch.Tensor:
            """Compute smoothness loss for matched predictions per layer per sample.

            Args:
                preds: The predictions of shape [num_layers, num_matched_preds, num_points].
                all_batch_idx: The batch indices for the matched predictions.
                all_pred_idx: The prediction indices for the matched predictions.

            Returns:
                The reduced smoothness loss tensor of shape [num_layers, batch_size].
            """
            preds_diff = _second_order_diff(preds)
            loss = self._smooth_crit(preds_diff, torch.zeros_like(preds_diff))
            num_total_points_per_sample = torch.bincount(all_batch_idx, minlength=batch_size) * preds_diff.shape[-1]
            return _scatter_reduce_sample_valid_mean(
                loss,
                (slice(None), all_batch_idx, all_pred_idx),
                num_total_points_per_sample[None],
            ).mean()

        all_pred_idx, all_gt_idx, all_batch_idx = [], [], []
        # TODO: In case num_group > 1 of the sparse instance segmentation,
        # we have num_group * batch_size matched indices. The loop below is however only handling the first batch_size
        # many, throwing away the rest. Matched indices are ordered [sample0-grp0, sample0-grp1,..., sample1-grp0, ...]
        # so this is only handling a subset of all samples.
        # https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt
        for i in range(batch_size):
            match = matched_indices[i]
            if match.pred_idx.numel() > 0:
                all_pred_idx.append(match.pred_idx)
                all_gt_idx.append(match.gt_idx)
                all_batch_idx.append(torch.full_like(match.pred_idx, i, device=predictions.class_scores.device))

        if not all_pred_idx:
            # If no predictions are matched, only cls_loss has a non-zero value.
            labels = torch.zeros(predictions.class_scores.shape, dtype=torch.long, device=device)
            labels[..., 0] = 1
            cls_loss = (
                torch.mean(self._cls_crit(logits=predictions.class_scores, labels=labels), dim=[-2, -1])
                .mean(dim=1)
                .mean()
            )
            return DetectionLossOutput(
                xs_loss=torch.tensor(0.0, device=device),
                zs_loss=torch.tensor(0.0, device=device),
                vis_loss=torch.tensor(0.0, device=device),
                cls_loss=cls_loss,
                xs_smooth_loss=torch.tensor(0.0, device=device),
                zs_smooth_loss=torch.tensor(0.0, device=device),
            )

        all_pred_idx = torch.cat(all_pred_idx)
        all_gt_idx = torch.cat(all_gt_idx)
        all_batch_idx = torch.cat(all_batch_idx)

        matched_lane_preds_dense = predictions.lanes_dense[:, all_batch_idx, all_pred_idx]
        matched_gt_categories = targets.lane_category[all_batch_idx, all_gt_idx]
        matched_gt_lane_dense = targets.lanes_dense[all_batch_idx, all_gt_idx]

        # Dissect the lane tensors
        preds_xs = predictions.lanes_x[:, all_batch_idx, all_pred_idx]
        preds_zs = predictions.lanes_z[:, all_batch_idx, all_pred_idx]
        preds_xs_dense, preds_zs_dense, preds_vis_dense = self._dissect_lane_tensor(
            matched_lane_preds_dense, self._num_y_steps_dense
        )
        gt_xs_dense, gt_zs_dense, gt_vis_dense = self._dissect_lane_tensor(
            matched_gt_lane_dense, self._num_y_steps_dense
        )

        gt_xs_dense = gt_xs_dense.unsqueeze(0).expand(num_layers, -1, -1)
        gt_zs_dense = gt_zs_dense.unsqueeze(0).expand(num_layers, -1, -1)
        gt_vis_dense = gt_vis_dense.unsqueeze(0).expand(num_layers, -1, -1)

        # Location losses
        # TODO: Investigate using the visibility range mask for the location loss
        # https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt
        loc_mask = gt_vis_dense > 0
        xs_loss = _reg_loss(preds_xs_dense, gt_xs_dense, all_batch_idx, all_pred_idx, loc_mask).mean(dim=1).mean()
        zs_loss = _reg_loss(preds_zs_dense, gt_zs_dense, all_batch_idx, all_pred_idx, loc_mask).mean(dim=1).mean()
        vis_loss = self._vis_loss(preds_vis_dense, gt_vis_dense)
        vis_loss = (
            _scatter_reduce_sample_valid_mean(
                loss=vis_loss,
                index=(slice(None), all_batch_idx, all_pred_idx),
                numel=(torch.bincount(all_batch_idx, minlength=batch_size) * loc_mask.shape[-1])[None],
            )
            .mean(dim=1)
            .mean()
        )

        # Classification loss
        # TODO: Consider using Softmax-Crossentropy based Focal loss since we do one-hot encoding here!
        # https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt
        cls_target = torch.zeros(
            predictions.class_scores.shape, dtype=torch.long, device=predictions.class_scores.device
        )
        cls_target[..., 0] = 1
        cls_target[:, all_batch_idx, all_pred_idx] = matched_gt_categories[:, :-1].long()[None]
        cls_loss = (
            torch.mean(self._cls_crit(logits=predictions.class_scores, labels=cls_target), dim=[-2, -1])
            .mean(dim=1)
            .mean()
        )

        return DetectionLossOutput(
            xs_loss=xs_loss,
            zs_loss=zs_loss,
            vis_loss=vis_loss,
            cls_loss=cls_loss,
            xs_smooth_loss=(
                _smooth_loss(preds_xs, all_batch_idx, all_pred_idx)
                if self._use_smooth_loss
                else torch.tensor(0.0, device=device)
            ),
            zs_smooth_loss=(
                _smooth_loss(preds_zs, all_batch_idx, all_pred_idx)
                if self._use_smooth_loss
                else torch.tensor(0.0, device=device)
            ),
        )

    def _single_layer_loss(
        self,
        predictions: DetectionLossPredictions,
        targets: DetectionLossTargets,
        matched_indices: list[MatchedIndices],
        batch_size: int,
        device: torch.device,
    ) -> DetectionLossOutput:
        """Computes the detection loss for a single layer of the SparseLanecpp head."""

        xs_losses = []
        zs_losses = []
        xs_smooth_losses = []
        zs_smooth_losses = []
        vis_losses = []
        cls_losses = []

        for batch_idx in range(batch_size):
            # TODO: In case num_group > 1 of the sparse instance segmentation,
            # we have num_group * batch_size matched indices. The loop below is only handling the first batch_size
            # many & throws away rest. Matched indices are ordered [sample0-grp0, sample0-grp1,..., sample1-grp0, ...]
            # so this is only handling a subset of all samples.
            # https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt
            pred_idx = matched_indices[batch_idx].pred_idx
            gt_idx = matched_indices[batch_idx].gt_idx

            # Handle empty Ground Truths
            num_lanes = predictions.class_scores[batch_idx].shape[0]
            if gt_idx.shape[0] < 1:
                # Set all lane targets to background (class 0)
                class_target = predictions.class_scores.new_zeros(num_lanes, self._num_classes).long()
                class_target[:, 0] = 1
                cls_losses.append(
                    self._cls_crit(logits=predictions.class_scores[batch_idx], labels=class_target).mean()
                )
                # Set all other losses to 0.0
                # Do not simply use torch.tensor(0.0) as this breaks gradient_flow!
                mean_prediction = predictions.lanes_dense[batch_idx].mean()
                xs_losses.append(0.0 * mean_prediction)
                zs_losses.append(0.0 * mean_prediction)
                xs_smooth_losses.append(0.0 * mean_prediction)
                zs_smooth_losses.append(0.0 * mean_prediction)
                vis_losses.append(0.0 * mean_prediction)
                continue

            matched_lane_preds_dense = predictions.lanes_dense[batch_idx, pred_idx]
            matched_gt_categories = targets.lane_category[batch_idx, gt_idx]
            matched_gt_lane_dense = targets.lanes_dense[batch_idx, gt_idx]

            # Dissect the lane tensors
            preds_xs = predictions.lanes_x[batch_idx, pred_idx]
            preds_zs = predictions.lanes_z[batch_idx, pred_idx]
            preds_xs_dense, preds_zs_dense, preds_vis_dense = self._dissect_lane_tensor(
                matched_lane_preds_dense, self._num_y_steps_dense
            )
            gt_xs_dense, gt_zs_dense, gt_vis_dense = self._dissect_lane_tensor(
                matched_gt_lane_dense, self._num_y_steps_dense
            )

            # Location losses
            # TODO: Investigate using the visibility range mask for the location loss
            # https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt
            # loc_mask = get_loc_mask_with_occ(gt_vis_dense)
            loc_mask = gt_vis_dense > 0
            xs_loss = self._reg_crit(preds_xs_dense, gt_xs_dense)
            zs_loss = self._reg_crit(preds_zs_dense, gt_zs_dense)
            xs_loss = (xs_loss * loc_mask).sum() / torch.clamp(loc_mask.sum(), min=1)
            zs_loss = (zs_loss * loc_mask).sum() / torch.clamp(loc_mask.sum(), min=1)
            xs_losses.append(xs_loss)
            zs_losses.append(zs_loss)

            # Visbility loss
            vis_losses.append(self._vis_loss(preds_vis_dense, gt_vis_dense).mean())

            # Classification loss
            # TODO: Consider using Softmax-Crossentropy based Focal loss since we do one-hot encoding here!
            # https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt
            # Set all lane targets to background (class 0)
            class_target = predictions.class_scores.new_zeros(num_lanes, self._num_classes).long()
            class_target[:, 0] = 1
            # Update with the actual class labels for matched lanes
            # :-1 removes the track_id from the matched_gt_categories (added by the data loader)
            class_target[pred_idx, :] = matched_gt_categories[:, :-1].long()
            cls_losses.append(self._cls_crit(logits=predictions.class_scores[batch_idx], labels=class_target).mean())

            # Smoothness loss
            if self._use_smooth_loss:
                preds_xs_diff = _second_order_diff(preds_xs)
                preds_zs_diff = _second_order_diff(preds_zs)
                xs_smooth_losses.append(self._smooth_crit(preds_xs_diff, torch.zeros_like(preds_xs_diff)).mean())
                zs_smooth_losses.append(self._smooth_crit(preds_zs_diff, torch.zeros_like(preds_zs_diff)).mean())

        return DetectionLossOutput(
            xs_loss=torch.stack(xs_losses).mean(),
            zs_loss=torch.stack(zs_losses).mean(),
            vis_loss=torch.stack(vis_losses).mean(),
            cls_loss=torch.stack(cls_losses).mean(),
            xs_smooth_loss=torch.stack(xs_smooth_losses).mean()
            if self._use_smooth_loss
            else torch.tensor(0.0, device=device),
            zs_smooth_loss=torch.stack(zs_smooth_losses).mean()
            if self._use_smooth_loss
            else torch.tensor(0.0, device=device),
        )

    def _dissect_lane_tensor(
        self, lane_pred: torch.Tensor, num_y_steps: int
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Dissects the lane tensor into x coordinates, z coordinates and the visibility.

        Args:
            lane_pred: The lane tensor of shape [num_lanes, 3 * num_y_steps].
            num_y_steps: The number of y steps.

        Returns: The dissected lane tensor as a tuple of (x coordinates, z coordinates, visibility).
        """
        xs = lane_pred[..., :num_y_steps]
        zs = lane_pred[..., num_y_steps : 2 * num_y_steps]
        vis = lane_pred[..., 2 * num_y_steps :]
        return xs, zs, vis


def get_detection_targets(target: SparseLanecppGroundTruth) -> DetectionLossTargets:
    """Prepare the target tensors for the detection loss computation.

    Args:
        target: The SparseLanecppGroundTruth.

    Returns: The detection head targets.
    """
    return DetectionLossTargets(lane_category=target.lane_category, lanes_dense=target.lanes_dense)


def get_detection_preds(pred: SparseLanecppHeadOutput) -> DetectionLossPredictions:
    """Prepare the prediction tensors for the detection computation.

    Args:
        pred: The SparseLanecppHeadOutput.

    Returns: The detection head predictions.
    """
    assert pred.lanes_dense is not None
    assert pred.scaled_output is not None
    return DetectionLossPredictions(
        class_scores=pred.raw_output.class_scores,
        lanes_x=pred.scaled_output.lanes_x,
        lanes_z=pred.scaled_output.lanes_z,
        lanes_visibility=pred.scaled_output.lanes_visibility,
        lanes_dense=pred.lanes_dense,
    )


def _second_order_diff(coords: torch.Tensor) -> torch.Tensor:
    """Calculates the second order discrete difference of the given coordinates.

    This function computes the second order discrete difference along the last dimension
    of the input tensor. It is useful for measuring the curvature or smoothness of the
    predicted coordinates.

    Args:
        coords: The predicted coordinates with shape [..., coordinates].

    Returns: The second order discrete differences with shape [..., coordinates - 2].
    """
    return coords[..., 2:] - 2 * coords[..., 1:-1] + coords[..., :-2]
