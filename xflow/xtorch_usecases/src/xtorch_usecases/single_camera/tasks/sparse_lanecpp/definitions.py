"""Definitions for the SparseLanecpp task."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Iterator
from dataclasses import dataclass, field, fields
from typing import Final, NamedTuple, cast

import numpy as np
from numpy.typing import NDArray
from torch import Tensor

from data_formats.lane_3d.mpc4_loader_helper import HEL_CATEGORIES, LANE_CATEGORIES, ROAD_EDGE_CATEGORIES
from xcontract.data.definitions.image import HW
from xcontract.geometry.definitions.volume import VolumeRange
from xtorch.multi_task.task_config import TaskSpecificConfig
from xtorch.nn.backbones.interface import HasStride8, HasStride16, HasStride32
from xtorch.nn.blocks.sliced_multihead_attention import AttentionNonlinearityType
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.anchors import (
    ANCHOR_Y_STEPS_DENSE,
    LineRepresentation,
    get_anchor_y_steps,
)


@dataclass
class PostUncertaintySublossWeights:
    """The LaneCPP subloss weights applied after uncertainty weighting.

    NOTE: The regression losses and their corresponding smoothness losses are already joined before uncertainty
        weighting to prevent fighting gradients!
    """

    xs_loss: float = 4.0
    zs_loss: float = 20.0
    vis_loss: float = 1.0
    cls_loss: float = 20.0
    sparse_inst_cls_loss: float = 10.0
    mask_loss: float = 25.0
    dice_loss: float = 10.0
    objectness_loss: float = 5.0


CLASS_ID_TO_COLOR: Final = {
    0: (147, 112, 219),  # mediumpurple
    1: (72, 209, 204),  # mediumturquoise
    2: (186, 85, 211),  # mediumorchid
    3: (135, 206, 250),  # lightskyblue
    4: (255, 105, 180),  # hotpink
    5: (100, 149, 237),  # cornflowerblue
    6: (154, 205, 50),  # yellowgreen
    7: (30, 144, 255),  # dodgerblue
    8: (250, 128, 114),  # salmon
    9: (240, 128, 128),  # lightcoral
    10: (255, 127, 80),  # coral
    11: (32, 178, 170),  # lightseagreen
    12: (65, 105, 225),  # royalblue
    13: (34, 139, 34),  # forestgreen
    14: (0, 0, 0),  # black
    15: (0, 0, 0),  # black
    16: (0, 0, 0),  # black
    17: (0, 0, 0),  # black
    18: (0, 0, 0),  # black
    19: (255, 215, 0),  # gold
    20: (255, 215, 0),  # gold
    21: (0, 0, 0),  # black
    22: (255, 0, 0),  # red
    23: (0, 0, 0),  # black
    24: (0, 0, 0),  # black
    25: (0, 0, 0),  # black
    26: (0, 0, 0),  # black
    27: (0, 0, 0),  # black
    28: (0, 0, 0),  # black
    29: (0, 0, 0),  # black
}


class SparseLanecppHeadInput(HasStride8, HasStride16, HasStride32):
    """SparseLanecpp head input."""


@dataclass
class SparseLanecppCameraDataConfig:
    """Camera data config for the SparseLanecpp task."""

    target_shape: HW = field(default_factory=lambda: HW(640, 2304))
    original_shape: HW = field(default_factory=lambda: HW(1280, 2304))
    # NOTE: The bottom-center crop is currently hard-coded in
    # `xtorch_usecases/single_camera/size_modification/pyper/offset.py`!
    # Make sure to adapt the code there if you change the crop_bottom value here.
    crop_bottom: int = 0


@dataclass
class SparseLanecppLoaderConfig:
    """Loader config for the SparseLanecpp task."""

    num_classes_lanes_road_edges: int = len(LANE_CATEGORIES) + len(ROAD_EDGE_CATEGORIES) + 1
    num_classes_holistic_ego_lanes: int = len(HEL_CATEGORIES) + 1
    line_representation: LineRepresentation = LineRepresentation.CATMULL_ROM  # Line representation type
    max_lanes: int = 40  # Maximum number of lanes to load
    gt_conf_threshold: float = 0.55  # Ground truth confidence threshold
    use_occlusions: bool = False  # Use occluded line segments in visibility loss
    lane_width: int = 140  # Lane width in mm
    latr_constant: int = 2650  # Lane Transformer
    train_hel: bool = True  # Train Holistic Ego Lanes
    use_fixed_camera_params: bool = False  # Whether to use fixed camera parameters
    cam_height: float = 1.55
    _intrinsics: list[list[float]] = field(
        default_factory=lambda: [[1000.0, 0.0, 960.0], [0.0, 1000.0, 640.0], [0.0, 0.0, 1.0]]
    )
    _top_view_region: list[list[float]] = field(default_factory=lambda: [[-10, 203], [10, 203], [-10, 3], [10, 3]])

    @property
    def num_classes(self) -> int:
        """Get the number of classes for the predicted lanes."""
        # Subtract 1 due to shared background class
        return self.num_classes_lanes_road_edges + self.num_classes_holistic_ego_lanes - 1

    @property
    def anchor_y_steps(self) -> NDArray[np.float32]:
        """Get the anchor y steps for the predicted lanes."""
        return get_anchor_y_steps(self.line_representation)

    @property
    def num_points_per_line(self) -> int:
        """Get the number of points per line."""
        return len(self.anchor_y_steps)

    @property
    def anchor_y_steps_dense(self) -> NDArray[np.float32]:
        """Get the anchor y steps for the predicted lanes."""
        return ANCHOR_Y_STEPS_DENSE

    @property
    def intrinsics(self) -> NDArray[np.float32]:
        """Get the default camera intrinsics."""
        return np.array(self._intrinsics, dtype=np.float32)

    @property
    def top_view_region(self) -> NDArray[np.float32]:
        """Get the top view region."""
        return np.array(self._top_view_region, dtype=np.float32)


@dataclass(kw_only=False)
class SparseLanecppConfig(TaskSpecificConfig):
    """Config parameters for the SparseLanecpp task."""

    camera_data_config: SparseLanecppCameraDataConfig = field(default_factory=SparseLanecppCameraDataConfig)
    loader_config: SparseLanecppLoaderConfig = field(default_factory=SparseLanecppLoaderConfig)
    post_uncertainty_subloss_weights: PostUncertaintySublossWeights = field(
        default_factory=PostUncertaintySublossWeights
    )
    label_set_name: str = "sparse_lanecpp"
    label_set_version: str = "1.0.0"
    embedding_dimension: int = 128
    num_queries_lanes_road_edges: int = 16  # Number of queries for lanes and road edges
    num_queries_hel: int = 4  # Number of queries for Holistic Ego Lanes
    points_as_query: bool = True  # Use points as queries
    with_iterative_refinement: bool = False  # If true each decoder layer predicts relative offsets of control
    #  points, if false each decoder layer predicts global coordinates.
    enlarge_length = 20  # Lane Transformer proposal to enlarge position range
    visu_threshold: float = 0.3
    eval_foreground_threshold: float = 0.3
    num_validation_images: int = 800  # not nessesary but better to have more than num workers * batch size
    attention_nonlinearity_function: str = "relu"

    @property
    def num_queries(self) -> int:
        """Get the total number of queries."""
        return self.num_queries_hel + self.num_queries_lanes_road_edges

    @property
    def position_range(self) -> VolumeRange:
        """Get the position range for the predicted lanes."""
        # Enlarge the position range to avoid clipping of predicted lanes
        # in the top view region.
        return VolumeRange(
            float(self.loader_config.top_view_region[0, 0]) - self.enlarge_length,
            float(self.loader_config.top_view_region[2, 1]) - self.enlarge_length,
            -10.0,
            float(self.loader_config.top_view_region[1, 0]) + self.enlarge_length,
            float(self.loader_config.top_view_region[0, 1]) + self.enlarge_length,
            10.0,
        )

    @property
    def attention_nonlinearity(self) -> AttentionNonlinearityType:
        """Validate and cast attention_nonlinearity to allowed literal values."""
        try:
            return cast(AttentionNonlinearityType, self.attention_nonlinearity_function)
        except ValueError as e:
            msg = (
                f"Invalid attention_nonlinearity_function: {self.attention_nonlinearity_function}. "
                f"Allowed values are: {AttentionNonlinearityType.__args__}."
            )
            raise ValueError(msg) from e


@dataclass(kw_only=True)
class SparseLanecppInstanceHeadOutput:
    """Data-class holding the output of the SparseLanecpp sparse instance head."""

    scores: Tensor
    logits: Tensor
    masks: Tensor

    def __iter__(self) -> Iterator[tuple[str, Tensor]]:
        """Iterator over tuples of (field name, field value)."""
        return ((field.name, getattr(self, field.name)) for field in fields(self))


@dataclass
class SparseLanecppHeadRawOutput:
    """Data-class holding the raw output of the raw SparseLanecppHead during inference."""

    class_scores: Tensor
    lane_preds_unscaled: Tensor

    def __iter__(self) -> Iterator[tuple[str, Tensor]]:
        """Iterator over tuples of (field name, field value)."""
        return ((field.name, getattr(self, field.name)) for field in fields(self))


@dataclass
class SparseLanecppHeadScaledOutput:
    """Data-class holding the raw output of the scaled SparseLanecppHead."""

    lanes_x: Tensor
    lanes_z: Tensor
    lanes_visibility: Tensor

    def __iter__(self) -> Iterator[tuple[str, Tensor]]:
        """Iterator over tuples of (field name, field value)."""
        return ((field.name, getattr(self, field.name)) for field in fields(self))


@dataclass(kw_only=True)
class SparseLanecppHeadOutput:
    """Data-class holding all outputs of the SparseLanecppHead."""

    raw_output: SparseLanecppHeadRawOutput
    scaled_output: SparseLanecppHeadScaledOutput | None = None
    instance_output: SparseLanecppInstanceHeadOutput | None = None
    lanes_dense: Tensor | None = None

    def __iter__(self) -> Iterator[tuple[str, Tensor]]:
        """Iterator over tuples of (field name, field value)."""
        yield from ((f"raw_output_{n}", v) for n, v in self.raw_output)
        if self.scaled_output is not None:
            yield from ((f"scaled_output{n}", v) for n, v in self.scaled_output)
        if self.instance_output is not None:
            yield from ((f"instance_output{n}", v) for n, v in self.instance_output)
        if self.lanes_dense is not None:
            yield "lanes_dense", self.lanes_dense


class SparseLanecppGroundTruth(NamedTuple):
    """SparseLanecppGroundTruth as expected by the loss function."""

    gt_id: Tensor
    lanes_dense: Tensor
    lane_category: Tensor
    ego_pose: Tensor
    ego_pose_inv: Tensor
    segmentation_idx: Tensor
