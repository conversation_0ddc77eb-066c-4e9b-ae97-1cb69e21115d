"""Contains the prediction saver for the SparseLaneCPP task."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path
from typing import Any, TypedDict

import numpy as np
import orjson
from pytorch_lightning import LightningModule, Trainer
from pytorch_lightning.utilities.types import STEP_OUTPUT
from torch import Tensor

from data_formats.camera.definitions import CameraParameters
from data_formats.lane_3d.mpc4_loader import ROTATION_VEHICLE_FRONT_TO_GROUND
from xcommon.training.callbacks.savers.utils.common import decode_identifier
from xcontract.camera_models.definitions import CameraModelType
from xcontract.data.definitions.usage import ValueKey
from xtorch.callbacks.savers.prediction_saver import PredictionSaver
from xtorch.training import Stage
from xtorch.training.training_module import StepOutputKey
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppHeadOutput,
    SparseLanecppHeadRawOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.lane_postprocessing import (
    PostprocessedPreds,
    post_processing_preds,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_decoder import SparseLanecppLineDecoder


class LaneJsonContent(TypedDict):
    """Defines how lanes are stored in the json file."""

    category: int
    confidence: float
    line_3d: list[float]
    visibility: list[int]


class IntrinsicParametersJsonContent(TypedDict):
    """Defines how the intrinsic camera parameters are stored in the json file."""

    camera_model_type: str
    pp_u: float
    pp_v: float
    fl_u: float
    fl_v: float


class CameraParametersJSONContent(TypedDict):
    """Defines how the camera parameters are stored in the json file."""

    intrinsic: IntrinsicParametersJsonContent
    extrinsic: list[list[float]]


class SparseLanecppPredictionSaver(PredictionSaver):
    """Saves SparseLanecpp predictions into a json file."""

    def __init__(  # noqa: PLR0913
        self,
        task_id: str,
        evaluation_folder: str,
        input_key: str,
        line_decoder: SparseLanecppLineDecoder,
        camera_parameter_task_id: str | None,
        number_of_samples_saved: int = 2000,
        dataset_prefix: str = "",
        *,
        delete_predictions_afterwards: bool = True,
        save_sample_predictions: bool = True,
        save_raw_predictions: bool = False,
    ) -> None:
        """Initializes the Lane3DPredictionSaver.

        Args:
            task_id: Id of the task.
            evaluation_folder: Folder in which the prediction and dataset files are saved.
            input_key: The key of the input in the EvaluatorState.
            line_decoder: The line decoder used for decoding the lane predictions.
            camera_parameter_task_id: The task id used to access the camera parameters in the batch.
            number_of_samples_saved: Total number of files saved if save_sample_predictions is True,
            dataset_prefix: String that can be used to tell apart subsamples of datasets for the same task_id.
            delete_predictions_afterwards: If True predictions will be deleted on evaluation end.
            save_sample_predictions: If true sample predictions are saved to evaluation_folder.
            save_raw_predictions: If true also save raw predictions within the sample predictions.
        """
        super().__init__(
            task_id,
            evaluation_folder,
            delete_predictions_afterwards=delete_predictions_afterwards,
            save_sample_predictions=save_sample_predictions,
            number_of_samples_saved=number_of_samples_saved,
            dataset_prefix=dataset_prefix,
        )
        self._input_key = input_key
        self._line_decoder = line_decoder
        self._camera_parameter_task_id = camera_parameter_task_id
        self.save_raw_predictions = save_raw_predictions

    def on_validation_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: STEP_OUTPUT,
        batch: Any,
        batch_idx: int,
        dataloader_idx: int = 0,
    ) -> None:
        """Saves the predictions of the SparseLaneCPP task during validation steps."""
        return self.save_predictions(trainer, outputs, batch, Stage.VALIDATE)

    def on_predict_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: STEP_OUTPUT,
        batch: Any,
        batch_idx: int,
        dataloader_idx: int = 0,
    ) -> None:
        """Saves the predictions of the SparseLaneCPP task during prediction steps."""
        return self.save_predictions(trainer, outputs, batch, Stage.PREDICT)

    def save_predictions(
        self,
        trainer: Trainer,
        outputs: STEP_OUTPUT,
        batch: Any,
        stage: Stage,
    ) -> None:
        """Saves the predictions of the SparseLaneCPP task and writes them to json in raw format.

        Args:
            trainer: The PyTorch Lightning trainer.
            outputs: The outputs of the model.
            batch: The inputs provided to the model.
            stage: The experiment stage that is executed.
        """
        if self._task_id not in batch:
            return

        valid_flags = batch[self._task_id][ValueKey.VALID].cpu().numpy()
        if not np.any(valid_flags):
            return

        if not isinstance(outputs, dict):
            msg = f"Outputs are expected to be a dict, but are {type(outputs)}."
            raise TypeError(msg)

        if "qnn" in outputs:
            raw_output = SparseLanecppHeadRawOutput(
                class_scores=outputs["qnn"]["sparse_lanecpp_raw_output_class_scores"],
                lane_preds_unscaled=outputs["qnn"]["sparse_lanecpp_raw_output_lane_preds_unscaled"],
            )
            predictions = SparseLanecppHeadOutput(raw_output=raw_output)
        else:
            if StepOutputKey.PREDICTIONS in outputs:
                output_key = StepOutputKey.PREDICTIONS
            elif "torch" in outputs:
                output_key = "torch"
            else:
                msg = "Unspported output_key!"
                raise ValueError(msg)
            predictions: SparseLanecppHeadOutput = outputs[output_key][self._task_id]
            raw_output = _extract_raw_output(predictions)

        post_processed_preds = _extract_lane_output(predictions, valid_flags, self._line_decoder)
        gt_paths = batch[self._task_id][ValueKey.IDENTIFIER][valid_flags]
        img_paths = batch[self._input_key][ValueKey.IDENTIFIER][valid_flags]
        for batch_idx, (img_path, gt_path, postprocessed_pred) in enumerate(
            zip(img_paths, gt_paths, post_processed_preds)
        ):
            # Convert paths from byte to string
            image_path, _ = decode_identifier(img_path)
            gt_path_decoded, _ = decode_identifier(gt_path)

            # Create output filename from image file name (hash)
            output_filename = str(Path(image_path).with_suffix(".json").name)
            prediction_path = self._get_epoch_prediction_folder(stage=stage, trainer=trainer)
            pred_save_path = prediction_path / output_filename
            pred_save_path.parent.mkdir(parents=True, exist_ok=True)

            # Save predictions to json file
            content = {}
            camera_parameters = _camera_parameters_to_json(self._camera_parameter_task_id, batch, batch_idx)
            if camera_parameters is not None:
                content.update(camera_parameters)
            content["lane_lines"] = _lane_preds_to_json(postprocessed_pred)
            if self.save_raw_predictions:
                content["raw_lanes"] = {key: value[batch_idx].cpu().numpy() for key, value in raw_output}

            with pred_save_path.open("ab") as pred_file:
                pred_file.write(
                    orjson.dumps(
                        content,
                        option=orjson.OPT_SERIALIZE_NUMPY | orjson.OPT_INDENT_2,
                    )
                )

            self._append_data_identifiers_to_dataset_files(pred_save_path, gt_path_decoded, input_data_path=image_path)


def _camera_parameters_to_json(
    camera_parameter_task_id: str | None,
    batch: Any,
    batch_idx: int,
) -> CameraParametersJSONContent | None:
    """Formats the camera parameters into a json content dict.

    Args:
        camera_parameter_task_id: The task id used to access the camera parameters in the batch.
        batch: The batch containing the camera parameters.
        batch_idx: The index of the batch element.
    """
    if camera_parameter_task_id is not None:
        try:
            camera_parameters: CameraParameters = batch[camera_parameter_task_id][ValueKey.DATA]
            principal_point = camera_parameters.principal_point[batch_idx].cpu().numpy()
            focal_length = camera_parameters.focal_length[batch_idx].cpu().numpy()
            # Rotate extrinsics back from vehicle front (predictions) to ground (labels)
            extrinsics = np.matmul(
                camera_parameters.extrinsics[batch_idx].cpu().numpy(), ROTATION_VEHICLE_FRONT_TO_GROUND
            )
            return {
                "intrinsic": {
                    "camera_model_type": CameraModelType(
                        camera_parameters.camera_model_type[batch_idx].cpu().numpy()
                    ).name.lower(),
                    "pp_u": principal_point[0],
                    "pp_v": principal_point[1],
                    "fl_u": focal_length[0],
                    "fl_v": focal_length[1],
                },
                "extrinsic": extrinsics,
            }
        except KeyError:
            # If the camera parameters are not available in the batch, return None
            return None
    return None


def _extract_raw_output(
    lane_output: SparseLanecppHeadOutput,
) -> SparseLanecppHeadRawOutput:
    """Extracts the raw output from the lane output.

    Args:
        lane_output: Output of the lane head.

    Returns: The raw output of the lane predictions.
    """
    raw_output = lane_output.raw_output

    # Dense lanes are available during training and online validation but not during inference
    if lane_output.lanes_dense is not None:
        raw_output = SparseLanecppHeadRawOutput(
            class_scores=lane_output.raw_output.class_scores[-1],
            lane_preds_unscaled=lane_output.raw_output.lane_preds_unscaled[-1],
        )

    return raw_output


def _extract_lane_output(
    lane_output: SparseLanecppHeadOutput,
    valid_flags: Tensor,
    line_decoder: SparseLanecppLineDecoder,
) -> list[PostprocessedPreds]:
    """Extracts the lane predictions, filters them for valid entries and applies postprocessing.

    Args:
        lane_output: The lane output to be prepared.
        valid_flags: The valid flags indicating which samples are valid.
        line_decoder: The line decoder used for decoding the lane predictions.

    Returns: The postprocessed lane predictions.
    """
    # Dense lanes are available during training and online validation but not during inference
    if lane_output.lanes_dense is not None:
        lanes_category = lane_output.raw_output.class_scores[:, valid_flags]
        lanes_dense = lane_output.lanes_dense[:, valid_flags]
    else:
        # During inference, only the last transformer layer is available
        lanes_category = lane_output.raw_output.class_scores[valid_flags][None, ...]
        line_preds_unscaled = lane_output.raw_output.lane_preds_unscaled[valid_flags][None, ...]
        lanes_xz = line_preds_unscaled[..., :2]
        lanes_visibility = line_preds_unscaled[..., 2:]
        _, _, _, lanes_dense = line_decoder(lanes_xz, lanes_visibility, batch_size=lanes_category.shape[1])

    assert lanes_category.dim() == lanes_dense.dim(), "lanes_category and lanes_dense must have same rank!"
    return post_processing_preds(
        all_cls_scores=lanes_category, all_line_preds=lanes_dense, extrinsics=ROTATION_VEHICLE_FRONT_TO_GROUND
    )


def _lane_preds_to_json(postprocessed_pred: PostprocessedPreds) -> list[LaneJsonContent]:
    """Formats the lane predictions into a json content dictionary.

    Args:
        postprocessed_pred: The postprocessed lane predictions of a single batch element.
        file: The json file to write the predictions to.
    """
    return [
        {
            "category": int(np.argmax(postprocessed_pred.pred_lanes_prob[lane_idx])),
            "confidence": float(np.max(postprocessed_pred.pred_lanes_prob[lane_idx])),
            "line_3d": postprocessed_pred.pred_lanes[lane_idx].tolist(),
            "visibility": postprocessed_pred.pred_lanes_vis[lane_idx].astype(int).tolist(),
        }
        for lane_idx in range(len(postprocessed_pred.pred_lanes))
    ]
