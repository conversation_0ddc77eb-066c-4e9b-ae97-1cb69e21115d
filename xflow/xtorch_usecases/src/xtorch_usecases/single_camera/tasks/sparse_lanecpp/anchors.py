"""Anchor definitions for SparseLanecpp."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


from typing import Final

import numpy as np
from numpy.typing import NDArray

from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_representation.splines import LineRepresentation

# Configuration for Lane representation
_NUM_Y_STEPS: Final = 28  # Results in total 30 points with 3rd order splines for efficient QNN inference.
# Determines where regression and visibility loss is evaluated.
NUM_Y_STEPS_DENSE: Final = 300

MIN_Y: Final = 3.0  # Minimum y coordinate for the lane anchor points.
MAX_Y: Final = 203.0  # Maximum y coordinate for the lane anchor points.

# Configuration for Lane representation
ANCHOR_Y_STEPS = np.linspace(
    MIN_Y, MAX_Y, _NUM_Y_STEPS, dtype=np.float32
)  # Fixed positions in y-direction of queries / anchor points.

# Fixed positions in y-direction of line labels.
ANCHOR_Y_STEPS_DENSE = np.linspace(MIN_Y, MAX_Y, NUM_Y_STEPS_DENSE, dtype=np.float32)


def get_anchor_y_steps(line_representation: LineRepresentation) -> NDArray[np.float32]:
    """Get the anchor y steps based on the line representation."""

    if line_representation in [LineRepresentation.B_SPLINE_DEG_3, LineRepresentation.CATMULL_ROM]:
        # Add extra anchor at beginning and end for higher degree splines
        step_size = (ANCHOR_Y_STEPS[-1] - ANCHOR_Y_STEPS[0]) / (_NUM_Y_STEPS - 1)
        return np.concatenate(
            (np.array([ANCHOR_Y_STEPS[0] - step_size]), ANCHOR_Y_STEPS, np.array([ANCHOR_Y_STEPS[-1] + step_size]))
        )

    return ANCHOR_Y_STEPS
