"""Sparse LaneCPP Postprocessing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

# Copyright (c) 2022 The PersFormer Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# Modified by Robert Bosch GmbH and Cariad SE

from typing import Any, NamedTuple, TypeAlias, cast

import numpy as np
import torch
from numpy.typing import NDArray

from data_formats.lane_3d.lane_processing import get_start_end_ind
from xcontract.data.definitions.usage import ValueKey
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.anchors import ANCHOR_Y_STEPS_DENSE, NUM_Y_STEPS_DENSE
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_decoder import SparseLanecppLineDecoder

# Type aliases
LaneArray: TypeAlias = NDArray[np.float32]
ClassArray: TypeAlias = NDArray[np.float32]
VisibilityMaskArray: TypeAlias = NDArray[np.bool_]

LaneList: TypeAlias = list[LaneArray]
ClassList: TypeAlias = list[ClassArray] | list[float]
VisibilityMaskList: TypeAlias = list[VisibilityMaskArray]


class PostprocessedGTs(NamedTuple):
    """Postprocessed gts."""

    gt_lanes_coord: list[NDArray[np.float32]]
    gt_lanes_class: list[int]
    gt_lanes_vis: list[NDArray[np.bool_]]


class PostprocessedPreds(NamedTuple):
    """Postprocessed predictions."""

    pred_lanes: list[NDArray[np.float32]]
    pred_lanes_prob: list[NDArray[np.float32]]
    pred_lanes_vis: list[NDArray[np.bool_]]


def postprocess(
    lanes_in: LaneArray,
    cls_in: ClassArray,
    anchor_y_steps_dense: NDArray[np.float32],
    num_y_steps_dense: int,
    extrinsics: NDArray[np.float32] | None = None,
) -> tuple[
    LaneList,
    ClassList,
    VisibilityMaskList,
]:
    """Post-process the lane predictions to filter out invalid points and format them for further evaluation.

    Args:
        lanes_in: Input lane predictions with shape (N, 3 * num_y_steps_dense).
        cls_in: Classification scores for each lane or category of each lane.
        anchor_y_steps_dense: Dense y-steps used for interpolation.
        num_y_steps_dense: Number of dense y-steps.
        extrinsics: Optional camera extrinsics to apply to the lane predictions.

    Returns:
        tuple: A tuple containing:
            - lanes_out: Processed lanes as numpy arrays.
            - cls_out: Processed classification scores as numpy arrays.
            - vis_out: Processed visibility information as numpy arrays.
    """
    lanes_out = []
    cls_out = []
    vis_out = []

    if lanes_in.shape[0]:
        xs_dense = lanes_in[:, 0:num_y_steps_dense]
        ys_dense = np.tile(anchor_y_steps_dense.copy(), (xs_dense.shape[0], 1))
        zs_dense = lanes_in[:, num_y_steps_dense : 2 * num_y_steps_dense]
        vis_dense = lanes_in[:, 2 * num_y_steps_dense :]

        for batch_idx in range(lanes_in.shape[0]):
            cur_vis = vis_dense[batch_idx] > 0
            start_ind, end_ind = get_start_end_ind(cur_vis)
            cur_xs = xs_dense[batch_idx][start_ind : end_ind + 1]
            cur_ys = ys_dense[batch_idx][start_ind : end_ind + 1]
            cur_zs = zs_dense[batch_idx][start_ind : end_ind + 1]
            cur_vis = cur_vis[start_ind : end_ind + 1]

            # Check if the lane is less than two visible points
            if cur_vis.sum() < 2:
                continue

            lane_out = np.array([cur_xs, cur_ys, cur_zs]).T  # (N, 3)
            if extrinsics is not None:
                lane_out = np.hstack((lane_out, np.ones((lane_out.shape[0], 1))))  # (N, 4)
                lane_out = np.matmul(lane_out, extrinsics)
                lane_out = lane_out[:, :3]  # (N, 3)

            lanes_out.append(lane_out)
            cls_out.append(cls_in[batch_idx])  # Keep as numpy array
            vis_out.append(cur_vis)

    return lanes_out, cls_out, vis_out


def post_processing_preds(
    all_cls_scores: torch.Tensor, all_line_preds: torch.Tensor | None, extrinsics: NDArray[np.float32] | None = None
) -> list[PostprocessedPreds]:
    """Handles post processing of predictions for visualization and metrics calculation.

    Args:
        all_cls_scores: Classification scores for each lane (num_transformer_out, batch_size, N, num_class).
        all_line_preds: Input lane predictions with shape (num_transformer_out, batch_size, N, 3 * NUM_Y_STEPS_DENSE).
        extrinsics: Optional camera extrinsics to apply to the lane predictions.

    Returns:
        A list of PostprocessedPreds, each containing:
            - pred_lanes: Processed lanes as numpy arrays.
            - pred_lanes_prob: Processed classification scores as numpy arrays.
            - pred_lanes_vis: Processed visibility information as numpy arrays.
    """
    if all_line_preds is None:
        error_message = "all_line_preds should be filled!"
        raise ValueError(error_message)
    pred_lines_sub = []
    all_line_preds = all_line_preds[-1]
    all_cls_scores = all_cls_scores[-1]
    batch_size = all_cls_scores.shape[0]

    # Write results
    for batch_idx in range(batch_size):
        # Process predictions
        lane_pred_np = all_line_preds[batch_idx].detach().cpu().to(torch.float32).numpy()
        cls_pred = torch.argmax(all_cls_scores[batch_idx], dim=-1)
        pos_lanes_np = lane_pred_np[cls_pred.detach().cpu().to(torch.float32).numpy() > 0]
        scores_pred_np = torch.softmax(all_cls_scores[batch_idx][cls_pred > 0], dim=-1).detach().cpu().numpy()

        lanes_pred, lanes_prob, vis_pred = postprocess(
            lanes_in=pos_lanes_np,
            cls_in=scores_pred_np,
            anchor_y_steps_dense=ANCHOR_Y_STEPS_DENSE,
            num_y_steps_dense=NUM_Y_STEPS_DENSE,
            extrinsics=extrinsics,
        )

        lanes_prob = cast(list[NDArray[np.float32]], lanes_prob)
        processed_prediction = PostprocessedPreds(
            pred_lanes=lanes_pred, pred_lanes_prob=lanes_prob, pred_lanes_vis=vis_pred
        )
        pred_lines_sub.append(processed_prediction)

    return pred_lines_sub


def post_processing_gts(all_cls_gt: torch.Tensor, all_line_gt: torch.Tensor) -> list[PostprocessedGTs]:
    """Handles post processing of gts for visulization and metrices calculation.

    Args:
        all_cls_gt: Classification onehot gt for each lane (batch_size, N, num_class).
        all_line_gt: Input lane gt with shape (batch_size, N, 3 * NUM_Y_STEPS_DENSE).

    Returns:
        A list of PostprocessedGTs, each containing:
            - gt_lanes_coord: Processed lanes as numpy arrays.
            - gt_lanes_class: Processed classification scores as numpy arrays.
            - gt_lanes_vis: Processed visibility information as numpy arrays.
    """
    gt_lines_sub = []
    # last element is the track ID
    all_cls_gt = all_cls_gt[:, :, :-1]
    batch_size = all_cls_gt.shape[0]

    # Write results
    for batch_idx in range(batch_size):
        # Process GTs
        lane_gt_np = all_line_gt[batch_idx].detach().cpu().to(torch.float32).numpy()
        cls_gt_np = torch.argmax(all_cls_gt[batch_idx], dim=-1).detach().cpu().to(torch.float32).numpy()
        pos_lanes = lane_gt_np[cls_gt_np > 0]
        cls_in_gt = cls_gt_np[cls_gt_np > 0]

        lanes_coord, lanes_class, vis_gt = postprocess(
            lanes_in=pos_lanes,
            cls_in=cls_in_gt,
            anchor_y_steps_dense=ANCHOR_Y_STEPS_DENSE,
            num_y_steps_dense=NUM_Y_STEPS_DENSE,
        )

        lanes_class = [int(lane_cls) for lane_cls in lanes_class]
        processed_gt = PostprocessedGTs(gt_lanes_coord=lanes_coord, gt_lanes_class=lanes_class, gt_lanes_vis=vis_gt)
        gt_lines_sub.append(processed_gt)

    return gt_lines_sub


def handle_predictions(network_out: dict[str, Any], task_id: str) -> list[PostprocessedPreds]:
    """Extracts the Lane-Predictions from the network output and processes them. We use this function for metrics.

    Args:
        network_out: Classification onehot gt for each lane (batch_size, N, num_class).
        task_id: The task ID of the SparseLanecpp task.

    Returns:
        A list of PostprocessedPreds, each containing:
            - pred_lanes: Processed lanes as numpy arrays.
            - pred_lanes_prob: Processed classification scores as numpy arrays.
            - pred_lanes_vis: Processed visibility information as numpy arrays.
    """
    assert isinstance(network_out[task_id], SparseLanecppHeadOutput)
    all_cls_scores = network_out[task_id].raw_output.class_scores
    all_line_preds = network_out[task_id].lanes_dense
    return post_processing_preds(all_cls_scores, all_line_preds)


def handle_inference_outputs(
    network_out: dict[str, Any], line_decoder: SparseLanecppLineDecoder
) -> list[PostprocessedPreds]:
    """Extracts the Lane-Predictions from the network output when in inference mode.

    Args:
        network_out: Classification onehot gt for each lane (batch_size, N, num_class).
        line_decoder: The line decoder used to decode the lane predictions.

    Returns:
        A list of PostprocessedPreds, each containing:
            - pred_lanes: Processed lanes as numpy arrays.
            - pred_lanes_prob: Processed classification scores as numpy arrays.
            - pred_lanes_vis: Processed visibility information as numpy arrays.
    """
    cls_scores = network_out["sparse_lanecpp_raw_output_class_scores"]
    line_preds_unscaled = network_out["sparse_lanecpp_raw_output_lane_preds_unscaled"]
    batch_size = cls_scores.shape[0]

    all_cls_scores = cls_scores[None, ...]
    all_line_preds_orig, all_lines_vis = line_preds_unscaled[None, ..., :2], line_preds_unscaled[None, ..., 2:]
    _, _, _, all_line_preds = line_decoder(all_line_preds_orig, all_lines_vis, batch_size=batch_size)
    return post_processing_preds(all_cls_scores, all_line_preds)


def handle_gts(gt_labels: OnDeviceBatchedTrainingDict, task_id: str) -> list[PostprocessedGTs]:
    """Extracts the Lane-ground truth from the TrainingDict and processes them. We use this function for metrics.

    Args:
        gt_labels: gt batch to be post-processed, filled with `torch.Tensor` objects instead of NumPy arrays.
        task_id: The task ID of the SparseLanecpp task.

    Returns:
        A list of PostprocessedGTs, each containing:
            - gt_lanes_coord: Processed lanes as numpy arrays.
            - gt_lanes_class: Processed classification scores as numpy arrays.
            - gt_lanes_vis: Processed visibility information as numpy arrays.
    """
    gt_labels_data = cast(SparseLanecppGroundTruth, gt_labels[task_id][ValueKey.DATA])
    return post_processing_gts(all_cls_gt=gt_labels_data.lane_category, all_line_gt=gt_labels_data.lanes_dense)
