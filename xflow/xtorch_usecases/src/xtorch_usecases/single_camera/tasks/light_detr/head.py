"""Module for defining the LightHead class used for light detection tasks."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import math
from collections.abc import Callable, Sequence

import numpy.typing as npt
import torch
import torch.nn.functional as F
from torch import nn
from torchvision.ops import box_convert

from data_formats.light.light_classes import LightClasses
from xtorch.multi_task.structured_task import StructuredTaskHead
from xtorch.nn.blocks.ffnn import FFNN
from xtorch.nn.encoders.positional import MaskedSinePositionalEncoding2D
from xtorch.nn.heads.object_detection.deformable_transformer import DeformableTransformer
from xtorch_usecases.single_camera.config.model.input_configs import HW
from xtorch_usecases.single_camera.network import ModelInputType
from xtorch_usecases.single_camera.tasks.light_detr.definitions import (
    LightHeadInput,
    LightHeadOutput,
)


class LightHead(StructuredTaskHead[LightHeadInput, ModelInputType, LightHeadOutput]):
    """Light detection DETR head."""

    def __init__(  # noqa: PLR0913
        self,
        *,
        in_channels: Sequence[int],
        strides: tuple[int, ...],
        num_classes: int,
        image_size: HW,
        hidden_dim: int = 128,
        num_heads: int = 1,
        num_queries: int = 100,
        num_points: int = 4,
        num_decoder_layers: int = 1,
        view_masks: list[npt.ArrayLike] | None = None,
        pad_functions: Sequence[Callable[[torch.Tensor], torch.Tensor]] | None = None,
        crop_functions: Sequence[Callable[[torch.Tensor], torch.Tensor]] | None = None,
    ) -> None:
        """Initialize the LightHead."""
        super().__init__()
        self._strides = strides
        self._image_size = image_size

        self._hidden_dim = hidden_dim
        assert self._hidden_dim % 2 == 0, "Hidden dimension must be even."

        self._input_proj = nn.ModuleList(
            [
                nn.Sequential(
                    nn.Conv2d(in_dim, hidden_dim, kernel_size=1),
                )
                for in_dim in in_channels
            ]
        )
        self._pos_encoder = MaskedSinePositionalEncoding2D(num_feats=self._hidden_dim // 2)

        self._tgt_embed = nn.Parameter(torch.Tensor(torch.Size((num_queries, hidden_dim))).normal_(0, 1))
        self._refpoint_embed = nn.Parameter(torch.Tensor(torch.Size((num_queries, 2))).normal_(0, 1))
        self._refsize_embed = nn.Parameter(torch.Tensor(torch.Size((num_queries, 2))).zero_())

        self._class_embed = nn.ModuleList(
            [nn.Linear(self._hidden_dim, num_classes + 1) for _ in range(num_decoder_layers)]
        )

        self._bbox_embed = nn.ModuleList(
            [
                nn.Sequential(
                    nn.Linear(self._hidden_dim, self._hidden_dim),
                    nn.ReLU(),
                    nn.Linear(self._hidden_dim, self._hidden_dim),
                    nn.ReLU(),
                    nn.Linear(self._hidden_dim, self._hidden_dim, bias=False),
                    nn.Hardsigmoid(),
                )
                for _ in range(num_decoder_layers)
            ]
        )

        self._reference_points = FFNN(2 * hidden_dim, [hidden_dim], hidden_dim // 2, add_identity=False)

        self._reference_sizes = FFNN(2 * hidden_dim, [hidden_dim], hidden_dim // 2, add_identity=False)

        self._center_embed = nn.ModuleList(
            [nn.Conv2d(self._hidden_dim, 2, kernel_size=1, bias=False) for _ in range(num_decoder_layers)]
        )
        self._size_embed = nn.ModuleList(
            [nn.Conv2d(self._hidden_dim, 2, kernel_size=1, bias=False) for _ in range(num_decoder_layers)]
        )

        self._transformer = DeformableTransformer(
            d_model=self._hidden_dim,
            num_feature_levels=len(self._strides),
            enc_n_points=num_points,
            dec_n_points=num_points,
            dim_feedforward=192,
            num_decoder_layers=num_decoder_layers,
            num_heads=num_heads,
            activation="leaky_relu",
        )

        pad_functions = pad_functions if pad_functions is not None else [lambda x: x for _ in self._strides]
        crop_functions = crop_functions if crop_functions is not None else [lambda x: x for _ in self._strides]

        masks = (
            [torch.from_numpy(msk) for msk in view_masks]
            if view_masks is not None
            else [
                torch.ones((self._image_size.height // stride, self._image_size.width // stride), dtype=torch.float32)
                for stride in self._strides
            ]
        )

        # pad masks to get correct positional embeddings
        masks = [(1 - (pad_fn(mask))).squeeze().bool()[None, :, :] for pad_fn, mask in zip(pad_functions, masks)]
        self._pos_embeds = [
            nn.Parameter((crop_fn(self._pos_encoder(mask))), requires_grad=False)
            for crop_fn, mask in zip(crop_functions, masks)
        ]
        # crop masks again to feature map size
        self._view_masks = [
            nn.Parameter(crop_fn(mask[None, :, :]).squeeze(dim=0), requires_grad=False)
            for crop_fn, mask in zip(crop_functions, masks)
        ]

        # register buffers for masks and positional encodings
        # buffers are not copied to device (see https://github.com/pytorch/pytorch/issues/43815)
        # so we register them as non-trainable parameters
        for idx, mask in enumerate(self._view_masks):
            assert isinstance(mask, torch.Tensor), "Mask must be a tensor."
            self.register_parameter(f"view_mask_{idx}", mask)
        for idx, pos_embed in enumerate(self._pos_embeds):
            assert isinstance(pos_embed, torch.Tensor), "Positional embedding must be a tensor."
            self.register_parameter(f"pos_embed_{idx}", pos_embed)

        self._initalize_weights_and_biases()

    def _initalize_weights_and_biases(self) -> None:
        """Initialize weights and biases for the model."""
        for module in self._input_proj:
            assert isinstance(module, nn.Sequential), "Input proj must be a Sequential module."
            conv = module[0]
            assert isinstance(conv, nn.Conv2d), "Input proj must contain a Conv2d layer."
            assert conv.bias is not None, "Input proj Conv2d layer must have a bias."
            nn.init.xavier_uniform_(conv.weight, gain=1)
            nn.init.constant_(conv.bias.data, 0)

    def forward(self, inputs: LightHeadInput, model_inputs: ModelInputType) -> LightHeadOutput:
        """Forward pass for the light head.

        Args:
            inputs: The input features for the light head.
            model_inputs: The model inputs.
        """

        inp_feature_maps = [getattr(inputs, f"stride_{s}") for s in self._strides]
        assert len(inp_feature_maps) == len(self._strides), "Mismatch between input feature maps and strides."
        feature_maps = [inp_proj(inp) for inp_proj, inp in zip(self._input_proj, inp_feature_maps)]

        bs = feature_maps[0].shape[0]
        reference_points = F.hardsigmoid(torch.cat((self._refpoint_embed, self._refsize_embed), dim=-1)).expand(
            bs, -1, -1
        )

        query_pos = torch.cat(
            (
                self._reference_points(self._sineembed(reference_points)),
                self._reference_sizes(self._sineembed(reference_points)),
            ),
            dim=-1,
        )

        hs, _, inter_ref = self._transformer(
            feature_maps,
            pos_embeds=self._pos_embeds,
            tgt_embed=self._tgt_embed,
            masks=None,  # masks are not used in the light head
            reference_points=reference_points,
            query_pos=query_pos,
        )

        outputs_class = self._class_embed[-1](hs[-1])

        reference_points = inter_ref.split(inter_ref.shape[0], dim=0)[-1].squeeze(dim=0)
        reference_points = (reference_points - 0.5) * 6  # inverse hardsigmoid

        bbox_intermediates = self._bbox_embed[-1](hs[-1])

        centers = self._center_embed[-1](bbox_intermediates.permute(0, 2, 1).unsqueeze(dim=-1))
        centers = centers.squeeze(dim=-1).permute(0, 2, 1)
        centers = centers + reference_points[..., :2]

        sizes = self._size_embed[-1](hs[-1].permute(0, 2, 1).unsqueeze(dim=-1)) / 1e2
        sizes = sizes.squeeze(dim=-1).permute(0, 2, 1)
        sizes = sizes + reference_points[..., 2:]

        device = outputs_class.device

        return LightHeadOutput(
            centers=centers,
            sizes=sizes,
            scores=outputs_class,
            classes=torch.empty(0, device=device),  # fill in postprocessing
            features=torch.empty(0, device=device),
            group_boxes=torch.empty(0, device=device),
            group_scores=torch.empty(0, device=device),
        )

    def light_postprocessing(
        self,
        training_output: LightHeadOutput,
        box_clamp_min: float = 5.0,
        no_object_class_id: int = len(LightClasses),
    ) -> LightHeadOutput:
        """Postprocessing function for the light head.

        Args:
            training_output: The training output of the light head.
            box_clamp_min: Minimum size for the bounding boxes after rescaling.
            no_object_class_id: Class ID to use for 'no object' predictions.

        Returns:
            A LightInferenceOutput instance with filtered and concatenated predictions.
        """

        boxes = torch.cat((training_output.centers, training_output.sizes), dim=-1)
        boxes = F.hardsigmoid(boxes)

        boxes[..., 2:] = (boxes[..., 2:] - 0.5) * 2.0

        absolute_boxes_cxcywh = boxes * torch.tensor(
            (self._image_size.width, self._image_size.height, self._image_size.width, self._image_size.height),
            device=training_output.centers.device,
        )

        # clip boxes to minimum size
        absolute_boxes_cxcywh[..., 2:] = torch.clamp(absolute_boxes_cxcywh[..., 2:], min=box_clamp_min)
        absolute_boxes_xyxy = box_convert(absolute_boxes_cxcywh, "cxcywh", "xyxy")

        probabilities = training_output.scores.softmax(dim=-1)
        classes = probabilities.argmax(dim=-1)

        group_scores = probabilities[..., -2]
        group_scores[classes != LightClasses.DETECTION_GROUP] = 0.0

        probs_without_groups = torch.cat((probabilities[..., :-2], probabilities[..., -1:]), dim=-1)
        classes[classes == LightClasses.DETECTION_GROUP] = no_object_class_id

        return LightHeadOutput(
            centers=absolute_boxes_cxcywh[..., :2],
            sizes=absolute_boxes_cxcywh[..., 2:],
            scores=probs_without_groups,
            classes=classes,
            features=training_output.features,
            group_boxes=absolute_boxes_xyxy,  # resolve inconsistency to centers/sizes later
            group_scores=group_scores,
        )

    def _sineembed(self, pos_tensor: torch.Tensor, temperature: float = 20.0) -> torch.Tensor:
        """Generate sine embeddings for the given position tensor.

        Args:
            pos_tensor: A tensor of shape (batch_size, num_queries, dims) containing the positions.
            temperature: A scaling factor for the positional encoding.

        Returns:
            A tensor of shape (batch_size, num_queries, hidden_dim) containing the sine embeddings.
        """
        scale = 2 * math.pi
        dim_t = torch.arange(self._hidden_dim // 2, dtype=torch.float32, device=pos_tensor.device)
        dim_t = temperature ** (2 * (dim_t // 2) / (self._hidden_dim // 2))

        def embed(single_dim: torch.Tensor) -> torch.Tensor:
            scaled = single_dim * scale
            elem = scaled[:, :, None] / dim_t
            return torch.stack((elem[:, :, 0::2].sin(), elem[:, :, 1::2].cos()), dim=3).flatten(2)

        return torch.cat([embed(pos_tensor[:, :, dim]) for dim in range(pos_tensor.shape[-1])], dim=2)
