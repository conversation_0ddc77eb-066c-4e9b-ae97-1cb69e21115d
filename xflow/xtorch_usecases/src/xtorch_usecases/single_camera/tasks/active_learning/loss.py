"""Active Learning loss dummy implementation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from xtorch.losses.interface import LossOutput
from xtorch.multi_task.structured_task import (
    StructuredTaskLoss,
)
from xtorch_usecases.single_camera.tasks.active_learning.definitions import (
    ActiveLearningGroundTruthDummy,
    ActiveLearningHeadOutput,
)


class ActiveLearningDummyLoss(StructuredTaskLoss[ActiveLearningHeadOutput, ActiveLearningGroundTruthDummy]):
    """Active Learning dummy loss implementation."""

    def __init__(self) -> None:
        """Initialize the active learning loss."""
        super().__init__()

    def forward(self, task_output: ActiveLearningHeadOutput, target: ActiveLearningGroundTruthDummy) -> LossOutput:
        """Return loss of 0 since active learning tasks are not trained.

        Args:
            task_output: The output of the active learning traffic light head.
            target: The ground truth of the active learning traffic light task.
        """
        return LossOutput(
            total_loss=task_output.score * 0.0,
        )
