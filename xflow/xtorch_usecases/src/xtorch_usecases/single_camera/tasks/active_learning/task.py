"""Active Learning tasks (not trained) that can be added after trained task heads."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from functools import cache
from typing import Any, Generic

import numpy as np
import numpy.typing as npt
from pytorch_lightning.callbacks import Callback

from conversion.qnn.onnx_surgery.onnx_graph_surgery_rules import (
    AbstractGraphRewriter,
    OptimizeSumHardEntropy,
    ReattachSumHardEntropy,
    RemoveOutput,
)
from data_formats.definitions import Usage
from pyper.dict_pipeline import TaskProcessingConfig
from xtorch.losses.meta.uncertainty_weighted_loss import SublossConfig
from xtorch.multi_task.structured_task import MetricRegistry, StructuredTask, SupportedData
from xtorch.multi_task.task import HeadInputT_contra
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.config.common_task_config import SingleCameraCommonTaskConfig
from xtorch_usecases.single_camera.definitions import ModelInputType
from xtorch_usecases.single_camera.tasks.active_learning.definitions import (
    ActiveLearningConfig,
    ActiveLearningGroundTruthDummy,
    ActiveLearningHeadOutput,
)
from xtorch_usecases.single_camera.tasks.active_learning.head import (
    ActiveLearningLightSumHardEntropyHead,
    ActiveLearningTrafficLightSumHardEntropyHead,
    ActiveLearningTrafficSignSumHardEntropyHead,
)
from xtorch_usecases.single_camera.tasks.active_learning.loss import (
    ActiveLearningDummyLoss,
)
from xtorch_usecases.single_camera.tasks.light.definitions import LightInferenceOutput
from xtorch_usecases.single_camera.tasks.traffic_light.definitions import TrafficLightHeadOutput
from xtorch_usecases.single_camera.tasks.traffic_sign.definitions import TrafficSignHeadOutput


def _dummy_inflator_and_adjuster(*args: Any, **kwargs: Any) -> tuple[npt.NDArray[np.uint8]]:
    msg = "Actual data loading is not supported for Active Learning at the moment!"
    raise NotImplementedError(msg)


@cache
def _ignorer() -> tuple[npt.NDArray[np.uint8]]:
    return (np.zeros((1,), dtype=np.uint8),)


class ActiveLearningTaskGeneric(
    StructuredTask[
        ActiveLearningConfig,
        SingleCameraCommonTaskConfig,
        HeadInputT_contra,
        ModelInputType,
        ActiveLearningHeadOutput,
        ActiveLearningGroundTruthDummy,
    ],
    Generic[HeadInputT_contra],
):
    """Base implementation for various active learning tasks."""

    def __init__(self, config: ActiveLearningConfig, common_config: SingleCameraCommonTaskConfig) -> None:
        """Initialize the active learning task.

        Args:
            config: The task configuration.
            common_config: Common config shared across tasks.
        """
        self._config = config
        self._callback_config = common_config.callback_config
        self._reference_view_config = common_config.reference_view_config
        self._backbone_output_channels = common_config.backbone_output_channels
        self._multifocal_view_config = common_config.multifocal_view_config

    @property
    def loss(self) -> ActiveLearningDummyLoss:
        """The task is not trainable and therefore does not define any loss."""
        return ActiveLearningDummyLoss()

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """Returns the metrics for the active learning task."""
        return []

    def pyper_processing_config(self, usage: Usage) -> TaskProcessingConfig[ActiveLearningGroundTruthDummy]:
        """Return the Pyper processing configuration for the task."""

        return TaskProcessingConfig(
            task_id=self.identifier(),
            inflator=_dummy_inflator_and_adjuster,
            adjuster=_dummy_inflator_and_adjuster,
            ignorer=_ignorer,
            formatter=lambda labels: ActiveLearningGroundTruthDummy(score=labels[0]),  # type: ignore[reportArgumentType]
        )

    def subloss_configs(self) -> dict[str, SublossConfig]:
        """No subloss configs for this task."""
        return {}

    @property
    def conversion_rewriters(self) -> list[AbstractGraphRewriter]:
        """Conversion rewriters for active learning tasks.

        Optimize sum hard entropy task, remove pre_nms output from tasks again and reattach light trigger task.
        """

        return [
            ReattachSumHardEntropy(
                pattern=r".*model.*active_learning_light_sum_hard_entropy", output_name="light_scores"
            ),
            OptimizeSumHardEntropy(pattern=r".*model.*active_learning_.*_sum_hard_entropy/Mul"),
            RemoveOutput(pattern_list=[r".*trifocal_filtered_pre_nms_class_probs"]),
        ]


class ActiveLearningTrafficLightSumHardEntropyTask(ActiveLearningTaskGeneric[TrafficLightHeadOutput]):
    """Sum hard entropy task for the traffic light objects."""

    def __init__(self, config: ActiveLearningConfig, common_config: SingleCameraCommonTaskConfig) -> None:
        """Initialize the sum hard entropy for the traffic light objects task.

        Args:
            config: The task specific config.
            common_config: Common config shared across tasks.
        """
        super().__init__(config, common_config)

    @property
    def head(self) -> ActiveLearningTrafficLightSumHardEntropyHead:
        """Returns hard entropy head configured for the traffic light objects."""
        return ActiveLearningTrafficLightSumHardEntropyHead()

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Returns the callbacks for the task.

        The current implementation does not support any callbacks.
        """
        return []

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """The task is not trainable and therefore does not define any loss."""
        return []


class ActiveLearningTrafficSignSumHardEntropyTask(ActiveLearningTaskGeneric[TrafficSignHeadOutput]):
    """Sum hard entropy task for the traffic sign objects."""

    def __init__(self, config: ActiveLearningConfig, common_config: SingleCameraCommonTaskConfig) -> None:
        """Initialize the sum hard entropy for the traffic sign objects task.

        Args:
            config: The task specific config.
            common_config: Common config shared across tasks.
        """
        super().__init__(config, common_config)

    @property
    def head(self) -> ActiveLearningTrafficSignSumHardEntropyHead:
        """Returns hard entropy head configured for the traffic sign objects."""
        return ActiveLearningTrafficSignSumHardEntropyHead()

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Returns the callbacks for the task.

        The current implementation does not support any callbacks.
        """
        return []

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """The task is not trainable and therefore does not define any loss."""
        return []


class ActiveLearningLightSumHardEntropyTask(ActiveLearningTaskGeneric[LightInferenceOutput]):
    """Sum hard entropy task for the light objects."""

    def __init__(self, config: ActiveLearningConfig, common_config: SingleCameraCommonTaskConfig) -> None:
        """Initialize the sum hard entropy for the light objects task.

        Args:
            config: The task specific config.
            common_config: Common config shared across tasks.
        """
        super().__init__(config, common_config)

    @property
    def head(self) -> ActiveLearningLightSumHardEntropyHead:
        """Returns hard entropy head configured for the light objects."""
        return ActiveLearningLightSumHardEntropyHead()

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Returns the callbacks for the task.

        The current implementation does not support any callbacks.
        """
        return []
