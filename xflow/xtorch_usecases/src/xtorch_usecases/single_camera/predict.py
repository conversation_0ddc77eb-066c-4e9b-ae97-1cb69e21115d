"""Entrypoint for predict stage for the single camera use case."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from typing import cast

import coloredlogs
import hydra
from omegaconf import DictConfig, OmegaConf
from pytorch_lightning import seed_everything

from xtorch.training import Stage
from xtorch.training.checkpoint import CheckpointFile, StateDictApplicationOptions
from xtorch.training.runner import LightningRunner
from xtorch_extensions.environment import log_environment
from xtorch_usecases.common.checkpoint import construct_absolute_checkpoint_path, select_checkpoint_file
from xtorch_usecases.common.environment import RuntimeEnvironment
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.build_pyper import build_pyper_data_module
from xtorch_usecases.single_camera.callbacks.registry import register_callbacks
from xtorch_usecases.single_camera.config.schema import Config
from xtorch_usecases.single_camera.config.single_camera import SingleCameraConfig, register_single_camera_configs
from xtorch_usecases.single_camera.setup_pipeline_info import setup_pipeline_info
from xtorch_usecases.single_camera.training_module import SingleCameraInference

_LOGGER = logging.getLogger(__name__)

register_callbacks()
register_single_camera_configs()

seed_everything(42, workers=True)


@hydra.main(version_base=None, config_name="fc1_yuv444_multi_task")
def main(raw_config: DictConfig) -> None:
    """Hydra decorated main function for the single camera use case training script."""
    coloredlogs.install(level=logging.INFO)

    _LOGGER.info(f"Config as `DictConfig` object: \n {OmegaConf.to_yaml(raw_config)}")
    config = cast(SingleCameraConfig, OmegaConf.to_object(raw_config)).config
    config.setup_callbacks_for_pipeline_step(PipelineStep.VISUALIZATION_TORCH)

    setup_pipeline_info(config)

    _LOGGER.info(f"Config as `Config` object: \n {OmegaConf.to_yaml(config)}")
    single_camera_predict(config)


def single_camera_predict(config: Config) -> None:
    """Single camera inferencing function.

    Note: Separated from the main function for testing purposes.
    """

    log_environment()

    if config.trainer.default_root_dir is not None:
        config.trainer.default_root_dir.mkdir(parents=True, exist_ok=True)
    assert config.trainer.default_root_dir is not None

    # TODO: Remove once AL is integrated in eval  # noqa: TD003
    config.task_configs.active_learning_light_sum_hard_entropy = None
    config.task_configs.active_learning_traffic_light_sum_hard_entropy = None
    config.task_configs.active_learning_traffic_sign_sum_hard_entropy = None

    LightningRunner(
        is_cloud_run=config.runtime_environment != RuntimeEnvironment.LOCAL,
        # TODO: should we use Stage or PipelineStep?  # noqa: TD003
        training_module=SingleCameraInference(
            config, config.multi_task_collection(PipelineStep.VISUALIZATION_TORCH).enabled_tasks
        ),
        data_module=build_pyper_data_module(
            config=config, pipeline_step=PipelineStep.VISUALIZATION_TORCH, predict=True
        ),
        config=config,
    ).run(
        experiment_name=config.experiment_name,
        stage=Stage.PREDICT,
        pretrained_weights=load_checkpoint(config),
        pretrained_weights_application_options=StateDictApplicationOptions.IGNORE_MISSING_OR_UNEXPECTED_NAMES,
    )


def load_checkpoint(config: Config) -> CheckpointFile | None:
    """Select and return a CheckpointFile."""

    if not config.checkpoint:
        msg = "No checkpoint path provided."
        raise ValueError(msg)

    abs_checkpoint_path = construct_absolute_checkpoint_path(
        checkpoint_path=config.checkpoint,
        base_path=config.input_folder,
    )

    return select_checkpoint_file(checkpoint_path=abs_checkpoint_path)


if __name__ == "__main__":
    # hydra fills the parameter for us with its decorator :)
    main()
