"""Build PyPerDataModule for single camera use case for different usages."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import time
from collections import Counter, defaultdict
from collections.abc import Generator, Iterable, Sequence
from functools import partial
from itertools import chain
from pathlib import Path
from typing import Any, TypeAlias, cast

import pandas as pd
import torch

from pyper.dict_pipeline import OffsetGenerator, PyperLoadable
from pyper.multi_task.dataset_formatter import AbsolutePathFormatter, DatasetSamplesFormatter
from pyper.multi_task.dataset_input import DatasetInputIterable, IterationPolicy
from pyper.multi_task.dataset_processor import ColumnKeeper, ColumnRenamer, UsageDatasetProcessor
from pyper.multi_task.dataset_reader import AmlDatasetReader, CsvDatasetReader, MltableDatasetReader
from pyper.multi_task.dataset_sample import DatasetSample
from pyper.multi_task.dataset_simple_input import SimpleInputIterable
from xcontract.data.definitions.image import FloatHW
from xcontract.data.definitions.inputs import InputImageView
from xcontract.data.definitions.usage import Usage
from xtorch.multi_task.structured_task import IterableTaskOutput, StructuredTask
from xtorch.multi_task.task_config import TaskSpecificConfig
from xtorch.nn.backbones.interface import HasStrides
from xtorch.training.data_module.pyper.data_module import PipelineParams, PyPerDataModule
from xtorch_usecases.common.conversion.data_module import (
    NumpyModelInputDict,
    build_input_pipeline,
)
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.config.camera_parameter_config import (
    CAMERA_PARAMETER_TASK_ID,
    get_camera_parameter_task_processing_config,
)
from xtorch_usecases.single_camera.config.common_task_config import SingleCameraCommonTaskConfig
from xtorch_usecases.single_camera.config.schema import Config, SingleCameraDataModuleConfig
from xtorch_usecases.single_camera.network import ModelInputType
from xtorch_usecases.single_camera.size_modification.pyper.offset import multifocal_qc_offset_generator
from xtorch_usecases.single_camera.tasks.image_processing_configs import (
    get_image_processing_config,
    get_offset_generator,
)

_LOGGER = logging.getLogger(__name__)

StructuredTaskList: TypeAlias = Iterable[
    StructuredTask[
        TaskSpecificConfig, SingleCameraCommonTaskConfig, HasStrides, ModelInputType, IterableTaskOutput, Any
    ]
]


def load_dataset_as_data_frame(
    dataset_base_path: Path | None,
    dataset_name: str,
    dataset_workspace: str | None,
) -> pd.DataFrame:
    """Load a dataset and return it as DataFrame."""

    if dataset_workspace is not None:
        if dataset_base_path is not None and dataset_name.endswith("mltable"):
            dataset = MltableDatasetReader(dataset_base_path / dataset_name.replace("mltable", "")).read()
        else:
            dataset = AmlDatasetReader(workspace_name=dataset_workspace, dataset_name=dataset_name).read()
    elif dataset_base_path is not None:
        if dataset_name.endswith("mltable"):
            dataset = MltableDatasetReader(dataset_base_path / dataset_name.replace("mltable", "")).read()
        else:
            dataset = CsvDatasetReader(dataset_base_path / dataset_name).read()
    else:
        msg = "Either dataset_workspace or dataset_base_path has to be set."
        raise ValueError(msg)

    return dataset


def get_view_to_colum_map(config: Config, pipeline_step: PipelineStep) -> dict[str, str]:
    """Get the mapping from view to column name for the given configuration."""

    def _build_column_name(input_data_id: str | None, view: InputImageView) -> str:
        if not input_data_id:
            msg = "input_data_id must be set."
            raise ValueError(msg)
        if config.is_multifocal(pipeline_step):
            return f"{input_data_id}_{view}"
        return input_data_id

    view_to_dataset_column = {
        view.value: _build_column_name(input_.stored_image_type.value.input_data_id, view)
        for view, input_ in config.inputs[pipeline_step].items()
    }
    return view_to_dataset_column


def get_camera_parameter_task_id_to_colum_map(config: Config) -> dict[str, str]:
    """Get the mapping from task_id to column name for the camera parameters."""
    if config.camera_parameter_inputs is None:
        return {}
    return {config.camera_parameter_inputs.task_id: config.camera_parameter_inputs.source_column}


def build_input_iterable(
    dataset: pd.DataFrame,
    active_tasks: Sequence[str],
    task_id_to_dataset_column: dict[str, str],
    input_ids: list[str],
    target_data_ratio: dict[str, float],
    usage: Usage,
    data_base_path: Path,
    seed: int,
) -> DatasetInputIterable:
    """Build a DatasetInputIterable for the given parameters."""

    # we do usage filtering first as it reduces the time for all following steps
    dataset = UsageDatasetProcessor(dataset, usage).process()

    # Duplicate camera_parameters target column if a single column is also used for task labels
    # TODO: Properly setup camera parameter loading via an additional dataset column and remove this workaround
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/379146/
    if CAMERA_PARAMETER_TASK_ID in task_id_to_dataset_column:
        dataset.loc[:, CAMERA_PARAMETER_TASK_ID] = dataset.loc[:, task_id_to_dataset_column[CAMERA_PARAMETER_TASK_ID]]
        task_id_to_dataset_column = task_id_to_dataset_column.copy()
        task_id_to_dataset_column.pop(CAMERA_PARAMETER_TASK_ID)

    columns_requested_by_multiple_tasks = [
        column for column, count in Counter(task_id_to_dataset_column.values()).items() if count > 1
    ]
    if columns_requested_by_multiple_tasks:
        msg = (
            f"The following column(s) have been requested to be renamed by multiple tasks!"
            f" {columns_requested_by_multiple_tasks}"
        )
        raise ValueError(msg)

    dataset = ColumnRenamer(
        dataset,
        renaming_map={col: task_id for task_id, col in task_id_to_dataset_column.items()},
    ).process()

    dataset = ColumnKeeper(dataset, list(active_tasks)).process()
    dataset = AbsolutePathFormatter(data_base_path.as_posix()).format(dataset)
    samples = DatasetSamplesFormatter([str(id_) for id_ in input_ids]).format(dataset)
    _log_samples_info(samples)

    if usage is Usage.TRAINING:
        _log_effective_data_ratios(samples, target_data_ratio)

        iterable = DatasetInputIterable(
            samples=samples,
            target_data_ratios=target_data_ratio,
            seed=seed,
        )
    else:
        iterable = DatasetInputIterable(
            samples=samples,
            target_data_ratios=dict.fromkeys(target_data_ratio.keys(), 1.0),
            seed=seed,
            iteration_policy=IterationPolicy.NO_REPEAT,
            shuffle_samples=False,
        )

    return iterable


def build_input_iterable_from_config(
    dataset: pd.DataFrame, compatible_task_ids: list[str], config: Config, usage: Usage, pipeline_step: PipelineStep
) -> DatasetInputIterable:
    """Build a DatasetInputIterable for the given configuration and usage.

    Args:
        dataset: A dataframe with all possible samples for all usages.
        compatible_task_ids: A list of identifiers of the tasks listed in compatible_tasks.
        config: The configuration object that is the source for metadata for the `DatasetInputIterable`.
        usage: The usage attribute of the data to be loaded (e.g., training, validation, calibration).
        pipeline_step: The pipeline step for which the `DatasetInputIterable` is built.

    Returns:
        The constructed `DatasetInputIterable` object.
    """

    view_to_dataset_column = get_view_to_colum_map(config=config, pipeline_step=pipeline_step)
    camera_parameter_column = get_camera_parameter_task_id_to_colum_map(config=config)

    active_tasks = config.input_ids + compatible_task_ids
    if config.camera_parameter_inputs is not None:
        active_tasks += [config.camera_parameter_inputs.task_id]

    return build_input_iterable(
        dataset,
        active_tasks=active_tasks,
        task_id_to_dataset_column={
            **view_to_dataset_column,
            **camera_parameter_column,
            **{
                task_id: data_identifier
                for task_id, data_identifier in config.multi_task_collection(
                    pipeline_step
                ).enabled_task_to_data_identifier.items()
                if task_id in compatible_task_ids
            },
        },
        input_ids=[input_id.value for input_id in config.input_ids],
        target_data_ratio={
            task_id: data_weight
            for task_id, data_weight in config.multi_task_collection(pipeline_step).per_task_data_weights.items()
            if task_id in compatible_task_ids and data_weight > 0.0
        },
        usage=usage,
        data_base_path=config.data.data_base_path,
        seed=config.data.data_seed or time.time_ns(),
    )


def build_pipeline_params_from_config(
    config: Config,
    pipeline_step: PipelineStep,
    compatible_task_ids: list[str],
    compatible_tasks: list[PyperLoadable[Any]],
    dataset: pd.DataFrame,
    usage: Usage,
    *,
    debug_mode: bool,
) -> PipelineParams[Any]:
    """Build a PipelineParams object the given configuration.

    Args:
        config: The configuration object that is the source for metadata for the `PipelineParams`.
        pipeline_step: The pipeline step for which the `PipelineParams` is built.
        compatible_task_ids: A list of identifiers of the tasks listed in compatible_tasks.
        compatible_tasks: A list of tasks that implement the `PyperLoadable` interface and are therefore compatible
            with a PyPer data pipeline.
        dataset: A dataframe with all possible samples for all usages.
        usage: The usage attribute of the data to be loaded (e.g., training, validation, calibration).
        debug_mode: Whether to run in debug mode.

    Returns:
        The constructed `PipelineParams` object.
    """

    inputs = config.inputs[pipeline_step]

    processing_configs = [
        get_image_processing_config(
            input_config,
            task_id=view.value,
            pipeline_step=pipeline_step,
        )
        for view, input_config in inputs.items()
    ]
    if config.camera_parameter_inputs is not None:
        processing_configs += [
            get_camera_parameter_task_processing_config(
                config.camera_parameter_inputs,
            )
        ]
    processing_configs += [task.pyper_processing_config(usage) for task in compatible_tasks]

    if config.is_multifocal(pipeline_step):
        prepared_offset_generator = None
        if pipeline_step.is_qnn:
            prepared_offset_generator = cast(
                OffsetGenerator,
                partial(
                    multifocal_qc_offset_generator,
                    input_ids=config.input_ids,
                    relative_offsets=[
                        (
                            FloatHW(
                                getattr(inputs[input_id].target_image_crop, "top", 0.0),
                                getattr(inputs[input_id].target_image_crop, "left", 0.0),
                            )
                        )
                        for input_id in config.input_ids
                    ],
                    target_shapes=[inputs[input_id].target_image_size for input_id in config.input_ids],
                ),
            )
        _LOGGER.info(
            "No offset generator function is used. Assuming loaded image size equals training image size. "
            "Offset generator is only supported for a single input id."
        )
    else:
        prepared_offset_generator = get_offset_generator(
            image_config=inputs[config.input_ids[0]],
            size_provider_task_id=config.input_ids[0],
            pipeline_step=pipeline_step,
            usage=usage,
        )

    target_device = torch.device("cuda") if torch.cuda.is_available() else torch.device("cpu")
    pipeline_params = PipelineParams(
        build_input_iterable_from_config(dataset, compatible_task_ids, config, usage, pipeline_step),
        processing_configs=processing_configs,
        offset_generator=prepared_offset_generator,
        debug=debug_mode,
        target_device=target_device,
        catch_errors_in_step_funcs=config.data.catch_errors_in_step_funcs,
    )

    return pipeline_params


def build_pyper_data_module(
    *,
    config: Config,
    pipeline_step: PipelineStep,
    train: bool = False,
    val: bool = False,
    test: bool = False,
    predict: bool = False,
    debug_mode: bool = False,
) -> PyPerDataModule[SingleCameraDataModuleConfig, Any]:
    """Build a PyPerDataModule for the given configuration and enabled tasks.

    Args:
        config: The configuration object that is the source for metadata for the `PyPerDataModule`.
        pipeline_step: The pipeline step for which the `PyPerDataModule` is built.
        train: If True, the `PyPerDataModule` will have a train data loader, if False, it will raise an `ValueError` on
            access of the train data loader.
        val: If True, the `PyPerDataModule` will have a validation data loader, if False, it will raise an `ValueError`
            on access of the validation data loader.
        test: If True, the `PyPerDataModule` will have a test data loader, if False, it will raise an `ValueError` on
            access of the test data loader.
        predict: If True, the `PyPerDataModule` will have a prediction data loader, if False, it will raise an
            `ValueError` on access of the prediction data loader.
        debug_mode: Whether to run the data pipeline in debug mode. Only do this if you want to debug something locally,
            or you know what you are doing!

    Returns:
        The constructed `PyPerDataModule`.
    """

    if not (train | val | test | predict):
        msg = "At least one of train, val, test or predict must be set to True."
        raise ValueError(msg)

    dataset = load_dataset_as_data_frame(
        dataset_base_path=config.data.dataset_base_path,
        dataset_name=config.data.dataset_name,
        dataset_workspace=config.data.dataset_workspace,
    )

    param_config_to_usage = {}
    if train:
        param_config_to_usage["train_pipeline_params"] = Usage.TRAINING
    if val:
        param_config_to_usage["val_pipeline_params"] = Usage.VALIDATION
    if test:
        param_config_to_usage["test_pipeline_params"] = Usage.TEST
    if predict:
        # TODO: what is the correct usage for this?  # noqa: TD003
        param_config_to_usage["predict_pipeline_params"] = Usage.VALIDATION

    compatible_task_ids, compatible_tasks = zip(
        *[
            (task.identifier(), task)
            for task in config.multi_task_collection(pipeline_step).enabled_tasks
            if isinstance(task, PyperLoadable) and isinstance(task, StructuredTask)
        ]
    )

    param_configs = {
        param_name: build_pipeline_params_from_config(
            config=config,
            pipeline_step=pipeline_step,
            compatible_tasks=list(compatible_tasks),
            compatible_task_ids=list(compatible_task_ids),
            dataset=dataset,
            usage=usage,
            debug_mode=debug_mode,
        )
        for param_name, usage in param_config_to_usage.items()
    }

    pyper_data_module = PyPerDataModule(config=config.data, **param_configs)
    return pyper_data_module


def build_image_sample_iterable(
    dataset: pd.DataFrame,
    view_to_column_map: dict[str, str],
    camera_parameter_to_column_map: dict[str, str],
    columns: Sequence[str],
    usage: Usage,
    data_base_path: Path,
    num_calib_samples: int,
) -> SimpleInputIterable:
    """Build a SimpleInputIterable for the given parameters."""

    # TODO: this is a workaround for the fact that the usage is not set correctly in the dataset.  # noqa: TD003
    if usage == Usage.CALIBRATION:
        usage = Usage.TRAINING
        max_samples = num_calib_samples
    elif usage == Usage.QUANTIZATION_CHECK:
        # for now use validation as quantization check usage
        # in future could change to QUANTIZATION_CHECK split or use the real
        # CALIBRATION split if available
        usage = Usage.VALIDATION  # this is not suffled
        max_samples = 1  # we just take one sample for quantization check
    else:
        max_samples = -1

    dataset = UsageDatasetProcessor(dataset, usage).process()
    dataset = ColumnRenamer(
        dataset,
        renaming_map={col: view for view, col in {**view_to_column_map, **camera_parameter_to_column_map}.items()},
    ).process()
    dataset = ColumnKeeper(dataset, list(columns)).process()
    dataset = AbsolutePathFormatter(data_base_path.as_posix()).format(dataset)
    samples = DatasetSamplesFormatter([str(id_) for id_ in columns]).format(dataset, skip_unlabeled=False)
    iterable = SimpleInputIterable(
        samples=samples[:max_samples],
        seed=123,
        shuffle_samples=False,
    )

    return iterable


def build_pyper_image_data_pipeline(
    config: Config,
    usage: Usage,
    pipeline_step: PipelineStep,
    batch_size: int = 1,
    *,
    debug_mode: bool = False,
) -> Generator[NumpyModelInputDict, None, None]:
    """Build a PyPer image only data pipeline for the given configuration.

    Args:
        config: The configuration object.
        usage: The usage attribute of the data to be loaded (e.g., training, validation, calibration).
        pipeline_step: The pipeline step for which the data pipeline is built.
        batch_size: The batch size for the data.
        debug_mode: Whether to run in debug mode.
    """

    dataset = load_dataset_as_data_frame(
        dataset_base_path=config.data.dataset_base_path,
        dataset_name=config.data.dataset_name,
        dataset_workspace=config.data.dataset_workspace,
    )

    inputs = config.inputs[pipeline_step]

    processing_configs = [
        get_image_processing_config(
            input_config,
            task_id=view.value,
            pipeline_step=pipeline_step,
        )
        for view, input_config in inputs.items()
    ]
    columns = [view.value for view in config.inputs[pipeline_step]]
    if config.camera_parameter_inputs is not None:
        processing_configs += [
            get_camera_parameter_task_processing_config(
                config.camera_parameter_inputs,
            )
        ]
        columns += [config.camera_parameter_inputs.task_id]

    if config.is_multifocal(pipeline_step):
        offset_generator = None
        if pipeline_step.is_qnn:
            offset_generator = cast(
                OffsetGenerator,
                partial(
                    multifocal_qc_offset_generator,
                    input_ids=config.input_ids,
                    relative_offsets=[
                        (
                            FloatHW(
                                getattr(inputs[input_id].target_image_crop, "top", 0.0),
                                getattr(inputs[input_id].target_image_crop, "left", 0.0),
                            )
                        )
                        for input_id in config.input_ids
                    ],
                    target_shapes=[inputs[input_id].target_image_size for input_id in config.input_ids],
                ),
            )
        _LOGGER.info(
            "No offset generator function is used. Assuming loaded image size equals training image size. "
            "Offset generator is only supported for a single input id."
        )
    else:
        offset_generator = get_offset_generator(
            image_config=inputs[config.input_ids[0]],
            size_provider_task_id=config.input_ids[0],
            pipeline_step=pipeline_step,
            usage=usage,
        )

    input_iterable = build_image_sample_iterable(
        dataset=dataset,
        columns=columns,
        usage=usage,
        data_base_path=config.data.data_base_path,
        view_to_column_map=get_view_to_colum_map(config, pipeline_step),
        camera_parameter_to_column_map=get_camera_parameter_task_id_to_colum_map(config=config),
        num_calib_samples=config.convert_config.num_calib_samples,
    )

    data_pipeline = build_input_pipeline(
        input_data=input_iterable,
        processing_configs=processing_configs,
        offset_generator=offset_generator,
        batch_size=batch_size,
        debug=debug_mode,
    )

    return data_pipeline


def _log_samples_info(samples: list[DatasetSample]) -> None:
    """Log info of labels per task in the dataset based on dataset samples.

    Args:
        samples: List of `DatasetSample` instances to analyze.
    """
    _LOGGER.info("Logging infos about a collection of DatasetSample objects:")

    input_keys = set(chain.from_iterable(sample.inputs.keys() for sample in samples))
    label_keys = set(chain.from_iterable(sample.labels.keys() for sample in samples))

    _LOGGER.info(f"Input keys: {input_keys}")
    _LOGGER.info(f"Label keys: {label_keys}")

    shared_sample_mapping = defaultdict(Counter)

    for sample in samples:
        for task in sample.labels:
            shared_sample_mapping[task].update(sample.labels.keys())

    for task, shared_samples in shared_sample_mapping.items():
        _LOGGER.info(f"Task '{task}' has {shared_samples.pop(task)} labels.")
        _LOGGER.info(
            f"Task '{task}' shares {sum(count for count in shared_samples.values())} labels "
            f"with {len(shared_samples)} other tasks: {dict(shared_samples)}"
        )


def _log_effective_data_ratios(
    samples: list[DatasetSample],
    per_task_data_weight: dict[str, float],
) -> None:
    """Log the effective data ratios for each task based on the dataset samples.

    Args:
        samples: List of `DatasetSample` instances to analyze.
        per_task_data_weight: Data weights for each task.
    """
    _LOGGER.info("Logging effective data ratios based on data weights and shared samples.")

    # normalize the data weights
    sum_weights = sum(per_task_data_weight.values())
    task_data_ratios = {task: weight / sum_weights for task, weight in per_task_data_weight.items()}

    _LOGGER.info("Task data weights:")
    for task, weight in per_task_data_weight.items():
        _LOGGER.info(f"'{task}': {weight:.4f}")

    _LOGGER.info("Task data ratios (normalized data weights):")
    for task, ratio in task_data_ratios.items():
        _LOGGER.info(f"'{task}': {ratio:.4f}")

    shared_sample_mapping = defaultdict(Counter)
    task_count = defaultdict(int)

    # Count the number of samples for each task
    # Count the number of shared samples for each task
    for sample in samples:
        for task in sample.labels:
            task_count[task] += 1
            shared_sample_mapping[task].update(sample.labels.keys())

    # Calculate the fractions of shared samples between tasks (if any)
    fractions_shared_samples_per_task = {
        task: {
            shared_task: count / task_count[task]
            for shared_task, count in shared_samples.items()
            if shared_task != task
        }  # exclude self from sharing
        for task, shared_samples in shared_sample_mapping.items()
        if len(shared_samples) > 1  # only consider tasks that share labels with others
    }

    for task, fractions_shared_samples in fractions_shared_samples_per_task.items():
        _LOGGER.info(
            f"For task '{task}' the fractions of shared samples with other tasks are: {dict(fractions_shared_samples)}"
        )

    effective_data_ratio = defaultdict(float)

    # Calculate the effective data ratio for each task
    # NOTE: samples which share labels with other tasks are counted multiple times
    # 1. start with the normalized data ratio
    for current_task, task_ratio in task_data_ratios.items():
        # 2. sum up contributions which arise from shared labels with other tasks
        shared_weight_contribution = 0.0
        for task, task_data_weight in task_data_ratios.items():
            shared_weight_contribution += (
                fractions_shared_samples_per_task.get(current_task, {}).get(task, 0.0) * task_data_weight
            )

        # 3. effective data ratio
        effective_data_ratio[current_task] = task_ratio + shared_weight_contribution

    _LOGGER.info("Effective (normalized) task data ratios (including shared labels):")
    for task, effective_ratio in effective_data_ratio.items():
        _LOGGER.info(f"'{task}': {effective_ratio:.4f}")
    _LOGGER.info(
        f"Sum of effective data ratios: {sum(effective_data_ratio.values())} (can be larger 1.0 due to shared labels)"
    )
