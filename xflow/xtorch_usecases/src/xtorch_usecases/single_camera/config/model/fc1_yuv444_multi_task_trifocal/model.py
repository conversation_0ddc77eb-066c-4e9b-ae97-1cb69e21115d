"""Configuration for FC1 YUV444 multi task trifocal model."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import os
import time
from dataclasses import dataclass, field
from pathlib import Path

from conversion.qnn.definitions import QuantCheckerConfig
from xcontract.training.run_mode import RunMode
from xtorch.nn.backbones.image.yolo_v4_tiny.yolo_v4_tiny import FC1_TRIFOCAL_BACKBONE_PARAMETERS
from xtorch.training.trainer_config import TrainerConfig
from xtorch_extensions.qnn.inference_backend import QNNInferenceConfig, QNNInferenceOutputFormat
from xtorch_usecases.common.helpers import (
    get_env_specific_setting,
    get_gpu_specific_setting,
    get_test_data_folder,
)
from xtorch_usecases.common.pipeline import EVALUATE_EVIL_SUBFOLDER
from xtorch_usecases.single_camera.config.callback_config import CallbackConfig
from xtorch_usecases.single_camera.config.convert_config import ConvertConfig
from xtorch_usecases.single_camera.config.deployment_config import DeploymentConfig
from xtorch_usecases.single_camera.config.eval_config import EvaluateConfig
from xtorch_usecases.single_camera.config.model.input_configs import fc1_yuv444_trifocal_input_config
from xtorch_usecases.single_camera.config.schema import (
    Config,
    SingleCameraDataModuleConfig,
    TaskConfigs,
)
from xtorch_usecases.single_camera.tasks.active_learning import (
    ActiveLearningConfig,
    ActiveLearningLightSumHardEntropyTask,
    ActiveLearningTrafficLightSumHardEntropyTask,
    ActiveLearningTrafficSignSumHardEntropyTask,
)
from xtorch_usecases.single_camera.tasks.blockage import BlockageConfig, BlockageTrifocalTask
from xtorch_usecases.single_camera.tasks.light import LightConfig, LightTask
from xtorch_usecases.single_camera.tasks.pole import PoleConfig, PoleTrifocalTask
from xtorch_usecases.single_camera.tasks.traffic_light import TrafficLightConfig, TrafficLightTrifocalTask
from xtorch_usecases.single_camera.tasks.traffic_sign import TrafficSignConfig, TrafficSignTrifocalTask


def get_multi_task_config() -> Config:
    """Get multi task config."""
    output_folder = get_env_specific_setting(
        local=Path(f"outputs/{time.strftime('%Y-%m-%d/%H-%M-%S')}"), azure=Path("outputs")
    )

    trainer_config = TrainerConfig(
        log_every_n_steps=100,
        check_val_every_n_epoch=None,
        limit_val_batches=0,  # To disable online validation
        default_root_dir=output_folder,
        max_epochs=get_env_specific_setting(local=2, azure=2),
        train_epoch_length=get_env_specific_setting(local=500, azure=30000),
        precision=get_gpu_specific_setting(a100="bf16-mixed", h100="bf16-mixed", default="16-mixed"),
    )

    task_config = TaskConfigs(
        # Since we are using legacy datasets, we need to set data_identifier to "traffic_light" to match the csv file.
        traffic_light_trifocal=TrafficLightConfig(
            data_weight=1, loss_weight=1, data_identifier="traffic_light", active_learning_task=False
        ),
        traffic_sign_trifocal=TrafficSignConfig(
            data_weight=1, loss_weight=2.5, data_identifier="traffic_sign", active_learning_task=False
        ),
        blockage_trifocal=BlockageConfig(data_weight=0.3, loss_weight=1, data_identifier="blockage"),
        pole_trifocal=PoleConfig(data_weight=1.0, loss_weight=1.0, data_identifier="pole"),
        # NOTE: The depth_trifocal field below should be added once a sufficient dataset is in place.
        # depth_trifocal=DepthConfig(data_weight=1, loss_weight=1, uw_log_sigma_init=0, data_identifier="depth"),
        light=LightConfig(
            data_weight=1,
            loss_weight=1,
            data_identifier="light",
            strides=(8, 16, 32),
        ),
        active_learning_light_sum_hard_entropy=ActiveLearningConfig(data_identifier="active_learning_light"),
        active_learning_traffic_light_sum_hard_entropy=ActiveLearningConfig(data_identifier="dsp_al"),
        active_learning_traffic_sign_sum_hard_entropy=ActiveLearningConfig(
            data_identifier="active_learning_traffic_sign"
        ),
    )

    data_config = SingleCameraDataModuleConfig(
        prefetch_factor=4,
        data_base_path=get_env_specific_setting(
            local=get_test_data_folder() / "camera/tasks/",
            azure=lambda: Path(os.environ["AZUREML_DATAREFERENCE_INGEST_CACHE"]),
        ),
        dataset_base_path=get_env_specific_setting(local=get_test_data_folder() / "camera", azure=None),
        dataset_name=get_env_specific_setting(
            local="yuv_single_camera_trifocal_dataset.csv", azure="PaceGeneralYUVTrifocal"
        ),
        dataset_workspace=get_env_specific_setting(local=None, azure="continuous_training"),
        num_workers_train=get_gpu_specific_setting(v100=5, a100=20, h100=30, default=2),
        num_workers_val=get_gpu_specific_setting(v100=5, a100=20, h100=30, default=2),
        batch_size_train=get_gpu_specific_setting(v100=5, a100=20, h100=40, a3000=2, default=2),
        batch_size_val=get_gpu_specific_setting(v100=5, a100=20, h100=40, a3000=2, default=2),
    )

    callback_config = CallbackConfig(
        image_logging_frequency=get_env_specific_setting(local=200, azure=1000),
        output_root_dir=output_folder,
        evaluation_subfolder=EVALUATE_EVIL_SUBFOLDER,
        # NOTE: This differs from output_root_dir in AML pipelines to make ImageCallbacks visible in AML!
        image_root_dir=output_folder / "images",
    )
    qnn_inference_config = QNNInferenceConfig(
        output_format=QNNInferenceOutputFormat.TORCH_LIKE,
        model_name=f"{Path(__file__).parent.name}_eval_qc",
    )
    eval_config = EvaluateConfig(
        qnn=qnn_inference_config,
    )

    deployment_configs = [
        DeploymentConfig(name_suffix="eval_qc"),
        DeploymentConfig(
            name_suffix="short",
            enabled_task_ids=[
                TrafficSignTrifocalTask.identifier(),
                BlockageTrifocalTask.identifier(),
            ],
        ),
        DeploymentConfig(
            name_suffix="long",
            enabled_task_ids=[
                TrafficLightTrifocalTask.identifier(),
                TrafficSignTrifocalTask.identifier(),
                LightTask.identifier(),
                PoleTrifocalTask.identifier(),
            ],
        ),
        DeploymentConfig(
            name_suffix="active_learning",
            enabled_task_ids=[
                TrafficLightTrifocalTask.identifier(),
                ActiveLearningTrafficLightSumHardEntropyTask.identifier(),
                TrafficSignTrifocalTask.identifier(),
                ActiveLearningTrafficSignSumHardEntropyTask.identifier(),
                LightTask.identifier(),
                ActiveLearningLightSumHardEntropyTask.identifier(),
            ],
        ),
    ]

    qnn_quant_check_config = QuantCheckerConfig()
    convert_config = ConvertConfig(quant_check_config=qnn_quant_check_config)

    config = Config(
        trainer=trainer_config,
        backbone_parameters=FC1_TRIFOCAL_BACKBONE_PARAMETERS,
        task_configs=task_config,
        data=data_config,
        inputs=fc1_yuv444_trifocal_input_config(),
        learning_rate=3e-4,
        weight_decay=1e-2,
        callback_config=callback_config,
        eval_config=eval_config,
        output_folder=output_folder,
        deployment_configs=deployment_configs,
        convert_config=convert_config,
    )
    return config


@dataclass
class Fc1Yuv444MultiTaskTrifocalConfig:
    """Wrapper class for hydra."""

    config: Config = field(default_factory=get_multi_task_config)

    def __post_init__(self) -> None:
        """Post init method."""

        # TODO: refactor. One possible long term solution see https://github.com/PACE-INT/xflow/pull/11692/files
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/382057
        if self.config.run_mode == RunMode.SHORT:
            self.config.adapt_to_short_run(disable_online_validation=True)
