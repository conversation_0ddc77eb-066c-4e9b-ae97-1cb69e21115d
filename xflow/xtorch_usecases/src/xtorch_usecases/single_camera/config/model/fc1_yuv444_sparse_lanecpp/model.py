"""Configuration for FC1 YUV444 SparseLanecpp model."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import copy
import os
import time
from dataclasses import dataclass, field
from pathlib import Path

from conversion.qnn.definitions import InputTensorConfig, InputTensorLayout, InputTensorType, QuantCheckerConfig
from xcontract.data.definitions.image import AvailableImageTypes
from xcontract.data.definitions.inputs import InputImageView
from xcontract.data.definitions.usage import Usage
from xcontract.training.run_mode import RunMode
from xtorch.nn.backbones.image.yolo_v4_tiny.yolo_v4_tiny import MONOFOCAL_BACKBONE_PARAMETERS
from xtorch.training.trainer_config import TrainerConfig
from xtorch_extensions.qnn.inference_backend import QNNInferenceConfig
from xtorch_usecases.common.helpers import get_env_specific_setting, get_gpu_specific_setting, get_test_data_folder
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.config.callback_config import CallbackConfig
from xtorch_usecases.single_camera.config.camera_data_config import CameraDataConfig, OffsetStrategyID
from xtorch_usecases.single_camera.config.camera_parameter_config import CameraParameterConfig
from xtorch_usecases.single_camera.config.convert_config import ConvertConfig
from xtorch_usecases.single_camera.config.eval_config import EvaluateConfig
from xtorch_usecases.single_camera.config.schema import (
    Config,
    SingleCameraDataModuleConfig,
    TaskConfigs,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import SparseLanecppConfig
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.task import SparseLanecppTask


def _create_input_tensor_config() -> list[InputTensorConfig]:
    """Create input tensor info for the single camera conversion.

    Returns:
         List of input tensor information for the single camera conversion.
    """
    input_1 = InputTensorConfig(
        name="single_view",
        input_type=InputTensorType.IMAGE,
    )
    input_2 = InputTensorConfig(
        name="focal_length",
        input_type=InputTensorType.DEFAULT,
        input_layout=InputTensorLayout.NONTRIVIAL,
    )
    input_3 = InputTensorConfig(
        name="principal_point",
        input_type=InputTensorType.DEFAULT,
        input_layout=InputTensorLayout.NONTRIVIAL,
    )
    input_4 = InputTensorConfig(
        name="extrinsics",
        input_type=InputTensorType.DEFAULT,
        input_layout=InputTensorLayout.NONTRIVIAL,
    )

    return [input_1, input_2, input_3, input_4]


def get_multi_task_config() -> Config:
    """Get multi task config."""

    output_folder = get_env_specific_setting(
        local=Path(f"outputs/{time.strftime('%Y-%m-%d/%H-%M-%S')}"), azure=Path("outputs")
    )

    trainer_config = TrainerConfig(
        log_every_n_steps=100,
        # TODO: Deactivate this once Evil Evaluation is setup # noqa: TD003
        check_val_every_n_epoch=1,
        limit_val_batches=None,
        default_root_dir=output_folder,
        max_epochs=get_env_specific_setting(local=10, azure=5),
        train_epoch_length=get_env_specific_setting(local=250, azure=26000),
        precision=get_gpu_specific_setting(a100="32-true", h100="32-true", default="32-true"),
    )

    sparse_lanecpp_config = SparseLanecppConfig(data_weight=1, loss_weight=1)
    task_config = TaskConfigs(
        sparse_lanecpp=sparse_lanecpp_config,
    )

    data_config = SingleCameraDataModuleConfig(
        prefetch_factor=2,
        data_base_path=get_env_specific_setting(
            local=get_test_data_folder() / "camera/tasks/sparse_lanecpp/",
            azure=lambda: Path(os.environ["AZUREML_DATAREFERENCE_INGEST_CACHE"]),
        ),
        dataset_base_path=get_env_specific_setting(
            local=get_test_data_folder() / "camera/tasks/sparse_lanecpp/", azure=None
        ),
        # IMPORTANT: if change the dataset name, pls. also change
        # xcommon/src/xcommon/dt_flow/example_dt_flow_configs/pytorch_lane_cpp_eval.sh
        # xcommon/src/xcommon/dt_flow/qc_target_workflow/download_from_azure/pace_dataset_config.json5
        dataset_name=get_env_specific_setting(local="yuv_sparse_lanecpp_dataset.csv", azure="lane3d_autolabel_v6"),
        dataset_workspace=get_env_specific_setting(local=None, azure="viper_lane"),
        num_workers_train=get_gpu_specific_setting(v100=5, a100=20, h100=25, default=2),
        num_workers_val=get_gpu_specific_setting(v100=5, a100=20, h100=25, default=1),
        batch_size_train=get_gpu_specific_setting(v100=16, a100=32, h100=32, default=8),
        batch_size_val=get_gpu_specific_setting(v100=16, a100=32, h100=32, default=8),
    )

    callback_config = CallbackConfig(
        evaluation_subfolder=Path("evil"),
        image_logging_frequency=get_env_specific_setting(local=200, azure=1000),  # only matter for non-validation stage
        output_root_dir=output_folder,
        # NOTE: This differs from output_root_dir in AML pipelines to make ImageCallbacks visible in AML!
        image_root_dir=output_folder / "images",
    )

    qnn_inference_config = QNNInferenceConfig()
    eval_config = EvaluateConfig(qnn=qnn_inference_config)
    eval_config.qnn_embedded_eval_config.launcher_argv = "--argument_translator=\
xtorch_usecases.single_camera.config.launcher.translate \
--config-name=fc1_yuv444_sparse_lanecpp \
config.eval_config.evaluate_torch=False \
config.eval_config.qnn.backend=HTP \
config.data.batch_size_val=1"  # TODO: fix htp eval issue with BS > 1 # noqa: TD003

    eval_config.qnn_embedded_eval_config.launcher_argv_basic_profiling = "--argument_translator=\
xtorch_usecases.single_camera.config.launcher.translate \
--config-name=fc1_yuv444_sparse_lanecpp \
config.eval_config.evaluate_torch=False \
config.eval_config.qnn.backend=HTP \
config.data.batch_size_val=1 \
config.eval_config.qnn.profiling_level=BASIC \
config.trainer.limit_predict_batches=1"  # TODO: fix htp eval issue with BS > 1 # noqa: TD003

    eval_config.qnn_embedded_eval_config.launcher_argv_detailed_profiling = "--argument_translator=\
xtorch_usecases.single_camera.config.launcher.translate \
--config-name=fc1_yuv444_sparse_lanecpp \
config.eval_config.evaluate_torch=False \
config.eval_config.qnn.backend=HTP \
config.data.batch_size_val=1 \
config.eval_config.qnn.profiling_level=DETAILED \
config.trainer.limit_predict_batches=1"  # TODO: fix htp eval issue with BS > 1 # noqa: TD003

    if eval_config.qnn is not None:
        eval_config.qnn.profiling_components = [
            "backbone",
            "fpn",
            "multiscale_fusion",
            "query_generator",
            "transformer__decoder__decoder_layers_0",
            "transformer__decoder__decoder_layers_0",
            "transformer__decoder__decoder_layers_1",
            "transformer__decoder__decoder_layers_2",
            "transformer__decoder__decoder_layers_3",
            "transformer__decoder__decoder_layers_4",
            "transformer__decoder__decoder_layers_5",
            "transformer__decoder_reg_branch",
            "transformer__decoder_cls_branch",
            "transformer__decoder_vis_branches",
            "transformer",
        ]

    qnn_quant_check_config = QuantCheckerConfig(
        enable_activation_histograms=True,
        enable_quant_error_plots=True,
        enable_weight_plots=True,
    )
    convert_config = ConvertConfig(
        quant_check_config=qnn_quant_check_config,
        quantization_override_dict={
            "version": "0.5.0",
            "activation_encodings": {
                #########################################################################
                # first put the more generic ones, then the more specific ones
                # so that the specific ones will win over the general ones
                #########################################################################
                # run query generator and transformer in int16
                "regex:.*_query_generator.*": [{"bitwidth": 16}],
                "regex:.*_transformer/_decoder.*": [{"bitwidth": 16}],
                # this improves query generator quantization
                "regex:.*_query_generator/Sigmoid_output_0": [{"bitwidth": 16}],
                # critical parts in the head related to camera projection
                "regex:.*_denormalize_reference_points.*": [{"bitwidth": 16}],
                "regex:.*_generate_sampling_locations2d.*": [{"bitwidth": 16}],
                "regex:.*_vehicle_to_camera_coord/Mul.*": [{"bitwidth": 16, "min": -250.0, "max": 250.0}],
                "regex:.*_vehicle_to_camera_coord/Add.*": [{"bitwidth": 16, "min": -250.0, "max": 250.0}],
                # most ffn layers can be quantized to int8
                "regex:.*?/_ffn.0/.*": [{"bitwidth": 8}],
                "regex:.*?/_ffn.0/Add_output_0": [{"bitwidth": 16}],
                # some layers in attention in int8 (will put more in next steps)
                "regex:.*_prepare_value_query.*": [{"bitwidth": 8}],
                # use 16bit for these two due to different ranges between xz and vis
                "regex:.*_transformer/_decoder/Sigmoid_5_output_0": [{"bitwidth": 16}],
                "regex:.*_transformer/_decoder/vis_branches.0/vis_branches.0.4/Add_output_0": [{"bitwidth": 16}],
                #########################################################################
                # specific overrides
                #########################################################################
                # inputs:
                "extrinsics": [{"bitwidth": 16, "min": -3.2, "max": 1.5}],
                "focal_length": [{"bitwidth": 16, "min": 0.0, "max": 1700.0}],
                "principal_point": [{"bitwidth": 16, "min": 0.0, "max": 1300.0}],
                # outputs
                "sparse_lanecpp_raw_output_class_scores": [{"bitwidth": 16}],
                "sparse_lanecpp_raw_output_lane_preds_unscaled": [{"bitwidth": 16}],
            },
            "param_encodings": {
                "regex:.*_generate_sampling_locations2d.*": [{"bitwidth": 16}],
            },
        },
        regex_overwrite_is_error=False,  # Note: LaneCpp has currently duplicate overrides set via regex
        per_channel=False,
        per_row=False,
        inputs_config=_create_input_tensor_config(),
    )

    single_view_camera_config = {
        InputImageView.SINGLE_VIEW: CameraDataConfig(
            target_image_size=sparse_lanecpp_config.camera_data_config.target_shape,
            stored_image_type=AvailableImageTypes.YUV420,
            loaded_image_type=AvailableImageTypes.YUV444,
            offset_strategy_mapping=dict.fromkeys(Usage, OffsetStrategyID.FIXED_BOTTOM_CENTER),
        ),
    }

    single_view_camera_config_augmented = copy.deepcopy(single_view_camera_config)
    single_view_camera_config_augmented[InputImageView.SINGLE_VIEW].offset_strategy_mapping = {
        usage: OffsetStrategyID.BETA_BIASED if usage == Usage.TRAINING else OffsetStrategyID.FIXED_BOTTOM_CENTER
        for usage in Usage
    }

    single_view_camera_config_augmented[InputImageView.SINGLE_VIEW].offset_strategy_kwargs = {
        OffsetStrategyID.BETA_BIASED: {
            "alpha_x": 10.0,  # normal like distribution
            "beta_x": 10.0,
            "alpha_y": 1.0,  # linear distribution
            "beta_y": 1.0,
            "min_relative_offset_x": -(
                50.0 / sparse_lanecpp_config.camera_data_config.original_shape.width
            ),  # use 50px offsets
            "max_relative_offset_x": (50.0 / sparse_lanecpp_config.camera_data_config.original_shape.width),
            "min_relative_offset_y": 0.5 - (50.0 / sparse_lanecpp_config.camera_data_config.original_shape.height),
            "max_relative_offset_y": 0.5 + (50.0 / sparse_lanecpp_config.camera_data_config.original_shape.height),
        }
    }

    qnn_camera_config = {
        InputImageView.SINGLE_VIEW: CameraDataConfig(
            # Use the target shape like in training for correct camera parameter adjustment.
            # The offset generator will take care of skipping cropping for the QC model.
            target_image_size=sparse_lanecpp_config.camera_data_config.target_shape,
            stored_image_type=AvailableImageTypes.YUV420,
            loaded_image_type=AvailableImageTypes.YUV420_QC,
            model_performs_cropping=True,
            offset_strategy_mapping=dict.fromkeys(Usage, OffsetStrategyID.FIXED_BOTTOM_CENTER),
            stored_image_size=sparse_lanecpp_config.camera_data_config.original_shape,
        ),
    }

    input_config = {
        PipelineStep.TRAIN: single_view_camera_config_augmented,
        PipelineStep.EXPORT: single_view_camera_config,
        PipelineStep.CONVERT_QNN: qnn_camera_config,
        PipelineStep.EVALUATE_TORCH: single_view_camera_config,
        PipelineStep.EVALUATE_QNN: qnn_camera_config,
        PipelineStep.PROFILE_QNN: qnn_camera_config,
        PipelineStep.VISUALIZATION_TORCH: single_view_camera_config,
    }

    camera_parameter_inputs = CameraParameterConfig(
        source_column=SparseLanecppTask.identifier(), task_config=sparse_lanecpp_config
    )

    config = Config(
        trainer=trainer_config,
        backbone_parameters=MONOFOCAL_BACKBONE_PARAMETERS,
        task_configs=task_config,
        data=data_config,
        inputs=input_config,
        camera_parameter_inputs=camera_parameter_inputs,
        learning_rate=1e-4,
        weight_decay=1e-2,
        callback_config=callback_config,
        eval_config=eval_config,
        output_folder=output_folder,
        convert_config=convert_config,
    )
    return config


@dataclass
class Fc1Yuv444SparseLanecppConfig:
    """Wrapper class for hydra."""

    config: Config = field(default_factory=get_multi_task_config)

    def __post_init__(self) -> None:
        """Post init method."""

        # TODO: refactor. One possible long term solution see https://github.com/PACE-INT/xflow/pull/11692/files
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/382057
        if self.config.run_mode == RunMode.SHORT:
            self.config.adapt_to_short_run()

            if self.config.convert_config.quant_check_config:
                self.config.convert_config.quant_check_config.short_run = True
                self.config.convert_config.quant_check_config.enable_activation_histograms = False
                self.config.convert_config.quant_check_config.enable_quant_error_plots = True
                self.config.convert_config.quant_check_config.enable_weight_plots = False
