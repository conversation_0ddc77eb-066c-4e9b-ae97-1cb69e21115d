"""Classes to construct classifier heads for traffic sign classification."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import dataclasses

import torch

ModelReturnType = tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]]


@dataclasses.dataclass
class AttributeGroup:
    """Defines a group of attributes that semantically belong together."""

    class_ids: list[int] = dataclasses.field(default_factory=list)
    word_attributes: list[str] = dataclasses.field(default_factory=list)
    weekday_attributes: list[int] = dataclasses.field(default_factory=list)


@dataclasses.dataclass
class InterchangeableGroup:
    """Defines a group of semantically equivalent AttributeGroups that may be filled in any order.

    Per interchangeable groups, there are multiple attribute groups.
    Attributes from one group can be aligned with attributes from another group at the same index within that group.
    Here is an example with one interchangeable group: [[0, 1], [2, 3], [4, 5]]. This would mean that the index 0 of
    the output could be aligned with the output at index 0, 2 or 4, and the output at index 1 could be aligned with the
    output at index 1, 3 or 5.
    """

    groups: list[AttributeGroup]
    name: str = ""


@dataclasses.dataclass
class TsClassifierGen2HeadParameters:
    """Parameters for encoder and head.

    Args:
        number_class_entries: Number of class ids that should be predicted. Each prediction has a one hot encoding of
                          the class, as well as a one hot encoding for each letter of the value.
        number_class_ids: Number of different possible classes.
        max_text_length: The maximum number of letters for a word attribute.
        alphabet_size: Number of different characters.
        word_attributes: List of names of attributes that encode a word. Encoding is done by encoding the probability
                        for each letter in a one hot encoding.
        number_weekday_entries: Number of weekday attributes, which are stored as the probablity for each weekday to
                                   be valid
        number_weekday_indices: Number of weekdays + special days (like holiday and so on)
        interchangeable_groups: List of lists of attributes that have the same semantic and should be able to be
                                filled in arbitrary order without affecting the loss. For example, it does not matter
                                in which order the class_ids are recognized, as long as all needed classes are
                                recognized.
    """

    number_class_entries: int
    number_class_ids: int
    max_text_length: int
    alphabet_size: int
    word_attributes: list[str]
    number_weekday_entries: int
    number_weekday_indices: int
    interchangeable_groups: list[InterchangeableGroup]


class TsClassifierGen2HeadParametersHelper:
    """Class to help compute indices and weights usable in the loss algorithm from TsClassifierGen2HeadParameters."""

    def __init__(self, head_parameters: TsClassifierGen2HeadParameters) -> None:
        """Initialize the helper.

        Args:
            head_parameters: Parameters for the head
        """
        self._head_parameters = head_parameters

    def compute_interchangeable_group_indices(
        self,
    ) -> list[list[list[int]]]:
        """Compute the indices for interchangeable groups according to the parameters.

        Returns:
            Tuple of indices for interchangeable groups.
        """
        return self.compute_interchangeable_group_indices_and_weights()[0]

    def compute_interchangeable_group_indices_and_weights(
        self,
        indices_to_weight: list[float] | None = None,
    ) -> tuple[list[list[list[int]]], list[list[list[float]]]]:
        """Compute the indices and weights for interchangeable groups according to the parameters.

        Returns:
            Tuple of indices and weights for interchangeable groups.
        """
        interchangeable_group_indices = []
        interchangeable_group_weights = []
        num_class_id_indices = self._head_parameters.number_class_entries * 2
        num_word_attributes = len(self._head_parameters.word_attributes)

        for interchangeable_group in self._head_parameters.interchangeable_groups:
            attribute_group_indices = []
            attribute_group_weights = []

            for attribute_group in interchangeable_group.groups:
                indices = []
                weights = []

                for class_id in attribute_group.class_ids:
                    indices.extend([class_id * 2, (class_id * 2) + 1])
                    if indices_to_weight is not None:
                        weights.extend([indices_to_weight[class_id * 2], indices_to_weight[(class_id * 2) + 1]])

                for ocr_attribute in attribute_group.word_attributes:
                    index_to_add = num_class_id_indices + self._head_parameters.word_attributes.index(ocr_attribute)
                    indices.append(index_to_add)
                    if indices_to_weight is not None:
                        weights.append(indices_to_weight[index_to_add])

                for weekday_attribute in attribute_group.weekday_attributes:
                    index_to_add = num_class_id_indices + num_word_attributes + weekday_attribute
                    indices.append(index_to_add)
                    if indices_to_weight is not None:
                        weights.append(indices_to_weight[index_to_add])

                attribute_group_indices.append(indices)
                attribute_group_weights.append(weights)

            interchangeable_group_indices.append(attribute_group_indices)
            interchangeable_group_weights.append(attribute_group_weights)

        return interchangeable_group_indices, interchangeable_group_weights

    def compute_remaining_indices(
        self, num_indices: int, interchangeable_group_indices: list[list[list[int]]]
    ) -> set[int]:
        """Compute all indices that are not part of an interchangeable group.

        Args:
            num_indices: Overall number of indices
            interchangeable_group_indices: interchangeable_group_indices description

        Returns:
            List of indices not part of any interchangeable group.
        """
        remaining_indices = set(range(num_indices)) - {
            idx for group in interchangeable_group_indices for attribute_group in group for idx in attribute_group
        }

        return remaining_indices

    def map_indices_to_attributes(
        self,
    ) -> tuple[dict[int, str], dict[int, str], int]:
        """Map each index to its attribute name and type.

        Returns:
            Tuple containing:
                - Dict mapping index to attribute name.
                - Dict mapping index to attribute type.
        """
        index_to_attribute = {}
        index_to_type = {}

        num_class_entries = self._head_parameters.number_class_entries
        num_class_indices = num_class_entries * 2
        num_word_attributes = len(self._head_parameters.word_attributes)
        num_weekday_attributes = self._head_parameters.number_weekday_entries
        num_indices = num_class_indices + num_word_attributes + num_weekday_attributes

        class_id_indices = [i * 2 for i in range(num_class_entries)]
        class_value_indices = [(i * 2) + 1 for i in range(num_class_entries)]

        # Class ID and Class Value
        for class_id_index in class_id_indices:
            index_to_attribute[class_id_index] = f"class_id_{class_id_index // 2}"
            index_to_type[class_id_index] = "class_id"
        for class_value_index in class_value_indices:
            index_to_attribute[class_value_index] = f"class_value_{class_value_index // 2}"
            index_to_type[class_value_index] = "class_value"

        # Word Attributes
        for i in range(num_word_attributes):
            assert i < len(self._head_parameters.word_attributes)
            idx = num_class_indices + i
            attribute_name = self._head_parameters.word_attributes[i]
            attribute_type = "word_attribute"
            index_to_attribute[idx] = attribute_name
            index_to_type[idx] = attribute_type

        # Weekday Attributes
        for i in range(num_weekday_attributes):
            idx = num_class_indices + num_word_attributes + i
            attribute_name = f"weekday_{i}"
            attribute_type = "weekday"
            index_to_attribute[idx] = attribute_name
            index_to_type[idx] = attribute_type

        return index_to_attribute, index_to_type, num_indices


class TimeDistributed(torch.nn.Module):
    """Class to perform time distributed actions on a tensor."""

    def __init__(self, module: torch.nn.Module) -> None:
        """Init time distribution module.

        Args:
            module: Module that should be performed time distributed.
        """
        super().__init__()
        self.module = module

    def forward(self, x: torch.Tensor) -> torch.Tensor:
        """Calls the module on each time step and returns the result as tensors.

        Args:
            x: Input Tensor

        Returns:
            output tensor.
        """
        batch_size, time_steps, _ = x.size()
        # handle time steps as part of the batch
        x = x.contiguous().view(batch_size * time_steps, -1)
        x = self.module(x)
        # give out results in original shape (except last dimension)
        x = x.contiguous().view(batch_size, time_steps, -1)
        return x


class TsClassifierGen2Head(torch.nn.Module):
    """Head for network that provides multiple attributes from an input image."""

    def __init__(self, parameters: TsClassifierGen2HeadParameters, encoder_final_channel_size: int) -> None:
        """Initialize the model.

        Args:
            parameters: parameters for the head
            encoder_final_channel_size: Final channel size of the decoder, and therefore the input tensor
        """
        super().__init__()
        # Contains the layers for each attribute
        self._head_layers = torch.nn.ModuleDict()
        # For each attribute, has an indicator whether the attribute is 'nothing' (1)
        # and the actual value should be ignored.
        self._nothing_indicator_layers = torch.nn.ModuleDict()

        self.max_text_length = parameters.max_text_length
        self.number_weekday_entries = parameters.number_weekday_entries
        self.number_class_entries = parameters.number_class_entries
        self.word_attributes = parameters.word_attributes
        alphabet_size = parameters.alphabet_size
        number_class_ids = parameters.number_class_ids
        for class_prediction_index in range(self.number_class_entries):
            self._head_layers["class_id_" + str(class_prediction_index)] = torch.nn.Linear(
                encoder_final_channel_size * self.max_text_length, number_class_ids
            )
            self._nothing_indicator_layers["class_id_" + str(class_prediction_index)] = torch.nn.Linear(
                encoder_final_channel_size * self.max_text_length, 1
            )
            self._head_layers["class_value_" + str(class_prediction_index)] = TimeDistributed(
                torch.nn.Linear(encoder_final_channel_size, alphabet_size)
            )
            self._nothing_indicator_layers["class_value_" + str(class_prediction_index)] = torch.nn.Linear(
                encoder_final_channel_size * self.max_text_length, 1
            )

        for word_attribute_name in self.word_attributes:
            self._head_layers[word_attribute_name] = TimeDistributed(
                torch.nn.Linear(encoder_final_channel_size, alphabet_size)
            )
            self._nothing_indicator_layers[word_attribute_name] = torch.nn.Linear(
                encoder_final_channel_size * self.max_text_length, 1
            )

        self.flatten = torch.nn.Flatten()

        for weekday_index in range(self.number_weekday_entries):
            self._head_layers["weekday_" + str(weekday_index)] = torch.nn.Linear(
                encoder_final_channel_size * self.max_text_length, parameters.number_weekday_indices
            )
            self._nothing_indicator_layers["weekday_" + str(weekday_index)] = torch.nn.Linear(
                encoder_final_channel_size * self.max_text_length, 1
            )

    def forward(self, x: torch.Tensor) -> ModelReturnType:
        """Calls the model and returns the output as tensors.

        Args:
            x: Input Tensor

        Returns:
            list of output tensors for each attribute, and a list of indicators foreach attribute
            whether the output is actually 'nothing' and should be ignored.
        """
        batch_size, channels, height, width = x.size()
        time_steps = height * width
        # merge width and height into time steps, put channels into the last dimension
        x = x.view(batch_size, channels, time_steps).permute(0, 2, 1)
        # decrease timesteps to maximal text length by performing pooling
        assert time_steps > self.max_text_length, (
            f"Input tensor has {time_steps} time steps, but max text length is {self.max_text_length}. "
            "This means that the input tensor is too small for the model."
        )
        pooling_factor = int(time_steps // self.max_text_length)

        x = torch.nn.functional.avg_pool1d(x.permute(0, 2, 1), kernel_size=pooling_factor).permute(0, 2, 1)
        x = x[:, : self.max_text_length, :]

        outputs = []
        outputs_nothing_indicators = []
        x_flat = self.flatten(x)

        for class_prediction_index in range(self.number_class_entries):
            class_id = self._head_layers["class_id_" + str(class_prediction_index)](x_flat)
            nothing_indicator_class_id = self._nothing_indicator_layers["class_id_" + str(class_prediction_index)](
                x_flat
            )
            class_value = self._head_layers["class_value_" + str(class_prediction_index)](x)
            nothing_indicator_class_value = self._nothing_indicator_layers[
                "class_value_" + str(class_prediction_index)
            ](x_flat)

            outputs.append(class_id)
            outputs_nothing_indicators.append(nothing_indicator_class_id)
            outputs.append(class_value)
            outputs_nothing_indicators.append(nothing_indicator_class_value)

        for word_attribute_name in self.word_attributes:
            word_output = self._head_layers[word_attribute_name](x)
            nothing_indicator_word = self._nothing_indicator_layers[word_attribute_name](x_flat)
            outputs.append(word_output)
            outputs_nothing_indicators.append(nothing_indicator_word)

        for weekday_index in range(self.number_weekday_entries):
            weekday_output = self._head_layers["weekday_" + str(weekday_index)](x_flat)
            nothing_indicator_weekday = self._nothing_indicator_layers["weekday_" + str(weekday_index)](x_flat)
            outputs.append(weekday_output)
            outputs_nothing_indicators.append(nothing_indicator_weekday)

        return tuple(outputs), tuple(outputs_nothing_indicators)
