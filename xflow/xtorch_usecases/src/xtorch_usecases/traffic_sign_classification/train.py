"""Traffic sign classifier train module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import sys
from pathlib import Path

import coloredlogs
import hydra
from omegaconf import DictConfig
from pytorch_lightning import Trainer
from pytorch_lightning.loggers import TensorBoardLogger

from xtorch.training import Stage
from xtorch.training.runner import LightningRunner
from xtorch_usecases.common.pipeline import TRAIN_CHECKPOINT_SUBFOLDER
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen1 import (
    override_trainer_for_stage,
)
from xtorch_usecases.traffic_sign_classification.modules.pretrain_module import (
    TrafficSignClassificationPretrainingLM,
)
from xtorch_usecases.traffic_sign_classification.modules.select_data_structures import (
    TrafficSignClassificationConfig,
    build_config,
    build_data_module,
    build_train_module,
)

_LOGGER = logging.getLogger(__name__)


def main(argv: list[str] | None = None) -> None:
    """Entry point for the `launcher.py` script.

    Args:
        argv: Command line arguments.
    """
    config_name = "traffic_sign_classification_gen1"
    if argv is not None:
        for arg in argv:
            if arg.startswith("--config_name="):
                config_name = arg.split("=")[1]
                break
        # clear the cli from non-hydra arguments otherwise hydra will raise an error
        sys.argv = [sys.argv[0]] + [
            item for item in sys.argv[1:] if ("=" in item and not item.startswith("--config_name"))
        ]

    @hydra.main(version_base=None, config_path="config", config_name=config_name)
    def _hydra_main(raw_config: DictConfig) -> None:
        """Hydra decorated main function for the traffic sign classification usecase training script."""
        _LOGGER.info(f"config name: {config_name}")
        _LOGGER.info(f"Hydra config: {raw_config}")
        config = build_config(raw_config=raw_config, config_name=config_name)
        traffic_sign_classification_train(config)

    _hydra_main()


def traffic_sign_classification_train(
    config: TrafficSignClassificationConfig,
) -> None:
    """Traffic sign classification training function.

    Note: Separated from the main function for testing purposes.
    """

    override_trainer_for_stage(config.trainer, config.stage_trainer.train)
    config.trainer.default_root_dir = Path(config.output_folder)
    config.output_folder = Path(config.output_folder) / "train"
    config.output_folder.mkdir(exist_ok=True, parents=True)

    ###########################################################################
    # SETUP LIGHTNING MODULES
    ###########################################################################

    _LOGGER.info("Setting up data and training module.")
    data_module = build_data_module(config=config)
    train_module = build_train_module(config=config)
    ###########################################################################
    # SETUP CALLBACKS
    ###########################################################################

    _LOGGER.info("Setting up callbacks.")
    for callback in train_module.get_train_callbacks(config):  # type: ignore[reportArgumentType]
        config.trainer.add_callback(callback)

    ###########################################################################
    # SETUP TRAINER
    ###########################################################################

    _LOGGER.info("Setting up the trainer.")
    trainer = Trainer(
        logger=TensorBoardLogger(
            Path(config.output_folder) / "tensorboard",
        ),
        **config.trainer.to_kwargs(),
    )

    ###########################################################################
    # LOAD PRETRAINED MODEL
    ###########################################################################

    pretrain_checkpoint_path = Path(config.input_folder) / "pretrain" / TRAIN_CHECKPOINT_SUBFOLDER / "best.ckpt"
    if pretrain_checkpoint_path.exists():
        _LOGGER.info("Loading pretrained weights.")
        pretrain_module = TrafficSignClassificationPretrainingLM.load_from_checkpoint(pretrain_checkpoint_path)
        train_module.model.set_encoder(pretrain_module.model.encoder)  # type: ignore[reportCallIssue]
    else:
        _LOGGER.info("No checkpoints from pretraining found. Not loading any weights.")

    ###########################################################################
    # TRAIN MODEL
    ###########################################################################

    _LOGGER.info("Starting the training.")
    train_runner = LightningRunner(
        training_module=train_module,  # type: ignore  # noqa: PGH003 # Type is correct, but can't be resolved by static code analysis
        data_module=data_module,  # pyright: ignore[reportArgumentType]
        config=config,
        is_cloud_run=config.environment == "azure",
        trainer=trainer,
    )

    train_runner.run(
        experiment_name="traffic_sign_classification_experiment",
        stage=Stage.TRAIN,
    )


if __name__ == "__main__":
    coloredlogs.install(level=logging.INFO)
    main(argv=sys.argv[1:])
