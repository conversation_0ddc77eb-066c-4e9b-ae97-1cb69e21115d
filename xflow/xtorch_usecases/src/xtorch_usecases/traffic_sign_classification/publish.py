"""Traffic sign classifier publish module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import logging
import sys
from pathlib import Path

import coloredlogs

from azure_tools.logging import runs_on_aml_node
from xtension.azure_ct import azureml_publishing  # pylint: disable=forbidden-import

_LOGGER = logging.getLogger(__name__)


def main(argv: list[str]) -> None:
    """Entry point for publishing."""

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_folder",
        type=str,
        nargs="*",
        help=(
            "Input folder where the stage outputs are stored. One can also specify multiple folders. "
            "In that case, all files are copied together into a single folder during the publishing step."
        ),
    )
    parser.add_argument(
        "--publish_to_model_store",
        action="store_true",
        help="Whether or not to save the model and metrics to the model store.",
    )
    parser.add_argument(
        "--publish_folder",
        type=str,
        default=Path("outputs") / "publish",
        help="Path to the folder that will store the published results.",
    )

    args, _ = parser.parse_known_args(argv)

    for input_folder in args.input_folder:
        azureml_publishing.copy_data(input_folder=input_folder, output_folder=args.publish_folder)

    if args.publish_to_model_store:
        if runs_on_aml_node():
            _LOGGER.info("Publishing outputs and metrics.")
            azureml_publishing.publish_experiment_results(
                args.publish_folder,
                # NOTE: metrics logging is not enabled
                metrics={},
                metrics_version=0,  # This effectively disables publish to model store
            )
        else:
            _LOGGER.info("Local execution does not support publishing to Azure.")


if __name__ == "__main__":
    coloredlogs.install(level=logging.INFO)
    main(argv=sys.argv[1:])
