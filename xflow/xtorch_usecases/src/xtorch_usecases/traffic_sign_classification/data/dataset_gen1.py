"""Implementation of the traffic sign classification Dataset for Gen1."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections import defaultdict
from pathlib import Path

import torch
from ts_label_mapping.classifier import ClassMappings

from xcontract.data.definitions.usage import ValueKey
from xtorch_usecases.traffic_sign_classification.data.dataset import BaseTrafficSignClassificationDataset
from xtorch_usecases.traffic_sign_classification.data.loaders import load_traffic_sign_class_name_gt


class TrafficSignClassificationDatasetGen1(BaseTrafficSignClassificationDataset):
    """Dataset class for Traffic Sign Classification Generation 1."""

    def _create_labels(self, label_paths: list[Path]) -> None:
        """Creates a list of labels.

        Args:
            label_paths: List of paths to label jsons.
        """
        class_mappings = ClassMappings()
        class_name_to_class_id_mapping = class_mappings.get_name_to_id_mapping(country_code="ALL")

        self._labels = []
        for label_path in label_paths:
            class_name = load_traffic_sign_class_name_gt(label_path)
            class_id = class_name_to_class_id_mapping[class_name]
            class_id = torch.tensor(class_id, dtype=torch.long)
            self._labels.append({ValueKey.IDENTIFIER: str(label_path), ValueKey.DATA: class_id})

    def compute_class_distribution(self) -> None:
        """Scans the labels and counts the number of samples per class."""
        class_distribution_dict = defaultdict(int)
        for label_dict in self._labels:
            label = label_dict[ValueKey.DATA]

            assert isinstance(label, torch.Tensor)
            class_distribution_dict[label.item()] += 1

        class_ids = list(class_distribution_dict.keys())
        highest_class_id = max(class_ids)

        self.class_distribution = [0] * (highest_class_id + 1)
        for class_id, count in class_distribution_dict.items():
            self.class_distribution[class_id] = count
