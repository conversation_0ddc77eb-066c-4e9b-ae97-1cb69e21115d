"""Implementation of the traffic sign classification Dataset for Gen2."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
from collections import defaultdict
from pathlib import Path

import torch
from ts_label_mapping.classifier import ClassMappings

from data_formats.traffic_sign.attribute_extractor.attribute_extractor import (
    TrafficSignData,
    Weekday,
    extract_traffic_sign_data_from_raw_text_and_class_ids,
)
from xcontract.data.definitions.usage import ValueKey
from xtorch_usecases.traffic_sign_classification.data.augmentations import (
    AugmentationParameters,
)
from xtorch_usecases.traffic_sign_classification.data.dataset import BaseTrafficSignClassificationDataset
from xtorch_usecases.traffic_sign_classification.data.loaders import (
    get_traffic_sign_class_names_gt,
    get_traffic_sign_text_gt,
    get_traffic_sign_values_gt,
)
from xtorch_usecases.traffic_sign_classification.definitions import SpecialLabels, SpecialLabelsOCR
from xtorch_usecases.traffic_sign_classification.models.heads.ts_classifier_gen2_head import ModelReturnType


class TrafficSignClassificationDatasetGen2(BaseTrafficSignClassificationDataset):
    """Dataset class for Traffic Sign Classification Gen2."""

    def __init__(
        self,
        *,
        dataset_csv_path: Path,
        input_size: list[int],
        is_pretrain: bool,
        augmentation_params: AugmentationParameters,
    ) -> None:
        """Initialize the traffic sign classification dataset.

        Args:
            dataset_csv_path: Path to a dataset csv file.
            input_size: Height and width of the network input.
            is_pretrain: Flag to set wether the data wil be used for the train or pretrain stage.
            augmentation_params: Parameters for the augmentations.
        """

        self.token_to_letter_lookup = None
        self.alphabet_size = None
        self.max_text_length = None
        self._letter_to_token_lookup = None
        self._traffic_sign_data_list = None

        self.class_value_distribution = defaultdict(int)
        self.attribute_distribution = defaultdict(int)

        self._number_class_entries = 4

        self._attribute_indices = [
            "full_text",
            "distance_from",
            "distance_to",
            "distance",
            "time_start_0",
            "time_end_0",
            "time_start_1",
            "time_end_1",
            "time_start_2",
            "time_end_2",
        ]

        super().__init__(
            dataset_csv_path=dataset_csv_path,
            input_size=input_size,
            is_pretrain=is_pretrain,
            augmentation_params=augmentation_params,
        )

    def _prettify_number(self, number: str | None) -> str:
        """Prettify a number by removing trailing zeros and delimiter if not used.

        Args:
            number: The number to prettify.

        Returns:
            The prettified number.
        """
        if number is None:
            return ""
        number = number.rstrip("0")
        number = number.removesuffix(".")
        return number

    def _create_labels(self, label_paths: list[Path]) -> None:
        """Creates a list of labels.

        Args:
            label_paths: List of paths to label jsons.
        """
        class_mappings = ClassMappings()
        class_name_to_class_id_mapping = class_mappings.get_name_to_id_mapping(country_code="ALL")

        self._traffic_sign_data_list = []
        for label_path in label_paths:
            with label_path.open(mode="r") as file:
                label_file = json.load(file)
            class_names = get_traffic_sign_class_names_gt(label_file)
            class_ids = [class_name_to_class_id_mapping[class_name] for class_name in class_names]
            values = get_traffic_sign_values_gt(label_file)
            text = get_traffic_sign_text_gt(label_file)
            traffic_sign_data = extract_traffic_sign_data_from_raw_text_and_class_ids(
                class_id_list=class_ids, value_list=values, full_text=text
            )
            self._traffic_sign_data_list.append(traffic_sign_data)

        all_words = self._collect_all_words()
        self._create_token_lookups(all_words)
        assert self.max_text_length is not None

        self._labels = [
            {
                ValueKey.IDENTIFIER: str(label_path),
                ValueKey.DATA: self._create_label(traffic_sign_data, self.max_text_length),
            }
            for traffic_sign_data, label_path in zip(self._traffic_sign_data_list, label_paths)
        ]

    def _collect_all_words(self) -> list[str]:
        """Collect all words from the traffic sign data list.

        Returns:
            List of all words.
        """
        all_class_values = []
        all_full_texts = []
        all_attribute_words = []
        assert self._traffic_sign_data_list is not None
        for traffic_sign_data in self._traffic_sign_data_list:
            all_class_values.extend(self._collect_class_values(traffic_sign_data))
            all_full_texts.extend(self._collect_full_texts(traffic_sign_data))
            all_attribute_words.extend(self._collect_attribute_words(traffic_sign_data))

        return all_class_values + all_full_texts + all_attribute_words

    def _collect_class_values(self, traffic_sign_data: TrafficSignData) -> list[str]:
        """Collect class values from a traffic sign data.

        Args:
            traffic_sign_data: Traffic sign data.

        Returns:
            List of class values.
        """
        class_values = []
        for i in range(self._number_class_entries):
            value_attr = f"value_{i}"
            value = getattr(traffic_sign_data, value_attr)
            if value is not None:
                class_values.append(self._prettify_number(value))
        return class_values

    def _collect_full_texts(self, traffic_sign_data: TrafficSignData) -> list[str]:
        """Collect full texts from a traffic sign data.

        Args:
            traffic_sign_data: Traffic sign data.

        Returns:
            List of full texts.
        """
        full_texts = []
        if traffic_sign_data.full_text is not None:
            full_texts.append(traffic_sign_data.full_text)
        return full_texts

    def _collect_attribute_words(self, traffic_sign_data: TrafficSignData) -> list[str]:
        """Collect attribute words from a traffic sign data.

        Args:
            traffic_sign_data: Traffic sign data.

        Returns:
            List of attribute words.
        """
        attribute_words = []
        attributes = [
            "distance_from",
            "distance_to",
            "distance",
            "time_start_0",
            "time_end_0",
            "time_start_1",
            "time_end_1",
            "time_start_2",
            "time_end_2",
        ]
        for attr in attributes:
            value = getattr(traffic_sign_data, attr)
            if value is not None:
                attribute_words.append(value)
        return attribute_words

    def _create_token_lookups(self, all_words: list[str]) -> None:
        """Create token lookups from all words.

        Args:
            all_words: List of all words.
        """
        all_letters = set()
        for word in all_words:
            for letter in word:
                all_letters.add(letter)
        current_token = SpecialLabelsOCR.NOTHING_LABEL.value + 1
        self._letter_to_token_lookup = {letter: current_token + i for i, letter in enumerate(sorted(all_letters))}
        self.token_to_letter_lookup = {value: key for key, value in self._letter_to_token_lookup.items()}
        self.token_to_letter_lookup[SpecialLabelsOCR.PADDING_LABEL.value] = ""
        self.max_text_length = max(len(s) for s in all_words)
        self.alphabet_size = max(self.token_to_letter_lookup.keys()) + 1

    def _create_label(self, traffic_sign_data: TrafficSignData, max_text_length: int) -> ModelReturnType:
        """Create a label from traffic sign data.

        Args:
            traffic_sign_data: Traffic sign data.
            max_text_length: Maximum text length.

        Returns:
            Tuple of label tensors.
        """
        label_parts = []
        label_parts_nothing_indicators = []
        class_labels, class_nothing_indicators = self._create_class_labels(traffic_sign_data, max_text_length)
        label_parts.extend(class_labels)
        label_parts_nothing_indicators.extend(class_nothing_indicators)

        attribute_labels, attribute_nothing_indicators = self._create_attribute_labels(
            traffic_sign_data, max_text_length
        )
        label_parts.extend(attribute_labels)
        label_parts_nothing_indicators.extend(attribute_nothing_indicators)

        weekday_labels, weekday_nothing_indicators = self._create_weekday_labels(traffic_sign_data)
        label_parts.extend(weekday_labels)
        label_parts_nothing_indicators.extend(weekday_nothing_indicators)

        return (tuple(label_parts), tuple(label_parts_nothing_indicators))

    def _create_class_labels(
        self, traffic_sign_data: TrafficSignData, max_text_length: int
    ) -> tuple[list[torch.Tensor], list[torch.Tensor]]:
        """Create class labels from traffic sign data.

        Args:
            traffic_sign_data: Traffic sign data.
            max_text_length: Maximum text length.

        Returns:
            List of class label tensors.
        """
        class_labels = []
        class_labels_nothing_indicators = []
        for i in range(self._number_class_entries):
            class_id_attr = f"class_id_{i}"
            class_id = getattr(traffic_sign_data, class_id_attr)
            # class ids are always labeled, at least one entry is filled. But often other entries are empty
            if class_id is None:
                class_labels_nothing_indicators.append(torch.tensor(1.0))
                class_labels.append(torch.tensor(SpecialLabels.NOTHING_LABEL.value))
            else:
                class_labels_nothing_indicators.append(torch.tensor(0.0))
                class_labels.append(torch.tensor(class_id, dtype=torch.long))

            class_value_attr = f"value_{i}"
            class_value_text = self._prettify_number(getattr(traffic_sign_data, class_value_attr))
            if class_id is None:
                class_labels.append(torch.full((max_text_length,), SpecialLabelsOCR.PADDING_LABEL.value))
                class_labels_nothing_indicators.append(torch.tensor(1.0))
            else:
                class_labels.append(self._attribute_to_tensor(class_value_text, max_text_length))
                class_labels_nothing_indicators.append(torch.tensor(0.0))
        return class_labels, class_labels_nothing_indicators

    def _create_attribute_labels(
        self, traffic_sign_data: TrafficSignData, max_text_length: int
    ) -> tuple[list[torch.Tensor], list[torch.Tensor]]:
        """Create attribute labels from traffic sign data.

        Args:
            traffic_sign_data: Traffic sign data.
            max_text_length: Maximum text length.

        Returns:
            List of attribute label tensors.
        """
        attribute_labels = []
        attribute_labels_nothing_indicators = []
        text_data_available = traffic_sign_data.full_text is not None

        for attr in self._attribute_indices:
            attr_value = getattr(traffic_sign_data, attr)
            if attr_value is None:
                if text_data_available:
                    attribute_labels_nothing_indicators.append(torch.tensor(1.0))
                    attribute_labels.append(
                        torch.full((max_text_length,), SpecialLabelsOCR.PADDING_LABEL.value, dtype=torch.long)
                    )
                else:
                    # Note: Nothing indicator will be ignored in loss, since the rest of the label is "unlabeled"
                    attribute_labels_nothing_indicators.append(torch.tensor(0.0))
                    attribute_labels.append(
                        torch.full((max_text_length,), SpecialLabels.UNLABELED_LABEL.value, dtype=torch.long)
                    )
            else:
                attribute_labels_nothing_indicators.append(torch.tensor(0.0))
                attribute_labels.append(self._attribute_to_tensor(getattr(traffic_sign_data, attr), max_text_length))

        return (attribute_labels, attribute_labels_nothing_indicators)

    def _create_weekday_labels(
        self, traffic_sign_data: TrafficSignData
    ) -> tuple[list[torch.Tensor], list[torch.Tensor]]:
        """Create weekday labels from traffic sign data.

        Args:
            traffic_sign_data: Traffic sign data.

        Returns:
            List of weekday label tensors.
        """
        weekday_labels = []
        weekday_labels_nothing_indicators = []
        text_data_available = traffic_sign_data.full_text is not None

        weekdays = ["weekdays_0", "weekdays_1", "weekdays_2"]
        for weekday_attr in weekdays:
            weekday_attributes = getattr(traffic_sign_data, weekday_attr)
            weekday_labels_nothing_indicator = 0.0
            if weekday_attributes is None:
                if text_data_available:
                    weekday_labels_nothing_indicator = 1.0
                    is_valid_value = 1.0
                    weekday_attributes = [is_valid_value] * len(Weekday)
                else:
                    # Note: Nothing indicator will be ignored in loss, since the rest of the label is "unlabeled"
                    weekday_labels_nothing_indicator = 1.0
                    weekday_attributes = [SpecialLabels.UNLABELED_LABEL.value] * len(Weekday)

            weekday_labels.append(torch.tensor(weekday_attributes, dtype=torch.float))
            weekday_labels_nothing_indicators.append(torch.tensor(weekday_labels_nothing_indicator, dtype=torch.float))

        return weekday_labels, weekday_labels_nothing_indicators

    def _attribute_to_tensor(
        self,
        attribute_string: str,
        max_text_length: int,
    ) -> torch.Tensor:
        """Converts a string attribute to a tensor.

        Args:
            attribute_string: The string to convert.
            max_text_length: Maximum text length of a string.
            none_value: Value the result should be filled with if there is no string available.

        Returns:
            The converted tensor.
        """
        assert self._letter_to_token_lookup is not None
        tokens = [self._letter_to_token_lookup[letter] for letter in attribute_string]
        tokens += [SpecialLabelsOCR.PADDING_LABEL.value] * (max_text_length - len(tokens))
        return torch.tensor(tokens, dtype=torch.long)

    def compute_class_distribution(self) -> None:
        """Scans the labels and counts the number of samples per class."""
        assert self._traffic_sign_data_list is not None
        class_distribution_dict = defaultdict(int)

        for traffic_sign_data in self._traffic_sign_data_list:
            for i in range(self._number_class_entries):
                class_id_attr = f"class_id_{i}"
                class_id = getattr(traffic_sign_data, class_id_attr)
                if class_id is not None:
                    class_distribution_dict[class_id] += 1
                    value_attr = f"value_{i}"
                    value = getattr(traffic_sign_data, value_attr)
                    if value is not None:
                        self.class_value_distribution[(class_id, value)] += 1
            for attribute_index in self._attribute_indices:
                attribute_value = getattr(traffic_sign_data, attribute_index)
                if attribute_value is not None:
                    self.attribute_distribution[(attribute_index, attribute_value)] += 1

        class_ids = list(class_distribution_dict.keys())
        highest_class_id = max(class_ids)

        self.class_distribution = [0] * (highest_class_id + 1)
        for class_id, count in class_distribution_dict.items():
            self.class_distribution[class_id] = count

    def store_token_lookup(self, file_path: Path) -> None:
        """Stores the token to letter dictionary.

        Args:
            file_path: Path where the dictionary should be stored.
        """
        if self.token_to_letter_lookup is None:
            return
        with Path.open(file_path, "w", encoding="utf-8") as file:
            json.dump(self.token_to_letter_lookup, file)
