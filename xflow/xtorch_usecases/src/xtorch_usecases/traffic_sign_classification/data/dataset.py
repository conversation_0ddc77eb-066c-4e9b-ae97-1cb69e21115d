"""Implementation of the traffic sign classification Dataset."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import csv
from abc import ABC, abstractmethod
from collections.abc import Iterator
from pathlib import Path
from typing import NamedTuple

import torch
from torch.utils.data import Dataset
from torchvision.io import read_image
from torchvision.transforms import CenterCrop

from xcontract.data.definitions.usage import ValueKey
from xtorch_usecases.traffic_sign_classification.data.augmentations import (
    AugmentationParameters,
    apply_augmentations,
)

ImageDict = dict[ValueKey, str | torch.Tensor]
LabelDict = dict[ValueKey, str | torch.Tensor | tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]]]


class ImageAndLabel(NamedTuple):  # noqa: D101
    image: ImageDict
    label: LabelDict


class BaseTrafficSignClassificationDataset(Dataset[ImageAndLabel], ABC):
    """Abstract base class for the traffic sign classification datasets."""

    def __init__(
        self,
        *,
        dataset_csv_path: Path,
        input_size: list[int],
        is_pretrain: bool,
        augmentation_params: AugmentationParameters,
    ) -> None:
        """Initialize the traffic sign classification dataset.

        Args:
            dataset_csv_path: Path to a dataset csv file.
            input_size: Height and width of the network input.
            is_pretrain: Flag to set wether the data wil be used for the train or pretrain stage.
            augmentation_params: Parameters for the augmentations.
        """

        assert dataset_csv_path.exists()
        assert dataset_csv_path.suffix.lower() == ".csv"

        self._dataset_csv_path = dataset_csv_path
        self._database_path = self._dataset_csv_path.parent
        self._augmentation_params = augmentation_params

        self._center_crop_fn = CenterCrop(size=input_size)

        self._image_paths, self._label_paths = self._collect_paths_from_csv()

        if is_pretrain:
            self._images = self._create_pretrain_stage_input_data(image_paths=self._image_paths)
        else:
            self._images = self._create_train_stage_input_data(image_paths=self._image_paths)

        self._labels: list[LabelDict] = []
        # Loading and creating directly from json files.
        # For performance reasons, this should be replace in the future by reading labels created offline.
        self._create_labels(label_paths=self._label_paths)
        # List of sample number per class, index is class id.
        self.class_distribution: list[int]
        self.compute_class_distribution()

    def _collect_paths_from_csv(self) -> tuple[list[Path], list[Path]]:
        """Creates a list of images for training.

        Args:
            image_paths: List of paths to image files.

        Returns:
            List of images.
        """
        image_paths = []
        label_paths = []
        with self._dataset_csv_path.open(mode="r") as file:
            reader = csv.DictReader(file, delimiter=";")
            for row in reader:
                image_paths.append(self._database_path.joinpath(row["image_path"]))
                label_paths.append(self._database_path.joinpath(row["label_path"]))

        return image_paths, label_paths

    def _create_train_stage_input_data(self, image_paths: list[Path]) -> list[ImageDict]:
        """Creates a list of images for the training stage.

        Args:
            image_paths: List of paths to image files.

        Returns:
            List of images.
        """
        images = []
        for image_path in image_paths:
            image = read_image(str(image_path))
            # Remove the alpha channel if it exists
            if image.shape[0] == 4:
                image = image[:3, :, :]

            image = apply_augmentations(image, params=self._augmentation_params)

            image = self._center_crop_fn(image)
            normalized_image = (image / 127.5) - 1

            image_data = {
                ValueKey.IDENTIFIER: str(image_path),
                ValueKey.DATA: normalized_image,
            }

            images.append(image_data)

        return images

    def _create_pretrain_stage_input_data(self, image_paths: list[Path]) -> list[ImageDict]:
        """Creates a list of images for contrastive pretraining stage.

        Each returned image shows two augmented versions of the same input data point.
        The image has the doubled width of the input datapoint. The left side shows one
        augmented version, the right side the second.

        Args:
            image_paths: List of paths to image files.

        Returns:
            List of images.
        """
        images = []
        for image_path in image_paths:
            # augment each image twice and concatenated them along the x-axis
            # making the resulting image twice as wide

            image_1 = read_image(str(image_path))
            image_2 = image_1.clone()

            image_1 = apply_augmentations(image_1, params=self._augmentation_params)
            image_1 = self._center_crop_fn(image_1)

            image_2 = apply_augmentations(image_2, params=self._augmentation_params)
            image_2 = self._center_crop_fn(image_2)

            # normalize the images to [-1, 1] range
            normalized_image_1 = (image_1 / 127.5) - 1
            normalized_image_2 = (image_2 / 127.5) - 1

            image = torch.cat([normalized_image_1, normalized_image_2], dim=-1)

            image_data = {
                ValueKey.IDENTIFIER: str(image_path),
                ValueKey.DATA: image,
            }

            images.append(image_data)

        return images

    @abstractmethod
    def _create_labels(self, label_paths: list[Path]) -> None:
        """Creates a list of labels.

        Args:
            label_paths: List of paths to label jsons.
        """

    @abstractmethod
    def compute_class_distribution(self) -> None:
        """Scans the labels and counts the number of samples per class."""

    def __len__(self) -> int:
        """Number of rows for the dataset."""
        return len(self._image_paths)

    def __getitem__(self, index: int) -> ImageAndLabel:
        """Get the dataset row at the global index.

        Args:
            index: Global index of the row in the dataset (0 <= index < len(self)).

        Returns:
            Dictionary of the dataset row where keys are strings.
        """
        return ImageAndLabel(self._images[index], self._labels[index])

    def __iter__(self) -> Iterator[ImageAndLabel]:
        """Iterator implementation.

        Note: This is mostly to make mypy happy. https://stackoverflow.com/a/61739436

        Returns:
            Iterator into dataset rows.
        """
        for i in range(len(self)):
            yield self[i]
