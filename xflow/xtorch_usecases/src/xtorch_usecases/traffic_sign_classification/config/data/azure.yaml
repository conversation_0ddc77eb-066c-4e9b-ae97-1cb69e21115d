# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# copyright (C) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

pretrain:
  train_data_csv_path: ${oc.env:AZUREML_DATAREFERENCE_ingest_datasets}/static_objects/traffic_signs/xtorch_test_data/test_dataset.csv
  val_data_csv_path: ""
  batch_size_train: 10
  batch_size_val: 10
  batch_size_convert: 64
  shuffle: True
  drop_last: False
  num_workers_train: 0
  num_workers_val: 0
  augmentation_params:
    min_rotation_angle_deg: -15.0
    max_rotation_angle_deg: 15.0

train:
  train_data_csv_path: ${oc.env:AZUREML_DATAREFERENCE_ingest_datasets}/static_objects/traffic_signs/xtorch_test_data/test_dataset.csv
  val_data_csv_path: ${oc.env:AZUREML_DATAREFERENCE_ingest_datasets}/static_objects/traffic_signs/xtorch_test_data/test_dataset.csv
  batch_size_train: 5
  batch_size_val: 5
  batch_size_convert: 64
  shuffle: True
  drop_last: False
  num_workers_train: 0
  num_workers_val: 0
  augmentation_params:
    min_rotation_angle_deg: -15.0
    max_rotation_angle_deg: 15.0
