"""Traffic sign classifier pretrain module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import sys
from pathlib import Path

import coloredlogs
import hydra
from omegaconf import DictConfig
from pytorch_lightning import Trainer
from pytorch_lightning.loggers import TensorBoardLogger

from xtorch.config.hydra_loading import instantiate_config
from xtorch.training.runner import LightningRunner, Stage
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen1 import (
    TrafficSignClassificationConfigGen1,
    override_trainer_for_stage,
)
from xtorch_usecases.traffic_sign_classification.modules.data_module_gen1 import (
    TrafficSignClassificationLDMGen1,
)
from xtorch_usecases.traffic_sign_classification.modules.pretrain_module import (
    TrafficSignClassificationPretrainingLM,
)

_LOGGER = logging.getLogger(__name__)


def main(argv: list[str] | None = None) -> None:
    """Entry point for the `launcher.py` script.

    Args:
        argv: Command line arguments.
    """
    coloredlogs.install(level=logging.INFO)

    if argv is not None:
        sys.argv = [sys.argv[0]] + [item for item in sys.argv[1:] if "=" in item]

    @hydra.main(version_base=None, config_path="config", config_name="traffic_sign_classification_gen1")
    def _hydra_main(raw_config: DictConfig) -> None:
        """Hydra decorated main function for the traffic sign classification usecase training script."""

        _LOGGER.info(f"Hydra config: {raw_config}")
        config = instantiate_config(dict_config=raw_config, config_cls=TrafficSignClassificationConfigGen1)
        traffic_sign_classification_pretrain(config)

    _hydra_main()


def traffic_sign_classification_pretrain(
    config: TrafficSignClassificationConfigGen1,
) -> None:
    """Traffic sign classification pretraining function.

    Args:
        config: Configuration object containing all parameters for the pretraining stage.

    Returns:
        None
    """

    ###########################################################################
    # OVERRIDE DEFAULT TRAINER WITH STAGE CONFIG
    ###########################################################################

    override_trainer_for_stage(config.trainer, config.stage_trainer.pretrain)
    config.trainer.default_root_dir = config.output_folder / "pretrain"
    config.output_folder = Path(config.output_folder)
    config.output_folder.mkdir(exist_ok=True, parents=True)

    ###########################################################################
    # SETUP LIGHTNING MODULES
    ###########################################################################

    _LOGGER.info("Setting up data and pretraining module.")
    data_module = TrafficSignClassificationLDMGen1(
        config=config.data.train,
        input_size=[config.input.input_height, config.input.input_width],
        is_pretrain=True,
    )
    pretrain_module = TrafficSignClassificationPretrainingLM(config=config)

    ###########################################################################
    # SETUP CALLBACKS
    ###########################################################################

    _LOGGER.info("Setting up callbacks.")
    for callback in pretrain_module.get_pretrain_callbacks(config):
        config.trainer.add_callback(callback)

    ###########################################################################
    # SETUP TRAINER
    ###########################################################################

    _LOGGER.info("Setting up the trainer.")
    assert config.trainer.default_root_dir is not None
    trainer = Trainer(
        logger=TensorBoardLogger(
            config.trainer.default_root_dir / "tensorboard",
        ),
        **config.trainer.to_kwargs(),
    )

    ###########################################################################
    # TRAIN MODEL
    ###########################################################################

    _LOGGER.info("Starting the training.")
    pretrain_runner = LightningRunner(
        training_module=pretrain_module,  # type: ignore  # noqa: PGH003 # Type is correct, but can't be resolved by static code analysis
        data_module=data_module,  # pyright: ignore[reportArgumentType]
        config=config,
        is_cloud_run=config.environment == "azure",
        trainer=trainer,
    )
    pretrain_runner.run(
        experiment_name="traffic_sign_classification_experiment",
        stage=Stage.TRAIN,
    )


if __name__ == "__main__":
    main(argv=sys.argv[1:])
