"""Traffic sign classifier convert module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import copy
import itertools
import logging
import sys
from collections.abc import Generator
from pathlib import Path
from typing import Any, cast

import hydra
import numpy as np
import onnx
import onnxsim
import torch
import ts_label_mapping
from numpy.typing import NDArray
from omegaconf import DictConfig
from qnn_custom_ops import QnnBackend, QnnOpPackage

from conversion.qnn.common import QnnModelArtifacts
from conversion.qnn.conversion import QnnConversion
from conversion.qnn.onnx_surgery.onnx_graph_surgery_rules import (
    AbstractGraphRewriter,
    RewriteYUV444Plugin,
)
from conversion.qnn.quantization_checker import (
    check_qnn_quantization,
)
from xcontract.data.definitions.image import ColorFormat
from xcontract.data.definitions.usage import ValueKey
from xtorch_extensions.conversion.metadata import create_metadata
from xtorch_extensions.environment import log_environment
from xtorch_usecases.common.pipeline import CONVERT_QNN_SUBFOLDER, EXPORT_SUBFOLDER
from xtorch_usecases.traffic_sign_classification.config.deployment_config import DeploymentConfig
from xtorch_usecases.traffic_sign_classification.modules.select_data_structures import (
    TrafficSignClassificationConfig,
    build_config,
    build_data_module,
)

logging.basicConfig(level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s")
_LOGGER = logging.getLogger(__name__)

# ONNX graph rewriters and node rewriters not necessary for Gen1 TS classifier, which uses standard layers supported in
# QNN tooling. Leaving this here as it may be necessary to define some for Gen2 model
_COMMON_ONNX_GRAPH_REWRITERS = {}
_ONNX_NODE_REWRITERS = []


class _CalibrationWrapper:
    """Wrapper class to generate calibration samples for quantization."""

    # TODO: Remove as soon as the QnnConversion class is refactored to accept a simple Generator # noqa: TD003
    def __init__(self, config: TrafficSignClassificationConfig) -> None:
        """Initialize the calibration wrapper.

        Args:
            config: TrafficSignClassificationConfig object containing configuration parameters.
        """
        self.data_pipeline = build_data_module(config=config)

    def _generate_input_sample(self) -> Generator[dict[str, NDArray[Any]], None, None]:
        """Generate an single input sample for calibration dataset used for quantization.

        Yields:
            A dictionary containing the input data as a numpy array.
        """
        # convert tuples of tensors from build_validation_dataset() to dicts of numpy arrays
        batch_size = self.data_pipeline._config.batch_size_convert  # noqa: SLF001
        validation_dataset = self.data_pipeline.build_validation_dataset()
        if validation_dataset is None:
            error_message = "Validation dataset returned None. Please check the data pipeline configuration."
            raise ValueError(error_message)

        # Ensure a batch size greater than the unique number of samples can be created by cycling through the dataset
        dataset_iterator = itertools.cycle(validation_dataset)
        batch_samples = [
            cast(torch.Tensor, sample.image[ValueKey.DATA]).numpy()
            for sample in itertools.islice(dataset_iterator, batch_size)
        ]
        # 'x' is the name of the input tensor of the model
        yield {"x": np.stack(batch_samples, axis=0)}


def main(argv: list[str] | None = None) -> None:
    """Entry point for the `launcher.py` script.

    Args:
        argv: Command line arguments.
    """
    config_name = "traffic_sign_classification_gen1"
    if argv is not None:
        for arg in argv:
            if arg.startswith("--config_name="):
                config_name = arg.split("=")[1]
                break
        # clear the cli from non-hydra arguments otherwise hydra will raise an error
        sys.argv = [sys.argv[0]] + [
            item for item in sys.argv[1:] if ("=" in item and not item.startswith("--config_name="))
        ]

    @hydra.main(version_base=None, config_path="config", config_name=config_name)
    def _hydra_main(raw_config: DictConfig) -> None:
        """Hydra decorated main function for the traffic sign classification usecase training script."""

        _LOGGER.info(f"Hydra config: {raw_config}")
        config = build_config(raw_config=raw_config, config_name=config_name)
        traffic_sign_classification_conversion(model_name=config_name, config=config)

    _hydra_main()


def traffic_sign_classification_conversion(
    model_name: str,
    config: TrafficSignClassificationConfig,
) -> None:
    """Traffic sign classification convert function.

    Args:
        model_name: Name of the model to be converted.
        config: TrafficSignClassificationConfig object containing configuration parameters.
    """
    log_environment()

    if not config.output_folder:
        msg = "output_folder is required for the convert stage."
        raise ValueError(msg)
    output_path = config.output_folder / CONVERT_QNN_SUBFOLDER
    output_path.mkdir(parents=True, exist_ok=True)

    if config.convert_config.cpu_conversion or config.convert_config.htp_conversion:
        if not config.input_folder:
            msg = "input_folder is required for the convert stage."
            raise ValueError(msg)

        if not config.deployment_configs:
            msg = "deployment_configs is required for the convert stage."
            raise ValueError(msg)

        input_path = config.input_folder / EXPORT_SUBFOLDER

        # Get all models/deployments which should be converted
        for deployment in config.deployment_configs:
            deployment_name = f"{model_name}_{deployment.name_suffix}" if deployment.name_suffix != "" else model_name
            model = onnx.load(input_path / f"{deployment_name}.onnx")
            enabled_task_ids = deployment.enabled_task_ids if deployment.enabled_task_ids is not None else []
            convert_deployment(
                deployment_name=deployment_name,
                deployment=deployment,
                model=model,
                output_path=output_path,
                enabled_task_ids=enabled_task_ids,
                config=config,
            )
            if not config.convert_config.skip_quant_checker:
                # in case of deployments the quant checker with
                run_quant_checker(config, output_path, deployment_name)

    elif not config.convert_config.skip_quant_checker:
        # Run without conversion. TODO: allow to specify deployment-based model name
        run_quant_checker(config, output_path, model_name)
    else:
        _LOGGER.warning("No conversion or quantization checking is defined! Nothing will be done")


def convert_deployment(
    deployment_name: str,
    deployment: DeploymentConfig,
    model: onnx.ModelProto,
    output_path: Path,
    enabled_task_ids: list[str],
    config: TrafficSignClassificationConfig,
) -> None:
    """Convert a onnx model to QNN format.

    Args:
        deployment_name: Name of the deployment.
        deployment: Deployment configuration object.
        model: ONNX model to be converted.
        output_path: Path to the output folder where the converted model will be saved.
        enabled_task_ids: List of task IDs that are enabled for this deployment.
        config: TrafficSignClassificationConfig object containing configuration parameters.
    """
    model_metadata = create_metadata(deployment_name, task_ids=enabled_task_ids)
    # The version is used in mono repo to check if class name to id mapping during training matches the mapping in AOS
    model_metadata.tasks[enabled_task_ids[0]].metadata = {"label_mapping_version": ts_label_mapping.__version__}

    onnx_graph_rewriters: list[AbstractGraphRewriter] = []
    if deployment.model_input_image_type == ColorFormat.YUV444.value:
        onnx_graph_rewriters.append(RewriteYUV444Plugin(yuv444_node_pattern="/model/_rgb2yuv/Conv"))
    elif deployment.model_input_image_type != ColorFormat.RGB.value:
        error_msg = f"Model input image type {deployment.model_input_image_type} not supported yet."
        raise NotImplementedError(error_msg)
    onnx_graph_rewriters.extend(_COMMON_ONNX_GRAPH_REWRITERS)

    _LOGGER.info(f"Simplify {deployment_name} model.")
    model_opt, check = onnxsim.simplify(model)
    if not check:
        msg = "Simplification failed"
        raise ValueError(msg)

    model = onnx.shape_inference.infer_shapes(model_opt, check_type=True, data_prop=True)

    if config.convert_config.cpu_conversion:
        _LOGGER.info(f"Start conversion of {deployment_name} for CPU.")
        qnn_converter_cpu = QnnConversion(
            QnnBackend.CPU,
            conversion_folder=output_path,
        )
        _, _ = qnn_converter_cpu.convert_model(
            onnx_graph=model,
            onnx_model_path=str(output_path / f"{deployment_name}_qnn_cpu.onnx"),
            custom_ops=QnnOpPackage.VIPER,
            model_metadata=copy.deepcopy(model_metadata),
            onnx_graph_rewriters=onnx_graph_rewriters,
            onnx_node_rewriters=_ONNX_NODE_REWRITERS,
            enable_dlbc=True,
            enable_O3=True,
            use_per_channel_quantization=config.convert_config.per_channel,
            use_per_row_quantization=config.convert_config.per_row,
            non_matching_regex_is_error=config.convert_config.non_matching_regex_is_error,
            regex_overwrite_is_error=config.convert_config.regex_overwrite_is_error,
        )
        _LOGGER.info(f"\t✅ {deployment_name} model convert for CPU.")

    if config.convert_config.htp_conversion:
        _LOGGER.info(f"Start conversion of {deployment_name} for HTP.")
        calibrator = _CalibrationWrapper(config=config)

        qnn_converter_htp = QnnConversion(
            QnnBackend.HTP,
            conversion_folder=output_path,
            calibrator=calibrator,
            enable_optrace_profiling=config.convert_config.enable_optrace_profiling,
        )

        _, _ = qnn_converter_htp.convert_model(
            onnx_graph=model,
            onnx_model_path=str(output_path / f"{deployment_name}_qnn_htp.onnx"),
            custom_ops=QnnOpPackage.VIPER,
            model_metadata=copy.deepcopy(model_metadata),
            onnx_graph_rewriters=onnx_graph_rewriters,
            onnx_node_rewriters=_ONNX_NODE_REWRITERS,
            enable_dlbc=True,
            enable_O3=True,
            use_per_channel_quantization=config.convert_config.per_channel,
            use_per_row_quantization=config.convert_config.per_row,
            quantization_override_dict=config.convert_config.quantization_override_dict,
            non_matching_regex_is_error=config.convert_config.non_matching_regex_is_error,
            regex_overwrite_is_error=config.convert_config.regex_overwrite_is_error,
        )
        _LOGGER.info(f"\t✅ {deployment_name} model convert for HTP.")


def run_quant_checker(config: TrafficSignClassificationConfig, output_path: Path, model_name: str) -> None:
    """Run quantization checker.

    Args:
        config: TrafficSignClassificationConfig object containing configuration parameters.
        output_path: Path to the output folder where the quantization checker results will be saved.
        model_name: Name of the model to be checked.
    """
    _LOGGER.info(f"Start quantization checker for {model_name}.")

    if not config.convert_config.cpu_conversion:
        _LOGGER.warning("Re-using qnn cpu conversion artifact from previous iteration!")

    if not config.convert_config.htp_conversion:
        _LOGGER.warning("Re-using qnn htp conversion artifact from previous iteration!")

    qnn_artifacts_cpu = QnnModelArtifacts(output_path, model_name=model_name, backend=QnnBackend.CPU)
    qnn_artifacts_htp = QnnModelArtifacts(output_path, model_name=model_name, backend=QnnBackend.HTP)

    quant_sample_generator = _CalibrationWrapper(config=config)
    qnn_inputs_float = next(quant_sample_generator._generate_input_sample())  # noqa: SLF001
    meta_data = qnn_artifacts_htp.model_metadata()
    assert meta_data is not None, "HTP model metadata must be available"
    qnn_inputs_quantized = meta_data.quantize_inputs(qnn_inputs_float)

    # NOTE: Simulator inference for quantized HTP model not working with intermediate tensors. Unclear even to
    #       EmbeddedAI team. Ensure to set`config.convert_config.quant_check_config.short_run=True` for now.
    check_qnn_quantization(
        cpu_qnn_artifacts=qnn_artifacts_cpu,
        quantized_qnn_artifacts=qnn_artifacts_htp,
        cpu_input_data=qnn_inputs_float,
        quantized_input_data=qnn_inputs_quantized,
        qnn_op_package=QnnOpPackage.VIPER,
        output_dir=output_path,
        config=config.convert_config.quant_check_config,
        model_batch_size=config.data.train.batch_size_convert,
    )
    _LOGGER.info(f"\t✅ Quantization checker finished for {model_name}.")


if __name__ == "__main__":
    main(argv=sys.argv[1:])
