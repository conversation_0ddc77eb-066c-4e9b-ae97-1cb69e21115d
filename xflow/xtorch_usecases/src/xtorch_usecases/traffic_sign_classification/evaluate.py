"""Traffic sign classifier evaluate module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import sys
from pathlib import Path

import coloredlogs
import hydra
from omegaconf import DictConfig
from pytorch_lightning import Trainer
from pytorch_lightning.loggers import TensorBoardLogger

from xtorch.training import Stage
from xtorch.training.checkpoint import CheckpointFile
from xtorch.training.runner import LightningRunner
from xtorch_usecases.traffic_sign_classification.modules.select_data_structures import (
    TrafficSignClassificationConfig,
    build_config,
    build_data_module,
    build_train_module,
)

_LOGGER = logging.getLogger(__name__)


def main(argv: list[str] | None = None) -> None:
    """Entry point for the `evaluate.py` script.

    Args:
        argv: Command line arguments.
    """
    coloredlogs.install(level=logging.INFO)

    # clear the cli from non-hydra arguments otherwise hydra will raise an error
    config_name = "traffic_sign_classification_gen1"
    if argv is not None:
        for arg in argv:
            if arg.startswith("--config_name="):
                config_name = arg.split("=")[1]
                break
        # clear the cli from non-hydra arguments otherwise hydra will raise an error
        sys.argv = [sys.argv[0]] + [
            item for item in sys.argv[1:] if ("=" in item and not item.startswith("--config_name="))
        ]

    @hydra.main(version_base=None, config_path="config", config_name=config_name)
    def _hydra_main(raw_config: DictConfig) -> None:
        """Hydra decorated main function for the traffic sign classification usecase evaluate script."""

        _LOGGER.info(f"Hydra config: {raw_config}")
        config = build_config(raw_config=raw_config, config_name=config_name)
        traffic_sign_classification_evaluate(config)

    _hydra_main()


def traffic_sign_classification_evaluate(
    config: TrafficSignClassificationConfig,
) -> None:
    """Traffic sign classification evaluation function.

    Note: Separated from the main function for testing purposes.
    """
    ###########################################################################
    # OVERRIDE DEFAULT TRAINER ROOT DIR
    ###########################################################################

    config.trainer.default_root_dir = config.output_folder / "evaluate"
    config.output_folder.mkdir(exist_ok=True, parents=True)

    ###########################################################################
    # SETUP LIGHTNING MODULES
    ###########################################################################

    _LOGGER.info("Setting up data and training module.")
    data_module = build_data_module(config=config)
    eval_module = build_train_module(config=config)

    ###########################################################################
    # SETUP CALLBACKS
    ###########################################################################

    _LOGGER.info("Setting up callbacks.")
    for callback in eval_module.get_eval_callbacks(config):  # type: ignore[reportArgumentType]
        config.trainer.add_callback(callback)

    ###########################################################################
    # SETUP TRAINER
    ###########################################################################

    _LOGGER.info("Setting up the Evaluator.")
    trainer = Trainer(
        logger=TensorBoardLogger(
            Path(config.output_folder) / "tensorboard",
        ),
        **config.trainer.to_kwargs(),
    )

    ###########################################################################
    # TRAIN MODEL
    ###########################################################################

    _LOGGER.info("Starting the evaluation.")
    eval_runner = LightningRunner(
        training_module=eval_module,  # type: ignore  # noqa: PGH003 # Type is correct, but can't be resolved by static code analysis
        data_module=data_module,  # pyright: ignore[reportArgumentType]
        config=config,
        is_cloud_run=config.environment == "azure",
        trainer=trainer,
    )

    eval_runner.run(
        experiment_name="traffic_sign_classification_experiment",
        stage=Stage.VALIDATE,
        pretrained_weights=CheckpointFile(Path(config.output_folder) / "checkpoints" / "last-v1.ckpt"),
    )


if __name__ == "__main__":
    main(argv=sys.argv[1:])
