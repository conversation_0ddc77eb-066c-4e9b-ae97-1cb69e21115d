"""Traffic sign classifier multi class metric callback."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
import logging
from pathlib import Path
from typing import Any, cast

import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import torch
from pytorch_lightning import LightningModule, Trainer
from pytorch_lightning.callbacks import Callback
from pytorch_lightning.utilities.types import STEP_OUTPUT
from torchmetrics.classification import (
    ConfusionMatrix,
    F1Score,
    Precision,
    Recall,
)
from ts_label_mapping.classifier import ClassMappings

from xcontract.data.definitions.usage import ValueKey
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel

_LOGGER = logging.getLogger(__name__)


class MultiClassMetricsCallback(Callback):
    """Callback for Multi-Class Metrics."""

    def __init__(
        self,
        num_classes: int,
        log_dir: Path,
    ) -> None:
        """Init MultiClassMetricsCallback.

        Args:
            num_classes: Number of classes that can be classified
            log_dir: Directory where the metrics will be saved
        """
        super().__init__()
        self._num_classes = num_classes
        self._log_dir = log_dir
        self._log_dir.mkdir(exist_ok=True, parents=True)

        self._conf_matrix = ConfusionMatrix(task="multiclass", num_classes=num_classes)
        self._precision = Precision(task="multiclass", num_classes=num_classes, average=None)
        self._recall = Recall(task="multiclass", num_classes=num_classes, average=None)
        self._f1 = F1Score(task="multiclass", num_classes=num_classes, average=None)

        # Micro and Macro metrics
        self._precision_micro = Precision(task="multiclass", num_classes=num_classes, average="micro")
        self._recall_micro = Recall(task="multiclass", num_classes=num_classes, average="micro")
        self._f1_micro = F1Score(task="multiclass", num_classes=num_classes, average="micro")

        self._precision_macro = Precision(task="multiclass", num_classes=num_classes, average="macro")
        self._recall_macro = Recall(task="multiclass", num_classes=num_classes, average="macro")
        self._f1_macro = F1Score(task="multiclass", num_classes=num_classes, average="macro")

        self._all_preds = []
        self._all_targets = []

        class_mappings = ClassMappings()
        self._class_id_to_class_name_mapping = class_mappings.get_id_to_name_mapping(country_code="ALL")

    def on_validation_batch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
        outputs: STEP_OUTPUT,
        batch: ImageAndLabel,
        batch_idx: int,
        dataloader_idx: int = 0,
    ) -> None:
        """On end of a batch during validation.

        Args:
            trainer: Unused
            pl_module: Lightning module to be used
            outputs: Unused
            batch: Batch including the data and the target
            batch_idx: Unused
            dataloader_idx: Unused
        """
        logits = pl_module(batch)
        preds = torch.argmax(logits, dim=1)
        y = cast(torch.Tensor, batch.label[ValueKey.DATA])

        self._all_preds.append(preds.cpu())
        self._all_targets.append(y.cpu())

    def on_validation_epoch_end(
        self,
        trainer: Trainer,
        pl_module: LightningModule,
    ) -> None:
        """On the end of an epoch during validation.

        Args:
            trainer: Unused
            pl_module: Unused
        """
        preds = torch.cat(self._all_preds, dim=0)
        targets = torch.cat(self._all_targets, dim=0)

        conf_matrix = self._conf_matrix(preds, targets).cpu().numpy()
        self.save_conf_matrix(conf_matrix)

        per_class_count = self._conf_matrix(preds, targets).sum(dim=1)
        per_class_metrics_dict = self.calculate_per_class_metrics(preds, targets, per_class_count)
        avg_metrics_dict = self.calculate_averaged_metrics(preds, targets)

        metrics_dict = {
            "avg_metrics": avg_metrics_dict,
            "per_class_metrics": per_class_metrics_dict,
        }

        json_path = self._log_dir / "classification_report.json"
        with json_path.open("w") as f:
            json.dump(metrics_dict, f, indent=4)

        self._all_preds.clear()
        self._all_targets.clear()

    def calculate_per_class_metrics(
        self,
        preds: torch.Tensor,
        targets: torch.Tensor,
        counts: torch.Tensor,
    ) -> dict[Any, Any]:
        """Compute metrics that are per class.

        Args:
            preds: Predictions from classifier
            targets: Labels
            counts: Amount of occurrences of all classes
        """
        precision = self._precision(preds, targets)
        recall = self._recall(preds, targets)
        f1 = self._f1(preds, targets)

        metrics_dict = {}

        for i in range(self._num_classes):
            output_index_dict = {
                "class_name": f"{self._class_id_to_class_name_mapping[i]}",
                "count": counts[i].numpy().item(),
                "precision": precision[i].numpy().item(),
                "recall": recall[i].numpy().item(),
                "f1": f1[i].numpy().item(),
            }
            metrics_dict[i] = output_index_dict

        return metrics_dict

    def calculate_averaged_metrics(
        self,
        preds: torch.Tensor,
        targets: torch.Tensor,
    ) -> dict[Any, Any]:
        """Calculate metrics over the whole set.

        Args:
            preds: Predictions from classifier
            targets: Labels
        """

        metrics_dict = {}

        count = len(preds)
        metrics_dict["count"] = count

        _LOGGER.info("### Averaged Metrics ###")
        _LOGGER.info(f"count: {count}")

        precision_micro, recall_micro, f1_micro = self.calculate_micro_averaged_metrics(preds, targets)
        metrics_dict["micro-averaged"] = {
            "count": count,
            "precision": precision_micro.numpy().item(),
            "recall": recall_micro.numpy().item(),
            "f1": f1_micro.numpy().item(),
        }

        precision_macro, recall_macro, f1_macro = self.calculate_macro_averaged_metrics(preds, targets)
        metrics_dict["macro-averaged"] = {
            "count": count,
            "precision": precision_macro.numpy().item(),
            "recall": recall_macro.numpy().item(),
            "f1": f1_macro.numpy().item(),
        }

        return metrics_dict

    def calculate_micro_averaged_metrics(self, preds: torch.Tensor, targets: torch.Tensor) -> tuple[Any, Any, Any]:
        """Calculate micro average.

        Args:
            preds: Predictions from classifier
            targets: Labels
        """
        precision = self._precision_micro(preds, targets)
        recall = self._recall_micro(preds, targets)
        f1 = self._f1_micro(preds, targets)

        return (precision, recall, f1)

    def calculate_macro_averaged_metrics(self, preds: torch.Tensor, targets: torch.Tensor) -> tuple[Any, Any, Any]:
        """Calculate macro average.

        Args:
            preds: Predictions from classifier
            targets: Labels
        """
        precision = self._precision_macro(preds, targets)
        recall = self._recall_macro(preds, targets)
        f1 = self._f1_macro(preds, targets)

        return (precision, recall, f1)

    def save_conf_matrix(
        self,
        conf_matrix: np.ndarray[Any, Any],
    ) -> None:
        """Save confusion matrix.

        Args:
            conf_matrix: Confusion matrix to be saved
        """
        self.save_conf_matrix_plot(conf_matrix)
        self.save_conf_matrix_csv(conf_matrix)

    def save_conf_matrix_plot(
        self,
        conf_matrix: np.ndarray[Any, Any],
    ) -> None:
        """Create and save plot of the confusion matrix.

        Args:
            conf_matrix: Confusion matrix to be saved
        """
        plt.figure(figsize=(8, 6))  # Adjust size as needed
        plt.imshow(conf_matrix, cmap="Blues", interpolation="nearest")
        plt.colorbar()

        plt.xlabel("Predicted Label")
        plt.ylabel("True Label")
        plt.title("Confusion Matrix")

        # Save the confusion matrix image
        plt.savefig(f"{self._log_dir}/confusion_matrix.png")
        plt.close()

    def save_conf_matrix_csv(
        self,
        conf_matrix: np.ndarray[Any, Any],
    ) -> None:
        """Save confusion matrix as csv file.

        Args:
            conf_matrix: Confusion matrix to be saved
        """

        conf_matrix_df = pd.DataFrame(conf_matrix.astype(int))
        conf_matrix_df.index = pd.Index([f"True_{i}" for i in range(conf_matrix.shape[0])])
        conf_matrix_df.columns = pd.Index([f"Pred_{i}" for i in range(conf_matrix.shape[1])])

        file_path = self._log_dir / "confusion_matrix.csv"
        conf_matrix_df.to_csv(file_path, sep=";", index=True, header=True)
