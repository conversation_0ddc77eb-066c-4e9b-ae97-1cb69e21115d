"""Module for loading the traffic sign classification data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging

from xtorch.training.data_module import DataLoaderDataModule
from xtorch_usecases.traffic_sign_classification.config.classifier_config_common import (
    TrafficSignClassificationLDMConfig,
)
from xtorch_usecases.traffic_sign_classification.data.augmentations import no_augmentation_params
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel
from xtorch_usecases.traffic_sign_classification.data.dataset_gen1 import TrafficSignClassificationDatasetGen1

_LOGGER = logging.getLogger(__name__)


class TrafficSignClassificationLDMGen1(
    DataLoaderDataModule[
        TrafficSignClassificationLDMConfig,
        ImageAndLabel,  # pyright: ignore[reportInvalidTypeArguments]
    ]
):
    """Definition of the data module for the traffic sign classification usecase, gen1."""

    def __init__(self, *, config: TrafficSignClassificationLDMConfig, input_size: list[int], is_pretrain: bool) -> None:
        """Initializes the data module.

        Args:
            config: Dict with config parameters for the data module.
            input_size: List containing height and width of model input data
            is_pretrain: Flag to indicate wether the LDM will be used for the pretraining stage or not.
        """
        super().__init__(config=config)

        self._input_size = input_size
        self._is_pretrain = is_pretrain
        self._train_data_path = config.train_data_csv_path
        self._val_data_path = config.val_data_csv_path
        self._augmentation_params = config.augmentation_params

    def build_train_dataset(self) -> TrafficSignClassificationDatasetGen1:
        """Builds the training dataset."""
        train_dataset = TrafficSignClassificationDatasetGen1(
            dataset_csv_path=self._config.train_data_csv_path,
            input_size=self._input_size,
            is_pretrain=self._is_pretrain,
            augmentation_params=self._config.augmentation_params,
        )
        _LOGGER.info(f"train_dataset count: {len(train_dataset)}.")

        return train_dataset

    def build_validation_dataset(self) -> TrafficSignClassificationDatasetGen1 | None:
        """Builds the validation dataset.

        Note:
            No validation is performed in the 'pretrain' stage. Therefore `None` is returned in that case.
        """
        if self._is_pretrain:
            return None

        val_dataset = TrafficSignClassificationDatasetGen1(
            dataset_csv_path=self._config.val_data_csv_path,
            input_size=self._input_size,
            is_pretrain=self._is_pretrain,
            augmentation_params=no_augmentation_params(),  # apply no augmentations to validation data
        )
        _LOGGER.info(f"val_dataset count: {len(val_dataset)}.")

        return val_dataset

    def build_test_dataset(self) -> TrafficSignClassificationDatasetGen1 | None:
        """Builds the test dataset."""
        error_message = "Test dataset is not implemented yet."
        raise NotImplementedError(error_message)

    def build_predict_dataset(self) -> TrafficSignClassificationDatasetGen1 | None:
        """Builds the predict dataset."""
        error_message = "Predict dataset is not implemented yet."
        raise NotImplementedError(error_message)
