"""The training module for the traffic sign classification usecase, Gen2."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path
from typing import Any

import torch
from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from torch import nn, optim
from ts_label_mapping.classifier import ClassMappings

from data_formats.traffic_sign.attribute_extractor.attribute_extractor import Weekday
from xcontract.data.definitions.usage import ValueKey
from xtorch.losses.interface import LossOutput
from xtorch.training.metric_handling import MetricsContainer
from xtorch.training.training_module import TrainingModule
from xtorch_usecases.common.pipeline import TRAIN_CHECKPOINT_SUBFOLDER
from xtorch_usecases.traffic_sign_classification.callbacks.gen2_attributes_metric import MultiClassAndOCRMetricsCallback
from xtorch_usecases.traffic_sign_classification.callbacks.loss_summary_logger import LossSummaryLogger
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen2 import (
    TrafficSignClassificationConfigGen2,
)
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel
from xtorch_usecases.traffic_sign_classification.losses.ts_classifier_gen2_loss import LossWeights, TsClassifierGen2Loss
from xtorch_usecases.traffic_sign_classification.models.encoders.encoder_parameters_gen2 import (
    EncoderParametersGen2,
)
from xtorch_usecases.traffic_sign_classification.models.heads.ts_classifier_gen2_head import (
    ModelReturnType,
    TsClassifierGen2HeadParameters,
)
from xtorch_usecases.traffic_sign_classification.models.network_gen2 import (
    ClassifierModelGen2,
    ClassifierModelGen2Parameters,
)


class TrafficSignClassificationTrainingLMGen2(
    TrainingModule[
        TrafficSignClassificationConfigGen2,
        ImageAndLabel,  # pyright: ignore[reportInvalidTypeArguments]
        ModelReturnType,
        ClassifierModelGen2,
    ]
):  # pylint: disable=too-many-ancestors
    """The traffic sign classification experiment."""

    def __init__(self, config: TrafficSignClassificationConfigGen2) -> None:
        """Instantiates all the resources necessary for the module."""

        super().__init__(config=config)

    def forward(self, batch: ImageAndLabel) -> ModelReturnType:
        """Forward pass through the model."""
        model_inputs = batch.image[ValueKey.DATA]
        preds = self._model(model_inputs)
        return preds

    def get_eval_callbacks(self, config: TrafficSignClassificationConfigGen2) -> list[Any]:
        """Return the callbacks for the training."""
        callbacks: list[Any] = [
            MultiClassAndOCRMetricsCallback(
                num_classes=self._config.model.num_classes,
                log_dir=Path(self._config.output_folder) / "results",
                token_to_letter_lookup_path=Path(
                    self._config.data.train.train_data_csv_path.parent / "token_to_letter_lookup.json"
                ),
                head_parameters=self._get_model_params()[1],
            )
        ]

        return callbacks

    def get_train_callbacks(self, config: TrafficSignClassificationConfigGen2) -> list[Any]:
        """Return the callbacks for the training."""
        callbacks: list[Any] = [
            LossSummaryLogger(
                metrics_to_log={
                    "train": [
                        "train_total_loss_epoch",
                    ],
                    "val": [
                        "val_total_loss_epoch",
                    ],
                }
            ),
            ModelCheckpoint(
                dirpath=Path(config.output_folder) / TRAIN_CHECKPOINT_SUBFOLDER,
                save_weights_only=True,
                save_last=True,
                monitor="val_total_loss_epoch",
                mode="min",
                filename="best",
            ),
        ]
        return callbacks

    def build_model(self) -> ClassifierModelGen2:
        """Instantiate a ClassifierModel model that expects rgb input images."""

        encoder_parameters, head_parameters = self._get_model_params()

        classifier_parameters = ClassifierModelGen2Parameters(
            encoder_parameters=encoder_parameters,
            head_parameters=head_parameters,
            rgb_bitdepth=self._config.input.rgb_bitdepth,
            yuv_bitdepth=self._config.input.yuv_bitdepth,
        )

        return ClassifierModelGen2(parameters=classifier_parameters)

    def _get_model_params(self) -> tuple[EncoderParametersGen2, TsClassifierGen2HeadParameters]:
        """Return parameters for encoder and head of model."""

        encoder_parameters = EncoderParametersGen2(
            input_size=self._config.input.input_height,
            input_channels=self._config.input.input_channels,
            num_filters=self._config.model.encoder_num_filters,
            resnet_config=self._config.model.encoder_resnet_config,
            max_filters=self._config.model.encoder_max_filters,
        )

        class_mappings = ClassMappings()
        number_class_ids = len(class_mappings.get_gapless_class_names(country_code="ALL"))

        head_parameters = TsClassifierGen2HeadParameters(
            number_class_entries=self._config.model.number_class_entries,
            number_class_ids=number_class_ids,
            max_text_length=self._config.model.max_text_length,
            alphabet_size=self._config.model.alphabet_size,
            word_attributes=self._config.model.word_attributes,
            number_weekday_entries=self._config.model.number_weekday_entries,
            number_weekday_indices=len(Weekday),
            interchangeable_groups=self._config.model.interchangeable_groups,
        )

        return encoder_parameters, head_parameters

    def build_optimizer(self) -> optim.Optimizer:
        """Construct the AdamW optimizer used in this experiment."""

        return optim.AdamW(
            self._model.parameters(), lr=self._config.optimizer.lr, weight_decay=self._config.optimizer.weight_decay
        )

    def build_lr_scheduler(self, optimizer: optim.Optimizer) -> None | optim.lr_scheduler.LRScheduler:
        """Construct the lr scheduler."""

        t_max = (
            self._config.optimizer.scheduler_t_max
            if self._config.optimizer.scheduler_t_max
            else self.trainer.max_epochs
        )
        assert t_max is not None
        return torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=t_max)

    def build_loss(self) -> nn.Module:
        """Instantiates the CrossEntropy loss module."""

        loss_weights = LossWeights(
            self._config.loss.class_id_weight,
            self._config.loss.class_value_weight,
            self._config.loss.word_attribute_weight,
            self._config.loss.weekday_weight,
            self._config.loss.nothing_weight,
        )
        _, head_parameters = self._get_model_params()
        return TsClassifierGen2Loss(head_parameters, loss_weights)

    def build_metrics(self) -> MetricsContainer[ModelReturnType, ImageAndLabel]:  # pyright: ignore[reportInvalidTypeArguments]
        """Instantiates the metrics."""

        metrics: MetricsContainer[ModelReturnType, ImageAndLabel] = MetricsContainer()  # pyright: ignore[reportInvalidTypeArguments]
        # To be added

        return metrics

    def compute_loss(self, batch: ImageAndLabel, predictions: ModelReturnType) -> LossOutput:
        """Compute the loss."""

        targets = batch.label[ValueKey.DATA]
        return self._loss_fn(predictions, targets)
