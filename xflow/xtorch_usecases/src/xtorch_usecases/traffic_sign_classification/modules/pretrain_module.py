"""The training logic for the traffic sign classification experiment."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any, cast

import torch
from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from torch import nn, optim

from xcontract.data.definitions.usage import ValueKey
from xtorch.losses.interface import LossOutput
from xtorch.training.metric_handling import MetricsContainer
from xtorch.training.training_module import TrainingModule
from xtorch_usecases.common.pipeline import TRAIN_CHECKPOINT_SUBFOLDER
from xtorch_usecases.traffic_sign_classification.callbacks.loss_summary_logger import LossSummaryLogger
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen1 import (
    TrafficSignClassificationConfigGen1,
)
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel
from xtorch_usecases.traffic_sign_classification.losses.contrastive_loss import SupervisedContrastiveLoss
from xtorch_usecases.traffic_sign_classification.models.encoders.encoder_parameters_gen1 import (
    EncoderParametersGen1,
    get_encoder_output_feature_count,
)
from xtorch_usecases.traffic_sign_classification.models.heads.fully_connected_head import FullyConnectedHeadParameters
from xtorch_usecases.traffic_sign_classification.models.network_gen1 import (
    ClassifierModelGen1,
    ClassifierModelGen1Parameters,
)


class TrafficSignClassificationPretrainingLM(
    TrainingModule[
        TrafficSignClassificationConfigGen1,
        ImageAndLabel,  # pyright: ignore[reportInvalidTypeArguments]
        torch.Tensor,
        ClassifierModelGen1,
    ]
):  # pylint: disable=too-many-ancestors
    """The traffic sign classification experiment."""

    def __init__(self, config: TrafficSignClassificationConfigGen1) -> None:
        """Instantiates all the resources necessary for the module."""

        super().__init__(config=config)

    def forward(self, batch: ImageAndLabel) -> torch.Tensor:
        """Forward pass through the model."""

        # each datapoint has a shape of (batch_size, channels, height, width * 2)
        # each datapoints contains two images, both are augmented versions of the same image
        input_data = cast(torch.Tensor, batch.image[ValueKey.DATA])
        split_size = input_data.shape[-1] // 2
        datapoints_1, datapoints_2 = torch.split(input_data, split_size_or_sections=split_size, dim=-1)

        model_inputs = torch.cat([datapoints_1, datapoints_2], dim=0)
        preds = self._model(model_inputs)

        return preds

    def get_pretrain_callbacks(self, config: TrafficSignClassificationConfigGen1) -> list[Any]:
        """Return the callbacks for the training."""
        assert config.trainer.default_root_dir is not None
        callbacks: list[Any] = [
            LossSummaryLogger(
                metrics_to_log={
                    "train": ["train_total_loss_epoch"],
                    "val": [],
                }
            ),
            ModelCheckpoint(
                dirpath=config.trainer.default_root_dir / TRAIN_CHECKPOINT_SUBFOLDER,
                save_weights_only=True,
                save_last=True,
                monitor="train_total_loss_epoch",
                mode="min",
                filename="best",
            ),
        ]
        return callbacks

    def build_model(self) -> ClassifierModelGen1:
        """Instantiate a ClassifierModel model."""

        encoder_parameters = EncoderParametersGen1(
            type=self._config.model.encoder_type,
            input_size=self._config.input.input_height,
            input_channels=self._config.input.input_channels,
            num_filters=self._config.model.encoder_num_filters,
            num_block_list=self._config.model.encoder_num_block_list,
            max_filters=self._config.model.encoder_max_filters,
        )

        head_num_features_list = self._config.model.head_num_neuron_list + [
            self._config.model.head_contrastive_projection_features
        ]
        is_pretrain = True

        head_parameters = FullyConnectedHeadParameters(
            in_features=get_encoder_output_feature_count(encoder_parameters),
            num_features_list=head_num_features_list,
            dropout_rates_list=self._config.model.head_contrastive_pretrain_dropout_rates_list,
            pretrain=is_pretrain,
        )

        classifier_parameters = ClassifierModelGen1Parameters(
            encoder_parameters=encoder_parameters,
            head_parameters=head_parameters,
            rgb_bitdepth=self._config.input.rgb_bitdepth,
            yuv_bitdepth=self._config.input.yuv_bitdepth,
        )

        return ClassifierModelGen1(parameters=classifier_parameters)

    def build_optimizer(self) -> optim.Optimizer:
        """Construct the AdamW optimizer used in this experiment."""

        return optim.AdamW(
            self._model.parameters(), lr=self._config.optimizer.lr, weight_decay=self._config.optimizer.weight_decay
        )

    def build_lr_scheduler(self, optimizer: optim.Optimizer) -> None | optim.lr_scheduler.LRScheduler:
        """Construct the lr scheduler."""

        t_max = (
            self._config.optimizer.scheduler_t_max
            if self._config.optimizer.scheduler_t_max
            else int(self.trainer.estimated_stepping_batches)
        )
        return torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=t_max)

    def build_loss(self) -> nn.Module:
        """Instantiates the CrossEntropy loss module."""

        return SupervisedContrastiveLoss()

    def build_metrics(self) -> MetricsContainer[torch.Tensor, ImageAndLabel]:  # pyright: ignore[reportInvalidTypeArguments]
        """Instantiates the metrics."""

        metrics: MetricsContainer[torch.Tensor, ImageAndLabel] = MetricsContainer()  # pyright: ignore[reportInvalidTypeArguments]
        return metrics

    def compute_loss(self, batch: ImageAndLabel, predictions: torch.Tensor) -> LossOutput:
        """Compute the loss."""

        targets = cast(torch.Tensor, batch.label[ValueKey.DATA])

        # since each datapoint from the batch consists of two images, we need to double the targets
        labels = torch.cat([targets, targets], dim=0)

        total_loss, _, _, _ = self._loss_fn(predictions, labels)
        return LossOutput(total_loss=total_loss)
