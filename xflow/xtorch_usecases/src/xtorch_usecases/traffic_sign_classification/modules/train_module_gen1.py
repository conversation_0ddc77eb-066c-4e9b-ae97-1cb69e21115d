"""The training module for the traffic sign classification usecase, Gen1."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any

import torch
from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from torch import nn, optim
from torchmetrics import Accuracy, F1Score

from xcontract.data.definitions.usage import ValueKey
from xtorch.losses.interface import LossOutput
from xtorch.training.metric_handling import MetricsContainer
from xtorch.training.training_module import TrainingModule
from xtorch_usecases.common.pipeline import TRAIN_CHECKPOINT_SUBFOLDER
from xtorch_usecases.traffic_sign_classification.callbacks.loss_summary_logger import LossSummaryLogger
from xtorch_usecases.traffic_sign_classification.callbacks.mcl_metrics_callback import MultiClassMetricsCallback
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen1 import (
    TrafficSignClassificationConfigGen1,
)
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel
from xtorch_usecases.traffic_sign_classification.models.encoders.encoder_parameters_gen1 import (
    EncoderParametersGen1,
    get_encoder_output_feature_count,
)
from xtorch_usecases.traffic_sign_classification.models.heads.fully_connected_head import FullyConnectedHeadParameters
from xtorch_usecases.traffic_sign_classification.models.network_gen1 import (
    ClassifierModelGen1,
    ClassifierModelGen1Parameters,
)


class TrafficSignClassificationTrainingLMGen1(
    TrainingModule[
        TrafficSignClassificationConfigGen1,
        ImageAndLabel,  # pyright: ignore[reportInvalidTypeArguments]
        torch.Tensor,
        ClassifierModelGen1,
    ]
):  # pylint: disable=too-many-ancestors
    """The traffic sign classification experiment."""

    def __init__(self, config: TrafficSignClassificationConfigGen1) -> None:
        """Instantiates all the resources necessary for the module."""

        super().__init__(config=config)

    def forward(self, batch: ImageAndLabel) -> torch.Tensor:
        """Forward pass through the model."""
        model_inputs = batch.image[ValueKey.DATA]
        preds = self._model(model_inputs)
        return preds

    def get_eval_callbacks(self, config: TrafficSignClassificationConfigGen1) -> list[Any]:
        """Return the callbacks for the training."""
        assert config.trainer.default_root_dir is not None
        callbacks: list[Any] = [
            MultiClassMetricsCallback(
                num_classes=config.model.num_classes, log_dir=config.trainer.default_root_dir / "results"
            )
        ]
        return callbacks

    def get_train_callbacks(self, config: TrafficSignClassificationConfigGen1) -> list[Any]:
        """Return the callbacks for the training."""
        assert config.trainer.default_root_dir is not None
        callbacks: list[Any] = [
            LossSummaryLogger(
                metrics_to_log={
                    "train": [
                        "train_total_loss_epoch",
                        "train_accuracy_epoch",
                    ],
                    "val": [
                        "val_total_loss_epoch",
                        "val_accuracy_epoch",
                    ],
                }
            ),
            ModelCheckpoint(
                dirpath=config.trainer.default_root_dir / TRAIN_CHECKPOINT_SUBFOLDER,
                save_weights_only=True,
                save_last=True,
                monitor="val_total_loss_epoch",
                mode="min",
                filename="best",
            ),
        ]
        return callbacks

    def build_model(self) -> ClassifierModelGen1:
        """Instantiate a ClassifierModel model that expects rgb input images."""

        encoder_parameters, head_parameters = self._get_model_params()

        classifier_parameters = ClassifierModelGen1Parameters(
            encoder_parameters=encoder_parameters,
            head_parameters=head_parameters,
            rgb_bitdepth=self._config.input.rgb_bitdepth,
            yuv_bitdepth=self._config.input.yuv_bitdepth,
        )

        return ClassifierModelGen1(parameters=classifier_parameters)

    def _get_model_params(self) -> tuple[EncoderParametersGen1, FullyConnectedHeadParameters]:
        """Return parameters for encoder and head of model."""

        encoder_parameters = EncoderParametersGen1(
            type=self._config.model.encoder_type,
            input_size=self._config.input.input_height,
            input_channels=self._config.input.input_channels,
            num_filters=self._config.model.encoder_num_filters,
            num_block_list=self._config.model.encoder_num_block_list,
            max_filters=self._config.model.encoder_max_filters,
        )

        head_num_features_list = self._config.model.head_num_neuron_list + [self._config.model.num_classes]
        is_pretrain = False

        head_parameters = FullyConnectedHeadParameters(
            in_features=get_encoder_output_feature_count(encoder_parameters),
            num_features_list=head_num_features_list,
            dropout_rates_list=self._config.model.head_crossentropy_train_dropout_rates_list,
            pretrain=is_pretrain,
        )

        return encoder_parameters, head_parameters

    def build_optimizer(self) -> optim.Optimizer:
        """Construct the AdamW optimizer used in this experiment."""

        return optim.AdamW(
            self._model.parameters(), lr=self._config.optimizer.lr, weight_decay=self._config.optimizer.weight_decay
        )

    def build_lr_scheduler(self, optimizer: optim.Optimizer) -> None | optim.lr_scheduler.LRScheduler:
        """Construct the lr scheduler."""

        t_max = (
            self._config.optimizer.scheduler_t_max
            if self._config.optimizer.scheduler_t_max
            else int(self.trainer.estimated_stepping_batches)
        )
        return torch.optim.lr_scheduler.CosineAnnealingLR(optimizer, T_max=t_max)

    def build_loss(self) -> nn.Module:
        """Instantiates the CrossEntropy loss module."""

        return nn.CrossEntropyLoss(reduction=self._config.optimizer.reduction)

    def build_metrics(self) -> MetricsContainer[torch.Tensor, ImageAndLabel]:  # pyright: ignore[reportInvalidTypeArguments]
        """Instantiates the metrics."""

        metrics: MetricsContainer[torch.Tensor, ImageAndLabel] = MetricsContainer()  # pyright: ignore[reportInvalidTypeArguments]
        metrics.add(
            "accuracy",
            Accuracy(num_classes=self._config.model.num_classes, task="multiclass"),
            targets_access_function=lambda x: x.label[ValueKey.DATA],  #  access targets from batch list
        )
        metrics.add(
            "f1",
            F1Score(num_classes=self._config.model.num_classes, task="multiclass"),
            targets_access_function=lambda x: x.label[ValueKey.DATA],  #  access targets from batch list
        )
        return metrics

    def compute_loss(self, batch: ImageAndLabel, predictions: torch.Tensor) -> LossOutput:
        """Compute the loss."""

        targets = batch.label[ValueKey.DATA]
        return LossOutput(total_loss=self._loss_fn(predictions, targets))
