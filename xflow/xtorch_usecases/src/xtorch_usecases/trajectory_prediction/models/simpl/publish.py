"""Publish script for the trajectory prediction usecase."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2023 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import importlib.resources as pkg_resources
import logging
from pathlib import Path

from azure_tools.logging import runs_on_aml_node
from xtension.azure_ct import azureml_publishing  # pylint: disable=forbidden-import
from xtorch.config.hydra_loading import load_config
from xtorch_usecases.trajectory_prediction.models.simpl.config import SIMPLExperimentConfig
from xtorch_usecases.trajectory_prediction.models.simpl.qnn.conversion import trigger_embedded_qnn_eval_and_profiling

PCKG_NAME = "xtorch_usecases.trajectory_prediction.models.simpl"
CONFIG_ROOT_DIR = str(pkg_resources.path(PCKG_NAME, "hydra_config"))


def _parse_input_folders(argv: list[str] | None) -> tuple[list[str], list[str]]:
    """Parse input folders from command line arguments.

    Extra argument parser to grab "input_folder" before it is read by "parse_cli_arguments", to allow giving multiple
    input folders to this stage.

    Args:
        argv: Command line arguments
    Returns:
        input_folders: List of input folders
        remaining_args: Remaining command line arguments
    """

    assert argv is not None

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_folder",
        type=str,
        nargs="*",
        help=(
            "Input folder where the stage outputs are stored. One can also specify multiple folders. "
            "In that case, all files are copied together into a single folder during the publishing step."
        ),
    )
    args, remaining_args = parser.parse_known_args(argv)
    input_folders = args.input_folder
    assert len(input_folders) > 0
    return input_folders, remaining_args


def _setup_argparse() -> argparse.ArgumentParser:
    """Parse CLI flags that aren't Hydra-style overrides."""

    parser = argparse.ArgumentParser(add_help=False)
    parser.add_argument("--short_run", action="store_true", help="Short run flag (ignored here).")
    # like evaluate.py, but allow multiple input folders
    parser.add_argument(
        "--input_folder", type=str, nargs="*", default=None, help="One or more input folders to publish."
    )
    parser.add_argument(
        "--publish_folder",
        type=str,
        default=(Path("outputs") / "publish").as_posix(),
        help="Where to copy outputs before publishing.",
    )
    parser.add_argument(
        "--publish_to_model_store", action="store_true", help="Also push model+metrics into the AML model store."
    )
    parser.add_argument(
        "--dataset_path", type=str, default=None, help="Local dataset path; overrides the AZUREML_DATAREFERENCE… env."
    )
    return parser


def main(config_name: str, config_overrides: list[str] | None = None) -> None:
    """Entry point for publishing."""

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # Grab any --input_folder flags first
    input_folders, overrides = _parse_input_folders(config_overrides)

    # Parse all the publish-stage flags
    parser = _setup_argparse()
    args, overrides = parser.parse_known_args(overrides)

    # Turn dataset_path into a Hydra override if provided
    if args.dataset_path:
        overrides.append(f"data_config.dataset_path={args.dataset_path}")

    logger.info("Loading config %s with overrides: %s", config_name, overrides)
    config: SIMPLExperimentConfig = load_config(
        config_root=Path(CONFIG_ROOT_DIR),
        config_name=config_name,
        config_cls=SIMPLExperimentConfig,
        config_overrides=overrides,
    )

    # Copy staging inputs
    logger.info(f"Copying input folders to publish_folder: {args.publish_folder}")
    for input_folder in input_folders:
        azureml_publishing.copy_data(input_folder=input_folder, output_folder=args.publish_folder)

    if args.publish_to_model_store:
        if runs_on_aml_node():
            logger.info("Publishing outputs and metrics.")
            azureml_publishing.publish_experiment_results(
                args.publish_folder,
                metrics={},
                metrics_version=0,  # This effectively disables publish to model store
            )
        else:
            logger.info("Local execution does not support publishing to Azure.")

    # Trigger embedded QNN evaluation and profiling if configured
    trigger_embedded_qnn_eval_and_profiling(config, config_name)
