"""Utility script for generating random pointcloud."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path

import torch

from xtorch_usecases.deep_radar_objects.helper.cross_repo_tests.common import write_binary


def create_random_pointcloud(
    num_points: int,
    ranges: tuple[tuple[float, float], ...],
    boundaries: dict[int, tuple[float, float]] = {},  # noqa: B006
) -> torch.Tensor:
    """Create random pointcloud with given size and predefined ranges.

    Args:
        num_points: number of output point cloud
        ranges: the value ranges
        boundaries: boundaries where points should be avoided

    Returns:
        output pointcloud
    """
    torch.manual_seed(0)

    num_element = len(ranges)
    pointcloud = torch.rand(num_points, num_element, device="cpu")
    for i, (r0, r1) in enumerate(ranges):
        # transform the pointcloud values from [0,1] to defined ranges
        pointcloud[:, i] *= r1 - r0
        pointcloud[:, i] += r0
        if i in boundaries:
            thres = 0.5
            shift = 2.0
            lower = boundaries[i][0]
            upper = boundaries[i][0]
            pointcloud[((lower - thres) < pointcloud[:, i]) & (pointcloud[:, i] < (lower + thres))] += shift
            pointcloud[((upper - thres) < pointcloud[:, i]) & (pointcloud[:, i] < (upper + thres))] += shift
    return pointcloud


if __name__ == "__main__":
    # Please change the property as you need (including the attributes, not only for radar pointcloud)
    # x, y, z, velocity, RCS, delta_t
    ranges = ((-100.0, 100.0), (-40.0, 40.0), (-3.0, 3.0), (-30.0, 30.0), (-30.0, 30.0), (-0.5, 0.0))
    # Please change values around the boundaries
    boundaries = {0: (-48.0, 112.0), 1: (-48.0, 48.0)}
    pointcloud = create_random_pointcloud(100, ranges, boundaries)
    path = "xtorch_usecases/src/xtorch_usecases/deep_radar_objects/helper/cross_repo_tests/random_pointcloud"
    write_binary(pointcloud, Path(path))
    print("shape of the tensor ", pointcloud.shape)
