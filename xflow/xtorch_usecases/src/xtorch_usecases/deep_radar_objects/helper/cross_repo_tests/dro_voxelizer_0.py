"""Update the test data for DRO voxelizer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import json
from pathlib import Path

import torch

from xtorch.geometry.volumes.base_volume import VolumeType
from xtorch.geometry.volumes.common import VolumeParams
from xtorch_usecases.deep_radar_objects.helper.cross_repo_tests.common import (
    get_radar_range,
    get_root_dir,
    load_pointcloud,
    quantize,
    write_binary,
)
from xtorch_usecases.deep_radar_objects.nn.voxel_encoders.pypillars_voxelizer_encoder import (
    DroGridFeatureIndex,
    PypillarsVoxelizerEncoder,
)


def create(path_in: str, path_out: str) -> None:
    """Create test data."""
    with Path(path_in).open(encoding="utf-8") as file_in:
        data_in = json.load(file_in)

    data_in["params"].update(get_radar_range())

    num_voxels = int(data_in["params"]["num_voxels"])
    grid_size_x = int(data_in["params"]["grid_size_x"])
    grid_size_y = int(data_in["params"]["grid_size_y"])
    lower_bound_x = float(data_in["params"]["lower_bound_x"])
    upper_bound_x = float(data_in["params"]["upper_bound_x"])
    lower_bound_y = float(data_in["params"]["lower_bound_y"])
    upper_bound_y = float(data_in["params"]["upper_bound_y"])
    normalize_input_values = True
    device = data_in["device"]

    volume_params = VolumeParams(
        type_name=VolumeType.CARTESIAN.name,
        lower_bound=[lower_bound_x, lower_bound_y, -16],
        upper_bound=[upper_bound_x, upper_bound_y, 16],
    )

    if "pointcloud" in data_in["input"]:
        pointcloud = torch.as_tensor(
            data_in["input"]["pointcloud"],
            device=device,
        )
    else:
        assert "path" in data_in["input"]
        assert "size" in data_in["input"]
        pointcloud = load_pointcloud(
            path=(get_root_dir() / data_in["input"]["path"]), size=data_in["input"]["size"], ch="f", device=device
        )

    assert abs((upper_bound_x - lower_bound_x) / grid_size_x - 0.5) < 1e-5
    assert abs((upper_bound_y - lower_bound_y) / grid_size_y - 0.5) < 1e-5

    voxelizer = PypillarsVoxelizerEncoder(
        voxel_size=((upper_bound_x - lower_bound_x) / grid_size_x, (upper_bound_y - lower_bound_y) / grid_size_y, 32),
        volume_params=volume_params,
        in_channels=DroGridFeatureIndex.NUMBER_OF_FEATURES,
        out_channels=32,
        num_voxels=num_voxels,
        num_points_per_voxel=10,
        onnx_compatible=True,
        raw_feature_output=True,
        normalize_input_values=normalize_input_values,
    )

    pillar_features_enc, pillar_coors = voxelizer(pointcloud)

    pillar_features_limits = pillar_features_enc.min(), pillar_features_enc.max()  # log for debugging
    pillar_features_enc, feature_quant_offset, feature_quant_scale = quantize(pillar_features_enc)

    test_name = Path(path_in).stem
    if test_name.endswith(".in"):
        test_name = test_name.split(".")[0]

    write_binary(pillar_features_enc, get_root_dir() / (test_name + "_pillar_features_enc.bin"))
    write_binary(pillar_coors, get_root_dir() / (test_name + "_pillar_coors.bin"))

    data_in["output"] = {
        "pillar_features_enc": {
            "size": list(pillar_features_enc.shape),
            "path": test_name + "_pillar_features_enc.bin",
            "offset": feature_quant_offset,
            "scale": feature_quant_scale,
            "min_before_quant": pillar_features_limits[0].item(),
            "max_before_quant": pillar_features_limits[1].item(),
        },
        "pillar_coors": {"size": list(pillar_coors.shape), "path": test_name + "_pillar_coors.bin"},
    }

    with Path(path_out).open("w", encoding="utf-8") as file_out:
        json.dump(data_in, fp=file_out)
