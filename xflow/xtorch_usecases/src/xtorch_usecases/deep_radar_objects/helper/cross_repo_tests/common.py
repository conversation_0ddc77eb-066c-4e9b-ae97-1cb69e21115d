"""Utilities."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import math
import os
import struct
from pathlib import Path

import torch

from xtorch_usecases.multimodal.configs.geometry import DEFAULT_FAR_RANGE_RADAR_VOLUME


def get_root_dir() -> Path:
    """Get the root directory for cross-repo-tests.

    Returns:
        path of cross-repo-tests
    """
    return Path(os.path.realpath(__file__)).parent


def write_binary(tensor: torch.Tensor, path: Path) -> None:
    """Write a tensor to a binary file.

    Args:
        tensor: the tensor to write
        path: the path to write
    """
    tensor.numpy().tofile(str(path))


def load_pointcloud(path: Path, size: tuple[int, ...], ch: str = "f", device: str = "cpu") -> torch.Tensor:
    """Load one pointcloud.

    Args:
        path: path to the file
        size: size of the expected tensor size
        ch: which character represents the type of element, now only float32
        device: _description_

    Returns:
        torch.Tensor: _description_
    """
    with path.open("rb") as file:
        binary_data = file.read()

    supported_sizes = {"f": 4}
    num_elements = len(binary_data) // supported_sizes[ch]
    assert num_elements == math.prod(size)
    data = torch.tensor(list(struct.unpack(ch * num_elements, binary_data)), device=device)
    return data.reshape(*size)


def get_radar_range() -> dict[str, float]:
    """Get the default range of radar.

    Returns:
        dict[str, float]: ranges
    """
    return {
        "lower_bound_x": DEFAULT_FAR_RANGE_RADAR_VOLUME.lower_bound[0].item(),
        "upper_bound_x": DEFAULT_FAR_RANGE_RADAR_VOLUME.upper_bound[0].item(),
        "lower_bound_y": DEFAULT_FAR_RANGE_RADAR_VOLUME.lower_bound[1].item(),
        "upper_bound_y": DEFAULT_FAR_RANGE_RADAR_VOLUME.upper_bound[1].item(),
    }


def quantize(data_in: torch.Tensor) -> tuple[torch.Tensor, float, float]:
    """Quantize one tensor with its max and min to uint8."""
    if abs(data_in.min() - 0.0) > 1e-6 or abs(data_in.max() - 1.0) > 1e-6:
        print(
            f"The minima and maxima are not 0 and 1, but {data_in.min()} and {data_in.max()}. "
            "If there is test failure, please check the quantization."
        )
    offset = data_in.min()
    scale = (data_in.max() - offset) / 255.0
    data_out = (data_in - offset) / scale
    data_out = torch.clamp(data_out.to(torch.uint8), min=0, max=255)
    return data_out, offset.item(), scale.item()
