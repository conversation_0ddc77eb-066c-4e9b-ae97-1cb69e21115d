"""Pypillar Voxelizer Encoder implementation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import math
import warnings
from enum import IntEnum
from typing import NamedTuple, Union

import numpy as np
import torch
from numba import jit
from numpy.typing import NDArray
from torch import Tensor, nn
from torch.nn import functional as F

from xtorch.geometry.volumes.common import VolumeParams
from xtorch.nn.encoders.voxelizers.definitions import (
    PointCloudFeatureIndex,
    ValueRange,
)
from xtorch_usecases.common.datasets.alliance.definitions import (
    ALLIANCE_RADAR_FEATURE_STATISTICS,
    RADAR_ACCUMULATION_WINDOW_MILLI_SECONDS,
)


class DroGridFeatureIndex(IntEnum):
    """Feature index for deep radar objects.

    The feature index is used to access the input features list of DRO network.
    The order of the features is important and must be consistent with the pace repo order.
    """

    X_REL = 0
    Y_REL = 1
    X = 2
    Y = 3
    Z = 4
    RADIAL_DISTANCE = 5
    NUMBER_OF_POINTS = 6
    RADIAL_VELOCITY_OVER_GROUND = 7
    RCS = 8
    DELTA_T = 9
    NUMBER_OF_FEATURES = 10


@jit(nopython=True, nogil=True)
def numba_create_voxelized_output_single_batch(
    max_num_voxels: int,
    num_points_per_voxel: int,
    pillar_features_enc: NDArray[np.float32],
    pillar_coors: NDArray[np.int32],
    index_1d: NDArray[np.int32],
) -> tuple[NDArray[np.float32], NDArray[np.int32]]:
    """Creates the voxelized output for a single batch."""
    voxels = np.zeros(
        (max_num_voxels, num_points_per_voxel, pillar_features_enc.shape[-1]), dtype=pillar_features_enc.dtype
    )
    voxel_coords = np.zeros((max_num_voxels, 2), dtype=pillar_coors.dtype)
    sort_arg = np.argsort(index_1d)
    voxel_ind = 0
    cur_voxel_count = 0
    prev_1d = index_1d[sort_arg[0]]
    for j in range(len(pillar_features_enc)):
        cur_sort_idx = sort_arg[j]
        cur_1d = index_1d[cur_sort_idx]
        if cur_1d != prev_1d:
            cur_voxel_count = 0
            voxel_ind = voxel_ind + 1
            prev_1d = cur_1d
        if cur_voxel_count >= num_points_per_voxel:
            continue
        if voxel_ind >= max_num_voxels:
            break
        voxels[voxel_ind, cur_voxel_count] = pillar_features_enc[cur_sort_idx]
        current_coords = pillar_coors[cur_sort_idx]
        voxel_coords[voxel_ind, 0] = current_coords[1]
        voxel_coords[voxel_ind, 1] = current_coords[2]
        cur_voxel_count = cur_voxel_count + 1
    return voxels, voxel_coords


class RadarVoxelizerData(NamedTuple):
    """Class which holds the output of the (DRO) Radar Voxelizer."""

    voxel_features_enc: Tensor
    voxel_coords: Tensor


class PypillarsVoxelizerEncoder(nn.Module):
    """Voxel encoder that implements the Pypillars algorithm for point cloud voxelization.

    Args:
        voxel_size: The size of each voxel in the x, y and z dimensions.
        volume_params: The range of the point cloud in the x, y and z dimensions
                                  [xmin, ymin, zmin, xmax, ymax, zmax].
        in_channels: The number of input channels.
        out_channels: The number of output channels (has to be provided for raw_feature_output == False).
        num_voxels: The number of voxels which will be generated.
        num_points_per_voxel: The number of points per voxel.
        onnx_compatible: Whether the encoder should be ONNX compatible.
        raw_feature_output: Whether to output raw features.
        image_like_feature_representation: Whether to use an image-like feature representation.
        normalize_input_values: Voxelized values shall be normalized.
    """

    def __init__(  # noqa: PLR0913, PLR0915
        self,
        voxel_size: tuple[float, float, float],
        volume_params: VolumeParams,
        in_channels: int,
        out_channels: int | None = None,
        num_voxels: int = 10000,
        num_points_per_voxel: int = 10,
        *,
        onnx_compatible: bool = True,
        raw_feature_output: bool = False,
        normalize_input_values: bool = True,
        image_like_feature_representation: bool = False,
        normalization_statistics: dict[str, ValueRange] = ALLIANCE_RADAR_FEATURE_STATISTICS,
        accumulation_window: ValueRange = RADAR_ACCUMULATION_WINDOW_MILLI_SECONDS,
    ) -> None:
        """Constructor."""
        super().__init__()

        assert accumulation_window.min == normalization_statistics["delta_t"].min, (
            f"accumulation_window must match the delta_t statistics! The values are: \
                {accumulation_window.min} vs. {normalization_statistics['delta_t'].min}"
        )

        assert accumulation_window.max == normalization_statistics["delta_t"].max, (
            f"accumulation_window must match the delta_t statistics! The values are: \
                {accumulation_window.max} vs. {normalization_statistics['delta_t'].max}"
        )

        self.in_channels = in_channels
        self._num_voxels = num_voxels
        self._num_points_per_voxel = num_points_per_voxel
        self.raw_feature_output = raw_feature_output
        self._accumulation_window = accumulation_window
        self.xmin = volume_params.lower_bound[0]
        self.xmax = volume_params.upper_bound[0]
        self.ymin = volume_params.lower_bound[1]
        self.ymax = volume_params.upper_bound[1]
        self.zmin = volume_params.lower_bound[2]
        self.zmax = volume_params.upper_bound[2]
        self.xres = voxel_size[0]
        self.yres = voxel_size[1]

        self._min_grid_index_x = 0
        self._min_grid_index_y = 0

        self._max_grid_index_x = math.floor((self.xmax - self.xmin) / self.xres)
        self._max_grid_index_y = math.floor((self.ymax - self.ymin) / self.yres)

        self._grid_num_cells = self._max_grid_index_x * self._max_grid_index_y

        self._xy_min = nn.Buffer(torch.tensor([[self.xmin, self.ymin]], dtype=torch.float32), persistent=False)
        self._xy_min_t = nn.Buffer(torch.transpose(self._xy_min, 0, 1), persistent=False)
        self._xy_res = nn.Buffer(torch.tensor([[self.xres, self.yres]], dtype=torch.float32), persistent=False)
        self._xy_res_t = nn.Buffer(torch.transpose(self._xy_res, 0, 1), persistent=False)

        self._onnx_compatible = onnx_compatible

        # Calculate and check image-like feature representation parameters
        self._image_like_feature_representation = image_like_feature_representation
        if self._image_like_feature_representation:
            self._voxel_rep_x = 100
            if num_voxels % self._voxel_rep_x != 0:
                error_msg = f"num_voxels must be divisible by {self._voxel_rep_x}"
                raise ValueError(error_msg)
            if num_points_per_voxel % 2 != 0:
                error_msg = "num_points_per_voxel must be divisible by 2."
                raise ValueError(error_msg)

            self._voxel_rep_y = int(num_voxels // self._voxel_rep_x)

        if not raw_feature_output:
            assert out_channels is not None

        if not raw_feature_output and out_channels is not None:
            self.conv = nn.Linear(in_channels, out_channels, bias=False)
            self.norm = nn.BatchNorm1d(out_channels, eps=1e-3, momentum=0.01)

        self._normalize_values = normalize_input_values

        if normalize_input_values:
            assert raw_feature_output is True, "Normalization is only supported for raw feature output!"
            self._normalization_statistics = normalization_statistics

            def create_norm_params(min_val: float, max_val: float) -> dict[str, float]:
                assert min_val < max_val
                return {"offset": -min_val, "scale": 1.0 / (max_val - min_val)}

            self._norm_params_rel_x = create_norm_params(-self.xres / 2.0, self.xres / 2.0)
            self._norm_params_rel_y = create_norm_params(-self.yres / 2.0, self.yres / 2.0)
            self._norm_params_x = create_norm_params(self.xmin, self.xmax)
            self._norm_params_y = create_norm_params(self.ymin, self.ymax)
            self._norm_params_z = create_norm_params(
                3.0 * self.zmin, 3.0 * self.zmax
            )  # point cloud range seems to not fit well for z yet -> improve
            max_range = np.hypot(max(abs(self.xmin), abs(self.xmax)), max(abs(self.ymin), abs(self.ymax)))
            self._norm_params_r = create_norm_params(0.0, max_range)
            self._norm_params_num_points = create_norm_params(0.0, 70.0)

            self._norm_params_vr = create_norm_params(
                self._normalization_statistics["radial_velocity_over_ground"].min,
                self._normalization_statistics["radial_velocity_over_ground"].max,
            )
            self._norm_params_rcs = create_norm_params(
                self._normalization_statistics["rcs"].min, self._normalization_statistics["rcs"].max
            )
            self._norm_params_delta_t = create_norm_params(
                self._normalization_statistics["delta_t"].min, self._normalization_statistics["delta_t"].max
            )

    @property
    def onnx_compatible(self) -> bool:
        """Get if encoder is onnx compatible."""
        return self._onnx_compatible

    def _calc_global_pillar_feats_onnx(self, indicies: Tensor, dtype: torch.dtype) -> Tensor:
        """Calculates global pillar features for onnx.

        Currently, the following features are calculated:
        - num_points: Number of (valid) points within each pillar
        """
        index_1d = indicies[..., 0] + indicies[..., 1] * self._max_grid_index_x
        index_1d_ones = torch.ones_like(index_1d, dtype=torch.int32)
        num_points_grid = indicies.new_zeros(self._grid_num_cells, dtype=torch.int32)
        num_points_grid.scatter_add_(0, index_1d.type(torch.int64), index_1d_ones)
        num_points = torch.gather(num_points_grid, 0, index_1d.type(torch.int64))
        num_points = torch.unsqueeze(num_points, dim=-1)
        num_points = num_points.type(dtype)
        return num_points

    def _calc_global_pillar_feats(self, indicies: Tensor, dtype: torch.dtype) -> Tensor:
        """Calculates global pillar features.

        Currently, the following features are calculated:
        - num_points: Number of (valid) points within each pillar
        """
        # calculate num location in each pillar
        index_one = indicies[..., 0] + indicies[..., 1] * self._max_grid_index_x
        y, idx, count = torch.unique(index_one, return_inverse=True, return_counts=True)
        num_points = torch.gather(count, 0, idx)
        num_points = torch.unsqueeze(num_points, dim=-1)
        num_points = num_points.type(dtype)
        return num_points

    def _calculate_features(self, points: Tensor, indicies: Tensor) -> Tensor:
        """Augments the points within pillars similar to the original paper https://arxiv.org/pdf/1812.05784.pdf.

        Analogies are (code --> paper):
           - xyz --> x/y/z
           - xy_rel --> x_p/y_p
           - r_dist, num_points_expanded, feats --> not directly given in paper but similar to reflectance
        """
        # calculate relative position difference from pillar center
        points_xy = points[:, PointCloudFeatureIndex.X : PointCloudFeatureIndex.Z]
        pillar_center = (indicies + 0.5) * self._xy_res + self._xy_min
        xy_rel = pillar_center - points_xy

        # radial distance of point in xy plane (ignore z value)
        r_dist = torch.norm(points_xy, dim=-1, keepdim=True)  # pylint: disable=not-callable

        # feature index after position information
        skipped_position_idx = PointCloudFeatureIndex.Z + 1

        # extract features
        feats = points[:, skipped_position_idx:]

        xyz = points[..., 0:skipped_position_idx]

        if self._onnx_compatible:
            num_points = self._calc_global_pillar_feats_onnx(indicies, feats.dtype)
        else:
            num_points = self._calc_global_pillar_feats(indicies, feats.dtype)

        # Create point wise features in aligment with DroGridFeatureIndex
        pillar_points_features = torch.concat([xy_rel, xyz, r_dist, num_points, feats], dim=-1)
        return pillar_points_features

    def _numba_create_voxelized_output(
        self, pillar_features_enc: Tensor, pillar_coors: Tensor, batch_size: int
    ) -> tuple[Tensor, Tensor]:
        """Creates the voxelized output."""
        voxels = pillar_features_enc.new_zeros(
            (batch_size, self._num_voxels, self._num_points_per_voxel, self.in_channels)
        )
        voxel_coords = pillar_features_enc.new_zeros(
            (batch_size, self._num_voxels, 2),
            dtype=pillar_coors.dtype,
        )
        for i in range(batch_size):
            this_batch_mask = pillar_coors[:, 0] == i
            this_features = pillar_features_enc[this_batch_mask]
            this_coords = pillar_coors[this_batch_mask]
            index_1d = this_coords[:, 1] + this_coords[:, 2] * self._max_grid_index_x

            # numba needs to operate on numpy arrays on cpu
            in_np_featurese = this_features.detach().cpu().numpy()
            in_np_orig_coords = this_coords.detach().cpu().numpy()
            in_np_index_1d = index_1d.detach().cpu().numpy()
            np_voxels, np_voxel_coords = numba_create_voxelized_output_single_batch(
                self._num_voxels, self._num_points_per_voxel, in_np_featurese, in_np_orig_coords, in_np_index_1d
            )
            voxels[i] = voxels.new_tensor(np_voxels)
            voxel_coords[i] = voxel_coords.new_tensor(np_voxel_coords)
        return voxels, voxel_coords

    def _create_valid_points_mask(self, points: Tensor) -> Tensor:
        # points: see enum PointCloudFeatureIndex

        # filter out points which have any attribute (x,y,z or any features) that are NaN
        valid_points_mask = ~torch.isnan(points).any(dim=-1)

        # filter out points with unreasonable high absolute radial velocity values
        max_abs_radial_velocity = 50.0
        valid_radial_velocity_mask = (
            abs(points[:, PointCloudFeatureIndex.RADIAL_VELOCITY_OVER_GROUND]) <= max_abs_radial_velocity
        )

        time_aggregation_window_mask = torch.logical_and(
            points[:, PointCloudFeatureIndex.DELTA_T] >= self._accumulation_window.min,
            points[:, PointCloudFeatureIndex.DELTA_T] <= self._accumulation_window.max,
        )

        valid_points_mask = valid_points_mask & valid_radial_velocity_mask & time_aggregation_window_mask

        return valid_points_mask

    def _create_pillar_points(self, points: Tensor) -> tuple[Tensor, Tensor]:
        """Creates pillar points and their corresponding indices for a points tensor.

        Args:
            points: Input points tensor.

        Returns:
            A tuple containing the pillar points features and the pillar indices within the grid.
        """

        valid_points_mask = self._create_valid_points_mask(points)

        points_xy = points[:, PointCloudFeatureIndex.X : PointCloudFeatureIndex.Z]
        points_xy_t = torch.transpose(points_xy, 0, 1)
        pillar_indices_t = torch.floor((points_xy_t - self._xy_min_t) / self._xy_res_t).type(torch.int32)
        pillar_indices = torch.transpose(pillar_indices_t, 0, 1)

        # filter pillar_points indices outside grid
        pillar_in_grid_x = torch.logical_and(
            pillar_indices[..., 0] >= self._min_grid_index_x, pillar_indices[..., 0] < self._max_grid_index_x
        )
        pillar_in_grid_y = torch.logical_and(
            pillar_indices[..., 1] >= self._min_grid_index_y, pillar_indices[..., 1] < self._max_grid_index_y
        )
        pillar_in_grid = torch.logical_and(pillar_in_grid_x, pillar_in_grid_y)
        valid_points_mask = torch.logical_and(pillar_in_grid, valid_points_mask)

        points_in_grid = points[valid_points_mask, :]
        pillar_indices_in_grid = pillar_indices[valid_points_mask, :]

        pillar_points_features = self._calculate_features(points_in_grid, pillar_indices_in_grid)

        return pillar_points_features, pillar_indices_in_grid

    def _create_pillar_points_batch(self, batched_points: list[Tensor]) -> tuple[Tensor, Tensor]:
        """Creates pillar points and their corresponding indices for a batch of points.

        Args:
            batched_points: A list of tensors representing the batched points.

        Returns:
            A tuple containing the pillar features tensor and the pillar coordinates tensor.
            Note: coordinates are in "xy" order meaning that one element consists of [batch_idx, x_idx, y_idx]
        """

        points_feats_batched = []
        coors_batched = []
        for i, points in enumerate(batched_points):
            if points.nelement() != 0:
                pillar_points_features, pillar_indices = self._create_pillar_points(points)
            elif i == len(batched_points) - 1:
                # Workaround!
                # Special treatment when last batch-item has empty pointcloud: Pad dummy point at origin.
                # Reason: The batch index of the very last voxel is used in many downstream applications to
                # dynamically derive the batch size (see batch_size = voxel_dict['coors'][-1, 0] calls) -> If
                # the last batch element contains an empty point cloud, the batch size is not derived
                # correctly, which can lead to subsequent errors due to shape mismatches.
                # See also the discussion in
                # https://github.com/PACE-INT/assets-robert-bosch-techboost/tree/main/DeepFus_and_DRO/cr_repos/mm_aid400_plugins/pull-requests/143/overview
                # for more details and an explanation of why we chose this workaround instead of finding cleaner
                # solutions.
                pillar_points_features, pillar_indices = self._create_pillar_points(
                    points.new_zeros(1, points.size(1), dtype=points.dtype)
                )
                warnings.warn(
                    (
                        f"Encountered empty pointcloud in batch item {i}. Dummy point added at origin "
                        "to prevent subsequent shape mismatch errors."
                    ),
                    stacklevel=2,
                )
            else:
                pillar_points_features = points.new_zeros((0, self.in_channels))
                pillar_indices = points.new_zeros((0, 2), dtype=torch.int32)
            pillar_indices = F.pad(pillar_indices, (1, 0), mode="constant", value=i)  # pylint: disable=not-callable
            points_feats_batched.append(pillar_points_features)
            coors_batched.append(pillar_indices)

        pillar_features = torch.cat(points_feats_batched, dim=0)
        pillar_coors = torch.cat(coors_batched, dim=0)

        return pillar_features, pillar_coors

    def _normalize(self, voxel_features: Tensor) -> Tensor:
        """Normalize feature to range [0,1].

        Args:
            voxel_features: the feature of all voxels and points, the input values will also be changed

        Returns:
            normalized features
        """

        def normalize_feature(feature: Tensor, params: dict[str, float]) -> Tensor:
            return torch.clip((feature + params["offset"]) * params["scale"], 0, 1)

        # Mask for the features to be normalized.
        # Some voxels contain no point, some voxels are not full (the capacity of points not reached), and the input
        # features are zero-padded. In this way, doing normalization on all feature points would bring artifacts to the
        # data. This mask is used for selecting the filled point features.
        # In measurement it is not probable that a feature point has all values equal to 0, thus it is possible to use
        # it to avoid false normalization.
        valid_indices = ~(voxel_features == 0.0).all(dim=-1)

        for id_feature, param_feature_normalization in [
            (DroGridFeatureIndex.X_REL, self._norm_params_rel_x),
            (DroGridFeatureIndex.Y_REL, self._norm_params_rel_y),
            (DroGridFeatureIndex.X, self._norm_params_x),
            (DroGridFeatureIndex.Y, self._norm_params_y),
            (DroGridFeatureIndex.Z, self._norm_params_z),
            (DroGridFeatureIndex.RADIAL_DISTANCE, self._norm_params_r),
            (DroGridFeatureIndex.NUMBER_OF_POINTS, self._norm_params_num_points),
            (DroGridFeatureIndex.RADIAL_VELOCITY_OVER_GROUND, self._norm_params_vr),
            (DroGridFeatureIndex.RCS, self._norm_params_rcs),
            (DroGridFeatureIndex.DELTA_T, self._norm_params_delta_t),
        ]:
            voxel_features[..., id_feature][valid_indices] = normalize_feature(
                voxel_features[..., id_feature][valid_indices], param_feature_normalization
            )

        return voxel_features

    def forward(self, points: Union[Tensor, list[Tensor]]) -> RadarVoxelizerData:
        """Forward pass of the voxel encoder.

        Args:
            points: Single input point cloud or list (=batch) of input point clouds.

        Returns:
            A dictionary containing the voxelized features, voxel coordinates, and number of points.
            Note: coordinates are in "xy" order meaning that one element consists of [batch_idx, x_idx, y_idx]
        """
        if isinstance(points, list):
            batch_size = len(points)
            pillar_features, pillar_coors = self._create_pillar_points_batch(points)
        else:  # the input can be Tensor or proxy
            batch_size = 1
            # special case to simplify inference graph if no batching is required
            pillar_features, pillar_coors = self._create_pillar_points(points)
            # Note: F.pad() produces strange warnings during onnx export -> use torch.cat instead
            pillar_coors = torch.cat([torch.zeros_like(pillar_coors[:, 0:1]), pillar_coors], dim=1)

        if self.raw_feature_output:
            pillar_features_enc = pillar_features
        else:
            x = self.conv(pillar_features)  # pylint: disable=not-callable
            x = self.norm(x)
            pillar_features_enc = F.relu(x)

        if self._onnx_compatible:
            assert self.raw_feature_output, (
                "when converting to voxilized version it is not possible \
                      to learn the conv and norm weights due to detaching before voxilization"
            )
            voxel_features, voxel_coords = self._numba_create_voxelized_output(
                pillar_features_enc, pillar_coors, batch_size
            )

            if self._normalize_values:
                voxel_features = self._normalize(voxel_features)

            if self._image_like_feature_representation:
                voxel_features = voxel_features.reshape(
                    batch_size, self._voxel_rep_x, self._voxel_rep_y, self._num_points_per_voxel * self.in_channels
                )
            return RadarVoxelizerData(voxel_features, voxel_coords)

        # This branch is unlikely to be used.
        if self._normalize_values:
            pillar_features_enc = self._normalize(pillar_features_enc)

        return RadarVoxelizerData(pillar_features_enc, pillar_coors)
