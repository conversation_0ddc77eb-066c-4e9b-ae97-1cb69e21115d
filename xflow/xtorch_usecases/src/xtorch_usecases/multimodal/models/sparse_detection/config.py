"""Configs for the model."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import argparse
import multiprocessing as mp
from collections.abc import Callable
from functools import partial
from typing import Any

from qnn_custom_ops import QnnBackend

from conversion.qnn.inference import QnnProfilingLevel
from xcontract.data.definitions.inputs import InputDataId
from xcontract.training.run_mode import RunMode
from xtorch_usecases.common.environment import RuntimeEnvironment
from xtorch_usecases.common.helpers import (
    filter_datasets_by_name,
    get_env_specific_setting,
    get_pipeline_step_specific_setting,
    get_run_mode_specific_setting,
    split_dataset_name_and_version,
)
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.configs.model.sparse_detection.model import (
    BATCH_SIZE_EVAL_QNN,
    FRAMES_PER_EVALUATION_LONG,
    STEPS_PER_EVALUATION_SHORT,
    compute_total_steps,
    get_sub_configs,
)
from xtorch_usecases.multimodal.configs.output_folders_factory import get_default_pipeline_step_paths
from xtorch_usecases.multimodal.configs.run import MultiModalRunConfig
from xtorch_usecases.multimodal.configs.usecase import UsecaseConfig
from xtorch_usecases.multimodal.models.definitions import Model

USE_CASES = ["alliance", "loomy"]
STREAMING_FILE_MODE = "streaming"

OUTPUT_FOLDER = "outputs"
INPUT_FOLDER = "outputs"

CAMERA_MODALITY = "camera"
RADAR_MODALITIY = "radar"
LIDAR_MODALITY = "lidar"
INPUT_MODALITIES = [CAMERA_MODALITY, RADAR_MODALITIY]

# NOTE: lidar not enabled by default
AVAILABLE_INPUT_MODALITIES = [CAMERA_MODALITY, RADAR_MODALITIY, LIDAR_MODALITY]


def _create_argument_parser() -> argparse.ArgumentParser:
    parser = argparse.ArgumentParser()
    parser.add_argument("--file_mode", type=str.lower, choices=["local", STREAMING_FILE_MODE], default="local")
    parser.add_argument("--output_folder", default=OUTPUT_FOLDER)
    parser.add_argument("--input_folder", default=INPUT_FOLDER)
    parser.add_argument(
        "--usecase",
        type=str.lower,
        help="Usecase configuration to use (affects network modality encoders and datasets used)",
        default="alliance",
        choices=USE_CASES,
    )
    parser.add_argument(
        "--train_checkpoint",
        type=str,
        help=(
            "Directory containing checkpoints or path to an explicit checkpoint file '... .ckpt' used to resume "
            "training from. If None, default pre-trained weights for only the backbones will be used."
        ),
        default=None,
    )
    parser.add_argument(
        "--eval_checkpoint",
        type=str,
        help=(
            "Directory containing checkpoints or path to an explicit checkpoint file '... .ckpt' that shall be "
            "restored for evaluation. If None, the last one from the default checkpoints directory is restored."
        ),
        nargs="?",
        default=None,
    )
    parser.add_argument(
        "--qnn_eval_backend", type=str.lower, choices=[QnnBackend.CPU.value, QnnBackend.HTP.value], default=None
    )
    parser.add_argument(
        "--qnn_eval_batch_size",
        type=int,
        help="Batch size during qnn evaluation.",
        default=BATCH_SIZE_EVAL_QNN,
    )
    parser.add_argument("--eval_full_model", help="set this to evaluate full model", action="store_true")
    parser.add_argument(
        "--target_address",
        action="store",
        type=str,
        nargs=2,
        default=None,
        help="Used for running the evaluation on dev targets. Requires two parameters defining the jumphost and "
        "target 'user@target' e.g. --target_address 'username'@'jump_host_ip_or_alias' root@'target_ip_or_alias'."
        "for example: '<EMAIL>' 'root@voldemort' "
        "Note that you could also do export QCB_HOSTNAME=voldemort, which is configured in ${HOME}/.ssh/config"
        "In that case, no need to provide the target_address",
    )
    parser.add_argument(
        "--steps_per_predict",
        type=int,
        help="Number of steps for offline evaluation.",
        default=-1,
    )
    parser.add_argument(
        "--log_eval_image_every_n_sample",
        type=int,
        help="Generate visu output after n samples.",
        default=-1,
    )
    parser.add_argument(
        "--dataset_path",
        type=str,
        help="Path to the local dataset, e.g. used during qnn evaluation. "
        "The path shall contain a .csv file with the dataset_name.",
        default=None,
    )
    parser.add_argument("--skip_qnn_conversion", help="set this to skip qnn conversion", action="store_true")
    parser.add_argument(
        "--dataset_name",
        type=str,
        help="Select specific dataset name from alliance datasets to allow operation on single dataset.",
        default=None,
    )
    parser.add_argument(
        "--profiling_level",
        choices=[QnnProfilingLevel.NO_PROFILING.value, QnnProfilingLevel.BASIC.value, QnnProfilingLevel.DETAILED.value],
        default=QnnProfilingLevel.NO_PROFILING.value,
    )
    parser.add_argument("--short_ct_run", type=str.lower, choices=["true", "false"], default="false")
    parser.add_argument(
        "--enabled_modalities",
        help=f"Activates the given modalities. If not given, default ({INPUT_MODALITIES}) are activated.",
        nargs="*",
        default=INPUT_MODALITIES,
        choices=AVAILABLE_INPUT_MODALITIES,
    )
    parser.add_argument(
        "--environment",
        type=str,
        default=None,
        help="Environment parameter passed into the stage (unused, deprecated).",
    )
    return parser


def _get_modality_setup(enabled_modalities: set[str]) -> tuple[tuple[InputDataId, ...], tuple[InputDataId, ...]]:
    enabled_data_types = []
    enabled_encoders = []

    if CAMERA_MODALITY in enabled_modalities:
        # TV camera (rgbs_intrinsics_extrinsics_deformed_cylinder)
        enabled_data_types.append(InputDataId.CAMERA_TV)
        enabled_encoders.append(InputDataId.CAMERA_DEF_CYLINDER)
        # FC1 camera (rgbs_intrinsics_extrinsics_cylinder)
        enabled_data_types.append(InputDataId.CAMERA_FC1)
        enabled_encoders.append(InputDataId.CAMERA_CYLINDER)

    if RADAR_MODALITIY in enabled_modalities:
        enabled_data_types.append(InputDataId.RADAR)  # combined radars (radar_pointcloud)
        enabled_encoders.append(InputDataId.RADAR)

    if LIDAR_MODALITY in enabled_modalities:
        enabled_data_types.append(InputDataId.REF_LIDAR)  # ref lidar (ref_lidar_pointcloud)
        enabled_encoders.append(InputDataId.REF_LIDAR)

    return tuple(enabled_data_types), tuple(enabled_encoders)


def get_run_config_sparse_detection(  # noqa: C901, PLR0915
    argv: list[str], pipeline_step: PipelineStep, node_count: int = 1
) -> tuple[MultiModalRunConfig, list[str]]:
    """Gets configs."""
    # parse args
    parser = _create_argument_parser()
    args, unknown_args = parser.parse_known_args(argv)
    assert args.usecase in USE_CASES, f"Unknown dataset usecase: {args.usecase}"

    qnn_backend = QnnBackend(args.qnn_eval_backend) if args.qnn_eval_backend else None

    enabled_input_modalities, enabled_modality_encoders = _get_modality_setup(args.enabled_modalities)

    run_mode = RunMode.SHORT if args.short_ct_run == "true" else RunMode.LONG
    (
        data_config,
        training_config,
        eval_config,
        callback_config,
        trainer_config,
        task_config,
        convert_config,
        qat_config,
    ) = get_sub_configs(run_mode, pipeline_step, args.usecase, qnn_backend, enabled_input_modalities, node_count)
    assert task_config.sparse_detection is not None

    run_mode_specific_setting_fn: Callable[..., Any] = partial(get_run_mode_specific_setting, run_mode=run_mode)
    pipeline_step_specific_setting_fn: Callable[..., Any] = partial(
        get_pipeline_step_specific_setting, pipeline_step=pipeline_step
    )

    # task config refinement
    if CAMERA_MODALITY not in args.enabled_modalities:
        task_config.sparse_detection.image_writer_callback_config = None
        task_config.sparse_detection.loss_config.camera_head_loss.enabled = False

    # data config refinement
    data_config.enabled_input_modalities = enabled_input_modalities
    if args.file_mode == STREAMING_FILE_MODE:
        data_config.alliance_data_basepath = "https://vdeepingestprod.blob.core.windows.net/cache"
        data_config.alliance_dataset_basepath = None
    if args.dataset_path:
        data_config.alliance_data_basepath = args.dataset_path
        data_config.alliance_dataset_basepath = args.dataset_path

    if args.target_address is not None:
        eval_config.target_address = tuple(args.target_address)
    eval_config.eval_full_model = args.eval_full_model
    eval_config.checkpoint = args.eval_checkpoint
    eval_config.profiling_level = QnnProfilingLevel(args.profiling_level)

    # trainer config refinement
    if args.steps_per_predict != -1:
        trainer_config.limit_predict_batches = args.steps_per_predict
    if eval_config.profiling_level is not QnnProfilingLevel.NO_PROFILING:
        trainer_config.limit_predict_batches = 1

    # training module config refinement
    training_config.enabled_input_modalities = enabled_modality_encoders
    if args.log_eval_image_every_n_sample != -1:
        training_config.log_eval_image_every_n_sample = args.log_eval_image_every_n_sample

    # handle paths
    pipeline_step_paths = get_default_pipeline_step_paths(
        pipeline_step=pipeline_step,
        input_base_folder=args.input_folder,
        output_base_folder=args.output_folder,
    )
    training_config.output_folder = pipeline_step_paths.output_artifacts
    callback_config.output_root_dir = pipeline_step_paths.output_artifacts
    trainer_config.default_root_dir = pipeline_step_paths.output_artifacts
    if task_config.sparse_detection.image_writer_callback_config is not None:
        task_config.sparse_detection.image_writer_callback_config.output_path = (
            pipeline_step_paths.output_artifacts.as_posix()
        )
    task_config.sparse_detection.evaluation_folder = pipeline_step_specific_setting_fn(
        default=pipeline_step_paths.evaluation_torch,
        convert=pipeline_step_paths.evaluation_qnn,
        evaluate_qnn=pipeline_step_paths.evaluation_qnn / split_dataset_name_and_version(args.dataset_name)[0],
        profile_qnn=pipeline_step_paths.evaluation_qnn,
    )
    convert_config.default_root_dir = pipeline_step_paths.conversion
    convert_config.skip_qnn_conversion = args.skip_qnn_conversion
    qat_config.default_root_dir = pipeline_step_paths.conversion_qat

    # environment
    environment = get_env_specific_setting(local=RuntimeEnvironment.LOCAL, azure=RuntimeEnvironment.AZURE)

    # qnn eval batch size
    if args.qnn_eval_backend and eval_config.profiling_level == QnnProfilingLevel.NO_PROFILING:
        batch_size_eval = args.qnn_eval_batch_size
        data_config.batch_size_val = batch_size_eval
        trainer_config.limit_val_batches = run_mode_specific_setting_fn(
            short=STEPS_PER_EVALUATION_SHORT,
            long=compute_total_steps(num_total_frames=FRAMES_PER_EVALUATION_LONG, batch_size=batch_size_eval),
        )
        assert task_config.sparse_detection.eval_run_environment is not None
        task_config.sparse_detection.eval_run_environment.num_processes = get_env_specific_setting(
            local=1, azure=max(mp.cpu_count() // batch_size_eval, 1), dt_flow=24
        )

    if args.dataset_name is not None:
        data_config.alliance_dataset_names = filter_datasets_by_name(
            args.dataset_name, data_config.alliance_dataset_names
        )

    # overall run config
    run_config = MultiModalRunConfig(
        model=Model.SPARSE_DETECTION,
        bev_layout=training_config.bev_layout,
        depth_configs=training_config.depth_configs,
        data_config=data_config,
        training_config=training_config,
        eval_config=eval_config,
        trainer=trainer_config,
        training_epoch_length=training_config.train_steps_per_epoch,
        convert_config=convert_config,
        train_checkpoint=args.train_checkpoint,
        pipeline_step_paths=pipeline_step_paths,
        runtime_environment=environment,
        task_configs=task_config,
        common_config=UsecaseConfig(callback_config=callback_config),
        qat_config=qat_config,
        enable_visualization_writer=True,
        run_mode=run_mode,
    )

    return run_config, unknown_args
