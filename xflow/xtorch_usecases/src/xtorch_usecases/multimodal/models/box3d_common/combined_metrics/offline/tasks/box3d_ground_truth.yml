experiment_name: "Box3d Combined Metrics"
tasks:
  - name: "alliance_manual_labels/PassengerCar/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/LargeVehicle/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/RidableVehicle/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/Pedestrian/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/PassengerCar/NearRange0-30"
    description: "Evaluation on alliance_manual_labels data for PassengerCar in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/LargeVehicle/NearRange0-30"
    description: "Evaluation on alliance_manual_labels data for LargeVehicle in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/RidableVehicle/NearRange0-30"
    description: "Evaluation on alliance_manual_labels data for RidableVehicle in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/Pedestrian/NearRange0-30"
    description: "Evaluation on alliance_manual_labels data for Pedestrian in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/DetectionScore/NearRange0-30"
    description: "Evaluation on alliance_manual_labels data for any class in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/PassengerCar/MidRange30-90"
    description: "Evaluation on alliance_manual_labels data for PassengerCar in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/LargeVehicle/MidRange30-90"
    description: "Evaluation on alliance_manual_labels data for LargeVehicle in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/RidableVehicle/MidRange30-90"
    description: "Evaluation on alliance_manual_labels data for RidableVehicle in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/Pedestrian/MidRange30-90"
    description: "Evaluation on alliance_manual_labels data for Pedestrian in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/DetectionScore/MidRange30-90"
    description: "Evaluation on alliance_manual_labels data for any class in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/PassengerCar/FarRange90+"
    description: "Evaluation on alliance_manual_labels data for PassengerCar in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/LargeVehicle/FarRange90+"
    description: "Evaluation on alliance_manual_labels data for LargeVehicle in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/RidableVehicle/FarRange90+"
    description: "Evaluation on alliance_manual_labels data for RidableVehicle in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/Pedestrian/FarRange90+"
    description: "Evaluation on alliance_manual_labels data for Pedestrian in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance_manual_labels/DetectionScore/FarRange90+"
    description: "Evaluation on alliance_manual_labels data for any class in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance_manual_labels.PassengerCar.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.LargeVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.RidableVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance_manual_labels.Pedestrian.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
