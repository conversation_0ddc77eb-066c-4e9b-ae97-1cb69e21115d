experiment_name: "Box3d Combined Metrics"
tasks:
  - name: "alliance/RidableVehicle/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/Pedestrian/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/RidableVehicle/NearRange0-30"
    description: "Evaluation on alliance data for RidableVehicle in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/Pedestrian/NearRange0-30"
    description: "Evaluation on alliance data for Pedestrian in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/DetectionScore/NearRange0-30"
    description: "Evaluation on alliance data for any class in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/RidableVehicle/MidRange30-90"
    description: "Evaluation on alliance data for RidableVehicle in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/Pedestrian/MidRange30-90"
    description: "Evaluation on alliance data for Pedestrian in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/DetectionScore/MidRange30-90"
    description: "Evaluation on alliance data for any class in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/RidableVehicle/FarRange90+"
    description: "Evaluation on alliance data for RidableVehicle in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/Pedestrian/FarRange90+"
    description: "Evaluation on alliance data for Pedestrian in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/DetectionScore/FarRange90+"
    description: "Evaluation on alliance data for any class in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.RidableVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.Pedestrian.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
