experiment_name: "Box3d Combined Metrics"
tasks:
  - name: "alliance/PassengerCar/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/LargeVehicle/DetectionScore"
    description: "..."
    impact: 1.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      # Matching threshold 1m
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/PassengerCar/NearRange0-30"
    description: "Evaluation on alliance data for PassengerCar in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/LargeVehicle/NearRange0-30"
    description: "Evaluation on alliance data for LargeVehicle in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/DetectionScore/NearRange0-30"
    description: "Evaluation on alliance data for any class in near distance (0-30m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 1m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.9000000@0.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/PassengerCar/MidRange30-90"
    description: "Evaluation on alliance data for PassengerCar in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/LargeVehicle/MidRange30-90"
    description: "Evaluation on alliance data for LargeVehicle in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/DetectionScore/MidRange30-90"
    description: "Evaluation on alliance data for any class in mid distance (30-90m)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.6000000@30.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/PassengerCar/FarRange90+"
    description: "Evaluation on alliance data for PassengerCar in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/LargeVehicle/FarRange90+"
    description: "Evaluation on alliance data for LargeVehicle in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"

  - name: "alliance/DetectionScore/FarRange90+"
    description: "Evaluation on alliance data for any class in far distance (90m+)"
    impact: 0.0
    reduction: "weighted_average"
    kpis:
      # Matching threshold 4m
      - metric: "alliance.PassengerCar.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
      - metric: "alliance.LargeVehicle.pr_curves_detailed.auc@0.6000000@90.0000000"
        loader_args:
          measure: "distance"
        weight: 1.0
        required_performance: ">=0.0"
