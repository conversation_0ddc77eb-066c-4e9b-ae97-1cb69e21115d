"""Publish script for the multiview usecase."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2023 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import contextlib
import logging
import sys
from pathlib import Path

import coloredlogs
from azure.ai.ml.entities import Model as AzureMLModel
from qnn_custom_ops import QnnBackend

from azure_tools.job import get_job, get_ml_client
from azure_tools.logging import runs_on_aml_node
from conversion.artifactory_publishing import ArtifactoryPublisher
from conversion.qnn.common import QnnModelArtifacts
from xcommon.logging import log_configuration
from xcommon.publishing.publishing import load_metrics_of_folder
from xtension.azure_ct import azureml_publishing  # pylint: disable=forbidden-import
from xtorch_extensions.environment import log_environment
from xtorch_usecases.common.configuration.aml_pipelines.pipeline_info_json import initialize_pipeline_info
from xtorch_usecases.common.credential_definitions import get_default_pace_artifactory_config
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.common.qnn_on_dt_flow import trigger_dt_flow_from_azure_context
from xtorch_usecases.multimodal.common.onnx_slicing_config import SliceSubgraphs
from xtorch_usecases.multimodal.common.qnn_inference_run_args import get_qnn_run_args
from xtorch_usecases.multimodal.configs.output_folders_factory import (
    get_default_pipeline_step_paths,
)
from xtorch_usecases.multimodal.models.box3d_simple.config import get_run_config_box3d_simple
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.models.lanesegnet.config import get_run_lanesegnet_config
from xtorch_usecases.multimodal.models.occupancy.config import get_run_config_occupancy
from xtorch_usecases.multimodal.models.sparse_detection.config import get_run_config_sparse_detection
from xtorch_usecases.multimodal.train import MODEL_CONFIG

LOGGER = logging.getLogger(__name__)


def _parse_input_folders(argv: list[str]) -> tuple[list[str], list[str]]:
    """Parse input folders from command line arguments.

    Extra argument parser to grab "input_folder" before it is read by "parse_cli_arguments", to allow giving multiple
    input folders to this stage.

    Args:
        argv: Command line arguments
    Returns:
        input_folders: List of input folders
        remaining_args: Remaining command line arguments
    """

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_folder",
        type=str,
        nargs="*",
        help=(
            "Input folder where the stage outputs are stored. One can also specify multiple folders. "
            "In that case, all files are copied together into a single folder during the publishing step."
        ),
    )
    args, remaining_args = parser.parse_known_args(argv)
    input_folders = args.input_folder
    assert len(input_folders) > 0
    return input_folders, remaining_args


def main(argv: list[str]) -> None:
    """Entry point for publishing."""

    coloredlogs.install(level=logging.INFO)

    log_environment()

    logger = logging.getLogger(__name__)

    input_folders, remaining_args = _parse_input_folders(argv)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--publish_to_model_store",
        action="store_true",
        help="Whether or not to save the model and metrics to the model store.",
    )
    parser.add_argument(
        "--publish_to_artifactory",
        action="store_true",
        help="Publish the qnn models to Pace dev Artifactory. Requires publish_to_model_store to be set to True.",
    )
    parser.add_argument(
        "--publish_folder",
        type=str,
        default=(Path("outputs") / "publish").as_posix(),
        help="Path to the folder that will store the published results.",
    )
    parser.add_argument(
        "--ignore_patterns",
        nargs="*",
        default=["summaries*", "conversion.*htp/calib_images"],
        help="List of patterns to be ignored for copying to model store (e.g., *.tmp).",
    )
    parser.add_argument(
        "--include_patterns",
        nargs="*",
        default=["checkpoints*", "conversion*", "evaluation/.*combined_metrics.*.json"],
        help="List of patterns to be included for copying to model store (e.g., checkpoints).",
    )
    parser.add_argument("--model", type=str, default="", help="Trigger dt-flow of the model type.")
    parser.add_argument(
        "--trigger_embedded_qnn_eval",
        action="store_true",
        help="Trigger dt-flow job to evaluation qnn model on qc boards.",
    )

    args, unknown_args = parser.parse_known_args(remaining_args)

    pipeline_step_output_folders = get_default_pipeline_step_paths(
        pipeline_step=PipelineStep.PUBLISH, output_base_folder=args.publish_folder, input_base_folder=""
    )
    ignore_patterns = args.ignore_patterns
    include_patterns = args.include_patterns

    log_configuration(name=__file__, config=pipeline_step_output_folders)

    logger.info(f"Copying input folders to publish_folder: {args.publish_folder}")

    for input_folder in input_folders:
        azureml_publishing.copy_data(
            input_folder=input_folder,
            output_folder=args.publish_folder,
        )

    initialize_pipeline_info(input_folders=map(Path, input_folders), output_folder=Path(args.publish_folder))

    metrics = load_metrics_of_folder(str(pipeline_step_output_folders.evaluation_torch), load_all=False)
    logger.info(f"Loaded metrics: {metrics}")
    if not runs_on_aml_node():
        logger.info("Local execution detected; skipping publishing to Azure.")
        return

    if args.publish_to_model_store:
        logger.info("Publishing outputs and metrics.")
        azureml_model = azureml_publishing.publish_experiment_results(
            args.publish_folder,
            metrics=metrics,
            metrics_version=1,
            include_patterns=include_patterns,
            ignore_patterns=ignore_patterns,
        )

        if args.model and args.publish_to_artifactory:
            if azureml_model is not None:
                try:
                    deploy_model_to_artifactory(args.publish_folder, args.model, azureml_model, unknown_args)
                except Exception:
                    logger.exception(f"Failed to deploy model {args.model} to Artifactory.")
            else:
                logger.error("AzureML model not found. Skipping deployment to Artifactory.")

    if args.model and args.trigger_embedded_qnn_eval:
        try:
            trigger_multimodal_qnn_eval(args.model, unknown_args)
        except Exception:
            logger.exception("Failed to trigger QNN embedded evaluation.")


def trigger_multimodal_qnn_eval(model: str, config_args: list[str]) -> None:
    """Trigger dt_flow evaluation job based on ModelConfig.

    Args:
        model: name of the model to evaluate
        config_args: list of arguments to pass to the model run config
    """
    eval_run_config = get_qnn_run_args(PipelineStep.EVALUATE_QNN, args=config_args)
    if model == Model.BOX3D_SIMPLE:
        config, unknown_args = get_run_config_box3d_simple(config_args, PipelineStep.EVALUATE_QNN)
    elif model == Model.OCCUPANCY:
        config, unknown_args = get_run_config_occupancy(config_args, PipelineStep.EVALUATE_QNN)
    elif model == Model.SPARSE_DETECTION:
        config, unknown_args = get_run_config_sparse_detection(config_args, PipelineStep.EVALUATE_QNN)
    elif model == Model.LANESEGNET:
        config, unknown_args = get_run_lanesegnet_config(config_args, PipelineStep.EVALUATE_QNN)
    else:
        msg = f"Unsupported model {model} for the qnn evaluation."
        raise ValueError(msg)

    if config.eval_config.trigger_dt_flow_qnn_embedded_evaluation:
        for dataset_name in config.data_config.alliance_dataset_names:
            trigger_dt_flow_from_azure_context(
                dataset_name=dataset_name,
                stage=eval_run_config.stage,
                launcher_args=eval_run_config.launcher_args,
                usecase_model=model,
            )
        if config.eval_config.run_on_default_dataset_on_dt_flow:
            trigger_dt_flow_from_azure_context(
                dataset_name="",
                stage=eval_run_config.stage,
                launcher_args=eval_run_config.launcher_args,
                usecase_model=model,
            )
        # Run profiling stage on dt-flow
        profile_run_config = get_qnn_run_args(PipelineStep.PROFILE_QNN, args=config_args, steps_per_predict=1)
        trigger_dt_flow_from_azure_context(
            dataset_name="",
            stage=profile_run_config.stage,
            launcher_args=profile_run_config.launcher_args,
            usecase_model=model,
        )

    # Run video creation on dt-flow
    if config.eval_config.trigger_dt_flow_qnn_embedded_visualization:
        video_run_config = get_qnn_run_args(PipelineStep.VISUALIZATION_QNN, args=config_args)
        trigger_dt_flow_from_azure_context(
            dataset_name="",
            stage=video_run_config.stage,
            launcher_args=video_run_config.launcher_args,
            usecase_model=model,
        )


def deploy_model_to_artifactory(
    release_folder: str, model_name: str, azureml_model: AzureMLModel | None, argv: list[str]
) -> None:
    """Deploy model to pace Artifactory.

    Args:
        release_folder: path to the release 'model' folder.
        model_name: Name of the model. Added to the name of the package.
        azureml_model: AzureML model. Defines the package version. If not provided, defaults to 0.
        argv: Other arguments.
    """
    parser = argparse.ArgumentParser(description="Parser for model Artifactory deployment.")
    parser.add_argument("--short_ct_run", type=str.lower, choices=["true", "false"], default="false")
    args, _ = parser.parse_known_args(argv)
    if args.short_ct_run == "true":
        LOGGER.info("Skipping model Artifactory deployment due to short CT run.")
        return

    try:
        model = Model(model_name)
    except ValueError:
        LOGGER.warning(f"Unknown model {model_name}. Skipping model publishing to Artifactory.")
        return

    config, _ = MODEL_CONFIG[model](argv, PipelineStep.PUBLISH, 1)

    ml_client = get_ml_client()
    ml_job = get_job(ml_client)
    if ml_client is None or ml_job is None:
        LOGGER.warning("Could not get pipeline context. Skipping model publishing to Artifactory.")
        return

    model_version = 0
    if azureml_model is not None and azureml_model.version is not None:
        with contextlib.suppress(ValueError):
            model_version = int(azureml_model.version)

    if ml_client.workspace_name is None:
        LOGGER.warning("Could not get workspace name from ml client. Skipping model publishing to Azure.")
        return

    publisher = ArtifactoryPublisher(
        azure_workspace=ml_client.workspace_name,
        artifactory_remote_config=get_default_pace_artifactory_config(),
        experiment_name=f"{model_name}_{ml_job.experiment_name}",
        experiment_version=model_version,
    )

    # Deploy the model splits as separate packages
    task_ids = [task.identifier() for task in config.multi_task_collection.enabled_tasks]
    for deployment_name in [f"{graph_slice.value}" for graph_slice in SliceSubgraphs] + task_ids:
        conversion_folder = Path(release_folder) / "conversion" / f"conversion_{deployment_name}"
        if not conversion_folder.exists():
            LOGGER.info(f"Conversion folder {conversion_folder} does not exist.")
            continue
        qnn_artifacts = QnnModelArtifacts(
            qnn_conversion_output_dir=conversion_folder,
            model_name=deployment_name,
            backend=QnnBackend.HTP,
        )
        publisher.deploy(artifacts_collectors=[qnn_artifacts], deployment_name=deployment_name)


if __name__ == "__main__":
    main(sys.argv[1:])
