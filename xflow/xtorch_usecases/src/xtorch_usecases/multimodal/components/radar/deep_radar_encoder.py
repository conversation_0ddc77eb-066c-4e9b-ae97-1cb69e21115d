"""Deep Radar Net implementation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from dataclasses import dataclass, field
from typing import Any

from torch import Tensor, nn, no_grad

from xtorch.geometry.volumes.common import VolumeParams
from xtorch.nn.encoders.voxelizers.definitions import ValueRange
from xtorch_usecases.common.datasets.alliance.definitions import (
    ALLIANCE_RADAR_FEATURE_STATISTICS,
    RADAR_ACCUMULATION_WINDOW_MILLI_SECONDS,
)
from xtorch_usecases.deep_radar_objects.nn.backbones.lh5_resnet import LH5ResNet, LH5ResNetConfig
from xtorch_usecases.deep_radar_objects.nn.encoders.efficient_pillar_scatter import (
    EfficientPillarEncoderConfig,
    EfficientPillarsScatter,
    EfficientPillarsScatterConfig,
)
from xtorch_usecases.deep_radar_objects.nn.encoders.lh5_fpn import LH5NetFPNLike, LH5NetFPNLikeConfig
from xtorch_usecases.deep_radar_objects.nn.encoders.pypillars_scatter import (
    PyPillarConfig,
    PyPillarsScatter,
    PyPillarsScatterVoxel,
)
from xtorch_usecases.deep_radar_objects.nn.voxel_encoders.pypillars_voxelizer_encoder import (
    DroGridFeatureIndex,
    PypillarsVoxelizerEncoder,
    RadarVoxelizerData,
)


@dataclass
class DeepRadarNetEncoderConfig:
    """Parameters for the DeepRadarNet Encoder.

    Attributes:
        encoder_params (PyPillarConfig): The pre- and postprocessing parameters for the encoder.
        lh5_res_net (LH5ResNetParams): The parameters for the LH5ResNet.
        lh5_net_fpn_like (LH5NetFPNLikeParams): The parameters for the LH5NetFPNLike.
        point_cloud_range: The range of the point cloud in the x, y dimensions.
        voxel_size: The size of the voxels in the x, y, z_size dimensions.
        grid_size: The grid size in the format [grid_x, grid_y] dimensions.
        onnx_compatible: Whether the model should be onnx compatible.
        normalize_input_values: input values should be normalized
        normalization_radar_feature_statistics: The normalization statistics for the radar features.
        num_voxels: The number of voxels which will be generated.
        num_points_per_voxel: The number of points per voxel.
        in_channels (int): Number of input channels. Default is 9.
        out_channels (int): Number of output channels. Default is 32.
        raw_feature_output (bool): Whether to output raw features. Default is True.
    """

    encoder_config: PyPillarConfig
    lh5_res_net_config: LH5ResNetConfig
    lh5_net_fpn_like_config: LH5NetFPNLikeConfig

    volume_params: VolumeParams
    voxel_size: tuple[float, float, float] = (0.5, 0.5, 32)
    grid_size: tuple[int, int] = (640, 320)

    num_voxels: int = 10000
    num_points_per_voxel: int = 10
    onnx_compatible: bool = True
    normalize_input_values: bool = True
    accumulation_window: ValueRange = field(default_factory=lambda: RADAR_ACCUMULATION_WINDOW_MILLI_SECONDS)
    normalization_radar_feature_statistics: dict[str, ValueRange] = field(
        default_factory=lambda: dict(ALLIANCE_RADAR_FEATURE_STATISTICS)
    )
    in_channels: int = int(DroGridFeatureIndex.NUMBER_OF_FEATURES)
    out_channels: int = 32
    raw_feature_output: bool = True
    efficent_pillar_scatter: bool = True


class DeepRadarNetEncoder(nn.Module):
    """The encoder of the DeepRadarNet.

    This encoder is a combination of the PypillarsVoxelizer, PypillarsScatter, LH5ResNet, and LH5NetFPNLike.
    The encoder takes a point cloud in vehicle coordinates as input and returns a list of feature maps in vehicle
    coordinates with the shape (B,C,H,W) where H is X-Axis dimension and W the Y-Axis dimension.
    """

    def __init__(
        self,
        config: DeepRadarNetEncoderConfig,
    ) -> None:
        """Constructor.

        Args:
            config: The parameters for the encoder.
        """

        super().__init__()
        self._config = config
        self._onnx_compatible = self._config.onnx_compatible
        self._efficent_pillar_scatter = self._config.efficent_pillar_scatter

        # Pypillars Voxelizer
        in_channels = self._config.in_channels
        out_channels = self._config.out_channels
        raw_feature_output = self._config.raw_feature_output
        self._pypillars_voxelizer = PypillarsVoxelizerEncoder(
            self._config.voxel_size,
            self._config.volume_params,
            in_channels,
            out_channels,
            num_voxels=self._config.num_voxels,
            num_points_per_voxel=self._config.num_points_per_voxel,
            onnx_compatible=self._onnx_compatible,
            raw_feature_output=raw_feature_output,
            normalize_input_values=self._config.normalize_input_values,
            normalization_statistics=self._config.normalization_radar_feature_statistics,
            accumulation_window=self._config.accumulation_window,
            image_like_feature_representation=self._efficent_pillar_scatter,
        )
        if self._efficent_pillar_scatter:
            self._pypillars_scatter = EfficientPillarsScatter(
                in_channels,
                num_points_per_voxel=self._config.num_points_per_voxel,
                output_shape=self._config.grid_size,
                config=EfficientPillarsScatterConfig(grid_preprocess_config=EfficientPillarEncoderConfig()),
            )
        else:
            self._pypillars_scatter = (
                PyPillarsScatterVoxel(
                    in_channels, output_shape=self._config.grid_size, config=self._config.encoder_config
                )
                if self._onnx_compatible
                else PyPillarsScatter(
                    in_channels, output_shape=self._config.grid_size, config=self._config.encoder_config
                )
            )

        # Backbone
        self._backbone = LH5ResNet(params=self._config.lh5_res_net_config)

        # Create the output paddings needed by the neck
        dummy_input = Tensor(1, self._backbone.in_channels, self._config.grid_size[0], self._config.grid_size[1])
        with no_grad():
            dummy_output = self._backbone(dummy_input)
        output_grid_sizes = [[t.shape[2], t.shape[3]] for t in dummy_output[:-1]]
        # Neck
        self._config.lh5_net_fpn_like_config.output_grid_sizes = output_grid_sizes
        self._neck = LH5NetFPNLike(params=self._config.lh5_net_fpn_like_config)

    def forward(
        self, points: Tensor | list[Tensor] | None, voxels: RadarVoxelizerData | None
    ) -> tuple[Any, RadarVoxelizerData | None, Any]:
        """The forward function of the encoder.

        Args:
            points: The input points.
            voxels: The input voxels from the voxelizer.

        Note: The voxelizer is not onnx convertible at the moment. Therefore, we might preprocess the radar pointcloud
              by the voxelizer before conversion. This is possible, because the voxelizer does not have any weights.

        Returns:
            The output feature maps, voxels and intermediate scatter output.
        """

        # Validate inputs
        has_points = points is not None
        has_voxels = voxels is not None

        if has_points:
            voxels = self._pypillars_voxelizer(points)
        elif not has_voxels:
            msg = "Either 'points' or 'voxels' must be provided"
            raise ValueError(msg)

        scatter_output = self._pypillars_scatter(voxels)

        x = self._backbone(scatter_output.permute(0, 3, 1, 2).contiguous())  # switch from channel last to channel first

        x = self._neck(x)

        return x, voxels, scatter_output
