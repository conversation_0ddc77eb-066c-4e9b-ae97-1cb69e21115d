"""Config for the Occupancy 5V common BEV model."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import multiprocessing as mp
import os
from collections.abc import Callable
from functools import partial
from pathlib import Path
from typing import Any, Final

import torch
from qnn_custom_ops import QnnBackend

from conversion.qnn.definitions import InputTensorConfig, InputTensorLayout, InputTensorType, QuantCheckerConfig
from conversion.qnn.inference import QnnProfilingLevel
from data_formats.occupancy_25d.data_loading import LABEL_DEFINITION
from evil.config.run import RunEnvironment
from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import Usage
from xcontract.training.run_mode import RunMode
from xtorch.training import Stage as TorchStage
from xtorch.training.trainer_config import TrainerConfig
from xtorch_usecases.common.datasets.alliance.factory import OCCUPANCY_MID_RANGE_TASK_ID
from xtorch_usecases.common.datasets.combined.data_module import CombinedDataModuleConfig
from xtorch_usecases.common.environment import RuntimeEnvironment
from xtorch_usecases.common.helpers import (
    get_env_specific_setting,
    get_eval_compute_type,
    get_pipeline_step_specific_setting,
    get_run_mode_specific_setting,
)
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.components.definitions import CameraBackboneType
from xtorch_usecases.multimodal.components.multimodal_encoder import MultimodalEncoderNames
from xtorch_usecases.multimodal.components.radar.definitions import RadarEncoderParams, RadarModelType
from xtorch_usecases.multimodal.configs.callback import CallbackConfig
from xtorch_usecases.multimodal.configs.camera_backbone import HALVED_CAMERA_BACKBONE_CONFIG
from xtorch_usecases.multimodal.configs.convert import MultiModalConvertConfig
from xtorch_usecases.multimodal.configs.evaluate import MultiModalEvaluateConfig, get_evil_config_occupancy
from xtorch_usecases.multimodal.configs.geometry import (
    MID_RANGE_BEV_LAYOUT,
    MID_RANGE_DEPTH_CONFIG,
    MID_RANGE_OCCUPANCY_GRID_LAYOUT,
)
from xtorch_usecases.multimodal.configs.output_folders_factory import get_default_pipeline_step_paths
from xtorch_usecases.multimodal.configs.run import (
    MultiModalRunConfig,
    TaskConfigs,
)
from xtorch_usecases.multimodal.configs.train import MultiModalTrainingConfig
from xtorch_usecases.multimodal.configs.usecase import UsecaseConfig
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.tasks.occupancy.definitions import OccupancyLossType, OccupancyTaskConfig

_XUSECASES_DATADIR = (
    Path(__file__).parent.parent.parent.parent.parent.parent.parent.parent / "xusecases/tests/test_data"
)
_OCCUPANCY_DATA_BASEPATH = get_env_specific_setting(
    local=(_XUSECASES_DATADIR / "vision" / "datasets" / "multiview" / "occupancy_25d").as_posix(),
    azure=lambda: f"{os.environ.get('AZUREML_DATAREFERENCE_ingest_datasets')}/generic_objects/ai_occupancy/gt_bev_25d/"
    "gt_hesai/gt_agg_2.2M/dualgrid_lidarmap/",
)
_OCCUPANCY_DATASET_BASEPATH = get_env_specific_setting(
    local=(_XUSECASES_DATADIR / "vision" / "datasets" / "multiview" / "occupancy_25d").as_posix(),
    azure=lambda: f"{os.environ.get('AZUREML_DATAREFERENCE_ingest_datasets')}/generic_objects/ai_occupancy/gt_bev_25d/"
    "gt_hesai/gt_agg_2.2M/dualgrid_lidarmap/",
)
_OCCUPANCY_DATASET_NAME = "occupancy_jpg"
_MAX_NUM_CPUS = os.cpu_count() or 4

_LOOMY_DATASET_INDEX_PATH = get_env_specific_setting(
    local="xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy",
    azure="https://vdeephierarchicalprod.blob.core.windows.net/loomy-catalogs/testing/ada",
    dt_flow="https://vdeephierarchicalprod.blob.core.windows.net/loomy-catalogs/testing/ada",
)
_LOOMY_DATASET_NAME = get_env_specific_setting(
    local="ada",
    azure="multitask_dyo_occ",
    dt_flow="multitask_dyo_occ",
)
_LOOMY_DATASET_VERSION: dict[Usage, int | None] = {
    Usage.TRAINING: get_env_specific_setting(local=1, azure=2),
    Usage.VALIDATION: get_env_specific_setting(local=1, azure=1, dt_flow=1),
    Usage.TEST: get_env_specific_setting(local=1, azure=1, dt_flow=1),
    Usage.CALIBRATION: get_env_specific_setting(local=1, azure=1),
}
_NUM_TEST_FRAMES_LOOMY = 216 * 450  # 216 test recordings at 15Hz


# Currently not used but None currently not supported for radar_encoder_params at several places
RADAR_ENCODER_PARAMS: Final = RadarEncoderParams(
    bev_layout=MID_RANGE_BEV_LAYOUT, model_type=RadarModelType.NV_RADAR_NET, use_voxelizer=True
)


def get_multi_modal_run_config(  # noqa: PLR0913
    run_mode: RunMode,
    pipeline_step: PipelineStep,
    node_count: int = 1,
    output_folder: Path = Path("outputs"),
    input_folder: Path = Path("outputs"),
    data_base_path: str | None = None,
    dataset_base_path: str | None = None,
    dataset_name: str | None = None,
    eval_checkpoint: str | None = None,
    train_checkpoint: str | None = None,
    qnn_eval_backend: str | None = None,
    target_address: tuple[str, str] | None = None,
    *,
    eval_full_model: bool = False,
    profiling_level: QnnProfilingLevel = QnnProfilingLevel.NO_PROFILING,
    use_loomy: bool = False,
    loomy_dataset_index: str | None = None,
) -> MultiModalRunConfig:
    """Returns the configuration for the occupancy 5V common BEV model.

    Args:
        run_mode: The run mode of the configuration.
        pipeline_step: The pipeline step.
        node_count: The number of nodes used for training.
        output_folder: The base output folder.
        input_folder: The base input folder.
        data_base_path: The base path for the data.
        dataset_base_path: The base path for the dataset.
        dataset_name: The name of the dataset.
        eval_checkpoint: Directory containing checkpoints or path to an explicit checkpoint file '... .ckpt' that shall
            be restored for evaluation. If using a checkpoint from an azure ml experiment, provide only the experiment
            name or relative path to the dedicated checkpoint, e.g.
            xtorch_testing_...[/outputs/checkpoints/step=1881.ckpt]. If None, the last one from the default
            checkpoints directory is restored.
        train_checkpoint: "Directory containing checkpoints or path to an explicit checkpoint file '... .ckpt' used to
            resume training from. If using a checkpoint from an azure ml experiment, provide only the experiment name or
            relative path to the dedicated checkpoint, e.g. xtorch_testing_...[/outputs/checkpoints/step=1881.ckpt].
            If None, default pre-trained weights for only the backbones will be used."
        qnn_eval_backend: The backend to use for QNN evaluation.
        eval_full_model: Whether to evaluate the full model.
        target_address: The target address for the QNN evaluation.
        profiling_level: Profiling level to use in qnn net run.
        use_loomy: Whether to use loomy for data loading.
        loomy_dataset_index: Path to the local loomy catalog.

    Returns: The multimodal run config.
    """
    pipeline_step_specific_setting_fn: Callable[..., Any] = partial(
        get_pipeline_step_specific_setting, pipeline_step=pipeline_step
    )
    pipeline_step_folders = get_default_pipeline_step_paths(
        pipeline_step=pipeline_step,
        input_base_folder=input_folder,
        output_base_folder=output_folder,
    )

    data_config = CombinedDataModuleConfig(
        environment=get_env_specific_setting(local="local", azure="azure"),
        dataset_name="occupancy",
        loomy_dataindex_path=_LOOMY_DATASET_INDEX_PATH,
        loomy_dataset_name=_LOOMY_DATASET_NAME,
        loomy_dataset_version=_LOOMY_DATASET_VERSION,
        loomy_loading_enabled=use_loomy,
        sequential_loading=use_loomy,
        occupancy_data_basepath=_OCCUPANCY_DATA_BASEPATH if data_base_path is None else data_base_path,
        occupancy_dataset_basepath=_OCCUPANCY_DATASET_BASEPATH if dataset_base_path is None else dataset_base_path,
        occupancy_dataset_name=_OCCUPANCY_DATASET_NAME if dataset_name is None else dataset_name,
        occupancy_dataset_workspace=get_env_specific_setting(local=None, azure="ai_semantic"),
        enabled_input_modalities=(InputDataId.CAMERA_TV, InputDataId.CAMERA_FC1),
        enabled_tasks=(OCCUPANCY_MID_RANGE_TASK_ID,),
        training_input_shapes={
            InputDataId.CAMERA_DEF_CYLINDER: [1536, 1536],
            InputDataId.CAMERA_CYLINDER: [1280, 2304],
        },
        occupancy_mid_range_grid_layout=MID_RANGE_OCCUPANCY_GRID_LAYOUT,
        batch_size_train=get_env_specific_setting(local=1, azure=8),
        batch_size_val=get_env_specific_setting(local=1, azure=8, dt_flow=8),
        alliance_dataset_basepath="",
        alliance_data_basepath="",
        alliance_dataset_names=[""],
        bev_layouts={"occupancy": MID_RANGE_BEV_LAYOUT},
        box3d_label_set_name="",
        box3d_label_set_version="",
        num_workers_train=get_env_specific_setting(
            local=2, azure=min(32, _MAX_NUM_CPUS // max(torch.cuda.device_count(), 1))
        ),
        num_workers_val=get_env_specific_setting(
            local=1, azure=min(16, _MAX_NUM_CPUS // max(torch.cuda.device_count(), 1)), dt_flow=12
        ),
        pin_memory=get_env_specific_setting(local=False, azure=True),
        prefetch_factor=1,
        shuffle=pipeline_step_specific_setting_fn(
            default=True,
            evaluate_torch=False,
            evaluate_qnn=False,
            visualization_torch=False,
        ),
        single_worker_wrapper=use_loomy,
    )
    assert data_config.occupancy_mid_range_grid_layout is not None
    assert data_config.occupancy_dataset_name is not None

    # Overwrite the standard loomy paths for local execution. Used by qnn_eval on dt-flow.
    if loomy_dataset_index is not None:
        data_config.loomy_dataindex_path = loomy_dataset_index

    num_frames_per_epoch = 350_000
    train_steps_per_epoch = get_run_mode_specific_setting(
        run_mode,
        long=num_frames_per_epoch // (data_config.batch_size_train * max(torch.cuda.device_count(), 1) * node_count),
        short=10,
    )
    num_epochs = get_run_mode_specific_setting(run_mode, long=6, short=2)

    training_config = MultiModalTrainingConfig(
        label_class_names=LABEL_DEFINITION.classes,
        background_id=LABEL_DEFINITION.ignore_id,
        lr=3e-4,
        weight_decay=0.01,
        num_epochs=num_epochs,
        train_steps_per_epoch=train_steps_per_epoch,
        warmup_steps=int(0.15 * train_steps_per_epoch),
        cooldown_steps=(train_steps_per_epoch * num_epochs) // 4,
        output_folder=pipeline_step_folders.output_artifacts,
        enabled_input_modalities=(InputDataId.CAMERA_DEF_CYLINDER, InputDataId.CAMERA_CYLINDER),
        weight_init_checkpoint_paths=None,
        log_eval_image_every_n_sample=pipeline_step_specific_setting_fn(
            default=get_run_mode_specific_setting(run_mode, long=500, short=2),
            visualization_torch=1,  # log every sample for VISUALIZATION step
        ),
        strict_loading=False,
        freeze_encoder_weights_patterns=None,
        num_cams={
            InputDataId.CAMERA_CYLINDER: 1,  # FC1
            InputDataId.CAMERA_DEF_CYLINDER: 4,  # TV
        },
        camera_backbone_configs={
            InputDataId.CAMERA_DEF_CYLINDER: HALVED_CAMERA_BACKBONE_CONFIG,  # TV
        },
        skip_encoder_weight_init=[MultimodalEncoderNames.CAMERA_DEF_CYLINDER_ENCODER],
        radar_encoder_params=RADAR_ENCODER_PARAMS,  # Not used, just here as None is not supported.
    )
    train_device = "cpu" if pipeline_step in (PipelineStep.EVALUATE_QNN, PipelineStep.PROFILE_QNN) else "cuda"
    trainer_config = TrainerConfig(
        max_epochs=training_config.num_epochs,
        default_root_dir=pipeline_step_folders.output_artifacts,
        accelerator=train_device,
        train_epoch_length=training_config.train_steps_per_epoch,
        limit_val_batches=get_run_mode_specific_setting(run_mode, long=500, short=10),
        limit_test_batches=get_run_mode_specific_setting(
            run_mode, long=_NUM_TEST_FRAMES_LOOMY // data_config.batch_size_val if use_loomy else None, short=10
        ),
        limit_predict_batches=get_run_mode_specific_setting(
            run_mode, long=_NUM_TEST_FRAMES_LOOMY // data_config.batch_size_val if use_loomy else None, short=10
        ),
        log_every_n_steps=1000,
        num_sanity_val_steps=1,
        check_val_every_n_epoch=1,
        enable_checkpointing=True,
        enable_progress_bar=True,
        enable_model_summary=True,
        gradient_clip_val=10.0,
        accumulate_grad_batches=1,
        precision="bf16-mixed",
        deterministic=False,
        benchmark=True,
        use_distributed_sampler=False,  # distributed sampler is already created by data loader
        devices=get_env_specific_setting(local=1, azure="auto"),
        num_nodes=node_count,
    )

    quant_check_config = QuantCheckerConfig(
        short_run=get_run_mode_specific_setting(
            run_mode,
            short=True,
            long=False,
        )
    )

    # qnn conversion config for all input tensors
    inputs_config = [
        InputTensorConfig(
            name="input_0",
            input_type=InputTensorType.IMAGE,
        ),
        InputTensorConfig(
            name="input_1",
            input_type=InputTensorType.IMAGE,
        ),
        InputTensorConfig(
            name="input_2",
            input_type=InputTensorType.IMAGE,
        ),
        InputTensorConfig(
            name="input_3",
            input_type=InputTensorType.IMAGE,
        ),
        InputTensorConfig(
            name="input_4",
            input_type=InputTensorType.IMAGE,
        ),
        InputTensorConfig(
            name="input_lut_0",
            input_type=InputTensorType.DEFAULT,
            input_layout=InputTensorLayout.NHWC,
        ),
        InputTensorConfig(
            name="input_lut_1",
            input_type=InputTensorType.DEFAULT,
            input_layout=InputTensorLayout.NHWC,
        ),
        InputTensorConfig(
            name="input_lut_2",
            input_type=InputTensorType.DEFAULT,
            input_layout=InputTensorLayout.NHWC,
        ),
        InputTensorConfig(
            name="input_lut_3",
            input_type=InputTensorType.DEFAULT,
            input_layout=InputTensorLayout.NHWC,
        ),
        InputTensorConfig(
            name="input_lut_4",
            input_type=InputTensorType.DEFAULT,
            input_layout=InputTensorLayout.NHWC,
        ),
    ]

    convert_config = MultiModalConvertConfig(
        default_root_dir=pipeline_step_folders.conversion,
        qnn_backends=[QnnBackend.CPU, QnnBackend.HTP],
        num_calib_samples=get_run_mode_specific_setting(run_mode=run_mode, long=300, short=1),
        skip_qnn_conversion=False,
        quant_check_config=quant_check_config,
        inputs_config=inputs_config,
    )

    callback_config = CallbackConfig(
        output_root_dir=output_folder,
        image_logging_frequency=pipeline_step_specific_setting_fn(
            default=training_config.log_eval_image_every_n_sample, visualization_torch=1
        ),
    )

    qnn_backend = QnnBackend(qnn_eval_backend) if qnn_eval_backend else None
    eval_run_environment = RunEnvironment(
        num_processes=get_env_specific_setting(
            local=pipeline_step_specific_setting_fn(default=1, evaluate_qnn=mp.cpu_count()),
            azure=mp.cpu_count(),
            dt_flow=24,
        ),
        eval_compute_type=get_eval_compute_type(pipeline_step, qnn_backend),
    )

    eval_config = MultiModalEvaluateConfig(
        checkpoint=eval_checkpoint,
        eval_torch_model=pipeline_step.is_torch,
        eval_full_model=eval_full_model,
        profiling_level=profiling_level,
        qnn_backend=qnn_backend,
        target_address=target_address,
        trigger_dt_flow_qnn_embedded_evaluation=run_mode == RunMode.LONG,
        run_environment=eval_run_environment,
    )

    summaries_folder = output_folder / "summaries"
    summaries_folder.mkdir(parents=True, exist_ok=True)

    evil_configs = {
        TorchStage.VALIDATE: get_evil_config_occupancy(online_eval=True)[0],
        TorchStage.PREDICT: get_evil_config_occupancy(online_eval=False)[0],
    }

    evaluation_folder = (
        pipeline_step_folders.evaluation_qnn
        if pipeline_step.is_convert or pipeline_step.is_qnn
        else pipeline_step_folders.evaluation_torch
    )

    task_config = TaskConfigs(
        occupancy=OccupancyTaskConfig(
            data_weight=1.0,
            loss_weight=1.0,
            camera_backbone_type=CameraBackboneType.BEV,
            enabled_input_modalities=training_config.enabled_input_modalities,
            bev_layout=MID_RANGE_BEV_LAYOUT,
            occupancy_grid_layout=data_config.occupancy_mid_range_grid_layout,
            num_predicted_classes=training_config.num_classes,
            ignore_class_id=LABEL_DEFINITION.ignore_id,
            loss_weights={
                OccupancyLossType.HEIGHT: 1.0,
                OccupancyLossType.SEMANTIC: 1.0,
                OccupancyLossType.VISIBILITY: 1.0,
            },
            class_weights=LABEL_DEFINITION.class_weights_mid_range,
            use_uncertainty_weighting=False,
            summary_output_path=summaries_folder,
            label_set_name=LABEL_DEFINITION.label_set.name,
            label_set_version=LABEL_DEFINITION.label_set.version,
            ignore_class=LABEL_DEFINITION.ignore_class,
            semantic_mapping_required=False,
            save_ground_truth_for_eval=use_loomy,  # We need to copy GT over for evaluation when using loomy
            delete_predictions_after_eval=True,
            evaluation_folder=evaluation_folder,
            evil_configs=evil_configs,
            eval_run_environment=eval_run_environment,
            evaluation_torch_folder=pipeline_step_folders.evaluation_torch,
            visualization_folder=pipeline_step_folders.visualization,
            combined_metrics_config=Path(get_evil_config_occupancy(online_eval=True)[1]),
            bev_features_id=InputDataId.CAMERA_BEV_MID_RANGE,
        ),
    )

    return MultiModalRunConfig(
        model=Model.OCCUPANCY,
        bev_layout=MID_RANGE_BEV_LAYOUT,
        depth_configs=MID_RANGE_DEPTH_CONFIG,
        data_config=data_config,
        training_config=training_config,
        eval_config=eval_config,
        trainer=trainer_config,
        training_epoch_length=training_config.train_steps_per_epoch,
        convert_config=convert_config,
        train_checkpoint=train_checkpoint,
        pipeline_step_paths=pipeline_step_folders,
        runtime_environment=get_env_specific_setting(local=RuntimeEnvironment.LOCAL, azure=RuntimeEnvironment.AZURE),
        task_configs=task_config,
        common_config=UsecaseConfig(callback_config=callback_config),
        enable_visualization_writer=True,
        run_mode=run_mode,
    )
