"""Release multitask configurations for multimodal usecases."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import multiprocessing as mp
import os
from collections.abc import Callable
from dataclasses import dataclass, replace
from pathlib import Path
from typing import Any

import torch
from qnn_custom_ops import QnnBackend

from conversion.qnn.definitions import QuantCheckerConfig
from conversion.qnn.inference import QnnProfilingLevel
from data_formats.occupancy_25d.data_loading import LABEL_DEFINITION as OCCUPANCY_LABEL_DEFINITION
from evil.config.run import RunEnvironment
from xcontract.data.definitions.inputs import InputDataId
from xcontract.training.run_mode import RunMode
from xcontract.usecases.multiview.multiview_split import MultiviewSplit
from xtorch.nn.heads.object_detection.box3d import DetectionRange
from xtorch.training import Stage
from xtorch.training import Stage as TorchStage
from xtorch.training.trainer_config import TrainerConfig
from xtorch_extensions.quantization.qat_config import QuantizationConfig
from xtorch_usecases.common.datasets.alliance.factory import OCCUPANCY_MID_RANGE_TASK_ID, OCCUPANCY_NEAR_RANGE_TASK_ID
from xtorch_usecases.common.datasets.combined.data_module import BOX_3D_RELEASE_TASK_ID, CombinedDataModuleConfig
from xtorch_usecases.common.datasets.combined.definitions import BACKGROUND_CLASS, DEFAULT_LABEL_DEFINITION
from xtorch_usecases.common.environment import RuntimeEnvironment
from xtorch_usecases.common.helpers import get_env_specific_setting, get_eval_compute_type
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.components.definitions import CameraBackboneType
from xtorch_usecases.multimodal.components.radar.definitions import RadarEncoderParams, RadarModelType
from xtorch_usecases.multimodal.configs.callback import CallbackConfig
from xtorch_usecases.multimodal.configs.camera_backbone import CameraBackboneConfig
from xtorch_usecases.multimodal.configs.convert import MultiModalConvertConfig
from xtorch_usecases.multimodal.configs.evaluate import (
    MultiModalEvaluateConfig,
    get_evil_config,
    get_evil_config_occupancy,
)
from xtorch_usecases.multimodal.configs.geometry import (
    FAR_RANGE_BEV_LAYOUT,
    MID_RANGE_BEV_LAYOUT,
    MID_RANGE_DEPTH_CONFIG,
    MID_RANGE_OCCUPANCY_GRID_LAYOUT,
    NEAR_RANGE_BEV_LAYOUT,
    NEAR_RANGE_DEPTH_CONFIG,
    NEAR_RANGE_OCCUPANCY_GRID_LAYOUT,
    BevLayout,
)
from xtorch_usecases.multimodal.configs.model.common import compute_total_steps, generate_specific_setting_fns
from xtorch_usecases.multimodal.configs.output_folders_factory import PipelineStepPaths, get_default_pipeline_step_paths
from xtorch_usecases.multimodal.configs.qat import MultiModalQatConfig
from xtorch_usecases.multimodal.configs.run import MultiModalRunConfig, TaskConfigs
from xtorch_usecases.multimodal.configs.train import MultiModalTrainingConfig
from xtorch_usecases.multimodal.configs.usecase import UsecaseConfig
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.tasks.occupancy.definitions import OccupancyLossType, OccupancyTaskConfig
from xtorch_usecases.multimodal.tasks.sparse_detection.definitions import (
    EvaluationConfig,
    FocalHeadLossConfig,
    ImageWriterCallbackParams,
    LossConfig,
    NuscenesKPIsConfig,
    StreamPETRLossConfig,
    TrackingConfig,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.focal_head import FocalHeadParams
from xtorch_usecases.multimodal.tasks.sparse_detection.head import SparseDetectionHeadParams
from xtorch_usecases.multimodal.tasks.sparse_detection.sparse_detection_parameters import (
    DenoisingParams,
    PETRTemporalDecoderLayerParams,
    PETRTransformerDecoderParams,
    StreamPETRHeadParams,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.task import SparseDetectionTaskConfig


@dataclass
class SharedSubConfigArgs:
    """Args shared across the subconfigs of `MultiModalRunConfig`."""

    max_epochs: int
    train_epoch_length: int
    batch_size_train: int

    enabled_input_modalities: tuple[InputDataId, ...]
    bev_layout: BevLayout

    qnn_backend: QnnBackend | None = None
    eval_run_environment: RunEnvironment | None = None


def get_base_config(
    run_mode: RunMode, pipeline_step: PipelineStep, node_count: int, input_folder: str, output_folder: str
) -> MultiModalRunConfig:
    """Get the base MultimodalRunConfig for the multitask-multimodal-model."""
    run_mode_specific_setting_fn, pipeline_step_specific_setting_fn = generate_specific_setting_fns(
        run_mode=run_mode, pipeline_step=pipeline_step
    )

    qnn_backend: QnnBackend | None = None
    pipeline_step_folders = get_default_pipeline_step_paths(
        pipeline_step=pipeline_step, input_base_folder=input_folder, output_base_folder=output_folder
    )
    batch_size = get_env_specific_setting(local=2, azure=16)
    shared_sub_config_args = SharedSubConfigArgs(
        max_epochs=run_mode_specific_setting_fn(long=10, short=2),
        batch_size_train=batch_size,
        train_epoch_length=run_mode_specific_setting_fn(
            short=10,
            long=compute_total_steps(
                num_total_frames=614_725, batch_size=batch_size, node_count=node_count
            ),  # occ: 681730 // 2
        ),
        enabled_input_modalities=(InputDataId.CAMERA_TV, InputDataId.CAMERA_FC1, InputDataId.RADAR),
        bev_layout=MID_RANGE_BEV_LAYOUT,
        qnn_backend=qnn_backend,
        eval_run_environment=RunEnvironment(
            num_processes=get_env_specific_setting(local=1, azure=max(mp.cpu_count() // 2, 1)),
            eval_compute_type=get_eval_compute_type(pipeline_step, qnn_backend),
        ),
    )

    trainer_config = get_trainer_config(
        run_mode_specific_setting_fn,
        pipeline_step_specific_setting_fn,
        node_count,
        pipeline_step_folders,
        shared_sub_config_args,
    )
    training_config = get_training_config(
        run_mode_specific_setting_fn, pipeline_step_specific_setting_fn, pipeline_step_folders, shared_sub_config_args
    )
    eval_config = get_evaluation_config(
        run_mode_specific_setting_fn, pipeline_step_specific_setting_fn, shared_sub_config_args
    )
    convert_config = get_conversion_config(run_mode_specific_setting_fn, pipeline_step_folders)
    qat_config = get_qat_config(run_mode_specific_setting_fn, pipeline_step_folders)
    callback_config = get_callback_config(
        run_mode_specific_setting_fn, pipeline_step_specific_setting_fn, pipeline_step_folders
    )
    data_config = get_data_config(
        run_mode_specific_setting_fn, pipeline_step_specific_setting_fn, shared_sub_config_args
    )
    task_configs = get_tasks_configs(
        run_mode_specific_setting_fn, pipeline_step_specific_setting_fn, pipeline_step_folders, shared_sub_config_args
    )

    return MultiModalRunConfig(
        model=Model.RELEASE,
        bev_layout=training_config.bev_layout,
        depth_configs=training_config.depth_configs,
        bev_layout_near_range=training_config.bev_layout_near_range,
        depth_configs_near_range=training_config.depth_configs_near_range,
        data_config=data_config,
        training_config=training_config,
        eval_config=eval_config,
        trainer=trainer_config,
        training_epoch_length=training_config.train_steps_per_epoch,
        convert_config=convert_config,
        pipeline_step_paths=pipeline_step_folders,
        runtime_environment=get_env_specific_setting(local=RuntimeEnvironment.LOCAL, azure=RuntimeEnvironment.AZURE),
        task_configs=task_configs,
        common_config=UsecaseConfig(callback_config=callback_config),
        qat_config=qat_config,
        enable_visualization_writer=True,
        run_mode=run_mode,
    )


def get_trainer_config(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_specific_setting_fn: Callable[..., Any],
    node_count: int,
    pipeline_step_folders: PipelineStepPaths,
    common_args: SharedSubConfigArgs,
) -> TrainerConfig:
    """Get the trainer configuration for the multitask-multimodal-model."""

    return TrainerConfig(
        max_epochs=pipeline_step_specific_setting_fn(default=common_args.max_epochs, qat=1),
        train_epoch_length=common_args.train_epoch_length,
        limit_val_batches=run_mode_specific_setting_fn(short=10, long=500),
        limit_predict_batches=run_mode_specific_setting_fn(long=None, short=2),
        gradient_clip_val=10.0,
        accumulate_grad_batches=1,
        num_sanity_val_steps=0,
        check_val_every_n_epoch=1,
        log_every_n_steps=1000,
        enable_checkpointing=True,
        enable_progress_bar=True,
        deterministic=False,
        benchmark=True,
        default_root_dir=pipeline_step_folders.output_artifacts,
        accelerator=pipeline_step_specific_setting_fn(default="cuda", evaluate_qnn="cpu"),
        devices=get_env_specific_setting(local=1, azure="auto"),
        num_nodes=node_count,
        precision="bf16-mixed",
        use_distributed_sampler=False,  # distributed sampler is already created by data loader
    )


def get_training_config(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_specific_setting_fn: Callable[..., Any],
    pipeline_step_folders: PipelineStepPaths,
    common_args: SharedSubConfigArgs,
) -> MultiModalTrainingConfig:
    """Get the training configuration for the multitask-multimodal-model."""
    return MultiModalTrainingConfig(
        label_class_names=[],  # seems to be unused
        background_id=-1,  # seems to be unused
        lr=pipeline_step_specific_setting_fn(default=1e-3, qat=1e-4),
        weight_decay=0.01,
        loss_aggregation="weighted",
        num_epochs=common_args.max_epochs,
        train_steps_per_epoch=common_args.train_epoch_length,
        warmup_steps=int(0.15 * common_args.train_epoch_length),
        cooldown_steps=(common_args.train_epoch_length * common_args.max_epochs) // 4,  # Not used
        output_folder=pipeline_step_folders.output_artifacts,
        bev_layout=common_args.bev_layout,
        bev_layout_near_range=NEAR_RANGE_BEV_LAYOUT,
        camera_backbone_configs={
            InputDataId.CAMERA_DEF_CYLINDER: CameraBackboneConfig(image_downsampling_factor=2),  # TV
        },
        depth_configs=MID_RANGE_DEPTH_CONFIG,
        depth_configs_near_range=NEAR_RANGE_DEPTH_CONFIG,
        enabled_input_modalities=(InputDataId.CAMERA_DEF_CYLINDER, InputDataId.CAMERA_CYLINDER, InputDataId.RADAR),
        weight_init_checkpoint_paths=None,
        log_eval_image_every_n_sample=run_mode_specific_setting_fn(short=2, long=1000),
        strict_loading=False,
        freeze_encoder_weights_patterns=None,  # [".*RadarBevEncoder.*"],
        num_cams={
            InputDataId.CAMERA_CYLINDER: 1,  # FC1
            InputDataId.CAMERA_DEF_CYLINDER: 4,  # TV
        },
        camera_encoder_split_config=MultiviewSplit.CAM,
        radar_encoder_params=RadarEncoderParams(
            bev_layout=FAR_RANGE_BEV_LAYOUT,
            model_type=RadarModelType.NV_RADAR_NET,
            use_voxelizer=pipeline_step_specific_setting_fn(
                default=True, qat=False, convert=False, evaluate_qnn=False, profile_qnn=False
            ),
        ),
        skip_inputs_validation_for_each_encoder=True,  # Interleaving datasets may lead currently to empty radar data
    )


def get_evaluation_config(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_specific_setting_fn: Callable[..., Any],
    common_args: SharedSubConfigArgs,
) -> MultiModalEvaluateConfig:
    """Get the evaluation configuration for the multitask-multimodal-model."""

    return MultiModalEvaluateConfig(
        checkpoint=None,
        eval_torch_model=pipeline_step_specific_setting_fn(
            default=False, visualization_torch=True, evaluate_torch=True
        ),
        eval_full_model=False,
        qnn_backend_identifier=common_args.qnn_backend,
        target_address=None,
        multi_images_per_epoch=False,
        profiling_level=QnnProfilingLevel.NO_PROFILING,
        trigger_dt_flow_qnn_embedded_evaluation=run_mode_specific_setting_fn(short=False, long=True),
        run_environment=common_args.eval_run_environment,
    )


def get_conversion_config(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_folders: PipelineStepPaths,
) -> MultiModalConvertConfig:
    """Get the conversion configuration for the multitask-multimodal-model."""

    quant_check_config = QuantCheckerConfig(
        enable_activation_histograms=run_mode_specific_setting_fn(short=False, long=True),
        enable_quant_error_plots=run_mode_specific_setting_fn(short=False, long=True),
        enable_weight_plots=run_mode_specific_setting_fn(short=False, long=True),
        short_run=run_mode_specific_setting_fn(short=True, long=False),
    )
    return MultiModalConvertConfig(
        default_root_dir=pipeline_step_folders.conversion,
        qnn_backends=[QnnBackend.CPU, QnnBackend.HTP],
        num_calib_samples=run_mode_specific_setting_fn(long=300, short=1),
        skip_qnn_conversion=False,
        quant_check_config=quant_check_config,
    )


def get_qat_config(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_folders: PipelineStepPaths,
) -> MultiModalQatConfig:
    """Get the qat configuration for the multitask-multimodal-model."""

    return MultiModalQatConfig(
        default_root_dir=pipeline_step_folders.conversion_qat,
        quantization_config=QuantizationConfig(
            num_calibration_samples=run_mode_specific_setting_fn(long=300, short=1),
            config_file="htp_quantsim_config_v73.json",
        ),
    )


def get_callback_config(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_specific_setting_fn: Callable[..., Any],
    pipeline_step_folders: PipelineStepPaths,
) -> CallbackConfig:
    """Get the callback configuration for the multitask-multimodal-model."""

    return CallbackConfig(
        output_root_dir=pipeline_step_folders.output_artifacts,
        image_logging_frequency=pipeline_step_specific_setting_fn(
            default=run_mode_specific_setting_fn(short=2, long=1000), visualization_torch=1
        ),
    )


def get_data_config(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_specific_setting_fn: Callable[..., Any],
    common_args: SharedSubConfigArgs,
) -> CombinedDataModuleConfig:
    """Get the data configuration for the multitask-multimodal-model."""
    max_cpu = (os.cpu_count() or 4) // max(torch.cuda.device_count(), 1)
    test_data_path = Path(__file__).parents[7] / "xusecases/tests/test_data"
    return CombinedDataModuleConfig(
        environment=get_env_specific_setting(local="local", azure="azure"),
        dataset_name="occupancy,alliance",
        loomy_loading_enabled=False,
        enabled_input_modalities=common_args.enabled_input_modalities,
        enabled_tasks=(OCCUPANCY_MID_RANGE_TASK_ID, OCCUPANCY_NEAR_RANGE_TASK_ID, BOX_3D_RELEASE_TASK_ID),
        # Occupancy
        occupancy_data_basepath=get_env_specific_setting(
            local=(test_data_path / "vision" / "datasets" / "multiview" / "occupancy_25d").as_posix(),
            azure=lambda: f"{os.environ.get('AZUREML_DATAREFERENCE_ingest_datasets')}/generic_objects/ai_occupancy/gt_bev_25d/"  # noqa: E501
            "gt_hesai/gt_agg_2.2M/dualgrid_lidarmap/",
        ),
        occupancy_dataset_basepath=get_env_specific_setting(
            local=(test_data_path / "vision" / "datasets" / "multiview" / "occupancy_25d").as_posix(),
            azure=lambda: f"{os.environ.get('AZUREML_DATAREFERENCE_ingest_datasets')}/generic_objects/ai_occupancy/gt_bev_25d/"  # noqa: E501
            "gt_hesai/gt_agg_2.2M/dualgrid_lidarmap/",
        ),
        occupancy_dataset_name="occupancy_jpg",
        occupancy_dataset_workspace=get_env_specific_setting(local=None, azure="ai_semantic"),
        occupancy_mid_range_grid_layout=MID_RANGE_OCCUPANCY_GRID_LAYOUT,
        occupancy_near_range_grid_layout=NEAR_RANGE_OCCUPANCY_GRID_LAYOUT,
        bev_layouts={
            "occupancy": common_args.bev_layout,
            "alliance": FAR_RANGE_BEV_LAYOUT,
            "occupancy,alliance": FAR_RANGE_BEV_LAYOUT,
        },
        # Sparse detection
        alliance_data_basepath=get_env_specific_setting(
            local=str(test_data_path / "vision/datasets/multiview/alliance_dataset"),
            azure=lambda: f"{os.environ['AZUREML_DATAREFERENCE_ingest_cache']}",
        ),
        alliance_dataset_basepath=get_env_specific_setting(
            local=str(test_data_path / "vision/datasets/multiview/alliance_dataset"), azure=None
        ),
        alliance_dataset_names=pipeline_step_specific_setting_fn(
            default=["dynamic_objects_bev_multimodal_0_25Hz:18"],
            visualization_torch=[
                "dynamic_objects_bev_multimodal_0_25Hz:18",
                "dynamic_objects_bev_multimodal_manual_labels:6",
            ],
            evaluate_torch=[
                "dynamic_objects_bev_multimodal_0_25Hz:18",
                "dynamic_objects_bev_multimodal_manual_labels:6",
            ],
            evaluate_qnn=[
                "dynamic_objects_bev_multimodal_0_25Hz:18",
                "dynamic_objects_bev_multimodal_manual_labels:6",
            ],
        ),
        alliance_dataset_workspace="viper_birds_eye_view",
        box3d_label_set_name=DEFAULT_LABEL_DEFINITION.label_set.name,
        box3d_label_set_version=DEFAULT_LABEL_DEFINITION.label_set.version,
        # Common
        training_input_shapes={
            InputDataId.CAMERA_CYLINDER: [1280, 2304],
            InputDataId.CAMERA_PINHOLE: [1024, 1920],
            InputDataId.CAMERA_DEF_CYLINDER: [1536, 1536],
        },
        batch_size_train=pipeline_step_specific_setting_fn(
            default=common_args.batch_size_train,
            qat=get_env_specific_setting(local=1, azure=8),
        ),
        batch_size_val=run_mode_specific_setting_fn(long=get_env_specific_setting(local=1, azure=8), short=2),
        num_workers_train=get_env_specific_setting(local=2, azure=min(24, max_cpu)),
        num_workers_val=get_env_specific_setting(local=1, azure=min(12, max_cpu)),
        num_workers_calib=get_env_specific_setting(local=1, azure=min(4, max_cpu)),
        pin_memory=get_env_specific_setting(local=False, azure=True),
        prefetch_factor=1,
        shuffle=True,
        common_transforms=[],
        # TODO: check if we can safely enable this when occupancy is included  # noqa: TD003
        train_transforms=[],  # [GlobalAffineTransformationConfig(bev_layout=FAR_RANGE_BEV_LAYOUT)],
        # NOTE: ignore a few broken dataset samples for each modality in case they exist to prevent crashing training
        ignore_loading_errors_threshold_num=run_mode_specific_setting_fn(short=0, long=5),
    )


def get_tasks_configs(
    run_mode_specific_setting_fn: Callable[..., Any],
    pipeline_step_specific_setting_fn: Callable[..., Any],
    pipeline_step_folders: PipelineStepPaths,
    common_args: SharedSubConfigArgs,
) -> TaskConfigs:
    """Get the tasks configurations for the multitask-multimodal-model."""

    evaluation_folder = pipeline_step_specific_setting_fn(
        default=pipeline_step_folders.evaluation_torch,
        convert=pipeline_step_folders.evaluation_qnn,
        evaluate_qnn=pipeline_step_folders.evaluation_qnn,
    )

    occupancy_task = OccupancyTaskConfig(
        data_weight=1.0,
        loss_weight=0.5,
        camera_backbone_type=CameraBackboneType.BEV,
        enabled_input_modalities=common_args.enabled_input_modalities,
        bev_layout=common_args.bev_layout,
        occupancy_grid_layout=MID_RANGE_OCCUPANCY_GRID_LAYOUT,
        num_predicted_classes=len(OCCUPANCY_LABEL_DEFINITION.classes),
        ignore_class_id=OCCUPANCY_LABEL_DEFINITION.ignore_id,
        loss_weights={
            OccupancyLossType.HEIGHT: 1.0,
            OccupancyLossType.SEMANTIC: 1.0,
            OccupancyLossType.VISIBILITY: 1.0,
        },
        class_weights=OCCUPANCY_LABEL_DEFINITION.class_weights_mid_range,
        use_uncertainty_weighting=False,
        summary_output_path=pipeline_step_folders.summaries,
        label_set_name=OCCUPANCY_LABEL_DEFINITION.label_set.name,
        label_set_version=OCCUPANCY_LABEL_DEFINITION.label_set.version,
        ignore_class=OCCUPANCY_LABEL_DEFINITION.ignore_class,
        semantic_mapping_required=False,
        save_ground_truth_for_eval=False,
        delete_predictions_after_eval=True,
        evaluation_folder=evaluation_folder,
        evil_configs={
            TorchStage.VALIDATE: get_evil_config_occupancy(online_eval=True)[0],
            TorchStage.PREDICT: get_evil_config_occupancy(online_eval=False)[0],
        },
        eval_run_environment=common_args.eval_run_environment,
        evaluation_torch_folder=pipeline_step_folders.evaluation_torch,
        visualization_folder=pipeline_step_folders.visualization,
        combined_metrics_config=Path(get_evil_config_occupancy(online_eval=True)[1]),
        bev_features_id=InputDataId.CAMERA_BEV_MID_RANGE,
    )

    sparse_detection_task = SparseDetectionTaskConfig(
        head_config=_get_head_config(stride=32, bev_layout=FAR_RANGE_BEV_LAYOUT),
        loss_config=_get_loss_config(stride=32),
        image_writer_callback_config=ImageWriterCallbackParams(
            output_path=pipeline_step_folders.output_artifacts.as_posix(),
            logging_frequency=run_mode_specific_setting_fn(long=20_000, short=50),
            num_training_frames=run_mode_specific_setting_fn(long=8, short=1),
            num_validation_frames=run_mode_specific_setting_fn(long=20, short=1),
            score_threshold=0.35,
            camera_setup=(2, 2),
        ),
        label_set_name=DEFAULT_LABEL_DEFINITION.label_set.name,
        label_set_version=DEFAULT_LABEL_DEFINITION.label_set.version,
        label_set_background_class=BACKGROUND_CLASS,
        data_weight=1.0,
        loss_weight=1.0,
        camera_backbone_type=CameraBackboneType.IMAGE_SPACE,
        evaluation_folder=evaluation_folder,
        evil_configs={
            Stage.VALIDATE: get_evil_config("alliance", online_eval=True)[0],
            Stage.PREDICT: get_evil_config("alliance", online_eval=False)[0],
        },
        eval_run_environment=common_args.eval_run_environment,
        combined_metrics_yaml=(
            Path(__file__).parents[3]
            / "models"
            / "box3d_common"
            / "combined_metrics"
            / pipeline_step_specific_setting_fn(default="offline", train="online")
            / "tasks"
            / get_evil_config("alliance", online_eval=pipeline_step_specific_setting_fn(default=False, train=True))[2]
        ),
        eval_objectness_threshold=0.00005,
        store_gt_in_airbox=True,
        gt_key=BOX_3D_RELEASE_TASK_ID,
        max_num_detections=100,
        metric_configs=EvaluationConfig(
            nuscenes_detection_kpis=NuscenesKPIsConfig(
                nuscenes_map_detection_ranges={},
                class_id_to_nuscenes_label={0: "barrier", 1: "car", 2: "truck", 3: "bicycle", 4: "pedestrian"},
            ),
            tracking=TrackingConfig(min_threshold=0.3),
        ),
        bev_layout=FAR_RANGE_BEV_LAYOUT,
    )

    task_config = TaskConfigs(
        sparse_detection=sparse_detection_task,
        occupancy=occupancy_task,
        occupancy_near_range=replace(
            occupancy_task,
            bev_layout=NEAR_RANGE_BEV_LAYOUT,
            bev_features_id=InputDataId.CAMERA_BEV_NEAR_RANGE,
            occupancy_grid_layout=NEAR_RANGE_OCCUPANCY_GRID_LAYOUT,
            class_weights=OCCUPANCY_LABEL_DEFINITION.class_weights_near_range,
        ),
    )
    return task_config


def _get_head_config(stride: int, bev_layout: BevLayout) -> SparseDetectionHeadParams:
    # we set detection range with values from bev_layout because this is used for gt filtering
    detection_range = DetectionRange(
        x_min=bev_layout.x_min,
        y_min=bev_layout.y_min,
        z_min=-5.0,
        x_max=bev_layout.x_max,
        y_max=bev_layout.y_max,
        z_max=5.0,
    )

    topk_ratio = 0.15  # focal head topk image feature selection

    backbone_output_channels = 64
    embed_dim = 256
    num_classes = 4

    return SparseDetectionHeadParams(
        fusion_head_params=StreamPETRHeadParams(
            camera_in_channels=backbone_output_channels,
            radar_in_channels=256,  # number of channels is manipulated in the DataAdapter in SparseDetectionHead
            lidar_in_channels=256,  # number of channels is manipulated in the DataAdapter in SparseDetectionHead
            embed_dim=embed_dim,
            num_queries=600,
            num_tracks=128,
            use_tracking_loss=False,
            temporal_mode=False,
            batch_size=1,  # doesn't matter because head automatically adapts to different input
            radar_fm_shape=(40, 24),
            lidar_fm_shape=(40, 24),
            num_classes=num_classes,
            num_box_coords=10,
            memory_num_frames=1,
            memory_num_tokens=0,  # set this to 0 to deactivate temporal propagation
            decoder_params=PETRTransformerDecoderParams(
                num_layers=6, layer_params=PETRTemporalDecoderLayerParams(embed_dim=embed_dim)
            ),
            denoising_params=DenoisingParams(
                enabled=True,
                num_noise_groups=10,
                dn_noise_scale=1.0,
                dn_noise_trans=0.0,
                dn_large_distortion_threshold=0.75,
            ),
            position_range=detection_range,  # range for positional encoding
            detection_range=detection_range,  # range for scaling the predictions
        ),
        camera_head_params=FocalHeadParams(
            in_channels=backbone_output_channels,
            embed_dim=embed_dim,
            num_classes=num_classes,
            train_ratio=topk_ratio,
            infer_ratio=topk_ratio,
        ),
        num_camera_heads=2,
        camera_fm_shapes=[[4, 48, 48], [1, 40, 72]],
        stride=stride,
        detection_range=detection_range,
        num_classes=num_classes,
        max_detections=300,
        score_threshold=0.05,
    )


def _get_loss_config(stride: int) -> LossConfig:
    return LossConfig(
        fusion_head_loss=StreamPETRLossConfig(
            class_loss_weight=2,
            bbox_loss_weights=(0.5, 0.5, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25),
            dn_loss_weight=1,
            dn_class_average_factor=0.22,
        ),
        camera_head_loss=FocalHeadLossConfig(
            enabled=True,
            stride=stride,
            class_loss_weight=2,
            bbox_l1_loss_weight=5,
            bbox_giou_loss_weight=2,
            center_loss_weight=10,
            bbox_l1_cost_weight=0,
            center_cost_weight=0,
        ),
        use_uncertainty_weighting=False,
        num_camera_losses=2,
    )
