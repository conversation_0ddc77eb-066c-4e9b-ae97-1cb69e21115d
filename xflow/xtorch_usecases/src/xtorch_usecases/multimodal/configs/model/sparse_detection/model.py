"""Sparse detection model configurations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import multiprocessing as mp
import os
from collections.abc import Callable
from functools import partial
from pathlib import Path
from typing import Any

import torch
from qnn_custom_ops import QnnBackend

from conversion.qnn.definitions import InputTensorConfig, InputTensorLayout, InputTensorType, QuantCheckerConfig
from conversion.qnn.inference import QnnProfilingLevel
from evil.config.run import RunEnvironment
from xcontract.data.definitions.inputs import InputDataId
from xcontract.training.run_mode import RunMode
from xcontract.usecases.multiview.multiview_split import MultiviewSplit
from xtorch.nn.bev_transforms.efficient_bev import DepthScalingDef
from xtorch.nn.heads.object_detection.box3d import DetectionRange
from xtorch.training import Stage
from xtorch.training.trainer_config import TrainerConfig
from xtorch_extensions.datasets.csv_dataset import Usage
from xtorch_extensions.quantization.qat_config import QuantizationConfig
from xtorch_usecases.common.datasets.combined.data_module import BOX_3D_RELEASE_TASK_ID
from xtorch_usecases.common.datasets.combined.definitions import BACKGROUND_CLASS, DEFAULT_LABEL_DEFINITION
from xtorch_usecases.common.datasets.common.add_remove_batch_dimension import (
    AddBatchDimensionConfig,
    RemoveBatchDimensionConfig,
)
from xtorch_usecases.common.datasets.common.augmenter import GlobalAffineTransformationConfig
from xtorch_usecases.common.datasets.common.bev_box3d_loader import LabelDefinition
from xtorch_usecases.common.datasets.common.transformation_config import TransformationConfig
from xtorch_usecases.common.helpers import (
    get_env_specific_setting,
    get_eval_compute_type,
    get_pipeline_step_specific_setting,
    get_run_mode_specific_setting,
)
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.common.data.qnn_input_converter import MultiModalQnnInputConverterConfig
from xtorch_usecases.multimodal.common.data.vision_multiview_transforms import AdaptVideoSampleForConversionConfig
from xtorch_usecases.multimodal.components.definitions import CameraBackboneType
from xtorch_usecases.multimodal.components.lidar.lidar_encoder import LidarEncoderParams, LidarVoxelizerType
from xtorch_usecases.multimodal.components.multimodal_encoder import MultimodalEncoderNames
from xtorch_usecases.multimodal.components.radar.definitions import RadarEncoderParams, RadarModelType
from xtorch_usecases.multimodal.configs.callback import CallbackConfig
from xtorch_usecases.multimodal.configs.camera_backbone import HALVED_CAMERA_BACKBONE_CONFIG
from xtorch_usecases.multimodal.configs.convert import MultiModalConvertConfig
from xtorch_usecases.multimodal.configs.evaluate import MultiModalEvaluateConfig, get_evil_config
from xtorch_usecases.multimodal.configs.geometry import FAR_RANGE_BEV_LAYOUT, BevLayout
from xtorch_usecases.multimodal.configs.qat import MultiModalQatConfig
from xtorch_usecases.multimodal.configs.run import CombinedDataModuleConfig, TaskConfigs
from xtorch_usecases.multimodal.configs.train import MultiModalTrainingConfig
from xtorch_usecases.multimodal.tasks.sparse_detection.definitions import (
    EvaluationConfig,
    FocalHeadLossConfig,
    ImageWriterCallbackParams,
    LossConfig,
    NuscenesKPIsConfig,
    StreamPETRLossConfig,
    TrackingConfig,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.sparse_detection_parameters import (
    DenoisingParams,
    FocalHeadParams,
    PETRTemporalDecoderLayerParams,
    PETRTransformerDecoderParams,
    SparseDetectionHeadParams,
    StreamPETRHeadParams,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.task import SparseDetectionTaskConfig


def compute_total_steps(num_total_frames: int, batch_size: int) -> int:
    """Computes number of total steps given num_total_frames and batch_size when dropping overhanging frames."""
    return num_total_frames // (batch_size * max(torch.cuda.device_count(), 1))


USE_CASES = ["alliance"]

NUM_EPOCHS_LONG = 9
NUM_EPOCHS_SHORT = 2
NUM_FRAMES_PER_EPOCH = 612_392  # train split size of dynamic_objects_bev_multimodal_0_25Hz:15

BATCH_SIZE_TRAIN_LOCAL = 2
BATCH_SIZE_TRAIN_AZURE = 8
LEARNING_RATE = 1e-3

BATCH_SIZE_EVAL_LOCAL = 1
BATCH_SIZE_EVAL_QNN = 8
BATCH_SIZE_EVAL_AZURE = 8
BATCH_SIZE_EVAL_SHORT = 2

TRAIN_STEPS_PER_EPOCH_LONG = compute_total_steps(
    num_total_frames=NUM_FRAMES_PER_EPOCH,
    batch_size=get_env_specific_setting(local=BATCH_SIZE_TRAIN_LOCAL, azure=BATCH_SIZE_TRAIN_AZURE),
)
TRAIN_STEPS_PER_EPOCH_SHORT = 10

FRAMES_PER_EVALUATION_LONG = 800
STEPS_PER_EVALUATION_LONG = compute_total_steps(
    num_total_frames=FRAMES_PER_EVALUATION_LONG,
    batch_size=get_env_specific_setting(
        local=BATCH_SIZE_EVAL_LOCAL, azure=BATCH_SIZE_EVAL_AZURE, dt_flow=BATCH_SIZE_EVAL_QNN
    ),
)
STEPS_PER_EVALUATION_SHORT = 10

LOG_EVAL_IMAGE_EVERY_N_SAMPLES_LONG = 100
LOG_EVAL_IMAGE_EVERY_N_SAMPLES_SHORT = STEPS_PER_EVALUATION_SHORT // 2  # log 2 images

MAX_NUM_CPUS = os.cpu_count() or 4

# TV + FC1 + Radar usecase:
ENABLED_INPUT_MODALITIES = (
    InputDataId.CAMERA_TV,  # TV camera (rgbs_intrinsics_extrinsics_deformed_cylinder)
    InputDataId.CAMERA_FC1,  # FC1 camera (rgbs_intrinsics_extrinsics_cylinder)
    InputDataId.RADAR,  # combined radars (radar_pointcloud)
    InputDataId.REF_LIDAR,  # ref lidar (ref_lidar_pointcloud)
)
ENABLED_MODALITY_ENCODERS = (
    InputDataId.CAMERA_DEF_CYLINDER,
    InputDataId.CAMERA_CYLINDER,
    InputDataId.RADAR,
    InputDataId.REF_LIDAR,
)
NUM_CAMERAS = {
    InputDataId.CAMERA_CYLINDER: 1,  # FC1
    InputDataId.CAMERA_DEF_CYLINDER: 4,  # TV
}
CAMERA_FEAT_MAP_STRIDE = 32

# create the parameters for reference lidar with the bev_layout
NUM_OUTPUT_FEATURE_REF_LIDAR = 64
ENCODER_PARAMS_REF_LIDAR: LidarEncoderParams = LidarEncoderParams(
    voxelizer_type=LidarVoxelizerType.PixorVoxelizer,
    bev_layout=FAR_RANGE_BEV_LAYOUT,
    num_output_features=NUM_OUTPUT_FEATURE_REF_LIDAR,
)

USECASE_BASEDIR = Path(__file__).parents[3]
XFLOW_BASEDIR = Path(__file__).parents[7]
XUSECASES_DATADIR = XFLOW_BASEDIR / "xusecases/tests/test_data"
ALLIANCE_DATADIR_LOCAL = XUSECASES_DATADIR / "vision/datasets/multiview/alliance_dataset"

LOGGING_FREQUENCY = 250

QNN_PROFILING_LEVEL = QnnProfilingLevel.NO_PROFILING

# metric evaluation config for non-evil usage
METRIC_CONFIGS = EvaluationConfig(
    nuscenes_detection_kpis=NuscenesKPIsConfig(
        nuscenes_map_detection_ranges={},
        class_id_to_nuscenes_label={0: "barrier", 1: "car", 2: "truck", 3: "bicycle", 4: "pedestrian"},
    ),
    # corresponding label_set used for alliance is
    # ['Label', 'PassengerCar', 'LargeVehicle', 'RidableVehicle', 'Pedestrian']
    tracking=TrackingConfig(min_threshold=0.3),
)


def get_sub_configs(
    run_mode: RunMode,
    pipeline_step: PipelineStep,
    dataset_name: str,
    qnn_backend: QnnBackend | None,
    enabled_input_modalities: tuple[InputDataId, ...],
    node_count: int = 1,
) -> tuple[
    CombinedDataModuleConfig,
    MultiModalTrainingConfig,
    MultiModalEvaluateConfig,
    CallbackConfig,
    TrainerConfig,
    TaskConfigs,
    MultiModalConvertConfig,
    MultiModalQatConfig,
]:
    """Returns all sub configs to assemble the multi modal run config."""
    # specific setting functions
    run_mode_specific_setting_fn: Callable[..., Any] = partial(get_run_mode_specific_setting, run_mode=run_mode)
    pipeline_step_specific_setting_fn: Callable[..., Any] = partial(
        get_pipeline_step_specific_setting, pipeline_step=pipeline_step
    )

    # train / eval parameters
    batch_size_eval = run_mode_specific_setting_fn(
        long=get_env_specific_setting(
            local=BATCH_SIZE_EVAL_LOCAL, azure=BATCH_SIZE_EVAL_AZURE, dt_flow=BATCH_SIZE_EVAL_QNN
        ),
        short=BATCH_SIZE_EVAL_SHORT,
    )

    # training module config
    num_epochs = pipeline_step_specific_setting_fn(
        default=run_mode_specific_setting_fn(long=NUM_EPOCHS_LONG, short=NUM_EPOCHS_SHORT),
        qat=1,
    )
    train_steps_per_epoch = run_mode_specific_setting_fn(
        short=TRAIN_STEPS_PER_EPOCH_SHORT,
        long=pipeline_step_specific_setting_fn(
            default=TRAIN_STEPS_PER_EPOCH_LONG // node_count,
            # half the epoch length for QAT since it is about 2x slower
            qat=TRAIN_STEPS_PER_EPOCH_LONG // (2 * node_count),
        ),
    )
    freeze_encoder_weights_patterns = None
    # freeze_encoder_weights_patterns = [
    #     # freeze image encoder weights (loaded from checkpoint) yolo+BiFPN
    #     r".*CameraCylinderEncoder._camera_encoder._camera_backbone._image_encoder._yolov4tiny.*",
    #     r".*CameraCylinderEncoder._camera_encoder._camera_backbone._bifpn.*",
    #     r".*CameraDeformedCylinderEncoder._camera_encoder._camera_backbone._image_encoder._yolov4tiny.*",
    #     r".*CameraDeformedCylinderEncoder._camera_encoder._camera_backbone._bifpn.*",
    # ]

    depth_configs = {
        # NOTE: properly configure depth scaling values (possibly by usescase)
        InputDataId.CAMERA_PINHOLE: DepthScalingDef(min_m=1.0, max_m=112.0, spacing_m=2.0),
        InputDataId.CAMERA_DEF_CYLINDER: DepthScalingDef(min_m=1.0, max_m=112.0, spacing_m=2.0),
        InputDataId.CAMERA_CYLINDER: DepthScalingDef(min_m=1.0, max_m=112.0, spacing_m=2.0),
    }
    training_config = MultiModalTrainingConfig(
        label_class_names=DEFAULT_LABEL_DEFINITION.classes,
        background_id=DEFAULT_LABEL_DEFINITION.background_id,
        lr=pipeline_step_specific_setting_fn(default=LEARNING_RATE, qat=1e-5),
        weight_decay=1e-2,
        num_epochs=num_epochs,
        train_steps_per_epoch=train_steps_per_epoch,
        warmup_steps=int(0.15 * train_steps_per_epoch),
        cooldown_steps=1,  # NOTE: ignored now with cosine schedule (just kept for compatibility with other usecases)
        output_folder="",
        bev_layout=FAR_RANGE_BEV_LAYOUT,
        depth_configs=depth_configs,
        enabled_input_modalities=ENABLED_MODALITY_ENCODERS,
        log_eval_image_every_n_sample=run_mode_specific_setting_fn(
            short=LOG_EVAL_IMAGE_EVERY_N_SAMPLES_SHORT, long=LOG_EVAL_IMAGE_EVERY_N_SAMPLES_LONG
        ),
        camera_encoder_split_config=MultiviewSplit.CAM,
        radar_encoder_params=RadarEncoderParams(
            bev_layout=FAR_RANGE_BEV_LAYOUT,
            model_type=RadarModelType.DRO_RELEASE,
            use_voxelizer=pipeline_step_specific_setting_fn(
                default=True, qat=False, convert=False, evaluate_qnn=False, profile_qnn=False
            ),
        ),
        encoder_params_ref_lidar=LidarEncoderParams(
            voxelizer_type=LidarVoxelizerType.PixorVoxelizer, bev_layout=FAR_RANGE_BEV_LAYOUT, num_output_features=64
        ),
        strict_loading=False,
        freeze_encoder_weights_patterns=freeze_encoder_weights_patterns,
        num_cams=NUM_CAMERAS,
        camera_backbone_configs={
            InputDataId.CAMERA_DEF_CYLINDER: HALVED_CAMERA_BACKBONE_CONFIG,  # TV
        },
        skip_encoder_weight_init=[MultimodalEncoderNames.CAMERA_DEF_CYLINDER_ENCODER],
        tolerated_nan_gradients_per_epoch=10,
        enable_temporal_training=False,
    )

    # callback config
    logging_frequency = 250
    callback_config = CallbackConfig(
        output_root_dir=Path(),
        image_logging_frequency=logging_frequency,
    )

    # trainer config
    steps_per_predict = run_mode_specific_setting_fn(long=None, short=2)
    trainer_config = TrainerConfig(
        accelerator=pipeline_step_specific_setting_fn(default="cuda", evaluate_qnn="cpu"),
        max_epochs=num_epochs,
        train_epoch_length=train_steps_per_epoch,
        limit_val_batches=run_mode_specific_setting_fn(
            short=STEPS_PER_EVALUATION_SHORT, long=STEPS_PER_EVALUATION_LONG
        ),
        limit_test_batches=0,  # not used
        limit_predict_batches=steps_per_predict,
        gradient_clip_val=1.0,
        accumulate_grad_batches=1,
        log_every_n_steps=logging_frequency,
        num_sanity_val_steps=1,
        check_val_every_n_epoch=1,
        enable_checkpointing=True,
        default_root_dir=Path(),
        enable_progress_bar=True,
        precision="bf16-mixed",
        deterministic=False,
        benchmark=True,
        use_distributed_sampler=False,  # distributed sampler is already created by data loader
        devices=get_env_specific_setting(local=1, azure="auto"),
        num_nodes=node_count,
    )

    # task config
    sparse_detection_task_config = _get_task_config(
        dataset_name=dataset_name,
        batch_size_eval=batch_size_eval,
        pipeline_step=pipeline_step,
        bev_layout=FAR_RANGE_BEV_LAYOUT,
        label_definition=DEFAULT_LABEL_DEFINITION,
        qnn_backend=qnn_backend,
        run_mode=run_mode,
    )

    task_config = TaskConfigs(sparse_detection=sparse_detection_task_config)

    # data config
    # -> TV + FC1 + Radar usecase:
    data_config = _get_data_config(
        label_definition=DEFAULT_LABEL_DEFINITION,
        enabled_input_modalities=ENABLED_INPUT_MODALITIES,
        bev_layout=FAR_RANGE_BEV_LAYOUT,
        batch_size_val=batch_size_eval,
        dataset_name=dataset_name,
        pipeline_step=pipeline_step,
        run_mode_specific_setting_fn=run_mode_specific_setting_fn,
        training_config=training_config,
        task_config=task_config,
    )

    computed_shapes = [
        [
            4,
            *[
                s // CAMERA_FEAT_MAP_STRIDE
                for s in data_config.training_input_shapes[InputDataId.CAMERA_DEF_CYLINDER][-2:]
            ],
        ],
        [
            1,
            *[s // CAMERA_FEAT_MAP_STRIDE for s in data_config.training_input_shapes[InputDataId.CAMERA_CYLINDER][-2:]],
        ],
    ]
    assert sparse_detection_task_config.head_config.camera_fm_shapes == computed_shapes, (
        f"The camera shapes of the head {sparse_detection_task_config.head_config.camera_fm_shapes} and the ones from "
        f"the data loader {computed_shapes} do not match"
    )

    # eval config
    eval_config = MultiModalEvaluateConfig(
        checkpoint=None,
        eval_torch_model=pipeline_step_specific_setting_fn(
            default=False, visualization_torch=True, evaluate_torch=True
        ),
        qnn_backend=qnn_backend,
        target_address=None,
        eval_full_model=False,
        multi_images_per_epoch=False,
        profiling_level=QnnProfilingLevel.NO_PROFILING,
        trigger_dt_flow_qnn_embedded_evaluation=run_mode == RunMode.LONG,
        run_environment=sparse_detection_task_config.eval_run_environment,
        # if true, the data sample is adapted in the qnn engine during embedded evaluation, otherwise it is not adapted
        # (and expected to be already in the correct format / converted during data loading)
        adapt_data_sample=get_env_specific_setting(local=True, azure=True, dt_flow=False),
    )

    # convert config
    quant_check_config = QuantCheckerConfig(
        enable_activation_histograms=run_mode_specific_setting_fn(
            short=False,
            long=True,
        ),
        enable_quant_error_plots=run_mode_specific_setting_fn(
            short=False,
            long=True,
        ),
        enable_weight_plots=run_mode_specific_setting_fn(
            short=False,
            long=True,
        ),
        short_run=run_mode_specific_setting_fn(
            short=True,
            long=False,
        ),
    )
    inputs_config = []
    # qnn conversion config for all input tensors
    if InputDataId.CAMERA_TV in enabled_input_modalities and InputDataId.CAMERA_FC1 in enabled_input_modalities:
        inputs_config.extend(
            [
                InputTensorConfig(
                    name="input_0",
                    input_type=InputTensorType.IMAGE,
                ),
                InputTensorConfig(
                    name="input_1",
                    input_type=InputTensorType.IMAGE,
                ),
                InputTensorConfig(
                    name="input_2",
                    input_type=InputTensorType.IMAGE,
                ),
                InputTensorConfig(
                    name="input_3",
                    input_type=InputTensorType.IMAGE,
                ),
                InputTensorConfig(
                    name="input_4",
                    input_type=InputTensorType.IMAGE,
                ),
                InputTensorConfig(
                    name="input_coords_3d_normalized_tv",
                    input_type=InputTensorType.IMAGE,
                    input_layout=InputTensorLayout.NHWC,
                ),
                InputTensorConfig(
                    name="input_cone_fc1",
                    input_type=InputTensorType.IMAGE,
                    input_layout=InputTensorLayout.NHWC,
                ),
                InputTensorConfig(
                    name="input_coords_3d_normalized_fc1",
                    input_type=InputTensorType.IMAGE,
                    input_layout=InputTensorLayout.NHWC,
                ),
                InputTensorConfig(
                    name="input_cone_tv",
                    input_type=InputTensorType.IMAGE,
                    input_layout=InputTensorLayout.NHWC,
                ),
            ]
        )

    if InputDataId.RADAR in enabled_input_modalities:
        # Add radar input tensor configs based on the radar encoder model type.
        if training_config.radar_encoder_params.model_type == RadarModelType.NV_RADAR_NET:
            inputs_config.append(
                InputTensorConfig(
                    name="radar_features_lut",
                    input_type=InputTensorType.DEFAULT,
                    input_layout=InputTensorLayout.NHWC,
                )
            )
        elif training_config.radar_encoder_params.model_type.is_dro:
            inputs_config.append(
                InputTensorConfig(
                    name="radar_voxel_feat_enc",
                    input_type=InputTensorType.DEFAULT,
                    input_layout=InputTensorLayout.NHWC,
                )
            )
            inputs_config.append(
                InputTensorConfig(
                    name="radar_voxel_coords",
                    input_type=InputTensorType.DEFAULT,
                    input_layout=InputTensorLayout.NFC,
                )
            )
        else:
            msg = f"Unsupported radar model type {training_config.radar_encoder_params.model_type} for QNN conversion."
            raise ValueError(msg)

    # 300 necessary till a separate calibration dataset is created
    num_calib_samples = run_mode_specific_setting_fn(long=300, short=3)
    convert_config = MultiModalConvertConfig(
        default_root_dir=Path(),
        qnn_backends=[QnnBackend.CPU, QnnBackend.HTP],
        num_calib_samples=num_calib_samples,
        num_visualization_samples=run_mode_specific_setting_fn(short=1, long=10),
        skip_qnn_conversion=False,
        quant_check_config=quant_check_config,
        inputs_config=inputs_config,
    )

    # qat config
    qat_config = MultiModalQatConfig(
        default_root_dir=Path(),
        quantization_config=QuantizationConfig(
            num_calibration_samples=num_calib_samples,
            config_file="htp_quantsim_config_v73.json",
        ),
    )

    assert task_config.sparse_detection is not None
    assert (
        training_config.enable_temporal_training
        == task_config.sparse_detection.head_config.fusion_head_params.temporal_mode
    )

    return (
        data_config,
        training_config,
        eval_config,
        callback_config,
        trainer_config,
        task_config,
        convert_config,
        qat_config,
    )


def _get_data_config(
    *,
    batch_size_val: int,
    dataset_name: str,
    label_definition: LabelDefinition,
    enabled_input_modalities: tuple[InputDataId, ...],
    pipeline_step: PipelineStep,
    bev_layout: BevLayout,
    run_mode_specific_setting_fn: Callable[..., Any],
    training_config: MultiModalTrainingConfig,
    task_config: TaskConfigs,
) -> CombinedDataModuleConfig:
    pipeline_step_specific_setting_fn: Callable[..., Any] = partial(
        get_pipeline_step_specific_setting, pipeline_step=pipeline_step
    )

    alliance_data_basepath = get_env_specific_setting(
        local=ALLIANCE_DATADIR_LOCAL.as_posix(),
        azure=lambda: f"{os.environ.get('AZUREML_DATAREFERENCE_ingest_cache')}",
    )
    alliance_dataset_basepath = get_env_specific_setting(local=alliance_data_basepath, azure=None)

    common_transforms: list[TransformationConfig] = get_env_specific_setting(
        local=[],
        azure=[],
        dt_flow=[
            AddBatchDimensionConfig(),
            AdaptVideoSampleForConversionConfig(
                bev_layout=bev_layout,
                depth_scaling_def=training_config.depth_configs,
                radar_model=training_config.radar_encoder_params.model_type,
                ground_truth_key=BOX_3D_RELEASE_TASK_ID,
            ),
            MultiModalQnnInputConverterConfig(
                data_config_enabled_input_modalities=enabled_input_modalities,
                training_config=training_config,
                task_configs=task_config,
                ground_truth_key=BOX_3D_RELEASE_TASK_ID,
            ),
            RemoveBatchDimensionConfig(),
        ],
    )
    train_transforms: list[TransformationConfig] = [GlobalAffineTransformationConfig(bev_layout=bev_layout)]

    # Important: keep this same everywhere. E.g.
    # /evil/src/evil/config/configs/prototypes/vision/multiview/offline_multimodal.yml
    # Otherwise Evil will not work properly
    alliance_dataset_workspace = "viper_birds_eye_view"
    alliance_dataset_names = ["dynamic_objects_bev_multimodal_0_25Hz:18"]
    if pipeline_step.is_evaluate or pipeline_step.is_visualization:
        alliance_dataset_names = [
            "dynamic_objects_bev_multimodal_0_25Hz:18",
            "dynamic_objects_bev_multimodal_manual_labels:6",
        ]

    loomy_dataindex_path = get_env_specific_setting(
        local="xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy",
        azure="https://vdeephierarchicalprod.blob.core.windows.net/loomy-catalogs/testing/ada",
    )
    loomy_dataset_name = get_env_specific_setting(local="ada", azure="multitask_dyo_occ")
    loomy_dataset_version: dict[Usage, int | None] = {
        Usage.TRAINING: get_env_specific_setting(local=1, azure=2),
        Usage.VALIDATION: get_env_specific_setting(local=1, azure=1),
        Usage.TEST: get_env_specific_setting(local=1, azure=1),
    }

    return CombinedDataModuleConfig(
        environment=get_env_specific_setting(local="local", azure="azure"),
        batch_size_train=pipeline_step_specific_setting_fn(
            default=get_env_specific_setting(local=BATCH_SIZE_TRAIN_LOCAL, azure=BATCH_SIZE_TRAIN_AZURE),
            qat=get_env_specific_setting(local=1, azure=8),
        ),
        batch_size_val=batch_size_val,
        dataset_name=dataset_name,
        enabled_input_modalities=enabled_input_modalities,
        enabled_tasks=(BOX_3D_RELEASE_TASK_ID,),
        training_input_shapes={
            InputDataId.CAMERA_CYLINDER: [1280, 2304],
            InputDataId.CAMERA_PINHOLE: [1024, 1920],
            InputDataId.CAMERA_DEF_CYLINDER: [1536, 1536],
        },
        bev_layouts={dataset_name: bev_layout},
        alliance_data_basepath=alliance_data_basepath,
        alliance_dataset_basepath=alliance_dataset_basepath,
        alliance_dataset_names=alliance_dataset_names,
        alliance_dataset_workspace=alliance_dataset_workspace,
        loomy_dataindex_path=loomy_dataindex_path,
        loomy_dataset_name=loomy_dataset_name,
        loomy_dataset_version=loomy_dataset_version,
        shuffle=True,
        num_workers_train=get_env_specific_setting(local=2, azure=min(24, MAX_NUM_CPUS)),
        num_workers_val=get_env_specific_setting(local=1, azure=min(12, MAX_NUM_CPUS), dt_flow=8),
        num_workers_calib=get_env_specific_setting(local=1, azure=min(4, MAX_NUM_CPUS), dt_flow=4),
        box3d_label_set_name=label_definition.label_set.name,
        box3d_label_set_version=label_definition.label_set.version,
        pin_memory=get_env_specific_setting(local=False, azure=True),
        prefetch_factor=1,
        common_transforms=common_transforms,
        train_transforms=train_transforms,
        # NOTE: ignore a few broken dataset samples for each modality in case they exist to prevent crashing training
        ignore_loading_errors_threshold_num=run_mode_specific_setting_fn(short=0, long=5),
        # If we're using loomy, use the dataloader as a simple wrapper
        single_worker_wrapper=(dataset_name == "loomy"),
    )


def _get_head_config(
    stride: int,
    bev_layout: BevLayout,
    num_classes: int = 4,
    camera_fm_shapes: tuple[tuple[int, int, int], ...] = ((4, 48, 48), (1, 40, 72)),
    radar_fm_shape: tuple[int, int] = (40, 24),
    lidar_fm_shape: tuple[int, int] = (40, 24),
    *,
    enabled_focal_heads: bool = True,
) -> SparseDetectionHeadParams:
    # we set detection range with values from bev_layout because this is used for gt filtering
    detection_range = DetectionRange(
        x_min=bev_layout.x_min,
        y_min=bev_layout.y_min,
        z_min=-5.0,
        x_max=bev_layout.x_max,
        y_max=bev_layout.y_max,
        z_max=5.0,
    )

    topk_ratio = 0.112 if enabled_focal_heads else 1.0  # focal head topk image feature selection

    backbone_output_channels = 64
    embed_dim = 256

    return SparseDetectionHeadParams(
        fusion_head_params=StreamPETRHeadParams(
            camera_in_channels=backbone_output_channels,
            radar_in_channels=256,  # number of channels is manipulated in the DataAdapter in SparseDetectionHead
            lidar_in_channels=256,
            embed_dim=embed_dim,
            num_queries=576,
            num_tracks=128,
            use_tracking_loss=False,
            temporal_mode=False,  # set to False to deactivate memory and tracking
            batch_size=1,  # doesn't matter because head automatically adapts to different input
            radar_fm_shape=radar_fm_shape,
            lidar_fm_shape=lidar_fm_shape,
            num_classes=num_classes,
            num_box_coords=10,
            memory_num_frames=4,
            memory_num_tokens=256,
            decoder_params=PETRTransformerDecoderParams(
                num_layers=6, layer_params=PETRTemporalDecoderLayerParams(embed_dim=embed_dim)
            ),
            denoising_params=DenoisingParams(
                enabled=True,
                num_noise_groups=10,
                dn_noise_scale=1.0,
                dn_noise_trans=0.0,
                dn_large_distortion_threshold=0.75,
            ),
            position_range=detection_range,  # range for positional encoding
            detection_range=detection_range,  # range for scaling the predictions
        ),
        camera_head_params=FocalHeadParams(
            in_channels=backbone_output_channels,
            embed_dim=embed_dim,
            num_classes=num_classes,
            train_ratio=topk_ratio,
            infer_ratio=topk_ratio,
        ),
        num_camera_heads=len(camera_fm_shapes) if enabled_focal_heads else 0,
        camera_fm_shapes=[list(shape) for shape in camera_fm_shapes],
        stride=stride,
        detection_range=detection_range,
        num_classes=num_classes,
        max_detections=320,
        score_threshold=0.05,
    )


def _get_loss_config(
    stride: int,
    *,
    enabled_focal_heads: bool = True,
) -> LossConfig:
    return LossConfig(
        fusion_head_loss=StreamPETRLossConfig(
            class_loss_weight=2,
            bbox_loss_weights=(0.5, 0.5, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25, 0.25),
            dn_loss_weight=1,
            dn_class_average_factor=0.22,
        ),
        camera_head_loss=FocalHeadLossConfig(
            enabled=enabled_focal_heads,
            stride=stride,
            class_loss_weight=2,
            bbox_l1_loss_weight=5,
            bbox_giou_loss_weight=2,
            center_loss_weight=10,
            bbox_l1_cost_weight=0,
            center_cost_weight=0,
        ),
        use_uncertainty_weighting=False,
        num_camera_losses=2 if enabled_focal_heads else 0,
    )


def _get_task_config(
    *,
    dataset_name: str,
    batch_size_eval: int,
    bev_layout: BevLayout,
    label_definition: LabelDefinition,
    pipeline_step: PipelineStep,
    qnn_backend: QnnBackend | None,
    run_mode: RunMode,
) -> SparseDetectionTaskConfig:
    run_mode_specific_setting_fn: Callable[..., Any] = partial(get_run_mode_specific_setting, run_mode=run_mode)
    pipeline_step_specific_setting_fn: Callable[..., Any] = partial(
        get_pipeline_step_specific_setting, pipeline_step=pipeline_step
    )

    image_writer_callback_params = ImageWriterCallbackParams(
        output_path="",
        logging_frequency=run_mode_specific_setting_fn(long=20_000, short=50),
        num_training_frames=run_mode_specific_setting_fn(long=8, short=1),
        num_validation_frames=run_mode_specific_setting_fn(long=20, short=1),
        score_threshold=0.35,
        camera_setup=(2, 2),
    )

    evil_configs = {
        Stage.VALIDATE: get_evil_config(dataset_name, online_eval=True)[0],
        Stage.PREDICT: get_evil_config(dataset_name, online_eval=False)[0],
    }
    # use only 1 process locally to enable vscode debugging
    eval_run_environment = RunEnvironment(
        num_processes=get_env_specific_setting(local=1, azure=max(mp.cpu_count() // 2, 1), dt_flow=24),
        eval_compute_type=get_eval_compute_type(pipeline_step, qnn_backend),
    )

    store_gt_in_airbox: bool = True
    eval_objectness_threshold: float = 0.00005
    combined_metrics_yaml_base = USECASE_BASEDIR / "models" / "box3d_common" / "combined_metrics"
    combined_metrics_yaml = (
        combined_metrics_yaml_base
        / pipeline_step_specific_setting_fn(default="offline", train="online")
        / "tasks"
        / get_evil_config(dataset_name, online_eval=pipeline_step_specific_setting_fn(default=False, train=True))[2]
    )
    return SparseDetectionTaskConfig(
        head_config=_get_head_config(CAMERA_FEAT_MAP_STRIDE, bev_layout),
        loss_config=_get_loss_config(CAMERA_FEAT_MAP_STRIDE),
        image_writer_callback_config=image_writer_callback_params,
        label_set_name=label_definition.label_set.name,
        label_set_version=label_definition.label_set.version,
        label_set_background_class=BACKGROUND_CLASS,
        data_weight=1.0,
        loss_weight=1.0,
        camera_backbone_type=CameraBackboneType.IMAGE_SPACE,
        evaluation_folder=Path(),
        evil_configs=evil_configs,
        eval_run_environment=eval_run_environment,
        combined_metrics_yaml=combined_metrics_yaml,
        eval_objectness_threshold=eval_objectness_threshold,
        store_gt_in_airbox=store_gt_in_airbox,
        gt_key=BOX_3D_RELEASE_TASK_ID,
        max_num_detections=100,
        metric_configs=METRIC_CONFIGS,
        bev_layout=bev_layout,
    )
