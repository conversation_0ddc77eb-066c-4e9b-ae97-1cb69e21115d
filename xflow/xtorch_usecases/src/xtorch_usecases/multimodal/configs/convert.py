"""Contains configuration for convert stage."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from dataclasses import dataclass
from pathlib import Path

from omegaconf import MISSING
from qnn_custom_ops import QnnBackend

from conversion.qnn.definitions import InputTensorConfig, QuantCheckerConfig


@dataclass(kw_only=True)
class MultiModalConvertConfig:
    """Configuration for convert stage."""

    default_root_dir: Path = MISSING
    onnx_opset: int = 18
    qnn_backends: list[QnnBackend] = MISSING
    num_calib_samples: int = 250
    num_visualization_samples: int = 10
    visualization_objectness_threshold: float = 0.3
    skip_qnn_conversion: bool = False
    quant_check_config: QuantCheckerConfig | None = None
    skip_config_check: bool = False
    skip_subgraph_preparation: bool = False
    skip_quantization_check: bool = False
    skip_inference: bool = False
    onnx_export_aux_heads: bool = False
    onnx_export_dynamic_batch_axis: bool = False
    checkpoints_are_finetuned: bool = False
    convert_from_onnx_path: Path | None = None
    inputs_config: list[InputTensorConfig] | None = None

    @property
    def raw_onnx_model_path(self) -> Path:
        """Raw onnx model after pytorch conversion."""
        return self.default_root_dir / "raw_model.onnx"

    @property
    def patched_onnx_model_path(self) -> Path:
        """Onnx model graph after applying node / graph surgery."""
        return self.default_root_dir / "patched_model.onnx"

    @property
    def renamed_model_path(self) -> Path:
        """Onnx model graph after renaming some tensors."""
        return self.default_root_dir / "renamed_model.onnx"
