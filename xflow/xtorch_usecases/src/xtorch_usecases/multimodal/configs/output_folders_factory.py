"""Factory for creating output folder paths for the different steps of the pipeline."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from dataclasses import dataclass
from pathlib import Path

from xtorch_usecases.common.configuration.aml_pipelines.pipeline_info_json import initialize_pipeline_info
from xtorch_usecases.common.pipeline import PipelineStep


@dataclass
class TrainPipelineStepOutput:
    """Output folder paths for the training pipeline step."""

    checkpoints: Path
    evaluation: Path
    summaries: Path
    visualization: Path


@dataclass
class EvaluatePipelineStepOutput:
    """Output folder paths for the evaluation pipeline step."""

    evaluation: Path
    summaries: Path
    visualization: Path


@dataclass
class ConvertPipelineStepOutput:
    """Output folder paths for the conversion pipeline step."""

    conversion: Path
    evaluation: Path
    summaries: Path
    visualization: Path


@dataclass
class CheckConversionPipelineStepOutput:
    """Output folder paths for the conversion pipeline step."""

    check_conversion: Path
    evaluation: Path
    summaries: Path
    visualization: Path


@dataclass
class QatPipelineStepOutput:
    """Output folder paths for the qat stage."""

    checkpoints_qat: Path
    conversion_qat: Path
    evaluation: Path
    summaries: Path
    visualization: Path


@dataclass
class PipelineStepPaths:
    """Folder paths for a given pipeline step.

    Attributes:
        checkpoints_pretrained: Path to the pretrained checkpoints folder used in fine-tuning stage.
        checkpoints: Path to the checkpoints folder.
        checkpoints_qat: Path to the QAT checkpoints folder.
        conversion: Path to the conversion folder.
        check_conversion: Path to the check conversion folder.
        conversion_qat: Path to the QAT conversion folder.
        evaluation_torch: Path to the Torch evaluation folder.
        evaluation_qnn: Path to the QNN evaluation folder.
        output_artifacts: Path to the output artifacts folder.
        summaries: Path to the summaries folder.
        visualization: Path to the visualization folder.
    """

    checkpoints_pretrained: Path
    checkpoints: Path
    checkpoints_qat: Path
    conversion: Path
    check_conversion: Path
    conversion_qat: Path
    evaluation_torch: Path
    evaluation_qnn: Path
    output_artifacts: Path
    summaries: Path
    train_summaries: Path
    visualization: Path


PipelineStepOutput = (
    TrainPipelineStepOutput
    | EvaluatePipelineStepOutput
    | ConvertPipelineStepOutput
    | CheckConversionPipelineStepOutput
    | QatPipelineStepOutput
)


def get_pipeline_step_output(pipeline_step: PipelineStep, base_folder: Path) -> PipelineStepOutput:  # noqa: C901, PLR0911
    """Get the output folder paths for the given pipeline step.

    Args:
        pipeline_step: The step of the pipeline.
        base_folder: The base folder where the output folders are located.

    Returns:
        The PipelineStepOutput object containing the output folder paths.
    """

    if pipeline_step in (PipelineStep.TRAIN, PipelineStep.TRAIN_FINETUNE):
        return TrainPipelineStepOutput(
            checkpoints=Path(base_folder) / "checkpoints",
            evaluation=Path(base_folder) / "evaluation" / "online",
            summaries=Path(base_folder) / "summaries" / "train",
            visualization=Path(base_folder) / "visualizations" / "train",
        )
    if pipeline_step == PipelineStep.CONVERT:
        return ConvertPipelineStepOutput(
            conversion=Path(base_folder) / "conversion",
            evaluation=Path(base_folder) / "evaluation" / "qnn_online",
            summaries=Path(base_folder) / "summaries" / "convert",
            visualization=Path(base_folder) / "visualizations" / "convert",
        )
    if pipeline_step == PipelineStep.CHECK_CONVERSION_QNN:
        return CheckConversionPipelineStepOutput(
            check_conversion=Path(base_folder) / "check_conversion",
            evaluation=Path(base_folder) / "evaluation" / "qnn_online",
            summaries=Path(base_folder) / "summaries" / "check_conversion",
            visualization=Path(base_folder) / "visualizations" / "check_conversion",
        )
    if pipeline_step == PipelineStep.QAT:
        return QatPipelineStepOutput(
            checkpoints_qat=Path(base_folder) / "checkpoints_qat",
            conversion_qat=Path(base_folder) / "qat",
            evaluation=Path(base_folder) / "evaluation" / "qat_online",
            summaries=Path(base_folder) / "summaries" / "qat",
            visualization=Path(base_folder) / "visualizations" / "qat",
        )
    if pipeline_step == PipelineStep.EVALUATE_ONNX:
        return EvaluatePipelineStepOutput(
            evaluation=Path(base_folder) / "evaluation" / "onnx_offline",
            summaries=Path(base_folder) / "summaries" / "onnx_evaluate",
            visualization=Path(base_folder) / "visualizations" / "onnx_evaluate",
        )
    if pipeline_step == PipelineStep.EVALUATE_TORCH:
        return EvaluatePipelineStepOutput(
            evaluation=Path(base_folder) / "evaluation" / "offline",
            summaries=Path(base_folder) / "summaries" / "evaluate_torch_offline",
            visualization=Path(base_folder) / "visualizations" / "evaluate_torch_offline",
        )
    if pipeline_step == PipelineStep.EVALUATE_QNN:
        return EvaluatePipelineStepOutput(
            evaluation=Path(base_folder) / "evaluation" / "qnn_offline",
            summaries=Path(base_folder) / "summaries" / "evaluate_qnn_offline",
            visualization=Path(base_folder) / "visualizations" / "evaluate_qnn_offline",
        )
    if pipeline_step == PipelineStep.PROFILE_QNN:
        return EvaluatePipelineStepOutput(
            evaluation=Path(base_folder) / "evaluation" / "profile_qnn",
            summaries=Path(base_folder) / "summaries" / "profile_qnn",
            visualization=Path(base_folder) / "visualizations" / "profile_qnn",
        )
    if pipeline_step == PipelineStep.VISUALIZATION_TORCH:
        return EvaluatePipelineStepOutput(
            evaluation=Path(base_folder) / "evaluation" / "predict_torch",
            summaries=Path(base_folder) / "summaries" / "predict_torch",
            visualization=Path(base_folder) / "visualizations" / "predict_torch",
        )
    if pipeline_step == PipelineStep.VISUALIZATION_QNN:
        return EvaluatePipelineStepOutput(
            evaluation=Path(base_folder) / "evaluation" / "predict_qnn",
            summaries=Path(base_folder) / "summaries" / "predict_qnn",
            visualization=Path(base_folder) / "visualizations" / "predict_qnn",
        )
    msg = f"No PipelineStepOutput defined for pipeline step {pipeline_step}!"
    raise ValueError(msg)


def get_default_pipeline_step_paths(  # noqa: C901, PLR0911, PLR0912, PLR0915
    pipeline_step: PipelineStep,
    input_base_folder: str | Path,
    output_base_folder: str | Path,
) -> PipelineStepPaths:
    """Get the default output folder paths for the given pipeline step, assuming step sequence in a pipeline.

    Args:
        pipeline_step: The step of the pipeline.
        input_base_folder: The base folder where the input folders are located.
        output_base_folder: The base folder where the output folders are located
    """
    if pipeline_step == PipelineStep.PUBLISH:
        pipeline_step_output_train = get_pipeline_step_output(
            pipeline_step=PipelineStep.TRAIN, base_folder=Path(output_base_folder)
        )
        pipeline_step_output_eval_torch = get_pipeline_step_output(
            pipeline_step=PipelineStep.EVALUATE_TORCH, base_folder=Path(output_base_folder)
        )
        pipeline_step_output_eval_qnn = get_pipeline_step_output(
            pipeline_step=PipelineStep.EVALUATE_QNN, base_folder=Path(output_base_folder)
        )
        pipeline_step_output_convert = get_pipeline_step_output(
            pipeline_step=PipelineStep.CONVERT, base_folder=Path(output_base_folder)
        )
        pipeline_step_output_check_conversion = get_pipeline_step_output(
            pipeline_step=PipelineStep.CHECK_CONVERSION_QNN, base_folder=Path(output_base_folder)
        )
        pipeline_step_output_visualization = get_pipeline_step_output(
            pipeline_step=PipelineStep.VISUALIZATION_TORCH, base_folder=Path(output_base_folder)
        )
        assert isinstance(pipeline_step_output_train, TrainPipelineStepOutput)
        assert isinstance(pipeline_step_output_eval_torch, EvaluatePipelineStepOutput)
        assert isinstance(pipeline_step_output_eval_qnn, EvaluatePipelineStepOutput)
        assert isinstance(pipeline_step_output_convert, ConvertPipelineStepOutput)
        assert isinstance(pipeline_step_output_check_conversion, CheckConversionPipelineStepOutput)
        assert isinstance(pipeline_step_output_visualization, EvaluatePipelineStepOutput)
        pipeline_step_output_train.checkpoints.mkdir(parents=True, exist_ok=True)
        pipeline_step_output_eval_torch.evaluation.mkdir(parents=True, exist_ok=True)
        pipeline_step_output_eval_qnn.evaluation.mkdir(parents=True, exist_ok=True)
        pipeline_step_output_convert.conversion.mkdir(parents=True, exist_ok=True)
        pipeline_step_output_check_conversion.check_conversion.mkdir(parents=True, exist_ok=True)
        pipeline_step_output_visualization.visualization.mkdir(parents=True, exist_ok=True)
        return PipelineStepPaths(
            checkpoints_pretrained=Path("None"),
            checkpoints=pipeline_step_output_train.checkpoints,
            checkpoints_qat=Path("None"),
            conversion=pipeline_step_output_convert.conversion,
            check_conversion=pipeline_step_output_check_conversion.check_conversion,
            conversion_qat=Path("None"),
            evaluation_torch=pipeline_step_output_eval_torch.evaluation,
            evaluation_qnn=pipeline_step_output_eval_qnn.evaluation,
            output_artifacts=Path("None"),
            summaries=Path("None"),
            train_summaries=pipeline_step_output_train.summaries,
            visualization=pipeline_step_output_visualization.visualization,
        )

    pipeline_step_output = get_pipeline_step_output(pipeline_step=pipeline_step, base_folder=Path(output_base_folder))

    # determine paths
    if pipeline_step in (PipelineStep.TRAIN, PipelineStep.TRAIN_FINETUNE):
        pipeline_step_input = get_pipeline_step_output(
            pipeline_step=PipelineStep.TRAIN, base_folder=Path(input_base_folder)
        )

        assert isinstance(pipeline_step_input, TrainPipelineStepOutput)
        assert isinstance(pipeline_step_output, TrainPipelineStepOutput)

        pipeline_step_output.checkpoints.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.evaluation.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.summaries.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.visualization.mkdir(parents=True, exist_ok=True)
        initialize_pipeline_info(output_folder=Path(output_base_folder))
        return PipelineStepPaths(
            checkpoints_pretrained=pipeline_step_input.checkpoints,
            checkpoints=pipeline_step_output.checkpoints,
            checkpoints_qat=Path("None"),
            conversion=Path("None"),
            check_conversion=Path("None"),
            conversion_qat=Path("None"),
            evaluation_torch=pipeline_step_output.evaluation,
            evaluation_qnn=Path("None"),
            output_artifacts=pipeline_step_output.summaries,
            summaries=pipeline_step_output.summaries,
            train_summaries=pipeline_step_output.summaries,
            visualization=pipeline_step_output.visualization,
        )
    if pipeline_step == PipelineStep.EVALUATE_ONNX:
        pipeline_step_input = get_pipeline_step_output(
            pipeline_step=PipelineStep.CONVERT, base_folder=Path(input_base_folder)
        )
        assert isinstance(pipeline_step_input, ConvertPipelineStepOutput)
        assert isinstance(pipeline_step_output, EvaluatePipelineStepOutput)
        pipeline_step_output.evaluation.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.summaries.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.visualization.mkdir(parents=True, exist_ok=True)
        initialize_pipeline_info(input_folders=[Path(input_base_folder)], output_folder=Path(output_base_folder))
        return PipelineStepPaths(
            checkpoints_pretrained=Path("None"),
            checkpoints=Path("None"),
            checkpoints_qat=Path("None"),
            conversion=pipeline_step_input.conversion,
            check_conversion=Path("None"),
            conversion_qat=Path("None"),
            evaluation_torch=pipeline_step_output.evaluation,
            evaluation_qnn=Path("None"),
            output_artifacts=pipeline_step_output.summaries,
            summaries=pipeline_step_output.summaries,
            train_summaries=Path("None"),
            visualization=pipeline_step_output.visualization,
        )

    if pipeline_step in (PipelineStep.EVALUATE_TORCH, PipelineStep.VISUALIZATION_TORCH):
        pipeline_step_input = get_pipeline_step_output(
            pipeline_step=PipelineStep.TRAIN, base_folder=Path(input_base_folder)
        )
        assert isinstance(pipeline_step_input, TrainPipelineStepOutput)
        assert isinstance(pipeline_step_output, EvaluatePipelineStepOutput)

        if pipeline_step == PipelineStep.VISUALIZATION_TORCH:
            _pipeline_step_output_evaluation = Path("None")
        else:
            _pipeline_step_output_evaluation = pipeline_step_output.evaluation
            pipeline_step_output.evaluation.mkdir(parents=True, exist_ok=True)

        pipeline_step_output.summaries.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.visualization.mkdir(parents=True, exist_ok=True)
        initialize_pipeline_info(input_folders=[Path(input_base_folder)], output_folder=Path(output_base_folder))
        return PipelineStepPaths(
            checkpoints_pretrained=Path("None"),
            checkpoints=pipeline_step_input.checkpoints,
            checkpoints_qat=Path("None"),
            conversion=Path("None"),
            check_conversion=Path("None"),
            conversion_qat=Path("None"),
            evaluation_torch=_pipeline_step_output_evaluation,
            evaluation_qnn=Path("None"),
            output_artifacts=pipeline_step_output.summaries,
            summaries=pipeline_step_output.summaries,
            train_summaries=pipeline_step_input.summaries,
            visualization=pipeline_step_output.visualization,
        )

    if pipeline_step == PipelineStep.CONVERT:
        pipeline_step_input = get_pipeline_step_output(
            pipeline_step=PipelineStep.TRAIN, base_folder=Path(input_base_folder)
        )
        assert isinstance(pipeline_step_input, TrainPipelineStepOutput | QatPipelineStepOutput)
        assert isinstance(pipeline_step_output, ConvertPipelineStepOutput)
        pipeline_step_output.conversion.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.evaluation.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.summaries.mkdir(parents=True, exist_ok=True)
        initialize_pipeline_info(input_folders=[Path(input_base_folder)], output_folder=Path(output_base_folder))
        if isinstance(pipeline_step_input, TrainPipelineStepOutput):
            return PipelineStepPaths(
                checkpoints_pretrained=Path("None"),
                checkpoints=pipeline_step_input.checkpoints,
                checkpoints_qat=Path("None"),
                conversion=pipeline_step_output.conversion,
                check_conversion=Path("None"),
                conversion_qat=Path("None"),
                evaluation_torch=Path("None"),
                evaluation_qnn=pipeline_step_output.evaluation,
                output_artifacts=pipeline_step_output.summaries,
                summaries=pipeline_step_output.summaries,
                train_summaries=pipeline_step_input.summaries,
                visualization=Path("None"),
            )

        return PipelineStepPaths(
            checkpoints_pretrained=Path("None"),
            checkpoints=Path("None"),
            checkpoints_qat=pipeline_step_input.checkpoints_qat,
            conversion=pipeline_step_output.conversion,
            check_conversion=Path("None"),
            conversion_qat=pipeline_step_input.conversion_qat,
            evaluation_torch=Path("None"),
            evaluation_qnn=pipeline_step_output.evaluation,
            output_artifacts=pipeline_step_output.summaries,
            summaries=pipeline_step_output.summaries,
            train_summaries=Path("None"),
            visualization=Path("None"),
        )

    if pipeline_step == PipelineStep.CHECK_CONVERSION_QNN:
        pipeline_step_input = get_pipeline_step_output(
            pipeline_step=PipelineStep.CONVERT, base_folder=Path(input_base_folder)
        )
        assert isinstance(pipeline_step_input, ConvertPipelineStepOutput)
        assert isinstance(pipeline_step_output, CheckConversionPipelineStepOutput)
        pipeline_step_output.check_conversion.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.evaluation.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.summaries.mkdir(parents=True, exist_ok=True)
        initialize_pipeline_info(input_folders=[Path(input_base_folder)], output_folder=Path(output_base_folder))
        return PipelineStepPaths(
            checkpoints_pretrained=Path("None"),
            checkpoints=Path("None"),
            checkpoints_qat=Path("None"),
            conversion=pipeline_step_input.conversion,
            check_conversion=pipeline_step_output.check_conversion,
            conversion_qat=Path("None"),
            evaluation_torch=Path("None"),
            evaluation_qnn=pipeline_step_output.evaluation,
            output_artifacts=pipeline_step_output.summaries,
            summaries=pipeline_step_output.summaries,
            train_summaries=Path("None"),
            visualization=Path("None"),
        )

    if pipeline_step == PipelineStep.QAT:
        pipeline_step_input = get_pipeline_step_output(
            pipeline_step=PipelineStep.TRAIN, base_folder=Path(input_base_folder)
        )
        assert isinstance(pipeline_step_input, TrainPipelineStepOutput)
        assert isinstance(pipeline_step_output, QatPipelineStepOutput)
        pipeline_step_output.checkpoints_qat.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.conversion_qat.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.evaluation.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.summaries.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.visualization.mkdir(parents=True, exist_ok=True)
        initialize_pipeline_info(input_folders=[Path(input_base_folder)], output_folder=Path(output_base_folder))
        return PipelineStepPaths(
            checkpoints_pretrained=Path("None"),
            checkpoints=pipeline_step_input.checkpoints,
            checkpoints_qat=pipeline_step_output.checkpoints_qat,
            conversion=Path("None"),
            check_conversion=Path("None"),
            conversion_qat=pipeline_step_output.conversion_qat,
            evaluation_torch=pipeline_step_output.evaluation,
            evaluation_qnn=pipeline_step_output.evaluation,
            output_artifacts=pipeline_step_output.summaries,
            summaries=pipeline_step_output.summaries,
            train_summaries=pipeline_step_input.summaries,
            visualization=pipeline_step_output.visualization,
        )

    if pipeline_step in (PipelineStep.EVALUATE_QNN, PipelineStep.PROFILE_QNN, PipelineStep.VISUALIZATION_QNN):
        pipeline_step_input = get_pipeline_step_output(
            pipeline_step=PipelineStep.CONVERT, base_folder=Path(input_base_folder)
        )
        assert isinstance(pipeline_step_input, ConvertPipelineStepOutput)
        assert isinstance(pipeline_step_output, EvaluatePipelineStepOutput)

        if pipeline_step == PipelineStep.VISUALIZATION_QNN:
            _pipeline_step_output_evaluation = Path("None")
        else:
            _pipeline_step_output_evaluation = pipeline_step_output.evaluation
            pipeline_step_output.evaluation.mkdir(parents=True, exist_ok=True)

        pipeline_step_output.summaries.mkdir(parents=True, exist_ok=True)
        pipeline_step_output.visualization.mkdir(parents=True, exist_ok=True)
        initialize_pipeline_info(input_folders=[Path(input_base_folder)], output_folder=Path(output_base_folder))
        return PipelineStepPaths(
            checkpoints_pretrained=Path("None"),
            checkpoints=Path("None"),
            checkpoints_qat=Path("None"),
            conversion=pipeline_step_input.conversion,
            check_conversion=Path("None"),
            conversion_qat=Path("None"),
            evaluation_torch=Path("None"),
            evaluation_qnn=_pipeline_step_output_evaluation,
            output_artifacts=pipeline_step_output.summaries,
            summaries=pipeline_step_output.summaries,
            train_summaries=Path("None"),
            visualization=pipeline_step_output.visualization,
        )

    msg = f"No PipelineStepOutput defined for pipeline step {pipeline_step}!"
    raise ValueError(msg)
