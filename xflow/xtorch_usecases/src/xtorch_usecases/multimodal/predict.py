"""Multi-modal usecase training stage."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
import sys
from typing import cast

import coloredlogs
import hydra
from hydra.core.config_store import ConfigStore
from omegaconf import DictConfig, OmegaConf

from xcommon.logging import log_configuration
from xtorch.training import Stage
from xtorch.training.runner import CheckpointFile, LightningRunner
from xtorch_extensions.environment import log_environment
from xtorch_usecases.common.datasets.combined.data_module import CombinedDataModule
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.common.datasets.dassie.data_module import DassieDataModule, DassieDataModuleConfig
from xtorch_usecases.multimodal.common.hydra_safe_argv import filter_sys_args_for_hydra
from xtorch_usecases.multimodal.configs.run import MultiModalRunConfig
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.models.release.checkpoint_loading import load_checkpoint_from_dir
from xtorch_usecases.multimodal.models.training_module import MultiModalUseCaseInferenceModule
from xtorch_usecases.multimodal.train import DATA_CONVERTER, MODEL_CONFIG
from xtorch_usecases.multimodal.utils.pipeline_info_helpers import sync_dataset_versions_with_pipeline_info

_LOGGER = logging.getLogger("MultiModal Torch Predict Stage")


def main(argv: list[str]) -> None:
    """Main function."""

    coloredlogs.install(level=logging.INFO)

    log_environment()

    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str.lower, choices=[m.value for m in Model], default=Model.BOX3D_SIMPLE)
    args, unknown_args = parser.parse_known_args(argv)

    config, unknown_args = MODEL_CONFIG[args.model](unknown_args, PipelineStep.VISUALIZATION_TORCH, 1)
    config_store = ConfigStore.instance()
    config_store.store("config", node=config)
    filter_sys_args_for_hydra(unknown_args)
    hydra_main()


def run_predict_pipeline(
    config: MultiModalRunConfig,
    data_module: CombinedDataModule | DassieDataModule,
    training_module: MultiModalUseCaseInferenceModule,
) -> None:
    """Run the prediction pipeline."""

    config.setup_callbacks_for_pipeline_step(PipelineStep.VISUALIZATION_TORCH)
    log_configuration(name=__file__, config=config)

    sync_dataset_versions_with_pipeline_info(config)

    # TODO: refactor with convert stage together
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/335079

    eval_checkpoint_path = load_checkpoint_from_dir(
        checkpoints_dir=config.pipeline_step_paths.checkpoints,
        checkpoint_file=config.eval_config.checkpoint,
        try_find_best_ckpt=config.eval_config.try_find_best_ckpt,
        ckpt_name=config.ckpt_name,
    )
    if eval_checkpoint_path is not None:
        loaded_checkpoint = CheckpointFile(eval_checkpoint_path)
    else:
        msg = "Checkpoint file not found!"
        raise FileNotFoundError(msg)

    # Ignore types for now as they seem to be inconsistent/outdated at several places
    LightningRunner(
        data_module=data_module,  # pyright: ignore[reportArgumentType]
        training_module=training_module,  # pyright: ignore[reportArgumentType]
        config=config,
        is_cloud_run=config.data_config.environment == "azure",
    ).run(
        experiment_name="MultiModal Bev Experiment",
        stage=Stage.PREDICT,
        pretrained_weights=loaded_checkpoint,
    )


@hydra.main(version_base=None, config_name="config")
def hydra_main(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    _LOGGER.debug(f"Config as `MultiModalConfig` object: \n {OmegaConf.to_yaml(config)}")

    if isinstance(config.data_config, DassieDataModuleConfig):
        data_module = DassieDataModule(config.data_config)
    else:
        data_module = CombinedDataModule(config.data_config, data_converter=DATA_CONVERTER.get(config.model))

    training_module = MultiModalUseCaseInferenceModule(config, config.multi_task_collection.enabled_tasks)

    run_predict_pipeline(config=config, data_module=data_module, training_module=training_module)


if __name__ == "__main__":
    main(sys.argv[1:])
