"""Helper functions for the conversion in the xtorch usecases."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON>sch GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from typing import Any

from conversion.metadata import ModelMetadata
from conversion.qnn.common import OverrideUpdateRules
from conversion.qnn.onnx_surgery.onnx_graph_surgery_rules import (
    AbstractGraphRewriter,
    FixTransposeIntoGather,
    FixTransposeReshapeSoftmax,
    FuseLpNormalization,
    FuseMatMulAddOps,
    FuseMatMulMulOps,
    FuseShapeOps,
    FuseUnsqueezeConcatReshape,
    InsertGather2D,
    MergeConcatConstantGraphRewriter,
    RewriteYUVInputPlugin,
    SplitSumOp,
)
from conversion.qnn.onnx_surgery.onnx_node_surgery_rules import (
    AbstractNodeRewriter,
    Fix16GreaterLessConstantQuantizationOverride,
    RewriteActivationQuantizationOverrides,
    RewriteArgmaxImage,
    RewriteSoftplus,
    RewriteUnsqueeze,
)
from conversion.qnn.override_update_rules import FixQuantParams
from xtorch_extensions.conversion.metadata import create_metadata

LOGGER = logging.getLogger(__name__)


def get_default_surgery_rules(  # noqa: D417
    heads_to_outputs_mapping: dict[str, str], feature_extractor_name: str = "MultitaskNetwork"
) -> tuple[list[AbstractGraphRewriter], list[AbstractNodeRewriter]]:
    """Return default rewriters for Multimodal model.

    Args:
        heads_to_outputs_mapping: Mapping between {head_name: [head_outputs]}
            eg {traffic_light: tf_v2_boxes}
            From TaskCollection corresponds to {task.id: tasks.outputs._fields}

    Returns:
        graph_surgery_rules, node_surgery_rule
    """
    graph_surgery_rules = []
    node_surgery_rules = []
    graph_surgery_rules += [
        RewriteYUVInputPlugin(
            yuv_node_pattern=r".*_yuv_420_input_adapter",
            vcrop_node_pattern="",
            yuv_subsequent_conv_node_pattern=r".*image_encoder\W_conv_blocks_1\.0.*(Conv|._net)",
            split_input=False,
        ),
        FixTransposeIntoGather(),
        FixTransposeReshapeSoftmax(),
        InsertGather2D(),
        FuseMatMulMulOps(),
        FuseMatMulAddOps(),
        FuseLpNormalization(),
        FuseUnsqueezeConcatReshape(),
        SplitSumOp(),
        MergeConcatConstantGraphRewriter(),
        FuseShapeOps(),
    ]

    node_surgery_rules += [
        RewriteArgmaxImage(),
        RewriteUnsqueeze(),
        RewriteSoftplus(),
        RewriteActivationQuantizationOverrides(),
        Fix16GreaterLessConstantQuantizationOverride(),
    ]

    return graph_surgery_rules, node_surgery_rules


def get_override_rules() -> OverrideUpdateRules | None:
    """Return default rewriters for Multimodal model."""
    override_rules = {}
    # QNN concat bug workaround for incorrect scales/offsets calculation
    # for the case of inputs with mixed precision
    override_rules["Concat"] = {"Concat": FixQuantParams()}
    override_rules["MatMul"] = {"MatMul": FixQuantParams()}
    override_rules["Gemm"] = {"gemm": FixQuantParams()}

    return override_rules


def initialize_model_metadata_json(
    model_name: str, task_name: str, model_outputs: list[str], attributes: dict[str, Any] | None = None
) -> ModelMetadata:
    """Initialize metadata json and add tasks, outputs and required input groupings.

    Args:
        model_name: Name of model
        task_name: Name of task to which outputs to be associated.
        model_outputs: List of model output tensor names.
        attributes: Attributes of additional info. to be added

    Returns:
        ModelMetaData initialized instance.
    """
    model_metadata = create_metadata(model_name=model_name, task_ids=[task_name])
    # Add output tensors to task
    for out in model_outputs:
        model_metadata.add_output_tensor(out, task_name)
    # Add required input groupings for deployment (That is needed for PaceRepo integration)
    model_metadata.add_input_grouping("image_features", "vision_image_space_feats.*")

    if attributes is not None:
        model_metadata.metadata.attributes.update(attributes)
    return model_metadata
