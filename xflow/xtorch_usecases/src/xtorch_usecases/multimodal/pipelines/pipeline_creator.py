"""Pipeline creator class that provides builder functions for individual pipeline stages."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
from enum import Enum
from typing import Final, Literal

from azure.ai.ml import Input, Output, command
from azure.ai.ml.entities import Command, Environment

from azure_tools.pipelines.definitions import DockerImageType
from azure_tools.pipelines.pipeline_creator import PipelineCreator, PipelineCreatorWithTrigger
from xcontract.training.run_mode import RunMode
from xtorch_usecases.multimodal.pipelines.pipeline_launcher_args import SUPPORTED_MEDIUM_RUN_MODELS

LOGGER = logging.getLogger(__name__)


# Temporary until all single-task models are moved to the new config setup
USES_NEW_CONFIG: Final[set[str]] = set()


class DefaultCompute(str, Enum):
    """Default compute clusters for the multimodal pipelines usecase."""

    # Default compute for training on GPU
    GPU_TRAIN = "gpupool2h100"
    GPU_QAT = "gpupool2h100"  # noqa: PIE796
    # Default compute for eval on GPU
    GPU_EVAL = "gpupoolh100"  # NOTE: a smaller compute could be used if we increase the shm limits
    # Default compute for conversion on GPU
    GPU_CONVERT = "gpupoolt4"
    # Default compute for stages requiring  GPU inference like evaluation
    GPU_INFERENCE = "gpupool4coret4"
    # Default compute for stages requiring a CPU only
    CPU = "cpupool8core300"
    # Default compute for data updates
    UPDATE_DATA = "cpupool16core600"


class MultiModalPipelineCreator(PipelineCreator):
    """The usecase specific class for azure sdk v2 pipelines."""

    # -------------------------------------- Generic pipeline builder functions ----------------------------------------
    def _build_aggregation_stage(
        self,
        stage_entry_point: str,
        stage_inputs: dict[str, Input | Output],
        stage_name: str | None = None,
        fixed_stage_args: list[str] | None = None,
        default_compute: str = DefaultCompute.CPU.value,
        default_timeout: int | None = 10 * 3600,
    ) -> Command:
        """Returns a command job executing a stage aggregating multiple inputs (e.g. publish or gather).

        Arguments:
            stage_entry_point: The stage entry point to execute.
            stage_inputs: List of stage inputs from previous stages.
            stage_name: The name of the stage. Used as display name and identifier for the stage.
            fixed_stage_args: List of fixed arguments added to the stage.
            default_compute: The default compute to run this stage on. Used only if the init argument "stage_compute"
                does not contain an entry for this stage.
            default_timeout: The default timeout for this stage (in seconds), used only if the init argument
                "stage_timeout" does not contain an entry for this stage.
        """
        stage_name = stage_name if stage_name is not None else stage_entry_point

        launch_command = self._build_basic_launch_command(stage_entry_point, "${{ outputs.stage_output }}")
        launch_command = self._add_stage_args(launch_command, stage_name=stage_name, fixed_stage_args=fixed_stage_args)

        inputs_definition = {}

        if len(stage_inputs) >= 1:
            launch_command += " --input_folder "
            launch_command += " ".join([f"${{{{ inputs.{input_name} }}}}" for input_name in stage_inputs])
            inputs_definition.update({input_name: Input(type="uri_folder") for input_name in stage_inputs})

        stage_command = command(
            command=launch_command,
            environment=Environment(image=self._docker_images[DockerImageType.RELEASE]),
            inputs=inputs_definition,
            outputs={"stage_output": Output(type="uri_folder", mode="rw_mount")},
            compute=self._stage_compute.get(stage_name, default_compute),
            code=self._experiment_folder,
            display_name=stage_name,
            name=self._stage_name_prefix + stage_name,
            timeout=self._stage_timeout.get(stage_name, default_timeout),
            environment_variables={
                "DATASET_MOUNT_CACHE_SIZE": "-10GB",
            },
        )
        return stage_command(**stage_inputs)

    def _build_basic_launch_command(
        self,
        stage_entry_point: str,
        output_folder: str,
        input_folder: str | None = None,
        compute: str | None = None,
        instance_count: int | None = None,
    ) -> str:
        """Returns the basic launch command which can be further customized.

        Arguments:
            stage_entry_point: The stage entry point to execute.
            output_folder: The output folder to pass to the launcher.
            input_folder: The input folder to pass to the launcher, or None for no input argument.
            compute: The compute target to pass to the launcher.
            instance_count: The number of instances to pass to the launcher.

        Returns:
            The basic launch command which can be extended further.
        """

        launch_command = (
            # uncomment this to force using the local launcher.py
            # "ls -l ; "
            # "pip install xflow_xtorch_usecases-0.1.dev1+????????????.??????????-py3-none-any.whl ; "
            f"python -m xtorch_usecases.launcher --stage {stage_entry_point} --output_folder {output_folder} "
        )

        if self._model in USES_NEW_CONFIG:
            launch_command += f"--config_name {self._model} "
            if self.create_short_ct_run:
                launch_command += "--run_mode short "
        else:
            launch_command += f"--model {self._model} --environment azure "
            if self._model in SUPPORTED_MEDIUM_RUN_MODELS:
                launch_command += f"--run_mode {self.run_mode.value} "
            elif self.create_short_ct_run:
                launch_command += "--short_ct_run True "

        if input_folder is not None:
            launch_command += f"--input_folder {input_folder} "
        if compute is not None:
            launch_command += f"--aml_compute {compute} "
        if instance_count is not None:
            launch_command += f"--aml_node_count {instance_count} "

        return launch_command

    # ------------------------------------- Collection of specific pipeline stages -------------------------------------
    def _build_train_stage(
        self,
        start_trigger: Output | None = None,
        default_timeout: int = 2 * 24 * 3600,
    ) -> tuple[Command | None, Input | Output]:
        """Builds a main stage, if its output not already exists.

        Arguments:
            start_trigger: Input folder signaling when the stage is ready to start. Content of the folder is ignored.
            checkpoint_input: The output of the previous stage where the model checkpoint is located.
            default_timeout: The default timeout for this stage (in seconds), used only if the init argument
                "stage_timeout" does not contain an entry for this stage.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "train"
        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])
        train = self._build_single_input_stage(
            stage_entry_point=f"multimodal.{stage_name}",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_TRAIN.value,
            default_timeout=default_timeout,
            docker_image_type=DockerImageType.RELEASE,
            stage_trigger=start_trigger,
            input_datastores=[
                "ingest_init",
                "ingest_cache",
                "ingest_datasets",
                "workspaceartifactstore",
                "workspaceblobstore",
                "workspaceartifactstore_ct",
                "workspaceblobstore_ct",
            ],
            add_distribution_context=True,
        )
        return train, train.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_train_finetune_stage(
        self,
        start_trigger: Output | None = None,
        stage_input: Input | Output | None = None,
        default_timeout: int = 2 * 24 * 3600,
    ) -> tuple[Command | None, Input | Output]:
        """Builds a main stage, if its output not already exists.

        Arguments:
            start_trigger: Input folder signaling when the stage is ready to start. Content of the folder is ignored.
            stage_input: The output of the previous stage where the model checkpoint is located.
            default_timeout: The default timeout for this stage (in seconds), used only if the init argument
                "stage_timeout" does not contain an entry for this stage.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """

        stage_name = "train_finetune"

        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])
        train = self._build_single_input_stage(
            stage_entry_point=f"multimodal.{stage_name}",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_TRAIN.value,
            default_timeout=default_timeout,
            docker_image_type=DockerImageType.RELEASE,
            stage_trigger=start_trigger,
            stage_input=stage_input,
            input_datastores=[
                "ingest_init",
                "ingest_cache",
                "ingest_datasets",
                "workspaceartifactstore",
                "workspaceblobstore",
                "workspaceartifactstore_ct",
                "workspaceblobstore_ct",
            ],
            add_distribution_context=True,
        )
        return train, train.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_qat_stage(
        self,
        start_trigger: Output | None = None,
    ) -> tuple[Command | None, Input | Output]:
        """Builds a qat stage, if its output not already exists.

        Arguments:
            start_trigger: The output of the training or selection stage where the model checkpoint is located.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "qat"
        if "qat" in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])
        qat = self._build_single_input_stage(
            stage_entry_point=f"multimodal.{stage_name}",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_QAT.value,
            default_timeout=48 * 3600,
            docker_image_type=DockerImageType.RELEASE,
            stage_input=start_trigger,
            input_datastores=[
                "ingest_init",
                "ingest_cache",
                "ingest_datasets",
                "workspaceartifactstore",
                "workspaceblobstore",
                "workspaceartifactstore_ct",
                "workspaceblobstore_ct",
            ],
            add_distribution_context=True,
        )
        return qat, qat.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_convert_stage(
        self,
        stage_input: Input | Output,
        *,
        preceding_step: Literal["train", "train_finetune", "qat"] = "train",
    ) -> tuple[Command | None, Input | Output]:
        """Builds a conversion stage, if its output not already exists.

        Arguments:
            stage_input: The output of the training or selection stage where the model checkpoint is located.
            preceding_step: The step that precedes the conversion stage, used to determine the fixed stage arguments.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "convert"
        if "convert" in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])
        fixed_stage_args = None
        if preceding_step == "train_finetune":
            fixed_stage_args = ["convert_config.checkpoints_are_finetuned=True"]
        elif preceding_step == "qat":
            fixed_stage_args = [
                "convert_config.convert_from_onnx_path=${{ inputs.stage_input }}/qat/raw_model.onnx",
                "convert_config.skip_config_check=True",  # No access to training config when converting from qat onnx
            ]
        convert = self._build_single_input_stage(
            stage_entry_point=f"multimodal.{stage_name}",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_CONVERT.value,
            default_timeout=12 * 3600,
            docker_image_type=DockerImageType.RELEASE,
            stage_input=stage_input,
            fixed_stage_args=fixed_stage_args,
            input_datastores=[
                "ingest_cache",
                "ingest_datasets",
            ],
        )
        return convert, convert.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_check_conversion_stage(self, stage_input: Input | Output) -> tuple[Command | None, Input | Output]:
        """Builds a check conversion stage, if its output not already exists.

        Arguments:
            stage_input: The output of the training or selection stage where the model checkpoint is located.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "check_conversion"
        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])
        check_conversion = self._build_single_input_stage(
            stage_entry_point=f"multimodal.{stage_name}",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_CONVERT.value,
            default_timeout=12 * 3600,
            docker_image_type=DockerImageType.RELEASE,
            stage_input=stage_input,
            input_datastores=[
                "ingest_cache",
                "ingest_datasets",
            ],
        )
        return check_conversion, check_conversion.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_evaluate_torch_stage(self, stage_input: Input | Output) -> tuple[Command | None, Input | Output]:
        """Builds an evaluation stage for the torch model, if its output not already exists.

        Arguments:
            stage_input: The output of the previous stage where the model to evaluate is located.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "evaluate_torch"
        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])

        evaluate = self._build_single_input_stage(
            stage_entry_point="multimodal.evaluate",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_EVAL.value,
            docker_image_type=DockerImageType.RELEASE,
            default_timeout=12 * 3600,
            stage_input=stage_input,
            input_datastores=[
                "ingest_cache",
                "ingest_datasets",
            ],
            output_mode="upload",
        )
        return evaluate, evaluate.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_evaluate_onnx_stage(self, stage_input: Input | Output) -> tuple[Command | None, Input | Output]:
        """Builds an evaluation stage for the onnx model, if its output does not already exists.

        Arguments:
            stage_input: The output of the previous stage where the model to evaluate is located.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "evaluate_onnx"
        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])

        evaluate = self._build_single_input_stage(
            stage_entry_point="multimodal.onnx_evaluate",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_EVAL.value,
            docker_image_type=DockerImageType.RELEASE,
            default_timeout=12 * 3600,
            stage_input=stage_input,
            input_datastores=[
                "ingest_cache",
                "ingest_datasets",
            ],
            output_mode="upload",
        )
        return evaluate, evaluate.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_evaluate_qnn_stage(
        self,
        stage_input: Input | Output,
        model_type: Literal["qnn_cpu", "qnn_htp"],
    ) -> tuple[Command | None, Input | Output]:
        """Builds an evaluation stage for the qnn model, if its output not already exists.

        Arguments:
            stage_input: The output of the previous stage where the model to evaluate is located.
            model_type: "model_type" parameter passed to the evaluation.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_args = []
        stage_name = "evaluate_qnn"
        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])
        if self._model not in USES_NEW_CONFIG:
            stage_args.append(f"--model_type {model_type}")

        # Execute qnn evaluation always as short run
        if self._model in SUPPORTED_MEDIUM_RUN_MODELS:
            stage_args.append(f" --run_mode {RunMode.SHORT.value}")
        elif not self.create_short_ct_run:
            stage_args.append("--short_ct_run true")

        evaluate = self._build_single_input_stage(
            stage_entry_point="multimodal.qnn_evaluate",
            stage_name=stage_name,
            fixed_stage_args=stage_args,
            default_compute=DefaultCompute.CPU.value,
            docker_image_type=DockerImageType.RELEASE,
            default_timeout=12 * 3600,
            stage_input=stage_input,
            input_datastores=[
                "ingest_cache",
                "ingest_datasets",
            ],
            output_mode="upload",
        )
        return evaluate, evaluate.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_predict_torch_stage(self, stage_input: Input | Output) -> tuple[Command | None, Input | Output]:
        """Builds a prediction stage for the torch model, if its output not already exists.

        Arguments:
            stage_input: The output of the previous stage where the model to predict is located.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "predict_torch"
        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])

        predict = self._build_single_input_stage(
            stage_entry_point="multimodal.predict",
            stage_name=stage_name,
            default_compute=DefaultCompute.GPU_EVAL.value,
            docker_image_type=DockerImageType.RELEASE,
            default_timeout=12 * 3600,
            stage_input=stage_input,
            input_datastores=[
                "ingest_cache",
                "ingest_datasets",
            ],
            output_mode="upload",
        )
        return predict, predict.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_predict_qnn_stage(
        self,
        stage_input: Input | Output,
        model_type: Literal["qnn_cpu", "qnn_htp"],
    ) -> tuple[Command | None, Input | Output]:
        """Builds a prediction stage for the qnn model, if its output not already exists.

        Arguments:
            stage_input: The output of the previous stage where the model to predict is located.
            model_type: "model_type" parameter passed to the prediction.

        Returns:
            - Stage command, or None if stage output already exists.
            - Output, either from the newly created stage or the existing output.
        """
        stage_name = "predict_qnn"
        if stage_name in self._existing_outputs:
            return None, Input(type="uri_folder", path=self._existing_outputs[stage_name])
        stage_args = [f"--model_type {model_type}"]

        predict = self._build_single_input_stage(
            stage_entry_point="multimodal.qnn_predict",
            stage_name=stage_name,
            fixed_stage_args=stage_args,
            default_compute=DefaultCompute.CPU.value,
            docker_image_type=DockerImageType.RELEASE,
            default_timeout=12 * 3600,
            stage_input=stage_input,
            input_datastores=[
                "ingest_cache",
                "ingest_datasets",
            ],
            output_mode="upload",
        )
        return predict, predict.outputs.stage_output  # type: ignore[reportAttributeAccessIssue]

    def _build_publish_stage(self, stage_inputs: dict[str, Input | Output]) -> Command:
        # The publish stage needs output mode mount to guarantee the creation of the storage, because the storage is
        # used as trigger for the long run of the ct pipeline.
        stage_name = "publish"
        return self._build_aggregation_stage(
            stage_inputs=stage_inputs,
            stage_name=stage_name,
            stage_entry_point=f"multimodal.{stage_name}",
        )


class MultiModalPipelineCreatorWithTrigger(MultiModalPipelineCreator, PipelineCreatorWithTrigger):
    """Trajectory Prediction specific class for sdk v2 pipelines with start and end trigger."""
