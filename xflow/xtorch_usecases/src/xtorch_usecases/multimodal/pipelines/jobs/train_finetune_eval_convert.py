"""Pipeline job with the main stage."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Iterable

from azure.ai.ml import Output
from azure.ai.ml.entities import Command

from xtorch_extensions.collections import filter_none
from xtorch_usecases.multimodal.pipelines.pipeline_creator import MultiModalPipelineCreatorWithTrigger


class PipelineCreator(MultiModalPipelineCreatorWithTrigger):
    """Pipeline for training, torch evaluation and conversion."""

    def _get_pipeline_steps_with_trigger(self, start_trigger: Output | None = None) -> tuple[Iterable[Command], Output]:
        stage_outputs = {}

        train, stage_outputs["train"] = self._build_train_stage(
            start_trigger=start_trigger,
        )

        train_finetune, stage_outputs["train_finetune"] = self._build_train_finetune_stage(
            stage_input=stage_outputs["train"],
        )

        convert, stage_outputs["convert"] = self._build_convert_stage(
            stage_input=stage_outputs["train_finetune"], preceding_step="train_finetune"
        )

        evaluate_torch, stage_outputs["evaluate_torch"] = self._build_evaluate_torch_stage(
            stage_input=stage_outputs["train_finetune"],
        )

        # Don't include train stage in the publish stage
        # This prevents publishing files with the same name
        del stage_outputs["train"]

        publish = self._build_publish_stage(
            stage_inputs=stage_outputs,
        )

        return filter_none(train, train_finetune, convert, evaluate_torch, publish), publish.outputs.stage_output  # type: ignore[attr-defined]
