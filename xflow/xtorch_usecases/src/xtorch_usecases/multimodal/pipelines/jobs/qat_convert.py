"""Pipeline job with the main stage."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Iterable

from azure.ai.ml import Output
from azure.ai.ml.entities import Command

from xtorch_extensions.collections import filter_none
from xtorch_usecases.multimodal.pipelines.pipeline_creator import MultiModalPipelineCreatorWithTrigger


class PipelineCreator(MultiModalPipelineCreatorWithTrigger):
    """Pipeline for qat and conversion."""

    def _get_pipeline_steps_with_trigger(self, start_trigger: Output | None = None) -> tuple[Iterable[Command], Output]:
        stage_outputs = {}

        qat, stage_outputs["qat"] = self._build_qat_stage(start_trigger=start_trigger)

        convert, stage_outputs["convert"] = self._build_convert_stage(
            stage_input=stage_outputs["qat"], preceding_step="qat"
        )

        publish = self._build_publish_stage(stage_inputs=stage_outputs)

        return filter_none(qat, convert, publish), publish.outputs.stage_output  # type: ignore[attr-defined]
