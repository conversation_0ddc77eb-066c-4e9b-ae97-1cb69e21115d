"""Multi-modal usecase convert stage."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import json
import logging
import re
import sys
from collections.abc import Iterable
from pathlib import Path
from typing import Any, Literal, Protocol, cast, get_args, runtime_checkable

import coloredlogs
import hydra
import onnx
import onnx_graphsurgeon as gs
import torch
from deepdiff import DeepDiff
from deepdiff import parse_path as parse_path_deepdiff
from hydra.core.config_store import ConfigStore
from omegaconf import DictConfig, ListConfig, OmegaConf, SCMode
from packaging.version import Version
from qnn_custom_ops import QnnBackend
from torch import nn

from conversion.qnn.common import QnnModelArtifacts, move_param_quantization_overrides
from conversion.qnn.definitions import InputTensorConfig, InputTensorType
from conversion.qnn.inference import QnnProfilingLevel
from conversion.qnn.onnx_surgery.onnx_utils import rename_onnx_tensors
from conversion.qnn.quantization_overrides import ENCODING_GROUPS, merge_quantization_overrides
from xcommon.logging import log_configuration
from xcontract.data.definitions.inputs import InputDataId
from xtorch.export import ExportConfig
from xtorch.export.onnx import export_to_onnx
from xtorch.training.runner import CheckpointFile, save_config
from xtorch_extensions.conversion.optimization import cleanup_and_optimize_onnx
from xtorch_extensions.environment import log_environment
from xtorch_usecases.common.datasets.combined.data_module import CombinedDataModule
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.common.box3d_inference_visu import infer_box3d
from xtorch_usecases.multimodal.common.conversion import initialize_model_metadata_json
from xtorch_usecases.multimodal.common.conversion_sample import get_conversion_sample
from xtorch_usecases.multimodal.common.converter import CONVERSION_TARGETS, ConvertArgsType, Converter
from xtorch_usecases.multimodal.common.datasets.dassie.data_module import DassieDataModule
from xtorch_usecases.multimodal.common.hydra_safe_argv import filter_sys_args_for_hydra
from xtorch_usecases.multimodal.common.onnx_slicing_config import OnnxSlicingConfig, get_onnx_slicing_config
from xtorch_usecases.multimodal.common.qnn_calibrator import MultiModalQnnCalibrator
from xtorch_usecases.multimodal.configs.model.release.config import get_release_config
from xtorch_usecases.multimodal.configs.run import MultiModalRunConfig
from xtorch_usecases.multimodal.models.box3d_simple.config import get_run_config_box3d_simple
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.models.gmd.config import get_run_config_gmd_cnn
from xtorch_usecases.multimodal.models.ground_truth.config import get_run_config_ground_truth
from xtorch_usecases.multimodal.models.lanesegnet.config import get_run_lanesegnet_config
from xtorch_usecases.multimodal.models.occupancy.config import get_run_config_occupancy
from xtorch_usecases.multimodal.models.release.checkpoint_loading import load_checkpoint_from_dir
from xtorch_usecases.multimodal.models.sparse_detection.config import get_run_config_sparse_detection
from xtorch_usecases.multimodal.models.training_module import MultiModalUseCaseTrainingModule
from xtorch_usecases.multimodal.tasks.box3d.definitions import SimpleBevBox3d
from xtorch_usecases.multimodal.tasks.box3d.task import FusionSimpleHeadTask, get_quantization_override_dict_box3d
from xtorch_usecases.multimodal.tasks.gmd.definitions import ParkingSpaceInferenceDecoded
from xtorch_usecases.multimodal.tasks.gmd.gmd_inference_visu import infer_gmd
from xtorch_usecases.multimodal.tasks.gmd.task import GmdTask, get_quantization_override_dict_parking_cnn
from xtorch_usecases.multimodal.tasks.ground_truth.extended_model import ExtendedTemporalModel
from xtorch_usecases.multimodal.tasks.ground_truth.multitask_box_3d_fuse import GTBevBox3dInferenceFuse
from xtorch_usecases.multimodal.tasks.ground_truth.remove_raw_predictions import RemoveRawPredictions
from xtorch_usecases.multimodal.tasks.lanesegnet.task import QUANTIZATION_OVERRIDE_DICT_LANESEGNET, LanesegnetTask
from xtorch_usecases.multimodal.tasks.occupancy.task import get_quantization_override_dict_occupancy
from xtorch_usecases.multimodal.tasks.occupancy.utils.inference_visu import infer_occupancy
from xtorch_usecases.multimodal.tasks.sparse_detection.task import (
    SparseDetectionTask,
    get_quantization_override_dict_sparse_detection,
)
from xtorch_usecases.multimodal.utils.pipeline_info_helpers import sync_dataset_versions_with_pipeline_info

_LOGGER = logging.getLogger(__name__)

CONVERSION_TARGET_MAPPING: dict[QnnBackend, CONVERSION_TARGETS] = {QnnBackend.HTP: "qnn_htp", QnnBackend.CPU: "qnn_cpu"}
AUX_NAMES_SPARSE_DETECTION = [
    "fusion_head_output_box_logits_0",
    "fusion_head_output_box_logits_1",
    "fusion_head_output_box_logits_2",
    "fusion_head_output_box_logits_3",
    "fusion_head_output_box_logits_4",
    "fusion_head_output_box_logits_5",
    "fusion_head_output_box_position_0",
    "fusion_head_output_box_position_1",
    "fusion_head_output_box_position_2",
    "fusion_head_output_box_position_3",
    "fusion_head_output_box_position_4",
    "fusion_head_output_box_position_5",
    "fusion_head_output_box_dimension_0",
    "fusion_head_output_box_dimension_1",
    "fusion_head_output_box_dimension_2",
    "fusion_head_output_box_dimension_3",
    "fusion_head_output_box_dimension_4",
    "fusion_head_output_box_dimension_5",
    "fusion_head_output_box_orientation_0",
    "fusion_head_output_box_orientation_1",
    "fusion_head_output_box_orientation_2",
    "fusion_head_output_box_orientation_3",
    "fusion_head_output_box_orientation_4",
    "fusion_head_output_box_orientation_5",
    "fusion_head_output_box_velocity_0",
    "fusion_head_output_box_velocity_1",
    "fusion_head_output_box_velocity_2",
    "fusion_head_output_box_velocity_3",
    "fusion_head_output_box_velocity_4",
    "fusion_head_output_box_velocity_5",
    "fusion_head_output_track_indices",
]
AUX_NAMES_SPARSE_DETECTION_TV = [
    "camera_head_outputs_0_class_logits",
    "camera_head_outputs_0_centerness_logits",
    "camera_head_outputs_0_bboxes",
    "camera_head_outputs_0_centers",
    "camera_head_outputs_0_topk_indices",
    "camera_head_outputs_1_class_logits",
    "camera_head_outputs_1_centerness_logits",
    "camera_head_outputs_1_bboxes",
    "camera_head_outputs_1_centers",
    "camera_head_outputs_1_topk_indices",
]


@runtime_checkable
class ExpectedConfigChangeProvider(Protocol):
    """Protocol for classes that provide expected configuration changes for conversion.

    This protocol defines a method to retrieve a list of expected configuration changes
    between training and conversion configurations.
    """

    def identifier(self) -> str:
        """Get the identifier of the expected config change provider.

        Returns:
            Identifier string for the expected config change provider.
        """
        ...

    def get_expected_config_changes(
        self,
        producer_step: PipelineStep,
        producer_config: DictConfig | ListConfig,
        consumer_step: PipelineStep,
        consumer_config: DictConfig | ListConfig,
    ) -> list[str]:
        """Get expected config changes between two pipeline steps.

        Args:
            producer_step: The pipeline step that produces the configuration.
            producer_config: The configuration produced by the producer step.
            consumer_step: The pipeline step that consumes the configuration.
            consumer_config: The configuration consumed by the consumer step.

        Returns:
            List of elements where a change in the configurations is expected to be a regex pattern.
            Elements are identified by dotted paths such as "data_config.batch_size" and no difference is being made
            between member variables and containers such as dictionaries or lists. For containers that are not indexed
            by a string, the string representation of the key is expected, e.g. "data_config.datasets.0.name". Strings
            are interpreted as exact matches.
        """
        ...


def _generate_input_conversion_config(
    full_model_config: dict[str, InputTensorConfig], sub_graph_inputs: set[str]
) -> list[InputTensorConfig]:
    """Generate the qnn conversion input tensor config for the subgraph.

    Args:
        full_model_config: qnn conversion input tensor config by tensor name for the full model
        sub_graph_inputs: input names of the subgraph

    Returns:
        List of all input tensors for the subgraph, with corresponding qnn conversion parameters._
    """
    sub_graph_inputs_config = [full_model_config[name] for name in sub_graph_inputs if name in full_model_config]
    # Add configs for any subgraph inputs not present in the full model config
    additional_inputs = sub_graph_inputs - full_model_config.keys()
    # It is assumed that the additional input is InputTensorType.IMAGE
    sub_graph_inputs_config.extend(
        InputTensorConfig(name=name, input_type=InputTensorType.IMAGE) for name in additional_inputs
    )
    return sub_graph_inputs_config


def differences_to_training_config_are_expected(
    training_step_config: DictConfig | ListConfig,
    conversion_step_config: DictConfig | ListConfig,
    task_list: Iterable[ExpectedConfigChangeProvider],
) -> bool:
    """Check if the training config is compatible with the conversion config."""
    deep_diff = DeepDiff(training_step_config, conversion_step_config, ignore_order=True)

    expected_diffs = [
        "task_configs\\.[a-z0-9_]+\\.combined_metrics_yaml\\..*",
        "task_configs\\.[a-z0-9_]+\\.evaluation_folder\\..*",
        re.escape("common_config.callback_config.output_root_dir") + ".*",
        re.escape("convert_config") + ".*",
        re.escape("data_config.alliance_dataset_names") + ".*",
        re.escape("data_config.batch_size") + ".*",
        re.escape("eval_config") + ".*",
        re.escape("pipeline_step_paths") + ".*",
        re.escape("trainer.default_root_dir") + ".*",
        re.escape("trainer.strategy"),
        re.escape("training_config.output_folder") + ".*",
        re.escape("training_config.radar_encoder_params.use_voxelizer"),
        re.escape("training_config.weight_init_checkpoint_paths") + ".*",
    ]

    producer_step = (
        PipelineStep.TRAIN
        if not conversion_step_config.convert_config.checkpoints_are_finetuned
        else PipelineStep.TRAIN_FINETUNE
    )

    any_errors = False
    for task in task_list:
        expected_diffs_of_task = task.get_expected_config_changes(
            producer_step, training_step_config, PipelineStep.CONVERT, conversion_step_config
        )

        for expected_diff in expected_diffs_of_task:
            try:
                re.compile(expected_diff)
            except re.error as e:  # noqa: PERF203
                _LOGGER.error(  # noqa: TRY400
                    "Failed to compile regex '%s' from task %s: %s", expected_diff, task.identifier(), e
                )
                any_errors = True

        expected_diffs.extend(expected_diffs_of_task)

    if any_errors:
        error_message = "Could not check expected differences to training config due to regex errors."
        raise RuntimeError(error_message)

    expected_diffs_regex = re.compile("|".join("(?:" + expected_diff + ")" for expected_diff in expected_diffs))

    unexpected_differences = []
    for diffs_per_type in deep_diff.values():
        for diff_path in diffs_per_type:
            dotted_path = ".".join(str(x) for x in parse_path_deepdiff(diff_path))
            if expected_diffs_regex.fullmatch(dotted_path):
                continue
            unexpected_differences.append(dotted_path)

    if unexpected_differences:
        _LOGGER.error(
            "Found unexpected differences to training config. Please check the config or rerun with "
            "`convert_config.skip_config_check=True`."
        )
        _LOGGER.error("Unexpected differences:")
        for difference in unexpected_differences:
            _LOGGER.error(" %s", difference)

    return len(unexpected_differences) == 0


def subgraph_preparation(
    root_dir: Path,
    model_onnx_config: OnnxSlicingConfig,
    qnn_full_model_artifacts: dict[str, QnnModelArtifacts],
    run_config: MultiModalRunConfig,
    datamodule_class: type[CombinedDataModule | DassieDataModule] = CombinedDataModule,
    *,
    enable_detailed_profiling: bool = False,
    additional_convert_kwargs: ConvertArgsType | None = None,
    inputs_config: dict[str, InputTensorConfig] | None = None,
) -> None:
    """Extracts the sub-graph and performs the Qnn conversions on them.

    Args:
        root_dir: Output folder path for conversion artifacts.
        model_onnx_config: Dictionary containing information on sub-graph splitting.
        qnn_full_model_artifacts: Full model QNN artifacts (htp backend) for sub-graph conversion.
        run_config: Lighting Run configuration.
        datamodule_class: Data module class to be used for conversion.
        enable_detailed_profiling: if detailed/optrace profiling is enabled during conversion
        additional_convert_kwargs: Additional kwargs for converter
        inputs_config: All input tensors for the full model, with corresponding qnn conversion parameters
    """
    if additional_convert_kwargs is None:
        additional_convert_kwargs = {}

    # Use qnn_htp model with surgery rules applied, for accurate overrides generation (incase of structural changes)
    # So, find onnx from qnn_full_model_artifacts
    htp_qnn_artifacts = qnn_full_model_artifacts["htp"]
    full_model_path = htp_qnn_artifacts._find_file(htp_qnn_artifacts.model_name_with_backend() + ".onnx")  # noqa: SLF001

    target_mapping: dict[str, CONVERSION_TARGETS] = {"htp": "qnn_htp", "cpu": "qnn_cpu"}
    for subgr_name, sub_config in model_onnx_config.subgraphs.items():
        _LOGGER.info(f"Processing sub-graph: {subgr_name}")
        sub_conversion_folder = root_dir / f"conversion_{subgr_name}"
        sub_conversion_folder.mkdir(exist_ok=True)

        # sub-graph Onnx extraction
        sub_model_name = subgr_name
        sub_model_path = sub_conversion_folder / f"{sub_model_name}.onnx"
        onnx.utils.extract_model(
            full_model_path.as_posix(),
            sub_model_path.as_posix(),
            input_names=sub_config["inputs"],
            output_names=sub_config["outputs"],
        )

        # prepare input tensor qnn conversion parameters for sub-graph
        sub_graph_inputs_config = None
        if inputs_config is not None:
            sub_graph_inputs_config = _generate_input_conversion_config(
                full_model_config=inputs_config, sub_graph_inputs=set(sub_config["inputs"])
            )

        for target, model_artifacts in qnn_full_model_artifacts.items():
            # sub-graph Qnn conversion
            # Note: Must not pass src_quantized_model for cpu model because it will
            #       trigger an error about quantization overrides.

            # Initialized model metadata and required input model grouping
            model_metadata = initialize_model_metadata_json(
                subgr_name,
                subgr_name,
                sub_config["outputs"],
                attributes={
                    "config": OmegaConf.to_container(
                        OmegaConf.structured(run_config),
                        resolve=True,
                        enum_to_str=True,
                        structured_config_mode=SCMode.DICT,
                    )
                },
            )

            sub_model = onnx.load(sub_model_path.as_posix())
            converter = Converter(sub_model_name)
            converter.convert(
                model=sub_model,
                model_name=sub_model_name,
                conversion_folder=sub_conversion_folder,
                conversion_target=[target_mapping[target]],
                model_metadata=model_metadata,
                calibrators={
                    "qnn_htp": MultiModalQnnCalibrator(run_config=run_config, datamodule_class=datamodule_class)
                }
                if target == "htp"
                else None,
                src_quantized_model=model_artifacts if target == "htp" else None,
                label_class_names=run_config.training_config.label_class_names,
                skip_quantization_check=True,
                enable_detailed_profiling=enable_detailed_profiling,
                additional_convert_kwargs=additional_convert_kwargs,
                inputs_config=sub_graph_inputs_config,
            )


def _get_tensor_names(onnx_model_path: Path) -> set[str]:
    onnx_model = onnx.load(onnx_model_path.as_posix())
    graph = gs.import_onnx(onnx_model)
    return set(graph.tensors().keys())


def create_override_dict(
    conversion_folder: Path,
    override_dict_name: str,
    quantization_override_dict: dict[str, Any],
) -> Path:
    """Create quantization override dict for mm bev model.

    Args:
        conversion_folder: Path to conversion folder where the override will be saved
        override_dict_name: Name of the override dict
        quantization_override_dict: Dictionary containing the quantization overrides

    Returns:
        Path to quantization override dict for qnn-onnx-converter.
    """

    conversion_folder.mkdir(parents=True, exist_ok=True)
    quantization_override_path = conversion_folder / f"{override_dict_name}.json"
    with quantization_override_path.open("w", encoding="utf-8") as override_fp:
        json.dump(quantization_override_dict, override_fp, indent=4)
    return quantization_override_path


def _resolve_names(
    names: dict[str | tuple[str, ...], Any], available_names: set[str] | None, fallback: int
) -> dict[str, Any]:
    target: dict[str, Any] = {}

    for key, value in names.items():
        if isinstance(key, str):
            target[key] = value
            continue
        assert isinstance(key, tuple), f"Key must be a string or a tuple not {type(key)}"
        if len(key) == 1:
            target[key[0]] = value
            continue

        if available_names is None:
            msg = f"Ambiguous key {key} provided but no onnx model"
            raise ValueError(msg)

        for candidate in key:
            if candidate in available_names:
                target[candidate] = value
                break
        else:
            _LOGGER.warning(f"None of the keys {key} found in available names, falling back to {fallback}")
            target[key[fallback]] = value

    return target


# TODO: Move into Converter, when Task class expanded to contain quantization info.
# https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/336897
def prepare_quantization_overrides(
    conversion_folder: Path,
    conversion_targets: list[CONVERSION_TARGETS],
    quantization_override_dict: dict[str, Any],
    onnx_model: onnx.ModelProto | None = None,
    fallback: int = 0,
) -> dict[str, Path]:
    """Creates initial quantization override files for each Qnn target.

    Args:
        conversion_folder: Path for conversion output artifacts.
        conversion_targets: List of conversion targets
        quantization_override_dict: Dictionary containing the quantization overrides
        onnx_model: Onnx model to be used for quantization overrides generation
        fallback: If node not found in onnx model, use fallback index

    Returns:
        Path to created quantization overrides for each target
    """

    available_names: set[str] | None = None
    if onnx_model is not None:
        available_names = set()
        for node in onnx_model.graph.node:
            for input_name in node.input:
                available_names.add(input_name)
            for output_name in node.output:
                available_names.add(output_name)

    quant_override_paths = {}
    for target in conversion_targets:
        if "qnn" not in target:
            continue

        quantization_override_dict_selected: dict[str, Any] = {}
        if Version(quantization_override_dict["version"]) >= Version("1.0.0"):
            quantization_override_dict_selected = quantization_override_dict
        else:
            for key, value in quantization_override_dict.items():
                if key in get_args(ENCODING_GROUPS):
                    quantization_override_dict_selected[key] = _resolve_names(value, available_names, fallback)
                else:
                    quantization_override_dict_selected[key] = value

        quantization_override_path = create_override_dict(
            conversion_folder, f"quantization_override_dict_{target}", quantization_override_dict_selected
        )
        quant_override_paths[target] = quantization_override_path

    return quant_override_paths


def main(argv: list[str]) -> None:  # noqa: PLR0911
    """Main function."""
    coloredlogs.install(level=logging.INFO)

    log_environment()

    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str.lower, choices=[m.value for m in Model], default=Model.BOX3D_SIMPLE)
    args, unknown_args = parser.parse_known_args(argv)

    if args.model == Model.BOX3D_SIMPLE:
        # TODO: config handling like in MNIST example
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/328455
        config, unknown_args = get_run_config_box3d_simple(unknown_args, pipeline_step=PipelineStep.CONVERT)
        config_store = ConfigStore.instance()
        config_store.store("box3d_simple_config", node=config)
        filter_sys_args_for_hydra(unknown_args)
        hydra_main_box3d_simple()
        return

    if args.model == Model.SPARSE_DETECTION:
        config, unknown_args = get_run_config_sparse_detection(unknown_args, pipeline_step=PipelineStep.CONVERT)
        config_store = ConfigStore.instance()
        config_store.store("sparse_detection_config", node=config)
        filter_sys_args_for_hydra(unknown_args)
        hydra_main_sparse_detection()
        return

    if args.model == Model.OCCUPANCY:
        config, unknown_args = get_run_config_occupancy(unknown_args, pipeline_step=PipelineStep.CONVERT)

        config_store = ConfigStore.instance()
        config_store.store("occupancy_config", node=config)

        filter_sys_args_for_hydra(unknown_args)
        hydra_main_occupancy()
        return

    if args.model == Model.LANESEGNET:
        # TODO: config handling like in MNIST example
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/328455
        config, unknown_args = get_run_lanesegnet_config(unknown_args, pipeline_step=PipelineStep.CONVERT)

        config_store = ConfigStore.instance()
        config_store.store("lanesegnet_config", node=config)

        filter_sys_args_for_hydra(unknown_args)
        hydra_main_lanesegnet()
        return

    if args.model == Model.RELEASE:
        # TODO: config handling like in MNIST example
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/328455
        config, unknown_args = get_release_config(pipeline_step=PipelineStep.CONVERT, args_list=unknown_args)

        config_store = ConfigStore.instance()
        config_store.store("release_config", node=config)

        filter_sys_args_for_hydra(unknown_args)
        hydra_main_release()
        return

    if args.model == Model.GROUND_TRUTH:
        # TODO: config handling like in MNIST example
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/328455
        config, unknown_args = get_run_config_ground_truth(unknown_args, pipeline_step=PipelineStep.CONVERT)

        config_store = ConfigStore.instance()
        config_store.store("ground_truth_config", node=config)

        filter_sys_args_for_hydra(unknown_args)
        hydra_main_ground_truth()
        return

    if args.model == Model.GMD_CNN:
        config, unknown_args = get_run_config_gmd_cnn(unknown_args, pipeline_step=PipelineStep.CONVERT)
        config_store = ConfigStore.instance()
        config_store.store("gmd_parking_config", node=config)
        filter_sys_args_for_hydra(unknown_args)
        hydra_main_gmd_parking()
        return

    raise NotImplementedError


def run_convert_pipeline(  # noqa: C901, PLR0912, PLR0915
    config: MultiModalRunConfig,
    training_module: MultiModalUseCaseTrainingModule | nn.Module,
    datamodule_class: type[CombinedDataModule | DassieDataModule],
    aux_names: dict[str, Any],
    quant_override: dict[str, Any],
    *,
    task_ids: list[str] | None = None,
    additional_convert_kwargs: ConvertArgsType | None = None,
) -> None:
    """Run the convert pipeline.

    Args:
        config: Run configuration.
        training_module: Training module or the model itself to be used for conversion.
        datamodule_class: Data module class to be used for conversion.
        aux_names: Auxiliary names for the conversion.
        quant_override: Quantization override dictionary.
        task_ids: Task IDs for the conversion.
        additional_convert_kwargs: Additional kwargs for converter.
    """
    if additional_convert_kwargs is None:
        additional_convert_kwargs = {}
    log_configuration(name=__file__, config=config)
    save_config(config, config.pipeline_step_paths.summaries / "composed_config.yaml")

    sync_dataset_versions_with_pipeline_info(config)

    if not config.convert_config.skip_config_check:
        # Load the current config from file for comparison to account for any changes done during save, e.g. enum values
        # converted to strings.
        conversion_step_config = OmegaConf.load(config.pipeline_step_paths.summaries / "composed_config.yaml")
        training_step_config = OmegaConf.load(config.pipeline_step_paths.train_summaries / "composed_config.yaml")
        if not differences_to_training_config_are_expected(
            training_step_config,
            conversion_step_config,
            (
                task
                for task in config.multi_task_collection.enabled_tasks
                if isinstance(task, ExpectedConfigChangeProvider)
            ),
        ):
            msg = "Found unexpected differences to training config. Please check the config."
            raise RuntimeError(msg)

    task_ids = config.multi_task_collection.enabled_task_ids if task_ids is None else task_ids
    taks_ids_occupancy = [
        task_id for task_id in config.multi_task_collection.enabled_task_ids if "occupancy" in task_id
    ]

    conversion_sample = get_conversion_sample(config, datamodule_class=datamodule_class)
    onnx_slicing_config = get_onnx_slicing_config(
        sample=conversion_sample,
        model=config.model,
        task_ids=task_ids,
        camera_encoder_split_config=config.training_config.camera_encoder_split_config,
        onnx_export_aux_heads=config.convert_config.onnx_export_aux_heads,
        radar_model=config.training_config.radar_encoder_params.model_type,
        occupancy_dualgrid=config.model in [Model.OCCUPANCY, Model.RELEASE] and len(taks_ids_occupancy) > 1,
    )

    overwrite_output_names = []
    for task_id in task_ids:
        overwrite_output_names.extend(aux_names[task_id] + onnx_slicing_config.subgraphs[task_id]["outputs"])

    conversion_config = config.convert_config

    if config.convert_config.convert_from_onnx_path is None:
        # TODO: shall we use a different checkpoint for conversion?
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/335079
        # TODO: refactor with evaluate stage together
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/335079

        eval_checkpoint_path = load_checkpoint_from_dir(
            checkpoints_dir=config.pipeline_step_paths.checkpoints,
            checkpoint_file=config.eval_config.checkpoint,
            try_find_best_ckpt=config.eval_config.try_find_best_ckpt,
            ckpt_name=config.ckpt_name,
        )
        checkpoint_file = None
        if eval_checkpoint_path is not None:
            checkpoint_file = CheckpointFile(eval_checkpoint_path)

            with checkpoint_file.fetch() as local_pretrained_weights:
                checkpoint = torch.load(
                    local_pretrained_weights.path, map_location=checkpoint_file.map_location, weights_only=False
                )

                old_state_dict = checkpoint["state_dict"]
                new_state_dict = {
                    key.replace("._task_heads.sparse_detection", "._temporal_task_heads.sparse_detection"): value
                    for key, value in old_state_dict.items()
                }

                # NOTE: Filter out _loss_fn when training_module is not a MultiModalUseCaseTrainingModule
                if not isinstance(training_module, MultiModalUseCaseTrainingModule):
                    new_state_dict = {k: v for k, v in new_state_dict.items() if "_loss_fn" not in k}

                missing_keys, unexpected_keys = training_module.load_state_dict(
                    new_state_dict, strict=config.training_config.strict_loading
                )

                _LOGGER.info(
                    f"Tried to load {len(new_state_dict)} weights:"
                    f"\n- Missing keys (len={len(missing_keys)}): {missing_keys}"
                    f"\n- Unexpected keys (len={len(unexpected_keys)}): {unexpected_keys}"
                )

            _LOGGER.info("✅  Checkpoint loaded.")
            checkpoint_file = None

        # ____________________________________________ ONNX conversion _________________________________________________
        _LOGGER.info("Start onnx conversion.")

        # Currently only passing an empty initial state works (addressing TemporalMultiTask with a
        # non-temporal Task)
        # If the state is properly initialized, OnnxInferenceBackend is presumably having an issue because it does not
        # set/handle the state.
        temporal_state_sample: dict[str, Any] = {}

        intermediate_export_file: Path = (
            conversion_config.raw_onnx_model_path.parent
            / "export"
            / f"{conversion_config.raw_onnx_model_path.stem}.export.onnx"
        )
        export_to_onnx(
            training_module=training_module,
            checkpoint=checkpoint_file,
            export_config=ExportConfig(
                export_path=intermediate_export_file.parent,
                opset_version=conversion_config.onnx_opset,
                onnx_file_name=intermediate_export_file.name,
                onnx_export_dynamic_batch_axis=conversion_config.onnx_export_dynamic_batch_axis,
            ),
            input_tensors=(conversion_sample, temporal_state_sample),  # type: ignore[reportArgumentType]
            overwrite_output_names=overwrite_output_names,
            strict_loading=config.training_config.strict_loading,
        )

        cleanup_and_optimize_onnx(
            intermediate_export_file,
            conversion_config.raw_onnx_model_path,
            qnn_optimize=not conversion_config.skip_qnn_conversion,
        )
        cleaned_tensors_names = set()
    else:
        _initial_tensors_names = _get_tensor_names(config.convert_config.convert_from_onnx_path)
        cleanup_and_optimize_onnx(
            config.convert_config.convert_from_onnx_path,
            conversion_config.raw_onnx_model_path,
            qnn_optimize=not conversion_config.skip_qnn_conversion,
        )
        _opt_tensors_names = _get_tensor_names(conversion_config.raw_onnx_model_path)
        cleaned_tensors_names = _initial_tensors_names - _opt_tensors_names

    # run inference and visu using raw onnx model
    if not config.convert_config.skip_inference:
        # 3D box usecase specific inference. Needs to be skipped for other heads.
        if config.model in (Model.BOX3D_SIMPLE, Model.SPARSE_DETECTION, Model.GROUND_TRUTH, Model.RELEASE):
            task_id_for_visu = "sparse_detection" if config.model == Model.RELEASE else task_ids[0]
            _LOGGER.info(f"Start '{config.model}' ONNX Smoketests and Visu.")
            infer_box3d(
                config=config,
                task_id=task_id_for_visu,
                backend_engine="onnx",
                max_num_samples=conversion_config.num_visualization_samples,
                objectness_threshold=conversion_config.visualization_objectness_threshold,
            )
        elif config.model == Model.GMD_CNN:
            _LOGGER.info(f"Start '{config.model}' ONNX Smoketests and Visu.")
            infer_gmd(
                config=config,
                task_id=task_ids[0],
                backend_engine="onnx",
                max_num_samples=conversion_config.num_visualization_samples,
                objectness_threshold=conversion_config.visualization_objectness_threshold,
            )
        else:
            _LOGGER.error(f"ONNX inference not supported for this task: {config.model}")

    # _______________________________________________ QNN conversion ______________________________________________
    if not conversion_config.skip_qnn_conversion:
        _LOGGER.info("Start QNN conversion.")

        model_name = conversion_config.patched_onnx_model_path.stem

        # Setup conversion paths and folders
        conversion_full_folder = conversion_config.default_root_dir / "full"
        conversion_full_folder.mkdir(exist_ok=True)

        # Prepare Onnx models for conversion, full and splitted
        # -----------------------------------------------------
        # Rename some tensor in Onnx
        name_mapping_old_new = {v: k for k, v in onnx_slicing_config.patched_full["renaming"].items()}
        rename_onnx_tensors(
            conversion_config.raw_onnx_model_path.as_posix(),
            conversion_config.renamed_model_path.as_posix(),
            name_mapping_old_new,
        )

        # (Radar input adapted) Full model preparation with voxelized radar inputs
        renamed_model_tensor_names = _get_tensor_names(conversion_config.renamed_model_path)
        onnx.utils.extract_model(
            conversion_config.renamed_model_path.as_posix(),
            conversion_config.patched_onnx_model_path.as_posix(),
            input_names=onnx_slicing_config.patched_full["inputs"],
            output_names=onnx_slicing_config.patched_full["outputs"],
        )
        extracted_model_tensor_names = _get_tensor_names(conversion_config.patched_onnx_model_path)

        # Conversion of models
        # --------------------
        conversion_targets: list[CONVERSION_TARGETS] = [
            CONVERSION_TARGET_MAPPING[k] for k in conversion_config.qnn_backends
        ]

        # Prepare quantization overrides
        onnx_encodings_path = (
            config.convert_config.convert_from_onnx_path.with_suffix(".encodings")
            if config.convert_config.convert_from_onnx_path
            else None
        )

        model_full = onnx.load(conversion_config.patched_onnx_model_path.as_posix())

        _merge_encodings_with_quant_overrides(
            model_full,
            quant_override,
            onnx_encodings_path,
            cleaned_tensors_names | (renamed_model_tensor_names - extracted_model_tensor_names),
            name_mapping_old_new,
        )

        quant_override_paths = prepare_quantization_overrides(
            conversion_full_folder,
            conversion_targets,
            quant_override,
            onnx_model=model_full,
            fallback=-1 if config.convert_config.convert_from_onnx_path else 0,
        )

        # Initialized model metadata and required input model grouping
        model_metadata = initialize_model_metadata_json(
            config.model,
            config.model,
            onnx_slicing_config.patched_full["outputs"],
            attributes={
                "config": OmegaConf.to_container(
                    OmegaConf.structured(config), resolve=True, enum_to_str=True, structured_config_mode=SCMode.DICT
                )
            },
        )

        # Full model Qnn conversion
        qnn_calibrator = MultiModalQnnCalibrator(run_config=config, datamodule_class=datamodule_class)

        converter = Converter(model_name, run_config=config)
        converter.convert(
            model=model_full,
            model_name=model_name,
            conversion_folder=conversion_full_folder,
            conversion_target=conversion_targets,
            model_metadata=model_metadata,
            quant_check_config=conversion_config.quant_check_config,
            qnn_quantization_override_paths=quant_override_paths,
            calibrators={"qnn_htp": qnn_calibrator},
            sample=conversion_sample,
            label_class_names=config.training_config.label_class_names,
            skip_quantization_check=config.convert_config.skip_quantization_check,
            enable_detailed_profiling=(config.eval_config.profiling_level == QnnProfilingLevel.DETAILED),
            additional_convert_kwargs=additional_convert_kwargs,
            inputs_config=conversion_config.inputs_config,
        )

        # Sub-graph Onnx Extraction and Qnn conversion
        if config.convert_config.skip_subgraph_preparation:
            _LOGGER.info("Skipping sub-graph preparation.")
        else:
            full_config = conversion_config.inputs_config
            if full_config is not None:
                full_config = {config.name: config for config in full_config}
            subgraph_preparation(
                conversion_config.default_root_dir,
                onnx_slicing_config,
                converter.qnn_conversion_artifacts,
                run_config=config,
                datamodule_class=datamodule_class,
                enable_detailed_profiling=(config.eval_config.profiling_level == QnnProfilingLevel.DETAILED),
                additional_convert_kwargs=additional_convert_kwargs,
                inputs_config=full_config,
            )

    # Inference on QNN artifacts
    if not config.convert_config.skip_inference:
        inference_full_model = config.convert_config.skip_subgraph_preparation or config.eval_config.eval_full_model

        # Check if artifacts exists, incase Qnn conversion skipped
        for qnn_backend in conversion_config.qnn_backends:
            try:
                if inference_full_model:
                    qnn_artifacts = QnnModelArtifacts(
                        config.convert_config.default_root_dir / "full", None, qnn_backend
                    )
                    qnn_artifacts.model_path()  # Throws exception if model not found
                else:
                    for subgraph_name in onnx_slicing_config.subgraphs:
                        qnn_artifacts = QnnModelArtifacts(
                            config.convert_config.default_root_dir / f"conversion_{subgraph_name}", None, qnn_backend
                        )
                        qnn_artifacts.model_path()  # Throws exception if model not found
            except FileNotFoundError:  # noqa: PERF203
                _LOGGER.warning("Qnn model artifacts not found, skipping inference!")
                return

        if config.model in (Model.BOX3D_SIMPLE, Model.SPARSE_DETECTION, Model.RELEASE):
            for qnn_backend in conversion_config.qnn_backends:
                if qnn_backend not in [QnnBackend.CPU, QnnBackend.HTP]:
                    continue
                _LOGGER.info(f"Start '{config.model}' {qnn_backend} Smoketests and Visu for 3D box detection.")
                task_id_for_visu = "sparse_detection" if config.model == Model.RELEASE else task_ids[0]
                infer_box3d(
                    config=config,
                    task_id=task_id_for_visu,
                    full_model=inference_full_model,
                    onnx_slicing_config=onnx_slicing_config,
                    backend_engine=cast(Literal[QnnBackend.CPU, QnnBackend.HTP], qnn_backend),
                    max_num_samples=conversion_config.num_visualization_samples,
                    objectness_threshold=conversion_config.visualization_objectness_threshold,
                )

        if config.model in (Model.OCCUPANCY, Model.RELEASE):
            _LOGGER.info(f"Start '{config.model}' QNN Model Smoketests and Visu for occupancy.")
            task_ids_for_visu = [
                task.identifier()
                for task in config.multi_task_collection.enabled_tasks
                if "occupancy" in task.identifier()
            ]
            infer_occupancy(
                config=config,
                task_ids=task_ids_for_visu,
                full_model=inference_full_model,
                onnx_slicing_config=onnx_slicing_config,
            )

        if config.model == Model.GMD_CNN:
            for qnn_backend in conversion_config.qnn_backends:
                if qnn_backend not in [QnnBackend.CPU, QnnBackend.HTP]:
                    continue
                _LOGGER.info(f"Start '{config.model}' {qnn_backend} Smoketests and Visu.")
                infer_gmd(
                    config=config,
                    task_id=task_ids[0],
                    full_model=inference_full_model,
                    backend_engine=cast(Literal[QnnBackend.CPU, QnnBackend.HTP], qnn_backend),
                    max_num_samples=conversion_config.num_visualization_samples,
                    objectness_threshold=conversion_config.visualization_objectness_threshold,
                    onnx_slicing_config=onnx_slicing_config,
                )

        if config.model not in (
            Model.BOX3D_SIMPLE,
            Model.SPARSE_DETECTION,
            Model.OCCUPANCY,
            Model.RELEASE,
            Model.GMD_CNN,
        ):
            _LOGGER.error(f"QNN inference not supported for this model: {config.model}")


def _merge_encodings_with_quant_overrides(  # noqa: C901, PLR0912
    onnx_model: onnx.ModelProto,
    quant_override: dict[str, Any],
    onnx_encodings_path: Path | None,
    removed_tensors: set[str],
    name_mapping_old_new: dict[str | tuple[str, ...], str],
) -> None:
    """Merge encodings with quantization overrides."""

    if onnx_encodings_path is None or not onnx_encodings_path.is_file():
        return

    name_mapping_flat = {}
    for k, v in name_mapping_old_new.items():
        if isinstance(k, tuple):
            for kk in k:
                name_mapping_flat[kk] = v
        else:
            name_mapping_flat[k] = v

    onnx_encodings = json.loads(onnx_encodings_path.read_text())

    if Version(onnx_encodings["version"]) != Version(quant_override["version"]):
        _LOGGER.warning(
            f"Encodings file version {onnx_encodings['version']} does not match quantization override version "
            f"{quant_override['version']}. Please update the encodings file to use it for QNN conversion."
        )
        return

    if Version(onnx_encodings["version"]) >= Version("1.0.0"):
        quant_override["activation_encodings"] += onnx_encodings["activation_encodings"]
        quant_override["param_encodings"] += onnx_encodings["param_encodings"]
        quant_override["quantizer_args"] = onnx_encodings["quantizer_args"]
    else:
        for k, v in onnx_encodings.items():
            if k in ["version"]:
                continue
            if k not in quant_override:
                quant_override[k] = v
            else:
                for kk, vv in v.items():
                    kk_new = name_mapping_flat.get(kk, kk)
                    if kk_new not in quant_override[k] and kk_new not in removed_tensors:
                        quant_override[k][kk_new] = vv

    # AIMET QAT sometimes put param overrides into activation encodings.
    move_param_quantization_overrides(onnx_model, quant_override)


@hydra.main(version_base=None, config_name="box3d_simple_config")
def hydra_main_box3d_simple(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the box3d simple usecase."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    config.data_config.dataset_name = "alliance"  # use alliance data for calibration and qnn evaluation
    training_module = MultiModalUseCaseTrainingModule(
        config.training_config, config.multi_task_collection.enabled_tasks
    )
    quant_override = get_quantization_overrides(config)
    task_id = FusionSimpleHeadTask.identifier()
    aux_names = {}
    aux_names[task_id] = [f"aux_{n}" for n in SimpleBevBox3d._fields]
    run_convert_pipeline(
        config=config,
        training_module=training_module,
        datamodule_class=CombinedDataModule,
        aux_names=aux_names,
        quant_override=quant_override,
    )


@hydra.main(version_base=None, config_name="sparse_detection_config")
def hydra_main_sparse_detection(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the sparse_detection usecase."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    config.data_config.dataset_name = "alliance"  # use alliance data for calibration and qnn evaluation
    training_module = MultiModalUseCaseTrainingModule(
        config.training_config, config.multi_task_collection.enabled_tasks
    )
    quant_override = get_quantization_overrides(config)
    task_id = SparseDetectionTask.identifier()

    aux_names = {}
    aux_names[task_id] = AUX_NAMES_SPARSE_DETECTION
    if InputDataId.CAMERA_TV in config.data_config.enabled_input_modalities:
        assert InputDataId.CAMERA_FC1 in config.data_config.enabled_input_modalities, "Camera FC1 must be enabled."
        aux_names[task_id] += AUX_NAMES_SPARSE_DETECTION_TV

    run_convert_pipeline(
        config=config,
        training_module=training_module,
        datamodule_class=CombinedDataModule,
        aux_names=aux_names,
        quant_override=quant_override,
    )


@hydra.main(version_base=None, config_name="occupancy_config")
def hydra_main_occupancy(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the occupancy usecase."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    training_module = MultiModalUseCaseTrainingModule(
        config.training_config, config.multi_task_collection.enabled_tasks
    )
    task_id = [task.identifier() for task in config.multi_task_collection.enabled_tasks]
    quant_override = get_quantization_overrides(config)
    aux_names = {}
    for task in task_id:
        if task == "occupancy":
            aux_names[task] = ["aux_height_map", "aux_semantic_logits", "aux_visibility_logits"]
        elif task == "occupancy_near_range":
            aux_names[task] = ["aux_height_map_near", "aux_semantic_logits_near", "aux_visibility_logits_near"]
    run_convert_pipeline(
        config=config,
        training_module=training_module,
        datamodule_class=CombinedDataModule,
        aux_names=aux_names,
        quant_override=quant_override,
    )


@hydra.main(version_base=None, config_name="lanesegnet_config")
def hydra_main_lanesegnet(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the lanesegnet usecase."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    config.data_config.batch_size_train = 1  # conversion only works with batch size 1
    config.data_config.batch_size_val = 1
    training_module = MultiModalUseCaseTrainingModule(
        config.training_config, config.multi_task_collection.enabled_tasks
    )
    quant_override = get_quantization_overrides(config)
    task_id = LanesegnetTask.identifier()
    aux_names = {task_id: []}
    additional_convert_kwargs: ConvertArgsType = {"qnn_htp": {"use_per_channel_quantization": False}}
    run_convert_pipeline(
        config=config,
        training_module=training_module,
        datamodule_class=DassieDataModule,
        aux_names=aux_names,
        quant_override=quant_override,
        additional_convert_kwargs=additional_convert_kwargs,
    )


@hydra.main(version_base=None, config_name="release_config")
def hydra_main_release(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the release model."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    training_module = MultiModalUseCaseTrainingModule(
        config.training_config, config.multi_task_collection.enabled_tasks
    )
    aux_names = {}

    # occupancy task ids and quantization overrides
    task_ids_occupancy = [
        task.identifier()
        for task in config.multi_task_collection.enabled_tasks
        if task.identifier() in ["occupancy", "occupancy_near_range"]
    ]
    assert task_ids_occupancy, "Occupancy task not found in enabled tasks for release model."
    quant_override_occupancy = get_quantization_override_dict_occupancy(
        task_ids=task_ids_occupancy,
        num_cams=config.training_config.num_cams,
        enabled_input_modalities=config.training_config.enabled_input_modalities,
    )
    for task in task_ids_occupancy:
        if task == "occupancy":
            aux_names[task] = ["aux_height_map", "aux_semantic_logits", "aux_visibility_logits"]
        elif task == "occupancy_near_range":
            aux_names[task] = ["aux_height_map_near", "aux_semantic_logits_near", "aux_visibility_logits_near"]

    # sparse detection task id and quantization overrides
    task_id_sparse_detection = (
        SparseDetectionTask.identifier()
        if SparseDetectionTask.identifier() in config.multi_task_collection.enabled_task_ids
        else None
    )
    assert task_id_sparse_detection, "Sparse detection task not found in enabled tasks for release model."
    quant_override_sparse_detection = get_quantization_override_dict_sparse_detection(
        qat=config.convert_config.convert_from_onnx_path is not None
    )
    aux_names[task_id_sparse_detection] = AUX_NAMES_SPARSE_DETECTION
    if InputDataId.CAMERA_TV in config.data_config.enabled_input_modalities:
        assert InputDataId.CAMERA_FC1 in config.data_config.enabled_input_modalities, "Camera FC1 must be enabled."
        aux_names[task_id_sparse_detection] += AUX_NAMES_SPARSE_DETECTION_TV

    # combine task ids and quantization overrides
    task_id = [*task_ids_occupancy, task_id_sparse_detection] if task_id_sparse_detection else task_ids_occupancy
    release_quant_overrides = merge_quantization_overrides(quant_override_sparse_detection, quant_override_occupancy)

    run_convert_pipeline(
        config=config,
        training_module=training_module,
        datamodule_class=CombinedDataModule,
        task_ids=[task_id] if not isinstance(task_id, list) else task_id,
        aux_names=aux_names,
        quant_override=release_quant_overrides,
    )


@hydra.main(version_base=None, config_name="ground_truth_config")
def hydra_main_ground_truth(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the ground_truth simple usecase."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))

    enabled_tasks = config.multi_task_collection.enabled_tasks

    model_to_export = None
    aux_names = {}

    if all(isinstance(task, FusionSimpleHeadTask) for task in enabled_tasks):
        quant_override = get_quantization_overrides(config, model_override=Model.BOX3D_SIMPLE)
        task_id = FusionSimpleHeadTask.identifier()

        training_module = MultiModalUseCaseTrainingModule(
            config.training_config, config.multi_task_collection.enabled_tasks
        )

        if len(enabled_tasks) == 2:
            aux_names[task_id] = []
            # If the model is a multi-task model, we need to fuse the different tasks outputs into a single object
            assert config.task_configs.fusion_simple_head_veh is not None, (
                "FusionSimpleHeadVeh task config is required for GT fusion."
            )
            model_to_export = ExtendedTemporalModel(
                training_module.model,
                additional_layer=GTBevBox3dInferenceFuse(
                    # VRU are fused after Veh, hence use veh number of classes.
                    vru_label_id_offset=config.task_configs.fusion_simple_head_veh.num_classes - 1
                ),
            )
        elif len(enabled_tasks) == 1:
            aux_names[task_id] = []
            # aux outputs are removed by an additional layer
            model_to_export = ExtendedTemporalModel(
                training_module.model,
                additional_layer=RemoveRawPredictions(task_id),
            )

        else:
            raise NotImplementedError
    else:
        raise NotImplementedError

    run_convert_pipeline(
        config=config,
        training_module=model_to_export if model_to_export else training_module,
        datamodule_class=CombinedDataModule,
        task_ids=[task_id],
        aux_names=aux_names,
        quant_override=quant_override,
    )


@hydra.main(version_base=None, config_name="gmd_parking_config")
def hydra_main_gmd_parking(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the gmd parking usecase.

    This function prepares the configuration, initializes the training module, sets up quantization overrides,
    defines auxiliary output names for the GMD CNN head task, and runs the conversion pipeline.

    Args:
        raw_config: Hydra configuration object containing all necessary settings.
    """
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    config.data_config.dataset_name = "gmd"  # use alliance data for calibration and qnn evaluation
    training_module = MultiModalUseCaseTrainingModule(
        config.training_config, config.multi_task_collection.enabled_tasks
    )
    quant_override = get_quantization_override_dict_parking_cnn()
    task_id = GmdTask.identifier()
    aux_names = {}
    aux_names[task_id] = [f"aux_{n}" for n in ParkingSpaceInferenceDecoded._fields]
    run_convert_pipeline(
        config=config,
        training_module=training_module,
        datamodule_class=CombinedDataModule,
        aux_names=aux_names,
        quant_override=quant_override,
    )


def get_quantization_overrides(config: MultiModalRunConfig, model_override: Model | None = None) -> dict[str, Any]:
    """Get quantization overrides for the given model.

    Args:
        config: MultiModalRunConfig containing the model and training configuration.
        model_override: Optional override for the model type.

    Returns:
        A dictionary containing the quantization overrides for the specified model.
    """
    model = model_override if model_override is not None else config.model

    if model == Model.BOX3D_SIMPLE:
        return get_quantization_override_dict_box3d(radar_model=config.training_config.radar_encoder_params.model_type)
    if model == Model.SPARSE_DETECTION:
        return get_quantization_override_dict_sparse_detection(
            qat=config.convert_config.convert_from_onnx_path is not None,
            input_modalities=config.data_config.enabled_input_modalities,
        )
    if model == Model.OCCUPANCY:
        task_ids = [task.identifier() for task in config.multi_task_collection.enabled_tasks]
        return get_quantization_override_dict_occupancy(
            task_ids=task_ids,
            num_cams=config.training_config.num_cams,
            enabled_input_modalities=config.training_config.enabled_input_modalities,
        )
    if model == Model.LANESEGNET:
        return QUANTIZATION_OVERRIDE_DICT_LANESEGNET.copy()

    message = f"Quantization overrides not implemented for model {model}."
    raise NotImplementedError(message)


if __name__ == "__main__":
    main(sys.argv[1:])
