"""Multi-modal usecase training stage."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
import sys
from collections.abc import Callable
from pathlib import Path
from typing import Any, cast

import coloredlogs
import hydra
from hydra.core.config_store import ConfigStore
from omegaconf import DictConfig, OmegaConf
from pytorch_lightning import LightningDataModule, seed_everything
from pytorch_lightning.strategies import DDPStrategy

from xcommon.logging import log_configuration
from xtorch.training import Stage
from xtorch.training.runner import CheckpointFile, LightningRunner
from xtorch_usecases.common.datasets.combined.data_module import CombinedDataModule
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.common.hydra_safe_argv import filter_sys_args_for_hydra
from xtorch_usecases.multimodal.configs.run import MultiModalRunConfig
from xtorch_usecases.multimodal.models.box3d_simple.config import get_run_config_box3d_simple
from xtorch_usecases.multimodal.models.box3d_simple.simple_3d_data_converter import gt_key_to_task_id_mapping
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.models.gmd.config import get_run_config as get_run_config_gmd
from xtorch_usecases.multimodal.models.ground_truth.config import get_run_config_ground_truth
from xtorch_usecases.multimodal.models.occupancy.config import get_run_config_occupancy
from xtorch_usecases.multimodal.models.release.checkpoint_loading import load_mmbev_encoder_checkpoints
from xtorch_usecases.multimodal.models.sparse_detection.config import get_run_config_sparse_detection
from xtorch_usecases.multimodal.models.sparse_detection.data_converter import ada_data_to_sparse_detection_converter
from xtorch_usecases.multimodal.models.sparse_detection_temporal.config import get_run_config_sparse_detection_temporal
from xtorch_usecases.multimodal.models.training_module import MultiModalUseCaseTrainingModule
from xtorch_usecases.sparse_detection.config import SparseDetectionExperimentConfig

_LOGGER = logging.getLogger(__name__)

_DATA_CONVERTER: Callable[..., Any] | None = None


def main(argv: list[str]) -> None:
    """Main function."""
    seed_everything(42, workers=True)

    coloredlogs.install(level=logging.INFO)

    parser = argparse.ArgumentParser()
    parser.add_argument("--model", type=str.lower, choices=[m.value for m in Model], default=Model.BOX3D_SIMPLE)
    parser.add_argument(
        "--aml_node_count", type=int, help="Number of compute nodes which were launched.", required=False, default=1
    )
    args, unknown_args = parser.parse_known_args(argv)

    # Intermediate workaround as we cannot pass args to the hydra main function directly. In an aligned multitask
    # future, data converters won't exist any more because all tasks will have to get along with the same data format.
    global _DATA_CONVERTER  # noqa: PLW0603

    if args.model == Model.BOX3D_SIMPLE:
        # TODO: config handling like in MNIST example
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/328455
        config = get_run_config_box3d_simple(
            unknown_args, pipeline_step=PipelineStep.TRAIN, node_count=args.aml_node_count
        )
    elif args.model == Model.OCCUPANCY:
        config = get_run_config_occupancy(
            unknown_args, pipeline_step=PipelineStep.TRAIN, node_count=args.aml_node_count
        )
    elif args.model == Model.GROUND_TRUTH:
        config = get_run_config_ground_truth(
            unknown_args, pipeline_step=PipelineStep.TRAIN, node_count=args.aml_node_count
        )
        _DATA_CONVERTER = gt_key_to_task_id_mapping
    elif args.model == Model.SPARSE_DETECTION:
        config = get_run_config_sparse_detection(
            unknown_args, pipeline_step=PipelineStep.TRAIN, node_count=args.aml_node_count
        )
        _DATA_CONVERTER = ada_data_to_sparse_detection_converter
    elif args.model == Model.SPARSE_DETECTION_TEMPORAL:
        config = get_run_config_sparse_detection_temporal(
            unknown_args, pipeline_step=PipelineStep.TRAIN, node_count=args.aml_node_count
        )
        _DATA_CONVERTER = ada_data_to_sparse_detection_converter
    elif args.model == Model.GMD_BEV:
        config = get_run_config_gmd(unknown_args, pipeline_step=PipelineStep.TRAIN)
    else:
        msg = f"Unknown model {args.model}."
        raise ValueError(msg)

    config_store = ConfigStore.instance()
    config_store.store("multimodal_config", node=config)

    filter_sys_args_for_hydra()
    hydra_main()


@hydra.main(version_base=None, config_name="multimodal_config")
def hydra_main(raw_config: DictConfig) -> None:
    """Initial workaround to enable omegaconf/hydra for the multimodal usecase."""
    config = cast(MultiModalRunConfig, OmegaConf.to_object(raw_config))
    _LOGGER.debug(f"Config as `MultiModalConfig` object: \n {OmegaConf.to_yaml(config)}")

    train_checkpoint_path, weight_init_checkpoint_paths = load_mmbev_encoder_checkpoints(
        train_checkpoint=config.train_checkpoint
    )
    config.training_config.weight_init_checkpoint_paths = weight_init_checkpoint_paths
    data_module = CombinedDataModule(config.data_config, data_converter=_DATA_CONVERTER)
    training_module = MultiModalUseCaseTrainingModule(
        config.training_config, config.multi_task_collection.enabled_tasks
    )

    run_training_pipeline(config, training_module, data_module, train_checkpoint_path)


def run_training_pipeline(
    config: MultiModalRunConfig | SparseDetectionExperimentConfig,
    training_module: MultiModalUseCaseTrainingModule,
    data_module: LightningDataModule,
    train_checkpoint_path: Path | None = None,
) -> None:
    """Run the training pipeline with the given configuration, modules, and arguments."""
    if isinstance(config, MultiModalRunConfig):
        config.setup_callbacks_for_pipeline_step(PipelineStep.TRAIN)
    log_configuration(name=__file__, config=config)

    experiment_name = "MultiModal Bev Experiment"

    assert config.trainer.default_root_dir is not None
    # This strategy also works for single-gpu, we set static_graph to True for performance optimizations
    # and to tell pytorch that the set of outputs we don't use
    # - currently, depth output - are not changing between different steps
    config.trainer.strategy = DDPStrategy(static_graph=True)  # pyright: ignore[reportAttributeAccessIssue]

    train_checkpoint = None
    if train_checkpoint_path is not None:
        train_checkpoint = CheckpointFile(train_checkpoint_path)

    if config.training_config.freeze_encoder_weights_patterns is not None:
        training_module.freeze_modules(config.training_config.freeze_encoder_weights_patterns)
        _LOGGER.info("Encoder weights frozen.")

    LightningRunner(
        training_module=training_module,  # type: ignore[reportArgumentType]
        data_module=data_module,  # type: ignore[reportArgumentType]
        config=config,
        is_cloud_run=config.data_config.environment == "azure",
    ).run(
        experiment_name=experiment_name,
        stage=Stage.TRAIN,
        pretrained_weights=train_checkpoint,
    )


if __name__ == "__main__":
    main(sys.argv[1:])
