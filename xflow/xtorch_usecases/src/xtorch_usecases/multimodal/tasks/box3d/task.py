"""Task definition for multi-task learning."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import re
from collections.abc import Callable, Collection
from typing import Any, Final

import torch
from omegaconf import DictConfig, ListConfig
from pytorch_lightning.callbacks import Callback

from xcommon.training.callbacks.savers.utils.air_class_mapping import box3d_label_id_to_air_class_mapping
from xcontract.data.definitions.usage import ValueKey
from xtorch.data.definitions.bev import BevBox3dGTTuple
from xtorch.geometry.volumes.volumetric_grid import VolumetricGrid
from xtorch.losses.interface import Loss
from xtorch.multi_task.structured_task import StructuredTask, SupportedData
from xtorch.nn.backbones.interface import BackboneInputT
from xtorch.nn.heads.interface import Head
from xtorch.training.metric_handling import MetricRegistry
from xtorch_extensions.visualization.callbacks.topics import (
    BASE_FRAME,
    PRED_BOXES_TOPIC,
    GroundTruthPublisher,
    PredictionsPublisher,
    TopicPublisher,
    Visualizable,
)
from xtorch_extensions.visualization.converters import (
    QuaternionOrder,
    extract_prediction_boxes_from_decoding,
    get_center_size_quaternion_boxes_converter,
)
from xtorch_extensions.visualization.data import UniformBinaryGridTopic
from xtorch_usecases.common.callbacks.combined_metrics.task import TaskCombinedMetrics
from xtorch_usecases.common.callbacks.evil_evaluation import EvilEvaluation
from xtorch_usecases.common.callbacks.summary_logging import EvilSummaryLogger
from xtorch_usecases.common.datasets.common.bev_box3d_loader import LabelDefinition
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.common.definitions import ModelInputType
from xtorch_usecases.multimodal.components.radar.definitions import RadarModelType
from xtorch_usecases.multimodal.configs.usecase import UsecaseConfig
from xtorch_usecases.multimodal.tasks.box3d.callbacks import AirBevBox3DSaver
from xtorch_usecases.multimodal.tasks.box3d.definitions import (
    FusionSimpleHeadConfig,
    InferenceOutputAdapterType,
    SimpleBevBox3d,
)

# TODO: check embedding head
# https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/330875
from xtorch_usecases.multimodal.tasks.box3d.fusion_head import FusionBevBox3dHead
from xtorch_usecases.multimodal.tasks.box3d.loss import BevBox3dLoss
from xtorch_usecases.multimodal.tasks.box3d_common.output_adapters import (
    BevBoxOnnxEvilInferenceOutputAdapter,
    GTDeFuseBevBoxOnnxEvilInferenceOutputAdapter,
)
from xtorch_usecases.multimodal.tasks.box3d_common.visualization import (
    extract_bev_box3d_decoding,
    ground_truth_topic_publishers_for_box3d,
)


class FusionSimpleHeadTask(
    StructuredTask[
        FusionSimpleHeadConfig,
        UsecaseConfig,
        dict[str, BackboneInputT],
        ModelInputType,
        # TODO: interface not compatible with FusionBevBox3dHead yet  # noqa: TD003
        # dict[str, list[PredictedBoxes] | TensorDict] | BevBox3dInferenceDecoding
        Any,
        dict[str, list[torch.Tensor] | torch.Tensor],
    ],
    Visualizable,
):
    """3D Object Detection task with Simple Head."""

    def __init__(
        self,
        config: FusionSimpleHeadConfig,
        common_config: UsecaseConfig,
    ) -> None:
        """Constructor."""
        self._volumetric_grid = VolumetricGrid.from_config(
            volume_params=config.bev_layout.volume_params, resolution=config.bev_layout.resolutions
        )
        self._config = config
        self._callback_config = common_config.callback_config
        if config.inference_output_adapter == InferenceOutputAdapterType.FusionOutputAdapter:
            self.inference_output_adapter = BevBoxOnnxEvilInferenceOutputAdapter()
        elif config.inference_output_adapter == InferenceOutputAdapterType.DeFusionOutputAdapter:
            self.inference_output_adapter = GTDeFuseBevBoxOnnxEvilInferenceOutputAdapter(
                defuse_inference_adapter_range=config.defuse_inference_adapter_range,
                label_offset=config.defuse_inference_output_adapter_class_offset,
                head_output_size=config.nb_nms_boxes,
            )

    @property
    def head(self) -> Head[dict[str, torch.Tensor], ModelInputType, Any]:
        """Return the head for the task."""
        return FusionBevBox3dHead(
            in_channels=self._config.in_channels,
            num_classes=self._config.num_classes,
            volumetric_grid=self._volumetric_grid,
            feature_map_data_ids=self._config.feature_map_data_ids,
            min_l1_distance_meters=self._config.min_l1_distance_meters,
            nb_nms_boxes=self._config.nb_nms_boxes,
        )

    @property
    def loss(  # type: ignore [reportIncompatibleMethodOverride] # TODO: fix loss return type  # noqa: TD003
        self,
    ) -> Loss[
        SimpleBevBox3d | tuple[SimpleBevBox3d, torch.Tensor],
        dict[ValueKey, torch.Tensor | BevBox3dGTTuple],
    ]:
        """Return the loss function for the task."""
        return BevBox3dLoss(
            num_classes=self._config.num_classes,
            volumetric_grid=self._volumetric_grid,
            loss_weight=self._config.loss_weight,
        )

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Returns the callbacks for the multimodal task."""

        assert self._callback_config is not None, "Callbacks have not been initialized."

        if pipeline_step not in {
            PipelineStep.TRAIN,
            PipelineStep.TRAIN_FINETUNE,
            PipelineStep.EVALUATE_TORCH,
            PipelineStep.EVALUATE_QNN,
            PipelineStep.EVALUATE_ONNX,
        }:
            return []

        label_definition = LabelDefinition(
            label_set_name=self._config.label_set_name,
            label_set_version=self._config.label_set_version,
            background_class=self._config.background_class,
        )

        return [
            AirBevBox3DSaver(
                task_id=self.identifier(),
                evaluation_folder=str(self._config.evaluation_folder),
                predicted_class_id_mapping=box3d_label_id_to_air_class_mapping(
                    label_definition.label_set.predicted_labels
                ),
                background_id=label_definition.background_id,
                objectness_threshold=self._config.eval_objectness_threshold,
                delete_predictions_afterwards=True,
                store_gt_in_airbox=self._config.store_gt_in_airbox,
                gt_key=self._config.gt_key,
            ),
            # EvilEvaluation has to be added after AirBevBox3DSaver since the AirBevBox3DSaver callback will only write
            # the dataset files at the end of an evaluation epoch
            EvilEvaluation(
                task_id=self.identifier(),
                evaluation_folder=str(self._config.evaluation_folder),
                evil_configs=self._config.evil_configs,
                summary_logger=EvilSummaryLogger(),
                run_environment=self._config.eval_run_environment,
            ),
            TaskCombinedMetrics(
                evaluation_folder=self._config.evaluation_folder,
                task_id=self.identifier(),
                combined_metrics_yaml=self._config.combined_metrics_yaml,
                run_environment=self._config.eval_run_environment,
            ),
        ]

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """Returns the metrics for the multimodal task."""
        return []

    @property
    def topic_publishers(self) -> Collection[TopicPublisher]:
        """Update the visualization publishers for the box3d task."""
        task_id = self.identifier()
        topics = ground_truth_topic_publishers_for_box3d(task_id, gt_key=self._config.gt_key)
        ignore_rgba: Final = (1.0, 0.0, 0.0, 0.5)
        topics.append(
            GroundTruthPublisher(
                UniformBinaryGridTopic(
                    self._config.bev_layout, ignore_rgba, name=f"/{task_id}/ignore_area", frame_id=BASE_FRAME
                ),
                extractor=lambda key, batch: ~batch[key][ValueKey.DATA]
                .bev_grid_mask.detach()
                .cpu()
                .numpy()
                .astype(bool),
                key=self._config.gt_key,
            ),
        )
        topics.append(
            PredictionsPublisher(
                PRED_BOXES_TOPIC.replace(name=f"/{task_id}{PRED_BOXES_TOPIC.name}"),
                converter=get_center_size_quaternion_boxes_converter(QuaternionOrder.XYZW),
                extractor=lambda predictions: extract_prediction_boxes_from_decoding(
                    extract_bev_box3d_decoding(predictions)
                ),
                task_id=task_id,
            )
        )
        return topics

    @property
    def onnx_inference_output_adapter(self) -> Callable[[dict[str, torch.Tensor]], Any] | None:
        """Method to provide output adapter for onnx eval."""
        return self.inference_output_adapter

    def get_expected_config_changes(
        self,
        producer_step: PipelineStep,
        producer_config: DictConfig | ListConfig,
        consumer_step: PipelineStep,
        consumer_config: DictConfig | ListConfig,
    ) -> list[str]:
        """Return expected config changes between the two pipeline steps."""
        if producer_step == PipelineStep.TRAIN_FINETUNE and consumer_step == PipelineStep.CONVERT:
            return [
                re.escape("trainer.max_epochs"),
                re.escape("trainer.train_epoch_length"),
                re.escape("training_config.lr"),
                re.escape("training_config.num_epochs"),
                re.escape("training_config.train_steps_per_epoch"),
                re.escape("training_config.warmup_steps"),
                re.escape("training_epoch_length"),
            ]
        return []


class FusionSimpleHeadVruTask(
    FusionSimpleHeadTask[
        StructuredTask[
            FusionSimpleHeadConfig,
            UsecaseConfig,
            dict[str, BackboneInputT],
            ModelInputType,
            Any,
            dict[str, list[torch.Tensor] | torch.Tensor],
        ]
    ]
):
    """3D Object Detection VRU task with Simple Head ."""


class FusionSimpleHeadVehTask(
    FusionSimpleHeadTask[
        StructuredTask[
            FusionSimpleHeadConfig,
            UsecaseConfig,
            dict[str, BackboneInputT],
            ModelInputType,
            Any,
            dict[str, list[torch.Tensor] | torch.Tensor],
        ]
    ]
):
    """3D Object Detection VRU task with Simple Head ."""


# TODO: refactor
# https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/344560
def get_quantization_override_dict_box3d(
    *, radar_model: RadarModelType = RadarModelType.NV_RADAR_NET
) -> dict[str, Any]:
    """Quantization override dict for Box3D task."""

    task_head_id = f"model/{FusionSimpleHeadTask.identifier()}"
    task_head_qat_id = "model/_inner_model/_task_heads/fusion_simple_head"

    quantization_override_dict_box3d = {
        "version": "0.5.0",
        "activation_encodings": {
            # Inputs
            # Note: that the quant overrrides for images inputs and YUV plugin related tensors
            # are handled in the onnx surgery rules
            "input_lut_0": [{"bitwidth": 8, "min": -1.1, "max": 1.1}],
            "input_lut_1": [{"bitwidth": 8, "min": -1.1, "max": 1.1}],
            "input_lut_2": [{"bitwidth": 8, "min": -1.1, "max": 1.1}],
            "input_lut_3": [{"bitwidth": 8, "min": -1.1, "max": 1.1}],
            "input_lut_4": [{"bitwidth": 8, "min": -1.1, "max": 1.1}],
            # center_xy":
            ("aux_delta_center_xy", "fusion_simple_head_0_delta_center_xy"): [
                {"bitwidth": 16, "min": -128, "max": 128}
            ],
            (
                f"/{task_head_id}/_head/_prediction_decoder/Add_output_0",
                f"/{task_head_qat_id}/_head/_prediction_decoder/module_add_158/Add_output_0",
            ): [{"bitwidth": 16, "min": -128, "max": 128}],
            ("aux_delta_front_xy", "fusion_simple_head_0_delta_front_xy"): [{"bitwidth": 16, "min": -128, "max": 128}],
            ("aux_z_center", "fusion_simple_head_0_z_center"): [{"bitwidth": 16, "min": -128, "max": 128}],
            # length:
            # (f"/{task_head_id}/_head/_prediction_decoder/Mul_output_0", "/model/Mul_output_0"): [{"bitwidth": 16}],
            (f"/{task_head_id}/_head/_prediction_decoder/ReduceSum_output_0", "/model/ReduceSum_output_0"): [
                {"bitwidth": 16}
            ],
            (f"/{task_head_id}/_head/_prediction_decoder/Sqrt_output_0", "/model/Sqrt_output_0"): [{"bitwidth": 16}],
            # (
            #    f"/{task_head_id}/_head/_prediction_decoder/Mul_1_output_0",
            #    f"/{task_head_qat_id}/_head/_prediction_decoder/module_mul_96/Mul_output_0",
            # ): [{"bitwidth": 16, "min": 0, "max": 26}],
            # quaternion:
            # to make the insertion of the conversion nodes deterministic run this in 16bit even though not needed
            (f"/{task_head_id}/_head/_prediction_decoder/GreaterOrEqual_output_0", "/model/GreaterOrEqual_output_0"): [
                {"bitwidth": 16}
            ],
            (
                f"/{task_head_id}/_head/_prediction_decoder/Div_output_0",
                f"/{task_head_qat_id}/_head/_prediction_decoder/module_truediv_40/Div_output_0",
            ): [{"bitwidth": 16, "min": -1, "max": 1}],
            (
                f"/{task_head_id}/_head/_prediction_decoder/Sub_1_output_0",
                f"/{task_head_qat_id}/_head/_prediction_decoder/module_sub_6/Sub_output_0",
            ): [{"bitwidth": 16, "min": 0, "max": 2}],
            # (
            #    f"/{task_head_id}/_head/_prediction_decoder/Mul_3_output_0",
            #    f"/{task_head_qat_id}/_head/_prediction_decoder/module_mul_98/Mul_output_0",
            # ): [{"bitwidth": 16, "min": 0, "max": 1}],
            (f"/{task_head_id}/_head/_prediction_decoder/Sqrt_2_output_0", "/model/Sqrt_2_output_0"): [
                {"bitwidth": 8, "min": -1, "max": 1}
            ],
            # (
            #    f"/{task_head_id}/_head/_prediction_decoder/Add_1_output_0",
            #    f"/{task_head_qat_id}/_head/_prediction_decoder/module_add_139/Add_output_0",
            # ): [{"bitwidth": 16, "min": 0, "max": 2}],
            # (
            #    f"/{task_head_id}/_head/_prediction_decoder/Mul_2_output_0",
            #    f"/{task_head_qat_id}/_head/_prediction_decoder/module_mul_97/Mul_output_0",
            # ): [{"bitwidth": 16, "min": 0, "max": 1}],
            (f"/{task_head_id}/_head/_prediction_decoder/Sqrt_1_output_0", "/model/Sqrt_1_output_0"): [
                {"bitwidth": 8, "min": -1, "max": 1}
            ],
            # width:
            ("aux_width", "fusion_simple_head_0_width"): [{"bitwidth": 16, "min": 0, "max": 26}],
            # height:
            ("aux_height", "fusion_simple_head_0_height"): [{"bitwidth": 16, "min": 0, "max": 26}],
            # dimension
            "dimension": [{"bitwidth": 16}],  # for avoid wrong scale due to mix precision (Concat bug workaround)
        },
        "param_encodings": {},
    }

    if radar_model == RadarModelType.NV_RADAR_NET:
        quantization_override_dict_box3d["activation_encodings"]["radar_features_lut"] = [
            {"bitwidth": 8, "min": 0, "max": 1}
        ]

    return quantization_override_dict_box3d
