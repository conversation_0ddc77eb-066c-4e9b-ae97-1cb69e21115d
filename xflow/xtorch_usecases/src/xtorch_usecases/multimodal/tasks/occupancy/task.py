"""2.5D occupancy task."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bo<PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import re
from collections.abc import Callable, Collection
from typing import Any

import torch
from omegaconf import DictConfig, ListConfig
from pytorch_lightning.callbacks import Callback

from data_formats.occupancy_25d.data_loading import Occupancy25dLabelDefinition
from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.geometry.volumes.common import BevLayout
from xtorch.geometry.volumes.volumetric_grid import VolumetricGrid
from xtorch.losses.interface import Loss
from xtorch.multi_task.structured_task import StructuredTask, SupportedData
from xtorch.nn.heads.interface import Head
from xtorch.training.metric_handling import MetricRegistry
from xtorch_extensions.visualization.callbacks.topics import TopicPublisher, Visualizable
from xtorch_usecases.common.callbacks.combined_metrics.task import TaskCombinedMetrics
from xtorch_usecases.common.callbacks.evil_evaluation import EvilEvaluation
from xtorch_usecases.common.callbacks.summary_logging import EvilSummaryLogger, SummaryLogger
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.common.definitions import ModelInputType
from xtorch_usecases.multimodal.configs.geometry import MAX_HEIGHT_M, MIN_HEIGHT_M
from xtorch_usecases.multimodal.configs.usecase import UsecaseConfig
from xtorch_usecases.multimodal.tasks.occupancy.callbacks.image_summaries import ImageSummaries
from xtorch_usecases.multimodal.tasks.occupancy.callbacks.prediction_saver import OccupancyPredictionSaver
from xtorch_usecases.multimodal.tasks.occupancy.callbacks.video_exporter import OccupancyVideoExporter
from xtorch_usecases.multimodal.tasks.occupancy.callbacks.visualization import topic_publishers_for_occupancy
from xtorch_usecases.multimodal.tasks.occupancy.definitions import (
    OccupancyOutput,
    OccupancyTaskConfig,
    OccupancyTrainOutput,
    VideoExporterConfig,
)
from xtorch_usecases.multimodal.tasks.occupancy.head import OccupancyHead, OccupancyHeadLayerConfig
from xtorch_usecases.multimodal.tasks.occupancy.loss import OccupancyLoss


class OccupancyTask(
    StructuredTask[
        OccupancyTaskConfig,
        UsecaseConfig,
        torch.Tensor,
        ModelInputType,
        Any,
        dict[str, list[torch.Tensor] | torch.Tensor],
    ],
    Visualizable,
):
    """2.5D occupancy prediction task."""

    def __init__(
        self,
        config: OccupancyTaskConfig,
        common_config: UsecaseConfig,
    ) -> None:
        """Constructor."""

        self._config = config
        self._common_config = common_config

        self._volumetric_grid = VolumetricGrid.from_config(
            volume_params=config.bev_layout.volume_params, resolution=config.bev_layout.resolutions
        )
        self._occupancy_grid_layout = config.occupancy_grid_layout

        self._summary_logger = SummaryLogger(config.summary_output_path)

        self._label_def = Occupancy25dLabelDefinition(
            label_set_name=self._config.label_set_name,
            label_set_version=self._config.label_set_version,
            ignore_class=self._config.ignore_class,
            semantic_mapping_required=self._config.semantic_mapping_required,
        )

        self._head = OccupancyHead(
            volumetric_grid=self._volumetric_grid,
            occupancy_grid_layout=self._occupancy_grid_layout,
            layer_config=OccupancyHeadLayerConfig(),
            num_semantic_classes=config.num_predicted_classes,
            visibility_prob_threshold=config.visibility_prob_threshold,
            bev_features_id=config.bev_features_id,
        )
        self._loss = OccupancyLoss(
            loss_weights=config.loss_weights,
            num_predicted_classes=config.num_predicted_classes,
            ignore_class_id=config.ignore_class_id,
            class_weights=config.class_weights,
            use_uncertainty_weighting=config.use_uncertainty_weighting,
        )

    def get_weight(self) -> float:
        """Return the task weight for the loss function."""
        return self._config.loss_weight

    @property
    def head(self) -> Head[torch.Tensor, ModelInputType, OccupancyOutput]:
        """Return the head for the task."""
        return self._head

    @property
    # TODO: resolve type error  # noqa: TD003
    def loss(  # type: ignore[reportIncompatibleMethodOverride]
        self,
    ) -> Loss[OccupancyTrainOutput, dict[ValueKey, torch.Tensor]]:
        """Return the loss function for the task."""
        return self._loss

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Returns a list of task-specific callbacks."""
        image_summaries = ImageSummaries(
            task_id=self.identifier(),
            summary_logger=self._summary_logger,
            image_logging_frequency=self._common_config.callback_config.image_logging_frequency,
            label_def=self._label_def,
            height_range_m=(self._occupancy_grid_layout.height_min_m, self._occupancy_grid_layout.height_max_m),
            visibility_prob_threshold=self._config.visibility_prob_threshold,
            log_train=False,
        )

        if pipeline_step in {PipelineStep.CONVERT, PipelineStep.CONVERT_QNN}:
            return [image_summaries]

        prediction_saver = OccupancyPredictionSaver(
            task_id=self.identifier(),
            evaluation_folder=str(self._config.evaluation_folder),
            label_def=self._label_def,
            occupancy_grid_layout=self._occupancy_grid_layout,
            delete_predictions_afterwards=self._config.delete_predictions_after_eval,
            save_sample_predictions=True,
            save_gt_for_eval=self._config.save_ground_truth_for_eval,
        )

        evil_evaluation = EvilEvaluation(
            task_id=self.identifier(),
            evaluation_folder=str(self._config.evaluation_folder),
            evil_configs=self._config.evil_configs,
            summary_logger=EvilSummaryLogger(),
            config_update_fns=[self._get_evil_layout_update_fn(), self._get_evil_near_range_specific_update_fn()],
            run_environment=self._config.eval_run_environment,
        )

        task_combined_metric = TaskCombinedMetrics(
            evaluation_folder=self._config.evaluation_folder,
            combined_metrics_yaml=self._config.combined_metrics_config,
            task_id=self.identifier(),
            run_environment=self._config.eval_run_environment,
        )

        video_exporter = OccupancyVideoExporter(
            video_export_folder=self._config.visualization_folder / "videos",
            task_id=self.identifier(),
            config=VideoExporterConfig(),
            occupancy_grid_layout=self._occupancy_grid_layout,
            label_def=self._label_def,
        )

        # EvilEvaluation has to be added after OccupancyPredictionSaver since the saver callback will only
        # write the .dataset files at the end of an evaluation epoch
        if pipeline_step == PipelineStep.VISUALIZATION_TORCH:
            return [video_exporter]
        return [prediction_saver, evil_evaluation, image_summaries, task_combined_metric]

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """Returns the metrics for the multimodal task."""
        return []

    @property
    def topic_publishers(self) -> Collection[TopicPublisher]:
        """Update the visualization publishers for the occupancy task."""
        return topic_publishers_for_occupancy(
            # central visualization class is used here, therefore saving the occ grid layout as BevLayout, however, this
            # is not the actual BEV layout of the trained network
            BevLayout(
                x_min=self._occupancy_grid_layout.x_min_m,
                x_max=self._occupancy_grid_layout.x_max_m,
                x_res=self._occupancy_grid_layout.x_cell_size_m,
                y_min=self._occupancy_grid_layout.y_min_m,
                y_max=self._occupancy_grid_layout.y_max_m,
                y_res=self._occupancy_grid_layout.y_cell_size_m,
            ),
            self._label_def.label_set,
            key=self.identifier(),
        )

    def _get_evil_layout_update_fn(self) -> Callable[[dict[str, Any]], None]:
        """Get an update function to patch evil yml with correct occupancy grid layout.

        Returns:
            The config update function for the evil evaluation.
        """

        def _update_bev_layout(config: dict[str, Any]) -> None:
            for group in config["EVALUATION_GROUPS"]:
                for evaluation in group["evaluations"]:
                    if "x_min_m" in evaluation:
                        assert "x_max_m" in evaluation
                        assert "x_cell_size_m" in evaluation
                        assert "y_min_m" in evaluation
                        assert "y_max_m" in evaluation
                        assert "y_cell_size_m" in evaluation
                        evaluation["x_min_m"] = self._occupancy_grid_layout.x_min_m
                        evaluation["x_max_m"] = self._occupancy_grid_layout.x_max_m
                        evaluation["x_cell_size_m"] = self._occupancy_grid_layout.x_cell_size_m
                        evaluation["y_min_m"] = self._occupancy_grid_layout.y_min_m
                        evaluation["y_max_m"] = self._occupancy_grid_layout.y_max_m
                        evaluation["y_cell_size_m"] = self._occupancy_grid_layout.y_cell_size_m

        return _update_bev_layout

    def _get_evil_near_range_specific_update_fn(self) -> Callable[[dict[str, Any]], None]:
        """Get an update function to patch evil yml and enable very near range metrics only for near range grid.

        Returns:
            The config update function for the evil evaluation.
        """

        def _enable_very_near_range_metrics(config: dict[str, Any]) -> None:
            if self.identifier() != "occupancy_near_range":
                return
            for group in config["EVALUATION_GROUPS"]:
                for evaluation in group["evaluations"]:
                    if "enable_very_near_range_metrics" in evaluation:
                        evaluation["enable_very_near_range_metrics"] = True

        return _enable_very_near_range_metrics

    def get_expected_config_changes(
        self,
        producer_step: PipelineStep,
        producer_config: DictConfig | ListConfig,
        consumer_step: PipelineStep,
        consumer_config: DictConfig | ListConfig,
    ) -> list[str]:
        """Return expected config changes between the two pipeline steps."""
        return [
            re.escape(f"task_configs.{self.identifier()}.{attribute}") + ".*"
            for attribute in [
                "evaluation_folder",
                "evaluation_torch_folder",
                "summary_output_path",
                "visualization_folder",
            ]
        ]


class OccupancyNearRangeTask(OccupancyTask):
    """2.5D occupancy prediction task for near range."""


def get_quantization_override_dict_occupancy(
    task_ids: list[str], num_cams: dict[InputDataId, int], enabled_input_modalities: tuple[InputDataId, ...]
) -> dict[str, Any]:
    """Get the quantization override dictionary for occupancy task.

    Args:
        task_ids: Tuple of task IDs for occupancy and near range occupancy tasks.
        num_cams: Dictionary mapping input data IDs to the number of cameras.
        enabled_input_modalities: Tuple of enabled input modalities.
    """
    enabled_camera_inputs = list(
        {
            InputDataId.CAMERA_PINHOLE,
            InputDataId.CAMERA_CYLINDER,
            InputDataId.CAMERA_DEF_CYLINDER,
        }
        & set(enabled_input_modalities)
    )
    num_luts_mid = sum([num_cams[input_id] for input_id in enabled_camera_inputs])

    quantization_override_dict_occupancy = {
        "version": "0.5.0",
        "activation_encodings": {
            f"input_lut_{i}": [{"bitwidth": 8, "min": -1.1, "max": 1.1}] for i in range(num_luts_mid)
        },
        "param_encodings": {},
    }
    if "occupancy" in task_ids:
        quantization_override_dict_occupancy["activation_encodings"]["aux_visibility_logits"] = [{"bitwidth": 16}]
    if "occupancy_near_range" in task_ids:
        quantization_override_dict_occupancy["activation_encodings"]["aux_visibility_logits_near"] = [{"bitwidth": 16}]

    if len(task_ids) == 1:
        quantization_override_dict_occupancy["activation_encodings"]["height_map"] = [
            {"bitwidth": 8, "min": MIN_HEIGHT_M, "max": MAX_HEIGHT_M}
        ]
        return quantization_override_dict_occupancy

    quantization_override_dict_occupancy["activation_encodings"].update(
        {
            f"input_lut_{i}_near": [{"bitwidth": 8, "min": -1.1, "max": 1.1}]
            for i in range(num_cams[InputDataId.CAMERA_DEF_CYLINDER])
        }
    )
    quantization_override_dict_occupancy["activation_encodings"].update(
        {
            f"{task_ids[0]}_height_map": [{"bitwidth": 8, "min": MIN_HEIGHT_M, "max": MAX_HEIGHT_M}],
            f"{task_ids[1]}_height_map": [{"bitwidth": 8, "min": MIN_HEIGHT_M, "max": MAX_HEIGHT_M}],
        }
    )
    return quantization_override_dict_occupancy
