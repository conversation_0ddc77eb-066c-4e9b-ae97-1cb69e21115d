"""Task definition for multi-task learning."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import re
from collections.abc import Collection, Mapping, Sequence
from dataclasses import dataclass
from pathlib import Path
from typing import Any, cast

import torch
from omegaconf import DictConfig, ListConfig
from pytorch_lightning.callbacks import Callback

from xcommon.training.callbacks.savers.utils.air_class_mapping import box3d_label_id_to_air_class_mapping
from xcontract.data.definitions.inputs import InputDataId
from xtorch.metrics.tracking.tracking import TrackingMetric
from xtorch.multi_task.structured_temporal_task import StructuredTemporalTask
from xtorch.nn.heads.object_detection.box3d import TrackedBox3dInferenceDecoding
from xtorch.training import Stage
from xtorch.training.data_container import SupportedData
from xtorch.training.metric_handling import MetricRegistry
from xtorch_extensions.visualization.callbacks.topics import (
    PRED_TRACKING_BOXES_EXTENDED_TOPIC,
    PredictionsPublisher,
    TopicPublisher,
    Visualizable,
)
from xtorch_extensions.visualization.converters import (
    QuaternionOrder,
    extract_prediction_tracked_boxes_from_decoding,
    get_center_size_quaternion_boxes_converter,
)
from xtorch_usecases.common.callbacks.combined_metrics.task import TaskCombinedMetrics
from xtorch_usecases.common.callbacks.evil_evaluation import EvilEvaluation
from xtorch_usecases.common.callbacks.summary_logging import EvilSummaryLogger
from xtorch_usecases.common.datasets.common.bev_box3d_loader import LabelDefinition
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.common.definitions import ModelInputType
from xtorch_usecases.multimodal.configs.geometry import BevLayout
from xtorch_usecases.multimodal.configs.task import MultiModalTaskSpecificConfig
from xtorch_usecases.multimodal.configs.usecase import UsecaseConfig
from xtorch_usecases.multimodal.tasks.box3d_common.visualization import ground_truth_topic_publishers_for_box3d
from xtorch_usecases.multimodal.tasks.sparse_detection.callbacks import AirBevBox3DSaver, ImageWriterCallbackADA
from xtorch_usecases.multimodal.tasks.sparse_detection.definitions import (
    EvaluationConfig,
    ImageWriterCallbackParams,
    LossConfig,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.head import (
    MultiModalDataHook,
    SparseDetectionHead,
    SparseDetectionHeadInput,
    SparseDetectionHeadOutput,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.loss import FocalHeadLoss, SparseDetectionLoss, StreamPETRLoss
from xtorch_usecases.multimodal.tasks.sparse_detection.memory_manager import MemoryState
from xtorch_usecases.multimodal.tasks.sparse_detection.metrics import (
    tracking_predictions_access_function,
    tracking_target_access_function,
)
from xtorch_usecases.multimodal.tasks.sparse_detection.sparse_detection_parameters import SparseDetectionHeadParams


@dataclass(kw_only=True)
class SparseDetectionTaskConfig(MultiModalTaskSpecificConfig):
    """Config parameters for the sparse detection task task.

    Args:
        num_classes: The number of object class labels.
        bev_layout: The layout of the BEV.
        background_id: the id of the background class
        camera_backbone_type: The type of backbone that is used for processing camera images
        enabled_input_modalities: The ids for the input modalities that are enabled for the task.
    """

    head_config: SparseDetectionHeadParams
    loss_config: LossConfig
    image_writer_callback_config: ImageWriterCallbackParams | None = None

    label_set_name: str
    label_set_version: str
    label_set_background_class: str

    # config for prediction/gt saving
    eval_objectness_threshold: float
    store_gt_in_airbox: bool
    gt_key: str
    max_num_detections: int

    # config for the evil evaluation
    evaluation_folder: Path
    evil_configs: dict[Stage, Path]

    # config for the combined metrics
    combined_metrics_yaml: Path

    # metric config
    metric_configs: EvaluationConfig | None
    bev_layout: BevLayout


class SparseDetectionTask(
    StructuredTemporalTask[
        SparseDetectionTaskConfig,
        UsecaseConfig,
        SparseDetectionHeadInput,
        ModelInputType,
        MemoryState,
        Any,  # to do: map from SparseDetectionHeadOutput to list[tuple[str, Any]]
        dict[str, list[torch.Tensor] | torch.Tensor],
    ],
    Visualizable,
):
    """Task definition."""

    def __init__(self, config: SparseDetectionTaskConfig, common_config: UsecaseConfig) -> None:
        """Constructor."""
        self._config = config
        self._callback_config = common_config.callback_config
        self._metrics = self._build_metrics()

    @property
    def head_params(self) -> SparseDetectionHeadParams:
        """Task specific parameters of head."""
        return self._config.head_config

    @property
    def loss_params(self) -> LossConfig:
        """Task specific parameters of loss."""
        return self._config.loss_config

    def get_weight(self) -> float:
        """Return the task weight for the loss function."""
        return self._config.loss_weight

    @property
    def head(self) -> Any:
        """Task head that processes backbone outputs."""
        data_hook = MultiModalDataHook(
            gt_data_key=self.identifier(),
            radar_feature_map_hw=self._config.head_config.fusion_head_params.radar_fm_shape,
            lidar_feature_map_hw=self._config.head_config.fusion_head_params.lidar_fm_shape,
        )
        return SparseDetectionHead(self.head_params, data_converter_prehook=data_hook)

    @property
    def loss(self) -> Any:
        """Task specific loss."""
        fusion_head_loss_config = self.loss_params.fusion_head_loss
        camera_head_loss_config = self.loss_params.camera_head_loss

        fusion_head_loss = StreamPETRLoss(
            class_loss_weight=fusion_head_loss_config.class_loss_weight,
            bbox_loss_weights=fusion_head_loss_config.bbox_loss_weights,
            dn_loss_weight=fusion_head_loss_config.dn_loss_weight,
        )

        focal_head_loss = None
        if camera_head_loss_config.enabled:
            focal_head_loss = [
                FocalHeadLoss(
                    stride=camera_head_loss_config.stride,
                    class_loss_weight=camera_head_loss_config.class_loss_weight,
                    bbox_giou_loss_weight=camera_head_loss_config.bbox_giou_loss_weight,
                    bbox_l1_loss_weight=camera_head_loss_config.bbox_l1_loss_weight,
                    center_loss_weight=camera_head_loss_config.center_loss_weight,
                    class_cost_weight=camera_head_loss_config.class_cost_weight,
                    bbox_giou_cost_weight=camera_head_loss_config.bbox_giou_cost_weight,
                    bbox_l1_cost_weight=camera_head_loss_config.bbox_l1_cost_weight,
                    center_cost_weight=camera_head_loss_config.center_cost_weight,
                )
                for _ in range(self.loss_params.num_camera_losses)
            ]

        return SparseDetectionLoss(
            fusion_head_loss=fusion_head_loss, camera_head_losses=focal_head_loss, use_uncertainty_weighting=False
        )

    def initial_temporal_state(self, batch_size: int, device: str | torch.device) -> MemoryState:
        """Initial temporal state for the task (if temporal mode is enabled)."""
        assert self.head_params.fusion_head_params.temporal_mode is not None, "Temporal mode is not enabled."
        assert isinstance(self.head, SparseDetectionHead), "Head is not of type SparseDetectionHead."
        memory_manager = self.head.memory_manager
        assert memory_manager is not None, "Memory manager is not initialized in the head."
        return memory_manager.initialize_memory_state(batch_size=batch_size, device=device)

    def callbacks(self, pipeline_step: PipelineStep) -> list[Callback]:
        """Returns the callbacks for the multimodal task."""

        assert self._callback_config is not None, "Callbacks have not been initialized."

        if pipeline_step not in {
            PipelineStep.TRAIN,
            PipelineStep.TRAIN_FINETUNE,
            PipelineStep.QAT,
            PipelineStep.EVALUATE_TORCH,
            PipelineStep.EVALUATE_QNN,
        }:
            return []

        label_definition = LabelDefinition(
            label_set_name=self._config.label_set_name,
            label_set_version=self._config.label_set_version,
            background_class=self._config.label_set_background_class,
        )

        callbacks = [
            AirBevBox3DSaver(
                task_id=self.identifier(),
                evaluation_folder=str(self._config.evaluation_folder),
                predicted_class_id_mapping=box3d_label_id_to_air_class_mapping(
                    label_definition.label_set.predicted_labels
                ),
                background_id=label_definition.background_id,
                objectness_threshold=self._config.eval_objectness_threshold,
                delete_predictions_afterwards=True,
                store_gt_in_airbox=self._config.store_gt_in_airbox,
                max_num_detections=self._config.max_num_detections,
                gt_key=self._config.gt_key,
                save_sample_predictions=True,
            ),
            # EvilEvaluation has to be added after AirBevBox3DSaver since the AirBevBox3DSaver callback will only write
            # the dataset files at the end of an evaluation epoch
            EvilEvaluation(
                task_id=self.identifier(),
                evaluation_folder=str(self._config.evaluation_folder),
                evil_configs=self._config.evil_configs,
                summary_logger=EvilSummaryLogger(),
                run_environment=self._config.eval_run_environment,
            ),
            TaskCombinedMetrics(
                evaluation_folder=self._config.evaluation_folder,
                task_id=self.identifier(),
                combined_metrics_yaml=self._config.combined_metrics_yaml,
                run_environment=self._config.eval_run_environment,
            ),
        ]
        if self._config.image_writer_callback_config is not None:
            img_writer_callback = ImageWriterCallbackADA(
                task_id=self.identifier(),
                bev_layout=self._config.bev_layout,
                **self._config.image_writer_callback_config.asdict(),
            )
            callbacks.append(img_writer_callback)

        return callbacks

    @property
    def metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """Returns the metrics for the multimodal task."""
        return self._metrics

    def _extract_tracked_box3d_decoding(
        self,
        predictions: Mapping[str, Any],
    ) -> TrackedBox3dInferenceDecoding:
        """Extract the BEV box3d decoding from the predictions.

        Args:
            predictions: The predictions from the model.
        """
        if isinstance(predictions, SparseDetectionHeadOutput):
            return predictions.fusion_head_output.to_tracked_box3d_inference_decoding()
        if (
            isinstance(predictions, Sequence)
            and len(predictions_casted := cast(Sequence[Any], predictions)) >= 2
            and isinstance(decoding := predictions_casted[1], TrackedBox3dInferenceDecoding)
        ):
            return decoding
        msg = (
            "Expected predictions to be of type SparseDetectionHeadOutput "
            "or containing type TrackedBox3dInferenceDecoding at position 1, "
            f"but got {type(predictions)}."
        )
        raise TypeError(msg)

    @property
    def topic_publishers(self) -> Collection[TopicPublisher]:
        """Update the visualization publishers for the sparse_detection task."""
        task_id = self.identifier()
        topics = ground_truth_topic_publishers_for_box3d(task_id, gt_key=self._config.gt_key)
        topics.append(
            PredictionsPublisher(
                PRED_TRACKING_BOXES_EXTENDED_TOPIC.replace(name=f"/{task_id}{PRED_TRACKING_BOXES_EXTENDED_TOPIC.name}"),
                converter=get_center_size_quaternion_boxes_converter(QuaternionOrder.XYZW),
                extractor=lambda predictions: extract_prediction_tracked_boxes_from_decoding(
                    self._extract_tracked_box3d_decoding(predictions)
                ),
                task_id=task_id,
            )
        )
        return topics

    def _build_metrics(self) -> list[MetricRegistry[SupportedData, SupportedData]]:
        """Build the metrics for the task as registry objects."""
        metric_configs = self._config.metric_configs
        metrics: list[MetricRegistry[Any, Any]] = []

        if metric_configs is not None:
            # establish class names
            label_definition = LabelDefinition(
                label_set_name=self._config.label_set_name,
                label_set_version=self._config.label_set_version,
                background_class=self._config.label_set_background_class,
            )

            class_names = [
                label.name
                for label in label_definition.label_set.predicted_labels
                if label.name != self._config.label_set_background_class
            ]

            # tracking metrics like mota, motp, idf1, etc. need sequence info to be loaded in converter
            metrics.append(
                MetricRegistry(
                    metric_identifier="tracking",
                    metric=TrackingMetric(
                        class_names=class_names,
                        only_aggregated=False,
                        min_score=metric_configs.tracking.min_threshold,
                    ),
                    stages=[Stage.VALIDATE],
                    inference_backends=["torch"],
                    predictions_access_function=tracking_predictions_access_function(self.head),
                    targets_access_function=tracking_target_access_function,
                )
            )

            # # nuscenes detection metrics like mAP, NDS, etc
            # # deactivated due to memory/runtime issues
            # metrics.append(
            #     MetricRegistry(
            #         metric_identifier="nuscenes-detection",
            #         metric=NuscenesDetectionMetric(
            #             detection_ranges=metric_configs.nuscenes_detection_kpis.nuscenes_map_detection_ranges,
            #             class_id_to_nuscenes_label=metric_configs.nuscenes_detection_kpis.class_id_to_nuscenes_label,
            #         ),
            #         predictions_access_function=nuscenes_devkit_prediction_access_function(self.head),
            #         targets_access_function=nuscenes_devkit_target_access_function,
            #     )
            # )

        return metrics

    def get_expected_config_changes(
        self,
        producer_step: PipelineStep,
        producer_config: DictConfig | ListConfig,
        consumer_step: PipelineStep,
        consumer_config: DictConfig | ListConfig,
    ) -> list[str]:
        """Return expected config changes between the two pipeline steps."""
        return [re.escape("task_configs.sparse_detection.image_writer_callback_config.output_path")]


class SparseDetectionVruTask(
    SparseDetectionTask,
    Visualizable,
):
    """3D Object Detection VRU task with Sparse detection head ."""


class SparseDetectionVehTask(
    SparseDetectionTask,
    Visualizable,
):
    """3D Object Detection Vehicle task with Sparse detection head ."""


def get_quantization_override_dict_sparse_detection(
    *, qat: bool = False, input_modalities: tuple[InputDataId, ...] = (InputDataId.CAMERA_TV,)
) -> dict[str, Any]:
    """Quantization override dict for the sparse detection task."""

    if qat:
        return {
            "version": "0.6.1",
            "activation_encodings": {
                # vision pe inputs
                "input_coords_3d_normalized_tv": [
                    {"bitwidth": 16, "is_symmetric": "true", "min": -6.9068, "max": 6.9068}
                ],
                "input_coords_3d_normalized_fc1": [
                    {"bitwidth": 16, "is_symmetric": "true", "min": -6.9068, "max": 6.9068}
                ],
                "input_cone_tv": [{"bitwidth": 16, "is_symmetric": "true", "min": -4.0, "max": 4.0}],
                "input_cone_fc1": [{"bitwidth": 16, "is_symmetric": "true", "min": -4.0, "max": 4.0}],
                "quaternion": [{"bitwidth": 8, "is_symmetric": "true", "min": -1, "max": 1}],
                "dimension": [{"bitwidth": 8}],
                "center": [{"bitwidth": 16}],
                # explicitly disable the range override for the sigmoid to not impose anything on the topk
                "/model/TopK_2_output_0": [{"bitwidth": 8}],
                "objectness": [{"bitwidth": 8}],
            },
            "param_encodings": {
                "model._tensor_constant6": [{"bitwidth": 16}],
            },
        }

    override_dict = {
        "version": "0.5.0",
        "activation_encodings": {
            r"regex:/model/sparse_detection/_fusion_head/_transformer_decoder/(?!_layers.\d+/_cross_attn/_attn/_v.\d+/MatMul_output_\d+).*": [  # noqa: E501
                {"bitwidth": 16}
            ],
            r"regex:/model/sparse_detection/_fusion_head/_transformer_decoder/_layers.\d+/_cross_attn/_attn/_v.\d+/MatMul_output_\d+": [  # noqa: E501
                {"bitwidth": 8, "is_symmetric": "true"}
            ],
            "/model/_backbone/RadarBevEncoder/_radar_encoder/_backbone/_stem/_stem/_stem.0/_net/_net.2/Clip_output_0": [
                {"bitwidth": 16}
            ],
            "/model/_backbone/RadarBevEncoder/_radar_encoder/_backbone/_stem/_stem/_stem.1/_net/_net.2/Clip_output_0": [
                {"bitwidth": 8}
            ],
            "regex:/model/sparse_detection/_fusion_head/reg_head/.*": [{"bitwidth": 16}],
            "regex:/model/sparse_detection/_fusion_head/cls_head/.*": [{"bitwidth": 8}],
            "fusion_head_output_box_position_5": [{"bitwidth": 16}],
            "fusion_head_output_box_orientation_5": [{"bitwidth": 16}],
            "/model/sparse_detection/_fusion_head/reg_dimension_5/MatMul_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_fusion_head/reg_velocity_5/MatMul_output_0": [{"bitwidth": 16}],
            # <quaternion>
            # this is the quaternion branch of the inference decoder. it is not necessary to run this in 16 bit to
            # get good quantization but calib-less conversion otherwise fails
            "/model/sparse_detection/_inference_decoder/GatherElements_2_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Split_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Squeeze_1_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Squeeze_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/GreaterOrEqual_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Sub_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Mul_6_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Sqrt_2_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Neg_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Where_4_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Unsqueeze_1_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Add_1_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Mul_5_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Sqrt_1_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_inference_decoder/Unsqueeze_2_output_0": [{"bitwidth": 16}],
            "quaternion": [{"bitwidth": 16, "is_symmetric": "true", "min": -1, "max": 1}],
            # </quaternion>
            "dimension": [{"bitwidth": 16}],
            "center": [{"bitwidth": 16}],
            # explicitly disable the range override for the sigmoid to not impose anything on the topk
            "/model/sparse_detection/_inference_decoder/TopK_output_0": [{"bitwidth": 8}],
            "objectness": [{"bitwidth": 8}],
            "velocity_xy": [{"bitwidth": 16}],
        },
        "param_encodings": {
            "/model/sparse_detection/_fusion_head/Add_1_output_0": [{"bitwidth": 16}],
            "/model/sparse_detection/_fusion_head/Unsqueeze_2_output_0": [{"bitwidth": 16}],  # radar pe constant
        },
    }

    if InputDataId.CAMERA_TV in input_modalities:
        override_dict["activation_encodings"].update(
            {
                # vision pe inputs
                "input_coords_3d_normalized_tv": [
                    {"bitwidth": 16, "is_symmetric": "true", "min": -6.9068, "max": 6.9068}
                ],
                "input_coords_3d_normalized_fc1": [
                    {"bitwidth": 16, "is_symmetric": "true", "min": -6.9068, "max": 6.9068}
                ],
                "input_cone_tv": [{"bitwidth": 16, "is_symmetric": "true", "min": -4.0, "max": 4.0}],
                "input_cone_fc1": [{"bitwidth": 16, "is_symmetric": "true", "min": -4.0, "max": 4.0}],
                "regex:/model/sparse_detection/_positional_encoder.*/GatherElements_output_0": [
                    {"bitwidth": 16, "is_symmetric": "true"}
                ],
                "/model/sparse_detection/Concat_11_output_0": [{"bitwidth": 16}],
                "regex:/model/sparse_detection/_positional_encoder/_position_encoder/_position_encoder.*": [
                    {"bitwidth": 16}
                ],
                "regex:/model/sparse_detection/_fusion_head/_featurized_pe_mln.*": [{"bitwidth": 16}],
                "/model/sparse_detection/_fusion_head/_spatial_alignment_mln/reduce/reduce.0/MatMul_output_0": [
                    {"bitwidth": 16}
                ],
                "/model/sparse_detection/_fusion_head/_spatial_alignment_mln/gamma/MatMul_output_0": [{"bitwidth": 16}],
                "/model/sparse_detection/_fusion_head/_spatial_alignment_mln/beta/MatMul_output_0": [{"bitwidth": 16}],
                "/model/sparse_detection/_fusion_head/Concat_4_output_0": [{"bitwidth": 8}],
                "/model/sparse_detection/_fusion_head/Concat_5_output_0": [{"bitwidth": 16}],
            }
        )
    return override_dict
