"""Metric utility functions to access gt/prediction for evaluation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Callable

import torch

import xtorch_usecases.sparse_detection.dataset.field_definitions as fields
from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.geometry.transformation.quaternion import Quaternion
from xtorch.metrics.tracking.tracking import TrackingMetricsGroundTruth, TrackingMetricsPredictions
from xtorch.nn.heads.object_detection.box3d import PredictedBoxes
from xtorch.nn.heads.object_detection.track_id_manager import INACTIVE_ID
from xtorch_usecases.multimodal.tasks.sparse_detection.head import (
    SparseDetectionHead,
    SparseDetectionHeadOutput,
)
from xtorch_usecases.sparse_detection.training import Batch

TASK_KEY = "sparse_detection"
LABEL_KEY = "multiviewbox3d_embd"


# TODO: update mapping to correct ones. currently some temporal info are missed. # noqa: TD003
def tracking_predictions_access_function(
    head: SparseDetectionHead,
) -> Callable[[dict[str, SparseDetectionHeadOutput]], list[TrackingMetricsPredictions]]:
    """Adapt batch targets into TrackingMetric prediction format."""

    def _inner(
        predictions: dict[str, SparseDetectionHeadOutput],
    ) -> list[TrackingMetricsPredictions]:
        """Return access function of predcition."""
        assert isinstance(head, SparseDetectionHead)
        decoded_boxes = predictions[TASK_KEY].fusion_head_output.to_tracked_box3d_inference_decoding()
        batch_size, num_preds, _ = decoded_boxes.track_id.shape
        # If all track_ids are INACTIVE_ID, we create a range of ids for each batch
        tracking_ids = (
            decoded_boxes.track_id
            if not torch.all(decoded_boxes.track_id == INACTIVE_ID)
            else torch.arange(num_preds, device=decoded_boxes.track_id.device)
            .unsqueeze(0)
            .expand(batch_size, -1)
            .unsqueeze(-1)
        )

        return [
            TrackingMetricsPredictions(
                pred_obj_ids=tracking_ids[b, :, 0],
                pred_boxes=torch.cat(
                    (
                        decoded_boxes.box3d.center[b, :, :],
                        decoded_boxes.box3d.labels[b, :, :] - 1,
                        decoded_boxes.box3d.objectness[b, :, :],
                    ),
                    dim=-1,
                ),
            )
            for b in range(batch_size)
        ]

    return _inner


def tracking_target_access_function(batch: Batch) -> list[TrackingMetricsGroundTruth]:
    """Adapt batch targets into TrackingMetric gt format."""
    fusion_gt = batch[TASK_KEY].fusion
    bev_3d_gt = batch[LABEL_KEY][ValueKey.DATA]  # BevBox3dGTTuple
    frame_meta = batch[InputDataId.FRAME_META][ValueKey.DATA]
    batch_size = len(fusion_gt.bboxes)
    mask = bev_3d_gt.box_mask.to(dtype=torch.bool)
    gt_inputs = []
    for i in range(batch_size):
        mask_i = mask[i]
        gt_box_xyz = fusion_gt.bboxes[i][..., :3]
        gt_labels = fusion_gt.label_ids[i].unsqueeze(1)
        gt_inputs.append(
            TrackingMetricsGroundTruth(
                gt_obj_ids=bev_3d_gt.per_sequence_track_id[i][mask_i],
                gt_boxes=torch.cat((gt_box_xyz, gt_labels), dim=-1),
                frame_id=frame_meta.frame_idx[i],
                video_name=frame_meta.sequence_name[i],
            )
        )
    return gt_inputs


def nuscenes_devkit_prediction_access_function(
    head: SparseDetectionHead,
) -> Callable[[dict[str, SparseDetectionHeadOutput]], list[PredictedBoxes]]:
    """Adapt batch targets into NuscenesDetectionMetric prediction format."""
    assert isinstance(head, SparseDetectionHead)

    def _inner(
        predictions: dict[str, SparseDetectionHeadOutput],
    ) -> list[PredictedBoxes]:
        """Return access function of predcition."""
        boxes: list[PredictedBoxes] = head.decode(predictions[TASK_KEY])
        return boxes

    return _inner


def nuscenes_devkit_target_access_function(batch: Batch) -> Batch:
    """Adapt batch targets into NuscenesDetectionMetric gt format."""
    bev_3d_gt = batch[LABEL_KEY][ValueKey.DATA]
    nuscene_batch = {}
    nuscene_batch[fields.SCENE_TOKEN.name] = bev_3d_gt.sequence_name[0]
    nuscene_batch[fields.FRAME_IDX.name] = torch.zeros_like(bev_3d_gt.timestamp_ns)
    nuscene_batch[fields.CUBOID_PADDING_MASK.name] = bev_3d_gt.box_mask
    nuscene_batch[fields.AMODAL_CUBOID_DIMENSIONS.name] = bev_3d_gt.box_dimension
    nuscene_batch[fields.AMODAL_CUBOID_CENTERS.name] = bev_3d_gt.box_center
    rotation_matrix = bev_3d_gt.box_rotations
    nuscene_batch[fields.AMODAL_CUBOID_ROTATIONS_WXYZ.name] = (
        Quaternion.from_matrix(rotation_matrix).normalized().tensor
    )
    nuscene_batch[fields.AMODAL_CUBOID_LABEL_IDS.name] = bev_3d_gt.box_class_ids
    nuscene_batch[fields.AMODAL_CUBOID_VELOCITIES.name] = torch.zeros_like(bev_3d_gt.box_center, dtype=torch.float32)

    return nuscene_batch
