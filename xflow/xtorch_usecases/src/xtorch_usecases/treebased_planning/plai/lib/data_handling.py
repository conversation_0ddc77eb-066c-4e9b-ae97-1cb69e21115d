"""Dataset code for tree search planner."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import copy
import json
import logging
import pickle
import re
from collections.abc import Sized
from dataclasses import dataclass
from pathlib import Path
from typing import Any, ClassVar, cast

import h5py
import torch
from cachetools import LRUCache

from xtorch_usecases.treebased_planning.plai.learning.types import TreeBasedSample
from xtorch_usecases.treebased_planning.plai.lib.dataset_augmentation import (
    DatasetAugmentationClassConfig,
    MultiDatasetAugmentations,
    MultiDatasetAugmentationsConfig,
)

logger = logging.getLogger(__name__)


@dataclass
class DatasetPathConfig:
    """Configuration class for dataset paths.

    Attributes:
        path (str): The file path to the dataset.
        sampling_weight (float): The weight used for sampling the dataset. Default is 1.0.
        name (str | None): An optional name for the dataset. Default is None.
    """

    path: str
    sampling_weight: float = 1.0  # (see https://pytorch.org/docs/stable/data.html)
    name: str | None = None


class Dataset(torch.utils.data.Dataset[TreeBasedSample]):
    """Class for loading a TreeBased dataset. Code taken from plai (plai/core/lib/data_handling.py)."""

    expected_dataset_meta_keys: ClassVar = [
        "features_labels_infos",
        "dataset_config",
        # "data_frequency",
        "features_labels_generator_config",
        "static_features_generator_config",
        "is_frames_to_skip_config",
        "static_features_infos",
    ]

    def __init__(  # noqa: PLR0915
        self,
        dataset_path: Path,
        split: str,
        augmentation_configs: list[Any],
        set_speed_limits_zero: bool,  # noqa: FBT001
        dataset_type: str | None = None,
    ) -> None:
        """Initialize the dataset.

        Args:
            dataset_path: Path to the dataset directory.
            split: Split of the dataset (train, dev, test).
            augmentation_configs: List of augmentation configurations.
            set_speed_limits_zero: Whether to set speed limits to zero.
            dataset_type: Type of the dataset. Currently used for passing 'PLAI' Flag.
        """
        super().__init__()
        self._dataset_files = list(dataset_path.iterdir())
        self._dataset_name = str(dataset_path / split)
        self._dataset_type = dataset_type
        self._dataset_path = dataset_path
        self._split = split
        self._set_speed_limits_zero = set_speed_limits_zero

        if self._dataset_type == "PLAIDataset":
            meta_path = dataset_path / "meta.json"
            self._split_path = dataset_path
            pattern = re.compile(f"data_{split}_[0-9]*.h5")
        else:
            meta_path = dataset_path / split / "meta.json"
            self._split_path = dataset_path / split
            pattern = re.compile(r"data.*\.h5")

        with Path.open(meta_path) as fp:
            _meta = json.load(fp)
            if any(k not in _meta["info"] for k in self.expected_dataset_meta_keys):
                message = (
                    f"Dataset meta info does not contain expected keys."
                    f"Expected={self.expected_dataset_meta_keys}, received={_meta['info'].keys()}"
                )

                raise KeyError(message)

            def _read_from_meta_info_else_none(meta: dict[str, Any], key: str) -> Any | None:
                return meta["info"][key] if key in _meta["info"] else None

            self._features_labels_infos = _meta["info"]["features_labels_infos"]
            self._features_labels_names = list(self._features_labels_infos.keys())

            self._static_features_infos = _meta["info"]["static_features_infos"]
            self._static_features_names = list(self._static_features_infos.keys())

            self._data_source = (
                _meta["info"]["dataset_config"]["config"]["data_source"] if "dataset_config" in _meta["info"] else None
            )
            self._data_frequency = _meta.get("data_frequency", None)
            self._features_labels_generator_config = _read_from_meta_info_else_none(
                _meta, "features_labels_generator_config"
            )
            self._static_features_generator_config = _read_from_meta_info_else_none(
                _meta, "static_features_generator_config"
            )
            self._samples_perturbation_config = _read_from_meta_info_else_none(_meta, "samples_perturbation_config")
            self._is_frames_to_skip_config = _read_from_meta_info_else_none(_meta, "is_frames_to_skip_config")

        # load static features if available
        if self.static_features_names:
            data_file = h5py.File(dataset_path / "data_static.h5")
            self._static_features = {name: data_file[name] for name in self.static_features_names}
        else:
            self._static_features = {}

        self._data_files = LRUCache(maxsize=612)

        self._augmentation = MultiDatasetAugmentations.from_config(
            MultiDatasetAugmentationsConfig(
                configs=augmentation_configs,
                features_labels_infos=self.features_labels_infos,
            )
        )

        # use deterministic ordering of files for reproducibility
        files: list[Path] = sorted([file for file in self._split_path.iterdir() if pattern.match(str(file.name))])

        # Check if sample_index.pkl exists
        if (self._split_path / "sample_index.pkl").exists():
            # Load sample_index.pkl
            with Path.open(self._split_path / "sample_index.pkl", "rb") as f:
                self._sample_index = pickle.load(f)  # noqa: S301
        else:
            # Create Sample Index Pkl
            self._sample_index = self.build_sample_index(files)
            # Save the sample index to a pickle file for future use
            output_dir = Path("outputs")
            output_dir.mkdir(parents=True, exist_ok=True)
            filename = output_dir / f"{split}_sample_index.pkl"
            with Path.open(filename, "wb") as f:
                pickle.dump(self._sample_index, f)

        self.total_length = len(self._sample_index)

    def build_sample_index(self, files: list[Path]) -> dict[int, tuple[str, int, list[str]]]:
        """Build an index mapping sample indices to file paths and internal indices.

        Args:
            files (list[Path]): List of dataset files.

        Returns:
            dict[int, tuple[Path, int]]: A dictionary mapping global sample indices to a tuple of file path and index.
        """
        sample_index: dict[int, tuple[str, int, list[str]]] = {}
        previous_sample_count = 0
        for file in files:
            with h5py.File(file, "r") as data_file:
                features_labels = {name: data_file[name] for name in self.features_labels_names}
                current_sample_count = len(cast(Sized, next(iter(features_labels.values()))))
                for j in range(current_sample_count):
                    sample_index[previous_sample_count + j] = (file.name, j, [])
                previous_sample_count += current_sample_count
        return sample_index

    def _get_sample_location(self, index: int) -> tuple[str, int, list[str]]:
        """Get the sample location in the dataset.

        Args:
            index: Index of the sample.

        Returns:
            A tuple containing the file name and the index within that file.
        """
        sample_index = self._sample_index[index]
        # Add empty labels if the list isn't present
        if len(sample_index) < 3:
            sample_index = (sample_index[0], sample_index[1], [])
        return self._sample_index[index]

    @property
    def features_labels_names(self) -> list[str]:
        """Returns names of labels and features."""
        return self._features_labels_names

    @property
    def features_labels_infos(self) -> dict[str, Any]:
        """Returns infos of labels and features."""
        return self._features_labels_infos

    @property
    def static_features_names(self) -> list[str]:
        """Returns names of static features."""
        return self._static_features_names

    @property
    def static_features_infos(self) -> dict[str, Any]:
        """Returns infos of static features."""
        return self._static_features_infos

    @property
    def data_source(self) -> str | None:
        """Returns the data source."""
        return self._data_source

    @property
    def data_frequency(self) -> float | None:
        """Returns the data frequency."""
        return self._data_frequency

    @property
    def features_labels_generator_config(self) -> dict[str, Any] | None:
        """Returns the features labels generator config."""
        return self._features_labels_generator_config

    @property
    def samples_perturbation_config(self) -> dict[str, Any] | None:
        """Returns the samples perturbation config."""
        return self._samples_perturbation_config

    @property
    def is_frames_to_skip_config(self) -> dict[str, Any] | None:
        """Returns the frames to skip config."""
        return self._is_frames_to_skip_config

    @property
    def set_speed_limits_zero(self) -> bool:
        """Returns whether to set the speed limits to zero."""
        return self._set_speed_limits_zero

    def get_labels_from_sample_index(self) -> dict[int, list[str]]:
        """Get labels from sample index.

        Returns:
            A dictionary mapping sample indices to a list of labels.
        """
        labels = {}
        for i, (_, _, sample_labels) in self._sample_index.items():
            labels[i] = sample_labels
        return labels

    def __len__(self) -> int:
        """Number of dataset samples."""
        return self.total_length

    def __getitem__(self, index: int) -> TreeBasedSample:
        """Get a sample from the dataset.

        Args:
                index: Index of the sample to retrieve.

        Returns:
                A sample from the dataset.
        """
        file_name, sample_index, _ = self._get_sample_location(index)
        file_path = self._split_path / file_name
        if file_name not in self._data_files:
            # Close oldest file (handled by LRUCache)
            if len(self._data_files) >= self._data_files.maxsize:
                old_path, old_file = self._data_files.popitem()
                old_file.close()
            data_file = h5py.File(file_path, "r")
            self._data_files[str(file_name)] = data_file

        features_labels = cast(
            dict[str, h5py.Dataset], {name: self._data_files[file_name][name] for name in self.features_labels_names}
        )
        dynamic_features = self._augmentation(
            {
                key: copy.deepcopy(
                    features_labels[key][sample_index]
                    if not key.startswith("str::")
                    else features_labels[key][sample_index].tolist()
                )
                for key in self.features_labels_names
            }
        )
        if self.set_speed_limits_zero:
            map_polyline_shape = dynamic_features["segment_map_polyline"].shape
            speed_limit_index = 4
            dynamic_features["segment_map_polyline"][..., speed_limit_index] = torch.zeros(map_polyline_shape[:-1])

        _static_features = cast(dict[str, h5py.Dataset], self._static_features)
        static_features = {key: copy.deepcopy(_static_features[key][0]) for key in self.static_features_names}

        return dynamic_features | static_features


class MockDataset(Dataset):
    """'Mocked' dataset on top of Dataset.

    Loads and uses
    all the same data, i.e. number of samples is the same -
    just data access is 'mocked', meaning no disk HD5 read is
    done and instead a single dummy feature returned.
    """

    MOCK_FEATURE_KEY = "dummy"

    def __init__(
        self,
        dataset_path: str,
        dataset_name: str,
        augmentation_configs: list[DatasetAugmentationClassConfig | dict[str, Any]],
        set_speed_limits_zero: bool = True,  # noqa: FBT001,FBT002
    ) -> None:
        """Initialize the MockDataset.

        Args:
            dataset_path: Path to the dataset directory.
            dataset_name: Name of the dataset file.
            augmentation_configs: List of augmentation configurations.
            set_speed_limits_zero: Whether to set the speed limits to zero.
        """
        super().__init__(Path(dataset_path), "train", augmentation_configs, set_speed_limits_zero, None)
        # This should not have any influence - still clean up the data to avoid
        # any extra memory being kept allocated.
        self._features_labels = {}
        self._static_features = {}

    def __getitem__(self, idx: int) -> dict[str, Any]:
        """Retrieve an item from the dataset at the specified index.

        Args:
            idx (int): The index of the item to retrieve.

        Returns:
            dict[str, Any]: A dictionary containing the data for the specified index.
        """
        return {self.MOCK_FEATURE_KEY: torch.empty(0)}
