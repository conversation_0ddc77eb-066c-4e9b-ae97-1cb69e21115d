# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# copyright (C) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

devices: "auto"

# # 8A100
# num_nodes: 1

# 4T4
num_nodes: 4


default_root_dir: outputs/logs

callbacks:
  ModelCheckpoint:
    dirpath: ${...default_root_dir}/checkpoints # path must start with "outputs" or "logs". See https://learn.microsoft.com/en-us/azure/machine-learning/how-to-save-write-experiment-files?view=azureml-api-1#where-to-write-files
    monitor: "val_total_loss_epoch"
    save_last: true
    save_top_k: 5
    mode: "min"
    auto_insert_metric_name: false
    filename: 'epoch={epoch}-step={step}-val_total_loss_epoch={val_total_loss_epoch:.2f}'
