  dataset_path: [{
                    "path": "${oc.env:AZUREML_DATAREFERENCE_ingest_datasets}/ai_planner/dataset_generation/munich_good_recomputed/labeled_0612_manual__2025-06-13T20:46:57+00:00/ts/dataset",
                    "sampling_weight": 5.0,
                },{
                    "path": "${oc.env:AZUREML_DATAREFERENCE_ingest_datasets}/ai_planner/dataset_generation/adaverse_0_2_recomputed/labeled_0612_manual__2025-06-13T23:37:16+00:00/ts/dataset",
                    "sampling_weight": 1.0,
                },]

  # T4
  #batch_size_train: 80
  #batch_size_val: 80

  # A100 40GB
  # batch_size_train: 384
  # batch_size_val: 384

  # # A100 80GB (one node!)
  # batch_size_train: 192
  # batch_size_val: 192

  # 4t4 Nodes
  batch_size_train: 80
  batch_size_val: 80

  num_workers_train: 8
  num_workers_val: 8
  prefetch_factor: 4
  dataset_augmentations:
    - augmentation_type: RandomRotation
      config:
        augmentation_keys:
          - segment_map_polyline
          - segment_map_stopline
          - segment_past_traffic_poses
          - segment_past_waypoints
          - segment_path
          - segment_reference_states
        degrees: [-5.0,5.0]
        probability: 0.5
    - augmentation_type: RandomTranslationAllExceptEgoChannels
      config:
        augmentation_keys:
          - segment_map_polyline
          - segment_map_stopline
          - segment_past_traffic_poses
          - segment_past_waypoints
          - segment_path
          - segment_reference_states
        metres: 2
        probability: 0.5
  label_weights:
    drive_off: 5.0
    stopping: 5.0
  random_dev_subset: 1.0
  random_train_subset: 1.0
  use_weighted_sampler: False
  shuffle: True
  set_speed_limits_zero: False
