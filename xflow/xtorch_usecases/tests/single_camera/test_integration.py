"""Smoke and integration tests for the single-camera use-case."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import sys
from enum import Enum
from pathlib import Path
from typing import NamedTuple
from unittest import mock

import onnx
import pytest
import yaml
from omegaconf import OmegaConf

from conversion.qnn.common import QnnModelArtifacts
from xtorch.testing.run_in_subprocess import run_in_subprocess
from xtorch_usecases.common.helpers import get_package_dir
from xtorch_usecases.common.pipeline import CONVERT_QNN_SUBFOLDER, EXPORT_SUBFOLDER, TRAIN_CHECKPOINT_SUBFOLDER
from xtorch_usecases.single_camera.config.single_camera import SINGLE_CAMERA_CONFIG_NAMES
from xtorch_usecases.single_camera.convert import main as convert_main
from xtorch_usecases.single_camera.evaluate import main as eval_main
from xtorch_usecases.single_camera.export import main as export_main
from xtorch_usecases.single_camera.predict import main as predict_main
from xtorch_usecases.single_camera.sequence_runner import main as sequence_runner_main
from xtorch_usecases.single_camera.train import main as train_main

_LOGGER = logging.getLogger(__name__)


class TestStage(str, Enum):
    """Stages that are tested in the integration test."""

    TRAIN = "train"
    TRAIN_FOLLOW_UP = "train_follow_up"
    EVALUATE_TORCH = "evaluate_torch"
    EVALUATE_QNN = "evaluate_qnn"
    PREDICT_TORCH = "predict_torch"
    EXPORT = "export"
    CONVERT = "convert"
    SEQUENCE_RUNNER = "sequence_runner"


class ModelArtifacts(NamedTuple):
    """Holds the model artifact and the respective number of tasks."""

    qnn_artifacts: QnnModelArtifacts
    num_tasks: int | None = None


def _get_skipped_stages_from_integration_test_config(task_config_name: str) -> list[TestStage]:
    """Get the skipped stages from the integration test config."""

    integration_test_config_path = (
        get_package_dir("xtorch_usecases")
        / "src"
        / "xtorch_usecases"
        / "single_camera"
        / "config"
        / "model"
        / task_config_name
        / "integration_test_config.yml"
    )

    if not integration_test_config_path.exists():
        # If the integration test config does not exist, return an empty list
        return []

    with integration_test_config_path.open("r") as f:
        integration_test_config = yaml.safe_load(f)

    # If skipped stages are not defined, return an empty list
    return integration_test_config.get("skipped_stages", [])


@pytest.mark.slow
@pytest.mark.lfs_dependencies(
    [
        "xtorch_usecases/tests/test_data/camera",
        "%datadir%/common/sequence_reader/18747b0c5db35e8be8f69b2b5d8f41e8b364798ad7ffe55511d51e1e9a2854af",
        "conversion/src/conversion/qnn/onnx_surgery/replacement_subgraphs/lsr_template_subgraph_trifocal_xtorch.onnx",
    ]
)
@pytest.mark.parametrize("task_config_name", SINGLE_CAMERA_CONFIG_NAMES)
def test_single_camera_multi_task(
    tmpdir_delete_after: Path,
    datadir: Path,
    task_config_name: str,
) -> None:
    """Test if stages work together.

    In order to skip this test run with `python -m pytest -m "not slow" <dir>`.
    """

    # GIVEN a integration test config specifying the skipped stages
    skipped_stages = _get_skipped_stages_from_integration_test_config(task_config_name)
    # GIVEN the specific model config and a few test args
    common_args_for_testing = [
        "--config-name",
        task_config_name,
        f"hydra.run.dir={tmpdir_delete_after!s}",  # Dump hydra logs into the test dir. Keep CWD clean.
        # we want to crash on data loading errors during the integration test
        "config.data.catch_errors_in_step_funcs=false",
    ]

    # ---- TRAINING STAGE ----
    train_outputs = None
    if TestStage.TRAIN not in skipped_stages:
        train_outputs = tmpdir_delete_after / "train"
        run_train_stage(common_args_for_testing, train_outputs)

    # ---- FOLLOW-UP TRAINING STAGE ----
    if TestStage.TRAIN_FOLLOW_UP not in skipped_stages:
        assert train_outputs is not None, "Train outputs are required for follow-up training."
        train_follow_up_outputs = tmpdir_delete_after / "train_follow_up"
        run_train_stage(
            common_args_for_testing,
            input_path=train_outputs,
            output_path=train_follow_up_outputs,
        )

    # ---- EVALUATION TORCH STAGE ----
    if TestStage.EVALUATE_TORCH not in skipped_stages:
        assert train_outputs is not None, "Train outputs are required for evaluation."
        eval_outputs = tmpdir_delete_after / "eval"
        run_eval_stage(
            common_args_for_testing,
            input_path=train_outputs,
            output_path=eval_outputs,
        )

    # ---- SEQUENCE RUNNER (must be skipped when no model was generated) ----
    if TestStage.SEQUENCE_RUNNER not in skipped_stages:
        assert train_outputs is not None, "train outputs are required for sequence runner."
        sequence_runner_outputs = tmpdir_delete_after / "sequence_runner_torch"
        run_sequence_runner(
            common_args_for_testing,
            input_path=train_outputs,
            output_path=sequence_runner_outputs,
            datadir=datadir,
            evaluate_torch=True,
        )

    # ---- PREDICTION TORCH STAGE ----
    # TODO: add Prediction QNN stage # noqa: TD003
    if TestStage.PREDICT_TORCH not in skipped_stages:
        assert train_outputs is not None, "Train outputs are required for inference."
        predict_outputs = tmpdir_delete_after / "predict"
        run_predict_stage(
            common_args_for_testing,
            input_path=train_outputs,
            output_path=predict_outputs,
        )

    # ---- EXPORT STAGE ----
    export_outputs = None
    if TestStage.EXPORT not in skipped_stages:
        assert train_outputs is not None, "Train outputs are required for export."
        export_outputs = tmpdir_delete_after / "export"
        run_export_stage(common_args_for_testing, input_path=train_outputs, output_path=export_outputs)

    # ---- CONVERT STAGE ----
    convert_outputs = None
    if TestStage.CONVERT not in skipped_stages:
        assert export_outputs is not None, "Export outputs are required for conversion."
        convert_outputs = tmpdir_delete_after / "convert"
        run_convert_stage(common_args_for_testing, input_path=export_outputs, output_path=convert_outputs)

    # ---- SEQUENCE RUNNER (must be skipped when no model was generated) ----
    if TestStage.CONVERT not in skipped_stages and TestStage.SEQUENCE_RUNNER not in skipped_stages:
        assert convert_outputs is not None, "Convert outputs are required for sequence runner."
        sequence_runner_outputs = tmpdir_delete_after / "sequence_runner_qnn"
        run_sequence_runner(
            common_args_for_testing,
            input_path=convert_outputs,
            output_path=sequence_runner_outputs,
            datadir=datadir,
            evaluate_torch=False,
        )

    # ---- EVALUATION QNN STAGE ----
    if TestStage.EVALUATE_QNN not in skipped_stages:
        assert convert_outputs is not None, "Convert outputs are required for evaluation."
        eval_qnn_outputs = tmpdir_delete_after / "eval_qnn"
        run_eval_stage(
            common_args_for_testing, input_path=convert_outputs, output_path=eval_qnn_outputs, evaluate_torch=False
        )


@run_in_subprocess(context="spawn")
def run_train_stage(
    train_args: list[str],
    output_path: Path,
    input_path: Path | None = None,
) -> None:
    """Test the train stage."""
    # GIVEN the specific model config and a few test args
    train_args_for_testing = train_args + [
        f"config.input_folder={input_path!s}",
        f"config.output_folder={output_path!s}",
        f"config.trainer.default_root_dir={output_path!s}",
        f"config.callback_config.output_root_dir={output_path!s}",
        "config.trainer.train_epoch_length=2",
        "config.trainer.log_every_n_steps=1",
        "config.trainer.max_epochs=2",
        "config.data.batch_size_train=2",
        "config.data.batch_size_val=2",
        "config.run_mode=SHORT",
    ]

    # WHEN the train stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + train_args_for_testing):
        train_main()

    # THEN checkpoints have been written
    last_ckpt_file = Path(f"{output_path.parent!s}/train/{TRAIN_CHECKPOINT_SUBFOLDER!s}/last.ckpt")
    assert last_ckpt_file.exists(), "Checkpoint file not found."

    # WHEN the config file path is collected
    composed_config_files = list(output_path.glob("composed_config.yaml"))

    # THEN the composed config file is found
    assert composed_config_files, "No composed config found."
    assert len(composed_config_files) == 1, "More than one composed config found."

    # WHEN the composed config is loaded
    composed_config = OmegaConf.load(composed_config_files[0])

    # THEN we print the config for debugging
    _LOGGER.info(f"Composed config: {OmegaConf.to_yaml(composed_config)}")

    # THEN the output directory is not empty
    assert next(output_path.iterdir(), None) is not None, "Output directory is empty."


@run_in_subprocess(context="spawn")
def run_eval_stage(
    common_args: list[str],
    input_path: Path,
    output_path: Path,
    *,
    evaluate_torch: bool = True,
) -> None:
    """Test the eval stage."""
    # GIVEN the specific model config and a few test args
    eval_args_for_testing = common_args + [
        f"config.output_folder={output_path!s}",
        f"config.trainer.default_root_dir={output_path!s}",
        f"config.callback_config.output_root_dir={output_path!s}",
        "config.trainer.limit_predict_batches=2",
        f"config.input_folder={input_path!s}",
        f"config.eval_config.evaluate_torch={evaluate_torch}",
    ]

    # WHEN the eval stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + eval_args_for_testing):
        eval_main()

    # WHEN the config file path is collected
    composed_config_files = list(output_path.glob("composed_config.yaml"))

    # THEN the composed config file is found
    assert composed_config_files, "No composed config found."
    assert len(composed_config_files) == 1, "More than one composed config found."

    # WHEN the composed config is loaded
    composed_config = OmegaConf.load(composed_config_files[0])

    # THEN we print the config for debugging
    _LOGGER.info(f"Composed config: {OmegaConf.to_yaml(composed_config)}")

    # THEN the output directory is not empty
    assert next(output_path.iterdir(), None) is not None, "Output directory is empty."


@run_in_subprocess(context="spawn")
def run_predict_stage(common_args: list[str], input_path: Path, output_path: Path) -> None:
    """Test the predict stage."""
    # GIVEN the specific model config and a few test args
    predict_args_for_testing = common_args + [
        f"config.output_folder={output_path!s}",
        f"config.trainer.default_root_dir={output_path!s}",
        f"config.callback_config.output_root_dir={output_path!s}",
        "config.trainer.limit_predict_batches=2",
        f"config.input_folder={input_path!s}",
        "config.checkpoint='checkpoints'",
    ]

    # WHEN the predict stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + predict_args_for_testing):
        predict_main()

    # WHEN the config file path is collected
    composed_config_files = list(output_path.glob("composed_config.yaml"))

    # THEN the composed config file is found
    assert composed_config_files, "No composed config found."
    assert len(composed_config_files) == 1, "More than one composed config found."

    # THEN the composed config can be loaded
    OmegaConf.load(composed_config_files[0])

    # THEN the output directory is not empty
    assert next(output_path.iterdir(), None) is not None, "Output directory is empty."


@run_in_subprocess(context="spawn")
def run_export_stage(common_args: list[str], input_path: Path, output_path: Path) -> None:
    """Test the export stage."""
    # GIVEN the specific model config and a few test args
    export_args_for_testing = common_args + [
        f"config.output_folder={output_path!s}",
        f"config.trainer.default_root_dir={output_path!s}",
        f"config.callback_config.output_root_dir={output_path!s}",
        f"config.input_folder={input_path!s}",
    ]

    # WHEN the export stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + export_args_for_testing):
        export_main()

    # THEN the output directory is not empty
    assert next(output_path.iterdir(), None) is not None, "Output directory is empty."

    config_file_name = common_args[1]
    model_name = Path(config_file_name).stem
    output_dir = output_path / EXPORT_SUBFOLDER

    if Path(config_file_name).stem != "fc1_yuv444_multi_task_trifocal":
        model = onnx.load(output_dir / f"{model_name}.onnx")
        input_names = [i.name for i in model.graph.input]
        assert input_names in (
            ["single_view"],
            ["far", "mid", "wide"],
            ["single_view", "focal_length", "principal_point", "extrinsics"],
        )
    else:
        model_paths = output_dir.glob(f"{model_name}_*.onnx")
        assert len(list(model_paths)) == 4


@run_in_subprocess(context="spawn")
def run_convert_stage(common_args: list[str], input_path: Path, output_path: Path) -> None:
    """Test the convert stage."""
    # GIVEN the specific model config and a few test args
    convert_args_for_testing = common_args + [
        f"config.output_folder={output_path!s}",
        f"config.trainer.default_root_dir={output_path!s}",
        f"config.callback_config.output_root_dir={output_path!s}",
        f"config.input_folder={input_path!s}",
        "config.convert_config.num_calib_samples=2",
        "config.convert_config.quant_check_config.short_run=True",
    ]

    # WHEN the convert stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + convert_args_for_testing):
        convert_main()

    # THEN the output directory is not empty
    qnn_artifacts_path_htp = output_path / CONVERT_QNN_SUBFOLDER / "htp"
    assert next(qnn_artifacts_path_htp.iterdir(), None) is not None, "Output directory is empty."

    # THEN the output directory is not empty
    qnn_artifacts_path_cpu = output_path / CONVERT_QNN_SUBFOLDER / "cpu"
    assert next(qnn_artifacts_path_cpu.iterdir(), None) is not None, "Output directory is empty."

    # THEN the output directory is not empty
    qnn_artifacts_path_checker = output_path / CONVERT_QNN_SUBFOLDER / "quantization_checker"
    assert next(qnn_artifacts_path_checker.iterdir(), None) is not None, "Output directory is empty."

    config_file_name = common_args[1]

    model_artifacts = []
    if Path(config_file_name).stem != "fc1_yuv444_multi_task_trifocal":
        model_artifacts.append(
            ModelArtifacts(qnn_artifacts=QnnModelArtifacts(qnn_artifacts_path_htp, model_name=None), num_tasks=None)
        )

    else:
        # for multi_task_trifocal multiple models are generated so we need to provide a model_name
        model_artifacts.append(
            ModelArtifacts(
                qnn_artifacts=QnnModelArtifacts(
                    qnn_artifacts_path_htp, model_name="fc1_yuv444_multi_task_trifocal_eval_qc"
                ),
                num_tasks=8,
            )
        )
        model_artifacts.append(
            ModelArtifacts(
                qnn_artifacts=QnnModelArtifacts(
                    qnn_artifacts_path_htp, model_name="fc1_yuv444_multi_task_trifocal_long"
                ),
                num_tasks=4,
            )
        )
        model_artifacts.append(
            ModelArtifacts(
                qnn_artifacts=QnnModelArtifacts(
                    qnn_artifacts_path_htp, model_name="fc1_yuv444_multi_task_trifocal_short"
                ),
                num_tasks=2,
            )
        )

        model_artifacts.append(
            ModelArtifacts(
                qnn_artifacts=QnnModelArtifacts(
                    qnn_artifacts_path_htp, model_name="fc1_yuv444_multi_task_trifocal_active_learning"
                ),
                num_tasks=6,
            )
        )

    for model_artifact in model_artifacts:
        # THEN the output directory contains the metadata
        assert model_artifact.qnn_artifacts.model_metadata_path().exists()
        model_metadata = model_artifact.qnn_artifacts.model_metadata()
        assert model_metadata is not None
        assert Path(common_args[1]).stem in model_metadata.metadata.model_name
        if model_artifact.num_tasks:
            assert len(model_metadata.tasks) == model_artifact.num_tasks


@run_in_subprocess(context="spawn")
def run_sequence_runner(
    common_args: list[str], input_path: Path, output_path: Path, datadir: Path, *, evaluate_torch: bool
) -> None:
    """Test the sequence runner."""
    # GIVEN the specific model config and a few test args
    scene_file = (
        datadir
        / "common"
        / "sequence_reader"
        / "18747b0c5db35e8be8f69b2b5d8f41e8b364798ad7ffe55511d51e1e9a2854af"
        / "ALLIANCE-CL04_BSVF664E-D-001_20250305_190101.scene"
    )

    args_for_testing = common_args + [
        f"config.output_folder={output_path!s}",
        f"config.callback_config.image_root_dir={output_path / 'images'!s}",
        "config.trainer.limit_predict_batches=1",
        f"config.eval_config.evaluate_torch={evaluate_torch}",
        f"config.input_folder={input_path!s}",
        f"config.cassandra_scene={scene_file!s}",
    ]
    if evaluate_torch:
        args_for_testing += [
            "config.checkpoint=checkpoints/last.ckpt",
        ]
    else:
        args_for_testing += [
            "config.eval_config.qnn.backend=CPU",
            f"config.eval_config.qnn.model_folder={input_path / 'qnn' / 'cpu'!s}",
        ]

    # WHEN the sequence runner is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + args_for_testing):
        sequence_runner_main()

    # THEN there is one (as requested by the command line args) image.
    outputs = (output_path / "images").rglob("*.jpeg")
    assert len(list(outputs)) == 1
