"""Pytest for the the SparseLanecpp head module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from dataclasses import dataclass
from typing import Any

import numpy as np
import pytest
import torch

from xcontract.data.definitions.image import HW
from xcontract.geometry.definitions.volume import VolumeRange
from xtorch_usecases.single_camera.network import BIFPN_CHANNELS
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.anchors import LineRepresentation
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppHeadInput,
    SparseLanecppHeadOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.head import (
    IGNORE_VALUE,
    SparseLanecppHead,
    SparseLanecppTransformerOutput,
    _join_transformer_outputs,
)

_NUM_QUERIES_LANES_ROAD_EDGES = 16
_NUM_QUERIES_HOLISTIC_EGO_LANES = 4
_TARGET_SHAPE = HW(512, 1024)
_NUM_CLASSES_HOLISTIC_EGO_LANES = 2
_NUM_CLASSES_LANES_ROAD_EDGES = 17
_NUM_CLASSES = _NUM_CLASSES_HOLISTIC_EGO_LANES + _NUM_CLASSES_LANES_ROAD_EDGES - 1
_NUM_POINTS_PER_LINE = 30
_ANCHOR_Y_STEPS_LEN = 30
_ANCHOR_Y_STEPS_DENSE_LEN = 90
_POSITION_RANGE = VolumeRange(-30.0, -17.0, -10.0, 30.0, 223.0, 10.0)
_ATTENTION_NONLINEARITY = "relu"


@dataclass(kw_only=True)
class ConcreteSparseLanecppHeadInput(SparseLanecppHeadInput):
    """implementation of SparseLanecppHeadInput providing the stride and input param."""

    stride_8: torch.Tensor
    stride_16: torch.Tensor
    stride_32: torch.Tensor


@pytest.fixture(name="head", params=[True, False])
def fixture_head(request: pytest.FixtureRequest) -> SparseLanecppHead:
    """Provides the module under test in train/infer modes."""
    anchor_y_steps = np.linspace(0, 1, _ANCHOR_Y_STEPS_LEN, dtype=np.float32)  # Use float64
    anchor_y_steps_dense = np.linspace(0, 1, _ANCHOR_Y_STEPS_DENSE_LEN, dtype=np.float32)  # 1D array
    head = SparseLanecppHead(
        target_shape=_TARGET_SHAPE,
        num_classes_holistic_ego_lanes=_NUM_CLASSES_HOLISTIC_EGO_LANES,
        num_classes_lanes_road_edges=_NUM_CLASSES_LANES_ROAD_EDGES,
        num_queries_holistic_ego_lanes=_NUM_QUERIES_HOLISTIC_EGO_LANES,
        num_queries_lanes_road_edges=_NUM_QUERIES_LANES_ROAD_EDGES,
        num_points_per_line=_NUM_POINTS_PER_LINE,
        anchor_y_steps=anchor_y_steps,
        anchor_y_steps_dense=anchor_y_steps_dense,
        position_range=_POSITION_RANGE,
        attention_nonlinearity=_ATTENTION_NONLINEARITY,
        line_representation=LineRepresentation.CATMULL_ROM,
    )
    if request.param:
        head.infer()
    return head


@pytest.fixture(name="inputs")
def fixture_inputs() -> tuple[ConcreteSparseLanecppHeadInput, dict[str, torch.Tensor]]:
    """Provides the inputs."""
    batch_size = 8
    stride_8 = torch.rand(batch_size, BIFPN_CHANNELS, _TARGET_SHAPE.height // 8, _TARGET_SHAPE.width // 8)
    stride_16 = torch.rand(batch_size, 192, _TARGET_SHAPE.height // 16, _TARGET_SHAPE.width // 16)
    stride_32 = torch.rand(batch_size, 384, _TARGET_SHAPE.height // 32, _TARGET_SHAPE.width // 32)
    inputs = ConcreteSparseLanecppHeadInput(
        stride_8=stride_8,
        stride_16=stride_16,
        stride_32=stride_32,
    )
    model_inputs = {
        "principal_point": torch.rand(batch_size, 2),
        "focal_length": torch.rand(batch_size, 2),
        "camera_model_type": torch.ones(batch_size, 1, dtype=torch.int8),
        "extrinsics": torch.eye(4).unsqueeze(0).repeat(batch_size, 1, 1),
    }
    return inputs, model_inputs


def test_sparse_lanecpp_head_forward(
    head: SparseLanecppHead, inputs: tuple[ConcreteSparseLanecppHeadInput, dict[str, torch.Tensor]]
) -> None:
    """Test the forward pass of SparseLanecppHead."""
    # GIVEN a SparseLanecppHead instance
    # GIVEN mocked head inputs
    # GIVEN mocked camera parameters
    batch_size = inputs[0].stride_8.size(0)

    # WHEN calling the forward method
    output = head.forward(*inputs)

    # THEN the output should have the expected shapes
    assert isinstance(output, SparseLanecppHeadOutput)
    assert (output.scaled_output is None) == head.inferencing
    assert (output.lanes_dense is None) == head.inferencing
    num_queries = _NUM_QUERIES_HOLISTIC_EGO_LANES + _NUM_QUERIES_LANES_ROAD_EDGES
    num_transformer_decoders = len(head._lane_road_edge_transformer._decoder._decoder_layers)  # noqa: SLF001
    if output.scaled_output is not None and output.lanes_dense is not None:
        assert output.raw_output.class_scores.shape == (num_transformer_decoders, batch_size, num_queries, _NUM_CLASSES)
        assert output.scaled_output.lanes_x.shape == (
            num_transformer_decoders,
            batch_size,
            num_queries,
            _NUM_POINTS_PER_LINE,
        )
        assert output.scaled_output.lanes_z.shape == (
            num_transformer_decoders,
            batch_size,
            num_queries,
            _NUM_POINTS_PER_LINE,
        )
        assert output.scaled_output.lanes_visibility.shape == (
            num_transformer_decoders,
            batch_size,
            num_queries,
            _NUM_POINTS_PER_LINE,
        )
        assert output.lanes_dense.shape == (
            num_transformer_decoders,
            batch_size,
            num_queries,
            _ANCHOR_Y_STEPS_DENSE_LEN * 3,
        )


class _BugWorkaround(torch.nn.Module):
    """Workaround a bug.

    Internally, the traced module passes itself to an untraced function. This doesn't work when the module
    itself is the tracing entry point. Therefore the module is wrapped into this extra class.
    """

    def __init__(self, head: SparseLanecppHead) -> None:
        """Inits."""
        super().__init__()
        self.head = head

    def forward(self, a: Any, b: Any) -> SparseLanecppHead:
        """Forwards."""
        return self.head(a, b)


def test_sparse_lanecpp_head_tracing(
    head: SparseLanecppHead,
    inputs: tuple[ConcreteSparseLanecppHeadInput, dict[str, torch.Tensor]],
) -> None:
    """Test if the head works with symbolic_trace."""
    # GIVEN a SparseLanecppHead instance and reference outputs
    reference_out = head(*inputs)  # Also initializes lazy initialized members

    # WHEN traced
    traced = torch.fx.symbolic_trace(_BugWorkaround(head))

    # THEN the traced version generates outputs with the same optional fields as the original model.
    traced_out = traced(*inputs)

    assert isinstance(traced_out, SparseLanecppHeadOutput)
    assert isinstance(reference_out, SparseLanecppHeadOutput)
    assert (reference_out.scaled_output is None) == (traced_out.scaled_output is None)
    assert (reference_out.instance_output is None) == (traced_out.instance_output is None)


def test_sparse_lanecpp_head_join_transformer_outputs() -> None:
    """Tests joining the split transformer outputs within the SparseLanecppHead."""
    # GIVEN a SparseLanecppHead
    # GIVEN split transformer outputs for Holistic Ego Lanes & Lanes + Road
    batch_size = 8
    num_decoders = 6
    lanes_road_edges = SparseLanecppTransformerOutput(
        classes=torch.rand(
            num_decoders,
            batch_size,
            _NUM_QUERIES_LANES_ROAD_EDGES,
            _NUM_CLASSES_LANES_ROAD_EDGES,
        ),
        coordinates=torch.rand(
            num_decoders,
            batch_size,
            _NUM_QUERIES_LANES_ROAD_EDGES * _NUM_POINTS_PER_LINE,
            2,
        ),
        visibility=torch.rand(
            num_decoders,
            batch_size,
            _NUM_QUERIES_LANES_ROAD_EDGES,
            _NUM_POINTS_PER_LINE,
        ),
    )

    holistic_ego_lanes = SparseLanecppTransformerOutput(
        classes=torch.rand(
            num_decoders,
            batch_size,
            _NUM_QUERIES_HOLISTIC_EGO_LANES,
            _NUM_CLASSES_HOLISTIC_EGO_LANES,
        ),
        coordinates=torch.rand(
            num_decoders,
            batch_size,
            _NUM_QUERIES_HOLISTIC_EGO_LANES * _NUM_POINTS_PER_LINE,
            2,
        ),
        visibility=torch.rand(
            num_decoders,
            batch_size,
            _NUM_QUERIES_HOLISTIC_EGO_LANES,
            _NUM_POINTS_PER_LINE,
        ),
    )

    # WHEN joining the outputs
    joined_output = _join_transformer_outputs(
        lanes_road_edges,
        holistic_ego_lanes,
        num_classes_holistic_ego_lanes=_NUM_CLASSES_HOLISTIC_EGO_LANES,
        num_classes_lanes_road_edges=_NUM_CLASSES_LANES_ROAD_EDGES,
    )

    # THEN all output shapes are as expected
    assert joined_output.classes.shape == (
        num_decoders,
        batch_size,
        _NUM_QUERIES_LANES_ROAD_EDGES + _NUM_QUERIES_HOLISTIC_EGO_LANES,
        _NUM_CLASSES,
    )
    assert joined_output.coordinates.shape == (
        num_decoders,
        batch_size,
        (_NUM_QUERIES_LANES_ROAD_EDGES + _NUM_QUERIES_HOLISTIC_EGO_LANES) * _NUM_POINTS_PER_LINE,
        2,
    )
    assert joined_output.visibility.shape == (
        num_decoders,
        batch_size,
        _NUM_QUERIES_LANES_ROAD_EDGES + _NUM_QUERIES_HOLISTIC_EGO_LANES,
        _NUM_POINTS_PER_LINE,
    )

    # THEN class 0 is the joint background class. Lane-Background for first queries, HEL-Background for last entries
    torch.testing.assert_close(
        joined_output.classes[:, :, :_NUM_QUERIES_LANES_ROAD_EDGES, 0], lanes_road_edges.classes[:, :, :, 0]
    )
    torch.testing.assert_close(
        joined_output.classes[:, :, _NUM_QUERIES_LANES_ROAD_EDGES:, 0], holistic_ego_lanes.classes[:, :, :, 0]
    )

    # THEN the lane + road edge classes are set for Lane + Road Edge queries
    torch.testing.assert_close(
        joined_output.classes[:, :, :_NUM_QUERIES_LANES_ROAD_EDGES, 1:_NUM_CLASSES_LANES_ROAD_EDGES],
        lanes_road_edges.classes[:, :, :, 1:],
    )

    # THEN the HEL classes are set for HEL queries
    torch.testing.assert_close(
        joined_output.classes[
            :, :, -_NUM_QUERIES_HOLISTIC_EGO_LANES:, _NUM_CLASSES_LANES_ROAD_EDGES : _NUM_CLASSES_LANES_ROAD_EDGES + 1
        ],
        holistic_ego_lanes.classes[:, :, :, 1:],
    )

    # THEN the Lane + Road Edge queries have ignore-values for HEL class
    torch.testing.assert_close(
        joined_output.classes[:, :, :_NUM_QUERIES_LANES_ROAD_EDGES, _NUM_CLASSES_LANES_ROAD_EDGES:],
        torch.full(
            (num_decoders, batch_size, _NUM_QUERIES_LANES_ROAD_EDGES, _NUM_CLASSES - _NUM_CLASSES_LANES_ROAD_EDGES),
            IGNORE_VALUE,
        ),
    )
    # THEN the HEL queries have ignore-values for Lane + Road Edge classes
    torch.testing.assert_close(
        joined_output.classes[:, :, -_NUM_QUERIES_HOLISTIC_EGO_LANES:, 1:_NUM_CLASSES_LANES_ROAD_EDGES],
        torch.full(
            (num_decoders, batch_size, _NUM_QUERIES_HOLISTIC_EGO_LANES, _NUM_CLASSES_LANES_ROAD_EDGES - 1),
            IGNORE_VALUE,
        ),
    )
