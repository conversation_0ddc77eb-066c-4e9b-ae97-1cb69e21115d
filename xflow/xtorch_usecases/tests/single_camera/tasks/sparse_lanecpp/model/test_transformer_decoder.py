"""Pytest for SparseLanecpp Transformer Decoder."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Any

import numpy as np
import pytest
import torch

from xcontract.data.definitions.image import HW
from xtorch.nn.heads.lane3d.ms_deformable_attention_3d import VolumeRange
from xtorch.nn.heads.lane3d.transformer_decoder_layer import TransformerDecoderLayerParams
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.transformer_decoder import (
    SparseLanecppTransformerDecoder,
    SparseLanecppTransformerDecoderInput,
)


@pytest.fixture
def decoder_init_args() -> dict[str, Any]:
    """Fixture for initialization arguments of SparseLanecppTransformerDecoder."""

    transformer_decoder_layer_params = TransformerDecoderLayerParams(
        operation_order=["self_attn", "norm", "cross_attn", "norm", "ffn", "norm"],
        num_pt_per_query=30,
        image_shape=HW(512, 1024),
        point_cloud_range=VolumeRange(-30.0, -17.0, -10.0, 30.0, 223.0, 10.0),
        attention_nonlinearity="relu",
        num_heads=4,
        num_levels=1,
        num_points=30,
        ffnn_intermediate_dims_multiplier=4,
        dropout=0.1,
        points_as_query=True,
        export_use_pinhole_model=False,
    )

    return {
        "anchor_y_steps": np.array(
            [
                -4.40740741,
                3.0,
                10.40740741,
                17.81481481,
                25.22222222,
                32.62962963,
                40.03703704,
                47.44444444,
                54.85185185,
                62.25925926,
                69.66666667,
                77.07407407,
                84.48148148,
                91.88888889,
                99.2962963,
                106.7037037,
                114.11111111,
                121.51851852,
                128.92592593,
                136.33333333,
                143.74074074,
                151.14814815,
                158.55555556,
                165.96296296,
                173.37037037,
                180.77777778,
                188.18518519,
                195.59259259,
                203.0,
                210.40740741,
            ]
        ),
        "embed_dims": 128,
        "num_query": 16,
        "num_points_per_anchor": 1,
        "num_classes": 23,
        "num_decoder_layers": 6,
        "num_output_layers": 2,
        "num_anchor_per_query": 30,
        "with_iterative_refinement": False,
        "use_sigmoid_for_classification": True,
        "transformer_decoder_layer_params": transformer_decoder_layer_params,
    }


def test_sparse_lanecpp_transformer_decoder_initialization(decoder_init_args: dict[str, Any]) -> None:
    """Test that SparseLanecppTransformerDecoder initializes without errors."""
    # GIVEN a SparseLanecppTransformerDecoder instance
    # WHEN initializing with the provided arguments
    # THEN it should not raise any exceptions
    decoder = SparseLanecppTransformerDecoder(**decoder_init_args)
    assert isinstance(decoder, SparseLanecppTransformerDecoder)


@pytest.fixture(name="decoder_inputs")
def fixture_decoder_inputs() -> SparseLanecppTransformerDecoderInput:
    """Fixture for inputs to the forward function of SparseLanecppTransformerDecoder."""
    batch_size = 8
    num_query = 16
    embed_dims = 128
    num_anchor_per_query = 30
    spatial_shapes = (64, 128)
    num_flattened_features = 64 * 128

    return SparseLanecppTransformerDecoderInput(
        query=torch.randn(batch_size, num_query * num_anchor_per_query, embed_dims),
        key=torch.randn(batch_size, num_flattened_features, embed_dims),
        value=[torch.randn(batch_size, embed_dims, *spatial_shapes)],
        query_pos=torch.randn(batch_size, num_query * num_anchor_per_query, embed_dims),
        key_pos=None,
        principal_point=torch.randn(batch_size, 2),
        focal_length=torch.randn(batch_size, 2),
        is_pinhole_model=torch.full((batch_size, 1), 0, dtype=torch.bool),
        extrinsics=torch.randn(batch_size, 4, 4),
        sin_embed=[torch.randn(batch_size, embed_dims, *spatial_shapes)],
        reference_points_x=torch.randn(batch_size, num_query * num_anchor_per_query, 1),
        reference_points_z=torch.randn(batch_size, num_query * num_anchor_per_query, 1),
        spatial_shapes=spatial_shapes,
    )


def test_sparse_lanecpp_transformer_decoder_forward(
    decoder_init_args: dict[str, Any], decoder_inputs: SparseLanecppTransformerDecoderInput
) -> None:
    """Test the forward function of SparseLanecppTransformerDecoder."""
    # GIVEN a SparseLanecppTransformerDecoder instance
    decoder = SparseLanecppTransformerDecoder(**decoder_init_args)

    # WHEN calling the forward function with the provided inputs
    outputs_classes, outputs_coords, outputs_vis = decoder.forward(decoder_inputs)

    # THEN the outputs should have the expected shapes
    batch_size = decoder_inputs.query.shape[0]
    num_query = decoder_init_args["num_query"]
    num_classes = decoder_init_args["num_classes"]
    num_anchor_per_query = decoder_init_args["num_anchor_per_query"]
    num_points_per_anchor = decoder_init_args["num_points_per_anchor"]
    num_decoder_layers = decoder_init_args["num_decoder_layers"]

    assert outputs_classes.shape == (num_decoder_layers, batch_size, num_query, num_classes)
    assert outputs_coords.shape == torch.Size(
        [num_decoder_layers, batch_size, num_query * num_anchor_per_query * num_points_per_anchor, 2]
    )
    assert outputs_vis.shape == torch.Size(
        [num_decoder_layers, batch_size, num_query * num_anchor_per_query * num_points_per_anchor, 1]
    )
