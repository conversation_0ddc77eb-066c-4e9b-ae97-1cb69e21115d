"""Module for testing SparseLanecpp Transformer."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Any

import numpy as np
import pytest
import torch

from xcontract.data.definitions.image import HW
from xtorch.nn.heads.lane3d.ms_deformable_attention_3d import VolumeRange
from xtorch.nn.heads.lane3d.transformer_decoder_layer import TransformerDecoderLayerParams
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.transformer import (
    SparseLanecppTransformer,
    SparseLanecppTransformerInput,
)


@pytest.fixture(name="transformer_params")
def transformer_params() -> dict[str, Any]:
    """Fixture to provide shared parameters for SparseLanecppTransformer tests."""

    transformer_decoder_layer_params = TransformerDecoderLayerParams(
        operation_order=["self_attn", "norm", "cross_attn", "norm", "ffn", "norm"],
        num_pt_per_query=30,
        image_shape=HW(512, 1024),
        point_cloud_range=VolumeRange(-30.0, -17.0, -10.0, 30.0, 223.0, 10.0),
        attention_nonlinearity="relu",
        num_heads=4,
        num_levels=1,
        num_points=30,
        ffnn_intermediate_dims_multiplier=4,
        dropout=0.1,
        points_as_query=True,
        export_use_pinhole_model=False,
    )

    return {
        "num_classes": 23,
        "embed_dims": 128,
        "feature_width": 1024 // 8,
        "feature_height": 512 // 8,
        "num_query": 16,
        "num_anchor_per_query": 30,
        "num_points_per_anchor": 1,
        "anchor_y_steps": np.linspace(-4.40740741, 210.40740741, 30).tolist(),
        "num_decoder_layers": 6,
        "num_output_layers": 2,
        "transformer_decoder_layer_params": transformer_decoder_layer_params,
        "with_iterative_refinement": False,
        "use_sigmoid_for_classification": True,
        "batch_size": 2,
    }


def test_transformer_forward(transformer_params: dict[str, Any]) -> None:
    """Test the forward pass of SparseLanecppTransformer."""

    # GIVEN valid input
    transformer = SparseLanecppTransformer(
        num_classes=transformer_params["num_classes"],
        embed_dims=transformer_params["embed_dims"],
        feature_dim=HW(transformer_params["feature_height"], transformer_params["feature_width"]),
        num_query=transformer_params["num_query"],
        num_anchor_per_query=transformer_params["num_anchor_per_query"],
        num_points_per_anchor=transformer_params["num_points_per_anchor"],
        anchor_y_steps=transformer_params["anchor_y_steps"],
        num_decoder_layers=transformer_params["num_decoder_layers"],
        num_output_layers=transformer_params["num_output_layers"],
        transformer_decoder_layer_params=transformer_params["transformer_decoder_layer_params"],
        with_iterative_refinement=transformer_params["with_iterative_refinement"],
        use_sigmoid_for_classification=transformer_params["use_sigmoid_for_classification"],
    )

    img_feats = torch.rand(
        transformer_params["batch_size"],
        transformer_params["embed_dims"],
        transformer_params["feature_height"],
        transformer_params["feature_width"],
    )
    query = torch.rand(
        transformer_params["batch_size"],
        transformer_params["num_query"] * transformer_params["num_anchor_per_query"],
        transformer_params["embed_dims"],
    )
    query_embed = torch.rand(
        transformer_params["batch_size"],
        transformer_params["num_query"] * transformer_params["num_anchor_per_query"],
        transformer_params["embed_dims"],
    )
    reference_points_x = torch.rand(
        transformer_params["batch_size"],
        transformer_params["num_query"] * transformer_params["num_anchor_per_query"],
        1,
    )
    reference_points_z = torch.rand(
        transformer_params["batch_size"],
        transformer_params["num_query"] * transformer_params["num_anchor_per_query"],
        1,
    )
    principal_point = torch.rand(transformer_params["batch_size"], 2)
    focal_length = torch.rand(transformer_params["batch_size"], 2)
    is_pinhole_model = torch.ones(transformer_params["batch_size"], 1, dtype=torch.bool)
    extrinsics = torch.eye(4).unsqueeze(0).repeat(transformer_params["batch_size"], 1, 1)

    transformer_input = SparseLanecppTransformerInput(
        x=img_feats,
        query=query,
        query_embed=query_embed,
        pos_embed=None,
        reference_points_x=reference_points_x,
        reference_points_z=reference_points_z,
        principal_point=principal_point,
        focal_length=focal_length,
        is_pinhole_model=is_pinhole_model,
        extrinsics=extrinsics,
    )

    # WHEN the forward pass is called
    outputs_classes, outputs_coords, outputs_vis = transformer(transformer_input)

    # THEN the output should match expected output shape
    assert outputs_classes.shape == (
        transformer_params["num_decoder_layers"],
        transformer_params["batch_size"],
        transformer_params["num_query"],
        transformer_params["num_classes"],
    )
    assert outputs_coords.shape == torch.Size(
        [
            transformer_params["num_decoder_layers"],
            transformer_params["batch_size"],
            transformer_params["num_query"]
            * transformer_params["num_anchor_per_query"]
            * transformer_params["num_points_per_anchor"],
            2,
        ]
    )
    assert outputs_vis.shape == torch.Size(
        [
            transformer_params["num_decoder_layers"],
            transformer_params["batch_size"],
            transformer_params["num_query"]
            * transformer_params["num_anchor_per_query"]
            * transformer_params["num_points_per_anchor"],
            1,
        ]
    )
