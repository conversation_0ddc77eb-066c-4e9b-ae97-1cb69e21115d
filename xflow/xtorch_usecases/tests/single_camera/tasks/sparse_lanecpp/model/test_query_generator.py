"""Module for testing the query generator."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import io
from typing import Final
from unittest.mock import MagicMock

import pytest
import torch

from xcontract.data.definitions.image import HW
from xtorch.export.tracing_adapter import TracingAdapter
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.query_generator import (
    SparseInstanceDecoder,
    SparseLanecppQueryGenerator,
    _InstanceBranch,
    _MaskBranch,
)

torch.manual_seed(42)

_BATCH_SIZE: Final = 2
_FEATURE_DIMS: Final = HW(8, 16)
EMBEDDING_DIMENSION: Final = 4
NUM_CLASSES: Final = 5
NUM_QUERIES: Final = 6
NUM_POINTS_PER_LINE: Final = 10
SCALE_FACTOR: Final = 1.0


@pytest.fixture(name="image_features", scope="module")
def fixture_image_features() -> torch.Tensor:
    """Generates random image features."""
    return torch.rand((_BATCH_SIZE, EMBEDDING_DIMENSION, _FEATURE_DIMS.height, _FEATURE_DIMS.width))


def test_query_generator_init() -> None:
    """Test the initialization of the query generator class."""

    # GIVEN query generator parameters

    # WHEN initializing the lanecpp query generator
    query_generator = SparseLanecppQueryGenerator(EMBEDDING_DIMENSION, NUM_QUERIES, NUM_CLASSES, NUM_POINTS_PER_LINE)

    # THEN instance variables should be set correctly
    assert query_generator._num_points_per_anchor == 1  # noqa: SLF001
    assert query_generator._point_embedding.shape == (NUM_POINTS_PER_LINE, EMBEDDING_DIMENSION)  # noqa: SLF001
    assert (
        isinstance(query_generator._sparse_instance_decoder, SparseInstanceDecoder)  # noqa: SLF001
        or isinstance(query_generator._lane_embedding, torch.nn.Embedding)  # noqa: SLF001
    )


def test_query_generator_forward(image_features: torch.Tensor) -> None:
    """Test the forward method of the query generator class."""

    # GIVEN a query generator and image features
    query_generator = SparseLanecppQueryGenerator(EMBEDDING_DIMENSION, NUM_QUERIES, NUM_CLASSES, NUM_POINTS_PER_LINE)

    # WHEN calling the forward method
    query_output = query_generator(image_features)

    # THEN returned tensors should have the expected shapes
    assert query_output.query_embeds.shape == (_BATCH_SIZE, NUM_QUERIES * NUM_POINTS_PER_LINE, EMBEDDING_DIMENSION)
    assert query_output.reference_points_x.shape == (_BATCH_SIZE, NUM_QUERIES * NUM_POINTS_PER_LINE, 1)
    assert query_output.reference_points_z.shape == (_BATCH_SIZE, NUM_QUERIES * NUM_POINTS_PER_LINE, 1)
    assert query_output.instance_decoder_output.pred_logits.shape == (_BATCH_SIZE, NUM_QUERIES, NUM_CLASSES)
    assert query_output.instance_decoder_output.pred_kernel.shape == (_BATCH_SIZE, NUM_QUERIES, EMBEDDING_DIMENSION)
    assert query_output.instance_decoder_output.pred_scores.shape == (_BATCH_SIZE, NUM_QUERIES, 1)
    assert query_output.instance_decoder_output.queries.shape == (_BATCH_SIZE, NUM_QUERIES, EMBEDDING_DIMENSION)
    assert query_output.instance_decoder_output.iam_prob.shape == (
        _BATCH_SIZE,
        NUM_QUERIES,
        _FEATURE_DIMS.height * _FEATURE_DIMS.width,
    )

    assert query_output.instance_decoder_output.pred_masks.shape == (
        _BATCH_SIZE,
        NUM_QUERIES,
        _FEATURE_DIMS.height,
        _FEATURE_DIMS.width,
    )


def test_sparse_instance_decoder_init() -> None:
    """Test the initialization of the sparse instance decoder class."""

    # GIVEN instance decoder parameters

    # WHEN initializing the instance decoder
    instance_decoder = SparseInstanceDecoder(EMBEDDING_DIMENSION, NUM_QUERIES, NUM_CLASSES, SCALE_FACTOR)

    # THEN instance variables should be set correctly
    assert instance_decoder._scale_factor == 1.0  # noqa: SLF001
    assert isinstance(instance_decoder._inst_branch, _InstanceBranch)  # noqa: SLF001
    assert isinstance(instance_decoder._mask_branch, _MaskBranch)  # noqa: SLF001


def test_compute_coordinates(image_features: torch.Tensor) -> None:
    """Tests image positional encoding computation of sparse instance decoder class."""

    # GIVEN random image features

    # WHEN computing coordinates
    coord_features = SparseInstanceDecoder._compute_coordinates(image_features)  # noqa: SLF001

    # THEN the returned coordinates should have the expected shape
    assert coord_features.shape == (2, _FEATURE_DIMS.height, _FEATURE_DIMS.width)

    # THEN the width coordinates (in the second dimension) should match the expected values
    expected_width_coords = torch.linspace(-1, 1, _FEATURE_DIMS.width)
    assert torch.allclose(coord_features[0, :, :], expected_width_coords)

    # THEN the height coordinates (in the second dimension) should match the expected values
    expected_height_coords = torch.linspace(-1, 1, _FEATURE_DIMS.height)
    assert torch.allclose(coord_features[1, :, :], expected_height_coords[..., None])


@pytest.mark.parametrize("scale_factor", [0.5, 1.0, 2.0])
def test_sparse_instance_decoder_compute_masks(image_features: torch.Tensor, scale_factor: float) -> None:
    """Test instance mask computation of the sparse instance decoder class."""

    # GIVEN an instance mask kernel
    pred_kernel = torch.zeros(_BATCH_SIZE, NUM_QUERIES, EMBEDDING_DIMENSION)
    pred_kernel += torch.arange(NUM_QUERIES)[None:, None]  # for each query initialize all kernel values with same index

    # ... and instance mask features
    mask_features = torch.zeros(_BATCH_SIZE, EMBEDDING_DIMENSION, _FEATURE_DIMS.height, _FEATURE_DIMS.width)
    mask_features += torch.arange(EMBEDDING_DIMENSION)[
        None, :, None, None
    ]  # for each kernel dim initialize all features with the same index

    scaled_mask_features = torch.zeros(
        _BATCH_SIZE,
        EMBEDDING_DIMENSION,
        int(_FEATURE_DIMS.height * scale_factor),
        int(_FEATURE_DIMS.width * scale_factor),
    )
    scaled_mask_features += torch.arange(EMBEDDING_DIMENSION)[None, :, None, None]

    #  expect simplified matrix multiplication (due to the same index in the kernel dim for each query)
    expected_pred_masks = (
        torch.sum(scaled_mask_features, dim=1, keepdim=True) * torch.arange(NUM_QUERIES)[None, :, None, None]
    )

    # ... and a sparse instance decoder having the scale factor set
    instance_decoder = SparseInstanceDecoder(EMBEDDING_DIMENSION, NUM_QUERIES, NUM_CLASSES, scale_factor=scale_factor)

    instance_decoder._mask_branch = MagicMock(spec=torch.nn.Module, return_value=mask_features)  # noqa: SLF001

    # WHEN computing the instance masks
    pred_masks = instance_decoder._compute_masks(image_features, pred_kernel)  # noqa: SLF001

    # THEN the returned masks should have the expected shape
    assert pred_masks.shape == (
        _BATCH_SIZE,
        NUM_QUERIES,
        int(_FEATURE_DIMS.height * scale_factor),
        int(_FEATURE_DIMS.width * scale_factor),
    )

    # THEN the returned masks should match the expected values
    assert torch.allclose(pred_masks, expected_pred_masks)


def test_query_generator_fx_tracing(image_features: torch.Tensor) -> None:
    """Test fx.symbolic_trace for the query generator."""

    # GIVEN a query generator instance
    query_generator = SparseLanecppQueryGenerator(EMBEDDING_DIMENSION, NUM_QUERIES, NUM_CLASSES, NUM_POINTS_PER_LINE)
    # Warmup for lazy init:
    query_generator(image_features)

    # WHEN traced
    traced = torch.fx.symbolic_trace(query_generator)

    # THEN the traced model delivers the same outputs
    out_ref = query_generator(image_features)
    out_traced = traced(image_features)
    torch.testing.assert_close(
        out_ref.instance_decoder_output.pred_logits, out_traced.instance_decoder_output.pred_logits
    )


def test_query_generator_onnx(image_features: torch.Tensor) -> None:
    """Test onnx export of the query generator.

    Especially it should export with batch size equal to 1.
    Warmup should not be required for onnx export.
    """
    # GIVEN a query generator instance
    query_generator = SparseLanecppQueryGenerator(EMBEDDING_DIMENSION, NUM_QUERIES, NUM_CLASSES, NUM_POINTS_PER_LINE)
    assert image_features.size(0) > 1

    # WHEN exported as onnx
    model = TracingAdapter(query_generator)
    model.register_inputs(image_features)

    iobuffer = io.BytesIO()
    torch.onnx.export(model, args=(image_features[:1],), f=iobuffer, export_params=True, verbose=False)  # type: ignore[reportArgumentType]
    # THEN no error occurs
