"""Pytest for Sparse LaneCPP line decoder module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import numpy as np
import pytest
import torch
from numpy.typing import NDArray

from xcontract.geometry.definitions.volume import VolumeRange
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_decoder import SparseLanecppLineDecoder
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_representation.splines import LineRepresentation


@pytest.mark.parametrize(
    (
        "line_representation",
        "position_range",
        "num_query",
        "num_pt_per_line",
        "anchor_y_steps",
        "anchor_y_steps_dense",
        "points_as_query",
    ),
    [
        (
            LineRepresentation.CATMULL_ROM,
            VolumeRange(-30.0, -17.0, -10.0, 30.0, 223.0, 10.0),
            16,
            30,
            torch.linspace(0, 1, 16).numpy(),
            torch.linspace(0, 1, 30).numpy(),
            True,
        ),
    ],
)
def test_sparse_lanecpp_line_decoder_forward(
    line_representation: LineRepresentation,
    position_range: VolumeRange,
    num_query: int,
    num_pt_per_line: int,
    anchor_y_steps: NDArray[np.float32],
    anchor_y_steps_dense: NDArray[np.float32],
    *,
    points_as_query: bool,
) -> None:
    """Test the forward pass of the SparseLanecppLineDecoder."""

    # Initialize the decoder
    decoder = SparseLanecppLineDecoder(
        line_representation=line_representation,
        position_range=position_range,
        num_query=num_query,
        num_pt_per_line=num_pt_per_line,
        anchor_y_steps=anchor_y_steps,
        anchor_y_steps_dense=anchor_y_steps_dense,
        points_as_query=points_as_query,
    )

    # GIVEN Mock inputs
    batch_size = 8
    num_transformer_out = 6
    num_anchor_per_query = num_pt_per_line if points_as_query else 1
    num_points_per_anchor = num_pt_per_line // num_anchor_per_query
    line_preds = torch.rand(
        num_transformer_out, batch_size, num_query * num_anchor_per_query * num_points_per_anchor, 3
    )

    # WHEN calling the forward function
    line_preds_x, line_preds_z, line_vis, line_preds_3d_dense = decoder.forward(
        line_preds[..., :2], line_preds[..., 2], batch_size
    )

    # THEN the output shapes and types are as expected
    assert isinstance(line_preds_x, torch.Tensor)
    assert isinstance(line_preds_z, torch.Tensor)
    assert isinstance(line_vis, torch.Tensor)
    assert isinstance(line_preds_3d_dense, torch.Tensor)
    assert line_preds_x.shape == (
        num_transformer_out,
        batch_size,
        num_query,
        num_anchor_per_query * num_points_per_anchor,
    )
    assert line_preds_z.shape == (
        num_transformer_out,
        batch_size,
        num_query,
        num_anchor_per_query * num_points_per_anchor,
    )
    assert line_vis.shape == (
        num_transformer_out,
        batch_size,
        num_query,
        num_anchor_per_query * num_points_per_anchor,
    )
