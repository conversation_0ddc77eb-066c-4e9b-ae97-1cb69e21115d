"""Tests the SparseLanecpp detection loss."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import pytest
import torch

from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
    SparseLanecppHeadRawOutput,
    SparseLanecppHeadScaledOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.losses.detection import (
    _DEFAULT_REGRESSION_LOSS_WEIGHTS,
    DetectionLoss,
    DetectionLossOutput,
    DetectionLossPredictions,
    DetectionLossTargets,
    _second_order_diff,
    get_detection_preds,
    get_detection_targets,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.matcher.sparse_instance_matcher import MatchedIndices

NUM_LAYERS = 2
BATCH_SIZE = 3
NUM_QUERIES = 4
NUM_GT_LANES = 5
NUM_POINTS = 6
NUM_POINTS_DENSE = 12
NUM_CLASSES = 7

torch.manual_seed(0)


@pytest.fixture(name="dummy_preds")
def fixture_dummy_preds(device: str) -> DetectionLossPredictions:
    """Fixture for dummy detection loss predictions."""
    return DetectionLossPredictions(
        class_scores=torch.randn(NUM_LAYERS, BATCH_SIZE, NUM_QUERIES, NUM_CLASSES, device=device),
        lanes_x=torch.randn(NUM_LAYERS, BATCH_SIZE, NUM_QUERIES, NUM_POINTS, device=device),
        lanes_z=torch.randn(NUM_LAYERS, BATCH_SIZE, NUM_QUERIES, NUM_POINTS, device=device),
        lanes_visibility=torch.randn(NUM_LAYERS, BATCH_SIZE, NUM_QUERIES, NUM_POINTS, device=device),
        lanes_dense=torch.randn(NUM_LAYERS, BATCH_SIZE, NUM_QUERIES, NUM_POINTS_DENSE * 3, device=device),
    )


@pytest.fixture(name="dummy_head_output")
def fixture_dummy_head_output(dummy_preds: DetectionLossPredictions, device: str) -> SparseLanecppHeadOutput:
    """Fixture for dummy detection loss head output."""

    lane_preds_unscaled = torch.randn(NUM_LAYERS, BATCH_SIZE, NUM_QUERIES, NUM_POINTS * 3, device=device)
    return SparseLanecppHeadOutput(
        raw_output=SparseLanecppHeadRawOutput(
            class_scores=dummy_preds.class_scores, lane_preds_unscaled=lane_preds_unscaled
        ),
        scaled_output=SparseLanecppHeadScaledOutput(
            lanes_x=dummy_preds.lanes_x, lanes_z=dummy_preds.lanes_z, lanes_visibility=dummy_preds.lanes_visibility
        ),
        lanes_dense=dummy_preds.lanes_dense,
        instance_output=None,
    )


@pytest.fixture(name="dummy_targets")
def fixture_dummy_targets(device: str) -> DetectionLossTargets:
    """Fixture for dummy detection loss ground truth."""
    return DetectionLossTargets(
        lane_category=torch.ones(BATCH_SIZE, NUM_GT_LANES, NUM_CLASSES + 1, device=device),
        lanes_dense=torch.ones(BATCH_SIZE, NUM_GT_LANES, NUM_POINTS_DENSE * 3, device=device),
    )


@pytest.fixture(name="matched_indices")
def fixture_matched_indices(device: str) -> list[MatchedIndices]:
    """Fixture for matched indices."""
    return BATCH_SIZE * [
        MatchedIndices(pred_idx=torch.tensor([0, 1, 2], device=device), gt_idx=torch.tensor([0, 1, 2], device=device))
    ]


@pytest.fixture(name="dummy_gt")
def fixture_dummy_gt(device: str, dummy_targets: DetectionLossTargets) -> SparseLanecppGroundTruth:
    """Fixture for dummy detection loss ground truth."""
    return SparseLanecppGroundTruth(
        lane_category=dummy_targets.lane_category,
        lanes_dense=dummy_targets.lanes_dense,
        # Values not used by the detection loss
        gt_id=torch.ones(BATCH_SIZE, device=device),
        ego_pose=torch.ones(BATCH_SIZE, 4, 4, device=device),
        ego_pose_inv=torch.ones(BATCH_SIZE, 4, 4, device=device),
        segmentation_idx=torch.ones(BATCH_SIZE, NUM_GT_LANES, device=device),
    )


@pytest.fixture(name="detection_loss")
def fixture_detection_loss() -> DetectionLoss:
    """Fixture for the detection loss."""
    return DetectionLoss(
        num_classes=NUM_CLASSES,
        num_y_steps=NUM_POINTS,
        num_y_steps_dense=NUM_POINTS_DENSE,
    )


def test_detection_loss(
    detection_loss: DetectionLoss,
    dummy_preds: DetectionLossPredictions,
    dummy_gt: SparseLanecppGroundTruth,
    matched_indices: list[tuple[torch.Tensor, torch.Tensor]],
) -> None:
    """Tests the SparseLanecpp DetectionLoss forward function."""
    # GIVEN dummy predictions and a dummy ground truth
    # GIVEN a detection loss
    # WHEN the loss is computed
    loss = detection_loss(dummy_preds, dummy_gt, matched_indices)

    # THEN the loss is of type DetectionLossOutput
    assert isinstance(loss, DetectionLossOutput)

    # THEN each loss component is a tensor
    assert all(isinstance(sub_loss, torch.Tensor) for sub_loss in loss)

    # THEN each loss component is larger than 0
    assert all(sub_loss > 0 for sub_loss in loss)


def test_detection_loss_empty_matches(
    detection_loss: DetectionLoss,
    dummy_preds: DetectionLossPredictions,
    dummy_gt: SparseLanecppGroundTruth,
    device: str,
) -> None:
    """Tests the SparseLanecpp DetectionLoss forward function in case of no matches."""
    # GIVEN dummy predictions and a dummy ground truth
    # GIVEN a detection loss
    # GIVEN empty matches
    matched_indices = BATCH_SIZE * [
        MatchedIndices(pred_idx=torch.tensor([], device=device), gt_idx=torch.tensor([], device=device))
    ]

    # WHEN the loss is computed
    loss = detection_loss(dummy_preds, dummy_gt, matched_indices)

    # THEN the loss is of type DetectionLossOutput
    assert isinstance(loss, DetectionLossOutput)

    # THEN each loss component is a tensor
    assert all(isinstance(sub_loss, torch.Tensor) for sub_loss in loss)

    # THEN all but the class loss components are  0
    assert all(sub_loss == 0.0 for sub_loss in loss if sub_loss not in (loss.cls_loss))


def test_detection_loss_one_empty_mixed_matches_gradient_flow(
    detection_loss: DetectionLoss,
    dummy_preds: DetectionLossPredictions,
    dummy_gt: SparseLanecppGroundTruth,
    device: str,
) -> None:
    """Tests the SparseLanecpp DetectionLoss gradient flow with 1 empty and 2 correct matches in the batch."""
    # GIVEN dummy predictions and a dummy ground truth
    # GIVEN a detection loss
    # GIVEN a mixed batch where one element has no matches, and 2 have matches
    matched_indices = [
        MatchedIndices(pred_idx=torch.tensor([], device=device), gt_idx=torch.tensor([], device=device)),
        MatchedIndices(pred_idx=torch.tensor([0, 1, 2], device=device), gt_idx=torch.tensor([0, 1, 2], device=device)),
        MatchedIndices(pred_idx=torch.tensor([0, 1, 2], device=device), gt_idx=torch.tensor([0, 1, 2], device=device)),
    ]

    # Set all dummy predictions to requires_grad=True s.t. we can compute the gradients
    dummy_preds.class_scores.requires_grad = True
    dummy_preds.lanes_x.requires_grad = True
    dummy_preds.lanes_z.requires_grad = True
    dummy_preds.lanes_visibility.requires_grad = True
    dummy_preds.lanes_dense.requires_grad = True

    # WHEN the loss is computed
    loss = detection_loss(dummy_preds, dummy_gt, matched_indices)

    # THEN the loss is of type DetectionLossOutput
    assert isinstance(loss, DetectionLossOutput)

    # THEN each loss component is a tensor
    assert all(isinstance(sub_loss, torch.Tensor) for sub_loss in loss)

    # THEN all loss components are larger than 0
    assert all(sub_loss > 0.0 for sub_loss in loss)

    # THEN the gradient of each sub-loss w.r.t. at least one prediction is not None and not 0
    # Note: We use any() rather than all() here, since not all sub-losses are connected to all predictions
    for sub_loss in loss:
        sub_loss.backward(retain_graph=True)
        assert any(param.grad is not None and param.grad.sum() != 0 for param in dummy_preds)


def test_detection_loss_deactivated_smootheness_loss(
    dummy_preds: DetectionLossPredictions,
    dummy_gt: SparseLanecppGroundTruth,
    matched_indices: list[tuple[torch.Tensor, torch.Tensor]],
) -> None:
    """Tests the SparseLanecpp DetectionLoss forward function with deactivated smootheness loss."""
    # GIVEN dummy predictions and a dummy ground truth
    # GIVEN a detection loss with deactivated smootheness loss
    regression_loss_weights = _DEFAULT_REGRESSION_LOSS_WEIGHTS
    regression_loss_weights["xs_smooth_loss"] = 0.0
    regression_loss_weights["zs_smooth_loss"] = 0.0
    detection_loss = DetectionLoss(
        num_classes=NUM_CLASSES,
        num_y_steps=NUM_POINTS,
        num_y_steps_dense=NUM_POINTS_DENSE,
        regression_loss_weights=regression_loss_weights,
    )

    # WHEN the loss is computed
    loss = detection_loss(dummy_preds, dummy_gt, matched_indices)

    # THEN the smoothneess loss components are 0
    assert loss.xs_smooth_loss == 0.0
    assert loss.zs_smooth_loss == 0.0

    # THEN all other loss components are larger than 0
    assert all(sub_loss > 0 for sub_loss in loss if sub_loss not in (loss.xs_smooth_loss, loss.zs_smooth_loss))


def test_get_detection_target(
    dummy_gt: SparseLanecppGroundTruth,
) -> None:
    """Tests the get_detection_targets function."""
    # GIVEN a dummy ground truth
    # WHEN the detection target is obtained
    target = get_detection_targets(dummy_gt)

    # THEN the target is of type DetectionLossTargets
    assert isinstance(target, DetectionLossTargets)

    # THEN the target holds the corresponding data from the ground truth
    assert target.lane_category is dummy_gt.lane_category
    assert target.lanes_dense is dummy_gt.lanes_dense


def test_get_prediction_target(
    dummy_head_output: SparseLanecppHeadOutput,
) -> None:
    """Tests the test_get_prediction_target function."""
    # GIVEN a dummy head output
    # WHEN the prediction target is obtained
    pred = get_detection_preds(dummy_head_output)

    # THEN the prediction target is of type DetectionLossPredictions
    assert isinstance(pred, DetectionLossPredictions)
    assert dummy_head_output.scaled_output is not None

    # THEN the prediction target holds the corresponding data from the predictions
    assert pred.class_scores is dummy_head_output.raw_output.class_scores
    assert torch.allclose(pred.lanes_x, dummy_head_output.scaled_output.lanes_x)
    assert torch.allclose(pred.lanes_z, dummy_head_output.scaled_output.lanes_z)
    assert torch.allclose(pred.lanes_visibility, dummy_head_output.scaled_output.lanes_visibility)
    assert pred.lanes_dense is dummy_head_output.lanes_dense


@pytest.mark.parametrize(
    ("dummy_vector", "expected_result"),
    [
        (torch.tensor([[1.0, 2.0, 4.0, 8.0]]), torch.tensor([[1.0, 2.0]])),
        (torch.tensor([[10.0, 0.0, 10.0, 0.0]]), torch.tensor([[20.0, -20.0]])),
    ],
)
def test_second_order_diff(dummy_vector: torch.Tensor, expected_result: torch.Tensor) -> None:
    """Tests the _second_order_diff function."""
    # GIVEN a dummy vector
    # WHEN the second order difference is computed
    result = _second_order_diff(dummy_vector)

    # THEN the result is as expected
    torch.testing.assert_close(result, expected_result)


def test_multi_layer_loss_matches_single_layer_loss(
    detection_loss: DetectionLoss,
    dummy_preds: DetectionLossPredictions,
    dummy_targets: DetectionLossTargets,
    matched_indices: list[MatchedIndices],
) -> None:
    """Tests that _multi_layer_loss produces the same result as _single_layer_loss."""

    xs_loss = []
    zs_loss = []
    xs_smooth_loss = []
    zs_smooth_loss = []
    vis_loss = []
    cls_loss = []
    for i in range(NUM_LAYERS):
        single_layer_preds = DetectionLossPredictions(
            class_scores=dummy_preds.class_scores[i],  # shape: (BATCH_SIZE, NUM_QUERIES, NUM_CLASSES)
            lanes_x=dummy_preds.lanes_x[i],
            lanes_z=dummy_preds.lanes_z[i],
            lanes_visibility=dummy_preds.lanes_visibility[i],
            lanes_dense=dummy_preds.lanes_dense[i],
        )

        single_layer_loss = detection_loss._single_layer_loss(  # noqa: SLF001
            single_layer_preds, dummy_targets, matched_indices, BATCH_SIZE, device=dummy_preds.class_scores.device
        )
        xs_loss.append(single_layer_loss.xs_loss)
        zs_loss.append(single_layer_loss.zs_loss)
        xs_smooth_loss.append(single_layer_loss.xs_smooth_loss)
        zs_smooth_loss.append(single_layer_loss.zs_smooth_loss)
        vis_loss.append(single_layer_loss.vis_loss)
        cls_loss.append(single_layer_loss.cls_loss)

    multi_layer_loss = detection_loss._multi_layer_loss(  # noqa: SLF001
        dummy_preds, dummy_targets, matched_indices, BATCH_SIZE, device=dummy_preds.class_scores.device
    )

    assert torch.allclose(torch.stack(xs_loss).mean(), multi_layer_loss.xs_loss, equal_nan=True)
    assert torch.allclose(torch.stack(zs_loss).mean(), multi_layer_loss.zs_loss, equal_nan=True)
    assert torch.allclose(torch.stack(xs_smooth_loss).mean(), multi_layer_loss.xs_smooth_loss, equal_nan=True)
    assert torch.allclose(torch.stack(zs_smooth_loss).mean(), multi_layer_loss.zs_smooth_loss, equal_nan=True)
    assert torch.allclose(torch.stack(vis_loss).mean(), multi_layer_loss.vis_loss, equal_nan=True)
    assert torch.allclose(torch.stack(cls_loss).mean(), multi_layer_loss.cls_loss, equal_nan=True)
