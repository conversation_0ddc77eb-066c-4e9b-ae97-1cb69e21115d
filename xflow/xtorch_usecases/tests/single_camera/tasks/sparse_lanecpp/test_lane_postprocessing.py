"""Module for testing Sparse LaneCPP Postprocessing."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import numpy as np
import pytest
import torch
from numpy.testing import assert_array_equal
from numpy.typing import NDArray

from xcontract.data.definitions.usage import ValueKey
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.anchors import ANCHOR_Y_STEPS_DENSE, NUM_Y_STEPS_DENSE
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
    SparseLanecppHeadRawOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.lane_postprocessing import (
    handle_gts,
    handle_predictions,
    post_processing_gts,
    post_processing_preds,
    postprocess,
)


# ========== test postprocess ==========
@pytest.fixture(name="anchor_y_steps_dense_and_num")
def fixture_anchor_y_steps_dense_and_num() -> tuple[NDArray[np.float32], int]:
    """Create anchor_y_steps_dense and num_y_steps_dense."""
    anchor_y_steps_dense = np.array([1.0, 2.0, 3.0, 4.0])
    num_y_steps_dense = len(anchor_y_steps_dense)
    return anchor_y_steps_dense, num_y_steps_dense


def test_postprocess_with_valid_data(anchor_y_steps_dense_and_num: tuple[NDArray[np.float32], int]) -> None:
    """Test postprocess with valid input data."""

    # GIVEN two lanes with 4 points each (x, z, vis). All points are visible. Two classes plus a background class.
    lanes_in = np.array(
        [
            [0.0, 1.0, 2.0, 3.0, 0.0, 1.0, 2.0, 3.0, 0.5, 0.5, 0.5, 0.5],  # x, z, visibility
            [4.0, 5.0, 6.0, 7.0, 4.0, 5.0, 6.0, 7.0, 0.5, 0.5, 0.5, 0.5],
        ]
    )
    cls_in = np.array([[0.9, 0.1, 0.0], [0.8, 0.1, 0.1]])
    anchor_y_steps_dense, num_y_steps_dense = anchor_y_steps_dense_and_num

    # WHEN postprocess is called
    lanes_out, cls_out, vis_out = postprocess(
        lanes_in=lanes_in,
        cls_in=cls_in,
        anchor_y_steps_dense=anchor_y_steps_dense,
        num_y_steps_dense=num_y_steps_dense,
    )

    # THEN it should return two lanes with correct coordinates and class scores, and all points marked as visible.
    assert len(lanes_out) == 2
    assert len(cls_out) == 2
    assert len(vis_out) == 2

    lanes_out_target = [
        np.array([[0.0, 1.0, 0.0], [1.0, 2.0, 1.0], [2.0, 3.0, 2.0], [3.0, 4.0, 3.0]]),
        np.array([[4.0, 1.0, 4.0], [5.0, 2.0, 5.0], [6.0, 3.0, 6.0], [7.0, 4.0, 7.0]]),
    ]
    assert_array_equal(lanes_out, lanes_out_target)
    assert_array_equal(cls_out, cls_in)
    assert_array_equal(vis_out[0], [True, True, True, True])
    assert_array_equal(vis_out[1], [True, True, True, True])


def test_postprocess_with_valid_data_half_visible(
    anchor_y_steps_dense_and_num: tuple[NDArray[np.float32], int],
) -> None:
    """Test postprocess with half visible points."""
    # GIVEN one lane with 4 points each (x, z, vis). Half of points are visible. Two classes plus a background class.
    lanes_in = np.array(
        [
            [0.0, 1.0, 2.0, 3.0, 0.0, 1.0, 2.0, 3.0, 0.0, 0.5, 0.5, 0.0],  # x, z, visibility
        ]
    )
    cls_in = np.array([[0.9, 0.1, 0.0]])
    anchor_y_steps_dense, num_y_steps_dense = anchor_y_steps_dense_and_num

    # WHEN postprocess is called
    lanes_out, cls_out, vis_out = postprocess(
        lanes_in=lanes_in,
        cls_in=cls_in,
        anchor_y_steps_dense=anchor_y_steps_dense,
        num_y_steps_dense=num_y_steps_dense,
    )

    # THEN it should return one lane with two points with correct coordinates,class scores, and visibility.
    assert len(lanes_out) == 1
    assert len(cls_out) == 1
    assert len(vis_out) == 1

    lanes_out_target = [
        np.array([[1.0, 2.0, 1.0], [2.0, 3.0, 2.0]]),
    ]

    assert_array_equal(lanes_out, lanes_out_target)
    assert_array_equal(cls_out, cls_in)
    assert_array_equal(vis_out[0], [True, True])


def test_postprocess_with_insufficient_visibility(
    anchor_y_steps_dense_and_num: tuple[NDArray[np.float32], int],
) -> None:
    """Test postprocess with insufficient visibility."""

    # GIVEN two lanes with 4 points each (x, z, vis). One has insufficient visibility.
    lanes_in = np.array(
        [
            [0.0, 1.0, 2.0, 3.0, 0.0, 1.0, 2.0, 3.0, 0.5, 0.5, 0.5, 0.5],  # x, z, visibility
            [4.0, 5.0, 6.0, 7.0, 4.0, 5.0, 6.0, 7.0, 0.0, 0.5, 0.0, 0.0],
        ]
    )
    cls_in = np.array([[0.9, 0.1, 0.0], [0.8, 0.1, 0.1]])
    anchor_y_steps_dense, num_y_steps_dense = anchor_y_steps_dense_and_num

    # WHEN postprocess is called
    lanes_out, cls_out, vis_out = postprocess(
        lanes_in=lanes_in,
        cls_in=cls_in,
        anchor_y_steps_dense=anchor_y_steps_dense,
        num_y_steps_dense=num_y_steps_dense,
    )

    # THEN it should return one lane with correct coordinates,class scores, and visibility.
    assert len(lanes_out) == 1
    assert len(cls_out) == 1
    assert len(vis_out) == 1

    lanes_out_target = [np.array([[0.0, 1.0, 0.0], [1.0, 2.0, 1.0], [2.0, 3.0, 2.0], [3.0, 4.0, 3.0]])]
    cls_out_target = np.array([[0.9, 0.1, 0.0]])

    assert_array_equal(lanes_out, lanes_out_target)
    assert_array_equal(cls_out, cls_out_target)
    assert_array_equal(vis_out[0], [True, True, True, True])


def test_postprocess_with_empty_input(anchor_y_steps_dense_and_num: tuple[NDArray[np.float32], int]) -> None:
    """Test postprocess with empty input data."""

    # GIVEN empty lanes and class scores
    lanes_in = np.empty((0, 9), dtype=np.float32)  # 3 points (x, z, vis) * 3
    cls_in = np.empty((0,), dtype=np.float32)
    anchor_y_steps_dense, num_y_steps_dense = anchor_y_steps_dense_and_num

    # WHEN postprocess is called
    lanes_out, cls_out, vis_out = postprocess(
        lanes_in=lanes_in,
        cls_in=cls_in,
        anchor_y_steps_dense=anchor_y_steps_dense,
        num_y_steps_dense=num_y_steps_dense,
    )

    # THEN it should return no lanes, class scores, or visibility.
    assert lanes_out == []
    assert cls_out == []
    assert vis_out == []


# ========== predictions/gts  mock ==========


@pytest.fixture(name="three_create_lane_unscaled")
def fixture_three_create_lane_unscaled() -> torch.Tensor:
    """Create three dummy lines unscaled."""
    x = z = [x / (NUM_Y_STEPS_DENSE - 1) for x in range(NUM_Y_STEPS_DENSE)]
    vis_1 = [0.5] * NUM_Y_STEPS_DENSE
    vis_2 = [0.5 if 99 <= i < 199 else 0.0 for i in range(NUM_Y_STEPS_DENSE)]
    vis_3 = [0.5 if i == 149 else 0.0 for i in range(NUM_Y_STEPS_DENSE)]
    base = x + z
    lanes = [base + vis for vis in (vis_1, vis_2, vis_3)]
    return torch.tensor(lanes)


@pytest.fixture(name="three_create_lane")
def fixture_three_create_lane(three_create_lane_unscaled: torch.Tensor) -> torch.Tensor:
    """Create three dummy lines."""
    scales = torch.tensor([NUM_Y_STEPS_DENSE - 1, NUM_Y_STEPS_DENSE - 1, 1])
    return three_create_lane_unscaled * scales.repeat_interleave(NUM_Y_STEPS_DENSE)


@pytest.fixture(name="three_cls_score_prediction")
def fixture_three_cls_score_prediction() -> torch.Tensor:
    """Create three dummy score predictions."""
    scores = [
        [
            [1.0, 9.0, 1.0, 1.0],
            [2.0, 9.0, 2.0, 1.0],
            [3.0, 9.0, 5.0, 1.0],
        ]
    ]
    return torch.tensor(scores)


@pytest.fixture(name="three_cls_gts")
def fixture_three_cls_gts() -> torch.Tensor:
    """Create three dummy class gts."""
    scores = [
        [
            [0, 1, 0, 0, 20],
            [0, 0, 1, 0, 21],
            [1, 0, 0, 0, 22],
        ]
    ]
    return torch.tensor(scores)


_NUM_TRANSFORMER_LAYERS = 6
_BATCH_SIZE = 1

# ========== test post_processing_preds ==========


def test_post_processing_preds_with_valid_data(
    three_create_lane: torch.Tensor, three_cls_score_prediction: torch.Tensor
) -> None:
    """Test post_processing_preds with valid input data."""
    # GIVEN three lanes with different visibility with the last one having insufficient visibility
    all_line_preds_setup = three_create_lane
    all_cls_scores_setup = three_cls_score_prediction

    # Add batch dimension and repeat for 6 output of transformer
    all_cls_scores = all_cls_scores_setup.repeat(_NUM_TRANSFORMER_LAYERS, _BATCH_SIZE, 1, 1)
    all_line_preds = all_line_preds_setup.repeat(_NUM_TRANSFORMER_LAYERS, _BATCH_SIZE, 1, 1)

    # WHEN post_processing_preds is called
    results = post_processing_preds(all_cls_scores, all_line_preds)

    # THEN it should return two lanes with correct coordinates, class scores, and visibility.
    # The second lane contains only 100 points due to visibility.

    # class target
    class_target = torch.softmax(all_cls_scores_setup[0][0:2, :], dim=-1).detach().cpu().numpy()
    class_targets = [class_target[0], class_target[1]]

    # lane target
    lane_1_target = np.stack(
        (all_line_preds_setup[0, :300], ANCHOR_Y_STEPS_DENSE, all_line_preds_setup[0, 300:600]), axis=1
    )
    lane_2_target = np.stack(
        (all_line_preds_setup[1, :300][99:199], ANCHOR_Y_STEPS_DENSE[99:199], all_line_preds_setup[1, 300:600][99:199]),
        axis=1,
    )
    lane_targets = [lane_1_target, lane_2_target]

    # vis target
    vis_1_target = np.array([True] * NUM_Y_STEPS_DENSE)
    vis_2_target = vis_1_target.copy()[99:199]
    vis_targets = [vis_1_target, vis_2_target]

    assert len(results) == _BATCH_SIZE
    for result in results:  # over batch
        assert len(result.pred_lanes) == 2
        assert len(result.pred_lanes_prob) == 2
        assert len(result.pred_lanes_vis) == 2

        assert_array_equal(result.pred_lanes_prob, class_targets)

        for result_lane, lane_target in zip(result.pred_lanes, lane_targets):
            assert_array_equal(result_lane, lane_target)

        for result_vis, vis_target in zip(result.pred_lanes_vis, vis_targets):
            assert_array_equal(result_vis, vis_target)


def test_post_processing_preds_with_background_class() -> None:
    """Test post_processing_preds with background class."""
    # GIVEN one lane with full visibility but has background class
    x = z = list(range(NUM_Y_STEPS_DENSE))

    vis = [0.5] * NUM_Y_STEPS_DENSE
    lanes = [x + z + vis]
    all_line_preds_setup = torch.tensor(lanes)

    all_cls_scores_setup = torch.tensor(
        [
            [
                [9.0, 1.0, 1.0, 1.0],
            ]
        ]
    )

    # Add batch dimension and repeat for 6 output of transformer
    all_cls_scores = all_cls_scores_setup.repeat(_NUM_TRANSFORMER_LAYERS, _BATCH_SIZE, 1, 1)
    all_line_preds = all_line_preds_setup.repeat(_NUM_TRANSFORMER_LAYERS, _BATCH_SIZE, 1, 1)

    # WHEN post_processing_preds is called
    results = post_processing_preds(all_cls_scores, all_line_preds)

    # THEN it should return no lanes, class scores, or visibility.
    assert len(results) == _BATCH_SIZE
    for result in results:
        assert len(result.pred_lanes) == 0
        assert len(result.pred_lanes_prob) == 0
        assert len(result.pred_lanes_vis) == 0
        assert_array_equal(result.pred_lanes, np.array([]))
        assert_array_equal(result.pred_lanes_prob, np.array([]))
        assert_array_equal(result.pred_lanes_vis, np.array([]))


def test_post_processing_preds_with_empty_preds() -> None:
    """Test post_processing_preds with empty predictions."""
    # GIVEN empty predictions
    all_cls_scores = torch.tensor([[]])
    all_line_preds = torch.tensor([[]])

    # WHEN post_processing_preds is called
    results = post_processing_preds(all_cls_scores, all_line_preds)

    # THEN it should return no lanes, class scores, or visibility.
    assert len(results) == 0


def test_post_processing_preds_with_none_preds() -> None:
    """Test post_processing_preds with None as predictions."""
    # GIVEN empty None predictions
    all_cls_scores = torch.tensor(
        [
            [
                [0.9, 0.1],
                [0.8, 0.2],
            ]
        ]
    )
    all_line_preds = None

    # WHEN post_processing_preds is called
    # THEN it should raise ValueError
    with pytest.raises(ValueError, match="all_line_preds should be filled!"):
        post_processing_preds(all_cls_scores, all_line_preds)


# ========== test handle_predictions ==========


def test_handle_predictions_with_valid_data(
    three_create_lane_unscaled: torch.Tensor, three_cls_score_prediction: torch.Tensor
) -> None:
    """Test handle_predictions with valid input data."""

    # GIVEN three lanes with with correct datatype as inputs
    all_line_preds_setup = three_create_lane_unscaled
    all_cls_scores_setup = three_cls_score_prediction

    # Add batch dimension and repeat for 6 output of transformer
    all_cls_scores = all_cls_scores_setup.repeat(_NUM_TRANSFORMER_LAYERS, _BATCH_SIZE, 1, 1)
    all_line_preds = all_line_preds_setup.repeat(_NUM_TRANSFORMER_LAYERS, _BATCH_SIZE, 1, 1)

    sparse_lanecpp_head_output_raw = SparseLanecppHeadRawOutput(
        class_scores=all_cls_scores,
        lane_preds_unscaled=three_create_lane_unscaled,
    )
    sparse_lanecpp_head_output = SparseLanecppHeadOutput(
        raw_output=sparse_lanecpp_head_output_raw, lanes_dense=all_line_preds
    )

    network_out = {"sparse_lanecpp": sparse_lanecpp_head_output}

    # WHEN handle_predictions is called
    results = handle_predictions(network_out, task_id="sparse_lanecpp")

    # THEN it should return one result with two lanes.
    assert len(results) == 1  # Batch size is 1
    for result in results:
        assert len(result.pred_lanes) == 2
        assert len(result.pred_lanes_prob) == 2
        assert len(result.pred_lanes_vis) == 2


def test_handle_predictions_with_invalid_sparse_lanecpp_type() -> None:
    """Test handle_predictions with invalid sparse_lanecpp type."""
    # GIVEN three lanes with with invalid_type as inputs
    network_out = {"sparse_lanecpp": "invalid_type"}
    # WHEN handle_predictions is called
    # THEN it should raise AssertionError
    with pytest.raises(AssertionError):
        handle_predictions(network_out, task_id="sparse_lanecpp")


# ========== test post_processing_gts ==========


def test_post_processing_gts_with_valid_data(three_create_lane: torch.Tensor, three_cls_gts: torch.Tensor) -> None:
    """Test post_processing_preds with valid input data."""
    # GIVEN three lanes with different visibility with the last one having insufficient visibility
    all_line_gt_setup = three_create_lane
    all_cls_gt_setup = three_cls_gts

    # Add batch dimension and repeat for 6 output of transformer
    all_cls_gt = all_cls_gt_setup.repeat(_BATCH_SIZE, 1, 1)
    all_line_gt = all_line_gt_setup.repeat(_BATCH_SIZE, 1, 1)

    # WHEN post_processing_gts is called
    results = post_processing_gts(all_cls_gt, all_line_gt)

    # THEN it should return two lanes with correct coordinates, class scores, and visibility.
    # The second lane contains only 100 points due to visibility.

    # class target
    class_target = torch.argmax(all_cls_gt_setup[0][0:2, :-1], dim=-1).detach().cpu().numpy()
    class_targets = [class_target[0], class_target[1]]

    # lane target
    lane_1_target = np.stack((all_line_gt_setup[0, :300], ANCHOR_Y_STEPS_DENSE, all_line_gt_setup[0, 300:600]), axis=1)
    lane_2_target = np.stack(
        (all_line_gt_setup[1, :300][99:199], ANCHOR_Y_STEPS_DENSE[99:199], all_line_gt_setup[1, 300:600][99:199]),
        axis=1,
    )
    lane_targets = [lane_1_target, lane_2_target]

    # vis target
    vis_1_target = np.array([True] * NUM_Y_STEPS_DENSE)
    vis_2_target = vis_1_target.copy()[99:199]
    vis_targets = [vis_1_target, vis_2_target]

    assert len(results) == _BATCH_SIZE
    for result in results:  # over batch
        assert len(result.gt_lanes_coord) == 2
        assert len(result.gt_lanes_class) == 2
        assert len(result.gt_lanes_vis) == 2

        assert_array_equal(result.gt_lanes_class, class_targets)

        for result_lane, lane_target in zip(result.gt_lanes_coord, lane_targets):
            assert_array_equal(result_lane, lane_target)

        for result_vis, vis_target in zip(result.gt_lanes_vis, vis_targets):
            assert_array_equal(result_vis, vis_target)


def test_post_processing_gts_with_backgroud_class() -> None:
    """Test post_processing_gts with backgroud class."""
    # GIVEN one lane with full visibility but has background class
    x = z = list(range(NUM_Y_STEPS_DENSE))

    vis = [0.5] * NUM_Y_STEPS_DENSE
    lanes = [x + z + vis]

    all_cls_gt_setup = torch.tensor(lanes)
    all_cls_scores_setup = torch.tensor(
        [
            [
                [1, 0, 0, 0, 0],
            ]
        ]
    )

    # Add batch dimension and repeat for 6 output of transformer
    all_cls_gt = all_cls_gt_setup.repeat(_BATCH_SIZE, 1, 1)
    all_cls_scores = all_cls_scores_setup.repeat(_BATCH_SIZE, 1, 1)

    # WHEN post_processing_gts is called
    results = post_processing_gts(all_cls_gt, all_cls_scores)

    # THEN it should return no lanes, class scores, or visibility.
    assert len(results) == _BATCH_SIZE
    for result in results:
        assert len(result.gt_lanes_coord) == 0
        assert len(result.gt_lanes_class) == 0
        assert len(result.gt_lanes_vis) == 0
        assert_array_equal(result.gt_lanes_coord, np.array([]))
        assert_array_equal(result.gt_lanes_class, np.array([]))
        assert_array_equal(result.gt_lanes_vis, np.array([]))


# ========== test handle_gts ==========


def test_handle_gts_with_valid_data(three_create_lane: torch.Tensor, three_cls_gts: torch.Tensor) -> None:
    """Test handle_gts with valid input data."""
    # GIVEN three lanes with correct data type.
    all_line_gt_setup = three_create_lane
    all_cls_gt_setup = three_cls_gts
    # Add batch dimension
    all_cls_gt = all_cls_gt_setup.repeat(_BATCH_SIZE, 1, 1)
    all_line_gt = all_line_gt_setup.repeat(_BATCH_SIZE, 1, 1)
    dummy_tensor = torch.zeros((1, 1, 1, 1), dtype=torch.float32)

    gt_data = SparseLanecppGroundTruth(
        lanes_dense=all_line_gt,
        lane_category=all_cls_gt,
        # Dummy values for all unused parameters
        gt_id=dummy_tensor,
        ego_pose=dummy_tensor,
        ego_pose_inv=dummy_tensor,
        segmentation_idx=dummy_tensor,
    )

    batch = OnDeviceBatchedTrainingDict()
    batch["sparse_lanecpp"] = {ValueKey.DATA: gt_data}

    # WHEN handle_gts is called
    results = handle_gts(batch, task_id="sparse_lanecpp")

    # THEN it should return one result with two lanes.
    assert len(results) == _BATCH_SIZE
    for result in results:
        assert len(result.gt_lanes_coord) == 2
        assert len(result.gt_lanes_class) == 2
        assert len(result.gt_lanes_vis) == 2
