"""Tests for the Lane3D metrics."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import copy
from dataclasses import fields
from pathlib import Path
from unittest.mock import MagicMock

import numpy as np
import pytest
from numpy.typing import NDArray

from xtorch_usecases.single_camera.tasks.sparse_lanecpp.evaluation.metrics import (
    Lane3DEval,
    Lane3DEvalStats,
    Lane3DMetrics,
    _to_camel_case,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.lane_postprocessing import PostprocessedGTs, PostprocessedPreds

# ====================Lane3DEval====================


@pytest.fixture(name="mock_add_eval_to_csv_do_nothing_fixture")
def fixture_mock_add_eval_to_csv(mocker: MagicMock) -> MagicMock:
    """Fixture to mock the _add_eval_to_csv method to do nothing."""
    mock_add_eval_to_csv_do_nothing = mocker.MagicMock(return_value=None)
    return mock_add_eval_to_csv_do_nothing


@pytest.fixture(name="eval_instance")
def fixture_eval_instance(mock_add_eval_to_csv_do_nothing_fixture: MagicMock) -> Lane3DEval:
    """Fixture to provide a Lane3DEval instance."""

    eval_instance = Lane3DEval(
        task_id="lane3d_task",
        pos_threshold=0.3,
        top_view_region=np.array([[-10, 21], [10, 21], [-10, 1], [10, 1]], dtype=np.float32),
        metric_save_path=Path("not-used-variable-in-this-test"),
        num_y_steps=4,  # set y-sample to only 4 just in this test mock
        distance_bins=[10, 100],
    )

    eval_instance._add_eval_to_csv = mock_add_eval_to_csv_do_nothing_fixture  # noqa: SLF001

    return eval_instance


# ==========Lane3DEval.update()==========
@pytest.fixture(name="two_lanes_with_full_visibility_fixture")
def fixture_two_lanes_with_full_visibility() -> tuple[list[NDArray[np.float32]], list[NDArray[np.bool_]], list[int]]:
    """Fixture to generate two lanes with full visibility."""
    # Generate two lanes with full visibility
    lanes, vis, categories = [], [], []

    first_lane = [np.array([[0.0, 1.0, 0.0], [5, 11, 5], [10, 21, 10]], dtype=np.float32)]
    first_lane_full_vis = [np.array([True, True, True], dtype=np.bool_)]
    lanes.extend(first_lane)
    vis.extend(first_lane_full_vis)
    categories.append(1)

    second_lane = [np.array([[2.0, 1.0, 0.0], [7, 11, 5], [12, 21, 10]], dtype=np.float32)]  # Shift the second lane
    second_lane_full_vis = [np.array([True, True, True], dtype=np.bool_)]
    lanes.extend(second_lane)
    vis.extend(second_lane_full_vis)
    categories.append(1)

    return lanes, vis, categories


def test_update(
    eval_instance: Lane3DEval,
    two_lanes_with_full_visibility_fixture: tuple[list[NDArray[np.float32]], list[NDArray[np.bool_]], list[int]],
) -> None:
    """Test the update function with various scenarios."""
    # GIVEN a Lane3DEval instance
    # GIVEN mocked predictions and ground truths
    pred_lanes, pred_lanes_vis, _ = two_lanes_with_full_visibility_fixture
    pred_lanes_prob = [np.array([0.2, 0.9, 0.1]), np.array([0.2, 0.9, 0.1])]
    postprocesses_preds = PostprocessedPreds(
        pred_lanes=pred_lanes, pred_lanes_vis=pred_lanes_vis, pred_lanes_prob=pred_lanes_prob
    )

    gt_lanes, gt_lanes_vis = copy.deepcopy(pred_lanes), copy.deepcopy(pred_lanes_vis)
    gt_lanes[1] = gt_lanes[1] + 0.5  # shift the second lane a bit
    gt_lanes_prob = [1, 1]
    postprocessed_gts = PostprocessedGTs(
        gt_lanes_coord=gt_lanes, gt_lanes_class=gt_lanes_prob, gt_lanes_vis=gt_lanes_vis
    )

    expected_output = Lane3DEvalStats(
        recall=2.0,
        precision=2.0,
        category_matches=2.0,
        num_gts=2,
        num_preds=2,
        num_matches=2,
        num_matches_by_distance=np.array([2, 2]),
        x_error_by_distance=np.array([0.25, 0.25]),
        z_error_by_distance=np.array([0.25, 0.25]),
    )

    # WHEN calling the function
    eval_instance.update([postprocesses_preds], [postprocessed_gts])

    # THEN the stats should match the expected results
    _assert_equal_lane_eval_stats(eval_instance._stats, expected_output)  # noqa: SLF001


# ==========Lane3DEval.compute()==========
@pytest.fixture(name="eval_result")
def fixture_eval_result() -> Lane3DEvalStats:
    """A dummy eval result."""
    return Lane3DEvalStats(
        recall=1.0,
        precision=1.0,
        category_matches=0.0,
        num_gts=2,
        num_preds=2,
        num_matches=1,
        num_matches_by_distance=np.array([1, 1]),
        x_error_by_distance=np.array([0.0, 0.0]),
        z_error_by_distance=np.array([0.0, 0.0]),
    )


def test_compute(eval_instance: Lane3DEval, eval_result: Lane3DEvalStats) -> None:
    """Test compute method with valid metrics."""
    # GIVEN a Lane3DEval with mocked Lane3DEvalStatss
    eval_instance._stats = eval_result  # noqa: SLF001

    # WHEN calling the compute method
    results = eval_instance.compute()

    # THEN the metrics should be computed correctly
    expected_precision = eval_result.precision / eval_result.num_preds
    expected_recall = eval_result.recall / eval_result.num_gts
    x_error_by_distance = eval_result.x_error_by_distance / eval_result.num_matches_by_distance
    z_error_by_distance = eval_result.z_error_by_distance / eval_result.num_matches_by_distance
    expected_metrics = Lane3DMetrics(
        f1_score=2 * expected_precision * expected_recall / (expected_precision + expected_recall),
        precision=expected_precision,
        recall=expected_recall,
        cat_accuracy=eval_result.category_matches / eval_result.num_gts,
        x_error_by_distance={
            eval_instance._distance_bins[i]: x_error_by_distance[i]  # noqa: SLF001
            for i in range(len(x_error_by_distance))
        },
        z_error_by_distance={
            eval_instance._distance_bins[i]: z_error_by_distance[i]  # noqa: SLF001
            for i in range(len(z_error_by_distance))
        },
    )

    eval_prefix = f"evaluation/{eval_instance._task_id}/"  # noqa: SLF001
    assert results[eval_prefix + "f1_score"] == expected_metrics.f1_score
    assert results[eval_prefix + "recall"] == expected_metrics.recall
    assert results[eval_prefix + "precision"] == expected_metrics.precision
    assert results[eval_prefix + "cat_accuracy"] == expected_metrics.cat_accuracy
    for distance, x_error in expected_metrics.x_error_by_distance.items():
        assert results[eval_prefix + f"x_error_by_distance_{distance}"] == x_error
    for distance, z_error in expected_metrics.z_error_by_distance.items():
        assert results[eval_prefix + f"z_error_by_distance_{distance}"] == z_error


# ==========Lane3DEval._compute_eval_stats==========
@pytest.fixture(name="compute_eval_stats_input")
def fixture_compute_eval_stats_input() -> tuple[
    NDArray[np.float32],
    NDArray[np.float32],
    NDArray[np.float32],
    NDArray[np.float32],
]:
    """Fixture to generate inputs for compute_eval_metrics with full visibility."""
    dist_mat_close_x = np.array([[0.0, 2.5], [2.5, 0.0]], dtype=np.float32)
    dist_mat_close_z = np.array([[0.0, 2.5], [2.66666667, 0.16666667]], dtype=np.float32)
    dist_mat_far_x = np.array([[0.0, 2.5], [2.5, 0.0]], dtype=np.float32)
    dist_mat_far_z = np.array([[0.0, 2.5], [2.5, 0.0]], dtype=np.float32)
    dist_mat_close = np.stack((dist_mat_close_x, dist_mat_close_z), axis=-1)
    dist_mat_far = np.stack((dist_mat_far_x, dist_mat_far_z), axis=-1)
    dist_mat = np.stack((dist_mat_close, dist_mat_far), axis=-2)
    num_vis_pred_points = np.array([4.0, 4.0])
    num_vis_gt_points = np.array([4.0, 4.0])
    num_match_mat = np.array([[4.0, 0.0], [0.0, 4.0]])

    return dist_mat, num_vis_pred_points, num_vis_gt_points, num_match_mat


@pytest.mark.parametrize(
    (
        "match_results",
        "pred_cat",
        "gt_cat",
        "expected_output",
    ),
    [
        # Case 1: Valid matches with distances below the threshold
        (
            np.array([[0, 0, 0], [1, 1, 1]]),
            [1, 20],
            [1, 20],
            Lane3DEvalStats(
                recall=2.0,
                precision=2.0,
                category_matches=2.0,
                num_gts=2,
                num_preds=2,
                num_matches=2,
                num_matches_by_distance=np.array([2, 2]),
                x_error_by_distance=np.array([0.0, 0.0]),
                z_error_by_distance=np.array([0.16666667, 0.0]),
            ),
        ),
        # Case 2: Distances cost exceeding the threshold and Category mismatches
        (
            np.array([[0, 0, 0], [1, 1, 6]]),
            [2, 3],
            [1, 3],
            Lane3DEvalStats(
                recall=1.0,
                precision=1.0,
                category_matches=0.0,
                num_gts=2,
                num_preds=2,
                num_matches=1,
                num_matches_by_distance=np.array([1, 1]),
                x_error_by_distance=np.array([0.0, 0.0]),
                z_error_by_distance=np.array([0.0, 0.0]),
            ),
        ),
    ],
)
def test_compute_eval_stats(
    eval_instance: Lane3DEval,
    compute_eval_stats_input: tuple[
        NDArray[np.float32],
        NDArray[np.float32],
        NDArray[np.float32],
        NDArray[np.float32],
    ],
    match_results: NDArray[np.float32],
    pred_cat: list[int],
    gt_cat: list[int],
    expected_output: Lane3DEvalStats,
) -> None:
    """Test _compute_eval_stats with various scenarios."""
    # GIVEN inputs fixture for the function
    dist_mat, num_vis_pred_points, num_vis_gt_points, num_match_mat = compute_eval_stats_input
    # WHEN calling the function
    output = eval_instance._compute_eval_stats(  # noqa: SLF001
        dist_mat,
        num_vis_pred_points,
        num_vis_gt_points,
        match_results,
        num_match_mat,
        pred_cat,
        gt_cat,
    )

    # THEN the output should match the expected results
    _assert_equal_lane_eval_stats(output, expected_output)


def test_compute_eval_stats_first_lane_double_num_vis_points(
    eval_instance: Lane3DEval,
    compute_eval_stats_input: tuple[
        NDArray[np.float32],
        NDArray[np.float32],
        NDArray[np.float32],
        NDArray[np.float32],
    ],
) -> None:
    """Test _compute_eval_stats with only double num_vis_points so that the matched points is half."""
    # GIVEN inputs fixture for the function
    dist_mat, _, _, _ = compute_eval_stats_input
    # Adjust the number of visible points for the lanes
    num_vis_pred_points = np.array([8.0, 4.0])
    num_vis_gt_points = np.array([8.0, 4.0])
    num_match_mat = np.array([[4.0, 0.0], [0.0, 4.0]])
    match_results = np.array([[0, 0, 0], [1, 1, 1]])
    pred_cat = [1, 20]
    gt_cat = [1, 20]
    expected_output = Lane3DEvalStats(
        recall=1.0,
        precision=1.0,
        category_matches=2.0,
        num_gts=len(gt_cat),
        num_preds=len(pred_cat),
        num_matches=2,
        num_matches_by_distance=np.array([2, 2]),
        x_error_by_distance=np.array([0.0, 0.0]),
        z_error_by_distance=np.array([0.16666667, 0.0]),
    )

    # WHEN calling the function
    output = eval_instance._compute_eval_stats(  # noqa: SLF001
        dist_mat,
        num_vis_pred_points,
        num_vis_gt_points,
        match_results,
        num_match_mat,
        pred_cat,
        gt_cat,
    )

    # THEN the output should match the expected
    _assert_equal_lane_eval_stats(output, expected_output)


# ==========Lane3DEval._sample_eval()==========
def test_sample_eval_all_matching(
    eval_instance: Lane3DEval,
    two_lanes_with_full_visibility_fixture: tuple[list[NDArray[np.float32]], list[NDArray[np.bool_]], list[int]],
) -> None:
    """Test _sample_eval with all gts/preds matching ."""
    # GIVEN a Lane3DEval instance with matching GTs and Predictions
    gt_lanes, gt_visibility, gt_category = two_lanes_with_full_visibility_fixture
    pred_lanes, pred_category = copy.deepcopy(gt_lanes), copy.deepcopy(gt_category)

    expected_output = Lane3DEvalStats(
        recall=2.0,
        precision=2.0,
        category_matches=2.0,
        num_gts=2,
        num_preds=2,
        num_matches=2,
        num_matches_by_distance=np.array([2, 2]),
        x_error_by_distance=np.array([0.0, 0.0]),
        z_error_by_distance=np.array([0.0, 0.0]),
    )

    # WHEN calling the function
    sample_eval_result = eval_instance._sample_eval(  # noqa: SLF001
        pred_lanes, gt_lanes, gt_visibility, pred_category, gt_category
    )

    # THEN the output should match the expected results
    _assert_equal_lane_eval_stats(sample_eval_result, expected_output)


def test_sample_eval_no_matching(
    eval_instance: Lane3DEval,
    two_lanes_with_full_visibility_fixture: tuple[list[NDArray[np.float32]], list[NDArray[np.bool_]], list[int]],
) -> None:
    """Test _sample_eval with no gts/preds matching ."""
    # GIVEN a Lane3DEval instance
    # GIVEN Ground Truth Lanes and Predicted Lanes with no matching points
    gt_lanes, gt_visibility, gt_category = two_lanes_with_full_visibility_fixture
    pred_lanes, pred_category = copy.deepcopy(gt_lanes), copy.deepcopy(gt_category)
    # Modify the predicted lanes to have no matching points
    pred_lanes[0][0, :] = pred_lanes[0][0, :] + 10.0
    pred_lanes[1][0, :] = pred_lanes[0][0, :] + 10.0

    expected_output = Lane3DEvalStats(
        recall=0.0,
        precision=0.0,
        category_matches=0.0,
        num_gts=2,
        num_preds=0,
        num_matches=0,
        num_matches_by_distance=np.array([0, 0]),
        x_error_by_distance=np.array([0.0, 0.0]),
        z_error_by_distance=np.array([0.0, 0.0]),
    )

    # WHEN calling the function
    sample_eval_result = eval_instance._sample_eval(  # noqa: SLF001
        pred_lanes, gt_lanes, gt_visibility, pred_category, gt_category
    )

    # THEN the output should match the expected results
    _assert_equal_lane_eval_stats(sample_eval_result, expected_output)


# ==========Lane3DEval._add_eval_to_csv==========
@pytest.fixture
def mock_path_methods(mocker: MagicMock) -> tuple[MagicMock, MagicMock, MagicMock, MagicMock, MagicMock, MagicMock]:
    """Fixture to mock Path methods and csv.writer."""
    mock_mkdir = mocker.patch("pathlib.Path.mkdir")

    # Patch Path.is_file in the actual module where _add_eval_to_csv is defined
    mock_is_file = mocker.patch("pathlib.Path.is_file", return_value=False)

    # Mock file context manager
    mock_file_handle = MagicMock()
    mock_file_context = MagicMock()
    mock_file_context.__enter__.return_value = mock_file_handle
    mock_open = mocker.patch(
        "pathlib.Path.open",
        return_value=mock_file_context,
    )

    # Mock CSV writer
    mock_writer_instance = MagicMock()
    mock_csv_writer = mocker.patch(
        "csv.writer",
        return_value=mock_writer_instance,
    )

    return mock_writer_instance, mock_is_file, mock_file_handle, mock_mkdir, mock_open, mock_csv_writer


def test_add_eval_to_csv_new_file(
    mock_path_methods: tuple[MagicMock, MagicMock, MagicMock, MagicMock, MagicMock, MagicMock],
) -> None:
    """Test _add_eval_to_csv when the CSV file does not exist."""
    eval_instance = Lane3DEval(
        task_id="test_task",
        pos_threshold=0.3,
        top_view_region=np.array([[-10, 21], [10, 21], [-10, 1], [10, 1]], dtype=np.float32),
        metric_save_path=Path("not-used-variable-in-this-test"),
    )
    mock_writer_instance, _, _, _, _, _ = mock_path_methods

    metrics_and_errors = Lane3DMetrics(
        f1_score=0.85,
        recall=0.9,
        precision=0.8,
        cat_accuracy=0.95,
        x_error_by_distance={
            eval_instance._distance_bins[0]: 5.0,  # noqa: SLF001
            eval_instance._distance_bins[1]: 10.0,  # noqa: SLF001
        },
        z_error_by_distance={
            eval_instance._distance_bins[0]: 2.5,  # noqa: SLF001
            eval_instance._distance_bins[1]: 7.5,  # noqa: SLF001
        },
    )
    result_dir = Path("./tmp/path")

    eval_instance._add_eval_to_csv(metrics_and_errors, result_dir=result_dir)  # noqa: SLF001

    # Assert CSV header written
    mock_writer_instance.writerow.assert_any_call([_to_camel_case(key) for key in metrics_and_errors.get_result_dict()])

    # Assert data row written
    mock_writer_instance.writerow.assert_any_call(list(metrics_and_errors.get_result_dict().values()))


def test_add_eval_to_csv_existing_file(
    mock_path_methods: tuple[MagicMock, MagicMock, MagicMock, MagicMock, MagicMock, MagicMock],
) -> None:
    """Test _add_eval_to_csv when the CSV file already exist."""
    eval_instance = Lane3DEval(
        task_id="test_task",
        pos_threshold=0.3,
        top_view_region=np.array([[-10, 21], [10, 21], [-10, 1], [10, 1]], dtype=np.float32),
        metric_save_path=Path("not-used-variable-in-this-test"),
    )
    mock_writer_instance, mock_is_file, _, _, _, _ = mock_path_methods
    mock_is_file.return_value = True  # Simulate file exists

    metrics_and_errors = Lane3DMetrics(
        f1_score=0.85,
        recall=0.9,
        precision=0.8,
        cat_accuracy=0.95,
        x_error_by_distance={
            eval_instance._distance_bins[0]: 5.0,  # noqa: SLF001
            eval_instance._distance_bins[1]: 10.0,  # noqa: SLF001
        },
        z_error_by_distance={
            eval_instance._distance_bins[0]: 2.5,  # noqa: SLF001
            eval_instance._distance_bins[1]: 7.5,  # noqa: SLF001
        },
    )
    result_dir = Path("./tmp/path")

    eval_instance._add_eval_to_csv(metrics_and_errors, result_dir=result_dir)  # noqa: SLF001

    # Assert data row written
    mock_writer_instance.writerow.assert_any_call([0.85, 0.8, 0.9, 0.95, 5.0, 10.0, 2.5, 7.5])


def test_reset_clears_accumulated_stats(eval_instance: Lane3DEval) -> None:
    """Test that reset clears all accumulated statistics and matched lane counters."""
    # GIVEN: Lane3DEval instance with nonzero stats and matched lane counters
    eval_instance._stats = Lane3DEvalStats(  # noqa: SLF001
        recall=2.0,
        precision=2.0,
        category_matches=2.0,
        num_gts=2,
        num_preds=2,
        num_matches=2,
        num_matches_by_distance=np.array([2, 2]),
        x_error_by_distance=np.array([0.25, 0.25]),
        z_error_by_distance=np.array([0.25, 0.25]),
    )

    # WHEN: Calling reset
    eval_instance.reset()

    # THEN: All stats and counters should be reset
    reset_stats = Lane3DEvalStats(
        precision=0.0,
        category_matches=0.0,
        num_gts=0,
        num_preds=0,
        num_matches=0,
        num_matches_by_distance=np.array([0, 0]),
        x_error_by_distance=np.array([0.0, 0.0]),
        z_error_by_distance=np.array([0.0, 0.0]),
    )
    _assert_equal_lane_eval_stats(eval_instance._stats, reset_stats)  # noqa: SLF001


def _assert_equal_lane_eval_stats(
    actual: Lane3DEvalStats,
    expected: Lane3DEvalStats,
) -> None:
    """Assert that two Lane3DEvalStats dataclasses are equal."""
    for field in fields(expected):
        if isinstance(getattr(expected, field.name), np.ndarray):
            np.testing.assert_allclose(getattr(actual, field.name), getattr(expected, field.name))
        else:
            assert getattr(actual, field.name) == getattr(expected, field.name)
