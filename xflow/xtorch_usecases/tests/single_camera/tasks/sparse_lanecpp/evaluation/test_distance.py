"""Tests for the Lane3D distance computation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import numpy as np
import pytest
from numpy.typing import NDArray

from xtorch_usecases.single_camera.tasks.sparse_lanecpp.evaluation.distance import (
    IGNORE_DISTANCE_VAL,
    _compute_average_dist,
    compute_dists_cost_mat,
)

_Y_LIMITS = (1, 21)
_NUM_Y_STEPS = 4
_Y_STEPS = np.linspace(_Y_LIMITS[0], _Y_LIMITS[1], num=_NUM_Y_STEPS, endpoint=False)


@pytest.mark.parametrize(
    ("gt_lanes", "gt_visibility_mat", "pred_lanes", "pred_visibility_mat", "expected_result"),
    [
        # Case 1: Valid input with two predicted and two ground truth lanes, last
        (
            [
                np.array([[0.0, 0.0], [2.5, 2.5], [5.0, 5.0], [7.5, 7.5]], dtype=np.float32),
                np.array([[2.5, 2.5], [5.0, 5.0], [7.5, 8.0], [10.0, 10.0]], dtype=np.float32),
            ],
            np.array([[1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0]], dtype=np.float32),
            [
                np.array([[0.0, 0.0], [2.5, 2.5], [5.0, 5.0], [7.5, 7.5]], dtype=np.float32),
                np.array([[2.5, 2.5], [5.0, 5.0], [7.5, 7.5], [10.0, 10.0]], dtype=np.float32),
            ],
            np.array(
                [[1.0, 1.0, 1.0, 1.0], [1.0, 1.0, 1.0, 1.0]],
                dtype=np.float32,
            ),
            {
                "cost_mat": np.array([[0, 14], [14, 1]]),
                "adj_mat": np.array([[1, 1], [1, 1]]),
                "num_match_mat": np.array([[4.0, 0.0], [0.0, 4.0]]),
                "x_dist_mat_close": np.array([[0.0, 2.5], [2.5, 0.0]]),
                "x_dist_mat_far": np.array([[0.0, 2.5], [2.5, 0.0]]),
                "z_dist_mat_close": np.array([[0.0, 2.5], [2.5, 0.0]]),
                "z_dist_mat_far": np.array([[0.0, 2.5], [2.75, 0.25]]),
            },
        ),
        # Case 2: Partial visibility
        (
            [
                np.array([[5.0, 5.0], [7.5, 7.5], [10.0, 10.0], [12.5, 12.5]], dtype=np.float32),
            ],
            np.array([[1.0, 1.0, 1.0, 0.0]], dtype=np.float32),
            [
                np.array([[15.0, 15.0]], dtype=np.float32),
            ],
            np.array(
                [[0.0, 0.0, 0.0, 1.0]],
                dtype=np.float32,
            ),
            {
                "cost_mat": np.array([[6]]),
                "adj_mat": np.array([[1]]),
                "num_match_mat": np.array([[0.0]]),
                "x_dist_mat_close": np.array([[-1.0]]),
                "x_dist_mat_far": np.array([[-1.0]]),
                "z_dist_mat_close": np.array([[-1.0]]),
                "z_dist_mat_far": np.array([[-1.0]]),
            },
        ),
    ],
)
def test_compute_dists_cost_mat(
    gt_lanes: list[NDArray[np.float32]],
    gt_visibility_mat: NDArray[np.float32],
    pred_lanes: list[NDArray[np.float32]],
    pred_visibility_mat: NDArray[np.float32],
    expected_result: dict[str, NDArray[np.float32]],
) -> None:
    """Test compute_dists_cost_mat with various scenarios."""
    # GIVEN lanes and visibility inputs, where the last point is in far range based on y_steps
    # WHEN calling the function
    cost_mat, adj_mat, num_match_mat, dist_mat = compute_dists_cost_mat(
        pred_lanes,
        pred_visibility_mat,
        gt_lanes,
        gt_visibility_mat,
        y_steps=_Y_STEPS,
        distance_bins=np.array([10.0, 21.0]),
        dist_threshold=1.5,
    )

    # THEN the values of the returned matrices should match the expected results
    expected_dist_close = np.stack((expected_result["x_dist_mat_close"], expected_result["z_dist_mat_close"]), axis=-1)
    expected_dist_far = np.stack((expected_result["x_dist_mat_far"], expected_result["z_dist_mat_far"]), axis=-1)
    expected_dist_mat = np.stack((expected_dist_close, expected_dist_far), axis=-2)
    assert np.allclose(cost_mat, expected_result["cost_mat"])
    assert np.allclose(adj_mat, expected_result["adj_mat"])
    assert np.allclose(num_match_mat, expected_result["num_match_mat"])
    assert np.allclose(dist_mat, expected_dist_mat)


@pytest.mark.parametrize("dist_bin_idxs", [np.array([1, 3]), np.array([1, 2, 4])])
def test_compute_average_dist_all_visible(dist_bin_idxs: NDArray[np.int16]) -> None:
    """Tests the compute_average_dist function with visible points."""
    # GIVEN distance_bin_idxs
    # GIVEN a distance matrix with 4 points that are all visible
    num_points = 4
    dist_matrix = np.ones((num_points, 2), dtype=np.float32)
    visibility = np.ones((num_points), dtype=bool)

    # WHEN calculating the average distance
    result = _compute_average_dist(dist_matrix, visibility, dist_bin_idxs)

    # THEN the result is as expected (all 1s)
    assert result.shape[0] == len(dist_bin_idxs)
    np.testing.assert_allclose(result, np.ones_like(result))


@pytest.mark.parametrize("dist_bin_idxs", [np.array([1, 3]), np.array([1, 2, 4])])
def test_compute_average_dist_all_invisible(dist_bin_idxs: NDArray[np.int16]) -> None:
    """Tests the compute_average_dist function with invisible points."""
    # GIVEN distance_bin_idxs
    # GIVEN a distance matrix with no visible points
    num_points = 4
    dist_matrix = np.ones((num_points, 2), dtype=np.float32)
    visibility = np.zeros((num_points), dtype=bool)

    # WHEN calculating the average distance
    result = _compute_average_dist(dist_matrix, visibility, dist_bin_idxs)

    # THEN the result is as expected (all IGNORE_DISTANCE_VAL)
    assert result.shape[0] == len(dist_bin_idxs)
    np.testing.assert_allclose(result, np.full_like(result, IGNORE_DISTANCE_VAL))
