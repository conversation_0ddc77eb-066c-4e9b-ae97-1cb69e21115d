"""Tests the SparseLanecpp loss."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import pytest
import torch

from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
    SparseLanecppHeadRawOutput,
    SparseLanecppHeadScaledOutput,
    SparseLanecppInstanceHeadOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.loss import SparseLanecppLoss, sum_to_one_loss
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.losses.detection import DetectionLossOutput
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.losses.sparse_instance import SparseInstanceLossOutput

NUM_LAYERS = 2
BATCH_SIZE = 2
NUM_QUERIES = 4
NUM_Y_STEPS = 5
NUM_Y_STEPS_DENSE = 10
NUM_CLASSES = 2
NUM_GROUPS = 2
HEIGHT = 16
WIDTH = 16
STRIDE = 4
NUM_GT_LANES = 5

torch.manual_seed(0)


@pytest.fixture(name="dummy_head_output")
def fixture_dummy_head_output(device: str) -> SparseLanecppHeadOutput:
    """Fixture for a dummy SparseLanecppHeadOutput."""
    return SparseLanecppHeadOutput(
        raw_output=SparseLanecppHeadRawOutput(
            class_scores=torch.ones(NUM_LAYERS, BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, NUM_CLASSES, device=device),
            lane_preds_unscaled=torch.ones(
                NUM_LAYERS, BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, 3 * NUM_Y_STEPS, device=device
            ),
        ),
        scaled_output=SparseLanecppHeadScaledOutput(
            lanes_x=torch.ones(NUM_LAYERS, BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, NUM_Y_STEPS, device=device),
            lanes_z=torch.ones(NUM_LAYERS, BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, NUM_Y_STEPS, device=device),
            lanes_visibility=torch.ones(NUM_LAYERS, BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, NUM_Y_STEPS, device=device),
        ),
        lanes_dense=torch.ones(NUM_LAYERS, BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, 3 * NUM_Y_STEPS_DENSE, device=device),
        instance_output=SparseLanecppInstanceHeadOutput(
            masks=torch.ones(BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, HEIGHT // STRIDE, WIDTH // STRIDE, device=device),
            scores=torch.ones(BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, device=device),
            logits=torch.ones(BATCH_SIZE, NUM_GROUPS * NUM_QUERIES, NUM_CLASSES, device=device),
        ),
    )


@pytest.fixture(name="dummy_gt")
def fixture_dummy_gt(device: str) -> SparseLanecppGroundTruth:
    """Fixture for a dummy SparseLanecppGroundTruth."""
    return SparseLanecppGroundTruth(
        lane_category=torch.randint(0, 2, (BATCH_SIZE, NUM_GT_LANES, NUM_CLASSES + 1), device=device),
        lanes_dense=torch.ones(BATCH_SIZE, NUM_GT_LANES, 3 * NUM_Y_STEPS_DENSE, device=device),
        segmentation_idx=torch.ones(BATCH_SIZE, NUM_GT_LANES, HEIGHT // STRIDE, WIDTH // STRIDE, device=device),
        gt_id=torch.ones(BATCH_SIZE, NUM_GT_LANES, device=device),
        ego_pose=torch.ones(BATCH_SIZE, 4, 4, device=device),
        ego_pose_inv=torch.ones(BATCH_SIZE, 4, 4, device=device),
    )


@pytest.fixture(name="lane_cpp_loss")
def fixture_lane_cpp_loss() -> SparseLanecppLoss:
    """Fixture for the sparse LaneCPP loss."""
    return SparseLanecppLoss(
        num_categories=NUM_CLASSES,
        num_queries=NUM_QUERIES,
        num_y_steps=NUM_Y_STEPS,
        num_y_steps_dense=NUM_Y_STEPS_DENSE,
        num_groups=NUM_GROUPS,
    )


def test_loss(
    lane_cpp_loss: SparseLanecppLoss,
    dummy_head_output: SparseLanecppHeadOutput,
    dummy_gt: SparseLanecppGroundTruth,
) -> None:
    """Tests the SparseLanecpp SparseInstanceLoss forward function."""
    # GIVEN dummy predictions and a dummy ground truth
    # GIVEN the sparse lane cpp loss
    # WHEN the loss is computed
    loss = lane_cpp_loss(dummy_head_output, dummy_gt)

    # THEN the total loss is a tensor
    assert isinstance(loss.total_loss, torch.Tensor)

    # THEN the total loss is larger than 0
    assert loss.total_loss > 0

    # THEN the sub-losses are a dict
    assert isinstance(loss.sublosses, dict)

    # THEN the sub-loss keys are as expected
    # Add all keys from SparseInstanceLossOutput and DetectionLossOutput
    # Drop the smoothness losses because they are added to the regression losses before returning the LossOutput
    expected_keys = set(list(SparseInstanceLossOutput._fields) + list(DetectionLossOutput._fields))
    expected_keys -= {"xs_smooth_loss", "zs_smooth_loss"}
    assert set(loss.sublosses.keys()) == expected_keys

    # THEN all sub-losses are tensors
    for subloss in loss.sublosses.values():
        assert isinstance(subloss, torch.Tensor)


@pytest.mark.parametrize(
    ("predictions", "expected_loss"),
    [
        (torch.tensor([0.0, 1.0]), 0.0),
        (torch.tensor([0.0, 0.0]), 1.0),
        (torch.tensor([1.0, 1.0]), 1.0),
        (torch.tensor([1.0, 2.0]), 4.0),
    ],
)
def test_sum_to_one_loss(predictions: torch.Tensor, expected_loss: float, device: str) -> None:
    """Tests if the sum_to_one_loss function works as expected."""
    # GIVEN a tensor of predictions
    predictions = predictions.view(1, -1).to(device)
    # WHEN the sum_to_one_loss function is called
    loss = sum_to_one_loss(predictions)
    # THEN the loss is as expected
    assert loss.cpu().numpy() == expected_loss
