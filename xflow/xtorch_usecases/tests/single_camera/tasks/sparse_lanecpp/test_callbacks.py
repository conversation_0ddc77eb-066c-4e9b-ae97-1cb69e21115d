"""Pytest for the callback extraction function of SparseLanecpp."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import numpy as np
import torch

from xtorch_extensions.visualization.callbacks.lane3d import VisuLane3D
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.callbacks import (
    extract_visu_lane3d_from_gt,
    extract_visu_lane3d_from_preds,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
    SparseLanecppHeadRawOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.lane_postprocessing import (
    post_processing_preds,
)


def test_extract_visu_lane3d_from_gt() -> None:
    """Test the extract_visu_lane3d_from_gt function."""
    # GIVEN input data
    batch_size = 2
    num_lanes = 3
    num_points = 5
    num_classes = 2  # Number of classes for one-hot encoding

    ground_truth = SparseLanecppGroundTruth(
        gt_id=torch.zeros(batch_size),
        lanes_dense=torch.rand(batch_size, num_lanes, num_points, 3),
        lane_category=torch.randint(0, 2, (batch_size, num_lanes, num_classes)),  # One-hot encoded
        ego_pose=torch.zeros(batch_size, 4, 4),
        ego_pose_inv=torch.zeros(batch_size, 4, 4),
        segmentation_idx=torch.zeros(batch_size),
    )

    # Expected output
    rng = np.random.default_rng()
    expected_visu_lane3d = [
        VisuLane3D(
            lanes_category=[np.array([1, 0])],
            lanes_dense_points=[rng.random((num_points, 3), dtype=np.float32)],
            lanes_vis=[np.array([True, False, True])],
        )
        for _ in range(batch_size)
    ]

    # WHEN Calling the function
    visu_lane3d = extract_visu_lane3d_from_gt(ground_truth)

    # THEN assert the output is as expected
    assert len(visu_lane3d) == len(expected_visu_lane3d)


def test_extract_visu_lane3d_from_preds() -> None:
    """Test the extract_visu_lane3d_from_preds function."""
    # GIVEN input data
    batch_size = 2
    num_lanes = 3
    num_points = 5
    num_classes = 2  # Number of classes for classification scores

    # Create realistic input data
    class_scores = torch.rand(batch_size, num_lanes, num_classes)  # Classification scores
    lanes_preds_unscaled = torch.rand(batch_size, num_lanes, num_points * 3)  # Lane predictions
    lanes_dense = torch.rand(batch_size, num_lanes, num_points * 3)  # Lane predictions

    preds = SparseLanecppHeadOutput(
        raw_output=SparseLanecppHeadRawOutput(class_scores=class_scores, lane_preds_unscaled=lanes_preds_unscaled),
        lanes_dense=lanes_dense,
    )

    task_identifier = "dummy"
    outputs = {task_identifier: preds}

    # Expected output (processed predictions)
    postprocessed_lanes = post_processing_preds(class_scores, lanes_dense)
    expected_visu_lane3d = [
        VisuLane3D(
            lanes_category=pred.pred_lanes_prob,
            lanes_dense_points=pred.pred_lanes,
            lanes_vis=pred.pred_lanes_vis,
        )
        for pred in postprocessed_lanes
    ]

    # WHEN calling the function
    visu_lane3d = extract_visu_lane3d_from_preds(outputs, task_identifier, get_preds=lambda x, y: x[y])

    # THEN assert the output is as expected
    assert len(visu_lane3d) == len(expected_visu_lane3d)
    for idx in range(len(visu_lane3d)):
        assert len(visu_lane3d[idx].lanes_category) == len(expected_visu_lane3d[idx].lanes_category)
        assert len(visu_lane3d[idx].lanes_dense_points) == len(expected_visu_lane3d[idx].lanes_dense_points)
        assert len(visu_lane3d[idx].lanes_vis) == len(expected_visu_lane3d[idx].lanes_vis)
