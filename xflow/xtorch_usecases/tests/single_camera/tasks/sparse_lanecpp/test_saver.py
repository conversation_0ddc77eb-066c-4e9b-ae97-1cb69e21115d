"""Tests the SparseLaneCPP-Saver."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path
from typing import Any
from unittest.mock import MagicMock, patch

import numpy as np
import orjson
import pytest
import torch

from xcontract.camera_models.definitions import CameraModelType
from xcontract.data.definitions.usage import ValueKey
from xtorch.camera_models.camera_model import CameraParameters
from xtorch.training import Stage
from xtorch.training.training_module import StepOutputKey
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.definitions import (
    SparseLanecppGroundTruth,
    SparseLanecppHeadOutput,
    SparseLanecppHeadRawOutput,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.model.line_decoder import SparseLanecppLineDecoder
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.saver import SparseLanecppPredictionSaver

BATCH_SIZE = 2
NUM_LANES = 3
NUM_POINTS = 5
NUM_POINTS_DENSE = 10
NUM_CLASSES = 2
NUM_TRANSFORMER_LAYERS = 6
torch.manual_seed(42)
DUMMY_TASK_ID = "dummy_lane_task"
DUMMY_CAMERA_PARAMETER_TASK_ID = "dummy_camera_task"
DUMMY_INPUT_KEY = "dummy_input_key"


@pytest.fixture(name="dummy_ground_truth")
def fixture_dummy_ground_truth(device: str) -> SparseLanecppGroundTruth:
    """Returns a dummy ground truth object."""
    return SparseLanecppGroundTruth(
        gt_id=torch.zeros(BATCH_SIZE, device=device),
        lanes_dense=torch.rand(BATCH_SIZE, NUM_LANES, NUM_POINTS, 3, device=device),
        lane_category=torch.randint(0, 2, (BATCH_SIZE, NUM_LANES, NUM_CLASSES), device=device),  # One-hot encoded
        ego_pose=torch.zeros(BATCH_SIZE, 4, 4, device=device),
        ego_pose_inv=torch.zeros(BATCH_SIZE, 4, 4, device=device),
        segmentation_idx=torch.zeros(BATCH_SIZE, device=device),
    )


@pytest.fixture(name="dummy_camera_params")
def fixture_dummy_camera_params(device: str) -> CameraParameters:
    """Returns dummy camera parameters."""
    return CameraParameters(
        focal_length=torch.rand(BATCH_SIZE, 2, device=device),
        principal_point=torch.rand(BATCH_SIZE, 2, device=device),
        extrinsics=torch.rand(BATCH_SIZE, 4, 4, device=device),
        camera_model_type=torch.ones(BATCH_SIZE, dtype=torch.int8, device=device) * CameraModelType.PINHOLE.value,
    )


@pytest.fixture(name="dummy_batch")
def fixture_dummy_batch(
    dummy_camera_params: CameraParameters, dummy_ground_truth: SparseLanecppGroundTruth, device: str
) -> dict[str, Any]:
    """Returns a dummy batch containing the input data required for the saver."""
    return {
        DUMMY_CAMERA_PARAMETER_TASK_ID: {
            ValueKey.IDENTIFIER: np.asarray([f"dummy_camera_id_{idx}.json" for idx in range(BATCH_SIZE)]),
            ValueKey.VALID: torch.ones(BATCH_SIZE, dtype=torch.bool, device=device),
            ValueKey.DATA: dummy_camera_params,
        },
        DUMMY_TASK_ID: {
            ValueKey.IDENTIFIER: np.asarray([f"dummy_lane_{idx}" for idx in range(BATCH_SIZE)]),
            ValueKey.VALID: torch.ones(BATCH_SIZE, dtype=torch.bool, device=device),
            ValueKey.DATA: dummy_ground_truth,
        },
        DUMMY_INPUT_KEY: {
            ValueKey.IDENTIFIER: np.asarray([f"dummy_image_{idx}.png" for idx in range(BATCH_SIZE)]),
            ValueKey.VALID: torch.ones(BATCH_SIZE, dtype=torch.bool, device=device),
            ValueKey.DATA: torch.rand(BATCH_SIZE, 3, 224, 224),  # Dummy image data
        },
    }


@pytest.fixture(name="dummy_preds")
def fixture_dummy_preds(device: str) -> SparseLanecppHeadOutput:
    """Returns a dummy prediction object."""
    class_scores = torch.rand(NUM_TRANSFORMER_LAYERS, BATCH_SIZE, NUM_LANES, NUM_CLASSES, device=device)
    lanes_preds_unscaled = torch.rand(NUM_TRANSFORMER_LAYERS, BATCH_SIZE, NUM_LANES, NUM_POINTS * 3, device=device)
    lanes_dense = torch.rand(NUM_TRANSFORMER_LAYERS, BATCH_SIZE, NUM_LANES, NUM_POINTS_DENSE * 3, device=device)
    return SparseLanecppHeadOutput(
        raw_output=SparseLanecppHeadRawOutput(
            class_scores=class_scores,
            lane_preds_unscaled=lanes_preds_unscaled,
        ),
        lanes_dense=lanes_dense,
    )


@pytest.fixture(name="dummy_preds_inference")
def fixture_dummy_preds_inference(dummy_preds: SparseLanecppHeadOutput) -> SparseLanecppHeadOutput:
    """Returns a dummy prediction object for inference.

    During inference, only the raw output of the last transformer layer is available.
    """
    return SparseLanecppHeadOutput(
        raw_output=SparseLanecppHeadRawOutput(
            class_scores=dummy_preds.raw_output.class_scores[-1],
            lane_preds_unscaled=dummy_preds.raw_output.lane_preds_unscaled[-1],
        ),
    )


@pytest.fixture(name="sparse_lanecpp_saver")
def fixture_sparse_lanecpp_saver(tmpdir: Path) -> SparseLanecppPredictionSaver:
    """Returns a SparseLanecppPredictionSaver instance."""

    return SparseLanecppPredictionSaver(
        task_id=DUMMY_TASK_ID,
        evaluation_folder=str(tmpdir),
        input_key=DUMMY_INPUT_KEY,
        line_decoder=MagicMock(spec=SparseLanecppLineDecoder),
        camera_parameter_task_id=DUMMY_CAMERA_PARAMETER_TASK_ID,
    )


def test_lanecpp_saver_validation(
    dummy_batch: dict[str, Any],
    dummy_preds: SparseLanecppHeadOutput,
    sparse_lanecpp_saver: SparseLanecppPredictionSaver,
    tmpdir: Path,
) -> None:
    """Tests the SparseLanecppPredictionSaver in validation mode."""
    # GIVEN a SparseLanecppPredictionSaver instance

    # GIVEN a dummy batch and dummy predictions stored in the step output
    outputs = {StepOutputKey.PREDICTIONS.value: {DUMMY_TASK_ID: dummy_preds}}

    # GIVEN a mocked _get_epoch_prediction_folder method and a mocked trainer
    with patch.object(sparse_lanecpp_saver, "_get_epoch_prediction_folder") as mock_get_epoch_prediction_folder:
        mock_get_epoch_prediction_folder.return_value = Path(tmpdir)

        # WHEN saving the predictions in validation mode
        sparse_lanecpp_saver.save_predictions(
            trainer=MagicMock(), outputs=outputs, batch=dummy_batch, stage=Stage.VALIDATE
        )

    # THEN the predictions should be saved correctly as a JSON file using the image identifier
    for batch_idx in range(BATCH_SIZE):
        identifier = Path(dummy_batch[DUMMY_INPUT_KEY][ValueKey.IDENTIFIER][batch_idx]).stem
        pred_save_path = tmpdir / f"{identifier}.json"
        assert pred_save_path.exists()

        # THEN the saved JSON file should contain the expected keys
        with pred_save_path.open("rb") as f:
            pred_data = orjson.loads(f.read())
            assert "lane_lines" in pred_data
            assert "intrinsic" in pred_data
            assert "extrinsic" in pred_data
            assert pred_data["intrinsic"] is not None
            assert pred_data["extrinsic"] is not None


def test_lanecpp_saver_predict(
    dummy_batch: dict[str, Any],
    dummy_preds_inference: SparseLanecppHeadOutput,
    sparse_lanecpp_saver: SparseLanecppPredictionSaver,
    tmpdir: Path,
) -> None:
    """Tests the SparseLanecppPredictionSaver in prediction mode."""
    # GIVEN a SparseLanecppPredictionSaver instance

    # GIVEN a dummy batch and dummy predictions stored in the step output
    outputs = {StepOutputKey.PREDICTIONS.value: {DUMMY_TASK_ID: dummy_preds_inference}}

    # GIVEN a mocked _get_epoch_prediction_folder method, a mocked trainer and a mocked line decoder
    with (
        patch.object(sparse_lanecpp_saver, "_get_epoch_prediction_folder") as mock_get_epoch_prediction_folder,
        patch.object(sparse_lanecpp_saver, "_line_decoder") as mock_decoder,
    ):
        mock_get_epoch_prediction_folder.return_value = Path(tmpdir)
        # Only the dense lanes are used in the saver
        mock_decoder.side_effect = lambda *args, **kwargs: (
            None,
            None,
            None,
            torch.rand(1, BATCH_SIZE, NUM_LANES, NUM_POINTS_DENSE * 3, device="cpu"),
        )

        # WHEN saving the predictions in prediction mode
        sparse_lanecpp_saver.save_predictions(
            trainer=MagicMock(), outputs=outputs, batch=dummy_batch, stage=Stage.PREDICT
        )

    # THEN the predictions should be saved correctly as a JSON file using the image identifier
    for batch_idx in range(BATCH_SIZE):
        identifier = Path(dummy_batch[DUMMY_INPUT_KEY][ValueKey.IDENTIFIER][batch_idx]).stem
        pred_save_path = tmpdir / f"{identifier}.json"
        assert pred_save_path.exists()

        # THEN the saved JSON file should contain the expected keys
        with pred_save_path.open("rb") as f:
            pred_data = orjson.loads(f.read())
            assert "lane_lines" in pred_data
            assert "intrinsic" in pred_data
            assert "extrinsic" in pred_data
            assert pred_data["intrinsic"] is not None
            assert pred_data["extrinsic"] is not None


def test_lanecpp_saver_missing_output_key(
    dummy_batch: dict[str, Any],
    sparse_lanecpp_saver: SparseLanecppPredictionSaver,
) -> None:
    """Tests if the SparseLanecppPredictionSaver raises an error when the supported output keys are missing."""
    # GIVEN a SparseLanecppPredictionSaver instance

    # GIVEN a dummy batch and dummy predictions stored in the step output
    outputs = {"unsupported_backend": "dummy_values"}

    # WHEN saving the predictions in prediction mode
    # THEN a ValueError should be raised
    with pytest.raises(ValueError, match="Unspported output_key!"):
        sparse_lanecpp_saver.save_predictions(
            trainer=MagicMock(), outputs=outputs, batch=dummy_batch, stage=Stage.PREDICT
        )


def test_lanecpp_saver_missing_camera_parameters(
    dummy_batch: dict[str, Any],
    dummy_preds: SparseLanecppHeadOutput,
    sparse_lanecpp_saver: SparseLanecppPredictionSaver,
    tmpdir: Path,
) -> None:
    """Tests if the SparseLanecppPredictionSaver raises an error when the camera parameters are missing."""
    # GIVEN a SparseLanecppPredictionSaver instance

    # GIVEN dummy predictions stored in the step output
    outputs = {StepOutputKey.PREDICTIONS.value: {DUMMY_TASK_ID: dummy_preds}}

    # GIVEN a dummy batch without any camera parameters
    del dummy_batch[DUMMY_CAMERA_PARAMETER_TASK_ID]

    # GIVEN a mocked _get_epoch_prediction_folder method and a mocked trainer
    with patch.object(sparse_lanecpp_saver, "_get_epoch_prediction_folder") as mock_get_epoch_prediction_folder:
        mock_get_epoch_prediction_folder.return_value = Path(tmpdir)

        # WHEN saving the predictions in validation mode
        sparse_lanecpp_saver.save_predictions(
            trainer=MagicMock(), outputs=outputs, batch=dummy_batch, stage=Stage.VALIDATE
        )

    # THEN the predictions should be saved correctly as a JSON file using the image identifier
    for batch_idx in range(BATCH_SIZE):
        identifier = Path(dummy_batch[DUMMY_INPUT_KEY][ValueKey.IDENTIFIER][batch_idx]).stem
        pred_save_path = tmpdir / f"{identifier}.json"
        assert pred_save_path.exists()

        # THEN the saved JSON file should contain the expected keys without camera parameters
        with pred_save_path.open("rb") as f:
            pred_data = orjson.loads(f.read())
            assert "lane_lines" in pred_data
