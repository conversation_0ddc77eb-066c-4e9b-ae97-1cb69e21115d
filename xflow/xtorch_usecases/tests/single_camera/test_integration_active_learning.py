"""Test the export and convert functionality for fc1_yuv444_multi_task_trifocal with respect to active_learning."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import sys
from pathlib import Path
from unittest import mock

import onnx
import pytest

from xtorch.testing.run_in_subprocess import run_in_subprocess
from xtorch_usecases.common.pipeline import CONVERT_QNN_SUBFOLDER, EXPORT_SUBFOLDER
from xtorch_usecases.single_camera.convert import main as hydra_main_convert
from xtorch_usecases.single_camera.export import main as hydra_main_export


@pytest.mark.slow
@pytest.mark.lfs_dependencies(
    [
        "xtorch_usecases/tests/test_data/camera",
        "%datadir%/common/sequence_reader/18747b0c5db35e8be8f69b2b5d8f41e8b364798ad7ffe55511d51e1e9a2854af",
        "conversion/src/conversion/qnn/onnx_surgery/replacement_subgraphs/lsr_template_subgraph_trifocal_xtorch.onnx",
    ]
)
def test_export_convert(*, tmpdir_delete_after: Path) -> None:
    """Test the export and convert for fc1_yuv444_multi_task_trifocal with respect to active_learning."""

    output_folder = tmpdir_delete_after / "export"
    model_path = output_folder / EXPORT_SUBFOLDER / "fc1_yuv444_multi_task_trifocal_active_learning.onnx"
    export_path = str(model_path.parent.parent)

    # WHEN the config is loaded and the model is exported
    common_args_for_testing = [
        "--config-name",
        "fc1_yuv444_multi_task_trifocal",
        f"hydra.run.dir={tmpdir_delete_after!s}",  # Dump hydra logs into the test dir. Keep CWD clean.
    ]
    run_export(output_folder, common_args_for_testing)

    convert_outputs = tmpdir_delete_after / "convert"
    run_convert(export_path, common_args_for_testing, convert_outputs)


@run_in_subprocess(context="spawn")
def run_convert(export_path: str, common_args_for_testing: list[str], convert_outputs: Path) -> None:
    """Run the convert functionality with the given export path and common arguments."""

    convert_args_for_testing = common_args_for_testing + [
        f"config.output_folder={convert_outputs!s}",
        f"config.trainer.default_root_dir={convert_outputs!s}",
        f"config.callback_config.output_root_dir={convert_outputs!s}",
        f"config.input_folder={export_path!s}",
        "config.convert_config.num_calib_samples=2",
        "config.convert_config.quant_check_config.short_run=True",
    ]

    # WHEN the config is loaded and the model is converted
    with mock.patch.object(sys, "argv", sys.argv[:1] + convert_args_for_testing):
        hydra_main_convert()

    model_path_active_learning = (
        convert_outputs / CONVERT_QNN_SUBFOLDER / "fc1_yuv444_multi_task_trifocal_active_learning_qnn_htp.onnx"
    )
    model_path_short = convert_outputs / CONVERT_QNN_SUBFOLDER / "fc1_yuv444_multi_task_trifocal_short_qnn_htp.onnx"
    model_path_long = convert_outputs / CONVERT_QNN_SUBFOLDER / "fc1_yuv444_multi_task_trifocal_long_qnn_htp.onnx"

    # THEN the model can be loaded
    model_active_learning = onnx.load(str(model_path_active_learning))
    model_short = onnx.load(str(model_path_short))
    model_long = onnx.load(str(model_path_long))

    # THEN the active_learning model has the expected nodes and outputs
    op_types = [node.op_type for node in model_active_learning.graph.node]
    output_names = [output.name for output in model_active_learning.graph.output]
    expected_output_names = [
        "active_learning_light_sum_hard_entropy_score",
        "active_learning_traffic_light_sum_hard_entropy_score",
        "active_learning_traffic_sign_sum_hard_entropy_score",
    ]
    expected_op_types = ["Reshape", "Transpose", "Conv", "Transpose", "TopK2DSum"]
    assert all(expected_output_name in output_names for expected_output_name in expected_output_names)
    assert all(op_type in op_types for op_type in expected_op_types)

    # THEN the short and long model do not have active learning outputs
    assert all("trifocal_filtered_pre_nms_class_probs" not in output.name for output in model_short.graph.output)
    assert all("trifocal_filtered_pre_nms_class_probs" not in output.name for output in model_long.graph.output)


@run_in_subprocess(context="spawn")
def run_export(output_folder: Path, common_args_for_testing: list[str]) -> None:
    """Run the export functionality with the given output folder and common arguments."""

    export_args_for_testing = common_args_for_testing + [
        "config.checkpoint=null",
        f"config.output_folder={output_folder!s}",
        f"config.trainer.default_root_dir={output_folder!s}",
        f"config.callback_config.output_root_dir={output_folder!s}",
    ]
    # GIVEN the model_path the model is saved to
    model_path = output_folder / EXPORT_SUBFOLDER / "fc1_yuv444_multi_task_trifocal_active_learning.onnx"

    with mock.patch.object(sys, "argv", sys.argv[:1] + export_args_for_testing):
        hydra_main_export()

        # THEN the model can be loaded
        model = onnx.load(str(model_path))
        graph = model.graph
        ops_names = [node.name for node in graph.node]
        output_names = [output.name for output in graph.output]

        al_task_input_names = [
            "light_scores",
            "traffic_light_trifocal_filtered_pre_nms_class_probs",
            "traffic_sign_trifocal_filtered_pre_nms_class_probs",
        ]
        al_task_output_names = [
            "active_learning_light_sum_hard_entropy_score",
            "active_learning_traffic_light_sum_hard_entropy_score",
            "active_learning_traffic_sign_sum_hard_entropy_score",
        ]
        al_task_names = [
            "active_learning_light_sum_hard_entropy",
            "active_learning_traffic_light_sum_hard_entropy",
            "active_learning_traffic_sign_sum_hard_entropy",
        ]
        al_task_ops = ["Add", "Log", "Mul", "ReduceSum", "Neg", "TopK", "ReduceSum_1"]

        # THEN the model has the expected nodes and outputs
        for al_task_name, al_task_input_name, al_task_output_name in zip(
            al_task_names, al_task_input_names, al_task_output_names
        ):
            for al_task_op in al_task_ops:
                assert f"/model/{al_task_name}/{al_task_op}" in ops_names
            al_mul_node = next(node for node in graph.node if node.name == f"/model/{al_task_name}/Mul")
            al_add_node = next(node for node in graph.node if node.name == f"/model/{al_task_name}/Add")

            assert al_task_input_name in al_mul_node.input
            assert al_task_input_name in al_add_node.input
            assert al_task_output_name in output_names
