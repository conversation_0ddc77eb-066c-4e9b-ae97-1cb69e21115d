"""This module contains tests for the build_pyper of single camera usecase."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import json
import uuid
from collections.abc import Callable
from pathlib import Path
from unittest.mock import patch

import pandas as pd
import pytest

from pyper.dict_pipeline import PyperLoadable
from pyper.multi_task.dataset_input import IterationPolicy
from xcontract.data.definitions.inputs import InputImageView
from xcontract.data.definitions.usage import Usage
from xtorch.multi_task.structured_task import StructuredTask
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.single_camera.build_pyper import (
    build_input_iterable,
    build_pipeline_params_from_config,
    build_pyper_data_module,
    load_dataset_as_data_frame,
)
from xtorch_usecases.single_camera.config.camera_parameter_config import CAMERA_PARAMETER_TASK_ID
from xtorch_usecases.single_camera.config.model.fc1_yuv444_multi_task.model import (
    get_multi_task_config as get_fc1_multi_task_config,
)
from xtorch_usecases.single_camera.config.model.fc1_yuv444_multi_task_trifocal.model import (
    get_multi_task_config as get_fc1_trifocal_multi_task_config,
)
from xtorch_usecases.single_camera.config.schema import Config


def create_tmp(directory: str, suffix: str, content: str) -> str:
    """Create a temporary file inside the given directory and retunr its path."""
    temp_path = Path(directory) / f"{uuid.uuid4()}{suffix}"
    temp_path.write_text(content)
    return str(temp_path)


@pytest.fixture
def dummy_mltable_content(tmpdir_delete_after: str) -> list[dict[str, str | None]]:
    """Return a list of dictionaries with dummy data in JSON format."""

    tmp_png1_1 = create_tmp(tmpdir_delete_after, ".png", "Dummy content for png1_1")
    tmp_png3_1 = create_tmp(tmpdir_delete_after, ".png", "Dummy content for png3_1")
    tmp_png1_2 = create_tmp(tmpdir_delete_after, ".png", "Dummy content for png1_2")
    tmp_png2_2 = create_tmp(tmpdir_delete_after, ".png", "Dummy content for png2_2")
    tmp_png3_2 = create_tmp(tmpdir_delete_after, ".png", "Dummy content for png3_2")
    tmp_label1_2 = create_tmp(tmpdir_delete_after, ".json", "Dummy content for label1_2")
    tmp_png1_3 = create_tmp(tmpdir_delete_after, ".png", "Dummy content for png1_3")
    tmp_label1_3 = create_tmp(tmpdir_delete_after, ".json", "Dummy content for label1_3")

    # Return the data with paths to the temporary files
    return [
        {"png1": tmp_png1_1, "png2": None, "png3": tmp_png3_1, "label1": None},
        {
            "png1": tmp_png1_2,
            "png2": tmp_png2_2,
            "png3": tmp_png3_2,
            "label1": tmp_label1_2,
        },
        {"png1": tmp_png1_3, "png2": None, "png3": None, "label1": tmp_label1_3},
    ]


@pytest.fixture
def dummy_mltable_path(dummy_mltable_content: list[dict[str, str | None]], tmpdir_delete_after: Path) -> Path:
    """Return a path to a temporary directory containing a dummy MLTable dataset."""
    tmpdir_delete_after_as_path = tmpdir_delete_after  # Persistent temp directory
    data_file = tmpdir_delete_after_as_path / "dataset.jsonl"
    metadata_file = tmpdir_delete_after_as_path / "MLTable"

    # Write JSONL data file
    with Path.open(data_file, "w", encoding="utf-8") as f:
        for row in dummy_mltable_content:
            f.write(json.dumps(row) + "\n")

    # Write MLTable metadata file
    metadata_content = {
        "paths": [{"file": "dataset.jsonl"}],
        "transformations": [{"read_json_lines": {"encoding": "utf-8"}}],
    }
    with Path.open(metadata_file, "w", encoding="utf-8") as f:
        json.dump(metadata_content, f, indent=4)

    return tmpdir_delete_after_as_path


@pytest.fixture
def csv_path(datadir: Path) -> Path:
    """Path to a CSV file used in test functions in this module."""
    csv_path = datadir / "camera/yuv_single_camera_dataset.csv"
    return csv_path


@pytest.fixture
def fc1_dataset(datadir: Path) -> pd.DataFrame:
    """A pandas DataFrame used in test functions in this module."""
    csv_path = datadir / "camera/yuv_single_camera_dataset.csv"
    dataset = load_dataset_as_data_frame(
        dataset_base_path=csv_path.parent, dataset_name=csv_path.name, dataset_workspace=None
    )
    return dataset


@pytest.fixture
def trifocal_dataset(datadir: Path) -> pd.DataFrame:
    """A pandas DataFrame used in test functions in this module."""
    csv_path = datadir / "camera/yuv_single_camera_trifocal_dataset.csv"
    dataset = load_dataset_as_data_frame(
        dataset_base_path=csv_path.parent, dataset_name=csv_path.name, dataset_workspace=None
    )
    return dataset


class TestLoadDatasetAsDataFrame:
    """Group for all tests of the load_dataset_as_data_frame function."""

    @pytest.mark.parametrize("dataset_workspace", ["dummy_workspace", None], ids=["on_azure", "local"])
    def test_can_handle_mltable(
        self,
        dummy_mltable_content: list[dict[str, str | None]],
        dummy_mltable_path: Path,
        dataset_workspace: str | None,
    ) -> None:
        """Test that the `load_dataset_as_data_frame` function.

        Checks whether it reads an MLTable dataset
        and returns it as a pandas DataFrame.
        """
        # GIVEN dataset_name which indicates a dataset stored as an MLTable
        dataset_name: str = "mltable"
        # and GIVEN an expected dataset as Dataframe
        expected = pd.DataFrame.from_records(dummy_mltable_content)

        # WHEN the dataset is read
        dataset = load_dataset_as_data_frame(dummy_mltable_path, dataset_name, dataset_workspace)

        # THEN the returned dataset should match the expected one
        pd.testing.assert_frame_equal(dataset, expected)

    def test_can_handle_csv_files(self, csv_path: Path) -> None:
        """Test that the load_dataset_as_data_frame function can handle CSV files as expected."""

        # GIVEN a loadable csv file

        # WHEN loading trying to load it
        dataset = load_dataset_as_data_frame(
            dataset_base_path=csv_path.parent,
            dataset_name=csv_path.name,
            dataset_workspace=None,
        )

        # THEN we get a non-empty dataframe
        assert dataset is not None
        assert isinstance(dataset, pd.DataFrame)
        assert not dataset.empty

    def test_can_handle_aml_datasets(self) -> None:
        """Test that the load_dataset_as_data_frame function can handle AML datasets as expected."""

        # GIVEN a mocked AmlDatasetReader
        with patch("xtorch_usecases.single_camera.build_pyper.AmlDatasetReader") as aml_loader_mock:
            # WHEN trying to load a dataset
            _ = load_dataset_as_data_frame(
                dataset_workspace="testing",
                dataset_name="best_AML_dataset",
                dataset_base_path=None,
            )

        # THEN the AmlDatasetReader constructor and its read function got called
        aml_loader_mock.assert_called_once()
        aml_loader_mock().read.assert_called_once()

    def test_raises_an_error_if_workspace_and_dataset_basepath_are_both_none(self) -> None:
        """Test that the `load_dataset_as_data_frame` function raises an error with invalid parameters.

        Checks whether it reads an MLTable dataset
        and returns it as a pandas DataFrame.
        """
        # GIVEN dataset_name which indicates a dataset stored as an MLTable
        dataset_name: str = "mltable"

        # WHEN the dataset is read
        # THEN it should raise a ValueError
        with pytest.raises(ValueError, match="Either dataset_workspace or dataset_base_path has to be set."):
            load_dataset_as_data_frame(None, dataset_name, None)


class TestBuildInputIterable:
    """Group for all tests of the build_input_iterable function."""

    def test_can_handle_training_scenario(self, fc1_dataset: pd.DataFrame) -> None:
        """Test that the build_input_iterable can build an iterable fit for training."""

        # GIVEN a preloaded dataset and an expected absolute path parent
        expected_base_path = Path("/foo/bar")

        # WHEN building the iterable
        iterable = build_input_iterable(
            dataset=fc1_dataset,
            active_tasks=[InputImageView.SINGLE_VIEW.value, "traffic_light_monofocal", "traffic_sign"],
            task_id_to_dataset_column={
                "traffic_light_monofocal": "traffic_light",
                InputImageView.SINGLE_VIEW.value: "yuv420",
            },
            input_ids=[InputImageView.SINGLE_VIEW],
            target_data_ratio={"traffic_light_monofocal": 1.0, "traffic_sign": 1.0},
            usage=Usage.TRAINING,
            data_base_path=expected_base_path,
            seed=42,
        )

        # THEN it generated an iterable fit for training
        assert iterable.iteration_policy == IterationPolicy.REPEAT
        for _, path_dict in zip(range(10), iter(iterable)):
            assert InputImageView.SINGLE_VIEW.value in path_dict
            assert path_dict[InputImageView.SINGLE_VIEW.value].startswith(expected_base_path.as_posix())

            traffic_light_available = "traffic_light_monofocal" in path_dict
            traffic_sign_available = "traffic_sign" in path_dict
            assert traffic_light_available or traffic_sign_available

            if traffic_light_available:
                assert path_dict["traffic_light_monofocal"].startswith(expected_base_path.as_posix())
            else:
                assert path_dict["traffic_sign"].startswith(expected_base_path.as_posix())

    def test_can_handle_training_scenario_with_camera_parameters_from_label(self, fc1_dataset: pd.DataFrame) -> None:
        """Test that build_input_iterable builds an iterable and loads camera parameters from an existing label."""

        # GIVEN a preloaded dataset and an expected absolute path parent
        expected_base_path = Path("/foo/bar")

        # GIVEN a task_id_to_dataset_column mapping where the camera parameters are loaded from an existing label
        task_id_to_dataset_column = {
            InputImageView.SINGLE_VIEW.value: "yuv420",
            CAMERA_PARAMETER_TASK_ID: "traffic_light",
            "traffic_light_monofocal": "traffic_light",
        }

        # WHEN building the iterable
        iterable = build_input_iterable(
            dataset=fc1_dataset,
            active_tasks=[
                InputImageView.SINGLE_VIEW.value,
                "traffic_light_monofocal",
                CAMERA_PARAMETER_TASK_ID,
            ],
            task_id_to_dataset_column=task_id_to_dataset_column,
            input_ids=[InputImageView.SINGLE_VIEW],
            target_data_ratio={"traffic_light_monofocal": 1.0},
            usage=Usage.TRAINING,
            data_base_path=expected_base_path,
            seed=42,
        )

        # THEN it generated an iterable fit for training including the camera parameters
        assert iterable.iteration_policy == IterationPolicy.REPEAT
        for _, path_dict in zip(range(10), iter(iterable)):
            assert CAMERA_PARAMETER_TASK_ID in path_dict

            assert InputImageView.SINGLE_VIEW.value in path_dict
            assert path_dict[InputImageView.SINGLE_VIEW.value].startswith(expected_base_path.as_posix())

            assert "traffic_light_monofocal" in path_dict
            assert path_dict["traffic_light_monofocal"].startswith(expected_base_path.as_posix())

    def test_can_handle_non_training_scenario(self, fc1_dataset: pd.DataFrame) -> None:
        """Test that the build_input_iterable can build an iterable fit for other purposes than training."""

        # GIVEN a preloaded dataset and an expected absolute path parent
        expected_base_path = Path("/foo/bar")

        # WHEN building the iterable
        iterable = build_input_iterable(
            dataset=fc1_dataset,
            active_tasks=[InputImageView.SINGLE_VIEW.value, "traffic_light_monofocal", "traffic_sign"],
            task_id_to_dataset_column={
                "traffic_light_monofocal": "traffic_light",
                InputImageView.SINGLE_VIEW.value: "yuv420",
            },
            input_ids=[InputImageView.SINGLE_VIEW],
            target_data_ratio={"traffic_light_monofocal": 1.0, "traffic_sign": 1.0},
            usage=Usage.VALIDATION,
            data_base_path=expected_base_path,
            seed=42,
        )

        # THEN it generated an iterable fit for other purposes than training
        assert iterable.iteration_policy == IterationPolicy.NO_REPEAT
        for path_dict in iter(iterable):
            assert InputImageView.SINGLE_VIEW.value in path_dict
            assert path_dict[InputImageView.SINGLE_VIEW.value].startswith(expected_base_path.as_posix())

            traffic_light_available = "traffic_light_monofocal" in path_dict
            traffic_sign_available = "traffic_sign" in path_dict
            assert traffic_light_available or traffic_sign_available

            if traffic_light_available:
                assert path_dict["traffic_light_monofocal"].startswith(expected_base_path.as_posix())
            else:
                assert path_dict["traffic_sign"].startswith(expected_base_path.as_posix())


@pytest.mark.parametrize(
    "config_builder_and_dataset_fixture",
    [(get_fc1_multi_task_config, "fc1_dataset"), (get_fc1_trifocal_multi_task_config, "trifocal_dataset")],
    ids=["fc1", "trifocal"],
)
@pytest.mark.parametrize("pipeline_step", [PipelineStep.TRAIN, PipelineStep.EVALUATE_TORCH])
@pytest.mark.parametrize("usage", [Usage.TRAINING, Usage.VALIDATION])
def test_build_pipeline_params_from_config(
    config_builder_and_dataset_fixture: tuple[Callable[[], Config], str],
    pipeline_step: PipelineStep,
    usage: Usage,
    request: pytest.FixtureRequest,
) -> None:
    """Test that the build_pipeline_params_from_config works for common argument combinations."""

    # GIVEN a fitting dataset and config for the test
    config_builder, dataset_fixture = config_builder_and_dataset_fixture
    config = config_builder()
    # AL tasks are not used during training and evaluation
    config.task_configs.active_learning_light_sum_hard_entropy = None
    config.task_configs.active_learning_traffic_light_sum_hard_entropy = None
    config.task_configs.active_learning_traffic_sign_sum_hard_entropy = None
    dataset = request.getfixturevalue(dataset_fixture)

    # WHEN calling the function
    compatible_task_ids, compatible_tasks = zip(
        *[
            (task.identifier(), task)
            for task in config.multi_task_collection(pipeline_step).enabled_tasks
            if isinstance(task, PyperLoadable) and isinstance(task, StructuredTask)
        ]
    )
    params = build_pipeline_params_from_config(
        config=config,
        pipeline_step=pipeline_step,
        dataset=dataset,
        usage=usage,
        compatible_task_ids=list(compatible_task_ids),
        compatible_tasks=list(compatible_tasks),
        debug_mode=False,
    )

    # THEN no error got raised and the output is not None
    assert params is not None


@pytest.mark.parametrize(
    "config",
    [get_fc1_multi_task_config(), get_fc1_trifocal_multi_task_config()],
    ids=["fc1", "trifocal"],
)
@pytest.mark.parametrize("pipeline_step", [PipelineStep.TRAIN, PipelineStep.EVALUATE_TORCH])
@pytest.mark.parametrize(
    "iterable_config",
    [{"train": True}, {"train": True, "val": True}, {"val": True}, {"test": True}, {"predict": True}],
    ids=["train", "train+val", "val", "test", "predict"],
)
def test_build_pyper_data_module(
    config: Config,
    pipeline_step: PipelineStep,
    iterable_config: dict[str, bool],
) -> None:
    """Test that the build_pyper_data_module works for common argument combinations."""

    # GIVEN everything to call the function

    # AL tasks are not used during training and evaluation
    config.task_configs.active_learning_light_sum_hard_entropy = None
    config.task_configs.active_learning_traffic_light_sum_hard_entropy = None
    config.task_configs.active_learning_traffic_sign_sum_hard_entropy = None

    # WHEN calling the function
    data_module = build_pyper_data_module(config=config, pipeline_step=pipeline_step, **iterable_config)

    # THEN no error got raised and the internal parm objects of the data module exist as expected
    if iterable_config.get("train", False):
        assert data_module._train_pipeline_params is not None  # noqa: SLF001
    else:
        assert data_module._train_pipeline_params is None  # noqa: SLF001
    if iterable_config.get("val", False):
        assert data_module._val_pipeline_params is not None  # noqa: SLF001
    else:
        assert data_module._val_pipeline_params is None  # noqa: SLF001
    if iterable_config.get("test", False):
        assert data_module._test_pipeline_params is not None  # noqa: SLF001
    else:
        assert data_module._test_pipeline_params is None  # noqa: SLF001
    if iterable_config.get("predict", False):
        assert data_module._predict_pipeline_params is not None  # noqa: SLF001
    else:
        assert data_module._predict_pipeline_params is None  # noqa: SLF001
