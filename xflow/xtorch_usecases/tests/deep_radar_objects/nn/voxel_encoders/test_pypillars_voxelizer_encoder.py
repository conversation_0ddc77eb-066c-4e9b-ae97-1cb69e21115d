"""Tests for the Pypillar Voxelizer Encoder."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Any

import numpy as np
import pytest
import torch

from xtorch.geometry.volumes.base_volume import VolumeType
from xtorch.geometry.volumes.common import VolumeParams
from xtorch.nn.encoders.voxelizers.definitions import PointCloudFeatureIndex, ValueRange
from xtorch_usecases.common.datasets.alliance.definitions import (
    ALLIANCE_RADAR_FEATURE_STATISTICS,
)
from xtorch_usecases.deep_radar_objects.nn.voxel_encoders.pypillars_voxelizer_encoder import (
    DroGridFeatureIndex,
    PypillarsVoxelizerEncoder,
)


def default_init_weights(m: torch.nn.Module) -> None:
    """Initialize the weights of the model."""
    if isinstance(m, (torch.nn.Linear, torch.nn.BatchNorm1d)):
        m.weight.data.fill_(1.0)
        if m.bias is not None:
            m.bias.data.fill_(1.0)


def create_encoder(device: str, voxelizer_cfg: dict[str, Any]) -> PypillarsVoxelizerEncoder:
    """Create an ecoder for a given device and config."""
    encoder = PypillarsVoxelizerEncoder(**voxelizer_cfg).to(device)
    return encoder


class TestPypillarsVoxelizerEncoder:
    """Test cases for the class PypillarsVoxelizerEncoder."""

    @staticmethod
    @pytest.fixture
    def voxelizer_cfg_raw() -> dict[str, Any]:
        """Create config for raw feature output."""
        return {
            "voxel_size": [0.5, 0.5],
            "volume_params": VolumeParams(
                type_name=VolumeType.CARTESIAN.name, lower_bound=[-80, -40, -5], upper_bound=[80, 40, 3]
            ),
            "in_channels": DroGridFeatureIndex.NUMBER_OF_FEATURES,
            "raw_feature_output": True,
            "onnx_compatible": False,
            "normalize_input_values": False,
        }

    @staticmethod
    @pytest.fixture
    def voxelizer_cfg_full(voxelizer_cfg_raw: dict[str, Any]) -> dict[str, Any]:
        """Create config for no raw feature output."""
        d = {
            "raw_feature_output": False,
            "out_channels": 32,
        }
        return {**voxelizer_cfg_raw, **d}

    @staticmethod
    @pytest.fixture
    def voxelizer_cfg_accumulation_window(voxelizer_cfg_raw: dict[str, Any]) -> dict[str, Any]:
        """Create config for no raw feature output."""
        statistics = ALLIANCE_RADAR_FEATURE_STATISTICS.copy()
        statistics["delta_t"] = ValueRange(min=-300, max=0.0)
        accumulation_window = ValueRange(min=-300, max=0.0)
        d = {
            "normalization_statistics": statistics,
            "accumulation_window": accumulation_window,
        }
        return {**voxelizer_cfg_raw, **d}

    @staticmethod
    @pytest.fixture
    def valid_pypillars_voxelizer_encoder_raw(
        device: str, voxelizer_cfg_raw: dict[str, Any]
    ) -> PypillarsVoxelizerEncoder:
        """Create a valid PypillarsVoxelizerEncoder instance for raw feature output."""
        return create_encoder(device, voxelizer_cfg_raw)

    @staticmethod
    @pytest.fixture
    def valid_pypillars_voxelizer_encoder_full(
        device: str, voxelizer_cfg_full: dict[str, Any]
    ) -> PypillarsVoxelizerEncoder:
        """Create a valid PypillarsVoxelizerEncoder instance for no raw feature output."""
        return create_encoder(device, voxelizer_cfg_full)

    @staticmethod
    @pytest.fixture
    def valid_points(device: str) -> list[torch.Tensor]:
        """Create two batches of valid points."""
        points_batch1 = torch.tensor(
            [[70.0, 10.5, 0.0, 30.0, 10.0, -100.0], [70.0, 10.5, -0.1, 0.25, -3.2, 0.0]], device=device
        )
        points_batch2 = torch.tensor(
            [[-35.0, -7.5, 0.3, 0.2, 10.0, -100.0], [19.0, 12.0, -0.1, -0.1, -10.0, 0.0]], device=device
        )

        return [points_batch1, points_batch2]

    @staticmethod
    @pytest.fixture
    def valid_points_with_high_delta_t(device: str) -> list[torch.Tensor]:
        """Create two batches of valid points."""
        points_batch1 = torch.tensor(
            [
                [70.0, 10.5, 0.0, 30.0, 10.0, -301.0],
                [70.0, 10.5, 0.0, 30.0, 10.0, -100.0],
                [70.0, 10.5, -0.1, 0.25, -3.2, 0.0],
            ],
            device=device,
        )
        points_batch2 = torch.tensor(
            [
                [-35.0, -7.5, 0.3, 0.2, 10.0, -450.0],
                [-35.0, -7.5, 0.3, 0.2, 10.0, -100.0],
                [19.0, 12.0, -0.1, -0.1, -10.0, 0.0],
            ],
            device=device,
        )

        return [points_batch1, points_batch2]

    @staticmethod
    @pytest.fixture
    def get_inputs_empty_points(device: str) -> list[torch.Tensor]:
        """Get handcrafted inputs for data preprocessors in which point cloud for second sample is empty."""
        random_generator = torch.Generator(device=device)
        random_generator.manual_seed(6)
        points_data1 = torch.randn(
            (3, PointCloudFeatureIndex.NUMBER_OF_FEATURES - 1), generator=random_generator, device=device
        )
        delta_t = torch.tensor([[0.0], [-0.1], [-0.2]], device=device)
        points_data1 = torch.cat((points_data1, delta_t), dim=1)
        points_data2 = torch.empty((0, PointCloudFeatureIndex.NUMBER_OF_FEATURES), device=device)

        return [points_data1, points_data2, points_data1]

    @staticmethod
    @pytest.fixture
    def get_inputs_empty_points_last_batch_item(device: str) -> list[torch.Tensor]:
        """Get handcrafted inputs for data preprocessors in which point cloud for last sample is empty."""
        random_generator = torch.Generator(device=device)
        random_generator.manual_seed(6)
        points_data1 = torch.randn(
            (3, PointCloudFeatureIndex.NUMBER_OF_FEATURES - 1), generator=random_generator, device=device
        )
        delta_t = torch.tensor([[0.0], [-0.1], [-0.2]], device=device)
        points_data1 = torch.cat((points_data1, delta_t), dim=1)
        points_data2 = torch.empty((0, PointCloudFeatureIndex.NUMBER_OF_FEATURES), device=device)

        return [points_data1, points_data1, points_data2]

    @staticmethod
    @pytest.mark.parametrize("normalize", [True, False])
    def test_forward_raw(
        device: str,
        voxelizer_cfg_raw: dict[str, Any],
        valid_points: list[torch.Tensor],
        normalize: bool,  # noqa: FBT001
    ) -> None:
        """Test the forward function of a PypillarsVoxelizerEncoder with raw feature output."""
        voxelizer_cfg_raw["normalize_input_values"] = normalize
        encoder = create_encoder(device, voxelizer_cfg_raw)
        voxel_features, voxel_coords = encoder.forward(valid_points)

        assert voxel_features is not None
        assert voxel_features.shape == (4, DroGridFeatureIndex.NUMBER_OF_FEATURES)
        assert voxel_coords is not None
        assert voxel_coords.shape == (4, 3)

        expect_numpoints_one = 1.0
        expect_numpoints_two = 2.0

        if normalize:
            expected_voxels = torch.tensor(
                [
                    [
                        1.0,
                        1.0,
                        0.9375,
                        0.6312500000000001,
                        0.625,
                        0.7913791162908269,
                        expect_numpoints_two / 70.0,
                        0.8,
                        0.6692913385826772,
                        0.8,
                    ],
                    [
                        1.0,
                        1.0,
                        0.9375,
                        0.6312500000000001,
                        0.6208333333333333,
                        0.7913791162908269,
                        expect_numpoints_two / 70.0,
                        0.5025000000000001,
                        0.4960629921259842,
                        1.0,
                    ],
                    [
                        1.0,
                        1.0,
                        0.28125,
                        0.40625,
                        0.6375,
                        0.4001957941370699,
                        expect_numpoints_one / 70.0,
                        0.502,
                        0.6692913385826772,
                        0.8,
                    ],
                    [
                        1.0,
                        1.0,
                        0.61875,
                        0.65,
                        0.6208333333333333,
                        0.25124683401985387,
                        expect_numpoints_one / 70.0,
                        0.499,
                        0.4068241469816273,
                        1.0,
                    ],
                ],
                device=device,
            )
        else:
            expected_voxels = torch.tensor(
                [
                    # x_rel y_rel  x      y      z      r        num points          vr    rcs   delta_t
                    [0.250, 0.250, 70.0, 10.50, 0.00, 70.7831, expect_numpoints_two, 30.0, 10.0, -100.0],
                    [0.250, 0.250, 70.0, 10.50, -0.10, 70.7831, expect_numpoints_two, 0.25, -3.2, 0.0],
                    [0.250, 0.250, -35.0, -7.50, 0.30, 35.7946, expect_numpoints_one, 0.20, 10.0, -100.0],
                    [0.250, 0.250, 19.0, 12.00, -0.10, 22.4722, expect_numpoints_one, -0.10, -10.0, 0.0],
                ],
                device=device,
            )

        torch.testing.assert_close(voxel_features, expected_voxels)

    @staticmethod
    def test_assertion_if_accumulation_window_does_not_match_to_statistics_min(
        device: str,
        voxelizer_cfg_accumulation_window: dict[str, Any],
        valid_points_with_high_delta_t: list[torch.Tensor],
    ) -> None:
        """Test if an assertion is raised when the accumulation window does not match the statistics."""
        # GIVEN: Accumulation window data which differs from statistics for normalization
        voxelizer_cfg_accumulation_window["accumulation_window"] = ValueRange(min=-100, max=0.0)

        # WHEN: constructing the voxelizer
        # THEN: assertion is raised
        with pytest.raises(AssertionError):
            _ = create_encoder(device, voxelizer_cfg_accumulation_window)

    @staticmethod
    def test_assertion_if_accumulation_window_does_not_match_to_statistics_max(
        device: str,
        voxelizer_cfg_accumulation_window: dict[str, Any],
        valid_points_with_high_delta_t: list[torch.Tensor],
    ) -> None:
        """Test if an assertion is raised when the accumulation window does not match the statistics."""
        # GIVEN: Accumulation window data which differs from statistics for normalization
        voxelizer_cfg_accumulation_window["accumulation_window"] = ValueRange(min=-300, max=10)

        # WHEN: constructing the voxelizer
        # THEN: assertion is raised
        with pytest.raises(AssertionError):
            _ = create_encoder(device, voxelizer_cfg_accumulation_window)

    @staticmethod
    @pytest.mark.parametrize("normalize", [True, False])
    def test_forward_raw_with_high_delta_t(
        device: str,
        voxelizer_cfg_accumulation_window: dict[str, Any],
        valid_points_with_high_delta_t: list[torch.Tensor],
        normalize: bool,  # noqa: FBT001
    ) -> None:
        """Test the forward function of a PypillarsVoxelizerEncoder."""
        # GIVEN: Accumulation window and locations which are outside the accumulation window
        voxelizer_cfg_accumulation_window["normalize_input_values"] = normalize
        encoder_accumulation = create_encoder(device, voxelizer_cfg_accumulation_window)
        # WHEN: Calling the forward function these locations are filtered out
        voxel_features, voxel_coords = encoder_accumulation.forward(valid_points_with_high_delta_t)

        # THEN: And these locations shouldn't be part of the output voxels
        assert voxel_features is not None
        assert voxel_features.shape == (4, DroGridFeatureIndex.NUMBER_OF_FEATURES)
        assert voxel_coords is not None
        assert voxel_coords.shape == (4, 3)

        expect_numpoints_one = 1.0
        expect_numpoints_two = 2.0

        if normalize:
            expected_voxels = torch.tensor(
                [
                    [
                        1.0,
                        1.0,
                        0.9375,
                        0.6312500000000001,
                        0.625,
                        0.7913791162908269,
                        expect_numpoints_two / 70.0,
                        0.8,
                        0.6692913385826772,
                        0.6666666666666667,
                    ],
                    [
                        1.0,
                        1.0,
                        0.9375,
                        0.6312500000000001,
                        0.6208333333333333,
                        0.7913791162908269,
                        expect_numpoints_two / 70.0,
                        0.5025000000000001,
                        0.4960629921259842,
                        1.0,
                    ],
                    [
                        1.0,
                        1.0,
                        0.28125,
                        0.40625,
                        0.6375,
                        0.4001957941370699,
                        expect_numpoints_one / 70.0,
                        0.502,
                        0.6692913385826772,
                        0.6666666666666667,
                    ],
                    [
                        1.0,
                        1.0,
                        0.61875,
                        0.65,
                        0.6208333333333333,
                        0.25124683401985387,
                        expect_numpoints_one / 70.0,
                        0.499,
                        0.4068241469816273,
                        1.0,
                    ],
                ],
                device=device,
            )
        else:
            expected_voxels = torch.tensor(
                [
                    # x_rel y_rel  x      y      z      r        num points          vr    rcs   delta_t
                    [0.250, 0.250, 70.0, 10.50, 0.00, 70.7831, expect_numpoints_two, 30.0, 10.0, -100.0],
                    [0.250, 0.250, 70.0, 10.50, -0.10, 70.7831, expect_numpoints_two, 0.25, -3.2, 0.0],
                    [0.250, 0.250, -35.0, -7.50, 0.30, 35.7946, expect_numpoints_one, 0.20, 10.0, -100.0],
                    [0.250, 0.250, 19.0, 12.00, -0.10, 22.4722, expect_numpoints_one, -0.10, -10.0, 0.0],
                ],
                device=device,
            )
        # THEN: And the values should be as the expected voxels
        torch.testing.assert_close(voxel_features, expected_voxels)

    @staticmethod
    def test_forward_raw_no_list_as_input(
        device: str,
        valid_pypillars_voxelizer_encoder_raw: PypillarsVoxelizerEncoder,
        valid_points: list[torch.Tensor],
    ) -> None:
        """Test the forward function of a PypillarsVoxelizerEncoder with a single tensor as input instead of a list."""
        in_tensor = valid_points[0]
        voxel_features, voxel_coords = valid_pypillars_voxelizer_encoder_raw.forward(in_tensor)

        assert voxel_features is not None
        assert voxel_features.shape == (2, DroGridFeatureIndex.NUMBER_OF_FEATURES)
        assert voxel_coords is not None
        assert voxel_coords.shape == (2, 3)

        expect_numpoints_two = 2.0
        expected_voxels = torch.tensor(
            [
                [0.2500, 0.2500, 70.0000, 10.5000, 0.0000, 70.7831, expect_numpoints_two, 30.0000, 10.0000, -100.0],
                [0.2500, 0.2500, 70.0000, 10.5000, -0.1000, 70.7831, expect_numpoints_two, 0.2500, -3.2000, 0.0],
            ],
            device=device,
        )

        torch.testing.assert_close(voxel_features, expected_voxels)

    @staticmethod
    @pytest.mark.parametrize("raw_encoder", [True, False])
    @pytest.mark.parametrize("normalize", [True, False])
    def test_forward_invalid_input_type(
        device: str,
        voxelizer_cfg_raw: dict[str, Any],
        voxelizer_cfg_full: dict[str, Any],
        raw_encoder: bool,  # noqa: FBT001
        normalize: bool,  # noqa: FBT001
    ) -> None:
        """Test the forward function of a PypillarsVoxelizerEncoder with an invalid data type as input."""
        if not raw_encoder and normalize:
            return

        input_points = {}  # invalid input type
        config = voxelizer_cfg_raw if raw_encoder else voxelizer_cfg_full
        config["normalize_input_values"] = normalize
        encoder = create_encoder(device, config)
        with pytest.raises(TypeError):
            encoder.forward(input_points)  # pyright: ignore[reportArgumentType]

    @staticmethod
    @pytest.mark.parametrize("raw_encoder", [True, False])
    @pytest.mark.parametrize("normalize", [True, False])
    def test_single_batch_path_equals_batched_path(
        device: str,
        voxelizer_cfg_raw: dict[str, Any],
        voxelizer_cfg_full: dict[str, Any],
        valid_points: list[torch.Tensor],
        raw_encoder: bool,  # noqa: FBT001
        normalize: bool,  # noqa: FBT001
    ) -> None:
        """Test special forward function for non batched input.

        Tests if special forward path for non batched input (single batch item) produces same result as batched
        path.
        """
        if not raw_encoder and normalize:
            return

        config = voxelizer_cfg_raw if raw_encoder else voxelizer_cfg_full
        config["normalize_input_values"] = normalize
        encoder = create_encoder(device, config)

        voxel_features, voxel_coords = encoder.forward(valid_points[0])
        voxel_features_batched, voxel_coords_batched = encoder.forward([valid_points[0]])

        assert voxel_features is not None
        assert voxel_coords is not None
        assert voxel_coords.shape == (2, 3)
        assert voxel_features.shape == (2, DroGridFeatureIndex.NUMBER_OF_FEATURES) if raw_encoder else (2, 32)

        torch.testing.assert_close(voxel_features, voxel_features_batched)
        torch.testing.assert_close(voxel_coords, voxel_coords_batched)

    @staticmethod
    @pytest.mark.parametrize("num_points_per_voxel", [10, 2])
    @pytest.mark.parametrize("normalize", [True, False])
    def test_onnx_compatible_version_equals_normal_version(
        device: str,
        voxelizer_cfg_raw: dict[str, Any],
        num_points_per_voxel: int,
        normalize: bool,  # noqa: FBT001
    ) -> None:
        """Test if onnx compatible version outputs same result as normal version."""
        points_batch1 = torch.tensor(
            [
                [70.0, 10.5, 0.0, 30.0, 10.0, 0.0],
                [70.0, 10.5, -0.1, 0.25, -3.2, 0.0],
                [70.0, 10.5, -0.15, 0.35, -3.2, 0.0],
                [70.0, 10.5, 0.5, 0.55, -3.2, 0.0],
                [70.0, 10.5, 0.18, 0.25, -3.2, 0.0],
            ],
            device=device,
        )
        points_batch2 = torch.tensor(
            [
                [-35.0, -7.5, 0.3, 0.2, 10.0, 0.0],
                [19.0, 12.0, -0.1, -0.1, -10.0, 0.0],
                [19.0, 12.0, -1.1, -1.1, 10.0, 0.0],
                [-19.0, 10.0, -0.1, -0.1, -10.0, 0.0],
            ],
            device=device,
        )
        valid_points = [points_batch1, points_batch2]

        voxelizer_cfg_raw["num_points_per_voxel"] = num_points_per_voxel
        voxelizer_cfg_raw["normalize_input_values"] = normalize

        encoder_norm = PypillarsVoxelizerEncoder(**voxelizer_cfg_raw).to(device)
        voxelizer_cfg_raw["onnx_compatible"] = True
        encoder_onnx = PypillarsVoxelizerEncoder(**voxelizer_cfg_raw).to(device)
        encoder_norm.load_state_dict(encoder_onnx.state_dict())  # ensure that both layers have same trainable weights
        assert encoder_onnx.onnx_compatible
        assert not encoder_norm.onnx_compatible

        voxels_feature_onnx, voxel_coords_onnx = encoder_onnx.forward(valid_points)
        voxels_features_norm, voxel_coords_norm = encoder_norm.forward(valid_points)

        torch.testing.assert_close(voxel_coords_norm[0, 1:], voxel_coords_onnx[0, 0, :])
        torch.testing.assert_close(voxel_coords_norm[1, 1:], voxel_coords_onnx[0, 0, :])
        torch.testing.assert_close(voxel_coords_norm[2, 1:], voxel_coords_onnx[0, 0, :])
        torch.testing.assert_close(voxel_coords_norm[3, 1:], voxel_coords_onnx[0, 0, :])
        torch.testing.assert_close(voxel_coords_norm[4, 1:], voxel_coords_onnx[0, 0, :])
        torch.testing.assert_close(voxel_coords_norm[5, 1:], voxel_coords_onnx[1, 0, :])
        torch.testing.assert_close(voxel_coords_norm[6, 1:], voxel_coords_onnx[1, 2, :])
        torch.testing.assert_close(voxel_coords_norm[7, 1:], voxel_coords_onnx[1, 2, :])
        torch.testing.assert_close(voxel_coords_norm[8, 1:], voxel_coords_onnx[1, 1, :])

        torch.testing.assert_close(voxels_features_norm[0], voxels_feature_onnx[0, 0, 0, :])
        torch.testing.assert_close(voxels_features_norm[1], voxels_feature_onnx[0, 0, 1, :])
        if num_points_per_voxel > 2:
            torch.testing.assert_close(voxels_features_norm[2], voxels_feature_onnx[0, 0, 2, :])
            torch.testing.assert_close(voxels_features_norm[3], voxels_feature_onnx[0, 0, 3, :])
            torch.testing.assert_close(voxels_features_norm[4], voxels_feature_onnx[0, 0, 4, :])
        torch.testing.assert_close(voxels_features_norm[5], voxels_feature_onnx[1, 0, 0, :])
        torch.testing.assert_close(voxels_features_norm[6], voxels_feature_onnx[1, 2, 0, :])
        torch.testing.assert_close(voxels_features_norm[7], voxels_feature_onnx[1, 2, 1, :])
        torch.testing.assert_close(voxels_features_norm[8], voxels_feature_onnx[1, 1, 0, :])

    @staticmethod
    @pytest.mark.parametrize("raw_encoder", [True, False])
    @pytest.mark.parametrize("normalize", [True, False])
    def test_empty_points_forward(
        device: str,
        voxelizer_cfg_raw: dict[str, Any],
        voxelizer_cfg_full: dict[str, Any],
        get_inputs_empty_points: list[torch.Tensor],
        raw_encoder: bool,  # noqa: FBT001,
        normalize: bool,  # noqa: FBT001
    ) -> None:
        """Test forward pass for empty points."""
        if not raw_encoder and normalize:
            return

        config = voxelizer_cfg_raw if raw_encoder else voxelizer_cfg_full
        config["normalize_input_values"] = normalize
        encoder = create_encoder(device, config)
        voxel_features, voxel_coords = encoder.forward(get_inputs_empty_points)

        assert voxel_features is not None
        assert voxel_coords is not None
        assert voxel_coords.shape == (6, 3)
        assert voxel_coords[-1, 0] == 2

        assert voxel_features.shape == (6, DroGridFeatureIndex.NUMBER_OF_FEATURES) if raw_encoder else (6, 32)

    @staticmethod
    @pytest.mark.parametrize("raw_encoder", [True, False])
    @pytest.mark.parametrize("normalize", [True, False])
    def test_empty_points_in_last_batch_item_forward(
        device: str,
        voxelizer_cfg_raw: dict[str, Any],
        voxelizer_cfg_full: dict[str, Any],
        get_inputs_empty_points_last_batch_item: list[torch.Tensor],
        raw_encoder: bool,  # noqa: FBT001
        normalize: bool,  # noqa: FBT001
    ) -> None:
        """Test forward pass for empty points in the last batch item."""
        if not raw_encoder and normalize:
            return

        config = voxelizer_cfg_raw if raw_encoder else voxelizer_cfg_full
        config["normalize_input_values"] = normalize
        encoder = create_encoder(device, config)
        voxel_features, voxel_coords = encoder.forward(get_inputs_empty_points_last_batch_item)

        assert voxel_features is not None
        assert voxel_coords is not None
        assert voxel_coords.shape == (7, 3)
        assert voxel_coords[-1, 0] == 2

        assert voxel_features.shape == (7, DroGridFeatureIndex.NUMBER_OF_FEATURES) if raw_encoder else (7, 32)

    @staticmethod
    @pytest.mark.lfs_dependencies(
        [
            "xtorch_usecases/tests/deep_radar_objects/nn/voxel_encoders/data/pypillars_voxelizer_encoder_raw_test_data.npz",
            "xtorch_usecases/tests/deep_radar_objects/nn/voxel_encoders/data/pypillars_voxelizer_encoder_full_test_data.npz",
        ]
    )
    @pytest.mark.parametrize("raw_encoder", [True, False])
    def test_forward_yields_same_result_as_asset_repo_raw(
        valid_pypillars_voxelizer_encoder_raw: PypillarsVoxelizerEncoder,
        valid_pypillars_voxelizer_encoder_full: PypillarsVoxelizerEncoder,
        valid_points: list[torch.Tensor],
        raw_encoder: bool,  # noqa: FBT001
    ) -> None:
        """Test that the result of a forward pass is the same as in the original implementation."""
        encoder = valid_pypillars_voxelizer_encoder_raw if raw_encoder else valid_pypillars_voxelizer_encoder_full
        encoder.apply(default_init_weights)
        voxel_features, voxel_coords = encoder.forward(valid_points)

        assert voxel_features is not None
        assert voxel_coords is not None

        filename = (
            "pypillars_voxelizer_encoder_raw_test_data.npz"
            if raw_encoder
            else "pypillars_voxelizer_encoder_full_test_data.npz"
        )

        # np.savez(
        #     filename,
        #     voxel_features.cpu().detach().numpy(),
        #     voxel_coords.cpu().detach().numpy(),
        # )

        other = np.load("xtorch_usecases/tests/deep_radar_objects/nn/voxel_encoders/data/" + filename)

        assert voxel_features.cpu().size() == torch.tensor(other["voxels"]).size()
        assert voxel_coords.cpu().size() == torch.tensor(other["coors"]).size()
        torch.testing.assert_close(voxel_features.cpu(), torch.tensor(other["voxels"]))
        torch.testing.assert_close(voxel_coords.cpu(), torch.tensor(other["coors"]))

    @staticmethod
    def test_forward_raw_with_nans(
        device: str,
        valid_pypillars_voxelizer_encoder_raw: PypillarsVoxelizerEncoder,
    ) -> None:
        """Test the forward function of a PypillarsVoxelizerEncoder with raw feature output and NaN values."""
        points_with_nans = [
            torch.tensor([[70.0, 10.5, 0.0, 30.0, 10.0, 0.0], [70.0, 10.5, np.nan, 0.25, -3.2, 0.0]], device=device),
            torch.tensor([[-35.0, -7.5, 0.3, 0.2, 10.0, 0.0], [np.nan, 12.0, -0.1, -0.1, -10.0, 0.0]], device=device),
        ]
        voxel_features, voxel_coords = valid_pypillars_voxelizer_encoder_raw.forward(points_with_nans)

        assert voxel_features is not None
        assert voxel_features.shape == (2, DroGridFeatureIndex.NUMBER_OF_FEATURES)
        assert voxel_coords is not None
        assert voxel_coords.shape == (2, 3)

        expect_numpoints_one = 1.0
        expected_voxels = torch.tensor(
            [
                [0.2500, 0.2500, 70.0000, 10.5000, 0.0000, 70.7831, expect_numpoints_one, 30.0000, 10.0000, 0.0],
                [0.2500, 0.2500, -35.0000, -7.5000, 0.3000, 35.7946, expect_numpoints_one, 0.2000, 10.0000, 0.0],
            ],
            device=device,
        )

        torch.testing.assert_close(voxel_features, expected_voxels)


def test_normalization_masking(device: str) -> None:
    """Testing whether normalization is applied to filled features and not to invalid features."""
    # GIVEN one encoder
    encoder = PypillarsVoxelizerEncoder(
        voxel_size=(0.5, 0.5, 0.5),
        volume_params=VolumeParams(
            type_name=VolumeType.CARTESIAN.name, lower_bound=[-80, -40, -5], upper_bound=[80, 40, 3]
        ),
        in_channels=5,
        out_channels=3,
        num_voxels=3,
        num_points_per_voxel=2,
        raw_feature_output=True,
        onnx_compatible=True,
        normalize_input_values=True,
    ).to(device)

    # GIVEN RCS filled with three of them the features with boundary values
    voxel_features = torch.zeros(1, 3, 2, DroGridFeatureIndex.NUMBER_OF_FEATURES, device=device)
    voxel_features[0, 0, 0, DroGridFeatureIndex.RCS] = ALLIANCE_RADAR_FEATURE_STATISTICS["rcs"].max
    voxel_features[0, 0, 1, DroGridFeatureIndex.RCS] = ALLIANCE_RADAR_FEATURE_STATISTICS["rcs"].min
    voxel_features[0, 1, 0, DroGridFeatureIndex.RCS] = ALLIANCE_RADAR_FEATURE_STATISTICS["rcs"].max

    # WHEN _normalize function is called
    result = encoder._normalize(voxel_features)  # noqa: SLF001

    # THEN the filled features are normalized
    # the RCS is correctly normalized, min -> 0.0 and max -> 1.0
    assert pytest.approx(1.0) == result[0, 0, 0, DroGridFeatureIndex.RCS].cpu().item()
    assert pytest.approx(0.0) == result[0, 0, 1, DroGridFeatureIndex.RCS].cpu().item()
    assert pytest.approx(1.0) == result[0, 1, 0, DroGridFeatureIndex.RCS].cpu().item()
    # the VEL is 0-initialized, and normalized to 0.5 (symmetric)
    assert pytest.approx(0.5) == result[0, 0, 0, DroGridFeatureIndex.RADIAL_VELOCITY_OVER_GROUND].cpu().item()

    # THEN the invalid features are not normalized (otherwise the values should be around 0.5)
    # the features are 0-initialized and normalitation skipped, so the values stay 0.0
    assert pytest.approx(0.0) == result[0, 1, 1, DroGridFeatureIndex.RCS].cpu().item()
    assert pytest.approx(0.0) == result[0, 2, 0, DroGridFeatureIndex.RCS].cpu().item()
    assert pytest.approx(0.0) == result[0, 2, 1, DroGridFeatureIndex.RCS].cpu().item()
    # the VEL is 0-initialized, normalization skipped so it stays 0.0
    assert pytest.approx(0.0) == result[0, 2, 0, DroGridFeatureIndex.RADIAL_VELOCITY_OVER_GROUND].cpu().item()
