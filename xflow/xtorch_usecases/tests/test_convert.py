"""Module for testing the convert functionality in xtorch_usecases."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import pytest
from omegaconf import DictConfig, ListConfig, OmegaConf

from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.convert import differences_to_training_config_are_expected


class DummyTask:
    """Dummy implementation of ExpectedConfigChangeProvider for testing."""

    def __init__(self, identifier: str, expected_diffs: list[str] | None = None) -> None:
        """Initialize the dummy task with an identifier and expected differences.

        Args:
            identifier: Unique identifier for the task.
            expected_diffs: List of expected differences as regex patterns.
        """
        self._identifier = identifier
        self._expected_diffs = expected_diffs or []

    def identifier(self) -> str:
        """Return the unique identifier of the task."""
        return self._identifier

    def get_expected_config_changes(
        self,
        producer_step: PipelineStep,
        producer_config: DictConfig | ListConfig,
        consumer_step: PipelineStep,
        consumer_config: DictConfig | ListConfig,
    ) -> list[str]:
        """Return the expected configuration changes for this task.

        Args:
            producer_step: The step producing the configuration.
            producer_config: The configuration of the producer step.
            consumer_step: The step consuming the configuration.
            consumer_config: The configuration of the consumer step.

        Returns:
            List of expected differences as regex patterns.
        """
        return self._expected_diffs


def make_config(**kwargs) -> DictConfig:  # noqa: ANN003
    """Helper to create OmegaConf DictConfig."""
    if "checkpoints_are_finetuned" not in (convert_config := kwargs.setdefault("convert_config", {})):
        # Ensure the finetuning flag is set correctly
        convert_config["checkpoints_are_finetuned"] = False
    return OmegaConf.create(kwargs)


def test_no_differences() -> None:
    """Test that no differences are detected."""
    # GIVEN two identical configurations
    config1 = make_config(a=1, b=2)
    config2 = make_config(a=1, b=2)

    # WHEN checking for differences
    result = differences_to_training_config_are_expected(config1, config2, [])

    # THEN no differences should be expected
    assert result is True


def test_expected_difference_from_task() -> None:
    """Test that expected differences from a task are handled correctly."""
    # GIVEN two configurations with expected differences defined in a task
    config1 = make_config(foo=1)
    config2 = make_config(foo=2)
    task = DummyTask("dummy", expected_diffs=[r"foo"])

    # WHEN checking for differences with the task
    result = differences_to_training_config_are_expected(config1, config2, [task])

    # THEN the differences should be expected
    assert result is True


def test_unexpected_difference() -> None:
    """Test that unexpected differences are detected."""
    # GIVEN two configurations with unexpected differences
    config1 = make_config(bar=1)
    config2 = make_config(bar=2)

    # WHEN checking for differences without any expected tasks
    result = differences_to_training_config_are_expected(config1, config2, [])

    # THEN the differences should not be expected
    assert result is False


def test_multiple_differences_some_expected() -> None:
    """Test that multiple differences are handled correctly."""
    # GIVEN two configurations with some expected differences
    config1 = make_config(a=1, b=2)
    config2 = make_config(a=2, b=3)
    task = DummyTask("dummy", expected_diffs=[r"a"])

    # WHEN checking for differences with the task
    result = differences_to_training_config_are_expected(config1, config2, [task])

    # THEN the differences should be unexpected
    assert result is False


def test_expected_diff_regex_error() -> None:
    """Test that an error is raised for invalid regex in expected differences."""
    config1 = make_config(foo=1)
    config2 = make_config(foo=2)

    # Provide an invalid regex to trigger error handling
    class BadTask(DummyTask):
        def get_expected_config_changes(self, *a, **k) -> list[str]:  # noqa: ANN002, ANN003
            return ["["]  # Invalid regex

    bad_task = BadTask("bad")

    # WHEN checking for differences with the bad task
    # THEN it should raise a RuntimeError due to invalid regex
    with pytest.raises(RuntimeError):
        differences_to_training_config_are_expected(config1, config2, [bad_task])


@pytest.mark.parametrize("is_finetuning", [True, False])
def test_checkpoints_are_finetuned_branch(is_finetuning: bool) -> None:  # noqa: FBT001
    """Test that the 'checkpoints_are_finetuned' branch is handled correctly."""
    # GIVEN two configurations with 'checkpoints_are_finetuned' set to True
    config1 = make_config(convert_config={"checkpoints_are_finetuned": is_finetuning}, a=1)
    config2 = make_config(convert_config={"checkpoints_are_finetuned": is_finetuning}, a=2)

    class TaskWithExpectedChangeInFinetuning(DummyTask):
        def get_expected_config_changes(
            self,
            producer_step: PipelineStep,
            producer_config: DictConfig | ListConfig,
            consumer_step: PipelineStep,
            consumer_config: DictConfig | ListConfig,
        ) -> list[str]:
            if producer_step == PipelineStep.TRAIN_FINETUNE:
                return ["a"]
            return []

    task = TaskWithExpectedChangeInFinetuning("finetune_task")

    result = differences_to_training_config_are_expected(config1, config2, [task])
    assert result is is_finetuning
