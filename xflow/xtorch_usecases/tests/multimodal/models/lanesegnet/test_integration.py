"""Smoke and integration test for training, and convert stages of lanesegnet usecase."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
from pathlib import Path, PosixPath
from typing import Any

import pytest
import torch.distributed as dist
from omegaconf import OmegaConf

from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal import check_conversion, convert, evaluate, predict, qnn_evaluate, qnn_predict, train
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.models.lanesegnet.config import get_run_lanesegnet_config


@pytest.mark.slow
@pytest.mark.lfs_dependencies(
    [
        "xtorch_usecases/tests/test_data/multimodal/lane/dataset_tiny_new",
        "xtorch_usecases/tests/test_data/multimodal/lane/maps",
        "xtorch_usecases/src/xtorch_usecases/multimodal/tasks/lanesegnet/visualization/sub_visualization/resources",
        "xtorch_usecases/tests/test_data/multimodal/initial_encoder_weights",
    ]
)
@pytest.mark.enable_socket
def test_integration_lanesegnet(tmpdir_delete_after: Path) -> None:  # noqa: PLR0915
    """Test if stages work together.

    In order to skip this test run with `python -m pytest -m "not slow" <dir>`.
    """
    train_output_folder = tmpdir_delete_after / "train"
    torch_eval_output_folder = tmpdir_delete_after / "evaluate_torch"
    torch_predict_output_folder = tmpdir_delete_after / "predict_torch"
    convert_output_folder = tmpdir_delete_after / "convert"
    check_conversion_output_folder = tmpdir_delete_after / "check_conversion"
    qnn_eval_output_folder = tmpdir_delete_after / "evaluate_qnn"
    qnn_predict_output_folder = tmpdir_delete_after / "predict_qnn"

    model = Model.LANESEGNET
    common_reference_args = ["--environment", "local", "--short_ct_run", "true"]
    common_args = ["--model", model] + common_reference_args

    print("""
    #############################################
    # ----------------- TRAIN ----------------- #
    #############################################
    """)
    # GIVEN a reference config for train step for comparison later
    train_io_config = _get_io_config(train_output_folder, train_output_folder)
    train_reference_config, unknown_args = get_run_lanesegnet_config(
        argv=common_reference_args + train_io_config,
        pipeline_step=PipelineStep.TRAIN,
    )
    assert unknown_args == [], "Unknown args found in train reference config."

    # WHEN running the train step
    train.main(common_args + train_io_config + ["eval_config.profiling_level=NO_PROFILING"])

    # THEN the train step runs successfully and writes output to the output folder
    assert next(Path(train_output_folder).iterdir(), None) is not None, "Directory is empty."
    assert next(train_reference_config.pipeline_step_paths.output_artifacts.iterdir(), None) is not None, (
        "Directory is empty."
    )
    assert next(train_reference_config.pipeline_step_paths.checkpoints.iterdir(), None) is not None, (
        "Checkpoints directory is empty."
    )
    assert next(train_reference_config.pipeline_step_paths.summaries.iterdir(), None) is not None, (
        "Summaries directory is empty."
    )

    # THEN clean up and reset the distributed setting
    if dist.is_available and dist.is_initialized:
        dist.destroy_process_group()

    print("""
    #############################################
    # --------------- TORCH EVAL -------------- #
    #############################################
    """)
    # GIVEN a reference config for torch eval step for comparison later
    torch_eval_io_config = _get_io_config(train_output_folder, torch_eval_output_folder)
    torch_eval_reference_config, unknown_args = get_run_lanesegnet_config(
        argv=common_reference_args + torch_eval_io_config,
        pipeline_step=PipelineStep.EVALUATE_TORCH,
    )
    assert unknown_args == [], "Unknown args found in torch eval reference config."

    # WHEN running the torch eval step
    evaluate.main(common_args + torch_eval_io_config + ["trainer.limit_test_batches=0"])

    # THEN the torch eval step runs successfully and writes output to the output folder
    assert next(torch_eval_output_folder.iterdir(), None) is not None, "Torch evaluation directory is empty."
    assert {"summaries", "visualizations", "evaluation"} <= {p.name for p in torch_eval_output_folder.iterdir()}
    output_imgs = set((torch_eval_output_folder / "visualizations/evaluate_torch_offline/log_imgs").rglob("*.png"))
    assert any("predict" in str(p) for p in output_imgs), "No prediction images found in torch eval visualizations."
    assert all("annotation" not in str(p) for p in output_imgs), "Annotations should not be logged in torch eval."
    assert next(torch_eval_reference_config.pipeline_step_paths.summaries.iterdir(), None) is not None, (
        "No torch evaluation results in summary directory."
    )

    print("""
    #############################################
    # ------------- TORCH PREDICT ------------- #
    #############################################
    """)
    # GIVEN a reference config for torch predict step for comparison later
    qnn_predict_io_config = _get_io_config(train_output_folder, torch_predict_output_folder)
    qnn_predict_reference_config, unknown_args = get_run_lanesegnet_config(
        argv=common_reference_args + qnn_predict_io_config,
        pipeline_step=PipelineStep.VISUALIZATION_TORCH,
    )
    assert unknown_args == [], "Unknown args found in torch predict reference config."

    # WHEN running the torch predict step
    predict.main(common_args + qnn_predict_io_config)

    # THEN the predict step runs successfully and writes output to the output folder
    assert next(Path(torch_predict_output_folder).iterdir(), None) is not None, "Predictions directory is empty."
    output_videos = {
        p.stem for p in (Path(torch_predict_output_folder) / "visualizations" / "predict_torch").glob("*.mp4")
    }
    assert "lanesegnet_annotations" not in output_videos, "Annotations video should not be generated while torch pred."
    assert output_videos == {"lanesegnet_predictions"}, "Torch predictions video should be generated."

    print("""
    #############################################
    # -------------- CONVERSION --------------- #
    #############################################
    """)
    # GIVEN a reference config for convert step for comparison later
    convert_io_config = _get_io_config(train_output_folder, convert_output_folder)
    convert_reference_config, unknown_args = get_run_lanesegnet_config(
        argv=common_reference_args + convert_io_config,
        pipeline_step=PipelineStep.CONVERT,
    )
    assert unknown_args == [], "Unknown args found in convert reference config."

    # WHEN running the convert step
    convert.main(
        common_args
        + convert_io_config
        + ["convert_config.skip_quantization_check=True", "convert_config.skip_inference=True"]
    )

    # THEN the convert step runs successfully and writes output to the output folder
    assert convert_reference_config.convert_config.raw_onnx_model_path.exists()
    conversion_lanesegnet_folder = convert_reference_config.convert_config.default_root_dir / "conversion_lanesegnet"

    metadata_json_filepath = conversion_lanesegnet_folder / "htp/lanesegnet_qnn_htp_metadata.json"
    convert_composed_config_filepath = convert_reference_config.pipeline_step_paths.summaries / "composed_config.yaml"
    assert (conversion_lanesegnet_folder / "cpu/lanesegnet_qnn_cpu_metadata.json").is_file()
    assert (conversion_lanesegnet_folder / "cpu/x86_64-linux-clang/liblanesegnet_qnn_cpu.so").is_file()
    assert (conversion_lanesegnet_folder / "htp/lanesegnet_qnn_htp.bin").is_file()
    assert metadata_json_filepath.is_file()
    assert convert_composed_config_filepath.is_file()

    config_saved = OmegaConf.to_container(OmegaConf.load(convert_composed_config_filepath))
    config_to_deploy = json.loads(metadata_json_filepath.read_text())["metadata"]["attributes"]["config"]

    def instantiate_paths(config: dict[str, Any]) -> None:
        """Replace paths in the config to match the expected format."""
        for key, value in config.items():
            if isinstance(value, str) and value.startswith("PosixPath('") and value.endswith("')"):
                config[key] = PosixPath(value[11:-2])
            elif isinstance(value, dict):
                instantiate_paths(value)

    instantiate_paths(config_to_deploy)

    assert config_saved == config_to_deploy

    print("""
    #############################################
    # ----------- CHECK CONVERSION ------------ #
    #############################################
    """)
    # GIVEN a reference config for check conversion step for comparison later
    check_conversion_io_config = _get_io_config(convert_output_folder, check_conversion_output_folder)
    check_conversion_reference_config, unknown_args = get_run_lanesegnet_config(
        argv=common_reference_args + check_conversion_io_config,
        pipeline_step=PipelineStep.CHECK_CONVERSION_QNN,
    )
    assert unknown_args == [], "Unknown args found in QNN eval reference config."

    check_conversion_extra_args = [
        "convert_config.skip_quantization_check=False",
        "convert_config.quant_check_config.enable_activation_histograms=False",
        "convert_config.quant_check_config.enable_quant_error_plots=False",
        "convert_config.quant_check_config.enable_weight_plots=False",
        "convert_config.quant_check_config.short_run=True",
    ]

    # WHEN running the check conversion step
    check_conversion.main(common_args + check_conversion_io_config + check_conversion_extra_args)

    # THEN the check conversion step runs successfully and writes output to the output folder
    assert next(check_conversion_output_folder.iterdir(), None) is not None, "QNN evaluation directory is empty."

    print("""
    #############################################
    # --------------- QNN EVAL ---------------- #
    #############################################
    """)
    # GIVEN a reference config for QNN eval step for comparison later
    qnn_eval_io_config = _get_io_config(convert_output_folder, qnn_eval_output_folder)
    qnn_eval_reference_config, unknown_args = get_run_lanesegnet_config(
        argv=common_reference_args + qnn_eval_io_config,
        pipeline_step=PipelineStep.EVALUATE_QNN,
    )
    assert unknown_args == [], "Unknown args found in QNN eval reference config."

    # WHEN running the QNN eval step
    qnn_evaluate.main(common_args + qnn_eval_io_config + ["--model_type", "qnn_htp"])

    # THEN the QNN eval step runs successfully and writes output to the output folder
    assert next(qnn_eval_output_folder.iterdir(), None) is not None, "QNN evaluation directory is empty."
    assert {"summaries", "visualizations", "evaluation"} <= {p.name for p in qnn_eval_output_folder.iterdir()}
    output_imgs = set((qnn_eval_output_folder / "visualizations/evaluate_qnn_offline/log_imgs").rglob("*.png"))
    assert any("predict" in str(p) for p in output_imgs), "No prediction images found in qnn eval visualizations."
    assert all("annotation" not in str(p) for p in output_imgs), "Annotations should not be logged in qnn eval."
    assert next(qnn_eval_reference_config.pipeline_step_paths.summaries.iterdir(), None) is not None, (
        "No QNN evaluation results in summary directory."
    )

    print("""
    #############################################
    # -------------- QNN PREDICT -------------- #
    #############################################
    """)
    # GIVEN a reference config for QNN predict step for comparison later
    qnn_predict_io_config = _get_io_config(convert_output_folder, qnn_predict_output_folder)
    qnn_predict_reference_config, unknown_args = get_run_lanesegnet_config(
        argv=common_reference_args + qnn_predict_io_config,
        pipeline_step=PipelineStep.VISUALIZATION_QNN,
    )
    assert unknown_args == [], "Unknown args found in QNN predict reference config."

    # WHEN running the QNN predict step
    qnn_predict.main(common_args + qnn_predict_io_config)

    # THEN the predict step runs successfully and writes output to the output folder
    assert next(Path(qnn_predict_output_folder).iterdir(), None) is not None, "Predictions directory is empty."
    output_videos = {p.stem for p in (Path(qnn_predict_output_folder) / "visualizations" / "predict_qnn").glob("*.mp4")}
    assert "lanesegnet_annotations" not in output_videos, "Annotations video should not be generated while QNN pred."
    assert output_videos == {"lanesegnet_predictions"}, "QNN predictions video should be generated."


def _get_io_config(input_path: Path, output_path: Path) -> list[str]:
    """Get IO arguments for the pipeline step."""
    return ["--input_folder", str(input_path), "--output_folder", str(output_path)]
