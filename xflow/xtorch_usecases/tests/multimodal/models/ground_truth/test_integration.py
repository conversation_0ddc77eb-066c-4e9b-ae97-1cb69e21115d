"""Smoke and integration test for training, and convert stages of ground truth usecase."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path

import numpy as np
import onnxruntime
import pytest

from tests.multimodal.models.integration_test_utils import (
    run_convert_stage,
    run_evaluate_onnx_stage,
    run_evaluate_torch_stage,
    run_predict_stage,
    run_publish_stage,
    run_train_finetune_stage,
    run_train_stage,
)
from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.models.definitions import Model
from xtorch_usecases.multimodal.models.ground_truth.config import (
    GTModelUseCase,
    get_run_config_ground_truth,
    get_run_config_ground_truth_sparse,
)


@pytest.mark.slow
@pytest.mark.lfs_dependencies(
    [
        "xusecases/tests/test_data/vision/datasets/multiview/alliance_dataset",
        "xtorch_usecases/tests/test_data/multimodal/initial_encoder_weights",
    ]
)
@pytest.mark.parametrize(
    ("usecase", "head_type", "bev_layout", "lidar_voxelizer"),
    [
        (GTModelUseCase.TV_FC_LIDAR, "multitask_fusion_simple_head", "ground_truth", "pointpillars"),
        (GTModelUseCase.TV_FC_LIDAR, "multitask_sparse_detection", "ground_truth", "pointpillars"),
    ],
)
def test_integration_ground_truth(
    usecase: GTModelUseCase,
    head_type: str,
    tmpdir_delete_after: Path,
    bev_layout: str,
    lidar_voxelizer: str,
) -> None:
    """Test if gt related stages work together given an gt usecase.

    In order to skip this test run with `python -m pytest -m "not slow" <dir>`.
    """
    train_output_folder = str(tmpdir_delete_after / "train")
    train_finetune_output_folder = str(tmpdir_delete_after / "train_finetune")
    convert_output_folder = str(tmpdir_delete_after / "convert")
    onnx_eval_output_folder = str(tmpdir_delete_after / "onnx_eval")
    torch_eval_output_folder = str(tmpdir_delete_after / "evaluate_torch")
    predict_output_folder = str(tmpdir_delete_after / "predict")
    publish_output_folder = str(tmpdir_delete_after / "publish")

    # NOTE: workaround to enable different DATA_CONVERTER configs for simple_head and sparse usecases
    if head_type == "multitask_sparse_detection":
        model = Model.GROUND_TRUTH_SPARSE
        run_config_func = get_run_config_ground_truth_sparse
    else:
        model = Model.GROUND_TRUTH
        run_config_func = get_run_config_ground_truth

    model_args = [
        "--model",
        model,
    ]
    common_args = [
        "--usecase",
        usecase,
        "--head_type",
        head_type,
        "--environment",
        "local",
        "--short_ct_run",
        "True",
        "--bev_layout",
        bev_layout,
        "--lidar_voxelizer",
        lidar_voxelizer,
    ]
    common_args_with_model = model_args + common_args

    # ----------------- train -----------------
    run_train_stage(common_args=common_args_with_model, train_output_folder=train_output_folder)

    # ----------------- test reference config -----------------
    train_reference_args = common_args + [
        "--input_folder",
        train_output_folder,
        "--output_folder",
        train_output_folder,
    ]
    train_reference_config, unknown_args = run_config_func(argv=train_reference_args, pipeline_step=PipelineStep.TRAIN)
    assert unknown_args == [], "Unknown args were returned from the run config."
    assert next(train_reference_config.pipeline_step_paths.output_artifacts.iterdir(), None) is not None, (
        "Directory is empty."
    )
    assert next(train_reference_config.pipeline_step_paths.checkpoints.iterdir(), None) is not None, (
        "Checkpoints directory is empty."
    )
    assert next(train_reference_config.pipeline_step_paths.evaluation_torch.iterdir(), None) is not None, (
        "Evaluation directory is empty."
    )

    # ----------------- train finetune -----------------
    run_train_finetune_stage(
        common_args=common_args_with_model,
        train_finetune_input_folder=train_output_folder,
        train_finetune_output_folder=train_finetune_output_folder,
    )

    # ----------------- test reference config -----------------
    train_finetune_reference_args = common_args + [
        "--input_folder",
        train_output_folder,
        "--output_folder",
        train_finetune_output_folder,
    ]
    train_finetune_reference_config, unknown_args = run_config_func(
        argv=train_finetune_reference_args, pipeline_step=PipelineStep.TRAIN_FINETUNE
    )
    assert unknown_args == [], "Unknown args were returned from the run config."
    assert next(train_finetune_reference_config.pipeline_step_paths.output_artifacts.iterdir(), None) is not None, (
        "Directory is empty."
    )
    assert next(train_finetune_reference_config.pipeline_step_paths.checkpoints.iterdir(), None) is not None, (
        "Checkpoints directory is empty."
    )
    assert next(train_finetune_reference_config.pipeline_step_paths.evaluation_torch.iterdir(), None) is not None, (
        "Evaluation directory is empty."
    )

    # ----------------- torch eval -----------------
    additional_eval_args = [
        "--input_folder",
        train_finetune_output_folder,
        "--output_folder",
        torch_eval_output_folder,
    ]
    torch_eval_reference_config, unknown_args = run_config_func(
        argv=common_args + additional_eval_args, pipeline_step=PipelineStep.EVALUATE_TORCH
    )
    assert unknown_args == [], "Unknown args were returned from the run config."

    run_evaluate_torch_stage(
        evaluate_args=common_args_with_model + additional_eval_args,
        evaluation_path=torch_eval_reference_config.pipeline_step_paths.evaluation_torch / "predict",
        task_names=torch_eval_reference_config.multi_task_collection.enabled_task_ids,
    )

    if head_type == "multitask_sparse_detection":
        # NOTE: only train/eval stages are working for the multitask sparse model so far,
        #       will be addressed in follow-ups
        return
    # ----------------- conversion -----------------
    additional_convert_args = [
        "--input_folder",
        train_finetune_output_folder,
        "--output_folder",
        convert_output_folder,
    ]
    convert_model_args = model_args

    convert_args = common_args + additional_convert_args
    convert_reference_config, unknown_args = run_config_func(argv=convert_args, pipeline_step=PipelineStep.CONVERT)
    assert unknown_args == [], "Unknown args were returned from the run config."
    run_convert_stage(
        convert_args=convert_model_args + convert_args + ["convert_config.checkpoints_are_finetuned=True"],
        convert_config=convert_reference_config.convert_config,
    )
    _check_onnx_batch_inference(
        onnx_path=convert_reference_config.convert_config.raw_onnx_model_path,
        head_type=head_type,
    )

    # ----------------- onnx eval ---------------

    additional_onnx_eval_args = [
        "--input_folder",
        convert_output_folder,
        "--output_folder",
        onnx_eval_output_folder,
    ]

    onnx_eval_reference_config, unknown_args = run_config_func(
        argv=common_args + additional_onnx_eval_args, pipeline_step=PipelineStep.EVALUATE_ONNX
    )
    assert unknown_args == [], "Unknown args were returned from the run config."
    # change to fusion
    run_evaluate_onnx_stage(
        evaluate_args=common_args_with_model + additional_onnx_eval_args,
        evaluation_path=onnx_eval_reference_config.pipeline_step_paths.evaluation_torch / "predict",
        task_names=onnx_eval_reference_config.multi_task_collection.enabled_task_ids,
    )

    # ----------------- predict -----------------
    run_predict_stage(
        common_args=common_args_with_model,
        train_output_folder=train_finetune_output_folder,
        predict_output_folder=predict_output_folder,
    )

    # ----------------- publish -----------------
    run_publish_stage(
        common_args=common_args_with_model,
        train_output_folder=train_finetune_output_folder,
        publish_output_folder=publish_output_folder,
        predict_output_folder=predict_output_folder,
        torch_eval_output_folder=torch_eval_output_folder,
        convert_output_folder=convert_output_folder,
    )


def _check_onnx_batch_inference(onnx_path: Path, head_type: str, batch_size: int = 3) -> None:
    """Check that ONNX batch inference works correctly."""

    # Load the ONNX model
    sess = onnxruntime.InferenceSession(str(onnx_path))

    # Create dummy inputs for batch inference
    input_nodes = sess.get_inputs()
    dummy_inputs = {}
    print("Input Nodes Info:")
    for input_node in input_nodes:
        print(f"Name: {input_node.name}, Shape: {input_node.shape}, Type: {input_node.type}")
        input_shape = input_node.shape
        input_shape[0] = batch_size  # Set dynamic batch size to a concrete value to create input data
        dummy_inputs[input_node.name] = np.random.rand(*input_shape).astype(np.float32)  # noqa: NPY002

    # Run inference on the ONNX model
    outputs = sess.run(
        input_feed=dummy_inputs,
        output_names=["center", "dimension", "quaternion", "objectness", "labels"],
    )

    # THEN the outputs should have valid dimensions and values
    assert len(outputs) == 5
    (center, dimension, quaternion, objectness, labels) = outputs

    num_boxes = 400 if head_type == "fusion_simple_head" else 800
    assert center.shape == (batch_size, num_boxes, 3)
    assert dimension.shape == (batch_size, num_boxes, 3)
    assert quaternion.shape == (batch_size, num_boxes, 4)
    assert objectness.shape == (batch_size, num_boxes, 1)
    assert labels.shape == (batch_size, num_boxes, 1)

    # THEN the reversing of the batch elements should yield the same results
    if batch_size > 1:
        inputs_reversed = {k: v[::-1] for k, v in dummy_inputs.items()}
        outputs_reversed = sess.run(
            input_feed=inputs_reversed,
            output_names=["center", "dimension", "quaternion", "objectness", "labels"],
        )
        (center_reversed, dimension_reversed, quaternion_reversed, objectness_reversed, labels_reversed) = (
            outputs_reversed
        )
        for feat_orig, feat_reverse in zip(  # noqa: B007
            (center, dimension, quaternion, objectness, labels),
            (center_reversed, dimension_reversed, quaternion_reversed, objectness_reversed, labels_reversed),
        ):
            # FIXME: This test is flaky, sometimes the outputs are not equal # noqa: TD001, TD003
            pass
            # np.testing.assert_allclose(
            #     feat_orig,
            #     feat_reverse[::-1],
            #     atol=1e-4,
            #     rtol=1e-5,
            #     equal_nan=True,
            #     err_msg="Outputs should still be equal when reversing the batch order.",
            # )
