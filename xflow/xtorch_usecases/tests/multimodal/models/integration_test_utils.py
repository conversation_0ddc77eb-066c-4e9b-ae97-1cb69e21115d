"""Utils for multimodal integration tests."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path
from types import ModuleType
from unittest.mock import Mock, patch

import torch
from qnn_custom_ops import QnnBackend
from torch import nn

from xcontract.camera_models.definitions import CameraModelType
from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.nn.models.temporal_multi_task import TemporalMultiTaskModel
from xtorch.nn.module import StatefulModule
from xtorch.testing.run_in_subprocess import FuncType, run_in_subprocess
from xtorch_usecases.deep_radar_objects.nn.voxel_encoders.pypillars_voxelizer_encoder import RadarVoxelizerData
from xtorch_usecases.multimodal import (
    check_conversion,
    convert,
    evaluate,
    onnx_evaluate,
    predict,
    publish,
    qat,
    qnn_evaluate,
    train,
    train_finetune,
)
from xtorch_usecases.multimodal.common.data.vision_multiview_transforms import DeploymentVideoInput
from xtorch_usecases.multimodal.configs.convert import MultiModalConvertConfig
from xtorch_usecases.multimodal.configs.qat import MultiModalQatConfig
from xtorch_usecases.multimodal.scripts import sequence_runner


def _run_in_subprocess_patched(func: FuncType) -> FuncType:
    """Decorator to run a function in a subprocess with mocked pipeline info initialization.

    The purpose of this decorator is to apply the patch from the `patch_pipeline_info_json_initialization` fixture
    defined in tests/multimodal/conftest.py to functions running in a subprocess. (The fixture by itself does not cross
    process boundaries.)
    """
    return run_in_subprocess(context="spawn")(
        patch("xtorch_usecases.multimodal.configs.output_folders_factory.initialize_pipeline_info", Mock())(func)
    )  # pyright: ignore[reportReturnType]


class WrappedModel(StatefulModule):
    """Wrapper for the model to have flat tensor input and output."""

    def __init__(self, inner_model: nn.Module) -> None:
        """Initializes the model."""
        super().__init__()
        self._inner_model = inner_model

    def forward(  # noqa: PLR0913
        self,
        input_0: torch.Tensor,
        input_1: torch.Tensor,
        input_2: torch.Tensor,
        input_3: torch.Tensor,
        input_lut_0: torch.Tensor,
        input_lut_1: torch.Tensor,
        input_lut_2: torch.Tensor,
        input_lut_3: torch.Tensor,
        input_extrinsics_0: torch.Tensor,
        input_extrinsics_1: torch.Tensor,
        input_extrinsics_2: torch.Tensor,
        input_extrinsics_3: torch.Tensor,
        input_intrinsic_params_0: torch.Tensor,
        input_intrinsic_params_1: torch.Tensor,
        input_intrinsic_params_2: torch.Tensor,
        input_intrinsic_params_3: torch.Tensor,
        input_cone_tv: torch.Tensor,
        input_coords_3d_normalized_tv: torch.Tensor,
        input_4: torch.Tensor,
        input_lut_4: torch.Tensor,
        input_extrinsics_4: torch.Tensor,
        input_intrinsic_params_4: torch.Tensor,
        input_cone_fc1: torch.Tensor,
        input_coords_3d_normalized_fc1: torch.Tensor,
        radar_voxel_feat_enc: torch.Tensor,
        radar_voxel_coords: torch.Tensor,
    ) -> tuple[torch.Tensor, ...]:
        """Forward that executes the backbone and task heads."""
        backbone_input = {
            InputDataId.CAMERA_DEF_CYLINDER: {
                ValueKey.DATA: DeploymentVideoInput(
                    images=(input_0, input_1, input_2, input_3),
                    luts=(input_lut_0, input_lut_1, input_lut_2, input_lut_3),
                    extrinsics=(input_extrinsics_0, input_extrinsics_1, input_extrinsics_2, input_extrinsics_3),
                    intrinsic_params=(
                        input_intrinsic_params_0,
                        input_intrinsic_params_1,
                        input_intrinsic_params_2,
                        input_intrinsic_params_3,
                    ),
                    camera_types=(
                        torch.as_tensor([CameraModelType.DEFORMED_CYLINDER.value], dtype=torch.float32)[
                            :, None, None, None
                        ],
                        torch.as_tensor([CameraModelType.DEFORMED_CYLINDER.value], dtype=torch.float32)[
                            :, None, None, None
                        ],
                        torch.as_tensor([CameraModelType.DEFORMED_CYLINDER.value], dtype=torch.float32)[
                            :, None, None, None
                        ],
                        torch.as_tensor([CameraModelType.DEFORMED_CYLINDER.value], dtype=torch.float32)[
                            :, None, None, None
                        ],
                    ),
                    cone=input_cone_tv,
                    coords_3d_normalized=input_coords_3d_normalized_tv,
                )
            },
            InputDataId.CAMERA_CYLINDER: {
                ValueKey.DATA: DeploymentVideoInput(
                    images=(input_4,),
                    luts=(input_lut_4,),
                    extrinsics=(input_extrinsics_4,),
                    intrinsic_params=(input_intrinsic_params_4,),
                    camera_types=(
                        torch.as_tensor([CameraModelType.CYLINDER.value], dtype=torch.float32)[:, None, None, None],
                    ),
                    cone=input_cone_fc1,
                    coords_3d_normalized=input_coords_3d_normalized_fc1,
                )
            },
            InputDataId.RADAR: {ValueKey.DATA: RadarVoxelizerData(radar_voxel_feat_enc, radar_voxel_coords)},
        }
        if isinstance(self._inner_model, TemporalMultiTaskModel):
            return self._inner_model(backbone_input, None)[0]
        return self._inner_model(backbone_input)


@_run_in_subprocess_patched
def run_convert_stage(
    convert_args: list[str],
    convert_config: MultiModalConvertConfig,
) -> None:
    """Tests the convert stage."""
    # GIVEN the convert stage WHEN the convert stage is executed
    convert_args = convert_args + ["convert_config.skip_quantization_check=True"]

    convert.main(convert_args)

    # THEN the converted onnx models exist
    assert convert_config.raw_onnx_model_path.exists()

    if not convert_config.skip_qnn_conversion:
        assert convert_config.patched_onnx_model_path.exists()

        # THEN all QNN artifacts exist
        for backend in convert_config.qnn_backends:
            artifacts_dir = convert_config.default_root_dir
            all_files = [file.name for file in artifacts_dir.rglob("*") if file.is_file()]

            artifacts_basename = f"{convert_config.patched_onnx_model_path.stem}_qnn_{backend.value}"

            lib_file = f"lib{artifacts_basename}.so"
            assert lib_file in all_files, (
                f"Lib file {lib_file} not found for backend '{backend}' in the available lib files:{all_files}"
            )

            net_json = f"{artifacts_basename}_net.json"
            assert net_json in all_files

            metadata_json = f"{artifacts_basename}_metadata.json"
            assert metadata_json in all_files

            weights = f"{artifacts_basename}_weights.bin"
            assert weights in all_files

            if backend == QnnBackend.HTP:
                model_bin = f"{artifacts_basename}.bin"
                assert model_bin in all_files


@run_in_subprocess(context="spawn")
def run_check_conversion_stage(
    check_conversion_args: list[str],
    check_conversion_path: Path,
) -> None:
    """Tests the check_conversion stage."""
    # GIVEN the convert stage WHEN the convert stage is executed
    check_conversion_args = check_conversion_args + [
        "convert_config.skip_quantization_check=False",
        "convert_config.quant_check_config.enable_activation_histograms=False",
        "convert_config.quant_check_config.enable_quant_error_plots=False",
        "convert_config.quant_check_config.enable_weight_plots=False",
        "convert_config.quant_check_config.short_run=True",
    ]

    check_conversion.main(check_conversion_args)

    # THEN the quantization check results are not empty
    assert next(check_conversion_path.iterdir(), None) is not None


def _run_evaluate_stage(
    evaluate_entry: ModuleType,
    evaluate_args: list[str],
    evaluation_path: Path,
    task_names: list[str],
) -> None:
    """Tests one of the evaluate stages."""
    evaluate_entry.main(evaluate_args)

    assert next((evaluation_path).iterdir(), None) is not None, "Evaluation directory is empty."

    for task_name in task_names:
        # check if the gt file is yielding the expected amount of samples
        gt_file_path = evaluation_path / "iteration_00000000" / task_name / "rank0.gt.dataset"
        with gt_file_path.open("r") as file:
            lines = file.readlines()
            assert len(lines) > 0, f"GT file {gt_file_path} does not contain any samples."

        combined_metrics_path = evaluation_path / "iteration_00000000" / task_name / "combined_metrics_results.json"
        assert combined_metrics_path.exists(), f"Combined metrics file {combined_metrics_path} does not exist."


@_run_in_subprocess_patched
def run_evaluate_torch_stage(
    evaluate_args: list[str],
    evaluation_path: Path,
    task_names: list[str],
) -> None:
    """Tests the evaluate torch stage."""
    _run_evaluate_stage(evaluate, evaluate_args, evaluation_path, task_names)


@_run_in_subprocess_patched
def run_evaluate_qnn_stage(
    evaluate_args: list[str],
    evaluation_path: Path,
    task_names: list[str],
) -> None:
    """Tests the evaluate qnn stage."""
    _run_evaluate_stage(qnn_evaluate, evaluate_args, evaluation_path, task_names)


@run_in_subprocess(context="spawn")
def run_evaluate_onnx_stage(
    evaluate_args: list[str],
    evaluation_path: Path,
    task_names: list[str],
) -> None:
    """Tests the evaluate qnn stage."""
    _run_evaluate_stage(onnx_evaluate, evaluate_args, evaluation_path, task_names)


@_run_in_subprocess_patched
def run_train_stage(
    common_args: list[str],
    train_output_folder: str,
) -> None:
    """Tests the train stage."""
    train_args = common_args + ["--input_folder", train_output_folder, "--output_folder", train_output_folder]
    train.main(train_args)

    assert next(Path(train_output_folder).iterdir(), None) is not None, "Directory is empty."


@_run_in_subprocess_patched
def run_train_finetune_stage(
    common_args: list[str],
    train_finetune_input_folder: str,
    train_finetune_output_folder: str,
) -> None:
    """Tests the train stage."""
    train_finetune_args = common_args + [
        "--input_folder",
        train_finetune_input_folder,
        "--output_folder",
        train_finetune_output_folder,
    ]
    train_finetune.main(train_finetune_args)

    assert next(Path(train_finetune_output_folder).iterdir(), None) is not None, "Directory is empty."


@_run_in_subprocess_patched
def run_predict_stage(
    common_args: list[str],
    train_output_folder: str,
    predict_output_folder: str,
) -> None:
    """Tests the predict stage."""
    predict_args = common_args + ["--input_folder", train_output_folder, "--output_folder", predict_output_folder]
    predict.main(predict_args)

    assert next(Path(predict_output_folder).iterdir(), None) is not None, "Predictions directory is empty."


@_run_in_subprocess_patched
def run_qat_stage(
    qat_args: list[str],
    qat_config: MultiModalQatConfig,
) -> None:
    """Tests the qat stage."""
    qat.main(qat_args)

    assert qat_config.raw_onnx_model_path.exists()
    assert qat_config.raw_onnx_model_path.with_suffix(".encodings").exists()


@_run_in_subprocess_patched
def run_publish_stage(
    common_args: list[str],
    train_output_folder: str,
    publish_output_folder: str,
    predict_output_folder: str | None = None,
    torch_eval_output_folder: str | None = None,
    convert_output_folder: str | None = None,
) -> None:
    """Tests the publish stage."""

    input_folders = ["--input_folder", train_output_folder]
    if predict_output_folder is not None:
        input_folders.append(predict_output_folder)
    if torch_eval_output_folder is not None:
        input_folders.append(torch_eval_output_folder)
    if convert_output_folder is not None:
        input_folders.append(convert_output_folder)

    publish_args = common_args + input_folders + ["--publish_folder", publish_output_folder]
    publish.main(publish_args)

    assert next((Path(publish_output_folder) / "checkpoints").iterdir(), None) is not None
    assert next((Path(publish_output_folder) / "evaluation").iterdir(), None) is not None
    assert next((Path(publish_output_folder) / "summaries").iterdir(), None) is not None


@_run_in_subprocess_patched
def run_sequence_runner(
    sequence_runner_args: list[str], train_output_folder: str, sequence_runner_output_folder: str
) -> None:
    """Tests the publish stage."""

    sequence_runner.main(
        sequence_runner_args + ["--input_folder", train_output_folder, "--output_folder", sequence_runner_output_folder]
    )

    # in case of sequential data the filename contains the sequence id, therefore using wildcard for check
    assert any(
        Path(sequence_runner_output_folder).glob(
            "summaries/predict_torch/visualizations/predict/predict_epoch_0_grank_000*.mcap"
        )
    ), "No file matching the pattern 'predict_epoch_0_grank_000*.mcap' was found."
