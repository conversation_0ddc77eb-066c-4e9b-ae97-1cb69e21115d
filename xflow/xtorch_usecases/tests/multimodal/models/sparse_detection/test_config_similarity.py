"""Tests that the sparse_detection and the sparse_detection_temporal config are close."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import pytest
from deepdiff import DeepDiff

from xtorch_usecases.common.pipeline import PipelineStep
from xtorch_usecases.multimodal.models.sparse_detection.config import get_run_config_sparse_detection
from xtorch_usecases.multimodal.models.sparse_detection_temporal.config import get_run_config_sparse_detection_temporal


@pytest.mark.slow
def test_config_differences_are_known() -> None:
    """Test that the sparse_detection and the sparse_detection_temporal config are close.

    # DESCRIPTION

    This test is used to keep the sparse_detection and the sparse_detection_temporal config in sync except for the known
    differences due to the temporal mode.
    The motivation for this test is that the sparse_detection and the sparse_detection_temporal config are very similar
    and the only differences are due to the temporal mode. If the configs are not in sync, it is easy to forget to
    update one of them when changing the other one. This test will fail if the configs are not in sync.

    The test helps to address the two conflicting requirements of the config in the sparse_detection and the
    sparse_detection_temporal case:
    1) The config must not have any switches and hence we create a separate config for the temporal case (overwriting
       parameters is not always easily possible anyways). => "two configs"
    2) The temporal config only changes a handful of parameters and hence we want to keep the configs in sync as much
       as possible (in order to e.g. have them benefitting from improvements of each other config). => "one config"

    Simply reusing a lot of config functions and parameters/constants from the sparse_detection config is not as simple
    as it may sound. Especially changing the batch size is not easily overwritten as it is used in calculations and
    multiple config structures. Addionally, reusing parameters may lead to undesirably silently chaning the other config
    if that parameter is changed in one config.

    # HOW TO USE THE TEST / REACT ON A FAILING TEST

    If the test fails, it means that the config has changed and the known differences need to be updated. The test error
    message will tell you which config path is different. You can then check if this is a known difference or not. If it
    is a known difference (e.g. you just changed the config exactly this way), you need to update the
    `known_difference_paths` list in this test. If it is not a known difference, you need to check your config code
    change and possibly adapt the other config accordingly.
    Please note: The test also checks that the known differences are actually different. Hence, the test will also fail
    if a previously different config path is now equal. In that case you need to remove the config path from the
    `known_difference_paths` list.

    In any case of a failing test/changing config, a ping to the "DYO 2.0 AI Online" team would be appreciated.

    P.S.: This test is a temporary workaround as long as the two modes of the sparse_detection model exist. Eventually
    there should only exist the temporal mode with a single config and this test can be removed.
    """

    config_sparse_detection, _ = get_run_config_sparse_detection([], pipeline_step=PipelineStep.TRAIN)
    config_sparse_detection_temporal, _ = get_run_config_sparse_detection_temporal([], pipeline_step=PipelineStep.TRAIN)

    # These are the known differences between the two configs. The type of difference is not considered.
    known_difference_paths = [
        # root
        "root.model",
        # data:
        "root.data_config.sequential_loading",
        "root.data_config.sequence_length",
        "root.data_config.sampling_step_size",
        "root.data_config.sequence_info_processor",
        "root.data_config.alliance_splits_json_path",
        "root.data_config.alliance_dataset_names[0]",
        "root.data_config.enabled_input_modalities",
        "root.data_config.train_transforms",
        "root.data_config.common_transforms",
        # model:
        "root.task_configs.sparse_detection.head_config.fusion_head_params.temporal_mode",
        "root.enabled_task_to_config['sparse_detection'].head_config.fusion_head_params.temporal_mode",
        "root.multi_task_collection.enabled_task_to_config['sparse_detection'].head_config.fusion_head_params.temporal_mode",
        "root.multi_task_collection.enabled_tasks[0]._config.head_config.fusion_head_params.temporal_mode",
        "root.multi_task_collection.enabled_tasks[0].head_params.fusion_head_params.temporal_mode",
        "root.task_configs.sparse_detection.combined_metrics_yaml",
        "root.enabled_task_to_config['sparse_detection'].combined_metrics_yaml",
        "root.multi_task_collection.enabled_task_to_config['sparse_detection'].combined_metrics_yaml",
        "root.multi_task_collection.enabled_tasks[0]._config.combined_metrics_yaml",
        "root.task_configs.sparse_detection.head_config.fusion_head_params.memory_num_tokens",
        "root.task_configs.sparse_detection.head_config.fusion_head_params.num_tracks",
        "root.enabled_task_to_config['sparse_detection'].head_config.fusion_head_params.memory_num_tokens",
        "root.enabled_task_to_config['sparse_detection'].head_config.fusion_head_params.num_tracks",
        "root.multi_task_collection.enabled_task_to_config['sparse_detection'].head_config.fusion_head_params.memory_num_tokens",
        "root.multi_task_collection.enabled_task_to_config['sparse_detection'].head_config.fusion_head_params.num_tracks",
        "root.multi_task_collection.enabled_tasks[0]._config.head_config.fusion_head_params.memory_num_tokens",
        "root.multi_task_collection.enabled_tasks[0]._config.head_config.fusion_head_params.num_tracks",
        "root.multi_task_collection.enabled_tasks[0].head_params.fusion_head_params.memory_num_tokens",
        "root.multi_task_collection.enabled_tasks[0].head_params.fusion_head_params.num_tracks",
        # training:
        "root.trainer.train_epoch_length",
        "root.training_config.train_steps_per_epoch",
        "root.training_config.warmup_steps",
        "root.training_epoch_length",
        "root.training_config.lr",
        "root.training_config.log_eval_image_every_n_sample",
        "root.training_config.enable_temporal_training",
        "root.training_config.update_every_n_steps",
        "root.training_config.backprop_for_n_states",
        # qnn conversion:
        "root.convert_config.inputs_config",
    ]

    known_difference_paths_is_different = [False] * len(known_difference_paths)

    # We are not using the exclude_paths option of DeepDiff, because we want to check that the excluded paths are
    # actually different as well without calling DeepDiff twice.
    differences = DeepDiff(config_sparse_detection, config_sparse_detection_temporal)

    diff_path_flattened = [diff_path for diff_type in differences for diff_path in differences[diff_type]]

    unknown_differences = []

    for diff_path in diff_path_flattened:
        known_diff_path_hit = [known_diff_path in diff_path for known_diff_path in known_difference_paths]
        is_known_difference = any(known_diff_path_hit)

        if is_known_difference:
            known_difference_paths_is_different[known_diff_path_hit.index(True)] = True
        elif "_cached_cparts" not in diff_path:
            # Ignore cached parts of the config
            # This is a workaround for the fact that DeepDiff does not ignore the cached parts of the config
            # and we don't want to add all the cached parts to the known differences
            unknown_differences.append(diff_path)

    assert not unknown_differences, (
        "The following paths are not known differences (check the full error message):\n"
        + "\n".join(f"- {path}" for path in unknown_differences)
        + "\nIf these differences are intended, please add them to the known_difference_paths list. "
        "If not, please check your config code change."
    )

    # Collect all paths from the known difference paths list which are not different
    equal_paths = [
        path for is_diff, path in zip(known_difference_paths_is_different, known_difference_paths) if not is_diff
    ]

    # Assert that the list is empty and print a message if it is not
    assert not equal_paths, (
        "The following known difference paths are actually equal or do not exist (check the full error message):\n"
        + "\n".join(f"- {path}" for path in equal_paths)
        + "\nIf that is intended, please remove them from the known_difference_paths list. "
        "If not, please check your config code change."
    )
