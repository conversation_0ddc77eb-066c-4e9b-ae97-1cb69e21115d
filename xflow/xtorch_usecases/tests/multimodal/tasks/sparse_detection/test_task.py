"""Test sparse detection task."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import copy
from pathlib import Path

import pytest

from evil.config.run import RunEnvironment
from xtorch.geometry.volumes.common import BevLayout
from xtorch.multi_task.task import Task
from xtorch_usecases.multimodal.components.definitions import CameraBackboneType
from xtorch_usecases.multimodal.configs.callback import CallbackConfig
from xtorch_usecases.multimodal.configs.geometry import FAR_RANGE_BEV_LAYOUT
from xtorch_usecases.multimodal.configs.model.sparse_detection.model import (
    METRIC_CONFIGS,
    _get_head_config,
    _get_loss_config,
)
from xtorch_usecases.multimodal.configs.model.sparse_detection_temporal.model import (
    _get_head_config as _get_head_config_temporal,
)
from xtorch_usecases.multimodal.configs.model.sparse_detection_temporal.model import (
    _get_loss_config as _get_loss_config_temporal,
)
from xtorch_usecases.multimodal.configs.usecase import UsecaseConfig
from xtorch_usecases.multimodal.tasks.sparse_detection.head import SparseDetectionHead
from xtorch_usecases.multimodal.tasks.sparse_detection.loss import SparseDetectionLoss
from xtorch_usecases.multimodal.tasks.sparse_detection.memory_manager import MemoryState
from xtorch_usecases.multimodal.tasks.sparse_detection.task import SparseDetectionTask, SparseDetectionTaskConfig


@pytest.fixture
def common_config(tmp_path: Path) -> UsecaseConfig:
    """Fixture for creating a callback config."""
    return UsecaseConfig(
        callback_config=CallbackConfig(
            image_logging_frequency=1,
            output_root_dir=tmp_path,
        )
    )


@pytest.fixture
def task_config(tmp_path: Path) -> SparseDetectionTaskConfig:
    """Fixture for creating a config for the sparse detection task."""
    stride = 32
    bev_layout = BevLayout(x_min=-40, x_max=200, x_res=0.5, y_min=-39, y_max=50, y_res=1)
    return SparseDetectionTaskConfig(
        head_config=_get_head_config(stride=stride, bev_layout=bev_layout),
        loss_config=_get_loss_config(stride=stride),
        image_writer_callback_config=None,
        data_weight=1.0,
        loss_weight=1.0,
        camera_backbone_type=CameraBackboneType.BEV,
        evaluation_folder=tmp_path / "evaluation_folder",
        evil_configs={},
        eval_run_environment=RunEnvironment(num_processes=1, eval_compute_type="pytorch"),
        combined_metrics_yaml=Path("dummy"),
        label_set_name="boxes_generic_4class",
        label_set_version="2.5.0",
        label_set_background_class="Label",
        eval_objectness_threshold=0.1,
        store_gt_in_airbox=True,
        gt_key="gt_key",
        max_num_detections=50,
        metric_configs=METRIC_CONFIGS,
        bev_layout=FAR_RANGE_BEV_LAYOUT,
    )


@pytest.fixture
def task_config_temporal(tmp_path: Path) -> SparseDetectionTaskConfig:
    """Fixture for creating a config for the sparse detection task."""
    stride = 32
    bev_layout = BevLayout(x_min=-40, x_max=200, x_res=0.5, y_min=-39, y_max=50, y_res=1)
    return SparseDetectionTaskConfig(
        head_config=_get_head_config_temporal(stride=stride, bev_layout=bev_layout),
        loss_config=_get_loss_config_temporal(stride=stride),
        image_writer_callback_config=None,
        data_weight=1.0,
        loss_weight=1.0,
        camera_backbone_type=CameraBackboneType.BEV,
        evaluation_folder=tmp_path / "evaluation_folder",
        evil_configs={},
        eval_run_environment=RunEnvironment(num_processes=1, eval_compute_type="pytorch"),
        combined_metrics_yaml=Path("dummy"),
        label_set_name="boxes_generic_4class",
        label_set_version="2.5.0",
        label_set_background_class="Label",
        eval_objectness_threshold=0.1,
        store_gt_in_airbox=True,
        gt_key="gt_key",
        max_num_detections=50,
        metric_configs=METRIC_CONFIGS,
        bev_layout=FAR_RANGE_BEV_LAYOUT,
    )


def test_sparse_detection_task_is_instance_of_task(
    task_config: SparseDetectionTaskConfig, common_config: UsecaseConfig
) -> None:
    """Test that sparse_detection task is instance of task class."""
    # GIVEN: An instance of SparseDetectionTask
    sparse_detection_task = SparseDetectionTask(task_config, common_config)

    # THEN: SparseDetectionTask should be an instance of Task
    assert isinstance(sparse_detection_task, Task)


def test_sparse_detection_task_returns_head(
    task_config: SparseDetectionTaskConfig, common_config: UsecaseConfig
) -> None:
    """Test that sparse_detection task returns head."""
    # GIVEN: An instance of SparseDetectionTask
    sparse_detection_task = SparseDetectionTask(task_config, common_config)

    # WHEN: Getting the head
    head = sparse_detection_task.head

    # THEN: Head should not be None
    assert head is not None
    assert isinstance(head, SparseDetectionHead)


def test_sparse_detection_task_returns_loss(
    task_config: SparseDetectionTaskConfig, common_config: UsecaseConfig
) -> None:
    """Test that sparse_detection task returns loss."""
    # GIVEN: An instance of SparseDetectionTask
    sparse_detection_task = SparseDetectionTask(task_config, common_config)

    # WHEN: Getting the loss
    loss = sparse_detection_task.loss

    # THEN: Loss should not be None
    assert loss is not None
    assert isinstance(loss, SparseDetectionLoss)


def test_sparse_detection_task_returns_initial_temporal_state(
    task_config_temporal: SparseDetectionTaskConfig, common_config: UsecaseConfig, device: str
) -> None:
    """Test that sparse_detection task returns the initial temporal state."""
    # GIVEN: An instance of SparseDetectionTask and necessary parameters
    task_config_temporal_with_tracks = copy.deepcopy(task_config_temporal)
    task_config_temporal_with_tracks.head_config.fusion_head_params.num_tracks = 16
    task_config_temporal_without_tracks = copy.deepcopy(task_config_temporal)
    task_config_temporal_without_tracks.head_config.fusion_head_params.num_tracks = 0

    sparse_detection_task_with_tracks = SparseDetectionTask(task_config_temporal_with_tracks, common_config)
    sparse_detection_task_without_tracks = SparseDetectionTask(task_config_temporal_without_tracks, common_config)
    batch_size = 2

    # WHEN: Getting the initial temporal state
    initial_temporal_state_with_tracks = sparse_detection_task_with_tracks.initial_temporal_state(
        batch_size=batch_size, device=device
    )
    initial_temporal_state_without_tracks = sparse_detection_task_without_tracks.initial_temporal_state(
        batch_size=batch_size, device=device
    )

    # THEN: The initial temporal state should not be None
    assert initial_temporal_state_with_tracks is not None
    assert initial_temporal_state_without_tracks is not None
    assert isinstance(initial_temporal_state_with_tracks, MemoryState)
    assert isinstance(initial_temporal_state_without_tracks, MemoryState)
    # THEN: The tensors in the initial temporal state should not be empty (except for track-related fields in the
    # without tracks case)
    for field_name in initial_temporal_state_with_tracks._fields:
        tensor = getattr(initial_temporal_state_with_tracks, field_name)
        assert tensor.numel() > 0, f"Tensor '{field_name}' is empty"
    for field_name in initial_temporal_state_without_tracks._fields:
        tensor = getattr(initial_temporal_state_without_tracks, field_name)
        if field_name in ["track_embedding", "track_reference_points", "track_indices", "num_misses"]:
            assert tensor.numel() == 0, f"Tensor '{field_name}' should be empty but is not"
        else:
            assert tensor.numel() > 0, f"Tensor '{field_name}' is empty"
