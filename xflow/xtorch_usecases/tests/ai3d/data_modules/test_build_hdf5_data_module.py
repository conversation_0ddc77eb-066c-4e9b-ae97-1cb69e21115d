"""Tests building the hdf5 data module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Iterable, Mapping, MutableMapping
from pathlib import Path
from typing import Any, NamedTuple

import pytest
import torch

from xcontract.data.definitions.usage import Usage, ValueKey
from xtorch_usecases.ai3d.data_formats.camera_calibration.data_loading import CameraMetadataIndex
from xtorch_usecases.ai3d.data_modules.build_hdf5_data_module import (
    Hdf5DataModuleConfig,
    Hdf5InputContainer,
    build_hdf5_data_module,
)
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import INFERENCE_INPUT_RESOLUTION, CameraName


@pytest.fixture(name="hdf_file_path", params=[Path("aos.hdf")])
def create_hdf_file_path(request: pytest.FixtureRequest) -> Path:
    """Create the path to the HDF5 file to be used in the tests.

    The path to the HDF5 file is relative to the location of this file.
    The file is located in the test_data directory which follows the same
    relative path as the test file.
    """

    # Get the path to the current file as a list of the directory parts
    current_path = list(Path(__file__).resolve().parent.parts)

    # Remove the last parts
    current_path = current_path[:-2]

    # Add test_data/ai3d/utils to the path
    current_path.extend(["test_data", "ai3d", "utils"])

    # Add the name of the HDF5 file to the path
    hdf_test_file = Path().joinpath(*current_path) / request.param

    return hdf_test_file


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/ai3d/utils/aos.hdf"])
def test_build_hdf5_data_module(hdf_file_path: Path) -> None:
    """Test the build_hdf5_data_module function.

    Args:
        hdf_file_path (Path): The path to the HDF5 file.

    Returns:
        None
    """
    # GIVEN a valid configuration for the HDF5DataModule
    config = Hdf5DataModuleConfig(
        metadata_topic="/aos/activities/pyramid_metadata_provider_fc1/outputs/image_pyramid_metadata",
        image_topic="/aos/activities/hwa_viper_stage1/outputs/front_camera_yuv_image_pyramid_layer_1_00payload",
        odometry_topic="aos/activities/viper_odometry_provider/outputs/synchronized_accumulated_vehicle_odometry_with_reset",
        extrinsic_topic="/aos/activities/warp_lut_generator/outputs/fc1_extrinsic_wide",
        intrinsic_topic="/aos/activities/warp_lut_generator/outputs/fc1_intrinsic_wide",
        pyramid_layer=1,
        dataset_name=hdf_file_path.name,
        data_base_path=hdf_file_path.parent,
        camera_type="CYLINDER",
    )

    # WHEN the HDF5DataModule is built
    data_module = build_hdf5_data_module(config, usages=[Usage.VALIDATION, Usage.TEST])

    # THEN the length of the data module reflects the number of frames in the HDF5 file
    assert len(data_module) == 2

    # THEN train_dataloader raises an exception NotImplementedError
    with pytest.raises(NotImplementedError):
        data_module.train_dataloader()

    # THEN val_dataloader() raises an exception NotImplementedError
    with pytest.raises(NotImplementedError):
        data_module.val_dataloader()

    # THEN test_dataloader() returns an iterable object
    test_dataloader = data_module.test_dataloader()
    assert isinstance(test_dataloader, Iterable)

    # THEN predict_dataloader() returns an iterable object
    predict_dataloader = data_module.predict_dataloader()
    assert isinstance(predict_dataloader, Iterable)

    # THEN the iter method returns the data module itself
    assert iter(data_module) == data_module

    # THEN calling next produces a valid sample batch
    sample_batch = next(data_module)

    # THEN a valid sample batch requires these keys:
    required_keys = [DataId.CURRENT_IMAGE, DataId.PREVIOUS_IMAGE, DataId.MASKED_WARPING_GRID, DataId.PRIOR_POSE]

    for key in required_keys:
        assert key in sample_batch

    # THEN all values in the sample batch are torch tensors and the VALID flag is set
    for value in sample_batch.values():
        assert value[ValueKey.DATA] is not None
        assert value[ValueKey.VALID] is not False
        assert check_torch_tensor(value[ValueKey.DATA])

    # THEN the dimensions of the image tensors are correct
    current_image = sample_batch[DataId.CURRENT_IMAGE][ValueKey.DATA]
    previous_image = sample_batch[DataId.PREVIOUS_IMAGE][ValueKey.DATA]
    assert isinstance(current_image, torch.Tensor)
    assert current_image.shape == (
        1,
        3,
        INFERENCE_INPUT_RESOLUTION[CameraName.FC1].height,
        INFERENCE_INPUT_RESOLUTION[CameraName.FC1].width,
    )
    assert isinstance(previous_image, torch.Tensor)
    assert previous_image.shape == (
        1,
        3,
        INFERENCE_INPUT_RESOLUTION[CameraName.FC1].height,
        INFERENCE_INPUT_RESOLUTION[CameraName.FC1].width,
    )

    # THEN the width and hight are consistent with the values computed from the metadata
    camera_metadata = getattr(sample_batch[CSVDataId.SYS_CALIB][ValueKey.DATA], "camera_metadata", None)
    assert isinstance(camera_metadata, torch.Tensor)
    assert camera_metadata[0, CameraMetadataIndex.camera_height] == previous_image.shape[2]
    assert camera_metadata[0, CameraMetadataIndex.camera_width] == previous_image.shape[3]

    # THEN the prior pose contains a batch dimension and the expected 4 entries
    prior_pose = sample_batch[DataId.PRIOR_POSE][ValueKey.DATA]
    assert isinstance(prior_pose, torch.Tensor)
    assert prior_pose.shape == (1, 4)

    # THEN config method should return the configuration used to build the data module
    assert data_module.config == config


def check_torch_tensor(data: Any) -> bool:
    """Check if data only contains torch tensors in its leaf elements.

    Recursively check if the data are either already torch tensors or if
    it is some nested element like a list, tuple or dictionary that only
    contains torch tensors.

    Returns:
        True if all leaf elements are torch tensors, False otherwise.
    """
    if isinstance(data, torch.Tensor):
        return True
    if isinstance(data, (list, tuple)):
        return all(check_torch_tensor(item) for item in data)
    if isinstance(data, Mapping):
        return all(check_torch_tensor(item) for item in data.values())
    return False


def test_hdf5_input_container_to() -> None:
    """Test the to method of Hdf5InputContainer."""
    # GIVEN a Hdf5InputContainer with nested tensors

    source_device = torch.device("cpu")
    data = {
        "tensor1": {ValueKey.DATA: torch.randn(2, 3).to(source_device)},
        "tuple": {ValueKey.DATA: (torch.randn(2, 3).to(source_device), torch.randn(2, 3).to(source_device))},
    }

    container = Hdf5InputContainer(**data)

    # THEN the resulting container should only contain torch tensors
    assert check_torch_tensor(container), "Container should only contain torch tensors"

    # WHEN transferring the container to a different device
    target_device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # Provide a warning if source device and target device are equal
    if source_device == target_device:
        # We let the test run anyway in case there is no GPU available
        pytest.warns(UserWarning, match="Source device and target device are the same.")

    transferred_container = container.to(target_device)

    # THEN all tensors should be on the specified device
    tensor1 = transferred_container["tensor1"][ValueKey.DATA]
    assert isinstance(tensor1, torch.Tensor)
    assert tensor1.device.type == target_device.type
    for tensor in transferred_container["tuple"][ValueKey.DATA]:
        assert isinstance(tensor, torch.Tensor)
        assert tensor.device.type == target_device.type


def test_hdf5_input_container_to_device_unsupported_type() -> None:
    """Test the _to_device method with an unsupported data type."""
    # GIVEN an unsupported data type
    data = "unsupported data type"

    # WHEN transferring the data to a different device
    device = torch.device("cuda" if torch.cuda.is_available() else "cpu")

    # THEN a TypeError should be raised
    with pytest.raises(TypeError, match="Unsupported data type .*"):
        Hdf5InputContainer._to_device(data, device)  # noqa: SLF001


@pytest.fixture
def mock_device(device: str) -> torch.device:
    """Fixture to provide a mock device.

    Args:
        device (str): The device type to mock, e.g., "cpu" or "cuda". Value is provided by existing device fixture
    """
    return torch.device(device)


def test_to_device_tensor(mock_device: torch.device) -> None:
    """Test the _to_device method of Hdf5InputContainer.

    This test verifies that a tensor can be successfully transferred
    to the specified device. It checks that the result is a tensor
    and that it is located on the correct device.

    Args:
        mock_device (torch.device): The device to which the tensor
        should be transferred.
    """
    # GIVEN: A tensor and a target device
    tensor = torch.tensor([1, 2, 3])

    # WHEN: The tensor is transferred to the device
    result = Hdf5InputContainer._to_device(tensor, mock_device)  # noqa: SLF001

    # THEN: The result should be a tensor on the specified device
    assert isinstance(result, torch.Tensor)
    assert result.device.type == mock_device.type


def test_to_device_mutable_mapping(mock_device: torch.device) -> None:
    """Test the _to_device method of Hdf5InputContainer.

    This test verifies that a mutable mapping containing tensors is correctly
    transferred to the specified device. It checks that the result is still a
    mutable mapping and that all tensors within it are located on the
    expected device.

    Args:
        mock_device (torch.device): The device to which the tensors should be transferred.
    """
    # GIVEN: A mutable mapping containing tensors
    data: MutableMapping[str, Any] = {
        "a": torch.tensor([1, 2, 3]),
        "b": torch.tensor([4, 5, 6]),
    }

    # WHEN: The mapping is transferred to the device
    result = Hdf5InputContainer._to_device(data, mock_device)  # noqa: SLF001

    # THEN: All tensors in the mapping should be on the specified device
    assert isinstance(result, MutableMapping)
    for value in result.values():
        assert isinstance(value, torch.Tensor)
        assert value.device.type == mock_device.type


def test_to_device_tuple(mock_device: torch.device) -> None:
    """Test the _to_device method of Hdf5InputContainer for a tuple of tensors.

    This test verifies that when a tuple containing PyTorch tensors is
    passed to the _to_device method along with a specified device,
    all tensors in the tuple are correctly transferred to that device.

    Args:
        mock_device (torch.device): The device to which the tensors should be transferred.
    """
    # GIVEN: A tuple containing tensors
    data = (torch.tensor([1, 2, 3]), torch.tensor([4, 5, 6]))

    # WHEN: The tuple is transferred to the device
    result = Hdf5InputContainer._to_device(data, mock_device)  # noqa: SLF001

    # THEN: All tensors in the tuple should be on the specified device
    assert isinstance(result, tuple)
    for value in result:
        assert isinstance(value, torch.Tensor)
        assert value.device.type == mock_device.type


def test_to_device_named_tuple(mock_device: torch.device) -> None:
    """Test the _to_device method with a named tuple containing tensors.

    This test verifies that the _to_device method correctly transfers
    all tensor elements within a named tuple to the specified device.
    It checks that the resulting named tuple maintains its structure
    and that each tensor is located on the expected device.

    Args:
        mock_device (torch.device): The device to which the tensors
        should be transferred.
    """

    class NamedTupleExample(NamedTuple):
        x: torch.Tensor
        y: torch.Tensor

    data = NamedTupleExample(torch.tensor([1, 2, 3]), torch.tensor([4, 5, 6]))

    # WHEN: The named tuple is transferred to the device
    result = Hdf5InputContainer._to_device(data, mock_device)  # noqa: SLF001

    # THEN: All tensors in the named tuple should be on the specified device
    assert isinstance(result, NamedTupleExample)
    assert result.x.device.type == mock_device.type
    assert result.y.device.type == mock_device.type


if __name__ == "__main__":
    pytest.main([__file__])
