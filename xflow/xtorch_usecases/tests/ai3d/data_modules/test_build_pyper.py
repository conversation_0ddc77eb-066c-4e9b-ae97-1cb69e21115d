"""Tests for the parallax Pyper data module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections.abc import Iterable, Mapping, Sequence
from copy import copy
from enum import IntEnum
from pathlib import Path
from typing import Any, Final
from unittest.mock import MagicMock

import numpy as np
import numpy.typing as npt
import pandas as pd
import pytest
import torch
from numpy.typing import NDArray
from PIL import Image
from pytorch_lightning import Trainer
from scipy.interpolate import RegularGridInterpolator

from conversion.qnn.utils.dtype import cast_calib
from data_formats.camera.image_conversion.rgb_to_yuv import rgb8_to_yuv10, rgb_to_yuv_qc_uint16
from xcontract.camera_models.definitions import CameraModelType, IntrinsicIndex
from xcontract.data.definitions.usage import ValueKey
from xtorch.geometry.transformation.pose_transformation import PoseTransformation
from xtorch.geometry.transformation.quaternion import Quaternion
from xtorch.training.data_module.pyper.pipeline import OnDeviceBatchedTrainingDict
from xtorch_usecases.ai3d.camera import CameraGeometry, CameraIntrinsicParameters, ViewParameters
from xtorch_usecases.ai3d.config.model import (
    DEFAULT_ANGLE_SCALE,
    DEFAULT_TRANSLATION_SCALE,
    Ai3dDataModuleConfig,
    Config,
    get_multi_task_config,
)
from xtorch_usecases.ai3d.data_formats.camera_calibration import read_calibration_data_from_file
from xtorch_usecases.ai3d.data_formats.camera_calibration.data_loading import CameraMetadataIndex
from xtorch_usecases.ai3d.data_formats.depth_map import read_depth_map_from_file
from xtorch_usecases.ai3d.data_formats.surface_normals.data_processing import load_surface_normals_map
from xtorch_usecases.ai3d.data_formats.vehicle_odometry import read_vehicle_odometry_data_from_file
from xtorch_usecases.ai3d.data_modules.build_pyper import Usage, build_pyper_data_module
from xtorch_usecases.ai3d.data_modules.post_processable_data_module import PostProcessableDataModule
from xtorch_usecases.ai3d.data_postprocessing.augmentation.augmentation_functions import AugmentationType
from xtorch_usecases.ai3d.data_postprocessing.augmentation.augmentation_registry import AugmentationRegistry
from xtorch_usecases.ai3d.data_postprocessing.augmentation.color_augmentations import ColorAugmentationIndex
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    INFERENCE_INPUT_RESOLUTION,
    OUTPUT_SUBSAMPLING_FACTOR,
    PLANE_NORMAL,
    SURFACE_NORMALS_MAP_RESOLUTION,
    TRAIN_INPUT_OFFSET,
    TRAIN_INPUT_RESOLUTION,
    UNCROPPED_INPUT_RESOLUTION,
    ColorAugmentation,
    DataAugmentationParams,
    RotationAugmentation,
)
from xtorch_usecases.ai3d.tasks.parallax.task import PARALLAX_TASK_ID
from xtorch_usecases.ai3d.transformation.planar_homography import PlanarHomography
from xtorch_usecases.ai3d.utils.pipeline_mappings import PIPELINE_TO_DATA_MODULE_USAGES
from xtorch_usecases.ai3d.utils.structure_parameter import depth_to_structure_parameter
from xtorch_usecases.common.datasets.alliance.definitions import CameraName
from xtorch_usecases.common.helpers import get_test_data_folder
from xtorch_usecases.common.pipeline import PipelineStep

_TEST_CAMERA_NAME = CameraName.FC1  # Camera name
_INPUT_VIEW_PARAMETERS_CROPPED = ViewParameters(
    crop_u=TRAIN_INPUT_OFFSET[_TEST_CAMERA_NAME].width,
    crop_v=TRAIN_INPUT_OFFSET[_TEST_CAMERA_NAME].height,
    crop_width=TRAIN_INPUT_RESOLUTION[_TEST_CAMERA_NAME].width,
    crop_height=TRAIN_INPUT_RESOLUTION[_TEST_CAMERA_NAME].height,
    scale=1.0,
)
_INPUT_VIEW_PARAMETERS_UNCROPPED = ViewParameters(
    crop_u=(UNCROPPED_INPUT_RESOLUTION[_TEST_CAMERA_NAME].width - INFERENCE_INPUT_RESOLUTION[_TEST_CAMERA_NAME].width)
    // 2,
    crop_v=0,
    crop_width=INFERENCE_INPUT_RESOLUTION[_TEST_CAMERA_NAME].width,
    crop_height=INFERENCE_INPUT_RESOLUTION[_TEST_CAMERA_NAME].height,
    scale=1.0,
)
INPUT_VIEW_PARAMETERS: Final = {
    Usage.TRAINING: _INPUT_VIEW_PARAMETERS_CROPPED,
    Usage.VALIDATION: _INPUT_VIEW_PARAMETERS_CROPPED,
    Usage.TEST: _INPUT_VIEW_PARAMETERS_UNCROPPED,
}
_SCALE_SURFACE_NORMAL_MAP: Final = 1 / OUTPUT_SUBSAMPLING_FACTOR


@pytest.fixture
def dataset_path_and_csv() -> tuple[Path, str]:
    """Return the path to the dataset and the CSV file name."""
    return get_test_data_folder() / "camera/tasks/ai3d/", "parallax_dataset.csv"


NumpyData = npt.NDArray[np.float32] | Sequence[npt.NDArray[np.float32]]
UsageData = dict[CSVDataId, NumpyData]
ExpectedRawData = dict[Usage, UsageData]
ExpectedPostProcessedData = dict[Usage, dict[DataId, torch.Tensor]]


def unpack_data_buffer(data_buffer: list[Any]) -> list[npt.NDArray[np.float32]]:
    """Unpack a data buffer with a sequence of samples each containing the fields of the batch for that sample.

    Args:
        data_buffer : buffer where each element contains all the fields of a single sample as np.arrays.

    Returns:
        Batch where each field is the concatenation of the corresponding field for all samples.
    """
    return [np.asarray([data_buffer[j][i] for j in range(len(data_buffer))]) for i in range(len(data_buffer[0]))]


def create_expected_torch_raw_data(dataset_path_and_csv: tuple[Path, str]) -> ExpectedRawData:
    """Create the expected pytorch raw data for the test."""
    ds = pd.read_csv(dataset_path_and_csv[0] / dataset_path_and_csv[1])

    data = {}
    _split_usage_map = {"test": Usage.TEST, "train": Usage.TRAINING, "val": Usage.VALIDATION}
    for split, usage in _split_usage_map.items():
        split_ds = ds[ds["split"] == split]
        usage_data = {}
        for img in [CSVDataId.CURRENT_IMAGE, CSVDataId.PREVIOUS_IMAGE]:
            data_buffer = [
                rgb8_to_yuv10(np.asarray(Image.open(dataset_path_and_csv[0] / img_file)))
                .transpose(2, 0, 1)
                .astype(np.float32)
                for img_file in split_ds[img]
            ]
            usage_data[img] = np.asarray(data_buffer)

        # TODO: Remove this workaround once the dataset is fixed.
        # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/387106
        for pose, pose_ds_id, pose_entry in [
            (CSVDataId.CURRENT_POSE, CSVDataId.CURRENT_POSE, "pose_fem"),
            (CSVDataId.PREVIOUS_POSE, CSVDataId.PREVIOUS_POSE, "pose_fem"),
            (CSVDataId.GT_CURRENT_POSE, CSVDataId.CURRENT_POSE, "pose_imu"),
            (CSVDataId.GT_PREVIOUS_POSE, CSVDataId.PREVIOUS_POSE, "pose_imu"),
        ]:
            data_buffer = [
                read_vehicle_odometry_data_from_file(dataset_path_and_csv[0] / pose_file, pose_entry)
                for pose_file in split_ds[pose_ds_id]
            ]
            data_buffer = [np.asarray([data[i] for data in data_buffer]) for i in range(len(data_buffer[0]))]
            usage_data[pose] = data_buffer
        for calib in [CSVDataId.SYS_CALIB]:
            data_buffer = [
                read_calibration_data_from_file(dataset_path_and_csv[0] / calib_file, _TEST_CAMERA_NAME)
                for calib_file in split_ds[calib]
            ]
            usage_data[calib] = unpack_data_buffer(data_buffer)
        for depth in [CSVDataId.GT_DEPTH]:
            data_buffer = [
                read_depth_map_from_file(
                    dataset_path_and_csv[0] / depth_file, UNCROPPED_INPUT_RESOLUTION[CameraName.FC1]
                )
                for depth_file in split_ds[depth]
            ]
            usage_data[depth] = unpack_data_buffer(data_buffer)
        for surface_normal in [CSVDataId.GT_SURFACE_NORMAL]:
            data_buffer = [
                load_surface_normals_map(
                    dataset_path_and_csv[0] / surface_normal_file, SURFACE_NORMALS_MAP_RESOLUTION[CameraName.FC1]
                )
                for surface_normal_file in split_ds[surface_normal]
            ]
            usage_data[surface_normal] = unpack_data_buffer(data_buffer)

        data[usage] = usage_data

    return data


def create_expected_qnn_raw_data(dataset_path_and_csv: tuple[Path, str]) -> ExpectedRawData:
    """Create the expected QNN raw data for the test."""
    ds = pd.read_csv(dataset_path_and_csv[0] / dataset_path_and_csv[1])
    split_ds = ds[ds["split"] == "train"]
    usage_data = {}
    view = INPUT_VIEW_PARAMETERS[Usage.TEST]
    h_start, h_end = view.crop_v, view.crop_v + view.crop_height
    w_start, w_end = view.crop_u, view.crop_u + view.crop_width
    for img in [CSVDataId.CURRENT_IMAGE, CSVDataId.PREVIOUS_IMAGE]:
        qc_img_view_buffer = [
            cast_calib(
                rgb_to_yuv_qc_uint16(
                    np.asarray(Image.open(dataset_path_and_csv[0] / img_file))[h_start:h_end, w_start:w_end, :]
                )
            ).squeeze(0)
            for img_file in split_ds[img]
        ]
        data_buffer = [np.concatenate([qc_img["lsb"], qc_img["msb"]], axis=-1) for qc_img in qc_img_view_buffer]
        usage_data[img] = np.asarray(data_buffer)

    # TODO: Remove this workaround once the dataset is fixed.
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/387106
    for pose, pose_ds_id, pose_entry in [
        (CSVDataId.CURRENT_POSE, CSVDataId.CURRENT_POSE, "pose_fem"),
        (CSVDataId.PREVIOUS_POSE, CSVDataId.PREVIOUS_POSE, "pose_fem"),
    ]:
        data_buffer = [
            read_vehicle_odometry_data_from_file(dataset_path_and_csv[0] / pose_file, pose_entry)
            for pose_file in split_ds[pose_ds_id]
        ]
        data_buffer = [np.asarray([data[i] for data in data_buffer]) for i in range(len(data_buffer[0]))]
        usage_data[pose] = data_buffer

    for calib in [CSVDataId.SYS_CALIB]:
        data_buffer = [
            copy(read_calibration_data_from_file(dataset_path_and_csv[0] / calib_file, _TEST_CAMERA_NAME))
            for calib_file in split_ds[calib]
        ]
        for elem in data_buffer:
            elem.intrinsic[IntrinsicIndex.u0] -= view.crop_u
            elem.intrinsic[IntrinsicIndex.v0] -= view.crop_v
            elem.camera_metadata[CameraMetadataIndex.camera_height] = view.crop_height
            elem.camera_metadata[CameraMetadataIndex.camera_width] = view.crop_width
        usage_data[calib] = unpack_data_buffer(data_buffer)

    data = {Usage.TEST: usage_data}
    return data


@pytest.fixture(params=[PipelineStep.TRAIN, PipelineStep.EVALUATE_TORCH, PipelineStep.CONVERT_QNN])
def pipeline_step(request: pytest.FixtureRequest) -> PipelineStep:
    """Return the pipeline step for the test."""
    return request.param


@pytest.fixture(name="expected_raw_data")
def create_expected_raw_data(pipeline_step: PipelineStep, dataset_path_and_csv: tuple[Path, str]) -> ExpectedRawData:
    """Create the expected raw data for the test."""
    if pipeline_step.is_torch:
        return create_expected_torch_raw_data(dataset_path_and_csv)

    if pipeline_step.is_qnn:
        return create_expected_qnn_raw_data(dataset_path_and_csv)

    error_msg = f"Unsupported pipeline step: {pipeline_step}"
    raise ValueError(error_msg)


class FieldIdVehicleOdometry(IntEnum):
    """Indices corresponding to fields in VehicleOdometryNumpyData."""

    timestamp = 0
    rotation = 1
    translation = 2


class FieldIdCameraCalibration(IntEnum):
    """Indices corresponding to fields in CameraCalibrationNumpyData."""

    rotation = 0
    translation = 1
    intrinsic = 2
    metadata = 3


class MapFieldId(IntEnum):
    """Indices corresponding to fields in DepthNumpyData and SurfaceNormalNumpyData."""

    value_map = 0  # depth_map or surface_normal_map
    valid_mask = 1


def compute_transformation(
    pose: Sequence[npt.NDArray[np.float32]],
    field_type: type[FieldIdCameraCalibration] | type[FieldIdVehicleOdometry],
    device: torch.device | None = None,
) -> PoseTransformation:
    """Instantiate pose transformation from pose data."""
    rotation = Quaternion(torch.as_tensor(pose[field_type.rotation], device=device))
    translation = torch.as_tensor(pose[field_type.translation], device=device)
    return PoseTransformation(rotation, translation)


def compute_camera_transformation(
    previous_pose: Sequence[npt.NDArray[np.float32]],
    current_pose: Sequence[npt.NDArray[np.float32]],
    calib_data: Sequence[npt.NDArray[np.float32]],
    device: torch.device | None = None,
) -> PoseTransformation:
    """Compute camera pose transformation from absolute odometries and calibration data."""
    world_from_vehicle_previous = compute_transformation(previous_pose, FieldIdVehicleOdometry, device)
    world_from_vehicle_current = compute_transformation(current_pose, FieldIdVehicleOdometry, device)
    vehicle_from_camera = compute_transformation(calib_data, FieldIdCameraCalibration, device)

    vehicle_previous_from_vehicle_current = world_from_vehicle_previous.inverse() * world_from_vehicle_current
    camera_previous_from_camera_current = vehicle_previous_from_vehicle_current.relocated(vehicle_from_camera)

    return camera_previous_from_camera_current


def compute_ground_truth_pose(
    previous_pose: Sequence[npt.NDArray[np.float32]],
    current_pose: Sequence[npt.NDArray[np.float32]],
    calib_data: Sequence[npt.NDArray[np.float32]],
) -> tuple[torch.Tensor, torch.Tensor]:
    """Compute ground truth angles and translation vector from raw ground truth pose data."""
    gt_pose = compute_camera_transformation(
        previous_pose=previous_pose, current_pose=current_pose, calib_data=calib_data
    )
    return gt_pose.rotation.normalized().to_yaw_pitch_roll(), gt_pose.translation


def _camera_intrinsic_parameters(calibration: Sequence[npt.NDArray[np.float32]]) -> CameraIntrinsicParameters:
    """Get camera intrinsic parameters from calibration data."""
    camera_intrinsic = CameraIntrinsicParameters(
        camera_type=CameraModelType(calibration[FieldIdCameraCalibration.metadata][0, CameraMetadataIndex.camera_type]),
        full_image_height=int(calibration[FieldIdCameraCalibration.metadata][0, CameraMetadataIndex.camera_height]),
        full_image_width=int(calibration[FieldIdCameraCalibration.metadata][0, CameraMetadataIndex.camera_width]),
        fu=calibration[FieldIdCameraCalibration.intrinsic][0, IntrinsicIndex.fu],
        fv=calibration[FieldIdCameraCalibration.intrinsic][0, IntrinsicIndex.fv],
        u0=calibration[FieldIdCameraCalibration.intrinsic][0, IntrinsicIndex.u0],
        v0=calibration[FieldIdCameraCalibration.intrinsic][0, IntrinsicIndex.v0],
        cut_angle_degrees=calibration[FieldIdCameraCalibration.intrinsic][0, IntrinsicIndex.cut_angle],
    )
    return camera_intrinsic


def _camera_transformation_to_prior_pose(camera_transformation: PoseTransformation) -> torch.Tensor:
    """Convert camera transformation to prior pose."""
    yaw_angle = camera_transformation.rotation.normalized().to_yaw_pitch_roll()[..., 0]
    return torch.cat((camera_transformation.translation, yaw_angle[..., torch.newaxis]), dim=-1)


@pytest.fixture(name="expected_post_processed_data")
def create_expected_post_processed_data(
    device: str, expected_raw_data: ExpectedRawData, test_configuration: Config, pipeline_step: PipelineStep
) -> ExpectedPostProcessedData:
    """Create the expected post-processed data for the test."""
    data = {}
    for usage, raw_data in expected_raw_data.items():
        usage_data = {}

        plane_normal = PLANE_NORMAL.to(device)
        calibration_data = raw_data[CSVDataId.SYS_CALIB]
        previous_pose = raw_data[CSVDataId.PREVIOUS_POSE]
        current_pose = raw_data[CSVDataId.CURRENT_POSE]
        assert isinstance(calibration_data, Sequence)
        assert isinstance(previous_pose, Sequence)
        assert isinstance(current_pose, Sequence)
        camera_intrinsic = _camera_intrinsic_parameters(calibration_data)

        # prior pose
        prior_pose = compute_camera_transformation(previous_pose, current_pose, calibration_data, torch.device(device))
        usage_data[DataId.PRIOR_POSE] = _camera_transformation_to_prior_pose(prior_pose)

        # masked warping grid
        with torch.device(device):
            usage_data[DataId.CAMERA_HEIGHT] = torch.as_tensor(
                calibration_data[FieldIdCameraCalibration.translation][..., 2]
            )
        homography = PlanarHomography(prior_pose, usage_data[DataId.CAMERA_HEIGHT], plane_normal)
        warping_grid_scaling_factor = 1.0 / (
            2 * test_configuration.task_configs.parallax.head_params.output_pyramid_level
        )
        warping_grid_view_parameters = (
            INPUT_VIEW_PARAMETERS[usage].scaled(warping_grid_scaling_factor)
            if pipeline_step.is_torch
            else ViewParameters.identity(camera_intrinsic).scaled(warping_grid_scaling_factor)
        )
        warping_grid_camera_geometry = CameraGeometry(
            camera_intrinsic, warping_grid_view_parameters, torch.device(device)
        )
        warped_light_rays = homography.apply(warping_grid_camera_geometry.light_rays)
        masked_warping_grid = warping_grid_camera_geometry.points_to_masked_warping_grid(warped_light_rays)
        usage_data[DataId.MASKED_WARPING_GRID] = masked_warping_grid

        if pipeline_step.is_torch:
            # GT pose
            gt_previous_pose = raw_data[CSVDataId.GT_PREVIOUS_POSE]
            gt_current_pose = raw_data[CSVDataId.GT_CURRENT_POSE]
            assert isinstance(gt_previous_pose, Sequence)
            assert isinstance(gt_current_pose, Sequence)
            gt_pose_angle, gt_pose_translation = compute_ground_truth_pose(
                gt_previous_pose, gt_current_pose, calibration_data
            )
            usage_data[DataId.GT_POSE_ANGLE] = gt_pose_angle.to(device)
            usage_data[DataId.GT_POSE_TRANSLATION] = gt_pose_translation.to(device)
            usage_data[DataId.GT_NORMALIZED_POSE_ANGLE] = gt_pose_angle.to(device) / torch.tensor(
                DEFAULT_ANGLE_SCALE, device=device
            )
            usage_data[DataId.GT_NORMALIZED_POSE_TRANSLATION] = gt_pose_translation.to(device) / torch.tensor(
                DEFAULT_TRANSLATION_SCALE, device=device
            )

            # calibration
            view_params = INPUT_VIEW_PARAMETERS[usage]
            camera_geometry_net_input = CameraGeometry(camera_intrinsic, view_params, torch.device(device))

            # images
            for input_id, output_id in zip(
                [CSVDataId.CURRENT_IMAGE, CSVDataId.PREVIOUS_IMAGE], [DataId.CURRENT_IMAGE, DataId.PREVIOUS_IMAGE]
            ):
                usage_data[output_id] = camera_geometry_net_input.scaled_crop(torch.as_tensor(raw_data[input_id]))

            # surface normals
            view_parameters_net_output = view_params.scaled(_SCALE_SURFACE_NORMAL_MAP)
            camera_geometry_net_output = CameraGeometry(
                camera_intrinsic, view_parameters_net_output, torch.device(device)
            )
            usage_data[DataId.GT_SURFACE_NORMALS_MAP] = camera_geometry_net_output.scaled_crop(
                torch.as_tensor(raw_data[CSVDataId.GT_SURFACE_NORMAL][MapFieldId.value_map])
            )
            usage_data[DataId.VALID_SURFACE_NORMALS_MASK] = camera_geometry_net_output.scaled_crop(
                torch.as_tensor(raw_data[CSVDataId.GT_SURFACE_NORMAL][MapFieldId.valid_mask])
            )

            # structure parameter
            with torch.device(device):
                depth_map = camera_geometry_net_input.scaled_crop(
                    torch.as_tensor(raw_data[CSVDataId.GT_DEPTH][MapFieldId.value_map])
                )
                usage_data[DataId.VALID_STRUCTURE_PARAMETER_MASK] = camera_geometry_net_input.scaled_crop(
                    torch.as_tensor(raw_data[CSVDataId.GT_DEPTH][MapFieldId.valid_mask])
                )
                usage_data[DataId.GT_STRUCTURE_PARAMETER_MAP] = depth_to_structure_parameter(
                    depth_map,
                    camera_geometry_net_input.light_rays,
                    usage_data[DataId.CAMERA_HEIGHT],
                    plane_normal,
                    validity_mask=usage_data[DataId.VALID_STRUCTURE_PARAMETER_MASK],
                )

        assert not any(torch.isnan(val).any() for val in usage_data.values()), "Data contains NaN values"
        data[usage] = usage_data

    return data


@pytest.fixture(name="test_configuration")
def create_parallax_test_configuration() -> Config:
    """Create a test configuration for the parallax task."""
    configuration = get_multi_task_config()

    configuration.data.num_workers_train = 1
    configuration.data.num_workers_val = 1
    configuration.task_configs.parallax.camera_name = _TEST_CAMERA_NAME
    configuration.data.prefetch_factor = None
    configuration.task_configs.parallax.data_augmentation.extrinsic_rotation.enabled = False
    configuration.task_configs.parallax.data_augmentation.odometry_rotation.enabled = False
    configuration.task_configs.parallax.data_augmentation.color_augmentation.enabled = False
    return configuration


def usage_data_loader(
    usage: Usage, data_module: PostProcessableDataModule[Ai3dDataModuleConfig, Any]
) -> Iterable[OnDeviceBatchedTrainingDict]:
    """Return the data loader for the given usage."""
    data_loader_from_usage_map: Final = {
        Usage.TRAINING: data_module.train_dataloader,
        Usage.VALIDATION: data_module.val_dataloader,
        Usage.TEST: data_module.test_dataloader,
    }
    assert usage in data_loader_from_usage_map

    usage_to_stage_map: Final = {
        Usage.TRAINING: "training",
        Usage.VALIDATION: "validating",
        Usage.TEST: "testing",
    }
    data_module.trainer = Trainer()
    setattr(data_module.trainer, usage_to_stage_map[usage], True)

    return data_loader_from_usage_map[usage]()


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/camera/tasks/ai3d"])
def test_build_pyper_data_module(
    pipeline_step: PipelineStep, test_configuration: Config, expected_raw_data: ExpectedRawData
) -> None:
    """Test the correctness of the data provided by the Pyper data module."""
    # GIVEN a configuration containing the parallax dataset setup and an intended pipeline step
    usages_to_test = PIPELINE_TO_DATA_MODULE_USAGES[pipeline_step]
    primary_usage = usages_to_test[0]
    number_of_samples = len(expected_raw_data[primary_usage][CSVDataId.CURRENT_IMAGE])
    batch_size = number_of_samples + 1
    test_configuration.data.batch_size_train = batch_size
    test_configuration.data.batch_size_val = batch_size

    # WHEN the Pyper data module is created for the given pipeline step
    data_module = build_pyper_data_module(test_configuration, pipeline_step=pipeline_step)
    data_module.trainer = MagicMock(global_rank=0, world_size=1)

    # THEN the batch of raw samples provided by the dataloader of the corresponding usage should be valid and contain
    # the expected data values
    for usage in usages_to_test:
        data_module.setup("")
        batch_samples = next(iter(usage_data_loader(usage, data_module)))
        data_module.teardown("")

        for data_id, expected_values in expected_raw_data[usage].items():
            assert torch.all(torch.as_tensor(batch_samples[data_id][ValueKey.VALID])[:number_of_samples])
            if usage != Usage.TRAINING:
                assert ~torch.any(torch.as_tensor(batch_samples[data_id][ValueKey.VALID])[number_of_samples:])

            batch_data = batch_samples[data_id][ValueKey.DATA]
            if isinstance(expected_values, np.ndarray):
                output_data = torch.as_tensor(batch_data)[:number_of_samples]
                torch.testing.assert_close(output_data, torch.from_numpy(expected_values).to(output_data.device))
            else:
                for output_batch_entry, expected_entry_value in zip(batch_data, expected_values):
                    output_data = torch.as_tensor(output_batch_entry)[:number_of_samples]
                    expected_data = torch.from_numpy(expected_entry_value).to(output_data.device)
                    assert output_data.shape == expected_data.shape, (
                        f"{data_id} mismatch shape: {output_data.shape} != {expected_data.shape}"
                    )
                    torch.testing.assert_close(
                        output_data, expected_data, msg=f"{data_id} value mismatch\n: {output_data} != {expected_data}"
                    )


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/camera/tasks/ai3d"])
def test_data_module_post_processing(
    device: str,
    pipeline_step: PipelineStep,
    test_configuration: Config,
    expected_post_processed_data: ExpectedPostProcessedData,
) -> None:
    """Test the correctness of the post-processed data provided by the Pyper data module."""
    # GIVEN a configuration containing the parallax dataset setup
    usages_to_test = PIPELINE_TO_DATA_MODULE_USAGES[pipeline_step]
    primary_usage = usages_to_test[0]
    number_of_samples = len(next(iter(expected_post_processed_data[primary_usage].values())))
    batch_size = number_of_samples + 1
    test_configuration.data.batch_size_train = batch_size
    test_configuration.data.batch_size_val = batch_size

    # WHEN the Pyper data module is created for the given pipeline step
    data_module = build_pyper_data_module(test_configuration, pipeline_step=pipeline_step)
    data_module.trainer = MagicMock(global_rank=0, world_size=1)

    # THEN the batch of post-processed samples provided by the dataloader after post-processsing should be valid and
    # contain the expected data values
    for usage in usages_to_test:
        data_module.setup("")
        batch_samples = next(iter(usage_data_loader(usage, data_module)))
        batch_samples = data_module.transfer_batch_to_device(batch_samples, torch.device(device), dataloader_idx=0)
        batch_samples = data_module.on_after_batch_transfer(batch_samples, dataloader_idx=0)
        data_module.teardown("")

        for data_id, expected_values in expected_post_processed_data[usage].items():
            assert torch.all(torch.as_tensor(batch_samples[data_id][ValueKey.VALID])[:number_of_samples])
            if usage != Usage.TRAINING:
                assert ~torch.any(torch.as_tensor(batch_samples[data_id][ValueKey.VALID])[number_of_samples:])

            batch_data = batch_samples[data_id][ValueKey.DATA]
            output_data = torch.as_tensor(batch_data)[:number_of_samples]
            torch.testing.assert_close(output_data, expected_values.to(device), msg=lambda m: f"{data_id} mismatch {m}")  # noqa: B023

        if pipeline_step.is_torch:
            parallax_task_labels = batch_samples[PARALLAX_TASK_ID][ValueKey.DATA]

            for label_id, label_value in parallax_task_labels._asdict().items():
                assert id(label_value) == id(batch_samples[label_id][ValueKey.DATA])


_ROTATION_AUGMENTATION = RotationAugmentation(delta_deg_yaw=15.0, delta_deg_pitch=10.0, delta_deg_roll=8.0)

# Use a color augmentation that technically should not change the image It still introduces a small rounding error due
# to the different color representation use there.
_COLOR_AUGMENTATION = ColorAugmentation(
    random_brightness_probability=0,
    random_hue_probability=0,
    random_saturation_probability=0,
    random_contrast_probability=0,
    random_gamma_probability=0,
)


def _validate_rotation_augmentation_data(data: torch.Tensor) -> None:
    ypr = torch.rad2deg(Quaternion(data).to_yaw_pitch_roll())
    limits_ypr = torch.tensor(
        [
            _ROTATION_AUGMENTATION.delta_deg_yaw,
            _ROTATION_AUGMENTATION.delta_deg_pitch,
            _ROTATION_AUGMENTATION.delta_deg_roll,
        ],
    ).to(ypr.device)
    assert torch.ge(ypr, -limits_ypr).all()
    assert torch.le(ypr, limits_ypr).all()


def _validate_color_augmentation_data(data: torch.Tensor) -> None:
    """Validate color augmentation data dimension."""
    assert data.shape == (len(ColorAugmentationIndex),)


_AUGMENTATION_VALIDATORS: Final = {
    AugmentationType.EXTRINSIC_ROTATION: _validate_rotation_augmentation_data,
    AugmentationType.ODOMETRY_ROTATION: _validate_rotation_augmentation_data,
    AugmentationType.COLOR_AUGMENTATION: _validate_color_augmentation_data,
}


@pytest.fixture(
    name="enabled_augmentations",
    params=[
        {AugmentationType.EXTRINSIC_ROTATION},
        {AugmentationType.ODOMETRY_ROTATION},
        {AugmentationType.EXTRINSIC_ROTATION, AugmentationType.ODOMETRY_ROTATION},
        {AugmentationType.COLOR_AUGMENTATION},
    ],
    ids=["extrinsic", "odometry", "extrinsic+odometry", "color"],
)
def set_enabled_augmentations(request: pytest.FixtureRequest) -> set[str]:
    """Set the type of augmentation to be enabled."""
    return request.param


@pytest.fixture(name="test_configuration_with_augmentation")
def create_parallax_test_configuration_with_augmentation(
    test_configuration: Config, enabled_augmentations: set[str]
) -> Config:
    """Create a test configuration for the parallax task."""
    augmentation_params = DataAugmentationParams(
        extrinsic_rotation=copy(_ROTATION_AUGMENTATION),
        odometry_rotation=copy(_ROTATION_AUGMENTATION),
        color_augmentation=copy(_COLOR_AUGMENTATION),
    )

    for augmentation in enabled_augmentations:
        getattr(augmentation_params, augmentation).enabled = True

    config = test_configuration
    config.task_configs.parallax.data_augmentation = augmentation_params
    return config


def _interpolate_data(data: NDArray[np.float32], warping_map: torch.Tensor) -> NDArray[np.float32]:
    """Interpolate data using the warping map."""
    half_pixel: Final = 0.5
    height, width = data.shape[2:]
    batch_grid = list(range(data.shape[0]))
    channel_grid = list(range(data.shape[1]))
    map_grid = (
        np.linspace(half_pixel, height - half_pixel, num=height),
        np.linspace(half_pixel, width - half_pixel, num=width),
    )
    data_grid = (batch_grid, channel_grid) + map_grid
    interpolator = RegularGridInterpolator(data_grid, data, method="nearest", bounds_error=False, fill_value=0.0)
    channel_index_out = [i * np.ones(warping_map.shape[:2]) for i in channel_grid]
    interpolated_data = interpolator((batch_grid, channel_index_out, warping_map[..., 1], warping_map[..., 0]))
    return interpolated_data.astype(np.float32)


def _get_expected_augmented_data(
    expected_raw_data: UsageData, augmentation: Mapping[AugmentationType, torch.Tensor], device: str
) -> dict[DataId, torch.Tensor]:
    """Get the expected augmented data for the test."""
    calibration_data: Final = expected_raw_data[CSVDataId.SYS_CALIB]
    current_pose: Final = expected_raw_data[CSVDataId.CURRENT_POSE]
    previous_pose: Final = expected_raw_data[CSVDataId.PREVIOUS_POSE]
    gt_current_pose: Final = expected_raw_data[CSVDataId.GT_CURRENT_POSE]
    gt_previous_pose: Final = expected_raw_data[CSVDataId.GT_PREVIOUS_POSE]
    raw_current_image: Final = expected_raw_data[CSVDataId.CURRENT_IMAGE]
    raw_previous_image: Final = expected_raw_data[CSVDataId.PREVIOUS_IMAGE]
    raw_gt_depth: Final = expected_raw_data[CSVDataId.GT_DEPTH]
    assert isinstance(calibration_data, Sequence)
    assert isinstance(current_pose, Sequence)
    assert isinstance(previous_pose, Sequence)
    assert isinstance(gt_current_pose, Sequence)
    assert isinstance(gt_previous_pose, list)
    assert isinstance(raw_current_image, np.ndarray)
    assert isinstance(raw_previous_image, np.ndarray)
    assert isinstance(raw_gt_depth, Sequence)

    def _crop_light_rays(light_ray_data: torch.Tensor, scale: float = 1.0) -> torch.Tensor:
        height = int(scale * _INPUT_VIEW_PARAMETERS_CROPPED.crop_height)
        width = int(scale * _INPUT_VIEW_PARAMETERS_CROPPED.crop_width)
        crop_v = int(scale * _INPUT_VIEW_PARAMETERS_CROPPED.crop_v)
        crop_u = int(scale * _INPUT_VIEW_PARAMETERS_CROPPED.crop_u)
        cropped_data = light_ray_data[crop_v : crop_v + height, crop_u : crop_u + width, ...]
        return cropped_data

    camera_intrinsic = _camera_intrinsic_parameters(calibration_data)
    camera_geometry_original = CameraGeometry(camera_intrinsic, ViewParameters.identity(camera_intrinsic))
    input_light_rays = _crop_light_rays(camera_geometry_original.light_rays)

    _zero_rotation: Final = torch.tensor([0.0, 0.0, 0.0, 1.0])
    cam_from_aug_cam_rotation: Final = Quaternion(
        augmentation[AugmentationType.EXTRINSIC_ROTATION].cpu()
        if AugmentationType.EXTRINSIC_ROTATION in augmentation
        else _zero_rotation
    )
    delta_extrinsic_rotation: Final = cam_from_aug_cam_rotation.conjugate()

    aug_prev_rot_from_prev_rot: Final = Quaternion(
        augmentation[AugmentationType.ODOMETRY_ROTATION].cpu()
        if AugmentationType.ODOMETRY_ROTATION in augmentation
        else _zero_rotation
    )

    rotation_input_warping_map = camera_geometry_original.point_to_pixel(
        delta_extrinsic_rotation.rotate(input_light_rays)
    )
    previous_rotation_input_warping_map = camera_geometry_original.point_to_pixel(
        aug_prev_rot_from_prev_rot.rotate(delta_extrinsic_rotation.rotate(input_light_rays))
    )
    scaled_camera_geometry = camera_geometry_original.scaled(_SCALE_SURFACE_NORMAL_MAP)
    scaled_light_rays = _crop_light_rays(scaled_camera_geometry.light_rays, _SCALE_SURFACE_NORMAL_MAP)
    rotation_scaled_warping_map = scaled_camera_geometry.point_to_pixel(
        delta_extrinsic_rotation.rotate(scaled_light_rays)
    )

    cropped_augmented_data = {
        CSVDataId.CURRENT_IMAGE: _interpolate_data(raw_current_image, rotation_input_warping_map),
        CSVDataId.PREVIOUS_IMAGE: _interpolate_data(raw_previous_image, previous_rotation_input_warping_map),
        CSVDataId.GT_DEPTH: [_interpolate_data(data, rotation_input_warping_map) for data in raw_gt_depth],
        CSVDataId.GT_SURFACE_NORMAL: [
            _interpolate_data(data, rotation_scaled_warping_map)
            for data in expected_raw_data[CSVDataId.GT_SURFACE_NORMAL]
        ],
    }

    cam_from_aug_cam_trafo = PoseTransformation(delta_extrinsic_rotation.conjugate(), torch.zeros(3))
    aug_prev_from_prev_trafo = PoseTransformation(aug_prev_rot_from_prev_rot, torch.zeros(3))
    transformations = {
        DataId.PRIOR_POSE_TRANSFORMATION: compute_camera_transformation(previous_pose, current_pose, calibration_data),
        DataId.GT_POSE_TRANSFORMATION: aug_prev_from_prev_trafo
        * compute_camera_transformation(gt_previous_pose, gt_current_pose, calibration_data).relocated(
            cam_from_aug_cam_trafo
        ),
    }

    structure_parameter_mask = torch.tensor(
        cropped_augmented_data[CSVDataId.GT_DEPTH][MapFieldId.valid_mask], dtype=torch.bool
    )
    structure_parameter_map = depth_to_structure_parameter(
        torch.tensor(cropped_augmented_data[CSVDataId.GT_DEPTH][MapFieldId.value_map]),
        input_light_rays,
        torch.tensor(calibration_data[FieldIdCameraCalibration.translation][..., 2]),
        torch.tensor(PLANE_NORMAL),
        validity_mask=structure_parameter_mask,
    )

    surface_normal_map = delta_extrinsic_rotation.rotate(
        torch.tensor(cropped_augmented_data[CSVDataId.GT_SURFACE_NORMAL][MapFieldId.value_map]).permute(1, 2, 0)
    ).permute(2, 0, 1)

    prior_pose = _camera_transformation_to_prior_pose(transformations[DataId.PRIOR_POSE_TRANSFORMATION])
    with torch.device(device):
        expected_augmented_data = {
            DataId.CURRENT_IMAGE: torch.tensor(cropped_augmented_data[CSVDataId.CURRENT_IMAGE]),
            DataId.PREVIOUS_IMAGE: torch.tensor(cropped_augmented_data[CSVDataId.PREVIOUS_IMAGE]),
            DataId.GT_STRUCTURE_PARAMETER_MAP: torch.tensor(structure_parameter_map),
            DataId.VALID_STRUCTURE_PARAMETER_MASK: torch.tensor(structure_parameter_mask),
            DataId.GT_SURFACE_NORMALS_MAP: torch.tensor(surface_normal_map),
            DataId.VALID_SURFACE_NORMALS_MASK: torch.tensor(
                cropped_augmented_data[CSVDataId.GT_SURFACE_NORMAL][MapFieldId.valid_mask], dtype=torch.bool
            ),
            DataId.PRIOR_POSE: torch.tensor(prior_pose),
            DataId.GT_POSE_ANGLE: torch.tensor(
                transformations[DataId.GT_POSE_TRANSFORMATION].rotation.to_yaw_pitch_roll()
            ),
            DataId.GT_POSE_TRANSLATION: torch.tensor(transformations[DataId.GT_POSE_TRANSFORMATION].translation),
            DataId.GT_NORMALIZED_POSE_ANGLE: torch.tensor(
                transformations[DataId.GT_POSE_TRANSFORMATION].rotation.to_yaw_pitch_roll()
            )
            / torch.tensor(DEFAULT_ANGLE_SCALE),
            DataId.GT_NORMALIZED_POSE_TRANSLATION: torch.tensor(
                transformations[DataId.GT_POSE_TRANSFORMATION].translation
            )
            / torch.tensor(DEFAULT_TRANSLATION_SCALE),
        }

        assert not any(torch.isnan(val).any() for val in expected_augmented_data.values()), "Data contains NaN values"

    return expected_augmented_data


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/camera/tasks/ai3d"])
def test_data_module_post_processing_with_augmentation(
    device: str,
    test_configuration_with_augmentation: Config,
    enabled_augmentations: set[AugmentationType],
) -> None:
    """Test the data augmentation as part of the post-processing chain in the Pyper data module."""
    # GIVEN a configuration containing the parallax dataset setup with enabled augmentation and an intended usage
    pipeline_step = PipelineStep.TRAIN
    usages_to_test = PIPELINE_TO_DATA_MODULE_USAGES[pipeline_step]
    primary_usage = usages_to_test[0]
    assert isinstance(test_configuration_with_augmentation.data.dataset_base_path, Path)
    expected_raw_data = create_expected_torch_raw_data(
        (
            test_configuration_with_augmentation.data.dataset_base_path,
            test_configuration_with_augmentation.data.dataset_name,
        )
    )
    number_of_samples = len(expected_raw_data[primary_usage][CSVDataId.CURRENT_IMAGE])
    test_configuration_with_augmentation.data.batch_size_train = number_of_samples
    test_configuration_with_augmentation.data.batch_size_val = number_of_samples

    AugmentationRegistry.clear_registry()

    # WHEN the Pyper data module is created for the given pipeline step
    data_module = build_pyper_data_module(test_configuration_with_augmentation, pipeline_step=pipeline_step)
    data_module.trainer = MagicMock(global_rank=0, world_size=1)

    min_similarity: Final = 99.8  # 99.8%
    for usage in usages_to_test:
        data_module.setup("")
        batch_samples = next(iter(usage_data_loader(usage, data_module)))
        batch_samples = data_module.transfer_batch_to_device(batch_samples, torch.device(device), dataloader_idx=0)
        batch_samples = data_module.on_after_batch_transfer(batch_samples, dataloader_idx=0)
        data_module.teardown("")

        # THEN the batch of post-processed samples provided by the dataloader after post-processsing should contain the
        # expected augmentation data only for the training usage. Otherwise, no augmentation shall be applied.
        if usage == Usage.TRAINING:
            augmentation_data: dict[AugmentationType, torch.Tensor] = batch_samples[DataId.AUGMENTATION_DATA][
                ValueKey.DATA
            ]
            assert set(augmentation_data) == enabled_augmentations

            for augmentation_id in enabled_augmentations:
                _AUGMENTATION_VALIDATORS[augmentation_id](augmentation_data[augmentation_id])

            expected_augmented_data = _get_expected_augmented_data(
                expected_raw_data=expected_raw_data[usage],
                augmentation=augmentation_data,
                device=device,
            )

            for data_id, expected_value in expected_augmented_data.items():
                output_data = batch_samples[data_id][ValueKey.DATA]

                if output_data.dtype == torch.bool:
                    data_deviation = expected_value != output_data
                else:
                    data_deviation = (expected_value - output_data).abs() > 1e-4

                similarity = 100 * (1 - torch.count_nonzero(data_deviation) / expected_value.numel())
                assert similarity >= min_similarity, f"Data similarity for {data_id}: {similarity:.2f}%"
        else:
            assert DataId.AUGMENTATION_DATA not in batch_samples
