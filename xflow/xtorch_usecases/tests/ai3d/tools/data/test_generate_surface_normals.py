"""Tests for surface normal generation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import re
import secrets
import sys
from functools import partial
from pathlib import Path
from shutil import copyfile
from typing import NamedTuple
from unittest.mock import patch

import numpy as np
import pandas as pd
import pytest

from xtorch_usecases.ai3d.config.model import Config
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import CameraName
from xtorch_usecases.ai3d.tools.data.common import AITopoCSVDataId
from xtorch_usecases.ai3d.tools.data.generate_surface_normals import _create_configuration, main


def modify_path(path: str, subfolder: str) -> str:
    """Modify the given path by adding a subfolder to the filename and changing the frame counter randomly."""
    counter = f"f{secrets.randbelow(1000):05}"
    new_path = path.replace("labels/", f"labels/{subfolder}/")
    new_path = re.sub(r"f\d{5}", counter, new_path)
    return new_path


class DatasetPaths(NamedTuple):
    """Paths containing original dataset and test dataset."""

    original: Path
    test: Path


def create_test_dataset(test_folder: Path, camera_name: CameraName) -> DatasetPaths:
    """Create test dataset for the surface normals generation."""
    config_data = _create_configuration(camera_name).data
    assert isinstance(config_data.dataset_base_path, Path)
    dataset_path = config_data.dataset_base_path / config_data.dataset_name
    data_path = config_data.data_base_path

    data = pd.read_csv(dataset_path)
    test_data = data.copy()

    test_data[CSVDataId.GT_DEPTH] = test_data[CSVDataId.GT_DEPTH].apply(lambda x: modify_path(x, subfolder="DEPTH"))
    test_data[CSVDataId.GT_SURFACE_NORMAL] = (
        test_data[CSVDataId.GT_DEPTH]
        .str.replace("DEPTH", AITopoCSVDataId.SURFACE_NORMALS.value)
        .str.replace(".png", ".npy")
    )

    for img_id in [CSVDataId.CURRENT_IMAGE, CSVDataId.PREVIOUS_IMAGE]:
        for img_file in data[img_id]:
            test_img_path = test_folder / img_file
            Path.mkdir(test_img_path.parent, exist_ok=True)
            copyfile(data_path / img_file, test_img_path)

    for depth_file, test_depth_file in zip(data[CSVDataId.GT_DEPTH], test_data[CSVDataId.GT_DEPTH]):
        test_depth_path = test_folder / test_depth_file
        test_depth_path.parent.mkdir(parents=True, exist_ok=True)
        copyfile(data_path / depth_file, test_depth_path)

    for calib_file in data[CSVDataId.SYS_CALIB]:
        test_calib_path = test_folder / calib_file
        test_calib_path.parent.mkdir(parents=True, exist_ok=True)
        copyfile(data_path / calib_file, test_calib_path)

    test_dataset_path = test_folder / dataset_path.name
    test_dataset_path.parent.mkdir(parents=True, exist_ok=True)
    test_data.to_csv(test_dataset_path, index=False)
    return DatasetPaths(original=dataset_path, test=test_dataset_path)


def create_configuration_with_base_path(
    camera: CameraName, dataset_name: str | None = None, base_path: Path | None = None
) -> Config:
    """Create configuration for the surface normals generation with a specific dataset / data base path."""
    configuration = _create_configuration(camera, dataset_name)
    if base_path is not None:
        configuration.data.dataset_base_path = base_path
        configuration.data.data_base_path = base_path
    configuration.data.batch_size_val = 2
    configuration.data.num_workers_val = 2
    return configuration


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/camera/tasks/ai3d"])
@pytest.mark.parametrize("camera", [CameraName.FC1, CameraName.TVFRONT])
def test_generate_surface_normals(camera: CameraName, tmpdir_delete_after: Path) -> None:
    """Test the surface normal generation from parallax dataset CSV."""
    # GIVEN a test parallax dataset for a selected camera
    dataset_paths = create_test_dataset(tmpdir_delete_after, camera)
    test_dataset = pd.read_csv(dataset_paths.test)
    original_dataset = pd.read_csv(dataset_paths.original)

    # WHEN the surface normals are generated (in a temporary path, to avoid overwriting the original dataset)
    with (
        patch(
            "xtorch_usecases.ai3d.tools.data.generate_surface_normals._create_configuration",
            partial(create_configuration_with_base_path, base_path=tmpdir_delete_after),
        ),
        patch.object(sys, "argv", sys.argv[:1] + ["-c", f"{camera.value}", "-o", "-r"]),
    ):
        main()

    # THEN the surface normals are found into the expected path, have the expected values and are dumped in RGB
    for original_path, test_path in zip(
        original_dataset[CSVDataId.GT_SURFACE_NORMAL], test_dataset[CSVDataId.GT_SURFACE_NORMAL]
    ):
        original_surface_normal_map = np.load(dataset_paths.original.parent / original_path)
        test_surface_normal_map = np.load(tmpdir_delete_after / test_path)
        assert np.allclose(original_surface_normal_map, test_surface_normal_map, atol=2e-5)

        test_rgb_path = tmpdir_delete_after / test_path.replace(".npy", ".png")
        assert test_rgb_path.exists(), f"RGB dump for surface normal {test_rgb_path} does not exist."
