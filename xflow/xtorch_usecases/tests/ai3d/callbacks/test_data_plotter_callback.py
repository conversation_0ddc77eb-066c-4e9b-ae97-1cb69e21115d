"""Tests for the AI3D data plotter callback."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import itertools
from dataclasses import dataclass
from pathlib import Path
from typing import Any, NamedTuple, cast
from unittest.mock import MagicMock

import pytest
import torch
from pytorch_lightning import LightningModule, Trainer
from pytorch_lightning.utilities.types import STEP_OUTPUT

from xcontract.camera_models.definitions import CameraModelType, IntrinsicIndex
from xcontract.data.definitions.image import HW
from xcontract.data.definitions.usage import ValueKey
from xtorch.training import Stage
from xtorch.training.training_module import StepOutputKey
from xtorch_usecases.ai3d.callbacks.data_plotter_callback import AI3DDataPlotterCallback
from xtorch_usecases.ai3d.config.model import DEFAULT_ANGLE_SCALE, DEFAULT_TRANSLATION_SCALE
from xtorch_usecases.ai3d.data_formats.camera_calibration.data_loading import CameraMetadataIndex
from xtorch_usecases.ai3d.tasks.parallax.data import CSVDataId, DataId
from xtorch_usecases.ai3d.tasks.parallax.definitions import OUTPUT_SUBSAMPLING_FACTOR, ParallaxHeadOutput
from xtorch_usecases.ai3d.utils.data_plotter import PosePlotter
from xtorch_usecases.common.datasets.alliance.definitions import CameraName


@pytest.fixture(params=[0, 1, 2], name="trainer")
def fixture_trainer(tmpdir_delete_after: str, request: pytest.FixtureRequest) -> Trainer:
    """Trainer used for testing in this module."""

    current_epoch = request.param

    trainer = MagicMock(spec=Trainer)
    trainer.default_root_dir = tmpdir_delete_after
    trainer.current_epoch = current_epoch

    return cast(Trainer, trainer)


@pytest.fixture(name="lightning_module")
def fixture_lightning_module() -> LightningModule:
    """LightningModule used for testing in this module."""
    module = MagicMock(spec=LightningModule)
    return cast(LightningModule, module)


@dataclass
class DataForTesting:
    """Data for testing the AI3D data plotter callback implementation."""

    batch_data: dict[str, Any]
    predictions: STEP_OUTPUT
    rel_output_folder: Path
    batch_idx: int
    data_ids_to_log: list[DataId]


class DummyDepthMap(NamedTuple):
    """Dummy class for depth map data containing only depth map."""

    depth_map: torch.Tensor


class DummySysCalib(NamedTuple):
    """Dummy class for system calibration data containing only intrinsic and camera metadata."""

    intrinsic: torch.Tensor
    camera_metadata: torch.Tensor


def create_dummy_sys_calib(batch_size: int, device: str) -> DummySysCalib:
    """Create a dummy system calibration object."""
    with torch.device(device):
        return DummySysCalib(
            intrinsic=torch.zeros((batch_size, 5), dtype=torch.float32),
            camera_metadata=torch.zeros(batch_size, 3, dtype=torch.int32),
        )


CAMERA_NAME = CameraName.FC1
IMAGE_SIZE = HW(640, 1792)
FULL_IMAGE_SIZE = HW(1280, 2560)

BATCH_SIZES = [1, 4]
BATCH_IDX = [2, 8]


@pytest.fixture(
    params=itertools.product(BATCH_SIZES, BATCH_IDX),
    name="data_plotter_test_data",
)
def generate_data_plotter_test_data(device: str, request: pytest.FixtureRequest) -> DataForTesting:
    """Generate data for testing the AI3DDataPlotterCallback module.

    Creates random input tensors for groundtruth and predicted data.

    Args:
        device: Device to run the tests on.
        request: Pytest request object.
    """

    batch_size, batch_idx = request.param
    height, width = IMAGE_SIZE.height, IMAGE_SIZE.width
    output_height, output_width = height // OUTPUT_SUBSAMPLING_FACTOR, width // OUTPUT_SUBSAMPLING_FACTOR
    full_height, full_width = FULL_IMAGE_SIZE.height, FULL_IMAGE_SIZE.width

    batch_data = {}
    predictions = {}

    with torch.device(device):
        rng = torch.Generator(device=device).manual_seed(42)

        valid_prob = 0.5

        image_path = "dummy/path/ALLIANCE-DC024_HNRG724-D-001_20241219_074842_FC1_f00010_RGB_ContextA_rectified.png"
        batch_data[DataId.CURRENT_IMAGE] = {
            ValueKey.DATA: torch.rand((batch_size, 3, height, width), generator=rng),
            ValueKey.IDENTIFIER: [image_path] * batch_size,
        }
        batch_data[DataId.PREVIOUS_IMAGE] = {ValueKey.DATA: torch.rand((batch_size, 3, height, width), generator=rng)}

        batch_data[DataId.GT_STRUCTURE_PARAMETER_MAP] = {
            ValueKey.DATA: torch.rand((batch_size, 1, height, width), generator=rng)
        }
        # Set bottom half of the structure parameter to invalid
        batch_data[DataId.VALID_STRUCTURE_PARAMETER_MASK] = {
            ValueKey.DATA: torch.ones((batch_size, 1, height, width), dtype=torch.bool)
        }
        batch_data[DataId.VALID_STRUCTURE_PARAMETER_MASK][ValueKey.DATA][:, :, int(height * valid_prob) :, :] = False

        batch_data[CSVDataId.GT_DEPTH] = {
            ValueKey.DATA: DummyDepthMap(
                depth_map=torch.rand((batch_size, 1, full_height, full_width), generator=rng) * 150.0
            )
        }

        batch_data[CSVDataId.SYS_CALIB] = {
            ValueKey.DATA: create_dummy_sys_calib(batch_size, device=device),
            ValueKey.VALID: torch.tensor([True]),
        }
        sys_calib_indices = torch.tensor([IntrinsicIndex.fu, IntrinsicIndex.fv, IntrinsicIndex.u0, IntrinsicIndex.v0])
        batch_data[CSVDataId.SYS_CALIB][ValueKey.DATA].intrinsic[..., sys_calib_indices] = torch.tensor(
            [1246.18, 1246.18, full_height / 2.0, full_width / 2.0], dtype=torch.float32
        )
        metadata_indices = torch.tensor(
            [CameraMetadataIndex.camera_type, CameraMetadataIndex.camera_height, CameraMetadataIndex.camera_width]
        )
        batch_data[CSVDataId.SYS_CALIB][ValueKey.DATA].camera_metadata[..., metadata_indices] = torch.tensor(
            [CameraModelType.CYLINDER, full_height, full_width], dtype=torch.int32
        )

        batch_data[DataId.CAMERA_HEIGHT] = {ValueKey.DATA: torch.tensor([1.0], dtype=torch.float32)}

        batch_data[DataId.GT_SURFACE_NORMALS_MAP] = {
            ValueKey.DATA: torch.rand((batch_size, 3, height, width), generator=rng) * 2.0 - 1.0
        }
        # Set right half of the surface normals map to invalid
        batch_data[DataId.VALID_SURFACE_NORMALS_MASK] = {
            ValueKey.DATA: torch.ones((batch_size, 1, height, width), dtype=torch.bool)
        }
        batch_data[DataId.VALID_SURFACE_NORMALS_MASK][ValueKey.DATA][..., int(width * valid_prob) :] = False

        translation_scales = torch.tensor([2.5, 0.1, 0.05])
        batch_data[DataId.GT_POSE_TRANSLATION] = {
            ValueKey.DATA: torch.rand((batch_size, 3), generator=rng) * translation_scales
        }
        angle_scales = torch.deg2rad(torch.tensor([2.0, 0.4, 0.4]))
        batch_data[DataId.GT_POSE_ANGLE] = {ValueKey.DATA: torch.rand((batch_size, 3), generator=rng) * angle_scales}

        pred_pose_translation = torch.rand((batch_size, 3), generator=rng) * translation_scales
        pred_pose_angle = torch.rand((batch_size, 3), generator=rng) * angle_scales
        parallax_head_output = ParallaxHeadOutput(
            structure_parameter=torch.rand((batch_size, 1, output_height, output_width), generator=rng),
            surface_normals=torch.rand((batch_size, 3, output_height, output_width), generator=rng) * 2.0 - 1.0,
            pose_translation=pred_pose_translation,
            pose_angle=pred_pose_angle,
            normalized_pose_translation=pred_pose_translation / torch.tensor(DEFAULT_TRANSLATION_SCALE),
            normalized_pose_angle=pred_pose_angle / torch.tensor(DEFAULT_ANGLE_SCALE),
        )
        predictions[StepOutputKey.PREDICTIONS] = {"parallax": parallax_head_output}

        data_ids_to_log = [
            DataId.CURRENT_IMAGE,
            DataId.PREVIOUS_IMAGE,
            DataId.GT_STRUCTURE_PARAMETER_MAP,
            DataId.VALID_STRUCTURE_PARAMETER_MASK,
            DataId.GT_DEPTH_MAP,
            DataId.GT_HEIGHT_MAP,
            DataId.GT_SURFACE_NORMALS_MAP,
            DataId.VALID_SURFACE_NORMALS_MASK,
            DataId.GT_POSE_TRANSLATION,
            DataId.GT_POSE_ANGLE,
        ]

    return DataForTesting(
        batch_data=batch_data,
        predictions=predictions,
        rel_output_folder=Path("data_plotter"),
        batch_idx=batch_idx,
        data_ids_to_log=data_ids_to_log,
    )


@pytest.mark.parametrize(
    ("callback_function_epoch_start", "callback_function_batch_end", "logging_frequency", "stage_name"),
    [
        ("on_train_epoch_start", "on_train_batch_end", 2, Stage.TRAIN.name),
        ("on_validation_epoch_start", "on_validation_batch_end", 2, Stage.VALIDATE.name),
    ],
)
def test_data_plotter_saving_callback(
    trainer: Trainer,
    lightning_module: LightningModule,
    data_plotter_test_data: DataForTesting,
    callback_function_epoch_start: str,
    callback_function_batch_end: str,
    logging_frequency: int,
    stage_name: str,
) -> None:
    """Test AI3DDataPlotterCallback for both training and validation callbacks.

    Parameter logging_frequency matches the running batch number (batch_idx) and therefore,
    plots are created.

    Args:
        trainer: Trainer instance.
        lightning_module: LightningModule instance.
        data_plotter_test_data: DataForTesting instance with batch data and predictions.
        callback_function_epoch_start: The callback function to create folder strcuture.
        callback_function_batch_end: The callback function to test for saving images.
        logging_frequency: Frequency of logging.
        stage_name: Stage as string, also expected sub-folder for the plot files.
    """

    # GIVEN all needed objects to create a plot with the AI3DDataPlotterCallback
    batch = data_plotter_test_data.batch_data
    predictions = data_plotter_test_data.predictions
    batch_idx = data_plotter_test_data.batch_idx
    data_ids_to_log = data_plotter_test_data.data_ids_to_log

    data_plotter_callback = AI3DDataPlotterCallback(
        task_id="parallax",
        camera_name=CAMERA_NAME,
        data_ids_to_log=data_ids_to_log,
        output_folder=Path(trainer.default_root_dir) / data_plotter_test_data.rel_output_folder,
        train_data_logging_frequency=logging_frequency,
        val_data_logging_frequency=logging_frequency,
    )

    data_plotter_callback.setup(trainer=trainer, pl_module=lightning_module, stage=stage_name)

    # create epoch folder structure
    callback = getattr(data_plotter_callback, callback_function_epoch_start)
    callback(
        trainer=trainer,
        pl_module=lightning_module,
    )

    # WHEN calling the specified callback function
    callback = getattr(data_plotter_callback, callback_function_batch_end)
    callback(
        trainer=trainer,
        pl_module=lightning_module,
        outputs=predictions,
        batch=batch,
        batch_idx=batch_idx,
    )

    # THEN check if the expected files are created
    folder_path = (
        Path(trainer.default_root_dir)
        / data_plotter_test_data.rel_output_folder
        / stage_name
        / f"epoch_{trainer.current_epoch:03d}"
    )
    pose_plotter = data_plotter_callback._pose_plotter  # noqa: SLF001
    assert isinstance(pose_plotter, PosePlotter)
    plot_path = folder_path / f"prediction_epoch_{trainer.current_epoch:03d}_iter_{batch_idx:05d}.png"
    correlation_path = folder_path / pose_plotter.NAME_POSE_CORRELATION_PLOT
    histogram_path = folder_path / pose_plotter.NAME_POSE_HISTOGRAM_PLOT
    history_path = folder_path / pose_plotter.NAME_POSE_HISTORY_PLOT

    for path in [plot_path, correlation_path, histogram_path, history_path]:
        assert path.exists(), f"Expected file {path} does not exist."
        assert path.is_file(), f"Expected file {path} is not a valid file."


@pytest.mark.parametrize(
    ("callback_function_epoch_start", "callback_function_batch_end", "logging_frequency", "stage_name"),
    [
        ("on_train_epoch_start", "on_train_batch_end", 3, Stage.TRAIN.name),
        ("on_validation_epoch_start", "on_validation_batch_end", 3, Stage.VALIDATE.name),
    ],
)
def test_data_plotter_no_saving_callbacks(
    tmpdir_delete_after: Path,
    trainer: Trainer,
    lightning_module: LightningModule,
    data_plotter_test_data: DataForTesting,
    callback_function_epoch_start: str,
    callback_function_batch_end: str,
    logging_frequency: int,
    stage_name: str,
) -> None:
    """Test AI3DDataPlotterCallback for both training and validation callback.

    Parameter logging_frequency does NOT match the running batch number (batch_idx) and therefore,
    plots are NOT created.

    Args:
        tmpdir_delete_after: Temporary directory to store output files.
        trainer: Trainer instance.
        lightning_module: LightningModule instance.
        data_plotter_test_data: DataForTesting instance with batch data and predictions.
        callback_function_epoch_start: The callback function to create folder strcuture.
        callback_function_batch_end: The callback function to test for saving images.
        logging_frequency: Frequency of logging.
        stage_name: Stage as string, also expected sub-folder for the plot files.
    """

    # GIVEN all needed objects to create a plot with the AI3DDataPlotterCallback
    batch = data_plotter_test_data.batch_data
    predictions = data_plotter_test_data.predictions
    batch_idx = data_plotter_test_data.batch_idx
    output_folder = tmpdir_delete_after / data_plotter_test_data.rel_output_folder
    data_plotter_callback = AI3DDataPlotterCallback(
        task_id="parallax",
        camera_name=CAMERA_NAME,
        data_ids_to_log=[DataId.CURRENT_IMAGE],
        output_folder=output_folder,
        train_data_logging_frequency=logging_frequency,
        val_data_logging_frequency=logging_frequency,
    )

    data_plotter_callback.setup(trainer=trainer, pl_module=lightning_module, stage=stage_name)

    # create epoch folder structure
    callback = getattr(data_plotter_callback, callback_function_epoch_start)
    callback(
        trainer=trainer,
        pl_module=lightning_module,
    )

    # WHEN calling the specified callback function
    callback = getattr(data_plotter_callback, callback_function_batch_end)
    callback(
        trainer=trainer,
        pl_module=lightning_module,
        outputs=predictions,
        batch=batch,
        batch_idx=batch_idx,
    )

    # THEN the expected file is missing
    folder_path = output_folder / stage_name / f"epoch_{trainer.current_epoch:03d}"
    assert not any(folder_path.iterdir()), f"Expected no saved files in {folder_path}, but found some."


@pytest.mark.parametrize(
    ("callback_function_epoch_start", "callback_function_batch_end", "logging_frequency", "stage_name"),
    [
        ("on_train_epoch_start", "on_train_batch_end", 2, Stage.TRAIN.name),
        ("on_validation_epoch_start", "on_validation_batch_end", 2, Stage.VALIDATE.name),
    ],
)
def test_data_plotter_missing_data_callback(
    device: str,
    trainer: Trainer,
    lightning_module: LightningModule,
    callback_function_epoch_start: str,
    callback_function_batch_end: str,
    logging_frequency: int,
    stage_name: str,
) -> None:
    """Test AI3DDataPlotterCallback for both training and validation callbacks.

    Data to be plotted defined by list of DataIds (data_ids_to_log).
    Checks for raising ValueError for missing DataId in batch dictionary.

    Args:
        device: Device to run the tests on.
        trainer: Trainer instance.
        lightning_module: LightningModule instance.
        callback_function_epoch_start: The callback function to create folder strcuture.
        callback_function_batch_end: The callback function to test for saving images.
        logging_frequency: Frequency of logging.
        stage_name: Stage as string, also expected sub-folder for the plot files.
    """

    # GIVEN all needed objects to create a plot with the AI3DDataPlotterCallback
    batch_size = 2
    batch_idx = 2
    data_ids_to_log = [DataId.CURRENT_IMAGE]
    height, width = 640, 1152

    with torch.device(device):
        rng = torch.Generator(device=device).manual_seed(42)

        batch_data: dict[str, Any] = {
            DataId.PREVIOUS_IMAGE: {ValueKey.DATA: torch.rand((batch_size, 3, height, width), generator=rng)},
        }

        parallax_head_output = ParallaxHeadOutput(
            structure_parameter=torch.rand((batch_size, 1, height, width), generator=rng),
        )

        predictions: STEP_OUTPUT = {StepOutputKey.PREDICTIONS: {"parallax": parallax_head_output}}

        data_plotter_callback = AI3DDataPlotterCallback(
            task_id="parallax",
            camera_name=CAMERA_NAME,
            data_ids_to_log=data_ids_to_log,
            output_folder=Path(trainer.default_root_dir) / Path("data_plotter"),
            train_data_logging_frequency=logging_frequency,
            val_data_logging_frequency=logging_frequency,
        )

        data_plotter_callback.setup(trainer=trainer, pl_module=lightning_module, stage=stage_name)

        # create epoch folder structure
        callback = getattr(data_plotter_callback, callback_function_epoch_start)
        callback(
            trainer=trainer,
            pl_module=lightning_module,
        )

        # WHEN calling the specified callback function
        callback = getattr(data_plotter_callback, callback_function_batch_end)
        # THEN a KeyError is raised
        with pytest.raises(KeyError):
            callback(
                trainer=trainer,
                pl_module=lightning_module,
                outputs=predictions,
                batch=batch_data,
                batch_idx=batch_idx,
            )
