"""This module contains test definitions for the Parallax task."""

from __future__ import annotations

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import pytest
import torch

from xtorch_usecases.ai3d.config.model import BACKBONE_ENCODER, DEFAULT_ANGLE_SCALE, DEFAULT_TRANSLATION_SCALE
from xtorch_usecases.ai3d.model.decoder.pwa_decoder import PWAttentionDecoder
from xtorch_usecases.ai3d.network import MultiTaskYoloV4Tiny
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    DecoderId,
    EnabledHeads,
    FeatureFusionId,
    Level,
    ParallaxHeadOutput,
    ParallaxHeadParams,
    PoseFusionId,
    PoseHeadParams,
    PyramidalDecoderParams,
    StructureParameterHeadParams,
    SurfaceNormalsHeadParams,
)


def test_parallax_head_output_initialization() -> None:
    """Test initialization of ParallaxHeadOutput with default values."""
    # GIVEN No parameters
    # WHEN The ParallaxHeadOutput is initialized
    output = ParallaxHeadOutput()

    # THEN All attributes should be None
    assert output.structure_parameter is None
    assert output.surface_normals is None
    assert output.pose_translation is None
    assert output.pose_angle is None
    assert output.normalized_pose_translation is None
    assert output.normalized_pose_angle is None


def test_parallax_head_output_initialization_with_values() -> None:
    """Test initialization of ParallaxHeadOutput with specific values."""
    # GIVEN specific values for the parameters
    structure_param = torch.rand(1, 3, 256, 256)
    surface_normals = torch.rand(1, 3, 256, 256)
    normalized_pose_translation = torch.rand(1, 3)
    normalized_pose_angle = torch.rand(1, 3, 3)
    with torch.device(normalized_pose_angle.device):
        pose_translation = normalized_pose_translation * torch.tensor(DEFAULT_TRANSLATION_SCALE)
        pose_angle = normalized_pose_angle * torch.tensor(DEFAULT_ANGLE_SCALE)

    # WHEN the ParallaxHeadOutput is initialized with these values
    output = ParallaxHeadOutput(
        structure_parameter=structure_param,
        surface_normals=surface_normals,
        pose_translation=pose_translation,
        pose_angle=pose_angle,
        normalized_pose_translation=normalized_pose_translation,
        normalized_pose_angle=normalized_pose_angle,
    )

    # THEN the attributes should match the input values

    assert isinstance(output.structure_parameter, torch.Tensor)
    assert isinstance(output.surface_normals, torch.Tensor)
    assert isinstance(output.pose_translation, torch.Tensor)
    assert isinstance(output.pose_angle, torch.Tensor)
    assert isinstance(output.normalized_pose_translation, torch.Tensor)
    assert isinstance(output.normalized_pose_angle, torch.Tensor)
    assert torch.equal(output.structure_parameter, structure_param)
    assert torch.equal(output.surface_normals, surface_normals)
    assert torch.equal(output.pose_translation, pose_translation)
    assert torch.equal(output.pose_angle, pose_angle)
    assert torch.equal(output.normalized_pose_translation, normalized_pose_translation)
    assert torch.equal(output.normalized_pose_angle, normalized_pose_angle)


def test_parallax_head_output_iter() -> None:
    """Test the __iter__ method of ParallaxHeadOutput."""

    # GIVEN specific values for the parameters
    structure_param = torch.rand(1, 3, 256, 256)
    surface_normals = torch.rand(1, 3, 256, 256)
    normalized_pose_translation = torch.rand(1, 3)
    normalized_pose_angle = torch.rand(1, 3, 3)
    with torch.device(normalized_pose_angle.device):
        pose_translation = normalized_pose_translation * torch.tensor(DEFAULT_TRANSLATION_SCALE)
        pose_angle = normalized_pose_angle * torch.tensor(DEFAULT_ANGLE_SCALE)

    # WHEN the ParallaxHeadOutput is initialized with these values
    output = ParallaxHeadOutput(
        structure_parameter=structure_param,
        surface_normals=surface_normals,
        pose_translation=pose_translation,
        pose_angle=pose_angle,
        normalized_pose_translation=normalized_pose_translation,
        normalized_pose_angle=normalized_pose_angle,
    )

    # THEN the __iter__ method should return an iterator over the items so that all items can be accessed
    items = dict(iter(output))
    assert items["structure_parameter"] is structure_param
    assert items["surface_normals"] is surface_normals
    assert items["pose_translation"] is pose_translation
    assert items["pose_angle"] is pose_angle
    assert items["normalized_pose_translation"] is normalized_pose_translation
    assert items["normalized_pose_angle"] is normalized_pose_angle


def test_parallax_head_output_items() -> None:
    """Test the items method of ParallaxHeadOutput."""
    # GIVEN specific values for the parameters
    structure_param = torch.rand(1, 3, 256, 256)
    surface_normals = torch.rand(1, 3, 256, 256)
    normalized_pose_translation = torch.rand(1, 3)
    normalized_pose_angle = torch.rand(1, 3, 3)
    with torch.device(normalized_pose_angle.device):
        pose_translation = normalized_pose_translation * torch.tensor(DEFAULT_TRANSLATION_SCALE)
        pose_angle = normalized_pose_angle * torch.tensor(DEFAULT_ANGLE_SCALE)

    # WHEN the ParallaxHeadOutput is initialized with these values
    output = ParallaxHeadOutput(
        structure_parameter=structure_param,
        surface_normals=surface_normals,
        pose_translation=pose_translation,
        pose_angle=pose_angle,
        normalized_pose_translation=normalized_pose_translation,
        normalized_pose_angle=normalized_pose_angle,
    )

    # THEN Iterating over the items should yield the expected values
    for key, value in output.items():
        match key:
            case "structure_parameter":
                assert torch.equal(value, structure_param)
            case "surface_normals":
                assert torch.equal(value, surface_normals)
            case "pose_translation":
                assert torch.equal(value, pose_translation)
            case "pose_angle":
                assert torch.equal(value, pose_angle)
            case "normalized_pose_translation":
                assert torch.equal(value, normalized_pose_translation)
            case "normalized_pose_angle":
                assert torch.equal(value, normalized_pose_angle)
            case _:
                pytest.fail(f"Unexpected key: {key}")


def test_parallax_head_output_items_empty() -> None:
    """Test the items method of ParallaxHeadOutput when all attributes are None."""
    # GIVEN No parameters
    output = ParallaxHeadOutput()
    # WHEN the items method is called
    items = dict(output.items())
    # THEN it should return a dictionary with all attributes set to None
    assert items["structure_parameter"] is None
    assert items["surface_normals"] is None
    assert items["pose_translation"] is None
    assert items["pose_angle"] is None
    assert items["normalized_pose_translation"] is None
    assert items["normalized_pose_angle"] is None


def test_post_init_valid_initialization() -> None:
    """Test __post_init__ initializes ParallaxHeadParams correctly with valid parameters."""
    # GIVEN valid parameters for ParallaxHeadParams
    valid_pyramid_levels = Level.stride_16
    valid_backbone_channels = MultiTaskYoloV4Tiny.get_encoder_channels(BACKBONE_ENCODER)
    valid_feature_fusion_id = FeatureFusionId.LOCAL_CROSS_ATTENTION
    valid_feature_fusion_params = {
        "dim_heads": {Level.stride_128: 8, Level.stride_64: 8, Level.stride_32: 8},
        "patch_lengths": {Level.stride_128: 3, Level.stride_64: 3, Level.stride_32: 5},
    }
    valid_pose_fusion_id = PoseFusionId.CHANNELWISE_CROSS_ATTENTION
    valid_pose_fusion_params = {"do_concatenation": False}
    valid_decoder_id = DecoderId.PWA_DECODER
    valid_enabled_heads = EnabledHeads(structure_parameter=True, pose=True, surface_normals=True)
    valid_structure_head_parameter = StructureParameterHeadParams(
        in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3,
    )
    valid_pose_head_parameter = PoseHeadParams(
        in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3,
        translation_scale=DEFAULT_TRANSLATION_SCALE,
        angle_scale=DEFAULT_ANGLE_SCALE,
    )
    valid_surface_normals_head_parameter = SurfaceNormalsHeadParams(in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3)

    params = ParallaxHeadParams(
        output_pyramid_level=valid_pyramid_levels,
        backbone_channels=valid_backbone_channels,
        feature_fusion_id=valid_feature_fusion_id,
        feature_fusion_params=valid_feature_fusion_params,
        pose_fusion_id=valid_pose_fusion_id,
        pose_fusion_params=valid_pose_fusion_params,
        decoder_id=valid_decoder_id,
        enabled_heads=valid_enabled_heads,
        structure_head_parameter=valid_structure_head_parameter,
        pose_head_parameter=valid_pose_head_parameter,
        surface_normals_head_parameter=valid_surface_normals_head_parameter,
    )
    # WHEN the __post_init__ method is executed (implicitly during initialization)

    # THEN the decoder_params should be initialized correctly
    assert isinstance(params.decoder_params, PyramidalDecoderParams)
    assert params.decoder_params.decoder_id == DecoderId.PWA_DECODER
    assert len(params.decoder_params.params.map_fusion_net_at_levels) == 3
    assert len(params.decoder_params.params.pose_fusion_net_at_levels) == 3

    assert isinstance(params.output_pyramid_level, int), "pyramid_levels must be an integer"
    assert params.output_pyramid_level > 0, "pyramid_levels must be greater than 0"

    assert isinstance(params.backbone_channels, list), "backbone_channels must be a list"
    assert all(isinstance(channel, int) for channel in params.backbone_channels), "backbone_channels must be integers"

    assert isinstance(params.feature_fusion_id, FeatureFusionId), "feature_fusion_id must be of type FeatureFusionId"
    assert isinstance(params.feature_fusion_params, dict), "feature_fusion_params must be a dictionary"

    assert isinstance(params.pose_fusion_id, PoseFusionId), "pose_fusion_id must be of type PoseFusionId"
    assert isinstance(params.pose_fusion_params, dict), "pose_fusion_params must be a dictionary"

    assert isinstance(params.decoder_id, DecoderId), "decoder_id must be of type DecoderId"

    assert isinstance(params.enabled_heads, EnabledHeads), "enabled_heads must be of type EnabledHeads"
    assert (
        params.enabled_heads.structure_parameter or params.enabled_heads.pose or params.enabled_heads.surface_normals
    ), "At least one head must be enabled"

    assert isinstance(params.structure_head_parameter, StructureParameterHeadParams), (
        "expecting StructureParameterHeadParams"
    )
    assert isinstance(params.pose_head_parameter, PoseHeadParams), "pose_head_parameter must be of type PoseHeadParams"
    assert isinstance(params.surface_normals_head_parameter, SurfaceNormalsHeadParams), (
        "surface_normals_head_parameter must be of type SurfaceNormalsHeadParams"
    )
