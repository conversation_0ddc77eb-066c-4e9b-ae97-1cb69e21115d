"""Tests Parallax head implementation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import itertools
import math
from dataclasses import dataclass

import pytest
import torch

from xcontract.data.definitions.image import HW
from xtorch.nn.backbones.image.yolo_v4_tiny.yolo_v4_tiny import YoloV4TinyOutput as BackboneOutput
from xtorch_usecases.ai3d.camera.pixel_operations import pixel_map_to_masked_warping_grid
from xtorch_usecases.ai3d.model.decoder.pose_head import PoseHeadParams
from xtorch_usecases.ai3d.model.decoder.pwa_decoder import Level, PWAttentionDecoder
from xtorch_usecases.ai3d.model.decoder.structure_parameter_head import StructureParameterHeadParams
from xtorch_usecases.ai3d.model.decoder.surface_normals_head import SurfaceNormalsHeadParams
from xtorch_usecases.ai3d.model.definitions import DecoderId, FeatureFusionId, PoseFusionId
from xtorch_usecases.ai3d.model.encoder.ai3d_pyramidal_encoder import AI3D_BACKBONE_CHANNELS
from xtorch_usecases.ai3d.model.sub_modules.pose_fusion_subnets import _TOTAL_POSE_DOFS
from xtorch_usecases.ai3d.network import YOLO_BACKBONE_CHANNELS
from xtorch_usecases.ai3d.tasks.parallax.definitions import (
    EnabledHeads,
    ParallaxHeadInput,
    ParallaxHeadOutput,
    ParallaxHeadParams,
    ParallaxHeadSpecificInputs,
)
from xtorch_usecases.ai3d.tasks.parallax.head import ParallaxHead


@dataclass
class DataForTesting:
    """Data for testing the Parallax head implementation."""

    inputs: ParallaxHeadInput
    expected_output: ParallaxHeadOutput
    parameter: ParallaxHeadParams


TRANSLATION_SCALE = (1.0, 2.0, 3.0)  # in meters
ANGLE_SCALE = (4.0, 2.0, 7.0)  # in radians


HEAD_DIMENSIONS = [
    {Level.stride_128: 8, Level.stride_64: 8, Level.stride_32: 8},
    {Level.stride_128: 16, Level.stride_64: 16, Level.stride_32: 16},
    {Level.stride_128: 8, Level.stride_64: 8, Level.stride_32: 16},
]
PATCH_LENGTH = [
    {Level.stride_128: 3, Level.stride_64: 3, Level.stride_32: 3},
    {Level.stride_128: 5, Level.stride_64: 5, Level.stride_32: 5},
    {Level.stride_128: 3, Level.stride_64: 3, Level.stride_32: 5},
]
BATCH_SIZES = [1, 2, 4]
IMAGE_SIZES = [HW(640, 1152)]
DO_CONCATENATION = [True, False]

# ordering from bottom-to-top [stride_4, stride_8, ..., stride_128]
ENCODER_CHANNELS = [YOLO_BACKBONE_CHANNELS, AI3D_BACKBONE_CHANNELS]


@pytest.fixture(
    params=itertools.product(
        HEAD_DIMENSIONS, PATCH_LENGTH, BATCH_SIZES, IMAGE_SIZES, DO_CONCATENATION, ENCODER_CHANNELS
    ),
    name="head_test_data",
)
def generate_head_test_data(device: str, request: pytest.FixtureRequest) -> DataForTesting:
    """Generate data for testing the ParallaxHead() module.

    Creates random input tensors and expected output tensor for testing
    the ParallaxHead() module with respect to the tensor shape.

    The input tensors are created with random values and their shapes matches the pyramid levels of the
    YOLOV4-tiny encoder.

    The expected output tensor is created with random values and its shape matches the expected output
    of the single AI3D heads (structure paramater head, pose head, surface normals head).

    Args:
        device: Device to run the tests on.
        request: Pytest request object.
    """

    dim_heads, patch_lengths, batch_size, image_size, do_concatenation, backbone_channels = request.param
    height, width = image_size.height, image_size.width

    input_pyramid_features_ref: dict[str, torch.Tensor] = {}
    input_pyramid_features_prev: dict[str, torch.Tensor] = {}

    with torch.device(device):
        rng = torch.Generator(device=device).manual_seed(42)

        # creates the pyramidal input features for the parallax head
        for stride_level, channels in zip(Level, backbone_channels):
            level_features_ref = torch.rand(
                (batch_size, channels, math.ceil(height / stride_level), math.ceil(width / stride_level)), generator=rng
            )
            level_features_prev = torch.rand(
                (batch_size, channels, math.ceil(height / stride_level), math.ceil(width / stride_level)), generator=rng
            )
            input_pyramid_features_ref[stride_level.name] = level_features_ref
            input_pyramid_features_prev[stride_level.name] = level_features_prev

        # warping grid is expected in the first lower resolution level
        warping_stride = next(iter(sorted(dim_heads.keys())))
        warping_width = width // warping_stride
        warping_height = height // warping_stride
        u = torch.arange(start=0.5, end=warping_width)  #  [0.5, width  - 0.5]
        v = torch.arange(start=0.5, end=warping_height)  # [0.5, height - 0.5]
        map_u, map_v = torch.meshgrid(u, v, indexing="xy")
        grid_map_coords = torch.stack([torch.stack([map_u, map_v], dim=-1)] * batch_size, dim=0)
        masked_warping_grid = pixel_map_to_masked_warping_grid(grid_map_coords, warping_height, warping_width)

        # pose
        relative_pose = torch.rand((batch_size, _TOTAL_POSE_DOFS), generator=rng)

        # create the input for the decoder
        specific_inputs = ParallaxHeadSpecificInputs(
            previous_features=BackboneOutput(**input_pyramid_features_prev),
            relative_pose=relative_pose,
            masked_warping_grid=masked_warping_grid,
        )

        parallax_head_input = (BackboneOutput(**input_pyramid_features_ref), specific_inputs)

        # create expected output tensors for the heads
        # Structure parameter head output
        stride_out = list(Level)[-len(dim_heads) - 1]
        output_dimension = HW(math.ceil(height / stride_out), math.ceil(width / stride_out))
        target_structure_parameter = torch.rand(
            (batch_size, 1, output_dimension.height, output_dimension.width), generator=rng
        )
        # Pose head outputs
        target_translation = torch.rand((batch_size, 3), generator=rng)
        target_angles = torch.rand((batch_size, 3), generator=rng)
        # Surface normal head outputs
        target_surface_normals = torch.rand(
            (batch_size, 3, output_dimension.height, output_dimension.width), generator=rng
        )

        head_output = ParallaxHeadOutput()
        head_output.structure_parameter = target_structure_parameter
        head_output.pose_translation = target_translation
        head_output.pose_angle = target_angles
        head_output.normalized_pose_translation = target_translation / torch.tensor(TRANSLATION_SCALE)
        head_output.normalized_pose_angle = target_angles / torch.tensor(ANGLE_SCALE)
        head_output.surface_normals = target_surface_normals

        # configuration for the AI3D parallax task head
        head_params = ParallaxHeadParams(
            output_pyramid_level=stride_out,
            backbone_channels=backbone_channels,
            feature_fusion_id=FeatureFusionId.LOCAL_CROSS_ATTENTION,
            feature_fusion_params={"dim_heads": dim_heads, "patch_lengths": patch_lengths},
            pose_fusion_id=PoseFusionId.CHANNELWISE_CROSS_ATTENTION,
            pose_fusion_params={"do_concatenation": do_concatenation},
            decoder_id=DecoderId.PWA_DECODER,
            enabled_heads=EnabledHeads(structure_parameter=True, pose=True, surface_normals=True),
            structure_head_parameter=StructureParameterHeadParams(in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3),
            pose_head_parameter=PoseHeadParams(
                in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3,
                translation_scale=TRANSLATION_SCALE,
                angle_scale=ANGLE_SCALE,
            ),
            surface_normals_head_parameter=SurfaceNormalsHeadParams(in_channels=PWAttentionDecoder.SE_CONV_CHANNELS_3),
        )

    return DataForTesting(
        inputs=parallax_head_input,
        expected_output=head_output,
        parameter=head_params,
    )


def check_backpropagation(parallax_head: ParallaxHead, output: ParallaxHeadOutput) -> None:
    """Check that the gradients of the block computed on a simple sum of the outputs do not contain any NaN values.

    Args:
        parallax_head: The ParallaxHead submodule.
        output: The output for which the gradient shall be calculated.
    """
    output_sum = [torch.sum(tensor) for tensor in output.__dict__.values() if tensor is not None]

    total_sum = torch.stack(output_sum, dim=0).sum()
    total_sum.backward()

    grads = [
        param.grad.view(-1) for param in parallax_head.parameters() if param.requires_grad and param.grad is not None
    ]

    assert all(not torch.any(torch.isnan(grad)).item() for grad in grads), "NaN values found in gradients."


def test_head_forward(device: str, head_test_data: DataForTesting) -> None:
    """Tests the forward pass and output tensor shape returned by ParallaxHead() module."""

    # GIVEN an input tensor and expected output tensor shape
    parallax_inputs = head_test_data.inputs
    parallax_head_params = head_test_data.parameter
    expected_heads_output = head_test_data.expected_output

    # WHEN performing ParallaxHead
    with torch.device(device):
        parallax_head = ParallaxHead(parallax_head_params)
        heads_ouput = parallax_head(parallax_inputs, {})

    # THEN the shape of the output should match the expected shape
    for head_id, head_output in heads_ouput.__dict__.items():
        if head_output is not None:
            expected_head_output = getattr(expected_heads_output, head_id)
            assert head_output.shape == expected_head_output.shape, (
                f"Shape mismatch: expected {expected_head_output.shape}, got {head_output.shape}"
            )

    # THEN the gradients should not contain any NaN values
    check_backpropagation(parallax_head, heads_ouput)

    # THEN the metric pose outputs are scaled according to the translation and angle scales
    normalized_translation = heads_ouput.normalized_pose_translation
    normalized_angles = heads_ouput.normalized_pose_angle
    translation = heads_ouput.pose_translation
    angles = heads_ouput.pose_angle
    with torch.device(device):
        expected_translation = normalized_translation * torch.tensor(TRANSLATION_SCALE)
        expected_angles = normalized_angles * torch.tensor(ANGLE_SCALE)
    assert torch.allclose(translation, expected_translation)
    assert torch.allclose(angles, expected_angles)
