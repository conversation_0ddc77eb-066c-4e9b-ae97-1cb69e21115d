"""Tests the implementation of the dataset for traffic sign classification gen1."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from pathlib import Path
from typing import cast

import pytest
import torch

from xcontract.data.definitions.usage import ValueKey
from xtorch_usecases.traffic_sign_classification.data.augmentations import AugmentationParameters
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel, ImageDict, LabelDict
from xtorch_usecases.traffic_sign_classification.data.dataset_gen1 import (
    TrafficSignClassificationDatasetGen1,
)

torch.manual_seed(42)

INPUT_CHANNELS = 3
INPUT_HEIGHT = 32
INPUT_WIDTH = 32
INPUT_SIZE = [INPUT_HEIGHT, INPUT_WIDTH]
INPUT_SHAPE = [INPUT_CHANNELS, INPUT_HEIGHT, INPUT_WIDTH]


@pytest.fixture
def test_data_dir_path(datadir: Path) -> Path:
    """Get the path to the test_data/traffic_sign_classification directory."""
    return datadir / "traffic_sign_classification"


@pytest.fixture
def test_dataset_path(test_data_dir_path: Path) -> Path:
    """Get the path to the dataset csv file."""
    return test_data_dir_path / "test_dataset.csv"


@pytest.fixture
def augmentation_params() -> AugmentationParameters:
    """Create a AugmentationParameters object."""
    return AugmentationParameters(
        min_rotation_angle_deg=-15.0,
        max_rotation_angle_deg=15.0,
    )


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
@pytest.mark.parametrize(("is_pretrain"), [False, True])
def test_dataset_len(
    test_dataset_path: Path,
    is_pretrain: bool,  # noqa: FBT001
    augmentation_params: AugmentationParameters,
) -> None:
    """Test the datasets len() call."""
    # GIVEN a dataset object
    assert test_dataset_path.exists()
    dataset = TrafficSignClassificationDatasetGen1(
        dataset_csv_path=test_dataset_path,
        input_size=INPUT_SIZE,
        is_pretrain=is_pretrain,
        augmentation_params=augmentation_params,
    )

    # WHEN calling len() on the dataset
    # THEN the dataset should hold 10 datapoints
    assert len(dataset) == 10


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
@pytest.mark.parametrize(("is_pretrain"), [False, True])
def test_dataset_getter(
    test_dataset_path: Path,
    is_pretrain: bool,  # noqa: FBT001
    augmentation_params: AugmentationParameters,
) -> None:
    """Test the datasets operator[]."""
    # GIVEN a dataset object
    assert test_dataset_path.exists()
    dataset = TrafficSignClassificationDatasetGen1(
        dataset_csv_path=test_dataset_path,
        input_size=INPUT_SIZE,
        is_pretrain=is_pretrain,
        augmentation_params=augmentation_params,
    )

    # WHEN accessing the first data point
    data_point = dataset[0]

    # THEN a ImageAndLabel tuple should be returned
    assert isinstance(data_point, ImageAndLabel), "Data point should be an ImageAndLabel tuple."
    validate_data_point_format(image=data_point.image, labels=data_point.label)


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
@pytest.mark.parametrize(("is_pretrain"), [False, True])
def test_dataset_iter(
    test_dataset_path: Path,
    is_pretrain: bool,  # noqa: FBT001
    augmentation_params: AugmentationParameters,
) -> None:
    """Test the datasets iterator."""
    # GIVEN a dataset object
    assert test_dataset_path.exists()
    dataset = TrafficSignClassificationDatasetGen1(
        dataset_csv_path=test_dataset_path,
        input_size=INPUT_SIZE,
        is_pretrain=is_pretrain,
        augmentation_params=augmentation_params,
    )

    # WHEN iterating over the data points
    for data_point in dataset:
        # THEN for each data point a ImageAndLabel tuple should be returned
        assert isinstance(data_point, ImageAndLabel), "Data point should be an ImageAndLabel tuple."
        validate_data_point_format(image=data_point.image, labels=data_point.label)


def validate_data_point_format(image: ImageDict, labels: LabelDict) -> None:
    """Validate the format of a data point returned by the dataset.

    Checks that image and label dictionaries have the correct structure and types.
    """
    assert isinstance(image, dict), "Image should be a dict."
    assert isinstance(image[ValueKey.DATA], torch.Tensor), "Image data should be a torch.Tensor."
    assert isinstance(image[ValueKey.IDENTIFIER], str), "Image identifier should be a str."

    assert isinstance(labels, dict), "Label should be a dict."
    assert isinstance(labels[ValueKey.DATA], torch.Tensor), "Label data should be a torch.Tensor"
    assert isinstance(labels[ValueKey.IDENTIFIER], str), "Label identifier should be a str."


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
def test_dataset_pretrain(
    test_dataset_path: Path,
    augmentation_params: AugmentationParameters,
) -> None:
    """Test the datasets image creation for the pretraining stage."""
    # GIVEN a dataset object
    assert test_dataset_path.exists()
    dataset = TrafficSignClassificationDatasetGen1(
        dataset_csv_path=test_dataset_path,
        input_size=INPUT_SIZE,
        is_pretrain=True,
        augmentation_params=augmentation_params,
    )

    # WHEN accessing the first data point
    image, _ = dataset[0]

    # THEN the image shape is as expected (doubled image width for pretrain)
    assert cast(torch.Tensor, image[ValueKey.DATA]).shape == torch.Size([INPUT_CHANNELS, INPUT_HEIGHT, INPUT_WIDTH * 2])


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
def test_dataset_train(
    test_dataset_path: Path,
    augmentation_params: AugmentationParameters,
) -> None:
    """Test the datasets image creation for the training stage."""
    # GIVEN a dataset object
    assert test_dataset_path.exists()
    dataset = TrafficSignClassificationDatasetGen1(
        dataset_csv_path=test_dataset_path,
        input_size=INPUT_SIZE,
        is_pretrain=False,
        augmentation_params=augmentation_params,
    )

    # WHEN accessing the first data point
    image, _ = dataset[0]

    # THEN the image shape is as expected
    assert cast(torch.Tensor, image[ValueKey.DATA]).shape == torch.Size(INPUT_SHAPE)
