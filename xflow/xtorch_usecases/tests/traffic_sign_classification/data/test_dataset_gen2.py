"""Tests the implementation of the dataset for traffic sign classification gen2."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON>sch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections import defaultdict
from pathlib import Path
from typing import cast

import pytest
import torch
from ts_label_mapping.classifier import ClassMappings

from data_formats.traffic_sign.attribute_extractor.attribute_extractor import Weekday
from xcontract.data.definitions.usage import ValueKey
from xtorch_usecases.traffic_sign_classification.data.augmentations import AugmentationParameters
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageDict, LabelDict
from xtorch_usecases.traffic_sign_classification.data.dataset_gen2 import (
    TrafficSignClassificationDatasetGen2,
)
from xtorch_usecases.traffic_sign_classification.definitions import SpecialLabels, SpecialLabelsOCR

torch.manual_seed(42)

INPUT_CHANNELS = 3
INPUT_HEIGHT = 32
INPUT_WIDTH = 32
INPUT_SIZE = [INPUT_HEIGHT, INPUT_WIDTH]
INPUT_SHAPE = [INPUT_CHANNELS, INPUT_HEIGHT, INPUT_WIDTH]

LABEL_INDICES = {
    "CLASS_ID_0_IDX": 0,
    "CLASS_VALUE_0_IDX": 1,
    "CLASS_ID_1_IDX": 2,
    "CLASS_VALUE_1_IDX": 3,
    "CLASS_ID_2_IDX": 4,
    "CLASS_VALUE_2_IDX": 5,
    "CLASS_ID_3_IDX": 6,
    "CLASS_VALUE_3_IDX": 7,
    "FULL_TEXT_IDX": 8,
    "DISTANCE_FROM_IDX": 9,
    "DISTANCE_TO_IDX": 10,
    "DISTANCE_IDX": 11,
    "TIME_START_0_IDX": 12,
    "TIME_END_0_IDX": 13,
    "TIME_START_1_IDX": 14,
    "TIME_END_1_IDX": 15,
    "TIME_START_2_IDX": 16,
    "TIME_END_2_IDX": 17,
    "WEEKDAY_0_IDX": 18,
    "WEEKDAY_1_IDX": 19,
    "WEEKDAY_2_IDX": 20,
}

SOMETHING = 0
NOTHING = 1


@pytest.fixture
def class_mapping() -> dict[str, int]:
    """Get class mapping conversions."""
    class_mappings = ClassMappings()
    return class_mappings.get_name_to_id_mapping(country_code="ALL")


@pytest.fixture
def test_data_dir_path(datadir: Path) -> Path:
    """Get the path to the test_data/traffic_sign_classification directory."""
    return datadir / "traffic_sign_classification"


@pytest.fixture
def dataset_csv_path(test_data_dir_path: Path, dataset_name: str) -> Path:
    """Parameterized fixture to get the path to any dataset CSV file."""
    return test_data_dir_path / f"test_dataset_{dataset_name}.csv"


@pytest.fixture
def dataset_csv_path_gen1(test_data_dir_path: Path) -> Path:
    """Parameterized fixture to get the path to any dataset CSV file."""
    return test_data_dir_path / "test_dataset.csv"


@pytest.fixture
def dataset_csv_path_gen2(test_data_dir_path: Path) -> Path:
    """Parameterized fixture to get the path to any dataset CSV file."""
    return test_data_dir_path / "test_dataset_gen2.csv"


@pytest.fixture
def augmentation_params() -> AugmentationParameters:
    """Create an AugmentationParameters object."""
    return AugmentationParameters(
        min_rotation_angle_deg=-15.0,
        max_rotation_angle_deg=15.0,
    )


@pytest.fixture
def dataset(
    dataset_csv_path_gen1: Path,
    is_pretrain: bool,  # noqa: FBT001
    augmentation_params: AugmentationParameters,
) -> TrafficSignClassificationDatasetGen2:
    """Initialize and return the dataset."""
    assert dataset_csv_path_gen1.exists(), f"Dataset path {dataset_csv_path_gen1} does not exist."
    return TrafficSignClassificationDatasetGen2(
        dataset_csv_path=dataset_csv_path_gen1,
        input_size=INPUT_SIZE,
        is_pretrain=is_pretrain,
        augmentation_params=augmentation_params,
    )


@pytest.fixture
def dataset_gen2(
    dataset_csv_path_gen2: Path,
    augmentation_params: AugmentationParameters,
) -> TrafficSignClassificationDatasetGen2:
    """Initialize and return the dataset."""
    assert dataset_csv_path_gen2.exists(), f"Dataset path {dataset_csv_path_gen1} does not exist."
    return TrafficSignClassificationDatasetGen2(
        dataset_csv_path=dataset_csv_path_gen2,
        input_size=INPUT_SIZE,
        is_pretrain=False,
        augmentation_params=augmentation_params,
    )


def validate_data_point_format(image: ImageDict, labels: LabelDict) -> None:
    """Validate the format of a data point returned by the dataset.

    Checks that image and label dictionaries have the correct structure and types.
    Also validates the tensor shapes of the labels.
    """
    assert isinstance(image, dict), "Image should be a dict."
    assert isinstance(image[ValueKey.DATA], torch.Tensor), "Image data should be a torch.Tensor."
    assert isinstance(image[ValueKey.IDENTIFIER], str), "Image identifier should be a str."

    assert isinstance(labels, dict), "Label needs to be a dict."
    assert isinstance(labels[ValueKey.IDENTIFIER], str), "Labels identifier should be a str."
    label_data = labels[ValueKey.DATA]
    assert isinstance(label_data, tuple), "Label data need to be a tuple of torch.Tensor"

    validate_tensor_shapes(label_data, max_text_length=1)


def validate_tensor_shapes(
    tensors: tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]], max_text_length: int
) -> None:
    """Generic function to validate tensor shapes."""
    expected_shapes = [
        torch.Size([]),
        torch.Size([max_text_length]),
        torch.Size([]),
        torch.Size([max_text_length]),
        torch.Size([]),
        torch.Size([max_text_length]),
        torch.Size([]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([max_text_length]),
        torch.Size([len(Weekday)]),
        torch.Size([len(Weekday)]),
        torch.Size([len(Weekday)]),
    ]
    expected_shape_nothing_indicator = torch.Size([])

    tensors_output, tensors_nothing_indicators = tensors

    assert len(tensors_output) == len(expected_shapes), (
        f"Expected {len(expected_shapes)} tensors, but got {len(tensors_output)}"
    )
    for i, (tensor, tensor_nothing_indicator, expected_shape) in enumerate(
        zip(tensors_output, tensors_nothing_indicators, expected_shapes)
    ):
        assert tensor.shape == expected_shape, f"Shape of tensor {i} is {tensor.shape}, but expected {expected_shape}"
        assert tensor_nothing_indicator.shape == expected_shape_nothing_indicator, (
            f"Nothing indicator tensor {i} should be {tensor_nothing_indicator.shape}, but got "
            f"{tensor_nothing_indicator.shape}"
        )


def validate_labels(
    labels: tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]],
    max_text_length: int,
    label_checks_single_value: dict[str, tuple[str | int, int]],
    label_checks_word_value: dict[str, tuple[str | int, int]],
    label_checks_array_value: dict[str, tuple[torch.Tensor, int]],
    expected_token_lookup: dict[int, str],
    class_mapping: dict[str, int],
) -> None:
    """Validates the labels tensor against expected values.

    Args:
        labels: tuple of tensors containing the labels to be validated.
        max_text_length: Maximum length of text labels.
        label_checks_single_value: Dictionary specifying expected single value labels.
        label_checks_word_value: Dictionary specifying expected word value labels.
        label_checks_array_value: Dictionary specifying expected array value labels.
        expected_token_lookup: Mapping from token indices to expected characters.
        class_mapping: Mapping from class names to IDs.
    """
    validate_tensor_shapes(labels, max_text_length=max_text_length)
    validate_single_value_labels(labels, label_checks_single_value, class_mapping)
    validate_word_value_labels(labels, label_checks_word_value, expected_token_lookup)
    validate_array_value_labels(labels, label_checks_array_value)


def validate_single_value_labels(
    labels: tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]],
    label_checks_single_value: dict[str, tuple[str | int, int]],
    class_mapping: dict[str, int],
) -> None:
    """Validates single value labels against expected values.

    Args:
        labels: Tensor containing the labels to be validated.
        label_checks_single_value: Dictionary specifying expected single value labels.
        class_mapping: Mapping from class names to IDs.
    """
    labels_output, labels_nothing_indicators = labels
    for label_key, expected in label_checks_single_value.items():
        expected_value, expected_nothing_indicator = expected
        idx = LABEL_INDICES[label_key]
        actual_value = labels_output[idx]
        actual_nothing_indicator = labels_nothing_indicators[idx]

        if expected_nothing_indicator == NOTHING:
            assert actual_nothing_indicator == NOTHING, (
                f"{label_key} nothing indicator mismatch: expected {expected_nothing_indicator}, "
                f"got {actual_nothing_indicator}."
            )
            # Actual value irrelevant
            return

        assert actual_nothing_indicator == SOMETHING, (
            f"{label_key} nothing indicator mismatch: expected {SOMETHING}, got {actual_nothing_indicator}."
        )

        if isinstance(expected_value, int):
            assert actual_value == expected_value, (
                f"{label_key} mismatch: expected {expected_value}, got {actual_value}."
            )
        else:
            assert isinstance(expected_value, str)
            assert actual_value == class_mapping[expected_value], (
                f"{label_key} mismatch: expected {expected_value}, which is id {class_mapping[expected_value]}, "
                f"got {actual_value}."
            )


def validate_word_value_labels(
    labels: tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]],
    label_checks_word_value: dict[str, tuple[str | int, int]],
    expected_token_lookup: dict[int, str],
) -> None:
    """Validates word value labels in the labels tensor.

    Args:
        labels: Tensor containing the labels to be validated.
        label_checks_word_value: Dictionary specifying expected word value labels.
        expected_token_lookup: Mapping from token indices to expected characters.
    """
    labels_output, labels_nothing_indicators = labels
    for label_key, expected in label_checks_word_value.items():
        expected_value, expected_nothing_indicator = expected
        idx = LABEL_INDICES[label_key]
        actual_value = labels_output[idx]
        actual_nothing_indicator = labels_nothing_indicators[idx]

        if expected_nothing_indicator == NOTHING:
            assert actual_nothing_indicator == NOTHING, (
                f"{label_key} nothing indicator mismatch: expected {expected_nothing_indicator}, "
                f"got {actual_nothing_indicator}."
            )
            # Actual value irrelevant
            return

        assert actual_nothing_indicator == SOMETHING, (
            f"{label_key} nothing indicator mismatch: expected {SOMETHING}, got {actual_nothing_indicator}."
        )

        if isinstance(expected_value, int):
            assert torch.all(actual_value == expected_value), (
                f"{label_key} mismatch: expected {expected_value}, got {actual_value}."
            )
        else:
            assert isinstance(expected_value, str)
            for i in range(len(expected_value)):
                token = int(actual_value[i].item())
                actual_char = expected_token_lookup[token]
                assert actual_char == expected_value[i], (
                    f"{label_key} mismatch: expected {expected_value[i]}, got {actual_char}."
                )
            assert torch.all(actual_value[len(expected_value) :] == SpecialLabelsOCR.PADDING_LABEL.value), (
                f"{label_key} mismatch: expected padding, got {actual_value[len(expected_value) :]}."
            )


def validate_array_value_labels(
    labels: tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]],
    label_checks_array_value: dict[str, tuple[torch.Tensor, int]],
) -> None:
    """Validates array value labels in the labels tensor.

    Args:
        labels: Tensor containing the labels to be validated.
        label_checks_array_value: Dictionary specifying expected array value labels.
    """
    labels_output, labels_nothing_indicators = labels
    for label_key, expected in label_checks_array_value.items():
        expected_value, expected_nothing_indicator = expected
        idx = LABEL_INDICES[label_key]
        actual_value = labels_output[idx]
        actual_nothing_indicator = labels_nothing_indicators[idx]

        if expected_nothing_indicator == NOTHING:
            assert actual_nothing_indicator == NOTHING, (
                f"{label_key} nothing indicator mismatch: expected {expected_nothing_indicator}, "
                f"got {actual_nothing_indicator}."
            )
            # Actual value irrelevant
            return

        assert actual_nothing_indicator == SOMETHING, (
            f"{label_key} nothing indicator mismatch: expected {SOMETHING}, got {actual_nothing_indicator}."
        )

        if isinstance(expected_value, int):
            assert torch.all(actual_value == expected_value), (
                f"{label_key} mismatch: expected {expected_value}, got {actual_value}."
            )
        else:
            assert isinstance(expected_value, torch.Tensor)
            assert torch.equal(expected_value, actual_value), (
                f"{label_key} mismatch: expected {expected_value}, got {actual_value}."
            )


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
@pytest.mark.parametrize("is_pretrain", [False, True])
def test_dataset_len(
    dataset_csv_path_gen1: Path,
    is_pretrain: bool,  # noqa: FBT001
    augmentation_params: AugmentationParameters,
) -> None:
    """Test the dataset's len() method."""
    # GIVEN a dataset object
    dataset = TrafficSignClassificationDatasetGen2(
        dataset_csv_path=dataset_csv_path_gen1,
        input_size=INPUT_SIZE,
        is_pretrain=is_pretrain,
        augmentation_params=augmentation_params,
    )

    # WHEN calling len() on the dataset
    # THEN the dataset should hold 10 datapoints
    assert len(dataset) == 10, "Dataset length should be 10."


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
@pytest.mark.parametrize("is_pretrain", [False, True])
def test_dataset_getter(
    dataset: TrafficSignClassificationDatasetGen2,
) -> None:
    """Test the dataset's getter method."""
    # GIVEN a dataset object
    # (Dataset is initialized via fixture)

    # WHEN accessing the first data point
    # THEN for each data point two objects should be returned. First is a tensor of an image, second is a tuple of
    # label tensors of correct shape
    assert len(dataset[0]) == 2, "Each data point should return (image, labels)."
    image, labels = dataset[0]

    validate_data_point_format(image, labels)


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
@pytest.mark.parametrize("is_pretrain", [False, True])
def test_dataset_iter(
    dataset: TrafficSignClassificationDatasetGen2,
) -> None:
    """Test the dataset's iterator."""
    # GIVEN a dataset object
    # (Dataset is initialized via fixture)

    # WHEN iterating over the data points
    for data_point in dataset:
        # THEN for each data point two objects should be returned. First is a tensor of an image, second is a tuple of
        # label tensors of correct shape
        assert len(data_point) == 2, "Each data point should return (image, labels)."
        image, labels = data_point

        validate_data_point_format(image, labels)


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
def test_dataset_pretrain(
    dataset_csv_path_gen1: Path,
    augmentation_params: AugmentationParameters,
) -> None:
    """Test dataset image shape during pretraining."""
    # GIVEN a dataset object
    dataset = TrafficSignClassificationDatasetGen2(
        dataset_csv_path=dataset_csv_path_gen1,
        input_size=INPUT_SIZE,
        is_pretrain=True,
        augmentation_params=augmentation_params,
    )

    # WHEN accessing the first data point
    image, _ = dataset[0]

    # THEN the image shape is as expected (doubled image width for pretrain)
    assert cast(torch.Tensor, image[ValueKey.DATA]).shape == torch.Size(
        [INPUT_CHANNELS, INPUT_HEIGHT, INPUT_WIDTH * 2]
    ), "Pretrain image shape is incorrect."


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
def test_dataset_train(
    dataset_csv_path_gen1: Path,
    augmentation_params: AugmentationParameters,
) -> None:
    """Test dataset image shape during training."""
    # GIVEN a dataset object
    dataset = TrafficSignClassificationDatasetGen2(
        dataset_csv_path=dataset_csv_path_gen1,
        input_size=INPUT_SIZE,
        is_pretrain=False,
        augmentation_params=augmentation_params,
    )

    # WHEN accessing the first data point
    image, _ = dataset[0]

    # THEN the image shape is as expected
    assert cast(torch.Tensor, image[ValueKey.DATA]).shape == torch.Size(INPUT_SHAPE), "Train image shape is incorrect."


@pytest.mark.parametrize(
    (
        "dataset_name",
        "expected_token_lookup",
        "max_text_length",
        "label_checks_single_value",
        "label_checks_word_value",
        "label_checks_array_value",
    ),
    [
        (
            "distance",
            {0: "", 1: "", 2: "0", 3: "1", 4: "m"},
            4,
            {
                "CLASS_ID_0_IDX": ("Distance_AddSign", SOMETHING),
                "CLASS_ID_1_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_2_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_3_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
            },
            {
                "CLASS_VALUE_0_IDX": (SpecialLabelsOCR.PADDING_LABEL.value, SOMETHING),
                "CLASS_VALUE_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_3_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "FULL_TEXT_IDX": ("100m", SOMETHING),
                "DISTANCE_FROM_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "DISTANCE_TO_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "DISTANCE_IDX": ("100m", SOMETHING),
                "TIME_START_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
            },
            {
                "WEEKDAY_0_IDX": (1, NOTHING),
                "WEEKDAY_1_IDX": (1, NOTHING),
                "WEEKDAY_2_IDX": (1, NOTHING),
            },
        ),
        (
            "distsect",
            {0: "", 1: "", 2: "-", 3: "0", 4: "2", 5: "8", 6: "m"},
            8,
            {
                "CLASS_ID_0_IDX": ("DistanceSection_AddSign", SOMETHING),
                "CLASS_ID_1_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_2_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_3_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
            },
            {
                "CLASS_VALUE_0_IDX": (SpecialLabelsOCR.PADDING_LABEL.value, SOMETHING),
                "CLASS_VALUE_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_3_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "FULL_TEXT_IDX": ("200-800m", SOMETHING),
                "DISTANCE_FROM_IDX": ("200", SOMETHING),
                "DISTANCE_TO_IDX": ("800m", SOMETHING),
                "DISTANCE_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
            },
            {
                "WEEKDAY_0_IDX": (1, NOTHING),
                "WEEKDAY_1_IDX": (1, NOTHING),
                "WEEKDAY_2_IDX": (1, NOTHING),
            },
        ),
        (
            "section",
            {0: "", 1: "", 2: ".", 3: "1", 4: "2", 5: "k", 6: "m"},
            5,
            {
                "CLASS_ID_0_IDX": ("Section_AddSign", SOMETHING),
                "CLASS_ID_1_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_2_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_3_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
            },
            {
                "CLASS_VALUE_0_IDX": (SpecialLabelsOCR.PADDING_LABEL.value, SOMETHING),
                "CLASS_VALUE_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_3_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "FULL_TEXT_IDX": ("1.2km", SOMETHING),
                "DISTANCE_FROM_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "DISTANCE_TO_IDX": ("1.2km", SOMETHING),
                "DISTANCE_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
            },
            {
                "WEEKDAY_0_IDX": (1, NOTHING),
                "WEEKDAY_1_IDX": (1, NOTHING),
                "WEEKDAY_2_IDX": (1, NOTHING),
            },
        ),
        (
            "time",
            {
                0: "",
                1: "",
                2: ",",
                3: "-",
                4: "0",
                5: "1",
                6: "7",
                7: "9",
                8: "_",
                9: "d",
                10: "f",
                11: "h",
                12: "i",
                13: "m",
                14: "o",
                15: "r",
            },
            24,
            {
                "CLASS_ID_0_IDX": ("Time_AddSign", SOMETHING),
                "CLASS_ID_1_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_2_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_3_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
            },
            {
                "CLASS_VALUE_0_IDX": (SpecialLabelsOCR.PADDING_LABEL.value, SOMETHING),
                "CLASS_VALUE_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_3_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "FULL_TEXT_IDX": ("di,mi,fr_10-17h_do10-19h", SOMETHING),
                "DISTANCE_FROM_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "DISTANCE_TO_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "DISTANCE_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_0_IDX": ("10", SOMETHING),
                "TIME_END_0_IDX": ("17h", SOMETHING),
                "TIME_START_1_IDX": ("10", SOMETHING),
                "TIME_END_1_IDX": ("19h", SOMETHING),
                "TIME_START_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
            },
            {
                "WEEKDAY_0_IDX": (torch.tensor([0.0, 1.0, 1.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]), SOMETHING),
                "WEEKDAY_1_IDX": (torch.tensor([0.0, 0.0, 0.0, 1.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0]), SOMETHING),
                "WEEKDAY_2_IDX": (1, NOTHING),
            },
        ),
        (
            "multiclass",
            {0: "", 1: "", 2: "a", 3: "c", 4: "h", 5: "u"},
            4,
            {
                "CLASS_ID_0_IDX": ("Also_AddSign", SOMETHING),
                "CLASS_ID_1_IDX": ("Bus_AddSign", SOMETHING),
                "CLASS_ID_2_IDX": ("CarTrailer_AddSign", SOMETHING),
                "CLASS_ID_3_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
            },
            {
                "CLASS_VALUE_0_IDX": (SpecialLabelsOCR.PADDING_LABEL.value, SOMETHING),
                "CLASS_VALUE_1_IDX": (SpecialLabelsOCR.PADDING_LABEL.value, SOMETHING),
                "CLASS_VALUE_2_IDX": (SpecialLabelsOCR.PADDING_LABEL.value, SOMETHING),
                "CLASS_VALUE_3_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "FULL_TEXT_IDX": ("auch", SOMETHING),
                "DISTANCE_FROM_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "DISTANCE_TO_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "DISTANCE_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_0_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_START_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "TIME_END_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
            },
            {
                "WEEKDAY_0_IDX": (1, NOTHING),
                "WEEKDAY_1_IDX": (1, NOTHING),
                "WEEKDAY_2_IDX": (1, NOTHING),
            },
        ),
        (
            "speed_limit_70",
            {0: "", 1: "", 2: "0", 3: "7"},
            2,
            {
                "CLASS_ID_0_IDX": ("SL70_MainSign", SOMETHING),
                "CLASS_ID_1_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_2_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
                "CLASS_ID_3_IDX": (SpecialLabels.NOTHING_LABEL.value, NOTHING),
            },
            {
                "CLASS_VALUE_0_IDX": ("70", SOMETHING),
                "CLASS_VALUE_1_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_2_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "CLASS_VALUE_3_IDX": (SpecialLabelsOCR.NOTHING_LABEL.value, NOTHING),
                "FULL_TEXT_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "DISTANCE_FROM_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "DISTANCE_TO_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "DISTANCE_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "TIME_START_0_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "TIME_END_0_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "TIME_START_1_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "TIME_END_1_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "TIME_START_2_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "TIME_END_2_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
            },
            {
                "WEEKDAY_0_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "WEEKDAY_1_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
                "WEEKDAY_2_IDX": (SpecialLabels.UNLABELED_LABEL.value, NOTHING),
            },
        ),
    ],
)
@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
def test_labels_generic(
    dataset_name: str,
    expected_token_lookup: dict[int, str],
    max_text_length: int,
    label_checks_single_value: dict[str, tuple[str | int, int]],
    label_checks_word_value: dict[str, tuple[str | int, int]],
    label_checks_array_value: dict[str, tuple[torch.Tensor, int]],
    dataset_csv_path: Path,
    augmentation_params: AugmentationParameters,
    class_mapping: dict[str, int],
) -> None:
    """Generic test for label validation across different dataset scenarios.

    Args:
        dataset_name: Name of the dataset scenario.
        expected_token_lookup: Mapping from token indices to expected characters.
        max_text_length: Maximum expected text length in labels.
        label_checks_single_value: Dictionary specifying expected label values  if there is a single label.
        label_checks_word_value: Dictionary specifying expected label values  if the label is a word.
        label_checks_array_value: Dictionary specifying expected label values  if the label is an array.
        dataset_csv_path: Path to the dataset CSV.
        augmentation_params: Augmentation parameters.
        class_mapping: Mapping from class names to IDs.
    """
    # GIVEN a dataset object
    dataset = TrafficSignClassificationDatasetGen2(
        dataset_csv_path=dataset_csv_path,
        input_size=INPUT_SIZE,
        is_pretrain=False,
        augmentation_params=augmentation_params,
    )

    labels = dataset[0].label[ValueKey.DATA]
    assert isinstance(labels, tuple), "Labels need to be a tuple of torch.Tensor"
    validate_tensor_shapes(labels, max_text_length=max_text_length)

    # WHEN accessing the first data point
    assert len(dataset) > 0, f"Dataset '{dataset_name}' is empty."
    data_point = dataset[0]
    assert len(data_point) == 2, "Each data point should return (image, labels)."
    labels = data_point.label[ValueKey.DATA]

    # THEN Labels are as expected
    assert isinstance(labels, tuple), "Labels need to be a tuple of torch.Tensor"
    validate_labels(
        labels,
        max_text_length,
        label_checks_single_value,
        label_checks_word_value,
        label_checks_array_value,
        expected_token_lookup,
        class_mapping,
    )


@pytest.mark.lfs_dependencies(["xtorch_usecases/tests/test_data/traffic_sign_classification/database/"])
def test_dataset_compute_class_distribution(
    dataset_gen2: TrafficSignClassificationDatasetGen2,
) -> None:
    """Test the dataset's getter method."""
    # GIVEN a dataset
    # (Dataset is initialized via fixture)

    # WHEN computing class distributions during initialization
    # THEN class distributions are as expected
    manual_class_value_distribution = defaultdict(int, {(510, "70.0"): 4})
    manual_attribute_distribution = defaultdict(
        int,
        {
            ("full_text", "auch"): 1,
            ("full_text", "100m"): 2,
            ("distance", "100m"): 2,
            ("full_text", "di,mi,fr_10-17h_do10-19h"): 3,
            ("time_start_0", "10"): 3,
            ("time_end_0", "17h"): 3,
            ("time_start_1", "10"): 3,
            ("time_end_1", "19h"): 3,
            ("full_text", "200-800m"): 1,
            ("distance_from", "200"): 1,
            ("distance_to", "800m"): 1,
            ("full_text", "1.2km"): 1,
            ("distance_to", "1.2km"): 1,
        },
    )
    assert manual_attribute_distribution == dataset_gen2.attribute_distribution
    assert manual_class_value_distribution == dataset_gen2.class_value_distribution
