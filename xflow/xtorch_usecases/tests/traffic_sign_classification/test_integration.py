"""Smoke and integration tests for the traffic_sign_classification use-case."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import sys
from pathlib import Path
from unittest import mock

import pytest
import ts_label_mapping
from omegaconf import OmegaConf

from conversion.qnn.common import QnnModelArtifacts
from xtorch.testing.run_in_subprocess import run_in_subprocess
from xtorch_usecases.common.helpers import get_test_data_folder
from xtorch_usecases.common.pipeline import CONVERT_QNN_SUBFOLDER, EXPORT_SUBFOLDER, TRAIN_CHECKPOINT_SUBFOLDER
from xtorch_usecases.traffic_sign_classification.convert import main as convert_main
from xtorch_usecases.traffic_sign_classification.export import main as export_main
from xtorch_usecases.traffic_sign_classification.train import main as train_main

_LOGGER = logging.getLogger(__name__)

TS_CLASSIFICATION_GEN1_CONFIG_NAME = "traffic_sign_classification_gen1"
TS_CLASSIFICATION_GEN1_CONFIG_NAMES = [TS_CLASSIFICATION_GEN1_CONFIG_NAME]
TS_CLASSIFICATION_GEN2_CONFIG_NAME = "traffic_sign_classification_gen2"
TS_CLASSIFICATION_GEN2_CONFIG_NAMES = [TS_CLASSIFICATION_GEN2_CONFIG_NAME]
TS_CLASSIFICATION_CONFIG_NAMES = TS_CLASSIFICATION_GEN1_CONFIG_NAMES + TS_CLASSIFICATION_GEN2_CONFIG_NAMES


@pytest.mark.slow
@pytest.mark.lfs_dependencies(
    [
        "xtorch_usecases/tests/test_data/traffic_sign_classification",
    ]
)
@pytest.mark.parametrize("task_config_name", TS_CLASSIFICATION_CONFIG_NAMES)
def test_traffic_sign_classification(
    tmpdir_delete_after: Path,
    task_config_name: str,
) -> None:
    """Test if stages work together.

    In order to skip this test run with `python -m pytest -m "not slow" <dir>`.
    """
    # GIVEN the specific model config and a few test args
    common_args_for_testing = [
        f"--config_name={task_config_name}",
        f"hydra.run.dir={tmpdir_delete_after!s}",  # Dump hydra logs into the test dir. Keep CWD clean.
        "environment=local",
    ]
    # gen1 and gen2 classifiers use differnt data configs
    if task_config_name in TS_CLASSIFICATION_GEN1_CONFIG_NAMES:
        common_args_for_testing += [
            "data=local_gen1.yaml",
            f"data.train.train_data_csv_path={get_test_data_folder()}/traffic_sign_classification/test_dataset.csv",
            f"data.train.val_data_csv_path={get_test_data_folder()}/traffic_sign_classification/test_dataset.csv",
        ]
    elif task_config_name in TS_CLASSIFICATION_GEN2_CONFIG_NAMES:
        common_args_for_testing += [
            "data=local_gen2.yaml",
            f"data.train.train_data_csv_path={get_test_data_folder()}/traffic_sign_classification/test_dataset_gen2.csv",
            f"data.train.val_data_csv_path={get_test_data_folder()}/traffic_sign_classification/test_dataset_gen2.csv",
        ]
    else:
        error_message = (
            f"Undefined which data config shall get used for the following arguments: {common_args_for_testing}"
        )
        raise ValueError(error_message)

    # ---- TRAINING STAGE ----
    train_outputs = tmpdir_delete_after / "train"
    run_train_stage(common_args_for_testing, train_outputs)

    # ---- EXPORT STAGE ----
    assert train_outputs is not None, "Train outputs are required for export."
    export_outputs = tmpdir_delete_after / "export"
    run_export_stage(common_args_for_testing, task_config_name, input_path=train_outputs, output_path=export_outputs)

    # ---- CONVERT STAGE ----
    # TODO: Re-Activate the convert stage once segmentation fault is fixed, see https://bos.ch/26y4svi
    if False:
        assert export_outputs is not None, "Export outputs are required for conversion."
        convert_outputs = tmpdir_delete_after / "convert"
        run_convert_stage(
            common_args_for_testing, task_config_name, input_path=export_outputs, output_path=convert_outputs
        )


@run_in_subprocess(context="spawn")
def run_train_stage(
    train_args: list[str],
    output_path: Path,
) -> None:
    """Test the train stage."""
    # GIVEN the specific model config and a few test args
    train_args_for_testing = train_args + [
        f"output_folder={output_path!s}",
        f"trainer.default_root_dir={output_path!s}",
        "stage_trainer.train.max_epochs=2",
        "data.train.batch_size_train=2",
        "data.train.batch_size_val=2",
    ]
    # WHEN the train stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + train_args_for_testing):
        train_main(sys.argv)

    # THEN checkpoints have been written
    if any("traffic_sign_classification_gen2" in arg for arg in train_args):
        last_ckpt_file = Path(f"{output_path!s}/train/{TRAIN_CHECKPOINT_SUBFOLDER!s}/last.ckpt")
    else:
        last_ckpt_file = Path(f"{output_path.parent!s}/train/{TRAIN_CHECKPOINT_SUBFOLDER!s}/last.ckpt")
    assert last_ckpt_file.exists(), "Checkpoint file not found."

    # WHEN the config file path is collected
    composed_config_files = list(output_path.glob("composed_config.yaml"))

    # THEN the composed config file is found
    assert composed_config_files, "No composed config found."
    assert len(composed_config_files) == 1, "More than one composed config found."

    # WHEN the composed config is loaded
    composed_config = OmegaConf.load(composed_config_files[0])

    # THEN we print the config for debugging
    _LOGGER.info(f"Composed config: {OmegaConf.to_yaml(composed_config)}")

    # THEN the output directory is not empty
    assert next(output_path.iterdir(), None) is not None, "Output directory is empty."


@run_in_subprocess(context="spawn")
def run_export_stage(common_args: list[str], config_name: str, input_path: Path, output_path: Path) -> None:
    """Test the export stage."""
    if any("traffic_sign_classification_gen2" in arg for arg in common_args):
        input_path = input_path / "train"

    # GIVEN the specific model config and a few test args
    export_args_for_testing = common_args + [
        f"input_folder={input_path!s}",
        f"output_folder={output_path!s}",
        f"trainer.default_root_dir={output_path!s}",
    ]

    # WHEN the export stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + export_args_for_testing):
        export_main(sys.argv)

    # THEN the output directory is not empty
    assert next(output_path.iterdir(), None) is not None, "Output directory is empty."

    output_dir = output_path / EXPORT_SUBFOLDER

    # THEN one ONNX model file is found for each deployment config

    model_paths = output_dir.glob(f"{config_name}_*.onnx")
    # TODO: obtain dynamically from deployment config instead of hard-coding
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/399014
    assert len(list(model_paths)) == 2


@run_in_subprocess(context="spawn")
def run_convert_stage(common_args: list[str], config_name: str, input_path: Path, output_path: Path) -> None:
    """Test the convert stage."""
    # GIVEN the specific model config and a few test args
    convert_args_for_testing = common_args + [
        f"input_folder={input_path!s}",
        f"output_folder={output_path!s}",
        f"trainer.default_root_dir={output_path!s}",
        "convert_config.num_calib_samples=2",
        "convert_config.quant_check_config.short_run=True",
    ]

    # WHEN the convert stage is executed
    with mock.patch.object(sys, "argv", sys.argv[:1] + convert_args_for_testing):
        convert_main(sys.argv)

    # THEN the output directory is not empty
    qnn_artifacts_path_htp = output_path / CONVERT_QNN_SUBFOLDER / "htp"
    assert next(qnn_artifacts_path_htp.iterdir(), None) is not None, "Output directory is empty."

    # THEN the output directory is not empty
    qnn_artifacts_path_cpu = output_path / CONVERT_QNN_SUBFOLDER / "cpu"
    assert next(qnn_artifacts_path_cpu.iterdir(), None) is not None, "Output directory is empty."

    # THEN the output directory is not empty
    qnn_artifacts_path_checker = output_path / CONVERT_QNN_SUBFOLDER / "quantization_checker"
    assert next(qnn_artifacts_path_checker.iterdir(), None) is not None, "Output directory is empty."

    # TODO: obtain dynamically from deployment config instead of hard-coding
    # https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/399014

    if config_name in TS_CLASSIFICATION_GEN1_CONFIG_NAMES:
        image_size = 32
    elif config_name in TS_CLASSIFICATION_GEN2_CONFIG_NAMES:
        image_size = 64
    else:
        error_message = f"Unknown config name: {config_name}"
        raise ValueError(error_message)
    yuv_model_name = f"{config_name}_64x3x{image_size!s}x{image_size!s}_yuv444"
    rgb_model_name = f"{config_name}_64x3x{image_size!s}x{image_size!s}_rgb"
    model_artifacts = [
        (QnnModelArtifacts(qnn_artifacts_path_htp, model_name=yuv_model_name), 1),
        (QnnModelArtifacts(qnn_artifacts_path_htp, model_name=rgb_model_name), 1),
    ]

    for qnn_artifacts, num_tasks in model_artifacts:
        # THEN the output directory contains the expected metadata
        assert qnn_artifacts.model_metadata_path().exists(), "Metadata file does not exist."
        model_metadata = qnn_artifacts.model_metadata()
        assert model_metadata is not None, "Model metadata is empty."
        assert config_name in model_metadata.metadata.model_name, (
            f"Model name in metadata does not match the expected model name: {config_name}"
        )
        if num_tasks:
            assert len(model_metadata.tasks) == num_tasks, (
                f"Expected {num_tasks} tasks in metadata, but found {len(model_metadata.tasks)}."
            )
        assert model_metadata.tasks, "Metadata does not contain 'tasks' field."
        assert "ts_classification" in model_metadata.tasks, "Metadata does not contain 'ts_classification' task."
        assert model_metadata.tasks["ts_classification"].tensors, "ts_classification does not contain 'tensors' field."
        assert model_metadata.tasks["ts_classification"].metadata, (
            "ts_classification does not contain 'metadata' field."
        )
        assert "label_mapping_version" in model_metadata.tasks["ts_classification"].metadata, (
            "ts_classification metadata does not contain 'label_mapping_version' field."
        )
        assert (
            model_metadata.tasks["ts_classification"].metadata["label_mapping_version"] == ts_label_mapping.__version__
        ), "label_mapping_version in metadata does not match the expected version from the 'ts_label_mapping' package."

        if config_name in TS_CLASSIFICATION_GEN1_CONFIG_NAMES:
            assert "output" in model_metadata.tasks["ts_classification"].tensors, (
                "Metadata does not contain 'output' tensor in 'tensors' field."
            )
        elif config_name in TS_CLASSIFICATION_GEN2_CONFIG_NAMES:
            output_layers = 20
            for output_index in range(output_layers):
                output_tensor_name = f"output_0_{output_index}"
                output_nothing_index_tensorname = f"output_1_{output_index}"
                assert output_tensor_name in model_metadata.tasks["ts_classification"].tensors, (
                    f"Metadata does not contain '{output_tensor_name}' tensor in 'tensors' field."
                )
                assert output_nothing_index_tensorname in model_metadata.tasks["ts_classification"].tensors, (
                    f"Metadata does not contain '{output_nothing_index_tensorname}' tensor in 'tensors' field."
                )
        else:
            error_message = f"Unknown config name: {config_name}"
            raise ValueError(error_message)
