"""Tests the implementation of the training module for traffic sign classification."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path
from unittest import mock

import pytest
import torch
from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from torch import optim
from ts_label_mapping.classifier import ClassMappings

from xcontract.data.definitions.usage import ValueKey
from xtorch.nn.backbones.image.resnet import ResnetSuperBlockConfig, ResnetSuperBlockType
from xtorch.training.metric_handling import MetricsContainer
from xtorch.training.trainer_config import TrainerConfig
from xtorch_usecases.traffic_sign_classification.callbacks.loss_summary_logger import LossSummaryLogger
from xtorch_usecases.traffic_sign_classification.config.classifier_config_common import (
    InputConfig,
    OptimizerConfig,
    TrainerOverrideConfig,
)
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen1 import (
    override_trainer_for_stage,
)
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen2 import (
    LossConfigGen2,
    ModelConfigGen2,
    TrafficSignClassificationConfigGen2,
)
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel
from xtorch_usecases.traffic_sign_classification.losses.ts_classifier_gen2_loss import TsClassifierGen2Loss
from xtorch_usecases.traffic_sign_classification.models.heads.ts_classifier_gen2_head import (
    AttributeGroup,
    InterchangeableGroup,
)
from xtorch_usecases.traffic_sign_classification.models.network_gen2 import ClassifierModelGen2
from xtorch_usecases.traffic_sign_classification.modules.train_module_gen2 import (
    TrafficSignClassificationTrainingLMGen2,
)

BATCH_SIZE = 10
NUMBER_CLASS_ENTRIES = 4
class_mappings = ClassMappings()
NUMBER_CLASS_IDS = len(class_mappings.get_gapless_class_names(country_code="ALL"))
MAX_TEXT_LENGTH = 20
ALPHABET_SIZE = 30
NUMBER_WEEKDAY_ENTRIES = 3
WEEKDAY_NUMBERS = 11


@pytest.fixture
def mock_config() -> TrafficSignClassificationConfigGen2:
    """Fixture for a mock configuration."""
    config = mock.MagicMock(TrafficSignClassificationConfigGen2(TrainerConfig(max_epochs=1)))
    config.input = InputConfig()
    config.input.rgb_bitdepth = 8
    config.input.yuv_bitdepth = 8
    config.input.input_height = 32
    config.input.input_channels = 3
    config.model = ModelConfigGen2()
    config.model.encoder_num_filters = 64
    config.model.encoder_resnet_config = [
        ResnetSuperBlockConfig(num_blocks=2, super_block_type=ResnetSuperBlockType.STANDARD),
        ResnetSuperBlockConfig(num_blocks=2, super_block_type=ResnetSuperBlockType.DOWNSAMPLE),
        ResnetSuperBlockConfig(num_blocks=1, super_block_type=ResnetSuperBlockType.INCREASE_FILTER),
        ResnetSuperBlockConfig(num_blocks=1, super_block_type=ResnetSuperBlockType.DILATE),
        ResnetSuperBlockConfig(num_blocks=1, super_block_type=ResnetSuperBlockType.DILATE_AND_INCREASE_FILTER),
        ResnetSuperBlockConfig(num_blocks=1, super_block_type=ResnetSuperBlockType.DOWNSAMPLE),
    ]
    config.model.encoder_max_filters = 512
    config.model.word_attributes = [
        "full_text",
        "distance_from",
        "distance_to",
        "distance",
        "time_start_0",
        "time_end_0",
        "time_start_1",
        "time_end_1",
        "time_start_2",
        "time_end_2",
    ]
    config.model.number_class_entries = NUMBER_CLASS_ENTRIES
    config.model.number_weekday_entries = NUMBER_WEEKDAY_ENTRIES
    config.model.max_text_length = MAX_TEXT_LENGTH
    config.model.alphabet_size = ALPHABET_SIZE
    config.model.num_classes = NUMBER_CLASS_IDS
    config.model.interchangeable_groups = [
        InterchangeableGroup(
            [
                AttributeGroup(class_ids=[0]),
                AttributeGroup(class_ids=[1]),
                AttributeGroup(class_ids=[2]),
                AttributeGroup(class_ids=[3]),
            ]
        ),
        InterchangeableGroup(
            [
                AttributeGroup(word_attributes=["time_start_0", "time_end_0"], weekday_attributes=[0]),
                AttributeGroup(word_attributes=["time_start_1", "time_end_1"], weekday_attributes=[1]),
                AttributeGroup(word_attributes=["time_start_2", "time_end_2"], weekday_attributes=[2]),
            ]
        ),
    ]

    config.optimizer = OptimizerConfig()
    config.optimizer.lr = 0.001
    config.optimizer.weight_decay = 0.0001
    config.optimizer.reduction = "mean"
    config.optimizer.scheduler_t_max = None
    config.output_folder = Path("test")
    config.loss = LossConfigGen2()
    config.loss.class_id_weight = 1.0
    config.loss.class_value_weight = 1.0
    config.loss.word_attribute_weight = 1.0
    config.loss.weekday_weight = 1.0
    config.loss.nothing_weight = 1.0

    config.data.train.train_data_csv_path = Path(
        "xtorch_usecases/tests/test_data/traffic_sign_classification/test_dataset_gen2.csv"
    )
    return config


@pytest.fixture
def training_module(
    device: str, mock_config: TrafficSignClassificationConfigGen2
) -> TrafficSignClassificationTrainingLMGen2:
    """Fixture for the training module."""
    return TrafficSignClassificationTrainingLMGen2(config=mock_config).to(device)


def test_forward(device: str, training_module: TrafficSignClassificationTrainingLMGen2) -> None:
    """Test the forward method."""
    # GIVEN training module with built model
    batch = ImageAndLabel(
        image={ValueKey.DATA: torch.randn(BATCH_SIZE, 3, 64, 64, device=device), ValueKey.IDENTIFIER: "test_image.png"},
        label={ValueKey.DATA: torch.tensor([0, 1, 2, 3], device=device), ValueKey.IDENTIFIER: "test_label.json"},
    )
    training_module.build_model()
    # WHEN calling function forward
    preds, nothing_indicators = training_module.forward(batch)
    # THEN shape of output is as expected
    class_indices = [0, 2, 4, 6]
    weekday_indices = [18, 19, 20]
    word_indices = [8, 9, 10, 11, 12, 13, 14, 15]
    value_indices = [1, 3, 5, 7]
    for tensor_idx in class_indices:
        assert preds[tensor_idx].shape == torch.Size([BATCH_SIZE, NUMBER_CLASS_IDS]), (
            f"Expected shape ({BATCH_SIZE}, {NUMBER_CLASS_IDS}), but got {preds[tensor_idx].shape}"
        )
    for word_idx in word_indices + value_indices:
        assert preds[word_idx].shape == torch.Size([BATCH_SIZE, MAX_TEXT_LENGTH, ALPHABET_SIZE]), (
            f"Expected shape ({BATCH_SIZE}, {MAX_TEXT_LENGTH}, {ALPHABET_SIZE}), but got {preds[word_idx].shape}"
        )
    for weekday_idx in weekday_indices:
        assert preds[weekday_idx].shape == torch.Size([BATCH_SIZE, WEEKDAY_NUMBERS]), (
            f"Expected shape ({BATCH_SIZE}, {WEEKDAY_NUMBERS}), but got {preds[weekday_idx].shape}"
        )
    for nothing_indicator in nothing_indicators:
        assert isinstance(nothing_indicator, torch.Tensor)
        assert nothing_indicator.shape == (BATCH_SIZE, 1), (
            f"Expected shape ({BATCH_SIZE}, 1), but got {nothing_indicator.shape}"
        )


def test_get_eval_callbacks(
    training_module: TrafficSignClassificationTrainingLMGen2, mock_config: TrafficSignClassificationConfigGen2
) -> None:
    """Test the get_eval_callbacks method."""
    # GIVEN Training Module
    # WHEN calling get_eval_callbacks
    mock_config.trainer = mock.MagicMock(spec=TrainerOverrideConfig)
    override_trainer_for_stage(mock_config.trainer, mock_config.stage_trainer.train)
    mock_config.trainer.default_root_dir = Path(mock_config.output_folder) / "train"
    mock_config.output_folder = Path(mock_config.output_folder)
    mock_config.output_folder.mkdir(exist_ok=True, parents=True)
    callbacks = training_module.get_eval_callbacks(mock_config)
    # THEN an empty list is returned
    assert isinstance(callbacks, list)
    assert len(callbacks) == 1


def test_get_train_callbacks(
    training_module: TrafficSignClassificationTrainingLMGen2, mock_config: TrafficSignClassificationConfigGen2
) -> None:
    """Test the get_train_callbacks method."""
    # GIVEN Training Module
    # WHEN calling get_train_callbacks
    callbacks = training_module.get_train_callbacks(mock_config)
    # THEN list consisting only of the ModelCheckpoint is returned
    mock_config.trainer = mock.MagicMock(spec=TrainerOverrideConfig)
    override_trainer_for_stage(mock_config.trainer, mock_config.stage_trainer.train)
    mock_config.trainer.default_root_dir = Path(mock_config.output_folder) / "train"
    mock_config.output_folder = Path(mock_config.output_folder)
    mock_config.output_folder.mkdir(exist_ok=True, parents=True)
    callbacks = training_module.get_train_callbacks(mock_config)
    assert isinstance(callbacks, list)
    assert len(callbacks) == 2
    assert isinstance(callbacks[0], LossSummaryLogger)
    assert isinstance(callbacks[1], ModelCheckpoint)


def test_build_model(training_module: TrafficSignClassificationTrainingLMGen2) -> None:
    """Test the build_model method."""
    # GIVEN Training Module
    # WHEN calling build_model
    model = training_module.build_model()
    # THEN classifier was returned
    assert isinstance(model, ClassifierModelGen2)


def test_build_optimizer(training_module: TrafficSignClassificationTrainingLMGen2) -> None:
    """Test the build_optimizer method."""
    # GIVEN Training Module with built model
    # WHEN calling build_optimizer
    training_module.build_model()
    optimizer = training_module.build_optimizer()
    # THEN AdamW optimizer is returned
    assert isinstance(optimizer, optim.AdamW)


def test_build_lr_scheduler(training_module: TrafficSignClassificationTrainingLMGen2) -> None:
    """Test the build_lr_scheduler method."""
    # GIVEN: Training Module
    optimizer = optim.AdamW(training_module._model.parameters())  # noqa: SLF001
    training_module.trainer = mock.MagicMock(max_epochs=1000)
    # WHEN: Calling build_lr_scheduler
    scheduler = training_module.build_lr_scheduler(optimizer)
    # THEN: Scheduler is CosineAnnealingLR
    assert isinstance(scheduler, torch.optim.lr_scheduler.CosineAnnealingLR)


def test_build_loss(training_module: TrafficSignClassificationTrainingLMGen2) -> None:
    """Test the build_loss method."""
    # GIVEN: Training Module
    # WHEN: Calling build_loss
    loss_fn = training_module.build_loss()
    # THEN: Loss is CrossEntropy
    assert isinstance(loss_fn, TsClassifierGen2Loss)


def test_build_metrics(training_module: TrafficSignClassificationTrainingLMGen2) -> None:
    """Test the build_metrics method."""
    # GIVEN: Training Module
    # WHEN: Calling build_metrics
    metrics = training_module.build_metrics()
    # THEN: Metrics are created (will be added in a future PR)
    assert isinstance(metrics, MetricsContainer)
    assert len(metrics) == 0


def test_compute_loss(
    device: str,
    training_module: TrafficSignClassificationTrainingLMGen2,
    targets_matching: tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]],
    outputs: tuple[tuple[torch.Tensor, ...], tuple[torch.Tensor, ...]],
) -> None:
    """Test the compute_loss method."""
    # GIVEN: Random output tensor and impossible class labels (class does not exist)
    # temp_output not used in the test, needed to create a valid batch
    temp_output = torch.randn(4, 3, 64, 64, device=device)
    batch = ImageAndLabel(
        image={ValueKey.DATA: temp_output, ValueKey.IDENTIFIER: "test_image.png"},
        label={ValueKey.DATA: targets_matching, ValueKey.IDENTIFIER: "test_label.json"},
    )
    # WHEN: Call compute_loss
    loss_output = training_module.compute_loss(batch, outputs)
    # THEN: loss is significantly somewhat close to zero
    assert loss_output.total_loss.item() < 1.0
