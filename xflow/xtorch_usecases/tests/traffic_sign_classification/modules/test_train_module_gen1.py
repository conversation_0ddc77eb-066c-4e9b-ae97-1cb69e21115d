"""Tests the implementation of the training module for traffic sign classification."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from pathlib import Path
from unittest import mock

import pytest
import torch
from pytorch_lightning.callbacks.model_checkpoint import ModelCheckpoint
from torch import nn, optim

from xcontract.data.definitions.usage import ValueKey
from xtorch.training import Stage
from xtorch.training.metric_handling import MetricsContainer
from xtorch_usecases.traffic_sign_classification.callbacks.loss_summary_logger import LossSummaryLogger
from xtorch_usecases.traffic_sign_classification.callbacks.mcl_metrics_callback import MultiClassMetricsCallback
from xtorch_usecases.traffic_sign_classification.config.classifier_config_common import (
    TrainerOverrideConfig,
)
from xtorch_usecases.traffic_sign_classification.config.classifier_config_gen1 import (
    TrafficSignClassificationConfigGen1,
    override_trainer_for_stage,
)
from xtorch_usecases.traffic_sign_classification.data.dataset import ImageAndLabel
from xtorch_usecases.traffic_sign_classification.models.encoders.encoder_parameters_gen1 import EncoderTypeGen1
from xtorch_usecases.traffic_sign_classification.models.network_gen1 import ClassifierModelGen1
from xtorch_usecases.traffic_sign_classification.modules.train_module_gen1 import (
    TrafficSignClassificationTrainingLMGen1,
)


@pytest.fixture
def mock_config() -> TrafficSignClassificationConfigGen1:
    """Fixture for a mock configuration."""
    config = mock.MagicMock(spec=TrafficSignClassificationConfigGen1)
    config.input.rgb_bitdepth = 8
    config.input.yuv_bitdepth = 8
    config.input.input_height = 32
    config.input.input_channels = 3
    config.model.encoder_type = EncoderTypeGen1.ResNet
    config.model.encoder_num_filters = 64
    config.model.encoder_num_block_list = [2, 2, 2, 2]
    config.model.encoder_max_filters = 512
    config.model.head_num_neuron_list = [256, 128]
    config.model.num_classes = 10
    config.model.head_crossentropy_train_dropout_rates_list = [0.5, 0.5, 0.5]
    config.optimizer.lr = 0.001
    config.optimizer.weight_decay = 0.0001
    config.optimizer.reduction = "mean"
    config.optimizer.scheduler_t_max = None
    config.output_folder = "test"
    return config


@pytest.fixture
def training_module(
    device: str, mock_config: TrafficSignClassificationConfigGen1
) -> TrafficSignClassificationTrainingLMGen1:
    """Fixture for the training module."""
    return TrafficSignClassificationTrainingLMGen1(config=mock_config).to(device)


def test_forward(device: str, training_module: TrafficSignClassificationTrainingLMGen1) -> None:
    """Test the forward method."""
    # GIVEN training module with built model
    batch = ImageAndLabel(
        image={ValueKey.DATA: torch.randn(4, 3, 32, 32, device=device), ValueKey.IDENTIFIER: "test_image.png"},
        label={ValueKey.DATA: torch.tensor([0, 1, 2, 3], device=device), ValueKey.IDENTIFIER: "test_label.json"},
    )
    training_module.build_model()
    # WHEN calling function forward
    preds = training_module.forward(batch)
    # THEN shape of output is as expected
    assert preds.shape == torch.Size([4, 10]), f"Expected shape (4, 10), but got {preds.shape}"


def test_get_eval_callbacks(
    training_module: TrafficSignClassificationTrainingLMGen1, mock_config: TrafficSignClassificationConfigGen1
) -> None:
    """Test the get_eval_callbacks method."""
    # GIVEN Training Module
    # WHEN calling get_eval_callbacks
    # Add dummy trainer attribute to mock_config
    mock_config.trainer = mock.MagicMock(spec=TrainerOverrideConfig)
    override_trainer_for_stage(mock_config.trainer, mock_config.stage_trainer.train)
    mock_config.trainer.default_root_dir = Path(mock_config.output_folder) / "train"
    mock_config.output_folder = Path(mock_config.output_folder)
    mock_config.output_folder.mkdir(exist_ok=True, parents=True)
    callbacks = training_module.get_eval_callbacks(mock_config)
    # THEN a list with entry MultiClassMetricsCallback is returned
    assert isinstance(callbacks, list)
    assert len(callbacks) == 1
    assert isinstance(callbacks[0], MultiClassMetricsCallback)


def test_get_train_callbacks(
    training_module: TrafficSignClassificationTrainingLMGen1, mock_config: TrafficSignClassificationConfigGen1
) -> None:
    """Test the get_train_callbacks method."""
    # GIVEN Training Module
    # WHEN calling get_train_callbacks
    # Add dummy trainer attribute to mock_config
    mock_config.trainer = mock.MagicMock(spec=TrainerOverrideConfig)
    override_trainer_for_stage(mock_config.trainer, mock_config.stage_trainer.train)
    mock_config.trainer.default_root_dir = Path(mock_config.output_folder) / "train"
    mock_config.output_folder = Path(mock_config.output_folder)
    mock_config.output_folder.mkdir(exist_ok=True, parents=True)
    callbacks = training_module.get_train_callbacks(mock_config)
    # THEN list consisting only of the ModelCheckpoint is returned
    assert isinstance(callbacks, list)
    assert len(callbacks) == 2
    assert isinstance(callbacks[0], LossSummaryLogger)
    assert isinstance(callbacks[1], ModelCheckpoint)


def test_build_model(training_module: TrafficSignClassificationTrainingLMGen1) -> None:
    """Test the build_model method."""
    # GIVEN Training Module
    # WHEN calling build_model
    model = training_module.build_model()
    # THEN classifier was returned
    assert isinstance(model, ClassifierModelGen1)


def test_build_optimizer(training_module: TrafficSignClassificationTrainingLMGen1) -> None:
    """Test the build_optimizer method."""
    # GIVEN Training Module with built model
    # WHEN calling build_optimizer
    training_module.build_model()
    optimizer = training_module.build_optimizer()
    # THEN AdamW optimizer is returned
    assert isinstance(optimizer, optim.AdamW)


def test_build_lr_scheduler(training_module: TrafficSignClassificationTrainingLMGen1) -> None:
    """Test the build_lr_scheduler method."""
    # GIVEN: Training Module
    optimizer = optim.AdamW(training_module._model.parameters())  # noqa: SLF001
    training_module.trainer = mock.MagicMock(max_epochs=1000)
    # WHEN: Calling build_lr_scheduler
    scheduler = training_module.build_lr_scheduler(optimizer)
    # THEN: Scheduler is CosineAnnealingLR
    assert isinstance(scheduler, torch.optim.lr_scheduler.CosineAnnealingLR)


def test_build_loss(training_module: TrafficSignClassificationTrainingLMGen1) -> None:
    """Test the build_loss method."""
    # GIVEN: Training Module
    # WHEN: Calling build_loss
    loss_fn = training_module.build_loss()
    # THEN: Loss is CrossEntropy
    assert isinstance(loss_fn, nn.CrossEntropyLoss)


def test_build_metrics(training_module: TrafficSignClassificationTrainingLMGen1) -> None:
    """Test the build_metrics method."""
    # GIVEN: Training Module
    # WHEN: Calling build_metrics
    metrics = training_module.build_metrics()
    # THEN: Metrics are created
    assert isinstance(metrics, MetricsContainer)
    assert "accuracy" in metrics._metrics[Stage.TRAIN]  # noqa: SLF001
    assert "f1" in metrics._metrics[Stage.TRAIN]  # noqa: SLF001


def test_compute_loss(device: str, training_module: TrafficSignClassificationTrainingLMGen1) -> None:
    """Test the compute_loss method."""
    # GIVEN: Random output tensor and impossible class labels (class does not exist)
    batch = ImageAndLabel(
        image={ValueKey.DATA: torch.randn(4, 3, 32, 32, device=device), ValueKey.IDENTIFIER: "test_image.png"},
        label={ValueKey.DATA: torch.tensor([5, 5, 5, 5], device=device), ValueKey.IDENTIFIER: "test_label.json"},
    )
    predictions = torch.randn(4, 10, device=device)
    # WHEN: Call compute_loss
    loss_output = training_module.compute_loss(batch, predictions)
    # THEN: loss is significantly higher than 0
    assert loss_output.total_loss.item() > 1.0
