{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"948799b2-b2de-4348-bcbc-cf7479fc16f2","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"table_identifier\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"group\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"type_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"schema_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}},{\"name\":\"table_identifier_str\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475188288,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-ce5e8a66-b826-4fbc-b880-b69d1770fead-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":6757,"modificationTime":1752475188295,"dataChange":true,"stats":"{\"numRecords\":12,\"minValues\":{\"table_identifier\":{\"schema_hash\":\"036fc4b56b8b9fbc24ca4f4eebd3ee848db2d7cba1e876091471fb3712f7e102\",\"group\":\"recording\",\"type_name\":\"AdaDYOSample\"},\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_dataset_v1/recording_AdaRecording_84a748d58693525c0c949ab8fb83ce822020c9196d5201780687bb9f09f988dd\\\"}\",\"table_identifier_str\":\"recording_AdaRecording_84a748d58693525c0c949ab8fb83ce822020c9196d5201780687bb9f09f988dd\",\"storage_location_type\":\"FileLocation\"},\"maxValues\":{\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_dataset_v1/stream_AdaRadarStream_2243fdd28f04f6eda3650cac1ebe0d2373bc8c5d681974f8a72a4cd97f2587ff\\\"}\",\"table_identifier\":{\"type_name\":\"AdaRecording\",\"schema_hash\":\"f8801988c124759daddfc3215ac5f44377770b8009d0956e26ab0f810d09fff9\",\"group\":\"stream\"},\"table_identifier_str\":\"stream_AdaRadarStream_2243fdd28f04f6eda3650cac1ebe0d2373bc8c5d681974f8a72a4cd97f2587ff\",\"storage_location_type\":\"FileLocation\"},\"nullCount\":{\"table_identifier_str\":0,\"storage_location_type\":0,\"storage_location\":0,\"table_identifier\":{\"schema_hash\":0,\"group\":0,\"type_name\":0}}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475188296,"operation":"WRITE","operationParameters":{"mode":"Append","partitionBy":"[\"partition_id\"]"},"operationMetrics":{"execution_time_ms":7,"num_added_files":1,"num_added_rows":12,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}