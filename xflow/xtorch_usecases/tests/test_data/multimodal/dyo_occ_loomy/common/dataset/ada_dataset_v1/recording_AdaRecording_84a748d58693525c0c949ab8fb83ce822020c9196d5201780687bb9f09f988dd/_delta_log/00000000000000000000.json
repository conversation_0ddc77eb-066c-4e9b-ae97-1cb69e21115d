{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"d91ecde2-0151-47e4-80f0-f28ad32b68e0","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"data_source_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"streams_info\",\"type\":{\"type\":\"map\",\"keyType\":\"string\",\"valueType\":{\"type\":\"struct\",\"fields\":[{\"name\":\"stream_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"schema_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_hash\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]},\"valueContainsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"recording_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"split_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"drive_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"usage\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":false,\"metadata\":{}},{\"name\":\"task\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":false,\"metadata\":{}},{\"name\":\"start_timestamp\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"end_timestamp\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"selection_start_timestamp\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"selection_end_timestamp\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}},{\"name\":\"source_fleet\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"project\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"vin\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"license_plate\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"syscal_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_height\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_rear_axle_to_front\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_rear_axle_to_rear\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_track_width_front\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_track_width_rear\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_vehicle_width_at_front_wheels\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_wheel_base\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_wheel_radius\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_width_with_mirrors\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_geometry_width_without_mirrors\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"vehicle_info_curb_weight\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"latitude\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"longitude\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"altitude\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"country\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"country_iso_alpha3\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"city\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"street\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"road_type\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"lane_count\",\"type\":{\"type\":\"array\",\"elementType\":\"long\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"speed_limit\",\"type\":{\"type\":\"array\",\"elementType\":\"long\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"curvature\",\"type\":{\"type\":\"array\",\"elementType\":\"double\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"is_roundabout\",\"type\":{\"type\":\"array\",\"elementType\":\"boolean\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"is_tunnel\",\"type\":{\"type\":\"array\",\"elementType\":\"boolean\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"is_ramp\",\"type\":{\"type\":\"array\",\"elementType\":\"boolean\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"is_parking_area\",\"type\":{\"type\":\"array\",\"elementType\":\"boolean\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"is_parking_garage\",\"type\":{\"type\":\"array\",\"elementType\":\"boolean\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"is_paved\",\"type\":{\"type\":\"array\",\"elementType\":\"boolean\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"time_of_day\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"weather_atmospheric_condition\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"weather_precipitation\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"weather_sky\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"geography\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"ego_speed\",\"type\":\"double\",\"nullable\":true,\"metadata\":{}},{\"name\":\"ego_direction\",\"type\":{\"type\":\"array\",\"elementType\":\"string\",\"containsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"recording_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"recording_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id_source\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475188038,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-d8619ff4-d8a2-4d3b-a3b9-6fa44c476f9b-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":26775,"modificationTime":1752475188049,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\",\"vehicle_geometry_rear_axle_to_front\":3.973,\"vehicle_geometry_wheel_radius\":0.391,\"vehicle_geometry_width_with_mirrors\":2.19,\"vehicle_geometry_width_without_mirrors\":1.995,\"start_timestamp\":1709546296000000000,\"license_plate\":\"HN-SC 837\",\"source_fleet\":\"ADA_DATA_COLLECTION_FLEET\",\"vehicle_geometry_wheel_base\":2.995,\"data_source_name\":\"ada_databricks\",\"syscal_name\":\"Syscal-Q816-2\",\"vehicle_geometry_height\":1.705,\"vehicle_name\":\"Q816\",\"vehicle_geometry_rear_axle_to_rear\":1.013,\"vin\":\"WAUZZZF16PD000067\",\"end_timestamp\":1709546325000000000,\"vehicle_geometry_track_width_front\":1.679,\"vehicle_geometry_track_width_rear\":1.691,\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"name\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"vehicle_geometry_vehicle_width_at_front_wheels\":1.995},\"maxValues\":{\"vehicle_geometry_height\":1.705,\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"license_plate\":\"HN-SC 837\",\"data_source_name\":\"ada_databricks\",\"syscal_name\":\"Syscal-Q816-2\",\"vehicle_geometry_vehicle_width_at_front_wheels\":1.995,\"vehicle_geometry_wheel_base\":2.995,\"vehicle_geometry_wheel_radius\":0.391,\"vehicle_name\":\"Q816\",\"vehicle_geometry_width_with_mirrors\":2.19,\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"vehicle_geometry_track_width_front\":1.679,\"start_timestamp\":1709546296000000000,\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\",\"vehicle_geometry_rear_axle_to_rear\":1.013,\"vehicle_geometry_track_width_rear\":1.691,\"name\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"vehicle_geometry_rear_axle_to_front\":3.973,\"source_fleet\":\"ADA_DATA_COLLECTION_FLEET\",\"vin\":\"WAUZZZF16PD000067\",\"end_timestamp\":1709546325000000000,\"vehicle_geometry_width_without_mirrors\":1.995},\"nullCount\":{\"vehicle_geometry_vehicle_width_at_front_wheels\":0,\"vehicle_geometry_wheel_radius\":0,\"item\":0,\"end_timestamp\":0,\"start_timestamp\":0,\"vin\":0,\"vehicle_geometry_height\":0,\"syscal_name\":0,\"vehicle_geometry_width_with_mirrors\":0,\"vehicle_geometry_width_without_mirrors\":0,\"project\":1,\"drive_hash\":0,\"vehicle_geometry_track_width_rear\":0,\"vehicle_info_curb_weight\":1,\"selection_end_timestamp\":1,\"key\":0,\"vehicle_geometry_wheel_base\":0,\"split_hash\":0,\"license_plate\":0,\"data_source_name\":0,\"vehicle_name\":0,\"recording_id\":0,\"vehicle_geometry_rear_axle_to_front\":0,\"name\":0,\"selection_start_timestamp\":1,\"source_fleet\":0,\"vehicle_geometry_rear_axle_to_rear\":0,\"vehicle_geometry_track_width_front\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"add":{"path":"partition_id=0/part-00001-4ccdc6b6-97cf-4ed7-9659-8d7f62d8c1d2-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":26930,"modificationTime":1752475188051,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"source_fleet\":\"ADA_DATA_COLLECTION_FLEET\",\"end_timestamp\":1718779267000000000,\"name\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"vehicle_geometry_wheel_radius\":0.391,\"vehicle_geometry_track_width_front\":1.679,\"vehicle_geometry_height\":1.705,\"license_plate\":\"HN-SC 837\",\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\",\"data_source_name\":\"ada_databricks\",\"vehicle_geometry_width_with_mirrors\":2.19,\"vin\":\"WAUZZZF16PD000067\",\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"start_timestamp\":1718779238000000000,\"syscal_name\":\"Syscal-Q816-3\",\"vehicle_geometry_rear_axle_to_front\":3.973,\"vehicle_geometry_track_width_rear\":1.691,\"vehicle_geometry_width_without_mirrors\":1.995,\"vehicle_name\":\"Q816\",\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"vehicle_geometry_rear_axle_to_rear\":1.013,\"vehicle_geometry_vehicle_width_at_front_wheels\":1.995,\"vehicle_geometry_wheel_base\":2.995},\"maxValues\":{\"vehicle_geometry_width_with_mirrors\":2.19,\"name\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"data_source_name\":\"ada_databricks\",\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"vehicle_geometry_track_width_rear\":1.691,\"start_timestamp\":1718779238000000000,\"source_fleet\":\"ADA_DATA_COLLECTION_FLEET\",\"vin\":\"WAUZZZF16PD000067\",\"license_plate\":\"HN-SC 837\",\"vehicle_geometry_rear_axle_to_rear\":1.013,\"vehicle_geometry_rear_axle_to_front\":3.973,\"vehicle_geometry_wheel_radius\":0.391,\"end_timestamp\":1718779267000000000,\"vehicle_geometry_wheel_base\":2.995,\"vehicle_geometry_width_without_mirrors\":1.995,\"vehicle_geometry_vehicle_width_at_front_wheels\":1.995,\"vehicle_name\":\"Q816\",\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\",\"syscal_name\":\"Syscal-Q816-3\",\"vehicle_geometry_height\":1.705,\"vehicle_geometry_track_width_front\":1.679},\"nullCount\":{\"key\":0,\"vehicle_geometry_rear_axle_to_front\":0,\"vehicle_geometry_track_width_front\":0,\"vehicle_geometry_vehicle_width_at_front_wheels\":0,\"name\":0,\"vehicle_geometry_wheel_base\":0,\"vehicle_geometry_width_with_mirrors\":0,\"vehicle_geometry_wheel_radius\":0,\"vin\":0,\"split_hash\":0,\"source_fleet\":0,\"vehicle_geometry_height\":0,\"vehicle_geometry_track_width_rear\":0,\"syscal_name\":0,\"vehicle_geometry_width_without_mirrors\":0,\"start_timestamp\":0,\"item\":0,\"license_plate\":0,\"vehicle_name\":0,\"project\":1,\"selection_start_timestamp\":1,\"recording_id\":0,\"end_timestamp\":0,\"vehicle_info_curb_weight\":1,\"selection_end_timestamp\":1,\"drive_hash\":0,\"vehicle_geometry_rear_axle_to_rear\":0,\"data_source_name\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475188051,"operation":"WRITE","operationParameters":{"partitionBy":"[\"partition_id\"]","mode":"Append"},"operationMetrics":{"execution_time_ms":12,"num_added_files":2,"num_added_rows":2,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}