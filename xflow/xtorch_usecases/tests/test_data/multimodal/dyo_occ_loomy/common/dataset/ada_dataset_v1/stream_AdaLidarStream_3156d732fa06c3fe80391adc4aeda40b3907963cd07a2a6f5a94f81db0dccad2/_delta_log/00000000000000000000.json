{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"d655160c-b1ee-42b7-90f1-e031c64f1376","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"recording_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"stream_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_schema_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_version\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_input_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"drive_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"split_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sensor_stream_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sensor_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"extractor_version\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"syscal_rotation\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"x\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"y\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"z\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"w\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}},{\"name\":\"syscal_translation\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"x\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"y\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"z\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}},{\"name\":\"offline_raw_rotation\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"x\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"y\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"z\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"w\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":true,\"metadata\":{}},{\"name\":\"offline_raw_translation\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"x\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"y\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}},{\"name\":\"z\",\"type\":\"double\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":true,\"metadata\":{}},{\"name\":\"container\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"data_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":true,\"metadata\":{}},{\"name\":\"type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"egomotion_compensated\",\"type\":\"boolean\",\"nullable\":false,\"metadata\":{}},{\"name\":\"undistorted\",\"type\":\"boolean\",\"nullable\":false,\"metadata\":{}},{\"name\":\"stream_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"stream_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id_source\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475188219,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-ae52fe33-eee4-4dd7-805c-9568429a5e0c-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":17047,"modificationTime":1752475188226,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"sample_schema_hash\":\"70a1c76cabfe271290a08f45e5c36394cd513f0f16b72f79262a48855612c784\",\"sensor_name\":\"lidar_motion_compensated_center\",\"processor_input_hash\":\"0272bbb9e76e9aea3f7eae5859978af9b17bed66c09cc1827c4cb42593df85c7\",\"sample_type\":\"AdaLidarSample\",\"sensor_stream_hash\":\"17aae0d246b31cbe2cbe3c1ad88f7c052721991ccc56c3b6056fbc0a1f8d4698_motion_compensated\",\"syscal_translation\":{\"x\":-0.0,\"y\":-0.0,\"z\":-0.0},\"offline_raw_translation\":{},\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\",\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"offline_raw_rotation\":{},\"stream_id\":\"64ee3bcc72ea63e374dc82868ba83eb0c68dccd0a88fe8acc29a634b6471264e\",\"extractor_version\":\"0.5.6\",\"processor_hash\":\"36110ea8d346b31ef1df154f9e134c78ba80a0082bd45b9ce3cef86d90aa413d\",\"name\":\"lidar_motion_compensated_center\",\"processor_name\":\"IdentityProcessor\",\"processor_version\":\"1.0\",\"container\":{},\"syscal_rotation\":{\"y\":-0.0,\"w\":1.0,\"x\":-0.0,\"z\":-0.0}},\"maxValues\":{\"offline_raw_translation\":{},\"extractor_version\":\"0.5.6\",\"processor_version\":\"1.0\",\"offline_raw_rotation\":{},\"name\":\"lidar_motion_compensated_center\",\"sensor_name\":\"lidar_motion_compensated_center\",\"syscal_translation\":{\"y\":0.0,\"x\":0.0,\"z\":0.0},\"sample_schema_hash\":\"70a1c76cabfe271290a08f45e5c36394cd513f0f16b72f79262a48855612c784\",\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\",\"processor_input_hash\":\"0272bbb9e76e9aea3f7eae5859978af9b17bed66c09cc1827c4cb42593df85c7\",\"sample_type\":\"AdaLidarSample\",\"stream_id\":\"64ee3bcc72ea63e374dc82868ba83eb0c68dccd0a88fe8acc29a634b6471264e\",\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"processor_hash\":\"36110ea8d346b31ef1df154f9e134c78ba80a0082bd45b9ce3cef86d90aa413d\",\"sensor_stream_hash\":\"17aae0d246b31cbe2cbe3c1ad88f7c052721991ccc56c3b6056fbc0a1f8d4698_motion_compensated\",\"processor_name\":\"IdentityProcessor\",\"syscal_rotation\":{\"y\":0.0,\"x\":0.0,\"z\":0.0,\"w\":1.0},\"container\":{}},\"nullCount\":{\"container\":{\"storage_location\":1,\"digest\":1,\"digest_type\":1,\"data_type\":1},\"syscal_rotation\":{\"x\":0,\"y\":0,\"w\":0,\"z\":0},\"sample_schema_hash\":0,\"processor_name\":0,\"drive_hash\":0,\"sensor_name\":0,\"split_hash\":0,\"recording_id\":0,\"sensor_stream_hash\":0,\"extractor_version\":0,\"offline_raw_rotation\":{\"w\":1,\"y\":1,\"z\":1,\"x\":1},\"stream_id\":0,\"processor_input_hash\":0,\"processor_version\":0,\"sample_type\":0,\"offline_raw_translation\":{\"x\":1,\"y\":1,\"z\":1},\"syscal_translation\":{\"x\":0,\"y\":0,\"z\":0},\"name\":0,\"processor_hash\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"add":{"path":"partition_id=0/part-00001-a9e052fa-c28c-4904-b0e4-698b8c925b22-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":17154,"modificationTime":1752475188226,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"container\":{},\"processor_version\":\"1.0\",\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"sample_type\":\"AdaLidarSample\",\"sensor_name\":\"lidar_motion_compensated_center\",\"syscal_rotation\":{\"x\":-0.0,\"y\":-0.0,\"z\":-0.0,\"w\":1.0},\"offline_raw_rotation\":{},\"processor_hash\":\"530115ad47ae7887b9c3bed5834598fded56dde05ba46606f4a660639187f402\",\"stream_id\":\"11d418fe5e8945cfa1e2c55d5489a566e42db96aec9e21ee4563ec841087a965\",\"processor_name\":\"IdentityProcessor\",\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\",\"syscal_translation\":{\"z\":-0.0,\"x\":-0.0,\"y\":-0.0},\"processor_input_hash\":\"780676650be4008d44a5e198a9a7af57c639cd697ea67e5051aebe64fbe9663c\",\"name\":\"lidar_motion_compensated_center\",\"sensor_stream_hash\":\"6e98dd80916382de5314248caadc32d8677b36b8d9a12f32ea5c82c28008b50d_motion_compensated\",\"offline_raw_translation\":{},\"sample_schema_hash\":\"70a1c76cabfe271290a08f45e5c36394cd513f0f16b72f79262a48855612c784\",\"extractor_version\":\"0.5.9.dev10+g0562c2c\"},\"maxValues\":{\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"offline_raw_rotation\":{},\"processor_version\":\"1.0\",\"sensor_stream_hash\":\"6e98dd80916382de5314248caadc32d8677b36b8d9a12f32ea5c82c28008b50d_motion_compensated\",\"processor_name\":\"IdentityProcessor\",\"syscal_translation\":{\"z\":0.0,\"x\":0.0,\"y\":0.0},\"sample_type\":\"AdaLidarSample\",\"extractor_version\":\"0.5.9.dev10+g0562c2c\",\"syscal_rotation\":{\"z\":0.0,\"x\":0.0,\"y\":0.0,\"w\":1.0},\"processor_input_hash\":\"780676650be4008d44a5e198a9a7af57c639cd697ea67e5051aebe64fbe9663c\",\"offline_raw_translation\":{},\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\",\"sample_schema_hash\":\"70a1c76cabfe271290a08f45e5c36394cd513f0f16b72f79262a48855612c784\",\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"container\":{},\"processor_hash\":\"530115ad47ae7887b9c3bed5834598fded56dde05ba46606f4a660639187f402\",\"name\":\"lidar_motion_compensated_center\",\"sensor_name\":\"lidar_motion_compensated_center\",\"stream_id\":\"11d418fe5e8945cfa1e2c55d5489a566e42db96aec9e21ee4563ec841087a965\"},\"nullCount\":{\"processor_name\":0,\"syscal_rotation\":{\"w\":0,\"x\":0,\"z\":0,\"y\":0},\"syscal_translation\":{\"x\":0,\"y\":0,\"z\":0},\"offline_raw_rotation\":{\"z\":1,\"w\":1,\"x\":1,\"y\":1},\"sample_type\":0,\"drive_hash\":0,\"name\":0,\"container\":{\"digest_type\":1,\"digest\":1,\"storage_location\":1,\"data_type\":1},\"processor_version\":0,\"offline_raw_translation\":{\"y\":1,\"z\":1,\"x\":1},\"sensor_stream_hash\":0,\"split_hash\":0,\"recording_id\":0,\"sensor_name\":0,\"sample_schema_hash\":0,\"stream_id\":0,\"extractor_version\":0,\"processor_input_hash\":0,\"processor_hash\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475188226,"operation":"WRITE","operationParameters":{"partitionBy":"[\"partition_id\"]","mode":"Append"},"operationMetrics":{"execution_time_ms":6,"num_added_files":2,"num_added_rows":2,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}