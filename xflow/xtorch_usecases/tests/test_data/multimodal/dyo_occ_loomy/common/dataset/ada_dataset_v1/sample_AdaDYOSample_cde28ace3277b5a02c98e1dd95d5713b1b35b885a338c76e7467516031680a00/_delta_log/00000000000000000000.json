{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"a61220a2-4868-43f9-aafe-34fe7ab4eaad","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"timestamp\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"annotation\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"data_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"sample_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id_source\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475188237,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-1e92e906-6aed-4192-8a02-1c65f4bcc22d-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":8862,"modificationTime":1752475188244,"dataChange":true,"stats":"{\"numRecords\":5,\"minValues\":{\"processor_hash\":\"70ae38a1931c761ddb81f32323f20a53752c9a93e46b45c5e6e538a2dcb0d1d1\",\"sample_type\":\"AdaDYOSample\",\"sample_hash\":\"74091c9e31cfc0407b28198fcd9a7fa8ad173c64cbd2ce5c8b7c62fc9757536f\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"name\":\"jbf_annotation_split_33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86_frame_241\",\"annotation\":{\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/3081d17628697bdfc05b01e806678f1d971247065620b14c38ed56ef671c3927.json\\\"}\",\"data_type\":\"JsonData\",\"digest\":\"3081d17628697bdfc05b01e806678f1d971247065620b14c38ed56ef671c3927\",\"digest_type\":\"sha256\",\"storage_location_type\":\"FileLocation\"},\"timestamp\":1709546313800763904,\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\"},\"maxValues\":{\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"name\":\"jbf_annotation_split_33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86_frame_269\",\"annotation\":{\"digest_type\":\"sha256\",\"storage_location_type\":\"FileLocation\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/fbd636b16ba5b1bce935bff3c9e9a20de21cf19b969bee3b6aa3f0195d8d0880.json\\\"}\",\"data_type\":\"JsonData\",\"digest\":\"fbd636b16ba5b1bce935bff3c9e9a20de21cf19b969bee3b6aa3f0195d8d0880\"},\"processor_hash\":\"70ae38a1931c761ddb81f32323f20a53752c9a93e46b45c5e6e538a2dcb0d1d1\",\"sample_type\":\"AdaDYOSample\",\"timestamp\":1709546315667423744,\"sample_hash\":\"bf4b188e9c481634aa3c431ffbdcc033e2fca2d152bd56b44bef7ca046b5e8a9\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"creation_timestamp\":\"2025/07/14-06:39:46\"},\"nullCount\":{\"modification_timestamp\":5,\"timestamp\":0,\"creation_timestamp\":0,\"name\":0,\"sample_hash\":0,\"sample_type\":0,\"processor_hash\":0,\"partition_id_source\":0,\"annotation\":{\"digest_type\":0,\"digest\":0,\"data_type\":0,\"storage_location\":0,\"storage_location_type\":0},\"git_commit_hash\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"add":{"path":"partition_id=0/part-00001-99b6b25d-2680-4749-8c8c-81be8be8aa89-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":8864,"modificationTime":1752475188244,"dataChange":true,"stats":"{\"numRecords\":5,\"minValues\":{\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"annotation\":{\"digest\":\"7a2d376b671e20b833cb04898ec8d171b172784376dfad8eb8f6ffed784e0d78\",\"digest_type\":\"sha256\",\"data_type\":\"JsonData\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/7a2d376b671e20b833cb04898ec8d171b172784376dfad8eb8f6ffed784e0d78.json\\\"}\",\"storage_location_type\":\"FileLocation\"},\"processor_hash\":\"1db1e27c48124740c9e8e9fa5f70cd98ace63800e0f5de096a40efbbb11e5cd8\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"timestamp\":1718779258467416576,\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"name\":\"jbf_annotation_split_6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727_frame_241\",\"sample_type\":\"AdaDYOSample\",\"sample_hash\":\"0ecf989aa6948746906e66179cacb90a516dac50d9bf2aef396e167fef20feff\"},\"maxValues\":{\"sample_hash\":\"ecd00ffc8fff8c77db5099b5ab4a2837be8727583acae9e92240c00abbac3dee\",\"annotation\":{\"digest_type\":\"sha256\",\"digest\":\"ee9176d69f22636e4bbcb0e6fbc6585b77e6b03e45c21c2b4bd8cc1e34354bac\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/ee9176d69f22636e4bbcb0e6fbc6585b77e6b03e45c21c2b4bd8cc1e34354bac.json\\\"}\",\"data_type\":\"JsonData\",\"storage_location_type\":\"FileLocation\"},\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"timestamp\":1718779260334096128,\"name\":\"jbf_annotation_split_6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727_frame_269\",\"processor_hash\":\"1db1e27c48124740c9e8e9fa5f70cd98ace63800e0f5de096a40efbbb11e5cd8\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"sample_type\":\"AdaDYOSample\"},\"nullCount\":{\"partition_id_source\":0,\"processor_hash\":0,\"creation_timestamp\":0,\"sample_hash\":0,\"git_commit_hash\":0,\"timestamp\":0,\"modification_timestamp\":5,\"name\":0,\"annotation\":{\"storage_location_type\":0,\"digest\":0,\"storage_location\":0,\"digest_type\":0,\"data_type\":0},\"sample_type\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475188244,"operation":"WRITE","operationParameters":{"partitionBy":"[\"partition_id\"]","mode":"Append"},"clientVersion":"delta-rs.py-1.0.2","operationMetrics":{"execution_time_ms":7,"num_added_files":2,"num_added_rows":10,"num_partitions":0,"num_removed_files":0}}}