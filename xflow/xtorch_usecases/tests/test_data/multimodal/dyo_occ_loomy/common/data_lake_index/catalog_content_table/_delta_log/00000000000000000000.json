{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"9fac9dac-0458-48f9-8741-76d640a89185","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"table_identifier\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"group\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"type_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"schema_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}},{\"name\":\"table_identifier_str\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":true,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475188013,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-d2f3f6c9-34ee-450e-aa50-7024b04fdef5-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":6692,"modificationTime":1752475188022,"dataChange":true,"stats":"{\"numRecords\":12,\"minValues\":{\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake_index/recording_AdaRecording_84a748d58693525c0c949ab8fb83ce822020c9196d5201780687bb9f09f988dd\\\"}\",\"table_identifier\":{\"schema_hash\":\"036fc4b56b8b9fbc24ca4f4eebd3ee848db2d7cba1e876091471fb3712f7e102\",\"group\":\"recording\",\"type_name\":\"AdaDYOSample\"},\"table_identifier_str\":\"recording_AdaRecording_84a748d58693525c0c949ab8fb83ce822020c9196d5201780687bb9f09f988dd\",\"storage_location_type\":\"FileLocation\"},\"maxValues\":{\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake_index/stream_AdaRadarStream_2243fdd28f04f6eda3650cac1ebe0d2373bc8c5d681974f8a72a4cd97f2587ff\\\"}\",\"table_identifier\":{\"schema_hash\":\"f8801988c124759daddfc3215ac5f44377770b8009d0956e26ab0f810d09fff9\",\"group\":\"stream\",\"type_name\":\"AdaRecording\"},\"storage_location_type\":\"FileLocation\",\"table_identifier_str\":\"stream_AdaRadarStream_2243fdd28f04f6eda3650cac1ebe0d2373bc8c5d681974f8a72a4cd97f2587ff\"},\"nullCount\":{\"storage_location_type\":0,\"table_identifier_str\":0,\"storage_location\":0,\"table_identifier\":{\"type_name\":0,\"schema_hash\":0,\"group\":0}}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475188022,"operation":"WRITE","operationParameters":{"mode":"Append","partitionBy":"[\"partition_id\"]"},"clientVersion":"delta-rs.py-1.0.2","operationMetrics":{"execution_time_ms":9,"num_added_files":1,"num_added_rows":12,"num_partitions":0,"num_removed_files":0}}}