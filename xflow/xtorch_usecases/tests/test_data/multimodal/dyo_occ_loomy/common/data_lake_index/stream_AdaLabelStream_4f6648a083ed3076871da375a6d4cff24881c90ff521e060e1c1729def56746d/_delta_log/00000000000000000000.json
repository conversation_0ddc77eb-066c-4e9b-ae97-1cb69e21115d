{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"3fba7d12-d68e-4767-bb98-607648eb4fd8","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"recording_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"stream_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_schema_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_version\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_input_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"drive_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"split_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"container\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"data_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":true,\"metadata\":{}},{\"name\":\"stream_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"stream_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id_source\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475187790,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-82aea2f4-8eb5-49f3-80c1-2d5bc7266b65-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":11119,"modificationTime":1752475187792,"dataChange":true,"stats":"{\"numRecords\":2,\"minValues\":{\"processor_input_hash\":\"431c3b1b5d7a77c1f678392ba7f2fac06da72228cad1896979686533e1cb302d\",\"processor_name\":\"IdentityProcessor\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"name\":\"gt_auto_label\",\"stream_hash\":\"5170b17c22d3fb5965329169b0237ba2bcd9173d1c441571d902f80ef4937f1a\",\"sample_schema_hash\":\"cde28ace3277b5a02c98e1dd95d5713b1b35b885a338c76e7467516031680a00\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"processor_hash\":\"7028967d68c5a0d8012d0c6695ed400013c86439c71fa1836a2ad3dad2f6380f\",\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"stream_id\":\"5c371683e13ddc3412420b72e72b66fc8781fbe1b08409c3cdfb1ebacdf3b36e\",\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"sample_type\":\"AdaDYOSample\",\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"processor_version\":\"1.0\",\"container\":{},\"stream_type\":\"AdaLabelStream\",\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\"},\"maxValues\":{\"processor_hash\":\"70ae38a1931c761ddb81f32323f20a53752c9a93e46b45c5e6e538a2dcb0d1d1\",\"container\":{},\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"stream_hash\":\"da9e3dc95f5f4db71d21b7242926d0ee4f312c7c2610ecbb34a116fa564c3c05\",\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"sample_type\":\"AdaOccupancySampleDualGrid\",\"sample_schema_hash\":\"f8801988c124759daddfc3215ac5f44377770b8009d0956e26ab0f810d09fff9\",\"processor_name\":\"IdentityProcessor\",\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"name\":\"occupancy_label\",\"stream_id\":\"74aa9e0665f5225ab3031f7f7980c567f090a446837509252ae82a82dd040d09\",\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"stream_type\":\"AdaLabelStream\",\"processor_input_hash\":\"b88f83c889eafc1358a94f1123961b610aaa124dea887051346e8da3687e2cd6\",\"processor_version\":\"1.0\"},\"nullCount\":{\"partition_id_source\":0,\"git_commit_hash\":0,\"processor_version\":0,\"stream_type\":0,\"processor_hash\":0,\"name\":0,\"container\":{\"digest\":2,\"data_type\":2,\"digest_type\":2,\"storage_location\":2,\"storage_location_type\":2},\"stream_id\":0,\"recording_id\":0,\"drive_hash\":0,\"creation_timestamp\":0,\"modification_timestamp\":2,\"sample_schema_hash\":0,\"split_hash\":0,\"stream_hash\":0,\"processor_input_hash\":0,\"processor_name\":0,\"sample_type\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"add":{"path":"partition_id=0/part-00001-51f428be-3183-42c1-8d4b-6e9a322bca11-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":11118,"modificationTime":1752475187793,"dataChange":true,"stats":"{\"numRecords\":2,\"minValues\":{\"name\":\"gt_auto_label\",\"stream_id\":\"692f3498fc3104a3645f6b3d4a696786afe8a9c4c99d12f5a54ff6e4db90244b\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"sample_schema_hash\":\"cde28ace3277b5a02c98e1dd95d5713b1b35b885a338c76e7467516031680a00\",\"processor_input_hash\":\"175e232b549cebb6a83244aba443dc7ed73805ee0f5ebf9df239be98436a0764\",\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"container\":{},\"stream_type\":\"AdaLabelStream\",\"processor_hash\":\"0453b9411be451bc3676e2f78fa62838de3aa2e9ccee0459de7018bc13cd80c3\",\"stream_hash\":\"3620effcc2b697cbd431a5e9a13f3f0c77ed15fe0d1fac9a7dd92e55ec644054\",\"sample_type\":\"AdaDYOSample\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"processor_name\":\"IdentityProcessor\",\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"processor_version\":\"1.0\",\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\"},\"maxValues\":{\"stream_hash\":\"801d2e1a5827bd8fa023f623dc9c16ba3c8def3c14e12c1ec14e81e67578b6c2\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"name\":\"occupancy_label\",\"stream_type\":\"AdaLabelStream\",\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"sample_type\":\"AdaOccupancySampleDualGrid\",\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\",\"container\":{},\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"sample_schema_hash\":\"f8801988c124759daddfc3215ac5f44377770b8009d0956e26ab0f810d09fff9\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"processor_name\":\"IdentityProcessor\",\"processor_input_hash\":\"6f4e0b7b3dd12de0deb30b9185af294688f86c1df5dd7892c891081e73332503\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"stream_id\":\"f4c0c973fbf5d25fd97111598d779d50f3484afae73fbf39226c1adecded42b9\",\"processor_version\":\"1.0\",\"processor_hash\":\"1db1e27c48124740c9e8e9fa5f70cd98ace63800e0f5de096a40efbbb11e5cd8\"},\"nullCount\":{\"processor_hash\":0,\"stream_hash\":0,\"name\":0,\"drive_hash\":0,\"git_commit_hash\":0,\"sample_schema_hash\":0,\"split_hash\":0,\"modification_timestamp\":2,\"stream_type\":0,\"container\":{\"digest_type\":2,\"storage_location_type\":2,\"digest\":2,\"data_type\":2,\"storage_location\":2},\"processor_input_hash\":0,\"sample_type\":0,\"stream_id\":0,\"processor_version\":0,\"recording_id\":0,\"processor_name\":0,\"partition_id_source\":0,\"creation_timestamp\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475187793,"operation":"WRITE","operationParameters":{"mode":"Append","partitionBy":"[\"partition_id\"]"},"clientVersion":"delta-rs.py-1.0.2","operationMetrics":{"execution_time_ms":3,"num_added_files":2,"num_added_rows":4,"num_partitions":0,"num_removed_files":0}}}