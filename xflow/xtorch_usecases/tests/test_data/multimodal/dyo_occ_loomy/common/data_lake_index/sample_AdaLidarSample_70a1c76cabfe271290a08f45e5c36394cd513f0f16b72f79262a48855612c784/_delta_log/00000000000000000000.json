{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"042e2e6e-df81-44d8-9dfb-06bb603ad392","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"timestamp\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"point_cloud\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"data_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}},{\"name\":\"min_timestamp\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}},{\"name\":\"max_timestamp\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"sample_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id_source\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475187920,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-3e3fbb0e-47f7-42b9-b6d8-786d5d5f38dd-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":9698,"modificationTime":1752475187925,"dataChange":true,"stats":"{\"numRecords\":5,\"minValues\":{\"sample_type\":\"AdaLidarSample\",\"processor_hash\":\"36110ea8d346b31ef1df154f9e134c78ba80a0082bd45b9ce3cef86d90aa413d\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"point_cloud\":{\"data_type\":\"PointCloudData\",\"digest\":\"22381ace82695f883dbfe353ba5942f295ad7b70bf768a8b83c9c3831ccb7967\",\"digest_type\":\"sha256\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/22381ace82695f883dbfe353ba5942f295ad7b70bf768a8b83c9c3831ccb7967.pcd\\\"}\",\"storage_location_type\":\"FileLocation\"},\"name\":\"22381ace82695f883dbfe353ba5942f295ad7b70bf768a8b83c9c3831ccb7967\",\"sample_hash\":\"0a7ed1f80b35f95f1855b21a7b4fdc87a23f02f38ba789385e31f1f2a19c4ba5\",\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"max_timestamp\":1709546313801466880,\"creation_timestamp\":\"2025/07/14-06:39:46\",\"timestamp\":1709546313801466880,\"min_timestamp\":1709546313801466880},\"maxValues\":{\"max_timestamp\":1709546315668143872,\"creation_timestamp\":\"2025/07/14-06:39:46\",\"point_cloud\":{\"digest\":\"e7424d2f1bb14f19aff4f6ca287a93fca3eac9a873cb656a29ab7b0d696bc6ff\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/e7424d2f1bb14f19aff4f6ca287a93fca3eac9a873cb656a29ab7b0d696bc6ff.pcd\\\"}\",\"data_type\":\"PointCloudData\",\"digest_type\":\"sha256\",\"storage_location_type\":\"FileLocation\"},\"min_timestamp\":1709546315668143872,\"sample_type\":\"AdaLidarSample\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"timestamp\":1709546315668143872,\"sample_hash\":\"a2f7ceb639c1c2639aea0b6193f901ef86022ed50ebc9380fb6658f6beea7433\",\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"processor_hash\":\"36110ea8d346b31ef1df154f9e134c78ba80a0082bd45b9ce3cef86d90aa413d\",\"name\":\"e7424d2f1bb14f19aff4f6ca287a93fca3eac9a873cb656a29ab7b0d696bc6ff\"},\"nullCount\":{\"sample_hash\":0,\"timestamp\":0,\"partition_id_source\":0,\"name\":0,\"modification_timestamp\":5,\"point_cloud\":{\"storage_location\":0,\"data_type\":0,\"digest\":0,\"digest_type\":0,\"storage_location_type\":0},\"min_timestamp\":0,\"max_timestamp\":0,\"sample_type\":0,\"processor_hash\":0,\"git_commit_hash\":0,\"creation_timestamp\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"add":{"path":"partition_id=0/part-00001-1a7cb199-28cf-465d-9269-6aeb0099f207-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":9697,"modificationTime":1752475187925,"dataChange":true,"stats":"{\"numRecords\":5,\"minValues\":{\"sample_hash\":\"35b45d8849a07176992ac722820a92f3e2e36c8dd111ecd984900a78cab895f6\",\"max_timestamp\":1718779258468145152,\"min_timestamp\":1718779258468145152,\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"sample_type\":\"AdaLidarSample\",\"point_cloud\":{\"digest_type\":\"sha256\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/1becb77f382bb27f998d184651a55a1a6d38b4a045cbc863cf54510df3889165.pcd\\\"}\",\"digest\":\"1becb77f382bb27f998d184651a55a1a6d38b4a045cbc863cf54510df3889165\",\"data_type\":\"PointCloudData\",\"storage_location_type\":\"FileLocation\"},\"timestamp\":1718779258468145152,\"name\":\"1becb77f382bb27f998d184651a55a1a6d38b4a045cbc863cf54510df3889165\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"processor_hash\":\"530115ad47ae7887b9c3bed5834598fded56dde05ba46606f4a660639187f402\"},\"maxValues\":{\"timestamp\":1718779260334798592,\"name\":\"e0dc27f82e65a93019ef599195a76d217c1d3db2bfa64db8494ed01e8db32602\",\"max_timestamp\":1718779260334798592,\"processor_hash\":\"530115ad47ae7887b9c3bed5834598fded56dde05ba46606f4a660639187f402\",\"point_cloud\":{\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/e0dc27f82e65a93019ef599195a76d217c1d3db2bfa64db8494ed01e8db32602.pcd\\\"}\",\"storage_location_type\":\"FileLocation\",\"digest\":\"e0dc27f82e65a93019ef599195a76d217c1d3db2bfa64db8494ed01e8db32602\",\"data_type\":\"PointCloudData\",\"digest_type\":\"sha256\"},\"min_timestamp\":1718779260334798592,\"sample_type\":\"AdaLidarSample\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"sample_hash\":\"f878d0bdd126c67dab86ec48935afdec3aca65c44cb9adefa607acdb67770dfb\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\"},\"nullCount\":{\"point_cloud\":{\"digest\":0,\"data_type\":0,\"storage_location_type\":0,\"digest_type\":0,\"storage_location\":0},\"sample_hash\":0,\"name\":0,\"timestamp\":0,\"min_timestamp\":0,\"sample_type\":0,\"processor_hash\":0,\"git_commit_hash\":0,\"partition_id_source\":0,\"max_timestamp\":0,\"creation_timestamp\":0,\"modification_timestamp\":5}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475187926,"operation":"WRITE","operationParameters":{"mode":"Append","partitionBy":"[\"partition_id\"]"},"clientVersion":"delta-rs.py-1.0.2","operationMetrics":{"execution_time_ms":6,"num_added_files":2,"num_added_rows":10,"num_partitions":0,"num_removed_files":0}}}