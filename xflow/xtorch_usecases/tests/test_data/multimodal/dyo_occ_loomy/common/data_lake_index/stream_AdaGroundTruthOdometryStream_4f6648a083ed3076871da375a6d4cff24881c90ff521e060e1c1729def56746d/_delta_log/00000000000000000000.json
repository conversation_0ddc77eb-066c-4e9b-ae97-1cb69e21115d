{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"00ed771c-5ad0-4162-a1e1-5b89f2cd24fd","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"recording_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"stream_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_schema_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_version\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_input_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"drive_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"split_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"container\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"data_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":true,\"metadata\":{}},{\"name\":\"stream_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"stream_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id_source\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475187799,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-b79c0337-8e95-4042-b0ff-d5e2e84f243d-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":10869,"modificationTime":1752475187804,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"processor_name\":\"IdentityProcessor\",\"sample_type\":\"AdaGroundTruthOdometrySample\",\"processor_version\":\"1.0\",\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"sample_schema_hash\":\"d48ddfb4820e78d29366e2cfa1047b22c35c176af6f7c8d76b03e728c583649c\",\"name\":\"gt_odometry\",\"stream_hash\":\"7442ce4e70d35cde80cf7a7bb2191d9b7660f671fe44b7dc8d807ce2090beefb\",\"processor_hash\":\"6746c2d2a0955cb56530e4b5f2b0c31abe252ae1c30e16627f0cc5b6de913a84\",\"stream_id\":\"d1c6b5dc300e5b1751b26ddac55e913513ab6482ece34d28c0f9bc2bcb07265f\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"stream_type\":\"AdaGroundTruthOdometryStream\",\"processor_input_hash\":\"7490a0286871206ffb5c8407afe224d4855e334644811c466351150e094c703d\",\"container\":{},\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\"},\"maxValues\":{\"split_hash\":\"33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86\",\"recording_id\":\"4b13ed0321e014bf6def838bdeabfe91c42d1513d70c9f27bdeaab1a190a2658\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"stream_type\":\"AdaGroundTruthOdometryStream\",\"sample_schema_hash\":\"d48ddfb4820e78d29366e2cfa1047b22c35c176af6f7c8d76b03e728c583649c\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"stream_id\":\"d1c6b5dc300e5b1751b26ddac55e913513ab6482ece34d28c0f9bc2bcb07265f\",\"processor_name\":\"IdentityProcessor\",\"sample_type\":\"AdaGroundTruthOdometrySample\",\"processor_version\":\"1.0\",\"name\":\"gt_odometry\",\"processor_input_hash\":\"7490a0286871206ffb5c8407afe224d4855e334644811c466351150e094c703d\",\"processor_hash\":\"6746c2d2a0955cb56530e4b5f2b0c31abe252ae1c30e16627f0cc5b6de913a84\",\"stream_hash\":\"7442ce4e70d35cde80cf7a7bb2191d9b7660f671fe44b7dc8d807ce2090beefb\",\"drive_hash\":\"3e804cc1598426cc90f87472a1b4225018ac9afeb8e6c4311db30df07a1666cd\",\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"container\":{}},\"nullCount\":{\"recording_id\":0,\"processor_input_hash\":0,\"split_hash\":0,\"sample_schema_hash\":0,\"container\":{\"digest_type\":1,\"storage_location\":1,\"digest\":1,\"data_type\":1,\"storage_location_type\":1},\"name\":0,\"git_commit_hash\":0,\"stream_hash\":0,\"processor_name\":0,\"modification_timestamp\":1,\"partition_id_source\":0,\"stream_type\":0,\"stream_id\":0,\"processor_hash\":0,\"processor_version\":0,\"sample_type\":0,\"drive_hash\":0,\"creation_timestamp\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"add":{"path":"partition_id=0/part-00001-daaa4368-7d26-4c68-848e-dfae7aa74e1a-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":10869,"modificationTime":1752475187804,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"stream_id\":\"94ce742ad7faf23a54a5d9a33e178bbe70912741710d9231a88cf2a8b2afa249\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\",\"sample_type\":\"AdaGroundTruthOdometrySample\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"name\":\"gt_odometry\",\"stream_hash\":\"5a1a79b00e4075ae514786a5f811a7e9ec4c99d33936cedee818b6a52ce4bfd4\",\"stream_type\":\"AdaGroundTruthOdometryStream\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"sample_schema_hash\":\"d48ddfb4820e78d29366e2cfa1047b22c35c176af6f7c8d76b03e728c583649c\",\"processor_version\":\"1.0\",\"processor_name\":\"IdentityProcessor\",\"processor_input_hash\":\"36654bc7d6ced27b522351fdac66d8c22030a45cee479034c56aa052a5f33a45\",\"container\":{},\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"processor_hash\":\"239bffb7c8ae071d999bc858151b5944903698fd4f33b26c12913d60f1f3cd5c\"},\"maxValues\":{\"split_hash\":\"6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"stream_hash\":\"5a1a79b00e4075ae514786a5f811a7e9ec4c99d33936cedee818b6a52ce4bfd4\",\"name\":\"gt_odometry\",\"processor_hash\":\"239bffb7c8ae071d999bc858151b5944903698fd4f33b26c12913d60f1f3cd5c\",\"sample_schema_hash\":\"d48ddfb4820e78d29366e2cfa1047b22c35c176af6f7c8d76b03e728c583649c\",\"stream_type\":\"AdaGroundTruthOdometryStream\",\"processor_name\":\"IdentityProcessor\",\"stream_id\":\"94ce742ad7faf23a54a5d9a33e178bbe70912741710d9231a88cf2a8b2afa249\",\"recording_id\":\"216aadb3043b5be1cd4975cffd4b48ee907e4a1408f73bdcf369211165929714\",\"sample_type\":\"AdaGroundTruthOdometrySample\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"container\":{},\"processor_version\":\"1.0\",\"drive_hash\":\"f126ea09440862175471c1407b7bb7355ce0b36741cbf8338d1cac2014b35f25\",\"processor_input_hash\":\"36654bc7d6ced27b522351fdac66d8c22030a45cee479034c56aa052a5f33a45\"},\"nullCount\":{\"modification_timestamp\":1,\"name\":0,\"drive_hash\":0,\"sample_type\":0,\"container\":{\"digest\":1,\"digest_type\":1,\"storage_location_type\":1,\"storage_location\":1,\"data_type\":1},\"git_commit_hash\":0,\"stream_type\":0,\"creation_timestamp\":0,\"processor_version\":0,\"partition_id_source\":0,\"recording_id\":0,\"stream_id\":0,\"sample_schema_hash\":0,\"processor_input_hash\":0,\"processor_hash\":0,\"stream_hash\":0,\"split_hash\":0,\"processor_name\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475187805,"operation":"WRITE","operationParameters":{"partitionBy":"[\"partition_id\"]","mode":"Append"},"operationMetrics":{"execution_time_ms":6,"num_added_files":2,"num_added_rows":2,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}