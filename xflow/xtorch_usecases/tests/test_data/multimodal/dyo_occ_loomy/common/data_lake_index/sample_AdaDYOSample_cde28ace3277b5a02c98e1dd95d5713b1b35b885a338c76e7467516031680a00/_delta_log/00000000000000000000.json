{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"5728056f-696e-45ea-87fa-6944f9b8631f","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"timestamp\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}},{\"name\":\"processor_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"annotation\",\"type\":{\"type\":\"struct\",\"fields\":[{\"name\":\"storage_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"data_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"digest_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"storage_location_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"nullable\":false,\"metadata\":{}},{\"name\":\"sample_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"sample_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id_source\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_id\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}}]}","partitionColumns":["partition_id"],"createdTime":1752475187942,"configuration":{}}}
{"add":{"path":"partition_id=0/part-00001-71af9523-5276-4266-8216-561dc94e0395-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":8862,"modificationTime":1752475187954,"dataChange":true,"stats":"{\"numRecords\":5,\"minValues\":{\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"name\":\"jbf_annotation_split_33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86_frame_241\",\"processor_hash\":\"70ae38a1931c761ddb81f32323f20a53752c9a93e46b45c5e6e538a2dcb0d1d1\",\"sample_hash\":\"74091c9e31cfc0407b28198fcd9a7fa8ad173c64cbd2ce5c8b7c62fc9757536f\",\"annotation\":{\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/3081d17628697bdfc05b01e806678f1d971247065620b14c38ed56ef671c3927.json\\\"}\",\"data_type\":\"JsonData\",\"digest\":\"3081d17628697bdfc05b01e806678f1d971247065620b14c38ed56ef671c3927\",\"digest_type\":\"sha256\",\"storage_location_type\":\"FileLocation\"},\"creation_timestamp\":\"2025/07/14-06:39:46\",\"sample_type\":\"AdaDYOSample\",\"timestamp\":1709546313800763904},\"maxValues\":{\"timestamp\":1709546315667423744,\"processor_hash\":\"70ae38a1931c761ddb81f32323f20a53752c9a93e46b45c5e6e538a2dcb0d1d1\",\"annotation\":{\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/fbd636b16ba5b1bce935bff3c9e9a20de21cf19b969bee3b6aa3f0195d8d0880.json\\\"}\",\"data_type\":\"JsonData\",\"digest\":\"fbd636b16ba5b1bce935bff3c9e9a20de21cf19b969bee3b6aa3f0195d8d0880\",\"digest_type\":\"sha256\",\"storage_location_type\":\"FileLocation\"},\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"sample_hash\":\"bf4b188e9c481634aa3c431ffbdcc033e2fca2d152bd56b44bef7ca046b5e8a9\",\"name\":\"jbf_annotation_split_33a07282efa1cc23ddee082e96243fffba51504a5aa0f41a835bc3b2b8162a86_frame_269\",\"partition_id_source\":\"8cdabcb80dfc867ea8f19e7bf9d261fd33be957b822f26aa64f1ebc0c4251483\",\"sample_type\":\"AdaDYOSample\"},\"nullCount\":{\"git_commit_hash\":0,\"processor_hash\":0,\"creation_timestamp\":0,\"annotation\":{\"digest_type\":0,\"data_type\":0,\"storage_location_type\":0,\"digest\":0,\"storage_location\":0},\"name\":0,\"sample_type\":0,\"modification_timestamp\":5,\"partition_id_source\":0,\"sample_hash\":0,\"timestamp\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"add":{"path":"partition_id=0/part-00001-3b7ff560-81e3-4e70-9c2c-6e9e640862ca-c000.snappy.parquet","partitionValues":{"partition_id":"0"},"size":8864,"modificationTime":1752475187954,"dataChange":true,"stats":"{\"numRecords\":5,\"minValues\":{\"creation_timestamp\":\"2025/07/14-06:39:46\",\"sample_hash\":\"0ecf989aa6948746906e66179cacb90a516dac50d9bf2aef396e167fef20feff\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"sample_type\":\"AdaDYOSample\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"timestamp\":1718779258467416576,\"processor_hash\":\"1db1e27c48124740c9e8e9fa5f70cd98ace63800e0f5de096a40efbbb11e5cd8\",\"name\":\"jbf_annotation_split_6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727_frame_241\",\"annotation\":{\"data_type\":\"JsonData\",\"digest\":\"7a2d376b671e20b833cb04898ec8d171b172784376dfad8eb8f6ffed784e0d78\",\"storage_location_type\":\"FileLocation\",\"digest_type\":\"sha256\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/7a2d376b671e20b833cb04898ec8d171b172784376dfad8eb8f6ffed784e0d78.json\\\"}\"}},\"maxValues\":{\"sample_hash\":\"ecd00ffc8fff8c77db5099b5ab4a2837be8727583acae9e92240c00abbac3dee\",\"processor_hash\":\"1db1e27c48124740c9e8e9fa5f70cd98ace63800e0f5de096a40efbbb11e5cd8\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"partition_id_source\":\"f8315617cb43c15494bad8e71e746413f7d0a42211e8a5eabb16a92e8411e379\",\"sample_type\":\"AdaDYOSample\",\"timestamp\":1718779260334096128,\"creation_timestamp\":\"2025/07/14-06:39:46\",\"name\":\"jbf_annotation_split_6e65f8aabdab291f74f5ee5a8cc358b066d93f5f2fed292ca8dd6cb16c1e2727_frame_269\",\"annotation\":{\"data_type\":\"JsonData\",\"digest\":\"ee9176d69f22636e4bbcb0e6fbc6585b77e6b03e45c21c2b4bd8cc1e34354bac\",\"digest_type\":\"sha256\",\"storage_location_type\":\"FileLocation\",\"storage_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/data_lake/ee9176d69f22636e4bbcb0e6fbc6585b77e6b03e45c21c2b4bd8cc1e34354bac.json\\\"}\"}},\"nullCount\":{\"git_commit_hash\":0,\"name\":0,\"processor_hash\":0,\"annotation\":{\"storage_location\":0,\"digest_type\":0,\"data_type\":0,\"digest\":0,\"storage_location_type\":0},\"sample_hash\":0,\"timestamp\":0,\"creation_timestamp\":0,\"partition_id_source\":0,\"sample_type\":0,\"modification_timestamp\":5}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475187955,"operation":"WRITE","operationParameters":{"mode":"Append","partitionBy":"[\"partition_id\"]"},"clientVersion":"delta-rs.py-1.0.2","operationMetrics":{"execution_time_ms":12,"num_added_files":2,"num_added_rows":10,"num_partitions":0,"num_removed_files":0}}}