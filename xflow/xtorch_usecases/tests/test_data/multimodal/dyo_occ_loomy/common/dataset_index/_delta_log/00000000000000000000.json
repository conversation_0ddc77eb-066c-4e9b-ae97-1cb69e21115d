{"protocol":{"minReaderVersion":1,"minWriterVersion":2}}
{"metaData":{"id":"61fe07fe-d615-4387-98f4-c9933ddce5c6","name":null,"description":null,"format":{"provider":"parquet","options":{}},"schemaString":"{\"type\":\"struct\",\"fields\":[{\"name\":\"git_commit_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"creation_timestamp\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"modification_timestamp\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}},{\"name\":\"dataset_name\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"dataset_hash\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"dataset_type\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"dataset_location\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_identifiers\",\"type\":{\"type\":\"map\",\"keyType\":\"long\",\"valueType\":{\"type\":\"struct\",\"fields\":[{\"name\":\"partition_id\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"num_partitions\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}},{\"name\":\"partition_column\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}}]},\"valueContainsNull\":true},\"nullable\":true,\"metadata\":{}},{\"name\":\"stats\",\"type\":\"string\",\"nullable\":false,\"metadata\":{}},{\"name\":\"stats_by_partition\",\"type\":{\"type\":\"map\",\"keyType\":\"long\",\"valueType\":\"string\",\"valueContainsNull\":true},\"nullable\":false,\"metadata\":{}},{\"name\":\"version\",\"type\":\"long\",\"nullable\":false,\"metadata\":{}},{\"name\":\"dataset_location_type\",\"type\":\"string\",\"nullable\":true,\"metadata\":{}}]}","partitionColumns":[],"createdTime":1752475188308,"configuration":{}}}
{"add":{"path":"part-00001-4763921a-f153-4bea-acc3-9566426d257a-c000.snappy.parquet","partitionValues":{},"size":35257,"modificationTime":1752475188313,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"creation_timestamp\":\"2025/07/14-06:39:46\",\"dataset_hash\":\"079fef4f3d5fa1b9c8657b7f155559638e3458347408ba3d665971a4606d53ce\",\"dataset_location_type\":\"FileLocation\",\"version\":1,\"dataset_name\":\"ada_dataset\",\"dataset_type\":\"ParquetDataset\",\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_dataset_v1\\\"}\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"sample_count\\\":{\\\"image_tv_front\\\":10,\\\"occupancy_label\\\":10,\\\"gt_odometry\\\":10,\\\"image_tv_right\\\":10,\\\"image_tv_rear\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"gt_auto_label\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"image_tv_left\\\":10,\\\"radar_combined\\\":10},\\\"sample_count_min\\\":{\\\"image_tv_front\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"gt_auto_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"radar_combined\\\":5},\\\"sample_count_max\\\":{\\\"image_tv_front\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"gt_auto_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"radar_combined\\\":5},\\\"stream_overlap\\\":{\\\"image_tv_front\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"occupancy_label\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"gt_odometry\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_right\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_rear\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"gt_auto_label\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_left\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"radar_combined\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2}},\\\"azure_container_urls\\\":[]}\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\"},\"maxValues\":{\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_dataset_v1\\\"}\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"dataset_name\":\"ada_dataset\",\"dataset_hash\":\"079fef4f3d5fa1b9c8657b7f155559638e3458347408ba3d665971a4606d53ce\",\"creation_timestamp\":\"2025/07/14-06:39:46\",\"version\":1,\"dataset_location_type\":\"FileLocation\",\"dataset_type\":\"ParquetDataset\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"sample_count\\\":{\\\"image_tv_front\\\":10,\\\"occupancy_label\\\":10,\\\"gt_odometry\\\":10,\\\"image_tv_right\\\":10,\\\"image_tv_rear\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"gt_auto_label\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"image_tv_left\\\":10,\\\"radar_combined\\\":10},\\\"sample_count_min\\\":{\\\"image_tv_front\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"gt_auto_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"radar_combined\\\":5},\\\"sample_count_max\\\":{\\\"image_tv_front\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"gt_auto_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"radar_combined\\\":5},\\\"stream_overlap\\\":{\\\"image_tv_front\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"occupancy_label\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"gt_odometry\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_right\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_rear\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"gt_auto_label\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_left\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2},\\\"radar_combined\\\":{\\\"image_tv_front\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"gt_auto_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"radar_combined\\\":2}},\\\"azure_container_urls\\\":[]}\"},\"nullCount\":{\"dataset_location_type\":0,\"dataset_hash\":0,\"dataset_location\":0,\"modification_timestamp\":1,\"git_commit_hash\":0,\"dataset_type\":0,\"value\":0,\"version\":0,\"dataset_name\":0,\"key\":0,\"creation_timestamp\":0,\"stats\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475188314,"operation":"WRITE","operationParameters":{"mode":"Append"},"operationMetrics":{"execution_time_ms":5,"num_added_files":1,"num_added_rows":1,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}