{"add":{"path":"part-00001-2c0afa9a-e07a-4a78-a567-1c793d455bb8-c000.snappy.parquet","partitionValues":{},"size":35230,"modificationTime":1752475193745,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"version\":1,\"dataset_location_type\":\"FileLocation\",\"dataset_type\":\"ParquetDataset\",\"dataset_hash\":\"57c0c4c2acf1e9117b218e386846443149215ba7ac1b73f4ec598af6fc412da0\",\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_val_v1\\\"}\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"sample_count\\\":{\\\"gt_odometry\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"radar_combined\\\":10,\\\"image_tv_rear\\\":10,\\\"image_tv_front\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"occupancy_label\\\":10,\\\"image_tv_right\\\":10,\\\"gt_auto_label\\\":10,\\\"image_tv_left\\\":10},\\\"sample_count_min\\\":{\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_rear\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"occupancy_label\\\":5,\\\"image_tv_right\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5},\\\"sample_count_max\\\":{\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_rear\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"occupancy_label\\\":5,\\\"image_tv_right\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5},\\\"stream_overlap\\\":{\\\"gt_odometry\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"radar_combined\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_rear\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_front\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"occupancy_label\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_right\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"gt_auto_label\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_left\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2}},\\\"azure_container_urls\\\":[]}\",\"dataset_name\":\"ada_val\",\"creation_timestamp\":\"2025/07/14-06:39:52\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\"},\"maxValues\":{\"dataset_hash\":\"57c0c4c2acf1e9117b218e386846443149215ba7ac1b73f4ec598af6fc412da0\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"dataset_name\":\"ada_val\",\"dataset_type\":\"ParquetDataset\",\"version\":1,\"dataset_location_type\":\"FileLocation\",\"creation_timestamp\":\"2025/07/14-06:39:52\",\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_val_v1\\\"}\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"sample_count\\\":{\\\"gt_odometry\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"radar_combined\\\":10,\\\"image_tv_rear\\\":10,\\\"image_tv_front\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"occupancy_label\\\":10,\\\"image_tv_right\\\":10,\\\"gt_auto_label\\\":10,\\\"image_tv_left\\\":10},\\\"sample_count_min\\\":{\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_rear\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"occupancy_label\\\":5,\\\"image_tv_right\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5},\\\"sample_count_max\\\":{\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_rear\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"occupancy_label\\\":5,\\\"image_tv_right\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5},\\\"stream_overlap\\\":{\\\"gt_odometry\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"radar_combined\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_rear\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_front\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"occupancy_label\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_right\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"gt_auto_label\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2},\\\"image_tv_left\\\":{\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_rear\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"occupancy_label\\\":2,\\\"image_tv_right\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2}},\\\"azure_container_urls\\\":[]}\"},\"nullCount\":{\"creation_timestamp\":0,\"dataset_hash\":0,\"dataset_location\":0,\"stats\":0,\"version\":0,\"dataset_location_type\":0,\"value\":0,\"modification_timestamp\":1,\"git_commit_hash\":0,\"dataset_name\":0,\"dataset_type\":0,\"key\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475193745,"operation":"WRITE","operationParameters":{"mode":"Append"},"operationMetrics":{"execution_time_ms":10,"num_added_files":1,"num_added_rows":1,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}