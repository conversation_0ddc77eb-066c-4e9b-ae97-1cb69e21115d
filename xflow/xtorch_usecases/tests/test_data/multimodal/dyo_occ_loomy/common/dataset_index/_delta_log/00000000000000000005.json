{"add":{"path":"part-00001-4a1318ed-2964-4cb9-8643-3b79cf58576f-c000.snappy.parquet","partitionValues":{},"size":35238,"modificationTime":1752475202087,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"version\":1,\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"sample_count\\\":{\\\"radar_combined\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"image_tv_left\\\":10,\\\"occupancy_label\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"image_tv_right\\\":10,\\\"gt_odometry\\\":10,\\\"gt_auto_label\\\":10,\\\"image_tv_front\\\":10,\\\"image_tv_rear\\\":10},\\\"sample_count_min\\\":{\\\"radar_combined\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"occupancy_label\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_front\\\":5,\\\"image_tv_rear\\\":5},\\\"sample_count_max\\\":{\\\"radar_combined\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"occupancy_label\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_front\\\":5,\\\"image_tv_rear\\\":5},\\\"stream_overlap\\\":{\\\"radar_combined\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_left\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"occupancy_label\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_right\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"gt_odometry\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"gt_auto_label\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_front\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_rear\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2}},\\\"azure_container_urls\\\":[]}\",\"dataset_type\":\"ParquetDataset\",\"dataset_location_type\":\"FileLocation\",\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_visu_v1\\\"}\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"creation_timestamp\":\"2025/07/14-06:40:00\",\"dataset_name\":\"ada_visu\",\"dataset_hash\":\"66a687a14acef51af855d9c37d6c1365004ad830385a9ba88cf790d0219b0e93\"},\"maxValues\":{\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"sample_count\\\":{\\\"radar_combined\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"image_tv_left\\\":10,\\\"occupancy_label\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"image_tv_right\\\":10,\\\"gt_odometry\\\":10,\\\"gt_auto_label\\\":10,\\\"image_tv_front\\\":10,\\\"image_tv_rear\\\":10},\\\"sample_count_min\\\":{\\\"radar_combined\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"occupancy_label\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_front\\\":5,\\\"image_tv_rear\\\":5},\\\"sample_count_max\\\":{\\\"radar_combined\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_left\\\":5,\\\"occupancy_label\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_front\\\":5,\\\"image_tv_rear\\\":5},\\\"stream_overlap\\\":{\\\"radar_combined\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_left\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"occupancy_label\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_right\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"gt_odometry\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"gt_auto_label\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_front\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2},\\\"image_tv_rear\\\":{\\\"radar_combined\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_left\\\":2,\\\"occupancy_label\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_front\\\":2,\\\"image_tv_rear\\\":2}},\\\"azure_container_urls\\\":[]}\",\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_visu_v1\\\"}\",\"dataset_name\":\"ada_visu\",\"dataset_location_type\":\"FileLocation\",\"creation_timestamp\":\"2025/07/14-06:40:00\",\"dataset_hash\":\"66a687a14acef51af855d9c37d6c1365004ad830385a9ba88cf790d0219b0e93\",\"dataset_type\":\"ParquetDataset\",\"version\":1},\"nullCount\":{\"stats\":0,\"dataset_location\":0,\"value\":0,\"creation_timestamp\":0,\"version\":0,\"dataset_hash\":0,\"key\":0,\"dataset_name\":0,\"dataset_location_type\":0,\"modification_timestamp\":1,\"dataset_type\":0,\"git_commit_hash\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475202087,"operation":"WRITE","operationParameters":{"mode":"Append"},"operationMetrics":{"execution_time_ms":6,"num_added_files":1,"num_added_rows":1,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}