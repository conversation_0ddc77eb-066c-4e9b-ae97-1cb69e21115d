{"add":{"path":"part-00001-85216a56-44d8-491f-b484-99b9311d46f5-c000.snappy.parquet","partitionValues":{},"size":35319,"modificationTime":1752475199323,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"creation_timestamp\":\"2025/07/14-06:39:57\",\"dataset_name\":\"ada_calibration\",\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_calibration_v1\\\"}\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"sample_count\\\":{\\\"occupancy_label\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"radar_combined\\\":10,\\\"image_tv_left\\\":10,\\\"image_tv_front\\\":10,\\\"gt_odometry\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"image_tv_right\\\":10,\\\"image_tv_rear\\\":10,\\\"gt_auto_label\\\":10},\\\"sample_count_min\\\":{\\\"occupancy_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"gt_auto_label\\\":5},\\\"sample_count_max\\\":{\\\"occupancy_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"gt_auto_label\\\":5},\\\"stream_overlap\\\":{\\\"occupancy_label\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"radar_combined\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_left\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_front\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"gt_odometry\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_right\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_rear\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"gt_auto_label\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2}},\\\"azure_container_urls\\\":[]}\",\"dataset_location_type\":\"FileLocation\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"dataset_type\":\"ParquetDataset\",\"version\":1,\"dataset_hash\":\"b2273f88b00da6b80e9ad2c61310fab5996443e882b581cd070627dde19dbaed\"},\"maxValues\":{\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"sample_count\\\":{\\\"occupancy_label\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"radar_combined\\\":10,\\\"image_tv_left\\\":10,\\\"image_tv_front\\\":10,\\\"gt_odometry\\\":10,\\\"lidar_motion_compensated_center\\\":10,\\\"image_tv_right\\\":10,\\\"image_tv_rear\\\":10,\\\"gt_auto_label\\\":10},\\\"sample_count_min\\\":{\\\"occupancy_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"gt_auto_label\\\":5},\\\"sample_count_max\\\":{\\\"occupancy_label\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"radar_combined\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"gt_odometry\\\":5,\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_right\\\":5,\\\"image_tv_rear\\\":5,\\\"gt_auto_label\\\":5},\\\"stream_overlap\\\":{\\\"occupancy_label\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"radar_combined\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_left\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_front\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"gt_odometry\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"lidar_motion_compensated_center\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_right\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"image_tv_rear\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2},\\\"gt_auto_label\\\":{\\\"occupancy_label\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"radar_combined\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"gt_odometry\\\":2,\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_right\\\":2,\\\"image_tv_rear\\\":2,\\\"gt_auto_label\\\":2}},\\\"azure_container_urls\\\":[]}\",\"dataset_hash\":\"b2273f88b00da6b80e9ad2c61310fab5996443e882b581cd070627dde19dbaed\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_calibration_v1\\\"}\",\"version\":1,\"dataset_location_type\":\"FileLocation\",\"creation_timestamp\":\"2025/07/14-06:39:57\",\"dataset_name\":\"ada_calibration\",\"dataset_type\":\"ParquetDataset\"},\"nullCount\":{\"dataset_location_type\":0,\"dataset_name\":0,\"git_commit_hash\":0,\"creation_timestamp\":0,\"key\":0,\"stats\":0,\"modification_timestamp\":1,\"value\":0,\"dataset_location\":0,\"version\":0,\"dataset_hash\":0,\"dataset_type\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475199323,"operation":"WRITE","operationParameters":{"mode":"Append"},"operationMetrics":{"execution_time_ms":7,"num_added_files":1,"num_added_rows":1,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}