{"add":{"path":"part-00001-c0936c87-d333-4a4b-bb3d-be2e88daf527-c000.snappy.parquet","partitionValues":{},"size":35236,"modificationTime":1752475196489,"dataChange":true,"stats":"{\"numRecords\":1,\"minValues\":{\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_train_v1\\\"}\",\"dataset_location_type\":\"FileLocation\",\"dataset_type\":\"ParquetDataset\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"sample_count\\\":{\\\"lidar_motion_compensated_center\\\":10,\\\"image_tv_rear\\\":10,\\\"occupancy_label\\\":10,\\\"gt_odometry\\\":10,\\\"gt_auto_label\\\":10,\\\"image_tv_left\\\":10,\\\"image_tv_front\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"image_tv_right\\\":10,\\\"radar_combined\\\":10},\\\"sample_count_min\\\":{\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_rear\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_right\\\":5,\\\"radar_combined\\\":5},\\\"sample_count_max\\\":{\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_rear\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_right\\\":5,\\\"radar_combined\\\":5},\\\"stream_overlap\\\":{\\\"lidar_motion_compensated_center\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_rear\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"occupancy_label\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"gt_odometry\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"gt_auto_label\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_left\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_front\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_right\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"radar_combined\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2}},\\\"azure_container_urls\\\":[]}\",\"creation_timestamp\":\"2025/07/14-06:39:54\",\"dataset_name\":\"ada_train\",\"version\":1,\"dataset_hash\":\"45fbf5aeb48eacae2916820dfe1c5568139386dacbbb8a0effee63980f9e77cf\"},\"maxValues\":{\"dataset_hash\":\"45fbf5aeb48eacae2916820dfe1c5568139386dacbbb8a0effee63980f9e77cf\",\"creation_timestamp\":\"2025/07/14-06:39:54\",\"stats\":\"{\\\"num_recordings\\\":2,\\\"stream_count\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"sample_count\\\":{\\\"lidar_motion_compensated_center\\\":10,\\\"image_tv_rear\\\":10,\\\"occupancy_label\\\":10,\\\"gt_odometry\\\":10,\\\"gt_auto_label\\\":10,\\\"image_tv_left\\\":10,\\\"image_tv_front\\\":10,\\\"image_fc1_A_trifocal_wide\\\":10,\\\"image_tv_right\\\":10,\\\"radar_combined\\\":10},\\\"sample_count_min\\\":{\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_rear\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_right\\\":5,\\\"radar_combined\\\":5},\\\"sample_count_max\\\":{\\\"lidar_motion_compensated_center\\\":5,\\\"image_tv_rear\\\":5,\\\"occupancy_label\\\":5,\\\"gt_odometry\\\":5,\\\"gt_auto_label\\\":5,\\\"image_tv_left\\\":5,\\\"image_tv_front\\\":5,\\\"image_fc1_A_trifocal_wide\\\":5,\\\"image_tv_right\\\":5,\\\"radar_combined\\\":5},\\\"stream_overlap\\\":{\\\"lidar_motion_compensated_center\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_rear\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"occupancy_label\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"gt_odometry\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"gt_auto_label\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_left\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_front\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_fc1_A_trifocal_wide\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"image_tv_right\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2},\\\"radar_combined\\\":{\\\"lidar_motion_compensated_center\\\":2,\\\"image_tv_rear\\\":2,\\\"occupancy_label\\\":2,\\\"gt_odometry\\\":2,\\\"gt_auto_label\\\":2,\\\"image_tv_left\\\":2,\\\"image_tv_front\\\":2,\\\"image_fc1_A_trifocal_wide\\\":2,\\\"image_tv_right\\\":2,\\\"radar_combined\\\":2}},\\\"azure_container_urls\\\":[]}\",\"dataset_type\":\"ParquetDataset\",\"dataset_location_type\":\"FileLocation\",\"git_commit_hash\":\"60dc5ac9bac9d0d89a4f7bcc3ef14cb3768cb630\",\"dataset_name\":\"ada_train\",\"version\":1,\"dataset_location\":\"{\\\"file_path\\\":\\\"xtorch_usecases/tests/test_data/multimodal/dyo_occ_loomy/common/dataset/ada_train_v1\\\"}\"},\"nullCount\":{\"dataset_name\":0,\"stats\":0,\"dataset_location\":0,\"creation_timestamp\":0,\"value\":0,\"dataset_hash\":0,\"dataset_type\":0,\"version\":0,\"modification_timestamp\":1,\"key\":0,\"git_commit_hash\":0,\"dataset_location_type\":0}}","tags":null,"baseRowId":null,"defaultRowCommitVersion":null,"clusteringProvider":null}}
{"commitInfo":{"timestamp":1752475196489,"operation":"WRITE","operationParameters":{"mode":"Append"},"operationMetrics":{"execution_time_ms":6,"num_added_files":1,"num_added_rows":1,"num_partitions":0,"num_removed_files":0},"clientVersion":"delta-rs.py-1.0.2"}}