"""Tests for the config_manager.py module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import logging
import sys
from dataclasses import dataclass, field
from unittest.mock import patch

import pytest

from xtorch.training.trainer_config import TrainerConfig
from xtorch_usecases.common.configuration.config_manager import (
    ConfigBuildFunc,
    IOSettings,
    OverrideRegistry,
    Overrides,
    PipelineStep,
    RunConfig,
    RunMode,
    RuntimeEnvironment,
    _load_config_with_overrides,
    load_config,
)


@dataclass
class DummyConfig(RunConfig):
    """Dummy config for testing the decorator."""

    trainer: TrainerConfig = field(default_factory=lambda: TrainerConfig(max_epochs=0))

    field_identifier: str | None = None
    field_a: list[float] | None = None
    field_b: dict[str, int] | None = None
    field_c: int | None = None


class ShortRunDummyOverride(Overrides[DummyConfig]):
    """Dummy override class for testing."""

    def override(self, config: DummyConfig) -> DummyConfig:
        """Override the dummy config."""

        config.field_identifier = "short"
        config.field_a = [2.0]
        config.field_b = {"a": 1, "b": 2, "c": 3}

        return config


class LocalRunDummyOverride(Overrides[DummyConfig]):
    """Dummy override class for testing."""

    def override(self, config: DummyConfig) -> DummyConfig:
        """Override the dummy config."""

        config.field_identifier = "local"
        config.field_c = 9

        return config


@pytest.fixture(name="config_without_overrides")
def fixture_config_without_overrides() -> DummyConfig:
    """Fixture for returning a config instance `DummyConfig` without overrides."""

    return DummyConfig(
        field_a=[1.0],
        field_b={"a": 1, "b": 2},
        field_c=5,
    )


@pytest.fixture(name="config_builder_fn")
def fixture_config_builder_fn(config_without_overrides: DummyConfig) -> ConfigBuildFunc[DummyConfig]:
    """Fixture for returning a config builder fn for the `DummyConfig`."""

    def get_test_config(pipeline_step: PipelineStep, io_settings: IOSettings) -> DummyConfig:
        """Config build function for test purposes fullfilling the required interface."""

        return config_without_overrides

    return get_test_config


@pytest.fixture(name="pipeline_step")
def fixture_pipeline_step() -> PipelineStep:
    """Fixture for returning a dummy pipeline step (Train)."""
    return PipelineStep.TRAIN


@pytest.fixture(name="config_name")
def fixture_config_name() -> str:
    """Fixture for the config name."""
    return "dummy_config"


@pytest.fixture(name="short_run_override")
def fixture_short_run_override() -> OverrideRegistry[DummyConfig]:
    """Fixture for returning an override object for short runs."""
    return {RunMode.SHORT: ShortRunDummyOverride()}


@pytest.fixture(name="local_run_override")
def fixture_local_run_override() -> OverrideRegistry[DummyConfig]:
    """Fixture for returning an override object for local runs."""
    return {RuntimeEnvironment.LOCAL: LocalRunDummyOverride()}


@patch.object(sys, "argv", [__file__, "--config_name", "unknown_config_name"])
def test_error_is_raised_for_unknown_config_name(
    config_builder_fn: ConfigBuildFunc[DummyConfig],
    pipeline_step: PipelineStep,
) -> None:
    """Test that an error is raised if the config name is not found in the available configs."""
    # GIVEN the mapping for available configs consisting of a config builder fn and empty overrides
    available_configs = {"dummy_config": (config_builder_fn, {})}

    # WHEN trying to load a config with an unknown config name
    # THEN a KeyError is raised indicating that the config name is not found
    with pytest.raises(KeyError, match="Config name 'unknown_config_name' not found in"):
        _load_config_with_overrides(available_configs, pipeline_step, "unknown_config")


@patch.object(sys, "argv", [__file__, "--config_name", "dummy_config"])
def test_config_loading_without_overrides(
    config_builder_fn: ConfigBuildFunc[DummyConfig],
    pipeline_step: PipelineStep,
    config_name: str,
    caplog: pytest.LogCaptureFixture,
    config_without_overrides: DummyConfig,
) -> None:
    """Test the config override."""
    # GIVEN the mapping for available configs consisting of a config builder fn without any overrides
    override_registry = {}
    available_configs = {config_name: (config_builder_fn, override_registry)}

    # WHEN loading the config with no overrides
    with caplog.at_level(logging.INFO):
        config_with_overrides = _load_config_with_overrides(available_configs, pipeline_step)

    # THEN the config must not be changed
    assert config_without_overrides == config_with_overrides

    # THEN caplog.text must not contain any entries related to overrides
    assert "Applying overrides for runtime environment" not in caplog.text
    assert "Applying overrides for run mode" not in caplog.text
    assert "Applying CLI overrides:" not in caplog.text

    # WHEN retrieving the config object via calling the decorator `load_config`
    # THEN the config object must match `config_with_overrides`
    assert_config_loaded_by_decorator_equals_expected(
        config_builder_fn=config_builder_fn,
        override_registry=override_registry,
        pipeline_step=pipeline_step,
        expected_config=config_with_overrides,
        config_name=config_name,
    )


@patch.object(sys, "argv", [__file__, "--config_name", "dummy_config", "--run_mode", "short"])
def test_config_loading_with_short_run_overrides(
    config_builder_fn: ConfigBuildFunc[DummyConfig],
    pipeline_step: PipelineStep,
    config_name: str,
    caplog: pytest.LogCaptureFixture,
    short_run_override: OverrideRegistry[DummyConfig],
) -> None:
    """Test the config override."""
    # GIVEN the mapping for available configs consisting of a config builder fn and short run overrides
    override_registry = short_run_override
    available_configs = {config_name: (config_builder_fn, override_registry)}

    # WHEN loading the config with short run overrides
    with caplog.at_level(logging.INFO):
        config_with_overrides = _load_config_with_overrides(available_configs, pipeline_step)

    # THEN the config must be overridden as defined by the short run override object
    assert config_with_overrides.field_identifier == "short"
    assert config_with_overrides.field_a == [2.0]
    assert config_with_overrides.field_b == {"a": 1, "b": 2, "c": 3}

    # THEN caplog.text must not contain any entries related to CLI or RuntimeEnvironment related overrides
    assert "Applying overrides for runtime environment" not in caplog.text
    assert "Applying CLI overrides:" not in caplog.text

    # THEN caplog.text must contain entries related to the run mode override
    assert "Applying overrides for run mode" in caplog.text

    # WHEN retrieving the config object via calling the decorator `load_config`
    # THEN the config object must match `config_with_overrides`
    assert_config_loaded_by_decorator_equals_expected(
        config_builder_fn=config_builder_fn,
        override_registry=override_registry,
        pipeline_step=pipeline_step,
        expected_config=config_with_overrides,
        config_name=config_name,
    )


@patch.object(sys, "argv", [__file__, "--config_name", "dummy_config"])
def test_config_loading_with_local_run_overrides(
    config_builder_fn: ConfigBuildFunc[DummyConfig],
    pipeline_step: PipelineStep,
    config_name: str,
    caplog: pytest.LogCaptureFixture,
    local_run_override: OverrideRegistry[DummyConfig],
) -> None:
    """Test the config override."""
    # GIVEN the mapping for available configs consisting of a config builder fn and short run overrides
    override_registry = local_run_override
    available_configs = {config_name: (config_builder_fn, override_registry)}

    with caplog.at_level(logging.INFO):
        config_with_overrides = _load_config_with_overrides(available_configs, pipeline_step)

    # THEN the config must be overriden as defined by the local run override object
    assert config_with_overrides.field_identifier == "local"
    assert config_with_overrides.field_c == 9

    # THEN caplog.text must not contain any entries related to CLI or RunMode related overrides
    assert "Applying overrides for run mode" not in caplog.text
    assert "Applying CLI overrides:" not in caplog.text

    # THEN caplog.text must contain entries related to the run mode override
    assert "Applying overrides for runtime environment" in caplog.text

    # WHEN retrieving the config object via calling the decorator `load_config`
    # THEN the config object must match `config_with_overrides`
    assert_config_loaded_by_decorator_equals_expected(
        config_builder_fn=config_builder_fn,
        override_registry=override_registry,
        pipeline_step=pipeline_step,
        expected_config=config_with_overrides,
        config_name=config_name,
    )


@patch.object(sys, "argv", [__file__, "--config_name", "dummy_config", "--run_mode", "short"])
def test_config_loading_with_local_and_short_run_overrides(
    config_builder_fn: ConfigBuildFunc[DummyConfig],
    pipeline_step: PipelineStep,
    config_name: str,
    caplog: pytest.LogCaptureFixture,
    short_run_override: OverrideRegistry[DummyConfig],
    local_run_override: OverrideRegistry[DummyConfig],
) -> None:
    """Test the config override."""
    # GIVEN the mapping for available configs consisting of a config builder fn and short+local run overrides
    override_registry = {**local_run_override, **short_run_override}
    available_configs = {config_name: (config_builder_fn, override_registry)}

    with caplog.at_level(logging.INFO):
        config_with_overrides = _load_config_with_overrides(available_configs, pipeline_step)

    # THEN the config must be overriden as defined by the local run override object
    assert config_with_overrides.field_c == 9

    # THEN the config must be overriden as defined by the short run override object
    assert config_with_overrides.field_a == [2.0]
    assert config_with_overrides.field_b == {"a": 1, "b": 2, "c": 3}

    # THEN the field_identifier must be set to `short` since short run overrides are applied after local run overrides
    assert config_with_overrides.field_identifier == "short"

    # THEN caplog.text must not contain any entries related to CLI or RunMode related overrides
    assert "Applying CLI overrides:" not in caplog.text

    # THEN caplog.text must contain entries related to the run mode and runtime environment overrides
    assert "Applying overrides for run mode" in caplog.text
    assert "Applying overrides for runtime environment" in caplog.text

    # WHEN retrieving the config object via calling the decorator `load_config`
    # THEN the config object must match `config_with_overrides`
    assert_config_loaded_by_decorator_equals_expected(
        config_builder_fn=config_builder_fn,
        override_registry=override_registry,
        pipeline_step=pipeline_step,
        expected_config=config_with_overrides,
        config_name=config_name,
    )


@patch.object(
    sys,
    "argv",
    [
        __file__,
        "--config_name",
        "dummy_config",
        "--run_mode",
        "short",
        "--overrides",
        "trainer.max_epochs=123",
        "field_identifier=cli_override",
    ],
)
def test_config_loading_with_local_short_and_cli_run_overrides(
    config_builder_fn: ConfigBuildFunc[DummyConfig],
    pipeline_step: PipelineStep,
    config_name: str,
    caplog: pytest.LogCaptureFixture,
    short_run_override: OverrideRegistry[DummyConfig],
    local_run_override: OverrideRegistry[DummyConfig],
) -> None:
    """Test the config override."""
    # GIVEN the mapping for available configs consisting of a config builder fn and short+local run overrides
    override_registry = {**local_run_override, **short_run_override}
    available_configs = {config_name: (config_builder_fn, override_registry)}

    with caplog.at_level(logging.INFO):
        config_with_overrides = _load_config_with_overrides(available_configs, pipeline_step)

    # THEN the config must be overriden as defined by the local run override object
    assert config_with_overrides.field_c == 9

    # THEN the config must be overriden as defined by the short run override object
    assert config_with_overrides.field_a == [2.0]
    assert config_with_overrides.field_b == {"a": 1, "b": 2, "c": 3}

    # THEN the config must be overriden as defined by the CLI overrides
    assert config_with_overrides.trainer.max_epochs == 123

    # THEN the field_identifier must be set to `cli_overrides` since cli overrides are always applied last
    assert config_with_overrides.field_identifier == "cli_override"

    # THEN caplog.text must contain entries related to the run mode / runtime environment / CLI overrides
    assert "Applying overrides for run mode" in caplog.text
    assert "Applying overrides for runtime environment" in caplog.text
    assert "Applying CLI overrides:" in caplog.text

    # WHEN retrieving the config object via calling the decorator `load_config`
    # THEN the config object must match `config_with_overrides`
    assert_config_loaded_by_decorator_equals_expected(
        config_builder_fn=config_builder_fn,
        override_registry=override_registry,
        pipeline_step=pipeline_step,
        expected_config=config_with_overrides,
        config_name=config_name,
    )


def assert_config_loaded_by_decorator_equals_expected(
    config_builder_fn: ConfigBuildFunc[DummyConfig],
    override_registry: OverrideRegistry[DummyConfig],
    pipeline_step: PipelineStep,
    expected_config: DummyConfig,
    config_name: str,
) -> None:
    """Helper to assert that the config loaded via the decorator matches the expected config."""

    @load_config({config_name: (config_builder_fn, override_registry)}, pipeline_step)
    def get_config(config_decorator: DummyConfig) -> None:
        assert config_decorator == expected_config

    get_config()
