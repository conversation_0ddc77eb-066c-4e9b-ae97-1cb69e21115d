"""Augmentation for the bev data."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import copy
import math
from enum import Enum
from typing import Any

import pytest
import torch

from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.data.definitions.bev import (
    BevBox3dGTTuple,
    BevDataDict,
    LidarPointCloudData,
    MultiViewFrameData,
    RadarPointCloudData,
)
from xtorch.geometry.volumes.common import BevLayout
from xtorch_usecases.common.datasets.common.augmenter import (
    DataAugmentationTransformationParameters,
    GlobalAffineTransformation,
    GlobalAffineTransformationConfig,
    GlobalAffineTransformationParameters,
)

BOX_3D_RELEASE_TASK_ID = "multiviewbox3d_embd"


class TransformationTests(Enum):
    """Transformation tests types to properly sample transformation parameters."""

    Identity = "Identity"
    Yaw_only = "Yaw_only"
    Everything = "Everything"


class TestAffineTransformation(GlobalAffineTransformation):
    """Test global transformation class."""

    def __init__(self, config: GlobalAffineTransformationConfig) -> None:
        """Initialize the test global transformation class."""
        super().__init__(config)
        self._sample_setup = None

    def sample_setup(self, value: TransformationTests | None) -> None:
        """Sample setup method."""
        self._sample_setup = value

    def sample_parameters(self) -> DataAugmentationTransformationParameters:
        """Fix the transformation parameters for testing."""
        if self._sample_setup == TransformationTests.Identity:
            return GlobalAffineTransformationParameters(
                roll=0.0,
                pitch=0.0,
                yaw=0.0,
                trans_x=0.0,
                trans_y=0.0,
                trans_z=0.0,
                scale=1.0,
            )
        if self._sample_setup == TransformationTests.Yaw_only:
            return GlobalAffineTransformationParameters(
                roll=0.0, pitch=0.0, yaw=math.radians(90.0), trans_x=0.0, trans_y=0.0, trans_z=0.0, scale=1.0
            )
        if self._sample_setup == TransformationTests.Everything:
            return GlobalAffineTransformationParameters(
                roll=math.radians(90.0),
                pitch=math.radians(90.0),
                yaw=math.radians(90.0),
                trans_x=0.1,
                trans_y=0.2,
                trans_z=0.3,
                scale=1.0,  # not testing scale, because currently it is not used in the augmenter.
            )
        msg = f"Invalid sample setup: {self._sample_setup}"
        raise ValueError(msg)


class TestGlobalTransformationConfig(GlobalAffineTransformationConfig):
    """Test global transformation configuration class."""

    def make_transformation(self) -> GlobalAffineTransformation:
        """Make a global transformation object."""
        return TestAffineTransformation(self)


@pytest.fixture(name="device", params=["cuda", "cpu"] if torch.cuda.is_available() else ["cpu"])
def device(request: pytest.FixtureRequest) -> str:
    """Get the device for the test."""
    return request.param


def test_global_transformation_config(
    device: str,
    get_dummy_input: tuple[
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
    ],
    get_dummy_yaw_only_result: tuple[
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
    ],
    get_dummy_everything_result: tuple[
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
        torch.Tensor,
    ],
) -> None:
    """Test the global transformation configuration.

    This method tests the functionality of the global transformation configuration by applying transformations
    to sample data and comparing the results with expected augmented data.

    Args:
        device: The device to run the test on.
        get_dummy_input: The sample input data for the test.
        get_dummy_yaw_only_result: The expected augmented data for the yaw only transformation.
        get_dummy_everything_result: The expected augmented data for the everything transformation.

    Returns:
        None

    Raises:
        AssertionError: If any of the assertions fail, indicating that the transformations were not applied correctly.
    """
    torch.set_default_device(device)
    transform = TestGlobalTransformationConfig()
    transform.bev_layout = BevLayout(x_min=-20.0, x_max=20.0, x_res=5.0, y_min=-20.0, y_max=20.0, y_res=5.0)

    transformation_setup = [TransformationTests.Identity, TransformationTests.Yaw_only, TransformationTests.Everything]

    (
        sample_extrinsics,
        sample_radar_point_cloud,
        sample_lidar_point_cloud,
        sample_box_dimension,
        sample_box_rotations,
        sample_box_center,
        sample_box_mask,
        sample_bev_grid_mask,
    ) = get_dummy_input

    identity_result = (
        copy.deepcopy(sample_extrinsics),
        copy.deepcopy(sample_radar_point_cloud),
        copy.deepcopy(sample_lidar_point_cloud),
        copy.deepcopy(sample_box_dimension),
        copy.deepcopy(sample_box_rotations),
        copy.deepcopy(sample_box_center),
        copy.deepcopy(sample_bev_grid_mask),
        # sample_box_mask is checked against source sample
    )

    expected_outputs = {
        TransformationTests.Identity: identity_result,
        TransformationTests.Yaw_only: get_dummy_yaw_only_result,
        TransformationTests.Everything: get_dummy_everything_result,
    }

    sample_camera = {
        ValueKey.DATA: MultiViewFrameData(
            extrinsics=sample_extrinsics,
            intrinsic_params=torch.tensor([[1, 2, 3], [4, 5, 6], [7, 8, 9]], device=device),
            images=torch.tensor([[[1, 2, 3], [4, 5, 6], [7, 8, 9]]], device=device),
            camera_types=torch.tensor([1], device=device),
        ),
        ValueKey.IDENTIFIER: "test",
        ValueKey.VALID: True,
    }

    sample_data: dict[str, BevDataDict] = {
        BOX_3D_RELEASE_TASK_ID: {
            ValueKey.DATA: BevBox3dGTTuple(
                box_class_ids=torch.tensor([1, 2, 3], device=device),
                box_center=sample_box_center,
                box_dimension=sample_box_dimension,
                box_rotations=sample_box_rotations,
                box_mask=sample_box_mask,
                bev_grid_mask=sample_bev_grid_mask,
                sequence_name=["test"],
                per_sequence_track_id=torch.tensor([1, 2, 3], device=device),
                global_track_id=torch.tensor([1, 2, 3], device=device),
                timestamp_ns=torch.tensor([1, 2, 3], device=device),
                box_motion_mask=torch.zeros((3,), device=device, dtype=torch.bool),
                box_velocity_xy=torch.zeros((3, 2), device=device, dtype=torch.float32),
                box_acceleration_xy=torch.zeros((3, 2), device=device, dtype=torch.float32),
                box_yaw_rate=torch.zeros((3, 1), device=device, dtype=torch.float32),
            ),
            ValueKey.IDENTIFIER: "test",
            ValueKey.VALID: True,
        },
        InputDataId.CAMERA_PINHOLE: sample_camera,
        InputDataId.CAMERA_DEF_CYLINDER: sample_camera,
        InputDataId.CAMERA_CYLINDER: sample_camera,
        InputDataId.RADAR: {
            ValueKey.DATA: RadarPointCloudData(
                point_cloud=sample_radar_point_cloud,
                point_features=torch.tensor([[1, 2, 3], [4, 5, 6], [7, 8, 9]], device=device),
            ),
            ValueKey.IDENTIFIER: "test",
            ValueKey.VALID: True,
        },
        InputDataId.REF_LIDAR: {
            ValueKey.DATA: LidarPointCloudData(
                point_cloud=sample_lidar_point_cloud,
                intensity=torch.tensor([1, 2, 3], device=device),
            ),
            ValueKey.IDENTIFIER: "test",
            ValueKey.VALID: True,
        },
    }
    for i in transformation_setup:
        test_transform = transform.make_transformation()
        assert isinstance(test_transform, TestAffineTransformation)
        test_transform.sample_setup(i)
        result_sample = test_transform(copy.deepcopy(sample_data))
        assert isinstance(result_sample, dict)
        check_transformation(sample_data, result_sample, expected_outputs[i])


def check_transformation(
    sample_data: dict[str, Any],
    result_sample: dict[str, Any],
    expected_data: tuple[
        torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor
    ],
) -> None:
    """Check the transformation for the source and transformed data.

    Compare the transformed data with the expected data to check if the transformation was applied correctly.
    This function does only several asserts.

    Args:
        sample_data: The source data before transformation.
        result_sample: The transformed data.
        expected_data: The expected data after transformation.
    """
    (
        example_augmented_camera_extrinsics,
        example_augmented_radar_point_cloud,
        example_augmented_lidar_point_cloud,
        example_augmented_box_dimension,
        example_augmented_box_rotations,
        example_augmented_box_center,
        example_augmented_bev_grid_mask,
    ) = expected_data

    tolerance = 1e-6

    bbox_data = result_sample[BOX_3D_RELEASE_TASK_ID][ValueKey.DATA]
    bbox_sample_data = sample_data[BOX_3D_RELEASE_TASK_ID][ValueKey.DATA]
    assert isinstance(bbox_data, BevBox3dGTTuple)
    assert isinstance(bbox_sample_data, BevBox3dGTTuple)
    torch.testing.assert_close(
        bbox_data.box_center,
        example_augmented_box_center,
        atol=tolerance,
        rtol=tolerance,
        msg="Box center is not the same, not correctly transformed",
    )
    torch.testing.assert_close(
        bbox_data.box_rotations,
        example_augmented_box_rotations,
        atol=tolerance,
        rtol=tolerance,
        msg="Box rotations are not the same, not correctly transformed",
    )
    torch.testing.assert_close(
        bbox_data.box_dimension,
        example_augmented_box_dimension,
        atol=tolerance,
        rtol=tolerance,
        msg="Box dimensions are not the same, not correctly transformed",
    )
    torch.testing.assert_close(
        bbox_data.bev_grid_mask,
        example_augmented_bev_grid_mask,
        atol=tolerance,
        rtol=tolerance,
        msg="BEV grid mask is not the same, not correctly transformed",
    )

    # check attributes that are not suppose to be transformed
    torch.testing.assert_close(
        bbox_data.box_mask,
        bbox_sample_data.box_mask,
        atol=tolerance,
        rtol=tolerance,
        msg="Box mask is not the same, should not be transformed",
    )

    torch.testing.assert_close(
        bbox_data.box_class_ids,
        bbox_sample_data.box_class_ids,
        atol=0,
        rtol=0,
        msg="Box class ids is not the same, should not be transformed",
    )
    assert bbox_data.sequence_name[0] == bbox_sample_data.sequence_name[0], (
        "Sequence name is not the same, should not be transformed"
    )
    torch.testing.assert_close(
        bbox_data.per_sequence_track_id,
        bbox_sample_data.per_sequence_track_id,
        atol=0,
        rtol=0,
        msg="Per sequence track id is not the same, should not be transformed",
    )

    # check RadarPointCloudData.
    radar_result_sample = result_sample[InputDataId.RADAR][ValueKey.DATA]
    radar_sample_data = sample_data[InputDataId.RADAR][ValueKey.DATA]
    assert isinstance(radar_result_sample, RadarPointCloudData)
    assert isinstance(radar_sample_data, RadarPointCloudData)
    torch.testing.assert_close(
        radar_result_sample.point_cloud,
        example_augmented_radar_point_cloud,
        atol=tolerance,
        rtol=tolerance,
        msg="Radar point cloud is not the same, not correctly transformed",
    )
    torch.testing.assert_close(
        radar_result_sample.point_features,
        radar_sample_data.point_features,
        atol=0,
        rtol=0,
        msg="Radar point features is not the same, should not be transformed",
    )
    # check LidarPointCloudData
    lidar_result_sample = result_sample[InputDataId.REF_LIDAR][ValueKey.DATA]
    lidar_sample_data = sample_data[InputDataId.REF_LIDAR][ValueKey.DATA]
    assert isinstance(lidar_result_sample, LidarPointCloudData)
    assert isinstance(lidar_sample_data, LidarPointCloudData)
    torch.testing.assert_close(
        lidar_result_sample.point_cloud,
        example_augmented_lidar_point_cloud,
        atol=tolerance,
        rtol=tolerance,
        msg="Lidar point cloud is not the same, not correctly transformed",
    )
    torch.testing.assert_close(
        lidar_sample_data.intensity,
        lidar_result_sample.intensity,
        atol=0,
        rtol=0,
        msg="Lidar point features is not the same, should not be transformed",
    )

    # check MultiViewFrameData
    for camera in [InputDataId.CAMERA_PINHOLE, InputDataId.CAMERA_DEF_CYLINDER, InputDataId.CAMERA_CYLINDER]:
        camera_data = result_sample[camera][ValueKey.DATA]
        camera_sample_data = sample_data[camera][ValueKey.DATA]
        assert isinstance(camera_sample_data, MultiViewFrameData)
        assert isinstance(camera_data, MultiViewFrameData)
        torch.testing.assert_close(
            camera_data.extrinsics,
            example_augmented_camera_extrinsics,
            atol=tolerance,
            rtol=tolerance,
            msg="Camera extrinsics is not the same, not correctly transformed",
        )
        torch.testing.assert_close(
            camera_data.intrinsic_params,
            camera_sample_data.intrinsic_params,
            atol=0,
            rtol=0,
            msg="Camera intrinsic params is not the same, should not be transformed",
        )
        torch.testing.assert_close(
            camera_data.images,
            camera_sample_data.images,
            atol=0,
            rtol=0,
            msg="Camera images is not the same, should not be transformed",
        )
        torch.testing.assert_close(
            camera_data.camera_types,
            camera_sample_data.camera_types,
            atol=0,
            rtol=0,
            msg="Camera types is not the same, should not be transformed",
        )


@pytest.fixture(name="get_dummy_input")
def get_dummy_input(
    device: str,
) -> tuple[
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
]:
    """Get the sample input data for the test."""
    dummy_extrinsics = torch.tensor(
        [
            [
                [
                    [0.1, 0.0, 0.9, -1.0],
                    [-0.9, 0.0, 0.1, 0.5],
                    [0.0, -1.0, 0.0, 1.0],
                    [0.0, 0.0, 0.0, 1.0],
                ]
            ]
        ],
        device=device,
    )

    dummy_radar_point_cloud = torch.tensor(
        [
            [1.0, -1.0, 0.0],
            [2.0, -2.0, 0.0],
            [3.0, -3.0, 0.0],
            [4.0, -4.0, 1.0],
            [5.0, -5.0, 1.0],
        ],
        device=device,
    )

    dummy_lidar_point_cloud = torch.tensor(
        [
            [1.0, -1.0, 0.0],
            [2.0, -2.0, 0.0],
            [3.0, -3.0, 0.0],
            [4.0, -4.0, 1.0],
            [5.0, -5.0, 1.0],
        ],
        device=device,
    )

    dummy_box_dimension = torch.tensor(
        [
            [4.0, 2.0, 1.5],
            [1.0, 1.0, 2.0],
            [5.0, 2.0, 3.0],
            [6.0, 3.0, 3.5],
            [0.0, 0.0, 0.0],
        ],
        device=device,
    )

    dummy_box_rotations = torch.tensor(
        [
            [[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]],
            [[0.0, -1.0, 0.0], [1.0, 0.0, 0.0], [0.0, 0.0, 1.0]],
            [[-1.0, 0.0, 0.0], [0.0, -1.0, 0.0], [0.0, 0.0, 1.0]],
            [[0.0, 1.0, 0.0], [-1.0, 0.0, 0.0], [0.0, 0.0, 1.0]],
            [[1.0, 0.0, 0.0], [0.0, 0.0, -1.0], [0.0, 1.0, 0.0]],
        ],
        device=device,
    )
    dummy_box_center = torch.tensor(
        [
            [10.0, -10.0, 1.0],
            [20.0, 0.0, 1.5],
            [30.0, 5.0, 0.5],
            [-10.0, -5.0, 0.5],
            [-20.0, -2.0, 1.0],
        ],
        device=device,
    )

    dummy_box_mask = torch.tensor([True, True, True, False, False], device=device)

    dummy_bev_grid_mask = torch.tensor(
        [
            [True, True, True, True, True, True, True, True],
            [False, False, False, False, False, False, False, False],
            [True, True, True, True, True, True, True, True],
            [True, True, True, True, True, True, True, True],
            [True, True, True, True, True, True, True, True],
            [True, True, True, True, True, True, True, True],
            [True, True, True, True, True, True, True, True],
            [True, True, True, True, True, True, True, True],
        ],
        device=device,
        dtype=torch.bool,
    )

    return (
        dummy_extrinsics,
        dummy_radar_point_cloud,
        dummy_lidar_point_cloud,
        dummy_box_dimension,
        dummy_box_rotations,
        dummy_box_center,
        dummy_box_mask,
        dummy_bev_grid_mask,
    )


@pytest.fixture(name="get_dummy_yaw_only_result")
def get_dummy_yaw_only_result(
    device: str,
) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
    """Get the expected augmented data for the yaw only transformation."""
    dummy_yaw_only_result_extrinsics = torch.tensor(
        [
            [
                [
                    [-0.9, 0.0, 0.10, 0.5],
                    [-0.1, 0.0, -0.9, 1.0],
                    [0.0, -1.0, 0.0, 1.0],
                    [0.0, 0.0, 0.0, 1.0],
                ]
            ]
        ],
        device=device,
    )
    dummy_yaw_only_result_radar_point_cloud = torch.tensor(
        [[1.0, 1.0, 0.0], [2.0, 2.0, 0.0], [3.0, 3.0, 0.0], [4.0, 4.0, 1.0], [5.0, 5.0, 1.0]], device=device
    )
    dummy_yaw_only_result_lidar_point_cloud = torch.tensor(
        [[1.0, 1.0, 0.0], [2.0, 2.0, 0.0], [3.0, 3.0, 0.0], [4.0, 4.0, 1.0], [5.0, 5.0, 1.0]], device=device
    )
    dummy_yaw_only_result_box_dimension = torch.tensor(
        [
            [4.0, 2.0, 1.5],
            [1.0, 1.0, 2.0],
            [5.0, 2.0, 3.0],
            [6.0, 3.0, 3.5],
            [0.0, 0.0, 0.0],
        ],
        device=device,
    )
    dummy_yaw_only_result_box_rotations = torch.tensor(
        [
            [[-0.0, -1.0, 0.0], [1.0, -0.0, 0.0], [0.0, 0.0, 1.0]],
            [[-1.0, 0.0, 0.0], [-0.0, -1.0, 0.0], [0.0, 0.0, 1.0]],
            [[0.0, 1.0, 0.0], [-1.0, 0.0, 0.0], [0.0, 0.0, 1.0]],
            [[1.0, 0.0, 0.0], [0.0, 1.0, 0.0], [0.0, 0.0, 1.0]],
            [[1.0, 0.0, 0.0], [0.0, 0.0, -1.0], [0.0, 1.0, 0.0]],
        ],
        device=device,
    )
    dummy_yaw_only_result_box_center = torch.tensor(
        [
            [10.0, 10.0, 1.0],
            [-0.0, 20.0, 1.5],
            [-5.0, 30.0, 0.5],
            [5.0, -10.0, 0.5],
            [-20.0, -2.0, 1.0],
        ],
        device=device,
    )
    dummy_yaw_only_result_bev_grid_map = torch.tensor(
        [
            [True, False, True, True, True, True, True, True],
            [True, False, True, True, True, True, True, True],
            [True, False, True, True, True, True, True, True],
            [True, False, True, True, True, True, True, True],
            [True, False, True, True, True, True, True, True],
            [True, False, True, True, True, True, True, True],
            [True, False, True, True, True, True, True, True],
            [True, False, True, True, True, True, True, True],
        ],
        device=device,
        dtype=torch.bool,
    )

    return (
        dummy_yaw_only_result_extrinsics,
        dummy_yaw_only_result_radar_point_cloud,
        dummy_yaw_only_result_lidar_point_cloud,
        dummy_yaw_only_result_box_dimension,
        dummy_yaw_only_result_box_rotations,
        dummy_yaw_only_result_box_center,
        dummy_yaw_only_result_bev_grid_map,
    )


@pytest.fixture(name="get_dummy_everything_result")
def get_dummy_everything_result(
    device: str,
) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor, torch.Tensor]:
    """Get the expected augmented data for the everything transformation."""
    dummy_yaw_only_result_extrinsics = torch.tensor(
        [
            [
                [
                    [0.0000, 1.0000, -0.0000, -0.7000],
                    [-0.9000, 0.0000, 0.1000, 1.1000],
                    [0.1000, 0.0000, 0.9000, -1.1000],
                    [0.0000, 0.0000, 0.0000, 1.0000],
                ]
            ]
        ],
        device=device,
    )
    dummy_yaw_only_result_radar_point_cloud = torch.tensor(
        [
            [0.3000, -0.8000, -1.1000],
            [0.3000, -1.8000, -2.1000],
            [0.3000, -2.8000, -3.1000],
            [1.3000, -3.8000, -4.1000],
            [1.3000, -4.8000, -5.1000],
        ],
        device=device,
    )
    dummy_yaw_only_result_lidar_point_cloud = torch.tensor(
        [
            [0.3000, -0.8000, -1.1000],
            [0.3000, -1.8000, -2.1000],
            [0.3000, -2.8000, -3.1000],
            [1.3000, -3.8000, -4.1000],
            [1.3000, -4.8000, -5.1000],
        ],
        device=device,
    )
    dummy_yaw_only_result_box_dimension = torch.tensor(
        [
            [4.0000, 2.0000, 1.5000],
            [1.0000, 1.0000, 2.0000],
            [5.0000, 2.0000, 3.0000],
            [6.0000, 3.0000, 3.5000],
            [0.0000, 0.0000, 0.0000],
        ],
        device=device,
    )
    dummy_yaw_only_result_box_rotations = torch.tensor(
        [
            [[0.0, 0.0, 1.0], [-0.0, 1.0, 0.0], [-1.0, -0.0, 0.0]],
            [[0.0, -1.0, 0.0], [0.0, 0.0, 1.0], [-1.0, -0.0, 0.0]],
            [[-0.0, 0.0, -1.0], [0.0, -1.0, 0.0], [-1.0, -0.0, 0.0]],
            [[0.0, 1.0, 0.0], [0.0, 0.0, -1.0], [-1.0, 0.0, 0.0]],
            [[1.0, 0.0, 0.0], [0.0, 0.0, -1.0], [0.0, 1.0, 0.0]],
        ],
        device=device,
    )
    dummy_yaw_only_result_box_center = torch.tensor(
        [
            [1.3000, -9.8000, -10.1000],
            [1.8000, 0.2000, -20.1000],
            [0.8000, 5.2000, -30.1000],
            [0.8000, -4.8000, 9.9000],
            [-20.0000, -2.0000, 1.0000],
        ],
        device=device,
    )
    dummy_yaw_only_result_bev_grid_map = torch.tensor(
        [
            [False, False, False, True, True, True, True, True],
            [False, False, False, True, True, True, True, True],
            [False, False, False, True, True, True, True, True],
            [False, False, False, True, True, True, True, True],
            [False, False, False, True, True, True, True, True],
            [False, False, False, True, True, True, True, True],
            [False, False, False, True, True, True, True, True],
            [False, False, False, False, False, False, False, False],
        ],
        device=device,
        dtype=torch.bool,
    )

    return (
        dummy_yaw_only_result_extrinsics,
        dummy_yaw_only_result_radar_point_cloud,
        dummy_yaw_only_result_lidar_point_cloud,
        dummy_yaw_only_result_box_dimension,
        dummy_yaw_only_result_box_rotations,
        dummy_yaw_only_result_box_center,
        dummy_yaw_only_result_bev_grid_map,
    )
