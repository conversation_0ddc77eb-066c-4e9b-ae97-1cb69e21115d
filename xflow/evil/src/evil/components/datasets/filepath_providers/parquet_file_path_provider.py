"""Module for providing file paths for parquet files."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import itertools
import typing
from collections.abc import Callable, Generator
from enum import Enum
from typing import Any, NamedTuple, NewType

import pyarrow as pa
import pyarrow.compute as pc
import pyarrow.dataset as ds

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from evil.components.datasets.definitions import DI
from evil.components.datasets.filepath_providers.baseFilepathProvider import BaseFilepathProvider, FilepathDicts
from evil.components.datasets.loading_functions import parquet_file_loading_fn

###################################################################################################
#                                         HELPER CLASSES                                          #
###################################################################################################

DatasetFile = typing.NamedTuple("DatasetFile", [("id", str), ("path", str)])  # noqa: UP014
FileListsDict = NewType("FileListsDict", dict[DI, dict[str, pa.Table]])
ArrowDataset = NewType("ArrowDataset", dict[DI, dict[str, ds.Dataset]])


###################################################################################################
#                                 PARQUET FILES FILEPATH PROVIDER                                 #
###################################################################################################


class InputType(Enum):
    """Enum for input types."""

    IMAGE = "image"
    PR = "pred"


class PredictionColumn(NamedTuple):
    """Prediction data class."""

    class_name: str
    class_conf: str


class PredictionTable(Enum):
    """Enum containing list of prediction data attributes."""

    TOP1 = PredictionColumn("class1", "class1_conf")
    TOP2 = PredictionColumn("class2", "class2_conf")
    TOP3 = PredictionColumn("class3", "class3_conf")


class ParquetFilesFilepathProvider(BaseFilepathProvider):
    """Filepath provider that loads several parquet files and output of each one presents under the configured key.

    .. note::
        The ground truth and prediction parquet files are configured as lists of dictionaries in
        the following form:
        .. code::
            [
                {
                    "id": "BOX",
                    "path": "path/to/box.parquet"
                },
                {
                    "id": "SEMSEG",
                    "path": "path/to/semseg.parquet"
                },
                ...
            ]
        The ids are the keys under which the loaded filepaths will be present in the output of the
        :py:func:`filepaths()` generator. They are meant to be used to map the paths to configured
        loaders further in the pipeline.

    Args:
        gt_parquet_files: Ground truth dataset files' :obj:`DatasetFile` setups
        pr_parquet_files: Prediction dataset files' :obj:`DatasetFile` setups
        loading_fn: Loading function for dataset files (for testing)
    """

    name = "parquet_files"

    def __init__(
        self,
        gt_dataset_files: list[DatasetFile],
        pr_dataset_files: list[DatasetFile],
        loading_fn: Callable[[str], ds.Dataset] = parquet_file_loading_fn,
    ) -> None:
        """Initialize the ParquetFilesFilepathProvider.

        Args:
            gt_dataset_files: Ground truth dataset files' DatasetFile setups.
            pr_dataset_files: Prediction dataset files' DatasetFile setups.
            loading_fn: Loading function for dataset files (for testing).
        """
        self.parquet_dataset_files_dict = {DI.GT: gt_dataset_files, DI.PR: pr_dataset_files}

        self.loading_fn = loading_fn

    def filepaths(self) -> Generator[FilepathDicts, None, None]:
        """Returns a generator of filepath dictionaries.

        .. note::
            The returned dictionary is indexed by dataset identifiers (DI.GT, DI.PR) and the
            underlying dictionaries are indexed by the ids of the configured dataset files:
            .. code::
                {
                    DI.GT: {
                        "id1": "id1_gt_data",
                        "id2": "id2_gt_data",
                        ...
                    },
                    DI.PR: {
                        "id3": "id3_pred_data",
                        ...
                    }
                }

        Returns:
            Generator of filepath dictionaries
        """
        file_lists_dict = self._load_parquet_dataset_files()
        num_files = _compare_and_get_length(file_lists_dict)

        file_path_dicts_gen = (
            FilepathDicts(
                {
                    di: {id: next(file_list) for id, file_list in file_lists.items()}  # noqa: A001
                    for di, file_lists in file_lists_dict.items()
                }
            )
            for _ in range(num_files)
        )

        yield from file_path_dicts_gen

        # ------------------------------------- PRIVATE METHODS ------------------------------------- #

    def _load_parquet_dataset_files(self) -> FileListsDict:
        """Load the parquet dataset files into a dictionary of lists of file paths."""
        file_lists_dict: FileListsDict = FileListsDict({})

        # Since the GT dataset is same for both images and labels in the parquet files, it is loaded only once
        dataset_gt = self.loading_fn(next(iter(self.parquet_dataset_files_dict[DI.GT])).path)
        dataset_pr = self.loading_fn(next(iter(self.parquet_dataset_files_dict[DI.PR])).path)

        # Filter and sort datasets by "object_id" without loading into memory
        dataset_pr_table = ds.Scanner.from_dataset(
            dataset_pr, columns=[ClassifierParquetDataset.TrainColumns.OBJECT_ID.value]
        ).to_table()
        unique_paths = pc.unique(  # type: ignore[arg-type]
            dataset_pr_table[ClassifierParquetDataset.TrainColumns.OBJECT_ID.value]
        )

        filter_expression = pc.field(ClassifierParquetDataset.TrainColumns.OBJECT_ID.value).isin(unique_paths)
        dataset_gt = dataset_gt.filter(filter_expression).sort_by(ClassifierParquetDataset.TrainColumns.OBJECT_ID.value)
        dataset_pr = dataset_pr.sort_by(ClassifierParquetDataset.TrainColumns.OBJECT_ID.value)

        # Process the datasets in batches
        dataset_gt_image_data, dataset_gt_json_data = self._process_gt_table(dataset_gt)
        dataset_pr_data = self._process_pr_table(dataset_pr)

        # Populate file_lists_dict
        for di, parquet_dataset_files in self.parquet_dataset_files_dict.items():
            file_lists_dict[di] = {}
            for parquet_dataset_file in parquet_dataset_files:
                if di == DI.GT:
                    if InputType.IMAGE.value in parquet_dataset_file.id.lower():
                        file_lists_dict[di][parquet_dataset_file.id] = dataset_gt_image_data
                    else:
                        file_lists_dict[di][parquet_dataset_file.id] = dataset_gt_json_data
                else:
                    file_lists_dict[di][parquet_dataset_file.id] = dataset_pr_data

        return file_lists_dict

    def _process_gt_table(
        self, dataset_gt_table: ds.Dataset
    ) -> tuple[Generator[Any, None, None], Generator[Any, None, None]]:
        """Process ground truth dataset table in chunks using Arrow Scanner."""
        # Use a scanner to process the dataset in chunks
        snippet_gt_data_columns = ClassifierParquetDataset.get_column_names(ClassifierParquetDataset.EvilGTColumns)
        scanner = ds.Scanner.from_dataset(
            dataset_gt_table,
            columns=[
                ClassifierParquetDataset.TrainColumns.IMAGE_DATA.value,
                ClassifierParquetDataset.TrainColumns.OBJECT_ID.value,
            ]
            + snippet_gt_data_columns,
        )

        def image_data_generator() -> Generator[Any, None, None]:
            last_key = None
            for batch in scanner.to_batches():
                for row in batch.to_pylist():
                    current_key = row[ClassifierParquetDataset.TrainColumns.OBJECT_ID.value]
                    if current_key != last_key:
                        last_key = current_key
                        yield row[ClassifierParquetDataset.TrainColumns.IMAGE_DATA.value]

        def gt_data_generator() -> Generator[Any, None, None]:
            last_key = None
            for batch in scanner.to_batches():
                for row in batch.to_pylist():
                    current_key = row[ClassifierParquetDataset.TrainColumns.OBJECT_ID.value]
                    if current_key != last_key:
                        last_key = current_key
                        yield {column_name: row[column_name] for column_name in snippet_gt_data_columns}

        return image_data_generator(), gt_data_generator()

    def _process_pr_table(self, dataset_pr_table: ds.Dataset) -> Generator[dict[str, float], None, None]:
        """Process prediction dataset table in chunks using Arrow Scanner."""
        # Use a scanner to process the dataset in chunks
        pred_parquet_columns = [
            attr for pred in PredictionTable for attr in (pred.value.class_name, pred.value.class_conf)
        ] + [ClassifierParquetDataset.TrainColumns.OBJECT_ID.value]
        scanner = ds.Scanner.from_dataset(dataset_pr_table, columns=pred_parquet_columns)

        def prediction_data_generator() -> Generator[Any, None, None]:
            last_key = None
            for batch in scanner.to_batches():
                for row in batch.to_pylist():
                    current_key = row[ClassifierParquetDataset.TrainColumns.OBJECT_ID.value]
                    if current_key != last_key:
                        last_key = current_key
                        # Extract the relevant fields from the row
                        yield {row[pred.value.class_name]: row[pred.value.class_conf] for pred in PredictionTable}

        return prediction_data_generator()


###################################################################################################
#                                        HELPER FUNCTIONS                                         #
###################################################################################################


def _compare_and_get_length(file_lists_dict: FileListsDict) -> int:
    """Compares the lengths of all lists or generators in the given dictionary and returns the length if all are equal.

    otherwise raises a ValueError.

    Args:
        file_lists_dict(dict): Dictionary of dictionaries of file lists or generators

    Returns:
        Number of files in a list or generator

    Raises:
        ValueError if all the lists or generators are not the same length
    """
    num_files = None

    for file_lists in file_lists_dict.values():
        for key, file_list in file_lists.items():
            if isinstance(file_list, Generator):
                # Use itertools.tee to create two independent copies of the generator
                file_list, file_list_copy = itertools.tee(file_list)  # noqa: PLW2901
                file_lists[key] = file_list_copy  # Replace the original generator with the copy
                file_list_len = sum(1 for _ in file_list)
            else:
                file_list_len = len(file_list)

            if num_files is None:
                num_files = file_list_len
            elif file_list_len != num_files:
                msg = f"File lists have different lengths ('{num_files}' != '{file_list_len}')!"
                raise ValueError(msg)

    if num_files is None:
        msg = "No file lists have been provided."
        raise ValueError(msg)

    return num_files
