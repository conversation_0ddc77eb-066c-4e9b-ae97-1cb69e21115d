"""Dataset loader for ground truth classes of a classifier in the JSON format from parquet files.

Example of setting in the YAML configuration file for a joint label format ground truth dataset:
.. code:: yaml
    - type: "cv3ts_parquet_gt"
      label_mapping_country : "DE"
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON>. All rights reserved.
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Callable
from typing import Any

from ts_label_mapping.classifier import ClassMappings

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from evil.components.common.data.classificationData import ClassificationData
from evil.components.datasets.definitions import DI, DLG
from evil.components.datasets.loaders.baseLoader import BaseLoader
from evil.components.datasets.loaders.cv3.ts.loader_utils import get_label_to_ID_map
from evil.components.datasets.loading_functions import parquet_gt_loading_fn

###################################################################################################
#                                         PUBLIC CLASSES                                          #
###################################################################################################


class CV3TSParquetGTLoader(BaseLoader):
    """Class to load the ground truth data for a classifier from Parquet files."""

    groups: list[DLG] = [DLG.CLASSIFICATION]  # noqa: RUF012
    name = "cv3ts_parquet_gt"

    def __init__(
        self, label_mapping_country: str, loading_fn: Callable[[dict[str, Any]], Any] = parquet_gt_loading_fn
    ) -> None:
        """Initialize the patch loader parameters with country code and read the label mapping file.

        Args:
            label_mapping_country: country code
            loading_fn: Loading function for ground truth data from Parquet file.
        """
        super().__init__(loading_fn)  # type: ignore[arg-type]
        # Generate the class name and class Id mapping.
        class_mappings = ClassMappings()
        label_mapping_file_path = class_mappings.get_mapping_table_path()
        # Generate the class name and class Id mapping.
        self.label_to_ID_map = get_label_to_ID_map(label_mapping_file_path, label_mapping_country)

    def parse(
        self, data: dict[str, [str | int | Any]], selectors: list[Any] | None = None, di: DI | None = None
    ) -> dict[DLG, Any]:
        """Parses the JSON data into :obj:`.ClassificationData`.

        Args:
            data: json data containing label information.
            selectors: List of selectors.
            di: Dataset types.

        Returns:
            dictionary containing the ground truth value.
        """

        class_name = ClassifierParquetDataset.TrainColumns.CLASS_NAME.value
        data.update({"class_id": self.label_to_ID_map.index(data[class_name])})
        data["class_label"] = data.pop(class_name)

        classification_gt = ClassificationData(samples=[data])

        return {DLG.CLASSIFICATION: classification_gt}
