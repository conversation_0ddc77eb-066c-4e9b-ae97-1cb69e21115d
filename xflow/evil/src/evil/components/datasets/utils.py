"""Utility functions for datasets."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Callable, Iterable
from typing import TypeVar
from urllib.parse import urlparse

import pyarrow.dataset as ds

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from evil.components.common.data.boundingBox import BoundingBox
from evil.components.common.data.detection import Detection

T = TypeVar("T")
B = TypeVar("B", bound=BoundingBox)
D = TypeVar("D", bound=Detection)

###################################################################################################
#                                        PUBLIC FUNCTIONS                                         #
###################################################################################################


def load_dataset_file(path_dataset: str) -> list[str]:
    """Loads the .dataset file with file paths into a list. Retains the original order!

    Args:
        path_dataset: Path to the dataset file to be loaded

    Returns:
        :obj:`list` of paths contained in the dataset
    """
    with open(path_dataset) as infile:
        file_paths = [line.strip() for line in infile]

    return file_paths


def load_parquet_file(path_parquet: str) -> ds.Dataset:
    """Loads the .parquet file with file paths into a list. Retains the original order!

    Args:
        path_parquet: Path to the parquet file to be loaded

    Returns:
        pyarrow dataset object
    """
    with open(path_parquet, encoding="utf-8") as f_in:
        parquet_file_paths = [item.strip() for item in f_in]
    parquet_file_paths = parquet_file_paths if parquet_file_paths[0].endswith(".parquet") else parquet_file_paths[0]
    classifier_dataset = ClassifierParquetDataset()
    dataset = classifier_dataset.load_parquet_dataset(parquet_file_paths, shuffle=False)  # type: ignore[arg-type]
    return dataset


def load_file_from_anywhere(file_path: str, loading_fn: Callable[[str], T]) -> T:
    """Wrapper around the given :py:func`loading_fn`.

    Provides functionality to load a file from both a relative and absolute path.

    .. note::

        The supported paths are the following (sample paths):

            * Relative system path: "some/relative/path/file.json"
            * Absolute system path: "/some/absolute/path/file.json"

    Args:
        file_path: String file path to the file to be loaded
        loading_fn: Function to be used to load the file and returns loaded data

    Returns:
        :obj:`object` data loaded by the :py:func:`loading_fn`

    Raises:
        IOError: If the type of the file path cannot be recognized.
    """
    parse_result = urlparse(file_path.strip())

    if parse_result.scheme == "":
        # This is a normal system file path - just open the file
        return loading_fn(parse_result.path)

    # Some other type of path we don't know how to read
    msg = f"I don't know how to load the file '{file_path}'! It is not a regular file path or an S3 path!"
    raise OSError(msg)


def lock_detections(detections: Iterable[D]) -> Iterable[D]:
    """Run the :meth:`Detection.lock` method on all detections in the container.

    Args:
        detections: A container of detections.

    Returns:
        The same container as the input.
    """

    for detection in detections:
        detection.lock()

    return detections
