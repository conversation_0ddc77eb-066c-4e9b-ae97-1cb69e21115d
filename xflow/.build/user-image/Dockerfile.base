# ===================================================================================
#  C O P Y R I G H T
# -----------------------------------------------------------------------------------
#  Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
#  Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ===================================================================================

FROM vdeepacrprod.azurecr.io/xflow_dev_base:latest

ARG HOME
ARG HTTP_PROXY
ARG HTTPS_PROXY
ARG USER_GID
ARG USER_NAME
ARG USER_UID
ARG WORKSPACE

SHELL ["/bin/bash", "-c"]

RUN mkdir -p $(dirname ${HOME})

# Create user
RUN groupadd -g $USER_GID user && \
    echo "Creating user $USER_NAME with home $HOME" && \
    useradd -l -u $USER_UID -s /bin/bash -g user --home-dir $HOME -m $USER_NAME  && \
    echo "$USER_NAME ALL=(ALL) NOPASSWD:ALL" >> /etc/sudoers

USER $USER_NAME
ENV PATH="$HOME/.local/bin:/usr/bin:$PATH"

RUN bash -c "$(curl -fsSL https://raw.githubusercontent.com/ohmybash/oh-my-bash/master/tools/install.sh)" && \
    mkdir -p ${HOME}/.pace/cache && \
    mkdir -p ${HOME}/.config/xflow && \
    mkdir -p ${HOME}/.config/pip

# Enable persistent Bash-History
RUN SNIPPET="export HISTFILE=$HOME/commandhistory/.bash_history" \
    && mkdir $HOME/commandhistory \
    && touch $HOME/commandhistory/.bash_history \
    && chown -R $USER_NAME $HOME/commandhistory \
    && echo "$SNIPPET" >> "$HOME/.bashrc"

# Enable persistent .bashrc_user for customizations
RUN touch $HOME/.bashrc_user \
    && echo ""  >> "$HOME/.bashrc" \
    && echo "# Customize your Devcontainer's bashrc persistently by using ~/.bashrc_user"  >> "$HOME/.bashrc" \
    && echo "source ~/.bashrc_user" >> "$HOME/.bashrc" \
    && echo ""  >> "$HOME/.bashrc" \
    && echo "# This needs to be the last line s.t. bashrc_user is used correctly"  >> "$HOME/.bashrc" \
    && echo 'source "$OSH"/oh-my-bash.sh'  >> "$HOME/.bashrc"

RUN curl -LsSf https://astral.sh/uv/install.sh | sudo -EH sh

ENV VIRTUAL_ENV=$WORKSPACE/.venv \
    PATH=$WORKSPACE/.venv/bin:$PATH \
    LD_LIBRARY_PATH=$WORKSPACE/.venv/lib:$LD_LIBRARY_PATH

WORKDIR $WORKSPACE
