ARG BASE_IMAGE

FROM $BASE_IMAGE

# Disable the package download cache to avoid increasing the image size even more
RUN echo "Acquire::http {No-Cache=True;};" > /etc/apt/apt.conf.d/no-cache

RUN set -ex && \
    curl -fsSL https://cli.github.com/packages/githubcli-archive-keyring.gpg | dd of=/usr/share/keyrings/githubcli-archive-keyring.gpg && \
    chmod go+r /usr/share/keyrings/githubcli-archive-keyring.gpg && \
    echo "deb [arch=$(dpkg --print-architecture) signed-by=/usr/share/keyrings/githubcli-archive-keyring.gpg] https://cli.github.com/packages stable main" | tee /etc/apt/sources.list.d/github-cli.list > /dev/null && \
    \
    . /etc/os-release && \
    curl -sSL -O https://packages.microsoft.com/config/ubuntu/${VERSION_ID}/packages-microsoft-prod.deb && \
    dpkg -i packages-microsoft-prod.deb && \
    rm packages-microsoft-prod.deb

RUN --mount=type=cache,target=/root/.cache \
    --mount=type=secret,id=netrc,target=/root/.netrc \
    set -ex && \
    apt-get update && \
    DEBIAN_FRONTEND=noninteractive apt-get install -y --no-install-recommends \
        azcopy \
        bash-completion \
        btop \
        datacenter-gpu-manager \
        fd-find \
        file \
        gdb \
        gh \
        protobuf-compiler \
        psmisc \
        ripgrep \
        tree \
    && \
    \
    # cleanup
    apt-get clean && \
    rm -rf /var/lib/apt/lists/* && \
    \
    ln -s $(which fdfind) /usr/local/bin/fd

RUN --mount=type=cache,target=/root/.cache \
    set -ex && \
    # download and install quarto
    wget https://quarto.org/download/latest/quarto-linux-amd64.deb && \
    dpkg -i quarto-linux-amd64.deb && \
    rm quarto-linux-amd64.deb && \
    # download and install kubectl to interact with kubernetes
    curl -LO "https://dl.k8s.io/release/$(curl -L -s https://dl.k8s.io/release/stable.txt)/bin/linux/amd64/kubectl" && \
    install -m 0755 kubectl /usr/local/bin/kubectl && \
    rm kubectl && \
    # download and install kubelogin to use AAD authentication with kubernetes
    curl -L https://github.com/Azure/kubelogin/releases/latest/download/kubelogin-linux-amd64.zip -o kubelogin.zip && \
    unzip kubelogin.zip -d /tmp/kubelogin && \
    install -m 0755 /tmp/kubelogin/bin/linux_amd64/kubelogin /usr/local/bin/kubelogin && \
    rm -rf /tmp/kubelogin kubelogin.zip
