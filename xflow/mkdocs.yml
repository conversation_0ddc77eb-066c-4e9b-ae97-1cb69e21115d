# @copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
#
# The reproduction, distribution and utilization of this file as well as the communication of its contents to others
# without express authorization is prohibited. Offenders will be held liable for the payment of damages and can be
# prosecuted. All rights reserved particularly in the event of the grant of a patent, utility model or design.

site_name: "xflow"
site_url: "https://pace-docs.azurewebsites.net/xflow"
repo_url: "https://github.com/PACE-INT/xflow/"
repo_name: "PACE-INT/xflow"
edit_uri: edit/main/.docs/

docs_dir: .docs
site_dir: .site

nav:
  - Overview:
    - Get Started:
        - index.md
    - Packages:
        - cluster_cloud:
            - cluster_cloud/index.md
        - conversion:
            - conversion/index.md
        - conversion_contract:
            - conversion_contract/index.md
        - data_formats:
            - overview: data_formats/index.md
            - air: data_formats/air.md
        - data_test_framework:
            - data_test_framework/index.md
        - evil:
            - evil/index.md
        - loomy:
            - loomy/index.md
        - loomy_usecases:
            - loomy_usecases/index.md
        - pyper:
            - pyper/index.md
        - pytest_lfs_tracker:
            - pytest_lfs_tracker/index.md
        - release_report:
            - release_report/index.md
        - xazure:
            - xazure/index.md
        - xcommon:
            - overview: xcommon/index.md
            - ct manager: xcommon/ct_manager.md
        - xcontract:
            - xcontract/index.md
        - xhelpers:
            - xhelpers/index.md
        - xreport:
            - xreport/index.md
        - xtension:
            - overview: xtension/index.md
            - data cache: xtension/data_cache.md
            - pre training: xtension/pre_training.md
        - xtensorflow:
            - xtensorflow/index.md
        - xtorch:
            - overview: xtorch/index.md
            - config registries: xtorch/config_registry.md
            - hydra configs: xtorch/hydra_configs.md
            - data transforms: xtorch/data/transforms/README.md
        - xtorch_extensions:
            - xtorch_extensions/index.md
        - xtorch_usecases:
            - overview: xtorch_usecases/index.md
            - example: xtorch_usecases/example.md
            - mnist example: xtorch_usecases/mnist_example.md
            - dynamic graphs pitfalls: xtorch_usecases/dynamic_graphs_pitfalls.md
        - xusecases:
            - overview: xusecases/index.md
            - common: xusecases/common.md
            - classification: xusecases/classification.md
            - experimental: xusecases/experimental.md
            - light rnn: xusecases/light_rnn.md
            - vision:
                - overview: xusecases/vision.md
                - offline_perception: xusecases/vision/offline_perception.md
                - pace_general.release: xusecases/vision/pace_general.release.md
                - pace_general.robustification: xusecases/vision/pace_general.robustification.md
  - API reference: reference/
  - Manifest:
      - Manifest: manifest.md
      - Review Guidelines: review_guidelines.md
      - Dependency management: dependency_management.md
  - TDP:
      - Tool Split: tool_development_plan/tool_split.md
      - Tool Information & Classification: tool_development_plan/tool_information_and_classification.md
      - UC1 Data Preparation: tool_development_plan/uc1_data_preparation.md
      - UC2 Core: tool_development_plan/uc2_core.md
      - UC3 Evaluation: tool_development_plan/uc3_evaluation.md
      - Tool Development: tool_development_plan/tool_development.md
  - License: license.md

theme:
  name: material
  features:
    - content.action.edit
    - content.action.view
    - content.code.annotate
    - content.code.copy
    - navigation.expand
    - navigation.indexes
    - navigation.instant
    - navigation.tabs
    - navigation.sections
    - navigation.tabs.sticky
    - navigation.tracking
    - navigation.top
    - search.highlight
    - search.suggest
  icon:
    repo: fontawesome/brands/github
  language: 'en'
  palette:
    - media: "(prefers-color-scheme: light)"
      scheme: default
      toggle:
        icon: material/weather-night
        name: Switch to dark mode
    - media: "(prefers-color-scheme: dark)"
      scheme: slate
      toggle:
        icon: material/weather-sunny
        name: Switch to light mode

extra:
  generator: false

markdown_extensions:
  # Recommended configuration from
  # https://squidfunk.github.io/mkdocs-material/setup/extensions/#recommended-configuration
  # Python Markdown
  - abbr
  - admonition
  - attr_list
  - def_list
  - footnotes
  - meta
  - md_in_html
  - toc:
      permalink: true

  # Python Markdown Extensions
  - pymdownx.arithmatex:
      generic: true
  - pymdownx.betterem:
      smart_enable: all
  - pymdownx.caret
  - pymdownx.details
  - pymdownx.emoji:
      emoji_index: !!python/name:material.extensions.emoji.twemoji
      emoji_generator: !!python/name:material.extensions.emoji.to_svg
  - pymdownx.highlight
  - pymdownx.inlinehilite
  - pymdownx.keys
  - pymdownx.mark
  - pymdownx.smartsymbols
  - pymdownx.snippets:
      check_paths: true
  - pymdownx.superfences:
      custom_fences:
        - name: mermaid
          class: mermaid
          format: !!python/name:pymdownx.superfences.fence_code_format
  - pymdownx.tabbed:
      alternate_style: true
  - pymdownx.tasklist:
      custom_checkbox: true
  - pymdownx.tilde

extra_css:
  - stylesheets/extra.css

plugins:
- autorefs
- search
- markdown-exec
- gen-files:
    scripts:
    - .build/docs/gen_ref_nav.py
- literate-nav:
    nav_file: SUMMARY.md
- mkdocstrings:
    handlers:
      python:
        import:
        - https://docs.python.org/3/objects.inv
        paths:
        - cluster_cloud/src
        - conversion/src
        - conversion_contract/src
        - data_formats/src
        - data_test_framework/src
        - evil/src
        - loomy/src
        - loomy_usecases/src
        - pyper/src
        - release_report/src
        - xcloud/azure_tools/src
        - xazure/src
        - xazure_pipelines/src
        - xcommon/src
        - xcontract/src
        - xreport/src
        - xtension/src
        - xtensorflow/src
        - xtorch/src
        - xtorch_extensions/src
        - xtorch_usecases/src
        - xusecases/src
        options:
          # docstring_options:
          #   ignore_init_summary: true
          docstring_section_style: list
          # filters: ["!^_"]
          # heading_level: 1
          inherited_members: true
          merge_init_into_class: true
          parameter_headings: true
          separate_signature: true
          show_root_heading: true
          # show_root_full_path: false
          show_signature_annotations: true
          # show_source: false
          show_symbol_type_heading: true
          show_symbol_type_toc: true
          signature_crossrefs: true
          summary: true
- mkdocs-jupyter:
    include_source: True
