[shard]
	missingTrees = true
	modAuthorName = "Robert <PERSON>sch GmbH"
	modAuthorEmail = "<EMAIL>"
	modCommitterName = "Robert <PERSON> GmbH"
	modCommitterEmail = "<EMAIL>"

[tree "cluster_cloud"]
	map = libs/cluster_cloud

[tree "conversion"]
	map = libs/conversion

[tree "conversion_contract"]
	map = libs/conversion_contract

[tree "data_formats"]
	map = libs/data_formats

[tree "evil"]
	map = libs/evil

[tree "pyper"]
	map = libs/pyper

[tree "xreport"]
	map = libs/xreport

[tree "xazure"]
	map = libs/xazure

[tree "xcommon"]
	map = libs/xcommon

[tree "xcloud"]
	map = libs/xcloud

[tree "xcontract"]
	map = libs/xcontract

[tree "xhelpers"]
	map = libs/xhelpers

[tree "xtension"]
	map = libs/xtension

[tree "xtensorflow"]
	map = libs/xtensorflow

[tree "xtorch"]
	map = libs/xtorch

[tree "xtorch_usecases"]
	map = libs/xtorch_usecases

[tree "xusecases"]
	map = libs/xusecases

[tree ".build"]
    map = .build/.build_ada

[tree ".fossid"]
	map = .fossid

[blob ".gitattributes"]  # .gitattributes for lfs
	map = true

[blob "uv.lock"]  # requirements for docker build
	map = true
