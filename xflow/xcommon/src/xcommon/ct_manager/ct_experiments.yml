# Copyright (c) 2017-2022 Daimler AG and Robert <PERSON> GmbH. All rights reserved.
# Copyright (c) 2023-2024 Robert <PERSON> GmbH and Cariad SE. All rights reserved.

# NOTE: The "owner_ids" field contains the Entra ID of the owner(s) responsible for maintaining the experiment.
# Due to regulations, don't put the full name of the person in that field. (also not in a comment)
# The Entra ID can be obtained at https://portal.azure.com/#view/Microsoft_AAD_UsersAndTenants/UserManagementMenuBlade/~/AllUsers
# the specified IDs can also be validated by calling "xcommon/ct_manager/main.py --validate_owner_ids"

experiments:

# --------------------------------------------------- VISION -----------------------------------------------------------
- experiment_name: vision_pace_general_yolane_yolo_v4_tiny_ct
  launcher: PipelineLauncherCt
  usecase_package: xusecases.vision.pipelines
  pipeline: concurrent_release
  entry_model: pace_general.release
  model_config: yolane_yolo_v4_tiny
  workpackage: viper_lane
  stage_max_runtime_days:
    train: 3
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    train: gpupoolt4


- experiment_name: vision_xtorch_single_camera_fc1_multi_task_ct
  owner_ids: 426ac507-49a6-40c4-99f3-b8900da61439;99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5
  launcher: PipelineLauncherCt
  stage_args:
    publish: config.publish_config.publish_to_artifactory=True
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_multi_task
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train: 3
    evaluate: 3
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupoolh100
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_fc1_multi_task_cct_ct
  owner_ids: 426ac507-49a6-40c4-99f3-b8900da61439;99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5
  launcher: PipelineLauncher
  stage_args:
    train_cct: config.experiment_name=vision_xtorch_single_camera_fc1_multi_task_cct_ct
    evaluate: config.experiment_name=vision_xtorch_single_camera_fc1_multi_task_cct_ct
    publish: config.publish_config.publish_to_artifactory=True config.experiment_name=vision_xtorch_single_camera_fc1_multi_task_cct_ct
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_cct_eval
  model_config: fc1_yuv444_multi_task
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train_cct: 3
    evaluate: 3
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train_cct: gpupoolh100
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_tv_multi_task_ct
  owner_ids: 426ac507-49a6-40c4-99f3-b8900da61439;99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5
  launcher: PipelineLauncherCt
  stage_args:
    publish: config.publish_config.publish_to_artifactory=True
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: tv_yuv444_multi_task
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train: 3
    evaluate: 3
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupoolh100
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_tv_multi_task_cct_ct
  owner_ids: 426ac507-49a6-40c4-99f3-b8900da61439;99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5
  launcher: PipelineLauncher
  stage_args:
    train_cct: config.experiment_name=vision_xtorch_single_camera_tv_multi_task_cct_ct
    evaluate: config.experiment_name=vision_xtorch_single_camera_tv_multi_task_cct_ct
    publish: config.publish_config.publish_to_artifactory=True config.experiment_name=vision_xtorch_single_camera_tv_multi_task_cct_ct
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_cct_eval
  model_config: tv_yuv444_multi_task
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train_cct: 3
    evaluate: 3
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train_cct: gpupoolh100
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_multi_task_trifocal_ct
  owner_ids: 426ac507-49a6-40c4-99f3-b8900da61439;94121137-d616-4050-b963-e5f4afaa1297;99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;07065411-0e4d-4fe4-90ec-1f45802820a5;032b1792-faea-4ddb-89f9-7904a214ecda;144a4038-7e34-4c21-aeb9-fdd2c4557b38;f791ac73-4ded-44cd-b359-9a23e8ea874a;8995e742-8eeb-47da-adf0-4659d0cd6869;24c66057-e274-4767-a076-9abc9f4b9193;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  stage_args:
    train: config.task_configs.active_learning_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_sign_sum_hard_entropy=null
    evaluate: config.task_configs.active_learning_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_sign_sum_hard_entropy=null
    publish: config.publish_config.publish_to_artifactory=True
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: release_no_qnn_eval
  model_config: fc1_yuv444_multi_task_trifocal
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train: 3
    evaluate: 3
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupoolh100
    evaluate: gpupoolt4
    export: gpupoolt4
    convert_qnn: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_multi_task_trifocal_cct_ct
  owner_ids: 426ac507-49a6-40c4-99f3-b8900da61439;94121137-d616-4050-b963-e5f4afaa1297;99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;07065411-0e4d-4fe4-90ec-1f45802820a5;032b1792-faea-4ddb-89f9-7904a214ecda;144a4038-7e34-4c21-aeb9-fdd2c4557b38;f791ac73-4ded-44cd-b359-9a23e8ea874a;8995e742-8eeb-47da-adf0-4659d0cd6869;24c66057-e274-4767-a076-9abc9f4b9193;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncher
  stage_args:
    train_cct: config.experiment_name=vision_xtorch_single_camera_multi_task_trifocal_cct_ct config.task_configs.active_learning_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_sign_sum_hard_entropy=null
    evaluate: config.experiment_name=vision_xtorch_single_camera_multi_task_trifocal_cct_ct config.task_configs.active_learning_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_light_sum_hard_entropy=null config.task_configs.active_learning_traffic_sign_sum_hard_entropy=null
    export: config.experiment_name=vision_xtorch_single_camera_multi_task_trifocal_cct_ct
    convert_qnn: config.experiment_name=vision_xtorch_single_camera_multi_task_trifocal_cct_ct
    publish: config.publish_config.publish_to_artifactory=True config.experiment_name=vision_xtorch_single_camera_multi_task_trifocal_cct_ct
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_cct_eval_export_convert
  model_config: fc1_yuv444_multi_task_trifocal
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train_cct: 3
    evaluate: 3
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train_cct: gpupoolh100
    evaluate: gpupoolt4
    export: gpupoolt4
    convert_qnn: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_sparse_lanecpp_ct
  owner_ids: fcec9c7a-dd7c-4d88-96f7-2fddee289977;707707b6-fc5f-4e51-8d0b-434b96484696;9d42ae2a-4704-4482-8c70-ab8592bab8c3;98e2d36b-d6ca-43f7-94a9-98fb03d9e769;4f2de6e8-4415-4e33-b03d-2bc98e5e0c01;33e19925-8dff-4b63-b392-0a914f1b345a
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: release
  model_config: fc1_yuv444_sparse_lanecpp
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train: 3
    evaluate: 3
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupoolh100
    evaluate: gpupoolt4
    export: gpupoolt4
    convert_qnn: cpupool16core600
  distribution_context: torch
  launcher_args:
    final_publish_args: config.publish_config.publish_to_artifactory=True config.eval_config.trigger_embedded_qnn_eval=True

- experiment_name: vision_xtorch_single_camera_traffic_light_trifocal_ct
  owner_ids: 07065411-0e4d-4fe4-90ec-1f45802820a5;9589e297-b3f2-4678-81e6-8ce4b9666c6b;24c66057-e274-4767-a076-9abc9f4b9193;8995e742-8eeb-47da-adf0-4659d0cd6869
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_traffic_light_trifocal
  workpackage: traffic_lights
  stage_max_runtime_days:
    train: 3
    evaluate: 3
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupoolh100
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_traffic_sign_trifocal_ct
  owner_ids: 24c66057-e274-4767-a076-9abc9f4b9193;8995e742-8eeb-47da-adf0-4659d0cd6869;032b1792-faea-4ddb-89f9-7904a214ecda;fe5965b2-8b69-4fce-beb4-560a113d1a46
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_traffic_sign_trifocal
  workpackage: traffic_signs
  stage_max_runtime_days:
    train: 3
    evaluate: 3
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupoolh100
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_blockage_trifocal_ct
  owner_ids: f791ac73-4ded-44cd-b359-9a23e8ea874a;702f7d89-74d4-464d-972d-cc7d21dc31eb;94121137-d616-4050-b963-e5f4afaa1297;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_blockage_trifocal
  workpackage: environment
  stage_max_runtime_days:
    train: 1
    evaluate: 1
  schedule:
    period_days: 14
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4coret4
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_blockage_tv_ct
  owner_ids: f791ac73-4ded-44cd-b359-9a23e8ea874a;702f7d89-74d4-464d-972d-cc7d21dc31eb;94121137-d616-4050-b963-e5f4afaa1297;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: tv_yuv444_blockage
  workpackage: environment
  stage_max_runtime_days:
    train: 1
    evaluate: 1
  schedule:
    period_days: 14
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4coret4
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_road_condition_ct
  owner_ids: f791ac73-4ded-44cd-b359-9a23e8ea874a;702f7d89-74d4-464d-972d-cc7d21dc31eb;94121137-d616-4050-b963-e5f4afaa1297;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_road_condition_trifocal
  workpackage: environment
  stage_max_runtime_days:
    train: 1
    evaluate: 1
  schedule:
    period_days: 14
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4coret4
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_road_type_ct
  owner_ids: f791ac73-4ded-44cd-b359-9a23e8ea874a;702f7d89-74d4-464d-972d-cc7d21dc31eb;94121137-d616-4050-b963-e5f4afaa1297;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_road_type_trifocal
  workpackage: environment
  stage_max_runtime_days:
    train: 1
    evaluate: 1
  schedule:
    period_days: 14
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4coret4
    evaluate: gpupoolt4
  distribution_context: torch


- experiment_name: vision_xtorch_single_camera_fog_detection_ct
  owner_ids: f791ac73-4ded-44cd-b359-9a23e8ea874a;702f7d89-74d4-464d-972d-cc7d21dc31eb;94121137-d616-4050-b963-e5f4afaa1297;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_fog_detection_trifocal
  workpackage: environment
  stage_max_runtime_days:
    train: 1
    evaluate: 1
  schedule:
    period_days: 14
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4coret4
    evaluate: gpupoolt4
  distribution_context: torch

- experiment_name: vision_xtorch_single_camera_tunnel_ct
  owner_ids: f791ac73-4ded-44cd-b359-9a23e8ea874a;702f7d89-74d4-464d-972d-cc7d21dc31eb;94121137-d616-4050-b963-e5f4afaa1297;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.single_camera.pipelines
  pipeline: train_eval
  model_config: fc1_yuv444_tunnel_trifocal
  workpackage: environment
  stage_max_runtime_days:
    train: 1
    evaluate: 1
  schedule:
    period_days: 14
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4coret4
    evaluate: gpupoolt4
  distribution_context: torch

# ---------------------------------------------------- MULTIMODAL -----------------------------------------------------------
- experiment_name: multimodal_release_ct
  owner_ids: 426ac507-49a6-40c4-99f3-b8900da61439;94121137-d616-4050-b963-e5f4afaa1297;99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;07065411-0e4d-4fe4-90ec-1f45802820a5;032b1792-faea-4ddb-89f9-7904a214ecda;144a4038-7e34-4c21-aeb9-fdd2c4557b38;f791ac73-4ded-44cd-b359-9a23e8ea874a;8995e742-8eeb-47da-adf0-4659d0cd6869;24c66057-e274-4767-a076-9abc9f4b9193;03e680d7-4d77-4731-a6f1-e543cf99ccc1
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval
  entry_model: release
  workpackage: multi_task_learning
  stage_max_runtime_days:
    train: 4
  # schedule:
  #   period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  # stage_args:
  #   publish: --trigger_embedded_qnn_eval --publish_to_artifactory

- experiment_name: multimodal_box3dsimple_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;1abebfaf-9a6d-4790-b64c-c8c42e910c04;ae8550ae-897f-42df-bfdd-82d2f3be9297;ec98ea45-9c58-4ab7-bde3-c742bc866953
  launcher: PipelineLauncherCt
  launcher_args:
    final_publish_args: --publish_to_model_store --trigger_embedded_qnn_eval --publish_to_artifactory
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval_convert_eval
  entry_model: box3d_simple
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
    convert: gpupoolt4
  compute_count:
    train: 2
  distribution_context: torch

- experiment_name: camera_box3dsimple_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;1abebfaf-9a6d-4790-b64c-c8c42e910c04;ae8550ae-897f-42df-bfdd-82d2f3be9297;ec98ea45-9c58-4ab7-bde3-c742bc866953
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval
  entry_model: box3d_simple
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    train: --enabled_modalities camera
    evaluate_torch: --enabled_modalities camera

- experiment_name: radar_box3dsimple_ct
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval
  entry_model: box3d_simple
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    train: --enabled_modalities radar
    evaluate_torch: --enabled_modalities radar

- experiment_name: multimodal_box3dsparse_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;1abebfaf-9a6d-4790-b64c-c8c42e910c04;ae8550ae-897f-42df-bfdd-82d2f3be9297;ec98ea45-9c58-4ab7-bde3-c742bc866953
  launcher: PipelineLauncherCt
  launcher_args:
    final_publish_args: --publish_to_model_store --trigger_embedded_qnn_eval --publish_to_artifactory
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval_convert
  entry_model: sparse_detection
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
    convert: gpupoolt4
  compute_count:
    train: 2
  distribution_context: torch

- experiment_name: multimodal_box3dsparsetemporal_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;1abebfaf-9a6d-4790-b64c-c8c42e910c04;ae8550ae-897f-42df-bfdd-82d2f3be9297;ec98ea45-9c58-4ab7-bde3-c742bc866953
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval
  entry_model: sparse_detection_temporal
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 5
  # enable the schedule as soon as we have a meaningful temporal model running
  # schedule:
  #   period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch

- experiment_name: camera_box3dsparse_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;1abebfaf-9a6d-4790-b64c-c8c42e910c04;ae8550ae-897f-42df-bfdd-82d2f3be9297;ec98ea45-9c58-4ab7-bde3-c742bc866953
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval
  entry_model: sparse_detection
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    train: --enabled_modalities camera
    evaluate_torch: --enabled_modalities camera

- experiment_name: radar_box3dsparse_ct
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval_convert
  entry_model: sparse_detection
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
    convert: gpupoolt4
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    train: --enabled_modalities radar
    evaluate_torch: --enabled_modalities radar
    convert: --enabled_modalities radar

- experiment_name: multimodal_ground_truth_ct
  owner_ids: 68b29d15-a1b8-48df-ac63-c87f9236ea23;04d10cee-f5be-4209-b23d-a38c7d3f8150;dc00aead-573b-4045-bd06-686b9e8968e6
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval_convert_onnx_eval
  entry_model: ground_truth
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 3
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
    convert: gpupoolt4
  compute_count:
    train: 2
  distribution_context: torch

- experiment_name: camera_ground_truth_ct
  owner_ids: 68b29d15-a1b8-48df-ac63-c87f9236ea23;04d10cee-f5be-4209-b23d-a38c7d3f8150;dc00aead-573b-4045-bd06-686b9e8968e6
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval
  entry_model: ground_truth
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    train: --usecase camera_only
    evaluate_torch: --usecase camera_only

- experiment_name: lidar_ground_truth_ct
  owner_ids: 68b29d15-a1b8-48df-ac63-c87f9236ea23;04d10cee-f5be-4209-b23d-a38c7d3f8150;dc00aead-573b-4045-bd06-686b9e8968e6
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval
  entry_model: ground_truth
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 7
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    train: --usecase lidar_only
    evaluate_torch: --usecase lidar_only

- experiment_name: multimodal_dual_grid_occupancy_loomy_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;4948c080-0a34-4094-b0dc-ed09f7711923;827ad927-22a2-4d77-ac02-a51fd04cb7fc;ed318e3c-4b12-4373-8061-1bbf9325ff24;9b209f1f-e40d-4384-b2f1-23fb13b1252f;5c7d42c0-9764-4ceb-ba45-1ee7ddeae9d4;f0420a8d-4aa1-46b5-a1f3-ce89775dc407;c0333f1d-4cc9-4929-87ae-4aad524748fb;cad67d3f-422b-47c7-ac65-f7fb1dbd2028
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval_convert
  entry_model: occupancy
  workpackage: ai_occupancy
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
    convert: gpupoolt4
  compute_count:
    train: 1
  distribution_context: torch
  stage_args:
    train: --usecase dual_grid --use_loomy
    evaluate_torch: --usecase dual_grid --use_loomy
    convert: --usecase dual_grid --use_loomy

- experiment_name: multimodal_dual_grid_occupancy_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;4948c080-0a34-4094-b0dc-ed09f7711923;827ad927-22a2-4d77-ac02-a51fd04cb7fc;ed318e3c-4b12-4373-8061-1bbf9325ff24;9b209f1f-e40d-4384-b2f1-23fb13b1252f;5c7d42c0-9764-4ceb-ba45-1ee7ddeae9d4;f0420a8d-4aa1-46b5-a1f3-ce89775dc407;c0333f1d-4cc9-4929-87ae-4aad524748fb;cad67d3f-422b-47c7-ac65-f7fb1dbd2028
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval_convert_predict
  entry_model: occupancy
  workpackage: ai_occupancy
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
    convert: gpupoolt4
    predict_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    train: --usecase dual_grid
    evaluate_torch: --usecase dual_grid
    convert: --usecase dual_grid
    predict_torch: --usecase dual_grid data_config.predict_usage="VISUALIZATION"
  launcher_args:
    final_publish_args: --publish_to_model_store --publish_to_artifactory

- experiment_name: multimodal_lanesegnet_ct
  owner_ids: 99c73e0f-7130-4686-a36b-ab82c4a52dce;03a1d0c3-cd07-45be-8e21-7e285b29c3d5;7ddf26d3-1ffb-4faf-8f1f-0cd2b5170b04;9735e9cd-1c72-4278-9e67-11d02c913d2d
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.multimodal.pipelines
  pipeline: train_eval_predict_convert
  entry_model: lanesegnet
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  schedule:
    period_days: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    evaluate_torch: gpupoolh100
  compute_count:
    train: 2
  distribution_context: torch
  stage_args:
    publish: --trigger_embedded_qnn_eval
  launcher_args:
    # Only publish from long run in CT runs triggered via PipelineLauncherCt
    final_publish_args: --trigger_embedded_qnn_eval --publish_to_model_store --publish_to_artifactory

# ---------------------------------------------------- MULTIVIEW -----------------------------------------------------------

- experiment_name: vision_multiview_top_view_release_ct
  launcher: PipelineLauncherCt
  usecase_package: xusecases.vision.pipelines
  pipeline: train_convert_eval
  pipeline_args:
    eval_model_types: tensorrt qnn_htp
  entry_model: multiview.release
  model_config: multiview_release_tv
  workpackage: viper_birds_eye_view
  stage_max_runtime_days:
    train: 2
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    train: gpupool2h100
    convert: gpupoolh100
    evaluate_tensorrt: gpupoolh100


# # ---------------------------------------------------- CLASSIFICATION -----------------------------------------------------------
- experiment_name: classification_ts_class_ct
  owner_ids: 24c66057-e274-4767-a076-9abc9f4b9193;8995e742-8eeb-47da-adf0-4659d0cd6869;032b1792-faea-4ddb-89f9-7904a214ecda;e4e43732-f17e-472d-bdaa-d117aeaa3f01;ea5aecb7-f7f7-440d-b5da-b4c3291dc59e
  launcher: PipelineLauncherCt
  usecase_package: xusecases.classification.pipelines
  pipeline: pretrain_train_convert_eval
  entry_model: ts #path to sub-package of use case
  workpackage: traffic_signs
  model_config: vgg_europe
  schedule:
    period_days: 7
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    pretrain: gpupool4coret4
    train: gpupool4coret4
    convert: gpupool4coret4
    evaluate_tensorrt: gpupool4coret4
    publish: gpupool4coret4

- experiment_name: classification_ts_class_north_america_ct
  owner_ids: 24c66057-e274-4767-a076-9abc9f4b9193;8995e742-8eeb-47da-adf0-4659d0cd6869;032b1792-faea-4ddb-89f9-7904a214ecda;e4e43732-f17e-472d-bdaa-d117aeaa3f01;ea5aecb7-f7f7-440d-b5da-b4c3291dc59e
  launcher: PipelineLauncherCt
  usecase_package: xusecases.classification.pipelines
  pipeline: pretrain_train_convert_eval
  entry_model: ts #path to sub-package of use case
  workpackage: traffic_signs
  model_config: vgg_north_america
  # Re-enable after extracting 64x64 patches
  #schedule:
  #  period_days: 7
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    pretrain: gpupool4coret4
    train: gpupool4coret4
    convert: gpupool4coret4
    evaluate_tensorrt: gpupool4coret4
    publish: gpupool4coret4

- experiment_name: classification_ts_class_asia_ct
  owner_ids: 24c66057-e274-4767-a076-9abc9f4b9193;8995e742-8eeb-47da-adf0-4659d0cd6869;032b1792-faea-4ddb-89f9-7904a214ecda;e4e43732-f17e-472d-bdaa-d117aeaa3f01;ea5aecb7-f7f7-440d-b5da-b4c3291dc59e
  launcher: PipelineLauncherCt
  usecase_package: xusecases.classification.pipelines
  pipeline: pretrain_train_convert_eval
  entry_model: ts #path to sub-package of use case
  workpackage: traffic_signs
  model_config: vgg_asia
  # Re-enable after extracting 64x64 patches
  #schedule:
  #  period_days: 7
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    pretrain: gpupool4coret4
    train: gpupool4coret4
    convert: gpupool4coret4
    evaluate_tensorrt: gpupool4coret4
    publish: gpupool4coret4

- experiment_name: classification_ts_class_rapid_ct
  owner_ids: 24c66057-e274-4767-a076-9abc9f4b9193;8995e742-8eeb-47da-adf0-4659d0cd6869;032b1792-faea-4ddb-89f9-7904a214ecda;e4e43732-f17e-472d-bdaa-d117aeaa3f01;ea5aecb7-f7f7-440d-b5da-b4c3291dc59e
  launcher: PipelineLauncherCt
  usecase_package: xusecases.classification.pipelines
  pipeline: pretrain_train_eval
  entry_model: ts #path to sub-package of use case
  workpackage: traffic_signs
  model_config: vgg_europe
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    pretrain: gpupool4coret4
    train: gpupool4coret4
    evaluate_tensorflow: gpupool4coret4
    publish: gpupool4coret4


# # --------------------------------------------------- LIDAR -----------------------------------------------------------

- experiment_name: lidar_od_pixor_centernet_finetuning_front_ct
  launcher: &lidar_ct_launcher PipelineLauncherCt
  usecase_package: &lidar_usecase_package xusecases.lidar.pipelines
  pipeline: &lidar_pipeline_prod release_prod
  entry_model: object_detection
  model_config: pixor_centernet_finetuning_front
  workpackage: &lidar_workpackage lidar_object_detection
  train_image: &lidar_train_image xflow_train
  release_image: &lidar_release_image xflow_release
  distribution_context: &lidar_distribution_context horovod
  compute_target: &lidar_compute_target
    train: gpupoola100
    evaluate_tensorflow: gpupool4coret4
    convert: gpupool4coret4
    publish: cpupool8core300


- experiment_name: lidar_od_pixor_centernet_ct
  launcher: *lidar_ct_launcher
  usecase_package: *lidar_usecase_package
  pipeline: *lidar_pipeline_prod
  entry_model: object_detection
  model_config: pixor_centernet
  workpackage: *lidar_workpackage
  train_image: *lidar_train_image
  release_image: *lidar_release_image
  compute_target: *lidar_compute_target
  distribution_context: *lidar_distribution_context


- experiment_name: lidar_od_pointpillars_centernet_ct
  launcher: *lidar_ct_launcher
  usecase_package: *lidar_usecase_package
  pipeline: *lidar_pipeline_prod
  entry_model: object_detection
  model_config: pointpillars_centernet
  workpackage: *lidar_workpackage
  train_image: *lidar_train_image
  release_image: *lidar_release_image
  compute_target: *lidar_compute_target
  distribution_context: *lidar_distribution_context


- experiment_name: lidar_od_pixor_centernet_pretraining_ct
  launcher: *lidar_ct_launcher
  usecase_package: *lidar_usecase_package
  pipeline: *lidar_pipeline_prod
  entry_model: object_detection
  model_config: pixor_centernet_pretraining_ct
  workpackage: *lidar_workpackage
  train_image: *lidar_train_image
  release_image: *lidar_release_image
  compute_target: *lidar_compute_target
  distribution_context: *lidar_distribution_context


- experiment_name: lidar_od_pointpillars_centernet_reflidar_cct_ct
  launcher: *lidar_ct_launcher
  usecase_package: *lidar_usecase_package
  pipeline: &lidar_pipeline_gt release_gt
  entry_model: object_detection
  model_config: pointpillars_centernet_reflidar_cct
  workpackage: *lidar_workpackage
  train_image: *lidar_train_image
  release_image: *lidar_release_image
  compute_target: *lidar_compute_target
  distribution_context: *lidar_distribution_context

# # ---------------------------------------------------- LIGHT_RNN -----------------------------------------------------------
- experiment_name: light_rnn
  launcher: PipelineLauncherCt
  usecase_package: xusecases.light_rnn.pipelines
  pipeline: rnn_pipeline
  entry_model: light_rnn
  workpackage: viper_lights
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    train: gpupool4coret4
    evaluate: gpupool4coret4
    convert: gpupool4coret4

- experiment_name: light_rnn_with_track_generation
  launcher: PipelineLauncherCt
  usecase_package: xusecases.light_rnn.pipelines
  pipeline: rnn_pipeline_with_track_gen
  entry_model: light_rnn
  workpackage: viper_lights
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    track_generation: gpupool4coret4
    update_data: cpupool8core300
    train: gpupool4coret4
    evaluate: gpupool4coret4
    convert: gpupool4coret4

# # ------------------------------------------ SITUATION INTERPRETATION ------------------------------------------------

- experiment_name: lcir_train
  launcher: PipelineLauncherCt
  usecase_package: xusecases.situation_interpretation.pipelines
  pipeline: train
  entry_model: lane_change
  workpackage: planning
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    train: gpupoolt4

- experiment_name: lcir_data_preprocess
  launcher: PipelineLauncherCt
  usecase_package: xusecases.situation_interpretation.pipelines
  pipeline: data_preprocess
  entry_model: lane_change
  workpackage: planning
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    data_preprocess: cpupool16core600

- experiment_name: lcir_data_preprocess_train
  launcher: PipelineLauncherCt
  usecase_package: xusecases.situation_interpretation.pipelines
  pipeline: data_preprocess_train
  entry_model: lane_change
  workpackage: planning
  train_image: xflow_train
  release_image: xflow_release
  compute_target:
    train: gpupoolt4
    data_preprocess: cpupool16core600



# # -------------------------------------------- TRAJECTORY PREDICTION ------------------------------------------------

- experiment_name: trajectory_prediction_train_eval_ct
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.trajectory_prediction.pipelines
  pipeline: train_eval
  entry_model: simpl
  workpackage: planning
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupoolt4
    evaluate: gpupoolt4
    publish: gpupoolt4
  model_config: simpl_av2

- experiment_name: trajectory_prediction_av2_release_ct
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.trajectory_prediction.pipelines
  owner_ids: 53bdd2a1-2b65-46a8-9ce1-5387c1dbd445;94edc75f-7700-481a-96bc-a8d99856b7c4;d2b9b7ae-f464-485b-ac0c-a78ca56b6462;5d1e3aba-3fe5-45c5-973b-ce6ba999dbf1
  pipeline: release
  entry_model: simpl
  workpackage: planning
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4t4
    evaluate: gpupoolt4
    convert: gpupoolt4
    publish: gpupoolt4
  model_config: release_simpl_av2

- experiment_name: trajectory_prediction_adaverse_release_ct
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.trajectory_prediction.pipelines
  owner_ids: 53bdd2a1-2b65-46a8-9ce1-5387c1dbd445;94edc75f-7700-481a-96bc-a8d99856b7c4;d2b9b7ae-f464-485b-ac0c-a78ca56b6462;5d1e3aba-3fe5-45c5-973b-ce6ba999dbf1
  pipeline: release
  entry_model: simpl
  workpackage: planning
  schedule:
    weekday: 5
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4t4
    evaluate: gpupoolt4
    convert: gpupoolt4
    publish: gpupoolt4
  model_config: release_simpl_adaverse

- experiment_name: trajectory_prediction_av2_adaverse_finetune_release_ct
  launcher: PipelineLauncherCt
  usecase_package: xtorch_usecases.trajectory_prediction.pipelines
  owner_ids: 53bdd2a1-2b65-46a8-9ce1-5387c1dbd445;94edc75f-7700-481a-96bc-a8d99856b7c4;d2b9b7ae-f464-485b-ac0c-a78ca56b6462;5d1e3aba-3fe5-45c5-973b-ce6ba999dbf1
  pipeline: av2_adaverse_chain
  entry_model: simpl
  workpackage: planning
  schedule:
    weekday: 5
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    train: gpupool4t4
    evaluate: gpupoolt4
    convert: gpupoolt4
    publish: cpupool2core
  model_config: release_simpl_av2


# # ---------------------------------------------------- OTHER ---------------------------------------------------------

# - experiment_name: ct_tutorial_ct
#   pipeline_builder_module: pipeline_builder.ct_tutorial_pipeline
#   entry_model: ct_tutorial # path to sub-package of usecase, currently not used here
#   workpackage: multi_task_learning
#   train_image: xflow_train
#   release_image: xflow_release
#   compute_target:
#     train: cpupool2core
#     evaluate: cpupool2core

- experiment_name: xtorch_example
  launcher: PipelineLauncher
  usecase_package: xtorch_usecases.example.pipelines
  pipeline: main
  workpackage: multi_task_learning
  entry_model: example
  distribution_context: tensorflow
  stage_max_runtime_days:
    main: 1
  train_image: xflow_release
  release_image: xflow_release
  compute_target:
    main: gpupoolh100
