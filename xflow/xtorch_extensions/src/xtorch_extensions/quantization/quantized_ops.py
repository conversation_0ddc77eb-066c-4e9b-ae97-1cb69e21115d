"""Support for OPs that AIMET doesn't recognize by default."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import copy
from collections.abc import Sequence
from typing import Any

import aimet_torch._base.nn.modules.custom as aimet_modules
import aimet_torch.model_preparer
import torch
from aimet_torch.nn import QuantizationMixin
from aimet_torch.v2.nn.true_quant import _DispatchMixin


class Stack(torch.nn.Module):
    """Stack module for a functional stack."""

    def __init__(self, axis: int = 0) -> None:
        """Init."""
        super().__init__()
        self._concat = aimet_modules.Concat(axis=axis)
        self._axis = axis

    def forward(self, *xs: torch.Tensor) -> torch.Tensor:
        """Forward-pass routine for stack op."""
        return self._concat(*(x.unsqueeze(dim=self._axis) for x in xs))


_stack_create_node = aimet_torch.model_preparer.concat_create_node


def _stack_create_module(node: torch.fx.Node) -> torch.nn.Module:
    """Create the replacement module.

    Args:
      node: Current node in the graph after which new node will be inserted

    Return:
      New module.
    """

    num_args = len(node.args)
    if num_args == 1 and "dim" not in node.kwargs:
        # Handle torch.cat being called with default parameter dim
        kwargs = node.kwargs
        module = Stack()
    else:
        axis = node.args[1] if num_args > 1 else node.kwargs["dim"]
        assert isinstance(axis, int)
        module = Stack(axis)
        kwargs = {"axis": axis}

    for key, value in kwargs.items():
        setattr(module, key, value)

    return module


class Max(torch.nn.Module):
    """Max following AIMETs style."""

    def __init__(self, dim: int | None = None, *, keepdim: bool = False) -> None:
        """Init."""
        super().__init__()
        self._dim = dim
        self._keepdim = keepdim

    def forward(self, x: torch.Tensor) -> tuple[torch.Tensor, torch.Tensor] | torch.Tensor:
        """Forward-pass."""
        if self._dim is not None:
            return torch.max(x, dim=self._dim, keepdim=self._keepdim)

        return torch.max(x)


@QuantizationMixin.implements(Max)
class QuantizedMax(_DispatchMixin, QuantizationMixin, Max):
    """Quantized Add."""

    __quant_init__ = QuantizationMixin.__unary__  # TODO: ask Michael # noqa: TD003
    _builtin_torch_fn = torch.max  # TODO: ask Michael # noqa: TD003


def _extract_module_args(
    node: torch.fx.Node, forward_arg_names: Sequence[str]
) -> tuple[tuple[Any, ...], tuple[Any, ...]]:
    """Extract the module arguments and forward arguments from the node."""
    module_kwargs = copy.deepcopy(node.kwargs)
    forward_kwargs = dict(zip(forward_arg_names, node.args))
    module_args = node.args[len(forward_arg_names) :]
    for name in forward_arg_names:
        if name in module_kwargs:
            forward_kwargs[name] = module_kwargs.pop(name)
    forward_args = tuple(forward_kwargs[name] for name in forward_arg_names)
    return (module_args, module_kwargs), forward_args


def _max_create_node(traced_model: torch.fx.GraphModule, module_name: str, node: torch.fx.Node) -> torch.fx.Node:
    """Create the node to be inserted in the graph model."""

    with traced_model.graph.inserting_after(node):
        _, forward_args = _extract_module_args(node, ("input",))
        new_node = traced_model.graph.call_module(module_name, args=forward_args)
        return new_node


def _max_create_module(node: torch.fx.Node) -> torch.nn.Module:
    """Create the replacement module."""

    (module_args, module_kwargs), _ = _extract_module_args(node, ("input",))
    module = Max(*module_args, **module_kwargs)
    return module


class GridSample(torch.nn.Module):
    """GridSample following AIMETs style."""

    def __init__(self, mode: str, padding_mode: str, *, align_corners: bool) -> None:
        """Init."""
        super().__init__()
        self.mode = mode
        self.padding_mode = padding_mode
        self.align_corners = align_corners

    def forward(self, inputs: torch.Tensor, grid: torch.Tensor) -> torch.Tensor:
        """Forward-pass."""
        return torch.nn.functional.grid_sample(inputs, grid, self.mode, self.padding_mode, self.align_corners)


@QuantizationMixin.implements(GridSample)
class QuantizedGridSample(_DispatchMixin, QuantizationMixin, GridSample):
    """Quantized GridSample."""

    __quant_init__ = QuantizationMixin.__binary__
    _builtin_torch_fn = torch.nn.functional.grid_sample


def _grid_sample_create_node(
    traced_model: torch.fx.GraphModule, module_name: str, node: torch.fx.Node
) -> torch.fx.Node:
    """Create the node to be inserted in the graph model."""

    with traced_model.graph.inserting_after(node):
        _, forward_args = _extract_module_args(node, ("input", "grid"))
        new_node = traced_model.graph.call_module(module_name, args=forward_args)
        return new_node


def _grid_sample_create_module(node: torch.fx.Node) -> torch.nn.Module:
    """Create the replacement module."""
    (module_args, module_kwargs), _ = _extract_module_args(node, ("input", "grid"))
    module = GridSample(*module_args, **module_kwargs)
    return module


class MaskedFill(torch.nn.Module):
    """MaskedFill following AIMETs style."""

    def __init__(self, value: float) -> None:
        """Init."""
        super().__init__()
        self.value = value

    def forward(self, inputs: torch.Tensor, mask: torch.Tensor) -> torch.Tensor:
        """Forward-pass."""
        return torch.masked_fill(inputs, mask, self.value)


@QuantizationMixin.implements(MaskedFill)
class QuantizedMaskedFill(_DispatchMixin, QuantizationMixin, MaskedFill):
    """Quantized MaskedFill."""

    __quant_init__ = QuantizationMixin.__binary__
    _builtin_torch_fn = torch.masked_fill


def _masked_fill_create_node(
    traced_model: torch.fx.GraphModule, module_name: str, node: torch.fx.Node
) -> torch.fx.Node:
    """Create the node to be inserted in the graph model."""
    with traced_model.graph.inserting_after(node):
        _, forward_args = _extract_module_args(node, ("input", "mask"))
        new_node = traced_model.graph.call_module(module_name, args=forward_args)
        return new_node


def _masked_fill_create_module(node: torch.fx.Node) -> torch.nn.Module:
    """Create the replacement module."""
    (module_args, module_kwargs), _ = _extract_module_args(node, ("input", "mask"))
    module = MaskedFill(*module_args, **module_kwargs)
    return module


def register_ops_replacements_with_preparer() -> None:
    """Register the custom OPs with AIMET's model preparer."""

    aimet_torch.model_preparer.functional_with_special_handling["stack"] = Stack
    aimet_torch.model_preparer.special_handler_functions["stack"] = {
        "node_fn": _stack_create_node,
        "module_fn": _stack_create_module,
    }

    aimet_torch.model_preparer.functional_with_special_handling["max"] = Max
    aimet_torch.model_preparer.special_handler_functions["max"] = {
        "node_fn": _max_create_node,
        "module_fn": _max_create_module,
    }

    aimet_torch.model_preparer.functional_with_special_handling["grid_sample"] = GridSample
    aimet_torch.model_preparer.special_handler_functions["grid_sample"] = {
        "node_fn": _grid_sample_create_node,
        "module_fn": _grid_sample_create_module,
    }

    aimet_torch.model_preparer.functional_with_special_handling["masked_fill"] = MaskedFill
    aimet_torch.model_preparer.special_handler_functions["masked_fill"] = {
        "node_fn": _masked_fill_create_node,
        "module_fn": _masked_fill_create_module,
    }
