"""Customized visualization callback."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import logging
from abc import ABC, abstractmethod
from collections.abc import Callable, Collection, Iterable, Mapping
from dataclasses import dataclass
from typing import Any, Final, TypeAlias

import matplotlib as mpl
import matplotlib.colors as colors_plt
import numpy as np
import numpy.typing as npt
import torch
from transforms3d.quaternions import mat2quat

from data_formats.radar.definitions import RADAR_FEATURES
from xcontract.data.definitions.inputs import InputDataId
from xcontract.data.definitions.usage import ValueKey
from xtorch.data.definitions.bev import MultiViewFrameData
from xtorch_extensions.visualization.converters import (
    CameraSetSource,
    QuaternionOrder,
    alliance_camera_set_converter,
    get_center_size_quaternion_boxes_converter,
    get_pointcloud_converter,
)
from xtorch_extensions.visualization.data import (
    Box3dMarkerTopic,
    CameraSetTopic,
    Converter,
    FrameTransform,
    FrameTransformTopic,
    LogTopic,
    PointcloudTopic,
    Source,
    Topic,
)

LOGGER = logging.getLogger(__name__)

ExtractedBatchData: TypeAlias = Collection[Source] | None
BatchExtractor: TypeAlias = Callable[[str, Any], ExtractedBatchData]
PredictionsExtractor: TypeAlias = Callable[[Mapping[str, Any]], ExtractedBatchData]

BASE_FRAME: Final = "base_link"
COLORS_MAPPING: Final[Any] = [colors_plt.to_rgb(c) for c in mpl.colormaps["tab20"].colors]  # pyright: ignore[reportAttributeAccessIssue]

# Hardcoded dimensions and position offset for ego vehicle
EGO_HEIGHT_AUDI_Q8_M: Final = 1.749
EGO_DIMENSIONS_AUDI_Q8_LENGTH_WIDTH_HEIGHT_M: Final = np.array([5.006, 1.995, EGO_HEIGHT_AUDI_Q8_M], dtype=np.float32)
EGO_WHEELBASE_AUDI_Q8_M: Final = 2.995
EGO_BOX_CENTER_POSITION_IN_VEHICLE_COORDS: Final = np.array(
    [EGO_WHEELBASE_AUDI_Q8_M / 2, 0.0, EGO_HEIGHT_AUDI_Q8_M / 2], dtype=np.float32
)


def gt_boxes_color_func(attribute_tuple: tuple[int]) -> tuple[float, float, float, float]:
    """Color function for ground truth boxes.

    Args:
        attribute_tuple: A tuple of len == 1 containing the label ID of the box.
    """
    label_id = attribute_tuple[0]
    return *COLORS_MAPPING[label_id], 0.4


def pred_boxes_color_func(attribute_tuple: tuple[int, float]) -> tuple[float, float, float, float]:
    """Color function for predicted boxes.

    Args:
        attribute_tuple: A tuple of len >= 2 containing the label ID and score of the box.
    """
    label_id, score, *_ = attribute_tuple
    return *COLORS_MAPPING[label_id], score * 0.9


GT_BOXES_TOPIC: Final = Box3dMarkerTopic(("label_id",), gt_boxes_color_func, name="/gt_boxes", frame_id=BASE_FRAME)
GT_BOXES_EXTENDED_TOPIC: Final = Box3dMarkerTopic(
    ("label_id", "inter_sequence_track_id", "track_id", "velocity_x", "velocity_y"),
    gt_boxes_color_func,
    name="/gt_boxes",
    frame_id=BASE_FRAME,
)
PRED_BOXES_TOPIC: Final = Box3dMarkerTopic(
    ("label_id", "score"), pred_boxes_color_func, name="/pred_boxes", frame_id=BASE_FRAME
)
PRED_TRACKING_BOXES_TOPIC: Final = Box3dMarkerTopic(
    ("label_id", "score", "track_id"),
    pred_boxes_color_func,
    name="/pred_boxes",
    frame_id=BASE_FRAME,
)
PRED_TRACKING_BOXES_EXTENDED_TOPIC: Final = Box3dMarkerTopic(
    ("label_id", "score", "track_id", "velocity_x", "velocity_y"),
    pred_boxes_color_func,
    name="/pred_boxes",
    frame_id=BASE_FRAME,
)


@dataclass(frozen=True)
class TopicPublisher(ABC):
    """Topic publisher for visualization.

    This class defines a publisher that retrieves data, transforms it, and makes it available on a particular topic for
    visualization.

    Attributes:
        topic: The topic to which the processed data will be published.
        converter: A function that transforms each extracted data sample into the desired format for publication.
            Defaults to an identity function (no transformation).
    """

    topic: Topic
    converter: Converter = lambda x: x


@dataclass(kw_only=True, frozen=True)
class BatchPublisher(TopicPublisher):
    """Topic publisher for batch data.

    Additional attribute:
        extractor: The function that extracts the relevant data from the batch data structure.
        key: The key to access the batch data.
    """

    extractor: BatchExtractor
    key: str

    def extract(self, batch: Any) -> ExtractedBatchData:
        """Get the output extractor.

        Note: It is guaranteed by the caller that the key is in the batch.
        """
        return self.extractor(self.key, batch)


@dataclass(kw_only=True, frozen=True)
class InputPublisher(BatchPublisher):
    """Topic publisher for input data."""


@dataclass(kw_only=True, frozen=True)
class GroundTruthPublisher(BatchPublisher):
    """Topic publisher for ground truth data."""


@dataclass(kw_only=True, frozen=True)
class PredictionsPublisher(TopicPublisher):
    """Topic publisher for predictions data.

    Additional attribute:
        extractor: The function that extracts the relevant data from the predictions data structure.
        task_id: The task ID to access the predictions data.
    """

    extractor: PredictionsExtractor
    task_id: str

    def extract(self, predictions: Any) -> ExtractedBatchData:
        """Get the output extractor.

        Note: It is guaranteed by the caller that the task_id is in the predictions.
        """
        return self.extractor(predictions[self.task_id])


def create_frames_id_publishers(publishers: Iterable[BatchPublisher]) -> list[BatchPublisher]:
    """Create frame IDs publishers for visualization.

    Args:
        publishers: The publishers to log frame IDs for.
        cls: The class of the publishers to create.
    """
    return [
        type(pub)(
            topic=LogTopic(name=f"/log/frame_id/{pub.topic.name.removeprefix('/')}", frame_id=pub.topic.frame_id),
            extractor=lambda key, batch: batch[key][ValueKey.IDENTIFIER],
            key=pub.key,
        )
        for pub in publishers
    ]


class Visualizable(ABC):
    """Base class for tasks that support customizable visualization publishers.

    This class provides a common interface for tasks that adds publishers to the visualization callback.
    """

    @property
    @abstractmethod
    def topic_publishers(self) -> Collection[TopicPublisher]:
        """Get the topic publishers of the task for visualization."""
        raise NotImplementedError


class AllianceTopics:
    """Alliance dataset topics."""

    @staticmethod
    def extract_pc_batch(input_data_id: str, batch: Any) -> npt.NDArray[Any]:
        """Extract point cloud batch for visualization.

        Args:
            input_data_id: The input data id, which is expected to be either REF_LIDAR or RADAR.
            batch: The batch data structure, which supposed to be a Mapping containing the given input_data_id as a key.
                The key checking is expected to be done before calling this function.

        Returns:
            The point cloud data as a numpy array, shape (batch_size, num_points, 4)
        """
        assert input_data_id in (InputDataId.REF_LIDAR, InputDataId.RADAR)
        pc = batch[input_data_id][ValueKey.DATA]
        points_xyz = pc.point_cloud.detach().cpu().numpy()
        if input_data_id == InputDataId.REF_LIDAR:
            return np.concatenate([points_xyz, pc.intensity.detach().cpu().numpy()], axis=-1)
        assert input_data_id == InputDataId.RADAR
        return np.concatenate([points_xyz, pc.point_features.detach().cpu().numpy()], axis=-1)

    @staticmethod
    def extract_camera_batch(input_data_id: str, batch: Any) -> list[CameraSetSource]:
        """Extract camera batch for visualization.

        Args:
            input_data_id: The input data id, which is expected to a valid camera input data id.
            batch: The batch data structure, which supposed to be a Mapping containing the given input_data_id as a key.
                The key checking is expected to be done before calling this function.

        Returns:
            The camera data as a list (len=batch_size) of CameraSetSource, where each CameraSetSource is a
            list (len=num_cameras) of tuples of images, extrinsics, intrinsics and camera_types
                image: The image Tensor, with shape (C, H, W). GPU tensors are expected for faster decoding.
                extrinsics: array of shape (4, 4).
                intrinsics: array of shape (5, ): [fu, fv, u0, v0, cut_angle].
                camera_type: the camera type as int.
        """
        multi_view_frame_data: MultiViewFrameData = batch[input_data_id][ValueKey.DATA]
        return [
            [
                (image, np.squeeze(extr), np.squeeze(intr), int(np.squeeze(cam_type)))  # one camera
                for image, extr, intr, cam_type in zip(image_tensor, extrinsics_array, intrinsics_array, types_array)
            ]  # all cameras
            for image_tensor, extrinsics_array, intrinsics_array, types_array in zip(
                multi_view_frame_data.images.detach(),
                multi_view_frame_data.extrinsics.detach().cpu().numpy(),
                multi_view_frame_data.intrinsic_params.detach().cpu().numpy(),
                multi_view_frame_data.camera_types.detach().cpu().numpy(),
            )  # all rows
        ]

    @staticmethod
    def extract_ego_vehicle_data(
        input_data_id: str, batch_data: Any
    ) -> tuple[tuple[npt.NDArray[np.float32], ...], ...]:
        """Extract ego vehicle data for visualization to draw the ego vehicle and append ego motion as attributes.

        Args:
            input_data_id: The input data id, which is expected to be ODOM.
            batch_data: The batch data structure, which is supposed to be a Mapping containing the given input_data_id
                        as a key.

        Returns:
            The ego vehicle data is a tuple containing for each batch row a tuple with
            [center position, dimensions, id quaternion (order WXYZ), velocity_x, velocity_y, velocity_z].
            The velocity is prepared as separate 1d attributes by intent.
        """
        if input_data_id != InputDataId.ODOM:
            msg = f"Expected input_data_id to be {InputDataId.ODOM}, got {input_data_id}."
            raise ValueError(msg)

        # Use fc1 to get batch size, since it is guaranteed to be present in the batch, whereas odometry is missing
        # currently for non-sequential datasets.
        batch_size = batch_data[InputDataId.CAMERA_CYLINDER][ValueKey.DATA].images.shape[0]

        # Initialize velocity as zeros
        ego_velocities = np.zeros((batch_size, 1, 3), dtype=np.float32)

        if input_data_id in batch_data and batch_data[input_data_id][ValueKey.VALID].all():
            ego_velocities = (
                batch_data[input_data_id][ValueKey.DATA].ego_velocities.to(torch.float32).detach().cpu().numpy()
            )
            if ego_velocities.shape != (batch_size, 1, 3):
                msg = f"Expected ego_velocities to have shape (batch_size, 1, 3), but got {ego_velocities.shape}."
                raise ValueError(msg)

        return tuple(
            zip(
                np.full((batch_size, 1, 3), EGO_BOX_CENTER_POSITION_IN_VEHICLE_COORDS),
                np.full((batch_size, 1, 3), EGO_DIMENSIONS_AUDI_Q8_LENGTH_WIDTH_HEIGHT_M),
                np.full((batch_size, 1, 4), np.array([1, 0, 0, 0], dtype=np.float32)),  # Quaternion identity
                ego_velocities[..., 0],
                ego_velocities[..., 1],
                ego_velocities[..., 2],
                strict=True,
            )
        )

    @staticmethod
    def extract_odometry_transforms(input_data_id: str, batch_data: Any) -> tuple[FrameTransform, ...]:
        """Extract odometry transforms of each batch row for visualization.

        Args:
            input_data_id: The input data id, which is expected to be ODOM.
            batch_data: The batch data structure, which is supposed to be a Mapping containing the given input_data_id
                        as a key.

        Returns:
            The odometry transforms as a tuple of FrameTransform for each batch row, where each FrameTransform is a
            tuple of (translation, quaternion), where translation is a 3-tuple (tx, ty, tz) and quaternion is a 4-tuple
            (qw, qx, qy, qz).
        """
        if input_data_id != InputDataId.ODOM:
            msg = f"Expected input_data_id to be {InputDataId.ODOM}, got {input_data_id}."
            raise ValueError(msg)

        # Use fc1 to get batch size, since it is guaranteed to be present in the batch, whereas odometry is missing
        # currently for non-sequential datasets.
        batch_size = batch_data[InputDataId.CAMERA_CYLINDER][ValueKey.DATA].images.shape[0]

        transforms: list[FrameTransform] = []
        if input_data_id in batch_data and batch_data[input_data_id][ValueKey.VALID].all():
            ego_poses = batch_data[input_data_id][ValueKey.DATA].ego_poses.to(torch.float32).detach().cpu().numpy()
            if ego_poses.shape == (batch_size, 1, 4, 4):
                for i in range(batch_size):
                    rotation_matrix = ego_poses[i, 0, :3, :3]
                    qw, qx, qy, qz = mat2quat(rotation_matrix)
                    tx, ty, tz = ego_poses[i, 0, :3, 3]
                    transform: FrameTransform = (tx, ty, tz), (qw, qx, qy, qz)
                    transforms.append(transform)
            else:
                msg = f"Expected ego_poses to have shape (batch_size, 1, 4, 4), but got {ego_poses.shape}."
                raise ValueError(msg)
        else:
            # If no odometry data is available, create empty transforms
            transforms = [(np.zeros(3, dtype=np.float32), (1.0, 0.0, 0.0, 0.0))] * batch_size

        return tuple(transforms)

    @staticmethod
    def topic_publishers() -> list[BatchPublisher]:
        """Get the input topics for visualization."""
        input_publishers = [
            InputPublisher(
                PointcloudTopic(fields=["x", "y", "z", "intensity"], name="/point_cloud_lidar", frame_id=BASE_FRAME),
                get_pointcloud_converter(),
                extractor=AllianceTopics.extract_pc_batch,
                key=InputDataId.REF_LIDAR,
            ),
            InputPublisher(
                PointcloudTopic(
                    fields=["x", "y", "z", *RADAR_FEATURES], name="/point_cloud_radar", frame_id=BASE_FRAME
                ),
                get_pointcloud_converter(tuple(index for index in range(3, 3 + len(RADAR_FEATURES)))),
                extractor=AllianceTopics.extract_pc_batch,
                key=InputDataId.RADAR,
            ),
            InputPublisher(
                CameraSetTopic(
                    name="/cam_tv",
                    frame_id=BASE_FRAME,
                    camera_names=("cam_tv_front", "cam_tv_left", "cam_tv_right", "cam_tv_rear"),
                ),
                alliance_camera_set_converter,
                extractor=AllianceTopics.extract_camera_batch,
                key=InputDataId.CAMERA_DEF_CYLINDER,
            ),
            InputPublisher(
                CameraSetTopic(name="/cam_fc1", frame_id=BASE_FRAME, camera_names=("cam_fc1",)),
                alliance_camera_set_converter,
                extractor=AllianceTopics.extract_camera_batch,
                key=InputDataId.CAMERA_CYLINDER,
            ),
            InputPublisher(
                CameraSetTopic(name="/cam_tv_left", frame_id=BASE_FRAME, camera_names=("cam_tv_left",)),
                alliance_camera_set_converter,
                extractor=AllianceTopics.extract_camera_batch,
                key=InputDataId.TV_LEFT,
            ),
            InputPublisher(
                CameraSetTopic(name="/cam_tv_right", frame_id=BASE_FRAME, camera_names=("cam_tv_right",)),
                alliance_camera_set_converter,
                extractor=AllianceTopics.extract_camera_batch,
                key=InputDataId.TV_RIGHT,
            ),
            InputPublisher(
                CameraSetTopic(name="/cam_tv_front", frame_id=BASE_FRAME, camera_names=("cam_tv_front",)),
                alliance_camera_set_converter,
                extractor=AllianceTopics.extract_camera_batch,
                key=InputDataId.TV_FRONT,
            ),
            InputPublisher(
                CameraSetTopic(name="/cam_tv_rear", frame_id=BASE_FRAME, camera_names=("cam_tv_rear",)),
                alliance_camera_set_converter,
                extractor=AllianceTopics.extract_camera_batch,
                key=InputDataId.TV_REAR,
            ),
            InputPublisher(
                Box3dMarkerTopic(
                    ("velocity_x", "velocity_y", "velocity_z"),
                    lambda _: (0.0, 0.0, 0.0, 0.8),
                    name="/ego_vehicle/3d_box",
                    frame_id=BASE_FRAME,
                ),
                get_center_size_quaternion_boxes_converter(QuaternionOrder.WXYZ),
                extractor=AllianceTopics.extract_ego_vehicle_data,
                key=InputDataId.ODOM,
            ),
            InputPublisher(
                FrameTransformTopic(name="/ego_vehicle/tf_odometry", frame_id=BASE_FRAME, parent_frame_id=BASE_FRAME),
                lambda x: x,
                extractor=AllianceTopics.extract_odometry_transforms,
                key=InputDataId.ODOM,
            ),
        ]
        return input_publishers + create_frames_id_publishers(input_publishers)


def get_input_publishers(dataset_name: str) -> list[BatchPublisher] | None:
    """Get the input topics for visualization."""
    supported_datasets: Final = ("alliance", "occupancy", "ground_truth", "loomy", "cassandra_scene")
    if all(dset_name in supported_datasets for dset_name in dataset_name.split(",")):
        return AllianceTopics.topic_publishers()
    msg = f"Currently only {supported_datasets} datasets are supported for visualization, but got {dataset_name}."
    LOGGER.warning(msg)
    return None
