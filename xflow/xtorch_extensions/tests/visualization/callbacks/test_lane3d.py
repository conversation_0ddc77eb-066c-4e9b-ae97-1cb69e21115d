"""Pytest for Visualization Callback for 3D Lanes using OpenCV."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from functools import partial
from pathlib import Path
from unittest.mock import patch

import numpy as np
import pytest
import torch
from numpy.typing import NDArray

from xcontract.camera_models.definitions import CameraModelType
from xcontract.data.definitions.image import HW, AvailableImageTypes
from xcontract.data.definitions.inputs import InputImageView
from xtorch.camera_models.camera_model import CameraParameters
from xtorch.training import Stage
from xtorch_extensions.visualization.callbacks.lane3d import (
    Lane3DVisualizationCallback,
    PreparedLaneData,
    VisuLane3D,
)
from xtorch_usecases.single_camera.tasks.sparse_lanecpp.callbacks import (
    extract_visu_lane3d_from_gt,
    extract_visu_lane3d_from_preds,
)

_PRED_LANE3D_EXTRACTION_FUNCTION = partial(
    extract_visu_lane3d_from_preds,
    get_preds=lambda outputs, task_identifier: outputs["torch"][task_identifier],
)

_CAMERA_PARAMETERS_ID = "dummy_camera_parameter_id"


@pytest.mark.parametrize(
    ("lanes", "cam_params", "confidence", "category", "vis"),
    [
        (
            [np.array([[0, 1], [2, 3], [4, 5]], dtype=np.float32)],
            CameraParameters(
                principal_point=torch.tensor([528.0, 262.97], dtype=torch.float32),
                focal_length=torch.tensor([1183.09, 1183.09], dtype=torch.float32),
                extrinsics=torch.eye(4, dtype=torch.float32),
                camera_model_type=torch.ones(1, dtype=torch.int32) * CameraModelType.PINHOLE,
            ),
            [0.9],
            [1],
            [np.array([True, False], dtype=bool)],
        ),
    ],
)
def test_prepare_lane_data(
    lanes: NDArray[np.float32],
    cam_params: CameraParameters,
    confidence: list[float],
    category: list[int],
    vis: list[NDArray[np.bool_]],
) -> None:
    """Test the `_prepare_lane_data` method of `Lane3DVisualizationCallback`."""

    # GIVEN mocked data
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )
    # WHEN calling the method
    result = callback._prepare_lane_data([lanes], cam_params, confidence, category, vis)  # noqa: SLF001

    # THEN the result is an instance of LaneData
    assert isinstance(result, PreparedLaneData)

    # THEN the principal point and focal length are numpy arrays
    assert isinstance(result.principal_point, np.ndarray)
    assert isinstance(result.focal_length, np.ndarray)


def test_plot_gts_on_image_canvas() -> None:
    """Test the `_plot_gts_on_image_canvas` method of `Lane3DVisualizationCallback`."""

    # GIVEN mocked data
    image = torch.zeros((3, 512, 1024), dtype=torch.uint8)  # Mock image tensor
    ground_truth = VisuLane3D(
        lanes_category=[0, 1],
        lanes_dense_points=[
            np.array([[0, 1, 1], [2, 3, 1], [4, 5, 1]], dtype=np.float32),
            np.array([[6, 7, 1], [8, 9, 1], [10, 11, 1]], dtype=np.float32),
        ],
        lanes_vis=[
            np.array([True, False, True], dtype=bool),
            np.array([False, True, True], dtype=bool),
        ],
    )
    cam_params = CameraParameters(
        principal_point=torch.tensor([528.0, 262.97], dtype=torch.float32),
        focal_length=torch.tensor([1183.09, 1183.09], dtype=torch.float32),
        extrinsics=torch.eye(4, dtype=torch.float32),
        camera_model_type=torch.ones(1, dtype=torch.int32) * CameraModelType.PINHOLE,
    )

    # Initialize the callback
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        line_width=3,
        lane_transparency=0.6,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )

    # WHEN calling the method
    result_image = callback._plot_gts_on_image_canvas((image, ground_truth, cam_params, ""))  # noqa: SLF001

    # THEN the output is a tensor with the same shape as the input image
    assert isinstance(result_image, torch.Tensor)


def test_plot_preds_on_image_canvas() -> None:
    """Test the `_plot_preds_on_image_canvas` method of `Lane3DVisualizationCallback`."""

    # GIVEN Mock input data
    image = torch.zeros((3, 512, 1024), dtype=torch.uint8)  # Mock image tensor
    predictions = VisuLane3D(
        lanes_category=[
            np.array([0.1, 0.9], dtype=np.float32),
            np.array([0.2, 0.8], dtype=np.float32),
        ],  # Mock probabilities for categories
        lanes_dense_points=[
            np.array([[0, 1, 1], [2, 3, 1], [4, 5, 1]], dtype=np.float32),
            np.array([[6, 7, 1], [8, 9, 1], [10, 11, 1]], dtype=np.float32),
        ],
        lanes_vis=[
            np.array([True, False, True], dtype=bool),
            np.array([False, True, True], dtype=bool),
        ],
    )
    cam_params = CameraParameters(
        principal_point=torch.tensor([528.0, 262.97], dtype=torch.float32),
        focal_length=torch.tensor([1183.09, 1183.09], dtype=torch.float32),
        extrinsics=torch.eye(4, dtype=torch.float32),
        camera_model_type=torch.ones(1, dtype=torch.int32) * CameraModelType.PINHOLE,
    )

    # Initialize the callback
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        line_width=3,
        lane_transparency=0.6,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )

    # WHEN calling the method
    result_image = callback._plot_preds_on_image_canvas((image, predictions, cam_params, ""))  # noqa: SLF001

    # THEN the output is a tensor with the same shape as the input image
    assert isinstance(result_image, torch.Tensor)


def test_draw_lanes_3d() -> None:
    """Test the `_draw_lanes_3d` method of `Lane3DVisualizationCallback`."""
    # GIVEN mock input data
    image = torch.zeros((3, 512, 1024), dtype=torch.uint8)  # Mock image tensor
    bev_height_width = HW(height=600, width=400)
    side_view_height_width = HW(height=300, width=600)
    horizontal_gap = 30
    draw_inputs = {
        "lanes": [
            np.array([[[0, 1, 2], [3, 4, 5], [6, 7, 8]]], dtype=np.float32),
            np.array([[[9, 10, 11], [12, 13, 14], [15, 16, 17]]], dtype=np.float32),
        ],
        "confidences": [0.9, 0.8],
        "categories": [0, 1],
        "vis": [
            np.array([[True, False, True]], dtype=bool),
            np.array([[False, True, True]], dtype=bool),
        ],
        "principal_point": np.array([528.0, 262.97], dtype=np.float32),
        "focal_length": np.array([1183.09, 1183.09], dtype=np.float32),
        "extrinsics": np.eye(4, dtype=np.float32),
        "is_pinhole_model": np.array([True], dtype=bool),
    }

    default_color = (0, 255, 0)  # Green
    line_width = 3
    line_style = "solid"
    lane_transparency = 0.6

    # Initialize the callback
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        line_width=line_width,
        lane_transparency=lane_transparency,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )

    # WHEN calling the method with the mock data
    result_image = callback._draw_lanes_3d(  # noqa: SLF001
        image,
        PreparedLaneData(
            lanes=draw_inputs["lanes"],
            confidences=draw_inputs["confidences"],
            categories=draw_inputs["categories"],
            vis=draw_inputs["vis"],
            principal_point=draw_inputs["principal_point"],
            focal_length=draw_inputs["focal_length"],
            extrinsics=draw_inputs["extrinsics"],
            is_pinhole_model=draw_inputs["is_pinhole_model"],
        ),
        default_color=default_color,
        line_width=line_width,
        line_style=line_style,
        line_transparency=lane_transparency,
        bev_height_width=bev_height_width,
        side_view_height_width=side_view_height_width,
        horizontal_gap=horizontal_gap,
    )

    # THEN the output is a tensor with expected shape and type
    expected_height = max([image.shape[1], bev_height_width.height, side_view_height_width.height])
    expected_width = (
        image.shape[2]
        + bev_height_width.width
        + side_view_height_width.width
        + horizontal_gap * 2  # left and right margin
        + horizontal_gap * 2  # gaps between 3 images = 2 gaps
    )

    result_height = result_image.shape[1]
    result_width = result_image.shape[2]

    assert isinstance(result_image, torch.Tensor)
    assert result_height == expected_height
    assert result_width == expected_width


def test_convert_tensor_image_to_numpy() -> None:
    """Test the `_convert_tensor_image_to_numpy` method of `Lane3DVisualizationCallback`."""

    # GIVEN mock input tensor image
    tensor_image = torch.randint(0, 256, (3, 512, 1024), dtype=torch.uint8)  # Shape [C, H, W]

    # Initialize the callback
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        line_width=3,
        lane_transparency=0.6,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )

    # WHEN calling the function
    numpy_image = callback._convert_tensor_image_to_numpy(tensor_image)  # noqa: SLF001

    # THEN the output is a numpy array with the expected shape and type
    assert isinstance(numpy_image, np.ndarray)
    assert numpy_image.shape == (512, 1024, 3)  # Shape [H, W, C]
    assert numpy_image.dtype == np.uint8


def test_convert_numpy_image_to_tensor() -> None:
    """Test the `_convert_numpy_image_to_tensor` method of `Lane3DVisualizationCallback`."""

    # GIVEN mock input numpy image
    rng = np.random.default_rng()
    numpy_image = rng.integers(0, 256, (512, 1024, 3), dtype=np.uint8)  # Shape [H, W, C]

    # Initialize the callback
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        line_width=3,
        lane_transparency=0.6,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )

    # WHEN calling the function
    tensor_image = callback._convert_numpy_image_to_tensor(numpy_image)  # noqa: SLF001

    # THEN the output is a tensor with the expected shape and type
    assert isinstance(tensor_image, torch.Tensor)
    assert tensor_image.shape == (3, 512, 1024)  # Shape [C, H, W]
    assert tensor_image.dtype == torch.uint8


def test_tensor_to_numpy_and_back() -> None:
    """Test the conversion of a tensor image to numpy and back to tensor."""

    # GIVEN mock input tensor image
    tensor_image = torch.randint(0, 256, (3, 512, 1024), dtype=torch.uint8)  # Shape [C, H, W]

    # Initialize the callback
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        line_width=3,
        lane_transparency=0.6,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )

    # WHEN calling the conversion functions
    numpy_image = callback._convert_tensor_image_to_numpy(tensor_image)  # noqa: SLF001
    converted_tensor_image = callback._convert_numpy_image_to_tensor(numpy_image)  # noqa: SLF001

    # THEN the converted tensor image is equal to the original tensor image
    assert torch.equal(tensor_image, converted_tensor_image)


def test_start_writer(tmp_path: Path) -> None:
    """Test the `_start_writer` method of `Lane3DVisualizationCallback`."""

    # GIVEN mock input data
    filepath_stem = tmp_path / "visualization"
    epoch = 1
    batch_idx = 2
    idx = 3
    image_name = "test_image"

    # Initialize the callback
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder=tmp_path,
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        line_width=3,
        lane_transparency=0.6,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
    )

    # Patch Path.mkdir and self._vis_manager.start
    with patch("pathlib.Path.mkdir") as mock_mkdir, patch.object(callback._vis_manager, "start") as mock_start:  # noqa: SLF001
        # WHEN calling the function
        result_path = callback._start_writer(filepath_stem, epoch, batch_idx, idx, image_name)  # noqa: SLF001

        # THEN the image_name is in the result path, and mkdir was called
        assert image_name in result_path
        mock_mkdir.assert_called_once()  # Ensure mkdir was called
        mock_start.assert_called_once()  # Ensure visualization manager was started


@pytest.mark.parametrize(
    (
        "stage",
        "max_num_images_per_epoch",
        "num_logged_images_epoch",
        "image_logging_frequency",
        "batch_idx",
        "expected",
    ),
    [
        # VALIDATE stage, not over image limit
        ("VALIDATE", 10, 5, 1, 0, False),
        # VALIDATE stage, over image limit
        ("VALIDATE", 5, 5, 1, 0, True),
        # TRAIN stage, frequency matches, not over image limit
        ("TRAIN", 10, 5, 2, 4, False),
        # TRAIN stage, frequency does not match, not over image limit
        ("TRAIN", 10, 5, 2, 3, True),
        # TRAIN stage, frequency does not match and over image limit
        ("TRAIN", 5, 5, 2, 3, True),
    ],
)
def test_should_skip_image_logging(
    stage: str,
    max_num_images_per_epoch: int,
    num_logged_images_epoch: int,
    image_logging_frequency: int,
    batch_idx: int,
    *,
    expected: bool,
) -> None:
    """Test the _should_skip_image_logging method for various scenarios."""

    # GIVEN mock input data
    callback = Lane3DVisualizationCallback(
        image_shape=HW(512, 1024),
        output_folder="",
        adjust_image_fn=lambda x: x,
        gt_lane3d_extraction_fn=extract_visu_lane3d_from_gt,
        pred_lane3d_extraction_fn=_PRED_LANE3D_EXTRACTION_FUNCTION,
        task_identifier="test_task",
        loader_image_type=AvailableImageTypes.YUV444.value,
        loader_image_view=InputImageView.SINGLE_VIEW,
        camera_parameters_id=_CAMERA_PARAMETERS_ID,
        image_logging_frequency=image_logging_frequency,
        max_num_images_per_epoch=max_num_images_per_epoch,
    )
    stage_enum = getattr(Stage, stage)
    callback._num_logged_images_epoch = num_logged_images_epoch  # noqa: SLF001

    # WHEN calling the method
    result = callback._should_skip_image_logging(stage_enum, batch_idx)  # noqa: SLF001

    # THEN the result matches the expected outcome
    assert result is expected
