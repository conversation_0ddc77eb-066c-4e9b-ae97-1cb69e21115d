"""Tests for the quantized (AIMET compatible) ops."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


from xtorch_extensions.quantization.quantized_ops import register_ops_replacements_with_preparer


def test_register_ops_replacements_with_preparer() -> None:
    """Test the register_ops_replacements_with_preparer function."""

    # WHEN calling the function
    register_ops_replacements_with_preparer()

    # THEN no exceptions should be raised and the function should complete successfully
    assert True  # Replace with actual assertions as needed
