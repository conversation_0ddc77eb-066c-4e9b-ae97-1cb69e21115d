#!/usr/bin/env python3
# /// script
# dependencies = [
#   "click",
#   "packaging>=21.3",
#   "requests>=2.25.1",
#   "pathspec",
#   "pyyaml",
#   "tomli",
#   "uv>=0.6.14",
#   "urllib3>=1.26.5",
# ]
# [tool.uv]
# find-links = []
# ///

"""Setup script for xflow users."""

from __future__ import annotations

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


import os
import shutil
import subprocess
import sys
from pathlib import Path
from tempfile import NamedTemporaryFile

REQUIRED_PYTHON_VERSION = "3.10"
UV_VERSION = "0.7.18"


def _install_uv() -> None:
    if input().lower() == "n":
        sys.exit(1)
    subprocess.check_call(f"curl -LsSf https://astral.sh/uv/{UV_VERSION}/install.sh | sh", shell=True)
    _inject_local_path()


def _inject_local_path() -> None:
    local_bin = Path("~/.local/bin").expanduser().as_posix()
    paths = os.getenv("PATH", "").split(":")
    if not paths or paths[0] != local_bin:
        os.environ["PATH"] = ":".join([local_bin, *paths])


def _get_uv_version() -> str:
    return subprocess.check_output(["uv", "--version"], text=True).split(" ")[1].strip()


if "UV_RUN_RECURSION_DEPTH" not in os.environ:
    if shutil.which("uv") is None:
        _inject_local_path()

        if shutil.which("uv") is None:
            print("uv is not installed. Shall I install it for you? [Y/n]", end="")
            _install_uv()

    uv_version_str = _get_uv_version()
    uv_version = tuple(map(int, uv_version_str.split(".")))

    if uv_version < tuple(map(int, UV_VERSION.split("."))):
        _inject_local_path()
        uv_version_str = _get_uv_version()
        uv_version = tuple(map(int, uv_version_str.split(".")))

        if uv_version < tuple(map(int, UV_VERSION.split("."))):
            print(f"uv {uv_version_str} is too old. Shall I update it to {UV_VERSION} for you? [Y/n]", end="")
            _install_uv()

    with NamedTemporaryFile(mode="w", prefix="uv", suffix=".toml", delete=False) as f:
        # remove find-links from uv environment so that we do not need a .netrc file to create the env for set-me-up
        f.write("""[pip]
find-links = []
""")
        if "VIRTUAL_ENV" in os.environ:
            os.environ["VIRTUAL_ENV_SET_ME_UP"] = os.environ["VIRTUAL_ENV"]
        os.environ["UV_PYTHON"] = REQUIRED_PYTHON_VERSION

        os.execvp(  # noqa: S606
            "uv",
            ["uv", "--quiet", "run", "--config-file", f.name, "--no-project", "--no-env-file", "--script", __file__]
            + sys.argv[1:],
        )

# flake8: noqa: E402

import getpass
import hashlib
import json
import netrc
import os
import re
import shutil
import sys
import time
from collections import defaultdict
from collections.abc import Generator, Sequence
from dataclasses import dataclass, fields
from enum import Enum, auto
from functools import lru_cache
from subprocess import call, check_call, check_output, run
from typing import Any
from urllib.parse import urlparse

import click
import requests
import tomli
from packaging.requirements import Requirement
from packaging.specifiers import SpecifierSet
from packaging.tags import sys_tags
from packaging.utils import InvalidWheelFilename, canonicalize_name, parse_wheel_filename
from packaging.version import Version
from pathspec import PathSpec
from requests.adapters import HTTPAdapter
from urllib3 import Retry

USER_NAME = check_output("whoami").decode().rstrip().lower()
XFLOW_WORKSPACE = Path(__file__).parent.resolve()
XFLOW_NAME = XFLOW_WORKSPACE.name.lower()  # Use folder name to allow running multiple xFlow containers at the same time
XFLOW_REPO_PARENT_DIR = (  # Parent directory of main xflow repo. Important to differentiate when using worktrees.
    Path(subprocess.check_output(["git", "rev-parse", "--git-common-dir"], text=True).strip()).resolve().parents[1]
)

LFS_CACHE_PATH = "~/.pace/cache/lfs"
DOCKER_DEV_IMAGE_DIR = XFLOW_WORKSPACE / ".build" / "user-image"
DOCKER_COMPOSE = [
    "docker",
    "compose",
    "--file",
    f"{DOCKER_DEV_IMAGE_DIR}/docker-compose.base.yaml",
]

# Add user & headless docker-compose files, if they exist
if (DOCKER_DEV_IMAGE_DIR / "docker-compose.user.yaml").is_file():
    DOCKER_COMPOSE += [
        "--file",
        f"{DOCKER_DEV_IMAGE_DIR}/docker-compose.user.yaml",
    ]

if (DOCKER_DEV_IMAGE_DIR / "docker-compose.headless.yaml").is_file():
    DOCKER_COMPOSE += [
        "--file",
        f"{DOCKER_DEV_IMAGE_DIR}/docker-compose.headless.yaml",
    ]

DOCKER_COMPOSE_RUN = [*DOCKER_COMPOSE, "run", "--name", f"{XFLOW_NAME}-dev-{USER_NAME}", "--rm", "xflow_dev"]

DOCKER_COMPOSE_RUN_DETACHED = [
    *DOCKER_COMPOSE,
    "run",
    "-d",
    "--name",
    f"{XFLOW_NAME}-dev-{USER_NAME}",
    "--rm",
    "xflow_dev",
]

ARTIFACTORY_HOST = "jfrog.ad-alliance.biz"


class Environment(Enum):
    """Defines the different requirement environments."""

    TRAINING = auto()
    RELEASE = auto()
    DEV = auto()
    NVIDIA = auto()
    XFLOW_INSTALL = auto()


@dataclass
class Versions:
    """Collects the versions of the installed packages."""

    ubuntu_version: str
    cuda_full_version: str
    cudnn_version: str
    nccl_version: str
    tensorrt_version: str
    tensorflow_version: str
    torch_version: str
    qnn_version: str

    @property
    def cuda_version(self) -> str:
        """Return the CUDA version as major.minor."""
        return self.cuda_full_version.rsplit(".", maxsplit=1)[0]


DEFAULT_VERSIONS = Versions(
    ubuntu_version="22.04",
    cuda_full_version="12.3.2",
    cudnn_version="8.9.7",
    nccl_version="2.21.5",
    tensorrt_version="8.6.1",
    tensorflow_version="2.18.1",
    torch_version="2.7.0",
    qnn_version="2.32.2",
)


NON_TRAIN_KEYS = ["tensorrt_version", "qnn_version"]


class ArtifactoryCredentials:
    """Represents an artifactory user + apikey.

    The class can be initialized and updated with new information, e.g.
    credentials obtained from config files. When credentials are actually requested but not available, the user is
    prompted.

    NOTE: 'azureuser' - as is used on compute instances - is ignored and considered as None since it is not a valid
    username.
    """

    HELP_MESSAGE_ARTIFACTORY_SETUP = (
        "For guidance on how to set up artifactory credentials, check out "
        "https://pace-docs.azurewebsites.net/pace/main/docs/developer/development_environment/work_with_artifactory.html."
    )

    def __init__(self, username: str | None, apikey: str | None) -> None:
        """Initialize the ArtifactoryCredentials."""
        self._username = ArtifactoryCredentials._filter_invalid_username(username)
        self._apikey = apikey

        if self._apikey is None:
            af_netrc = _get_netrc_credentials_for_hostname(ARTIFACTORY_HOST)
            if af_netrc is not None:
                self._username, self._apikey = af_netrc

    @staticmethod
    def _filter_invalid_username(username: str | None) -> str | None:
        return username if username != "azureuser" else None

    @property
    def username(self) -> str:
        """Return the artifactory username."""
        if not self._username:
            self._username = click.prompt(f"artifactory-user ({self.HELP_MESSAGE_ARTIFACTORY_SETUP})")
        assert self._username is not None
        return self._username

    @property
    def apikey(self) -> str:
        """Return the artifactory apikey."""
        if not self._apikey:
            self._apikey = click.prompt(f"artifactory-apikey ({self.HELP_MESSAGE_ARTIFACTORY_SETUP})", hide_input=True)
        assert self._apikey is not None
        return self._apikey

    def verify(self, message: str = "Could not connect to artifactory with supplied credentials.") -> None:
        """Verify the artifactory credentials."""
        _verify_artifactory_apikey(self.username, self.apikey, message)

    def setdefault_username(self, username: str | None) -> None:
        """Set the username if not already set."""
        self._username = self._username or ArtifactoryCredentials._filter_invalid_username(username)

    def setdefault_apikey(self, apikey: str | None) -> None:
        """Set the apikey if not already set."""
        self._apikey = self._apikey or apikey


def get_lsb_release() -> str:
    """Get the Linux release.

    Get the Linux release such as 18.04. The `lsb_release` python package is only available for the ubuntu vendored
    python which may not be available on all azure configurations.
    """
    return check_output(["lsb_release", "-rs"], text=True).rstrip()


def get_lsb_codename() -> str:
    """Get the Linux code name.

    Get the Linux code name such as focal. The `lsb_release` python package is only available for the ubuntu vendored
    python which may not be available on all azure configurations.
    """
    return check_output(["lsb_release", "-cs"], text=True).rstrip()


def wrap_check_call(*popenargs: Any, activate_script: Path | None = None, debug: bool = False, **kwargs: Any) -> int:
    """Run command with arguments using check_call with optionally sourcing a venv.

    Args:
        *popenargs: arguments passed to check_call
        activate_script: Optional venv activate script. Defaults to None.
        debug: If true, print shell commands before execution. Defaults to False.
        **kwargs: kwargs passed to check_call

    Returns:
        The exit code of the command.

    """
    lpopenargs = list(popenargs)
    cmd = lpopenargs.pop(0)
    if activate_script:
        cmd = f". {activate_script} && {cmd}"
    if debug:
        if isinstance(cmd, list):
            click.secho(subprocess.list2cmdline(cmd), fg="blue")
        else:
            click.secho(cmd, fg="blue")
    return check_call(cmd, *lpopenargs, **kwargs)


def wrap_check_output(
    *popenargs: Any, activate_script: Path | None = None, debug: bool = False, **kwargs: Any
) -> str | bytes:
    """Run command with arguments using check_output with optionally sourcing a venv.

    Args:
        *popenargs: arguments passed to check_output
        activate_script: Optional venv activate script. Defaults to None.
        debug: If true, print shell commands before execution. Defaults to False.
        **kwargs: kwargs passed to check_output

    Returns:
        The output of the command.

    """
    lpopenargs = list(popenargs)
    cmd = lpopenargs.pop(0)
    if activate_script:
        cmd = f". {activate_script} && {cmd}"
    if debug:
        if isinstance(cmd, list):
            click.secho(subprocess.list2cmdline(cmd), fg="blue")
        else:
            click.secho(cmd, fg="blue")
    return check_output(cmd, *lpopenargs, **kwargs)


@lru_cache
def _find_installed_deb_packages() -> set[str]:
    """List installed debian packages.

    Returns:
        Set of package names.

    """
    installed_deb_packages = check_output(["apt", "list", "-i"]).decode().rstrip()
    return set(re.findall("^(.+?)/.+[installed]", installed_deb_packages, re.MULTILINE))


def _install_dotnet_sdk(artifactory_credentials: ArtifactoryCredentials) -> None:
    """Install the dotnet sdk if not already installed.

    AML SDK v1 requires dotnet 3.1 which is quite old and not available for jammy from official sources, so we
    install it from the artifactory.
    """
    required_deb_packages = {
        "liblttng-ust0",
        "dotnet-runtime-3.1",
    }
    missing_deb_packages = required_deb_packages - _find_installed_deb_packages()

    if not missing_deb_packages:
        return

    click.secho("Setting up ada debian sources...", fg="green", bold=True)

    netrc_config = (
        f"machine {ARTIFACTORY_HOST}\\n"
        f"login {artifactory_credentials.username}\\n"
        f"password {artifactory_credentials.apikey}"
    )
    apt_auth_file = "/etc/apt/auth.conf.d/80-ada-archive.conf"
    check_call(f"printf '{netrc_config}' | sudo sponge {apt_auth_file}", shell=True)

    keyring_file = "/usr/share/keyrings/ada-archive-keyring.gpg"
    check_call(
        (
            f"curl --netrc-file {apt_auth_file}"
            f" -s https://{ARTIFACTORY_HOST}/artifactory/api/security/keypair/shared-prod-gpg-key/public"
            f" | gpg --dearmor | sudo sponge {keyring_file}"
        ),
        shell=True,
    )

    apt_source_list = (
        f"deb [signed-by={keyring_file}] https://{ARTIFACTORY_HOST}/artifactory/shared-debian-dev-local"
        f" {get_lsb_codename()} vdeep"
    )
    command = f"echo '{apt_source_list}' | sudo tee /etc/apt/sources.list.d/vdeep.list"
    check_call(command, shell=True)

    check_call("sudo apt update", shell=True)

    _install_required_deb_packages(missing_deb_packages)


def _install_required_deb_packages(required_deb_packages: set[str]) -> None:
    """Install the required debian packages skipping the already installed ones.

    Args:
        required_deb_packages: Set of packages to install.

    """
    missing_deb_packages = {
        req.split("=", maxsplit=1)[0] for req in required_deb_packages
    } - _find_installed_deb_packages()

    if any(missing_deb_packages):
        msg = ", ".join(sorted(missing_deb_packages))
        click.secho(f"Installing missing packages: {msg}", fg="green", bold=True)
        check_call(["sudo", "apt", "install", *list(missing_deb_packages)])
        check_call(["sudo", "apt", "autoremove", "-y"])


def _setup_git_lfs() -> None:
    """Install git-lfs if necessary, configure lfs in the xflow repo, and configure lfs cache."""
    click.secho("Setting up Git LFS...", fg="green", bold=True)

    if not shutil.which("git-lfs"):
        click.secho("Installing git lfs...", fg="green", bold=True)
        check_call(
            "curl -s https://packagecloud.io/install/repositories/github/git-lfs/script.deb.sh | sudo bash",
            shell=True,
        )
        check_call("sudo apt-get install -y git-lfs", shell=True)

    lfs_install = ["git", "lfs", "install", "--local", "--force"]
    check_call(lfs_install, cwd=XFLOW_WORKSPACE)

    lfs_path = Path(LFS_CACHE_PATH).expanduser()
    lfs_path.mkdir(exist_ok=True, parents=True)

    lfs_config = ["git", "config", "lfs.storage", str(lfs_path)]
    check_call(lfs_config, cwd=XFLOW_WORKSPACE)


def _resolve_wheels(
    package_names: list[str],
    find_links_path: str,
    debug: bool = False,  # noqa: FBT001, FBT002
) -> list[str]:
    """Resolve wheel urls for the given packages.

    Args:
        package_names: The list of package names to download wheels for.
        find_links_path: The find_links to use when downloading.
        debug: If true, print the full command before execution. Defaults to False.

    Returns:
        A list of resolved wheel urls.

    """
    resolved_wheels: list[str] = []

    if not package_names:
        return resolved_wheels

    response = _wrap_requests_get(find_links_path, timeout=5)

    response.raise_for_status()

    found_wheels_raw = re.findall(r'<a href="([^"]+)">', response.text)
    found_wheels = defaultdict(list)

    for whl in found_wheels_raw:
        try:
            name, ver, build, tags = parse_wheel_filename(whl)

            found_wheels[canonicalize_name(name)].append({"ver": ver, "build": build, "tags": tags, "whl": whl})
        except InvalidWheelFilename:  # noqa: PERF203
            continue

    for package_name in package_names:
        if package_name.startswith("git+https"):
            resolved_wheels.append(package_name)
            continue
        req = Requirement(package_name)
        req_name = canonicalize_name(req.name)

        if req_name not in found_wheels:
            click.secho(
                f"Could not find any wheel for {req.name} at {find_links_path}. "  # noqa: ISC003
                + f"Available wheels are: {sorted(found_wheels.keys())}",
                fg="red",
            )
            sys.exit(1)

        found_wheel = _find_matching_wheel(found_wheels[req_name], req)

        if found_wheel is None:
            available_versions = sorted(f"{whl['ver']} @ {whl['tags']}" for whl in found_wheels[req_name])
            click.secho(
                f"Could not find suitable version for {req} at {find_links_path}. "  # noqa: ISC003
                + f"Available versions are: {available_versions}",
                fg="red",
            )
            sys.exit(1)

        resolved_wheels.append(f"{req_name} @ {os.path.join(find_links_path, found_wheel)}")  # noqa: PTH118

    return resolved_wheels


def _wrap_requests_get(url: str, max_retries: int = 5, delay_in_s: int = 2, **kwargs: Any) -> Any:
    """Sends a GET request with retries on specified transient errors."""
    hostname = urlparse(url).hostname

    retry_strategy = Retry(
        total=max_retries,
        status_forcelist=[502, 500, 503, 504],
        allowed_methods={"HEAD", "GET", "OPTIONS"},
        backoff_factor=delay_in_s,  # Exponential backoff factor
    )

    adapter = HTTPAdapter(max_retries=retry_strategy)
    http = requests.Session()
    http.mount("http://", adapter)
    http.mount("https://", adapter)

    try:
        return http.get(url, **kwargs)
    except requests.exceptions.RequestException as e:
        click.secho(f"ERROR: cannot get a valid response from {hostname}: {e}", fg="red", bold=True)
        raise


def _find_matching_wheel(found_wheels: list[Any], req: Requirement) -> str | None:
    """Select wheels from a given list which match a given requirements.

    Args:
        found_wheels: The wheels to select from.
        req: The requirement to match.

    Returns:
        The path of the wheel if it is matched; otherwise None.

    """
    filtered_candidates = []
    for candidate_wheel in found_wheels:
        if Version(str(candidate_wheel["ver"])) in SpecifierSet(str(req.specifier)):
            filtered_candidates.append(candidate_wheel)  # noqa: PERF401

    filtered_candidates = sorted(filtered_candidates, key=lambda w: Version(str(w["ver"])), reverse=True)

    # compatible_tags is sorted so that the best suiting is first
    for compatible_tag in sys_tags():
        for candidate_wheel in filtered_candidates:
            if any(tag == compatible_tag for tag in candidate_wheel["tags"]):
                return candidate_wheel["whl"]

    return None


@click.group()
def _cli() -> None:
    pass


def _in_conda_env() -> bool:
    """Check if we are running from a conda environment."""
    # Check if the 'conda' key is in sys.modules or if a typical conda environment variable is set
    return "conda" in sys.modules or "CONDA_DEFAULT_ENV" in os.environ


def is_azure_compute_instance() -> bool:
    """Check if we are on an AzureML Compute Instance."""
    return getpass.getuser() == "azureuser" and not Path("/.dockerenv").exists()


@_cli.command("venv")
@click.option("--artifactory-user", type=str, required=False, help="The user to login to artifactory")
@click.option("--artifactory-apikey", type=str, required=False, help="The api key to login to artifactory")
@click.option("-y", "--yes", "yes_please", is_flag=True, help="Just get it done: Install everything", show_default=True)
@click.option("--skip-lfs-pull", is_flag=True, help="Skip lfs pull", show_default=True)
@click.option("--debug", is_flag=True, help="Verbose output.")
@click.option("--install-qnn-sdk", is_flag=True, default=True, help="Install qnn sdk.")
def venv(  # noqa: PLR0915
    *,
    artifactory_user: str | None,
    artifactory_apikey: str | None,
    yes_please: bool,
    skip_lfs_pull: bool,
    debug: bool,
    install_qnn_sdk: bool,
) -> None:
    """Setup a venv to use for xflow development."""
    _verify_python_environment()
    _verify_os()

    artifactory_credentials = ArtifactoryCredentials(artifactory_user, artifactory_apikey)

    required_deb_packages = {
        "curl",
        "libpq-dev",
        "libprotobuf-dev",
        "libopenmpi-dev",
        "git-lfs",
        "moreutils",
        "lsb-release",
        "libopenblas-base",
        "libgraphviz-dev",
    }

    if install_qnn_sdk:
        required_deb_packages |= {
            "xz-utils",
            "flatbuffers-compiler",
            "libflatbuffers-dev",
            "rename",
            "libtinfo5",
            "liblapacke",
            "libncurses5",
            "python3-pyqt5",
            "debootstrap",
            "clang",
            "libc++-dev",
        }

    _install_required_deb_packages(required_deb_packages)
    _install_dotnet_sdk(artifactory_credentials)
    _install_az_cli(yes_please)

    venv_path = (XFLOW_WORKSPACE / ".venv").expanduser().resolve()

    if not venv_path.is_dir():
        click.secho(f"Creating venv {venv_path}...", fg="green", bold=True)

        venv_path.parent.mkdir(exist_ok=True, parents=True)
        check_call([sys.executable, "-m", "uv", "venv", "--python", REQUIRED_PYTHON_VERSION, str(venv_path)])
    else:
        click.secho(f"Using venv {venv_path}...", fg="blue")

    versions = DEFAULT_VERSIONS

    activate_script = venv_path / "bin" / "activate"
    _prepare_activate_script(activate_script, versions.cuda_full_version, venv_path, install_qnn_sdk=install_qnn_sdk)

    pip_config_file = venv_path / "pip.conf"

    build_xflow_requirements(
        pip_config_file=pip_config_file,
        artifactory_credentials=artifactory_credentials,
        versions=versions,
        debug=debug,
        activate_script=activate_script,
    )

    click.secho("Syncing requirements with uv sync...", fg="green", bold=True)
    cmd = ["uv", "sync", "--all-groups", "--link-mode", "symlink"]

    wrap_check_call(  # noqa: S604
        subprocess.list2cmdline(cmd),
        shell=True,
        activate_script=activate_script,
        cwd=XFLOW_WORKSPACE,
        debug=debug,
    )

    _setup_git_lfs()

    click.secho("Setting up git hooks...", fg="green", bold=True)
    call(["git", "config", "--unset-all", "core.hooksPath"], cwd=XFLOW_WORKSPACE)
    githook_install_command = (
        "pre-commit install --overwrite --hook-type commit-msg --hook-type post-checkout --hook-type pre-commit "
        "--hook-type prepare-commit-msg"
    )
    wrap_check_call(  # noqa: S604
        githook_install_command,
        shell=True,
        activate_script=activate_script,
        cwd=XFLOW_WORKSPACE,
        debug=debug,
    )

    if install_qnn_sdk:
        qnn_sdk_path = venv_path / "qnn_sdk"
        qnn_sdk_yaml_path = qnn_sdk_path / "sdk.yaml"
        skip_qnn_sdk_install = False

        if qnn_sdk_yaml_path.is_file():
            qnn_sdk_yaml = qnn_sdk_yaml_path.read_text()
            qnn_version = versions.qnn_version if versions.qnn_version != "2.29.1" else "2.29.0"
            skip_qnn_sdk_install = f"version: {qnn_version}" in qnn_sdk_yaml

        if not skip_qnn_sdk_install:
            _install_jf_cli()

            click.secho(f"Installing qnn sdk {versions.qnn_version}...", fg="green", bold=True)

            shutil.rmtree(venv_path / "download", ignore_errors=True)
            (venv_path / "download").mkdir(exist_ok=True, parents=True)
            check_call(
                [
                    "jf",
                    "rt",
                    "dl",
                    "--url",
                    f"https://{ARTIFACTORY_HOST}/artifactory",
                    "--user",
                    artifactory_credentials.username,
                    "--access-token",
                    artifactory_credentials.apikey,
                    "--fail-no-op",
                    "--explode",
                    "--flat",
                    f"shared-generic-prod/qualcomm/qnn_sdk/qnn_sdk_v{versions.qnn_version}_auto_qnx.zip",
                ],
                cwd=venv_path / "download",
            )
            shutil.rmtree(qnn_sdk_path, ignore_errors=True)
            shutil.move(
                venv_path
                / "download"
                / f"qnn_sdk_v{versions.qnn_version}_auto_qnx/qaisw-v{versions.qnn_version}-auto-qnx",
                qnn_sdk_path,
            )

    check_call(["git", "lfs", "fetch"], cwd=XFLOW_WORKSPACE)

    if not skip_lfs_pull and (yes_please or click.confirm("Download unit test data?")):
        check_call(
            [
                "git lfs pull "
                "--include='xusecases/tests/test_data, xtensorflow/tests/test_data, evil/tests/test_data' "
                "--exclude=''",
            ],
            cwd=XFLOW_WORKSPACE,
            shell=True,
        )

    if not skip_lfs_pull and (yes_please or click.confirm("Download initializations?")):
        check_call(
            ["git lfs pull --include=xtension/src/xtension/initialization --exclude=''"],
            cwd=XFLOW_WORKSPACE,
            shell=True,
        )

    if activate_script:
        suffix_msg = f"Start using xflow by sourcing\nsource {activate_script}"
    else:
        suffix_msg = "Start using xflow :)"

    click.secho(f"All done. {suffix_msg}", fg="green", bold=True)


def _verify_python_environment() -> None:
    """Verify compatibility of the python environment."""
    if _in_conda_env():
        click.secho(
            "Cannot be run from a conda env! Call `conda deactivate` as many times as necessary to fully leave conda.",
            fg="red",
            bold=True,
        )
        sys.exit(1)

    if Version(REQUIRED_PYTHON_VERSION) != Version(f"{sys.version_info.major}.{sys.version_info.minor}"):
        click.secho(
            f"Please rerun with python {REQUIRED_PYTHON_VERSION}, "
            f"i.e.\npython{REQUIRED_PYTHON_VERSION} {subprocess.list2cmdline(sys.argv)}",
            fg="red",
            bold=True,
        )
        sys.exit(1)


def _verify_os() -> None:
    """Verify compatibility of the OS."""
    ubuntu_release = get_lsb_release()
    if int(ubuntu_release.split(".")[0]) < 22:
        click.secho(
            f"Cannot run on ubuntu {ubuntu_release}; update to 22.04 or later",
            fg="red",
            bold=True,
        )
        sys.exit(1)


@_cli.command()
@click.option("--pip-config-file", type=Path, required=False, help="The path to the pip.config file")
@click.option("--artifactory-user", type=str, required=False, help="The user to login to artifactory")
@click.option("--artifactory-apikey", type=str, required=False, help="The api key to login to artifactory")
@click.option("--debug", is_flag=True, help="Verbose output.")
@click.option("--verify-only", is_flag=True, help="Only verify that requirements are up-to-date.")
@click.option("--upgrade", is_flag=True, help="Upgrade all requirements to the latest possible version.")
def build_req(
    *,
    pip_config_file: Path | None,
    artifactory_user: str | None,
    artifactory_apikey: str,
    debug: bool,
    verify_only: bool,
    upgrade: bool,
) -> None:
    """Build merged requirements files."""
    _verify_python_environment()

    artifactory_credentials = ArtifactoryCredentials(artifactory_user, artifactory_apikey)

    versions = DEFAULT_VERSIONS

    build_xflow_requirements(
        pip_config_file=pip_config_file,
        artifactory_credentials=artifactory_credentials,
        versions=versions,
        debug=debug,
        activate_script=None,
        verify_only=verify_only,
        upgrade=upgrade,
    )


def build_xflow_requirements(  # noqa: C901, PLR0912, PLR0915
    *,
    pip_config_file: Path | None,
    artifactory_credentials: ArtifactoryCredentials,
    versions: Versions,
    debug: bool,
    activate_script: Path | None,
    verify_only: bool = False,
    upgrade: bool = False,
) -> None:
    """Build xflow requirements file(s).

    Args:
        pip_config_file: The pip.conf to update.
        artifactory_credentials: The credentials to download from artifactory.
        versions: The package versions to use.
        debug: If true, print shell commands before execution.
        activate_script: Optional activate script to run in a venv.
        verify_only: Only verify outputs are up to date.
        upgrade: If true, upgrade all requirements to the latest possible version.

    Returns:
        dict of requirements files

    """

    if Version(REQUIRED_PYTHON_VERSION) != Version(f"{sys.version_info.major}.{sys.version_info.minor}"):
        click.secho(f"This script needs to be run with python {REQUIRED_PYTHON_VERSION}", fg="red", bold=True)
        sys.exit(1)

    find_links = _build_find_links(versions)

    _verify_credentials(artifactory_credentials)

    if pip_config_file:
        _configure_pip(pip_config_file, find_links)

    # This file needs to be patched in-place since `install.sh` refers to it directly.
    pyproject_file = XFLOW_WORKSPACE / "pyproject.toml"
    _configure_uv(pyproject_file, find_links)

    constraints_content = pyproject_file.read_text()
    constraints_content = re.sub(r"uv==.+\"", f'uv=={UV_VERSION}"', constraints_content)
    constraints_content = re.sub(
        r"tensorflow==.+\"", f'tensorflow=={versions.tensorflow_version}"', constraints_content
    )

    tf_version = Version(versions.tensorflow_version)
    if tf_version >= Version("2.15.0") and tf_version < Version("2.16.0"):
        tensorflow_probability_version = "0.23.0"
        tensorflow_io_version = "0.36.0"
    elif tf_version >= Version("2.16.0") and tf_version < Version("2.17.0"):
        tensorflow_probability_version = "0.24.0"
        tensorflow_io_version = "0.37.1"
    elif tf_version >= Version("2.18.0") and tf_version < Version("2.19.0"):
        tensorflow_probability_version = "0.25.0"
        tensorflow_io_version = "0.37.1"
    else:
        msg = f"Unsupported tf version {versions.tensorflow_version}"
        raise RuntimeError(msg)

    torch_version = Version(versions.torch_version)
    if torch_version >= Version("2.5.0") and torch_version < Version("2.6.0"):
        torchvision_version = "0.20.1"
        triton_version = "3.1"
    elif torch_version >= Version("2.6.0") and torch_version < Version("2.7.0"):
        torchvision_version = "0.21.0"
        triton_version = "3.2"
    elif torch_version >= Version("2.7.0") and torch_version < Version("2.8.0"):
        torchvision_version = "0.22.0"
        triton_version = None
    else:
        msg = f"Unsupported torch version {versions.torch_version}"
        raise RuntimeError(msg)

    constraints_content = re.sub(
        r'"tensorflow-probability~=.+"',
        f'"tensorflow-probability~={tensorflow_probability_version}"',
        constraints_content,
    )
    constraints_content = re.sub(
        r'"tensorflow-io~=.+"', f'"tensorflow-io~={tensorflow_io_version}"', constraints_content
    )

    click.secho("Resolving tensorflow...", fg="green", bold=True)

    tensorflow_wheels = _resolve_wheels(
        package_names=[f"tensorflow=={versions.tensorflow_version}"],
        find_links_path=_build_find_links(versions, url_filter=["cudnn"])[0] + "broadwell/",
        debug=debug,
    )
    constraints_content = re.sub(r'"tensorflow @ .+?"', f'"{tensorflow_wheels[0]}"', constraints_content)

    horovod_wheels = _resolve_wheels(
        package_names=["horovod"],
        find_links_path=_build_find_links(versions, url_filter=["tensorflow"])[0],
        debug=debug,
    )
    constraints_content = re.sub(r'"horovod @ .+?"', f'"{horovod_wheels[0]}"', constraints_content)

    click.secho("Resolving torch...", fg="green", bold=True)

    torch_wheels = _resolve_wheels(
        package_names=[f"torch=={versions.torch_version}", f"torchvision=={torchvision_version}"],
        find_links_path=_build_find_links(versions, url_filter=["cudnn"])[0],
        debug=debug,
    )
    aimet_wheels = _resolve_wheels(
        package_names=["aimet-onnx==2.9.0+cu123.alliance8", "aimet-torch==2.9.0+cu123.alliance8"],
        find_links_path=_build_find_links(versions, url_filter=["torch"])[0],
        debug=debug,
    )

    constraints_content = re.sub(r'"torch @ .+?"', f'"{torch_wheels[0]}"', constraints_content)
    constraints_content = re.sub(r'"torchvision @ .+?"', f'"{torch_wheels[1]}"', constraints_content)
    constraints_content = re.sub(r'"aimet-onnx @ .+?"', f'"{aimet_wheels[0]}"', constraints_content)
    constraints_content = re.sub(r'"aimet-torch @ .+?"', f'"{aimet_wheels[1]}"', constraints_content)
    if triton_version:
        constraints_content = re.sub(r'"triton==.+?"', f'"triton=={triton_version}"', constraints_content)
    else:
        constraints_content = re.sub(r'"triton==.+?",', "", constraints_content)

    click.secho("Resolving pycuda wheels...", fg="green", bold=True)

    pycuda_wheels = _resolve_wheels(
        package_names=["pycuda"],
        find_links_path=_build_find_links(versions, url_filter=["cuda"])[0],
        debug=debug,
    )
    constraints_content = re.sub(r'"pycuda @ .+?"', f'"{pycuda_wheels[0]}"', constraints_content)

    pyproject_file.write_text(constraints_content)

    _update_image_tags(versions, verify_only=verify_only)

    uv_lock_file = Path("uv.lock")
    if uv_lock_file.is_file() and "<<<<<<< HEAD" in uv_lock_file.read_text():
        click.secho(f"{uv_lock_file.name}: file has merge markers, resetting to HEAD", fg="blue")
        check_call(["git", "reset", "-q", "--", uv_lock_file.as_posix()])
        call(["git", "checkout", "-q", "--", uv_lock_file.as_posix()])

    cmd = ["uv", "lock"]

    if verify_only:
        cmd += ["--check"]
    if upgrade:
        cmd += ["-U"]

    try:
        cmdline = subprocess.list2cmdline(cmd)
        if upgrade:
            cmdline += " 2>&1 | tee upgrade.log"
        wrap_check_call(  # noqa: S604
            cmdline,
            shell=True,
            activate_script=activate_script,
            debug=debug,
        )
    except subprocess.CalledProcessError as e:
        if e.returncode == 2:
            click.secho("uv.lock needs to be updated, run ./set-me-up.py build-req", fg="red", bold=True)
        else:
            click.secho(
                f"uv lock --check failed with {e.returncode}, run ./set-me-up.py build-req", fg="red", bold=True
            )
        sys.exit(1)


def _update_image_tags(versions: Versions, *, verify_only: bool) -> None:
    """Recompute the tags for all images and store the result in .build/dl_image/tags.txt.

    Args:
        versions: The versions of the packages.
        verify_only: If True, will fail if the tags have changed, and do no changes otherwise.

    """
    uv_version_file = XFLOW_WORKSPACE / ".build/dl_image/uv.txt"
    uv_version_file.write_text(UV_VERSION)

    version_file = XFLOW_WORKSPACE / "VERSION"

    docker_files = [
        f
        for f in sorted(
            _glob(XFLOW_WORKSPACE / ".build/dl_image", "*", _load_gitignore(XFLOW_WORKSPACE), recursive=True)
        )
        if f.suffix not in [".txt", ".md", ".dockerfile"] and f.is_file()
    ]

    hash_sha1 = hashlib.sha1()  # noqa: S324

    hash_sha1.update(version_file.read_text().strip().encode())
    hash_sha1.update(uv_version_file.read_text().strip().encode())

    for f in docker_files:
        hash_sha1.update(f.read_bytes())

    hash_sha1.update((XFLOW_WORKSPACE / ".build/dl_image/train.dockerfile").read_bytes())
    train_tag = hash_sha1.hexdigest()

    hash_sha1.update((XFLOW_WORKSPACE / ".build/dl_image/release.dockerfile").read_bytes())
    release_tag = hash_sha1.hexdigest()

    ci_sha1 = hash_sha1.copy()
    ci_sha1.update((XFLOW_WORKSPACE / ".build/dl_image/ci.dockerfile").read_bytes())
    ci_tag = ci_sha1.hexdigest()

    hash_sha1.update((XFLOW_WORKSPACE / ".build/dl_image/dev.dockerfile").read_bytes())
    dev_tag = hash_sha1.hexdigest()

    tag_variables = XFLOW_WORKSPACE / ".build/dl_image/tags.txt"

    tag_content = ""
    train_suffix = ""
    release_suffix = ""
    dev_suffix = ""
    for field in fields(versions):
        tag_content += f"{field.name}={getattr(versions, field.name)}\n"

        if field.name.startswith("mm"):
            # do not add mm libs; otherwise tag becomes too long for acr
            continue

        tag_spec = f"_{_shorten_tag_specifier(field.name)}{getattr(versions, field.name)}"
        if field.name not in NON_TRAIN_KEYS:
            train_suffix += tag_spec
        release_suffix += tag_spec
        dev_suffix += tag_spec

    train_tag = f"{train_tag}{train_suffix}"
    release_tag = f"{release_tag}{release_suffix}"
    ci_tag = f"{ci_tag}{release_suffix}"
    dev_tag = f"{dev_tag}{dev_suffix}"

    tag_content += f"""cuda_version={versions.cuda_version}
uv_version={UV_VERSION}
train_tag={train_tag}
release_tag={release_tag}
ci_tag={ci_tag}
dev_tag={dev_tag}
train_suffix={train_suffix}
release_suffix={release_suffix}
dev_suffix={dev_suffix}
"""

    if not tag_variables.is_file() or tag_variables.read_text().strip() != tag_content.strip():
        click.secho(f"{tag_variables.name}: docker hashes have changed!")

        if verify_only:
            click.secho(
                f"""{tag_variables}: hash verification failed! To update the hash run
                ./set-me-up.py build-req""",
                fg="red",
                bold=True,
            )
            sys.exit(1)

        tag_variables.write_text(tag_content)
    else:
        click.secho(f"{tag_variables.name}: docker unchanged!")


def _shorten_tag_specifier(spec: str) -> str:
    """Shorten the specifier to save chars to stay below 128 char tag length limit."""
    return (
        spec.replace("_version", "")
        .replace("ubuntu", "")
        .replace("cuda_full", "cu")
        .replace("cuda", "cu")
        .replace("tensorflow", "tf")
        .replace("tensorrt", "trt")
        .replace("open_mpi", "mpi")
    )


def _build_index_url() -> str | None:
    """Build the index-url for the pip environment."""
    return f"https://{ARTIFACTORY_HOST}/artifactory/api/pypi/shared-pypi-prod/simple"


def _build_extra_index_urls() -> list[str]:
    """Build the extra-index-urls for the pip environment."""
    return []


def _build_find_links(version: Versions, *, url_filter: Sequence[str] | None = None) -> list[str]:
    """Build the find-links for the pip environment."""

    prefix = f"https://{ARTIFACTORY_HOST}/artifactory/list/shared-generic-prod/vdeep/"
    ubuntu_suffix = f"wheels/ubuntu{version.ubuntu_version}/"
    cuda_suffix = f"{ubuntu_suffix}cuda{version.cuda_version}/"
    suffixes = {
        "ubuntu": ubuntu_suffix,
        "cuda": cuda_suffix,
        "cudnn": f"{cuda_suffix}cudnn{version.cudnn_version}/",
        "tensorflow": f"{cuda_suffix}cudnn{version.cudnn_version}/tensorflow{version.tensorflow_version}/",
        "torch": f"{cuda_suffix}cudnn{version.cudnn_version}/torch{version.torch_version}/",
    }

    return [f"{prefix}{suffix}" for key, suffix in suffixes.items() if url_filter is None or key in url_filter]


def _expand_and_consolidate_requirements(requirements: Sequence[str]) -> list[str]:
    """Expand and consolidate requirements by following recursions and consolidating duplicates.

    A known issue is that requirements and constraints are not distinguished so moving from a constraint to an
    actual requirement, or vice versa, does not change the hash.
    This issue will be solved when migrating to uv: https://github.com/PACE-INT/xflow/pull/8395
    """
    expanded: set[str] = set()
    consolidated = {}

    for req in requirements:
        _expand_requirement(expanded, req)

    for req_str in expanded:
        req = Requirement(req_str)
        req.name = canonicalize_name(req.name)

        if req.name in consolidated:
            # Combine specifiers
            consolidated[req.name].specifier &= req.specifier
            consolidated[req.name].extras |= req.extras
            assert consolidated[req.name].url is None or req.url is None
            consolidated[req.name].url = req.url or consolidated[req.name].url
        else:
            consolidated[req.name] = req

    return sorted(str(r) for r in consolidated.values())


def _expand_requirement(requirements: set[str], requirement: str) -> None:
    """Expand a requirement by following recursions."""
    requirement = requirement.strip()
    if not requirement:
        return
    if requirement.startswith("#"):
        return
    if requirement.startswith("--hash"):
        return
    if requirement.endswith("\\"):
        requirement = requirement[:-1].strip()

    if requirement.startswith(("-c ", "-r ")):
        for inner_req in Path(requirement[3:]).read_text().splitlines():
            _expand_requirement(requirements, inner_req)
    else:
        requirement = requirement.split("#")[0].strip()
        requirements.add(requirement)


def _prepare_activate_script(
    activate_script: Path, cuda_full_version: str, venv_path: Path, *, install_qnn_sdk: bool
) -> None:
    """Add LD_LIBRARY_PATH and PATH to activate script of venv.

    Args:
        activate_script: The activate script to modify.
        cuda_full_version: The CUDA version.
        venv_path: The path of the venv.
        install_qnn_sdk: Install the qnn sdk.

    """
    activate_script_content = activate_script.read_text()

    nvidia_lib_path = f"{venv_path.resolve()}/lib/python3.10/site-packages/nvidia"

    ld_library_path = (
        f"export LD_LIBRARY_PATH=/usr/local/lib"
        f":{nvidia_lib_path}/cublas/lib"
        f":{nvidia_lib_path}/cuda_cupti/lib"
        f":{nvidia_lib_path}/cuda_nvcc/lib"
        f":{nvidia_lib_path}/cuda_nvrtc/lib"
        f":{nvidia_lib_path}/cuda_runtime/lib"
        f":{nvidia_lib_path}/cudnn/lib"
        f":{nvidia_lib_path}/cufile/lib"
        f":{nvidia_lib_path}/cufft/lib"
        f":{nvidia_lib_path}/curand/lib"
        f":{nvidia_lib_path}/cusolver/lib"
        f":{nvidia_lib_path}/cusparse/lib"
        f":{nvidia_lib_path}/cusparselt/lib/"
        f":{nvidia_lib_path}/nccl/lib"
        f":{nvidia_lib_path}/nvjitlink/lib"
        f":{nvidia_lib_path}/nvjpeg/lib"
        f":{venv_path.resolve()}/lib/python3.10/site-packages/cusparselt/lib"
        f":{venv_path.resolve()}/lib/python3.10/site-packages/tensorrt_libs"
        f":{venv_path.resolve()}/lib"
        ":$LD_LIBRARY_PATH"
    )
    if ld_library_path not in activate_script_content:
        click.secho("Updating LD_LIBRARY_PATH...", fg="green", bold=True)

        with activate_script.open("a") as f:
            f.write("\n")
            f.write(ld_library_path)
            f.write("\n")

    wrapt_workaround = "export WRAPT_DISABLE_EXTENSIONS=1"
    if wrapt_workaround not in activate_script_content:
        with activate_script.open("a") as f:
            f.write("\n")
            f.write(wrapt_workaround)
            f.write("\n")

    tf_legacy_keras = "export TF_USE_LEGACY_KERAS=1"
    if tf_legacy_keras not in activate_script_content:
        with activate_script.open("a") as f:
            f.write("\n")
            f.write(tf_legacy_keras)
            f.write("\n")

    if install_qnn_sdk:
        qnn_sdk_root = venv_path / "qnn_sdk"

        lines = [
            f'\nexport QNN_SDK_ROOT="{qnn_sdk_root}"\n',
            f'export LD_LIBRARY_PATH="{qnn_sdk_root}/lib/x86_64-linux-clang:${{LD_LIBRARY_PATH}}"\n',
            f'export PYTHONPATH="{qnn_sdk_root}/lib/python/:${{PYTHONPATH}}"\n',
            f'export QNN_SDK_TOOLS="{qnn_sdk_root}/bin/x86_64-linux-clang"\n',
            "export PATH=${QNN_SDK_TOOLS}:${PATH}\n\n",
        ]

        with activate_script.open("a") as f:
            for line in lines:
                if line in activate_script_content:
                    continue
                f.write(line)


def _configure_pip(
    pip_config_file: Path,
    find_links: Sequence[str],
) -> None:
    """Update pip.conf with extra-index-url and find-links.

    Args:
        pip_config_file: The pip.conf to update
        find_links: The find links urls for additional packages.

    Returns:
        The full find-links prefix.

    """
    if not _should_write_pip_conf(pip_config_file, find_links):
        return

    click.secho("Updating pip.conf...", fg="green", bold=True)

    pip_config = "[global]\n"

    if index_url := _build_index_url():
        pip_config += f"index-url = {index_url}\n"

    if extra_index_urls := _build_extra_index_urls():
        pip_config += f"extra-index-url = {extra_index_urls[0]}\n"
        for url in extra_index_urls[1:]:
            pip_config += f"                  {url}\n"

    if find_links:
        pip_config += f"find-links = {find_links[0]}\n"
        for url in find_links[1:]:
            pip_config += f"             {url}\n"

    pip_config_file.write_text(pip_config)

    check_call(["chmod", "600", str(pip_config_file)])


def _should_write_pip_conf(pip_config_file: Path, find_links: Sequence[str]) -> bool:
    """Check if the pip.conf should be updated."""
    if not pip_config_file.is_file():
        return True

    write_pip_conf = False
    pip_config_content = pip_config_file.read_text()

    if (index_url := _build_index_url()) and f"index-url = {index_url}\n" not in pip_config_content:
        write_pip_conf = True

    for extra_index_url in _build_extra_index_urls():
        if extra_index_url not in pip_config_content:
            write_pip_conf = True

    for find_links_url in find_links:
        if find_links_url not in pip_config_content:
            write_pip_conf = True

    return write_pip_conf


def _configure_uv(
    pyproject_toml_file: Path,
    find_links: Sequence[str],
) -> None:
    """Update uv.toml with extra-index-url and find-links.

    Args:
        pyproject_toml_file: The uv.toml to update
        find_links: The find links urls for additional packages.

    Returns:
        The full find-links prefix.

    """
    uv_toml = pyproject_toml_file.parent / "uv.toml"
    if uv_toml.exists():
        uv_toml.unlink()

    with pyproject_toml_file.open("rb") as fd:
        pyproject_toml = tomli.load(fd)

    # If multiple urls exist search for default and check whether the url matches
    uv_indexes = pyproject_toml["tool"]["uv"]["index"]
    correct_index = next(
        (entry for entry in uv_indexes if entry.get("default") and entry.get("url") == _build_index_url()),
        None,
    )

    if not correct_index:
        click.secho(
            f"pyproject.toml: Update tool.uv.index to include an entry with default = true and url = \
            '{_build_index_url()}'",
            fg="red",
            bold=True,
        )

    if set(pyproject_toml["tool"]["uv"].get("extra-index-url", [])) != set(_build_extra_index_urls()):
        click.secho(
            f"pyproject.toml: Update tool.uv.extra-index-url to {_build_extra_index_urls()}", fg="red", bold=True
        )

    if set(pyproject_toml["tool"]["uv"].get("find-links", [])) != set(find_links):
        click.secho(f"pyproject.toml: Update tool.uv.find-links to {find_links}", fg="red", bold=True)


def _verify_artifactory_apikey(artifactory_user: str, artifactory_apikey: str, message: str = "") -> None:
    click.echo("Verifying artifactory credentials...")

    response = _wrap_requests_get(
        f"https://{ARTIFACTORY_HOST}/artifactory/api/system/ping",
        auth=(artifactory_user, artifactory_apikey),
        timeout=5,
    )
    if response.status_code != 200 or response.text != "OK":
        click.secho(
            f"{message}\n{response}",
            fg="red",
            bold=True,
        )
        sys.exit(42)


def _verify_credentials(artifactory_credentials: ArtifactoryCredentials) -> None:
    _verify_netrc(ARTIFACTORY_HOST, artifactory_credentials.username, artifactory_credentials.apikey, write=True)

    artifactory_credentials.verify(
        "Could not connect to artifactory with supplied credentials. Please check whether user name and access "
        "token are successfully configured in ~/.netrc:",
    )

    click.echo("Credentials verified")


def _verify_netrc(hostname: str, user: str | None, password: str | None, write: bool = False) -> None:  # noqa: FBT001, FBT002
    netrc_file = Path(os.getenv("NETRC", "~/.netrc")).expanduser()

    try:
        n = netrc.netrc(netrc_file)
    except FileNotFoundError:
        if write:
            netrc_file.write_text(
                f"""machine {hostname}
    login {user}
    password {password}
    """,
            )
            check_call(["chmod", "600", str(netrc_file)])
        return

    auth = n.authenticators(hostname)

    if auth is None:
        if write:
            netrc_file.write_text(
                netrc_file.read_text()
                + f"""

    machine {hostname}
    login {user}
    password {password}
    """,
            )
            check_call(["chmod", "600", str(netrc_file)])
        return

    netrc_user, _, netrc_apikey = auth

    if user is not None and netrc_user != user:
        click.secho(
            f"{hostname}: ~/.netrc username is {netrc_user} while venv is configured with {user}. "
            "Please verify the credentials in ~/.netrc and the venv's pip.conf",
            fg="red",
            bold=True,
        )
        sys.exit(42)

    if password is not None and netrc_apikey != password:
        click.secho(
            f"{hostname}: ~/.netrc apikey is {password} while venv is configured with {password}. "
            "Please verify the credentials in ~/.netrc and the venv's pip.conf",
            fg="red",
            bold=True,
        )
        sys.exit(42)


def _get_netrc_credentials_for_hostname(hostname: str) -> tuple[str, str | None] | None:
    netrc_file = Path(os.getenv("NETRC", "~/.netrc")).expanduser()
    try:
        n = netrc.netrc(netrc_file)
    except netrc.NetrcParseError:
        if netrc_file.is_file():
            check_call(["chmod", "600", netrc_file.as_posix()])
        n = netrc.netrc(netrc_file)
    except FileNotFoundError:
        return None

    auth = n.authenticators(hostname)

    if auth is None:
        return None

    netrc_user, _, netrc_password = auth

    return netrc_user, netrc_password


def _load_gitignore(workspace: Path) -> PathSpec:
    """Load the .gitignore file from the workspace and return a PathSpec object."""
    gitignore_path = workspace / ".gitignore"

    if gitignore_path.exists():
        with gitignore_path.open("r") as f:
            gitignore_patterns = f.read().splitlines()
    else:
        gitignore_patterns = []

    return PathSpec.from_lines("gitwildmatch", gitignore_patterns)


def _glob(path: Path, pattern: str, gitignore: PathSpec, *, recursive: bool = False) -> Generator[Path]:
    """Glob files in the workspace matching the given pattern."""
    for p in path.rglob(pattern) if recursive else path.glob(pattern):
        if not gitignore.match_file(p):
            yield p


def _install_jf_cli() -> None:
    """Install the jfrog cli if it is not available."""
    if shutil.which("jf") is not None:
        return

    click.secho("Installing jfrog cli...", fg="green", bold=True)
    check_call(
        "curl -fL https://install-cli.jfrog.io | sudo sh",
        shell=True,
    )


def _install_az_cli(yes_please: bool) -> None:  # noqa: FBT001
    """Install the azure cli if it is not available and set-up the REQUESTS_CA_BUNDLE environment variable."""
    if shutil.which("az") is not None:
        return
    click.secho("Installing azure cli...", fg="green", bold=True)

    check_call("curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash", shell=True)

    # Set up additional ca certificates environment variable
    ca_certificate_bundle = Path("/etc/ssl/certs/ca-certificates.crt")
    if ca_certificate_bundle.is_file():
        click.secho("Configuring REQUESTS_CA_BUNDLE...", fg="green", bold=True)

        for config_file in [".zshrc", ".bashrc"]:
            config_path = Path.home() / config_file

            if config_path.is_file():
                requests_ca_bundle = f"export REQUESTS_CA_BUNDLE={ca_certificate_bundle}"

                if requests_ca_bundle in config_path.read_text():
                    continue

                if yes_please or click.confirm(
                    f"Should I add '{requests_ca_bundle}' to the '{config_file}' config file I found in your home?",
                ):
                    with config_path.open("a") as f:
                        f.write("\n")
                        f.write(requests_ca_bundle)
                        f.write("\n")

        if "REQUESTS_CA_BUNDLE" not in os.environ:
            os.environ["REQUESTS_CA_BUNDLE"] = str(ca_certificate_bundle)


def _login_azure_account(*, debug: bool) -> None:
    """Log in to the azure account and the container registry."""
    current_tentant_id = ""
    required_tenant_id = "a6c60f0f-76aa-4f80-8dba-092771d439f0"

    # check if logged in into azure and token is valid
    try:
        cmd = ["az", "account", "get-access-token", "--query", "tenant"]
        ouput = wrap_check_output(cmd, stderr=subprocess.DEVNULL, text=True, debug=debug).rstrip()
        current_tentant_id = json.loads(ouput)
    except subprocess.CalledProcessError:
        pass

    if current_tentant_id != required_tenant_id:
        click.secho("Running 'az login'...", fg="green", bold=True)
        cmd = ["az", "login", "--tenant", "AADPACE.onmicrosoft.com", "--use-device-code"]
        wrap_check_call(cmd, debug=debug)

    # Log in the container registry if needed
    devcontainer_base = _find_docker_base_image(DOCKER_DEV_IMAGE_DIR / "Dockerfile.base")
    hostname = devcontainer_base.split("/")[0]
    if hostname.endswith(".azurecr.io"):
        click.secho("Logging into container registry...", fg="green", bold=True)
        wrap_check_call(["az", "acr", "login", "-n", hostname.removesuffix(".azurecr.io")], debug=debug)


def _login_docker(artifactory_credentials: ArtifactoryCredentials, *, debug: bool) -> None:
    """Log in to the docker registry."""
    click.secho("Logging into docker registry...", fg="green", bold=True)
    cmd = [
        "docker",
        "login",
        f"https://{ARTIFACTORY_HOST}/artifactory",
        "--username",
        artifactory_credentials.username,
        "--password",
        artifactory_credentials.apikey,
    ]
    wrap_check_call(cmd, debug=debug)


def _check_apt_package_available(package_name: str) -> bool:
    """Check if a package is available in the apt sources."""
    available = check_output(["apt-cache", "policy", package_name], text=True)
    return "Candidate:" in available


def _install_compose() -> None:
    """Install docker-compose and nvidia-container-toolkit if not available."""
    # the azure devops images do not install the docker-compose-plugin debian package but rather install the
    # latest plugin directly from github, hence check if compose v2 is available.
    compose_version_run = subprocess.run(
        ["docker compose version"],
        stdout=subprocess.PIPE,
        stderr=subprocess.DEVNULL,
        shell=True,
        text=True,
        check=False,
    )
    if compose_version_run.returncode != 0:
        if not _check_apt_package_available("docker-compose-plugin"):
            if not click.confirm(
                click.style(
                    "docker-compose-plugin not found in apt sources. Proceed by adding it?",
                    bold=True,
                    fg="yellow",
                ),
                default=True,
            ):
                return

            check_call("sudo install -m 0755 -d /etc/apt/keyrings", shell=True)

            check_call(
                """sudo curl -fsSL https://download.docker.com/linux/ubuntu/gpg \
    -o /etc/apt/keyrings/docker.asc""",
                shell=True,
            )
            check_call("sudo chmod a+r /etc/apt/keyrings/docker.asc", shell=True)

            check_call(
                f"""echo \
    "deb [arch=$(dpkg --print-architecture) signed-by=/etc/apt/keyrings/docker.asc] \
    https://download.docker.com/linux/ubuntu {get_lsb_codename()} stable" | \
    sudo tee /etc/apt/sources.list.d/docker.list > /dev/null""",
                shell=True,
            )

            check_call("sudo apt-get update", shell=True)

        _install_required_deb_packages({"docker-compose-plugin"})

    MIN_COMPOSE_VERSION = "2.16.0"  # noqa: N806
    compose_version_match = re.search(r"v(\d+\.\d+\.\d+)", compose_version_run.stdout)

    if not compose_version_match:
        click.secho("Could not determine docker compose version! Check install!", fg="red", bold=True)
        sys.exit(1)
    elif Version(compose_version_match.group(1)) < Version(MIN_COMPOSE_VERSION):
        click.secho(
            f"Your docker compose version {compose_version_match.group(1)} is too old. Update to "
            f"{MIN_COMPOSE_VERSION} before continuing!",
            fg="red",
            bold=True,
        )
        sys.exit(1)

    if "nvidia-container-toolkit" not in _find_installed_deb_packages():
        if not _check_apt_package_available("nvidia-container-toolkit"):
            if not click.confirm(
                click.style(
                    "nvidia-container-toolkit not found in apt sources. Proceed by adding it?",
                    bold=True,
                    fg="yellow",
                ),
                default=True,
            ):
                return

            check_call(
                """curl -fsSL https://nvidia.github.io/libnvidia-container/gpgkey | \
sudo gpg --dearmor -o /usr/share/keyrings/nvidia-container-toolkit-keyring.gpg""",
                shell=True,
            )
            check_call(
                """curl -s -L https://nvidia.github.io/libnvidia-container/stable/deb/nvidia-container-toolkit.list | \
sed 's#deb https://#deb [signed-by=/usr/share/keyrings/nvidia-container-toolkit-keyring.gpg] https://#g' | \
sudo tee /etc/apt/sources.list.d/nvidia-container-toolkit.list""",
                shell=True,
            )
            check_call("sudo apt-get update", shell=True)

        _install_required_deb_packages({"nvidia-container-toolkit"})

        click.secho("Restarting docker daemon...", fg="green", bold=True)
        check_call("sudo nvidia-ctk runtime configure --runtime=docker", shell=True)
        check_call("sudo systemctl restart docker", shell=True)

    docker_group_exists = (
        subprocess.call(["getent", "group", "docker"], stdout=subprocess.DEVNULL, stderr=subprocess.DEVNULL) == 0
    )
    # we explicitly allow failures here to not choke on non-existent groups.
    user_groups = run("groups", shell=True, capture_output=True, text=True, check=False).stdout.strip().split(" ")

    if docker_group_exists and "docker" not in user_groups:
        click.secho(f"Adding user {getpass.getuser()} to docker group...", fg="green", bold=True)
        check_call(f"sudo usermod -aG docker {getpass.getuser()}", shell=True)
        click.secho(
            f"User {getpass.getuser()} added to docker group, you have to logout and "  # noqa: ISC003
            + "login again for this change to become effective",
            fg="yellow",
            bold=True,
        )
        sys.exit(1)


def _configure_pip_in_container() -> None:
    """Configure pip in the container to use the correct find-links."""
    find_links = _build_find_links(DEFAULT_VERSIONS)

    local_config = Path("~/.config/xflow/docker-pip.conf").expanduser()
    local_config.parent.mkdir(parents=True, exist_ok=True)

    _configure_pip(local_config, find_links)


def _configure_uv_in_container() -> None:
    """Configure uv in the container to use the correct find-links."""
    find_links = _build_find_links(DEFAULT_VERSIONS)

    _configure_uv(XFLOW_WORKSPACE / "pyproject.toml", find_links)


def _configure_git(reconfigure: bool) -> None:  # noqa: FBT001
    """Configure git user name and email."""
    click.secho("Configuring Git...", fg="green", bold=True)

    request_reconfiguration = False
    git_name = None
    git_email = None

    try:
        git_name = check_output("git config --get user.name", shell=True, text=True)
        git_email = check_output("git config --get user.email", shell=True, text=True)
    except subprocess.CalledProcessError:
        request_reconfiguration = True

    if git_name and git_email:
        click.secho(
            f"The following Git configurations are found:\nuser.name: {git_name}\nuser.email: {git_email}",
            fg="green",
            bold=True,
        )
        if reconfigure:
            request_reconfiguration = click.confirm("Do you wish to reconfigure the Git user name and email?")

    if request_reconfiguration:
        git_name = click.prompt("Enter Git Name")
        check_call(f"git config --global user.name '{git_name}'", shell=True)
        git_email = click.prompt("Enter Git Email")
        check_call(f"git config --global user.email '{git_email}'", shell=True)

    click.secho("Set git credential cache to 10h for this repository")
    check_call("git config --local credential.helper 'cache --timeout=36000'", shell=True)


def _find_docker_base_image(dockerfile: Path) -> str:
    """Return the base image name by searching for the first "FROM" statement."""
    with dockerfile.open(encoding="utf8") as handle:
        for line in handle:
            line_no_comment = line.split("#")[0]  # Remove comments
            token = line_no_comment.split()  # Split into whitespace-separated tokens
            if len(token) == 2 and token[0].lower() == "from":
                return token[1]
    msg = f"Failed to parse {dockerfile} for base-image."
    raise RuntimeError(msg)


def _customize_devcontainer() -> None:
    """Customize the docker-compose.yaml file."""
    click.secho("Customizing docker-compose.yaml...", fg="green", bold=True)
    check_call(["python3", f"{DOCKER_DEV_IMAGE_DIR}/customize_devcontainer.py"])


@_cli.group()
def docker() -> None:
    """Entrypoint for Docker-related tasks."""


@docker.command("init")
@click.option("--artifactory-user", type=str, required=False, help="The user to login to artifactory")
@click.option("--artifactory-apikey", type=str, required=False, help="Artifactory API key.")
@click.option("-y", "--yes", "yes_please", is_flag=True, help="Just get it done: Install everything")
@click.option("-r", "--reconfigure", is_flag=True, help="Re-Configure git credentials")
@click.option("--debug", is_flag=True, help="Verbose output.")
def docker_init(
    *, artifactory_user: str | None, artifactory_apikey: str | None, yes_please: bool, reconfigure: bool, debug: bool
) -> None:
    """Initialize the Docker environment."""

    env_filename = DOCKER_DEV_IMAGE_DIR / ".env"

    if XFLOW_WORKSPACE.parent == Path.home():
        if env_filename.is_file():  # Allow existing xflow installations to keep xflow in the home directory for now.
            click.confirm(
                "Having xflow folder in host's home directory is known to cause issues and will be blocked in"
                " the future. Do you want to continue at your own risk?",
                default=True,
            )
        else:  # Prevent new installations of xflow in home directory
            msg = (
                f"The parent dir of your workspace ({XFLOW_WORKSPACE}) is equal to your home directory, which is"
                " known to cause issues. Use another directory for xflow. Read xflow README for more information."
            )
            raise RuntimeError(msg)

    _install_az_cli(yes_please)
    _login_azure_account(debug=debug)
    _install_compose()
    _customize_devcontainer()
    _configure_git(reconfigure=reconfigure)
    _setup_git_lfs()
    Path("~/.azure").expanduser().mkdir(exist_ok=True)
    Path("~/.azcopy").expanduser().mkdir(exist_ok=True)
    Path("~/.azure-devops").expanduser().mkdir(exist_ok=True)
    Path("~/.azureml").expanduser().mkdir(exist_ok=True)
    Path("~/.qnx").expanduser().mkdir(exist_ok=True)
    Path("~/.vscode-server").expanduser().mkdir(exist_ok=True)
    Path("~/.qnx").expanduser().mkdir(exist_ok=True)

    Path("~/.gitconfig").expanduser().touch()
    Path("~/.bashrc_user").expanduser().touch()

    click.echo("Successfully initialized Docker environment.")

    click.secho(f"Adding env file to '{env_filename}'...", fg="green", bold=True)
    docker_env = {
        "USER_UID": check_output(["id", "-u"]).decode().rstrip(),
        "USER_GID": check_output(["id", "-g"]).decode().rstrip(),
        "USER_NAME": USER_NAME,
        "WORKSPACE": str(XFLOW_WORKSPACE),
        "WORKSPACE_FOLDER_NAME": XFLOW_NAME,
        "XFLOW_REPO_PARENT_DIR": str(XFLOW_REPO_PARENT_DIR),
        # Workaround required to support multiple DevContainer users on single Workstation
        # Workaround can be removed above DevContainers #0.365.0(-pre-release)
        # https://github.com/microsoft/vscode-remote-release/issues/512
        "COMPOSE_PROJECT_NAME": f"{USER_NAME}_{XFLOW_NAME}",
    }
    with env_filename.open(mode="w") as file:
        for key, value in docker_env.items():
            file.write(f"{key}={value}\n")

    artifactory_credentials = ArtifactoryCredentials(artifactory_user, artifactory_apikey)

    _verify_credentials(artifactory_credentials)

    _configure_pip_in_container()
    _configure_uv_in_container()

    dockerfile = DOCKER_DEV_IMAGE_DIR / "Dockerfile.base"
    docker_base_image = _find_docker_base_image(dockerfile)
    if docker_base_image.startswith("artifactory-02"):
        _login_docker(artifactory_credentials, debug=debug)
    if _is_new_image_available(_find_docker_base_image(dockerfile)):  # noqa: SIM102
        if yes_please or click.confirm(
            "New image in registry detected, do you want to pull it & rebuild your container?",
            default=True,
        ):
            click.secho(
                "Rebuilding container...",
                fg="yellow",
                bold=True,
            )
            env = os.environ.copy()
            env.update({"COMPOSE_DOCKER_CLI_BUILD": "1", "DOCKER_BUILDKIT": "1"})
            wrap_check_call([*DOCKER_COMPOSE, "build", "--pull"], env=env, debug=debug)

    msg = """...done. You can now build an image and run a container via

./set-me-up.py docker run

or spin up a devcontainer in vscode.
    """
    click.echo(msg)


@docker.command("run")
@click.option(
    "-d",
    "--detached",
    is_flag=True,
    default=False,
    help="Run container in background and print container ID",
)
@click.option("--debug", is_flag=True, help="Verbose output.")
def docker_run(*, detached: bool, debug: bool) -> None:
    """Spin up a fresh container instance."""
    env_filename = DOCKER_DEV_IMAGE_DIR / ".env"
    if not env_filename.is_file():
        click.secho("Please run ./set-me-up.py docker init before", fg="red", bold=True)
        sys.exit(1)

    _login_azure_account(debug=debug)
    env = os.environ.copy()
    env.update({"COMPOSE_DOCKER_CLI_BUILD": "1", "DOCKER_BUILDKIT": "1"})
    if detached:
        docker_id = wrap_check_output(DOCKER_COMPOSE_RUN_DETACHED, env=env, text=True, debug=debug).strip()

        click.secho(f"Waiting for container {docker_id} to get ready...", fg="blue")

        while True:
            try:
                health_status = wrap_check_output(
                    ["docker", "inspect", "--format={{.State.Health.Status}}", docker_id],
                    env=env,
                    text=True,
                    debug=debug,
                ).strip()

                if health_status != "healthy":
                    time.sleep(10)
                else:
                    break
            except subprocess.CalledProcessError as e:
                if e.returncode == 1:
                    click.secho(
                        f"Container {docker_id} crashed during startup. Please re-run without --detached to analyze",
                        fg="red",
                        bold=True,
                    )
                    sys.exit(1)
                else:
                    raise

        click.secho(f"Container is ready: {docker_id}", fg="green", bold=True)
    else:
        wrap_check_call(DOCKER_COMPOSE_RUN, env=env, debug=debug)


@docker.command("rebuild")
@click.option("--debug", is_flag=True, help="Verbose output.")
def docker_rebuild(*, debug: bool) -> None:
    """Re-build the container image."""
    _login_azure_account(debug=debug)
    _customize_devcontainer()
    _configure_pip_in_container()
    _configure_uv_in_container()
    env = os.environ.copy()
    env.update({"COMPOSE_DOCKER_CLI_BUILD": "1", "DOCKER_BUILDKIT": "1"})
    wrap_check_call([*DOCKER_COMPOSE, "down", "--rmi", "all"], env=env)
    wrap_check_call([*DOCKER_COMPOSE, "build", "--pull"], env=env)


def _get_local_image_digest(image_uri: str) -> str | None:
    """Get the digest of the local image tag."""

    # Check if the image exists locally
    try:
        cmd = ["docker", "image", "inspect", f"{image_uri}"]
        output = check_output(cmd, env=os.environ)
    except subprocess.CalledProcessError:
        return None

    image_meta = json.loads(output)[0]
    repo_digests = image_meta.get("RepoDigests")
    if not repo_digests:
        return None

    # Extract the digest part from the repo digest
    return next(digest.split("@")[1] for digest in repo_digests)


def _get_remote_image_digest(image_uri: str) -> str:
    """Get the digest of the image in the container registry.

    This function assumes that the image_uri is in the format <registry>/<image>:<tag>.
    """
    name = image_uri.split(".")[0]
    repository = "".join(image_uri.split("/")[1:])
    cmd = ["az", "acr", "repository", "show", "--name", f"{name}", "--image", f"{repository}"]
    output = check_output(
        cmd,
        env=os.environ,
        stderr=subprocess.DEVNULL,
    )
    image_meta = json.loads(output)
    return image_meta.get("digest")


def _is_new_image_available(image_uri: str) -> bool:
    """Check if a new image is available in the container registry."""
    hostname = image_uri.split("/")[0]
    if not hostname.endswith(".azurecr.io"):
        click.secho(
            f"Devcontainer base image {image_uri} not hosted on an Azure Container Registry. "
            "Cannot check if a newer version is available. Continue assuming this is the case",
            fg="yellow",
        )
        return True
    local_digest = _get_local_image_digest(image_uri)
    remote_digest = _get_remote_image_digest(image_uri)
    return local_digest != remote_digest


if __name__ == "__main__":
    _cli()
