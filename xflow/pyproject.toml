[dependency-groups]
azure = [
    "azureml-mlflow",      # plugin for mlflow so it works on AzureML compute
    "pip",                 # required to install wheels for submission
    "uv>=0.5.16",
]
train = [
    "pydantic-settings", # warper-package depends on it but does not model this dependency.
    {include-group = "azure"},
]
xflow = [
    "xflow-cluster_cloud",
    "xflow-conversion",
    "xflow-conversion_contract",
    "xflow-data_formats",
    "xflow-data_test_framework",
    "xflow-evil",
    "xflow-loomy",
    "xflow-loomy_usecases",
    "xflow-pyper",
    "xflow-pytest_lfs_tracker",
    "xflow-release_report",
    "xflow-xazure",
    "xflow-azure_tools",
    "xflow-xcommon",
    "xflow-xcontract",
    "xflow-xhelpers",
    "xflow-xreport",
    "xflow-xtension",
    "xflow-xtensorflow",
    "xflow-xtorch",
    "xflow-xtorch_extensions",
    "xflow-xtorch_usecases",
    "xflow-xusecases",
    {include-group = "train"},
]
xflow_release = [
    "xflow-conversion[conversion]",
    "xflow-data_test_framework[conversion]",
    "xflow-xtorch_usecases[conversion]",
    "xflow-xusecases[conversion]",
    {include-group = "xflow"},
    {include-group = "release"},
]
xflow_test = [
    "xflow-cluster_cloud[test]",
    "xflow-conversion[test]",
    "xflow-conversion_contract[test]",
    "xflow-data_formats[test]",
    "xflow-data_test_framework[test]",
    "xflow-evil[test]",
    "xflow-loomy[test]",
    "xflow-loomy_usecases[test]",
    "xflow-release_report[test]",
    "xflow-xazure[test]",
    "xflow-azure_tools[test]",
    "xflow-xcommon[test]",
    "xflow-xcontract[test]",
    "xflow-xhelpers[test]",
    "xflow-xreport[test]",
    "xflow-xtension[test]",
    "xflow-xtensorflow[test]",
    "xflow-xtorch[test]",
    "xflow-xtorch_extensions[test]",
    "xflow-xtorch_usecases[test]",
    "xflow-xusecases[test]",
    {include-group = "xflow_release"},
]
xflow_dev = [
    {include-group = "xflow_release"},
    {include-group = "xflow_test"},
    {include-group = "dev"},
]
qnn_sdk = [
    "packaging",
    "pyyaml",
    "pandas",
    "lxml",
    "onnxsim==0.4.36+alliance1",
    "flatbuffers",
    "transformers",
]
test = [
    "pytest",
    "pytest-asyncio",
    "pytest-benchmark",
    "pytest-cov",
    "pytest-forked",
    "pytest-mock",
    "pytest-socket",
    "pytest-timeout",
    "pytest-xdist",
    "unittest-xml-reporting",
    "findimports",
    "astroid",
    "pylint",
]
release = [
    {include-group = "train"},
    {include-group = "qnn_sdk"},
    {include-group = "test"},
]
docs = [
    "markdown-exec",
    "mkdocs-drawio-exporter",
    "mkdocs-gen-files",
    "mkdocs-jupyter",
    "mkdocs-literate-nav",
    "mkdocs-material",
    "mkdocstrings-python",
]
set_me_up = [
    "click",
    "packaging>=21.3",
    "requests>=2.25.1",
    "pathspec",
    "pyyaml",
    "tomli",
    "uv",
    "urllib3>=1.26.5",
]
static_code_analysis = [
    # static code analysis
    "gitpython",
    "mypy==1.15.0",
    "pre-commit~=4.0.1",
    "pre-commit-hooks~=5.0.0",
    "pylint~=3.3.3",
    "ruff",
    "pyright",

    "types-aiofiles~=23.2",
    "types-antlr4-python3-runtime",
    "types-beautifulsoup4",
    "types-bleach",
    "types-cachetools",
    "types-cffi",
    "types-colorama",
    "types-decorator",
    "types-defusedxml",
    "types-Deprecated",
    "types-docker",
    "types-entrypoints",
    "types-greenlet",
    "types-humanfriendly",
    "types-jmespath",
    "types-markdown",
    "types-networkx",
    "types-oauthlib",
    "types-paramiko",
    "types-pexpect",
    "types-protobuf~=3.20.3",
    "types-psutil",
    "types-pyasn1",
    "types-pycurl",
    "types-pyflakes",
    "types-pygments",
    "types-pyopenssl",
    "types-python-dateutil",
    "types-python-jose",
    "types-pytz",
    "types-PyYAML",
    "types-regex",
    "types-requests",
    "types-requests-oauthlib",
    "types-send2trash",
    "types-setuptools",
    "types-shapely",
    "types-six",
    "types-tabulate",
    "types-tensorflow<2.17",        # NOTE: keep this in sync or close to our current TF version
    "types-toml",
    "types-xmltodict",

    # needed by the custom pyright_environments pre-commit githook
    "tomli",
]
build = [
    # we need those to build the wheels for submission
    "pip",
    "hatchling",
    "hatch-requirements-txt",
    "editables",
]
dev = [
    # sequence_runner.py requirements
    "imageio[pyav]",
    "plutocas",

    # needed by the custom pyright_environments pre-commit githook
    "tomli",

    # for visualizing graphs
    "netron",

    {include-group = "build"},
    {include-group = "release"},
    {include-group = "docs"},
    {include-group = "set_me_up"},
    {include-group = "static_code_analysis"},
]
loomy = [
    "pydantic-settings", # warper-package depends on it but does not model this dependency.
    "xflow-data_formats",
    "xflow-loomy",
    "xflow-loomy_usecases",
    "xflow-azure_tools",
    "xflow-xcontract",
]
nvidia = [
    "nvidia-cublas-cu12 ~= 12.3.0",
    "nvidia-cuda-cupti-cu12 ~= 12.3.0",
    "nvidia-cuda-nvcc-cu12 ~= 12.3.0",
    "nvidia-cuda-nvrtc-cu12 ~= 12.3.0",
    "nvidia-cuda-runtime-cu12 ~= 12.3.0",
    "nvidia-cudnn-cu12 ~= *******",
    "nvidia-cufft-cu12",
    "nvidia-curand-cu12",
    "nvidia-cusolver-cu12==11.5.4.101",
    "nvidia-cusparse-cu12==12.2.0.103",
    "nvidia-cusparselt-cu12",
    "nvidia-nccl-cu12 == 2.21.5",
    "nvidia-nvjitlink-cu12 ~= 12.3.0",
    "nvidia-nvjpeg-cu12 ~= 12.3.0",
    "nvidia-cufile-cu12",
    "tensorrt==8.6.1",
    "tensorrt-cu12-libs == 8.6.1",
]


[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.uv.sources]
xflow-cluster_cloud = { workspace = true }
xflow-conversion = { workspace = true }
xflow-conversion_contract = { workspace = true }
xflow-data_formats = { workspace = true }
xflow-data_test_framework = { workspace = true }
xflow-evil = { workspace = true }
xflow-loomy = { workspace = true }
xflow-loomy_usecases = { workspace = true }
xflow-pyper = { workspace = true }
xflow-pytest_lfs_tracker = { workspace = true }
xflow-release_report = { workspace = true }
xflow-xazure = { workspace = true }
xflow-azure_tools = { workspace = true }
xflow-databricks_tools = { workspace = true }
xflow-xcommon = { workspace = true }
xflow-xcontract = { workspace = true }
xflow-xhelpers = { workspace = true }
xflow-xreport = { workspace = true }
xflow-xtension = { workspace = true }
xflow-xtensorflow = { workspace = true }
xflow-xtorch = { workspace = true }
xflow-xtorch_extensions = { workspace = true }
xflow-xtorch_usecases = { workspace = true }
xflow-xusecases = { workspace = true }

[tool.uv.workspace]
members = [
    "cluster_cloud",
    "conversion",
    "conversion_contract",
    "data_formats",
    "data_test_framework",
    "evil",
    "loomy",
    "loomy_usecases",
    "pyper",
    "pytest_lfs_tracker",
    "release_report",
    "xazure",
    "xcloud/azure_tools",
    "xcloud/databricks_tools",
    "xcommon",
    "xcontract",
    "xhelpers",
    "xreport",
    "xtension",
    "xtensorflow",
    "xtorch",
    "xtorch_extensions",
    "xtorch_usecases",
    "xusecases",
]

[tool.uv]
default-groups = ["azure", "train", "xflow"]
environments = ["sys_platform == 'linux' and platform_machine == 'x86_64'"]

find-links = [
    "https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/",
    "https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/",
    "https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/",
    "https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/tensorflow2.18.1/",
    "https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/torch2.7.0/",
]

constraint-dependencies = [
    "horovod @ https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/tensorflow2.18.1/horovod-0.28.1-cp310-cp310-linux_x86_64.whl",
    "pycuda @ https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/pycuda-2024.1.2-cp310-cp310-linux_x86_64.whl",
    "tensorflow @ https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/broadwell/tensorflow-2.18.1-cp310-cp310-linux_x86_64.whl",

    "torch @ https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/torch-2.7.0-cp310-cp310-linux_x86_64.whl",
    "torchvision @ https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/torchvision-0.22.0-cp310-cp310-linux_x86_64.whl",
    "aimet-onnx @ https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/torch2.7.0/aimet_onnx-2.9.0+cu123.alliance8-cp310-cp310-linux_x86_64.whl",
    "aimet-torch @ https://jfrog.ad-alliance.biz/artifactory/list/shared-generic-prod/vdeep/wheels/ubuntu22.04/cuda12.3/cudnn8.9.7/torch2.7.0/aimet_torch-2.9.0+cu123.alliance8-cp310-cp310-linux_x86_64.whl",


    # constraints for pip packages
    # this file does not cause any packages to be installed or not
    # but if installed, the version is constraint to this one

    # disable tensorflow-gpu
    "tensorflow-gpu==999999",

    # tensorflow-addons is deprecated and shall not be used anymore
    "tensorflow_addons==999999",

    # https://github.com/tensorflow/probability/releases
    "tensorflow-probability~=0.25.0",

    # https://github.com/tensorflow/io#tensorflow-version-compatibility
    "tensorflow-io~=0.37.1",

    # air files need to be re-generated for newer protobuf
    "protobuf<3.21",

    # PyOpenSSL <23.2 does not work with cryptography >42.0.0 (https://github.com/conda/conda/issues/13619)
    "pyOpenSSL>=23.3.0",

    # Later versions break tensorflow (tf 2.12+ has a similar pin now) https://github.com/tensorflow/tensorflow/issues/63548
    #  TypeError: this __dict__ descriptor does not support '_DictWrapper' objects
    "wrapt<1.15.0",

    "kognic_io>=2.5",
    "kognic-openlabel>=2.0",

    # Version 2.3.14 fixes timestamp validations for Alliance OLF files
    "bosch_openlabel==2.3.14+6948305",

    "azure-storage-blob>=12.19.0",

    # azureml sdk v1 depends on pkg_resources.extern which was removed in setuptools 71
    "setuptools<71",

    "isort~=5.13.0",
    "pre-commit~=4.0.1",
    "pylint~=3.3.3",

    # We are using onnxruntime-gpu and should never install the cpu version
    "onnxruntime==999999",

    # speed up lengthy searches
    "matplotlib>=3.10.0",
    "numpy>=1.23",
    "pandas>=2.2",

    # 1.5.0 drags in a broken strawberry version
    "fiftyone>=1.4,<1.5",

    # 1.29 was introduced in https://github.com/PACE-INT/xflow/pull/5317 and lead to auth issues originating from the msal cache
    # See https://github.com/AzureAD/microsoft-authentication-library-for-python/issues/719
    "msal>=1.25,<1.29",

    # 19.0.0 has issues when reading parquet tables
    # https://github.com/apache/arrow/issues/45283
    "pyarrow>=19.0.1",

    "mdm-sdk-mdd>=2.2.0",
    "mdm-sdk-tds>=2.0.3",

    "uv==0.7.18",

    "hungarian_cuda>=0.3.0",

    # need label patcher for old data
    "label-hierarchy<2.27",

    # causes issues with dassie
    # https://github.com/PACE-INT/xflow/actions/runs/15676647694/job/44168164467?pr=12863#step:10:879
    "deepspeed<0.17.1",
]

python-preference = "system"

[[tool.uv.index]]
name = "prod"
url = "https://jfrog.ad-alliance.biz/artifactory/api/pypi/shared-pypi-prod/simple"
publish-url = "https://jfrog.ad-alliance.biz/artifactory/api/pypi/shared-pypi-prod"
default = true

[[tool.uv.index]]
name = "dev"
url = "https://jfrog.ad-alliance.biz/artifactory/api/pypi/shared-pypi-dev/simple"
publish-url = "https://jfrog.ad-alliance.biz/artifactory/api/pypi/shared-pypi-dev"
explicit = true

[tool.mypy]
packages = ["xusecases"]
strict = true
ignore_missing_imports = true
disallow_subclassing_any = false
implicit_reexport = true
warn_return_any = false
disallow_untyped_decorators = false
non_interactive = true
install_types = true
explicit_package_bases = false
follow_imports = "silent"

[[tool.mypy.overrides]]
module = "azure.*"
ignore_errors = true

[tool.ruff]
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".landing_zone",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pycache__",
    "__pypackages__",
    "_build",
    "build",
    "devel",
    "dist",
    "install",
    "node_modules",
    "venv",
    "conversion/src/conversion/tensorrt/utils/thirdparty/onnx_tensorrt/tensorrt_engine.py",
    "data_formats/proto/data_formats/athena_inference_results/proto",
    "data_formats/src/data_formats/athena_inference_results/proto",
    "*.ipynb",
]

line-length = 120

target-version = "py310"

[tool.ruff.lint]
select = ["ALL"]

ignore = [
    "A005", # stdlib-module-shadowing: Allow to shadow Python standard-library module names.
    "ANN401",  # any-type: Allow Any because we need this flexibility in some cases.
    "ARG001",  # unused-function-argument: Hard to write OOP style code with it.
    "ARG002",  # unused-method-argument: Hard to write OOP style code with it.
    "ARG003",  # unused-class-method-argument: Hard to write OOP style code with it.
    "ARG004",  # unused-static-method-argument: Hard to write OOP style code with it.
    "ARG005",  # unused-lambda-argument: Hard to write OOP style code with it.
    "B019",    # We allow the use of lru_cache assuming that users have in mind that it may lead to memory leaks
    "B905",    # The strict argument of zip is not understood by tf's autograph in v2.9; maybe with later tf
    "COM812",  # missing-trailing-comma (incompatible with ruff format)
    "D202",    # no-blank-line-after-function (looks stupid)
    "ERA001",  # commented-out-code: Incompatible with ruff format
    "FIX001",  # line-contains-fixme: Allow FIXME because we have TD forcing their format.
    "FIX002",  # line-contains-todo: Allow TODO because we have TD forcing their format.
    "FIX004",  # line-contains-hack: Allow HACK because we have TD forcing their format.
    "G004",    # logging-f-string: Allow f-strings in logging calls.
    "ISC001",  # does not work with ruff format
    "N812",    # lowercase-imported-as-non-lowercase: Allow to follow torch conventions
    "PD002",   # pandas-use-of-inplace-argument
    "PLR2004", # magic-value-comparison: Too sensitive, as it flagged all comparisons with any number.
    "RET504",  # unnecessary-assign Allow since it makes code more readable in some cases.
    "RUF005",  # collection-literal-concatenation: Breaks addition of tensors with non tensors
    "S101",    # assert: Allow to use assertions in code
    "S602",    # subprocess-popen-with-shell-equals-true: Judged as unnecessary constraint and mutually exclusive with S603
    "S603",    # subprocess-without-shell-equals-true: Judged as unnecessary constraint and mutually exclusive with S602
    "S607",    # start-process-with-partial-path: Judged as unnecessary constraint
    "SIM108",  # if-else-block-instead-of-if-exp: Code becomes less readable in many cases
    "SIM112",  # uncapitalized-environment-variables: our azureml variables contain lower case
    "T201",    # print: Allow print statements
    "TC001",   # typing-only-first-party-import: Allow since this requires future imports and often makes code less readable
    "TC002",   # typing-only-third-party-import: Allow since this requires future imports and often makes code less readable
    "TC003",   # typing-only-standard-library-import: Allow since this requires future imports and often makes code less readable
    "TC006",   # Checks for unquoted type expressions in typing.cast() calls
    "TD002",   # missing-todo-author: We should not write author names into the code
    "TRY300",  # try-consider-else: Allow to return directly from try
    "UP007",   # Replacing Union[X, Y] with X | Y is not always safe since for some types | is not valid
    "UP038",   # Replacing (X, Y) in isinstance with X | Y is not always safe since for some types | is not valid
]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["D104"]
"**/generated_pydantic_models/*" = [
    "D101", #   undocumented-public-class
    "D102", #   undocumented-public-method
    "D107", #   undocumented-public-init
]
"**/tests/*" = [
    "PLR2004", # magic-value-comparison; allowed in tests
    "TID252",  # relative-imports: Allow since tests are not proper packages
]

# should be fixed with the next update of the converters
"xtension/src/xtension/azure_ct/data_cache/converter/vision/beacon/data_converter.py" = [
    "ANN204",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/blockage/label_converter.py" = [
    "S324",
    "FBT003",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/dyo/data_converter.py" = [
    "RUF012",
    "PLR0913",
    "FBT001",
    "FBT002",
    "ANN204",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/dyo/image_converter.py" = [
    "FBT001",
    "FBT002",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/dyo/yuv_image_converter.py" = [
    "FBT001",
    "FBT002",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/image/horizontal_flip.py" = [
    "RUF010",
    "RUF012",
    "FBT001",
    "FBT002",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/image/warped_yuv_converter.py" = [
    "FBT001",
    "FBT002",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/image/yuv_converter.py" = [
    "PLR2004",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/lane/data_converter.py" = [
    "RUF012",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/sod/pole/wrapped_converter.py" = [
    "EM101",
    "TD002",
    "TD003",
    "TD004",
    "TRY003",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/sod/road_symbol/wrapped_converter.py" = [
    "FBT001",
    "FBT002",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/sod/traffic_light/wrapped_converter.py" = [
    "PGH003",
    "PLR2004",
    "FBT001",
    "FBT002",
    "PLR0913",
    "FBT003",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/sod/traffic_sign/wrapped_converter.py" = [
    "EM102",
    "TD002",
    "TD003",
    "TRY002",
    "TRY003",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/visual_relationship/data_converter.py" = [
    "PLR0913",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/warping/annotation_warping.py" = [
    "RUF012",
    "FBT001",
    "FBT002",
]
"xtension/src/xtension/azure_ct/data_cache/converter/vision/warping/image_warping.py" = [
    "RUF012",
    "FBT001",
    "FBT002",
]
"xtension/src/xtension/azure_ct/data_cache/data_converter.py" = ["PLR2004"]
"xusecases/src/xusecases/lidar/common/data/update_data/label_converter.py" = [
    "PT018",
]
"xusecases/src/xusecases/lidar/common/data/update_data/tfrecord_pointcloud_converter.py" = [
    "PGH003",
    "ANN204",
    "FBT001",
    "FBT002",
]

# legacy / tensorflow specific
".build/*" = ["PTH"]
"cluster_cloud/*" = ["PTH"]
"conversion/*" = ["PTH"]
"conversion_contract/*" = ["PTH"]
"data_formats/*" = ["PTH"]
"data_test_framework/*" = ["PTH"]
"evil/*" = ["PTH"]
"githooks/*" = [
    "PTH",
    "TID252", # relative-imports: Allow since githooks is not a proper package
]
"release_report/*" = ["PTH"]
"xazure/*" = ["PTH"]
"xcommon/*" = ["PTH"]
"xreport/*" = ["PTH"]
"xtension/*" = ["PTH"]
"xtensorflow/*" = ["PTH"]
"xusecases/*" = ["PTH"]
"xtorch*/**/__init__.py" = ["F401"]
"data_formats/**/__init__.py" = ["F401"]

# function-call-in-dataclass-default-argument, unproblematic for model configs, which are only initialized once
"xusecases/src/xusecases/vision/models/pace_general/release/model_configs/*" = [
    "RUF009",
]

[tool.ruff.lint.flake8-import-conventions.extend-aliases]
"torch.nn" = "nn"
"torch.nn.functional" = "F"

[tool.ruff.lint.pydocstyle]
convention = "google"

[tool.ruff.lint.mccabe]
max-complexity = 10

[tool.ruff.lint.pylint]
max-args = 9

[tool.ruff.lint.isort]

known-first-party = ["xaz", "azure_tools"]

known-third-party = ["label_set"]

[tool.pyright]
typeCheckingMode = "standard" # "strict"
pythonVersion = "3.10"
pythonPlatform = "Linux"
reportMissingImports = true
reportMissingTypeStubs = false
reportMissingTypeArgument = true # mypy reports missing type arguments by default, pyright only with "strict"
exclude = [
    "**/joint_box_format",
    "**/athena_inference_results/proto",
    ".landing_zone",
    ".venv",
]
ignore = [
    "xusecases", # Legacy code, checked by mypy on commit
]
executionEnvironments = [
    { "root" = "cluster_cloud" },
    { "root" = "conversion" },
    { "root" = "conversion_contract" },
    { "root" = "data_formats" },
    { "root" = "data_test_framework" },
    { "root" = "evil" },
    { "root" = "loomy", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "pyper", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "pytest_lfs_tracker", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "loomy_usecases", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "release_report" },
    { "root" = "xazure" },
    { "root" = "xcommon" },
    { "root" = "xreport" },
    { "root" = "xtension" },
    { "root" = "xtensorflow" },
    { "root" = "xcontract", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "xhelpers", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "xcloud/azure_tools", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "xcloud/databricks_tools", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "xtorch", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "xtorch_extensions", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "xtorch_usecases", typeCheckingMode = "strict", deprecateTypingAliases = true, reportInvalidTypeForm = "error", reportOperatorIssue = "error", reportPropertyTypeMismatch = "error", reportImportCycles = "warning", reportUninitializedInstanceVariable = "error", reportShadowedImports = "error", reportUnnecessaryTypeIgnoreComment = "error" },
    { "root" = "xtutorial" },
    { "root" = "xusecases", typeCheckingMode = "off" },                                                                                                                                                                                                                                                                                                                # Legacy code, checked by mypy on commit
    { "root" = ".build" },
]
# cannot use pyright line ignores as long as mypy is active, so disabling unfixable ones globally for now
reportInvalidTypeForm = false    # issue with tensorflow variables used as types (newer tf would have real types)
reportOperatorIssue = false      # since old tf has no type hints, operators are not recognized
reportPrivateImportUsage = false # some packages do not export some types, example onnx.helper.ValueInfoProto
