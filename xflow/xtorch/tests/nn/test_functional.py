"""Tests functional code."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


import os
from typing import Any, NamedTuple

import numpy as np
import pytest
import torch

from xtorch.nn.functional import (
    ReductionOp,
    crop_to_half,
    expand,
    inverse_sigmoid,
    masked_softmax,
    pad_to_double,
    scatter_nd_reduce,
    slice_structure,
    softmax_cross_entropy_with_logits,
    stack_structures,
    tensor_scatter_nd_max,
)
from xtorch.testing import run_in_subprocess


class TestScatterNdReduce:
    """Tests for the scatter_nd_reduce function."""

    @pytest.mark.parametrize(
        ("spatial_shape", "feature_shape"),
        [
            ((2, 3), ()),
            ((2, 3), (1,)),
            ((2, 3), (4,)),
            ((2, 3), (4, 5)),
            ((1,), ()),
            ((7,), (2,)),
            ((7,), (4, 5)),
        ],
    )
    def test_smoketest_with_different_input_shapes(
        self, spatial_shape: tuple[int, ...], feature_shape: tuple[int, ...], device: str
    ) -> None:
        """Smoke test."""

        # GIVEN inputs of varying suitable shapes
        count = max(1, int(np.prod(spatial_shape) // 2))
        target = torch.zeros((*spatial_shape, *feature_shape), device=device)
        indices = torch.stack([torch.randint(0, size, size=(count,), device=device) for size in spatial_shape], dim=-1)
        updates = torch.ones((count, *feature_shape), device=device)

        # WHEN scattered
        scatter_nd_reduce(target, indices, updates, "amax")

        # THEN there is no error and the target contains some values
        assert torch.count_nonzero(target)

    def test_duplicated_points_2d(self, device: str) -> None:
        """Test scattering with duplicated points in different positions."""

        # GIVEN test input with two-dimensional space, scalar features and duplicated points.
        target = torch.zeros((2, 3), device=device)
        indices = torch.tensor(
            [
                [0, 1],
                [0, 1],
                [0, 1],
                [1, 0],
                [1, 0],
                [1, 0],
            ],
            dtype=torch.long,
            device=device,
        )
        updates = torch.tensor([2.0, 3.0, 1.0, 20.0, 30.0, 10.0], device=device)

        # WHEN scattered
        scatter_nd_reduce(target, indices, updates, "amax")

        # THEN the reduction is applied and the values are at the desired location.
        torch.testing.assert_close(target, expected=torch.tensor([[0, 3.0, 0.0], [30.0, 0.0, 0.0]], device=device))

    @pytest.mark.parametrize("reduction", ["amax", "sum"])
    def test_reductions(self, reduction: ReductionOp, device: str) -> None:
        """Test reduction option."""

        # GIVEN inputs with duplicated points which have different features.
        target = torch.zeros((1,), device=device)
        indices = torch.zeros((3, 1), dtype=torch.long, device=device)
        updates = torch.tensor([1.0, 2.0, 3.0], device=device)

        # WHEN scattered
        scatter_nd_reduce(target, indices, updates, reduction)

        # THEN the desired reduction is correctly applied
        if reduction == "amax":
            assert target.squeeze().item() == updates.amax().item()
        else:
            assert target.squeeze().item() == updates.sum().item()

    def test_include_self(self, device: str) -> None:
        """Check that the target tensor is included in the reduction."""

        # GIVEN a non-zero target tensor and suitable updates
        target = torch.ones((1,), device=device)
        indices = torch.zeros((1, 1), dtype=torch.long, device=device)
        updates = torch.tensor([0.5], device=device)

        # WHEN scattered with "amax" reduction
        scatter_nd_reduce(target, indices, updates, "amax")

        # THEN the target is unchanged because the update value was smaller
        assert target.squeeze().item() == 1.0


def test_sigmoid_inverse_sigmoid(device: str) -> None:
    """Test inverse sigmoid function."""
    torch.manual_seed(0)
    # GIVEN a tensor
    x = torch.randn(100, device=device) * 2
    # WHEN the inverse sigmoid function is applied to a sigmoid tensor
    y = inverse_sigmoid(x.sigmoid())
    # THEN the original tensor is recovered
    torch.testing.assert_close(x, y)


def test_inverse_sigmoid_sigmoid(device: str) -> None:
    """Test inverse sigmoid function."""
    torch.manual_seed(0)
    # GIVEN a tensor
    x = torch.rand(100, device=device)
    # WHEN the sigmoid function is applied to an inverse sigmoid tensor
    y = inverse_sigmoid(x).sigmoid()
    # THEN the original tensor is recovered
    torch.testing.assert_close(x, y)


class TestSoftmaxCrossEntropyWithLogits:
    """Tests for the softmax cross entropy with logits function."""

    @pytest.fixture
    def labels_and_logits(self, device: str) -> tuple[torch.Tensor, torch.Tensor]:
        """Create test labels and logits for testing loss functions."""
        labels = torch.tensor([[0, 1, 0], [1, 0, 0]], dtype=torch.float32, device=device)
        logits = torch.tensor([[1.0, 10.0, 3.0], [0.5, 1.0, 1.5]], dtype=torch.float32, device=device)
        return labels, logits

    @pytest.fixture
    def test_data_float16(
        self,
        device: str,
        labels_and_logits: tuple[torch.Tensor, torch.Tensor],
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Create test data for the weighted softmax cross entropy with logits with float16 data type."""
        labels, logits = labels_and_logits
        weights = torch.tensor([2.0, 1.0, 1.0], dtype=torch.float16, device=device)
        return labels.to(dtype=torch.float16), logits.to(dtype=torch.float16), weights

    @pytest.fixture
    def test_data_float32(
        self,
        device: str,
        labels_and_logits: tuple[torch.Tensor, torch.Tensor],
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Create test data for the weighted softmax cross entropy with logits with float32 data type."""
        labels, logits = labels_and_logits
        weights = torch.tensor([2.0, 1.0, 1.0], dtype=torch.float32, device=device)
        return labels, logits, weights

    @pytest.fixture
    def test_data_float64(
        self,
        device: str,
        labels_and_logits: tuple[torch.Tensor, torch.Tensor],
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Create test data for the weighted softmax cross entropy with logits with float64 data type."""
        labels, logits = labels_and_logits
        weights = torch.tensor([2.0, 1.0, 1.0], dtype=torch.float64, device=device)
        return labels.to(dtype=torch.float64), logits.to(dtype=torch.float64), weights

    @pytest.mark.checks_torch_against_tensorflow
    @run_in_subprocess()
    def test_weighted_softmax_cross_entropy_with_logits_equals_tf_function_with_equal_weights(
        self,
        device: str,
        test_data_float32: tuple[torch.Tensor, torch.Tensor, torch.Tensor],
    ) -> None:
        """Test that the weighted loss with equal weights equals the tf softmax cross entropy loss."""
        os.environ["TF_FORCE_GPU_ALLOW_GROWTH"] = "true"
        tf = pytest.importorskip("tensorflow")

        # GIVEN labels, logits, and equal class weights
        labels, logits, _ = test_data_float32
        labels_tf = tf.constant(labels.cpu().numpy(), dtype=tf.float32)
        logits_tf = tf.constant(logits.cpu().numpy(), dtype=tf.float32)
        equal_weights = torch.tensor([1.0, 1.0, 1.0], dtype=torch.float32, device=device)

        # WHEN computing the weighted softmax cross entropy with equal weights
        result = softmax_cross_entropy_with_logits(labels, logits, weights=equal_weights)

        # THEN the result should match the normal softmax cross entropy loss
        expected_result_tf = torch.tensor(
            tf.nn.softmax_cross_entropy_with_logits(labels_tf, logits_tf).numpy(), device=device
        )
        torch.testing.assert_close(result, expected_result_tf)

    def test_weighted_softmax_cross_entropy_with_logits_returns_expected_result_with_supported_dtypes(
        self,
        device: str,
        test_data_float16: tuple[torch.Tensor, torch.Tensor, torch.Tensor],
        test_data_float32: tuple[torch.Tensor, torch.Tensor, torch.Tensor],
        test_data_float64: tuple[torch.Tensor, torch.Tensor, torch.Tensor],
    ) -> None:
        """Test that the weighted softmax cross entropy with logits returns the expected result."""
        # GIVEN test labels, logits, and class weights in float16, float32, and float64
        # WHEN computing the weighted softmax cross entropy
        result_float16 = softmax_cross_entropy_with_logits(
            test_data_float16[0], test_data_float16[1], weights=test_data_float16[2]
        )
        result_float32 = softmax_cross_entropy_with_logits(
            test_data_float32[0], test_data_float32[1], weights=test_data_float32[2]
        )
        result_float64 = softmax_cross_entropy_with_logits(
            test_data_float64[0], test_data_float64[1], weights=test_data_float64[2]
        )

        # THEN the result should match the expected result
        expected_result = torch.tensor([1.034756e-03, 3.360539], device=device)
        torch.testing.assert_close(result_float64, expected_result.to(torch.float64))
        torch.testing.assert_close(result_float32, expected_result.to(torch.float32))
        torch.testing.assert_close(result_float16, expected_result.to(torch.float16), rtol=1e-1, atol=0)

    def test_weighted_softmax_cross_entropy_with_logits_different_axes(
        self,
        device: str,
        test_data_float32: tuple[torch.Tensor, torch.Tensor, torch.Tensor],
    ) -> None:
        """Test that the loss returns the expected result when using different axes."""
        # GIVEN test labels, logits, and class weights
        labels, logits, weights = test_data_float32

        # WHEN computing the weighted softmax cross entropy with axis -1
        axis = -1
        result_axis_0 = softmax_cross_entropy_with_logits(labels, logits, axis, weights)

        # THEN the result should match the expected result
        expected_result = torch.tensor([1.034676e-03, 3.360539], device=device)
        torch.testing.assert_close(result_axis_0, expected_result)

        # WHEN computing the weighted softmax cross entropy with axis 0
        axis = 0
        labels = labels.permute(1, 0)
        logits = logits.permute(1, 0)
        result_axis_1 = softmax_cross_entropy_with_logits(labels, logits, axis, weights)

        # THEN the result should match the expected result
        torch.testing.assert_close(result_axis_1, expected_result)

    def test_weighted_softmax_cross_entropy_with_logits_with_3d_shape(self, device: str) -> None:
        """Test that the loss returns the expected result when using 3D shaped inputs."""
        # GIVEN test labels, logits, and class weights with 3D shape
        labels_3d = torch.tensor(
            [[[0.0, 1.0, 0.0], [1.0, 0.0, 0.0]], [[0.0, 0.0, 1.0], [1.0, 0.0, 0.0]]], device=device
        )
        logits_3d = torch.tensor(
            [[[1.0, 2.0, 3.0], [0.5, 1.0, 1.5]], [[0.5, 1.0, 1.5], [1.0, 2.0, 3.0]]], device=device
        )
        weights_3d = torch.tensor([1.0, 2.0, 1.0], device=device)

        # WHEN computing the weighted softmax cross entropy with 3D shape
        result_3d = softmax_cross_entropy_with_logits(labels_3d, logits_3d, weights=weights_3d)

        # THEN the result should match the expected result
        expected_result_3d = torch.tensor([[2.815212, 1.68027], [0.68027, 2.407606]], device=device)
        torch.testing.assert_close(result_3d, expected_result_3d)


@pytest.fixture
def dummy_tensor(device: str) -> torch.Tensor:
    """Creates a dummy tensor for testing."""
    return torch.zeros((2, 3, 4, 4), dtype=torch.float32, device=device)


def test_tensor_scatter_nd_max_basic(dummy_tensor: torch.Tensor, device: str) -> None:
    """Test basic scatter update with maximum operation."""

    # GIVEN: a dummy tensor, indices, and updates
    sys_device = torch.device(device=device)
    indices = torch.tensor([[0, 1, 2, 2], [1, 2, 1, 3]], dtype=torch.long, device=device)
    updates = torch.tensor([5.0, 7.0], dtype=torch.float32, device=device)
    expected_output = dummy_tensor.clone()
    expected_output[0, 1, 2, 2] = 5.0
    expected_output[1, 2, 1, 3] = 7.0

    # WHEN: tensor_scatter_nd_max is called
    output = tensor_scatter_nd_max(tensor=dummy_tensor, indices=indices, updates=updates)

    # THEN: output should match the expected tensor
    assert output.device.type == sys_device.type, f"Expected device {sys_device}, but got {output.device}."
    torch.testing.assert_close(output, expected_output, rtol=0.0, atol=0.0, msg="Basic scatter max update failed!")


def test_tensor_scatter_nd_max_overwrite(dummy_tensor: torch.Tensor, device: str) -> None:
    """Ensure max operation is applied correctly instead of overwriting."""

    # GIVEN: a dummy tensor with pre-existing values, indices, and updates
    sys_device = torch.device(device=device)
    dummy_tensor[0, 1, 2, 2] = 4.0  # Pre-existing value
    indices = torch.tensor([[0, 1, 2, 2]], dtype=torch.long, device=device)
    updates = torch.tensor([6.0], dtype=torch.float32, device=device)  # Should update (max of 4.0 and 6.0)
    expected_output = dummy_tensor.clone()
    expected_output[0, 1, 2, 2] = 6.0  # max(4.0, 6.0)

    # WHEN: tensor_scatter_nd_max is called
    output = tensor_scatter_nd_max(tensor=dummy_tensor, indices=indices, updates=updates)

    # THEN: output should match the expected tensor
    assert output.device.type == sys_device.type, f"Expected device {sys_device}, but got {output.device}."
    assert torch.equal(output, expected_output), "Max operation is not applied correctly!"


def test_tensor_scatter_nd_max_out_of_bounds(dummy_tensor: torch.Tensor, device: str) -> None:
    """Ensure function does not crash when given out-of-bounds indices."""

    # GIVEN: a dummy tensor, invalid indices, and updates
    sys_device = torch.device(device)
    indices = torch.tensor([[0, 1, 5, 5], [1, 2, -1, 0]], dtype=torch.long, device=device)  # Invalid indices
    updates = torch.tensor([5.0, 3.0], dtype=torch.float32, device=device)

    # WHEN: tensor_scatter_nd_max is called
    output = tensor_scatter_nd_max(dummy_tensor, indices, updates)

    # THEN: no changes should be made since indices are invalid
    assert torch.equal(output, dummy_tensor), "Out-of-bounds indices should be ignored!"
    assert output.device.type == sys_device.type, f"Expected device {sys_device}, but got {output.device}."


def test_tensor_scatter_nd_max_empty_update(dummy_tensor: torch.Tensor, device: str) -> None:
    """Ensure function handles empty updates correctly."""

    # GIVEN: a dummy tensor, empty indices, and empty updates
    sys_device = torch.device(device)
    indices = torch.empty((0, 4), dtype=torch.long, device=device)  # No indices provided
    updates = torch.empty((0,), dtype=torch.float32, device=device)

    # WHEN: tensor_scatter_nd_max is called
    output = tensor_scatter_nd_max(dummy_tensor, indices, updates)

    # THEN: no changes should be made since updates are empty
    assert output.device.type == sys_device.type, f"Expected device {sys_device}, but got {output.device}."
    assert torch.equal(output, dummy_tensor), "Empty updates should result in no changes!"


def test_tensor_scatter_nd_max_large_tensor(device: str) -> None:
    """Test efficiency on a large tensor."""

    # GIVEN: a large tensor, random indices, and updates
    sys_device = torch.device(device)
    large_tensor = torch.zeros((4, 8, 32, 32), dtype=torch.float32, device=device)
    indices = torch.randint(0, 32, (1000, 4), dtype=torch.long, device=device)
    updates = torch.rand((1000,), dtype=torch.float32, device=device)

    # WHEN: tensor_scatter_nd_max is called
    output = tensor_scatter_nd_max(large_tensor, indices, updates)

    # THEN: output should match the expected tensor
    assert output.device.type == sys_device.type, f"Expected device {sys_device}, but got {output.device}."
    assert output.shape == large_tensor.shape, "Large tensor shape mismatch!"
    assert torch.any(output > 0), "Some updates should have been applied!"


class TestTensorStructureOperations:
    """Tests operations on structures of tensors."""

    class Sample(NamedTuple):
        """Sample class."""

        x: torch.Tensor
        y: torch.Tensor

    @pytest.fixture
    def sample(self, device: str) -> Sample:
        """Sample data."""
        return TestTensorStructureOperations.Sample(
            torch.zeros((3, 4), device=device), torch.ones((3, 4), device=device)
        )

    @pytest.mark.parametrize(("dim", "expected_shape"), [(0, (2, 3, 4)), (-1, (3, 4, 2))])
    def test_stack_structures(self, sample: Sample, device: str, dim: int, expected_shape: tuple[int, ...]) -> None:
        """Tests stacking."""
        # GIVEN items
        # WHEN stack_structures is applied
        ret = stack_structures([sample, sample], dim=dim)
        # THEN type is preserved, device matches and shapes match expectations
        assert type(ret) is type(sample)
        torch.testing.assert_close(ret.x, torch.zeros(expected_shape, device=device))
        torch.testing.assert_close(ret.y, torch.ones(expected_shape, device=device))

    @pytest.mark.parametrize(("slice_args", "expected_shape"), [((1, 2), ()), (slice(2), (2, 4)), (([1, 2], 3), (2,))])
    def test_slice_structure(self, sample: Sample, slice_args: Any, expected_shape: tuple[int, ...]) -> None:
        """Tests slicing."""
        # GIVEN a named tuple of tensors
        # WHEN slice_structure is applied
        ret = slice_structure(sample).__getitem__(slice_args)
        # THEN type is preserved, device matches and shapes match expectations
        assert type(ret) is type(sample)
        assert ret.x.shape == expected_shape
        assert ret.y.shape == expected_shape
        assert ret.x.device == sample.x.device
        assert ret.y.device == sample.y.device

    def test_slice_syntax(self, sample: Sample) -> None:
        """Test slicing syntax."""
        ret = slice_structure(sample)[1, [0, 3]]
        assert ret.x.shape == (2,)
        assert ret.y.shape == (2,)


class CropToHalfTests:
    """Tests for the `crop_to_half` function."""

    @pytest.fixture(autouse=True)
    def set_seed(self) -> None:
        """Fixture to set the seed for reproducibility before each test."""
        torch.manual_seed(42)

    def test_crop_to_half_no_offset(self) -> None:
        """Test cropping a tensor to half its size without any offset."""
        # GIVEN a random input tensor
        random_input = torch.randn(2, 32, 8, 16)

        # WHEN cropping it to half its size
        cropped_input = crop_to_half(random_input)

        # THEN the cropped tensor should have half the height and width
        assert cropped_input.shape == (2, 32, 4, 8)

    def test_crop_to_half_with_x_and_y_offset(self) -> None:
        """Test cropping a tensor to half its size with x and y offset."""
        # GIVEN an input tensor
        input_tensor = torch.ones(2, 32, 8, 16)
        input_tensor[:8, :, -2:, :] = 0.0

        # WHEN cropping it to half its size with x and y offset
        cropped_tensor = crop_to_half(input_tensor, x_rel_offset=0.5, y_rel_offset=0.25)

        # THEN the cropped tensor should have half the height and width with expected values
        assert cropped_tensor.shape == (2, 32, 4, 8)
        assert torch.all(cropped_tensor == 1.0)

    def test_crop_to_half_invalid_offsets(self) -> None:
        """Test cropping a tensor to half its size with invalid x or y offset."""
        # GIVEN a random input tensor
        random_input = torch.randn(2, 32, 8, 16)

        # WHEN trying to crop it to half its size with invalid x offset
        # THEN an AssertionError should be raised
        with pytest.raises(AssertionError):
            crop_to_half(random_input, x_rel_offset=-1.6, y_rel_offset=0.25)

        # WHEN trying to crop it to half its size with invalid y offset
        # THEN an AssertionError should be raised
        with pytest.raises(AssertionError):
            crop_to_half(random_input, x_rel_offset=1.6, y_rel_offset=0.75)


class PadToDoubleTests:
    """Tests for the `pad_to_double` function."""

    @pytest.fixture(autouse=True)
    def set_seed(self) -> None:
        """Fixture to set the seed for reproducibility before each test."""
        torch.manual_seed(42)

    def test_pad_to_double_no_offset(self) -> None:
        """Test padding a tensor to double its size without any offset."""
        # GIVEN a random input tensor
        random_input = torch.randn(2, 32, 8, 16)

        # WHEN padding it to double its size
        padded_tensor = pad_to_double(random_input)

        # THEN the padded tensor should have double the height and width
        assert padded_tensor.shape == (2, 32, 16, 32)

    def test_pad_to_double_with_x_and_y_offset_values(self) -> None:
        """Test padding a tensor to double its size with x and y offset values."""
        # GIVEN an input tensor
        random_input = torch.ones(2, 32, 6, 8)

        # WHEN padding it to double its size with x and y offset values
        padded_tensor = pad_to_double(random_input, x_rel_offset=0.25, y_rel_offset=0.25)

        # THEN the padded tensor should have double the height and width with expected values
        assert padded_tensor.shape == (2, 32, 12, 16)
        assert torch.all(padded_tensor[:, :, 3:9, 4:12] == 1.0)
        assert torch.all(padded_tensor[:, :, :3, :] == 0.0)
        assert torch.all(padded_tensor[:, :, 9:, :] == 0.0)
        assert torch.all(padded_tensor[:, :, :, :4] == 0.0)
        assert torch.all(padded_tensor[:, :, :, 12:] == 0.0)

    def test_pad_to_double_with_invalid_offsets(self) -> None:
        """Test padding a tensor to double its size with invalid x and y offset."""
        # GIVEN a random input tensor
        random_input = torch.randn(2, 32, 8, 16)

        # WHEN trying to pad it to double its size with invalid x offset
        # THEN an AssertionError should be raised
        with pytest.raises(AssertionError):
            pad_to_double(random_input, x_rel_offset=0.6, y_rel_offset=0.25)

        # WHEN trying to pad it to double its size with invalid y offset
        # THEN an AssertionError should be raised
        with pytest.raises(AssertionError):
            pad_to_double(random_input, x_rel_offset=0.25, y_rel_offset=-0.25)


def test_masked_softmax(device: str) -> None:
    """Tests the masked softmax operation."""
    # GIVEN some random test data
    test_data = torch.randn(3, 25, 4, device=device)
    valid_elements = torch.ones_like(test_data, dtype=torch.bool, device=device)
    valid_elements[1, 1, 1] = False
    valid_elements[2, 2, 2] = False

    # GIVEN the result of the masked softmax as it is computed within the pytorch attention
    torch_masked_softmax_data = test_data
    torch_masked_softmax_data[~valid_elements] = -torch.inf
    torch_softmax_result = torch.softmax(torch_masked_softmax_data, dim=-1)

    # WHEN the masked softmax implementation is called with the testdata
    masked_softmax_result = masked_softmax(test_data, valid_elements, dim=-1)

    # THEN both results are expected to be the same
    torch.testing.assert_close(masked_softmax_result, torch_softmax_result)


def test_expand() -> None:
    """Tests the expand function."""

    # GIVEN a tensor
    x = torch.tensor([[1], [2]])  # Shape (2, 1)

    # GIVEN the desired shape for expansion
    expanded_shape = (2, 3)  # Desired shape

    # WHEN the expand function is called with the shape as a tuple
    expanded_tensor = expand(x, size=expanded_shape)

    # THEN the expanded tensor should have the desired shape
    assert expanded_tensor.shape == expanded_shape
    assert expanded_tensor.tolist() == [[1, 1, 1], [2, 2, 2]]
    assert expanded_tensor._base is x  # noqa: SLF001

    # WHEN the expand function is called with the shape as variadic arguments
    expanded_tensor = expand(x, *expanded_shape)

    # THEN the expanded tensor should have the desired shape
    assert expanded_tensor.shape == expanded_shape
    assert expanded_tensor.tolist() == [[1, 1, 1], [2, 2, 2]]
    assert expanded_tensor._base is x  # noqa: SLF001
