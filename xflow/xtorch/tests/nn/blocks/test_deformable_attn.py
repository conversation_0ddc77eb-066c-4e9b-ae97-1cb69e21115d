"""Tests for the MultiscaleDeformableAttention module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import pytest
import torch

from xtorch.nn.blocks.deformable_attn import MultiscaleDeformableAttention


def make_inputs(
    bs: int,
    n_queries: int,
    d_model: int,
    n_levels: int,
    hws: list[tuple[int, int]],
    device: str,
) -> tuple[
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
    torch.Tensor,
    list[torch.Tensor],
]:
    """Create random input tensors for MultiscaleDeformableAttention tests.

    Args:
        bs: Batch size.
        n_queries: Number of queries.
        d_model: Feature dimension.
        n_levels: Number of feature levels.
        hws: List of (height, width) tuples for each level.
        device: Device to create tensors on.

    Returns:
        Tuple containing:
            - query: (bs, n_queries, d_model)
            - reference_points: (bs, n_queries, n_levels, 4)
            - input_ftrs: (bs, sum(H*W), d_model)
            - input_spatial_shapes: (n_levels, 2)
            - input_padding_mask: list of (bs, H, W) bool tensors
    """
    query = torch.randn(bs, n_queries, d_model, device=device)
    reference_points = torch.rand(bs, n_queries, n_levels, 4, device=device)
    input_ftrs = torch.randn(bs, sum(h * w for h, w in hws), d_model, device=device)
    input_spatial_shapes = torch.tensor(hws, device=device)
    input_padding_mask = [torch.zeros(bs, h, w, dtype=torch.bool, device=device) for h, w in hws]
    return (
        query,
        reference_points,
        input_ftrs,
        input_spatial_shapes,
        input_padding_mask,
    )


def test_forward_output_shape(device: str) -> None:
    """Test that the output shape of MultiscaleDeformableAttention matches the expected shape."""
    # GIVEN: a MultiscaleDeformableAttention model and valid input tensors
    d_model = 256
    n_levels = 2
    n_heads = 8
    n_points = 4
    bs, n_queries = 2, 5
    hws = [(4, 4), (2, 2)]
    model = MultiscaleDeformableAttention(d_model, n_levels, n_heads, n_points).to(device)
    (
        query,
        reference_points,
        input_ftrs,
        input_spatial_shapes,
        input_padding_mask,
    ) = make_inputs(bs, n_queries, d_model, n_levels, hws, device=device)
    # WHEN: the model is called with the inputs
    output = model(
        query,
        reference_points,
        input_ftrs,
        input_spatial_shapes,
        input_padding_mask,
    )
    # THEN: the output shape should match (bs, n_queries, d_model)
    assert output.shape == (bs, n_queries, d_model)


def test_assertion_on_invalid_d_model() -> None:
    """Test that an AssertionError is raised if d_model is not divisible by n_heads."""
    # GIVEN: an invalid d_model that is not divisible by n_heads
    # WHEN: constructing the model
    # THEN: an AssertionError should be raised
    with pytest.raises(AssertionError):
        MultiscaleDeformableAttention(d_model=250, n_levels=2, n_heads=8, n_points=4)


def test_forward_without_padding_mask(device: str) -> None:
    """Test that the model works and produces correct output shape when no padding mask is provided."""
    # GIVEN: a MultiscaleDeformableAttention model and valid input tensors, with no padding mask
    d_model = 256
    n_levels = 2
    n_heads = 8
    n_points = 4
    bs, n_queries = 2, 5
    hws = [(4, 4), (2, 2)]
    model = MultiscaleDeformableAttention(d_model, n_levels, n_heads, n_points).to(device)
    (
        query,
        reference_points,
        input_ftrs,
        input_spatial_shapes,
        _,
    ) = make_inputs(bs, n_queries, d_model, n_levels, hws, device=device)
    # WHEN: the model is called with None as the padding mask
    output = model(
        query,
        reference_points,
        input_ftrs,
        input_spatial_shapes,
        None,
    )
    # THEN: the output shape should match (bs, n_queries, d_model)
    assert output.shape == (bs, n_queries, d_model)


def test_forward_with_different_batch_and_query(device: str) -> None:
    """Test that the model works with different batch and query sizes."""
    # GIVEN: a MultiscaleDeformableAttention model and input tensors with different batch and query sizes
    d_model = 128
    n_levels = 3
    n_heads = 4
    n_points = 2
    bs, n_queries = 1, 3
    hws = [(2, 2), (2, 2), (1, 1)]
    model = MultiscaleDeformableAttention(d_model, n_levels, n_heads, n_points).to(device)
    (
        query,
        reference_points,
        input_ftrs,
        input_spatial_shapes,
        input_padding_mask,
    ) = make_inputs(bs, n_queries, d_model, n_levels, hws, device=device)
    # WHEN: the model is called with the inputs
    output = model(
        query,
        reference_points,
        input_ftrs,
        input_spatial_shapes,
        input_padding_mask,
    )
    # THEN: the output shape should match (bs, n_queries, d_model)
    assert output.shape == (bs, n_queries, d_model)
