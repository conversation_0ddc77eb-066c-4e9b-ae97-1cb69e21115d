"""Tests for the MultiheadAttentionWithSkipConnection."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Final

import pytest
import torch

from xtorch.nn.blocks.sliced_multihead_attention import AttentionNonlinearityType, SlicedMultiheadAttention

BATCH: Final = 3
SEQ_LEN: Final = 10
EMBED_DIM: Final = 32
NUM_HEADS: Final = 4


def test_self_attention(*, device: str) -> None:
    """Tests that the SlicedMultiheadAttention forward pass works as self-attention."""
    # GIVEN a SlicedMultiheadAttention layer and an input tensor
    attention = SlicedMultiheadAttention(EMBED_DIM, NUM_HEADS, dropout=0.1)
    attention = attention.to(device)
    query = torch.randn(BATCH, SEQ_LEN, EMBED_DIM, device=device)

    # WHEN passing the input tensor as self-attention
    result = attention(query, query, query)

    # THEN the output tensor should have the expected shape
    assert result[0].shape == (BATCH, SEQ_LEN, EMBED_DIM)
    assert result[1].shape == (BATCH, SEQ_LEN, SEQ_LEN)


@pytest.mark.parametrize("attention_nonlinearity", ["softmax", "relu", "relu6", "hardswish"])
def test_self_attention_flash(*, device: str, attention_nonlinearity: AttentionNonlinearityType) -> None:
    """Tests that the SlicedMultiheadAttention forward pass returns the same output when using flash attention.

    NOTE: Only the softmax attention nonlinearity is supported by flash attention. The other non-linearities
    will use the manual implementation. The test is still valid to ensure the results are the same and the
    flash attention path is not accidentally used.
    """
    # GIVEN a SlicedMultiheadAttention layer and an input tensor
    attention = SlicedMultiheadAttention(
        EMBED_DIM, NUM_HEADS, dropout=0.0, attention_nonlinearity=attention_nonlinearity
    )
    attention = attention.to(device)
    attention.train()

    query = torch.randn(BATCH, SEQ_LEN, EMBED_DIM, device=device)

    # WHEN using flash attention and when using our manual implementation
    result_flash, _ = attention(query, query, query, need_weights=False)
    result_manual, _ = attention(query, query, query, need_weights=True)

    # THEN the output should be identical
    torch.testing.assert_close(result_flash, result_manual)


def test_cross_attention(*, device: str) -> None:
    """Tests that the SlicedMultiheadAttention forward pass works as cross-attention."""
    # GIVEN a SlicedMultiheadAttention layer and an input tensor
    attention = SlicedMultiheadAttention(EMBED_DIM, NUM_HEADS, dropout=0.1, average_attn_weights=False)
    attention = attention.to(device)
    query = torch.randn(BATCH, SEQ_LEN, EMBED_DIM, device=device)
    key = torch.randn(BATCH, SEQ_LEN * 2, EMBED_DIM, device=device)
    value = torch.randn(BATCH, SEQ_LEN * 2, EMBED_DIM, device=device)

    # WHEN passing the input tensor as cross-attention
    result = attention(query, key, value)

    # THEN the output tensor should have the expected shape
    assert result[0].shape == (BATCH, SEQ_LEN, EMBED_DIM)
    assert result[1].shape == (BATCH, NUM_HEADS, SEQ_LEN, SEQ_LEN * 2)


@pytest.mark.parametrize("attention_nonlinearity", ["softmax", "relu", "relu6", "hardswish"])
def test_cross_attention_flash(*, device: str, attention_nonlinearity: AttentionNonlinearityType) -> None:
    """Tests that the SlicedMultiheadAttention forward pass returns the same output when using flash attention.

    NOTE: Only the softmax attention nonlinearity is supported by flash attention. The other non-linearities
    will use the manual implementation. The test is still valid to ensure the results are the same and the
    flash attention path is not accidentally used.
    """
    # GIVEN a SlicedMultiheadAttention layer and an input tensor
    attention = SlicedMultiheadAttention(
        EMBED_DIM, NUM_HEADS, dropout=0.0, average_attn_weights=False, attention_nonlinearity=attention_nonlinearity
    )
    attention = attention.to(device)
    attention.train()
    query = torch.randn(BATCH, SEQ_LEN, EMBED_DIM, device=device)
    key = torch.randn(BATCH, SEQ_LEN * 2, EMBED_DIM, device=device)
    value = torch.randn(BATCH, SEQ_LEN * 2, EMBED_DIM, device=device)

    # WHEN using flash attention and when using our manual implementation
    result_flash, _ = attention(query, key, value, need_weights=False)
    result_manual, _ = attention(query, key, value, need_weights=True)

    # THEN the output should be identical
    torch.testing.assert_close(result_flash, result_manual)


@pytest.mark.parametrize("need_weights", [True, False])
@pytest.mark.parametrize("is_batched", [True, False])
@pytest.mark.parametrize("is_attention_mask_batched", [True, False])
@pytest.mark.parametrize("use_attention_mask", [True, False])
@pytest.mark.parametrize("use_padding_mask", [True, False])
def test_against_pytorch(
    device: str,
    *,
    need_weights: bool,
    is_batched: bool,
    is_attention_mask_batched: bool,
    use_attention_mask: bool,
    use_padding_mask: bool,
) -> None:
    """Tests the sliced multi head attention against pytorch."""
    torch.manual_seed(42)
    # GIVEN some random test data
    query = torch.randn(BATCH, SEQ_LEN, EMBED_DIM, device=device)
    key = torch.randn(BATCH, SEQ_LEN * 2, EMBED_DIM, device=device)
    value = torch.randn(BATCH, SEQ_LEN * 2, EMBED_DIM, device=device)

    if use_attention_mask:
        attn_mask = torch.zeros(size=(BATCH * NUM_HEADS, SEQ_LEN, SEQ_LEN * 2), dtype=torch.bool, device=device)
        attn_mask[0, 0, 1] = True
        attn_mask[0, 1, 0] = True
        attn_mask[2, 2, 2] = True
    else:
        attn_mask = None

    if use_padding_mask:
        key_padding_mask = torch.zeros(size=(BATCH, SEQ_LEN * 2), dtype=torch.bool, device=device)
        key_padding_mask[0, 2] = True
        key_padding_mask[1, 3] = True
    else:
        key_padding_mask = None

    if not is_batched:
        query = query[0]
        key = key[0]
        value = value[0]
        if attn_mask is not None:
            attn_mask = attn_mask[0]
        if key_padding_mask is not None:
            key_padding_mask = key_padding_mask[0]

    # GIVEN the pytorch MultiheadAttention
    torch_attention = torch.nn.MultiheadAttention(
        embed_dim=EMBED_DIM, num_heads=NUM_HEADS, dropout=0, bias=False, batch_first=True
    )
    torch_attention.to(device)

    # WHEN the SlicedMultiheadAttention is initialized with the same setting as the pytorch version
    sliced_attention = SlicedMultiheadAttention(
        embed_dim=EMBED_DIM,
        num_heads=NUM_HEADS,
        dropout=0,
        batch_first=True,
        is_batched=is_batched,
        is_attention_mask_batched=is_attention_mask_batched,
    )
    sliced_attention.to(device)

    # WHEN the SlicedMultiheadAttention weights are initialized with the same weight as the pytorch version
    actual_embedding_dim = EMBED_DIM // NUM_HEADS
    for i in range(NUM_HEADS):
        sliced_attention._q[i].weight = torch.nn.Parameter(  # noqa: SLF001
            data=torch_attention.in_proj_weight.data[i * actual_embedding_dim : (i + 1) * actual_embedding_dim, :]
        )
        sliced_attention._kk[i].weight = torch.nn.Parameter(  # noqa: SLF001
            data=torch_attention.in_proj_weight.data[
                EMBED_DIM + i * actual_embedding_dim : EMBED_DIM + (i + 1) * actual_embedding_dim, :
            ]
        )
        sliced_attention._v[i].weight = torch.nn.Parameter(  # noqa: SLF001
            data=torch_attention.in_proj_weight.data[
                2 * EMBED_DIM + i * actual_embedding_dim : 2 * EMBED_DIM + (i + 1) * actual_embedding_dim, :
            ]
        )

    sliced_attention._out.weight = torch.nn.Parameter(data=torch_attention.out_proj.weight.data)  # noqa: SLF001
    # need to set here to zero because with bias set to false the nn.MultiheadAttention has no bias at all
    sliced_attention._out.bias = torch.nn.Parameter(data=torch.zeros(size=(EMBED_DIM,), device=device))  # noqa: SLF001

    # WHEN both attention implementations are called with the test data
    if is_batched and not is_attention_mask_batched and attn_mask is not None:
        attn_mask = attn_mask[0]
    custom_out = sliced_attention(
        query, key, value, attn_mask=attn_mask, key_padding_mask=key_padding_mask, need_weights=need_weights
    )

    if is_batched and not is_attention_mask_batched and attn_mask is not None:
        # case where we have unbatched attention mask --> attention mask is same for all batches (required for pytorch
        # interface)
        attn_mask = attn_mask.unsqueeze(0).repeat(BATCH * NUM_HEADS, 1, 1)
    torch_res = torch_attention(
        query, key, value, attn_mask=attn_mask, key_padding_mask=key_padding_mask, need_weights=need_weights
    )

    # THEN the result is expected to be the same
    # torch version produces nan values, we want to make sure that not tooo many nan values are produced
    torch.testing.assert_close(custom_out[0], torch_res[0])
    if need_weights:
        torch.testing.assert_close(custom_out[1], torch_res[1])
