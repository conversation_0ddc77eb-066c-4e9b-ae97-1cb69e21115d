"""Tests for the deformable transformer module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""
import pytest
import torch

from xtorch.nn.heads.object_detection.deformable_transformer import (
    DeformableTransformer,
    DeformableTransformerDecoder,
    DeformableTransformerDecoderLayer,
    _get_clones,
)


@pytest.fixture
def dummy_inputs(
    device: str,
) -> tuple[list[torch.Tensor], list[torch.Tensor], list[torch.Tensor], torch.Tensor, torch.Tensor]:
    """Fixture that returns dummy input tensors for the DeformableTransformer.

    Returns:
        feature_maps: List of feature map tensors.
        masks: List of mask tensors.
        pos_embeds: List of positional embedding tensors.
        query_embed: Query embedding tensor.
    """
    # Given: dummy input tensors for the transformer
    batch_size = 2
    d_model = 16
    hws = [(8, 8), (4, 4), (2, 2)]
    feature_maps = [torch.randn(batch_size, d_model, h, w, device=device) for h, w in hws]
    masks = [torch.zeros(batch_size, h, w, dtype=torch.bool, device=device) for h, w in hws]
    pos_embeds = [torch.randn(batch_size, d_model, h, w, device=device) for h, w in hws]
    query_embed = torch.randn(1, d_model, device=device)
    query_pos = torch.randn(1, d_model, device=device)
    return feature_maps, masks, pos_embeds, query_embed, query_pos


def test_deformable_transformer_forward(
    dummy_inputs: tuple[list[torch.Tensor], list[torch.Tensor], list[torch.Tensor], torch.Tensor, torch.Tensor],
    device: str,
) -> None:
    """Test the forward pass of DeformableTransformer."""
    # Given: a DeformableTransformer model and dummy inputs
    feature_maps, masks, pos_embeds, query_embed, query_pos = dummy_inputs
    reference_points = torch.randn(1, 3, 4, device=device)
    model = DeformableTransformer(
        d_model=16,
        num_heads=2,
        num_encoder_layers=2,
        num_decoder_layers=2,
        dim_feedforward=32,
        num_feature_levels=3,
    ).to(device)
    # When: running the model forward
    hs, reference_points, inter_references = model(
        feature_maps, masks, pos_embeds, query_embed, reference_points, query_pos
    )
    # Then: outputs should be tensors with expected shapes
    assert isinstance(hs, torch.Tensor)
    assert isinstance(reference_points, torch.Tensor)
    assert isinstance(inter_references, torch.Tensor)
    assert hs.shape[0] == 1  # decoder output is stacked
    assert reference_points.shape[-1] == 4


def test_decoder_layer_forward(device: str) -> None:
    """Test the forward pass of DeformableTransformerDecoderLayer."""
    # Given: a DeformableTransformerDecoderLayer and input tensors
    layer = DeformableTransformerDecoderLayer(d_model=8, d_ffn=16, n_levels=1, n_heads=2, n_points=2).to(device)
    target = torch.randn(1, 5, 8, device=device)
    query_pos = torch.randn(1, 5, 8, device=device)
    reference_points = torch.randn(1, 5, 1, 4, device=device)
    src = torch.randn(1, 16, 8, device=device)
    src_spatial_shapes = [torch.tensor([4, 4], device=device)]
    # When: running the decoder layer forward
    out = layer(target, query_pos, reference_points, src, src_spatial_shapes)
    # Then: output shape should match target shape
    assert out.shape == target.shape


def test_decoder_forward(device: str) -> None:
    """Test the forward pass of DeformableTransformerDecoder."""
    # Given: a DeformableTransformerDecoder and input tensors
    decoder_layer = DeformableTransformerDecoderLayer(d_model=8, d_ffn=16, n_levels=1, n_heads=2, n_points=2).to(device)
    decoder = DeformableTransformerDecoder(decoder_layer, num_layers=2, n_points=2).to(device)
    target = torch.randn(1, 5, 8, device=device)
    reference_points = torch.sigmoid(torch.randn(1, 5, 4, device=device))
    src = torch.randn(1, 16, 8, device=device)
    src_spatial_shapes = [torch.tensor([4, 4], device=device)]
    src_valid_ratios = torch.ones(1, 1, 4, device=device)
    query_pos = torch.randn(1, 5, 8, device=device)
    # When: running the decoder forward
    out, ref = decoder(target, reference_points, src, src_spatial_shapes, src_valid_ratios, query_pos)
    # Then: output and reference shapes should match expectations
    assert out.shape[0] == 1
    assert ref.shape[0] == 1


def test_get_clones(device: str) -> None:
    """Test the _get_clones utility function for cloning layers."""
    # Given: a torch.nn.Linear layer
    layer = torch.nn.Linear(4, 4).to(device)
    # When: cloning the layer 3 times
    clones = _get_clones(layer, 3)
    # Then: clones should be a ModuleList of 3 Linear layers
    assert isinstance(clones, torch.nn.ModuleList)
    assert len(clones) == 3
    for clone in clones:
        assert isinstance(clone, torch.nn.Linear)
