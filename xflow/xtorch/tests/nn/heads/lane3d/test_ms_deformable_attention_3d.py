"""Tests for the MSDeformableAttention3D class."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


from typing import Any

import numpy as np
import pytest
import torch

from xcontract.data.definitions.image import HW
from xcontract.geometry.definitions.volume import VolumeRange
from xtorch.nn.heads.lane3d.ms_deformable_attention_3d import (
    MSDeformableAttention3D,
    MSDeformableAttention3DInput,
)


@pytest.fixture
def extrinsics() -> torch.Tensor:
    """Creates an extrinsics tensor."""
    base_matrix = torch.tensor(
        [
            [1.0000, 0.0000, 0.0000, 0.0024],
            [0.0000, 0.0000, -1.0000, 1.4541],
            [0.0000, 1.0000, 0.0000, -2.1365],
            [0.0000, 0.0000, 0.0000, 1.0000],
        ]
    )
    return base_matrix.unsqueeze(0).repeat(8, 1, 1)


@pytest.fixture
def dummy_input(extrinsics: torch.Tensor) -> MSDeformableAttention3DInput:
    """Creates a dummy MSDeformableAttention3DInput object."""
    return MSDeformableAttention3DInput(
        query=torch.zeros(8, 480, 128),
        key=torch.randn(8, 8052, 128),
        value=[torch.randn(8, 128, 128, 256), torch.randn(8, 128, 64, 128)],
        key_pos=[torch.randn(8, 128, 128, 256), torch.randn(8, 128, 64, 128)],
        query_pos=torch.randn(8, 480, 128),
        reference_points_x=torch.randn(8, 480, 1),
        reference_points_z=torch.randn(8, 480, 1),
        principal_point=torch.tensor([528, 263]).repeat(8, 1),
        focal_length=torch.tensor([1184, 1184]).repeat(8, 1),
        is_pinhole_model=torch.tensor(False).repeat(8, 1),  # noqa: FBT003
        extrinsics=extrinsics,
        spatial_shapes=[(128, 256), (64, 128)],
        identity=torch.zeros(8, 480, 128),
    )


@pytest.fixture
def init_args() -> dict[str, Any]:
    """Creates a dictionary of initialization arguments for the MSDeformableAttention3D class."""
    return {
        "embed_dims": 128,
        "num_heads": 4,
        "num_levels": 2,
        "num_points": 8,
        "num_query": 16,
        "num_anchor_per_query": 30,
        "anchor_y_steps": np.arange(30),
        "dropout": 0.0,  # No dropout for reproducible results
        "point_cloud_range": VolumeRange(-30.0, -17.0, -10.0, 30.0, 223.0, 10.0),
        "attention_nonlinearity": "relu",
        "image_to_column_step": 64,
        "image_shape": HW(128, 256),
    }


@pytest.fixture
def invalid_init_args() -> dict[str, Any]:
    """Creates a dictionary of invalid initialization arguments for the MSDeformableAttention3D class."""
    return {
        "embed_dims": 128,
        "num_heads": 5,
        "num_levels": 2,
        "num_points": 8,
        "num_query": 16,
        "num_anchor_per_query": 30,
        "anchor_y_steps": np.arange(30),
        "dropout": 0.0,
        "point_cloud_range": VolumeRange(-30, -17, -10, 30, 223, 10),
        "attention_nonlinearity": "relu",
        "image_to_column_step": 64,
        "image_shape": HW(128, 256),
    }


def test_ms_deformable_attention_3d_forward(
    init_args: dict[str, Any], dummy_input: MSDeformableAttention3DInput
) -> None:
    """Tests the forward method of the MSDeformableAttention3D class."""

    # GIVEN an initialized MSDeformableAttention3D object
    ms_deformable_attn_3d = MSDeformableAttention3D(**init_args)

    # WHEN calling the forward method with a dummy input of appropriate shape
    ms_deformable_attn_3d_output = ms_deformable_attn_3d(dummy_input)

    # THEN the output tensor should have the expected shape
    assert ms_deformable_attn_3d_output is not None
    assert ms_deformable_attn_3d_output.shape == (8, 480, 128)


def test_ms_deformable_attention_3d_init_non_divisible_error(invalid_init_args: dict[str, Any]) -> None:
    """Tests the non-divisible error of the __init__ method of the MSDeformableAttention3D class."""

    # GIVEN an invalid initialization dict
    # WHEN using it to initialize a MSDeformableAttention3D object
    # THEN a value error should be raised

    with pytest.raises(
        ValueError,
        match=(
            "embed_dims must be divisible by num_heads, "
            f"but got {invalid_init_args['embed_dims']} and {invalid_init_args['num_heads']}"
        ),
    ):
        MSDeformableAttention3D(**invalid_init_args)


def test_multi_scale_deformable_attn(init_args: dict[str, Any]) -> None:
    """Tests the multi_scale_deformable_attn function."""
    # GIVEN values for the batch_size, num_query, height, width, num_heads, num_embed_dims, num_levels & num_points
    batch_size = 4
    num_query = init_args["num_query"]
    num_heads = init_args["num_heads"]
    num_embed_dims = init_args["embed_dims"]
    num_levels = init_args["num_levels"]
    num_points = init_args["num_points"]
    ms_deformable_attn_3d = MSDeformableAttention3D(**init_args)

    # GIVEN dummy inputs of the appropriate shapes
    value = [torch.randn(batch_size, num_embed_dims, 32, 64), torch.randn(batch_size, num_embed_dims, 16, 32)]
    sampling_locations2d = torch.empty(batch_size, num_query, num_heads, num_levels * num_points, 2).uniform_(-4, 105)
    attention_scores = torch.empty(batch_size, num_query, num_heads * num_levels * num_points).uniform_(0, 1)

    # WHEN calling the multi_scale_deformable_attn function with these inputs
    output = ms_deformable_attn_3d._multi_scale_deformable_attn(  # noqa: SLF001
        value, sampling_locations2d, attention_scores
    )

    # THEN the output tensor should not be None and have the expected shape
    assert output is not None
    assert output.shape == (batch_size, num_query, num_heads, num_levels * num_points * num_embed_dims // num_heads)


def test_multi_scale_deformable_attn_fx_tracing(
    init_args: dict[str, Any], dummy_input: MSDeformableAttention3DInput
) -> None:
    """Tests the forward method of the MSDeformableAttention3D class."""

    assert dummy_input.value is not None

    # GIVEN an initialized MSDeformableAttention3D object
    reference = MSDeformableAttention3D(**init_args)

    # WHEN symbolically traced
    traced = torch.fx.symbolic_trace(reference)

    # THEN the traced object is a graph module and produces the same output as the original
    assert isinstance(traced, torch.fx.GraphModule)
    with torch.no_grad():
        reference_output = reference(dummy_input)
        traced_output = traced(dummy_input)

    torch.testing.assert_close(reference_output, traced_output)
