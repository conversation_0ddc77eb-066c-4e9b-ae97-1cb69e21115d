"""Pytest for Layer of the transformer decoder."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import Any

import numpy as np
import pytest
import torch

from xcontract.data.definitions.image import HW
from xcontract.geometry.definitions.volume import VolumeRange
from xtorch.nn.heads.lane3d.transformer_decoder_layer import (
    TransformerDecoderLayer,
    TransformerDecoderLayerInput,
    TransformerDecoderLayerParams,
)


@pytest.fixture
def decoder_layer_init_args() -> dict[str, Any]:
    """Fixture for initialization arguments of TransformerDecoderLayer."""

    # Define the parameters for the TransformerDecoderLayer
    decoder_layer_params = TransformerDecoderLayerParams(
        operation_order=["self_attn", "norm", "cross_attn", "norm", "ffn", "norm"],
        num_pt_per_query=30,
        image_shape=HW(512, 1024),
        point_cloud_range=VolumeRange(-30.0, -17.0, -10.0, 30.0, 223.0, 10.0),
        attention_nonlinearity="relu",
        num_heads=4,
        num_levels=1,
        num_points=30,
        ffnn_intermediate_dims_multiplier=4,
        dropout=0.1,
        points_as_query=True,
        export_use_pinhole_model=False,
    )

    return {
        "embed_dim": 128,
        "num_query": 16,
        "anchor_y_steps": np.array(
            [
                -4.40740741,
                3.0,
                10.40740741,
                17.81481481,
                25.22222222,
                32.62962963,
                40.03703704,
                47.44444444,
                54.85185185,
                62.25925926,
                69.66666667,
                77.07407407,
                84.48148148,
                91.88888889,
                99.2962963,
                106.7037037,
                114.11111111,
                121.51851852,
                128.92592593,
                136.33333333,
                143.74074074,
                151.14814815,
                158.55555556,
                165.96296296,
                173.37037037,
                180.77777778,
                188.18518519,
                195.59259259,
                203.0,
                210.40740741,
            ]
        ),
        "layer_params": decoder_layer_params,
    }


@pytest.fixture
def decoder_layer_input() -> TransformerDecoderLayerInput:
    """Fixture for TransformerDecoderLayerInput."""
    spatial_shapes = (64, 128)
    return TransformerDecoderLayerInput(
        query=torch.randn(8, 480, 128),
        key=torch.randn(8, 8192, 128),
        value=[torch.randn(8, 128, *spatial_shapes)],
        key_pos=[torch.randn(8, 128, *spatial_shapes)],
        reference_points_x=torch.randn(8, 480, 1),
        reference_points_z=torch.randn(8, 480, 1),
        principal_point=torch.tensor([528, 263]).repeat(8, 1),
        focal_length=torch.tensor([1184, 1184]).repeat(8, 1),
        is_pinhole_model=torch.full((8, 1), 0, dtype=torch.bool),
        extrinsics=torch.randn(8, 4, 4),
        query_pos=torch.randn(8, 480, 128),
        spatial_shapes=spatial_shapes,
    )


def test_transformer_decoder_layer_forward(
    decoder_layer_init_args: dict[str, Any], decoder_layer_input: TransformerDecoderLayerInput
) -> None:
    """Test the forward method of TransformerDecoderLayer."""
    # GIVEN a TransformerDecoderLayer instance
    decoder_layer = TransformerDecoderLayer(**decoder_layer_init_args)

    # WHEN calling the forward method
    output = decoder_layer(inputs=decoder_layer_input)

    # THEN the output tensor should have the expected shape
    assert output is not None
    assert output.shape == decoder_layer_input.query.shape


def test_transformer_decoder_layer_forward_eval(
    decoder_layer_init_args: dict[str, Any], decoder_layer_input: TransformerDecoderLayerInput
) -> None:
    """Test that the forward method of the TransformerDecoderLayer returns the same result for training & evaluation."""
    # GIVEN a TransformerDecoderLayer instance with deactivated dropout
    decoder_layer_init_args["layer_params"] = decoder_layer_init_args["layer_params"]._replace(
        dropout=0.0
    )  # Set dropout to 0 for reproducibility
    decoder_layer = TransformerDecoderLayer(**decoder_layer_init_args)

    # WHEN calling the forward method in train & eval mode
    decoder_layer.train()
    output_train = decoder_layer(inputs=decoder_layer_input)

    decoder_layer.eval()
    output_eval = decoder_layer(inputs=decoder_layer_input)

    # THEN the output tensors should be close
    torch.testing.assert_close(output_train, output_eval)
