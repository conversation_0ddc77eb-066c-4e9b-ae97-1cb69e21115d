"""Module for testing the camera projection."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import pytest
import torch

from xcontract.data.definitions.image import HW
from xtorch.camera_models.lane3d.camera_projection import (
    VehicleToCameraCoordinates,
    convert_3d_to_pixel_coordinates,
    convert_to_pixel_coordinates,
)


def test_convert_to_pixel_coordinates() -> None:
    """Test the `convert_to_pixel_coordinates` function."""
    # GIVEN camera parameters and light rays (or rather viewing rays) radiating
    # outward from the camera. Non normalized. Within an arc which is plausible
    # for a cameras field of view.
    torch.manual_seed(0)
    height, width = 768, 1280
    batch_size = 2
    num_points = 1000
    is_pinhole_model = torch.as_tensor([True, False], dtype=torch.bool)[:, None]
    principal_point = 0.5 * torch.as_tensor([width, height], dtype=torch.float32)[None, :].expand(batch_size, -1)
    focal_length = 1.2 * principal_point  # Should be also around half the image size (?). Added factor 1.2 to make it
    # distinguished from principal point
    min_valid_distance = 1.0

    min_length = 1.0e-3
    max_length = 10.0  # Approximately
    # Compute light ray in forward direction within some field of view.
    # Use spherical coordinates
    horizontal_fov = torch.pi / 180.0 * 120.0
    phi = horizontal_fov * (torch.rand((batch_size, num_points)) - 0.5)  # -half fov to half fov
    theta = height / width * horizontal_fov * (torch.rand((batch_size, num_points)) - 0.5)
    light_ray = torch.stack([phi.sin() * theta.cos(), theta.sin(), phi.cos() * theta.cos()], dim=-1)
    light_ray *= min_length + max_length * torch.rand((batch_size, num_points, 1))
    light_ray[:, 0, :] = 0.0  # Zero to test robustness against degenerate inputs

    # WHEN calculating the resulting pixel coordinates
    output, invalid_mask = convert_to_pixel_coordinates(
        light_ray[..., 0],
        light_ray[..., 1],
        light_ray[..., 2],
        principal_point,
        focal_length,
        is_pinhole_model,
        min_valid_distance,
    )

    # THEN the pixel coordinates and the invalid mask have the correct types and shapes
    assert output.dtype == torch.float32
    assert invalid_mask.dtype == torch.bool
    assert output.shape == (batch_size, num_points, 2)
    assert invalid_mask.shape == (batch_size, num_points)

    # THEN the pixel coordinates are finite
    assert torch.all(torch.isfinite(output))

    # THEN the pixel coordinates are within a reasonable range
    assert torch.all(output[..., 0] > -1000)
    assert torch.all(output[..., 0] < width + 1000)
    assert torch.all(output[..., 1] > -500)
    assert torch.all(output[..., 1] < height + 500)

    # THEN the elements of invalid mask are within {0.0, 1.0}
    assert set(torch.unique(invalid_mask).tolist()) <= {0.0, 1.0}

    # WHEN calculating the resulting pixel coordinates using boolean flag (pinhole model)
    output, invalid_mask = convert_to_pixel_coordinates(
        light_ray[..., 0],
        light_ray[..., 1],
        light_ray[..., 2],
        principal_point,
        focal_length,
        is_pinhole_model=True,
        min_valid_distance=min_valid_distance,
    )
    # THEN the pixel coordinates are within a reasonable range
    assert torch.all(output > -1000)
    assert torch.all(output < width + 1000)

    # WHEN calculating the resulting pixel coordinates using boolean flag (cylindrical model)
    output, invalid_mask = convert_to_pixel_coordinates(
        light_ray[..., 0],
        light_ray[..., 1],
        light_ray[..., 2],
        principal_point,
        focal_length,
        is_pinhole_model=False,
        min_valid_distance=min_valid_distance,
    )
    # THEN the pixel coordinates are within a reasonable range
    assert torch.all(output > -500)
    assert torch.all(output < width + 500)


@pytest.mark.parametrize("normalize", [True, False])
def test_convert_3d_to_pixel_coordinates(*, normalize: bool) -> None:
    """Test the `convert_3d_to_pixel_coordinates` function."""

    # GIVEN camera parameters and 3D coordinates
    coords3d = torch.arange(3 * 48 * 4).view(3, 48, 4).float()
    principal_point = torch.tensor([64, 128], dtype=torch.float32).repeat(3, 1)
    focal_length = torch.tensor([64, 64], dtype=torch.float32).repeat(3, 1)
    is_pinhole_model = torch.zeros(3, 1, dtype=torch.bool)
    extrinsics = torch.tensor(
        [
            [1.0000, 0.0000, 0.0000, 0.0024],
            [0.0000, 0.0000, -1.0000, 1.4541],
            [0.0000, 1.0000, 0.0000, -2.1365],
            [0.0000, 0.0000, 0.0000, 1.0000],
        ]
    )
    extrinsics = extrinsics.unsqueeze(0).repeat(3, 1, 1)
    original_shape = HW(128, 256)
    min_valid_distance = 1.0

    vehicle_to_camera_coord = VehicleToCameraCoordinates()
    x, y, z = vehicle_to_camera_coord(
        coords3d[..., 0],
        coords3d[..., 1],
        coords3d[..., 2],
        extrinsics=extrinsics,
    )

    # WHEN calling convert_3d_to_pixel_coordinates with these inputs
    pixel_coordinates, invalid_mask = convert_3d_to_pixel_coordinates(
        x,
        y,
        z,
        principal_point,
        focal_length,
        is_pinhole_model=is_pinhole_model,
        image_shape=original_shape,
        min_valid_distance=min_valid_distance,
        normalize=normalize,
    )

    # THEN the pixel coordinates and the invalid mask have the correct types and shapes
    assert pixel_coordinates.dtype == torch.float32
    assert invalid_mask.dtype == torch.bool
    assert pixel_coordinates.shape == (3, 48, 2)
    assert invalid_mask.shape == (3, 48)

    # THEN the pixel coordinates are finite
    assert torch.all(torch.isfinite(pixel_coordinates))

    # THEN the pixel coordinates are within a reasonable range
    if normalize:
        assert torch.all(pixel_coordinates[..., 0] > 0)
        assert torch.all(pixel_coordinates[..., 0] < 1.5)
        assert torch.all(pixel_coordinates[..., 1] > 0)
        assert torch.all(pixel_coordinates[..., 1] < 1.5)
    else:
        assert torch.all(pixel_coordinates[..., 0] > 0)
        assert torch.all(pixel_coordinates[..., 0] < original_shape.width + 100)
        assert torch.all(pixel_coordinates[..., 1] > 0)
        assert torch.all(pixel_coordinates[..., 1] < original_shape.height + 100)

    # THEN the elements of invalid mask are within {0.0, 1.0}
    assert set(torch.unique(invalid_mask).tolist()) <= {0.0, 1.0}
