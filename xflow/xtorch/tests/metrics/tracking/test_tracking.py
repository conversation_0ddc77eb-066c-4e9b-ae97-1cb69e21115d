"""Tests for multi-object CLEAR tracking metrics."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from typing import cast

import pytest
import torch

from xtorch.metrics.tracking.tracking import (
    _OBJ_IDS_KEY,
    TrackingMetric,
    TrackingMetricsGroundTruth,
    TrackingMetricsPredictions,
)

torch.manual_seed(0)


def generate_boxes(num_objects: int, device: str = "cpu") -> torch.Tensor:
    """Generate random boxes including class ids and scores."""
    boxes = torch.rand((num_objects, 3), device=device)
    classes = torch.randint(0, 1, (num_objects, 1), device=device)
    scores = torch.rand((num_objects, 1), device=device)
    return torch.cat((boxes, classes, scores), dim=-1)


def test_collect_data_per_video_id() -> None:
    """Tests _collect_data_per_video_id."""
    # GIVEN some object ids and boxes
    video_id, frame_id, obj_ids = (
        torch.Tensor(
            [
                [1, 1, 1],
                [1, 2, 1],
                [1, 2, 2],
                [1, 3, 1],
                [1, 3, 2],
                [1, 4, 1],
                [1, 4, 3],
                [1, 5, 1],
                [1, 5, 3],
                [7, 1, 1],
                [7, 2, 2],
                [7, 2, 1],
                [7, 3, 3],
                [7, 3, 2],
                [7, 3, 1],
                [9, 1, 4],
                [9, 2, 3],
                [9, 3, 2],
                [9, 4, 1],
                [9, 4, 4],
            ]
        )
        .long()
        .unbind(-1)
    )
    boxes = generate_boxes(len(obj_ids))
    # WHEN we store the data in dicts of dicts
    vals_per_video_id = TrackingMetric(class_names=["class_a"])._collect_data_per_video_id(  # noqa: SLF001
        obj_ids, boxes, video_id, frame_id
    )
    # THEN we expect the following structure
    assert set(vals_per_video_id.keys()) == {1, 7, 9}
    # these are OrderedDicts so they should preserve order
    assert list(vals_per_video_id[1].keys()) == [1, 2, 3, 4, 5]
    assert list(vals_per_video_id[7].keys()) == [1, 2, 3]
    assert list(vals_per_video_id[9].keys()) == [1, 2, 3, 4]

    # vals_per_video_id[video_id][frame_id] should contain the obj_ids and boxes for that frame
    assert set(vals_per_video_id[1][1][_OBJ_IDS_KEY].tolist()) == {1}
    assert set(vals_per_video_id[1][2][_OBJ_IDS_KEY].tolist()) == {1, 2}
    assert set(vals_per_video_id[1][3][_OBJ_IDS_KEY].tolist()) == {1, 2}
    assert set(vals_per_video_id[1][4][_OBJ_IDS_KEY].tolist()) == {1, 3}
    assert set(vals_per_video_id[1][5][_OBJ_IDS_KEY].tolist()) == {1, 3}

    assert set(vals_per_video_id[7][1][_OBJ_IDS_KEY].tolist()) == {1}
    assert set(vals_per_video_id[7][2][_OBJ_IDS_KEY].tolist()) == {1, 2}
    assert set(vals_per_video_id[7][3][_OBJ_IDS_KEY].tolist()) == {1, 2, 3}

    assert set(vals_per_video_id[9][1][_OBJ_IDS_KEY].tolist()) == {4}
    assert set(vals_per_video_id[9][2][_OBJ_IDS_KEY].tolist()) == {3}
    assert set(vals_per_video_id[9][3][_OBJ_IDS_KEY].tolist()) == {2}
    assert set(vals_per_video_id[9][4][_OBJ_IDS_KEY].tolist()) == {1, 4}


def assert_perfect_match(result: dict[str, torch.Tensor], total_num_objects: int) -> None:
    """Assert that the result is a perfect match."""
    for k, v in result.items():
        if k in {"idf1", "idp", "idr", "precision", "recall", "mota", "hota_alpha", "deta_alpha", "assa_alpha"}:
            assert v == 1.0
        elif k in {"num_unique_objects", "mostly_tracked"}:
            assert v == total_num_objects
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


def test_no_predictions_give_low_metrics(device: str) -> None:
    """Test TrackingMetric."""
    # GIVEN some gt boxes and no predictions
    gt_obj_ids = torch.Tensor([1, 2, 3]).to(device)
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    metric.update_single_frame(
        pred_obj_ids=torch.Tensor([]).to(device),
        pred_boxes=torch.Tensor([]).to(device),
        gt_obj_ids=gt_obj_ids,
        gt_boxes=generate_boxes(len(gt_obj_ids), device=device)[:, :4],
        video_name="video",
        frame_id=0,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())

    # THEN we expect the following metrics to be NaN since we don't have any prediction
    for k, v in result.items():
        if k in {"idp", "precision", "motp"}:
            assert torch.isnan(v)
        elif k in {"num_misses", "num_unique_objects", "mostly_lost"}:
            torch.testing.assert_close(v, torch.tensor(3.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


@pytest.mark.parametrize("num_objects", [3, 5])
@pytest.mark.parametrize("num_frames", [1, 3])
@pytest.mark.parametrize("num_videos", [1, 3])
def test_perfect_overlap_gives_high_metrics(num_objects: int, num_frames: int, num_videos: int, device: str) -> None:
    """Test TrackingMetric."""
    # GIVEN we have perfect overlap between predictions and GT
    obj_ids = torch.arange(num_objects, device=device)
    boxes = generate_boxes(len(obj_ids), device=device)
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    for video_id in range(num_videos):
        for frame_id in range(num_frames):
            metric.update_single_frame(
                pred_obj_ids=obj_ids,
                pred_boxes=boxes,
                gt_obj_ids=obj_ids,
                gt_boxes=boxes,
                video_name=str(video_id),
                frame_id=frame_id,
            )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())

    # THEN we expect a perfect match
    total_num_objects = num_objects * num_videos
    assert_perfect_match(result, total_num_objects)


def test_some_predicted_scores_below_threshold(device: str) -> None:
    """Test TrackingMetric.

    Test for one frame, where we have perfect matches but some predictions have a confidence below our set threshold.
    """
    # GIVEN we have perfect matches but some predictions have a confidence below our set threshold
    metric = TrackingMetric(class_names=["class_a"], min_score=0.3)
    num_objects = 10
    obj_ids = torch.arange(num_objects, device=device)
    boxes = generate_boxes(num_objects, device)
    boxes[5:, 4] = 1.0
    boxes[:5, 4] = 0.1  # set some scores below the threshold; those predictions will be ignored
    # WHEN we evaluate the CLEAR metrics
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes,
        gt_obj_ids=obj_ids,  # same object ids
        gt_boxes=boxes,  # same boxes
        video_name="video_id",
        frame_id=1,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())
    # THEN we expect the following metrics
    for k, v in result.items():
        if k in {"recall", "idr", "mota", "deta_alpha"}:
            assert v == 0.5
        elif k in {"precision", "idp", "assa_alpha"}:
            assert v == 1.0
        elif k in {"hota_alpha"}:
            torch.testing.assert_close(v, torch.ones_like(v) * (0.5 * 1.0) ** 0.5)
        elif k in {"idf1"}:
            torch.testing.assert_close(v, torch.ones_like(v) * (2 * 1.0 * 0.5) / (1.0 + 0.5))
        elif k in {"mostly_tracked", "num_misses", "mostly_lost"}:
            assert v == 5
        elif k in {"num_unique_objects"}:
            assert v == num_objects
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


def test_on_first_frame_id_match_but_on_second_frame_some_id_switch(device: str) -> None:
    """Test TrackingMetric.

    On the first frame the ids are the same between pred and GT, but on the second frame some of the ids are switched.
    Precision and recall should still be one because all things are detected, but for tracking some metrics are lower
    since there is an id switch.
    """
    # GIVEN two frames with perfect matches but some ids are switched
    num_objects = 10
    obj_ids_first_frame = torch.arange(num_objects, device=device)
    obj_ids_second_frame = torch.arange(num_objects, device=device)
    obj_ids_second_frame[:3] = torch.arange(3) + 100  # some objects change their ids
    boxes = generate_boxes(num_objects, device)
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    # frame 1
    metric.update_single_frame(
        pred_obj_ids=obj_ids_first_frame,
        pred_boxes=boxes,
        gt_obj_ids=obj_ids_first_frame,  # same object ids
        gt_boxes=boxes,  # same boxes
        video_name="video_id",
        frame_id=1,
    )
    # frame 2
    metric.update_single_frame(
        pred_obj_ids=obj_ids_second_frame,  # GT object ids remain the same, but the predicted object ids are different
        pred_boxes=boxes,
        gt_obj_ids=obj_ids_first_frame,
        gt_boxes=boxes,
        video_name="video_id",
        frame_id=2,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())
    # THEN we expect the following metrics
    # id values are lower than 1.0 because some boxes are swapped but precision and recall are still 1.0 because
    # all objects are matched and detected
    for k, v in result.items():
        if k in {"precision", "recall", "deta_alpha"}:
            assert v == 1.0
        elif k in {"idf1", "idp", "idr", "mota", "assa_alpha"}:
            assert v == 0.85
        elif k in {"hota_alpha"}:
            torch.testing.assert_close(v, torch.ones_like(v) * (0.85 * 1.0) ** 0.5)
        elif k in {"num_unique_objects", "mostly_tracked"}:
            assert v == num_objects
        elif k in {"num_switches", "num_ascend"}:  # num_switches is 3 because of the 3 objects that changed
            assert v == 3
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


def test_on_first_frame_boxes_match_but_on_second_frame_some_boxes_are_swapped(device: str) -> None:
    """Test TrackingMetric.

    On the first frame the boxes are the same between pred and GT, but on the second frame some of the boxes
    are swapped in order (similar to the id switch case but with boxes instead of ids)
    Precision and recall should still be one because all things are detected, but for tracking some metrics are lower
    since the box switch implies that there is an id switch.
    """
    # GIVEN perfect matches on the first frame but some box swaps on the second frame
    num_objects = 10
    obj_ids = torch.arange(num_objects, device=device)
    boxes_first_frame = generate_boxes(num_objects, device)
    boxes_first_frame[:3, :3] = torch.Tensor([[1.0, 1.0, 1.0], [5.0, 5.0, 5.0], [9.0, 9.0, 9.0]])
    boxes_second_frame = boxes_first_frame.clone()
    boxes_second_frame[:3, :3] = torch.Tensor([[5.0, 5.0, 5.0], [9.0, 9.0, 9.0], [1.0, 1.0, 1.0]])
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    # frame 1
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_first_frame,
        gt_obj_ids=obj_ids,  # same object ids
        gt_boxes=boxes_first_frame,  # same boxes
        video_name="video_id",
        frame_id=1,
    )
    # frame 2
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_second_frame,  # GT boxes remain the same, but some predicted boxes are different
        gt_obj_ids=obj_ids,
        gt_boxes=boxes_first_frame,
        video_name="video_id",
        frame_id=2,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())
    # THEN we expect the ID switches to negatively impact the switch-based metrics like idf1
    # id values are lower than 1.0 because some boxes are swapped but precision and recall are still 1.0 because
    # all objects are matched and detected
    for k, v in result.items():
        if k in {"precision", "recall", "deta_alpha"}:
            assert v == 1.0
        elif k in {"idf1", "idp", "idr", "mota"}:
            assert v == 0.85
        elif k in {"assa_alpha"}:
            torch.testing.assert_close(v, torch.tensor(0.8, device=v.device))
        elif k in {"hota_alpha"}:
            torch.testing.assert_close(v, torch.ones_like(v) * (0.8 * 1.0) ** 0.5)
        elif k in {"num_unique_objects", "mostly_tracked"}:
            assert v == num_objects
        elif k in {"num_switches", "num_transfer"}:  # num_switches is 3 because of the 3 objects that changed
            assert v == 3
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


def test_on_first_frame_boxes_match_but_on_second_frame_some_boxes_do_not_match(device: str) -> None:
    """Test TrackingMetric.

    On the first frame the boxes are the same between pred and GT, but on the second frame some of the boxes
    do not overlap any more. This is basically not detecting some objects.
    Precision and recall are lower than 1.0 and also the tracking metrics.
    """
    # GIVEN perfect matches on the first frame but some boxes do not overlap on the second frame
    num_objects = 10
    obj_ids = torch.arange(num_objects, device=device)
    boxes_first_frame = generate_boxes(num_objects, device)
    boxes_first_frame[:3, :3] = torch.Tensor([[1.0, 1.0, 1.0], [5.0, 5.0, 5.0], [9.0, 9.0, 9.0]])
    boxes_second_frame = boxes_first_frame.clone()
    boxes_second_frame[:3, :3] = torch.Tensor([[-2.0, -2.0, -2.0], [-5.0, -5.0, -5.0], [-9.0, -9.0, -9.0]])
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    # frame 1
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_first_frame,
        gt_obj_ids=obj_ids,  # same object ids
        gt_boxes=boxes_first_frame,  # same boxes
        video_name="video_id",
        frame_id=1,
    )
    # frame 2
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_second_frame,  # GT boxes remain the same, but some predicted boxes are different
        gt_obj_ids=obj_ids,
        gt_boxes=boxes_first_frame,
        video_name="video_id",
        frame_id=2,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())
    # THEN we expect various metrics to be affected negatively
    # boxes are too far from each other so the IOU is lower than 0.5, thus the boxes are not matched to any GT object
    # In this case id values and also precision and recall are lower than 1.0
    for k, v in result.items():
        if k in {"idf1", "idp", "idr", "precision", "recall"}:
            assert v == 0.85
        elif k in {"mota"}:
            assert v == 0.7
        elif k in {"deta_alpha"}:
            # we have 17 TPs, 3 FPs and 3 FNs
            torch.testing.assert_close(v, torch.ones_like(v) * (17 / (3 + 3 + 17)))
        elif k in {"assa_alpha"}:
            # We have 7 boxes that are matched in both frames, i.e. 14 TPs where we have perfect Ass-IoU score of 1.
            # Then we have 3 more TPs in the first frame without a match in the second frame, giving an Ass-IoU score of
            # 1/(1 + 1 + 1) =  1/3. AssA = 1/TP * \sum_c Ass-IoU(c)
            torch.testing.assert_close(v, torch.ones_like(v) * (14 + 1) / 17)
        elif k in {"hota_alpha"}:
            torch.testing.assert_close(v, torch.ones_like(v) * ((17 / (3 + 3 + 17)) * ((14 + 1) / 17)) ** 0.5)
        elif k in {"num_unique_objects"}:
            assert v == num_objects
        elif k in {"mostly_tracked"}:
            assert v == 7
        elif k in {"num_misses", "num_false_positives", "partially_tracked"}:
            # 3 objects in the second frame are not matched
            assert v == 3
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


def test_on_first_frame_boxes_match_but_on_second_frame_some_tracks_diverge(device: str) -> None:
    """Test TrackingMetric.

    On the first frame the boxes are the same between pred and GT, but on the second frame some of the tracks diverge.
    But since the tracks never deviate by more than `max_distance`, they are still valid matches. Also we have no
    switches, since CLEAR metrics keep tracks alive despite other objects having smaller distance. This leads to perfect
    recall and precision, with a reduced motp score though, which measures the accuracy of those matches in terms of
    """
    # GIVEN a perfect match on the first frame but some tracks diverge on the second frame
    num_objects = 10
    obj_ids = torch.arange(num_objects, device=device)
    boxes_first_frame = generate_boxes(num_objects, device)
    boxes_first_frame[:3, :3] = torch.Tensor([[1.0, 0.0, 0.0], [0.0, 0.0, 0.0], [2.0, 2.0, 2.0]])
    boxes_second_frame = boxes_first_frame.clone()
    boxes_second_frame[:3, :3] = torch.Tensor([[0.0, 0.0, 0.0], [1.0, 0.0, 0.0], [2.0, 2.0, 1.0]])
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"], max_distance=2.0)
    # frame 1
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_first_frame,
        gt_obj_ids=obj_ids,  # same object ids
        gt_boxes=boxes_first_frame,  # same boxes
        video_name="video_id",
        frame_id=1,
    )
    # frame 2
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_second_frame,  # GT boxes remain the same, but some predicted boxes are different
        gt_obj_ids=obj_ids,
        gt_boxes=boxes_first_frame,
        video_name="video_id",
        frame_id=2,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())
    # THEN we expect a reduced motp score but perfect recall and precision
    for k, v in result.items():
        if k in {"idf1", "idp", "idr", "precision", "recall", "mota", "deta_alpha", "assa_alpha", "hota_alpha"}:
            assert v == 1.0
        elif k in {"motp"}:
            # for the second frame we have 3 boxes with a distance of sqrt(1) each, all other boxes have distance 0
            assert v == 1 * 3 / (2 * num_objects)  # divide by total number of matches: 2 frames and all objects matched
        elif k in {"num_unique_objects"}:
            assert v == num_objects
        elif k in {"mostly_tracked"}:
            assert v == 10
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


def test_on_first_frame_boxes_match_but_on_second_frame_class_predictions_switch(device: str) -> None:  # noqa: C901
    """Test TrackingMetric.

    On the first frame the boxes are the same between pred and GT, but on the second frame for some boxes the wrong
    class is predicted. This results in FPs and FNs and a reduced mota score.
    """
    # GIVEN a perfect matching box positions for both frames but some wrong class predictions on the second frame
    num_objects = 10
    obj_ids = torch.arange(num_objects, device=device)
    boxes_first_frame = generate_boxes(num_objects, device)
    boxes_first_frame[5:, 3] = 1  # first half of boxes are class 0 and second half are class 1
    boxes_second_frame = boxes_first_frame.clone()
    boxes_second_frame[:3, 3] = 1  # setting three more predicted classes to 1
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a", "class_b"], max_distance=2.0)
    # frame 1
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_first_frame,
        gt_obj_ids=obj_ids,  # same object ids
        gt_boxes=boxes_first_frame,  # same boxes
        video_name="video_id",
        frame_id=1,
    )
    # frame 2
    metric.update_single_frame(
        pred_obj_ids=obj_ids,
        pred_boxes=boxes_second_frame,  # different predictions for second frame
        gt_obj_ids=obj_ids,
        gt_boxes=boxes_first_frame,
        video_name="video_id",
        frame_id=2,
    )
    # THEN we most metrics to be non-optimal
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())

    deta_class_a = 7 / (7 + 3)  # 5 + 2 TPs in Frame 1 + 2, 3 FNs in Frame 2
    deta_class_b = 10 / (10 + 3)  # 5 + 5 TPs in Frame 1 + 2, 3 FPs in Frame 2
    deta_alpha = (deta_class_a + deta_class_b) / 2

    # we have 2 perfect tracks, i.e. 4 perfect Ass-IoUs, and 3 TPs in Frame 1 which are not matched in Frame 2
    # i.e. an Ass-IoU of (1 / 1 (TPA) + 1 (FNA))
    assa_class_a = (4 + 3 * 1 / 2) / 7
    # since we only look at TPs here, we have perfect hota for class 2
    assa_class_b = 1
    assa_alpha = (assa_class_a + assa_class_b) / 2

    for k, v in result.items():
        # we have perfect recall for class `1` and 0.7 for class `0` due to 3 FNs
        if k in {"idr", "recall"}:
            assert v == (7 / 10 + 1) / 2
        # For class `1` we have 3 FPs on top of the 10 TPs
        elif k in {"idp", "precision"}:
            torch.testing.assert_close(v, torch.ones_like(v) * (1 + 10 / 13) / 2)
        elif k in {"idf1"}:
            idf1 = 0.5 * ((2 * 0.7 * 1) / (0.7 + 1) + (2 * 1 * 10 / 13) / (1 + 10 / 13))
            torch.testing.assert_close(v, torch.ones_like(v) * idf1)
        elif k in {"deta_alpha"}:
            torch.testing.assert_close(v, torch.ones_like(v) * deta_alpha)
        elif k in {"assa_alpha"}:
            torch.testing.assert_close(v, torch.ones_like(v) * assa_alpha)
        elif k in {"hota_alpha"}:
            torch.testing.assert_close(
                v,
                torch.ones_like(v) * ((deta_class_a * assa_class_a) ** 0.5 + (deta_class_b * assa_class_b) ** 0.5) / 2,
            )
        # three misses for class `0` and 3 FPs for class `1`
        elif k in {"mota"}:
            torch.testing.assert_close(v, torch.ones_like(v) * 0.5 * (1 - (3 / 10) + 1 - (3 / 10)))
        elif k in {"num_unique_objects"}:
            assert v == num_objects
        elif k in {"mostly_tracked"}:
            assert v == 7
        elif k in {"num_misses", "num_false_positives", "partially_tracked"}:
            assert v == 3
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


def test_id_changes_across_video_does_not_impact(device: str) -> None:
    """Test TrackingMetric.

    Videos are independent of each other, therefore even if the object ids change across videos, the metrics should
    be perfect.
    """
    # GIVEN two frames from two different videos
    num_objects = 10
    obj_ids_first_video = torch.arange(num_objects, device=device)
    obj_ids_second_video = torch.arange(num_objects, device=device) + 100
    boxes = generate_boxes(num_objects, device)

    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    # video 1
    metric.update_single_frame(
        pred_obj_ids=obj_ids_first_video,
        pred_boxes=boxes,
        gt_obj_ids=obj_ids_first_video,  # same object ids
        gt_boxes=boxes,  # same boxes
        video_name="1",
        frame_id=1,
    )
    # video 2
    metric.update_single_frame(
        pred_obj_ids=obj_ids_second_video,  # GT object ids remain the same, but the predicted object ids are different
        pred_boxes=boxes,
        gt_obj_ids=obj_ids_first_video,
        gt_boxes=boxes,
        video_name="2",
        frame_id=1,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())

    # THEN we expect perfect metrics
    # videos are independent from each other so even if ids change across videos, the metrics should be perfect
    total_num_objects = num_objects * 2
    assert_perfect_match(result, total_num_objects)


@pytest.mark.parametrize("num_pred_objects", [7, 11])
@pytest.mark.parametrize("num_gt_objects", [3, 6])
def test_more_preds_than_ground_truth(num_pred_objects: int, num_gt_objects: int, device: str) -> None:
    """Test TrackingMetric.

    There are more predictions than ground truth objects, therefore the recall should be good but the precision should
    be lower than 1.0. Same thing for tracking metrics such as idp (identity precision) and idr (identity recall).
    """
    # GIVEN more predictions than ground truth objects
    pred_obj_ids = torch.arange(num_pred_objects, device=device)
    gt_obj_ids = torch.arange(num_gt_objects, device=device)
    boxes = generate_boxes(num_pred_objects, device=device)
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    metric.update_single_frame(
        pred_obj_ids=pred_obj_ids,
        pred_boxes=boxes,
        gt_obj_ids=gt_obj_ids,
        gt_boxes=boxes[: len(gt_obj_ids)],  # fewer boxes than predicted
        video_name="video_id",
        frame_id=1,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())

    # THEN we expect the following metrics
    for k, v in result.items():
        if k in {"idr", "recall", "assa_alpha"}:  # perfect recall and perfect tracks for TPs
            assert v == 1.0
        elif k in {"idp", "precision"}:
            assert v == len(gt_obj_ids) / len(pred_obj_ids)
        elif k in {"idf1"}:
            torch.testing.assert_close(v, result["idr"] * result["idp"] * 2 / (result["idr"] + result["idp"]))
        elif k in {"deta_alpha"}:
            deta_alpha = num_gt_objects / (num_gt_objects + (num_pred_objects - num_gt_objects))
            torch.testing.assert_close(v, torch.tensor(deta_alpha, device=v.device))
        elif k in {"hota_alpha"}:
            torch.testing.assert_close(
                v, torch.tensor((result["deta_alpha"] * result["assa_alpha"]) ** 0.5, device=v.device)
            )
        elif k in {"num_unique_objects", "mostly_tracked"}:
            assert v == len(gt_obj_ids)
        elif k in {"num_false_positives"}:
            assert v == len(pred_obj_ids) - len(gt_obj_ids)
        elif k in {"mota"}:
            torch.testing.assert_close(v, 1 - result["num_false_positives"] / result["num_unique_objects"])
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


@pytest.mark.parametrize("num_pred_objects", [7, 4])
@pytest.mark.parametrize("num_gt_objects", [8, 12])
def test_more_gt_than_preds(num_pred_objects: int, num_gt_objects: int, device: str) -> None:  # noqa: C901
    """Test TrackingMetric.

    There are more ground truth objects than predicitons, therefore the precision should be good but the recall should
    be lower than 1.0. Same thing for tracking metrics such as idr (identity recall) and idp (identity precision).
    """
    # GIVEN more ground truth objects than predictions
    pred_obj_ids = torch.arange(num_pred_objects, device=device)
    gt_obj_ids = torch.arange(num_gt_objects, device=device)
    boxes = generate_boxes(num_gt_objects, device=device)
    # WHEN we evaluate the CLEAR metrics
    metric = TrackingMetric(class_names=["class_a"])
    metric.update_single_frame(
        pred_obj_ids=pred_obj_ids,
        pred_boxes=boxes[:num_pred_objects],
        gt_obj_ids=gt_obj_ids,
        gt_boxes=boxes,  # same boxes
        video_name="video_id",
        frame_id=1,
    )
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())

    # THEN we expect the following metrics
    # id values are lower than 1.0 because some boxes are swapped but precision and recall are still 1.0 because
    # all objects are matched and detected
    for k, v in result.items():
        if k in {"idp", "precision", "assa_alpha"}:  # perfect precision and perfect assa since we only have one frame
            assert v == 1.0
        elif k in {"idr", "recall"}:
            assert v == len(pred_obj_ids) / len(gt_obj_ids)
        elif k in {"idf1"}:
            torch.testing.assert_close(v, result["idr"] * result["idp"] * 2 / (result["idr"] + result["idp"]))
        elif k in {"deta_alpha"}:
            # all preds are TPs and we have some FNs
            deta_alpha = num_pred_objects / (num_pred_objects + (num_gt_objects - num_pred_objects))
            torch.testing.assert_close(v, torch.tensor(deta_alpha, device=v.device))
        elif k in {"hota_alpha"}:
            torch.testing.assert_close(
                v, torch.tensor((result["deta_alpha"] * result["assa_alpha"]) ** 0.5, device=v.device)
            )
        elif k in {"num_unique_objects"}:
            assert v == len(gt_obj_ids)
        elif k in {"mostly_tracked"}:
            assert v == len(pred_obj_ids)
        elif k in {"mostly_lost", "num_misses"}:
            assert v == len(gt_obj_ids) - len(pred_obj_ids)
        elif k in {"mota"}:
            torch.testing.assert_close(
                v, 1 - (result["num_false_positives"] + result["num_misses"]) / result["num_unique_objects"]
            )
        else:
            torch.testing.assert_close(v, torch.tensor(0.0, device=v.device), msg=f"Unexpected value for key {k}: {v}")


@pytest.mark.parametrize("num_objects", [3, 5])
@pytest.mark.parametrize("num_frames", [1, 3])
@pytest.mark.parametrize("num_videos", [1, 3])
def test_wrapper_metric(num_objects: int, num_frames: int, num_videos: int, device: str) -> None:
    """Test update function with more than a single frame."""
    # GIVEN we have perfect overlap between predictions and GT
    obj_ids = torch.arange(num_objects, device=device)
    boxes = generate_boxes(len(obj_ids), device)

    # WHEN we evaluate the CLEAR metrics
    prediction_inputs: list[TrackingMetricsPredictions] = []
    ground_truth_inputs: list[TrackingMetricsGroundTruth] = []
    metric = TrackingMetric(class_names=["class_a"])
    for video_id in range(num_videos):
        for frame_id in range(num_frames):
            prediction_inputs.append(TrackingMetricsPredictions(pred_obj_ids=obj_ids, pred_boxes=boxes))
            ground_truth_inputs.append(
                TrackingMetricsGroundTruth(
                    gt_obj_ids=obj_ids, gt_boxes=boxes, video_name=str(video_id), frame_id=frame_id
                )
            )

    metric.update(prediction_inputs, ground_truth_inputs)
    result: dict[str, torch.Tensor] = cast(dict[str, torch.Tensor], metric.compute())

    # THEN we expect a perfect match
    total_num_objects = num_objects * num_videos
    assert_perfect_match(result, total_num_objects)
