"""Camera projection for the Sparse LaneCPP model.

To be deprecated with technical debt - Re-Use CameraModels from xtorch rather than own implementation.
https://pace-project.atlassian.net/wiki/spaces/per/pages/1372881169/3D+Lane+Main+Migration+Technical+Debt.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


# Copyright (c) 2022 The PersFormer Authors. All Rights Reserved.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
#
# Modified by Robert Bosch GmbH and Cariad SE

import torch

from xcontract.data.definitions.image import HW


def convert_to_pixel_coordinates(
    x: torch.Tensor,
    y: torch.Tensor,
    z: torch.Tensor,
    principal_point: torch.Tensor,
    focal_length: torch.Tensor,
    is_pinhole_model: torch.Tensor | bool,  # noqa: FBT001
    min_valid_distance: float,
) -> tuple[torch.Tensor, torch.Tensor]:
    """Convert a batch of 3D or 4D light ray vectors to 2D pixel coordinates using intrinsic camera parameters.

    Parameters:
        x: x coordinates of the sampling light ray.
        y: y coordinates of the sampling light ray.
        z: z coordinates of the sampling light ray.
        principal_point: The principal points tensor of shape (batch_size, 2).
        focal_length: The focal lengths tensor of shape (batch_size, 2).
        is_pinhole_model: A Boolean tensor indicating whether each sample or a boolean flag indicating all samples
            in the batch is using a pinhole model.
        min_valid_distance: Points closer to the camera are considered invalid.

    Returns:
        pixel_coordinates: the 2D pixel coordinates tensor of shape (batch_size, num_points, 2).
        invalid_mask: A mask indicating invalid points.
    """

    # Caution: NANs must be avoided in either of the two models even though only one is selected in the end.
    #          It seems NANS might otherwise still propagate in the backward pass.
    #          To this end, the distance of the input positions is checked against the minimum distance parameter.

    if isinstance(is_pinhole_model, bool):
        if is_pinhole_model:
            return _compute_pinhole(
                x,
                y,
                z,
                principal_point,
                focal_length,
                min_valid_distance,
            )
        return _compute_cylindrical(
            x,
            y,
            z,
            principal_point,
            focal_length,
            min_valid_distance,
        )
    pinhole_coords, pinhole_invalid = _compute_pinhole(
        x,
        y,
        z,
        principal_point,
        focal_length,
        min_valid_distance,
    )
    cylindrical_coords, cylindrical_invalid = _compute_cylindrical(
        x,
        y,
        z,
        principal_point,
        focal_length,
        min_valid_distance,
    )

    # Controlflow can't depend on model inputs. Beware that both models are computed for all samples.
    # if is_pinhole_model is a Tensor, it may vary per tensor element
    pixel_coordinates = torch.where(is_pinhole_model[:, :, None], pinhole_coords, cylindrical_coords)
    invalid_mask = torch.where(is_pinhole_model, pinhole_invalid, cylindrical_invalid)

    return pixel_coordinates, invalid_mask


class VehicleToCameraCoordinates(torch.nn.Module):
    """Transform 3D coordinates from vehicle coordinates (DIN7k) to camera coordinate system (CV convention).

    This functionality is encapsulated in a torch module for easier PTQ
    quantization override later.
    """

    def forward(
        self,
        x_vehicle_coord: torch.Tensor,
        y_vehicle_coord: torch.Tensor,
        z_vehicle_coord: torch.Tensor,
        extrinsics: torch.Tensor,
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Transform 3D coordinates to camera coordinates.

        Args:
            x_vehicle_coord: The x coordinates of shape (batch_size, num_points).
            y_vehicle_coord: The y coordinates of shape (batch_size, num_points).
            z_vehicle_coord: The z coordinates of shape (batch_size, num_points).
            extrinsics: The extrinsics matrix of shape (batch_size, 4, 4).

        Returns:
            x_camera_coord: The transformed x coordinates in camera coordinates.
            y_camera_coord: The transformed y coordinates in camera coordinates.
            z_camera_coord: The transformed z coordinates in camera coordinates.
        """

        extrinsics = extrinsics[..., None]

        # Extract the extrinsics matrix components.
        # This is better for quantization than using a single MatMul:
        # coords3d.flatten(1, 2) @ extrinsics.permute(0, 2, 1)
        h00, h01, h02, h03 = (
            extrinsics[:, 0, 0, :],
            extrinsics[:, 0, 1, :],
            extrinsics[:, 0, 2, :],
            extrinsics[:, 0, 3, :],
        )
        h10, h11, h12, h13 = (
            extrinsics[:, 1, 0, :],
            extrinsics[:, 1, 1, :],
            extrinsics[:, 1, 2, :],
            extrinsics[:, 1, 3, :],
        )
        h20, h21, h22, h23 = (
            extrinsics[:, 2, 0, :],
            extrinsics[:, 2, 1, :],
            extrinsics[:, 2, 2, :],
            extrinsics[:, 2, 3, :],
        )
        x_camera_coord = h00 * x_vehicle_coord + h01 * y_vehicle_coord + h02 * z_vehicle_coord + h03
        y_camera_coord = h10 * x_vehicle_coord + h11 * y_vehicle_coord + h12 * z_vehicle_coord + h13
        z_camera_coord = h20 * x_vehicle_coord + h21 * y_vehicle_coord + h22 * z_vehicle_coord + h23

        return x_camera_coord, y_camera_coord, z_camera_coord


def convert_3d_to_pixel_coordinates(
    x_camera_coord: torch.Tensor,
    y_camera_coord: torch.Tensor,
    z_camera_coord: torch.Tensor,
    principal_point: torch.Tensor,
    focal_length: torch.Tensor,
    *,
    is_pinhole_model: torch.Tensor | bool,
    image_shape: HW,
    min_valid_distance: float,
    normalize: bool = True,
) -> tuple[torch.Tensor, torch.Tensor]:
    """Convert 3D coordinates to 2D image coordinates.

    Args:
        x_camera_coord: The transformed x coordinates in camera coordinates.
        y_camera_coord: The transformed y coordinates in camera coordinates.
        z_camera_coord: The transformed z coordinates in camera coordinates.
        principal_point: The principal points of shape (batch_size, 2).
        focal_length: The focal lengths of shape (batch_size, 2).
        is_pinhole_model: A Boolean tensor or a boolean flag indicating whether each sample in the batch is
            using a pinhole model.
        image_shape: The image shape.
        min_valid_distance: Points closer to the camera are considered invalid.
        normalize: If True, normalize the pixel coordinates by the image dimensions.

    Returns:
        A tensor containing the x-coordinates and y-coordinates and a mask indicating invalid points.

    """
    # Apply intrinsic transformation using convert_to_pixel_coordinates
    pixel_coordinates, invalid_mask = convert_to_pixel_coordinates(
        x_camera_coord,
        y_camera_coord,
        z_camera_coord,
        principal_point,
        focal_length,
        is_pinhole_model,
        min_valid_distance,
    )

    # Normalize with width (x) and height (y)
    if normalize:
        pixel_coordinates /= pixel_coordinates.new_tensor([image_shape.width, image_shape.height])

    return pixel_coordinates, invalid_mask


def _compute_pinhole(
    x: torch.Tensor,
    y: torch.Tensor,
    z: torch.Tensor,
    principal_point: torch.Tensor,
    focal_length: torch.Tensor,
    min_valid_distance: float,
) -> tuple[torch.Tensor, torch.Tensor]:
    """Computes the pixel values of a light ray using the pinhole camera model."""

    invalid_mask = z <= min_valid_distance
    # AIMET supports only the inplace version of this op
    z_masked = torch.masked_fill(z, invalid_mask, min_valid_distance)  # Avoid division by zero.

    pixel_x_pinhole = principal_point[:, None, 0] + (focal_length[:, None, 0] * (x / z_masked))
    pixel_y_pinhole = principal_point[:, None, 1] + (focal_length[:, None, 1] * (y / z_masked))
    return torch.stack((pixel_x_pinhole, pixel_y_pinhole), dim=-1), invalid_mask


def _compute_cylindrical(
    x: torch.Tensor,
    y: torch.Tensor,
    z: torch.Tensor,
    principal_point: torch.Tensor,
    focal_length: torch.Tensor,
    min_valid_distance: float,
) -> tuple[torch.Tensor, torch.Tensor]:
    """Computes the pixel values of a light ray using the cylindrical camera model."""

    radi = torch.hypot(x, z)
    invalid_mask = radi <= min_valid_distance
    radi = torch.masked_fill(radi, invalid_mask, min_valid_distance)

    pixel_x_cylindrical = focal_length[:, None, 0] * torch.atan2(x, z)
    pixel_y_cylindrical = (focal_length[:, None, 1] / radi) * y
    pixel_xy_cylindrical = torch.stack((pixel_x_cylindrical, pixel_y_cylindrical), dim=-1) + principal_point[:, None, :]

    return pixel_xy_cylindrical, invalid_mask
