"""Bev data structures."""

from __future__ import annotations

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2023 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""


from collections.abc import Sized
from typing import NamedTuple

import numpy as np
import numpy.typing as npt
import torch

from xcontract.camera_models.definitions import CameraModelType
from xcontract.data.definitions.image import HW
from xcontract.data.definitions.tracking import TrackMappingErrors
from xcontract.data.definitions.usage import ValueKey


class MultiViewFrameData(NamedTuple):
    """Represents multi-view frame data.

    extrinsics:     Transformations from I cv camera frames at exposure time to iso vehicle
                        frame at annotation time, represented as Ix1x4x4 matrix
    intrinsics:     Camera intrinsic parameters for I cameras, represented as Ix1x1x5 matrix.
                        Focal lengths (x, y), principal point (x, y) and cut angle (in radian / deg)
    images:         Camera images
    camera_types:   Integer representing camera model specification.
    """

    extrinsics: torch.Tensor
    intrinsic_params: torch.Tensor
    images: torch.Tensor
    camera_types: torch.Tensor

    @staticmethod
    def dummy(
        num_cameras: int,
        image_shape: HW,
        num_channels: int = 3,
        batch_size: int | None = None,
        device: str | torch.device = "cpu",
    ) -> MultiViewFrameData:
        """Creates dummy data."""

        images_shape = [num_cameras, num_channels, int(image_shape.height), int(image_shape.width)]
        images = torch.randint(low=0, high=256, size=images_shape, dtype=torch.uint8, device=device)

        # Estimate principal point
        default_u0_px = image_shape.width / 2
        default_v0_px = image_shape.height / 2
        default_focal_length = 2000
        dummy_intrinsics = torch.tensor(
            [[[default_focal_length, default_focal_length, default_u0_px, default_v0_px, 0.0]]],
            dtype=torch.float32,
            device=device,
        )
        # cv coordinates to din70k
        dummy_extrinsics = torch.tensor(
            [
                [
                    [0.0, 0.0, 1.0, 0.0],
                    [-1.0, 0.0, 0.0, 0.0],
                    [0.0, -1.0, 0.0, 1.0],
                    [0.0, 0.0, 0.0, 1.0],
                ]
            ],
            dtype=torch.float32,
            device=device,
        )
        dummy_camera_types = torch.tensor(
            [[[int(CameraModelType.PINHOLE)]]],
            dtype=torch.float32,
            device=device,
        )

        if batch_size is None:
            intrinsics = dummy_intrinsics.expand(num_cameras, -1, -1, -1)
            extrinsics = dummy_extrinsics.expand(num_cameras, -1, -1, -1)
            camera_types = dummy_camera_types.expand(num_cameras, -1, -1, -1)
        else:
            images = images.expand(batch_size, -1, -1, -1, -1)
            intrinsics = dummy_intrinsics.expand(batch_size, num_cameras, -1, -1, -1)
            extrinsics = dummy_extrinsics.expand(batch_size, num_cameras, -1, -1, -1)
            camera_types = dummy_camera_types.expand(batch_size, num_cameras, -1, -1, -1)

        return MultiViewFrameData(
            extrinsics=extrinsics,
            intrinsic_params=intrinsics,
            images=images,
            camera_types=camera_types,
        )

    def adapt_to_target_cam_count(self, target_cam_count: int) -> MultiViewFrameData:
        """Adapts the data such that the target amount of camera images is met.

        This function will randomly add existing camera images with their respective calibration
        till the target amount of cameras is reached.

        Args:
            target_cam_count: Amount of cameras which should be contained in the new MultiViewFrameData object.

        Returns:
            New MultiViewFrameData object adapted to the amount of target cameras.
        """

        is_batch = self.batch_size is not None

        if is_batch:
            # make camera dimension the first one to facilitate logic
            images = torch.transpose(self.images, 0, 1)
            extrinsics = torch.transpose(self.extrinsics, 0, 1)
            intrinsics = torch.transpose(self.intrinsic_params, 0, 1)
            camera_types = torch.transpose(self.camera_types, 0, 1)
        else:
            images = self.images
            extrinsics = self.extrinsics
            intrinsics = self.intrinsic_params
            camera_types = self.camera_types

        current_cam_count = images.shape[0]

        if current_cam_count == target_cam_count:
            return self

        if target_cam_count < current_cam_count:
            assert target_cam_count > 0
            shuffled_cam_indices = torch.randperm(current_cam_count, device=images.device)[:target_cam_count]
            images_list = []
            extrinsics_list = []
            intrinsics_list = []
            camera_types_list = []
            for i in range(target_cam_count):
                images_list.append(images[shuffled_cam_indices[i]])
                extrinsics_list.append(extrinsics[shuffled_cam_indices[i]])
                intrinsics_list.append(intrinsics[shuffled_cam_indices[i]])
                camera_types_list.append(camera_types[shuffled_cam_indices[i]])

            images = torch.stack(images_list, dim=0)
            extrinsics = torch.stack(extrinsics_list, dim=0)
            intrinsics = torch.stack(intrinsics_list, dim=0)
            camera_types = torch.stack(camera_types_list, dim=0)

            if is_batch:
                images = torch.transpose(images, 0, 1)
                extrinsics = torch.transpose(extrinsics, 0, 1)
                intrinsics = torch.transpose(intrinsics, 0, 1)
                camera_types = torch.transpose(camera_types, 0, 1)

            return MultiViewFrameData(
                intrinsic_params=intrinsics,
                extrinsics=extrinsics,
                images=images,
                camera_types=camera_types,
            )

        diff_cam_count = target_cam_count - current_cam_count
        if diff_cam_count > current_cam_count:
            msg = (
                f"Can't adapt camera count to a target camera count greater than two times the current "
                f"camera count. {target_cam_count}(target) > 2x{current_cam_count}(current)"
            )
            raise ValueError(msg)

        shuffled_cam_indices = torch.randperm(current_cam_count, device=images.device)

        for i in range(diff_cam_count):
            images = torch.cat([images, images[shuffled_cam_indices[i]].unsqueeze(0)], dim=0)
            extrinsics = torch.cat([extrinsics, extrinsics[shuffled_cam_indices[i]].unsqueeze(0)], dim=0)
            intrinsics = torch.cat([intrinsics, intrinsics[shuffled_cam_indices[i]].unsqueeze(0)], dim=0)
            camera_types = torch.cat([camera_types, camera_types[shuffled_cam_indices[i]].unsqueeze(0)], dim=0)

        if is_batch:
            images = torch.transpose(images, 0, 1)
            extrinsics = torch.transpose(extrinsics, 0, 1)
            intrinsics = torch.transpose(intrinsics, 0, 1)
            camera_types = torch.transpose(camera_types, 0, 1)

        return MultiViewFrameData(
            intrinsic_params=intrinsics,
            extrinsics=extrinsics,
            images=images,
            camera_types=camera_types,
        )

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if it is not batched."""
        pcs_shape = self.images.shape

        if len(pcs_shape) < 5:
            return None
        return pcs_shape[0] if pcs_shape[0] is not None else 1


MultiViewFrame = dict[ValueKey, bool | str | MultiViewFrameData]


class LidarPointCloudData(NamedTuple):
    """Lidar pointcloud data."""

    # point cloud with shape BxN_POINTSx3 with the last dimension corresponding to x, y, z
    point_cloud: torch.Tensor
    # point cloud intensity shape BxN_POINTSx1
    intensity: torch.Tensor

    @staticmethod
    def dummy(
        num_lidar_points: int,
        batch_size: int | None = None,
        device: str | torch.device = "cpu",
    ) -> LidarPointCloudData:
        """Returns dummy data containing only zeros of this datastructure."""

        batch_prefix = [] if batch_size is None else [batch_size]

        return LidarPointCloudData(
            point_cloud=torch.zeros([*batch_prefix, num_lidar_points, 3], dtype=torch.float32, device=device),
            intensity=torch.zeros([*batch_prefix, num_lidar_points, 1], dtype=torch.float32, device=device),
        )

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if it is not batched."""
        pcs_shape = self.point_cloud.shape

        if len(pcs_shape) < 3:
            return None
        return pcs_shape[0] if pcs_shape[0] is not None else 1


class RadarPointCloudData(NamedTuple):
    """Radar pointcloud data."""

    # point cloud with shape BxN_POINTSx3 with the last dimension corresponding to x, y, z
    point_cloud: torch.Tensor
    # features of the radar points BxN_POINTSxN_FEATURES
    point_features: torch.Tensor

    @staticmethod
    def dummy(
        num_radar_points: int,
        num_point_features: int,
        batch_size: int | None = None,
        device: str | torch.device = "cpu",
    ) -> RadarPointCloudData:
        """Returns dummy data containing only zeros of this datastructure."""

        batch_prefix = [] if batch_size is None else [batch_size]

        return RadarPointCloudData(
            point_cloud=torch.zeros([*batch_prefix, num_radar_points, 3], dtype=torch.float32, device=device),
            point_features=torch.zeros(
                [*batch_prefix, num_radar_points, num_point_features], dtype=torch.float32, device=device
            ),
        )

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if it is not batched."""
        pcs_shape = self.point_cloud.shape

        if len(pcs_shape) < 3:
            return None
        return pcs_shape[0] if pcs_shape[0] is not None else 1


class FrameMetaTuple(NamedTuple):
    """Frame meta data."""

    frame_idx: torch.Tensor
    sequence_name: str | list[str] | torch.Tensor
    timestamp_ns: torch.Tensor
    # 0 if first frame, >0 if not first frame, -1 if not set
    first_frame_in_sequence: torch.Tensor = torch.tensor((-1), dtype=torch.int8)


class BevBox3dGTTuple(NamedTuple):
    """Ground truth properties for 3d objects in the bird's eye view frame."""

    # Classification
    box_class_ids: torch.Tensor
    # 3d attributes
    box_center: torch.Tensor
    box_dimension: torch.Tensor
    box_rotations: torch.Tensor
    # mask for valid boxes
    box_mask: torch.Tensor

    bev_grid_mask: torch.Tensor

    # temporal
    # NOTE: torch does not support string tensors, so use a list instead (only required for loader/callbacks anyway)
    sequence_name: str | list[str]
    per_sequence_track_id: torch.Tensor
    global_track_id: torch.Tensor
    timestamp_ns: torch.Tensor  # single timestamp for all boxes

    # mask for valid motion properties
    box_motion_mask: torch.Tensor
    # motion properties
    box_velocity_xy: torch.Tensor
    box_acceleration_xy: torch.Tensor
    box_yaw_rate: torch.Tensor

    @staticmethod
    def dummy(
        max_box_count: int,
        bev_grid_cell_size: tuple[int, int],
        sequence_name: str = "",
        batch_size: int | None = None,
        device: str | torch.device = "cpu",
        *,
        has_motion_properties: bool = False,
    ) -> BevBox3dGTTuple:
        """Creates ignore box3d ground truth."""

        batch_prefix = [] if batch_size is None else [batch_size]

        _sequence_name = sequence_name
        if batch_size is not None:
            _sequence_name = [sequence_name for _ in range(batch_size)]

        return BevBox3dGTTuple(
            box_class_ids=torch.zeros([*batch_prefix, max_box_count], dtype=torch.int64, device=device),
            box_center=torch.zeros([*batch_prefix, max_box_count, 3], dtype=torch.float32, device=device),
            box_dimension=torch.zeros([*batch_prefix, max_box_count, 3], dtype=torch.float32, device=device),
            box_rotations=torch.zeros([*batch_prefix, max_box_count, 3, 3], dtype=torch.float32, device=device),
            box_mask=torch.zeros([*batch_prefix, max_box_count], dtype=torch.bool, device=device),
            bev_grid_mask=torch.zeros(
                [*batch_prefix, bev_grid_cell_size[0], bev_grid_cell_size[1]], dtype=torch.bool, device=device
            ),
            sequence_name=_sequence_name,
            per_sequence_track_id=torch.full(
                [*batch_prefix, max_box_count],
                int(TrackMappingErrors.K_NOT_SET),
                dtype=torch.long,
                device=device,
            ),
            global_track_id=torch.full(
                [*batch_prefix, max_box_count],
                int(TrackMappingErrors.K_NOT_SET),
                dtype=torch.long,
                device=device,
            ),
            timestamp_ns=torch.zeros([*batch_prefix], dtype=torch.int64, device=device),
            box_motion_mask=torch.ones([*batch_prefix, max_box_count], dtype=torch.bool, device=device)
            if has_motion_properties
            else torch.zeros([*batch_prefix, max_box_count], dtype=torch.bool, device=device),
            box_velocity_xy=torch.zeros([*batch_prefix, max_box_count, 2], dtype=torch.float32, device=device),
            box_acceleration_xy=torch.zeros([*batch_prefix, max_box_count, 2], dtype=torch.float32, device=device),
            box_yaw_rate=torch.zeros([*batch_prefix, max_box_count, 1], dtype=torch.float32, device=device),
        )

    def get_box_corners(self) -> torch.Tensor:
        """Get the 3D corners of the boxes.

        Returns:
            Corners of the boxes in the shape [..., N, 8, 3]. Similar shape as 'self.box_center' with one
            dimension added on index [-2] for the 8 corners of the box. The order of the corners follows the
            CornerBoxIndices definition (xcontract/src/xcontract/geometry/definitions/box_indices.py::CornerBoxIndices).
            Downstream algorithms might rely on this order so refrain from changing it without a good reason.
        """

        length, width, height = self.box_dimension[..., 0], self.box_dimension[..., 1], self.box_dimension[..., 2]

        # get the corners of the box in the local coordinate system
        rlb = torch.stack([-length / 2, width / 2, -height / 2], dim=-1)
        rrb = torch.stack([-length / 2, -width / 2, -height / 2], dim=-1)
        frb = torch.stack([length / 2, -width / 2, -height / 2], dim=-1)
        flb = torch.stack([length / 2, width / 2, -height / 2], dim=-1)
        rlt = torch.stack([-length / 2, width / 2, height / 2], dim=-1)
        rrt = torch.stack([-length / 2, -width / 2, height / 2], dim=-1)
        frt = torch.stack([length / 2, -width / 2, height / 2], dim=-1)
        flt = torch.stack([length / 2, width / 2, height / 2], dim=-1)
        corners = torch.stack([rlb, rrb, frb, flb, rlt, rrt, frt, flt], dim=-2)  # Shape: [..., N, 8, 3]

        # Rotate the corners by the orientations of the boxes and add the center
        # [..., N, 8, 3] @ [..., N, 3, 3] -> [..., N, 8, 3]
        # The reason for using torch.einsum is that it allows transposing the last two dimensions of a tensor with
        # unknown shape
        corners = corners @ torch.transpose(self.box_rotations, -1, -2) + torch.unsqueeze(self.box_center, dim=-2)
        return corners

    def apply_mask(self, batch_mask: torch.Tensor) -> BevBox3dGTTuple:
        """Applies a batch mask to the box3d ground truth data.

        Args:
            batch_mask: A boolean tensor of shape [B] where B is the batch size. True indicates that the
                corresponding batch element should be kept, False indicates that it should be removed.

        Returns:
            A new BevBox3dGTTuple with the mask applied.
        """
        if self.batch_size is None:
            msg = "Batch mask can only be applied to batched data."
            raise ValueError(msg)
        if batch_mask.shape != (self.batch_size,):
            msg = (
                f"Batch mask must be a 1D tensor with the same number of elements as the batch size. "
                f"Expected ({self.batch_size},), got {batch_mask.shape}."
            )
            raise ValueError(msg)
        if (
            isinstance(self.sequence_name, str)
            or not isinstance(self.sequence_name, Sized)
            or len(self.sequence_name) != self.batch_size
        ):
            msg = "Sequence name must be a list with the same number of elements as the batch size. "
            raise ValueError(msg)

        return BevBox3dGTTuple(
            box_class_ids=self.box_class_ids[batch_mask],
            box_center=self.box_center[batch_mask],
            box_dimension=self.box_dimension[batch_mask],
            box_rotations=self.box_rotations[batch_mask],
            box_mask=self.box_mask[batch_mask],
            bev_grid_mask=self.bev_grid_mask[batch_mask],
            sequence_name=[sequence_name for sequence_name, valid in zip(self.sequence_name, batch_mask) if valid],
            per_sequence_track_id=self.per_sequence_track_id[batch_mask],
            global_track_id=self.global_track_id[batch_mask],
            timestamp_ns=self.timestamp_ns[batch_mask],
            box_motion_mask=self.box_motion_mask[batch_mask],
            box_velocity_xy=self.box_velocity_xy[batch_mask],
            box_acceleration_xy=self.box_acceleration_xy[batch_mask],
            box_yaw_rate=self.box_yaw_rate[batch_mask],
        )

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if unbatched."""
        if len(self.box_mask.shape) < 2:
            return None

        return self.box_mask.shape[0] if self.box_mask.shape[0] is not None else 1


class ParkingGTTuple(NamedTuple):
    """Ground truth data for parking space detection.

    Attributes:
        quadrilateral: Tensor [N, 2, 4] representing four 2D coordinate points of parking spaces.
        center: Tensor [N, 2]  2D coordinates of the center of parking spaces.
        occupancy: Tensor [N,] indicating whether a parking space is occupied (1) or free (0).
        dedication: Tensor [N,] representing the dedication (e.g handicapped) status of parking spaces.
        parking_system: Tensor [N,] indicating the parking system type (e.g. coin based).
        elevation: Tensor [N,] representing the elevation values (high, low) of parking spaces.
        line_width: Tensor [N,] indicating the line widths of parking spaces.
        color: Tensor [N,] representing the color (white, yellow, red, etc.) of parking spaces.
        point_visibility: Tensor [N, 4] indicating if a corner point is visible or not.
        bev_grid_mask: Tensor [H, W] representing the bird's-eye view grid mask.
        quadrilateral_mask: Tensor [N, M] indicating the mask for valid quadrilaterals.

    Methods:
        Creates a dummy instance of ParkingGTTuple with zero-initialized tensors for the ignore dataloader.
    """

    quadrilateral: torch.Tensor
    center: torch.Tensor
    occupancy: torch.Tensor
    dedication: torch.Tensor
    parking_system: torch.Tensor
    elevation: torch.Tensor
    line_width: torch.Tensor
    color: torch.Tensor
    point_visibility: torch.Tensor
    bev_grid_mask: torch.Tensor
    quadrilateral_mask: torch.Tensor

    @staticmethod
    def dummy(
        max_parking_spaces: int,
        bev_grid_cell_size: tuple[int, int] | torch.Size,
        sequence_name: str = "",
        num_instances: int | None = None,
        device: str | torch.device = "cpu",
    ) -> ParkingGTTuple:
        """Creates a dummy (ignore) parking ground truth tuple with zeroed tensors for all fields.

        Args:
            max_parking_spaces: The maximum number of parking spaces to generate ground truth for.
            bev_grid_cell_size: The size of the BEV (Bird's Eye View) grid as (height, width) or a torch.Size.
            sequence_name: The name of the sequence. Defaults to "".
            num_instances: The number of instances (batches) to generate. If None, generates a single instance.
            device: The device on which to allocate the tensors. Defaults to "cpu".

        Returns:
            ParkingGTTuple: A tuple containing zeroed tensors for all parking ground truth fields, shaped according to
            the provided parameters.
        """

        batch_prefix = [] if num_instances is None else [num_instances]

        sequence_name_list = [sequence_name for _ in range(max_parking_spaces)]
        if num_instances is not None:
            sequence_name_list = [sequence_name_list for _ in range(num_instances)]

        return ParkingGTTuple(
            quadrilateral=torch.zeros([*batch_prefix, 4, 2], dtype=torch.int64, device=device),
            center=torch.zeros([*batch_prefix, 2], dtype=torch.float32, device=device),
            occupancy=torch.zeros(
                [*batch_prefix], dtype=torch.float32, device=device
            ),  # shape: [N,]  # N is number of parking spaces
            dedication=torch.zeros([*batch_prefix], dtype=torch.float32, device=device),  # shape: [N,]
            parking_system=torch.zeros([*batch_prefix], dtype=torch.float32, device=device),  # shape: [N,]
            elevation=torch.zeros([*batch_prefix], dtype=torch.float32, device=device),  # shape: [N,]
            line_width=torch.zeros([*batch_prefix], dtype=torch.float32, device=device),  # shape: [N,]
            color=torch.zeros([*batch_prefix], dtype=torch.float32, device=device),  # shape: [N,]
            point_visibility=torch.zeros([*batch_prefix, 4], dtype=torch.float32, device=device),  # shape: [N, 4]
            bev_grid_mask=torch.zeros(bev_grid_cell_size, dtype=torch.bool, device=device),  # shape: [H, W]
            quadrilateral_mask=torch.zeros([*batch_prefix], dtype=torch.bool, device=device),  # shape: [N]
        )


class OccupancyGTTuple(NamedTuple):
    """Ground truth data for 2.5D Occupancy."""

    # Occupancy predictions
    semantic_map: torch.Tensor
    height_map: torch.Tensor
    visibility_map: torch.Tensor

    # GT mask of valid BEV grid cells
    occupancy_mask: torch.Tensor

    @staticmethod
    def dummy(
        grid_size_x: int,
        grid_size_y: int,
        ignore_class_id: int,
        batch_size: int | None = None,
        device: str | torch.device = "cpu",
    ) -> OccupancyGTTuple:
        """Creates ignore occupancy ground truth."""

        batch_prefix = [] if batch_size is None else [batch_size]

        return OccupancyGTTuple(
            semantic_map=torch.full(
                [*batch_prefix, 1, grid_size_x, grid_size_y],
                ignore_class_id,
                dtype=torch.uint8,
                device=device,
            ),
            height_map=torch.zeros([*batch_prefix, 1, grid_size_x, grid_size_y], dtype=torch.float32, device=device),
            visibility_map=torch.zeros([*batch_prefix, 1, grid_size_x, grid_size_y], dtype=torch.bool, device=device),
            occupancy_mask=torch.zeros([*batch_prefix, 1, grid_size_x, grid_size_y], dtype=torch.bool, device=device),
        )

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if unbatched."""
        if self.semantic_map.dim() == 3:
            return None

        return self.semantic_map.size(0)


class EgoMotionData(NamedTuple):
    """Ego motion data.

    The ego motion refers to the _inertial frame_ which typically was initiated at vehicle startup.
    If the ego_poses are interpreted as transformation matrices, they transform points from the vehicle frame at the
    respective timestamp to the intertial frame.

    All axis are standard ISO.

    Attributes:
        ego_poses: Pose or poses with respect to the inertial frame in [m]. Shape: [N, 4, 4] or [BS, N, 4, 4]
        ego_pose_timestamps_ns: Timestamp or timestamps in [ns] corresponding to each ego pose. Shape: [N] or [BS, N]
        ego_velocities: Velocity or velocities [m/s] of ego estimated at the center of rear axle on ground level.
            Shape: [N, 3] or [BS, N, 3]
        ego_angular_rates: Offset compensated angular rate or rates (roll, pitch, yaw) [rad/s].
            Shape: [N, 3] or [BS, N, 3]
    """

    ego_poses: torch.Tensor
    ego_pose_timestamps_ns: torch.Tensor
    ego_velocities: torch.Tensor
    ego_angular_rates: torch.Tensor

    @staticmethod
    def dummy(
        batch_size: int | None = None,
        num_meas: int | None = None,
        device: str | torch.device = "cpu",
    ) -> EgoMotionData:
        """Creates ignore ego motion data."""

        batch_prefix = [] if batch_size is None else [batch_size]

        num_meas = 1 if num_meas is None else num_meas

        return EgoMotionData(
            ego_poses=torch.eye(4, dtype=torch.float64, device=device)
            .unsqueeze(0)
            .expand([*batch_prefix, num_meas, -1, -1]),
            ego_pose_timestamps_ns=torch.zeros([*batch_prefix, num_meas], dtype=torch.int64, device=device),
            ego_velocities=torch.zeros([*batch_prefix, num_meas, 3], dtype=torch.float32, device=device),
            ego_angular_rates=torch.zeros([*batch_prefix, num_meas, 3], dtype=torch.float32, device=device),
        )

    @property
    def number_of_measurements(self) -> int:
        """Return the number of measurements."""
        return self.ego_poses.size(-3)

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if unbatched."""
        if self.ego_poses.dim() == 3:
            return None

        return self.ego_poses.size(0)


class LaneSegmentGTTuple(NamedTuple):
    """Ground truth data for lane segments.

    Attributes:
        points: Tensor [N, 3, P, 3] representing N segments with 3 polylines (center, left, right) with P 3d points.
        labels: Tensor [N] indicating whether a parking space is occupied (1) or free (0).
        type_left: Tensor [N] representing the dedication (e.g handicapped) status of parking spaces.
        type_right: Tensor [N] indicating the parking system type (e.g. coin based).
        relationship: Tensor [N, N] representing the elevation values (high, low) of parking spaces.
        valid_mask: Tensor [N] indicating which segments are valid.
        instance_masks: Tensor [N, Y, X] containing an instance mask for each segment.
        ignore_loss: Tensor [N] indicating whether the loss for this segment should be ignored.
    """

    points: torch.Tensor
    labels: torch.Tensor
    type_left: torch.Tensor
    type_right: torch.Tensor
    relationship: torch.Tensor
    valid_mask: torch.Tensor
    instance_masks: torch.Tensor
    ignore_loss: torch.Tensor

    @staticmethod
    def dummy(
        num_segments: int,
        num_points: int,
        grid_size_x: int,
        grid_size_y: int,
        batch_size: int | None = None,
        device: str | torch.device = "cpu",
    ) -> LaneSegmentGTTuple:
        """Creates a dummy instance of LaneSegmentGTTuple filled with zeros."""

        batch_prefix = [] if batch_size is None else [batch_size]

        return LaneSegmentGTTuple(
            points=torch.zeros([*batch_prefix, num_segments, 3, num_points, 3], dtype=torch.float32, device=device),
            labels=torch.zeros([*batch_prefix, num_segments], dtype=torch.int8, device=device),
            type_left=torch.zeros([*batch_prefix, num_segments], dtype=torch.int8, device=device),
            type_right=torch.zeros([*batch_prefix, num_segments], dtype=torch.int8, device=device),
            relationship=torch.zeros([*batch_prefix, num_segments, num_segments], dtype=torch.int16, device=device),
            valid_mask=torch.zeros([*batch_prefix, num_segments], dtype=torch.bool, device=device),
            instance_masks=torch.zeros(
                [*batch_prefix, num_segments, grid_size_y, grid_size_x], dtype=torch.uint8, device=device
            ),
            ignore_loss=torch.zeros([*batch_prefix, num_segments], dtype=torch.bool, device=device),
        )

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if unbatched."""
        return None if self.valid_mask.dim() == 1 else self.valid_mask.size(0)


class SDMapInputData(NamedTuple):
    """SD map input data.

    Attributes:
        points: Tensor [N, 1, P, 3] representing N polylines with P 3d points.
        lengths: Tensor [N] storing the length of each segment (in meters, not number of points).
        relationship: Tensor [N, N] representing the successor relationship.
        valid_mask: Tensor [N] indicating which segments are valid.
    """

    points: torch.Tensor
    lengths: torch.Tensor
    relationship: torch.Tensor
    valid_mask: torch.Tensor

    @staticmethod
    def dummy(
        num_segments: int,
        num_points: int,
        batch_size: int | None = None,
        device: str | torch.device = "cpu",
    ) -> SDMapInputData:
        """Creates a dummy instance of SDMapInputData filled with zeros."""

        batch_prefix = [] if batch_size is None else [batch_size]

        return SDMapInputData(
            points=torch.zeros([*batch_prefix, num_segments, 1, num_points, 3], dtype=torch.float32, device=device),
            lengths=torch.zeros([*batch_prefix, num_segments], dtype=torch.float64, device=device),
            relationship=torch.zeros([*batch_prefix, num_segments, num_segments], dtype=torch.int32, device=device),
            valid_mask=torch.zeros([*batch_prefix, num_segments], dtype=torch.bool, device=device),
        )

    @property
    def batch_size(self) -> int | None:
        """Return the batch size or None if unbatched."""
        return None if self.valid_mask.dim() == 1 else self.valid_mask.size(0)


BevData = (
    MultiViewFrameData
    | LidarPointCloudData
    | RadarPointCloudData
    | BevBox3dGTTuple
    | OccupancyGTTuple
    | EgoMotionData
    | LaneSegmentGTTuple
    | SDMapInputData
    | ParkingGTTuple
    | torch.Tensor
    | FrameMetaTuple
    | npt.NDArray[np.float32]
)

BevDataDict = dict[ValueKey, bool | str | BevData]


def make_bev_data(
    valid: bool | torch.Tensor | npt.NDArray[np.float32],  # noqa: FBT001
    identifier: str | torch.Tensor | npt.NDArray[np.float32],
    data: BevData,
) -> BevDataDict:
    """Creates Bev Data structure in a type safe way."""
    return {
        ValueKey.VALID: valid,
        ValueKey.IDENTIFIER: identifier,
        ValueKey.DATA: data,
    }
