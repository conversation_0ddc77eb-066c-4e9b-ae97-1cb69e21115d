"""Losses for multi-task models."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from collections.abc import Mapping, Sequence
from typing import Any, Protocol, TypeAlias, runtime_checkable

import torch
from torch import nn

from xtorch.losses.interface import LossOutput
from xtorch.losses.meta.uncertainty_weighted_loss import (
    LOSS_TYPE_TO_WEIGHT,
    SublossConfig,
)
from xtorch.multi_task.task import Task

TaskSubLosses: TypeAlias = dict[str, dict[str, torch.Tensor]]


@runtime_checkable
class UncertaintyWeightableTask(Protocol):
    """Task that describes its sublosses."""

    def subloss_configs(self) -> dict[str, SublossConfig]:
        """Builds a dictionary that maps sublosses to their subloss configuration.

        Returns:
            A dictionary mapping sublosses to their subloss configuration.
        """
        ...


class UncertaintyWeightedTaskLoss(nn.Module):
    """Multi-task loss that weights task losses by their uncertainty.

    Based on the paper "Multi-Task Learning Using Uncertainty to Weigh Losses for Scene Geometry and Semantics",
    Kendall et al., 2018, URL: https://arxiv.org/abs/1705.07115.

    This class requires sublosses to be non-negative scalars, ideally averaged by batch size. It also extends the
    paper formulation by an optional weighting per task.
    """

    def __init__(
        self, tasks: Sequence[Task[Any, Any, Any, Any]], task_weights: Mapping[str, float] | None = None
    ) -> None:
        """Constructor of the loss.

        Even though the loss accept any task to its constructor, it requires them to adhere to the
        `UncertaintyWeightableTask` protocol, which is checked at runtime.
        """
        super().__init__()

        if len(tasks) == 0:
            msg = "At least one task is required."
            raise ValueError(msg)

        if task_weights is None:
            task_weights = {task.identifier(): 1.0 for task in tasks}

        for task in tasks:
            if task.identifier() not in task_weights:
                msg = f"Task {task.identifier()} is missing a weight."
                raise ValueError(msg)

        self._task_weights = task_weights
        self._task_id_to_loss_fn = nn.ModuleDict({task.identifier(): task.loss for task in tasks})
        self._subloss_id_to_config: dict[str, SublossConfig] = self._unpack_subloss_configs(tasks)
        self.log_sigmas = nn.ParameterDict(
            {
                subloss_id: nn.Parameter(torch.tensor(subloss_config.uw_log_sigma_init, requires_grad=True))
                for subloss_id, subloss_config in self._subloss_id_to_config.items()
            }
        )

    def _build_subloss_id(self, task_id: str, subloss_name: str) -> str:
        """Builds the subloss identifier."""
        return f"{task_id}/{subloss_name}"

    def _unpack_subloss_configs(self, tasks: Sequence[Task[Any, Any, Any, Any]]) -> dict[str, SublossConfig]:
        """Unpacks the subloss types from the tasks."""
        subloss_id_to_config: dict[str, SublossConfig] = {}
        for task in tasks:
            if not isinstance(task, UncertaintyWeightableTask):
                msg = f"Task {task} does not adhere to the 'UncertaintyWeightableTask' protocol."
                raise TypeError(msg)
            subloss_id_to_config.update(
                {
                    self._build_subloss_id(task.identifier(), subloss_id): subloss_config
                    for subloss_id, subloss_config in task.subloss_configs().items()
                }
            )
        return subloss_id_to_config

    def forward(self, task_outputs: dict[str, Any], task_targets: dict[str, Any]) -> LossOutput:
        """Combines the sublosses of each task, weighted by their estimated uncertainty.

        Note that this loss ignores the total_loss tasks may provide as it weights individual sublosses.

        Args:
            task_outputs: Outputs of the multi-task model.
            task_targets: Targets for each task.

        Returns:
            The total loss and various of its components gathered in `LossOutput`. The loss components include weighted
            and unweighted sublosses as well as subloss uncertainty weights (log-sigmas).
        """

        loss_components: dict[str, torch.Tensor] = {}
        weighted_sublosses: dict[str, torch.Tensor] = {}
        unweighted_sublosses: dict[str, torch.Tensor] = {}
        subloss_log_sigmas: dict[str, torch.Tensor] = {}
        weighted_task_losses: dict[str, torch.Tensor] = {}
        unweighted_task_losses: dict[str, torch.Tensor] = {}

        for task_id, task_output in task_outputs.items():
            task_target = task_targets[task_id]
            task_loss: LossOutput = self._task_id_to_loss_fn[task_id](task_output, task_target)

            # this buffer collects all weighted sublosses of a task before adding them altogether to the cross-task
            # weighted_sublosses dict. This is done to simplify the calculation of the task-level weighted loss
            # (weighted_task_loss).
            weighted_sublosses_buffer = {}

            for subloss_name, subloss in task_loss.sublosses.items():
                if subloss.shape != torch.Size([]):
                    msg = f"Subloss {subloss_name} of task {task_id} must be a scalar."
                    raise ValueError(msg)

                if subloss.item() < 0.0:
                    msg = f"Subloss {subloss_name} of task {task_id} must be non-negative."
                    raise ValueError(msg)

                subloss_id = self._build_subloss_id(task_id, subloss_name)
                loss_type_weight = LOSS_TYPE_TO_WEIGHT[self._subloss_id_to_config[subloss_id].loss_type]
                post_uw_weight = self._subloss_id_to_config[subloss_id].post_uw_weight
                log_sigma = self.log_sigmas[subloss_id]

                # compute weighted subloss
                uncertainty_weighted_subloss = (
                    subloss / (loss_type_weight * torch.exp(2.0 * log_sigma) + 1e-10) + log_sigma
                )
                weighted_subloss = self._task_weights[task_id] * post_uw_weight * uncertainty_weighted_subloss

                # fill dicts for (un-)weighted sublosses and log-sigmas
                weighted_sublosses_buffer[f"{subloss_id}/weighted_loss"] = weighted_subloss
                unweighted_sublosses[f"{subloss_id}/unweighted_loss"] = subloss
                subloss_log_sigmas[f"{subloss_id}/uncertainty_weight_log_sigma"] = log_sigma

            weighted_sublosses.update(weighted_sublosses_buffer)

            # calculate the weighted task loss
            if weighted_sublosses_buffer:
                weighted_task_loss = torch.stack(list(weighted_sublosses_buffer.values())).sum()
            else:
                # if we don't have sublosses to weight the total of it is considered as 0
                weighted_task_loss = torch.tensor(
                    0.0, dtype=task_loss.total_loss.dtype, device=task_loss.total_loss.device
                )

            # fill dicts for (un-)weighted task losses
            weighted_task_losses[f"{task_id}/weighted_task_loss"] = weighted_task_loss
            unweighted_task_losses[f"{task_id}/unweighted_task_loss"] = task_loss.total_loss

        # compute the weighted and unweighted total losses
        weighted_total_loss = torch.stack(list(weighted_sublosses.values())).sum()
        unweighted_total_loss = {"unweighted_total_loss": torch.stack(list(unweighted_sublosses.values())).sum()}

        # merge the various loss-component dicts
        loss_components = (
            unweighted_sublosses
            | unweighted_task_losses
            | unweighted_total_loss
            | subloss_log_sigmas
            | weighted_task_losses
        )

        return LossOutput(
            total_loss=weighted_total_loss,
            sublosses=weighted_sublosses,
            additional_loss_metrics=loss_components,
        )
