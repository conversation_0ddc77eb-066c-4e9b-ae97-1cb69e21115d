"""Deformable DETR - https://github.com/fundamentalvision/Deformable-DETR."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
# ------------------------------------------------------------------------
# Modified from Deformable DETR
# Copyright (c) 2020 SenseTime. All Rights Reserved.
# ------------------------------------------------------------------------
# Modified from DETR (https://github.com/facebookresearch/detr)
# Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved
# ------------------------------------------------------------------------

import copy
from collections.abc import Sequence

import torch
from torch import nn
from torch.nn.init import normal_

from xtorch.nn.blocks.deformable_attn import MultiscaleDeformableAttention
from xtorch.nn.blocks.ffnn import FFNN
from xtorch.nn.module_factories import ActivationFunctionName


class DeformableTransformer(nn.Module):
    """Deformable Transformer for object detection tasks."""

    def __init__(  # noqa: PLR0913
        self,
        d_model: int = 256,
        num_heads: int = 8,
        num_encoder_layers: int = 6,
        num_decoder_layers: int = 6,
        dim_feedforward: int = 1024,
        dropout: float = 0.1,
        activation: ActivationFunctionName = "relu",
        num_feature_levels: int = 4,
        dec_n_points: int = 4,
        enc_n_points: int = 4,
        bbox_refine_layers: nn.ModuleList | None = None,
    ) -> None:
        """Initializes the Deformable Transformer.

        Args:
            d_model: The dimension of the model.
            num_heads: The number of attention heads.
            num_encoder_layers: The number of encoder layers.
            num_decoder_layers: The number of decoder layers.
            dim_feedforward: The dimension of the feedforward network.
            dropout: The dropout rate.
            activation: The activation function to use.
            num_feature_levels: The number of feature levels.
            dec_n_points: The number of sampling points in the decoder.
            enc_n_points: The number of sampling points in the encoder.
            bbox_refine_layers: Optional layers for bounding box refinement.
            with_encoder: Whether to use an encoder in the transformer.
        """
        super().__init__()

        # TODO: fix bounding box refinement https://dev.azure.com/PACE-ADO/PACE/_workitems/edit/386119
        assert bbox_refine_layers is None, "box refinement is buggy and cannot be used yet."

        self._n_heads = num_heads

        decoder_layer = DeformableTransformerDecoderLayer(
            d_model, dim_feedforward, dropout, activation, num_feature_levels, num_heads, dec_n_points
        )
        self._decoder = DeformableTransformerDecoder(
            decoder_layer, num_decoder_layers, dec_n_points, bbox_refine_layers=bbox_refine_layers
        )

        self._level_embed = nn.Parameter(torch.Tensor(num_feature_levels, d_model))

        self._initialize_weights_and_biases()

    def _initialize_weights_and_biases(self) -> None:
        for p in self.parameters():
            if p.dim() > 1:
                nn.init.xavier_uniform_(p)
        normal_(self._level_embed)

    @property
    def decoder(self) -> "DeformableTransformerDecoder":
        """Returns the decoder of the transformer."""
        return self._decoder

    def _get_valid_ratio(self, mask: torch.Tensor) -> torch.Tensor:
        """Calculates the valid ratio of the mask.

        Args:
            mask: A tensor of shape (B, H, W) where B is the batch size, H is the height, and W is the width.

        Returns:
            A tensor of shape (B, 2) containing the valid ratio for height and width.
        """
        _, h, w = mask.shape
        valid_h = torch.sum(~mask[:, :, 0], 1)
        valid_w = torch.sum(~mask[:, 0, :], 1)
        valid_ratio_h = valid_h.float() / h
        valid_ratio_w = valid_w.float() / w
        valid_ratio = torch.stack([valid_ratio_w, valid_ratio_h, valid_ratio_w, valid_ratio_h], -1)
        return valid_ratio

    def forward(
        self,
        feature_maps: list[torch.Tensor],
        masks: list[torch.Tensor],
        pos_embeds: list[torch.Tensor],
        tgt_embed: torch.Tensor,
        reference_points: torch.Tensor,
        query_pos: torch.Tensor,
    ) -> tuple[torch.Tensor, torch.Tensor, torch.Tensor]:
        """Forward pass of the Deformable Transformer.

        Args:
            feature_maps: List of feature maps from different levels.
            masks: List of masks for each feature map.
            pos_embeds: List of positional embeddings for each feature map.
            tgt_embed: Target embeddings for the decoder.
            reference_points: Reference points for the decoder, of shape (B, Q, 4).
            query_pos: Positional embeddings for the decoder queries.

        Returns:
            A tuple containing:
                - hs: The output from the decoder.
                - reference_points: The reference points for the decoder.
                - inter_references: Intermediate reference points from the decoder.
        """
        spatial_shapes = [
            torch.tensor(inp.shape[-2:], dtype=torch.long, device=feature_maps[0].device) for inp in feature_maps
        ]

        valid_ratios = (
            torch.stack([self._get_valid_ratio(m) for m in masks], 1)
            if masks is not None
            else torch.ones(1, len(feature_maps), 4, device=feature_maps[0].device)
        )

        memory = torch.cat(
            [
                (fm + pos + lvl_embed[None, :, None, None]).flatten(2, 3)
                for fm, pos, lvl_embed in zip(feature_maps, pos_embeds, self._level_embed)
            ],
            dim=2,
        ).permute(0, 2, 1)

        # prepare input for decoder
        bs = memory.shape[0]

        # decoder
        hs, inter_references = self._decoder(
            target=tgt_embed.expand(bs, -1, -1),
            reference_points=reference_points,
            src=memory,
            src_spatial_shapes=spatial_shapes,
            src_valid_ratios=valid_ratios,
            query_pos=query_pos,
            src_padding_mask=masks,
        )

        return hs, reference_points, inter_references


class DeformableTransformerDecoderLayer(nn.Module):
    """Deformable Transformer Decoder Layer."""

    def __init__(
        self,
        d_model: int = 256,
        d_ffn: int = 1024,
        dropout: float = 0.1,
        activation: ActivationFunctionName = "relu",
        n_levels: int = 4,
        n_heads: int = 8,
        n_points: int = 4,
    ) -> None:
        """Initializes the Deformable Transformer Decoder Layer.

        Args:
            d_model: The dimension of the model.
            d_ffn: The dimension of the feedforward network.
            dropout: The dropout rate.
            activation: The activation function to use.
            n_levels: The number of feature levels.
            n_heads: The number of attention heads.
            n_points: The number of sampling points.
        """
        super().__init__()

        self._n_heads = n_heads

        # cross attention
        self._cross_attn = MultiscaleDeformableAttention(d_model, n_levels, n_heads, n_points)
        self._dropout1 = nn.Dropout(dropout)
        self._norm1 = nn.LayerNorm(d_model)

        # self attention
        self._self_attn = nn.MultiheadAttention(d_model, n_heads, dropout=dropout)
        self._dropout2 = nn.Dropout(dropout)
        self._norm2 = nn.LayerNorm(d_model)

        # ffn
        self._ffn = torch.nn.Sequential(
            FFNN(d_model, [d_ffn], dropout=dropout, act=activation, add_identity=True),
            nn.Dropout(dropout),
        )
        self._norm3 = nn.LayerNorm(d_model)

    @property
    def n_heads(self) -> int:
        """Returns the number of attention heads."""
        return self._n_heads

    def forward(
        self,
        target: torch.Tensor,
        query_pos: torch.Tensor | None,
        reference_points: Sequence[torch.Tensor],
        src: Sequence[torch.Tensor],
        src_spatial_shapes: Sequence[torch.Tensor],
        src_padding_mask: Sequence[torch.Tensor] | None = None,
    ) -> torch.Tensor:
        """Forward pass of the Deformable Transformer Decoder Layer.

        Args:
            target: The input tensor of shape (B, Q, W).
            query_pos: Optional positional embedding tensor of shape (B, Q, W).
            reference_points: A sequence of reference points for each feature level.
            src: A sequence of source tensors from the encoder.
            src_spatial_shapes: A sequence of spatial shapes for each source tensor.
            src_padding_mask: Optional padding masks for each source tensor.

        Returns:
            The output tensor of shape (B, Q, W).
        """
        if query_pos is not None:
            query = target + query_pos
            key = target + query_pos
        else:
            # if no positional embedding is provided, use the target as query and key
            query = target
            key = target

        # self attention
        self_attn_out = self._self_attn(query.transpose(0, 1), key.transpose(0, 1), target.transpose(0, 1))[
            0
        ].transpose(0, 1)
        target = target + self._dropout2(self_attn_out)
        target = self._norm2(target)

        # cross attention
        cross_attn_out = self._cross_attn(
            query=target + query_pos if query_pos is not None else self_attn_out,
            reference_points=reference_points,
            input_ftrs=src,
            input_spatial_shapes=src_spatial_shapes,
            input_padding_mask=src_padding_mask,
        )

        output = target + self._dropout1(cross_attn_out)
        output = self._norm1(output)

        # ffn
        output = self._ffn(output)
        output = self._norm3(output)

        return output


class DeformableTransformerDecoder(nn.Module):
    """Deformable Transformer Decoder."""

    def __init__(
        self,
        decoder_layer: DeformableTransformerDecoderLayer,
        num_layers: int,
        n_points: int,
        bbox_refine_layers: nn.ModuleList | None = None,
    ) -> None:
        """Initializes the Deformable Transformer Decoder.

        Args:
            decoder_layer: The decoder layer to be used in the transformer.
            num_layers: The number of decoder layers.
            n_points: The number of sampling points.
            bbox_refine_layers: Optional layers for bounding box refinement.
        """
        super().__init__()
        self._n_heads = decoder_layer.n_heads
        self._layers = _get_clones(decoder_layer, num_layers)
        self._n_points = n_points
        self._bbox_refine_layers: nn.ModuleList | None = bbox_refine_layers

    @property
    def bbox_refine_layers(self) -> nn.ModuleList | None:
        """Returns the bounding box refinement layers."""
        return self._bbox_refine_layers

    def forward(
        self,
        target: torch.Tensor,
        reference_points: torch.Tensor,
        src: torch.Tensor,
        src_spatial_shapes: Sequence[torch.Tensor],
        src_valid_ratios: torch.Tensor,
        query_pos: torch.Tensor | None = None,
        src_padding_mask: Sequence[torch.Tensor] | None = None,
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """Forward pass of the Deformable Transformer Decoder.

        Args:
            target: The input tensor of shape (B, Q, C).
            reference_points: The reference points for the decoder, of shape (B, Q, 2).
            src: The source tensor from the encoder.
            src_spatial_shapes: A sequence of spatial shapes for each source tensor.
            src_valid_ratios: A tensor containing valid ratios for each source tensor.
            query_pos: Optional positional embedding tensor of shape (B, Q, W).
            src_padding_mask: Optional padding masks for each source tensor.

        Returns:
            A tuple containing:
                - output: The output tensor after applying the decoder layers.
                - reference_points: The updated reference points for the decoder.
        """
        assert reference_points.shape[-1] == 4, "Only 4D reference points are supported"
        bs = target.shape[0]

        output = target

        for decoder_layer in self._layers:
            assert reference_points.shape[-1] in [2, 4], (
                f"Only 2D and 4D reference points are supported, but got {reference_points.shape[-1]}"
            )
            reference_points_input = (
                reference_points[0:bs][:, None, :, :] * src_valid_ratios[:, :, None, :]
            ).transpose(1, 2)

            output = decoder_layer(
                target=output,
                query_pos=query_pos,
                reference_points=reference_points_input,
                src=src,
                src_spatial_shapes=src_spatial_shapes,
                src_padding_mask=src_padding_mask,
            )

        return torch.stack([output]), torch.stack([reference_points[0:bs]])


def _get_clones(module: nn.Module | nn.ModuleList, num_clones: int) -> nn.ModuleList:
    """Creates N clones of the given module.

    Args:
        module: The module to clone.
        num_clones: The number of clones to create.

    Returns:
        A ModuleList containing num_clones of the module.
    """
    return nn.ModuleList([copy.deepcopy(module) for _ in range(num_clones)])
