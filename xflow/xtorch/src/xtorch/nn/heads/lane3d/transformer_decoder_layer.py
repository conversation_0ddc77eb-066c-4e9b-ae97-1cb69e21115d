"""Layer of the transformer decoder."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections.abc import Sequence
from dataclasses import dataclass
from typing import NamedTuple

import numpy as np
import torch
from numpy.typing import NDArray
from torch import nn

from xcontract.data.definitions.image import HW
from xcontract.geometry.definitions.volume import VolumeRange
from xtorch.nn.blocks.ffnn import FFNN
from xtorch.nn.blocks.skip_attention import MultiheadAttentionWithSkipConnection
from xtorch.nn.blocks.sliced_multihead_attention import AttentionNonlinearityType, SlicedMultiheadAttention
from xtorch.nn.heads.lane3d.ms_deformable_attention_3d import (
    MSDeformableAttention3D,
    MSDeformableAttention3DInput,
)


class TransformerDecoderLayerParams(NamedTuple):
    """Transformer layer architecture parameters for the SparseLanecppHead."""

    operation_order: list[str]
    num_pt_per_query: int
    image_shape: HW
    point_cloud_range: VolumeRange
    attention_nonlinearity: AttentionNonlinearityType
    num_heads: int
    num_levels: int
    num_points: int
    ffnn_intermediate_dims_multiplier: int
    dropout: float
    points_as_query: bool
    export_use_pinhole_model: bool


@dataclass
class TransformerDecoderLayerInput:
    """Transformer decoder layer input configuration class."""

    query: torch.Tensor
    key: torch.Tensor
    value: Sequence[torch.Tensor]
    key_pos: Sequence[torch.Tensor]
    reference_points_x: torch.Tensor
    reference_points_z: torch.Tensor
    principal_point: torch.Tensor
    focal_length: torch.Tensor
    is_pinhole_model: torch.Tensor
    extrinsics: torch.Tensor
    query_pos: torch.Tensor
    spatial_shapes: tuple[int, int]


class TransformerDecoderLayer(nn.Module):
    """Decoder layer for Transformer."""

    def __init__(
        self,
        embed_dim: int,
        num_query: int,
        anchor_y_steps: NDArray[np.float32],
        layer_params: TransformerDecoderLayerParams,
    ) -> None:
        """Initialize the `TransformerDecoderLayer`.

        Args:
            embed_dim : Dimension of the embedding.
            num_query : Number of queries.
            anchor_y_steps : Anchor y steps.
            layer_params: NamedTuple containing parameters for the layer.
                operation_order : Order of operations in the layer.
                num_pt_per_query : Number of points per query.
                image_shape : Shape of the image.
                point_cloud_range : Point cloud range.
                attention_nonlinearity: which non-linearity to use in the attention computation,
                                        ReLU, Softmax, HardSwish or ReLU6.
                num_heads : Number of attention heads.
                num_levels : Number of levels in the attention mechanism.
                num_points : Number of points in the attention mechanism.
                ffnn_intermediate_dims_multiplier : Multiplier used for the intermediate_dims in the FFNN.
                dropout : Dropout rate.
                points_as_query : Whether to use points as queries.
                export_use_pinhole_model: Whether to export the model as a pinhole model (or cylindrical model).
        """
        super().__init__()

        self._embed_dim = embed_dim
        self._num_query = num_query
        self._anchor_y_steps = anchor_y_steps
        self._operation_order = layer_params.operation_order
        self._pre_norm = layer_params.operation_order[0] == "norm"
        self._num_pt_per_query = layer_params.num_pt_per_query
        self._image_shape = layer_params.image_shape
        self._point_cloud_range = layer_params.point_cloud_range
        self._attention_nonlinearity: AttentionNonlinearityType = layer_params.attention_nonlinearity
        self._points_as_query = layer_params.points_as_query
        self._dropout = layer_params.dropout
        self._export_use_pinhole_model = layer_params.export_use_pinhole_model

        # deformable attention layer parameters
        self._num_heads = layer_params.num_heads
        self._num_levels = layer_params.num_levels
        self._num_points = layer_params.num_points

        # FFN layer parameters
        self._ffnn_intermediate_dims_multiplier = layer_params.ffnn_intermediate_dims_multiplier

        # Initialize layers
        self._attentions = self._init_attention_layers()
        self._num_attn = len(self._attentions)
        self._ffn = self._init_ffn_layers()
        self._norms = self._init_norm_layers()

    def _init_attention_layers(self) -> nn.ModuleList:
        """Initialize attention layers."""
        attentions = nn.ModuleList()
        attentions.append(
            MultiheadAttentionWithSkipConnection(
                embed_dim=self._embed_dim,
                num_heads=self._num_heads,
                dropout=self._dropout,
                attention_nonlinearity=self._attention_nonlinearity,
                mhsa=SlicedMultiheadAttention,
            )
        )
        attentions.append(
            MSDeformableAttention3D(
                embed_dims=self._embed_dim,
                num_heads=self._num_heads,
                num_levels=self._num_levels,
                num_points=self._num_points,
                num_query=self._num_query,
                num_anchor_per_query=self._num_pt_per_query if self._points_as_query else 1,
                anchor_y_steps=list(self._anchor_y_steps),
                image_shape=self._image_shape,
                dropout=self._dropout,
                point_cloud_range=self._point_cloud_range,
                attention_nonlinearity=self._attention_nonlinearity,
                export_use_pinhole_model=self._export_use_pinhole_model,
            )
        )

        return attentions

    def _init_ffn_layers(self) -> nn.ModuleList:
        """Initialize feed-forward network (FFN) layers."""
        ffn = nn.ModuleList()
        for _ in range(self._operation_order.count("ffn")):
            ffn.append(
                FFNN(
                    input_dim=self._embed_dim,
                    output_dim=self._embed_dim,
                    intermediate_dims=[self._embed_dim * self._ffnn_intermediate_dims_multiplier],
                    dropout=self._dropout,
                )
            )
        return ffn

    def _init_norm_layers(self) -> nn.ModuleList:
        """Initialize normalization layers."""
        return nn.ModuleList([nn.LayerNorm(self._embed_dim) for _ in range(self._operation_order.count("norm"))])

    def forward(
        self,
        inputs: TransformerDecoderLayerInput,
    ) -> torch.Tensor:
        """Forward pass of the transformer layer.

        Args:
            inputs: Input configuration for the transformer layer

        Returns:
            Output tensor
        """

        query = inputs.query

        norm_idx = 0
        attn_idx = 0
        ffn_idx = 0
        identity = query

        for operation in self._operation_order:
            if operation == "self_attn":
                query, _ = self._attentions[attn_idx](
                    query=query,
                    key=query,
                    value=query,
                    identity=identity,
                    query_pos=inputs.query_pos,
                    key_pos=inputs.query_pos,
                    need_weights=False,
                )
                identity = query  # update identity
                attn_idx += 1

            elif operation == "cross_attn":
                forward_pass_args = MSDeformableAttention3DInput(
                    query=query,
                    key=inputs.key,
                    value=inputs.value,
                    key_pos=inputs.key_pos,
                    query_pos=inputs.query_pos,
                    reference_points_x=inputs.reference_points_x,
                    reference_points_z=inputs.reference_points_z,
                    principal_point=inputs.principal_point,
                    focal_length=inputs.focal_length,
                    is_pinhole_model=inputs.is_pinhole_model,
                    extrinsics=inputs.extrinsics,
                    spatial_shapes=[inputs.spatial_shapes],
                    identity=identity,
                )

                query = self._attentions[attn_idx](forward_pass_args)
                identity = query  # update identity
                attn_idx += 1

            elif operation == "ffn":
                query = self._ffn[ffn_idx](query, identity=identity)
                identity = query  # update identity
                ffn_idx += 1

            elif operation == "norm":
                query = self._norms[norm_idx](query)
                norm_idx += 1
                if not self._pre_norm:
                    identity = query
        return query
