"""Deformable Attention 3D Module.

Based on: https://github.com/JMoonr/LATR/blob/1c9e62646274037faa4a2ebed3ce61b96baa68f9/models/transformer_bricks.py

Paper: https://arxiv.org/pdf/2308.04583
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

# MIT License.

# Copyright (c) 2023 JMoonr

# Permission is hereby granted, free of charge, to any person obtaining a copy
# of this software and associated documentation files (the "Software"), to deal
# in the Software without restriction, including without limitation the rights
# to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
# copies of the Software, and to permit persons to whom the Software is
# furnished to do so, subject to the following conditions:

# The above copyright notice and this permission notice shall be included in all
# copies or substantial portions of the Software.

# THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
# IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
# FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
# AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
# LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
# OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
# SOFTWARE.
#
# Modified by Robert Bosch GmbH and Cariad SE

import math
from collections.abc import Sequence
from dataclasses import dataclass

import torch
import torch.nn.functional as F
from torch import nn

from xcontract.data.definitions.image import HW
from xcontract.geometry.definitions.volume import VolumeRange
from xtorch.camera_models.lane3d.camera_projection import VehicleToCameraCoordinates, convert_3d_to_pixel_coordinates
from xtorch.nn.blocks.sliced_multihead_attention import AttentionNonlinearityType
from xtorch.nn.functional import expand

torch.fx.wrap("expand")  # must be called here in the calling module


@dataclass
class MSDeformableAttention3DInput:
    """Dataclass for the input of Multi-Scale Deformable Attention in 3D space."""

    query: torch.Tensor
    key: torch.Tensor | None
    value: Sequence[torch.Tensor]
    identity: torch.Tensor
    query_pos: torch.Tensor | None
    reference_points_x: torch.Tensor
    reference_points_z: torch.Tensor
    spatial_shapes: list[tuple[int, int]]
    principal_point: torch.Tensor
    focal_length: torch.Tensor
    is_pinhole_model: torch.Tensor
    extrinsics: torch.Tensor
    key_pos: Sequence[torch.Tensor]


class _DeNormReferencePoints(nn.Module):
    """Denormalize reference points into the specified range.

    Converts normalized points in the range from 0 to 1 to real-world coordinates
    with the ranges defined by point_cloud_range.
    """

    def __init__(self, point_cloud_range: VolumeRange) -> None:
        """Initializes a _DeNormReferencePoints module.

        Args:
            point_cloud_range: The point cloud range. Format (xmin, ymin, zmin, xmax, ymax, zmax).
        """
        super().__init__()
        self._point_cloud_range = point_cloud_range

    def forward(
        self, reference_points_x: torch.Tensor, reference_points_z: torch.Tensor
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """Denormalize reference points into the specified range.

        Converts normalized points in the range from 0 to 1 to real-world coordinates
        with the ranges defined by point_cloud_range.

        Args:
            reference_points_x: The x reference points tensor of shape (batch_size, num_points, 1).
            reference_points_z: The z reference points tensor of shape (batch_size, num_points, 1).

        Returns:
            x, z reference points (denormalized).
        """
        x = reference_points_x * self._point_cloud_range.x_length + self._point_cloud_range.x_min
        z = reference_points_z * self._point_cloud_range.z_length + self._point_cloud_range.z_min

        return x, z


class _GenerateSamplingLocations2D(nn.Module):
    """Generates 2D sampling locations for the deformable attention module."""

    def __init__(
        self,
        num_heads: int,
        num_levels: int,
        num_points: int,
        embed_dims: int,
        num_points_per_anchor: int,
        image_shape: HW,
        *,
        export_use_pinhole_model: bool = False,
        apply_sampling_offsets: bool = False,
    ) -> None:
        """Initializer."""
        super().__init__()

        self._min_valid_distance = 0.5  # in meters.

        self._num_levels = num_levels
        self._num_heads = num_heads
        self._num_points = num_points
        self._num_points_per_anchor = num_points_per_anchor
        self._image_shape = image_shape
        self._export_use_pinhole_model = export_use_pinhole_model
        self.apply_sampling_offsets = apply_sampling_offsets

        if self.apply_sampling_offsets:
            self.sampling_offsets = nn.Linear(
                embed_dims, num_heads * num_levels * num_points * 2 * self._num_points_per_anchor
            )

    def forward(
        self,
        x_camera_coord: torch.Tensor,
        y_camera_coord: torch.Tensor,
        z_camera_coord: torch.Tensor,
        query: torch.Tensor,
        inputs: MSDeformableAttention3DInput,
    ) -> tuple[torch.Tensor, torch.Tensor]:
        """Generates sampling locations for the deformable attention module.

        Args:
            x_camera_coord: The transformed x coordinates in camera coordinates.
            y_camera_coord: The transformed y coordinates in camera coordinates.
            z_camera_coord: The transformed z coordinates in camera coordinates.
            query: The query tensor.
            inputs: The input data MSDeformableAttention3DInput.

        Returns:
            Tuple of 2D sampling locations and invalid mask.
        """
        bs, num_query, _ = query.shape

        sampling_locations2d, invalid_mask = convert_3d_to_pixel_coordinates(
            x_camera_coord=x_camera_coord,
            y_camera_coord=y_camera_coord,
            z_camera_coord=z_camera_coord,
            principal_point=inputs.principal_point,
            focal_length=inputs.focal_length,
            is_pinhole_model=inputs.is_pinhole_model if self.training else self._export_use_pinhole_model,
            image_shape=self._image_shape,
            min_valid_distance=self._min_valid_distance,
            normalize=True,
        )

        sampling_locations2d = sampling_locations2d.repeat(
            (1, 1, self._num_points_per_anchor * self._num_heads * self._num_levels * self._num_points)
        )

        if self.apply_sampling_offsets:
            offset_normalizer = query.new_tensor(data=inputs.spatial_shapes, dtype=torch.float32)[..., [1, 0]]
            offset_normalizer = offset_normalizer[:, None, :].repeat(
                [1, 1, self._num_heads * self._num_levels * self._num_points * self._num_points_per_anchor]
            )
            sampling_offsets = self.sampling_offsets(query)
            sampling_offsets = sampling_offsets / offset_normalizer
            sampling_locations2d += sampling_offsets

        # Note that if we can really remove apply_sampling_offsets then the follow line could be merged
        # with the previous multiplication, resulting in less runtime
        sampling_locations2d = sampling_locations2d * 2 - 1
        sampling_locations2d = sampling_locations2d.view(
            bs, num_query, self._num_heads, self._num_levels * self._num_points * self._num_points_per_anchor, 2
        )

        return sampling_locations2d, invalid_mask


class MSDeformableAttention3D(nn.Module):
    """Class for Multi-Scale Deformable Attention in 3D space."""

    class _ValueQueryPreparer(nn.Module):
        """Some operations factored out for easier quantization override specification."""

        def __init__(self, embed_dims: int, num_levels: int) -> None:
            """Inits."""
            super().__init__()
            self._value_projection = nn.Conv2d(embed_dims, embed_dims, kernel_size=1)
            self._num_levels = num_levels

            if hasattr(self._value_projection, "weight") and self._value_projection.weight is not None:
                nn.init.xavier_uniform_(self._value_projection.weight, gain=1.0)
            if hasattr(self._value_projection, "bias") and self._value_projection.bias is not None:
                nn.init.constant_(self._value_projection.bias, 0.0)

        def forward(
            self,
            value: Sequence[torch.Tensor],
            query: torch.Tensor,
            query_pos: torch.Tensor | None,
            key_pos: Sequence[torch.Tensor] | None,
        ) -> tuple[Sequence[torch.Tensor], torch.Tensor]:
            """Prepare the value and query tensors for the forward pass.

            Args:
                value: The value tensor.
                key: The key tensor.
                query: The query tensor.
                query_pos: The query position tensor.
                key_pos: The key position tensor.

            Note: The value tensor is set to the key tensor if the value tensor is None. However, additional checks are
                made to ensure that the value tensor is not None.

            Returns:
                A tuple containing the value and query tensors.
            """

            if query_pos is not None:
                query = query + query_pos
            if key_pos is not None:
                # Note: Bad iteration code in order to support fx.symbolic_trace with its non-iterable Proxy objects.
                value = [value[i] + key_pos[i] for i in range(self._num_levels)]
            value = [self._value_projection(value[i]) for i in range(self._num_levels)]
            return value, query

    def __init__(  # noqa: PLR0913
        self,
        embed_dims: int,
        num_heads: int,
        num_levels: int,
        num_points: int,
        num_query: int,
        num_anchor_per_query: int,
        anchor_y_steps: list[float],
        dropout: float,
        image_shape: HW,
        point_cloud_range: VolumeRange,
        attention_nonlinearity: AttentionNonlinearityType,
        image_to_column_step: int = 64,
        *,
        use_padding_mask: bool = False,
        export_use_pinhole_model: bool = False,
    ) -> None:
        """Initializes a MSDeformableAttention3D module.

        Args:
            embed_dims: The number of embedding dimensions.
            num_heads: The number of attention heads.
            num_levels: The number of levels in the attention mechanism.
            num_points: The number of points in the attention mechanism.
            num_query: The number of queries.
            num_anchor_per_query: The number of anchors per query.
            anchor_y_steps: The anchor y steps.
            dropout: The dropout rate.
            image_shape: The image shape.
            point_cloud_range: A list of ints representing the point cloud range.
            image_to_column_step: An integer representing the step size for the image-to-column operation.
            use_padding_mask: Whether to zero out the value tensor where the mask equals true.
            export_use_pinhole_model: Whether to export the model as a pinhole model (or cylindrical model).
            attention_nonlinearity: which non-linearity to use in the attention computation,
                                    ReLU, Softmax, HardSwish or ReLU6.
        """
        super().__init__()

        if embed_dims % num_heads != 0:
            error_msg = f"embed_dims must be divisible by num_heads, but got {embed_dims} and {num_heads}"
            raise ValueError(error_msg)

        self._attention_nonlinearity = attention_nonlinearity
        if attention_nonlinearity == "softmax":
            self._non_linearity = nn.Softmax(dim=-1)  # AIMETs processing of functional softmax is bugged.
        elif attention_nonlinearity == "relu":
            self._non_linearity = nn.ReLU()
        elif attention_nonlinearity == "relu6":
            self._non_linearity = nn.ReLU6()
        elif attention_nonlinearity == "hardswish":
            self._non_linearity = nn.Hardswish()

        self._dropout = nn.Dropout(dropout)
        self._use_padding_mask = use_padding_mask

        self._num_query = num_query
        self._num_anchor_per_query = num_anchor_per_query
        self.anchor_y_steps = nn.Buffer(
            torch.as_tensor(anchor_y_steps, dtype=torch.float32).view(1, 1, self._num_anchor_per_query, 1, 1)
        )
        self._num_points_per_anchor = len(anchor_y_steps) // num_anchor_per_query

        self._image_to_column_step = image_to_column_step
        self._embed_dims = embed_dims
        self._num_levels = num_levels
        self._num_heads = num_heads
        self._num_points = num_points

        self._attention_layer = nn.Linear(embed_dims, num_heads * num_levels * num_points * self._num_points_per_anchor)
        self._prepare_value_query = MSDeformableAttention3D._ValueQueryPreparer(embed_dims, num_levels)
        self._output_projection = nn.Linear(embed_dims, embed_dims)

        # Runtime optimization: Use static buffer to reduce output from
        # (bs, num_queries, num_heads, num_levels * num_points * num_embed_dims) to
        # (bs, num_queries, num_embed_dims) via matrix multiplication.
        num_features = self._embed_dims // self._num_heads
        reduce_dim = self._num_points * self._num_levels * self._num_points_per_anchor
        weight = torch.zeros(reduce_dim * num_features, num_features, requires_grad=False)
        for i in range(num_features):
            for j in range(reduce_dim):
                weight[i + j * num_features, i] = 1.0

        self._reduce_projection = nn.Linear(reduce_dim * num_features, num_features, bias=False)
        self._reduce_projection.weight.data[...] = weight.T
        self._reduce_projection.weight.requires_grad = False

        self._denormalize_reference_points = _DeNormReferencePoints(point_cloud_range)
        self._generate_sampling_locations2d = _GenerateSamplingLocations2D(
            num_heads=num_heads,
            num_levels=num_levels,
            num_points=num_points,
            embed_dims=embed_dims,
            image_shape=image_shape,
            num_points_per_anchor=self._num_points_per_anchor,
            export_use_pinhole_model=export_use_pinhole_model,
        )

        self._vehicle_to_camera_coord = VehicleToCameraCoordinates()

        self._init_weights()

    def forward(
        self,
        inputs: MSDeformableAttention3DInput,
    ) -> torch.Tensor:
        """Forward function for multi-scale deformable attention 3d."""

        value, query = self._prepare_value_query(inputs.value, inputs.query, inputs.query_pos, inputs.key_pos)

        bs, num_query, _ = query.shape

        attention_scores = self._attention_layer(query)
        attention_scores = attention_scores.view(
            bs, num_query, self._num_points_per_anchor * self._num_heads * self._num_levels, self._num_points
        )
        if self._attention_nonlinearity == "softmax":
            # AIMET doesn't support the functional softmax.
            attention_scores = self._non_linearity(attention_scores) / self._num_points_per_anchor
        else:
            attention_scores = self._non_linearity(attention_scores) / (
                attention_scores.shape[1] * self._num_points_per_anchor
            )

        attention_scores = attention_scores.view(bs, num_query, -1)

        sampling_location_x, sampling_location_z = self._denormalize_reference_points(
            reference_points_x=inputs.reference_points_x,
            reference_points_z=inputs.reference_points_z,
        )

        sampling_location_y = expand(
            self.anchor_y_steps, bs, self._num_query, self._num_anchor_per_query, self._num_points_per_anchor, 1
        ).reshape(sampling_location_x.shape)

        x = sampling_location_x.squeeze(dim=2)
        y = sampling_location_y.squeeze(dim=2)
        z = sampling_location_z.squeeze(dim=2)

        x_camera_coord, y_camera_coord, z_camera_coord = self._vehicle_to_camera_coord(x, y, z, inputs.extrinsics)

        sampling_locations2d, invalid_mask = self._generate_sampling_locations2d(
            x_camera_coord=x_camera_coord,
            y_camera_coord=y_camera_coord,
            z_camera_coord=z_camera_coord,
            query=query,
            inputs=inputs,
        )

        attention_scores = torch.where(invalid_mask[:, :, None], torch.zeros_like(attention_scores), attention_scores)

        if value is None:
            error_message = "The value tensor is None."
            raise ValueError(error_message)

        # Output of multi_scale_deformable_attn is (bs, num_query, num_heads * num_levels * num_points * embed_dims)
        output = self._multi_scale_deformable_attn(value, sampling_locations2d, attention_scores)

        # Reshape output to (bs, num_query, num_heads * embed_dims) via static reduce projection.
        output = self._reduce_projection(output).flatten(-2, -1)
        output = self._output_projection(output)

        return self._dropout(output) + inputs.identity

    def _init_weights(self) -> None:  # noqa: C901
        """Default initialization for Parameters of Module."""
        if self._generate_sampling_locations2d.apply_sampling_offsets:
            if (
                hasattr(self._generate_sampling_locations2d.sampling_offsets, "weight")
                and self._generate_sampling_locations2d.sampling_offsets.weight is not None
            ):
                nn.init.constant_(self._generate_sampling_locations2d.sampling_offsets.weight, 0.0)
            if (
                hasattr(self._generate_sampling_locations2d.sampling_offsets, "bias")
                and self._generate_sampling_locations2d.sampling_offsets.bias is not None
            ):
                nn.init.constant_(self._generate_sampling_locations2d.sampling_offsets.bias, 0.0)

            thetas = torch.arange(self._num_heads, dtype=torch.float32) * (2.0 * math.pi / self._num_heads)
            grid_init = torch.stack([thetas.cos(), thetas.sin()], -1)
            grid_init = (
                (grid_init / grid_init.abs().max(-1, keepdim=True)[0])
                .view(self._num_heads, 1, 1, 1, 2)
                .repeat(1, self._num_points_per_anchor, self._num_levels, self._num_points, 1)
            )
            for i in range(self._num_points):
                grid_init[..., i, :] *= i + 1

            self._generate_sampling_locations2d.sampling_offsets.bias.data = grid_init.view(-1)

        if hasattr(self._attention_layer, "weight") and self._attention_layer.weight is not None:
            if self._attention_nonlinearity == "relu":
                nn.init.kaiming_uniform_(self._attention_layer.weight, nonlinearity="relu")
            elif self._attention_nonlinearity == "softmax":
                nn.init.xavier_uniform_(self._attention_layer.weight, gain=1.0)
            else:
                nn.init.constant_(self._attention_layer.weight, 0.0)
        if hasattr(self._attention_layer, "bias") and self._attention_layer.bias is not None:
            nn.init.constant_(self._attention_layer.bias, 0.0)

        if hasattr(self._output_projection, "weight") and self._output_projection.weight is not None:
            nn.init.xavier_uniform_(self._output_projection.weight, gain=1.0)
        if hasattr(self._output_projection, "bias") and self._output_projection.bias is not None:
            nn.init.constant_(self._output_projection.bias, 0.0)

    def _multi_scale_deformable_attn(
        self,
        value: Sequence[torch.Tensor],
        sampling_locations: torch.Tensor,
        attention_scores: torch.Tensor,
    ) -> torch.Tensor:
        """Multi-scale deformable attention.

        Args:
            value: The value has shape (bs, height * width * num_levels, num_embed_dims).
            value_spatial_shapes: Spatial shape of each feature map, has shape (num_levels, 2),
                last dimension represents (h, w).
            sampling_locations: The location of sampling points with shape
                (bs, num_queries, num_heads, num_levels * num_points, 2). The last dimension represents (x, y).
            attention_scores: The scores of sampling points used to calculate the attention with shape
                (bs, num_queries, num_heads*num_levels*num_points).

        Returns:
            The deformable attention output of shape (bs, num_queries, num_heads, num_levels * num_points * embed_dims).
        """
        bs, _, _, _ = value[0].shape  # shape is BCHW
        num_queries = sampling_locations.shape[1]  # Caution: Not the same as self._num_query
        num_all_points = self._num_points * self._num_points_per_anchor
        num_feats = self._embed_dims // self._num_heads

        assert len(value) == self._num_levels
        assert isinstance(value[0], torch.fx.Proxy) or value[0].shape[1] == self._embed_dims, (
            f"Value shape {value[0].shape} doesn't match embedding dimension {self._embed_dims}"
        )
        assert (
            isinstance(sampling_locations, torch.fx.Proxy)
            or sampling_locations.shape[-2] == self._num_levels * num_all_points
        )

        sampling_locations = sampling_locations.view(
            bs, num_queries, self._num_heads * self._num_levels * num_all_points, 2
        )
        sampling_value_list = []
        for head in range(self._num_heads):
            for level, level_featuremap in enumerate(value):
                level_featuremap_head_features = level_featuremap[:, head * num_feats : (head + 1) * num_feats, ...]
                # bs, num_queries, num_heads, num_points, 2 ->
                # bs*num_heads, num_queries, num_points, 2
                start_idx = head * self._num_levels * num_all_points + level * num_all_points
                sampling_grid_level = sampling_locations[:, :, start_idx : start_idx + num_all_points, :]
                # bs*num_heads, embed_dims, num_queries, num_points
                sampling_value_level = F.grid_sample(
                    level_featuremap_head_features,
                    sampling_grid_level,
                    mode="bilinear",
                    padding_mode="zeros",
                    align_corners=False,
                )
                sampling_value_list.append(
                    sampling_value_level.permute(0, 2, 3, 1).reshape(bs, num_queries, num_all_points, num_feats)
                )

        # bs, num_queries, num_heads, num_levels, num_points, 1
        attention_scores = attention_scores.unsqueeze(-1)

        # bs, num_queries, num_heads, num_levels * num_points * num_feats
        output = (torch.concat(sampling_value_list, dim=-2) * attention_scores).view(
            bs, num_queries, self._num_heads, self._num_levels * num_all_points * num_feats
        )

        return output
