"""Contains stateless functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

from collections.abc import Sequence
from typing import Any, Generic, Literal, TypeVar, cast, overload

import torch
import torch.nn.functional as F
import tree
from torch import Tensor

ReductionOp = Literal["sum", "prod", "mean", "amax", "amin"]


def scatter_nd_reduce(target: Tensor, indices: Tensor, updates: Tensor, reduce: ReductionOp) -> None:
    """Scatters values from updates into target where duplicated entries are subject to a reduction operation.

    Equivalent to
        for idx, update in zip(indices,updates):
            target[idx,...] = reduction(target[idx,...],update)
    with a binary operator "reduction".

    This is different to PyTorch's scatter_reduce which is described according to the official documentation by
        self[index[i][j][k]][j][k] += src[i][j][k]  # if dim == 0
        self[i][index[i][j][k]][k] += src[i][j][k]  # if dim == 1
        self[i][j][index[i][j][k]] += src[i][j][k]  # if dim == 2
    In case of the "sum" reduction. This implementation is more similar to the scatter_nd_* function family from
    tensorflow.

    Args:
        target: Tensor to scatter into with shape (N1, N2, ...N_d, F1, F2, ..., F_f)
        indices: Destination indices with shape (K, d)
        updates: New values with shape (K, F1, F2, ..., F_f), where K is the number of updates, d >= 1 is the spatial
                 dimension and f >= 0 is the feature rank. Note that the updates can also be scalar.
        reduce: The reduction op: "sum", "prod", "mean", "amax" or "amin"
    """
    num_spatial_dimensions = indices.shape[1]
    num_feature_dimensions = len(updates.shape) - 1  # the first dimension is for indexing the update
    assert len(indices.shape) == 2
    assert len(updates.shape) >= 1
    assert indices.shape[0] == updates.shape[0]
    assert len(target.shape) == num_spatial_dimensions + num_feature_dimensions
    assert updates.shape[1:] == target.shape[num_spatial_dimensions:]

    update_uids: Tensor

    # Cluster updates which have the same index.
    # unique_idxs contains the unique indices and has shape (P,d), where P is the number of unique indices.
    # update_uids contains the mapping from update to unique index and has shape (K,)
    unique_idxs, update_uids = torch.unique(indices, return_inverse=True, dim=0, sorted=False)

    # Gather indexed locations to use scatter_reduce_ on. It has shape (P, F1, ..., F_f)
    gathered_targets = target[unique_idxs.unbind(-1)]

    # To support non-scalar updates the flat-index must be repeated for all feature dimensions.
    # This is due to how scatter_reduce_ works. And it expects the index to have the same shape as the updates.
    # The repetition is efficiently done without copy.
    if num_feature_dimensions > 0:
        new_shape = (-1, *(1 for _ in range(num_feature_dimensions)))
        update_uids = update_uids.view(new_shape).expand(*updates.shape)
    # Now update_uids has shape (K, F1, ..., F_f)

    # Reduce updates over unique target locations
    reduced_features = gathered_targets.scatter_reduce_(
        dim=0, index=update_uids, src=updates, reduce=reduce, include_self=True
    )
    # Scatter the reduced features into the target, now with duplicate indices removed.
    # There is no equivalent of this scatter operation which also performs reduction.
    # Therefore the reduction was done in advance.
    target[unique_idxs.unbind(-1)] = reduced_features


def inverse_sigmoid(x: torch.Tensor, eps: float = 1e-5) -> torch.Tensor:
    """Inverse function of sigmoid.

    Hint: This implementation could be replaced by torch.logit(x,eps) whenever
    https://github.com/pytorch/pytorch/pull/145576 is in our used version of PyTorch.

    Args:
        x: The tensor to do the inverse. Should be in the range of [0, 1].
        eps: EPS to avoid numerical overflow.

    Returns:
        Tensor with inverse function of sigmoid, has same shape than input.
    """
    assert eps < 0.1, (
        "eps should be small to avoid numerical overflow."
    )  # see https://github.com/pytorch/pytorch/pull/145576
    return torch.logit(x, eps=eps)


def softmax_cross_entropy_with_logits(
    labels: torch.Tensor, logits: torch.Tensor, dim: int = -1, weights: torch.Tensor | None = None
) -> torch.Tensor:
    """Compute the softmax cross entropy with logits with arbitrary channel dimension. Optionally applies class weights.

    Args:
        logits: The logits tensor.
        labels: The one-hot encoded labels tensor.
        dim: The dimension along which to compute the softmax cross entropy and to apply the weight vector (if given)
        weights: Optional class weights vector.

    Returns:
        The softmax cross entropy loss.
    """
    labels = torch.movedim(labels, dim, 1)
    logits = torch.movedim(logits, dim, 1)
    loss = F.cross_entropy(logits, labels.to(logits.dtype), reduction="none", weight=weights)
    return loss


def tensor_scatter_nd_max(
    tensor: torch.Tensor, indices: torch.Tensor, updates: torch.Tensor, *, in_place: bool = False
) -> torch.Tensor:
    """Scatter updates into a tensor at specified indices, applying element-wise maximum.

    Args:
        tensor: The input tensor to update.
        indices: Tensor of shape (..., N) where each row is an index to update.
        updates: Values to be scattered, must match indices shape.
        in_place: If True, updates the tensor in place. Otherwise, returns a new tensor.

    Returns:
        Updated tensor with element-wise max applied at valid `indices`.
    """

    # Clone tensor to avoid in-place modifications
    updated_tensor = tensor if in_place else tensor.clone()
    # Get tensor shape
    batch_dim, channel_dim, height_dim, width_dim = updated_tensor.shape

    # Extract index values
    batch_idx = indices[..., 0]
    channel_idx = indices[..., 1]
    height_idx = indices[..., 2]
    width_idx = indices[..., 3]

    # Compute valid mask (True where indices are in range)
    valid_mask = (
        (batch_idx >= 0)
        & (batch_idx < batch_dim)
        & (channel_idx >= 0)
        & (channel_idx < channel_dim)
        & (height_idx >= 0)
        & (height_idx < height_dim)
        & (width_idx >= 0)
        & (width_idx < width_dim)
    )

    # Filter valid indices
    valid_indices = indices[valid_mask]
    valid_updates = updates[valid_mask].view(-1)

    if valid_indices.shape[0] > 0:  # Apply updates only if there are valid indices
        indices_tuple = tuple(valid_indices.T)  # Convert to tuple for PyTorch indexing

        updated_tensor[indices_tuple] = torch.maximum(updated_tensor[indices_tuple], valid_updates)

    return updated_tensor


_Structure = TypeVar("_Structure")


def stack_structures(data: Sequence[_Structure], dim: int = 0) -> _Structure:
    """Stacks tensors in a sequence of structures as if stacking them individually.

    Usual constraints for stacking tensors apply.

    Args:
        data: A sequence of structures with tensors as elements.
        dim: The dimension created by stacking.

    Returns:
        Carrying over the type, a named tuple with the stacked tensors.
    """

    def apply_stack(*values: torch.Tensor) -> torch.Tensor:
        """Stacks."""
        return torch.stack(values, dim=dim)

    return cast(_Structure, tree.map_structure(apply_stack, *data))


class _Slicer(Generic[_Structure]):
    """Helper to enable slicing syntax."""

    def __init__(self, data: _Structure) -> None:
        """Set the structure to be sliced."""
        self._data = data

    def __getitem__(self, arg: Any) -> _Structure:
        """Apply slicing."""
        return cast(_Structure, tree.map_structure(lambda v: v.__getitem__(arg), self._data))


def slice_structure(data: _Structure) -> _Slicer[_Structure]:
    """Slices tensors in a structure as if slicing them individually.

    Thereby the input hierarchy and classes will be preserved. The function
    returns an objects which will forward slicing arguments to the contained tensors.

    Example:
        x = tuple(tensor, other_tensor)
        tensor, other_tensor = slice_structure(x)[:1]

    Args:
        data: The input tensors

    Returns:
        An object which can be used with slicing syntax.
    """
    # Note: Not implemented for normal tuples because NamedTuples and tuples are constructed differently.
    return _Slicer(data)


def _get_height_and_width(feature_map: torch.Tensor) -> tuple[int, int]:
    """Get height and width from input tensor of format NCHW.

    Args:
        feature_map: Input tensor of format NCHW.

    Returns:
        Tuple containing height and width of the input tensor.
    """
    if feature_map.dim() != 4:
        msg = "Input tensor must be 4-dimensional in NCHW format."
        raise ValueError(msg)

    height_axis = 2
    width_axis = 3
    return feature_map.shape[height_axis], feature_map.shape[width_axis]


def crop_to_half(feature_map: torch.Tensor, x_rel_offset: float = 0, y_rel_offset: float = 0) -> torch.Tensor:
    """Crops a feature map starting from the given relative x and y offsets to half the height and width.

    Args:
        feature_map: Feature map that shall be cropped.
        x_rel_offset: Relative offset in x direction in the range [0, 0.5] (default 0)
        y_rel_offset: Relative offset in y direction in the range [0, 0.5] (default 0)

    Returns:
        Cropped feature map.
    """
    if not (0 <= x_rel_offset <= 0.5):
        msg = f"Invalid x_rel_offset {x_rel_offset}, must be in the range [0, 0.5]."
        raise ValueError(msg)
    if not (0 <= y_rel_offset <= 0.5):
        msg = f"Invalid y_rel_offset {y_rel_offset}, must be in the range [0, 0.5]."
        raise ValueError(msg)

    height, width = _get_height_and_width(feature_map)

    if height and width:
        x_start = int(width * x_rel_offset)
        x_end = x_start + width // 2
        y_start = int(height * y_rel_offset)
        y_end = y_start + height // 2
        return feature_map[..., y_start:y_end, x_start:x_end]

    return feature_map


def pad_to_double(feature_map: torch.Tensor, x_rel_offset: float = 0, y_rel_offset: float = 0) -> torch.Tensor:
    """Pads a feature map to double the height and width and according to the relative x and y offsets.

    Args:
        feature_map: Feature map that shall be padded
        x_rel_offset: Relative offset in x direction in the range [0, 0.5], controls how much to pad to the left
        y_rel_offset: Relative offset in y direction in the range [0, 0.5], controls how much to pad to the top

    Returns:
        The padded feature map.
    """
    if not (0 <= x_rel_offset <= 0.5):
        msg = f"Invalid x_rel_offset {x_rel_offset}, must be in the range [0, 0.5]."
        raise ValueError(msg)
    if not (0 <= y_rel_offset <= 0.5):
        msg = f"Invalid y_rel_offset {y_rel_offset}, must be in the range [0, 0.5]."
        raise ValueError(msg)

    height, width = _get_height_and_width(feature_map)

    if height and width:
        width = width * 2
        height = height * 2
        pad_left = int(width * x_rel_offset)
        pad_right = width - (pad_left + width // 2)
        pad_top = int(height * y_rel_offset)
        pad_bottom = height - (pad_top + height // 2)
        return F.pad(feature_map, (pad_left, pad_right, pad_top, pad_bottom))

    return feature_map


def masked_softmax(x: torch.Tensor, mask: torch.Tensor, dim: int = -1, eps: float = 1e-5) -> torch.Tensor:
    """Masked softmax operation.

    Args:
        x: Input tensor.
        mask: Boolean mask that tells which elements are valid.
        dim: Tensor dimension along which masked softmax is calculated.
        eps: Optional epsilon for numerical stability.

    Returns:
        The result of masked softmax operation
    """
    # for numerical stability, subtract max value before exponentiating
    x_max, _ = torch.max(x, dim=dim, keepdim=True)
    x = torch.exp(x - x_max)

    # the mask is used to set the values of elements to be ignored to zero
    x = x * mask

    # tensor is normalized by dividing with the sums of exponentials
    x = x / (torch.sum(x, dim=dim, keepdim=True) + eps)

    # On INT8 arithmetic, the normalization step produces undefined values
    # due to zero division in case all elements are masked
    # This can be fixed by multiplying normalized values with the mask to set elements to zero
    x = x * mask

    return x


@overload
def expand(x: torch.Tensor, *size: int | torch.SymInt) -> torch.Tensor: ...


@overload
def expand(x: torch.Tensor, *, size: Sequence[int | torch.SymInt]) -> torch.Tensor: ...


def expand(x: torch.Tensor, *args: Any, **kwargs: Any) -> torch.Tensor:
    """Help exclude expand from tracing so it works with Proxy objects."""

    if "size" in kwargs and isinstance(kwargs["size"], Sequence):
        return x.expand(kwargs["size"])

    return x.expand(args)
