"""Sliced Multi-head attention."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import math
from collections.abc import Sequence
from typing import Literal

import torch

from xtorch.nn.functional import masked_softmax

_LOGGER = logging.getLogger(__name__)

AttentionNonlinearityType = Literal["softmax", "relu", "relu6", "hardswish"]


class SlicedMultiheadAttention(torch.nn.Module):
    """Multi-head attention using slice to split heads.

    Default implementation of multi-head attention in PyTorch uses a reshapes and more transposes to
    separate the heads. Recommendation from QC is to use slice instead.

    TODO: Check training performance and memory usage of this implementation.
    """

    def __init__(
        self,
        embed_dim: int,
        num_heads: int,
        dropout: float = 0.0,
        *,
        batch_first: bool = True,
        average_attn_weights: bool = True,
        is_batched: bool = True,
        is_attention_mask_batched: bool = False,
        attention_nonlinearity: AttentionNonlinearityType = "softmax",
    ) -> None:
        """Initializes the sliced multi-head attention.

        Args:
            embed_dim: The dimension of the embedding.
            num_heads: The number of heads.
            dropout: The dropout rate.
            batch_first: Whether the input is batch first.
            average_attn_weights: Whether to average the attention weights across heads.
            is_batched: Wether the input is batched.
            is_attention_mask_batched: Wether the attention mask has a batch dimension.
            attention_nonlinearity: which non-linearity to use in the attention computation,
                                    ReLU, Softmax, HardSwish or ReLU6.
        """
        # Only there to mimic the pytorch interface
        assert batch_first, "This implementation only supports batch first."
        super().__init__()
        self._num_heads = num_heads
        hidden_dim = embed_dim // num_heads
        self._embed_dim = embed_dim
        self._avg_attn_weights = average_attn_weights
        self._dropout = torch.nn.Dropout(dropout)
        self._q = torch.nn.ModuleList([torch.nn.Linear(embed_dim, hidden_dim, bias=False) for _ in range(num_heads)])
        self._kk = torch.nn.ModuleList([torch.nn.Linear(embed_dim, hidden_dim, bias=False) for _ in range(num_heads)])
        self._v = torch.nn.ModuleList([torch.nn.Linear(embed_dim, hidden_dim, bias=False) for _ in range(num_heads)])
        self._out = torch.nn.Linear(embed_dim, embed_dim)
        self._scale_factor = 1 / math.sqrt(hidden_dim)
        self._is_batched = is_batched
        self._is_attention_mask_batched = is_attention_mask_batched
        self._attention_nonlinearity = attention_nonlinearity

        if attention_nonlinearity == "softmax":
            self._non_linearity = torch.nn.Softmax(dim=-1)
        elif attention_nonlinearity == "relu":
            self._non_linearity = torch.nn.ReLU()
        elif attention_nonlinearity == "relu6":
            self._non_linearity = torch.nn.ReLU6()
        elif attention_nonlinearity == "hardswish":
            self._non_linearity = torch.nn.Hardswish()

    def _compute_mask(
        self,
        head_number: int,
        batch_size: int | None,
        mask_dim: Sequence[int],
        attn_mask: torch.Tensor | None = None,
        key_padding_mask: torch.Tensor | None = None,
    ) -> torch.Tensor | None:
        """Computes the final mask from the attention mask and key padding mask.

        Args:
            head_number: Current head index.
            batch_size: Batchsize of the data.
            mask_dim: Dimension of the final mask.
            attn_mask: Optional attention mask.
            key_padding_mask: Optional key padding mask.

        Returns:
            The final combined mask.
        """
        if attn_mask is not None:
            device = attn_mask.device
        else:
            assert key_padding_mask is not None
            device = key_padding_mask.device

        mask = torch.zeros(size=mask_dim, dtype=torch.bool, device=device)
        if attn_mask is not None:
            assert self.training, "Attention mask is only supported during training."
            if self._is_batched and self._is_attention_mask_batched:
                mask += attn_mask[head_number :: self._num_heads]
            elif self._is_batched:
                mask += attn_mask.unsqueeze(0)
            else:
                mask += attn_mask

        if key_padding_mask is not None:
            if not self._is_batched:
                mask += key_padding_mask.unsqueeze(0)
            else:
                mask += key_padding_mask.unsqueeze(1)
        return mask

    def _compute_flash_attention_mask(
        self,
        batchsize: int,
        attn_mask: torch.Tensor | None = None,
        key_padding_mask: torch.Tensor | None = None,
    ) -> torch.Tensor | None:
        """Computes the final mask from the attention mask and key padding mask.

        Args:
            batchsize: Batchsize of the data.
            attn_mask: Optional attention mask.
            key_padding_mask: Optional key padding mask.

        Returns:
            The final combined mask or None if no mask is used.
        """
        if self._is_batched and key_padding_mask is not None:
            key_padding_mask = (
                key_padding_mask.reshape(batchsize, 1, 1, key_padding_mask.shape[-1])
                .expand(-1, self._num_heads, -1, -1)
                .reshape(batchsize * self._num_heads, 1, key_padding_mask.shape[-1])
            )
        elif key_padding_mask is not None:
            key_padding_mask = key_padding_mask.unsqueeze(0)

        if attn_mask is not None and self._is_batched and not self._is_attention_mask_batched:
            attn_mask = attn_mask.unsqueeze(0).expand(batchsize * self._num_heads, -1, -1)

        if attn_mask is not None:
            if key_padding_mask is not None:
                mask = attn_mask + key_padding_mask
            else:
                mask = attn_mask
        elif key_padding_mask is not None:
            mask = key_padding_mask
        else:
            mask = None

        return mask

    def forward(
        self,
        queries: torch.Tensor,
        keys: torch.Tensor,
        values: torch.Tensor,
        *,
        need_weights: bool = True,
        attn_mask: torch.Tensor | None = None,
        key_padding_mask: torch.Tensor | None = None,
    ) -> tuple[torch.Tensor, torch.Tensor | None]:
        """Computes the output of the sliced multi-head attention.

        Implementation tries to comply to pytorch nn.MultiheadAttention.

        Args:
            queries: The query tensor. Shape: [batch_size, seq_len_tgt, embed_dim].
            keys: The key tensor. Shape: [batch_size, seq_len_src, embed_dim].
            values: The value tensor. Shape: [batch_size, seq_len_src, embed_dim].
            need_weights: If True, returns the (averaged) attention weights in addition to the output tensor. Set
                need_weights=False to use the optimized scaled_dot_product_attention and achieve the best performance.
            attn_mask: Optional attention mask. Shape: [seq_len_tgt, seq_len_src]
            key_padding_mask: Optional key padding mask. Shape: [seq_len_src]

        Returns:
            Tuple containing the output tensor and the (averaged) attention weights.
        """
        if self.training and not need_weights and self._attention_nonlinearity == "softmax":
            q = torch.stack([self._q[i](queries) for i in range(self._num_heads)], dim=-3)
            k = torch.stack([self._kk[i](keys) for i in range(self._num_heads)], dim=-3)
            v = torch.stack([self._v[i](values) for i in range(self._num_heads)], dim=-3)
            mask_to_use = self._compute_flash_attention_mask(
                queries.shape[0], attn_mask=attn_mask, key_padding_mask=key_padding_mask
            )
            if self._is_batched and mask_to_use is not None:
                mask_to_use = ~mask_to_use.reshape(queries.shape[0], self._num_heads, -1, mask_to_use.shape[-1])
            elif mask_to_use is not None:
                mask_to_use = ~mask_to_use.unsqueeze(0)
            head_out = torch.nn.functional.scaled_dot_product_attention(
                q, k, v, attn_mask=mask_to_use, dropout_p=self._dropout.p, scale=self._scale_factor
            )
            head_out = head_out.transpose(-2, -3).flatten(-2)
            head_out = self._out(head_out)
            return head_out, None

        head_out = []
        attn_out = []
        for i in range(self._num_heads):
            # Apply scales factor to q instead of attn matrix as it results in the same output but
            # should be faster as q is smaller than the attn matrix in our usecases so far.
            # (q1*k1 + q2*k2 + ... + qn*kn) * scale = q1*scale*k1 + q2*scale*k2 + ... + qn*scale*kn
            # See also: https://github.com/pytorch/pytorch/blob/a9d84875a99b713676312580574702dc66f1b3df/torch/nn/functional.py#L6357  # noqa: E501
            # Theoretically at least at inference time one could merge the scale factor into the
            # weights of the q projection to save some computation.
            q = self._q[i](queries) * self._scale_factor
            k = self._kk[i](keys)
            v = self._v[i](values)
            k = k.transpose(-1, -2)
            attn = q @ k

            if attn_mask is not None or key_padding_mask is not None:
                mask = self._compute_mask(
                    head_number=i,
                    batch_size=queries.shape[0],
                    mask_dim=attn.shape,
                    attn_mask=attn_mask,
                    key_padding_mask=key_padding_mask,
                )
                mask = ~mask  # type: ignore reportOptionalOperand
                if self._attention_nonlinearity == "softmax":
                    attn = masked_softmax(attn, mask, dim=-1)
                else:
                    attn = self._non_linearity(attn) / attn.shape[1]
                    attn = attn * mask
            elif self._attention_nonlinearity == "softmax":
                attn = self._non_linearity(attn)
            else:
                attn = self._non_linearity(attn) / attn.shape[1]

            attn = self._dropout(attn)
            head_out.append(attn @ v)
            attn_out.append(attn.unsqueeze(1))

        out = torch.concat(head_out, -1)
        out = self._out(out)
        if need_weights:
            attn = torch.concat(attn_out, 1)
            if self._avg_attn_weights:
                attn = torch.mean(attn, 1)
        else:
            attn = None
        return out, attn
