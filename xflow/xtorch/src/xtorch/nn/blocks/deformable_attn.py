"""Multiscale Deformable Attention Module."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import math
from collections.abc import Sequence

import torch
import torch.nn.functional as F
from torch import nn
from torch.nn.init import constant_, xavier_uniform_


class MultiscaleDeformableAttention(nn.Module):
    """Multiscale Deformable Attention Module."""

    def __init__(self, d_model: int = 256, n_levels: int = 4, n_heads: int = 8, n_points: int = 4) -> None:
        """Initializes the Multiscale Deformable Attention Module.

        Args:
            d_model: The dimension of the model.
            n_levels: The number of feature levels.
            n_heads: The number of attention heads.
            n_points: The number of sampling points per attention head.
        """
        super().__init__()
        assert d_model % n_heads == 0, f"d_model must be divisible by n_heads, but got {d_model} and {n_heads}"

        self._n_heads = n_heads
        self._n_points = n_points
        self._n_levels = n_levels

        self._sampling_offsets = nn.Linear(d_model, n_heads * n_levels * n_points * 2)
        self._attention_weights = nn.Linear(d_model, n_heads * n_levels * n_points)
        self._value_proj = nn.Linear(d_model, d_model)
        self._output_proj = nn.Linear(d_model, d_model)

        self._initialize_weights_and_biases()

    def _initialize_weights_and_biases(self) -> None:
        constant_(self._sampling_offsets.weight.data, 0.0)
        thetas = torch.arange(self._n_heads, dtype=torch.float32) * (2.0 * math.pi / self._n_heads)
        grid_init = torch.stack([thetas.cos(), thetas.sin()], -1)
        grid_init = (
            (grid_init / grid_init.abs().max(-1, keepdim=True)[0])
            .view(self._n_heads, 1, 2)
            .repeat(1, self._n_points * self._n_levels, 1)
        )
        for i in range(self._n_points * self._n_levels):
            grid_init[:, i, :] *= i + 1
        with torch.no_grad():
            self._sampling_offsets.bias = nn.Parameter(grid_init.view(-1))
        constant_(self._attention_weights.weight.data, 0.0)
        constant_(self._attention_weights.bias.data, 0.0)
        xavier_uniform_(self._value_proj.weight.data)
        constant_(self._value_proj.bias.data, 0.0)
        xavier_uniform_(self._output_proj.weight.data)
        constant_(self._output_proj.bias.data, 0.0)

    def forward(
        self,
        query: torch.Tensor,
        reference_points: torch.Tensor,
        input_ftrs: torch.Tensor,
        input_spatial_shapes: list[torch.Tensor],
        input_padding_mask: list[torch.Tensor] | None = None,
    ) -> torch.Tensor:
        """Forward pass of the Multiscale Deformable Attention Module.

        Args:
            query: The query tensor of shape (B, Q, C).
            reference_points: A sequence of reference points for each feature level.
            input_ftrs: The input features of shape (C, prod(L, H, W), C).
            input_spatial_shapes: A sequence of spatial shapes for each feature level.
            input_padding_mask: Optional padding mask for the input features.

        Returns:
            output: The output tensor after applying multiscale deformable attention of shape (B, Q, C).
        """
        bs, _, c = input_ftrs.shape
        n_queries = query.shape[1]

        sampling_offsets = self._sampling_offsets(query).view(
            bs, n_queries, self._n_heads, self._n_levels, self._n_points, 2
        )

        attention_weights = self._attention_weights(query).view(
            bs, n_queries, self._n_heads, self._n_levels * self._n_points
        )
        attention_weights = F.softmax(attention_weights, -1).view(
            bs, n_queries, self._n_heads, self._n_levels, self._n_points
        )

        input_splits = [int(h * w) for h, w in input_spatial_shapes]

        value = self._value_proj(input_ftrs)
        value_list = torch.split(value, input_splits, dim=1)

        value_list = [
            v.view(bs, int(h), int(w), self._n_heads, c // self._n_heads).permute(0, 3, 1, 2, 4)
            for v, (h, w) in zip(value_list, input_spatial_shapes)
        ]

        if input_padding_mask is not None:
            value_list = [
                v.masked_fill(padding[:, None, :, :, None], float(0))
                for v, padding in zip(value_list, input_padding_mask)
            ]

        value_list = [v.flatten(0, 1) for v in value_list]  # Flatten batch and heads

        sampling_locations = (
            reference_points[:, :, None, :, None, :2]
            + sampling_offsets / self._n_points * reference_points[:, :, None, :, None, 2:] * 0.5
        )

        output = self._multiscale_deformable_attention(value_list, sampling_locations, attention_weights)
        return self._output_proj(output)

    def _multiscale_deformable_attention(
        self,
        value_list: Sequence[torch.Tensor],
        sampling_locations: torch.Tensor,
        attention_weights: torch.Tensor,
    ) -> torch.Tensor:
        bs, n_queries = sampling_locations.shape[:2]
        c = value_list[0].shape[-1] * self._n_heads

        sampling_locations = sampling_locations.permute(3, 0, 2, 1, 4, 5).flatten(1, 2)
        attention_weights = attention_weights.permute(3, 0, 2, 1, 4).flatten(1, 2)

        sampling_value_list: list[torch.Tensor] = []
        for value_l, sampling_grid_l, attention_l in zip(value_list, sampling_locations, attention_weights):
            sampling_value_l = torch.nn.functional.grid_sample(
                value_l.permute(0, 3, 1, 2),
                2 * sampling_grid_l - 1,  # grid sample expects locations in [-1, 1]
                mode="bilinear",
                padding_mode="zeros",
                align_corners=False,
            )

            # apply attention weights
            sampling_value_l = sampling_value_l * attention_l.unsqueeze(1)
            sampling_value_l = sampling_value_l.sum(dim=-1)

            sampling_value_l = sampling_value_l.view(bs, c, n_queries)
            sampling_value_l = sampling_value_l.permute(0, 2, 1)
            sampling_value_list.append(sampling_value_l)

        return torch.stack(sampling_value_list).sum(0)
