"""CLEAR metrics for multi-object tracking."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import time
from collections import OrderedDict
from typing import Final, NamedTuple, cast

import motmetrics as mm
import numpy as np
import pandas as pd
import torch
from motmetrics import math_util
from motmetrics.metrics import DataFrameMap, MetricsHost
from sklearn.metrics.pairwise import euclidean_distances
from torchmetrics import Metric
from torchmetrics.utilities.data import dim_zero_cat

_LOGGER = logging.getLogger(__name__)

_OBJ_IDS_KEY: Final = "obj_ids"
_BOXES_KEY: Final = "boxes"
_SCORES_KEY: Final = "scores"
_CLASSES_KEY: Final = "classes"
_AGGREGATED_KEY: Final = "all"


class TrackingMetricsPredictions(NamedTuple):
    """Dataclass for the input to the tracking metric coming from the predictions.

    Attributes:
        pred_obj_ids: Predicted object ids. Tensor of shape (N, ).
        pred_boxes: Predicted bounding boxes in (x, y, z, class, score). Tensor of shape (N, 5).
    """

    pred_obj_ids: torch.Tensor
    pred_boxes: torch.Tensor


class TrackingMetricsGroundTruth(NamedTuple):
    """Dataclass for the input to the tracking metric coming from the ground truth.

    Attributes:
        gt_obj_ids: Ground truth object ids. Tensor of shape (M, ).
        gt_boxes: Ground truth bounding boxes in (x, y, z, class). Tensor of shape (M, 4).
        video_name: Name of the video to which the frame belongs to.
        frame_id: Frame number of the current frame.
    """

    gt_obj_ids: torch.Tensor
    gt_boxes: torch.Tensor
    video_name: str
    frame_id: int


def assa_alpha(df: DataFrameMap, num_detections: int, num_gt_ids: int, num_dt_ids: int) -> float:
    """Custom implemetation of assa_alpha from the motmetrics library.

    Speeds up the original implementation significantly by replacing the for loop with native pandas operations.

    Args:
        df: DataFrameMap containing the matched detections and ground truth.
        num_detections: Total number of detections.
        num_gt_ids: Total number of ground truth ids.
        num_dt_ids: Total number of detection ids.

    Returns:
        The AssA metric value.
    """
    oids = np.sort(df.full["OId"].dropna().unique())
    hids = np.sort(df.full["HId"].dropna().unique())
    oids_idx = {o: i for i, o in enumerate(oids)}
    hids_idx = {h: i for i, h in enumerate(hids)}

    max_gt_ids = len(oids)
    max_dt_ids = len(hids)

    match_count_array = np.zeros((max_gt_ids, max_dt_ids), dtype=np.float32)
    gt_id_counts = np.zeros((max_gt_ids, 1), dtype=np.float32)
    tracker_id_counts = np.zeros((1, max_dt_ids), dtype=np.float32)

    # count occurances of pairs of OId and HId for matched objects
    if max_dt_ids > 0 and max_gt_ids > 0:
        counts = (
            df.noraw[df.noraw.Type.isin(["SWITCH", "MATCH"])][["OId", "HId"]].value_counts().to_frame().reset_index()
        )
        row_idx = counts["OId"].map(oids_idx).to_numpy()
        col_idx = counts["HId"].map(hids_idx).to_numpy()
        match_count_array[row_idx, col_idx] = counts["count"].to_numpy()
    # count occurances of predicted objects
    if max_dt_ids > 0:
        counts = df.noraw[~df.noraw["HId"].isna()][["HId"]].value_counts().to_frame().reset_index()
        idx = counts["HId"].map(hids_idx).to_numpy()
        tracker_id_counts[0, idx] = counts["count"].to_numpy()
    # count occurances of ground truth objects
    if max_gt_ids > 0:
        counts = df.noraw[~df.noraw["OId"].isna()][["OId"]].value_counts().to_frame().reset_index()
        idx = counts["OId"].map(oids_idx).to_numpy()
        gt_id_counts[idx, 0] = counts["count"].to_numpy()

    ass_a = match_count_array / np.maximum(1, gt_id_counts + tracker_id_counts - match_count_array)
    return math_util.quiet_divide((ass_a * match_count_array).sum(), max(1, num_detections))


class TrackingMetric(Metric):
    """Class to compute tracking-related metrics."""

    full_state_update = False

    def __init__(
        self,
        class_names: list[str],
        max_distance: float = 2.0,
        metrics: list[str] | None = None,
        min_score: float = 0.0,
        *,
        only_aggregated: bool = True,
    ) -> None:
        """Initializes the metric state.

        A more thorough description of each state can be found in the update method.

        Args:
            class_names: Names of all classes.
            max_distance: Maximum euclidean distance to consider a match. Object / hypothesis points with larger
                distance are set to np.nan signalling do-not-pair. Default: 2.0 as in nuScenes tracking benchmark.
            metrics: List of metrics to compute. If None, the default metrics from motmetrics are used.
            min_score: Predictions with a confidence score below `min_score` are ignored. Default: 0.0.
            only_aggregated: If True, only the aggregated metrics are returned.
        """
        super().__init__()
        self._class_names = class_names
        self._max_distance = max_distance
        self._metrics = (
            metrics
            if metrics is not None
            else mm.metrics.motchallenge_metrics + ["hota_alpha", "deta_alpha", "assa_alpha"]
        )
        # some metrics will be computed on the merged accumulator, others on the per-frame accumulator
        self._metrics_merged = [metric for metric in self._metrics if "_alpha" in metric or "motp" in metric]
        self._metrics_per_frame = list(set(self._metrics) - set(self._metrics_merged))
        self._min_score = min_score
        self._only_aggregated = only_aggregated

        self.add_state("pred_obj_ids", default=[], dist_reduce_fx="cat")
        self.add_state("pred_boxes", default=[], dist_reduce_fx="cat")
        self.add_state("pred_video_ids", default=[], dist_reduce_fx="cat")
        self.add_state("pred_frame_ids", default=[], dist_reduce_fx="cat")

        self.add_state("gt_obj_ids", default=[], dist_reduce_fx="cat")
        self.add_state("gt_boxes", default=[], dist_reduce_fx="cat")
        self.add_state("gt_video_ids", default=[], dist_reduce_fx="cat")
        self.add_state("gt_frame_ids", default=[], dist_reduce_fx="cat")

    def update(
        self,
        prediction_inputs: list[TrackingMetricsPredictions],
        ground_truth_inputs: list[TrackingMetricsGroundTruth],
    ) -> None:
        """Updates the local metric state for the current batch.

        Args:
            prediction_inputs: List of PredictionInput dataclasses.
            ground_truth_inputs: List of GroundTruthInput dataclasses.
        """
        for pred_input, gt_input in zip(prediction_inputs, ground_truth_inputs):
            self.update_single_frame(
                pred_obj_ids=pred_input.pred_obj_ids,
                pred_boxes=pred_input.pred_boxes,
                gt_obj_ids=gt_input.gt_obj_ids,
                gt_boxes=gt_input.gt_boxes,
                video_name=gt_input.video_name,
                frame_id=gt_input.frame_id,
            )

    def update_single_frame(
        self,
        pred_obj_ids: torch.Tensor,
        pred_boxes: torch.Tensor,
        gt_obj_ids: torch.Tensor,
        gt_boxes: torch.Tensor,
        *,
        video_name: str,
        frame_id: int,
    ) -> None:
        """Updates the local metric state for the current batch.

        Store separate metadata related to preds and ground truth given that the lists/tensors can have
        different lengths, and during compute we need to match them to their corresponding video and frame.

        Precondition: This function has to be called in an increasing order of frame_id, since this has an impact
        on the final metric computation.

        Args:
            pred_obj_ids: Predicted object ids. Tensor of shape (N,).
            pred_boxes: Predicted bounding boxes in (x, y, z, class, score). Tensor of shape (N, 5).
            gt_obj_ids: Ground truth object ids. Tensor of shape (M,).
            gt_boxes: Ground truth bounding boxes in (x, y, z, class). Tensor of shape (M, 4).
            video_name: Name of the video to which the current frame belongs.
            frame_id: Frame number of the current frame.
        """
        pred_obj_ids, pred_boxes, gt_obj_ids, gt_boxes = (
            x.detach() for x in (pred_obj_ids, pred_boxes, gt_obj_ids, gt_boxes)
        )
        # stores object ids and boxes
        self.pred_obj_ids.append(pred_obj_ids)  # type: ignore[reportArgumentType]
        self.pred_boxes.append(pred_boxes)  # type: ignore[reportArgumentType]
        self.gt_obj_ids.append(gt_obj_ids)  # type: ignore[reportArgumentType]
        self.gt_boxes.append(gt_boxes)  # type: ignore[reportArgumentType]

        # needs to store metadata for object ids and boxes, to later match them to their corresponding
        # video and frame
        # uses hash to convert video name to a "unique" id since tensors can only store numerical values
        video_id = hash(video_name)
        self.pred_video_ids.append(torch.full_like(pred_obj_ids, video_id, dtype=gt_obj_ids.dtype))  # type: ignore[reportArgumentType]
        self.pred_frame_ids.append(torch.full_like(pred_obj_ids, frame_id))  # type: ignore[reportArgumentType]
        self.gt_video_ids.append(torch.full_like(gt_obj_ids, video_id))  # type: ignore[reportArgumentType]
        self.gt_frame_ids.append(torch.full_like(gt_obj_ids, frame_id))  # type: ignore[reportArgumentType]

    def compute(self) -> dict[str | int, dict[str, torch.Tensor]] | dict[str, torch.Tensor]:
        """Computes the metric based on the accumulated state.

        The `compute` function does the following steps:
            1. Syncs metric states between processes and nodes.
            2. Reduce gathered metric states.
            3. Finally, this method is executed on the gathered metric states.

        Metrics need to be computed per video, and finally aggregated. We first compute metrics per class. To do so, one
        accumulator is created and updated for each video and class-wise metrics are computed based on all the
        accumulators. Finally, we average average/sum the class-wise metrics to get the overall metrics.

        Returns:
            Dictionary with the computed metrics.
        """
        # reduce lists, and converts them into tensors
        pred_obj_ids = dim_zero_cat(self.pred_obj_ids)  # type: ignore[reportArgumentType]
        pred_boxes = dim_zero_cat(self.pred_boxes)  # type: ignore[reportArgumentType]
        pred_video_ids = dim_zero_cat(self.pred_video_ids)  # type: ignore[reportArgumentType]
        pred_frame_ids = dim_zero_cat(self.pred_frame_ids)  # type: ignore[reportArgumentType]
        gt_obj_ids = dim_zero_cat(self.gt_obj_ids)  # type: ignore[reportArgumentType]
        gt_boxes = dim_zero_cat(self.gt_boxes)  # type: ignore[reportArgumentType]
        gt_video_ids = dim_zero_cat(self.gt_video_ids)  # type: ignore[reportArgumentType]
        gt_frame_ids = dim_zero_cat(self.gt_frame_ids)  # type: ignore[reportArgumentType]

        _LOGGER.info("Start compute tracking metrics.")
        start = time.time()

        preds_per_video_id = self._collect_data_per_video_id(pred_obj_ids, pred_boxes, pred_video_ids, pred_frame_ids)
        gts_per_video_id = self._collect_data_per_video_id(gt_obj_ids, gt_boxes, gt_video_ids, gt_frame_ids)

        metrics_summary = {}
        # register custom assa_alpha metric implementation (30x speedup for large dataframes)
        metric_host: MetricsHost = mm.metrics.create()
        metric_host.register(fnc=assa_alpha)

        # calculate class-wise metrics
        for class_id, class_name in enumerate(self._class_names):
            _LOGGER.info(f"Compute tracking metric for {class_name}.")
            accumulators = self._compute_accumulators(preds_per_video_id, gts_per_video_id, class_id)
            # calculate metrics on the merged accumulator with all tracks in one big sequence
            merged_accumulators = mm.MOTAccumulator.merge_event_dataframes(accumulators)
            # calculate a subset of metrics on the merged accumulator
            summary: pd.DataFrame = cast(
                pd.DataFrame,
                metric_host.compute(merged_accumulators, metrics=self._metrics_merged, return_dataframe=True),
            )
            metrics_summary[class_name] = {
                k: torch.tensor([v], device=gt_boxes.device) for k, v in summary.iloc[-1].to_dict().items()
            }
            # evaluate some metrics per frame where it does not make any difference regarding the result
            summary: pd.DataFrame = cast(
                pd.DataFrame,
                metric_host.compute_many(accumulators, metrics=self._metrics_per_frame, generate_overall=True),
            )
            metrics_summary[class_name].update(
                {k: torch.tensor([v], device=gt_boxes.device) for k, v in summary.iloc[-1].to_dict().items()}
            )

        # combine metrics across classes
        metrics_summary[_AGGREGATED_KEY] = self._combine_metrics_over_classes(metrics_summary)
        end = time.time()
        _LOGGER.info(f"Tracking metrics calculated in {end - start:.2f} seconds.")
        return metrics_summary[_AGGREGATED_KEY] if self._only_aggregated else metrics_summary

    @staticmethod
    def _collect_data_per_video_id(
        obj_ids: torch.Tensor, boxes: torch.Tensor, video_ids: torch.Tensor, frame_ids: torch.Tensor
    ) -> dict[int, OrderedDict[int, dict[str, torch.Tensor]]]:
        """Collects object ids, boxes, classes and scores in a dict of dicts, per video id and per frame id.

        Args:
            obj_ids: Object ids. Tensor of shape (N,).
            boxes: Bounding boxes (x,y,z), class (and score for predictions). Tensor of shape (N, 4 or 5).
            video_ids: Video ids. Tensor of shape (N,).
            frame_ids: Frame ids. Tensor of shape (N,).

        Returns:
            Dict of dicts with object ids and boxes per video id and per frame id.

        Example:
                    {
                        video_id_1: {
                            frame_id_1: {
                                obj_ids: [1, 2, 3],
                                boxes: [[0, 0, 1], [0, 0, 1], [0, 0, 1]]
                                classes: [0, 1, 1],
                                scores: [0.5, 0.3, 0.5]
                            },
                            frame_id_2: { ... },
                        },
                        video_id_2: { ... },
                    }
        """
        result = {}
        for this_video_id in torch.unique(video_ids):
            this_vid_mask = video_ids == this_video_id
            this_vid_obj_ids = obj_ids[this_vid_mask]
            this_vid_boxes = boxes[this_vid_mask]
            this_vid_frames = frame_ids[this_vid_mask]

            this_vid_result = OrderedDict()
            unique_frame_ids = torch.unique(this_vid_frames).sort().values  # noqa: PD011
            for this_frame_id in unique_frame_ids:
                this_frame_mask = this_vid_frames == this_frame_id
                # take care of the different layout of gt (N,4) and pred (N,5) box tensors
                score = (
                    this_vid_boxes[:, 4][this_frame_mask]
                    if this_vid_boxes.shape[1] > 4
                    else torch.tensor([], device=this_vid_boxes.device)
                )
                this_vid_result[int(this_frame_id)] = {
                    _OBJ_IDS_KEY: this_vid_obj_ids[this_frame_mask].long(),
                    _BOXES_KEY: this_vid_boxes[:, :3][this_frame_mask],
                    _CLASSES_KEY: this_vid_boxes[:, 3][this_frame_mask].long(),
                    _SCORES_KEY: score,
                }

            result[int(this_video_id)] = this_vid_result

        return result

    def _compute_accumulators(
        self,
        preds_per_video_id: dict[int, OrderedDict[int, dict[str, torch.Tensor]]],
        gts_per_video_id: dict[int, OrderedDict[int, dict[str, torch.Tensor]]],
        class_id: int,
    ) -> list[mm.MOTAccumulator]:
        """Computes the accumulators for each video.

        For the center distance, boxes need to be in (x,y,z) format.

        Args:
            preds_per_video_id: Dict of dicts with predictions per video id and per frame
            gts_per_video_id: Dict of dicts with ground truth per video id and per frame
            class_id: Class id for which to compute the metrics

        Returns:
            List of mm.MOTAccumulator, one per GT video.
        """
        accumulators = []

        for video_id, gts_per_frame in gts_per_video_id.items():
            try:
                preds_per_frame = preds_per_video_id.get(video_id, {})

                # Create an accumulator that will be updated for each frame
                acc = mm.MOTAccumulator(auto_id=True)
                # gts_per_frame (and also preds_per_frame) is an ordered dict, so we can iterate and frame order is kept
                for frame_id, gt_data in gts_per_frame.items():
                    gt_classes = gt_data[_CLASSES_KEY]
                    # only consider ground truth of the current class
                    class_mask = gt_classes == class_id
                    gt_ids = gt_data[_OBJ_IDS_KEY][class_mask]
                    # convert to numpy here for the euclidean distance calculation
                    gt_boxes = gt_data[_BOXES_KEY][class_mask]

                    pred_ids = torch.tensor([])
                    pred_boxes = torch.tensor([])
                    if frame_id in preds_per_frame:
                        pred_data = preds_per_frame[frame_id]
                        pred_classes = pred_data[_CLASSES_KEY]
                        # only consider predictions of the current class and with a score above the threshold
                        class_mask_pred = pred_classes == class_id
                        class_mask_pred &= pred_data[_SCORES_KEY] >= self._min_score
                        pred_ids = pred_data[_OBJ_IDS_KEY][class_mask_pred]
                        pred_boxes = pred_data[_BOXES_KEY][class_mask_pred]

                    # Use 3D euclidean center distance as in nuscenes
                    if len(pred_boxes) > 0 and len(gt_boxes) > 0:
                        distance = euclidean_distances(gt_boxes.cpu().numpy(), pred_boxes.cpu().numpy())
                        distance[distance > self._max_distance] = float("nan")
                    else:
                        distance = []

                    acc.update(gt_ids.cpu(), pred_ids.cpu(), distance)
            except KeyError:  # noqa: PERF203
                _LOGGER.exception(f"A KeyError occurred for Video {video_id}, it will be dropped from the evaluation.")
            else:
                accumulators.append(acc)

        return accumulators

    def _combine_metrics_over_classes(
        self, metrics_summary: dict[str | int, dict[str, torch.Tensor]]
    ) -> dict[str, torch.Tensor]:
        """Combines class-wise metrics to get overall metrics taking averages or sums depending on the metric.

        Args:
            metrics_summary: Dict of dicts with class-wise metrics.

        Returns:
            Dict with overall metrics.
        """
        overall_metrics = {}
        # average those metrics over all classes, sum the others
        metrics_names_to_average = {
            "idf1",
            "idp",
            "idr",
            "recall",
            "precision",
            "mota",
            "motp",
            "hota_alpha",
            "deta_alpha",
            "assa_alpha",
        }
        for metric_name in next(iter(metrics_summary.values())):
            stacked_metric = torch.stack([metrics_summary[class_name][metric_name] for class_name in self._class_names])
            overall_metrics[metric_name] = (
                torch.nanmean(stacked_metric) if metric_name in metrics_names_to_average else torch.sum(stacked_metric)
            )

        return overall_metrics
