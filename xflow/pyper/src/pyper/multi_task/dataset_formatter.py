"""Module containing the dataset formatter interface and implementations."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
from typing import Generic, Protocol, TypeVar

import numpy as np
import pandas as pd

from pyper.multi_task.dataset_sample import DatasetSample

I_contra = TypeVar("I_contra", contravariant=True)
O_co = TypeVar("O_co", covariant=True)


class DatasetFormatter(Protocol, Generic[I_contra, O_co]):
    """Protocol for formatting datasets."""

    def format(self, data: I_contra) -> O_co:
        """Method for formatting any pd DataFrame as needed."""
        ...


class DatasetColumnListFormatter(DatasetFormatter[pd.DataFrame, dict[str, list[str]]]):
    """Formats the pd DataFrame to a task-path mapping.

    This formatter assumes that the DataFrame represents a dataset where each row contains
    matching paths to data.
    Returns a dictionary where the keys are column names and the values are lists of paths to data.

    Example:
        {
        "column_name1": [path/to/data1, path/to/data2, ...],
        "column_name2": [path/to/data1, path/to/data2, ...],
        ...
        }
    """

    def format(self, data: pd.DataFrame) -> dict[str, list[str]]:
        """Format the pd DataFrame to a task-path mapping."""

        pd_dict = data.to_dict(orient="list")
        assert all(isinstance(key, str) for key in pd_dict)
        return {str(key): value for key, value in pd_dict.items()}


# TODO(christoph): avoid the magic strings  # noqa: TD003
class DatasetSamplesFormatter(DatasetFormatter[pd.DataFrame, list[DatasetSample]]):
    """Formats the dataset as a dictionary of inputs and labels.

    This formatter assumes that the DataFrame represents a dataset where each row contains
    matching paths to data. The columns of the DataFrame are divided into inputs and labels.
    """

    def __init__(self, input_ids: str | list[str]) -> None:
        """Initialize the formatter."""
        self.input_ids = [input_ids] if isinstance(input_ids, str) else input_ids

    def format(self, data: pd.DataFrame, *, skip_unlabeled: bool = True) -> list[DatasetSample]:
        """Format the pd DataFrame to a list of DatasetSamples."""

        samples = []
        # we use itertuples and getattr here as it is a lot faster with a large dataset
        for row in data.itertuples():
            inputs = {
                column: str(getattr(row, column))
                for column in data.columns
                if any(input_id in column for input_id in self.input_ids) and not pd.isna(getattr(row, column))
            }
            labels = {
                column: str(getattr(row, column))
                for column in data.columns
                if not any(input_id in column for input_id in self.input_ids)
                and column != "split"
                and not pd.isna(getattr(row, column))
            }

            if skip_unlabeled and not labels:
                continue

            sample = DatasetSample(
                inputs=inputs,
                labels=labels,
            )
            samples.append(sample)

        return samples


class AbsolutePathFormatter(DatasetFormatter[pd.DataFrame, pd.DataFrame]):
    """Formats the dataset to absolute paths.

    This formatter assumes that the DataFrame represents a dataset where each row contains
    relative paths to data. The formatter converts the relative paths to absolute paths.
    """

    def __init__(self, base_path: str) -> None:
        """Initialize the formatter with the base path.

        Args:
            base_path: The base path to which the relative paths are appended.
        """
        self.base_path = base_path

    def format(self, data: pd.DataFrame) -> pd.DataFrame:
        """Format the pd DataFrame to absolute paths."""

        data_except_split = data.loc[:, pd.Series(data.columns != "split", index=data.columns)]
        # we replace NaN values here with None values as otherwise we get an error if a column only contains NaN values
        data_except_split = data_except_split.replace(np.nan, None)
        data[data_except_split.columns] = self.base_path + "/" + data_except_split
        # we now once again have to replace NaN values to None values as the string operation readds them
        data = data.replace(np.nan, None)

        return data
