"""This module contains tests for the dataset formatters."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""


import pandas as pd
import pytest

from pyper.multi_task.dataset_formatter import (
    AbsoluteP<PERSON><PERSON><PERSON>atter,
    Dataset<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>atter,
    DatasetSamplesFormatter,
)


@pytest.fixture(name="dummy_df")
def dummy_dataset_path(dummy_data: dict[str, list[str | None]]) -> pd.DataFrame:
    """Return a dummy df."""

    return pd.DataFrame(dummy_data)


def test_dataset_column_list_formatter(dummy_df: pd.DataFrame, dummy_data: dict[str, list[str | None]]) -> None:
    """Test the DatasetColumnListFormatter class."""

    # GIVEN a dataframe

    # GIVEN an instance of the DatasetColumnListFormatter
    formatter = DatasetColumnListFormatter()

    # WHEN the dataframe is formatted
    formatted = formatter.format(dummy_df)

    # THEN we get the expected dictionary
    assert formatted == dummy_data


def test_dataset_samples_formatter(dummy_df: pd.DataFrame) -> None:
    """Test the DatasetSamplesFormatter class."""

    # GIVEN a dataframe

    # GIVEN an instance of the DatasetSamplesFormatter
    formatter = DatasetSamplesFormatter(input_ids="yuv")

    # WHEN the dataframe is formatted
    data_samples = formatter.format(dummy_df)

    # THEN there are three dataset samples
    assert len(data_samples) == 3

    # THEN the inputs are as expected
    assert data_samples[0].inputs == {
        "yuv1": "/path/to/yuv1_1",
        "yuv3": "/path/to/yuv3_1",
    }
    assert data_samples[1].inputs == {
        "yuv1": "/path/to/yuv1_2",
        "yuv2": "/path/to/yuv2_2",
        "yuv3": "/path/to/yuv3_2",
    }
    assert data_samples[2].inputs == {
        "yuv1": "/path/to/yuv1_3",
    }

    # THEN the labels are as expected
    assert data_samples[0].labels == {
        "task2": "/path/to/task2_1",
        "task3": "/path/to/task3_1",
        "task4": "/path/to/task4_1",
    }
    assert data_samples[1].labels == {
        "task1": "/path/to/task1_2",
        "task4": "/path/to/task4_2",
    }

    assert data_samples[2].labels == {
        "task1": "/path/to/task1_3",
        "task2": "/path/to/task2_3",
        "task3": "/path/to/task3_3",
        "task4": "/path/to/task4_3",
    }


@pytest.fixture(name="dummy_data_rel_paths")
def dummy_data_rel_paths() -> dict[str, list[str | None | float]]:
    """Return a dictionary with dummy data."""
    return {
        # we mix NaN and None here as for example the AML sdk uses NaN values instead of None for missing values
        "yuv1": ["path/to/yuv1_1", "path/to/yuv1_2", "path/to/yuv1_3"],
        "yuv2": [float("NaN"), "path/to/yuv2_2", float("NaN")],
        "yuv3": ["path/to/yuv3_1", "path/to/yuv3_2", float("NaN")],
        "task1": [None, "path/to/task1_2", "path/to/task1_3"],
        "task2": ["path/to/task2_1", float("NaN"), "path/to/task2_3"],
        "task3": ["path/to/task3_1", None, "path/to/task3_3"],
        "task4": ["path/to/task4_1", "path/to/task4_2", "path/to/task4_3"],
        "task5": [float("NaN"), float("NaN"), None],
    }


@pytest.fixture(name="dummy_df_rel_paths")
def dummy_dataset_rel_paths(dummy_data_rel_paths: dict[str, list[str | None]]) -> pd.DataFrame:
    """Return a dummy df."""

    return pd.DataFrame(dummy_data_rel_paths)


def test_absolute_path_formatter(dummy_df_rel_paths: pd.DataFrame) -> None:
    """Test the AbsolutePathFormatter class."""

    # GIVEN a dataframe

    # GIVEN an instance of the AbsolutePathFormatter
    formatter = AbsolutePathFormatter("/base/path")

    # WHEN the dataframe is formatted
    formatted = formatter.format(dummy_df_rel_paths)

    # THEN the paths are absolute
    assert formatted["yuv1"].tolist() == [
        "/base/path/path/to/yuv1_1",
        "/base/path/path/to/yuv1_2",
        "/base/path/path/to/yuv1_3",
    ]
    assert formatted["task1"].tolist() == [
        None,
        "/base/path/path/to/task1_2",
        "/base/path/path/to/task1_3",
    ]
    assert formatted["task4"].tolist() == [
        "/base/path/path/to/task4_1",
        "/base/path/path/to/task4_2",
        "/base/path/path/to/task4_3",
    ]
    assert formatted["yuv2"].tolist() == [None, "/base/path/path/to/yuv2_2", None]
    assert formatted["task2"].tolist() == [
        "/base/path/path/to/task2_1",
        None,
        "/base/path/path/to/task2_3",
    ]
    assert formatted["task3"].tolist() == [
        "/base/path/path/to/task3_1",
        None,
        "/base/path/path/to/task3_3",
    ]
    assert formatted["task5"].tolist() == [None] * 3
