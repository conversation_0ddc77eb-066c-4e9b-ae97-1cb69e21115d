"""Module providing helper functions for the Azure compute environment."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import os


def runs_on_aml_node() -> bool:
    """Check if this process run on an AML nodes."""
    run_id = os.environ.get("AZUREML_RUN_ID", "")
    # we could also check that the fqdn hostname ends with ".internal.cloudapp.net" or ".ax.internal.cloudapp.net"
    return bool(run_id)


def num_active_nodes() -> int:
    """Get the number of active nodes used by the run by checking the environment variable AZUREML_NODE_COUNT."""
    return int(os.environ.get("AZUREML_NODE_COUNT", "1"))
