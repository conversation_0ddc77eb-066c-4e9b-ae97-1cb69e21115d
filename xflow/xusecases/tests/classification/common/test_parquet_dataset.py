"""Tests for the Parquet dataset."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert <PERSON> GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import glob
import os
from typing import Any

import numpy as np
import pyarrow as pa
import pyarrow.parquet as pq
import pytest

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset


@pytest.fixture
def sample_input_dict() -> dict[str, Any]:
    """Creates a sample dictionary for testing purposes.

    Returns:
        dict: A dictionary containing sample data.
    """
    data = {
        "id": [1, 2],
        "snippet_data_without_schema": ["gt_data1", "gt_data2"],
        "snippet_image_data": [
            np.array([[[1, 2, 3], [4, 5, 6], [7, 8, 9]]], dtype=np.uint8),
            np.array([[[10, 11, 12], [13, 14, 15], [16, 17, 18]]], dtype=np.uint8),
        ],
        "snippet_image_type": ["type1", "type2"],
        "snippet_location": [[0.1, 0.2], [0.3, 0.4]],
        "box_width": [10.0, 20.0],
        "box_height": [5.0, 40.0],
        "split": ["train", "val"],
        "object_id": ["obj1", "obj2"],
        "track_id": ["track1", "track2"],
        "view": ["view1", "view2"],
        "stream_camera": ["camera1", "camera2"],
        "classifier_class_name": ["class1", "class2"],
        "trafficSignClass": ["sign1_hierarchy1", "sign2_hierarchy2"],
        "value": [0.1, 0.2],
        "country_code": ["US", "DE"],
        "blacklisted": [False, True],
        "blacklisted_reason": ["reason1", "reason2"],
        "frame_image_path": ["frame_path1", "frame_path2"],
        "shape": ["shape1", "shape2"],
        "hierarchy_class": ["hierarchy1", "hierarchy2"],
        "is_inverted": [False, True],
        "text": ["text1", "text2"],
    }
    return data


def test_dataset_to_parquet(tmpdir_delete_after: str, sample_input_dict: dict[str, Any]) -> None:
    """Test the conversion of datalake file lists to a Parquet file.

    Args:
        tmpdir_delete_after: Temporary directory to store the outputs.
        sample_input_dict: Sample dictionary for testing.
    """
    # GIVEN a sample Arrow Table created from the dictionary
    # convert the images to base64 before creating the Arrow Table
    dataset = ClassifierParquetDataset()
    sample_input_dict["snippet_image_data"] = [
        dataset.encode_image_to_bytes(image) for image in sample_input_dict["snippet_image_data"]
    ]
    input_table = pa.Table.from_pydict(sample_input_dict)
    output_dir = os.path.join(tmpdir_delete_after, "output")

    # WHEN saving the Arrow Table to a Parquet file
    dataset.save_to_parquet(input_table, str(output_dir))
    parquet_files = glob.glob(os.path.join(output_dir, "*/*.parquet"))

    # THEN the files generated are equal to the number of countries (=2) in the table.
    assert len(parquet_files) == 2

    # THEN the loaded Parquet file matches the original sample Arrow Table, ignoring column order and data types.
    output_table = pq.read_table(output_dir)

    # Sort both tables by "id" for consistent ordering of rows before validating equality
    if "id" in output_table.column_names and "id" in input_table.column_names:
        output_table = output_table.sort_by("id")
        input_table = input_table.sort_by("id")
    else:
        msg = "The 'id' column is missing in one of the tables."
        raise ValueError(msg)

    input_dict = input_table.to_pydict()
    output_dict = output_table.to_pydict()

    # THEN the columns in the input and output tables match
    for column in input_dict:
        assert column in output_dict, f"Column '{column}' is missing in the output dictionary."
        input_data = input_dict[column]
        output_data = output_dict[column]
        assert column in output_dict
        input_data = input_dict[column]
        output_data = output_dict[column]

        if isinstance(input_data[0], (np.ndarray, list)):
            # Compare numpy arrays using np.allclose
            for inp, out in zip(input_data, output_data):
                assert np.allclose(inp, out)
        elif isinstance(input_data[0], (float, np.float32)):
            # Compare float values using np.isclose
            for inp, out in zip(input_data, output_data):
                assert np.isclose(inp, out)
        else:
            # Compare other data types directly
            assert input_data == output_data

    # THEN the output table contains all the required columns
    for column in dataset.required_columns:
        assert column in output_dict

    # THEN the output table contains all the optional columns filled with None
    # if they are not present in the input table
    for column in dataset.column_names:
        if column not in input_dict and column not in dataset.required_columns:
            assert output_dict[column] == [None] * len(output_dict[column])
