"""Test script to check classifier patch extractor."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
import os
from pathlib import Path
from typing import Any

import pyarrow as pa
import pytest

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from xtension.parameter.environment import parse_cli_arguments
from xtension.parameter.model_config import parse_model_config_arguments
from xusecases.classification import model_configs
from xusecases.classification.tasks.ts_classifier_patch_extractor import process_data
from xusecases.classification.utils.classifier_patch_extractor.helpers.common_functions import similar_dataset_exists
from xusecases.helpers import get_test_data_folder


@pytest.fixture(name="config")
def fixture_config() -> dict[str, Any]:
    """Fixture to return patch extractor configuration.

    Returns:
        config: patch extractor configuration
    """

    data = {
        "input_config": {
            "csv_file_path": "sample_ts_alliance_dataset.csv",
            "csv_col_name_labels": "traffic_sign",
            "csv_col_name_images": "rgb",
            "multiview": False,
        },
        "patch_extractor_config": {"patch_size": 96, "core_size": 64},
        "dataset_filter_config": {
            "selected_filters": ["filter_size", "filter_position", "filter_attributes", "filter_class_count"],
            "available_filters": {
                "filter_size": {
                    "info": "Filter samples that do not satisfy the specified minimum height or width",
                    "height_threshold": 7,
                    "width_threshold": 18,
                },
                "filter_position": {
                    "info": "Filter samples that are not inside the specified ROI",
                    "x_min": 0,
                    "x_max": 2800,
                    "y_min": 0,
                    "y_max": 2000,
                },
                "filter_attributes": {
                    "info": "Filter out data according to a set of given attribute values",
                    "attributes": {"occluded": ">50%", "truncated": ">50%", "unrecognizable": True},
                },
                "filter_class_count": {"info": "Filter classes with too few samples", "count_threshold": 0},
            },
        },
        "image_filter": {
            "minimum_contrast": {
                "info": "Filter images with too low contrast",
                "roi_part": 0.4,
                "contrast_threshold": 10,
            }
        },
        "data_lake_config": {"data_lake_path": "classifier_data_lakes/training_lakes/", "dataset_name": "test_dataset"},
        "dataset_split_config": {"split_ratio": 20, "random_seed": 1, "include_single_track": True},
        "output_config": {
            "image_directory": "classifier_data_lakes/training_lakes/",
            "label_directory": "classifier_data_lakes/training_lakes/",
            "dataset_hashes_file": "classifier_data_lakes/training_lakes/",
            "image_cache_path": "classifier_data_lakes/cache/",
            "parquet": False,
        },
    }
    return data


@pytest.fixture(name="normal_config")
def get_normal_config(config: dict[str, Any]) -> tuple[dict[str, Any], str, str]:
    """Fixture to return normal patch extractor configuration.

    Args:
        config: patch extractor configuration

    Returns:
        normal_config: normal patch extractor configuration
    """
    normal_config = config
    test_base_path = get_test_data_folder()
    detector_dataset_sample = os.path.join(test_base_path, "vision/datasets/ts/detection")
    detector_image_json_base = os.path.join(test_base_path, "vision/datasets/ts/detection")
    return normal_config, detector_dataset_sample, detector_image_json_base


@pytest.fixture(name="multiview_config")
def get_multiview_config(config: dict[str, Any]) -> tuple[dict[str, Any], str, str]:
    """Fixture to return multiview patch extractor configuration.

    Args:
        config: patch extractor configuration

    Returns:
        multiview_config: multiview patch extractor configuration
    """
    multiview_config = config
    multiview_config["input_config"]["csv_file_path"] = "sample_multitask_dataset_yuv_trifocal.csv"
    multiview_config["input_config"]["multiview"] = True
    multiview_config["input_config"]["csv_col_name_labels"] = "traffic_sign"
    multiview_config["input_config"]["csv_col_name_images"] = [
        "compressed_rgb_l0_far",
        "compressed_rgb_l0_wide",
        "compressed_rgb_l1_wide",
    ]
    test_base_path = get_test_data_folder()
    detector_dataset_sample = os.path.join(test_base_path, "vision/datasets/pace_general_yuv")
    detector_image_json_base = os.path.join(test_base_path, "vision/datasets")

    return multiview_config, detector_dataset_sample, detector_image_json_base


@pytest.fixture(name="multiview_parquet_config")
def get_multiview_parquet_config(multiview_config: tuple[dict[str, Any], str, str]) -> tuple[dict[str, Any], str, str]:
    """Fixture to return multiview patch extractor configuration.

    Args:
        multiview_config: patch extractor configuration

    Returns:
        multiview_parquet_config: multiview patch extractor configuration
    """
    multiview_parquet_config, detector_dataset_sample, detector_image_json_base = multiview_config
    multiview_parquet_config["output_config"]["parquet"] = True

    return multiview_parquet_config, detector_dataset_sample, detector_image_json_base


@pytest.mark.parametrize(
    ("config_name", "expected_output"),
    [
        pytest.param("normal_config", {"train": 16, "val": 60, "test": 17, "tfrec_files": 9}, marks=pytest.mark.normal),
        pytest.param(
            "multiview_config", {"train": 2, "val": 11, "test": 13, "tfrec_files": 3}, marks=pytest.mark.multiview
        ),
        pytest.param(
            "multiview_parquet_config",
            {"train": 2, "val": 11, "test": 13},
            marks=pytest.mark.multiview_parquet,
        ),
    ],
)
@pytest.mark.lfs_dependencies(["%datadir%/vision/datasets"])
def test_classifier_patch_extractor(  # noqa: PLR0915
    request: pytest.FixtureRequest,
    config_name: str,
    expected_output: dict[str, int],
    tmpdir_delete_after: str,
) -> None:
    """Test script to test the classifier patch extractor function.

    Args:
        request: pytest fixture request object
        config_name: name of the config fixture
        expected_output: dictionary with expected output counts for train, val, and test
        tmpdir_delete_after: temporary path for the output folder
    """
    # GIVEN dataset for the TS detector, config for the patch extractor and output directory
    overall_config, detector_dataset_sample, detector_image_json_base = request.getfixturevalue(config_name)
    output_directory = os.path.join(tmpdir_delete_after, "output_directory")

    model_config, remaining_args = parse_model_config_arguments(
        [
            "--input_folder",
            detector_dataset_sample,
            "--detector_csv_label_base_path",
            detector_image_json_base,
            "--detector_csv_image_base_path",
            detector_image_json_base,
            "--output_folder",
            output_directory,
        ],
        model_configs,
    )
    environment, args, model_config = parse_cli_arguments(model_config.environments, remaining_args, model_config)

    # WHEN process_data is called to extract patches
    dataset_hashes_file = os.path.join(tmpdir_delete_after, "hash_file.txt")
    hash_key = "HASHKEY"
    logger = logging.getLogger(__name__)
    process_data(overall_config, str(dataset_hashes_file), hash_key, environment, logger)

    # THEN the output directory should contain the expected files
    classifier_training_lake = Path(
        os.path.join(output_directory, "classifier_data_lakes/training_lakes/", "test_dataset")
    )
    assert os.path.exists(str(classifier_training_lake))

    dataset_files = list(classifier_training_lake.glob("labels/*/filelists/*.dataset"))

    if config_name != "multiview_parquet_config":
        images = list(classifier_training_lake.glob("images/*/*/*/*.png"))
        labels = list(classifier_training_lake.glob("labels/*/label_lake/*/*/*.json"))
        tfrecord_files = list(classifier_training_lake.glob("tfrecords/*/*.tfrec"))
        assert len(images) == expected_output["train"] + expected_output["val"]
        assert len(labels) == expected_output["train"] + expected_output["val"]
        # image and label *.dataset file for each subset (train, val, test)
        assert len(dataset_files) == 6
        # tfrecord file for each subset (train, val, test)
        assert len(tfrecord_files) == expected_output["tfrec_files"]
        assert similar_dataset_exists(hash_key, str(dataset_hashes_file), logger)
        assert not similar_dataset_exists("HASHKEY_NOT_PRESENT", str(dataset_hashes_file), logger)

        # Check for split data into train and val
        img_train_dataset = next(filter(lambda x: "img_train.dataset" in str(x), dataset_files))
        img_val_dataset = next(filter(lambda x: "img_val.dataset" in str(x), dataset_files))
        img_test_dataset = next(filter(lambda x: "img_test.dataset" in str(x), dataset_files))
        gt_train_dataset = next(filter(lambda x: "gt_train.dataset" in str(x), dataset_files))
        gt_val_dataset = next(filter(lambda x: "gt_val.dataset" in str(x), dataset_files))
        gt_test_dataset = next(filter(lambda x: "gt_test.dataset" in str(x), dataset_files))

        with open(img_train_dataset, encoding="utf-8") as dataset_file:
            img_train = dataset_file.readlines()
        with open(img_val_dataset, encoding="utf-8") as dataset_file:
            img_val = dataset_file.readlines()
        with open(img_test_dataset, encoding="utf-8") as dataset_file:
            img_test = dataset_file.readlines()
        with open(gt_train_dataset, encoding="utf-8") as dataset_file:
            gt_train = dataset_file.readlines()
        with open(gt_val_dataset, encoding="utf-8") as dataset_file:
            gt_val = dataset_file.readlines()
        with open(gt_test_dataset, encoding="utf-8") as dataset_file:
            gt_test = dataset_file.readlines()
    else:
        parquet_files = list(classifier_training_lake.rglob("*/*.parquet"))
        # Convert Path objects to strings
        parquet_file_paths = [str(file) for file in parquet_files]
        assert len(parquet_files) == 3
        # read parquet files
        classifier_dataset = ClassifierParquetDataset()
        parquet_data_gen = classifier_dataset.load_parquet_to_data_batches(parquet_file_paths)
        parquet_data_list = list(parquet_data_gen)
        parquet_table = pa.Table.from_batches(parquet_data_list)

        parquet_schema = classifier_dataset.schema
        table_schema = parquet_table.schema
        partition_columns = classifier_dataset.partition_columns
        # Ensure table has the same schema as ClassifierParquetDataset
        # Partition columns are not included in the comparison as their type don't match after loading from the path
        assert len(parquet_schema) == len(table_schema)
        assert [field for field in parquet_schema if field.name not in partition_columns] == [
            field for field in table_schema if field.name not in partition_columns
        ]
        parquet_data = parquet_table.to_pandas()

        img_train = parquet_data[parquet_data["split"] == "train"]
        img_val = parquet_data[parquet_data["split"] == "val"]
        img_test = parquet_data[parquet_data["split"] == "test"]
        gt_train = img_train.copy()
        gt_val = img_val.copy()
        gt_test = img_test.copy()

    # Configured train/val split is not achieved because most samples in the test data are single track sequences, and
    # all single track samples gets accumulated in the val split.
    assert len(img_train) == expected_output["train"]
    assert len(gt_train) == expected_output["train"]
    assert len(img_val) == expected_output["val"]
    assert len(gt_val) == expected_output["val"]
    assert len(img_test) == expected_output["test"]
    assert len(gt_test) == expected_output["test"]
