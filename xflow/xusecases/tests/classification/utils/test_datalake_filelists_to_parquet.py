"""This module contains tests for converting datalake file lists to Parquet files."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import glob
import json
import os

import pandas as pd
import pytest
import yaml

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from xtension.dataset_provider.dataset_loader import DefaultDatasetLoader
from xtension.dataset_provider.dataset_path import CV3TSDatasetPath
from xusecases.classification.common.parameters import Environment
from xusecases.classification.data.traffic_sign_dataset_provider import dataset_provider_from_yaml
from xusecases.classification.utils.encode_to_parquet.datalake_to_parquet import convert_datalake_to_parquet
from xusecases.helpers import get_test_data_folder

# Params
BATCH_SIZE = 4

# (height, width, channels)
INPUT_IMAGE_SHAPE = Environment.input_image_shape
NETWORK_INPUT_SHAPE = (Environment.input_size, Environment.input_size, 3)


def _create_sample_metadata(tmp_path: str) -> None:
    """Create a dummy metadata, which is parsable.

    Args:
        tmp_path: pytest temporary directory path.
    """

    metadata = {
        "schema": "pace/label_set/label_set_3_0_0.schema.json",
        "name": "labels/2022_06_16_12_20_06",
        "version": "1.0.0",
        "labels": [
            {"name": "traffic_sign", "id": 0, "color": [255, 150, 0], "predicted": True},
        ],
    }

    metadata_dir = os.path.join(tmp_path, "metadata")
    os.makedirs(metadata_dir, exist_ok=True)
    metadata_path = os.path.join(metadata_dir, "label_set_1.0.0.json")
    with open(metadata_path, "w") as metadata_json:
        json.dump(metadata, metadata_json)


@pytest.fixture(name="sample_datasets")
def fixture_sample_datasets(tmpdir_delete_after: str) -> str:
    """Creates the sample dataset filelists and TS metadata.

    Args:
        tmpdir_delete_after: pytest temporary directory path.

    Returns:
        The folder path containing the dataset files.
    """
    base_path = get_test_data_folder()
    data_paths = {
        "train_img_filepath": [
            os.path.join(base_path, "classification/class_id/images/frame_000.png"),
            os.path.join(base_path, "classification/class_id/images/frame_001.png"),
            os.path.join(base_path, "classification/class_id/images/frame_002.png"),
            os.path.join(base_path, "classification/class_id/images/frame_003.png"),
        ],
        "train_gt_filepath": [
            os.path.join(base_path, "classification/class_id/gt/frame_000.json"),
            os.path.join(base_path, "classification/class_id/gt/frame_001.json"),
            os.path.join(base_path, "classification/class_id/gt/frame_002.json"),
            os.path.join(base_path, "classification/class_id/gt/frame_003.json"),
        ],
        "val_img_filepath": [
            os.path.join(base_path, "classification/class_id/images/frame_006.png"),
            os.path.join(base_path, "classification/class_id/images/frame_007.png"),
            os.path.join(base_path, "classification/class_id/images/frame_008.png"),
            os.path.join(base_path, "classification/class_id/images/frame_005.png"),
        ],
        "val_gt_filepath": [
            os.path.join(base_path, "classification/class_id/gt/frame_006.json"),
            os.path.join(base_path, "classification/class_id/gt/frame_007.json"),
            os.path.join(base_path, "classification/class_id/gt/frame_008.json"),
            os.path.join(base_path, "classification/class_id/gt/frame_005.json"),
        ],
    }

    # Create temporary train and validation dataset files
    filelists_dir = os.path.join(tmpdir_delete_after, "filelists")
    os.makedirs(filelists_dir, exist_ok=True)
    for key, file_name in [
        ("train_img_filepath", "img_train.dataset"),
        ("val_img_filepath", "img_val.dataset"),
        ("train_gt_filepath", "gt_train.dataset"),
        ("val_gt_filepath", "gt_val.dataset"),
    ]:
        file_path = os.path.join(filelists_dir, file_name)
        with open(file_path, "w", encoding="utf-8") as file:
            file.writelines("\n".join(data_paths[key]))

    _create_sample_metadata(tmpdir_delete_after)

    return tmpdir_delete_after


@pytest.fixture(name="parquet_dataset_file")
def fixture_parquet_dataset_file(tmp_path: str) -> str:
    """Create a dummy parquet dataset.

    Args:
        tmp_path: pytest temporary directory path.

    Returns:
        Path to the parquet dataset.
    """

    parquet_dataset_file = os.path.join(tmp_path, "parquet.dataset")
    with open(parquet_dataset_file, "w") as tf_dataset:
        tf_dataset.writelines(".")

    return parquet_dataset_file


@pytest.fixture(name="dataset_provider_config")
def fixutre_dataset_provider_config(tmp_path: str) -> str:
    """Creates a dummy configuration, which is correct in terms of configuration parsing.

    Args:
        tmp_path: pytest temporary directory path.
    """
    path_config = os.path.join(tmp_path, "dataset_provider.yml")

    dummy_config = {
        "name": "dummy dataset provider",
        "version": "1.0.0",
        "input_dataset": {"type": "rgb", "dataset_path": "img_*.dataset"},
        "label_datasets": [
            {"type": "gt", "dataset_path": "gt_*.dataset", "label_set": "", "label_set_version": "1.0.0"}
        ],
        "data_collections": {"train": [{"name": "", "export_date": ""}], "val": [{"name": "", "export_date": ""}]},
    }

    with open(path_config, "w") as outfile:
        yaml.dump(dummy_config, outfile)

    return path_config


@pytest.mark.lfs_dependencies(["%datadir%/classification/class_id"])
def test_create_datalake_to_parquet(
    sample_datasets: str, dataset_provider_config: str, parquet_dataset_file: str
) -> None:
    """Test to verify the creation of Parquet files and their content.

    Args:
        sample_datasets: Folder containing the datasets.
        dataset_provider_config: Path to the dataset config file.
        parquet_dataset_file: Parquet dataset file path.
    """
    # GIVEN a set of valid parameters
    dataset_loader = DefaultDatasetLoader(CV3TSDatasetPath(sample_datasets))
    dataset_provider = dataset_provider_from_yaml(dataset_provider_config, dataset_loader)
    parquet_output_folder = os.path.join(sample_datasets, "parquet_output")

    # WHEN converting the datalake files to Parquet format and saving them to the output folder
    convert_datalake_to_parquet(dataset_provider, parquet_output_folder, str(sample_datasets), "DE")

    # THEN the generated Parquet files should exist
    assert os.path.exists(parquet_output_folder)
    # Collect the generated parquet files.
    parquet_files = glob.glob(os.path.join(parquet_output_folder, "*/*.parquet"))
    # Read parquet into a dataframe table
    parquet_dataframe = pd.read_parquet(parquet_output_folder)

    # THEN the files generated are equal to the number of countries (=2) in the table.
    assert len(parquet_files) == 2
    # THEN the dataframe should contain the expected number of samples.
    assert len(parquet_dataframe) == 8

    # THEN the dataframe should contain the correct split information.
    assert len(parquet_dataframe[parquet_dataframe["split"] == "val"]) == 4
    assert len(parquet_dataframe[parquet_dataframe["split"] == "train"]) == 4

    parquet_schema_column_names = ClassifierParquetDataset().column_names
    # THEN the dataframe columns should match the schema columns.
    assert set(parquet_dataframe.columns) == set(parquet_schema_column_names)
