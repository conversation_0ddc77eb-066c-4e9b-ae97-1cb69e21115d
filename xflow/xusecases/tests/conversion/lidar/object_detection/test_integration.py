"""Integration test for the object detection model."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import glob
import os
from pathlib import Path
from unittest.mock import patch

import pytest

import xcommon.logging.logging

xcommon.logging.logging.DEFAULT_RAISE_ON_AUTOGRAPH_WARNING = False

import xcommon.logging  # noqa: E402
from xcommon.training.combined_metrics.task_metrics_loader import TaskMetricsLoader  # noqa: E402
from xusecases.helpers import run_in_subprocess  # noqa: E402
from xusecases.lidar.common.azure_pipeline.helpers import list_available_models  # noqa: E402
from xusecases.lidar.common.azure_pipeline.output_folders_factory import (  # noqa: E402
    ConvertStageOutput,
    EvaluateStageOutput,
    SelectStageOutput,
    Stage,
    TrainStageOutput,
    get_stage_output,
)
from xusecases.lidar.common.utils.string_utils import filter_list_regex  # noqa: E402
from xusecases.lidar.tasks.object_detection.definitions import TASK_ID  # noqa: E402

# NOTE:
# During training tensorflow grabs most of the GPU memory. Therefore we can't simply run the training
# followed by the conversion and evaluation, as those would have no memory to work with.
# Our solution is to run the steps in separate processes. When one process finishes all memory is freed
# and we can perform the next step.
# This has one caviat: All imports related to tensorflow (even indirect ones) have to be contained within
# the test functions.
# ----->

MODEL_NAME = "object_detection"  # specified in the object detection parameters as "conversion_model_name"
COMBINED_METRIC_COMPONENTS = (
    "all.LargeVehicle.pr_curves.auc@0.7000000",
    "all.PassengerCar.pr_curves.auc@0.7000000",
    "all.RidableVehicle.pr_curves.auc@0.5000000",
    "all.Pedestrian.pr_curves.auc@0.5000000",
)


def _check_combined_metric_components(evaluation_folder: str) -> None:
    """Assert that combined metric is composed of expected subcomponents."""
    subfolders = [element.path for element in os.scandir(evaluation_folder) if element.is_dir()]
    iteration_folder = os.path.basename(subfolders[-1])  # take last (online) or only (offline) iteration folder
    iteration = int(iteration_folder.split("_")[-1])
    task_metrics_loader = TaskMetricsLoader(evaluation_folder, TASK_ID)
    task_metrics_loader.update_iteration(iteration)
    for component in COMBINED_METRIC_COMPONENTS:
        _ = task_metrics_loader.get_value(metric_name=component, additional_args={})  # check if metric can be loaded


def _check_summary_artifacts(summaries_folder: str) -> None:
    assert len(glob.glob(os.path.join(summaries_folder, "outputs/**/*.png"), recursive=True)) > 0
    assert len(glob.glob(os.path.join(summaries_folder, "tensorboard/events.out.tfevents.*"))) > 0


@run_in_subprocess()
def __test_training(common_args: list[str], output_folder: str) -> None:
    from xusecases.lidar.models.object_detection import train  # noqa: PLC0415

    num_epochs = 2
    stage_args = common_args + [
        "--input_folder",
        output_folder,
        "--output_folder",
        output_folder,
        "--num_samples_per_epoch",
        "200",
        "--num_epochs",
        str(num_epochs),
    ]

    # TODO: remove this enforcement of the local runtime as soon as the summary logger no longer ignores the received  # noqa: E501, TD003
    # output directory!
    with patch("xtension.summaries.summary_logging.azure.get_azure_run", return_value=None):
        train.main(stage_args)

    # ========== Check training artifacts ===========
    stage_output = get_stage_output(stage=Stage.TRAIN, output_base_folder=output_folder)
    assert isinstance(stage_output, TrainStageOutput)

    assert num_epochs == len(os.listdir(stage_output.checkpoints))


@run_in_subprocess()
def __test_selection(common_args: list[str], input_folder: str, output_folder: str) -> None:
    from xusecases.lidar.models.object_detection import select  # noqa: PLC0415

    num_ckpt_to_eval = 1
    stage_args = common_args + [
        "--input_folder",
        input_folder,
        "--output_folder",
        output_folder,
        "--num_checkpoints_to_evaluate",
        str(num_ckpt_to_eval),
    ]

    # TODO: remove this enforcement of the local runtime as soon as the summary logger no longer ignores the received  # noqa: E501, TD003
    # output directory!
    with patch("xtension.summaries.summary_logging.azure.get_azure_run", return_value=None):
        select.main(stage_args)

    # ========== Check selection artifacts ===========
    stage_output = get_stage_output(stage=Stage.SELECT, output_base_folder=output_folder)
    assert isinstance(stage_output, SelectStageOutput)

    assert num_ckpt_to_eval == len(os.listdir(stage_output.evaluation))
    assert num_ckpt_to_eval == len(os.listdir(os.path.join(stage_output.summaries, "outputs")))
    _check_summary_artifacts(stage_output.summaries)
    _check_combined_metric_components(stage_output.evaluation)


@run_in_subprocess()
def __test_evaluation(common_args: list[str], input_folder: str, output_folder: str, model_type: str) -> None:
    from xusecases.lidar.models.object_detection import evaluate  # noqa: PLC0415

    stage_args = common_args + [
        "--input_folder",
        input_folder,
        "--output_folder",
        output_folder,
        "--model_type",
        model_type,
    ]

    # TODO: remove this enforcement of the local runtime as soon as the summary logger no longer ignores the received  # noqa: E501, TD003
    # output directory!
    with patch("xtension.summaries.summary_logging.azure.get_azure_run", return_value=None):
        evaluate.main(stage_args)

    # ========== Check evaluation artifacts ===========

    stage = Stage.EVALUATE if model_type == "tensorflow" else Stage.EVALUATE_ONNX
    stage_output = get_stage_output(stage=stage, output_base_folder=output_folder)
    assert isinstance(stage_output, EvaluateStageOutput)

    _check_summary_artifacts(stage_output.summaries)

    assert len(os.listdir(stage_output.evaluation)) >= 1  # Evaluate at least on one dataset

    evaluation_dataset_subfolders = [f.path for f in os.scandir(stage_output.evaluation) if f.is_dir()]
    for evaluation_folder in evaluation_dataset_subfolders:
        _check_combined_metric_components(evaluation_folder)
        composed_json_path = Path(evaluation_folder) / "bb3d_attribute_std_evaluation_all.json"
        assert composed_json_path.is_file()
        assert composed_json_path.stat().st_size > 0


@run_in_subprocess()
def __test_conversion(common_args: list[str], model_config: str, input_folder: str, output_folder: str) -> None:
    from xusecases.lidar.models.object_detection import convert  # noqa: PLC0415

    stage_args = common_args + [
        "--input_folder",
        input_folder,
        "--output_folder",
        output_folder,
        "--num_samples_qnn_evaluation",
        "1",
    ]

    convert.main(stage_args)

    # ========== Check conversion artifacts ===========

    stage_output = get_stage_output(stage=Stage.CONVERT, output_base_folder=output_folder)
    assert isinstance(stage_output, ConvertStageOutput)


@run_in_subprocess()
def __test_publish(common_args: list[str], input_folders: list[str], output_folder: str) -> None:
    from xusecases.lidar.models.object_detection import publish  # noqa: PLC0415

    stage_args = common_args + [
        "--input_folder",
        *input_folders,
        "--publish_folder",
        output_folder,
    ]

    publish.main(stage_args)

    # ========== Check publish artifacts ===========

    # check expected stage folders have been copied to publish stage
    stage_output_train = get_stage_output(stage=Stage.TRAIN, output_base_folder=output_folder)
    assert isinstance(stage_output_train, TrainStageOutput)
    assert os.path.isdir(stage_output_train.checkpoints)

    stage_output_select = get_stage_output(stage=Stage.SELECT, output_base_folder=output_folder)
    assert isinstance(stage_output_select, SelectStageOutput)
    assert os.path.isdir(stage_output_select.checkpoints)
    assert os.path.isdir(stage_output_select.evaluation)

    stage_output_evaluate = get_stage_output(stage=Stage.EVALUATE, output_base_folder=output_folder)
    assert isinstance(stage_output_evaluate, EvaluateStageOutput)
    assert os.path.isdir(stage_output_evaluate.evaluation)

    stage_output_convert = get_stage_output(stage=Stage.CONVERT, output_base_folder=output_folder)
    assert isinstance(stage_output_convert, ConvertStageOutput)
    assert os.path.isdir(stage_output_convert.conversion)
    # TODO: enable this check later when we evaluate onnx runtime  # noqa: TD003
    # assert os.path.isdir(stage_output_convert.evaluation)


def _list_models_to_test() -> list[str]:
    return filter_list_regex(
        list_available_models(filter_active_integration_test=True)["object_detection"],
        ignore_list=[".*cct.*"],
    )


@pytest.mark.parametrize("model_config", _list_models_to_test())
@pytest.mark.lfs_dependencies(["%datadir%/lidar/cache", "%datadir%/lidar/traininglake/datasets/mdm_local_storage"])
def test_integration(model_config: str, tmpdir_delete_after: str) -> None:
    """Integration test for Lidar ObjectDetection experiment."""

    # common arguments used by all stages
    common_test_args = ["--model_config", model_config]

    # The integration tests captures a full CT run consisting of the stages listed below. Since the stages depend
    # on outputs of previous stages they cannot be executed individually
    folder_train = os.path.join(tmpdir_delete_after, "train")
    folder_select = os.path.join(tmpdir_delete_after, "select")
    folder_evaluate = os.path.join(tmpdir_delete_after, "evaluate")
    folder_evaluate_onnx = os.path.join(tmpdir_delete_after, "evaluate_onnx")
    folder_convert = os.path.join(tmpdir_delete_after, "convert")
    folder_publish = os.path.join(tmpdir_delete_after, "publish")

    __test_training(common_test_args, output_folder=folder_train)
    __test_selection(common_test_args, input_folder=folder_train, output_folder=folder_select)
    __test_evaluation(
        common_test_args, input_folder=folder_select, output_folder=folder_evaluate, model_type="tensorflow"
    )
    __test_conversion(common_test_args, model_config, input_folder=folder_select, output_folder=folder_convert)
    if "pretraining" in model_config:
        __test_evaluation(
            common_test_args, input_folder=folder_convert, output_folder=folder_evaluate_onnx, model_type="onnx"
        )
    __test_publish(
        common_test_args,
        input_folders=(folder_train, folder_select, folder_evaluate, folder_convert),
        output_folder=folder_publish,
    )
