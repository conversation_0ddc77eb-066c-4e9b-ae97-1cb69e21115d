"""Dataset loader functions for classification use case."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON>sch GmbH. All rights reserved.
 Copyright (c) 2021-2022 <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
from collections.abc import Callable
from typing import Any

import numpy as np
import tensorflow as tf

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset


def load_dataset(
    image_dataset_file: str,
    label_dataset_file: str,
    image_loader_func: Callable[[str], tf.Tensor],
    label_loader_func: Callable[[str], tf.Tensor],
    num_parallel_calls: int = 2,
) -> tuple[dict[str, Any], tf.data.Dataset[Any]]:
    """Loads an image and corresponding ground truth .dataset file based on passed loader functions.

    Args:
        image_dataset_file: Path to the .dataset file of the image dataset.
        label_dataset_file: Path to the .dataset file of the ground truth dataset.
        image_loader_func: Function used to load the image file.
        label_loader_func: Function used to load the ground truth file.
        num_parallel_calls: Number of parallel calls used to load data. Defaults to None (automatic).

    Returns:
        Dataset info and tf.Dataset(zips the loaded image and the ground truth dataset).
    """
    image_dataset = tf.data.TextLineDataset(image_dataset_file).map(
        image_loader_func, num_parallel_calls=num_parallel_calls
    )
    label_dataset = tf.data.TextLineDataset(label_dataset_file).map(
        label_loader_func, num_parallel_calls=num_parallel_calls
    )

    dataset = tf.data.Dataset.zip((image_dataset, label_dataset))

    dataset_info = get_dataset_info(image_dataset_file)

    return (dataset_info, dataset)


def get_dataset_info(image_dataset: str) -> dict[str, Any]:
    """Compiles dataset info for input data.

    Args:
        image_dataset: Image dataset file path.

    Returns:
        Dataset info e.g. number of samples, number of classes
    """
    dataset_info = {}

    with open(image_dataset) as file:
        num_samples = len(file.readlines())
    dataset_info.update({"num_samples": num_samples})

    return dataset_info


def load_rgb_image(path: str) -> tf.Tensor:
    """Load an RGB image file as a Tensor.

    Args:
        path: Path to the image file.

    Returns:
        The loaded image as a tf.Tensor in the specified data_format.
    """

    image = tf.io.read_file(path)
    image = tf.image.decode_png(image, channels=3)
    image = _channels_last_to_channels_first(image)
    image = tf.cast(image, dtype=tf.float32)
    return image


def decode_bytes_to_image(data: tf.Tensor) -> tf.Tensor:
    """Decode the image from the PNG encoded bytes.

    Args:
        data: PNG encoded image bytes.

    Returns:
        Decoded image data.
    """
    image = tf.io.decode_image(data, channels=3, expand_animations=False)
    return image


def parse_from_parquet_fn(data: dict[str, Any]) -> dict[str, Any]:
    """Load the parquet file.

    Args:
        data: Data to be loaded.

    Returns:
        Parsed image and label data.
    """

    rgb = decode_bytes_to_image(data[ClassifierParquetDataset.TrainColumns.IMAGE_DATA.value])
    label = data[ClassifierParquetDataset.TrainColumns.CLASS_NAME.value]
    object_id = data[ClassifierParquetDataset.TrainColumns.OBJECT_ID.value]

    label_dict = {"rgb": rgb, "label": label, "object_id": object_id}
    return label_dict


def look_up_class_id_from_name(class_name: tf.Tensor, name_to_id: tf.lookup) -> tf.Tensor:
    """Look up the class id from a mapping table of class names and class ids.

    Args:
        class_name: Class name.
        name_to_id: tensorflow hash table to map the class name to class id.

    Returns:
        Parsed data.
    """

    def look_up_class_id(class_name: tf.Tensor, name_to_id: tf.lookup) -> tf.Tensor:
        """Look up the class id.

        Args:
            class_name: Class name to be loaded.
            name_to_id: Dictionary to map the class name to class id.

        Returns:
            Class id.
        """
        return name_to_id.lookup(class_name)

    return look_up_class_id(class_name, name_to_id)


def load_tf_record_fn(example: dict[str, Any]) -> dict[str, Any]:
    """Parses the record files as per the feature description.

    Args:
        example: sample to be parsed

    Returns:
        Parsed data.
    """
    feature_description = {
        "image": tf.io.FixedLenFeature([], tf.string),
        "path": tf.io.FixedLenFeature([], tf.string),
        "classifier_label_id": tf.io.FixedLenFeature([], tf.int64),
        "hierarchy_class": tf.io.VarLenFeature(tf.string),
        "shape": tf.io.FixedLenFeature([], tf.string),
        "trafficSignClass": tf.io.FixedLenFeature([], tf.string),
    }
    parsed_data = tf.io.parse_single_example(example, feature_description)
    return parsed_data


def _channels_last_to_channels_first(tensor: tf.Tensor) -> tf.Tensor:
    """Converts the tf.Tensor of rank 3 from 'channels_last' to 'channels_first' data_format.

    Args:
        tensor: Tensor with rank 3 in 'channels_last' data_format.

    Returns:
        Equivalent Tensor in 'channels_first' data_format.
    """
    return tf.transpose(tensor, [2, 0, 1])


def load_traffic_sign_class_id_gt(input_path: str) -> tf.Tensor:
    """Loads the traffic sign class from a json file.

    Args:
        input_path: Path to the ground truth file.

    Returns:
        Class ID as a tf.tensor.
    """

    # Load and parse the JLF json file
    def load_json(file_name: str) -> np.int32:
        """Parse json label data.

        Args:
            file_name: Path to the json file.

        Returns:
            Classifier label id.
        """
        json_string = tf.io.read_file(file_name).numpy()
        json_data_dict = json.loads(json_string)

        return np.int32(json_data_dict["classifier_label_id"])

    return tf.numpy_function(load_json, [input_path], tf.int32, stateful=False, name="load_traffic_sign_class_id_gt")


def load_traffic_sign_class_name_gt(input_path: str) -> tf.Tensor:
    """Loads class name from a json file.

    Args:
        input_path: Path to the ground truth file.

    Returns:
        Class Name as a tf.tensor.
    """

    # Load and parse the JLF json file
    def load_json(file_name: str) -> str:
        """Parse json label data.

        Args:
            file_name: Path to the json file.

        Returns:
            Traffic sign class name appended with hierarchy class.
        """
        json_string = tf.io.read_file(file_name).numpy()
        json_data_dict = json.loads(json_string)

        return "_".join([json_data_dict["trafficSignClass"], json_data_dict["hierarchy_class"]])

    return tf.numpy_function(load_json, [input_path], tf.string, stateful=False, name="load_traffic_sign_class_name_gt")


def load_traffic_sign_is_ts_gt(input_path: str) -> tf.Tensor:
    """Loads the 'is_ts' from a json file, which tells respective patch is a background or traffic sign.

    Args:
        input_path: Path to the ground truth file.

    Returns:
        0(background) or 1(foreground) as a tf.tensor.
    """

    # Load and parse the JLF json file
    def load_json(file_name: str) -> np.int32:
        """Parse json files.

        Args:
            file_name: File path.

        Returns:
            Integer value containing 0 or 1 flag indicating is it background.
        """
        json_string = tf.io.read_file(file_name).numpy()
        json_data_dict = json.loads(json_string)

        is_background = int(json_data_dict["classifier_label_id"]) == 0
        is_ts = np.int32(not is_background)

        return is_ts

    return tf.numpy_function(load_json, [input_path], tf.int32, stateful=False, name="load_traffic_sign_is_ts_gt")
