"""This file contains traffic sign classifier dataset factory functions."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert Bosch GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert <PERSON> GmbH. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import os
import random
from collections.abc import Callable, Generator
from pathlib import Path
from typing import Any

import pyarrow.dataset as ds
import tensorflow as tf
from tensorflow.keras.layers import CenterCrop
from ts_label_mapping.classifier import ClassMappings

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from xcontract.data.definitions.usage import Usage, ValueKey
from xtension.dataset_provider.dataset_loader import DefaultDatasetLoader
from xtension.dataset_provider.dataset_path import CV3TSDatasetPath
from xtension.dataset_provider.dataset_provider import DatasetProvider
from xtensorflow import data_formats
from xtensorflow.random import zip_with_seed
from xusecases.classification.common.definitions import DatasetMethod
from xusecases.classification.data.augmentation.augmentations import AugmentationsCallable, image_augmentations
from xusecases.classification.data.dataset_wrapper import (
    InputDataLoader,
    LabelDataLoader,
    ParquetDataLoader,
    TFRecordsLoader,
)
from xusecases.classification.data.loaders import (
    load_rgb_image,
    load_tf_record_fn,
    load_traffic_sign_class_id_gt,
    load_traffic_sign_class_name_gt,
    load_traffic_sign_is_ts_gt,
    look_up_class_id_from_name,
    parse_from_parquet_fn,
)
from xusecases.classification.data.traffic_sign_dataset_provider import dataset_provider_from_yaml


class TrafficSignPatchDatasetFactory:
    """Factory that creates RGB + traffic sign datasets to access a training lake."""

    def __init__(
        self,
        dataset_dict: dict[str, str],
        is_contrastive: bool,  # noqa: FBT001
        batch_size: int,
        input_image_shape: tuple[int, int, int],
        overall_augmentation_chance: float,
        dataset_method: DatasetMethod,
        shuffle: bool = True,  # noqa: FBT001, FBT002
        seed: int = 123,
        calc_class_distribution: bool = False,  # noqa: FBT001, FBT002
    ) -> None:
        """Initialize dataset factory.

        Args:
            dataset_dict: Path of dataset files
            is_contrastive: Boolean to indicate use of contrastive loss.
            batch_size: Batch size
            input_image_shape: Shape of the input data
            overall_augmentation_chance: Probable occurance of augmentation.
            dataset_method: Dataset load methods [FILELISTS, DATALAKE]
            shuffle: Flag to indicate data shuffle.
            seed: Seed value for random function.
            calc_class_distribution: Analyse dataset and build up class distribution list.

        """

        self.is_contrastive = is_contrastive
        self.batch_size = batch_size
        self.input_image_shape = input_image_shape
        self.overall_augmentation_chance = overall_augmentation_chance
        self.shuffle = shuffle
        self.seed = seed
        self.name_to_id = ClassMappings().get_name_to_id_mapping()
        self.pyarrow_to_tensorflow = {
            "bool": tf.bool,
            "int": tf.int32,
            "float": tf.float32,
            "list<item: uint8>": tf.uint8,
            "string": tf.string,
        }
        self.data_loaders = {
            "rgb": InputDataLoader(load_rgb_image),
            "gt": LabelDataLoader(
                load_traffic_sign_class_id_gt, load_traffic_sign_class_name_gt, load_traffic_sign_is_ts_gt
            ),
        }
        self.usage_to_subset = {
            Usage.TRAINING: "train",
            Usage.VALIDATION: "val",
            Usage.TEST: "test",
            Usage.CALIBRATION: "val",
        }
        self.tf_record_loaders = TFRecordsLoader(load_tf_record_fn)
        self.parse_from_parquet = ParquetDataLoader(parse_from_parquet_fn, self.name_to_id, look_up_class_id_from_name)

        self.dataset_method = dataset_method

        if self.dataset_method == DatasetMethod.FILELISTS:
            self.train_datasets = {"rgb": dataset_dict["train_img_filepath"], "gt": dataset_dict["train_gt_filepath"]}

            self.val_datasets = {"rgb": dataset_dict["val_img_filepath"], "gt": dataset_dict["val_gt_filepath"]}

        if self.dataset_method == DatasetMethod.DATALAKE:
            self.train_datasets = {}
            self.val_datasets = {}

            self.dataset_provider = build_dataset_provider(
                dataset_dict["data_lake_yml_path"], dataset_dict["data_lake_path"]
            )

        if self.dataset_method == DatasetMethod.TFRECORD:
            self.record_files = self._collect_tfrecord_files(
                dataset_dict["tf_record_list_file_dir_path"], dataset_dict["tf_record_list_file_name"]
            )
        if self.dataset_method == DatasetMethod.PARQUET:
            self.parquet_files = self._collect_parquet_paths(
                dataset_dict["tf_record_list_file_dir_path"], dataset_dict["tf_record_list_file_name"]
            )

        self.dataset_info = {
            "num_samples": -1,  # total number of samples in dataset
            "max_block_size": 1,  # maximum block size, e.g. size of biggest tfrecord file
        }
        self.calc_class_distribution = calc_class_distribution
        self.training_class_distribution: list[int] = []

    def get_class_distribution(self, dataset: tf.data.Dataset[Any]) -> list[int]:
        """Scans the dataset and counts the number of samples per class.

        Args:
            dataset: Tensorflow dataset.

        Returns:
            List of sample number per class, index is class id.
        """

        dataset_size = self.dataset_info["num_samples"]

        print("Check dataset...", end="")

        class_id_list = []
        dataset_iter = iter(dataset)
        max_class_id = 0
        for _ in range(dataset_size):
            data = next(dataset_iter)
            class_id = int(data["gt"][ValueKey.DATA]["class_id"])
            class_id_list.append(class_id)
            max_class_id = max(class_id, max_class_id)

        samples_per_class = [0] * (max_class_id + 1)
        for c in set(class_id_list):
            samples_per_class[c] = class_id_list.count(c)

        print(f"{sum(samples_per_class)} samples read.")

        return samples_per_class

    def create_train_dataset(self, input_shape: tuple[int, int, int]) -> tuple[tf.data.Dataset[Any], int]:
        """Creates an instance of `tf.data.Dataset` for training.

        The dataset is loaded and the class distribution is calculated.
        The samples are then shuffled, augmented, and cropped to match the input size of the classifier.

        Args:
            input_shape: Shape to which image has to be cropped (height, width, channels).

        Returns:
            tf.data.Dataset finalized classification dataset for training and the number of samples.
        """

        # create dataset
        num_parallel_calls = len(self.record_files) if self.dataset_method == DatasetMethod.TFRECORD else 2
        dataset = self.create_dataset(Usage.TRAINING, num_parallel_calls)

        # count sample number per class
        if self.calc_class_distribution:
            self.training_class_distribution = self.get_class_distribution(dataset)

        ########################
        # Dataset transformation
        ########################
        augmentations = image_augmentations(
            self.is_contrastive, self.input_image_shape, self.overall_augmentation_chance
        )

        # infinite dataset
        dataset = dataset.repeat(-1)

        # set shuffle buffer
        if self.shuffle:
            shuffle_buffer_size = max(self.dataset_info["max_block_size"], self.batch_size) * 2
            dataset.shuffle(shuffle_buffer_size)

        # Create mapping functions
        if self.is_contrastive:
            dataset_map_fn = self.__contrastive_aug_mapping_fn(augmentations)
        else:
            dataset_map_fn = self.__crossentropy_aug_mapping_fn(augmentations)

        # Apply augmentations now!
        dataset = dataset.apply(zip_with_seed(seed=10)).map(
            dataset_map_fn, num_parallel_calls=tf.data.experimental.AUTOTUNE
        )

        # Basic Dataset Transformations
        dataset = dataset.batch(self.batch_size)
        # Center Crop
        crop_fn = self.__centercrop_fn(output_resolution=input_shape[0], is_contrastive=self.is_contrastive)
        dataset = dataset.map(crop_fn, num_parallel_calls=tf.data.experimental.AUTOTUNE)
        dataset = dataset.prefetch(tf.data.AUTOTUNE)

        return dataset, self.dataset_info["num_samples"]

    def create_val_dataset(self, input_shape: tuple[int, int, int]) -> tuple[tf.data.Dataset[Any], int]:
        """Creates an instance of `tf.data.Dataset` for validation dataset.

        Args:
            input_shape: Shape to which image has to be cropped (height, width, channels).

        Returns:
            tf.data.Dataset finalized classification dataset for validation.
        """
        dataset = self.create_dataset(Usage.VALIDATION, 4)

        ##############################
        # Basic Dataset transformation
        ##############################
        dataset = dataset.batch(self.batch_size, drop_remainder=True)
        # Center Crop
        crop_fn = self.__centercrop_fn(output_resolution=input_shape[0], is_contrastive=self.is_contrastive)
        dataset = dataset.map(crop_fn, num_parallel_calls=tf.data.experimental.AUTOTUNE)
        dataset = dataset.prefetch(buffer_size=2)

        return dataset, self.dataset_info["num_samples"]

    def create_dataset(self, usage: Usage, num_parallel_calls: int) -> tf.data.Dataset[Any]:
        """Creates an instance of `tf.data.Dataset` that yields data in the TSR dataset format.

        Args:
            usage: Whether a training, validation, or test dataset should be returned.
            num_parallel_calls: Number of threads to be used for loading data.

        Returns:
            tf.data.Dataset with elements organized as Dict[str, Dict[ValueKey, Union[tf.Tensor, Any]]].
        """

        if self.dataset_method == DatasetMethod.TFRECORD:
            len_data, len_max, dataset = self._dataset_from_tfrecord_files(
                usage, self.usage_to_subset[usage], num_parallel_calls=tf.data.AUTOTUNE
            )
            self.dataset_info["num_samples"] = len_data
            self.dataset_info["max_block_size"] = len_max
        elif self.dataset_method == DatasetMethod.PARQUET:
            len_data, dataset = self._dataset_from_parquet_files(usage)
            self.dataset_info["num_samples"] = len_data
        else:
            if self.dataset_method == DatasetMethod.FILELISTS:
                if usage == Usage.TRAINING:
                    datasets = self.train_datasets
                else:
                    datasets = self.val_datasets

                data_paths = load_dataset(datasets)

            if self.dataset_method == DatasetMethod.DATALAKE:
                data_paths, _ = self.dataset_provider.collect_data(self.usage_to_subset[usage])

            self.dataset_info["num_samples"] = len(data_paths["rgb"])

            # Shuffle whole dataset once.
            if self.shuffle:
                data_paths_zipped = list(zip(data_paths["rgb"], data_paths["gt"]))
                random.shuffle(data_paths_zipped)
                rgb_files, gt_files = zip(*data_paths_zipped)
                data_paths["rgb"] = list(rgb_files)
                data_paths["gt"] = list(gt_files)

            dataset = self._dataset_from_path_lists(data_paths, usage, num_parallel_calls)

        if usage == Usage.CALIBRATION:
            # Center Crop, dimensions of calibration images should match the input shape of the model.
            crop_fn = self.__centercrop_fn(
                output_resolution=self.input_image_shape[0], is_contrastive=self.is_contrastive
            )
            dataset = dataset.map(crop_fn, num_parallel_calls=tf.data.experimental.AUTOTUNE)

        return dataset

    def _dataset_from_path_lists(
        self, data_path_lists: dict[str, list[str]], usage: Usage, num_parallel_calls: int
    ) -> tf.data.Dataset[Any]:
        """Creates a `tf.data.Dataset` from the lists of provided filepaths.

        .. note::

            For the TRAINING usage the data is always shuffled.

        Args:
            data_path_lists: Lists of paths of different data types.
            usage: For what usage is the dataset (for validation/test the actual label data is not loaded, training is
                always shuffled).
            num_parallel_calls: Number of threads to be used for loading data.

        Returns:
            tf.data.Dataset that is ready to be used in the TSR setting.
        """

        def load_data_fun(filepaths: dict[str, str | tf.Tensor]) -> dict[str, dict[ValueKey, Any]]:
            """Loads image and ground truth data from the given file paths.

            Args:
                filepaths: Dictionary containing the type of file as key and actual path of the file to be loaded.

            Returns:
                Data loaded from the file.
            """
            return self._load_data(filepaths, usage)

        tensor_data_path_lists = {k: tf.constant(v) for k, v in data_path_lists.items()}
        dataset = tf.data.Dataset.from_tensor_slices(tensor_data_path_lists).map(  # type: ignore[arg-type]
            load_data_fun, num_parallel_calls=num_parallel_calls
        )

        return dataset

    def _dataset_from_parquet_files(self, usage: Usage) -> tuple[int, tf.data.Dataset[Any]]:
        """Create tf dataset from parquet files.

        Args:
            usage: Dataset type (ex: train, val).

        Returns:
            Tuple of total sample counts in the records and tf Dataset.
        """
        parquet_dataset = ClassifierParquetDataset()
        selected_columns = ClassifierParquetDataset.get_column_names(ClassifierParquetDataset.TrainColumns)

        def _dataset_from_parquet_files_gen() -> Generator[dict[str, Any], None, None]:
            """Generator function to yield data batches from parquet files.

            This function reads data from parquet files in parallel, applying a filter condition
            based on the dataset usage (e.g., train, val, test). It yields data records as dictionaries.
            """

            filter_condition = ds.field("split") == self.usage_to_subset[usage]
            shuffle = False
            if usage == Usage.TRAINING and self.shuffle:
                shuffle = True
            data_batches = parquet_dataset.load_parquet_to_data_batches(
                self.parquet_files, filter_condition, selected_columns, shuffle=shuffle
            )

            for batch in data_batches:  # Iterate over the generator
                batch_df = batch.to_pandas()  # Convert each batch to a Pandas DataFrame
                yield from batch_df.to_dict(orient="records")  # Yield records as dictionaries

        output_signature = {
            train_column: tf.TensorSpec(
                shape=(),
                dtype=self.pyarrow_to_tensorflow.get(str(parquet_dataset.schema.field(train_column).type), tf.string),
            )
            for train_column in selected_columns
        }
        total_sample_count = sum(1 for _ in _dataset_from_parquet_files_gen())
        dataset = tf.data.Dataset.from_generator(_dataset_from_parquet_files_gen, output_signature=output_signature)

        def _map_parquet_data(
            data: dict[str, Any],
        ) -> dict[str, Any]:
            """Map the loaded parquet to classifier dataset.

            Args:
                data: Data from the parquet file.
            """
            data = self.parse_from_parquet(data, usage)
            return data

        dataset = dataset.map(_map_parquet_data)  # type: ignore[arg-type]
        return total_sample_count, dataset

    def _dataset_from_tfrecord_files(
        self, usage: Usage, subset_name: str, num_parallel_calls: int
    ) -> tuple[int, int, tf.data.Dataset[Any]]:
        """Create tf dataset from tf record files.

        It also returns the count of total samples from the file name.
        Note: Each tf record file's name is appended with the count of samples it contains.


        Args:
            usage: Dataset type (ex: train, val).
            subset_name: Name of subset used as prefix of file name.
            num_parallel_calls: Number of parallel threads to be created for optimal performance.

        Returns:
            Tuple of total sample counts in the records, size of biggest tfrecord and tf Dataset.
        """
        usage_tfrecord_list = []
        total_sample_count = 0
        max_sample_count = 0
        for record_file in self.record_files:
            file_base_name = os.path.splitext(os.path.basename(record_file))[0]
            if subset_name == file_base_name.split("_")[0]:
                usage_tfrecord_list.append(record_file)
                sample_count_string = file_base_name.split("-")[-1]
                if sample_count_string.isdigit():
                    sample_count = int(sample_count_string)
                    total_sample_count += sample_count
                    max_sample_count = max(max_sample_count, sample_count)

        def load_tfrecord_fn(example: dict[str, Any]) -> dict[str, dict[ValueKey, Any]]:
            """Load the data from tf record extracted sample.

            Args:
                example: A sample extracted from the tf record file.

            Returns:
                Data loaded from the record example.
            """
            data = self.tf_record_loaders(example, usage)
            return data

        random.shuffle(usage_tfrecord_list)
        dataset = tf.data.Dataset.from_tensor_slices(usage_tfrecord_list)
        dataset = dataset.interleave(tf.data.TFRecordDataset, cycle_length=len(usage_tfrecord_list), block_length=1)
        dataset = dataset.map(load_tfrecord_fn)

        # if sample count is not stored in tfrecord file name, count it
        if total_sample_count == 0:
            total_sample_count = int(dataset.reduce(0, lambda x, _: x + 1))

        return total_sample_count, max_sample_count, dataset

    def _collect_tfrecord_files(self, tf_record_list_file_dir_path: str, tf_record_list_file_name: str) -> list[str]:
        """Collects all the record files from each of the records directory specified in the file.

        Args:
            tf_record_list_file_dir_path: Path to the directory containing the tf record list .
            tf_record_list_file_name: File name containing the path to the records directory.

        Returns:
            List of tf record files from the directory.
        """
        dataset_path = os.path.join(tf_record_list_file_dir_path, tf_record_list_file_name)
        tf_record_directory_list = load_files(dataset_path)
        tf_record_file_list = []
        tf_record_extensions = [".tfrec", ".tfrecord", ".tfrecords"]
        for directory_path in tf_record_directory_list:
            full_directory_path = os.path.join(tf_record_list_file_dir_path, directory_path)
            for file_name in os.listdir(full_directory_path):
                if any(file_name.endswith(ext) for ext in tf_record_extensions):
                    file_path = os.path.join(full_directory_path, file_name)
                    tf_record_file_list.append(file_path)
        return tf_record_file_list

    def _collect_parquet_paths(self, parquet_dir_path: str, parquet_list_file_path: str) -> list[str]:
        """Collects all the parquet files from each of the records directory specified in the file.

        Args:
            parquet_dir_path: Path to the directory containing the parquet files.
            parquet_list_file_path: Path to the file containing the list of parquet files.

        Returns:
            List of parquet files from the directory.
        """
        dataset_path = os.path.join(parquet_dir_path, parquet_list_file_path)
        parquet_folder_list = load_files(dataset_path)
        # From the list of folder paths get the list of parquet files

        all_parquet_files: list[str] = []
        for folder_path in parquet_folder_list:
            full_folder_path = os.path.join(parquet_dir_path, folder_path)
            all_parquet_files.extend([str(f) for f in Path(full_folder_path).rglob("*.parquet")])
        return all_parquet_files

    @tf.function
    def _load_data(self, filepaths: dict[str, tf.Tensor | Any], usage: Usage) -> dict[str, dict[ValueKey, Any]]:
        """Loads one batch element of data.

        Args:
            filepaths: Dictionary of filepaths indexed by data types
            usage: For what usage is the data loaded (for validation/test the actual label data is not loaded)

        Returns:
            Dict[str, Dict[ValueKey, Any]] loaded batch element in the TSR data format
        """

        data = {}
        for data_type, filepath in filepaths.items():
            data[data_type] = self.data_loaders[data_type](filepath, usage)

        return data

    def __contrastive_aug_mapping_fn(
        self, augmentations: AugmentationsCallable
    ) -> Callable[[dict[str, dict[ValueKey, Any]], int], dict[str, dict[ValueKey, Any]]]:
        def dataset_map_fn(
            input_tensor_dict: dict[str, dict[ValueKey, Any]], seed: int
        ) -> dict[str, dict[ValueKey, Any]]:
            """Produces two augmentations of the same image & stacks them for Contrastive Loss."""

            xs = []
            for _ in range(2):  # Two transformations
                xs.append(augmentations(input_tensor_dict["rgb"][ValueKey.DATA], seed=seed))  # noqa: PERF401
            input_tensor_dict["rgb"][ValueKey.DATA] = tf.concat(xs, -1)
            return input_tensor_dict

        return dataset_map_fn

    def __crossentropy_aug_mapping_fn(
        self, augmentations: AugmentationsCallable | None = None
    ) -> Callable[[dict[str, dict[Any, Any]], int], dict[str, dict[Any, Any]]]:
        """Produces augmentations of an image for Crossentropy loss.

        Args:
            augmentations: List of augmentation functions for the cross entropy training.

        Returns:
            A callable mapping function.
        """

        def dataset_map_fn(input_tensor_dict: dict[str, Any], seed: int) -> dict[str, dict[Any, Any]]:
            """Function to apply augmentation on the image data.

            Args:
                input_tensor_dict: Dict containing image data to be augmented.
                seed: Seed value to the random function.

            Returns:
                Augmented image data
            """

            image = input_tensor_dict["rgb"][ValueKey.DATA]

            if augmentations is not None:
                image = augmentations(image, seed=seed)

            input_tensor_dict["rgb"][ValueKey.DATA] = image

            return input_tensor_dict

        return dataset_map_fn

    def __centercrop_fn(
        self,
        output_resolution: int = 32,
        is_contrastive: bool = False,  # noqa: FBT001, FBT002
    ) -> Callable[[dict[str, dict[ValueKey, Any]]], dict[str, dict[ValueKey, Any]]]:
        """Outer function used to call crop the image at center.

        Args:
            output_resolution: Desired output resolution after crop.
            is_contrastive: Boolean flag to indicate contrastive loss training.

        Returns:
            Dataset mapping function callable.
        """

        def centrecrop_map_fn(image: tf.Tensor) -> tf.Tensor:
            """Returns the CentreCropped image for a single Image Tensor.

            Args:
                image: Image data to be cropped.

            Returns:
                Cropped image data.
            """

            height = width = output_resolution

            image = data_formats.change_data_format(image, data_formats.DataFormat.NCHW, data_formats.DataFormat.NHWC)
            image = CenterCrop(height, width, name=None)(image)
            image = data_formats.change_data_format(image, data_formats.DataFormat.NHWC, data_formats.DataFormat.NCHW)

            return image

        def dataset_map_fn(input_tensor_dict: dict[str, dict[ValueKey, Any]]) -> dict[str, dict[ValueKey, Any]]:
            """Returns the CentreCropped image Tensor based on Contrastive/Crossentropy.

            Args:
                input_tensor_dict: Dictionary containing image data to be cropped.

            Returns:
                Dictionary with image data after applying center crop.
            """

            image = input_tensor_dict["rgb"][ValueKey.DATA]

            # We have two images concatenated (Contrastive)
            if is_contrastive:
                image1, image2 = tf.split(image, 2, -1)

                # Center Crop both the images
                image1 = centrecrop_map_fn(image1)
                image2 = centrecrop_map_fn(image2)

                xs = [image1, image2]
                # Concat the two images back
                image = tf.concat(xs, -1)

            else:
                image = centrecrop_map_fn(image)

            input_tensor_dict["rgb"][ValueKey.DATA] = image
            return input_tensor_dict

        return dataset_map_fn


def load_dataset(datasets: dict[str, str]) -> dict[str, list[str]]:
    """Load lists of entries (paths) from *.dataset files.

    Args:
        datasets: Dictionary with datset type and dataset file path.

    Returns:
        List of file paths present in the dataset file.
    """
    data_paths = {}

    for dataset_type, dataset_path in datasets.items():
        data_paths[dataset_type] = load_files(dataset_path)

    return data_paths


def load_files(dataset_path: str) -> list[str]:
    """Load lists of entries (paths) from *.dataset files.

    Args:
        dataset_path: Path of the .dataset file.

    Returns:
        List of file path entries from the dataset file.
    """
    with open(dataset_path) as f:
        path_lists = f.readlines()

    return [file_path.rstrip() for file_path in path_lists]


def build_dataset_provider(path_config: str, traininglake_path: str) -> DatasetProvider:
    """Builds dataset provider.

    Args:
        path_config: Path to a YAML configuration of a dataset provider.
        traininglake_path: Path to the data lake.

    Returns:
        DatasetProvider created dataset provider.
    """
    dataset_loader = DefaultDatasetLoader(CV3TSDatasetPath(traininglake_path))

    return dataset_provider_from_yaml(path_config, dataset_loader)
