"""Function to trigger classification publish metrics."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2018-2021 Daimler AG and Robert <PERSON>sch GmbH. All rights reserved.
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
import os
import sys
from typing import Any

from xcommon.logging import configure_general_logging
from xtension.azure_ct import azureml_publishing
from xtension.parameter.environment import parse_cli_arguments
from xtension.parameter.model_config import parse_model_config_arguments
from xusecases.classification import model_configs
from xusecases.classification.common.publishing import load_metrics_of_folder, load_tensorrt_model_timings

_LOGGER = logging.getLogger(__name__)


def main(argv: list[str]) -> None:
    """Entry function to publish classification metrics.

    Args:
        argv: List of arguments to pass evaluation task
    """
    # Set log level and configure the root logger explicitly to avoid side effects of other packages that potentially
    # overwrite the baseConfig (like azureml)
    configure_general_logging(level=logging.INFO)

    # READ PARAMETERS FROM CONFIG AND COMMAND LINE
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_folder",
        type=str,
        nargs="*",
        help=(
            "Input folder where the stage outputs are stored. One can also specify multiple folders. "
            "In that case, all files are copied together into a single folder during the publishing step."
        ),
    )
    parser.add_argument(
        "--publish_to_model_store",
        action="store_true",
        help="Whether or not to save the model and metrics to the model store.",
    )
    parser.add_argument(
        "--publish_folder",
        type=str,
        default=os.path.join("outputs", "publish"),
        help="Path to the folder that will store the published results.",
    )
    model_config, remaining_args = parse_model_config_arguments(argv, model_configs)
    environment, args, model_config = parse_cli_arguments(
        model_config.environments, remaining_args, model_config, parser=parser
    )
    for input_folder in args.input_folder:
        azureml_publishing.copy_data(input_folder=input_folder, output_folder=args.publish_folder)

    _LOGGER.info("Loading metrics for all tasks.")
    metrics, metrics_version = load_metrics(args.publish_folder)

    # Get the TensorRT model timings and report the total time
    timings = load_tensorrt_model_timings(args.publish_folder)
    metrics.update(timings)

    _LOGGER.info(f"metrics_version: {metrics_version}")
    for key, value in metrics.items():
        _LOGGER.info(f"{key}: {value:f}")

    if args.publish_to_model_store:
        _LOGGER.warning("Publishing to the model store is deactivated for the deprecated usecase.")


def load_metrics(input_folder: str) -> tuple[dict[str, Any], int]:
    """Load the combined metric value of all tasks."""
    metrics_version = 2
    metrics = {}

    metrics_folder = os.path.join(input_folder, "evaluation", "offline")
    metrics.update(load_metrics_of_folder(metrics_folder, metrics_prefix="offline/", load_all=True))

    return metrics, metrics_version


if __name__ == "__main__":
    main(sys.argv[1:])
