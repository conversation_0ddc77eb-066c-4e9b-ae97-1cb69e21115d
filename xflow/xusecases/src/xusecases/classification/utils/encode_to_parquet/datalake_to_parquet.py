"""Write the image and ground truth data to the parquet files."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import json
import logging
import os
import random
import warnings
from typing import Any

import numpy as np
import numpy.typing as npt
import pyarrow as pa
import yaml
from PIL import Image
from tqdm import tqdm
from ts_label_mapping.classifier import ClassMappings

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from xusecases.classification.utils.dataset_balancing_functions import balance_dataset
from xusecases.classification.utils.dataset_filtering import filter_dataset_with_expand

TABLE_SAVE_FREQUENCY = 20000


def encode_patches_to_parquet(
    img_gt: list[tuple[str, str]],
    split_type: str,
    output_dir: str,
    data_lake_path: str,
    country_code: str,
) -> None:
    """Encode patches to Parquet format using pyarrow.

    Args:
        img_gt: List of tuples containing image file paths and label file paths.
        split_type: Type of dataset.
        output_dir: Directory to save the Parquet file.
        data_lake_path: Path to the datalake folder.
        country_code: country code.
    """
    classifier_parquet_dataset = ClassifierParquetDataset()
    dataframe_dict = initialize_dataframe_dict(classifier_parquet_dataset)
    name_to_id = ClassMappings().get_name_to_id_mapping()

    for count, (image_file, label_file) in enumerate(tqdm(img_gt)):
        process_image_and_label(
            count,
            image_file,
            label_file,
            data_lake_path,
            split_type,
            country_code,
            name_to_id,
            dataframe_dict,
        )

        # Save to Parquet after every N samples
        if (count + 1) % TABLE_SAVE_FREQUENCY == 0:
            table = pa.table(dataframe_dict)
            classifier_parquet_dataset.save_to_parquet(table, output_dir)
            # Clear the dataframe_dict for the next batch
            dataframe_dict = initialize_dataframe_dict(classifier_parquet_dataset)

    # Save any remaining data after the loop
    if dataframe_dict["id"]:
        table = pa.table(dataframe_dict)
        classifier_parquet_dataset.save_to_parquet(table, output_dir)


def initialize_dataframe_dict(
    classifier_parquet_dataset: ClassifierParquetDataset,
) -> dict[str, list[Any]]:
    """Initialize the dataframe dictionary.

    Args:
        classifier_parquet_dataset: ClassifierParquetDataset object.

    Returns:
        Dataframe dictionary containing updated with label data.
    """
    dataframe_dict: dict[str, list[Any]] = {
        required_column: [] for required_column in classifier_parquet_dataset.required_columns
    }
    dataframe_dict.update(
        {
            "isYawRotated": [],
            "isRollRotated": [],
            "isDamaged": [],
            "isOccluded": [],
            "isPartiallyOutOfImage": [],
            "is_on_vehicle": [],
            "time_of_day": [],
            "text": [],
            "frame_label_path": [],
            "frame_image_path": [],
            "view": [],
        }
    )
    assert all(item in classifier_parquet_dataset.column_names for item in dataframe_dict), (
        "All items in the dataframe_dict should be in the ClassifierParquetDataset columns"
    )
    return dataframe_dict


def process_image_and_label(
    count: int,
    image_file: str,
    label_file: str,
    data_lake_path: str,
    split_type: str,
    country_code: str,
    name_to_id: dict[str, int],
    dataframe_dict: dict[str, list[Any]],
) -> None:
    """Process a single image and label file.

    Args:
        count: Count of the image.
        image_file: Image file path.
        label_file: Label file path.
        data_lake_path: Path to the datalake folder.
        split_type: Type of dataset.
        country_code: Country code.
        name_to_id: Mapping of class names to class IDs.
        dataframe_dict: Dataframe dictionary
    """
    image = np.asarray(Image.open(image_file), dtype=np.uint8)
    with open(label_file, encoding="utf-8") as f:
        label = json.load(f)

    relative_image_file = os.path.relpath(image_file, data_lake_path)

    update_dataframe_dict(
        image,
        label,
        dataframe_dict,
        country_code,
        count,
        split_type=split_type,
        relative_image_file=relative_image_file,
        name_to_id=name_to_id,
    )


def update_dataframe_dict(
    image: npt.NDArray[np.uint8],
    label: dict[str, Any],
    dataframe_dict: dict[str, list[Any]],
    country_code: str,
    count: int,
    split_type: str,
    relative_image_file: str,
    name_to_id: dict[str, int],
) -> None:
    """Process label attributes and update the dataframe dictionary.

    Args:
        image: Image data.
        label: Label data.
        dataframe_dict: Dataframe dictionary.
        country_code: Country code.
        count: Count of the image.
        split_type: Type of dataset.
        relative_image_file: Relative path to the image file.
        name_to_id: Mapping of class names to class IDs.
    """

    for key in ["variation_track_length", "variation_type", "created", "modified", "z_value", "instance_type"]:
        label.pop(key, None)
    # Remove all "NA" and "NONE" values from the label dictionary
    label = {k: v for k, v in label.items() if v not in ["NA", "None"]}

    dataframe_dict["id"].append(count)

    # Use the reusable method to encode image data
    dataframe_dict["snippet_image_data"].append(ClassifierParquetDataset.encode_image_to_bytes(image))

    dataframe_dict["split"].append(split_type)
    dataframe_dict["snippet_image_type"].append("RGB")
    dataframe_dict["classifier_class_name"].append(label.pop("trafficSignClass", None))

    if dataframe_dict["classifier_class_name"][-1] not in name_to_id:
        warnings.warn(
            f"Traffic sign class '{dataframe_dict['classifier_class_name'][-1]}' not found in the class mappings",
            stacklevel=2,
        )

    dataframe_dict["frame_label_path"].append(
        label.pop("frame_label_path", "frame_label_path_unknown").split("INPUT_ingest_cache_ingest_cache")[-1]
    )
    dataframe_dict["frame_image_path"].append(
        label.pop("frame_image_path", "frame_image_path_unknown").split("INPUT_ingest_cache_ingest_cache")[-1]
    )
    dataframe_dict["view"].append(label.pop("view", "view_unknown"))

    box_left = label.pop("box_left", 0)
    box_top = label.pop("box_top", 0)
    box_width = label.pop("box_width", 32)
    box_height = label.pop("box_height", 32)

    dataframe_dict["snippet_location"].append(np.array([box_left, box_top], dtype=np.float32))
    dataframe_dict["box_width"].append(box_width)
    dataframe_dict["box_height"].append(box_height)
    dataframe_dict["track_id"].append(label.pop("track_id", "track_id_unknown"))
    dataframe_dict["object_id"].append(label.pop("unique_id", relative_image_file))
    dataframe_dict["shape"].append(label.pop("shape", "shape_unknown"))
    dataframe_dict["trafficSignClass"].append(json.dumps(label.pop("classifier_label_name", "unknown")))
    dataframe_dict["hierarchy_class"].append(label.pop("hierarchy_class", None))

    value = label.pop("value", 0.0)
    while isinstance(value, list) and any(value):
        value = value[0]

    try:
        value = float(value)
    except (ValueError, TypeError):
        value = 0.0
    dataframe_dict["value"].append(value)

    def _convert_to_string(value: Any) -> str:
        """Convert a value to string if it's not already a string."""
        return str(value) if not isinstance(value, str) else value

    def _convert_to_boolean(value: Any) -> bool:
        """Convert a value to boolean if it's a string."""
        if isinstance(value, str):
            return value.lower() == "true"
        return bool(value)

    # Ensure proper type conversion for fields
    dataframe_dict["is_inverted"].append(
        _convert_to_boolean(
            (label.pop("vms", False) and label.pop("inverted_vms", False)) or label.pop("inverted", False)
        )
    )
    dataframe_dict["isYawRotated"].append(_convert_to_string(label.pop("turned", None)))
    dataframe_dict["isRollRotated"].append(_convert_to_string(label.pop("rotated", None)))
    dataframe_dict["isDamaged"].append(_convert_to_boolean(label.pop("damaged", None)))
    dataframe_dict["isOccluded"].append(_convert_to_string(label.pop("occlusion", None)))
    dataframe_dict["isPartiallyOutOfImage"].append(_convert_to_boolean(label.pop("isPartiallyOutOfImage", None)))
    dataframe_dict["is_on_vehicle"].append(_convert_to_string(label.pop("is_on_vehicle", None)))

    # Ensure all other fields are properly converted
    dataframe_dict["country_code"].append(
        _convert_to_string(label.pop("country_code", label.pop("country", country_code)))
    )
    dataframe_dict["time_of_day"].append(_convert_to_string(label.pop("timeOfDay", None)))
    dataframe_dict["text"].append(json.dumps(label.pop("text", [])))

    dataframe_dict["snippet_data_without_schema"].append(json.dumps(label))


# Define a module-level logger
DEFAULT_LOGGER = logging.getLogger(__name__)


def convert_datalake_to_parquet(
    dataset_provider: Any,
    output_dir: str,
    data_lake_path: str,
    country_code: str,
    logger: logging.Logger = DEFAULT_LOGGER,
    *,
    enable_balancing: bool = False,
) -> None:
    """Convert the data lake to Parquet format.

    Args:
        dataset_provider: Object providing access to the dataset.
        output_dir: Directory to save the Parquet file.
        data_lake_path: Path to the datalake folder.
        country_code: Country code.
        logger: Logger for logging messages.
        enable_balancing: Flag to enable dataset balancing.
    """
    for dataset_type in dataset_provider.data_collections:
        data_files = dataset_provider.collect_data(dataset_type)[0]
        # Shuffling at the dataset level
        img_gt = list(zip(data_files["rgb"], data_files["gt"]))
        # Read dataset balance configuration from the config file
        config_file = os.path.join(
            os.path.dirname(os.path.realpath(__file__)),
            "../dataset_balancing_config.yml",
        )
        with open(config_file, encoding="utf-8") as stream:
            config = yaml.safe_load(stream)
        # Filter dataset for attributes, count and size of samples.
        filter_result, img_gt = filter_dataset_with_expand(img_gt, config["dataset_filter_config"])
        logger.info(filter_result)
        if "train" in dataset_type:
            if enable_balancing:
                img_gt, balance_method_selection = balance_dataset(
                    img_gt, config["dataset_balance_config"], logger=logger
                )
                logger.info(balance_method_selection)
            random.seed(1)
            random.shuffle(img_gt)
        encode_patches_to_parquet(img_gt, dataset_type, output_dir, data_lake_path, country_code)
