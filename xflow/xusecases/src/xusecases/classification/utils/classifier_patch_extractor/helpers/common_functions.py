"""This file contains common functions used in the dataset creation."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""
import hashlib
import json
import logging
import os
import re
import warnings
from collections import defaultdict
from concurrent.futures import ThreadPoolExecutor
from itertools import groupby
from typing import Any, cast

import cv2
import numpy as np
import numpy.typing as npt
import pandas as pd
import pyarrow as pa
import yaml
from pandas import Series
from sklearn.calibration import LabelEncoder
from tqdm import tqdm
from ts_label_mapping.classifier import ClassMappings

from data_formats.classification.dataset.parquet_dataset import ClassifierParquetDataset
from xtension.parameter.model_config import parse_model_config_arguments
from xusecases.classification.common.crop_utils import (
    get_view_and_offset_from_bounding_box,
    scale_l0_wide_to_l0_far_box,
    scale_l0_wide_to_l1_wide_box,
)
from xusecases.classification.common.parameters import Environment
from xusecases.classification.utils.classifier_patch_extractor.helpers.extraction_functions import create_patch_for_box
from xusecases.classification.utils.encode_to_parquet.datalake_to_parquet import (
    initialize_dataframe_dict,
    update_dataframe_dict,
)
from xusecases.vision.models.pace_general.release import model_configs

STATS_MAP_KEYS = [
    "unrecognizable",
    "far",
    "dirty",
    "truncation",
    "occlusion",
    "phantom",
    "faded",
    "snowOver",
    "shadow",
    "reflection",
    "damaged",
    "isPartiallyOutOfImage",
    "occluded_track",
    "unreadable",
    "snowover",
    "reflection",
    "small",
    "ground",
]


def get_brightness_values(image: npt.NDArray[Any], center_roi_part: float) -> tuple[float, npt.ArrayLike, float]:
    """Returns min, max and mean brightness values from only the roi part of the image.

    Args:
        image: Array with rgb image.
        center_roi_part: ROI of image in terms of unit width/height.

    Returns:
        Tuple containing min, max and mean brightness values.
    """
    height, width, _ = image.shape

    left = int(width * (1.0 - center_roi_part) / 2 + 0.5)
    right = int(width - left + 0.5)
    top = int(height * (1.0 - center_roi_part) / 2 + 0.5)
    bottom = int(height - top + 0.5)

    roi = image[top:bottom, left:right, :]
    min_value = np.amin(roi)
    max_value = np.amax(roi)
    mean_value = np.mean(roi)

    return min_value, mean_value, max_value


def get_trifocal_view_and_image_from_bounding_box(
    box: list[float], far_to_wide_view_offset_hw: tuple[int, int], image_path: dict[str, str]
) -> tuple[str, str, list[float]]:
    """Get the view, corresponding image and offset curated bounding box.

    Args:
        box: Bounding box coordinates
        far_to_wide_view_offset_hw: Offset for far to wide view
        image_path: Dictionary containing image paths

    Returns:
        Tuple containing the view, view key and offset
    """
    # convert x1, y1, x2, y2 box to y1, x1, y2, x2
    np_box = np.array([box[1], box[0], box[3], box[2]], dtype=np.float64)
    # Adjust the offset for the far view to wide view based on the far crop ROI
    view, far_to_wide_view_offset_hw = get_view_and_offset_from_bounding_box(np_box, far_to_wide_view_offset_hw)
    if view == "far":
        np_box = np.array(scale_l0_wide_to_l0_far_box(np_box, far_to_wide_view_offset_hw), dtype=np.float64)
    else:
        view = "l1_wide"
        np_box = np.array(scale_l0_wide_to_l1_wide_box(np_box), dtype=np.float64)
    # revert y1, x1, y2, x2 box to x1, y1, x2, y2
    box = [np_box[1], np_box[0], np_box[3], np_box[2]]
    view_key = next(view_key for view_key in image_path if view in view_key)

    return view, image_path[view_key], box


def write_images_and_labels(  # noqa: PLR0913,
    label_base_path: str,
    image_base_path: str,
    image_cache_path: str,
    label_files: list[str],
    labels: list[dict[str, Any]],
    image_meta_data_dict: dict[str, tuple[str, list[float]]],
    dataset_type: str,
    core_size: int,
    patch_size: int,
    roi_part: float,
    minimum_contrast: int,
    logger: logging.Logger,
    *,
    multiview: bool = False,
) -> None:
    """Write images and labels to the given output directories.

    Args:
        label_base_path: String base path to output label directory
        image_base_path: String base path to output image directory
        image_cache_path: Path to cached images
        label_files: List of relative label file paths
        labels: List of labels
        image_meta_data_dict: Dictionary Image meta data stored from the patch extractor
        dataset_type: String dataset type (Eg. Val, Train)
        core_size: Int core size of the patches
        patch_size: Int patch size
        roi_part: Region of interest from the full patch
        minimum_contrast: Minimum contrast as a threshold value.
        logger: Logger object
        multiview: Boolean flag to indicate if the dataset is trifocal.
    """

    img_gt_file_list = []
    dataset_base_path = os.path.normpath(os.path.join(label_base_path, "..", "filelists"))
    img_dataset_path = os.path.join(dataset_base_path, f"img_{dataset_type}.dataset")
    gt_dataset_path = os.path.join(dataset_base_path, f"gt_{dataset_type}.dataset")
    trifocal_fc1, _ = parse_model_config_arguments(
        ["--model_config", "traffic_sign_yolo_v4_tiny_trifocal"], model_configs
    )

    logger.info(f"Dataset Type: {dataset_type}")
    low_contrast_counter = 0

    for file, label in zip(label_files, labels):
        label_hash = create_hash(file)
        image_path, box = image_meta_data_dict[label_hash]
        class_id, track_id, track_image = list(filter(None, file.split(os.path.sep)))[:3]
        label_output_path = os.path.join(label_base_path, class_id, track_id, track_image)
        image_output_path = os.path.join(image_base_path, class_id, track_id, track_image).replace(".json", ".png")

        try:
            split_path = os.path.split(label_output_path)
            os.makedirs(split_path[0], mode=0o755, exist_ok=True)
            with open(label_output_path, "w", encoding="utf-8") as outfile:
                json.dump(label, outfile, sort_keys=True, indent=4, separators=(",", ": "))

            # create patch
            if multiview and isinstance(image_path, dict):
                label["view"], image_path, box = get_trifocal_view_and_image_from_bounding_box(
                    box, trifocal_fc1.far_to_wide_view_offset_hw, image_path
                )

            image_path = os.path.abspath(image_path)  # cv2.imread is sensitive to path format
            if not os.path.exists(image_path):
                warnings.warn(f"Invalid path at {image_path}. Skipping image.", stacklevel=2)
                continue
            input_image = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
            if input_image is None:
                warnings.warn(f"Error reading image at {image_path}. Skipping image.", stacklevel=2)
                continue
            # cast box to round int
            box_rounded = [round(coord) for coord in box]
            patch = create_patch_for_box(box_rounded, int(core_size), int(patch_size), input_image)

            # check image contrast
            min_value, _, max_value = get_brightness_values(patch, roi_part)
            if max_value - min_value < minimum_contrast:
                low_contrast_counter += 1
                continue

            split_path = os.path.split(image_output_path)
            os.makedirs(split_path[0], mode=0o755, exist_ok=True)
            # Convert it to BGR before saving using cv2
            cv2.cvtColor(patch, cv2.COLOR_RGB2BGR, dst=patch)
            cv2.imwrite(image_output_path, patch)
        except OSError:
            logger.warning(f"Error writing data for {file}")
            continue
        out_img_file_path = os.path.relpath(image_output_path, dataset_base_path) + "\n"
        out_gt_file_path = os.path.relpath(label_output_path, dataset_base_path) + "\n"
        img_gt_file_list.append((out_img_file_path, out_gt_file_path))

    # write datasets
    img_file_list, gt_file_list = zip(*img_gt_file_list) if img_gt_file_list else ([], [])
    os.makedirs(dataset_base_path, mode=0o755, exist_ok=True)
    for path, data in [(img_dataset_path, img_file_list), (gt_dataset_path, gt_file_list)]:
        with open(path, "w", encoding="utf-8") as outfile:
            outfile.writelines(data)

    logger.info(f"Low contrast images skipped:{low_contrast_counter}")
    logger.info("Success: Finished writing dataset.")


def write_images_and_labels_parquet(  # noqa: PLR0913,
    label_base_path: str,
    image_base_path: str,
    image_cache_path: str,
    label_files: list[str],
    labels: list[dict[str, Any]],
    image_meta_data_dict: dict[str, tuple[str, list[float]]],
    dataset_type: str,
    core_size: int,
    patch_size: int,
    roi_part: float,
    minimum_contrast: int,
    logger: logging.Logger,
    *,
    multiview: bool = False,
) -> None:
    """Write images and labels to the given output directories.

    Args:
        label_base_path: String base path to output label directory
        image_base_path: String base path to output image directory
        image_cache_path: Path to cached images
        label_files: List of relative label file paths
        labels: List of labels
        image_meta_data_dict: Dictionary Image meta data stored from the patch extractor
        dataset_type: String dataset type (Eg. Val, Train)
        core_size: Int core size of the patches
        patch_size: Int patch size
        roi_part: Region of interest from the full patch
        minimum_contrast: Minimum contrast as a threshold value.
        logger: Logger object
        multiview: Boolean flag to indicate if the dataset is trifocal.
    """

    img_gt_file_list = []
    dataset_base_path = os.path.normpath(os.path.join(label_base_path, "..", "parquet_files"))
    img_dataset_path = os.path.join(dataset_base_path, f"img_{dataset_type}.dataset")
    gt_dataset_path = os.path.join(dataset_base_path, f"gt_{dataset_type}.dataset")
    trifocal_fc1, _ = parse_model_config_arguments(
        ["--model_config", "traffic_sign_yolo_v4_tiny_trifocal"], model_configs
    )

    logger.info(f"Dataset Type: {dataset_type}")
    low_contrast_counter = 0

    def _extract_patches(file: str, label: dict[str, Any]) -> None:
        """Extract patches from the image and write to the output directory.

        Args:
            file: Path to the label file
            label: Label data
        """
        nonlocal low_contrast_counter, img_gt_file_list
        label_hash = create_hash(file)
        image_path, box = image_meta_data_dict[label_hash]

        class_id, track_id, track_image = list(filter(None, file.split(os.path.sep)))[:3]
        label_output_path = os.path.join(label_base_path, class_id, track_id, track_image)
        image_output_path = os.path.join(image_base_path, class_id, track_id, track_image).replace(".json", ".png")

        try:
            # create patch
            if multiview and isinstance(image_path, dict):
                label["view"], image_path, box = get_trifocal_view_and_image_from_bounding_box(
                    box, trifocal_fc1.far_to_wide_view_offset_hw, image_path
                )

            image_path = os.path.abspath(image_path)  # cv2.imread is sensitive to path format
            if not os.path.exists(image_path):
                warnings.warn(f"Invalid path at {image_path}. Skipping image.", stacklevel=2)
                return
            input_image = cv2.imread(image_path, cv2.IMREAD_UNCHANGED)
            if input_image is None:
                warnings.warn(f"Error reading image at {image_path}. Skipping image.", stacklevel=2)
                return
            # cast box to round int
            box_rounded = [round(coord) for coord in box]
            patch = create_patch_for_box(box_rounded, int(core_size), int(patch_size), input_image)
            label["frame_image_path"] = image_path
            # check image contrast
            min_value, _, max_value = get_brightness_values(patch, roi_part)
            if max_value - min_value < minimum_contrast:
                low_contrast_counter += 1
                return

            patch = np.asarray(patch, dtype=np.uint8)
        except OSError:
            logger.warning(f"Error writing data for {file}")
            return
        out_img_file_path = os.path.relpath(image_output_path, dataset_base_path) + "\n"
        out_gt_file_path = os.path.relpath(label_output_path, dataset_base_path) + "\n"
        img_gt_file_list.append((out_img_file_path, out_gt_file_path, patch, label))

    # write thread for image and label files
    with ThreadPoolExecutor() as executor:
        executor.map(_extract_patches, label_files, labels)

    # write datasets
    img_file_list, gt_file_list, _, _ = zip(*img_gt_file_list) if img_gt_file_list else ([], [], [], [])
    os.makedirs(dataset_base_path, mode=0o755, exist_ok=True)
    for path, data in [(img_dataset_path, img_file_list), (gt_dataset_path, gt_file_list)]:
        with open(path, "w", encoding="utf-8") as outfile:
            outfile.writelines(data)

    logger.info(f"Low contrast images skipped:{low_contrast_counter}")
    logger.info("Success: Finished writing dataset.")
    write_to_parquet_dataset(img_gt_file_list, dataset_type, dataset_base_path, logger)


def write_to_parquet_dataset(
    img_gt_file_list: list[tuple[str, str, npt.NDArray[Any], dict[str, Any]]],
    dataset_type: str,
    output_dir: str,
    logger: logging.Logger,
) -> None:
    """Save the image, label, and path to a parquet file using pyarrow.

    Args:
        img_gt_file_list: List of tuples containing image, label, and path.
        dataset_type: Type of dataset (Eg. Val, Train)
        output_dir: Output directory to save the parquet file
        logger: Logger object
    """

    # Set the parquet file partition_count to 5000 to have multiple batches of data
    classifier_parquet_dataset = ClassifierParquetDataset(partition_count=5000)
    dataframe_dict = initialize_dataframe_dict(classifier_parquet_dataset)
    name_to_id = ClassMappings().get_name_to_id_mapping()
    logger.info(f"Writing parquet file for {dataset_type}")

    for count, (img_file, gt_file, img_data, label) in enumerate(tqdm(img_gt_file_list)):  # noqa: B007
        country = label.get("country", "unknown")
        update_dataframe_dict(
            img_data,
            label,
            dataframe_dict,
            country,
            count,
            dataset_type,
            img_file,
            name_to_id,
        )
        # Break the data into smaller chunks to avoid memory issues
        if (count + 1) % 20000 == 0:
            table = pa.table(dataframe_dict)
            classifier_parquet_dataset.save_to_parquet(table, output_dir)
            dataframe_dict = initialize_dataframe_dict(classifier_parquet_dataset)

    if len(dataframe_dict["country_code"]) > 0:
        table = pa.table(dataframe_dict)
        classifier_parquet_dataset.save_to_parquet(table, output_dir)

    logger.info("Success: Finished writing parquet file.")
    logger.info(120 * "=")


def append_to_yaml(path_to_file: str, dump: dict[str, Any], mode: str = "a") -> None:
    """Append given data to yaml.

    Args:
        path_to_file: Path to the yaml file
        dump: Data to be appended
        mode: File operation mode
    """

    with open(path_to_file, mode, encoding="utf-8") as file_:
        yaml.dump(dump, file_, default_flow_style=False)


def create_hash(to_be_hashed: str) -> str:
    """Create hash key for given input string.

    Args:
        to_be_hashed: String data to be hashed

    Returns:
        Hash key of the string
    """

    hasher = hashlib.sha256()
    hasher.update(str(to_be_hashed).encode("utf-8"))
    hash_key = hasher.hexdigest()

    return hash_key


def similar_dataset_exists(hash_key: str, hashes_file: str, logger: logging.Logger) -> bool:
    """Check if given hash key exists in the hashes file. If so, terminate script and points to the hash value.

    Args:
        hash_key: Hash key to be checked for
        hashes_file: File with existing hash keys
        logger: Logger object
    """

    try:
        with open(hashes_file, encoding="utf-8") as hash_file:
            hashes = yaml.safe_load(hash_file)
            for hash_ in hashes:
                if hash_key == str(hash_):
                    logger.warning(f"Subset data already exists at {hashes[hash_]}")
                    return True

        logger.info(f"No existing subset data hashes found in {hashes_file}")
    except SystemExit:
        logger.info(f"Error reading hash file {hashes_file} - Exiting")
        return True
    except FileNotFoundError:
        logger.info(f"No existing subset data hashes found in {hashes_file}")
    return False


def add_to_dict(dict_to_add: dict[str, Any], summed_dict: dict[str, Any]) -> dict[str, Any]:
    """Add the key value pairs from a given dictionary to another one.

     If the key exists, then terminates the program after raising an exception.

    Args:
        dict_to_add: Dictionary whose key-value pairs will be added to the other dict.
        summed_dict: Dictionary which will get additional key-value pairs from the other dict.

    Returns:
        Dictionary with combined key value pairs.
    """

    for key, value in dict_to_add.items():
        if key in summed_dict:
            msg = f"Cannot overwrite key: {key}"
            raise ValueError(msg)

        summed_dict[key] = value

    return summed_dict


def add_variation_info(
    track_info: tuple[str, dict[str, Any]], len_of_track: int, track_json_data: dict[str, Any]
) -> None:
    """Adds variation related attributes in each label file of a given track.

    Args:
        track_info: tuple (track, variation)
        len_of_track: length of the variation track, to be added to the label
        track_json_data: dictionary containing trackwise list of labels
    """
    track, variation_type = track_info
    track_data = track_json_data[track]

    for label_index, track_file_data in enumerate(track_data):
        # Extra attributes added to label files to keep track of variations. Changing type to list because json
        # does not support python sets.
        for key in variation_type:
            if not isinstance(variation_type[key], int):
                variation_type[key] = list(variation_type[key])

        extra_attributes = {
            "variation_type": variation_type,
            "variation_track_length": len_of_track,
        }
        track_json_data[track][label_index] = add_to_dict(extra_attributes, track_file_data)


def convert_to_binary(att_list: list[str]) -> list[int]:
    """Given a list of attributes, converts it into a binary encoded list based on STATS_MAP_KEYS.

    Args:
        att_list: list of attributes

    Returns:
        binary encoded list
    """

    # Encode the attribute keys to binary value based on their presence
    data = []
    for k in STATS_MAP_KEYS:
        if k in att_list:
            data.append(1)
        else:
            data.append(0)

    return data


def extract_intra_class_variations(  # pylint: disable=too-many-locals  # noqa: C901
    dataset: dict[tuple[str, str], dict[str, Any]], variation_map_keys: list[str], class_labels: dict[str, Any]
) -> None:
    """Extract intra class variations based on the attribute values of the label files.

    Args:
        dataset: List of all the file paths in the track
        variation_map_keys: Attributes keys to be considered for the variation
        class_labels: Structure to hold the resultant variation based class data
    """
    all_labels = set()

    # Group label data based on the track in separate lists
    track_based_data = defaultdict(list)
    for key, group in groupby(sorted(dataset.items()), lambda x: x[0][0]):
        track_based_data[key].extend(list(group))

    # Extract variation type in the track by reading map keys attribute values
    # track format is in /labels/class_xx/xxxxxxxx_xxxxxx_smpc/trackxxxxx_xxpoi
    for track_number, track_data in track_based_data.items():
        variation_within_tracks: dict[str, Any] = {}
        # json data has the json labels as a list [{}, {}, ..]
        # sample_file has the label path
        # eg. labels/class_xxx/xxxxxxxx_xxxxxx_smpc/trackxxxxx_xxpoi/img_xxxxxxxxx_xx_xx.json in a list [path, ..]
        json_data = list(zip(*track_data))[1]
        sample_file = list(map("/".join, next(zip(*track_data))))
        if len(json_data) == 0:
            continue
        for label_data in json_data:
            class_label = (
                label_data["trafficSignClass"]
                + "_"
                + label_data["hierarchy_class"]
                + "_"
                + str(label_data["classifier_label_id"])
            )

            all_labels.add(class_label)
            text_lines = label_data.get("num_of_textlines", 0)
            if text_lines > variation_within_tracks.get("num_of_textlines", 0):
                variation_within_tracks["textlines"] = text_lines
            # variation_in_sample is a dict = {"foreground": "xxx", "background": "xxx", "shape": "xxx", ...}
            variation_in_sample = {key: label_data.get(key, "") for key in variation_map_keys}
            variation_within_tracks = {**variation_within_tracks, **variation_in_sample}
            for key, value in variation_within_tracks.items():
                if key in variation_in_sample and key in variation_within_tracks:
                    variation_within_tracks[key] = set().union(
                        value if isinstance(value, list) else [value],
                        (
                            variation_in_sample[key]
                            if isinstance(variation_in_sample[key], list)
                            else [variation_in_sample[key]]
                        ),
                    )

        # Check the uniqueness of the acquired map keys
        unique_keys = True
        variation_index = -1
        for variation_index, variation_type in enumerate(class_labels[class_label]["variation_list"]):  # noqa: B007
            if variation_type == variation_within_tracks:
                unique_keys = False
                break

        # Append into the list of class variations if its unique
        if unique_keys:
            class_labels[class_label]["variation_list"].append(variation_within_tracks)
            variation_index = variation_index + 1
        # Segregate tracks based on the sample variations
        class_labels[class_label]["variation_tracks"][variation_index].append((track_number, variation_within_tracks))
        class_labels[class_label]["gt_files_per_track"][track_number].extend(sample_file)
        class_labels[class_label]["json_data_per_track"][track_number].extend(json_data)


def get_new_labels(attr_stats: list[list[int]]) -> npt.NDArray[Any]:
    """Compute new label values based on the attribute combination.

    Args:
        attr_stats: List of accumulated attribute stats of all the tracks in the variation

    Returns:
        New encoded labels based on attribute combination
    """
    transformed_attrs = LabelEncoder().fit_transform(["".join(str(label)) for label in attr_stats])
    if len(transformed_attrs) > 0:
        attr_stats_new = transformed_attrs
        return attr_stats_new
    msg = "Transformed attrs is None!"
    raise ValueError(msg)


def get_track_attributes(
    track_info: tuple[str, dict[str, Any]], track_json_data: dict[str, list[dict[str, Any]]]
) -> list[str]:
    """Given a track, gets the list of unique enabled attributes from the track.

    Args:
        track_info: tuple (track, variation)
        track_json_data: dictionary containing trackwise list of labels

    Returns:
        list of unique enabled attributes in the track
    """

    track, _ = track_info
    track_data = track_json_data[track]
    acc_data: list[Any] = []

    for track_file_data in track_data:
        # Accumulate the keys of enabled attributes for all samples in a track
        acc_data = list(set(acc_data + [key for key, val in track_file_data.items() if val and key in STATS_MAP_KEYS]))

    return acc_data


def write_datalake_yml(yml_file_path: str, dataset_name: str, date: str) -> None:
    """Write datalake yml file to be used by the datalake loader.

    Args:
        yml_file_path: Path to the datalake yml file
        dataset_name: Name of the dataset
        date: String containing date information for subfolder
    """

    yml_content_dict = {
        "name": f"{dataset_name}: (patch extraction generated)",
        "version": "1.0.0",
        "input_dataset": {"type": "rgb", "dataset_path": "img_*.dataset"},
        "label_datasets": [
            {"type": "gt", "dataset_path": "gt_*.dataset", "label_set": "labels", "label_set_version": "1.0.0"}
        ],
        "data_collections": {
            "train": [{"name": f"{dataset_name}", "export_date": f"{date}"}],
            "val": [{"name": f"{dataset_name}", "export_date": f"{date}"}],
            "test": [{"name": f"{dataset_name}", "export_date": f"{date}"}],
        },
    }

    with open(yml_file_path, "w", encoding="utf-8") as out_file:
        yaml.dump(yml_content_dict, out_file, default_flow_style=False)


def write_metadata_file(dataset_directory: str) -> None:
    """Writes a dummy metadata file to be used by the datalake loader.

    Args:
        dataset_directory: Path to the datalake directory to write the metadata file.
    """
    metadata_dict = {
        "schema": "pace/label_set/label_set_3_0_0.schema.json",
        "name": "labels",
        "version": "1.0.0",
        "labels": [
            {"name": "background", "id": 0, "color": [0, 0, 0], "predicted": True},
            {"name": "traffic_sign", "id": 1, "color": [255, 150, 0], "predicted": True},
            {"name": "Element", "id": 2, "color": [0, 255, 150], "predicted": False},
            {"name": "TextLine", "id": 3, "color": [150, 0, 255], "predicted": False},
            {"name": "Group", "id": 4, "color": [128, 128, 128], "predicted": False},
            {"name": "Situation", "id": 5, "color": [128, 128, 128], "predicted": False},
        ],
        "attributes": [
            {
                "name": "occlusion",
                "type": "enum",
                "predicted": False,
                "parents": ["traffic_sign", "Element", "TextLine"],
                "values": [
                    {"name": "0%", "id": 0, "predicted": False},
                    {"name": "1-10%", "id": 1, "predicted": False},
                    {"name": "11-50%", "id": 2, "predicted": False},
                    {"name": ">50%", "id": 3, "predicted": False},
                ],
            },
            {
                "name": "shape",
                "type": "enum",
                "predicted": True,
                "parents": ["traffic_sign"],
                "values": [
                    {"name": "background", "id": 0, "predicted": True},
                    {"name": "other", "id": 1, "predicted": True},
                    {"name": "triangleup", "id": 2, "predicted": True},
                    {"name": "triangledown", "id": 3, "predicted": True},
                    {"name": "rectangle", "id": 4, "predicted": True},
                    {"name": "square", "id": 5, "predicted": True},
                    {"name": "diamond", "id": 6, "predicted": True},
                    {"name": "pentagon", "id": 7, "predicted": True},
                    {"name": "arrow", "id": 8, "predicted": True},
                    {"name": "octagon", "id": 9, "predicted": True},
                    {"name": "cross", "id": 10, "predicted": True},
                    {"name": "circle", "id": 11, "predicted": True},
                ],
            },
            {
                "name": "type",
                "type": "enum",
                "predicted": True,
                "parents": ["traffic_sign"],
                "values": [
                    {"name": "background", "id": 0, "predicted": True},
                    {"name": "mainsign", "id": 1, "predicted": True},
                    {"name": "addsign", "id": 2, "predicted": True},
                ],
            },
        ],
    }
    if os.path.exists(dataset_directory):
        meta_data_file = os.path.join(dataset_directory, "metadata", "label_set_1.0.0.json")
        os.makedirs(os.path.dirname(os.path.abspath(meta_data_file)))
        with open(meta_data_file, "w", encoding="utf-8") as out_file:
            json.dump(metadata_dict, out_file, indent=4)


def remove_mount_location_from_path(path: str) -> str:
    """Splits the given path after the 'INPUT_x_x_x_x' section, if it exists. Otherwise return path.

    This function uses a regular expression to find the 'INPUT_x_x_x_x' section in the path,
    and then splits the path at the end of this section. The function returns the part of the
    path after the 'INPUT_x_x_x_x' section. If the 'INPUT_x_x_x_x' section is not found in the
    path, the function returns the original path.

    This is necessary because the ts_classifier_background_patch_extractor used to generate a csv
    dataset containing the detector's false positive bounding boxes may contain these kind of paths:
    - ../../cap/data-capability/wd/INPUT_ingest_cache_ingest_cache/cache/
        mdm-001eb553b1914c68d7c6e9344f3e81085f1a31f101560d3581d9e80218886bd2/
        image-ImageWarpingDataConverter_v1.5_alliance_far/
        8f90c5a1ad991ff8d5138ddb683940624e2afcc918024ded023be6e690f03d78.png

    The other use cases of the classifier_patch_extractor do not use mount points in the paths, but
    rather already contain relative paths to the images and labels, like this:
    - cache/mdm-001eb553b1914c68d7c6e9344f3e81085f1a31f101560d3581d9e80218886bd2/
        image-ImageWarpingDataConverter_v1.5_alliance_far/
        8f90c5a1ad991ff8d5138ddb683940624e2afcc918024ded023be6e690f03d78.png
    - mcod_fc1_fc2_corrected/FC1_FC2_camera/images/8a671b8ebe75af59690ef8f6db96ee72.png

    Args:
        path: The path to be split.

    Returns:
        str: The part of the path after the 'INPUT_x_x_x_x' section, or the original path if
        the 'INPUT_x_x_x_x' section is not found.
    """
    match = re.search(r"INPUT_[^/]+", path)
    if match:
        split_index = match.end()
        return path[split_index + 1 :]

    return path


def construct_absolute_cache_path(
    image_file_list: list[str] | Series,  # type: ignore[type-arg]
    label_file_list: list[str] | Series,  # type: ignore[type-arg]
    environment: Environment,
    *,
    multiview: bool = True,
) -> tuple[list[dict[str, str]] | list[str], list[str]]:
    """Constructs the absolute cache path for image and label files.

    This needs to be done since the csv datasets generally hold relative paths for the labels and images.

    For extract classifier patches from background predictions (detector false positives), the columns of the same csv
    dataset point to different data storages. The function can also handle this.

    Args:
        image_file_list: List of relative image file paths.
        label_file_list: List of relative label file paths.
        environment: Environment object that holds the base paths for images and labels.
        multiview: Boolean flag to indicate if the dataset is trifocal. Set to True if it is a trifocal dataset.

    Returns:
        A tuple containing the image file list and label file list with absolute cache paths.
    """
    assert len(label_file_list), "Label file list is empty."
    assert len(image_file_list), "Image file list is empty."
    assert len(image_file_list) == len(label_file_list), "Image and label count mismatch."

    if (
        multiview
        and isinstance(image_file_list, pd.DataFrame)
        and not os.path.isabs(image_file_list[image_file_list.columns[0]].iloc[0])
    ):
        image_file_dict_list = cast(list[dict[str, str]], image_file_list.to_dict(orient="records"))
        image_file_list_absolute: list[dict[str, str]] | list[str] = [
            {
                view: os.path.join(
                    environment.detector_csv_image_base_path,
                    os.path.normpath(remove_mount_location_from_path(image_file_dict[view])),
                )
                for view in image_file_dict
            }
            for image_file_dict in image_file_dict_list
        ]
    elif not os.path.isabs(image_file_list[0]):
        image_file_list = cast(list[str], image_file_list)
        image_file_list_absolute = [
            os.path.join(environment.detector_csv_image_base_path, os.path.normpath(remove_mount_location_from_path(i)))
            for i in image_file_list
        ]
    if not os.path.isabs(label_file_list[0]):
        label_file_list = cast(list[str], label_file_list)
        label_file_list_absolute: list[str] = [
            os.path.join(environment.detector_csv_label_base_path, os.path.normpath(remove_mount_location_from_path(i)))
            for i in label_file_list
        ]
    return image_file_list_absolute, label_file_list_absolute


def load_csv_table(
    csv_data_path: str, col_name_labels: str, col_name_images: str, environment: Environment, *, multiview: bool = True
) -> tuple[dict[str, list[dict[str, str]] | list[str]], dict[str, list[str]]]:
    """Reads images and labels from provided CSV table.

    Args:
        csv_data_path: path to CSV table file.
        col_name_labels: column name for labels.
        col_name_images: column name for images.
        environment: Environment variable holding command line arguments.
        multiview: Boolean flag to indicate if the dataset is trifocal. Set to True if it is a trifocal dataset.

    Returns:
        Dict for the image paths of each subset
        Dict for the label paths of each subset
    """

    # read CSV file
    csv_data = pd.read_csv(csv_data_path, sep=",", dtype="string", header=0)

    # drop all rows without a traffic sign label
    if col_name_labels in csv_data:
        csv_data.dropna(subset=[col_name_labels], inplace=True)

    # remove not good for training
    if "good_for_training" in csv_data:
        csv_data.drop(csv_data.index[(csv_data["good_for_training"] == "no")], axis=0, inplace=True)

    if "split" not in csv_data:
        msg = "The split column is not present in the CSV table. Check if the column has been renamed."
        raise ValueError(msg)

    if multiview:
        train_image_file_list: list[str] | Series = csv_data[csv_data["split"] == "train"][col_name_images]  # type: ignore[type-arg]
        val_image_file_list: list[str] | Series = csv_data[csv_data["split"] == "val"][col_name_images]  # type: ignore[type-arg]
        test_image_file_list: list[str] | Series = csv_data[csv_data["split"] == "test"][col_name_images]  # type: ignore[type-arg]
    else:
        train_image_file_list = csv_data[csv_data["split"] == "train"][col_name_images].to_list()
        val_image_file_list = csv_data[csv_data["split"] == "val"][col_name_images].to_list()
        test_image_file_list = csv_data[csv_data["split"] == "test"][col_name_images].to_list()

    train_label_file_list = csv_data[csv_data["split"] == "train"][col_name_labels].to_list()
    val_label_file_list = csv_data[csv_data["split"] == "val"][col_name_labels].to_list()
    test_label_file_list = csv_data[csv_data["split"] == "test"][col_name_labels].to_list()

    assert len(train_image_file_list) == len(train_label_file_list), "Train image and label count mismatch."
    assert len(val_image_file_list) == len(val_label_file_list), "Val image and label count mismatch."
    assert len(test_image_file_list) == len(test_label_file_list), "Test image and label count mismatch."

    # relative path --> absolute path
    train_image_files, train_label_files = (
        construct_absolute_cache_path(train_image_file_list, train_label_file_list, environment, multiview=multiview)
        if len(train_image_file_list) > 0
        else ([], [])
    )
    val_image_files, val_label_files = (
        construct_absolute_cache_path(val_image_file_list, val_label_file_list, environment, multiview=multiview)
        if len(val_image_file_list) > 0
        else ([], [])
    )
    test_image_files, test_label_files = (
        construct_absolute_cache_path(test_image_file_list, test_label_file_list, environment, multiview=multiview)
        if len(test_image_file_list) > 0
        else ([], [])
    )

    subset_image_file = {"train": train_image_files, "val": val_image_files, "test": test_image_files}
    subset_label_file = {"train": train_label_files, "val": val_label_files, "test": test_label_files}
    return subset_image_file, subset_label_file


def get_class_count(label_dataset: list[str]) -> dict[str, int]:
    """Count number of classes based on class ID.

    Args:
        label_dataset: label dataset dictionary.

    Returns:
        Dictionary containing the class count of each class.
    """
    class_count = {}
    for label_key in label_dataset:
        class_id = label_key.split("class_")[-1].split(os.sep)[0]

        if class_id not in class_count:
            class_count[class_id] = 1
        else:
            class_count[class_id] += 1
    return class_count


def read_label_files(label_meta_data_list: list[tuple[str, dict[str, Any]]]) -> dict[tuple[str, str], dict[str, Any]]:
    """Read all the label files from the list and store in a dictionary.

        Tuple of track id and sample filename is a key and the json label is stored as value.

    Args:
        label_meta_data_list: Dictionary containing the label track as key
        and label data as value
    """
    label_dataset = {}
    for label_meta_data in tqdm(label_meta_data_list):
        label_data = label_meta_data[1]
        folders = label_meta_data[0].split(os.path.sep)
        track_short = os.path.join(*folders[:-1])
        sample_short = folders[-1]
        label_dataset[(track_short, sample_short)] = label_data
    return label_dataset


def collect_image_label_paths(
    overall_config: dict[str, Any], environment: Environment
) -> tuple[dict[str, list[dict[str, str]] | list[str]], dict[str, list[str]]]:
    """Read images and labels from the given input directory or csv table.

    Args:
        overall_config: Configuration as a dictionary
        environment: Environment variable holding command line arguments

    Returns:
        Dict for the image paths of each subset
        Dict for the label paths of each subset.
    """
    csv_path = os.path.join(environment.input_folder, overall_config["input_config"]["csv_file_path"])
    subset_image_file, subset_label_file = load_csv_table(
        csv_path,
        overall_config["input_config"]["csv_col_name_labels"],
        overall_config["input_config"]["csv_col_name_images"],
        environment=environment,
        multiview=overall_config["input_config"].get("multiview"),
    )

    return subset_image_file, subset_label_file
