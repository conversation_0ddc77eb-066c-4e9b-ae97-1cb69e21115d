"""Stage that publishes the robustness evaluation results."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import json
import logging
import os
import sys
from collections.abc import Sequence
from typing import Any

from xcommon.logging.logging import configure_general_logging
from xcommon.publishing.publishing import load_metrics_of_folder
from xtension.azure_ct import azureml_publishing
from xtension.parameter.environment import parse_cli_arguments
from xtension.parameter.model_config import parse_model_config_arguments
from xusecases.vision.models.pace_general.release import model_configs
from xusecases.vision.models.pace_general.release.configuration.model_configs import ModelConfig
from xusecases.vision.tasks.semseg_god.task import SemsegGodTask
from xusecases.vision.tasks.traffic_light.task import TrafficLightTask
from xusecases.vision.tasks.traffic_sign.task import TrafficSignTask
from xusecases.vision.tasks.vehicle.task import VehicleTask


def main(argv: list[str]) -> None:  # noqa: D103
    configure_general_logging(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # READ PARAMETERS FROM CONFIG AND COMMAND LINE
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--publish_folder",
        type=str,
        default=os.path.join("outputs", "publish"),
        help="Path to the folder that will store the published results.",
    )

    model_config: ModelConfig
    model_config, remaining_args = parse_model_config_arguments(argv, model_configs)
    environment, args = parse_cli_arguments(model_config.environments, remaining_args, parser=parser)

    azureml_publishing.copy_data(
        input_folder=environment.input_folder,
        output_folder=args.publish_folder,
    )

    logger.info("Loading metrics for all tasks.")
    metrics, metrics_version = load_metrics(environment.input_folder, model_config.enabled_tasks)

    logger.info(f"metrics_version: {metrics_version}")
    for key, value in metrics.items():
        logger.info(f"{key}: {value:f}")


def load_metrics(input_folder: str, enabled_tasks: Sequence[str]) -> tuple[dict[str, Any], int]:
    """Load the metrics of all threat models and the combined robustness metric.

    Args:
        input_folder: The publish folder containing an evaluation folder.
        enabled_tasks: A list of enabled tasks.

    Returns:
        Dict mapping metric name to value.
    """
    metrics_version = 3
    metrics = {}

    for eval_folder in ["evaluation"]:
        eval_path = os.path.join(input_folder, eval_folder)
        robustness_metric_names = _get_metric_dirs(eval_path)

        for robustness_metric_name in robustness_metric_names:
            metrics_path = os.path.join(eval_path, robustness_metric_name.lower())
            threat_models = _get_threat_models(metrics_path)

            for threat_model in threat_models:
                if threat_model == "combined":  # this folder contains the combined robustness metric
                    metrics.update(
                        _load_robustness_metrics_of_folder(
                            metrics_folder=os.path.join(metrics_path, threat_model),
                            metrics_prefix=f"{robustness_metric_name.lower()}/{threat_model}/",
                        )
                    )
                else:
                    metrics.update(
                        load_metrics_of_folder(
                            os.path.join(metrics_path, threat_model),
                            metrics_prefix=f"{robustness_metric_name.lower()}/{threat_model}/",
                            load_all=True,
                        )
                    )

    # Filter loaded metrics to avoid spamming the AzureML run
    metrics = {name: value for name, value in metrics.items() if _filter_metric_by_name(name, enabled_tasks)}

    return metrics, metrics_version


def _get_metric_dirs(eval_path: str) -> list[str]:
    """Get robustness evaluation metric directory names."""
    return next(os.walk(eval_path))[1]


def _get_threat_models(metrics_path: str) -> list[str]:
    """Get robustness evaluation threat model names."""
    return os.listdir(metrics_path)


def _load_robustness_metrics_of_folder(
    metrics_folder: str,
    metrics_prefix: str = "",
) -> dict[str, float]:
    """Load the metric values of the `robustness_combined.json` file found in `metrics_folder`.

    Args:
        metrics_folder: Folder path to load metrics from.
            Must contain `robustness_combined.json`.
        metrics_prefix: String to append to each metric name as prefix.

    Returns:
        The flattened metric values.
    """
    metrics_file_name = "robustness_combined.json"
    for dirpath, _, filenames in os.walk(metrics_folder):
        if metrics_file_name in filenames:
            with open(os.path.join(dirpath, metrics_file_name), encoding="utf-8") as infile:
                json_content = json.load(infile)
                break

    metrics = _flatten(json_content, prefix=metrics_prefix)
    return metrics


def _flatten(results: dict[str, Any], prefix: str | None = None) -> dict[str, Any]:
    """Flatten nested dicts by joining keys with `/`.

    Args:
        results: The dict to flatten.
        prefix: Optional prefix.

    Examples:
        >>> flatten({"a": {"b": 42, "c": 23}})
        {"a/b": 42, "a/c": 23}

        >>> flatten({"a": {"b": 42, "c": 23}}, prefix="xyz")
        {"xyz/a/b": 42, "xyz/a/c": 23}

    Returns:
        Flattened dict.
    """
    flattened_results = {}
    for key, value in results.items():
        next_prefix = key if prefix is None else os.path.join(prefix, key)
        if isinstance(value, dict):
            flattened_results.update(_flatten(value, next_prefix))
        else:
            flattened_results[next_prefix] = value

    return flattened_results


def _filter_metric_by_name(name: str, enabled_tasks: Sequence[str]) -> bool:
    """Check if metric should be published based on its name.

    Args:
        name: The name of the metric.
        enabled_tasks: A list of enabled tasks.

    Returns:
        True if the metric should be published, False otherwise.
    """
    allowed_postfixes = ["combined_value"]

    if TrafficLightTask.id in enabled_tasks:
        # pylint: disable=line-too-long
        allowed_postfixes += [
            "all.traffic_light_front_40px.working_point.recall@match_threshold=0.3",
            "all.traffic_light_front_3px.working_point.recall@match_threshold=0.3",
            "all.traffic_light_front_3px.attribute_confusion_matrix.confusion_matrices@match_threshold=0.3@correct#status",
            "all.traffic_light_front_8px.attribute_confusion_matrix.confusion_matrices@match_threshold=0.3@correct#inlay",
            "all.traffic_light_front_3px.attribute_confusion_matrix.confusion_matrices@match_threshold=0.3@"
            "correct#target_road_user",
        ]
    if TrafficSignTask.id in enabled_tasks:
        allowed_postfixes += [
            "all.traffic_sign_32px.roc_curves.miss_rate@match_threshold=0.3@fppf=1.0",
            "all.traffic_sign_32px.attribute_confusion_matrix.confusion_matrices@match_threshold=0.3@correct#shape",
            "all.traffic_sign_32px.attribute_confusion_matrix.confusion_matrices@match_threshold=0.3@correct#type",
        ]
    if VehicleTask.id in enabled_tasks:
        allowed_postfixes += [
            "all.PassengerCar.pr_curves.auc@0.7",
            "all.LargeVehicle.pr_curves.auc@0.7",
            "all.Bicycle.pr_curves.auc@0.7",
            "all.Motorbike.pr_curves.auc@0.7",
        ]
    if SemsegGodTask.id in enabled_tasks:
        allowed_postfixes += [
            "all.semseg_god.iou_evaluation.IoU_per_image_mean",
        ]
    allowed_postfixes += [
        # Safe-AI
        "all.SafeAiFreeSpace.iobbox.false_positive_rate@0.1250000",
        "all.SafeAiFreeSpace.iobbox.mean_free_space_overlap",
    ]
    has_allowed_postfix = name.split("/")[-1] in allowed_postfixes

    allowed_infixes = ["relative_performance_under_corruption"]
    has_allowed_infix = any(infix in name for infix in allowed_infixes)

    return has_allowed_infix or has_allowed_postfix


if __name__ == "__main__":
    main(sys.argv[1:])
