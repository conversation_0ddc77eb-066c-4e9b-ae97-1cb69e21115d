"""Contains the update_data script for the blockage task."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2021-2022 Robert <PERSON>. All rights reserved.
 Copyright (c) 2023-2025 Robert <PERSON> and Cariad SE. All rights reserved.
===================================================================================
"""

import copy
import json
from collections import defaultdict
from collections.abc import Sequence
from pathlib import Path
from typing import Any

from azure.core.credentials import TokenCredential

from azure_tools.credential import get_default_azure_credential
from xcontract.data.definitions.image import ImageType, RGBImage, YUV420Image, YUV420ImageMDM, YUV420ImageMPC4
from xtension.azure_ct.data_cache.converter.vision.blockage.label_converter import (
    Fc1BlockageLabelConverter,
    TvBlockageLabelConverter,
)
from xtension.azure_ct.data_cache.converter.vision.image.bitshift_converter import Image16To10BitsConverter
from xtension.azure_ct.data_cache.converter.vision.image.cropping_converter import (
    ImageFarCropConverter,
    ImageMidCropConverter,
)
from xtension.azure_ct.data_cache.converter.vision.image.cropping_converter import (
    ImageType as CroppingConverterImageType,
)
from xtension.azure_ct.data_cache.creator.vision.image.rgb import Yuv16ToRgb8bitJpegCreator
from xtension.azure_ct.data_cache.creator.vision.image.rgb_stitched import (
    Yuv16ToRgb8FarCropMidStitchingJpegCreator,
)
from xtension.azure_ct.data_cache.data_converter import DataConverter, IdentityDataConverter
from xtension.azure_ct.data_cache.data_creator import DataCreator
from xtension.azure_ct.data_cache.data_sources.data_source import DataSource
from xtension.azure_ct.data_cache.definitions import Usage
from xtension.azure_ct.data_cache.mdm_data.data_source import MdmDataSource, MdmDataSourceConfig, MdmSampleElementConfig
from xtension.azure_ct.data_cache.mdm_data.data_types import Image as ImageOnMDM
from xtension.azure_ct.data_cache.mdm_data.data_types import JsonLabel as JsonLabelOnMDM
from xtension.azure_ct.data_cache.mdm_data.data_types.data_type_on_mdm import DataTypeOnMDM
from xtension.azure_ct.data_cache.mdm_data.dataset import (
    DEFAULT_SAMPLE_ELEMENT_SHA_FIELDS,
    DEFAULT_SAMPLE_ELEMENT_URL_FIELDS,
    JsonFileMdmDataset,
)
from xusecases.vision.common.data.dataset_registration.dataset_registration import (
    ModelDataset,
    SpecialPurposeDatasetName,
)
from xusecases.vision.models.pace_general.release.dataset_registration.dataset_registration import (
    COMPRESSED_RGB_L0_WIDE_COLUMN_NAME,
    CameraSet,
    DatasetRegistration,
    FrameView,
)
from xusecases.vision.tasks.blockage.task import (
    BlockageConfig,
    BlockageConfigTV,
    BlockageTask,
    BlockageTaskTopView,
    BlockageTrifocalTask,
)

FC1_PREFIX = Path("mdm/fc1/v6")
TV_PREFIX = Path("mdm/tv/v4")

META_DATA_FIELDS = {
    "frame_number": "frame_number",
    "image_sha": "image_sha",
    "label_sha": "label_sha",
    "label_spec": "label_spec",
    "image_id": "image_id",
    "supplier": "supplier",
    "image_type": "image_type",
    "image_file_url": "image_file_url",
    "label_file_url": "label_file_url",
    "dataset_name": "dataset_name",
}

META_DATA_FIELDS_TRIFOCAL = copy.deepcopy(META_DATA_FIELDS)
META_DATA_FIELDS_TRIFOCAL.pop("image_type")

YUV_CONFIG_FC1 = MdmDataSourceConfig.with_converters(
    data_converter=Image16To10BitsConverter(),
    label_converter=Fc1BlockageLabelConverter(
        project="alliance",
        view="wide",
        config_name=BlockageConfig.config_name,
        config_version=BlockageConfig.config_version,
    ),
    meta_data_fields=META_DATA_FIELDS,
    image_column_name=YUV420Image.input_data_id,
    store_compressed_rgb=True,
)

YUV_CONFIG_MPC4_FC1 = MdmDataSourceConfig.with_converters(
    data_converter=IdentityDataConverter(),
    label_converter=Fc1BlockageLabelConverter(
        project="alliance",
        view="wide",
        config_name=BlockageConfig.config_name,
        config_version=BlockageConfig.config_version,
    ),
    meta_data_fields=META_DATA_FIELDS,
    image_column_name=YUV420ImageMPC4.input_data_id,
    source_image_type=YUV420ImageMPC4,
    store_compressed_rgb=True,
)

YUV_CONFIG_FC1_TRIFOCAL_MID = MdmDataSourceConfig.with_converters(
    data_converter=Image16To10BitsConverter(),
    label_converter=Fc1BlockageLabelConverter(
        project="alliance",
        view="wide_trifocal",
        config_name=BlockageConfig.config_name,
        config_version=BlockageConfig.config_version,
    ),
    meta_data_fields=META_DATA_FIELDS,
    image_column_name=YUV420Image.input_data_id,
    store_compressed_rgb=True,
)

YUV_CONFIG_TV = MdmDataSourceConfig.with_converters(
    data_converter=Image16To10BitsConverter(),
    label_converter=TvBlockageLabelConverter(
        config_name=BlockageConfigTV.config_name,
        config_version=BlockageConfigTV.config_version,
    ),
    meta_data_fields=META_DATA_FIELDS,
    image_column_name=YUV420Image.input_data_id,
    store_compressed_rgb=True,
)


# config for FC1 and TV
YUV_CONFIG_CALIBRATION = MdmDataSourceConfig(
    data=MdmSampleElementConfig(
        "image", ImageOnMDM, converter=Image16To10BitsConverter(), dataset_column_name=YUV420Image.input_data_id
    ),
    label=None,
    meta_data_fields={},
    creators=[Yuv16ToRgb8bitJpegCreator("compressed_rgb", "image", "compressed_rgb")],
)

# The keys, e.g., 'yuv_tv_view', are the image types in the dataset JSON file.
MDM_DATASET_CONFIGS = {
    CameraSet.FC1: {
        YUV420Image: {
            "yuv_tri_mid_view": YUV_CONFIG_FC1_TRIFOCAL_MID,
            "yuv_bi_wide_view": YUV_CONFIG_FC1,
        },
        YUV420ImageMPC4: {"yuv_bi_wide_view": YUV_CONFIG_MPC4_FC1},
    },
    CameraSet.TV: {
        YUV420Image: {
            "yuv_tv_view": YUV_CONFIG_TV,
        },
    },
}


class RegisterBlockage(DatasetRegistration):
    """Register the blockage dataset."""

    def __init__(
        self,
        traininglake_path: str,
        task_id: str = BlockageTask.id,
        dataset_variant: ModelDataset = ModelDataset.GENERAL,
        cam: CameraSet = CameraSet.FC1,
        dataset_image_type: ImageType = RGBImage,
        frame_views: set[FrameView] | None = None,
        special_purpose_dataset: SpecialPurposeDatasetName | None = None,
        far_to_wide_view_offset_hw: tuple[float, float] | None = None,
    ) -> None:
        """Initialize the pole dataset registration.

        Args:
            traininglake_path: The path to the pole training lake. If None, it will be set based on the environment.
            task_id: The task ID.
            dataset_variant: The variant of the model dataset.
            cam: The camera set.
            dataset_image_type: Type of images used in the dataset. Defaults to RGBImage.
            frame_views: Set of views to generate the dataset for.
            special_purpose_dataset: The special purpose dataset name.
            far_to_wide_view_offset_hw: Offsets for the feature maps in the trifocal model. Defaults to None.
        """
        super().__init__(
            traininglake_path, task_id, dataset_variant, cam, dataset_image_type, frame_views, special_purpose_dataset
        )
        self.far_to_wide_view_offset_hw = far_to_wide_view_offset_hw

    def dataset_variants(self) -> dict[ModelDataset, str]:
        """Create a dictionary of dataset variants for the blockage task."""
        return super().dataset_variants()

    @property
    def dataset_name(self) -> str:
        """Return the dataset name for the given dataset variant."""
        ds_variants = self.dataset_variants()
        dataset_name = ds_variants[self.dataset_variant]
        if any(v == FrameView.TRIFOCAL for v in self.frame_views):
            dataset_name += "_trifocal"
        return dataset_name

    def get_calib_data_sources(self) -> Sequence[DataSource]:
        """List of calibration data sources to register in dataset."""
        assert self.dataset_image_type == YUV420Image, "Only YUV420Image is supported for calibration data sources."
        credential = get_default_azure_credential()
        if self.frame_views != set() and any(v == FrameView.TRIFOCAL for v in self.frame_views):
            return get_trifocal_calibration_data_source(self.traininglake_path, credential)
        return get_blockage_calib_mdm_data_sources(self.traininglake_path, self.cam, credential)

    def data_sources(self) -> Sequence[DataSource]:
        """List of data sources to register in dataset."""
        data_sources: Sequence[DataSource] = []
        if self.frame_views != set() and any(v == FrameView.TRIFOCAL for v in self.frame_views):
            assert self.dataset_image_type == YUV420Image, "Only YUV420Image is supported for trifocal model."
            assert self.far_to_wide_view_offset_hw is not None, "Offset must be provided for trifocal model."
            data_sources = get_trifocal_data_sources(
                self.traininglake_path, self.dataset_variant, self.far_to_wide_view_offset_hw
            )
        else:
            data_sources = get_blockage_mdm_data_sources(
                self.traininglake_path, self.dataset_variant, self.cam, self.dataset_image_type
            )
        return data_sources


def get_trifocal_data_sources(
    traininglake_path: str, dataset_variant: ModelDataset, far_to_wide_view_offset_hw: tuple[float, float]
) -> list[MdmDataSource]:
    """Get the MDM-based data sources for the trifocal model."""
    trifocal_json_files: dict[ModelDataset, list[tuple[Usage, Path]]] = {
        ModelDataset.GENERAL: [],
        ModelDataset.CALIBRATION: [],
        ModelDataset.REF_ROUTES: [],
        ModelDataset.TINY_VAL: [],
    }

    data_sources: list[MdmDataSource] = []
    credential = get_default_azure_credential()

    trifocal_json_files[ModelDataset.GENERAL].append(
        (Usage.TRAINING, FC1_PREFIX / "v6_yuv_trifocal_blockage_mdm_dataset_FC1_train_20250714.json")
    )
    trifocal_json_files[ModelDataset.GENERAL].append(
        (Usage.VALIDATION, FC1_PREFIX / "v6_yuv_trifocal_blockage_mdm_dataset_FC1_val_20250714.json")
    )
    trifocal_json_files[ModelDataset.GENERAL].append(
        (Usage.TEST, FC1_PREFIX / "v6_yuv_trifocal_blockage_mdm_dataset_FC1_test_20250714.json")
    )

    trifocal_json_files[ModelDataset.TINY_VAL].append(
        (Usage.VALIDATION, FC1_PREFIX / "v6_yuv_trifocal_blockage_mdm_dataset_FC1_tiny_val_20250714.json")
    )

    for usage, path in trifocal_json_files[dataset_variant]:
        data_source = _get_mdm_data_source_multiview(
            Path(traininglake_path) / path,
            usage,
            credential,
            far_to_wide_view_offset_hw,
        )
        data_sources.append(data_source)
    return data_sources


def get_blockage_mdm_data_sources(  # noqa: D417
    traininglake_path: str, dataset_variant: ModelDataset, camera: CameraSet, dataset_image_type: ImageType
) -> list[MdmDataSource]:
    """Get the MDM-based data sources based on a dataset_index.json located on the blob storage.

    Args:
        traininglake_path: Path to the base folder of the traininglake.
        dataset_variant: Gathering data sources for the given dataset variant.
        cam: Dataset name depending on camera set.
        dataset_image_type: Type of images used in the dataset.

    Returns:
        List of data sources.
    """

    data_sources: list[MdmDataSource] = []
    credential = get_default_azure_credential()

    mdm_data_sources: dict[ImageType, dict[ModelDataset, dict[CameraSet, list[tuple[Usage, Path]]]]] = {
        YUV420Image: {
            ModelDataset.GENERAL: {},
            ModelDataset.CALIBRATION: {},
            ModelDataset.REF_ROUTES: {CameraSet.FC1: [], CameraSet.TV: []},
            ModelDataset.TINY_VAL: {CameraSet.FC1: [], CameraSet.TV: []},
        },
        YUV420ImageMPC4: {
            ModelDataset.GENERAL: {CameraSet.FC1: []},
            ModelDataset.CALIBRATION: {CameraSet.FC1: []},
            ModelDataset.REF_ROUTES: {CameraSet.FC1: []},
            ModelDataset.TINY_VAL: {CameraSet.FC1: []},
        },
    }

    # YUV PaceGeneral FC1
    mdm_data_sources[YUV420Image][ModelDataset.GENERAL][CameraSet.FC1] = [
        # Native YUV
        (Usage.TRAINING, FC1_PREFIX / "v6_yuv_blockage_mdm_dataset_FC1_train_20250714.json"),
        (Usage.VALIDATION, FC1_PREFIX / "v6_yuv_blockage_mdm_dataset_FC1_val_20250714.json"),
        (Usage.TEST, FC1_PREFIX / "v6_yuv_blockage_mdm_dataset_FC1_test_20250714.json"),
    ]

    # YUV MPC4 Dataset
    mdm_data_sources[YUV420ImageMPC4][ModelDataset.GENERAL][CameraSet.FC1] = [
        # Native YUV
        (Usage.TRAINING, Path("mdm/fc1/mpc4") / "blockage_mpc4_yuv_train_only_dataset.json"),
    ]

    # YUV PaceGeneral TV
    mdm_data_sources[YUV420Image][ModelDataset.GENERAL][CameraSet.TV] = [
        # Native YUV
        (Usage.TRAINING, TV_PREFIX / "v4_yuv_blockage_mdm_dataset_TV_train_20250714.json"),
        (Usage.VALIDATION, TV_PREFIX / "v4_yuv_blockage_mdm_dataset_TV_val_20250714.json"),
        (Usage.TEST, TV_PREFIX / "v4_yuv_blockage_mdm_dataset_TV_test_20250714.json"),
    ]

    # YUV TinyVal TV
    mdm_data_sources[YUV420Image][ModelDataset.TINY_VAL][CameraSet.TV] = [
        # Native YUV
        (Usage.VALIDATION, TV_PREFIX / "v4_yuv_blockage_mdm_dataset_TV_tiny_val_20250714.json"),
    ]

    # YUV TinyVal FC1
    mdm_data_sources[YUV420Image][ModelDataset.TINY_VAL][CameraSet.FC1] = [
        (Usage.VALIDATION, FC1_PREFIX / "v6_yuv_blockage_mdm_dataset_FC1_tiny_val_20250714.json"),
    ]

    for usage, path in mdm_data_sources[dataset_image_type][dataset_variant][camera]:
        task_id = BlockageTaskTopView.id if camera == CameraSet.TV else BlockageTask.id
        configs, mdm_datasets = create_mdm_datasets(Path(traininglake_path) / path, dataset_image_type, camera)

        for config, mdm_dataset in zip(configs, mdm_datasets):
            data_source = MdmDataSource(
                dataset_name="blockage",
                data_source_name=path.stem,
                usage=usage,
                mdm_dataset=mdm_dataset,
                credential=credential,
                config=config,
                task_id=task_id,
            )
            data_sources.append(data_source)
    return data_sources


def get_blockage_calib_mdm_data_sources(
    traininglake_path: str, camera: CameraSet, credential: TokenCredential
) -> list[DataSource]:
    """Get the YUV calibration images for the blockage task."""
    task_id = BlockageTaskTopView.id if camera == CameraSet.TV else BlockageTask.id
    path_prefix = FC1_PREFIX if camera == CameraSet.FC1 else TV_PREFIX
    fc1_calibration_path = path_prefix / "v6_yuv_blockage_mdm_dataset_FC1_calibration_20250714.json"
    tv_calibration_path = path_prefix / "v4_yuv_blockage_mdm_dataset_TV_calibration_20250714.json"

    path = fc1_calibration_path if camera == CameraSet.FC1 else tv_calibration_path
    config = YUV_CONFIG_CALIBRATION

    data_source = MdmDataSource(
        dataset_name="blockage",
        data_source_name=path.stem,
        usage=Usage.CALIBRATION,
        mdm_dataset=JsonFileMdmDataset.from_json_dataset_file(
            f"{traininglake_path}/{path}",
            sample_element_sha_fields={"image": "image_sha"},
            sample_element_url_fields={"image": "image_file_url"},
            meta_data_fields=None,
        ),
        credential=credential,
        config=config,
        task_id=task_id,
    )
    return [data_source]


def create_mdm_datasets(
    json_path: Path, dataset_image_type: ImageType, camera: CameraSet
) -> tuple[list[MdmDataSourceConfig], list[JsonFileMdmDataset]]:
    """Create MDM datasets from a JSON file.

    The JSON file contains a list of samples of different image types, thus the samples are split into different
    MDMDatasets based on the image type.
    """
    configs = []
    mdm_datasets = []

    mdm_dataset_sample_lists: dict[tuple[CameraSet, ImageType, str], list[dict[str, Any]]] = defaultdict(list)

    with json_path.open("r", encoding="utf-8") as file:
        json_dataset_content = json.load(file)

    for sample_entry in json_dataset_content:
        entry_image_type = sample_entry["image_type"]
        mdm_dataset_sample_lists[(camera, dataset_image_type, entry_image_type)].append(sample_entry)

    for (camera_, dataset_image_type_, entry_image_type), samples in mdm_dataset_sample_lists.items():
        config = MDM_DATASET_CONFIGS[camera_][dataset_image_type_][entry_image_type]
        mdm_dataset = JsonFileMdmDataset.from_json_dataset_content(
            samples,
            sample_element_sha_fields=DEFAULT_SAMPLE_ELEMENT_SHA_FIELDS,
            sample_element_url_fields=DEFAULT_SAMPLE_ELEMENT_URL_FIELDS,
            meta_data_fields=list(config.meta_data_fields.keys()),
        )
        configs.append(config)
        mdm_datasets.append(mdm_dataset)
    return configs, mdm_datasets


def _get_mdm_data_source_multiview(
    json_path: Path,
    usage: Usage,
    credential: TokenCredential,
    far_to_wide_view_offset_hw: tuple[float, float] | None = None,
) -> MdmDataSource:
    """Get the MDM-based data source based on a dataset_index.json located on the blob storage.

    This index file provides the shas on mdm, created by xtension.azure_ct.data_cache.mdm_data.prototype.mdm_dataset.py.

    Args:
        json_path: Path to the JSON file containing the dataset.
        usage: The split the dataset is used for.
        credential: Azure credential.
        far_to_wide_view_offset_hw: Offset between far and wide view. Defaults to None.

    Returns:
        A list of MDM Data sources.
    """
    views = ["far", "mid", "wide"]
    dataset_column_names = {f"image_{v}": f"{YUV420ImageMDM.input_data_id}_{v}" for v in views}
    dataset_column_names["label"] = BlockageTask.column_name

    data_source_name = json_path.stem

    # The same image converters can be used
    data_converters: dict[str, DataConverter] = {
        "image_mid": ImageMidCropConverter(
            project="alliance",
            image_type=CroppingConverterImageType.YUV420,
            apply_6bit_shift=True,
        ),
        "image_far": ImageFarCropConverter(
            project="alliance",
            image_type=CroppingConverterImageType.YUV420,
            apply_6bit_shift=True,
        ),
        "image_wide": Image16To10BitsConverter(),
    }
    data_creators: list[DataCreator] = [
        Yuv16ToRgb8bitJpegCreator(
            element_name="compressed_rgb_wide",
            source_element_name="image_wide",
            dataset_column_name="compressed_rgb_wide",
            data_converter=IdentityDataConverter(),
        ),
        Yuv16ToRgb8bitJpegCreator(
            element_name="compressed_rgb_mid",
            source_element_name="image_mid",
            dataset_column_name="compressed_rgb_mid",
            data_converter=ImageMidCropConverter(
                project="alliance",
                image_type=CroppingConverterImageType.RGB,
                apply_6bit_shift=False,
            ),
        ),
        Yuv16ToRgb8FarCropMidStitchingJpegCreator(
            element_name="compressed_rgb_l0_wide",
            source_element_mid_name="image_mid",
            source_element_far_name="image_far",
            dataset_column_name=COMPRESSED_RGB_L0_WIDE_COLUMN_NAME,
            far_to_wide_view_offset_hw=far_to_wide_view_offset_hw,
        ),
        Yuv16ToRgb8bitJpegCreator(
            element_name="compressed_rgb_far",
            source_element_name="image_far",
            dataset_column_name="compressed_rgb_far",
            data_converter=ImageFarCropConverter(
                project="alliance",
                image_type=CroppingConverterImageType.RGB,
                apply_6bit_shift=False,
            ),
        ),
    ]

    created_element_names = [creator.element_name for creator in data_creators]
    assert all(f"image_{view}" in data_converters or f"image_{view}" in created_element_names for view in views), (
        "All views must be present in the converters or creators!"
    )

    data_converters["label"] = Fc1BlockageLabelConverter(
        project="alliance",
        view="wide_trifocal",  # L1 trifocal wide crop aka 1280x2304
        config_name=BlockageConfig.config_name,
        config_version=BlockageConfig.config_version,
    )

    data_types: dict[str, type[DataTypeOnMDM]] = {f"image_{v}": ImageOnMDM for v in views}
    data_types["label"] = JsonLabelOnMDM

    data_source_config = MdmDataSourceConfig.with_multiview_converters(
        data_converters=data_converters,
        dataset_column_names=dataset_column_names,
        data_types=data_types,
        meta_data_fields=META_DATA_FIELDS_TRIFOCAL,
        main_element="image_mid",
        creators=data_creators,
    )
    data_source = MdmDataSource(
        dataset_name=BlockageTrifocalTask.column_name,  # this is the column name in the CSV dataset file
        data_source_name=data_source_name,
        usage=usage,
        mdm_dataset=JsonFileMdmDataset.from_json_dataset_file(
            str(json_path.resolve()),
            meta_data_fields=META_DATA_FIELDS_TRIFOCAL,
        ),
        credential=credential,
        config=data_source_config,
    )
    return data_source


def get_trifocal_calibration_data_source(traininglake_path: str, credential: TokenCredential) -> list[DataSource]:
    """Get the trifocal calibration data source."""
    json_path = (
        Path(traininglake_path) / FC1_PREFIX / "v6_yuv_trifocal_blockage_mdm_dataset_FC1_calibration_20250714.json"
    )
    views = ["far", "mid", "wide"]
    dataset_column_names = {f"image_{v}": f"{YUV420ImageMDM.input_data_id}_{v}" for v in views}

    data_source_name = json_path.stem

    # The same image converters can be used
    data_converters: dict[str, DataConverter] = {
        "image_mid": ImageMidCropConverter(
            project="alliance",
            image_type=CroppingConverterImageType.YUV420,
            apply_6bit_shift=True,
        ),
        "image_far": ImageFarCropConverter(
            project="alliance",
            image_type=CroppingConverterImageType.YUV420,
            apply_6bit_shift=True,
        ),
        "image_wide": Image16To10BitsConverter(),
    }

    data_types: dict[str, type[DataTypeOnMDM]] = {f"image_{v}": ImageOnMDM for v in views}

    data_source_config = MdmDataSourceConfig.with_multiview_converters(
        data_converters=data_converters,
        dataset_column_names=dataset_column_names,
        data_types=data_types,
        meta_data_fields={},
        main_element="image_mid",
    )
    data_source = MdmDataSource(
        dataset_name=BlockageTrifocalTask.column_name,
        data_source_name=data_source_name,
        usage=Usage.CALIBRATION,
        mdm_dataset=JsonFileMdmDataset.from_json_dataset_file(
            str(json_path.resolve()),
            sample_element_sha_fields={"image": "image_sha"},
            sample_element_url_fields={"image": "image_file_url"},
        ),
        credential=credential,
        config=data_source_config,
    )
    return [data_source]
