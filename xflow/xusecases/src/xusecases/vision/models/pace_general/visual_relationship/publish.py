"""This module is the entry point for the publishing step of the pace_general release usecase."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
import os
import sys
from typing import Any

from xcommon.logging.logging import configure_general_logging
from xcommon.publishing.publishing import load_metrics_of_folder, load_tensorrt_model_timings
from xcontract.data.definitions.usage import Usage
from xtension.azure_ct import azureml_publishing
from xtension.parameter.environment import parse_cli_arguments
from xtension.parameter.model_config import parse_model_config_arguments
from xusecases.vision.models.pace_general.visual_relationship import model_configs


def main(argv: list[str]) -> None:
    """Main function for publishing the visual relationship model."""
    configure_general_logging(level=logging.INFO)
    logger = logging.getLogger(__name__)

    # READ PARAMETERS FROM CONFIG AND COMMAND LINE
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--publish_to_model_store",
        action="store_true",
        help="Whether or not to save the model and metrics to the model store.",
    )
    parser.add_argument(
        "--publish_folder",
        type=str,
        default=os.path.join("outputs", "publish"),
        help="Path to the folder that will store the published results.",
    )
    model_config, remaining_args = parse_model_config_arguments(argv, model_configs)
    environment, args = parse_cli_arguments(model_config.environments, remaining_args, parser=parser)

    logger.info("Loading metrics for all tasks.")
    metrics, metrics_version = load_metrics(environment.input_folder)

    # Get the TensorRT model timings and report the total time
    timings = load_tensorrt_model_timings(environment.input_folder)
    metrics.update(timings)

    logger.info(f"metrics_version: {metrics_version}")
    for key, value in metrics.items():
        logger.info(f"{key}: {value:f}")

    azureml_publishing.copy_data(
        input_folder=environment.input_folder,
        output_folder=args.publish_folder,
    )

    if args.publish_to_model_store:
        logger.warning("Publishing to the model store is deactivated for the deprecated usecase.")


def load_metrics(input_folder: str) -> tuple[dict[str, Any], int]:
    """Load the combined metric value of all tasks."""
    metrics_version = 3
    metrics = {}

    for eval_folder in ["evaluation", "evaluation_safe_ai"]:
        for usage in [Usage.VALIDATION, Usage.TEST]:
            metrics_folder = os.path.join(input_folder, eval_folder, "offline", usage.name.lower())
            metrics.update(
                load_metrics_of_folder(metrics_folder, metrics_prefix=f"offline/{usage.name.lower()}/", load_all=True)
            )

    # Publish just some of the loaded metrics to avoid spamming the AzureML run
    metrics_to_publish = [
        "combined_value",
    ]
    metrics = {name: value for name, value in metrics.items() if name.split("/")[-1] in metrics_to_publish}

    return metrics, metrics_version


if __name__ == "__main__":
    main(sys.argv[1:])
