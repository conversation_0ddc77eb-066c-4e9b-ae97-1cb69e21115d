"""Entry point for the publish stage of the lidar object_detection usecase."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import argparse
import logging
import os
import sys

from xcommon.logging import configure_general_logging, log_configuration, log_environment
from xtension.azure_ct import azureml_publishing
from xtension.parameter.environment import parse_cli_arguments
from xusecases.lidar.common.azure_pipeline.output_folders_factory import (
    ConvertStageOutput,
    EvaluateStageOutput,
    SelectStageOutput,
    Stage,
    StageOutput,
    get_stage_output,
)
from xusecases.lidar.common.utils.model_config_parser import parse_model_config_parameters
from xusecases.lidar.models.object_detection import model_configs
from xusecases.lidar.models.object_detection.configuration.common_model_config import update_environment_for_short_run

configure_general_logging(level=logging.INFO)
_LOGGER = logging.getLogger(__name__)


def _parse_input_folders(argv: list[str]) -> tuple[list[str], list[str]]:
    """Parse input folders from command line arguments.

    Extra argument parser to grab "input_folder" before it is read by "parse_cli_arguments", to allow giving multiple
    input folders to this stage.

    Args:
        argv: Command line arguments
    Returns:
        input_folders: List of input folders
        remaining_args: Remaining command line arguments
    """

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--input_folder",
        type=str,
        nargs="*",
        help=(
            "Input folder where the stage outputs are stored. One can also specify multiple folders. "
            "In that case, all files are copied together into a single folder during the publishing step."
        ),
    )
    args, remaining_args = parser.parse_known_args(argv)
    input_folders = args.input_folder
    assert len(input_folders) > 0
    return input_folders, remaining_args


def main(argv: list[str]) -> None:
    """Lidar ObjectDetection experiment publish stage."""

    log_environment()
    input_folders, remaining_args = _parse_input_folders(argv)

    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--publish_to_model_store",
        action="store_true",
        help="Whether or not to save the model and metrics to the model store.",
    )
    parser.add_argument(
        "--publish_folder",
        type=str,
        default=os.path.join("outputs", "publish"),
        help="Path to the folder that will store the published results.",
    )

    model_config_module, remaining_args = parse_model_config_parameters(remaining_args, model_configs)
    environment, args = parse_cli_arguments(model_config_module.ENVIRONMENTS, remaining_args, parser=parser)
    if environment.short_ct_run:
        update_environment_for_short_run(environment)

    # ====================================
    # This is a workaround to make the stage work without having to unnecessarily mount the data storages.
    # The publish stage itself does not need the "AZUREML_DATAREFERENCE_ingest_datasets" (mdm_dataset_dir) and
    # "AZUREML_DATAREFERENCE_ingest_cache" (training_cache_dir) storages to be mounted. However, by forwarding the
    # environment to the parameters and accessing the specific variables, the placeholder resolving mechansim for the
    # environment variables will be triggered and fail since the storages are not mounted (env variables are not set).
    # This workaround solves the issue by overwriting the variables with dummy values as they are not used anyhow.
    # FIXME: The configuration needs a bigger refactoring, especially the duplication of parameters is a problem.  # noqa: E501, TD001, TD003
    environment.mdm_dataset_dir = ""
    environment.training_cache_dir = ""
    # ===================================

    params = model_config_module.ModelConfig(environment)
    params.assemble()
    log_configuration(
        name=__file__, model_config=model_config_module, environment=environment, args=args, params=params
    )

    # Copy all input folders into a single folder
    for input_folder in input_folders:
        azureml_publishing.copy_data(input_folder=input_folder, output_folder=args.publish_folder)

    select_stage_output = get_stage_output(Stage.SELECT, output_base_folder=args.publish_folder)
    evaluate_stage_output = get_stage_output(Stage.EVALUATE, output_base_folder=args.publish_folder)
    convert_stage_output = get_stage_output(Stage.CONVERT, output_base_folder=args.publish_folder)
    assert isinstance(select_stage_output, SelectStageOutput)
    assert isinstance(evaluate_stage_output, EvaluateStageOutput)
    assert isinstance(convert_stage_output, ConvertStageOutput)

    evaluate_onnx_stage_output: StageOutput | None = get_stage_output(
        Stage.EVALUATE_ONNX, output_base_folder=args.publish_folder
    )
    assert isinstance(evaluate_onnx_stage_output, EvaluateStageOutput)
    if not os.path.isdir(evaluate_onnx_stage_output.evaluation):
        evaluate_onnx_stage_output = None

    if args.publish_to_model_store:
        _LOGGER.warning("Publishing to the model store is deactivated for the deprecated usecase.")


if __name__ == "__main__":
    main(sys.argv[1:])
