# xFlow - ADA Deep Learning Framework

![xtorch](logo.png)

[![xFlow gatekeeper](https://github.com/PACE-INT/xflow/actions/workflows/gatekeeper.yml/badge.svg)](https://github.com/PACE-INT/xflow/actions/workflows/gatekeeper.yml) [![Coverage](https://sonarqube.aadpace.com/api/project_badges/measure?project=PACE-INT_xflow_AYYJMvSuG9zxVnyvlTzz&metric=coverage&token=sqb_2e56e3f3bda244712ae1f46c6e78e1cff3ecb167)](https://sonarqube.aadpace.com/dashboard?id=PACE-INT_xflow_AYYJMvSuG9zxVnyvlTzz) [![Maintainability Rating](https://sonarqube.aadpace.com/api/project_badges/measure?project=PACE-INT_xflow_AYYJMvSuG9zxVnyvlTzz&metric=sqale_rating&token=sqb_2e56e3f3bda244712ae1f46c6e78e1cff3ecb167)](https://sonarqube.aadpace.com/dashboard?id=PACE-INT_xflow_AYYJMvSuG9zxVnyvlTzz) [![Reliability Rating](https://sonarqube.aadpace.com/api/project_badges/measure?project=PACE-INT_xflow_AYYJMvSuG9zxVnyvlTzz&metric=reliability_rating&token=sqb_2e56e3f3bda244712ae1f46c6e78e1cff3ecb167)](https://sonarqube.aadpace.com/dashboard?id=PACE-INT_xflow_AYYJMvSuG9zxVnyvlTzz)

The xFlow framework is a set of packages with different functionality, which together give the complete experience of
continuous training. xFlow currently covers these parts of the continuous training loop:

* Fetching of training/validation/test/calibration data
* Training
* Conversion to ONNX and TensorRT
* Evaluation of TensorFlow and TensorRT models + KPI reporting
* Azure cloud execution submission

## Development mindset

* XFlow is a community project.
  * Everyone can and should contribute to make it a success.
* Documentation is part of the code/repo.
  * If documentation is lacking please create a PR and extend the documentation.
* Overall architecture discussions take place in the [XFlow Admin Guild](https://pace-project.atlassian.net/wiki/spaces/VD/pages/29196296/ML+XFlow+Admin+Guild).
  * Each cluster has one representative in the guild
* Vision multitask/usecase exchange takes place in the [Multitask Tandem Sync](https://pace-project.atlassian.net/wiki/spaces/VD/pages/11277029/ML+Multi-Task+CNN+Tandem+Sync)
* Check out how to contribute: [Developing code in the xFlow repo](#developing-code-in-the-xflow-repo)

## Communication channels

Please use the following channels only for their intended purposes:

* Announcements: [discussions forum](https://github.com/PACE-INT/xflow/discussions)
  * Information or updates that are relevant to all/most xFlow users will be shared here. Make sure to set up your notifications and keep an eye on the latest announcements.
* Support and Q/A: [teams channel](https://teams.microsoft.com/l/channel/19%3a37f9ea375f8e41fd8b8614ec6ab8de3d%40thread.tacv2/XFlow%2520Support%2520Channel?groupId=972233c3-2305-46e5-84a8-f1a039849d2d&tenantId=a6c60f0f-76aa-4f80-8dba-092771d439f0)
  * First level support for and from other xFlow users. Please check if your issue was already mentioned by someone else before you add a new conversation.
* Technical debt ledger: [issue tracker](https://github.com/PACE-INT/xflow/issues)
  * Only topics, where clear technical debt in a shared piece of code has been built up, should be tracked here. Issues can be voted for by using the "Thumbs up" emoji on the main issue comment. The issues can then be sorted by that emoji. The xFlow admin guild will consider these issues accordingly.

## Code disclosure

This code originates from the Automated Driving Alliance project.
Disclosure of this code outside the Bosch Group is only permitted under specific conditions and is generally prohibited.
Reuse of the code is generally permitted, but subject to certain restrictions.
If disclosure of the source code to third parties is planned or likely, the specific use case must be reviewed with the responsible Third Party License Officer.

## Code transfer

All code in this repository is potentially transferred towards Bosch.
Putting any personal names inside the code is counted as explicit approval to share the name towards Bosch.

## Setup

We generally use docker to provide all users a common development environment. You can of course install all necessary dependencies in your system, but we strongly advise against it.

### Initial machine setup

#### On an Azure Compute Instance

* To get an Azure compute instance, open a PR to the vinfra repository as explained [here](https://github.com/PACE-INT/vinfra/tree/main/solutions/machine_learning).
* Go to [AzureML](https://ml.azure.com), navigate to your workspace, select 'Compute' from the navigation bar, select your compute instance and press 'Start'
* Click on 'Terminal' to open a shell in the browser
* Update azure-cli to the latest version by running `az upgrade`.
* Follow the normal setup for getting a docker and installing xFlow, but use '`az login --identity`' whenever you need to login to az.
* To use the managed identity on the machine, we have to start jupyter once and click to trust the compute instance.
If you skip this step, `az login --identity` will not work, with an error similar to
  > RuntimeError: MSI: Failed to retrieve a token from 'http://127.0.0.1:46808/MSI/auth/?resource=https://management.core.windows.net/&api-version=2017-09-01' with an error of 'SSO failure: unable to retrieve a token. Error '.

  The following picture shows the necessary steps.
  <img src="./compute_instance_first_login.jpg" alt="Login to jupyter to trust compute instance" width="600" />

#### On a workstation (needing root rights)

This only needs to be done if nobody has been using this workflow on a given machine, yet.

**NOTE**: You might not need to use sudo for any of the following commands if you already are in the docker group. If you run into errors like "Application is not registered with AAD", try the respective command without sudo.

##### Install nvidia-docker2

Make sure nvidia-docker2 is installed via apt.
If it is not, then follow this guide:
<https://docs.nvidia.com/datacenter/cloud-native/container-toolkit/install-guide.html>

##### Install Azure CLI

Install azure-cli to enable communication with the azure docker registry.

**Important Note**: The `azure-cli` package in Ubuntu's default repositories may be outdated and not recommended for use. It is advisable to install the latest version using the script provided by the Azure CLI team. For detailed instructions, refer to the [Azure CLI installation guide](https://learn.microsoft.com/en-us/cli/azure/install-azure-cli-linux?pivots=apt).

```bash
curl -sL https://aka.ms/InstallAzureCLIDeb | sudo bash
```

**Alternatively**, if you don't have sudo rights, you can install the latest version of azure cli via. pip (`pip install azure-cli`).
However, it only works if you are in a python venv, otherwise python will still use the azure cli installed in the system-wide dist-packages.
Also be aware that the `msal` package version >=1.29 may cause login issues, so you may want to try to downgrade `msal` to 1.28 after installing
azure-cli in python.

##### Add all expected users to the docker group

```bash
sudo gpasswd -a <username> docker
```

### Getting a docker

Create custom access token at <https://github.com/PACE-INT>
by clicking on Profile → Settings → Developer Settings → Personal Access Tokens. Important: After creating your token, you need to authorize it (Click on Configure SSO -> Authorize). Otherwise, some permission error (403) will be thrown.
Checkout xFlow using your token as password when prompted.

:warning: It is recommended to use a custom parent-directory, e.g. `~/pace_home` to checkout your repository into.
Otherwise, there might be unwanted side-effects from mounting the parent-directory persistently in your [DevContainer](.build/user-image/docker-compose.yaml), such as your container's `~/.bashrc` being overwritten from the outside!

```bash
mkdir ~/pace_home
cd ~/pace_home
git clone https://github.com/PACE-INT/xflow.git xflow
```

:warning: An important prerequisite for the following (accessing most Azure resources) is to request access to the [AP - Resource-Access - modeldeveloper_prod - Developer](https://myaccess.microsoft.com/@AADPACE.onmicrosoft.com#/access-packages/768071a2-aa68-4f00-9ab6-1b4be1689ffc) access package (or the respective dev/qa package if you really need it).

We provide a container image defined in `.build/user-image/Dockerfile` for development in a mostly isolated environment.

If you do not have `uv` installed, run

```sh
curl -LsSf https://astral.sh/uv/install.sh | sh
source $HOME/.local/bin/env
```

To set up the Docker development environment, run

```sh
./set-me-up.py docker init
```

once and follow the instructions (if any).
You can then build the container image and spin up a new disposable container via

```sh
./set-me-up.py docker run
```

or [reopen your workspace in a `vscode` devcontainer][vscode-open-in-container].

#### DevContainer Troubleshooting

##### Unauthorized: authentication required

You may hit a `unauthorized: authentication required` error while running the `docker run` command.
In this case, Azure doesn't redirect you to the browser to get authenticated.
To resolve this issue, login manually via

```sh
az login --tenant AADPACE.onmicrosoft.com --use-device-code
az acr login -n vdeepacrprod
```

If the second command, `az acr login ...`, results in an error message `User "***" does not exist in MSAL token cache.`,
despite a successful login in the first command, you may need to *downgrade* the `msal` python package to version 1.28.

#### Azure login not persistent in the docker container

Normally, the azure token cache (`~/.azure`) should be transferred from your local user account to the docker container.
If your login from the local machine seems to be not valid anymore in the docker, it could be caused by a version
mismatch of the azure cli package between the docker and your local machine.
Make sure to install the latest azure cli version in your workstation, see [Install Azure CLI](#install-azure-cli).

##### Crash during VS-Code DevContainer Launch

Depending on your docker-compose version, you may hit a `Missing mandatory value for "build" option interpolating...`
or `The Compose file is invalid because: Unsupported config option for services.xflow_dev: 'runtime'` error when
launching the DevContainer from VSCode. Potential workarounds are:

1. (*Warning: Needs sudo!*): If not present, install docker compose v2 `sudo apt-get install docker-compose-plugin`and use it for the Devcontainer
by adding `"dev.containers.dockerComposePath": "docker compose"` to your VS Code settings.json
2. (*No sudo required!*): Upgrade docker-compose v1 to the newest version `pip install docker-compose docker~=6.1 --upgrade`
    * Note: Docker~=6.1 is required as [Docker==7.0.0 broke the docker-compose interface](https://stackoverflow.com/questions/77641240/getting-docker-compose-typeerror-kwargs-from-env-got-an-unexpected-keyword-ar)
3. "Downgrade" the GPU mounting by removing `deploy: resources: reservations: devices: capabilities: [gpu]` and adding
`runtime:nvidia` to your [devcontainer.json](.devcontainer/devcontainer.json).
    * Warning: This will change a checked-in file
    * **In case of failure**, also remove `runtime:nvidia` from the [devcontainer.json](.devcontainer/devcontainer.json). Instead, check, if `default-runtime: nvidia` is set in `/etc/docker/daemon.json`. This enables GPU support in any Docker
    container by default!
      * (*Warning: Needs `sudo`*) If not set, add it to `/etc/docker/daemon.json`

#### General hints to work with the development container

* To start the container you can now always just execute `./set-me-up.py docker run`.
* To connect to the container from another terminal, you can use `docker exec -it xflow-dev-$(whoami) bash`.
  * If you are using multiple instances of the xflow repository on the same machine at the same time, replace `xflow`
    with the name of the directory you cloned the actual repository into.
* To update your container image at a later stage you simply have to run `./set-me-up.py docker rebuild`

##### How to attach VSCode to the development container

* Currently there are two options on how to attach VSCode to the development container. Just install these extensions for either of them (you don't even need to install docker on your laptop):
  * Dev Containers
  * Remote - SSH
  * Remote - SSH: Editing Configuration Files
  * Remote - WSL
  * Remote Development
  * Docker

Once the extensions are installed you have two options for attaching VSCode to the development container:

1. Attach VSCode to a running container: <br>
  Connect to your machine, run the container and attach VSCode to the running instance using the docker extension.
2. Start the container with VSCode directly attached to it using the provided [devcontainer.json](.devcontainer/devcontainer.json): <br>
  Connect VSCode to your machine, make sure the container is built and VSCode is opened inside the xFlow repo. Then run the following command using the command palette: `Dev Containers: Reopen in Container`

#### Customizing your DevContainer

##### Custom Docker Container

The default runtime configuration of the docker container is specified in the [docker-compose.base.yaml](.build/user-image/docker-compose.base.yaml) file. It is possible to extend and even overwrite the settings in this file by creating and modifying the [docker-compose.user.yaml](.build/user-image/docker-compose.user.yaml) file. VSCode's remote container extension will automatically combine the contents of these files, which allows users to customize the way their devcontainer is started.

Find more details on customization in the [Docker Compose](https://docs.docker.com/compose/compose-file/) file reference and the [PACE-Repo How-To](https://github.com/PACE-INT/pace/blob/main/docs/developer/how_tos/customize_devcontainer.rst).

:bulb: The [docker-compose.user.yaml](.build/user-image/docker-compose.user.yaml) file must exist for the container to start properly, therefore it is automatically generated during `set-me-up.py docker init`.

##### Custom Container Shell

You can customize your container shell by adapting your [~/.bashrc_user](~/.bashrc_user) file inside the container. It will automatically be sourced by [~/.bashrc](~/.bashrc), which currently is non-persistent and overwritten at each container build. [~/.bashrc_user](~/.bashrc_user) still allows you to define aliases, shell themes, etc... that survive Container rebuilds.

##### QNX setup

Get a QNX license and activate it - see this [description](https://pace-project.atlassian.net/wiki/spaces/KNOW/pages/19006388/QNX+Licenses+and+Support) for details. Adjust [.build/user_image/.envrc] if necessary. Afterwards, `qcc` should be available in the docker container.

### Installing xFlow in the docker

The former manual installation step is deprecated and is automatically done by the
[entrypoint.sh](.build/user-image/entrypoint.sh) script while starting the docker container.

## Developing code in the xFlow repo

### General guidelines

:warning: The [manifest](manifest.md) gives general guidelines on how to work with the repo. The [review guidelines](review_guidelines.md) help you to create, find and review pull requests. Make sure to read it!

Packages or other subspaces may extend the manifest guidelines with specific information to them as long as no contradictions with the manifest are created. Have a look at the [vision README](xusecases/vision.md) as an example.

For rapid prototyping purposes have a look at the [experimental usecase](xusecases/experimental.md).

### Working with GIT LFS

Make sure you set up git lfs properly as described above.
All test data and initialization weights have been moved to git lfs.

#### Downloading lfs data

To download data (e.g. test data), run

```bash
# Download only specific data
git lfs pull --include=<data_directory> --exclude=""
# Download all lfs data
git lfs pull --include="*" --exclude=""
```

#### Adding data to lfs

The [.gitattributes](.gitattributes) file defines what data will be handled by git lfs.

Any files that are covered by the existing rules will automatically be added to lfs using the normal git workflow (git add, git commit, git push).

For any new, large data that is not handled by the existing rules make sure to add a respective rule and also exclude the automatic fetching in the [.lfsconfig](.lfsconfig) file.

## Building Documentation

In order to build the documentation of xflow do

```bash
mkdocs serve
```

## xFlow training material

Additional training material for xFlow is located in [Confluence](https://pace-project.atlassian.net/wiki/spaces/VD/pages/11114989/XFlow+Training+Material).

## Package structure

Note: Specifically the ADA2.0 package structure is still evolving. In case of questions/suggestions, please get in contact with the xFlow-Admins.

### Tensorflow (ADA 1.0)
```mermaid
graph BT;

EXTERNAL[external:<br /><br />dll_client<br />label_hierarchie<br />label_format<br />label_set];

subgraph xflow eco system
    %%{ init: { 'flowchart': { 'curve': 'monotoneY' } } }%%
    direction BT

    CLUSTERCLOUD([cluster_cloud])

    TENSORFLOW[tensorflow];

    subgraph conversion
      CONVERSION([conversion]);
      CONVERSIONCONTRACT([conversion_contract]);
    end

    XCONTRACT([xcontract]);
    XCOMMON([xcommon]);
    XTENSION([xtension]);
    XTENSORFLOW([xtensorflow]);
    XAZ([xaz]);

    XDATA([xdata<br /><br />data_formats])

    EVIL([evil]);
    XUSECASES([xusecases]);

    XREPORT([xreport]);

    CONVERSION-->CONVERSIONCONTRACT;
    CONVERSION-->XCONTRACT;
    CONVERSION-->TENSORFLOW;
    CLUSTERCLOUD--->XAZ;

    EVIL--->XDATA;
    EVIL--->EXTERNAL;

    XCOMMON --> CLUSTERCLOUD;
    XCOMMON-->XCONTRACT;
    XCOMMON --> EVIL;
    XCOMMON --> TENSORFLOW;
    XCOMMON --> XTENSORFLOW;
    XCOMMON --> XTENSION;
    XCOMMON --> XAZ;

    XUSECASES-->XDATA;
    XUSECASES-->EXTERNAL;
    XUSECASES--->CONVERSIONCONTRACT;
    XUSECASES--->CLUSTERCLOUD;
    XUSECASES--->CONVERSION;
    XUSECASES--->EVIL;
    XUSECASES--->TENSORFLOW;
    XUSECASES--->XAZ;
    XUSECASES--->XCOMMON;
    XUSECASES--->XCONTRACT;
    XUSECASES--->XTENSION;
    XUSECASES--->XTENSORFLOW;
    XUSECASES--->XREPORT;

    XUSECASES-->EXTERNAL;

    XDATA--->EXTERNAL;

    XTENSION--->EXTERNAL;
    XTENSION--->XDATA;
    XTENSION--->XAZ;

    XTENSORFLOW-->XCONTRACT;
    XTENSORFLOW-->EXTERNAL;
    XTENSORFLOW-->TENSORFLOW;

    %%XTUTORIAL-->XTENSION;
    %%XTUTORIAL-->XTENSORFLOW;
    %%XTUTORIAL-->CONVERSION;
end
```

### Torch (ADA 2.0)
```mermaid
graph BT;

EXTERNAL[external:<br /><br />dll_client<br />label_hierarchie<br />label_format<br />label_set];

subgraph xflow eco system
    %%{ init: { 'flowchart': { 'curve': 'monotoneY' } } }%%
    direction BT

    XCLOUD([xcloud<br /><br />azure_tools])

    XTORCH_EXTENSIONS([xtorch_extensions<br /><br />visualization])

    TORCH[torch];

    subgraph conversion
      CONVERSION([conversion]);
      CONVERSIONCONTRACT([conversion_contract]);
    end

    XCONTRACT([xcontract]);
    XTORCH([xtorch]);

    XDATA([xdata<br /><br />data_formats])

    EVIL([evil]);

    LOOMY([loomy]);

    XTORCHUSECASES([xtorch_usecases]);

    CONVERSION-->CONVERSIONCONTRACT;
    CONVERSION-->XCONTRACT;
    CONVERSION-->TORCH;

    EVIL--->XDATA;
    EVIL--->EXTERNAL;

    XDATA--->EXTERNAL;

    XTORCH-->XCONTRACT;
    XTORCH-->TORCH;

    XTORCHUSECASES--->CONVERSIONCONTRACT;
    XTORCHUSECASES--->XCLOUD;
    XTORCHUSECASES--->XCONTRACT;
    XTORCHUSECASES-->XDATA;
    XTORCHUSECASES--->XTORCH_EXTENSIONS;
    XTORCHUSECASES--->LOOMY;
    XTORCHUSECASES-->XTORCH;
    XTORCHUSECASES-->TORCH;
    XTORCHUSECASES-->EXTERNAL;
    XTORCHUSECASES-->EVIL;
    XTORCHUSECASES--->CONVERSION;
end
```
