# syntax=docker/dockerfile:1
# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
FROM nvidia/cuda:12.3.1-devel-ubuntu22.04

# Set the user as the local user
ARG UID=1000
ARG GID=1000
ARG USERNAME=mutti_user

RUN groupadd -g $GID $USERNAME && useradd -m -u $UID -g $GID $USERNAME

# Get the binaries for UV
COPY --from=ghcr.io/astral-sh/uv:latest /uv /uvx /bin/

### Install some custom software that is missing from the regular container
RUN apt-get update && apt-get install -y --no-install-recommends \
    tree \
    bash-completion \
    file \
    curl \
    git \
    # needed to compile extensions in mmcv
    build-essential \
    # required by opencv (cv2)
    libgl1 \
    libglib2.0-0 \
    && rm -rf /var/lib/apt/lists/*

# Make cudnn and cuda libraries discoverable by tensorflow and onnxruntime
ENV LD_LIBRARY_PATH=/usr/local/cuda/lib64:$LD_LIBRARY_PATH
ENV PATH=/usr/local/cuda/bin:$PATH

# Create directory with the mutti code
ENV MUTTI_REPO_DIR="/home/<USER>/repo"
RUN mkdir $MUTTI_REPO_DIR && chown -R $UID:$GID $MUTTI_REPO_DIR

# Switch to the user
USER $UID

# Install python as user and add the executable to the PATH
RUN uv venv $MUTTI_REPO_DIR/.venv --python 3.10 --python-preference only-managed && uv cache clean
ENV PATH=$MUTTI_REPO_DIR/.venv/bin:$PATH

# Install inference libraries
RUN --mount=type=secret,id=PIP_INDEX_URL,env=PIP_INDEX_URL,uid=1000 \
    uv pip install --upgrade pip ipython setuptools packaging && \
    uv pip install torch==2.3.0 torchvision==0.18.0 torchaudio==2.3.0 --index-url=https://download.pytorch.org/whl/cu121 && \
    # CR TSR model dependencies
    uv pip install xformers==0.0.26.post1 --index-url=https://download.pytorch.org/whl/cu121 && \
    git clone https://github.com/baaivision/EVA $HOME/EVA && \
    cd $HOME/EVA/EVA-02/asuka/ && \
    uv pip install -r requirements.txt && \
    cd ../det/ && \
    uv pip install --no-build-isolation . && \
    uv cache clean

# Clone code with the tracker
ENV CR_TRACKER_SRC_PATH=/home/<USER>/cr_tracker_repo/assets-robert-bosch-techboost
ENV CR_TRACKER_SRC_RELATIVE_PATH=ViPER/tsr/cr_aim_108_prelabels/tracker
ENV CR_TRACKER_PACKAGE_RELATIVE_PATH=ViPER/tsr/cr_aim_108_prelabels/
RUN --mount=type=secret,id=ADA_GITHUB_TOKEN,env=ADA_GITHUB_TOKEN,uid=1000 \
    mkdir -p ${CR_TRACKER_SRC_PATH} && \
    git clone --single-branch --branch feature/tsr_cr_aim_108_prelabels_mutti_integration --no-checkout --depth=1 --filter=tree:0 https://${ADA_GITHUB_TOKEN}@github.com/PACE-INT/assets-robert-bosch-techboost.git ${CR_TRACKER_SRC_PATH} && \
    cd ${CR_TRACKER_SRC_PATH} && \
    git sparse-checkout set ${CR_TRACKER_SRC_RELATIVE_PATH} && git checkout
ENV PYTHONPATH=${CR_TRACKER_SRC_PATH}/${CR_TRACKER_PACKAGE_RELATIVE_PATH}:${CR_TRACKER_SRC_PATH}/${CR_TRACKER_SRC_RELATIVE_PATH}

# Copy the repo
WORKDIR $MUTTI_REPO_DIR
COPY --chown=$UID:$GID . .

# Install repo and requirements using ADA artificatory
RUN --mount=type=secret,id=PIP_INDEX_URL,env=PIP_INDEX_URL,uid=1000 \
    --mount=type=secret,id=PIP_EXTRA_INDEX_URL,env=PIP_EXTRA_INDEX_URL,uid=1000 \
    uv pip install --index-url=$PIP_INDEX_URL --extra-index-url=$PIP_EXTRA_INDEX_URL .  && \
    # downgrade PIL due to dependencies mismatch
    uv pip install "pillow<10.0" && \
    # delete large libraries not needed
    uv pip uninstall tensorflow && uv pip uninstall onnxruntime-gpu && uv pip uninstall onnxruntime && \
    uv cache clean

CMD ["python", "src/mutti_pipeline/scripts/dataloop_tsr_cr_model_tagging.py"]
