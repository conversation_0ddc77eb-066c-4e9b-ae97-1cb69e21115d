import hydra
import pyarrow as pa
import pyarrow.parquet as pq
from azure.identity import AzureCliCredential

from mutti_data.schemas.dyo_autolabeling_schema import (
    dyo_offline_raw_pcds_detection_object_level_output_schema_rich,
)
from mutti_pipeline.scripts.pipeline_components import dbx_writing

SEQUENCE_PATH = "test_data/mutti_pipeline/mutti_processing/dyo_multimodal/working_sequence/27139bbcabeb52154cffff908b2cd45fb00eb2c5f5ea20a409cb25629806663af00540tvrightdyobev3dautolabelv8kfm_sequence_level.parquet"
FRAME_PATH = "test_data/mutti_pipeline/mutti_processing/dyo_multimodal/working_sequence/27139bbcabeb52154cffff908b2cd45fb00eb2c5f5ea20a409cb25629806663af00540tvrightdyobev3dautolabelv8kfm_frame_level.parquet"
OBJECT_PATH = "test_data/mutti_pipeline/mutti_processing/dyo_multimodal/working_sequence/27139bbcabeb52154cffff908b2cd45fb00eb2c5f5ea20a409cb25629806663af00540tvrightdyobev3dautolabelv8kfm_object_level.parquet"

# DYO AC test data paths
DYO_AC_SEQUENCE_PATH = "test_data/mutti_pipeline/mutti_processing/dyo_ac_subclasses/output/sequence.parquet"
DYO_AC_FRAME_PATH = "test_data/mutti_pipeline/mutti_processing/dyo_ac_subclasses/output/frame.parquet"
DYO_AC_OBJECT_PATH = "test_data/mutti_pipeline/mutti_processing/dyo_ac_subclasses/output/object.parquet"


def test_dbx_writing(monkeypatch):
    # Load tables
    sequence_table = pq.read_table(SEQUENCE_PATH)
    frame_table = pq.read_table(FRAME_PATH)
    object_table = pq.read_table(OBJECT_PATH)

    # The test data does not contain the sequence_imu_sha and sequence_imu_url columns,
    # so we need to add them manually for the test to run, but it is not needed for the test
    sequence_imu_sha_array = pa.array(["dummy_sha"] * sequence_table.num_rows, pa.string())
    sequence_imu_url_array = pa.array(["dummy_url"] * sequence_table.num_rows, pa.string())

    sequence_table = sequence_table.append_column("sequence_imu_sha", sequence_imu_sha_array)
    sequence_table = sequence_table.append_column("sequence_imu_url", sequence_imu_url_array)

    # Create TableMetadata object
    dyo_mm_table_metadata = dbx_writing.TableMetadata(
        sequence_table_schema=hydra.utils.get_object(
            "mutti_data.schemas.mdm_search_enriched_schemas.DBX_FULL_ENRICHED_SCHEMA_SEQUENCE"
        ),
        frame_table_schema=hydra.utils.get_object(
            "mutti_data.schemas.mdm_search_enriched_schemas.AUTOLABELING_LIDAR_VISION_OUTPUT_ENRICHED_SCHEMA_FRAME"
        ),
        object_table_schema=dyo_offline_raw_pcds_detection_object_level_output_schema_rich,
        sequence_table_dbx_name="mutti_sequences_v0",
        frame_table_dbx_name="mutti_frames_5v1lr_v0",
        object_table_dbx_name="mutti_objects_dyo_offline_detections_v0",
    )

    # Mock credentials and external calls
    credential = AzureCliCredential()
    monkeypatch.setattr(dbx_writing, "write_tables_to_databricks", lambda *a, **kw: None)

    # So far, just ensure that the function runs without errors,
    # deeper testing to follow
    dbx_writing.write_to_databricks(
        sequence_table,
        frame_table,
        object_table,
        credential,
        table_metadata=dyo_mm_table_metadata,
        involved_models=["dummy_model"],
        config={"dummy": "config"},
        mutti_pipeline="test_pipeline",
    )


def test_dbx_writing_dyo_ac(monkeypatch):
    """Test DBX writing for DYO AC (attribute classification) pipeline."""
    # Load tables
    sequence_table = pq.read_table(DYO_AC_SEQUENCE_PATH)
    frame_table = pq.read_table(DYO_AC_FRAME_PATH)
    object_table = pq.read_table(DYO_AC_OBJECT_PATH)

    # Create TableMetadata object based on DYO AC config
    dyo_ac_table_metadata = dbx_writing.TableMetadata(
        sequence_table_schema=hydra.utils.get_object(
            "mutti_data.schemas.mdm_search_enriched_schemas.DBX_FULL_ENRICHED_SCHEMA_SEQUENCE"
        ),
        frame_table_schema=hydra.utils.get_object(
            "mutti_data.schemas.mdm_search_enriched_schemas.DBX_FC1_TV_ENRICHED_SCHEMA_FRAME"
        ),
        object_table_schema=hydra.utils.get_object(
            "mutti_data.schemas.dyo_ac_autolabeling_schemas.dyo_ac_vehicle_type_fc1_tv_object_lvl_schema_rich"
        ),
        sequence_table_dbx_name="mutti_sequences_v0",
        frame_table_dbx_name="mutti_frames_5v_v0",
        object_table_dbx_name="mutti_objects_dyo_tracked_with_subclasses_v0",
    )

    # Mock credentials and external calls
    credential = AzureCliCredential()
    monkeypatch.setattr(dbx_writing, "write_tables_to_databricks", lambda *a, **kw: None)

    # Test that the function runs without errors
    dbx_writing.write_to_databricks(
        sequence_table,
        frame_table,
        object_table,
        credential,
        table_metadata=dyo_ac_table_metadata,
        involved_models=["deepseek-vl2-tiny-occlusion-merged"],
        config={"mutti_inference": {"label_task": {"name": "attr_dyo_vehicle_type_occlusion_classification"}}},
        mutti_pipeline="dyo_ac_pipeline",
    )
