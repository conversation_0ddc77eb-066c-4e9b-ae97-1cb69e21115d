name: build_CR_model_push_image

on:
  workflow_dispatch:

env:
  artifactory-repository-download: shared-pypi-prod
  artifactory-extra-repository-download: shared-pypi-dev-local

permissions:
  id-token: write # required for the az login
  contents: read # required for checkout

jobs:
  Docker:
    runs-on: [gpu-t4-4core-prod]
    environment:
      name: default

    steps:
      - name: Show GPU
        run: |
          nvidia-smi

      - name: Check Out Repository
        id: checkout_repository
        uses: actions/checkout@v4
        with:
          lfs: true

      - run: echo "DOCKER_BRANCH_TAG=$(echo ${{ github.ref }} | cut -d'/' -f3- | sed 's/[^a-z0-9_-]/__/gi')" >> $GITHUB_ENV

      - name: Login az [docker build]
        shell: bash
        run: |
          az login --service-principal --username ${{ vars.AZURE_CLIENT_ID_TEST }} --password ${{ secrets.AZURE_CLIENT_SECRET_TEST }} --tenant ${{ vars.AZURE_TENANT_ID_TEST }} --allow-no-subscriptions

      - name: Azure OIDC prod tenant Lo<PERSON>
        uses: azure/login@v1
        with:
          client-id: ${{ vars.MODELDEVELOPER_PROD_CLIENT_ID }}
          subscription-id: ${{ secrets.AZURE_SUBSCRIPTION_ID }}
          tenant-id: ${{ secrets.AZURE_TENANT_ID }}
          allow-no-subscriptions: true

      - name: Set up Python 3.10
        uses: actions/setup-python@v3
        with:
          python-version: "3.10"

      - name: Lint with `pylint`, `black`, `flake8` and other standards
        env:
          SKIP: no-commit-to-branch
        run: |
          pip install pre-commit
          pre-commit run --all-files

      - name: Sonarqube
        run: |
          wget https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-${SONAR_SCANNER_VERSION}-linux.zip
          unzip sonar-scanner-cli-${SONAR_SCANNER_VERSION}-linux.zip
          ./sonar-scanner-${SONAR_SCANNER_VERSION}-linux/bin/sonar-scanner \
            -Dsonar.host.url=${SONAR_HOST_URL} \
            -Dsonar.login=${SONAR_TOKEN} \
            -Dsonar.c.file.suffixes=- \
            -Dsonar.cpp.file.suffixes=- \
            -Dsonar.objc.file.suffixes=-
        env:
          SONAR_SCANNER_VERSION: 4.7.0.2747
          SONAR_TOKEN: ${{ secrets.SONAR_TOKEN }}
          SONAR_HOST_URL: ${{ secrets.SONAR_HOST_URL }}

      - name: Build image for running tests [docker build]
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile_cr
          secrets: |
            PIP_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-repository-download }}/simple
            PIP_EXTRA_INDEX_URL=https://${{ secrets.JCA_ARTIFACTORY_PROD_USER }}:${{ secrets.JCA_ARTIFACTORY_PROD_TOKEN }}@${{ secrets.JCA_ARTIFACTORY_DOMAIN }}/artifactory/api/pypi/${{ env.artifactory-extra-repository-download }}/simple
            ADA_GITHUB_TOKEN=${{ secrets.ADA_GITHUB_TOKEN }}
          tags: |
            testing/dsp-op-mutti-cr:${{ github.sha }}
          push: false

      - name: Push docker image
        shell: bash
        run: |
          set -ex
          # On a PR, the docker shall be available as "latest" in qa, as a tagged version in qa and prod
          az acr login -n vdeepacrprod.azurecr.io
          az acr login -n crdllprod.azurecr.io
          az acr login -n crdllqa.azurecr.io

          echo "Pushing docker image to crdllprod.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}"
          docker tag testing/dsp-op-mutti-cr:${{ github.sha }} crdllprod.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}
          docker push crdllprod.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}

          echo "Pushing docker image to crdllqa.azurecr.io/dsp-op-mutti-cr:latest"
          docker tag testing/dsp-op-mutti-cr:${{ github.sha }} crdllqa.azurecr.io/dsp-op-mutti-cr:latest
          docker push crdllqa.azurecr.io/dsp-op-mutti-cr:latest

          echo "Pushing docker image to crdllqa.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}"
          docker tag testing/dsp-op-mutti-cr:${{ github.sha }} crdllqa.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}
          docker push crdllqa.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}

          echo "Pushing docker image to vdeepacrprod.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}"
          docker tag testing/dsp-op-mutti-cr:${{ github.sha }} vdeepacrprod.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}
          docker push vdeepacrprod.azurecr.io/dsp-op-mutti-cr:${{ github.sha }}
