"""Infer attributes of objects and store results in object-level table (parquet)."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from pathlib import Path

import hydra
import pandas as pd
import ray
from omegaconf import DictConfig, OmegaConf

from mutti_data.definitions import TablesIOSpecs
from mutti_data.schemas.dyo_ac_autolabeling_schemas import (
    create_cuboid_projection_object_level_schema,
    create_dyo_attribute_classification_object_level_schema,
)
from mutti_data.schemas.enriched_schema import EnrichedSchema
from mutti_inference.data.multi_modal_config import BuilderConfig
from mutti_inference.data.multi_modal_loader import MultiModalDatasetBuilder
from mutti_inference.data.transform.cuboid_projection import (
    DetectionsAppenderFlex,
    crop_n_unpack_bbox_detections,
)
from mutti_inference.model.vllm_attribute_decoder import apply_attribute_decoding_fn
from mutti_inference.utils import (
    consume_data_pipe,
    ensure_not_empty,
    init_ray,
    log_frame_header,
)

logger = logging.getLogger(__name__)


def prepare_tables(
    frame_table: pd.DataFrame, object_table: pd.DataFrame, cfg: DictConfig
) -> tuple[pd.DataFrame, pd.DataFrame]:
    """Prepare frame and object tables for attribute classification."""
    valid_max_bbox_truncation = cfg.data.projection.get("valid_max_bbox_truncation", 0.25)

    # 1) remove frames without detections
    frame_idx_with_objects = object_table.frame_master_index.unique()
    logger.info(f"FRAME TABLE - Removing {len(frame_table) - len(frame_idx_with_objects)} frames without objects.")
    frame_table = frame_table[frame_table.frame_master_index.isin(frame_idx_with_objects)]
    # 2 ) remove frames without any valid bboxes (below truncation threshold)
    # get rows from object_table with truncation < valid_max_bbox_truncation
    mask_valid_boxes = object_table.filter(regex="_truncation$") < valid_max_bbox_truncation
    frame_idx_with_boxes = object_table[mask_valid_boxes.any(axis=1)].frame_master_index.unique()
    logger.info(f"FRAME TABLE - Removing {len(frame_table) - len(frame_idx_with_boxes)} frames without valid bboxes.")
    frame_table = frame_table[frame_table.frame_master_index.isin(frame_idx_with_boxes)]
    # 3) diagnostics
    ensure_not_empty(frame_table, "FRAME TABLE")
    log_frame_header(frame_table)
    ensure_not_empty(object_table, "OBJECT TABLE")
    count = mask_valid_boxes.sum().sum()
    logger.info(
        f"OBJECT TABLE - Loaded with {len(object_table)} rows and {count} detection crops "
        f" with truncation <{valid_max_bbox_truncation}."
    )

    return frame_table, object_table


def create_data_pipe(
    frame_table: pd.DataFrame,
    object_table: pd.DataFrame,
    cfg: DictConfig,
    frame_table_schema: EnrichedSchema,
    object_table_schema: EnrichedSchema,
) -> ray.data.Dataset:
    """Run the attribute classification pipeline."""
    data_cfg = cfg.data
    image_prep_cfg = data_cfg.image_prep
    model_cfg = cfg.model
    infer_cfg = cfg.inference
    io_cfg = cfg.io

    # Get source_sensor_name and target_sensor_list
    attr_list = model_cfg.get("attr_list", [])
    source_sensor_name = data_cfg.projection.source_sensor_name
    target_sensor_dict = data_cfg.projection.col2sensor
    target_sensor_list = list(target_sensor_dict.values())
    logger.info(f"Source sensor: {source_sensor_name}")
    logger.info(f"Target sensor list: {target_sensor_list}")

    if attr_list:
        target_object_table_schema = create_dyo_attribute_classification_object_level_schema(
            attr_list, target_sensor_list, object_table_schema
        )
    else:
        logger.warning("DECODER - No attribute list provided. Skipping attribute decoding. ONLY USE FOR DEV.")

    # Get other params
    valid_max_bbox_truncation = data_cfg.projection.get("valid_max_bbox_truncation", 0.25)
    crop_context_margin = image_prep_cfg.get("crop_context_margin", 0.0)
    if not image_prep_cfg.get("crop_context_margin", 0):
        logger.warning("Detection box cropping: No crop context margin provided. Setting it to 0.0.")

    # Get the predictor/decoder class type and image augmentation function from cfg
    image_augment_fn = hydra.utils.get_method(image_prep_cfg.augment_fn)
    predictor_cls = hydra.utils.get_class(model_cfg.predictor_type)
    if attr_list:
        decoder_fn = hydra.utils.get_method(model_cfg.decoder_fn)

    ########
    # Run Attribute Classification Pipeline

    # 1. Load multi-modal sensor data from blob storage
    loading_parameters = data_cfg.get("loading_parameters", {})
    builder_config = BuilderConfig(frame_table_schema, loading_parameters)
    ds_builder = MultiModalDatasetBuilder(frame_table, builder_config.to_dict(), initial_num_blocks=20)
    logger.info(ds_builder)
    ds_attributes = ds_builder.create_dataset()

    # 2. Combine frames and detections (Append object detections as new column)
    ds_attributes = ds_attributes.map_batches(
        DetectionsAppenderFlex,
        fn_constructor_kwargs=dict(
            object_table=object_table, index_columns=["sequence_custom_sha", "frame_master_index"]
        ),
        batch_size=2,
        concurrency=2,
        num_cpus=0.5,
    )

    # 3. Crop the detections from the frames and unpack the detection rows into an object table
    ds_attributes = ds_attributes.map_batches(
        crop_n_unpack_bbox_detections,
        batch_size=2,
        fn_kwargs=dict(
            camera_col2sensor=target_sensor_dict,
            context_margin=crop_context_margin,
            valid_max_bbox_truncation=valid_max_bbox_truncation,
        ),
        num_cpus=0.5,
        concurrency=3,
    )
    # 4. Prepare the crops for inference
    ds_attributes = ds_attributes.map(
        image_augment_fn,
        num_cpus=0.5,
        concurrency=3,
        fn_kwargs=dict(
            context_margin=crop_context_margin,
            valid_max_bbox_truncation=valid_max_bbox_truncation,
        ),
    )

    if cfg.get("attr_debug_mode", False):
        # 4.5. Write the crops to disk for debugging
        from mutti_inference.data.transform.image import write_image_cols_to_disk

        output_dir = Path(io_cfg.output_path) / "processed_images"
        output_dir.mkdir(parents=True, exist_ok=True)
        ds_attributes = ds_attributes.map(
            write_image_cols_to_disk,
            num_cpus=0.5,
            fn_kwargs=dict(
                output_dir=output_dir,
                valid_max_bbox_truncation=valid_max_bbox_truncation,
            ),
        )

    # 5. Run the attribute classification model
    ds_attributes = ds_attributes.map_batches(
        predictor_cls,
        fn_constructor_kwargs=dict(
            target_sensor_list=target_sensor_list,
            valid_max_bbox_truncation=valid_max_bbox_truncation,
            **model_cfg.get("predictor_constructor_kwargs", {}),
        ),
        concurrency=int(infer_cfg.num_available_gpus // infer_cfg.get("num_gpus_per_actor", 1)),
        num_gpus=infer_cfg.get("num_gpus_per_actor", 1),
        batch_size=infer_cfg.batch_size,
    )

    # 6. Decode the model outputs
    if attr_list:
        ds_attributes = ds_attributes.map_batches(
            apply_attribute_decoding_fn,
            num_cpus=0.5,
            fn_kwargs=dict(
                decoding_fn=decoder_fn,
                attr_list=attr_list,
                sensor_list=target_sensor_list,
                target_schema=target_object_table_schema,
            ),
            batch_size=10,
            batch_format="pandas",  # important for attr decoder
        )
    return ds_attributes


@hydra.main(config_path="configs", config_name="default.yaml", version_base="1.1")
def infer_attributes(module_cfg: DictConfig) -> None:
    """Run model inference to classify attributes of detected object."""
    ########
    # Config Parsing
    logger.info(OmegaConf.to_yaml(module_cfg, resolve=True))
    cfg = module_cfg.mutti_inference
    data_cfg = cfg.data
    io_cfg = cfg.io

    # Get target_sensor_list
    target_sensor_list = list(data_cfg.projection.col2sensor.values())

    # Get the frame table schema
    frame_table_schema = hydra.utils.get_object(data_cfg.src.frame_table_schema)
    object_table_schema = hydra.utils.get_object(data_cfg.src.object_table_schema)
    proj_object_table_schema = create_cuboid_projection_object_level_schema(target_sensor_list, object_table_schema)

    ########
    # Sequence/Frame/Object Table Loading
    path_frame_table = Path(data_cfg.base_path) / data_cfg.src.frame_table_file
    path_object_table = Path(data_cfg.base_path) / data_cfg.src.object_table_file
    frame_table = pd.read_parquet(path_frame_table, schema=frame_table_schema.arrow_schema)
    object_table = pd.read_parquet(path_object_table, schema=proj_object_table_schema.arrow_schema)

    #######
    # Preprocessing tables

    if cfg.get("attr_debug_mode", False):
        frame_table = frame_table.head(10)
        logger.info("FRAME TABLE - DEBUG mode enabled. Using only the first 10 frames.")
        object_table["object_uid"] = object_table.index

    frame_table, object_table = prepare_tables(frame_table, object_table, cfg)

    ########
    # Run Attribute Classification Pipeline
    logger.info(
        f"Run attribute classification with `{cfg.model.get('name')}` model for `{cfg.label_task.get('name')}` task."
    )
    # Initialize for Ray Data
    init_ray()

    # Create the the data pipe
    ds_attributes = create_data_pipe(frame_table, object_table, cfg, frame_table_schema, object_table_schema)

    # Execute pipeline and store resulting object table as parquet files
    folder_name = f"{frame_table['sequence_custom_sha'].iloc[0]}_{TablesIOSpecs.OBJECT_LEVEL_TAG}"
    output_path = Path(io_cfg.output_path) / folder_name
    table_out = consume_data_pipe(ds_attributes, output_path)

    # Log the ray data statistics (after consumption)
    logger.info(ds_attributes.stats())

    logger.info(f"Attribute classification completed successfully. Results will be written to: {output_path}.")
    logger.info(f"Total frames processed: {len(frame_table)}.")
    logger.info(f"Number of detections saved to Parquet files: {table_out.num_rows}.")


if __name__ == "__main__":
    infer_attributes()  # pylint: disable=no-value-for-parameter
