"""Ray-based flexible and extensible multi-modal dataloader."""

import io
import re
import tempfile
from pathlib import Path

import numpy as np
import pandas as pd
import ray.data
from adlfs import AzureBlobFileSystem
from aiohttp.client_exceptions import ClientPayloadError
from aiohttp.http_exceptions import ContentLengthError

from .utils import get_abfs_handle


def create_fs_dict(df_frames: pd.DataFrame, url_cols: list[str]) -> dict[str, AzureBlobFileSystem]:
    """Create a dictionary of AzureBlobFileSystem objects from a DataFrame containing Azure blob URLs."""
    # Extract the storage account names from the URLs
    list_storage_account = []
    for url_col in url_cols:
        extracted_cols = (
            df_frames[url_col]
            .astype(str)
            .str.extract(
                r"https://(?P<storage_account>[a-z0-9]+)\.blob\.core\.windows\.net/(?P<blob_im_path>.*)", expand=True
            )
        )
        list_storage_account.extend(extracted_cols["storage_account"].unique())
    list_storage_account = list(set(list_storage_account))  # Remove duplicates
    fs_dict = {storage_account: get_abfs_handle(storage_account) for storage_account in list_storage_account}
    return fs_dict


def split_blob_url(url_string: str) -> tuple[str, str]:
    """Split a blob URL into the blob file path and the storage account name."""
    pattern = r"https://(?P<storage_account>[a-z0-9]+)\.blob\.core\.windows\.net/(?P<blob_file_path>.*)"
    match = re.match(pattern, url_string)
    return match.group("blob_file_path"), match.group("storage_account")


def read_binary_files(row: dict[str, any], col_name: str, fs_dict: dict[str, AzureBlobFileSystem]) -> dict[str, any]:
    """Read binary files from Azure Blob Storage and return the raw bytes in new column."""
    blob_file_path, storage_account = split_blob_url(row[col_name])
    fs = fs_dict[storage_account]
    file_bytes = fs.cat_file(blob_file_path)
    row[f"{col_name}_bytes"] = file_bytes
    return row


def load_image(
    row: dict[str, any], col_name: str, fs_dict: dict[str, AzureBlobFileSystem], size_wh: tuple[int, int] = None
) -> dict[str, any]:
    """Load an image from Azure Blob Storage and return the PIL image object and its size in new columns."""
    from PIL import Image, UnidentifiedImageError

    blob_file_path, storage_account = split_blob_url(row[col_name])
    fs = fs_dict[storage_account]
    file_bytes = fs.cat_file(blob_file_path)
    try:
        image = Image.open(io.BytesIO(file_bytes))
    except UnidentifiedImageError as e:
        raise ValueError(f"PIL couldn't load image file at path '{blob_file_path}'.") from e
    frame_width, frame_height = image.size
    if size_wh is not None:
        width, height = size_wh
        image = image.resize((width, height), resample=Image.BILINEAR)

    row[f"{col_name}_image"] = image
    row[f"{col_name}_image_wh"] = (frame_width, frame_height)
    return row


def load_label(
    row: dict[str, any], col_name: str, fs_dict: dict[str, AzureBlobFileSystem], as_json_obj: bool = False
) -> dict[str, any]:
    """Read a label file from Azure Blob Storage and return the label data in new column."""
    blob_file_path, storage_account = split_blob_url(row[col_name])
    fs = fs_dict[storage_account]
    file_bytes = fs.cat_file(blob_file_path)
    if not as_json_obj:
        row[f"{col_name}_label"] = file_bytes.decode()
    else:
        import json

        row[f"{col_name}_label"] = json.loads(file_bytes.decode())
    return row


def load_pcd(row: dict[str, any], col_name: str, fs_dict: dict[str, any], as_array: bool = False) -> dict[str, any]:
    """Read PCD bytes and return the point cloud data without the raw bytes in a new column."""
    import pcmanip

    blob_file_path, storage_account = split_blob_url(row[col_name])
    fs = fs_dict[storage_account]
    file_bytes = fs.cat_file(blob_file_path)
    pcd = pcmanip.io.read_point_cloud_from_pcd(io.BytesIO(file_bytes))
    if as_array:
        row[f"{col_name}_pcd_array"] = pcd.as_array()
    else:
        row[f"{col_name}_pcd_obj"] = pcd
    return row


def load_tfrecord_pcd(row: dict[str, any], col_name: str, fs_dict: dict[str, any]) -> dict[str, any]:
    """Read PCD bytes and return the point cloud data without the raw bytes in a new column.

    Based on the following example by the feature team:
    https://github.com/PACE-INT/xflow/blob/main/xusecases/src/xusecases/lidar/common/data/tfrecord/tfrecord_local.py
    """
    import tensorflow as tf

    # Stream the TFRecord file from Azure Blob Storage
    blob_file_path, storage_account = split_blob_url(row[col_name])
    fs = fs_dict[storage_account]
    file_bytes = fs.cat_file(blob_file_path)
    # Write the bytes to a temporary file and read the TFRecord
    with tempfile.TemporaryDirectory() as tmp_dir:
        tmp_file_path = Path(tmp_dir) / "temp.tfrecord"
        tmp_file_path.write_bytes(file_bytes)

        dataset = tf.data.TFRecordDataset(tmp_file_path)
        raw_record = next(iter(dataset))
        example = tf.train.Example()
        example.ParseFromString(raw_record.numpy())
        data = tf.io.parse_example(
            raw_record,
            features={
                "sensorXYZ_shape": tf.io.FixedLenFeature([], tf.string),
                "sensorXYZ_data": tf.io.FixedLenFeature([], tf.string),
                "intensityList_data": tf.io.FixedLenFeature([], tf.string),
                "intensityList_shape": tf.io.FixedLenFeature([], tf.string),
            },
        )
    # Convert the data to numpy arrays
    num, _ = np.frombuffer(data["intensityList_shape"].numpy(), dtype=np.int32)
    intensity = np.frombuffer(data["intensityList_data"].numpy(), dtype=np.float32).reshape(num, 1)
    xyz = np.frombuffer(data["sensorXYZ_data"].numpy(), dtype=np.float32).reshape(num, 3)
    # Create point cloud array with intensity
    row[f"{col_name}_pcd_array"] = np.concatenate([xyz, intensity], axis=1)
    return row


def write_pcd(row: dict[str, any], col_name: str, cache_path: Path, return_path_only: bool = True) -> dict[str, any]:
    """Write PCD bytes to a file and return the local path in a new column."""
    blob_file_path, storage_account = split_blob_url(row[col_name])
    file_path = cache_path / Path(blob_file_path).name
    file_path.write_bytes(row[f"{col_name}_bytes"])
    row["local_path"] = str(file_path)
    if return_path_only:
        row = {"local_path": str(file_path)}
    return row


class MultiModalDatasetBuilder:
    """DatasetBuilder constructs a Ray dataset for selected columns of a DataFrame with blob URLs."""

    def __init__(
        self,
        frame_table: pd.DataFrame,
        ds_config: dict,
        augmentations: dict = {},
        initial_num_blocks: int = 8,
        map_concurrency: int = 8,
        map_num_cpus: float = 0.2,
    ):
        """Initialize DatasetBuilder with the DataFrame, selected columns, and file systems.

        Args:
            frame_table:        The pandas DataFrame containing the columns with the blob URLs to be loaded.
            ds_config:          A dictionary mapping column names to function and kwargs.
            augmentations:      A dictionary mapping column names to a list of augmentation functions and kwargs.
            initial_num_blocks: The initial number of blocks for the Ray dataset. Should be roughly equal to the number
                                of cpu cores.
            map_concurrency:    The default concurrency level for the map functions, which determines the number of ray
                                workers that can concurrently execute a map function. This value can be overridden per
                                column by ds_config.
            map_num_cpus:       The default number of CPUs to allocate per map function execution. This value can be
                                overridden per column by ds_config to customize CPU usage for specific columns.

        Note:
        * The default settings for `num_cpus` and `concurrency` generally work okay for most multi-modal data.

        * In case you tune the data-loading standalone, beware that these settings might become obsolete once you
          integrate it into a larger pipeline or different type of compute node.

        * When tuning parameters, start with concurrency. Since data preparation is very I/O heavy with a lot
          of latency and primarily I/O-bound rather than CPU-bound, changing the `num_cpus` parameter from the default
          0.2 is rarely necessary.

        * Ray Data will bundle map operations that have the same resource requirements (`num_cpus` and `concurrency`)
          into the same task. Overall it appears beneficial for pipeline throughput if tasks are homogenous in their
          latency and compute requirements. For example, PCD loading is really fast, but image loading is rather slow.
          To improve throughput, one assigns different resource requirements (e.g., different 'concurrency' values) to
          them so that Ray can bundle them into different tasks.

        Example:
        ```
        df = pd.DataFrame({
            "image_url": ["https://storage.blob.core.windows.net/image1.jpg"],
            "label_url": ["https://storage.blob.core.windows.net/label1.json"],
        })
        ds_config = {
            "label_url": {"fn": load_label, num_cpus: 0.1},
            "image_url": {"fn": load_image, "ext_kwargs": {"size_wh": (224, 224)}},
        }
        builder = DatasetBuilder(df, ds_config, initial_num_blocks=8)
        ds = builder.create_dataset()
        ```
        """
        self.frame_table = frame_table
        self.ds_config = ds_config
        self.fs_dict = create_fs_dict(frame_table, list(ds_config.keys()))
        self.initial_num_blocks = initial_num_blocks
        self.augmentations = augmentations
        self.map_concurrency = map_concurrency
        self.map_num_cpus = map_num_cpus

    def create_dataset(self) -> ray.data.Dataset:
        """Build the Ray dataset config by applying the appropriate map functions."""
        ds = ray.data.from_pandas(self.frame_table, override_num_blocks=self.initial_num_blocks)
        for col_name, fn_dict in self.ds_config.items():
            concurrency = fn_dict.get("concurrency", self.map_concurrency)
            num_cpus = fn_dict.get("num_cpus", self.map_num_cpus)
            extra_kwargs = fn_dict.get("ext_kwargs", {})
            fn_kwargs = fn_dict.get("fn_kwargs", {})
            if not fn_kwargs:
                fn_kwargs = {**extra_kwargs, "fs_dict": self.fs_dict, "col_name": col_name}
            ds = ds.map(
                fn_dict["fn"],
                fn_kwargs=fn_kwargs,
                concurrency=concurrency,
                num_cpus=num_cpus,
                max_retries=3,
                retry_exceptions=[ContentLengthError, ClientPayloadError],  # Retry on azure blob storage errors
            )

        # Apply custom augmentations per column
        if self.augmentations:
            for col_name, aug_list in self.augmentations.items():
                for aug in aug_list:
                    fn_kwargs = aug.get("fn_kwargs", {})
                    # Pop out concurrency / num_cpus if the user provided them
                    concurrency = fn_kwargs.pop("concurrency", self.map_concurrency)
                    num_cpus = fn_kwargs.pop("num_cpus", self.map_num_cpus)
                    fn_kwargs["col_name"] = col_name  # Ensure col_name is in kwargs
                    ds = ds.map(aug["fn"], fn_kwargs=fn_kwargs, concurrency=concurrency, num_cpus=num_cpus)

        return ds

    def __str__(self):
        """Return a concise string representation of the pipeline steps and configurations."""

        def format_kwargs(kwargs):
            formatted_kwargs = {}
            for k, v in kwargs.items():
                # Check if the value is a pandas DataFrame
                if isinstance(v, pd.DataFrame):
                    formatted_kwargs[k] = f"<DataFrame shape={v.shape}>"
                else:
                    # Convert the value to its string representation
                    v_str = repr(v)
                    # Truncate the string if it's too long
                    if len(v_str) > 100:
                        v_str = v_str[:97] + "..."
                    formatted_kwargs[k] = v_str
            return formatted_kwargs

        lines = [f"Loading frame table with {len(self.frame_table)} records and {len(self.ds_config)} streams."]
        lines.append(f"Streaming data from {len(self.fs_dict)} Azure storage accounts: {list(self.fs_dict.keys())}")
        lines.append("Pipeline Steps:")
        for i, (col_name, fn_dict) in enumerate(self.ds_config.items(), start=1):
            ext_kwargs = format_kwargs(fn_dict.get("ext_kwargs", {}))
            fn_kwargs = format_kwargs(fn_dict.get("fn_kwargs", {}))
            concurrency = fn_dict.get("concurrency", self.map_concurrency)
            num_cpus = fn_dict.get("num_cpus", self.map_num_cpus)
            line_text = f"{i}. '{col_name}' → '{fn_dict['fn'].__name__}'"
            line_text += f" with extra kwargs: {ext_kwargs}" if not fn_kwargs else f" with func kwargs: {fn_kwargs}"
            line_text += f", concurrency: {concurrency}, num_cpus: {num_cpus}"
            lines.append(line_text)
        if self.augmentations:
            lines.append("Augmentations:")
            for i, (col_name, fn_list) in enumerate(self.augmentations.items(), start=1):
                for fn_dict in fn_list:
                    fn_kwargs = format_kwargs(fn_dict.get("fn_kwargs", {}))
                    concurrency = fn_kwargs.pop("concurrency", self.map_concurrency)
                    num_cpus = fn_kwargs.pop("num_cpus", self.map_num_cpus)
                    aug_text = f"{i}. '{col_name}' → '{fn_dict['fn'].__name__}' with func kwargs: {fn_kwargs}"
                    aug_text += f", concurrency: {concurrency}, num_cpus: {num_cpus}"
                    lines.append(aug_text)
        return "\n".join(lines)
