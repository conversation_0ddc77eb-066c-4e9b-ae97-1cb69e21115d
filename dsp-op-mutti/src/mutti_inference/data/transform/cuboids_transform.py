"""Utilities to deal with transformations of cuboids."""
from typing import TypeAlias

import numpy as np
import numpy.typing as npt
from scipy.spatial.transform import Rotation as R

FloatArray: TypeAlias = npt.NDArray[np.float16 | np.float32 | np.float64]


def convert_olf_quat_to_olf_euler(cuboids_olf_quat: FloatArray) -> FloatArray:
    """Convert the cuboids in the Open Label Format where rotations are given in terms of Euler angles.

    .. warning::
        ``cuboids_olf_format`` must be a 2D array, as the scripy function ``from_quat`` expects at most
        a 2D array.

    Args:
        cuboids_olf_quat: array of shape (batch_size, 10) with x, y, z, qa, qb, qc, qd, sx, sy, sz.
          - Center coordinates (x, y, z) in meters.
          - Quaternions (qa, qb, qc, qd) in scalar-last format (x, y, z, w).
          - Dimensions (sx, sy, sz) as (length, width, height) in meters.

    Returns:
        an array of shape (batch_size, 9) with the coordinates of the cuboids in the OLF-Euler format:
            (x, y, z, rx, ry, rz, sx, sy, sz), see: https://www.asam.net/index.php?eID=dumpFile&t=f&f=4566&token=9d976f840af04adee33b9f85aa3c22f2de4968dd#tab-34dfc570-989c-4f1f-84b9-c11831815bff

    .. note::
      the OLF format specified in the link has a slightly different order. The order used here is the correct one for
      the ALF writer.

    Raises:
        ValueError: if the input array is not 2D.
    """
    if not (ndims := cuboids_olf_quat.ndim) == 2:
        raise ValueError(f"The array with the cuboid must be two-dimensional. Got {ndims} dimensions.")
    cuboids = np.empty((cuboids_olf_quat.shape[0], 9), dtype=np.float32)

    quaternions = cuboids_olf_quat[:, 3:7]

    rots = R.from_quat(quaternions, scalar_first=False)  # explicit scipy defaults
    euler_angles = rots.as_euler("xyz", degrees=False)  # explicit scipy defaults

    cuboids[:, :3] = cuboids_olf_quat[:, :3]
    cuboids[:, 3:6] = euler_angles
    cuboids[:, 6:9] = cuboids_olf_quat[:, 7:10]
    return cuboids


def convert_xyz_sxsysz_rz_to_olf_euler(cuboids: FloatArray) -> FloatArray:
    """Convert cuboids from xyz_sxsysz_rz to olf_euler format.

    Args:
        cuboids: Array with last dimension of size 7,
                 representing cuboids in format (x, y, z, sx, sy, sz, rz).

    Returns:
        np.ndarray: Array with last dimension of size 9 in olf_euler format
                    (x, y, z, rx, ry, rz, sx, sy, sz).
    """
    x, y, z = cuboids[..., 0], cuboids[..., 1], cuboids[..., 2]
    sx, sy, sz = cuboids[..., 3], cuboids[..., 4], cuboids[..., 5]
    rz = cuboids[..., 6]

    zeros = np.zeros_like(x)
    return np.stack([x, y, z, zeros, zeros, rz, sx, sy, sz], axis=-1).astype(np.float32)


def reverse_transform_cuboids(
    cuboids: FloatArray, quaternion: FloatArray, translation: FloatArray = np.zeros(3)
) -> FloatArray:
    """Transform cuboids by reversing the provided coordinate transformation.

    .. warning::
        All input arrays must be at most two dimensional, as the scipy function ``from_quat`` expects at most
        a 2D array.

    Args:
        cuboids: ndarray of shape (N, 9) with rows [x, y, z, rx, ry, rz, sx, sy, sz].
        quaternion: [x, y, z, w] (scalar last, default for scipy).
        translation: [tx, ty, tz].

    Returns:
        transformed_cuboids: ndarray of shape (N, 9) in olf_euler format.
    """
    if cuboids.ndim > 2 or quaternion.ndim > 2:
        raise ValueError("Input arrays must be at most 2D.")
    # Create rotation object from quaternion (vehicle <- sensor)
    rotation = R.from_quat(quaternion)
    # Compute inverse rotation (sensor <- vehicle)
    rotation_inv = rotation.inv()

    # Extract cuboid parameters
    positions = cuboids[:, :3]  # x, y, z
    rotations = cuboids[:, 3:6]  # rx, ry, rz
    sizes = cuboids[:, 6:9]  # sx, sy, sz

    # Transform positions (sensor <- vehicle): x_sensor = R_inv * (x_cuboid - translation)
    positions_transformed = rotation_inv.apply(positions - translation)

    # Transform rotations (sensor <- vehicle): R_cuboid_sensor = R_inv * R_cuboid
    cuboid_rotations = R.from_euler("xyz", rotations, degrees=False)
    cuboid_rotations_sensor = rotation_inv * cuboid_rotations
    rotations_transformed = cuboid_rotations_sensor.as_euler("xyz", degrees=False)

    # Combine transformed parameters
    return np.hstack((positions_transformed, rotations_transformed, sizes))


def transform_points_to_cuboids(points: np.ndarray, cuboids: np.ndarray) -> np.ndarray:
    """Transform the coordinates of a set of points in the external reference frame to the frame defined by a cuboid.

    Args:
        points: array of shape (N, 3) with the coordinates of points with respect to the external reference frame.
        cuboids: array of shape (N_cuboids, 9) with the coordinates of the cuboids in the OLF-Euler format.
    """
    centers = cuboids[:, :3]
    euler_angles = cuboids[:, 3:6]
    # Compute rotation matrices for all cuboids, with respect to the extrinsic axes
    rot_mats = R.from_euler("xyz", euler_angles, degrees=False).as_matrix()
    # Transform points for all cuboids
    return np.matmul(points - centers[:, None, :], rot_mats)


def find_points_in_cuboids(cuboids: np.ndarray, points: np.ndarray) -> list[np.ndarray]:
    """Find points inside the given cuboids.

    Args:
        cuboids: array of shape (N, 9) representing the cuboids expressed in the same reference frame of `points`.
            The format of the cuboids is OLF-Euler.
        points: array of shape (Num_points, 3) with the coordinates of points in 3D.

    Returns:
        a list of arrays: for each cuboid returns a numpy array with the indices of the points inside the cuboid.
    """
    sizes = cuboids[:, 6:] / 2
    transformed_points = transform_points_to_cuboids(points, cuboids)
    # Create masks for points within each cuboid
    point_masks = np.all(np.abs(transformed_points) <= sizes[:, None, :], axis=2)

    # Get indices of points inside each cuboid
    indices = [np.where(point_masks[i])[0] for i in range(cuboids.shape[0])]

    return indices


def compute_boxes_timestamps_v(cuboids: np.ndarray, pcd_array: np.ndarray) -> np.ndarray:
    """Given a set of cuboids, calculate their timestamps as the median of the timestamps of the points in the cuboids.

    Args:
        cuboids: array of shape (N, 9), with the cuboids in OLF-Euler format.
        pcd_array: the PCDs for that frame. An array of shape (N_points, 5). The 3 first row are the
            coordinates of the points. Row 4 is the intensity and the last row is the timestamp.

    Returns:
        the timestamps.
    """
    pcd_timestamps = pcd_array[:, 4]  # float64
    pcd_points = pcd_array[:, :3]  # float64

    point_mask = find_points_in_cuboids(cuboids, pcd_points)
    timestamps = [np.median(pcd_timestamps[x]) if len(x) > 0 else np.median(pcd_timestamps) for x in point_mask]
    return np.asarray(timestamps).astype(np.int64)


def compute_box_timestamp(cuboid: np.ndarray, pcd_array: np.ndarray) -> float:
    """Compute timestamp for a given cuboid detected in the point cloud.

    Args:
        cuboid: cuboid expressed in the same reference frame of the PCD coordinates.
        pcd_array: corresponding pcd frame.

    Returns:
        the timestamp calculated for that cuboid.

    The function it is based on the method with the similar name in `liper_prediction_decoder.py`.
    """
    pcd_timestamps = pcd_array[:, 4]  # float64
    pcd_points = pcd_array[:, :3]  # float64

    # compute timestamps for each detection box
    center = cuboid[:3]
    euler_angles = cuboid[3:6]
    sizes = cuboid[6:] / 2
    rot_mat = R.from_euler("xyz", euler_angles, degrees=False).as_matrix()
    transformed_points = np.matmul((pcd_points - center), rot_mat)
    point_mask = np.all(np.abs(transformed_points) <= sizes, axis=1)
    if point_mask.sum() == 0:
        # No points found in bounding box. Falling back to median of all points
        return np.median(pcd_timestamps)
    else:
        return np.median(pcd_timestamps[point_mask])


def compute_boxes_timestamps(cuboids: np.ndarray, pcd_array: np.ndarray) -> np.ndarray:
    """Given a set of cuboids, calculate their timestamps as the median of the timestamps of the points in the cuboids.

    This is the serial version of `compute_boxes_timestamps_v`.

    Args:
        cuboids: array of shape (N, 9), with the cuboids in OLF-Euler format.
        pcd_array: the PCDs for that frame.

    Returns:
        the timestamps.
    """
    return np.asarray([compute_box_timestamp(box, pcd_array) for box in cuboids]).astype(np.int64)
