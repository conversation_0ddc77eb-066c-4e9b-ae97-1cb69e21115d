"""Functions to perform and facilitate the projection of 3D cuboids into 2D."""

import json

import numpy as np
import pandas as pd
import pyarrow as pa
from PIL import Image
from scipy.spatial.transform import Rotation

from mutti_data.definitions import sensor_set_alf_to_syscal_map
from mutti_data.schemas.enriched_schema import EnrichedSchema

from .external.dyo_common.data_types import CalibData
from .image import crop_with_context_margin

###
# DATA Transformations
###


class DetectionsAppender:
    """Append column with detections corresponding to each frame."""

    def __init__(self, object_table=None):
        """Initialize the class."""
        self.object_table = object_table

    def __call__(self, frame_batch: dict[str, any], frame_key_col: str = "frame_sha") -> pd.DataFrame:
        """Append detections to each frame in the batch."""
        frame_batch["detections"] = [
            self.object_table[self.object_table[frame_key_col] == frame_key].to_dict()
            for frame_key in frame_batch[frame_key_col]
        ]
        return frame_batch


class DetectionsAppenderFlex:
    """Append column with detections corresponding to each frame, matching on index columns."""

    def __init__(self, object_table: pd.DataFrame, index_columns: list[str]):
        """Initialize the class with the detections DataFrame."""
        self.object_table = object_table
        self.index_columns = index_columns
        # Ensure the index columns are present in the object_table
        missing_columns = set(index_columns) - set(object_table.columns)
        if missing_columns:
            raise ValueError(f"Missing columns in object_table: {missing_columns}")

    def __call__(
        self,
        frame_batch: dict[str, list[any]],
    ) -> dict[str, list[any]]:
        """Append detections to each frame in the batch (row in the frame_batch dictionary)."""
        detections_list = []
        obj_table_subset = self.object_table[self.index_columns]
        for index_values_tuple in zip(*(frame_batch[col] for col in self.index_columns)):
            current_index_series = pd.Series(index_values_tuple, index=self.index_columns)
            # Create the boolean mask. '.all(axis=1)' ensures all index columns match.
            mask = (obj_table_subset == current_index_series).all(axis=1)
            # Select the matching rows from the original object_table and convert to list of dicts
            detections_list.append(self.object_table[mask].to_dict(orient="dict"))
        frame_batch["detections"] = detections_list

        return frame_batch


def append_calib_to_frame_table(
    sequence_df: pd.DataFrame,
    frame_df: pd.DataFrame,
    source_sensor_name: str = "RefLidar",
    target_sensor_list: list[str] = ["FC1"],
) -> pd.DataFrame:
    """Append the sensor intrinsics (CalibData) and extrinsics (T & Quat) as three new columns to the frame_df."""
    from bosch_openlabel.model.v3 import BoschOpenLabelFormat as bolf

    # use a copy and parse the ALF data for each sequence only once (slow op)
    sequence_df = sequence_df.copy()
    sequence_df["alf_obj_data"] = sequence_df["sequence_alf_data"].map(bolf.parse_raw)

    def extract_extrinsics_from_syscal(row: pd.Series, sensor_name: str):
        # Parse the JSON string in the column
        json_obj = json.loads(row["split_sys_cal_parameters"])
        rotation = json_obj["devices"][sensor_name]["extrinsics"]["rotation"]
        translation = json_obj["devices"][sensor_name]["extrinsics"]["translation"]
        quat_xyzw = [
            rotation["quaternion_i"],
            rotation["quaternion_j"],
            rotation["quaternion_k"],
            rotation["quaternion_w"],
        ]
        t_xyz = [translation["x_m"], translation["y_m"], translation["z_m"]]
        # Return as a tuple so we can expand into multiple columns
        return quat_xyzw, t_xyz

    def extract_extrinsics_from_alf(row: pd.Series, sensor_name: str):
        # Parse the JSON string in the column
        alf_data = row["alf_obj_data"]
        coord_sys = alf_data.openlabel.coordinate_systems[sensor_name]
        quat_xyzw = coord_sys.pose_wrt_parent.quaternion
        t_xyz = coord_sys.pose_wrt_parent.translation
        # Return as a tuple so we can expand into multiple columns
        return quat_xyzw, t_xyz

    def create_calib_data_from_alf(row: pd.Series, sensor_name: str) -> CalibData:
        # Parse the JSON string in the column
        alf_data = row["alf_obj_data"]
        stream_props = alf_data.openlabel.streams[sensor_name].stream_properties
        params = stream_props.intrinsics
        imw, imh = stream_props.width_px, stream_props.height_px
        fu, fv = params.focal_length
        u0, v0 = params.principal_point

        if "TV" in sensor_name:
            camera_type = 2  # deformed cylindrical camera
            cut_angle = params.cut_angle_lower  # should be positive value
            # if that assumption fails, we would have to pass the lower cut angle as well
            assert params.cut_angle_upper == -params.cut_angle_lower
        else:
            camera_type = 1  # cylindrical camera
            cut_angle = None

        return CalibData(
            camera_type=camera_type,
            fu=fu,
            fv=fv,
            u0=u0,
            v0=v0,
            image_width=imw,
            image_height=imh,
            cut_angle=cut_angle,
            iso_vehicle_T_iso_sensor=None,
            camera_name=sensor_name,
        )

    # Append extrinsic and intrinsic params as columns
    if source_sensor_name in sensor_set_alf_to_syscal_map:
        # for the source sensor, we need to use the syscal data (assuming raw sensor data)
        sequence_df[["src_quat_xyzw", "src_t_xyz"]] = sequence_df.apply(
            extract_extrinsics_from_syscal,
            axis=1,
            result_type="expand",
            args=(sensor_set_alf_to_syscal_map[source_sensor_name],),  # map ALF sensor name to syscal name
        )
    else:
        # assume src is in vehicle coordinate system, e.g. if src_sensor_name is "Vehicle"
        sequence_df[["src_quat_xyzw", "src_t_xyz"]] = sequence_df.apply(
            lambda row: ([0.0, 0.0, 0.0, 1.0], [0.0, 0.0, 0.0]),  # use identity
            axis=1,
            result_type="expand",
        )
    for target_sensor_name in target_sensor_list:
        # for the target sensor, we need to use the ALF data (assuming everything is rectified)
        sequence_df[[f"{target_sensor_name}_quat_xyzw", f"{target_sensor_name}_t_xyz"]] = sequence_df.apply(
            extract_extrinsics_from_alf, axis=1, result_type="expand", args=(target_sensor_name,)
        )
        sequence_df[f"{target_sensor_name}_intrinsic"] = sequence_df.apply(
            create_calib_data_from_alf, axis=1, result_type="expand", args=(target_sensor_name,)
        )
    # Merge the dataframes on the 'sequence_custom_sha' column
    merged_df = frame_df.merge(
        sequence_df[
            [
                "sequence_custom_sha",
                "src_quat_xyzw",
                "src_t_xyz",
                *[f"{t}_intrinsic" for t in target_sensor_list],
                *[f"{t}_quat_xyzw" for t in target_sensor_list],
                *[f"{t}_t_xyz" for t in target_sensor_list],
            ]
        ],
        on="sequence_custom_sha",
        how="left",
        validate="m:1",
    )
    return merged_df


##
# IMAGE Transformations
##


def assemble_T_matrix(quat_xyzw, t_xyz) -> np.ndarray:
    """Assembles a 4x4 homogeneous transformation matrix from a quaternion and a translation vector.

    Args:
        quat_xyzw: A list or NumPy array representing the quaternion [qx, qy, qz, qw].
                   The scalar component 'qw' must be last.
        t_xyz: A list or NumPy array representing the translation vector [tx, ty, tz].
    """
    transformation_matrix = np.identity(4)
    transformation_matrix[0:3, 0:3] = Rotation.from_quat(quat_xyzw).as_matrix()
    transformation_matrix[0:3, 3] = t_xyz
    return transformation_matrix


def extract_detections_within_frame(batch: dict[str, any], context_margin: float = 0.0) -> pd.DataFrame:
    """Crop detections from each frame return detections together with crop."""
    list_df = []
    for ii, detections in enumerate(batch["detections"]):
        df_detections = pd.DataFrame(detections)
        pil_im = batch["frame_url_image"][ii]
        # for each detection in the frame, extract the crop
        for idx, detection in df_detections.iterrows():
            det_crop = crop_with_context_margin(pil_im, np.asarray(detection["bbox"]), context_margin)
            df_detections.loc[idx, "det_crop"] = det_crop
        list_df.append(df_detections)
    return pd.concat(list_df, axis=0)


def olf_cuboid_projection(
    cuboid: np.ndarray, target_calib: CalibData, vehicle_T_source: np.ndarray, vehicle_T_target: np.ndarray
) -> np.ndarray:
    """Project 3d cuboid in sensor coords to 2d by given calibration.

    Args:
        cuboid (np.ndarray): ndarray of shape [x, y, z, rx, ry, rz, sx, sy, sz] (OLF Euler format)
        target_calib (CalibData): Camera calibration
        vehicle_T_source (np.ndarray): 4x4 transformation matrix vehicle <- source
        vehicle_T_target (np.ndarray): 4x4 transformation matrix vehicle <- target
    Returns:
        np.ndarray: 2D Projected 3D Points
    """
    from .external.dyo_common.projection import points_3d_to_2d_projection

    # --- 0. Extract cuboid parameters ---
    positions = np.array(cuboid[0:3])
    rotations = cuboid[3:6]
    sizes = cuboid[6:9]

    # --- 1. Define 8 corners in local cuboid frame ---
    sx, sy, sz = sizes
    x_corners = [sx / 2, sx / 2, -sx / 2, -sx / 2, sx / 2, sx / 2, -sx / 2, -sx / 2]
    y_corners = [sy / 2, -sy / 2, -sy / 2, sy / 2, sy / 2, -sy / 2, -sy / 2, sy / 2]
    z_corners = [sz / 2, sz / 2, sz / 2, sz / 2, -sz / 2, -sz / 2, -sz / 2, -sz / 2]
    corners_local = np.vstack([x_corners, y_corners, z_corners])  # (3, 8)

    # --- 2. Transform corners to sensor frame ---
    rotation_cuboid = Rotation.from_euler("xyz", rotations, degrees=False)
    corners_source = rotation_cuboid.as_matrix() @ corners_local + positions.reshape(3, 1)  # (3, 8)

    # --- 3. Transform source sensor to target sensor frame ---
    # Make corners_source homogeneous (add row of ones) (4, 8)
    corners_source_hom = np.vstack((corners_source, np.ones((1, corners_source.shape[1]))))

    # target_T_source transforms points FROM source sensor frame TO target sensor frame
    target_T_source = np.linalg.inv(vehicle_T_target) @ vehicle_T_source  # (4, 4)
    corners_target_hom = target_T_source @ corners_source_hom  # (4, 8)

    # --- 4. Project corners from sensor frame to 2d image plane ---
    # transpose corners_target_hom for shape (8, 4)
    return np.asarray(points_3d_to_2d_projection(corners_target_hom.T, target_calib), dtype=np.float32)


def project_cuboid_detections_to_cameras(
    row: dict[str, any], target_sensor_list: list[str] = ["FC1"], valid_min_bbox_pixels: int = 256
) -> dict[str, any]:
    """Project cuboid detections onto each camera as 2d bboxes.

    Formats:
    - cuboids: ndarray of shape (N, 9) with rows [x, y, z, rx, ry, rz, sx, sy, sz].
    - quaternion: [x, y, z, w] (scalar last, default for scipy).
    - translation: [tx, ty, tz].

    """
    df_detections = pd.DataFrame(row["detections"])

    for sensor_name in target_sensor_list:
        # Create new columns for the projection results
        df_detections[f"{sensor_name}_bbox"] = pd.Series(dtype="object")
        df_detections[f"{sensor_name}_bbox_raw"] = pd.Series(dtype="object")
        df_detections[f"{sensor_name}_bbox_truncation"] = pd.Series(dtype="float")

        if not df_detections.empty:
            # target sensor intrinsic
            calib_data = row[f"{sensor_name}_intrinsic"]
            # sensor extrinsic (vehicle <- sensor)
            vehicle_T_source = assemble_T_matrix(row["src_quat_xyzw"], row["src_t_xyz"])
            vehicle_T_target = assemble_T_matrix(row[f"{sensor_name}_quat_xyzw"], row[f"{sensor_name}_t_xyz"])
            # we use the alf data instead of info from image loading
            # image_width, image_height = batch["image_width"][ii], batch["image_height"][ii]
            img_w, img_h = calib_data.image_width, calib_data.image_height

            # for each detection in the frame, extract the crop
            for idx, detection in df_detections.iterrows():
                cuboid = detection["cuboid"]

                # --- 1. Project cuboid corners onto the 2D image plane ---
                points_2d = olf_cuboid_projection(cuboid, calib_data, vehicle_T_source, vehicle_T_target)
                # print(f"points_2d: {points_2d}")
                # reshape as 1D array from (8, 2) -> (16,), so that schema is inferred correctly
                df_detections.at[idx, f"{sensor_name}_bbox_raw"] = points_2d.reshape(-1)

                # --- 2. Calculate 2D bounding box ---
                if points_2d.ndim >= 2 and points_2d.shape[0] > 0:
                    # Calculate the bounding box dimensions (for all points)
                    min_x, max_x = np.min(points_2d[:, 0]), np.max(points_2d[:, 0])
                    min_y, max_y = np.min(points_2d[:, 1]), np.max(points_2d[:, 1])

                    # Clamp bounding box to image dimensions
                    cl_min_x = max(0, min_x)
                    cl_min_y = max(0, min_y)
                    cl_max_x = min(img_w - 1, max_x)
                    cl_max_y = min(img_h - 1, max_y)
                    # Convert to olf format
                    bbw = cl_max_x - cl_min_x
                    bbh = cl_max_y - cl_min_y
                    x_center = (cl_min_x + cl_max_x) / 2
                    y_center = (cl_min_y + cl_max_y) / 2
                    # Basic sanity checks for the boxes, otherwise assume them invalid/truncated.
                    nonzero_size = min(bbh, bbw) > 0
                    sufficient_pixels = bbh * bbw > valid_min_bbox_pixels
                    # longest side no more than 50× the shortest
                    aspect_ratio_ok = nonzero_size and (max(bbh, bbw) / min(bbh, bbw) < 50)
                    # Flag problematic projections where the bounding box spans the whole image plane.
                    # At the extreme edges of a projection the left–right ordering of box corners can flip — think
                    # of an atlas map where Hawaii and Japan lie on opposite sides of the page even though
                    # they are neighbors on the globe. After clamping, such a box spans the whole image plane,
                    # so we treat it as an “ultra-wide” box which actually is behind the camera.
                    ultra_wide_or_behind_cam = bbw >= (img_w - 1)
                    if nonzero_size and sufficient_pixels and aspect_ratio_ok and not ultra_wide_or_behind_cam:
                        df_detections.at[idx, f"{sensor_name}_bbox"] = np.array([x_center, y_center, bbw, bbh])
                        # compare clamped and raw box area and compute ratio
                        raw_area = (max_y - min_y) * (max_x - min_x)
                        df_detections.at[idx, f"{sensor_name}_bbox_truncation"] = 1.0 - (bbh * bbw) / raw_area

                    else:  # Box collapsed after clamping OR is too wide/behind camera
                        df_detections.at[idx, f"{sensor_name}_bbox"] = np.array([-1.0] * 4)
                        df_detections.at[idx, f"{sensor_name}_bbox_truncation"] = 1.0
                else:  # Should not happen
                    df_detections.at[idx, f"{sensor_name}_bbox"] = np.array([-1.0] * 4)
                    df_detections.at[idx, f"{sensor_name}_bbox_truncation"] = 1.0
        # Convert the DataFrame to a dictionary and append to the list
        row["detections"] = df_detections.to_dict(orient="list")
    return row


def crop_n_unpack_bbox_detections(
    batch: dict[str, any],
    camera_col2sensor: dict[str, str] = {"fc1_rectified_frame_url": "FC1"},
    context_margin: float = 0.0,
    valid_max_bbox_truncation: float = 0.25,
) -> pd.DataFrame:
    """Crop bbox detections for each camera and return one row per detection.

    Each detection row contains one extra <sensor>_crop column for every sensor.
    """
    list_df = []
    for ii, detections in enumerate(batch["detections"]):
        df_detections = pd.DataFrame(detections)
        if df_detections.empty:
            print(f"Warning: No detections for frame {ii}.")
        # add a crop column for every sensor, filling it inside the loop
        for camera_col_name, sensor_name in camera_col2sensor.items():
            pil_im = batch[camera_col_name][ii]
            # extract the crop for each valid detection in the frame
            df_detections[f"{sensor_name}_crop"] = pd.Series(dtype="object")
            for idx, detection in df_detections.iterrows():
                bbox = detection[f"{sensor_name}_bbox"]
                trunc_val = detection[f"{sensor_name}_bbox_truncation"]
                # check if bbox is valid
                if trunc_val > valid_max_bbox_truncation:
                    # invalid -> store empty 1x1 black dummy image
                    df_detections.at[idx, f"{sensor_name}_crop"] = Image.new("RGB", (1, 1), (0, 0, 0))
                else:
                    det_crop = crop_with_context_margin(pil_im, np.asarray(bbox), context_margin)
                    df_detections.at[idx, f"{sensor_name}_crop"] = det_crop
        # append once per frame row
        list_df.append(df_detections)
    return pd.concat(list_df, axis=0, ignore_index=True)


def unpack_bbox_detections(batch: dict[str, any], target_schema: EnrichedSchema) -> pd.DataFrame:
    """Return unpacked detections as object table while discarding other frame table columns."""
    list_df = []
    for ii, detections in enumerate(batch["detections"]):
        df_detections = pd.DataFrame(detections)
        if df_detections.empty:
            print(f"Warning: No detections for frame {ii}.")
        list_df.append(df_detections)
    # concatenate DataFrames of all frames in batch
    df_full = pd.concat(list_df, axis=0)
    if target_schema is None:
        # return without schema validation - use with caution
        return df_full
    else:
        # enforce data types in the DataFrame for ColumnTypes where pa.Schema.from_pandas fails to correctly infer them
        col_types_to_check = ["BBox2D", "Cuboid", "AttributeScore", "ObjectScore", "AttributeValue"]
        for col_type in col_types_to_check:
            for col in target_schema.type2cols.get(col_type, []):
                pa_type = target_schema[col].pa_type
                if col in df_full.columns:
                    df_full[col] = df_full[col].astype(pd.ArrowDtype(pa_type))
        # merge data and target schema
        data_schema = pa.Schema.from_pandas(df_full, preserve_index=False)  # can only infer 1-dimensional array values
        unified_schema = pa.unify_schemas([target_schema.arrow_schema, data_schema])
        # return pyarrow table
        return pa.Table.from_pandas(df_full, schema=unified_schema, preserve_index=False)


def pick_n_best_rows_per_obj(group_df: pd.DataFrame, n: int = 5) -> pd.DataFrame:
    """Pick the cuboids (rows) per object group (uuid) with the top projected bboxes.

    The best rows (cuboids) are defined by the product of the confidence score (of the cuboid) and the area of the
    largest projected bounding box. Ignore all remaining boxes, by setting their truncation to 1.0.
    """
    if group_df.empty:
        return group_df
    df = group_df.copy()
    # find max area of all boxes in a row (per cuboid)
    # assume bboxes in OLF format [x_center, y_center, width, height]
    bbox_cols = [col for col in df.columns if col.endswith("_bbox")]
    max_area = df[bbox_cols].apply(lambda row: max(b[2] * b[3] for b in row), axis=1)

    # compute the product of confidence_score and max_area
    # pixels_per_token = 28**2  # [Qwen2-VL: 28**2, deepseek-vl: 16**2]
    # estimated_num_tokens = max_area / pixels_per_token. # TODO: use estimated_num_tokens instead of max_area?
    df["box_score"] = df["confidence_score"] * max_area
    if 0:
        df = df.nlargest(n, "box_score")  # keep the nlargest rows per group
        return group_df.loc[df.index]  # return selected rows from original group_df
    else:
        # for all rows except the ones with the n largest box scores, set truncation of boxes to 1.0
        truncation_cols = [col for col in df.columns if col.endswith("_truncation")]
        group_df.loc[df.index.difference(df.nlargest(n, "box_score").index), truncation_cols] = 1.0
        return group_df
