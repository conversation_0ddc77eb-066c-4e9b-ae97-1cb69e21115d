"""Code for decoding the output of DYO offline models."""
from typing import TypeAlias

import hydra
import numpy as np
import numpy.typing as npt
import pyarrow as pa
import pyarrow.compute as pc

from mutti_data.fields.object_level_data_fields import label_task as object_label_task
from mutti_data.schemas.dyo_autolabeling_schema import (
    dyo_offline_detection_object_level_output_schema_rich,
    dyo_offline_raw_pcds_detection_object_level_output_schema_rich,
)
from mutti_data.schemas.enriched_schema import EnrichedSchema
from mutti_inference.data.transform.cuboids_transform import (
    compute_boxes_timestamps_v,
    convert_olf_quat_to_olf_euler,
    reverse_transform_cuboids,
)

Float32Array: TypeAlias = npt.NDArray[np.float32]


class MultiModalDYODecoder:
    """Decode the output of the DYO offline model in the OLF format."""

    def __init__(
        self,
        label_task: str,
        frame_level_schema: EnrichedSchema | str,
        min_score_threshold: float,
        transform_output_cuboids_vehicle2sensor: bool = False,
        df_frames: None = None,  # dummy not needed
    ) -> None:
        """Initialize the object.

        Args:
            label_task: the label task for this usecase.
            frame_level_schema: the schema of the frame-level table used by the model predictor.
            min_score_threshold: objects with confidence score below or equal this value will be filtered out.
            transform_output_cuboids_vehicle2sensor: if True, cuboids are converted from the reference frame of vehicle
                (what the model expects as input and outputs) to the one of the RefLidar sensor.
        """
        self.label_task_tag = label_task
        self.boxes_score_thr = min_score_threshold
        self.transform_output_cuboids_vehicle2sensor = transform_output_cuboids_vehicle2sensor
        if isinstance(frame_level_schema, str):
            self.frame_level_schema = hydra.utils.get_object(frame_level_schema)
        else:
            self.frame_level_schema = frame_level_schema
        self.object_level_schema = (
            dyo_offline_raw_pcds_detection_object_level_output_schema_rich
            if self.transform_output_cuboids_vehicle2sensor
            else dyo_offline_detection_object_level_output_schema_rich
        )

    def convert_cuboids_olf_quat_to_olf_euler(self, batch: dict[str, Float32Array]) -> Float32Array:
        """Convert cuboids to ALF format."""

        def _conversion(cuboids_c: Float32Array, cuboids_s: Float32Array, cuboids_quat: Float32Array) -> Float32Array:
            # See the predictor for the keys corresponding to these values
            cuboids_olf_quat = np.empty((*cuboids_c.shape[:-1], 10), dtype=np.float32)
            cuboids_olf_quat[..., :3] = cuboids_c
            cuboids_olf_quat[..., 3:7] = cuboids_quat
            cuboids_olf_quat[..., 7:] = cuboids_s

            if cuboids_olf_quat.ndim != 3:
                raise ValueError(f"Cuboids must be given as 3D arrays. Got {cuboids_olf_quat.ndim} dimensions.")
            return np.vectorize(convert_olf_quat_to_olf_euler, signature="(m, n) -> (m, o)")(cuboids_olf_quat)

        cuboids_c = batch["cuboid_center"]
        cuboids_s = batch["cuboid_dimension"]
        cuboids_quat = batch["cuboid_quat"]
        return _conversion(cuboids_c, cuboids_s, cuboids_quat)

    def map_class_ids_to_classes(self, batch: dict[str, np.ndarray]) -> np.ndarray:
        """Get the types of the TSR (MainSign or AddSign) in the desired format.

        The numpy array will have the leading dimension equal to the batch size.
        """
        class_key = self.object_level_schema.type2cols["ObjectLabel"][0]
        type_enriched = self.object_level_schema[class_key]
        data = batch["cuboid_label"]
        if not isinstance(data, np.ndarray):
            raise TypeError(f"Cuboid labels should be numpy arrays. Got {type(data)}.")
        elif (lndim := data.ndim) != 3:
            raise ValueError(f"Labels should be given as a 2D array. Got {lndim}.")
        mapper = np.vectorize(type_enriched.label_set.get, otypes=[np.str_])
        return mapper(data)

    def prepare_record_batch(self, batch: dict[str, np.ndarray]) -> pa.RecordBatch | None:
        """Prepare a record batch for data belonging to a frame."""
        type2cols = self.object_level_schema.type2cols
        arrow_schema = self.object_level_schema.arrow_schema
        max_len_data = max([len(x) for x in batch.values()])
        arrays = []
        for name in self.object_level_schema.keys():
            data = batch[name]
            if data.shape[0] == 1:
                data = batch[name].repeat(max_len_data, axis=0)
            if data.shape[-1] == 1:
                data = np.squeeze(data, axis=-1)
            if data.ndim > 1:
                data = data.tolist()
            else:
                data = data
            array = pa.array(data)
            if isinstance(array, pa.lib.ChunkedArray):
                array = array.combine_chunks()
            arrays.append(array)
        # filter out low-confidence boxes
        score_key = type2cols["ObjectScore"][0]
        # There is a bug in the pyarrow version we are using, see: https://github.com/apache/arrow/pull/46057
        # Thus we must explicitly return None if there are no elements after filtering
        record_batch = pa.RecordBatch.from_arrays(arrays, schema=self.object_level_schema.arrow_schema)
        scores = record_batch[score_key]
        if len(scores.filter(pc.greater(scores, self.boxes_score_thr))) == 0:
            return None
        else:
            record_batch = record_batch.filter(pc.field(score_key) > self.boxes_score_thr)
        if self.transform_output_cuboids_vehicle2sensor:
            # Trasform cuboids coordinates to the reflidar reference frame.
            cuboid_key = type2cols["Cuboid"][0]
            cuboid_field = arrow_schema.field_by_name(cuboid_key)
            cuboids = np.asarray(record_batch[cuboid_key].to_pylist())
            quaternions = batch["src_quat_xyzw"].squeeze()
            translation = batch["src_t_xyz"].squeeze()
            t_cuboids = reverse_transform_cuboids(cuboids, quaternions, translation)
            t_cuboids_arr = pa.array(t_cuboids.tolist(), cuboid_field.type)
            record_batch = record_batch.set_column(
                record_batch.schema.get_field_index(cuboid_key), cuboid_key, t_cuboids_arr
            )
            # Modify the timestamps
            ts = compute_boxes_timestamps_v(t_cuboids, batch["ref_lidar_raw_frame_url_pcd_array"].squeeze())
            timestamp_key = type2cols["FrameTimestamp"][0]
            timestamp_field = arrow_schema.field_by_name(timestamp_key)
            ts_array = pa.array(ts, timestamp_field.type)
            record_batch = record_batch.set_column(
                record_batch.schema.get_field_index(timestamp_key), timestamp_key, ts_array
            )
        return record_batch

    def __call__(self, batch: dict[str, np.ndarray]) -> pa.Table:
        """Decode one batch of data."""
        type2cols = self.object_level_schema.type2cols

        cuboids_key = type2cols["Cuboid"][0]
        score_key = type2cols["ObjectScore"][0]
        class_key = type2cols["ObjectLabel"][0]

        scores = batch["cuboid_objectness"]
        mapped_classes = self.map_class_ids_to_classes(batch)

        cuboids_olf_euler = self.convert_cuboids_olf_quat_to_olf_euler(batch)

        # Write back results to the batch.
        batch[cuboids_key] = cuboids_olf_euler
        batch[score_key] = scores
        batch[class_key] = mapped_classes
        # Add the label task
        batch[object_label_task.name] = np.array([np.full(x.shape, self.label_task_tag) for x in mapped_classes])

        batch_size = scores.shape[0]
        minibatches = []
        for idx in range(batch_size):
            minibatch = {
                k: (v[idx] if (isinstance(v, np.ndarray) and v.ndim > 1) else np.array([v[idx]]))
                for k, v in batch.items()
            }
            if self.transform_output_cuboids_vehicle2sensor:
                # TODO: somehow ray passes the data in different formats. We should check why this happens.
                # For now we can use this workaround.
                pcd_array = batch["ref_lidar_raw_frame_url_pcd_array"][idx]
                if pcd_array.ndim == 2:
                    pcd_array = np.expand_dims(pcd_array, axis=0)
                minibatch["ref_lidar_raw_frame_url_pcd_array"] = pcd_array
            minibatches.append(minibatch)
        record_batches = [r_batch for x in minibatches if (r_batch := self.prepare_record_batch(x)) is not None]
        if record_batches:
            return pa.Table.from_batches(record_batches)
        else:
            return self.object_level_schema.arrow_schema.empty_table()
