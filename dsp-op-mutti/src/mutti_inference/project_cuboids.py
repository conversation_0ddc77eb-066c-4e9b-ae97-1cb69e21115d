"""Project 3D cuboids to cameras as 2D bboxes and store results in object-level table (parquet)."""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import logging
from pathlib import Path

import hydra
import pandas as pd
import ray
from omegaconf import DictConfig, OmegaConf

from mutti_data.definitions import TablesIOSpecs
from mutti_data.schemas.dyo_ac_autolabeling_schemas import (
    create_cuboid_projection_object_level_schema,
)
from mutti_data.schemas.enriched_schema import EnrichedSchema
from mutti_inference.data.transform.cuboid_projection import (
    DetectionsAppenderFlex,
    append_calib_to_frame_table,
    pick_n_best_rows_per_obj,
    project_cuboid_detections_to_cameras,
    unpack_bbox_detections,
)
from mutti_inference.utils import (
    consume_data_pipe,
    ensure_not_empty,
    init_ray,
    log_frame_header,
)

logger = logging.getLogger(__name__)


def prepare_tables(
    sequence_table: pd.DataFrame,
    frame_table: pd.DataFrame,
    object_table: pd.DataFrame,
    cfg: DictConfig,
) -> pd.DataFrame:
    """Prepare the frame table by adding calibration data and removing frames without objects."""
    # Get source_sensor_name and target_sensor_list
    data_cfg = cfg.data
    source_sensor_name = data_cfg.projection.source_sensor_name
    target_sensor_list = list(data_cfg.projection.col2sensor.values())

    # 1) add extrinsics/intrinsics to frame table (since this updates the index, do not perform after removing rows)
    frame_table = append_calib_to_frame_table(sequence_table, frame_table, source_sensor_name, target_sensor_list)

    # 2) remove frames without detections
    frame_idx_with_objects = object_table.frame_master_index.unique()
    logger.info(f"FRAME TABLE - Removing {len(frame_table) - len(frame_idx_with_objects)} frames without objects.")
    frame_table = frame_table[frame_table.frame_master_index.isin(frame_idx_with_objects)]

    # 3) diagnostics
    ensure_not_empty(frame_table, "FRAME TABLE")
    log_frame_header(frame_table)
    ensure_not_empty(object_table, "OBJECT TABLE")
    logger.info(f"OBJECT TABLE - Loaded with {len(object_table)} rows/detections.")

    return frame_table


def create_data_pipe(
    frame_table: pd.DataFrame,
    object_table: pd.DataFrame,
    cfg: DictConfig,
    object_table_schema: EnrichedSchema,
) -> ray.data.Dataset:
    """Run the cuboid projection pipeline."""
    data_cfg = cfg.data

    # Get source_sensor_name and target_sensor_list
    source_sensor_name = data_cfg.projection.source_sensor_name
    target_sensor_list = list(data_cfg.projection.col2sensor.values())
    logger.info(f"Source sensor: {source_sensor_name}")
    logger.info(f"Target sensor list: {target_sensor_list}")

    trgt_object_table_schema = create_cuboid_projection_object_level_schema(
        target_sensor_list, parent_obj_schema=object_table_schema
    )

    # 1. Load frame table
    ds_cuboids = ray.data.from_pandas(frame_table, override_num_blocks=8)

    # 2. Append object detections as new column
    ds_cuboids = ds_cuboids.map_batches(
        DetectionsAppenderFlex,
        fn_constructor_kwargs=dict(
            object_table=object_table, index_columns=["sequence_custom_sha", "frame_master_index"]
        ),
        batch_size=2,
        concurrency=1,
        num_cpus=0.5,
    )

    # 3. Project cuboid detections to cameras and store bboxes inside the detections dictionary (of each row)
    valid_min_bbox_pixels = data_cfg.projection.get("valid_min_bbox_pixels", 256)
    ds_cuboids = ds_cuboids.map(
        project_cuboid_detections_to_cameras,
        fn_kwargs=dict(target_sensor_list=target_sensor_list, valid_min_bbox_pixels=valid_min_bbox_pixels),
        num_cpus=0.5,
    )
    # 4. Unpack the bbox detections per row into a object table
    ds_cuboids = ds_cuboids.map_batches(
        unpack_bbox_detections,
        fn_kwargs=dict(target_schema=trgt_object_table_schema),
        batch_size=5,
        num_cpus=0.5,
    )
    # 5. Subsample bboxes: group by uuid (object) and pick the cuboids with the best projected boxes
    n_best = data_cfg.projection.get("n_best_rows_per_obj", -1)  # -1 means no subsampling by default
    if "uuid" in object_table_schema and n_best > 0:
        ds_cuboids = ds_cuboids.groupby(key="uuid").map_groups(
            pick_n_best_rows_per_obj, fn_kwargs=dict(n=n_best), num_cpus=0.5
        )

    return ds_cuboids


@hydra.main(config_path="configs", config_name="default.yaml", version_base="1.1")
def project_cuboids_to_cameras(module_cfg: DictConfig) -> None:
    """Project cuboids to cameras and output the results in object-level table (parquet)."""
    ########
    # Config Parsing
    logger.info(OmegaConf.to_yaml(module_cfg, resolve=True))
    cfg = module_cfg.mutti_inference
    data_cfg = cfg.data
    io_cfg = cfg.io

    # Get the table schemas
    frame_table_schema = hydra.utils.get_object(data_cfg.src.frame_table_schema)
    object_table_schema = hydra.utils.get_object(data_cfg.src.object_table_schema)

    ########
    # Sequence/Frame/Object Table Loading
    path_sequence_table = Path(data_cfg.base_path) / data_cfg.src.sequence_table_file
    path_frame_table = Path(data_cfg.base_path) / data_cfg.src.frame_table_file
    path_object_table = Path(data_cfg.base_path) / data_cfg.src.object_table_file

    sequence_table = pd.read_parquet(path_sequence_table)
    frame_table = pd.read_parquet(path_frame_table, schema=frame_table_schema.arrow_schema)
    object_table = pd.read_parquet(path_object_table, schema=object_table_schema.arrow_schema)

    #######
    # Preprocessing tables

    frame_table = prepare_tables(sequence_table, frame_table, object_table, cfg)

    ########
    # Run Cuboid Projection Pipeline
    logger.info(f"Run cuboid projection for `{cfg.label_task.get('name')}` task.")
    # Initialize for Ray Data
    init_ray()

    # Create the the data pipe
    ds_cuboids = create_data_pipe(frame_table, object_table, cfg, object_table_schema)

    # Execute pipeline and store resulting object table as parquet files
    folder_name = f"{frame_table['sequence_custom_sha'].iloc[0]}_{TablesIOSpecs.OBJECT_LEVEL_TAG}"
    output_path = Path(io_cfg.output_path) / folder_name
    table_out = consume_data_pipe(ds_cuboids, output_path)

    # Log the ray data statistics (after consumption)
    logger.info(ds_cuboids.stats())

    logger.info(f"Cuboid projection completed successfully. Results will be written to: {output_path}.")
    logger.info(f"Total frames processed: {len(frame_table)}.")
    logger.info(f"Number of detections saved to Parquet files: {table_out.num_rows}.")


if __name__ == "__main__":
    project_cuboids_to_cameras()  # pylint: disable=no-value-for-parameter
