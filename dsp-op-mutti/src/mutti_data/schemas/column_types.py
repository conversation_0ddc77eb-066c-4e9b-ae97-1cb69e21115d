"""Custom column types for defining enriched Arrow schemas."""

from typing import Any, Dict, List, Optional, Type, TypeVar, Union

import pyarrow as pa
from pydantic import BaseModel, ConfigDict, Field, model_validator

T = TypeVar("T", bound="DependencyMixin")


class DependencyMixin(BaseModel):
    """Mixin class to indicate that the column can be time-dependent and view-dependent."""

    is_time_dependent: bool = Field(default=True, description="Indicates if the column has a dependency on time.")
    is_view_dependent: Optional[bool] = Field(
        default=True, description="Indicates if the column has a dependency on the view/visibility field."
    )
    parent_shape_column: Optional[str] = Field(
        default=None,
        description="If this column is_time_dependent, this attribute "
        "contains the name of the GeometricShape column which is described by this column.",
    )

    @model_validator(mode="after")
    def check_time_independent(self: T) -> T:
        """Validate that parent_shape_column is not empty for view-dependent scores."""
        if self.is_time_dependent is False and (self.is_view_dependent is True or self.parent_shape_column):
            raise ValueError("Time-independent columns must be view-independent and have no parent_shape_column.")
        return self

    @model_validator(mode="after")
    def check_parent_shape_column(self: T) -> T:
        """Validate that parent_shape_column is not empty for view-dependent scores."""
        if self.is_view_dependent is True and not self.parent_shape_column:
            raise ValueError("parent_shape_column cannot be empty if is_view_dependent=True")
        return self

    @model_validator(mode="after")
    def check_is_time_dependent(self: T) -> T:
        """Validate that is_time_dependent is True for view-dependent scores."""
        if self.is_view_dependent is True and self.is_time_dependent is not True:
            raise ValueError("is_time_dependent must be True if is_view_dependent=True")
        return self


class ColumnType(BaseModel):
    """BaseModel to represent a column type in an EnrichedSchema.

    Attributes:
        pa_type (pyarrow.DataType): The Arrow data type of the column.
        nullable (bool): Specifies if the column can contain null values. Defaults to False.
        alf_name (Optional[str]): Optional ALF name for the column.
        src_key (Optional[str]): Optional source key to indicate the tensor this column is derived from.
    """

    pa_type: pa.DataType = Field(default=None, exclude=True)
    nullable: bool = Field(default=False)
    alf_name: Optional[str] = Field(default=None)
    src_key: Optional[str] = Field(default=None)
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def to_dict(self) -> Dict[str, Union[str, bool, int]]:
        """Convert the ColumnType object to a JSON-serializable dictionary."""
        data = self.model_dump(exclude={"pa_type"})
        return data

    @classmethod
    def from_dict(cls, data: Dict[str, Union[str, bool, int]], pa_type: pa.Field) -> "ColumnType":
        """Create a ColumnType object from a JSON-serialized dictionary and a pa.Field object."""
        data["pa_type"] = pa_type
        return cls.model_validate(data)


class Image(ColumnType):
    """Image column type.

    Attributes:
        pa_type (pyarrow.DataType): The Arrow data type. Defaults to pa.binary().
        as_paths (bool): Indicates if the image data is stored as file paths. Defaults to False.
    """

    pa_type: pa.DataType = pa.binary()
    as_paths: bool = False


class GeometricShape(ColumnType):
    """Geometric shape column type.

    Attributes:
        pa_type (pyarrow.DataType): The Arrow data type for binary data. Defaults to list of floats.
        shape_type (str): The type of geometric shape. Must be one of bbox, point2d, poly2d, poly3d, cuboid.
        ref_streams (Optional[str]): Name of reference stream. Defaults to None.
    """

    pa_type: pa.DataType = pa.list_(pa.float32())
    shape_type: str = Field(..., pattern="^bbox|poly2d_absolute|point2d|poly2d|poly3d|cuboid$")
    ref_stream: Optional[str] = Field(default=None)


class BBox2D(GeometricShape):
    """Bounding box 2D column type.

    Attributes:
        src_format (str): The source format of the bounding box. Must be one of pascal_voc, coco, yolo, olf.
        src_normalized (bool): Indicates if the source bounding box coordinates are normalized. Defaults to False.
    """

    shape_type: str = "bbox"
    src_format: Optional[str] = Field(default=None, pattern="^pascal_voc|coco|yolo|olf$")
    src_normalized: Optional[bool] = Field(default=None)


class PoleShape(GeometricShape):
    """Pole shape column type.

    Attributes:
        src_normalized (bool): Indicates if the source bounding box coordinates are normalized. Defaults to False.
    """

    shape_type: str = "poly2d_absolute"
    src_normalized: Optional[bool] = Field(default=None)


class Cuboid(GeometricShape):
    """Cuboid column type.

    Attributes:
        src_format (str): The format of the cuboid. Supported are `olf_euler`, `olf_quat`, and `xyz_sxsysz_rz`:
                          `olf_euler`: cuboid is defined as (x, y, z, rx, ry, rz, sx, sy, and sz)
                          `olf_quat`: cuboid is defined as (x, y, z, qa, qb, qc, qd, sx, sy, and sz)
                          'xyz_sxsysz_rz': cuboid is defined as (x, y, z, sx, sy, sz, rz)
                          Here is a legend:
                          x (m): Specifies the x-coordinate (in meters) of the 3D position of the center of the cuboid.
                          y (m): Specifies the y-coordinate (in meters) of the 3D position of the center of the cuboid.
                          z (m): Specifies the z-coordinate (in meters) of the 3D position of the center of the cuboid.
                          rx (rad): Specifies the roll (in radians), an Euler angle for rotation about the x-axis.
                          ry (rad): Specifies the pitch (in radians), an Euler angle for rotation about the y-axis.
                          rz (rad): Specifies the yaw (in radians), an Euler angle for rotation about the z-axis.
                          sx (m): Specifies the x-dimension (in meters) of the cuboid or the x-coordinate scale.
                          sy (m): Specifies the y-dimension (in meters) of the cuboid or the y-coordinate scale.
                          sz (m): Specifies the z-dimension (in meters) of the cuboid or the z-coordinate scale.
                          qa: Specifies the quaternion (x component) in non-unit form as in the SciPy convention.
                          qb: Specifies the quaternion (y component) in non-unit form as in the SciPy convention.
                          qc: Specifies the quaternion (z component) in non-unit form as in the SciPy convention.
                          qd: Specifies the quaternion (w component) in non-unit form as in the SciPy convention.
    Reference:
      - ASAM OpenLABEL v1.0.0 (12 Nov 2021): Table 16/17. Attributes of the 3D bounding box (cuboid) using Euler angles
        https://www.asam.net/index.php?eID=dumpFile&t=f&f=4566&token=9d976f840af04adee33b9f85aa3c22f2de4968dd#tab-34dfc570-989c-4f1f-84b9-c11831815bff
    """

    shape_type: str = "cuboid"
    src_format: Optional[str] = Field(default=None, pattern="^olf_euler|olf_quat|xyz_sxsysz_rz$")


class Score(ColumnType, DependencyMixin):
    """Score column type.

    Attributes:
        pa_type (pyarrow.DataType): The Arrow data type. Defaults to pa.float32().
        label_col (Optional[str]): The column name of the associated label. Defaults to None.
        is_attribute (bool): Indicates if the score is an attribute. Defaults to False.
    """

    pa_type: pa.DataType = pa.float32()
    label_col: Optional[str] = Field(default=None)
    is_attribute: bool = Field(default=False)


class ObjectScore(Score):
    """Object score column type."""

    is_attribute: bool = False


class AttributeScore(Score):
    """Attribute score column type."""

    is_attribute: bool = True


class Label(ColumnType, DependencyMixin):
    """Label column type.

    Attributes:
        pa_type (pyarrow.DataType): The Arrow data type. Defaults to pa.int32().
        is_attribute (bool): Indicates if the label is an attribute. Defaults to False.
        src_is_logits (bool): Indicates if the tensor associated with the label is logits. Defaults to False.
        num_classes (Optional[int]): Number of label classes.
        label_set (Optional[Dict[str, int]]): Dictionary mapping integers to label names.
        bg_class_list (Optional[List[int]]): List of background class indices.
        check_for_duplicates (bool): Whether the label set should be checked for duplicates. Defaults to False.
        alf_map (Optional[Dict[str, str]]): Mapping of label names to ALF names.

    Methods:
        str2int: Convert a label name to its corresponding index.
        int2str: Convert a label index to its corresponding name.
    """

    pa_type: pa.DataType = pa.int32()
    is_attribute: bool = Field(default=False)
    src_is_logits: Optional[bool] = Field(default=None)

    num_classes: int = Field(default=None)
    label_set: Optional[Dict[int, str]] = Field(default=None)
    class_hierarchy_map: Optional[Dict[str, str]] = Field(default=None)
    bg_class_list: Optional[List[int]] = Field(default=None)
    check_for_duplicates: bool = Field(default=True)

    alf_map: Optional[Dict[str, str]] = Field(default=None)

    _names: List[str]
    _int2str: Dict[int, str]
    _str2int: Dict[str, int]

    def model_post_init(self, __context: Any) -> None:
        """Post-init method to initialize the label set and class names."""
        if self.label_set is None and self.num_classes is None:
            raise ValueError("One of label_set or num_classes should be provided.")
        if self.label_set is not None:
            self._names = list(self.label_set.values())
            self.num_classes = len(self._names)
        elif self.num_classes is not None:
            self._names = [str(i) for i in range(self.num_classes)]
            self.label_set = {i: name for i, name in enumerate(self._names)}

        self._int2str = self.label_set
        self._str2int = {name: i for i, name in enumerate(self._names)}

        if self.check_for_duplicates and len(self._int2str) != len(self._str2int):
            raise ValueError(
                "There are duplicate label names in the mapping (names<->indices). "
                "Please ensure a bijective mapping OR set 'check_for_duplicates' to False to avoid the check for this "
                "label of the EnrichedSchema."
            )

    def str2int(self, label: str) -> int:
        """Convert a label name to its corresponding index."""
        lbl_idx = self._str2int.get(label)
        if lbl_idx is None:
            raise ValueError(f"Label {label} not found in the label set.")
        else:
            return lbl_idx

    def int2str(self, label_idx: int) -> str:
        """Convert a label index to its corresponding name."""
        lbl_name = self._int2str.get(label_idx)
        if lbl_name is None:
            raise ValueError(f"Label index {label_idx} not found in the label set.")
        else:
            return lbl_name


class ObjectLabel(Label):
    """Object label column type."""

    is_attribute: bool = False
    is_time_dependent: bool = False
    is_view_dependent: bool = False


class AttributeLabel(Label):
    """Attribute column type."""

    is_attribute: bool = True


class Value(ColumnType, DependencyMixin):
    """Value column type."""


class AttributeValue(Value):
    """Attribute value column type."""

    pa_type: pa.DataType = pa.list_(pa.float32())
    alf_type: str = Field(default="vec", description="Type of attribute in the OLF convention (boolean|num|text|vec)")


INFER_COLUMN_TYPES: Dict[str, Type[ColumnType]] = {
    Image.__name__: Image,
    Label.__name__: Label,
    ObjectLabel.__name__: ObjectLabel,
    AttributeLabel.__name__: AttributeLabel,
    Score.__name__: Score,
    ObjectScore.__name__: ObjectScore,
    AttributeScore.__name__: AttributeScore,
    Value.__name__: Value,
    AttributeValue.__name__: AttributeValue,
    GeometricShape.__name__: GeometricShape,
    BBox2D.__name__: BBox2D,
    Cuboid.__name__: Cuboid,
}

__all__ = [name for name in INFER_COLUMN_TYPES.keys()]
