"""Schemas used by DYO AC inference components."""

# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================
import copy
from typing import Optional

import pyarrow as pa

from mutti_data.encodings.xflow_label_mapper import TaskLabelMapper

from ..fields.object_level_data_fields import coordinate_system, uuid
from ..schemas.column_types import (
    AttributeLabel,
    AttributeScore,
    AttributeValue,
    BBox2D,
    Cuboid,
    ObjectLabel,
    ObjectScore,
)
from ..schemas.enriched_schema import EnrichedSchema
from .mdm_search_column_types import (
    FrameMasterIndex,
    SequenceCustomSha,
    SequenceLabelRequestID,
)
from .mdm_search_enriched_schemas import StreamNames, SubStreamNames

# Enriched schema for DYO AC input object level
dyo_ac_object_level_input_schema_rich = EnrichedSchema(
    {
        "sequence_custom_sha": SequenceCustomSha(stream=StreamNames.Any, substream=SubStreamNames.Any),
        "sequence_label_request_id": SequenceLabelRequestID(stream=StreamNames.Any, substream=SubStreamNames.Any),
        "frame_master_index": FrameMasterIndex(stream=StreamNames.Any, substream=SubStreamNames.Any),
        "uuid": uuid,
        "coordinate_system": coordinate_system,
        "cuboid": Cuboid(ref_stream="RefLidar", src_format="olf_quat"),
        "confidence_score": AttributeValue(
            pa_type=pa.float32(),
            alf_name="confidence_score",
            parent_shape_column="cuboid",
            alf_type="num",
        ),
        "velocity": AttributeValue(
            alf_name="velocity",
            parent_shape_column="cuboid",
            alf_type="vec",
        ),
        "acceleration": AttributeValue(
            alf_name="acceleration",
            parent_shape_column="cuboid",
            alf_type="vec",
        ),
        "yaw_rate": AttributeValue(
            alf_name="yaw_rate",
            parent_shape_column="cuboid",
            alf_type="vec",
        ),
        "class": ObjectLabel(
            pa_type=pa.string(),
            label_set=TaskLabelMapper.from_task_name("object_detection").to_dict(),
            class_hierarchy_map=TaskLabelMapper.from_task_name("object_detection").get_hierarchy_dict(),
        ),
        "class_confidence_score": ObjectScore(
            label_col="class",
            alf_name="label_confidence",
            is_time_dependent=False,
            is_view_dependent=False,
        ),
    }
)


# Enriched schema for DYO AC with cuboid projection columns
def create_cuboid_projection_object_level_schema(
    sensor_list: list[str], parent_obj_schema: EnrichedSchema = dyo_ac_object_level_input_schema_rich
) -> EnrichedSchema:
    """Create a cuboid projection object level schema for the given sensor list and parent schema."""
    # Use parent schema as base and add columns for the projections to each sensor
    object_schema = copy.deepcopy(parent_obj_schema)
    for sensor_name in sensor_list:
        object_schema[f"{sensor_name}_bbox"] = BBox2D(
            src_key="cuboid", src_format="olf", src_normalized=False, ref_stream=sensor_name
        )
        object_schema[f"{sensor_name}_bbox_truncation"] = AttributeScore(
            src_key="cuboid", parent_shape_column=f"{sensor_name}_bbox"
        )
    return object_schema


# Enriched schema for DYO AC object level with cuboid projection + attribute classification columns
def create_dyo_attribute_classification_object_level_schema(
    attr_list: list[str],
    sensor_list: list[str],
    parent_obj_schema: EnrichedSchema = dyo_ac_object_level_input_schema_rich,
    fused_attr_use_case: Optional[str] = None,
) -> EnrichedSchema:
    """Create a DYO AC object level schema for the given sensor list and parent schema."""
    object_schema = create_cuboid_projection_object_level_schema(
        sensor_list=sensor_list, parent_obj_schema=parent_obj_schema
    )
    for attr_name in attr_list:
        for sensor_name in sensor_list:
            if attr_name == "occlusion":
                object_schema[f"{sensor_name}_attr_{attr_name}"] = AttributeLabel(
                    pa_type=pa.string(),
                    alf_name="occlusion",
                    parent_shape_column=f"{sensor_name}_bbox",
                    label_set={0: "0.0", 1: "20.0", 2: "40.0", 3: "60.0", 4: "80.0", 5: "99.0"},
                    nullable=True,
                )
            if attr_name == "vehicle_type":
                label_set_list = [
                    "Bus",
                    "CarTrailer",
                    "Excavator",
                    "Truck",
                    "TruckTrailer",
                    "PassengerCar",
                    "Train",
                    "AgriculturalVehicle",
                    "Forklift",
                    "FunVehicle",
                ]
                object_schema[f"{sensor_name}_attr_{attr_name}"] = AttributeLabel(
                    pa_type=pa.string(),
                    alf_name="vehicle_type",
                    parent_shape_column=f"{sensor_name}_bbox",
                    label_set={i: k for i, k in enumerate(label_set_list)},
                    nullable=True,
                )
        # Add fused column if this is the selected use case
        if fused_attr_use_case == attr_name:
            fused_col_name = f"fused_{attr_name}"
            if attr_name == "vehicle_type":
                object_schema[fused_col_name] = AttributeLabel(
                    pa_type=pa.string(),
                    is_view_dependent=False,
                    is_time_dependent=False,
                    label_set={i: k for i, k in enumerate(label_set_list)},
                    nullable=True,
                )
            elif attr_name == "occlusion":
                object_schema[fused_col_name] = ObjectScore(
                    is_view_dependent=False,
                    label_set={0: "0.0", 1: "20.0", 2: "40.0", 3: "60.0", 4: "80.0", 5: "99.0"},
                    nullable=True,
                )
    return object_schema


def create_dyo_attribute_classification_alf_object_level_schema(
    attr_list: list[str],
    sensor_list: list[str],
    parent_obj_schema: EnrichedSchema,
    cols_to_remove: Optional[list[str]] = [],
) -> EnrichedSchema:
    """Create a DYO AC object level schema for ALF Writing.

    - Remove columns with should not be written to the ALF at all
    - For Vehicle Type, make the fused column (with the final prediction) the new ObjectLabel column

    Args:
        attr_list (list[str]): List of attributes to remove low-level attribute related columns from the schema.
        sensor_list (list[str]): List of sensors to remove cuboid-projection related columns from the schema.
        parent_obj_schema (EnrichedSchema): Parent schema to base the new schema on.
        cols_to_remove (Optional[list[str]]): List of additinoal columns to remove from the parent schema.

    Returns:
        EnrichedSchema: The modified object level schema ready for ALF writing.
    """
    object_schema = copy.deepcopy(parent_obj_schema)

    # Remove columns that should not be considered for ALF writing
    for sensor_name in sensor_list:
        # Remove cuboid projection columns
        cols_to_remove.append(f"{sensor_name}_bbox")
        cols_to_remove.append(f"{sensor_name}_bbox_truncation")
        # Remove attribute columns
        for attr_name in attr_list:
            cols_to_remove.append(f"{sensor_name}_attr_{attr_name}")
    for col in cols_to_remove:
        object_schema.pop(col)

    # Make fused column the new ObjectLabel column, remove the old ObjectLabel column
    if "vehicle_type" in attr_list:
        class_col = parent_obj_schema.type2cols["ObjectLabel"][0]
        object_schema["fused_vehicle_type"] = parent_obj_schema[class_col]
        object_schema.pop(class_col)

    return object_schema


# Enriched schemas for DYO AC object level with cuboid projection + attribute classification columns
dyo_ac_occlusion_fc1_tv_object_lvl_schema_rich = create_dyo_attribute_classification_object_level_schema(
    attr_list=["occlusion"],
    sensor_list=["FC1", "TV_front", "TV_right", "TV_left", "TV_rear"],
)

dyo_ac_occlusion_fc1_tv_object_lvl_schema_arrow = dyo_ac_occlusion_fc1_tv_object_lvl_schema_rich.arrow_schema

dyo_ac_vehicle_type_fc1_tv_object_lvl_schema_rich = create_dyo_attribute_classification_object_level_schema(
    attr_list=["vehicle_type"],
    sensor_list=["FC1", "TV_front", "TV_right", "TV_left", "TV_rear"],
    fused_attr_use_case="vehicle_type",
)

dyo_ac_vehicle_type_fc1_tv_object_lvl_schema_arrow = dyo_ac_vehicle_type_fc1_tv_object_lvl_schema_rich.arrow_schema

# Enriched schemas for DYO AC object level without low-level cuboid projection + attribute classification columns for ALF Writing
dyo_ac_vehicle_type_fc1_tv_alf_object_lvl_schema_rich = create_dyo_attribute_classification_alf_object_level_schema(
    attr_list=["vehicle_type"],
    sensor_list=["FC1", "TV_front", "TV_right", "TV_left", "TV_rear"],
    parent_obj_schema=dyo_ac_vehicle_type_fc1_tv_object_lvl_schema_rich,
    cols_to_remove=["class_confidence_score"],
)

dyo_ac_vehicle_type_fc1_tv_alf_object_lvl_schema_arrow = (
    dyo_ac_vehicle_type_fc1_tv_alf_object_lvl_schema_rich.arrow_schema
)
