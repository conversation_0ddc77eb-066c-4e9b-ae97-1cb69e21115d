_target_: mutti_processing.processing_steps.processing_attribute_label_fusion.AttributeLabelFusion
cfg:
  object_table_schema: mutti_data.schemas.dyo_ac_autolabeling_schemas.dyo_ac_object_level_input_schema_rich
  step_id: attr_fusion
  sequence_id: 
  use_case: vehicle_type # "vehicle_type" or "occlusion"
  vote_strategy: majority  # "majority" or "min", "max"
  validate_against_parent_class: True
  output_dir_base_path:
  output_dir_relative_path:
  col2sensor:
    fc1_rectified_frame_url: FC1
    tv_rear_rectified_frame_url: TV_rear
    tv_front_rectified_frame_url: TV_front
    tv_left_rectified_frame_url: TV_left
    tv_right_rectified_frame_url: TV_right
