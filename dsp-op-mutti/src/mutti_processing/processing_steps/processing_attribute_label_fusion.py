# ============================================================
#  C O P Y R I G H T
# ------------------------------------------------------------
#  Copyright (c) 2025 by <PERSON>. All rights reserved.
#
#  The reproduction, distribution and utilization of this file as
#  well as the communication of its contents to others without express
#  authorization is prohibited. Offenders will be held liable for the
#  payment of damages. All rights reserved in the event of the grant
#  of a patent, utility model or design.
# ============================================================
"""
Attribute Label Fusion Module.

This module implements logic to fuse attribute predictions from multiple sensors
into a single label. Fusion is performed
using configurable strategies: "majority", "min", or "max".

The module supports both categorical and numeric attributes. Numeric attributes
(e.g., occlusion, truncation) are handled using float comparisons and can be
aggregated via min/max or weighted voting based on visible bounding box area.

Key Components:
- `AttributeLabelFusion`: Main processing step for applying label fusion.
- `aggregate`: Function that applies the configured aggregation method to a group.
- `compute_visible_area`: Utility to weight predictions based on visible bounding box area.
- `is_valid_bbox`: Validates bounding box geometry and content.

The module integrates into a structured Hydra-based pipeline with defined schemas.
"""
import logging
from typing import List, Optional, Sequence, Union

import hydra
import numpy as np
import pandas as pd
import pyarrow as pa

logger = logging.getLogger(__name__)
logger.setLevel(logging.INFO)
from omegaconf import DictConfig

from mutti_data.schemas.column_types import ObjectLabel
from mutti_data.schemas.dyo_ac_autolabeling_schemas import (
    create_dyo_attribute_classification_object_level_schema,
)
from mutti_processing.processing_step import (
    ProcessingData,
    ProcessingStep,
    ProcessingStepSchemas,
)


def is_valid_bbox(bbox: Union[Sequence[float], np.ndarray]) -> bool:
    """
    Check whether a bounding box is valid.

    A bounding box is considered valid if:
    - It is a list, tuple, or NumPy array of exactly 4 elements.
    - It is not the sentinel value [-1, -1, -1, -1], which typically indicates an invalid or missing box.
    - The width (bbox[2]) and height (bbox[3]) are strictly greater than 0.

    Args:
        bbox (list | tuple | np.ndarray): The bounding box in the format [x, y, width, height].

    Returns:
        bool: True if the bounding box is valid, False otherwise.
    """
    if not isinstance(bbox, (list, tuple, np.ndarray)) or len(bbox) != 4:
        return False
    if all(v == -1 for v in bbox) or bbox[2] <= 0 or bbox[3] <= 0:
        return False
    return True


def compute_visible_area(bbox: Union[Sequence[float], np.ndarray], truncation: float) -> float:
    """
    Compute the visible area of a bounding box after accounting for truncation.

    The visible area is calculated as:
        visible_area = width * height * (1.0 - truncation)

    Args:
        bbox (list | tuple | np.ndarray): The bounding box in the format [x, y, width, height].
        truncation (float): A float between 0.0 and 1.0 indicating the proportion of the box that is truncated
                            (0.0 = fully visible, 1.0 = fully truncated).

    Returns:
        float: The visible area of the bounding box after applying truncation.
    """
    area = bbox[2] * bbox[3]
    return area * (1.0 - truncation)


def aggregate(
    group: pd.DataFrame, use_case: str, agg_method: str, target_sensors: List[str]
) -> Optional[Union[str, float]]:
    """
    Aggregate attribute labels for a group of rows using the configured voting strategy.

    This method computes a fused label from multiple sensor predictions within a group,
    based on one of the following strategies:

    - "majority": Uses a weighted vote where weights are based on the visible area
    of each bounding box (adjusted for truncation).
    - "min": Selects the minimum label among valid predictions.
    - "max": Selects the maximum label among valid predictions.

    A label is considered valid only if:
    - The corresponding bounding box is valid.
    - The label is not NaN.

    Args:
        group (pd.DataFrame): Grouped rows from the input DataFrame, typically grouped by
                            object ID, frame index, or both depending on attribute dependency.x
        use_case (str): Attribute column suffix (e.g., 'vehicle_type').
        agg_method (str): One of {'majority', 'min', 'max'}.
        target_sensors (List[str]): List of sensor names.

    Returns:
        str | int | None: The fused attribute label for the group, or None if no valid labels
                        were found.
    """
    NUMERIC_USE_CASES = {"occlusion", "truncation"}
    numeric = use_case in NUMERIC_USE_CASES
    label_list = []
    score_list = []

    for sensor in target_sensors:
        try:
            labels = group[f"{sensor}_attr_{use_case}"]
            bboxes = group[f"{sensor}_bbox"]
        except KeyError:
            continue
        truncs = group.get(f"{sensor}_bbox_truncation", 0.0)

        mask = labels.notna() & bboxes.apply(is_valid_bbox)
        if not mask.any():
            continue
        valid_labels = labels[mask]
        valid_bboxes = bboxes[mask]
        valid_truncs = truncs[mask] if isinstance(truncs, pd.Series) else truncs

        if numeric:
            valid_labels = pd.to_numeric(valid_labels, errors="coerce").dropna()

        if valid_labels.empty:
            continue

        label_list.append(valid_labels)

        if agg_method == "majority":
            weights = valid_bboxes.combine(valid_truncs, compute_visible_area)
            score_list.append(weights)
        else:
            score_list.append(pd.Series(1.0, index=valid_labels.index))

    if not label_list or not score_list:
        return None

    labels = pd.concat(label_list)
    scores = pd.concat(score_list)

    if agg_method == "majority":
        df = pd.DataFrame({"label": labels, "score": scores})
        fused_label = df.groupby("label", sort=False)["score"].sum().idxmax()
    elif agg_method == "min":
        fused_label = labels.min()
    elif agg_method == "max":
        fused_label = labels.max()
    else:
        raise NotImplementedError(f"Unsupported vote_strategy: {agg_method}")
    return float(fused_label) if numeric else str(fused_label)


class AttributeLabelFusion(ProcessingStep):
    """Apply attribute label fusion."""

    def __init__(self, cfg: DictConfig):
        """
        Initialize the AttributeLabelFusion processing step.

        This constructor sets up schema definitions, target sensors, aggregation
        strategy, and fusion logic configuration based on the provided Hydra config.

        Args:
            cfg (DictConfig): Hydra configuration containing:
                - use_case (str): The attribute to fuse (e.g., 'occlusion', 'vehicle_type').
                - vote_strategy (str): Aggregation strategy, one of {'majority', 'min', 'max'}.
                - col2sensor (dict): Mapping from column names to sensor identifiers.
                - object_table_schema (str): Fully-qualified path to the base object table schema.
        """
        super().__init__(cfg)
        self.object_table_schema = hydra.utils.get_object(cfg.object_table_schema)
        self.use_case = cfg.use_case
        self.vote_strategy = cfg.vote_strategy  # "majority", "min", or "max"
        self.col2sensor = cfg.col2sensor
        self.validate_against_parent_class = cfg.validate_against_parent_class
        self.target_sensors = list(self.col2sensor.values())
        self.input_schema = self._build_schema(fused=False)
        self.output_schema = self._build_schema(fused=True)
        self.fused_col_name = f"fused_{self.use_case}"
        self.attr_schema = self.output_schema.get(self.fused_col_name)
        if self.validate_against_parent_class:
            class_field, class_col_type = next(
                (
                    (col_name, col_type)
                    for col_name, col_type in self.object_table_schema.items()
                    if isinstance(col_type, ObjectLabel)
                ),
                (None, None),
            )
            if class_field is None or class_col_type is None:
                raise ValueError(
                    "ObjectLabel column is required in schema when 'validate_against_parent_class' is True."
                )
            if not getattr(class_col_type, "class_hierarchy_map", None):
                raise ValueError(f"ObjectLabel column '{class_field}' is missing a valid 'class_hierarchy_map'.")
            self.class_field = class_field
            self.class_hierarchy_map = class_col_type.class_hierarchy_map

        # Define schema input/output for ProcessingStep interface
        self.input_schemas = ProcessingStepSchemas(
            sequence_level_schema=None,
            frame_level_schema=None,
            object_level_schema=self.input_schema.arrow_schema,
        )
        self.output_schemas = ProcessingStepSchemas(
            sequence_level_schema=None,
            frame_level_schema=None,
            object_level_schema=self.output_schema.arrow_schema,
        )

    def _build_schema(self, fused: bool = False) -> pa.schema:
        return create_dyo_attribute_classification_object_level_schema(
            attr_list=[self.use_case],
            sensor_list=self.target_sensors,
            parent_obj_schema=self.object_table_schema,
            fused_attr_use_case=self.use_case if fused else None,
        )

    def _process_object_level_table(self, table: pa.Table) -> pa.Table:
        """
        Perform attribute label fusion on the object-level table.

        This method processes the input object-level table by applying label
        fusion across different views and/or time steps, based on the attribute's schema
        configuration. If the attribute is view-dependent, the fusion is skipped.

        Args:
            table (pa.Table): Input object-level table containing per-sensor predictions.

        Returns:
            pa.Table: A new table with fused labels added, or the original table if no processing is needed.
        """
        if table.num_rows == 0:
            return table
        table = table.select(self.input_schema.arrow_schema.names)
        table = table.cast(self.input_schema.arrow_schema, safe=True)
        df = table.to_pandas()
        if self.attr_schema.is_view_dependent:
            logger.warning(f"[INFO] Use case '{self.use_case}' is view-dependent. Skipping voting.")
            return table

        group_keys = self._get_groupby_keys()
        if group_keys is None:
            return table

        df_voted = self._label_fusion(df, group_keys)
        return pa.Table.from_pandas(df_voted, schema=self.output_schema.arrow_schema, preserve_index=False)

    def _get_groupby_keys(self) -> Optional[List[str]]:
        """Return groupby keys based on attribute dependency.

        The groupby keys determine how objects are grouped when aggregating predictions,
        based on whether the attribute is time-dependent and/or view-dependent.

        Cases:
        1. is_time_dependent=True, is_view_dependent=False:
            - Attribute varies over time but not across views.
            - Group by ["sequence_custom_sha", "uuid", "frame_master_idx"].

        2. is_time_dependent=False, is_view_dependent=False:
            - Attribute is static over time and views (e.g., vehicle type).
            - Group by ["sequence_custom_sha", "uuid"].

        3. is_time_dependent=True, is_view_dependent=True:
            - Attribute varies across both time and views.
            - No grouping is needed — prediction is per-frame, per-view.

        Args:
            self: The class instance, which must have an `attr_schema` attribute.

        Returns:
            list[str] or None: List of groupby keys, or None if no grouping required.

        """
        if self.attr_schema.is_view_dependent:
            return None
        if self.attr_schema.is_time_dependent:
            return ["sequence_custom_sha", "uuid", "frame_master_index"]
        return ["sequence_custom_sha", "uuid"]

    def _label_fusion(self, df: pd.DataFrame, group_keys: List[str]) -> pd.DataFrame:
        """
        Perform group-wise attribute label fusion using the selected voting strategy.

        Args:
            df (pd.DataFrame): Input DataFrame containing per-sensor attribute predictions.
            group_keys (list[str]): List of column names to group by (e.g., object ID, frame index).

        Returns:
            pd.DataFrame: A copy of the input DataFrame with an additional column containing
                        the fused labels.
        """
        voted_series = (
            df.groupby(group_keys)
            .apply(
                aggregate,
                use_case=self.use_case,
                agg_method=self.vote_strategy,
                target_sensors=self.target_sensors,
            )
            .rename(self.fused_col_name)
        )
        df = df.join(voted_series, on=group_keys)

        if self.use_case in {"occlusion"}:
            df[self.fused_col_name] = df[self.fused_col_name].astype("float32")
            return df

        if self.validate_against_parent_class:

            def validate_fused_label(row):
                parent_class = row[self.class_field]
                fused_subclass = row[self.fused_col_name]
                # Validate that the fused subclass is not None and in the class hierarchy map
                if pd.isna(fused_subclass) or fused_subclass not in self.class_hierarchy_map:
                    return parent_class
                fused_parent = self.class_hierarchy_map[fused_subclass]
                # If the fused parent is None, return the parent class
                if fused_parent is None:
                    return parent_class
                return fused_subclass if fused_parent == parent_class else parent_class

            df[self.fused_col_name] = df.apply(validate_fused_label, axis=1)
        return df

    def _call(self, inputs: ProcessingData) -> ProcessingData:
        return ProcessingData(
            sequence_level_table=inputs.sequence_level_table,
            frame_level_table=inputs.frame_level_table,
            object_level_table=self._process_object_level_table(inputs.object_level_table),
        )
