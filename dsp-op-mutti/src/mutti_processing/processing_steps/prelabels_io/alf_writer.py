"""Define processing steps related to the writing of pre-labels."""

# ============================================================================================================
# C O P Y R I G H T
# ------------------------------------------------------------------------------------------------------------
# \copyright (C) 2024 Robert Bosch GmbH and Cariad SE. All rights reserved.
# ============================================================================================================

from __future__ import annotations

import logging
from pathlib import Path

import pandas as pd
import pyarrow as pa
from omegaconf import DictConfig
from tqdm import trange
from tqdm.contrib.logging import logging_redirect_tqdm

from mutti_data.fields.sequence_level_data_fields import (
    sequence_custom_sha,
    sequence_generated_prelabels_format,
    sequence_generated_prelabels_url,
)
from mutti_data.schemas.alf_prelabel_schemas import alf_prelabel_seq_schema
from mutti_data.schemas.enriched_schema import EnrichedSchema
from mutti_data.utils import get_schema_from_registry
from mutti_processing.processing_step import (
    ProcessingData,
    ProcessingStep,
    ProcessingStepSchemas,
)
from mutti_utils.io.paths import from_path_to_possible_cloud_path

from .alf_model_builder import (
    ALFModelBuilderFromALF,
    ALFModelBuilderFromALFRaw,
    ALFModelBuilderV1,
    assign_uuid_to_objects,
)

logger = logging.getLogger(__name__)


class ALFPrelabelsJsonWriter(ProcessingStep):
    """Write prelabels in GA json format for one sequence."""

    # The actual input schema for object-level data are decided at runtime.
    input_schemas = ProcessingStepSchemas(None, None, None)
    # For the sequence, the output table just contains the url of the prelabels in the GA json format.
    output_schemas = ProcessingStepSchemas(alf_prelabel_seq_schema, None, None)

    # Prelabels format
    prelabels_format: str = "ALFJson"

    def __init__(self, cfg: DictConfig) -> None:
        """Initialize the object, including the schema for object-level data.

        Args:
          cfg: the hydra config.
            Object-specific keys are:
              - object_level_schema_name: the schema name for object-level data to be used
                 by a particular instance of this class.
              - label_task_name: The label task name. Sould be aligned with `object_level_schema_name`.
              - prelabels_output_dir_base_path: the base path for the directory where the prelabels will be saved.
              - prelabels_output_dir_relative_path: the correspodning relative path.
              - frame_mode: If True, one ALF file per frame will be written instead of one per sequence.
                 Default is False.
              - use_raw_pcd: if True, the writer will take into account that raw pcd files are used.
        """
        super().__init__(cfg)

        self.frame_mode = cfg.get("frame_mode", False)
        self.fm_filename_pattern = cfg.get("frame_mode_filename_pattern", "frame_{frame_idx}_{frame_sha}.json")

        self.build_from_alf = cfg.get("build_from_alf", False)
        self.use_raw_pcd = cfg.get("use_raw_pcd", True)

        self.label_task_name = cfg.label_task_name
        self.object_level_schema = get_schema_from_registry(cfg.object_level_schema_name)

        self.input_schemas = ProcessingStepSchemas(
            self.input_schemas.sequence_level_schema,
            self.input_schemas.frame_level_schema,
            self.object_level_schema,
        )

        self.prelabels_output_dir_path = Path(cfg.prelabels_output_dir_base_path).joinpath(
            cfg.prelabels_output_dir_relative_path
        )
        # Caches for exporting internal processing results to the output arrow tables
        self.seq_name: str = None
        self.sequence_prelabels_path: str = None
        self.object_uuid_column: list = None
        self.frame_lbl_paths_column: list = None

    def _write_prelabels_json_file(self, df_seq: pd.DataFrame, df_frame: pd.DataFrame, df_obj: pd.DataFrame) -> None:
        """Write the ALF json with the prelabels in `prelabels_output_dir_path`."""
        if self.build_from_alf:
            if self.use_raw_pcd:
                alf_builder = ALFModelBuilderFromALFRaw(
                    self.seq_name,
                    df_seq,
                    df_frame,
                    df_obj,
                    self.object_level_schema,
                    self.label_task_name,
                )
            else:
                alf_builder = ALFModelBuilderFromALF(
                    self.seq_name,
                    df_seq,
                    df_frame,
                    df_obj,
                    self.object_level_schema,
                    self.label_task_name,
                )
        else:
            alf_builder = ALFModelBuilderV1(
                self.seq_name,
                df_seq,
                df_frame,
                df_obj,
                self.object_level_schema,
                self.label_task_name,
            )
        alf_model = alf_builder.to_model()

        # write a nicely formatted json file
        output_file = self.prelabels_output_dir_path.joinpath(f"{self.seq_name}.json")
        json_string = alf_model.json(exclude_none=True, exclude_unset=True, indent=4)
        output_file.write_bytes(json_string.encode("utf-8"))

        self.sequence_prelabels_path = from_path_to_possible_cloud_path(output_file)
        logger.info(
            f"Saved {len(df_obj)} labels of sequence {self.seq_name} as single ALF "
            f"at path: {self.sequence_prelabels_path}."
        )

    def _write_prelabels_json_file_per_frame(self, df_frame: pd.DataFrame, df_obj: pd.DataFrame) -> None:
        """Write one ALF json file per frame with the prelabels in `prelabels_output_dir_path`."""
        # iterate over the frame_table and write one ALF json file per frame
        self.frame_lbl_paths_column = []
        with logging_redirect_tqdm():
            for frame_idx in trange(len(df_frame)):
                frame_sha = df_frame["frame_sha"][frame_idx]
                alf_builder = ALFModelBuilderV1(
                    self.seq_name,
                    None,
                    df_frame[df_frame["frame_sha"] == frame_sha],
                    df_obj[df_obj["frame_sha"] == frame_sha],
                    self.object_level_schema,
                    self.label_task_name,
                )
                alf_model = alf_builder.to_model()
                # get output file path
                batch_id = df_frame["batch_id"][frame_idx] if "batch_id" in df_frame.columns else "NA"
                variables = {
                    "frame_idx": frame_idx,
                    "frame_sha": frame_sha,
                    "batch_id": batch_id,
                }
                output_file = self.prelabels_output_dir_path.joinpath(self.fm_filename_pattern.format(**variables))
                # write a nicely formatted json file
                json_string = alf_model.json(exclude_none=True, exclude_unset=True, indent=4)
                output_file.write_bytes(json_string.encode("utf-8"))
                # frame label paths to frame-level table
                self.frame_lbl_paths_column.append(from_path_to_possible_cloud_path(output_file))
        # add parent folder for sequence-level table
        self.sequence_prelabels_path = from_path_to_possible_cloud_path(output_file.parent)  # "frame_mode"
        logger.info(
            f"Saved {len(df_obj)} labels of sequence {self.seq_name} as {len(df_frame)} ALF "
            f"files at path: {self.sequence_prelabels_path}."
        )

    def _process_sequence_level_table(self, table: pa.Table) -> pa.Table:
        """Add path and format of the generated pre-label file to sequence-level table."""
        table = table.append_column(
            sequence_generated_prelabels_url,
            pa.array(
                [self.sequence_prelabels_path] * table.num_rows,
                sequence_generated_prelabels_url.type,
            ),
        ).append_column(
            sequence_generated_prelabels_format,
            pa.array(
                [self.prelabels_format] * table.num_rows,
                sequence_generated_prelabels_format.type,
            ),
        )
        return table

    def _process_frame_level_table(self, table: pa.Table) -> pa.Table:
        """Add paths of the generated pre-label files as column to frame-level table."""
        if self.frame_lbl_paths_column is not None:
            table = table.append_column("prelabels_path", pa.array(self.frame_lbl_paths_column, pa.string()))
        return table

    def _process_object_level_table(self, table: pa.Table) -> pa.Table:
        """Add the object uuid column to the object-level table."""
        if self.object_uuid_column is not None and "uuid" not in table.column_names:
            table = table.append_column("uuid", pa.array(self.object_uuid_column, pa.string()))
        return table

    def _call(self, inputs: ProcessingData) -> ProcessingData:
        """Write the prelabels and return a sequence-level table that points to the location of the prelabels."""
        # Ensure that all required tables are provided
        required_tables = [
            inputs.sequence_level_table,
            inputs.frame_level_table,
            inputs.object_level_table,
        ]
        tab_seq, tab_frame, tab_obj = required_tables
        if any(table is None for table in required_tables):
            raise ValueError("Must provide tables at all levels.")
        object_level_schema_rich = EnrichedSchema.from_arrow_schema(self.object_level_schema)
        if len(object_level_schema_rich.type2cols["ObjectLabel"]) > 1:
            raise ValueError("Object level schema must contain only one ObjectLabel column.")
        # sequence & frame tables must have at least one row, while object table can be empty with valid schema
        if all(table.num_rows > 0 for table in [tab_seq, tab_frame]):
            self.seq_name = tab_seq[sequence_custom_sha.name].unique().to_pylist()[0]
            # convert to pd.DataFrame
            df_seq = tab_seq.to_pandas()
            df_frame = tab_frame.to_pandas()
            df_obj = tab_obj.to_pandas(types_mapper=pd.ArrowDtype, ignore_metadata=True)
            # assign uuids to each object (as required by ALF)
            df_obj = assign_uuid_to_objects(df_obj)
            self.object_uuid_column = df_obj.uuid.tolist()
            # write the prelabels
            self.prelabels_output_dir_path.mkdir(parents=True, exist_ok=True)
            if self.frame_mode:
                self._write_prelabels_json_file_per_frame(df_frame, df_obj)
            else:
                self._write_prelabels_json_file(df_seq, df_frame, df_obj)
        return ProcessingData(
            self._process_sequence_level_table(tab_seq),
            self._process_frame_level_table(tab_frame),
            self._process_object_level_table(tab_obj),
        )
