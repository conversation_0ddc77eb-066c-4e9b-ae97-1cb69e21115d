"""The script sets up a pipeline using mutti primitives for running batch attribute classification jobs from messages in an Azure queue."""
from __future__ import annotations

import logging
import sys
import warnings
from multiprocessing import cpu_count
from pathlib import Path

import hydra
import pandas as pd
import pyarrow as pa
import ray
from mdm_viper_lib.dataloop_integration.logger.standards import (
    ProcessingResult,
    ProcessingState,
)
from omegaconf import DictConfig, OmegaConf

from mutti_data.credentials import get_azure_credentials
from mutti_data.fields.sequence_level_data_fields import (
    sequence_custom_sha as sequence_custom_sha_field,
)
from mutti_inference import infer_attributes, project_cuboids
from mutti_inference.utils import consume_data_pipe
from mutti_pipeline.scripts.pipeline_components import dbx_writing
from mutti_pipeline.scripts.pipeline_components.alf_writing import (
    AlfWriterConfig,
    write_batch_of_alfs,
)
from mutti_pipeline.scripts.pipeline_components.data import (
    BlobClientConfig,
    MuttiDatafetchBaseConfig,
    delete_message_from_the_queue,
    download_messages_from_queue,
    fetch_batch_of_data,
    get_queue_consumer,
)
from mutti_pipeline.scripts.pipeline_components.inference import (
    ModelDownloadConfig,
    download_model,
)
from mutti_pipeline.scripts.pipeline_components.logging import (
    setup_logger_mutti,
    setup_ray_logger,
    setup_ray_remote_logger,
)
from mutti_processing.processing_step import ProcessingData
from mutti_processing.processing_steps.processing_attribute_label_fusion import (
    AttributeLabelFusion,
)

logger = logging.getLogger("Dataloop DYO Attribute Classification autolabeling pipeline")

# Silence ResourceWarnings from ray log files
warnings.filterwarnings(
    "ignore",
    category=ResourceWarning,
)


setup_ray_logger()


def setup_ray_remote_logger_dyo_ac() -> None:
    """Wrap the function to set up ray logger with the proper logger name."""
    setup_ray_remote_logger(logger.name)


def project_cuboids_to_cameras(
    sequence_table: pd.DataFrame, frame_table: pd.DataFrame, object_table: pd.DataFrame, cfg: DictConfig
) -> pa.Table:
    """Project cuboids to cameras and output the results in object-level table (parquet)."""
    object_table_schema = hydra.utils.get_object(cfg.data.src.object_table_schema)
    frame_table = project_cuboids.prepare_tables(sequence_table, frame_table, object_table, cfg)
    ds_cuboids = project_cuboids.create_data_pipe(frame_table, object_table, cfg, object_table_schema)
    return consume_data_pipe(ds_cuboids, None)


def inference_attributes(frame_table: pd.DataFrame, object_table: pd.DataFrame, cfg: DictConfig):
    """Run inference on the object-level table and return the results."""
    frame_table_schema = hydra.utils.get_object(cfg.data.src.frame_table_schema)
    object_table_schema = hydra.utils.get_object(cfg.data.src.object_table_schema)
    frame_table, object_table = infer_attributes.prepare_tables(frame_table, object_table, cfg)
    ds_attributes = infer_attributes.create_data_pipe(
        frame_table, object_table, cfg, frame_table_schema, object_table_schema
    )
    return consume_data_pipe(ds_attributes, None)


@hydra.main(
    version_base="1.3",
    config_path="configs",
    config_name="dataloop_dyo_ac_script_config_deepseek.yaml",
)
def run_dyo_attribute_classification_from_queue(cfg: DictConfig) -> None:
    """Process a batch of messages from some Azure queue and save the ALFs with autolabels to disk."""
    ray.init(runtime_env={"worker_process_setup_hook": setup_ray_remote_logger_dyo_ac}, ignore_reinit_error=True)
    setup_logger_mutti()
    logger.info(OmegaConf.to_yaml(cfg, resolve=True))
    # enable fallback to Arrow object extension type for compatibility
    ray.data.DataContext.get_current().enable_fallback_to_arrow_object_ext_type = True
    # Set up debug mode, if desired
    global DEBUG_MODE
    DEBUG_MODE = cfg.get("debug_mode", False)

    # Start by downloading the model asynchronously
    azure_cfg = cfg.mutti_inference.model_download
    azureml_cfg = ModelDownloadConfig(
        workspace_name=azure_cfg.workspace.workspace_name,
        resource_group=azure_cfg.workspace.resource_group,
        subscription_id=azure_cfg.workspace.subscription_id,
        model_name=azure_cfg.model_registry.model_name,
        model_version=azure_cfg.model_registry.model_version,
    )
    model_download_path = Path(cfg.mutti_inference.model.base_path).joinpath(
        azureml_cfg.model_name, str(azureml_cfg.model_version)
    )
    download_model_future = download_model.remote(azureml_cfg, model_download_path, logger)

    # Set up the queue consumer
    current_service = cfg.queue_client.current_service
    env_type = cfg.queue_client.environment
    max_messages = cfg.queue_client.max_messages
    num_cpus = cpu_count()
    max_ray_tasks = cfg.max_ray_tasks if cfg.max_ray_tasks is not None else num_cpus
    logger.info(
        "Mutti workflow start."
        "\nDownloading locally up to %d ALFs for the service %s. "
        "\nUp to %d ray tasks will be run at a time (number of available cores: %d).",
        max_messages,
        current_service,
        max_ray_tasks,
        num_cpus,
        extra=dict(
            processing_state=ProcessingState.WORKFLOW_START,
        ),
    )
    visibility_timeout = cfg.queue_client.message_visibility_timeout
    queue_consumer = get_queue_consumer(current_service, env_type)
    # Get messages from the queue
    processed_messages = download_messages_from_queue(queue_consumer, max_messages, visibility_timeout, logger=logger)
    if not processed_messages:
        logger.info(
            "No message could be downloaded from the queue for the workflow {cfg.queue_client.current_service}. Maybe the queue is empty?"
        )
        sys.exit(0)
    initial_label_request_ids = [x.label_request_id for x in processed_messages]
    logger.info("Done, %d messages were successful read from the queue.", len(processed_messages))

    logger.info("Fetching the data for the batch of %d messages...", len(processed_messages))
    mutti_data_search_cfg = MuttiDatafetchBaseConfig(
        pipeline=cfg.mutti_data_search.pipeline,
        sequence_table_schema=cfg.mutti_data_search.sequence_table_schema,
        frame_table_schema=cfg.mutti_data_search.frame_table_schema,
        object_table_schema=cfg.mutti_data_search.object_table_schema,
        view_filter=False,  # Not needed for DBX search but mandatory arg
        frame_isp="PYISP",  # Not needed for DBX search but mandatory arg
        stream_identification=cfg.mutti_data_search.stream_identification,
    )

    try:
        processed_messages, sequence_table, frame_table, object_table = fetch_batch_of_data(
            processed_messages, mutti_data_search_cfg, max_ray_tasks, logger=logger
        )
    except Exception as e:
        logger.exception(
            "No messages could be processed! Raised error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            exc_info=e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
    else:
        logger.info("Data could be fetched for %d messages.", len(processed_messages))
        fetched_label_request_ids = [x.get_label_request_id() for x in processed_messages]
        logger.info(
            "The messages have the following label request ids [message (ALF)]: \n%s",
            "\n".join([f"{msg_id}" for msg_id in fetched_label_request_ids]),
        )

    logger.info("Running Cuboid projection...")
    try:
        object_table = project_cuboids_to_cameras(
            sequence_table=sequence_table.to_pandas(),
            frame_table=frame_table.to_pandas(),
            object_table=object_table.to_pandas(),
            cfg=cfg.mutti_inference,
        )
    except Exception as e:
        logger.exception(
            "Cuboid projection error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
    else:
        logger.info("Cuboid projection completed successfully.")

    # If model has not been downloaded yet, block until it is.
    ray.get(download_model_future)
    # Raise error if the model is not there, since we cannot continue with the pipeline.
    if not Path(cfg.mutti_inference.model.path).exists():
        logger.exception("No model was found at %s. Exiting...", cfg.mutti_inference.model.path)
        raise FileNotFoundError(
            f"No model at {cfg.mutti_inference.model.path}! Maybe something went wrong with the download?"
        )
    else:
        logger.info("Model successfully retrieved. Running inference...")

    logger.info(
        "Predicting %d records (rows) for %d sequences (unique sequence_custom_shas)...",
        len(frame_table),
        len(sequence_table[sequence_custom_sha_field.name].unique()),
    )
    try:
        object_table = inference_attributes(
            frame_table=frame_table.to_pandas(),
            object_table=object_table.to_pandas(types_mapper=pd.ArrowDtype, ignore_metadata=True),
            cfg=cfg.mutti_inference,
        )
    except Exception as e:
        logger.exception(
            "Inference error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
    else:
        processed_data = ProcessingData(sequence_table, frame_table, object_table)
    logger.info("Inference completed successfully.")

    # Step: Attribute Label Fusion
    try:
        logger.info("Starting Attribute Label Fusion step...")
        label_fuser = AttributeLabelFusion(cfg.label_fusion)
        processed_data = label_fuser(processed_data)
        logger.info("Attribute Label Fusion completed successfully.")
    except Exception as e:
        logger.exception(
            "Label fusion error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e

    if DEBUG_MODE:
        out_path = (
            f"{cfg.blob_upload.azure_blob_account_name}/"
            f"{cfg.blob_upload.azure_blob_container_name}/"
            f"{cfg.blob_upload.azure_blob_relative_name}"
        )
        logger.info("Writing ALFs to output %s...", out_path)
        blob_client_cfg = BlobClientConfig(
            cfg.blob_upload.azure_blob_account_name,
            cfg.blob_upload.azure_blob_container_name,
            cfg.blob_upload.azure_blob_relative_name,
            cfg.blob_upload.parquet_blob_account_name,
            cfg.blob_upload.parquet_blob_container_name,
            cfg.blob_upload.parquet_blob_relative_name,
        )
    else:
        blob_client_cfg = None
    # Prepare config for the ALF writer and the message router
    alf_writer_cfg = AlfWriterConfig(
        step_id=cfg.alf_writer.step_id,
        sequence_id=None,
        object_level_schema_name=cfg.alf_writer.object_level_schema_name,
        label_task_name=cfg.alf_writer.label_task_name,
        output_dir_base_path=cfg.alf_writer.get("output_dir_base_path", None),
        output_dir_relative_path=cfg.alf_writer.get("output_dir_relative_path", None),
        prelabels_output_dir_base_path=cfg.alf_writer.get("prelabels_output_dir_base_path", None),
        prelabels_output_dir_relative_path=cfg.alf_writer.get("prelabels_output_dir_relative_path", None),
        build_from_alf=cfg.alf_writer.get("build_from_alf", True),
        frame_mode=cfg.alf_writer.get("frame_mode", False),
        use_raw_pcd=cfg.alf_writer.get("use_raw_pcd", False),
    )
    message_router_cfg = None
    mdm_upload = cfg.get("mdm_upload", False)
    processed_messages = write_batch_of_alfs(
        processed_messages,
        processed_data,
        alf_writer_cfg,
        message_router_cfg,
        blob_client_cfg,
        mdm_upload,
        max_ray_tasks,
        logger=logger,
    )

    involved_models = [
        f"{cfg.mutti_inference.model_download.workspace.workspace_name}/"
        f"{cfg.mutti_inference.model_download.model_registry.model_name}:"
        f"{cfg.mutti_inference.model_download.model_registry.model_version}"
    ]
    if ray.is_initialized():
        # If ray is initialized, we can use the ray shutdown to clean up resources,
        # otherwise quite often the following table writing pushes us
        # over the memory limit and we get OOM errors.
        logger.info("Shutting down ray...")
        ray.shutdown()

    dbx_writing.write_successful_to_databricks(
        processed_messages,
        processed_data.sequence_level_table,
        processed_data.frame_level_table,
        processed_data.object_level_table,
        cfg,
        credential=get_azure_credentials(),
        mutti_pipeline="dyo_ac",
        involved_models=involved_models,
    )

    if not DEBUG_MODE:
        for msg in processed_messages:
            delete_message_from_the_queue(msg, queue_consumer, logger=logger)

    # Log summary of successfull and unsuccessfull messages
    successful_ids = [x.get_label_request_id() for x in processed_messages]
    unsuccessful_ids = list(set(fetched_label_request_ids) - set(successful_ids))

    all_msgs = "\n".join(initial_label_request_ids)
    not_feched_msgs = "\n".join(list(set(initial_label_request_ids) - set(fetched_label_request_ids)))

    logger.info("Initial message label request ids retrieved from the queue:\n%s", all_msgs)
    logger.info(
        "These label request ids could be fetched [message (ALF)]:\n%s",
        "\n".join([f"{msg_id}" for msg_id in fetched_label_request_ids]),
    )
    logger.info("The messages with the following label request ids could not be fetched:\n%s", not_feched_msgs)
    logger.info(
        "ALFs could be written correctly for (message | ALF):\n%s",
        "\n".join([f"{msg_id}" for msg_id in successful_ids]),
    )
    logger.info(
        "Something went wrong for [message (ALF)]:\n%s\nMessages for these requests were left in the queue.",
        "\n".join([f"{msg_id}" for msg_id in unsuccessful_ids]),
    )
    logger.info(
        "Mutti workflow end",
        extra=dict(return_value=ProcessingResult.OK, processing_state=ProcessingState.WORKFLOW_END),
    )


if __name__ == "__main__":
    try:
        run_dyo_attribute_classification_from_queue()
    except Exception as e:
        logger.exception(
            "Unknown error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            exc_info=e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
