defaults:
  - override hydra/job_logging: none  # do not allow hydra to configure the logging.
  - override hydra/hydra_logging: none  # do not allow hydra to configure the logging.

max_ray_tasks: null 
debug_mode: true  # If set to True, artifacts will be uploaded to the blob storage. Use it for debugging failed messages in local run.

queue_client:
  current_service: "mutti_dyo_ac"
  message_visibility_timeout: 20  # test purposes: in production set to 1-2h or so
  max_messages: 3
  environment: "prod"  # "prod"

mutti_data_search:
  pipeline: mutti_data_search.pipelines.DBX_PIPELINE
  sequence_table_schema: mutti_data.schemas.mdm_search_enriched_schemas.DBX_FULL_ENRICHED_SCHEMA_SEQUENCE
  frame_table_schema: mutti_data.schemas.mdm_search_enriched_schemas.DBX_FC1_TV_ENRICHED_SCHEMA_FRAME
  object_table_schema: mutti_data.schemas.dyo_ac_autolabeling_schemas.dyo_ac_object_level_input_schema_rich
  stream_identification:
    fc1: rectified
    tv_front: rectified
    tv_left: rectified
    tv_rear: rectified
    tv_right: rectified

mutti_inference:
  model_download:
    workspace:
      workspace_name: "offline_perception"                           # the name of the workspace
      resource_group: "vdeep-ct-prod"                                # the workspace with the workspace
      subscription_id: "c4f1c7f3-9206-409f-a333-5b89a516e5dd"        # Azure subscription id for these resources
    model_registry: # deepseek-vl2-tiny-vehicle_type-merged
      model_name: "deepseek-vl2-tiny-occlusion-merged"            # the name of the model as on the registry
      model_version: 4                                               # the version of this model to pull
  label_task:
    name: attr_dyo_vehicle_type_occlusion_classification 
    guide_version: "1.1.0"
  data:
    projection:
      valid_max_bbox_truncation: 0.25  # should be in [0, 1)
      valid_min_bbox_pixels: 256  # boxes with fewer pixels are ignored
      n_best_rows_per_obj: 5  # number of rows(cuboids with best projected boxes) per object to keep, -1 means no subsampling
      source_sensor_name: Vehicle  # use vehicle coord (center rear axel)  
      col2sensor:
        fc1_rectified_frame_url_image: FC1
        tv_rear_rectified_frame_url_image: TV_front
        tv_front_rectified_frame_url_image: TV_right
        tv_left_rectified_frame_url_image: TV_left
        tv_right_rectified_frame_url_image: TV_rear
    image_prep:
      crop_context_margin: 0.5
      augment_fn: mutti_inference.data.transform.image.prepare_image_cols_deepseek
    loading_parameters:
      load_image:
        concurrency: 4
    src:
      frame_table_schema: ${mutti_data_search.frame_table_schema}
      object_table_schema: ${mutti_data_search.object_table_schema}
  inference:
    batch_size: 20
    num_available_gpus: 1
  model:
    name: ${mutti_inference.model_download.model_registry.model_name}
    base_path: ${oc.env:PWD}/models
    path: ${mutti_inference.model.base_path}/${mutti_inference.model.name}/4/deepseek_aug  # important: no trailing slash
    predictor_type: mutti_inference.model.vllm_attribute_classifier.DeepSeekClassifier
    predictor_constructor_kwargs:
      model_name: ${mutti_inference.model.path}
      llm_kwargs:
        limit_mm_per_prompt:
          image: 1
        max_model_len: 4096
        max_num_seqs: 32
        dtype: float16
        hf_overrides:
          architectures: 
            - "DeepseekVLV2ForCausalLM"
      question: |- 
        ### **Task: Vehicle Type Classification**  

        You are given an image with a **green bounding box** that perfectly fits a vehicle **as if no obstructions were present**. Your goal is to:  

        1. **Classify the vehicle inside the green bounding box** into one of the following types:
           `[Bus, CarTrailer, Excavator, Truck, TruckTrailer, PassengerCar, Train, AgriculturalVehicle, Forklift, FunVehicle]` 
        2. **Respond with only the matching vehicle type** from the list **without any extra explanation**.
        3. **If the vehicle does not clearly match any type from the list**, respond with `Unknown`.

        ---
        ### **Rules for Classification**
        **Consider Only the Vehicle Inside the Green Bounding Box:**  
        * Ignore any vehicles, objects, or structures **outside** the green bounding box.  

        **Do NOT Explain Your Reasoning:**  
        * Provide **only** the type from the list or `Unknown`.  

        ---
        ### **Output Format**
        - X
        - Replace **X** with one of the types from the list or `Unknown`.  
    decoder_fn: mutti_inference.model.vllm_attribute_decoder.vehicle_type_logic
    attr_list: 
      - vehicle_type
    label_task: ${mutti_inference.label_task.name}
  io:  # Used for debugging
    output_path: ${oc.env:PWD}/inference_output

label_fusion:
  _target_: mutti_processing.processing_steps.processing_attribute_label_fusion.AttributeLabelFusion
  object_table_schema: mutti_data.schemas.dyo_ac_autolabeling_schemas.dyo_ac_object_level_input_schema_rich
  step_id: attr_fusion
  sequence_id: 
  use_case: vehicle_type  # or "occlusion"
  vote_strategy: majority
  validate_against_parent_class: true
  output_dir_base_path:
  output_dir_relative_path:
  col2sensor:
    fc1_rectified_frame_url: FC1
    tv_rear_rectified_frame_url: TV_rear
    tv_front_rectified_frame_url: TV_front
    tv_left_rectified_frame_url: TV_left
    tv_right_rectified_frame_url: TV_right

alf_writer:
  step_id: Write_ac_predictions_as_ALF
  output_dir_base_path: null # ${hydra:runtime.cwd}/outputs/${now:%Y-%m-%d}/${now:%H-%M-%S}/
  output_dir_relative_path: null
  object_level_schema_name: dyo_ac_vehicle_type_fc1_tv_alf_object_lvl_schema_arrow
  label_task_name: ${mutti_inference.label_task.name}
  prelabels_output_dir_base_path: null
  prelabels_output_dir_relative_path: null
  frame_mode: false
  build_from_alf: true
  use_raw_pcd: false

dbx_writing:
  sequence_table_schema: ${mutti_data_search.sequence_table_schema}
  frame_table_schema: ${mutti_data_search.frame_table_schema}
  object_table_schema: mutti_data.schemas.dyo_ac_autolabeling_schemas.dyo_ac_vehicle_type_fc1_tv_object_lvl_schema_rich # ${label_fusion.object_table_schema} 
  sequence_table_dbx_name: "mutti_sequences_v0"
  frame_table_dbx_name: "mutti_frames_5v_v0"
  object_table_dbx_name: "mutti_objects_dyo_tracked_with_subclasses_v1"

blob_upload:
  # Account name for blob storage account.
  azure_blob_account_name: "vdeepingestprod"
  # Container name, inside the storage account.
  azure_blob_container_name: "datasets"
  # The relative path from the blob storage container where the ALF files will be uploaded.
  azure_blob_relative_name: "offline_perception/tmp/"  # TODO: Update
  # If not None, the parquet tables will also be uploaded
  parquet_blob_account_name: "vdeepingestprod"
  parquet_blob_container_name: "datasets"
  parquet_blob_relative_name: "offline_perception/tmp/"  # TODO: Update

mdm_upload: false  # Set to true in PROD
