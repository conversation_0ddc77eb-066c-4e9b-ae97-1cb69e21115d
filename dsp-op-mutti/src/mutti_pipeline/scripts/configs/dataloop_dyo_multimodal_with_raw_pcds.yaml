defaults:
  - override hydra/job_logging: none  # do not allow hydra to configure the logging.
  - override hydra/hydra_logging: none  # do not allow hydra to configure the logging.

max_ray_tasks: null 
debug_mode: true  # If set to True, artifacts will be uploaded to the blob storage. Use it for debugging failed messages in local run. Moreover, messages will not be deleted from the input queue.

queue_client:
  current_service: "mutti_dyo_mm"
  message_visibility_timeout: 20  # test purposes: in production set to 1-2h or so
  max_messages: 1
  environment: "prod"  # "prod"

mutti_data_search:
  pipeline: mutti_data_search.pipelines.DBX_PIPELINE
  sequence_table_schema: mutti_data.schemas.mdm_search_enriched_schemas.DBX_FULL_ENRICHED_SCHEMA_SEQUENCE
  frame_table_schema: mutti_data.schemas.mdm_search_enriched_schemas.AUTOLABELING_LIDAR_VISION_OUTPUT_ENRICHED_SCHEMA_FRAME
  frame_isp: "PYISP"  # Not used, but somehow still required?
  stream_identification:  # To associate query results wit
    fc1: rectified

mutti_inference:
  data:
    frame_table_schema: ${mutti_data_search.frame_table_schema}
    transform_input_raw_pcds_sensor2vehicle: true
    transform_output_cuboids_vehicle2sensor: true
    loading_parameters:
      load_image:
        concurrency: 5
        num_cpus: 0.25
      load_pcd:
        concurrency: 3
        num_cpus: 0.25
      fc1_rectified_frame_url:
        concurrency: 3
        num_cpus: 0.25

  model_download:
    workspace:
      workspace_name: "viper_birds_eye_view"                         # the name of the workspace
      resource_group: "vdeep-ct-prod"                                # the workspace with the workspace
      subscription_id: "c4f1c7f3-9206-409f-a333-5b89a516e5dd"        # Azure subscription id for these resources
    model_registry:
      model_name: "DYO_offline_model"                                # the name of the model as on the registry
      model_version: 6                                               # the version of this model to pull: v5 1.2 NMS, v6 1.2 no NMS
  model:
    base_path: ./models
    model_relative_path: ${mutti_inference.model_download.model_registry.model_name}/${mutti_inference.model_download.model_registry.model_version}
    path: ${mutti_inference.model.base_path}/${mutti_inference.model.model_relative_path}/outputs/publish/conversion/raw_model.onnx
    model_config_base_path: ${mutti_inference.model.base_path}
    predictor_type: mutti_inference.model.dyo_mm_predictor.MultiModalDYODetector                          
    predictor_constructor_kwargs:
      model_path: ${mutti_inference.model.path}
      use_raw_ref_lidar: ${mutti_inference.data.transform_output_cuboids_vehicle2sensor}
    decoder_type: mutti_inference.model.dyo_mm_prediction_decoder.MultiModalDYODecoder
    decoder_constructor_kwargs:
      frame_level_schema: ${mutti_inference.data.frame_table_schema}
      label_task: ${mutti_inference.label_task.tag}
      min_score_threshold: 0.25
      transform_output_cuboids_vehicle2sensor: ${mutti_inference.data.transform_output_cuboids_vehicle2sensor}
  label_task:
    name: dyo_3d_autolabel      # TODO: clarify exact value here
    guide_version: "1.1.0"       # TODO: Needed?
    tag: ${mutti_inference.label_task.name}_v${mutti_inference.label_task.guide_version}
  inference:
    batch_size: 1
    num_available_gpus: 1
    num_gpus_per_actor: 0.5

alf_writer:
  step_id: dyo_multimodal_alf_writing
  output_dir_base_path: null
  output_dir_relative_path: null
  object_level_schema_name: dyo_offline_raw_pcds_detection_object_level_output_schema_arrow  # TODO: Do we want to rename this?
  label_task_name: ${mutti_inference.label_task.name}
  prelabels_output_dir_base_path: null
  prelabels_output_dir_relative_path: null
  frame_mode: false
  build_from_alf: true
  use_raw_pcd: true
  
blob_upload:
  current_date: ${now:%Y-%m-%d_%H-%M-%S}
  # Account name for blob storage account.
  azure_blob_account_name: "vdeepingestprod"
  # Container name, inside the storage account.
  azure_blob_container_name: "datasets"
  # The relative path from the blob storage container where the ALF files will be uploaded.
  azure_blob_relative_name: "offline_perception/tmp/dyo_multimodal/${.current_date}"
  # If not None, the parquet tables will also be uploaded
  parquet_blob_account_name: "vdeepingestprod"
  parquet_blob_container_name: "datasets"
  parquet_blob_relative_name: "offline_perception/tmp/dyo_multimodal/${.current_date}"

dbx_writing:
  sequence_table_schema: ${mutti_data_search.sequence_table_schema}
  frame_table_schema: ${mutti_data_search.frame_table_schema}
  object_table_schema: mutti_data.schemas.dyo_autolabeling_schema.dyo_offline_raw_pcds_detection_object_level_output_schema_rich
  sequence_table_dbx_name: "mutti_sequences_v0"
  frame_table_dbx_name: "mutti_frames_5v1lr_v0"
  object_table_dbx_name: "mutti_objects_dyo_offline_detections_v0"

message_router:
  current_service: ${queue_client.current_service}
  environment: "prod"
  source: "dyo_multimodal"
  subject: "dyo_multimodal"