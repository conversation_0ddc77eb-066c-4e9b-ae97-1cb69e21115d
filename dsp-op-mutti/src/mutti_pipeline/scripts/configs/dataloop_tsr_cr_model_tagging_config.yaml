defaults:
  - override hydra/job_logging: none  # do not allow hydra to configure the logging.
  - override hydra/hydra_logging: none  # do not allow hydra to configure the logging.

max_ray_tasks: null 
debug_mode: true  # If set to True, artifacts will be uploaded to the blob storage. Use it for debugging failed messages in local run. Moreover, messages will not be deleted from the input queue.

queue_client:
  current_service: "mutti_tsr_fm"
  message_visibility_timeout: 20  # test purposes: in production set to 1-2h or so
  max_messages: 1
  environment: "qa"  # "prod"

mutti_data_search:
  pipeline: mutti_data_search.pipelines.DBX_PIPELINE
  sequence_table_schema: mutti_data.schemas.mdm_search_enriched_schemas.DBX_BASE_ENRICHED_SCHEMA_SEQUENCE
  frame_table_schema: mutti_data.schemas.mdm_search_enriched_schemas.DBX_FC1_TV_ENRICHED_SCHEMA_FRAME
  view_filter: False
  frame_isp: "PYISP"
  stream_identification:
    fc1: rectified


mutti_inference:
  data:
    image_scaling_factor: 0.75
    frame_table_schema: mutti_data.schemas.mdm_search_enriched_schemas.AUTOLABELING_FC1_OUTPUT_ENRICHED_SCHEMA_FRAME
    custom_input_image_size: [4568, 2306] # label view with scaling factor of mutti_inference.data.image_scaling_factor on both dimensions
    loading_parameters:
        load_image:
            size_wh: ${mutti_inference.data.custom_input_image_size}
  model_download:
    workspace:
      workspace_name: "offline_perception"                           # the name of the workspace
      resource_group: "vdeep-ct-prod"                                # the workspace with the workspace
      subscription_id: "c4f1c7f3-9206-409f-a333-5b89a516e5dd"        # Azure subscription id for these resources
    model_registry:
      model_name: "cr_tsr_detector"                                  # the name of the model as on the registry
      model_version: 1                                               # the version of this model to pull
  model:
    base_path: ./models
    model_relative_path: ${mutti_inference.model_download.model_registry.model_name}/${mutti_inference.model_download.model_registry.model_version}
    path: ${mutti_inference.model.base_path}/${mutti_inference.model.model_relative_path}/CR_TSR_model/model_0069999.pth
    model_config_base_path: ${mutti_inference.model.base_path}
    model_config_path: ${mutti_inference.model.model_config_base_path}/${mutti_inference.model.model_relative_path}/CR_TSR_model/model_config.yaml
    predictor_type: mutti_inference.model.detectron2_predictor.Detectron2TsrCRBatchPredictor
    predictor_constructor_kwargs:
      model_path: ${mutti_inference.model.path}
      model_config_path: ${mutti_inference.model.model_config_path}
      window_size: 1152
      stride: 896
      score_thr: 0.4
      nms_iou_threshold: 0.5
      image_w_resize_factor: ${mutti_inference.data.image_scaling_factor}
      image_h_resize_factor: ${mutti_inference.data.image_scaling_factor}
    decoder_type: mutti_inference.model.detectron2_prediction_decoder.Detectron2TsrCRDecoder
    decoder_constructor_kwargs:
      label_task: ${mutti_inference.label_task.name}
      label_task_tag: ${mutti_inference.label_task.tag}
      frame_level_schema: mutti_data.schemas.mdm_search_enriched_schemas.AUTOLABELING_FC1_OUTPUT_ENRICHED_SCHEMA_FRAME
  label_task:
    name: traffic_sign
    guide_version: "1.1.0"
    tag: ${mutti_inference.label_task.name}_v${mutti_inference.label_task.guide_version}
  inference:
    batch_size: 1
    num_available_gpus: 1

alf_writer:
  step_id: tsr_cr_tagging_alf_writing
  output_dir_base_path: null
  output_dir_relative_path: null
  object_level_schema_name: tsr_cr_tagging_object_level_output_schema_arrow
  label_task_name: ${mutti_inference.label_task.name}
  prelabels_output_dir_base_path: null
  prelabels_output_dir_relative_path: null
  frame_mode: false
  build_from_alf: true
  use_raw_pcd: false

blob_upload:
  current_date: ${now:%Y-%m-%d_%H-%M-%S}
  # Account name for blob storage account.
  azure_blob_account_name: "vdeepingestprod"
  # Container name, inside the storage account.
  azure_blob_container_name: "datasets"
  # The relative path from the blob storage container where the ALF files will be uploaded.
  azure_blob_relative_name: "offline_perception/tmp/tsr_cr_tagging/${.current_date}"
  # If not None, the parquet tables will also be uploaded
  parquet_blob_account_name: "vdeepingestprod"
  parquet_blob_container_name: "datasets"
  parquet_blob_relative_name: "offline_perception/tmp/tsr_cr_tagging/${.current_date}"

dbx_writing:
  sequence_table_schema: ${mutti_data_search.sequence_table_schema}
  frame_table_schema: ${mutti_data_search.frame_table_schema}
  object_table_schema: "mutti_data.schemas.tsr_tsr_prelabeling_schemas.tsr_cr_tagging_object_level_output_schema_rich"
  sequence_table_dbx_name: "mutti_sequences_base_v0"
  frame_table_dbx_name: "mutti_frames_1v_v0"
  object_table_dbx_name: "mutti_objects_tsr_tagging_v0"

message_router:
  current_service: ${queue_client.current_service}
  environment: "qa"
  source: "tsr_tagging"
  subject: "tsr_tagging"
  payload_cls: "pace.events.sequence_selected.SequenceSelectedPayLoadV1"
  event_cls: "pace.events.sequence_selected.SequenceSelectedV1"
