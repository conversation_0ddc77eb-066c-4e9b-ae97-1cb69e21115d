"""The script sets up a pipeline using mutti primitives for the tagging pre-labeling use case using the CR model."""
import logging
import sys
import warnings
from multiprocessing import cpu_count
from pathlib import Path
from typing import Any

import hydra
import pandas as pd
import pyarrow as pa
import ray
from mdm_viper_lib.dataloop_integration.logger.standards import (
    ProcessingResult,
    ProcessingState,
)
from omegaconf import DictConfig
from ray.exceptions import RaySystemError, RayTaskError

from mutti_data.credentials import get_azure_credentials
from mutti_data.fields.frame_level_data_fields import frame_master_index
from mutti_data.fields.sequence_level_data_fields import (
    sequence_custom_sha as sequence_custom_sha_field,
)
from mutti_data.schemas.enriched_schema import EnrichedSchema
from mutti_inference.data.multi_modal_config import BuilderConfig
from mutti_inference.data.multi_modal_loader import MultiModalDatasetBuilder
from mutti_pipeline.scripts.pipeline_components import dbx_writing
from mutti_pipeline.scripts.pipeline_components.alf_writing import (
    AlfWriterConfig,
    write_batch_of_alfs,
)
from mutti_pipeline.scripts.pipeline_components.data import (
    BlobClientConfig,
    MessageRouterConfig,
    MuttiDatafetchBaseConfig,
    delete_message_from_the_queue,
    download_messages_from_queue,
    fetch_batch_of_data,
    get_queue_consumer,
)
from mutti_pipeline.scripts.pipeline_components.inference import (
    BatchInferenceConfig,
    ModelDownloadConfig,
    download_model,
    run_batch_inference,
)
from mutti_pipeline.scripts.pipeline_components.logging import (
    setup_logger_mutti,
    setup_ray_logger,
    setup_ray_remote_logger,
)
from mutti_processing.processing_step import ProcessingData

logger = logging.getLogger("Dataloop TSR Tagging with CR model pipeline")

# Silence ResourceWarnings from ray log files
warnings.filterwarnings(
    "ignore",
    category=ResourceWarning,
)


setup_ray_logger()


def setup_ray_remote_logger_tsr() -> None:
    """Wrap the function to set up ray logger with the proper logger name."""
    setup_ray_remote_logger(logger.name)


def create_ray_dataset(
    frame_table: pa.Table, frame_table_enriched_schema: EnrichedSchema, loading_params: dict[str, Any]
) -> ray.data.Dataset:
    """Generate the ray dataset for this usecase.

    Args:
        frame_table: the frame table with the data to process.
        frame_table_enriched_schema: the corresponding enriched schema for the frame table.
        loading_params: the dictionary with the loading parameters for the multimodal loader.

    Returns:
        the ray.data Dataset.
    """
    builder_config = BuilderConfig(frame_table_enriched_schema, loading_parameters=loading_params)
    ds_config = builder_config.to_dict()
    frame_table_pd = frame_table.to_pandas(timestamp_as_object=True, types_mapper=pd.ArrowDtype)
    # Add frame_uid
    frame_table_pd["frame_uid"] = frame_table_pd[sequence_custom_sha_field.name].str.cat(
        frame_table_pd[frame_master_index.name].astype(str), sep="_"
    )

    ds_builder = MultiModalDatasetBuilder(frame_table_pd, ds_config, initial_num_blocks=20)
    logger.info(ds_builder)
    ds_inference = ds_builder.create_dataset()
    return ds_inference


@hydra.main(
    version_base="1.3",
    config_path="configs",
    config_name="dataloop_tsr_cr_model_tagging_config.yaml",
)
def run_autolabel_batch_inference_from_queue(cfg: DictConfig) -> None:
    """Process a batch of messages from some Azure queue and save the ALFs with autolabels to disk."""
    ray.init(runtime_env={"worker_process_setup_hook": setup_ray_remote_logger_tsr}, ignore_reinit_error=True)
    setup_logger_mutti()
    # Set up debug mode, if desired
    DEBUG_MODE = cfg.get("debug_mode", False)

    # Start by downloading the model asynchronously
    azure_cfg = cfg.mutti_inference.model_download
    azureml_cfg = ModelDownloadConfig(
        workspace_name=azure_cfg.workspace.workspace_name,
        resource_group=azure_cfg.workspace.resource_group,
        subscription_id=azure_cfg.workspace.subscription_id,
        model_name=azure_cfg.model_registry.model_name,
        model_version=azure_cfg.model_registry.model_version,
    )
    model_download_path = Path(cfg.mutti_inference.model.base_path).joinpath(
        azureml_cfg.model_name, str(azureml_cfg.model_version)
    )
    download_model_future = download_model.remote(azureml_cfg, model_download_path, logger)

    # Set up the queue consumer
    current_service = cfg.queue_client.current_service
    env_type = cfg.queue_client.environment
    max_messages = cfg.queue_client.max_messages
    num_cpus = cpu_count()
    max_ray_tasks = cfg.max_ray_tasks if cfg.max_ray_tasks is not None else num_cpus
    logger.info(
        "Mutti workflow start."
        "\nDownloading locally up to %d ALFs from the queue flow %s. "
        "\nUp to %d ray tasks will be run at a time (number of available cores: %d).",
        max_messages,
        current_service,
        max_ray_tasks,
        num_cpus,
        extra=dict(
            processing_state=ProcessingState.WORKFLOW_START,
        ),
    )
    visibility_timeout = cfg.queue_client.message_visibility_timeout
    queue_consumer = get_queue_consumer(current_service, env_type)
    # Get messages from the queue
    processed_messages = download_messages_from_queue(queue_consumer, max_messages, visibility_timeout, logger=logger)
    if not processed_messages:
        logger.info(
            "No message could be downloaded from the queue for the workflow {cfg.queue_client.current_service}. Maybe the queue is empty?"
        )
        sys.exit(0)
    initial_label_request_ids = [x.label_request_id for x in processed_messages]
    logger.info("Done, %d messages were successful read from the queue.", len(processed_messages))

    logger.info("Fetching the data for the batch of %d messages...", len(processed_messages))
    mutti_data_search_cfg = MuttiDatafetchBaseConfig(
        pipeline=cfg.mutti_data_search.pipeline,
        sequence_table_schema=cfg.mutti_data_search.sequence_table_schema,
        frame_table_schema=cfg.mutti_data_search.frame_table_schema,
        object_table_schema=None,
        view_filter=cfg.mutti_data_search.get("view_filter", False),
        frame_isp=cfg.mutti_data_search.frame_isp,
        stream_identification=cfg.mutti_data_search.get("stream_identification", None),
    )

    try:
        processed_messages, sequence_table, frame_table, _ = fetch_batch_of_data(
            processed_messages, mutti_data_search_cfg, max_ray_tasks, logger=logger
        )
    except Exception as e:
        logger.exception(
            "No messages could be processed! Raised error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            exc_info=e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
    else:
        logger.info("Data could be fetched for %d messages.", len(processed_messages))
        fetched_label_request_ids = [x.get_label_request_id() for x in processed_messages]
        logger.info(
            "The messages have the following label request ids [message (ALF)]: \n%s",
            "\n".join([f"{msg_id}" for msg_id in fetched_label_request_ids]),
        )

    # If model has not been downloaded yet, block until it is.
    ray.get(download_model_future)
    # Raise error if the model is not there, since we cannot continue with the pipeline.
    if not Path(cfg.mutti_inference.model.path).is_file():
        logger.exception("No model was found at %s. Exiting...", cfg.mutti_inference.model.path)
        raise FileNotFoundError(
            f"No model at {cfg.mutti_inference.model.path}! Maybe something went wrong with the download?"
        )
    else:
        logger.info("Model successfully retrieved. Running inference...")

    # Generate the ray dataset, inference config and the objects to use in the batch inference
    inference_cfg = cfg.mutti_inference
    frame_table_enriched_schema = hydra.utils.get_object(inference_cfg.data.frame_table_schema)
    loading_params = inference_cfg.data.loading_parameters
    ray_ds = create_ray_dataset(frame_table, frame_table_enriched_schema, loading_params)
    batch_inference_cfg = BatchInferenceConfig(
        predictor_batch_size=inference_cfg.inference.batch_size,
        predictor_concurrency=int(
            inference_cfg.inference.num_available_gpus // inference_cfg.inference.get("num_gpus_per_actor", 1)
        ),
        predictor_num_cpus=None,
        predictor_num_gpus=inference_cfg.inference.get("num_gpus_per_actor", 1),
        decoder_batch_size=2,
        decoder_concurrency=(2, 1 + 2 * int(inference_cfg.inference.num_available_gpus)),
        decoder_num_cpus=0.5,
        decoder_num_gpus=0,
    )
    predictor_type = hydra.utils.get_class(inference_cfg.model.predictor_type)
    decoder_type = hydra.utils.get_class(inference_cfg.model.decoder_type)

    logger.info(
        "Predicting %d records (rows) for %d sequences (unique sequence_custom_shas)...",
        len(frame_table),
        len(sequence_table[sequence_custom_sha_field.name].unique()),
    )
    try:
        object_table = run_batch_inference(
            predictor_type=predictor_type,
            decoder_type=decoder_type,
            dataset=ray_ds,
            predictor_constructor_kwargs=inference_cfg.model.predictor_constructor_kwargs,
            decoder_constructor_kwargs=inference_cfg.model.decoder_constructor_kwargs,
            inference_config=batch_inference_cfg,
            logger=logger,
        )
    except (RayTaskError, RaySystemError) as e:
        logger.exception(
            "Ray inference error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            exc_info=e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
    except Exception as e:
        logger.exception(
            "Unknown inference error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            exc_info=e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
    else:
        processed_data = ProcessingData(sequence_table, frame_table, object_table)
    logger.info("Inference completed successfully.")

    if DEBUG_MODE:
        out_path = (
            f"{cfg.blob_upload.azure_blob_account_name}/"
            f"{cfg.blob_upload.azure_blob_container_name}/"
            f"{cfg.blob_upload.azure_blob_relative_name}"
        )
        logger.info("Writing ALFs to output %s...", out_path)
        blob_client_cfg = BlobClientConfig(
            cfg.blob_upload.azure_blob_account_name,
            cfg.blob_upload.azure_blob_container_name,
            cfg.blob_upload.azure_blob_relative_name,
            cfg.blob_upload.parquet_blob_account_name,
            cfg.blob_upload.parquet_blob_container_name,
            cfg.blob_upload.parquet_blob_relative_name,
        )
    else:
        blob_client_cfg = None
    # Prepare config for the ALF writer and the message router
    alf_writer_cfg = AlfWriterConfig(
        step_id=cfg.alf_writer.step_id,
        sequence_id=None,
        object_level_schema_name=cfg.alf_writer.object_level_schema_name,
        label_task_name=cfg.alf_writer.label_task_name,
        output_dir_base_path=cfg.alf_writer.get("output_dir_base_path", None),
        output_dir_relative_path=cfg.alf_writer.get("output_dir_relative_path", None),
        prelabels_output_dir_base_path=cfg.alf_writer.get("prelabels_output_dir_base_path", None),
        prelabels_output_dir_relative_path=cfg.alf_writer.get("prelables_output_dir_relative_path", None),
        build_from_alf=cfg.alf_writer.get("build_from_alf", True),
        frame_mode=cfg.alf_writer.get("frame_mode", False),
        use_raw_pcd=cfg.alf_writer.get("use_raw_pcd", False),
    )
    message_router_cfg = MessageRouterConfig(
        cfg.message_router.current_service,
        cfg.message_router.environment,
        cfg.message_router.source,
        cfg.message_router.subject,
        cfg.message_router.payload_cls,
        cfg.message_router.event_cls,
    )
    processed_messages = write_batch_of_alfs(
        processed_messages,
        processed_data,
        alf_writer_cfg,
        message_router_cfg,
        blob_client_cfg,
        False,
        max_ray_tasks,
        logger=logger,
    )

    involved_models = [
        f"{cfg.mutti_inference.model_download.workspace.workspace_name}/"
        f"{cfg.mutti_inference.model_download.model_registry.model_name}:"
        f"{cfg.mutti_inference.model_download.model_registry.model_version}"
    ]
    dbx_writing.write_successful_to_databricks(
        processed_messages,
        sequence_table,
        frame_table,
        object_table,
        cfg,
        credential=get_azure_credentials(),
        mutti_pipeline="tsr_cr_model_tagging",
        involved_models=involved_models,
    )

    if not DEBUG_MODE:
        for msg in processed_messages:
            delete_message_from_the_queue(msg, queue_consumer, logger=logger)

    # Log summary of successfull and unsuccessfull messages
    successful_ids = [x.get_label_request_id() for x in processed_messages]
    unsuccessful_ids = list(set(fetched_label_request_ids) - set(successful_ids))

    all_msgs = "\n".join(initial_label_request_ids)
    not_feched_msgs = "\n".join(list(set(initial_label_request_ids) - set(fetched_label_request_ids)))

    logger.info("Initial message label request ids retrieved from the queue:\n%s", all_msgs)
    logger.info(
        "These label request ids could be fetched [message (ALF)]:\n%s",
        "\n".join([f"{msg_id}" for msg_id in fetched_label_request_ids]),
    )
    logger.info("The messages with the following label request ids could not be fetched:\n%s", not_feched_msgs)
    logger.info(
        "ALFs could be written correctly for (message | ALF):\n%s",
        "\n".join([f"{msg_id}" for msg_id in successful_ids]),
    )
    logger.info(
        "Something went wrong for [message (ALF)]:\n%s\nMessages for these requests were left in the queue.",
        "\n".join([f"{msg_id}" for msg_id in unsuccessful_ids]),
    )
    logger.info(
        "Mutti workflow end",
        extra=dict(return_value=ProcessingResult.OK, processing_state=ProcessingState.WORKFLOW_END),
    )


if __name__ == "__main__":
    try:
        run_autolabel_batch_inference_from_queue()
    except Exception as e:
        logger.exception(
            "Unknown error: %s." "\nTerminating execution." "\nMutti workflow end",
            e,
            exc_info=e,
            extra=dict(return_value=ProcessingResult.ERROR, processing_state=ProcessingState.WORKFLOW_END),
        )
        raise e
