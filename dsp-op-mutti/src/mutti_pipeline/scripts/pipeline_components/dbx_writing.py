"""Functionality to write data to Databricks.

This module dumps sequence, frame, and object tables to Databricks,
together with traceability information.
"""

__copyright__ = """
===================================================================================
 C O P Y R I G H T
-----------------------------------------------------------------------------------
 Copyright (c) 2023-2025 Robert Bosch GmbH and Cariad SE. All rights reserved.
===================================================================================
"""

import json
import logging
import os
import socket
import subprocess  # nosec B404
from dataclasses import dataclass
from datetime import datetime
from json import JSONDecodeError

import hydra
import numpy as np
import pyarrow as pa
from deltalake import write_deltalake
from deltalake.schema import Schema
from mdm.dpm.client import DataProductClient
from mdm.dpm.client.endpoints import AzureGen2Endpoint
from omegaconf import DictConfig, OmegaConf

from mutti_data.schemas.enriched_schema import EnrichedSchema
from mutti_data.schemas.mdm_search_column_types import FrameTimestamp
from mutti_pipeline.scripts.pipeline_components.data import ProcessedMessage

TENANT_ID = "a6c60f0f-76aa-4f80-8dba-092771d439f0"

logger = logging.getLogger(__name__)


@dataclass
class TableMetadata:
    """Pack the dbx table names and schemas together (constant for a usecase)."""

    object_table_schema: EnrichedSchema
    object_table_dbx_name: str
    frame_table_schema: EnrichedSchema
    frame_table_dbx_name: str
    sequence_table_schema: EnrichedSchema
    sequence_table_dbx_name: str


def write_successful_to_databricks(
    processed_messages: list[ProcessedMessage],
    sequence_table: pa.Table,
    frame_table: pa.Table,
    object_table: pa.Table,
    cfg: DictConfig,
    *args,
    **kwargs,
):
    """Write the sequence, frame, and object tables to Databricks.

    This is a wrapper that also first downfilters sequence/frame and object tables
    down to the processed messages that were successful.

    """
    table_metadata = TableMetadata(
        sequence_table_schema=hydra.utils.get_object(cfg.dbx_writing.sequence_table_schema),
        frame_table_schema=hydra.utils.get_object(cfg.dbx_writing.frame_table_schema),
        object_table_schema=hydra.utils.get_object(cfg.dbx_writing.object_table_schema),
        sequence_table_dbx_name=cfg.dbx_writing.sequence_table_dbx_name,
        frame_table_dbx_name=cfg.dbx_writing.frame_table_dbx_name,
        object_table_dbx_name=cfg.dbx_writing.object_table_dbx_name,
    )

    # Filter the sequence, frame, and object tables down to the processed messages based on sequence_custom_sha
    label_request_ids = [msg.label_request_id for msg in processed_messages]
    # Filter sequence_table by label_request_ids and extract sequence_custom_sha values
    filtered_sequences = sequence_table.filter(
        pa.compute.is_in(
            sequence_table.column("sequence_label_request_id"), pa.array(label_request_ids, type=pa.string())
        )
    )
    sequence_custom_shas = filtered_sequences.column("sequence_custom_sha")

    sequence_table_filtered = sequence_table.filter(
        pa.compute.is_in(sequence_table.column("sequence_custom_sha"), sequence_custom_shas)
    )
    frame_table_filtered = frame_table.filter(
        pa.compute.is_in(frame_table.column("sequence_custom_sha"), sequence_custom_shas)
    )
    object_table_filtered = object_table.filter(
        pa.compute.is_in(object_table.column("sequence_custom_sha"), sequence_custom_shas)
    )

    write_to_databricks(
        sequence_table_filtered,
        frame_table_filtered,
        object_table_filtered,
        table_metadata=table_metadata,
        config=OmegaConf.to_container(cfg, resolve=True),
        *args,
        **kwargs,
    )


def write_to_databricks(
    sequence_table: pa.Table,
    frame_table: pa.Table,
    object_table: pa.Table,
    credential,
    table_metadata: TableMetadata,
    involved_models: list[str] | None = None,
    config: dict | None = None,
    mutti_pipeline: str | None = None,
):
    """Write the sequence, frame, and object tables to Databricks.

    The schemas for the enriched sequence, frame, and object tables are
    extended with traceability information from the processed messages.


    Sequence table added fields:
    ------------------------------------
    Read from environment:
    - `processed_at`: datetime of processing,
    - `compute`: host name of the compute node (i.e. argo workflow pod name or local machine running this),
            as from socket.gethostname()
    - `argo_workflow_id`: If run on argo, the workflow ID, as read from environment, otherwise None.
    - `mutti_git_commit`: git commit of the mutti repo as read with subprocess call on this folder, None if fails.

    Read from frame table:
    - `sequence_start`: recording start of sequence, inferred from the frame table min recorded_at
    - `sequence_end`: recording end of sequence, inferred from the frame table max recorded_at

    Expected as input:
    - `config`: Full hydra config, None if not given.
    - `involved_models` : List of the involved detector models as loaded from AML registries, None if not given
    - `mutti_pipeline`: The name of the pipeline generating this, None if not given

    Sequence table required fields:
    --------------------------------
    - `sequence_custom_sha`: The custom sha of the sequence, encoding, needed to add to lower-level tables
    - `label_request_id`: The label request ID, needed to add to lower-level tables
    - `VIN`: The Vehicle Identification Number, needed to add to lower-level tables

    Note that either sequence_custom_sha or label_request_id must be present in the sequence table,
    the other may be null.


    Frame table added fields:
    -------------------------
    Read from environment:
    - `processed_at`: datetime of processing, guaranteed to be the same as in the sequence table

    Passed down from sequence table:
    - `VIN`
    - `label_request_id`: The label request ID

    Inferred from frame table:
    - recorded_at_min: min recorded time of the frames in this multisensor frame
    - recorded_at_max: max recorded time of the frames in this multisensor frame

    Frame table required fields:
    ----------------------------
    - `frame_master_index`: The master index of the frame, needed to relate to object tables
    - `sequence_custom_sha`: The custom sha of the sequence, encoding, needed to add
    - Some form of timestamp (based on enrichted schema) to calculate the `recorded_at_min` and `recorded_at_max`
        fields as well as sequence start and end times for sequence-level table.

    Object table added fields:
    -------------------------
    Read from environment:
    - `processed_at`: datetime of processing, guaranteed to be the same as in the sequence

    Passed down from sequence table:
    - `VIN`
    - `label_request_id`: The label request ID, needed to add to lower-level tables

    Inferred from frame table:
    - `recorded_at`: The recorded time of the frame, needed to relate to frame. Filled from timestamp column
        (but parsed into proper datetime)

    Object table required fields:
    ----------------------------
    - `frame_master_index`: The master index of the frame, needed to relate to object
    - `sequence_custom_sha`: The custom sha of the sequence, encoding, needed to add
    - `timestamp`: The timestamp of the object, needed to relate to frame tables and fill the recording_at field


    Args:
        sequence_table (pa.Table): Sequence table to write.
        frame_table (pa.Table): Frame table to write.
        object_table (pa.Table): Object table to write.
        processed_messages (list[ProcessedMessage]): List of processed messages.
        credential: Credentials for Databricks connection.
        table_metadata (TableMetadata): Metadata containing enriched schemas and dbx table names.
        involved_models (list[str] | None, optional): List of involved models. Defaults to None.
        config (dict | None, optional): Full hydra config of this pipeline. Defaults to None.
        mutti_pipeline (str | None, optional): Name of the mutti pipeline. Defaults
            to None.
    """
    logger.info("Writing sequence, frame, and object tables to Databricks.")

    processed_at = pa.scalar(datetime.utcnow(), type=pa.timestamp("us"))
    sequence_start_end = _get_sequence_start_end_from_frames(frame_table, table_metadata.frame_table_schema)
    vin, label_request_id = _get_vin_and_label_request_id_from_sequence_table(sequence_table)

    sequence_table_extended = _extend_sequence_table(
        processed_at,
        sequence_table,
        table_metadata.sequence_table_schema,
        sequence_start_end,
        vin,
        involved_models,
        config,
        mutti_pipeline,
    )

    frame_table_extended = _extend_frame_table(
        processed_at, vin, label_request_id, frame_table, table_metadata.frame_table_schema
    )

    object_table_extended = _extend_object_table(
        processed_at,
        vin,
        label_request_id,
        object_table,
        table_metadata.object_table_schema,
        frame_table,
    )

    write_tables_to_databricks(
        tables=[sequence_table_extended, frame_table_extended, object_table_extended],
        table_names=[
            table_metadata.sequence_table_dbx_name,
            table_metadata.frame_table_dbx_name,
            table_metadata.object_table_dbx_name,
        ],
        credential=credential,
    )


def _get_sequence_start_end_from_frames(
    frame_table: pa.Table, schema: EnrichedSchema
) -> dict[str, tuple[datetime, datetime]]:
    # get the minimum and maximum recorded_at from the frame table
    timestamps = {}

    sequence_custom_shas = set(frame_table.column("sequence_custom_sha").to_pylist())

    for sequence_custom_sha in sequence_custom_shas:
        # Collect all recorded_at values for this sequence_custom_sha from all FrameTimestamp columns
        recorded_ats = []
        for column in schema.values():
            if isinstance(column, FrameTimestamp):
                filtered = frame_table.filter(
                    pa.compute.equal(frame_table.column("sequence_custom_sha"), sequence_custom_sha)
                )
                recorded_ats.extend(filtered.column(column.name).to_pylist())

        if not recorded_ats:
            raise ValueError(f"No frames found for sequence_custom_sha {sequence_custom_sha} in the frame table.")

        # Remove None values if any
        recorded_ats = [ts for ts in recorded_ats if ts is not None]
        if not recorded_ats:
            raise ValueError(f"No valid timestamps found for sequence_custom_sha {sequence_custom_sha}.")

        # Convert nanosecond timestamps to datetime objects using numpy for brevity
        arr = np.array(np.array(recorded_ats) / 1000, dtype="datetime64[us]")
        min_ns = arr.min()
        max_ns = arr.max()
        min_dt = pa.scalar(min_ns, type=pa.timestamp("us")).as_py()
        max_dt = pa.scalar(max_ns, type=pa.timestamp("us")).as_py()
        timestamps[sequence_custom_sha] = (min_dt, max_dt)

    return timestamps


def _get_vin_and_label_request_id_from_sequence_table(
    sequence_table: pa.Table,
) -> tuple[dict[str, str], dict[str, str]]:
    """Extract VIN and label request ID from the sequence table.

    Args:
        sequence_table (pa.Table): The sequence table to extract from.

    Returns:
        tuple: A tuple containing two dictionaries:
            - A mapping from sequence_custom_sha to VIN.
            - A mapping from sequence_custom_sha to label_request_id.
    """
    # Build a mapping from sequence_custom_sha to VIN
    if "sequence_custom_sha" not in sequence_table.column_names:
        raise ValueError("The sequence table must contain a 'sequence_custom_sha' column.")

        # Extract VIN and label_request_id from the sequence table
    if "vin" not in sequence_table.column_names:
        logger.warning("The sequence table does not contain a 'VIN' column. Trying to extract VIN from ALF.")
        try:
            vin_list = [
                json.loads(alf)["openlabel"]["metadata"]["vin"]
                for alf in sequence_table.column("sequence_alf_data").to_pylist()
            ]
        except (KeyError, JSONDecodeError) as e:
            raise ValueError(
                "The sequence table does not contain a 'VIN' column and the 'sequence_alf_data' column is not "
                "in the expected format. Ensure that the sequence table contains either a 'VIN' column or a "
                "'sequence_alf_data' column with valid JSON data."
            ) from e
        logger.info("Extracted VINs %s from sequence_alf_data.", vin_list)
    else:
        vin_list = sequence_table.column("VIN").to_pylist()

    sequence_custom_sha_list = sequence_table.column("sequence_custom_sha").to_pylist()
    sequence_sha_to_vin = dict(zip(sequence_custom_sha_list, vin_list))

    if "sequence_label_request_id" not in sequence_table.column_names:
        raise ValueError("The sequence table must contain a 'sequence_label_request_id' column.")

    # Build a mapping from sequence_custom_sha to label_request_id
    label_request_id_list = sequence_table.column("sequence_label_request_id").to_pylist()
    sequence_sha_to_label_request_id = dict(zip(sequence_custom_sha_list, label_request_id_list))

    return sequence_sha_to_vin, sequence_sha_to_label_request_id


# Extend the schema with new fields if not already present
def _extend_sequence_schema(schema: pa.Schema) -> pa.Schema:
    # Define new fields to add
    new_fields = [
        pa.field("processed_at", pa.timestamp("us")),
        pa.field("vin", pa.string(), nullable=True),
        pa.field("compute", pa.string()),
        pa.field("sequence_start", pa.timestamp("us")),
        pa.field("sequence_end", pa.timestamp("us")),
        pa.field("mutti_pipeline", pa.string()),
        pa.field("argo_workflow_id", pa.string(), nullable=True),
        pa.field("mutti_git_commit", pa.string(), nullable=True),
        pa.field("config", pa.string(), nullable=True),
        pa.field("involved_models", pa.list_(pa.string()), nullable=True),
    ]
    # Merge the existing schema with the new fields using pyarrow's merge_schemas
    additional_schema = pa.schema(new_fields)
    merged_schema = pa.unify_schemas([additional_schema, schema])
    return merged_schema


def _get_git_commit():
    try:
        if "WORKFLOW_UID" in os.environ:
            # Then we are in an argo workflow, the package is not
            # installed as editable, but the repo location in the docker image is known
            cwd = "/home/<USER>/repo"
        else:
            # Otherwise we are running locally, so use the current working directory
            # as typically the package is installed as editable
            # and the git repo is the current working directory
            cwd = os.getcwd()
        commit = (
            subprocess.check_output(  # nosec B607
                ["git", "rev-parse", "HEAD"],
                stderr=subprocess.DEVNULL,
                cwd=cwd,
                shell=False,  # nosec: B603
            )
            .decode("utf-8")
            .strip()
        )
        return commit
    except Exception:
        return None


def _extend_sequence_table(
    processed_at: pa.timestamp,
    sequence_table: pa.Table,
    sequence_table_enriched_schema: EnrichedSchema,
    sequence_start_end: dict[str, tuple[pa.timestamp, pa.timestamp]],
    vin: dict[str, str],
    involved_models: list[str] | None = None,
    config: dict | None = None,
    mutti_pipeline: str | None = None,
) -> pa.Table:
    """Extend the sequence table with traceability information and other metadata.

    Args:
        processed_at (datetime): The datetime of processing.
        sequence_table (pa.Table): The original sequence table.
        sequence_table_enriched_schema (pa.Schema): The enriched schema for the sequence table.
        sequence_start_end (dict[str, tuple[pa.timestamp, pa.timestamp]]): A mapping from sequence_custom_sha to
            a tuple of (sequence_start, sequence_end) timestamps.
        vin (dict[str, str]): A mapping from sequence_custom_sha to VIN.
        processed_messages (list[ProcessedMessage]): List of processed messages.
        involved_models (list[str] | None, optional): List of involved models. Defaults to None.
        config (dict | None, optional): Full hydra config of this pipeline. Defaults to None.
        mutti_pipeline (str | None, optional): Name of the mutti pipeline. Defaults to None.

    Returns:
        pa.Table: The extended sequence table.
    """
    extended_schema = _extend_sequence_schema(sequence_table_enriched_schema.arrow_schema)

    # Extract traceability/environment info
    compute = socket.gethostname()
    argo_workflow_id = os.environ.get("WORKFLOW_UID", None)
    mutti_git_commit = _get_git_commit()

    # Prepare new columns
    num_rows = sequence_table.num_rows
    new_columns = {}

    sequence_starts = [sequence_start_end[s][0] for s in sequence_table.column("sequence_custom_sha").to_pylist()]
    sequence_ends = [sequence_start_end[s][1] for s in sequence_table.column("sequence_custom_sha").to_pylist()]

    config_str = json.dumps(config) if config is not None else None

    new_columns = {
        "vin": pa.array([vin[row["sequence_custom_sha"]] for row in sequence_table.to_pylist()], pa.string()),
        "processed_at": pa.array([processed_at] * num_rows, pa.timestamp("us")),
        "compute": pa.array([compute] * num_rows, pa.string()),
        "argo_workflow_id": pa.array([argo_workflow_id] * num_rows, pa.string()),
        "mutti_git_commit": pa.array([mutti_git_commit] * num_rows, pa.string()),
        # Use microsecond precision for sequence_start and sequence_end
        "sequence_start": pa.array(sequence_starts, pa.timestamp("us")),
        "sequence_end": pa.array(sequence_ends, pa.timestamp("us")),
        "config": pa.array([config_str] * num_rows, pa.string()),
        "involved_models": pa.array([involved_models] * num_rows, pa.list_(pa.string())),
        "mutti_pipeline": pa.array([mutti_pipeline] * num_rows, pa.string()),
    }

    # Merge new columns with existing columns, avoiding duplicates
    existing = {name: sequence_table.column(name) for name in sequence_table.schema.names}
    merged = {**existing, **new_columns}
    extended_table = pa.table(merged, schema=extended_schema)
    return extended_table


def _extend_frame_table(
    processed_at: np.datetime64,
    vin: dict[str, str],
    label_request_id: dict[str, str],
    frame_table: pa.Table,
    frame_table_enriched_schema: EnrichedSchema,
) -> pa.Table:
    """Extend the frame table with traceability information and other metadata.

    Args:
        processed_at (datetime): The datetime of processing, guaranteed to be the same as in the sequence table.
        vin (dict[str, str]): A mapping from sequence_custom_sha to VIN.
        label_request_id (dict[str, str]): A mapping from sequence_custom_sha to label_request_id.
        frame_table (pa.Table): The original frame table.
        frame_table_enriched_schema (EnrichedSchema): The enriched schema for the frame table.
        sequence_table (pa.Table): The sequence table to extract metadata from.

    Returns:
        pa.Table: The extended frame table.
    """
    # Extend the schema with new fields if not already present
    extended_schema = _extend_frame_schema(frame_table_enriched_schema)

    recorded_at_min_list, recorded_at_max_list = _calculate_frame_table_timestamps(
        frame_table, frame_table_enriched_schema
    )

    # Prepare new columns
    num_rows = frame_table.num_rows
    # Use columnar access for memory efficiency
    sequence_custom_sha_arr = frame_table.column("sequence_custom_sha")
    # Convert to Python objects only for lookup
    sequence_custom_sha_py = [s.as_py() for s in sequence_custom_sha_arr]
    vins = pa.array([vin.get(s, None) for s in sequence_custom_sha_py], pa.string())
    label_requests = pa.array([label_request_id.get(s, None) for s in sequence_custom_sha_py], pa.string())

    new_columns = {
        "processed_at": pa.array([processed_at] * num_rows, pa.timestamp("us")),
        "recorded_at_min": pa.array(recorded_at_min_list, pa.timestamp("us")),
        "recorded_at_max": pa.array(recorded_at_max_list, pa.timestamp("us")),
        "vin": vins,
        "label_request_id": label_requests,
    }

    # Merge new columns with existing columns, avoiding duplicates
    existing = {name: frame_table.column(name) for name in frame_table.schema.names}
    merged = {**existing, **new_columns}
    extended_table = pa.table(merged, schema=extended_schema)
    return extended_table


def _extend_frame_schema(schema: EnrichedSchema) -> pa.Schema:
    """Extend the frame schema with traceability information and other metadata.

    Args:
        schema (EnrichedSchema): The original enriched schema for the frame table.

    Returns:
        EnrichedSchema: The extended enriched schema for the frame table.
    """
    # Define new fields to add
    new_fields = {
        "vin": pa.field("vin", pa.string(), nullable=True),
        "recorded_at_min": pa.field("recorded_at_min", pa.timestamp("us")),
        "recorded_at_max": pa.field("recorded_at_max", pa.timestamp("us")),
        "processed_at": pa.field("processed_at", pa.timestamp("us")),
        "label_request_id": pa.field("label_request_id", pa.string(), nullable=True),
    }

    # Merge the existing schema with the new fields
    additional_schema = pa.schema(list(new_fields.values()))
    extended_schema = pa.unify_schemas([additional_schema, schema.arrow_schema])

    return extended_schema


def _calculate_frame_table_timestamps(
    frame_table: pa.Table, frame_table_enriched_schema: EnrichedSchema
) -> tuple[list[pa.timestamp], list[pa.timestamp]]:
    # For each row, find min and max across all non-null FrameTimestamp columns
    frame_rows = frame_table.to_pylist()
    timestamp_columns = [col.name for col in frame_table_enriched_schema.values() if isinstance(col, FrameTimestamp)]

    recorded_at_min_list = []
    recorded_at_max_list = []

    for row in frame_rows:
        timestamps = [row[col] for col in timestamp_columns if row.get(col) is not None]
        if timestamps:
            # Convert nanosecond integer timestamps to pa.timestamp("us")
            min_ts = min(timestamps)
            max_ts = max(timestamps)
            min_ts_us = pa.scalar(np.datetime64(min_ts // 1000, "us"), type=pa.timestamp("us"))
            max_ts_us = pa.scalar(np.datetime64(max_ts // 1000, "us"), type=pa.timestamp("us"))
            recorded_at_min_list.append(min_ts_us)
            recorded_at_max_list.append(max_ts_us)
        else:
            raise ValueError(
                f"No valid timestamps found for row with sequence_custom_sha {row.get('sequence_custom_sha')}. "
                "Ensure that at least one FrameTimestamp column is present."
            )

    return recorded_at_min_list, recorded_at_max_list


def _extend_object_table(
    processed_at: np.datetime64,
    vin: dict[str, str],
    label_request_id: dict[str, str],
    object_table: pa.Table,
    object_table_enriched_schema: EnrichedSchema,
    frame_table: pa.Table,
) -> pa.Table:
    """Extend the object table with traceability information and other metadata.

    Args:
        processed_at (datetime): The datetime of processing, guaranteed to be the same as in the sequence table.
        vin (dict[str, str]): A mapping from sequence_custom_sha to VIN.
        label_request_id (dict[str, str]): A mapping from sequence_custom_sha to label_request_id.
        object_table (pa.Table): The original object table.
        object_table_enriched_schema (EnrichedSchema): The enriched schema for the object table.

    Returns:
        pa.Table: The extended object table.
    """
    # Extend the schema with new fields if not already present
    extended_schema = _extend_object_schema(object_table_enriched_schema)

    # Prepare new columns
    num_rows = object_table.num_rows

    # Use columnar access for memory efficiency
    timestamp_arr = _estimate_best_object_timestamps(object_table, frame_table)
    sequence_custom_sha_arr = object_table.column("sequence_custom_sha")
    # Convert to Python objects only for lookup
    sequence_custom_sha_py = [s.as_py() for s in sequence_custom_sha_arr]
    # Convert timestamps to microseconds, handle None
    recorded_at_py = [ts.as_py() / 1e3 if ts is not None else None for ts in timestamp_arr]
    recorded_at_array = pa.array(recorded_at_py, type=pa.timestamp("us"))
    vins = pa.array([vin.get(s, None) for s in sequence_custom_sha_py], pa.string())
    label_request_ids = pa.array([label_request_id.get(s, None) for s in sequence_custom_sha_py], pa.string())

    new_columns = {
        "processed_at": pa.array([processed_at] * num_rows, pa.timestamp("us")),
        "vin": vins,
        "label_request_id": label_request_ids,
        "recorded_at": recorded_at_array,
    }

    # Merge new columns with existing columns, avoiding duplicates
    existing = {name: object_table.column(name) for name in object_table.schema.names}
    merged = {**existing, **new_columns}
    extended_table = pa.table(merged, schema=extended_schema)
    return extended_table


def _estimate_best_object_timestamps(object_table: pa.Table, frame_table: pa.Table) -> pa.Array:
    if "timestamp" in object_table.column_names:
        # If the object table already has a timestamp column, use it
        return object_table.column("timestamp")

    logger.info("Object table does not have a 'timestamp' column, using frame_level_timestamps instead.")

    # Define preferred camera timestamp columns in order of preference
    preferred_timestamp_columns = [
        "tv_right_rectified_frame_timestamp",
        "fc1_frame_rectified_timestamp",
        "tv_left_rectified_frame_timestamp",
        "tv_front_rectified_frame_timestamp",
        "tv_rear_rectified_frame_timestamp",
    ]

    # Find the first available timestamp column in the frame table
    selected_timestamp_col = None
    for col in preferred_timestamp_columns:
        if col in frame_table.column_names:
            selected_timestamp_col = col
            logger.info(f"Using {col} for object timestamps from frame table.")
            break

    if selected_timestamp_col is None:
        raise ValueError(
            "No suitable timestamp column found in the frame table. "
            "Ensure that the frame table contains one of the preferred timestamp columns."
        )

    # Create mapping from frame_master_index to timestamp
    frame_indices = frame_table.column("frame_master_index").to_pylist()
    frame_timestamps = frame_table.column(selected_timestamp_col).to_pylist()
    frame_index_to_timestamp = dict(zip(frame_indices, frame_timestamps))

    # Map object frame_master_index to timestamps
    object_frame_indices = object_table.column("frame_master_index").to_pylist()
    object_timestamps = [frame_index_to_timestamp.get(frame_idx, None) for frame_idx in object_frame_indices]

    return pa.array(object_timestamps)


def _extend_object_schema(schema: EnrichedSchema) -> pa.Schema:
    """Extend the object schema with traceability information and other metadata.

    Args:
        schema (EnrichedSchema): The original enriched schema for the object table.

    Returns:
        pa.Schema: The extended enriched schema for the object table.
    """
    # Define new fields to add
    new_fields = {
        "vin": pa.field("vin", pa.string(), nullable=True),
        "recorded_at": pa.field("recorded_at", pa.timestamp("us")),
        "processed_at": pa.field("processed_at", pa.timestamp("us")),
        "label_request_id": pa.field("label_request_id", pa.string(), nullable=True),
    }

    # Merge the existing schema with the new fields
    additional_schema = pa.schema(list(new_fields.values()))
    extended_schema = pa.unify_schemas([additional_schema, schema.arrow_schema])
    return extended_schema


def write_tables_to_databricks(
    tables: list[pa.Table],
    table_names: list[str],
    credential,
    medallion: str = "bronze",
    data_product: str = "viper-gt-manual-sbx",
    account_name: str = "stpaceprodeuw",
):
    """Write multiple tables to Databricks.

    Args:
        tables (list[pa.Table]): List of tables to write.
        table_names (list[str]): List of table names corresponding to the tables.
        medallion (str, optional): Medallion layer (e.g., "bronze", "silver", "gold"). Defaults to "bronze".
        data_product (str, optional): Data product name. Defaults to "viper-gt-manual-sbx".
        credential: Credentials for Databricks connection.
        account_name (str, optional): Azure account name. Defaults to "stpaceprodeuw".
    """
    endpoint = AzureGen2Endpoint(
        account_name=account_name,
        tenant_id=TENANT_ID,
        credential=credential,
    )

    assert len(tables) == len(table_names), "Number of tables and table names must match."

    for table, table_name in zip(tables, table_names):
        assert table.schema is not None, f"Table {table_name} has no schema."
        # dbx cannot handle enriched schemas and no nullable fields, so we cast to a simple schema
        # without metadata and all fields nullable
        table_no_meta = table.cast(
            pa.schema([pa.field(field.name, field.type, nullable=True) for field in table.schema])
        )

        logger.info(f"Writing table {table_name} to Databricks medallion {medallion}.")
        write_deltalake(
            table_or_uri=f"abfss://{data_product}@{account_name}.dfs.core.windows.net/{medallion}/{table_name}/",
            storage_options=dict(endpoint.table_storage_options()),
            data=table_no_meta,
            mode="append",
        )
        logger.info(f"Table {table_name} written to Databricks medallion {medallion}.")
