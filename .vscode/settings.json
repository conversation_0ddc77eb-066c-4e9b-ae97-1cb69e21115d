{"editor.formatOnSave": true, "files.trimFinalNewlines": true, "files.insertFinalNewline": true, "files.trimTrailingWhitespace": true, "terminal.integrated.scrollback": 10000, "editor.wordWrapColumn": 120, "editor.rulers": [79, 119], "python.defaultInterpreterPath": ".venv/bin/python", "python.testing.unittestEnabled": false, "python.testing.pytestEnabled": true, "python.testing.pytestPath": ".venv/bin/pytest", "autoDocstring.docstringFormat": "google", "autoDocstring.guessTypes": true, "notebook.output.textLineLimit": 1000, "python.testing.pytestArgs": ["xflow/xusecases/tests/vision/models/multiview/release/"]}